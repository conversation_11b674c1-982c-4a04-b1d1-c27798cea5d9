object TestPickAnzeigeForm: TTestPickAnzeigeForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Testen der Pickanzeige'
  ClientHeight = 257
  ClientWidth = 377
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    377
    257)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 93
    Width = 38
    Height = 13
    Caption = 'Anzeige'
  end
  object Label2: TLabel
    Left = 8
    Top = 11
    Width = 34
    Height = 13
    Caption = 'Station'
  end
  object Bevel1: TBevel
    Left = 6
    Top = 216
    Width = 365
    Height = 6
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 192
  end
  object Label25: TLabel
    Left = 8
    Top = 46
    Width = 50
    Height = 13
    Caption = 'LED-Farbe'
  end
  object Bevel2: TBevel
    Left = 6
    Top = 80
    Width = 365
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object DisplayComboBox: TComboBoxPro
    Left = 8
    Top = 112
    Width = 361
    Height = 21
    Style = csOwnerDrawFixed
    ItemHeight = 15
    TabOrder = 2
    OnChange = DisplayComboBoxChange
  end
  object AnzeigeAusButton: TButton
    Left = 56
    Top = 152
    Width = 75
    Height = 25
    Caption = 'Aus'
    TabOrder = 3
    OnClick = AnzeigeAusButtonClick
  end
  object StationComboBox: TComboBoxPro
    Left = 80
    Top = 8
    Width = 289
    Height = 21
    Style = csOwnerDrawFixed
    ItemHeight = 15
    TabOrder = 0
    OnChange = StationComboBoxChange
  end
  object AnzeigeEinButton: TButton
    Left = 144
    Top = 152
    Width = 75
    Height = 25
    Caption = 'Ein'
    TabOrder = 4
    OnClick = AnzeigeEinButtonClick
  end
  object AnzeigeBlinkButton: TButton
    Left = 233
    Top = 152
    Width = 75
    Height = 25
    Caption = 'Blinken'
    TabOrder = 5
    OnClick = AnzeigeBlinkButtonClick
  end
  object CloseButton: TButton
    Left = 294
    Top = 225
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 6
  end
  object PickByLightColorComboBox: TComboBox
    Left = 80
    Top = 43
    Width = 289
    Height = 21
    Style = csDropDownList
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 13
    TabOrder = 1
    OnChange = PickByLightColorComboBoxChange
    Items.Strings = (
      'Rot'
      'Gr'#252'n'
      'Blau')
  end
  object RundlaufButton: TButton
    Left = 56
    Top = 185
    Width = 252
    Height = 25
    Caption = 'Rundlauftest starten'
    TabOrder = 7
    OnClick = RundlaufButtonClick
  end
  object Timer1: TTimer
    Interval = 2000
    OnTimer = Timer1Timer
    Left = 320
    Top = 88
  end
end
