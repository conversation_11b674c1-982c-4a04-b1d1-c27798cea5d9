﻿{$define UsePNGObject}

unit LablePrinterUtils;

interface

uses PrinterUtils, PrintModul;

type
  TLEDaten = record
    MandantKopfText : String;
    Einheit : String;
    LENummer : String;
    ArtikelNr,
    ArtikelText : String;
    MHD : String;
    Charge : String;
    Menge : Integer;
    Gewicht : Integer;
  end;

function GetLELPCheckChar      (const Line : String) : AnsiChar;

function PrintLELabel          (const PrinterModel : String; const Anzahl : Integer; const Daten : TLEDaten) : Integer; overload;
function PrintLELabel          (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const LENr : String; const RefLT : Integer; var ErrText : String) : Integer; overload;
function PrintLELabel          (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefLE : Integer; var ErrText : String) : Integer; overload;
function PrintLEEinlagerLabel  (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefLE : Integer; var ErrText : String) : Integer;
function PrintPalettenLabels   (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefLE, RefBes : Integer; var ErrText : String) : Integer;
function PrintPalettenHULabels (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefLE, RefBes : Integer; var ErrText : String) : Integer;

function PrintBestandLabel     (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefBes : Integer; var ErrText : String) : Integer; overload;
function PrintBestandLabel     (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const LENr, NVENr : String; const RefArtikel, RefEinheit, Menge, NettoGewicht : Integer; const HSD, MHD : TDateTime; const ARVariante, Charge : String; var ErrText : String) : Integer; overload;
function PrintBestandLabel     (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefWE, RefWEPos : Integer; var ErrText : String) : Integer; overload;

function PrintWEPalettenLabels (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefWE, RefWEPos : Integer; const ShowProgress : Boolean; var ErrText : String) : Integer;

function PrintDOCID            (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const DocIDText : String; const DocIDStr : String; var ErrText : String) : Integer;

function PrintAdressLabel      (const PrtDaten : TPrinterPorts; const RefAuf : Integer; var ErrText : String) : Integer;
function PrintRetourenLabel    (const PrtDaten : TPrinterPorts; const RefAuf : Integer; var ErrText : String) : Integer;

function PrintRetourenPosLabel      (const RefRetPos : Integer; var ErrorMsg : String) : Integer; overload;
function PrintRetourenPosLabel      (const PrtDaten : TPrinterPorts; const RefRetPos : Integer; var ErrorMsg : String) : Integer; overload;
function PrintRetourenBestandLabel  (const PrtDaten : TPrinterPorts; const RefBestand : Integer; var ErrorMsg : String) : Integer;
function PrintKommBestandLabel      (const PrtDaten : TPrinterPorts; const RefAKP : Integer; var ErrorMsg : String) : Integer;

function PrintZusatzLabel      (const PrtDaten : TPrinterPorts; const LableName : String; const RefAuf : Integer; var ErrText : String) : Integer;
function PrintNVEZusatzLabel   (const PrtDaten : TPrinterPorts; const LableName : String; const RefNVE : Integer; var ErrText : String) : Integer;

function PrintGraphiLabel      (const PrtDaten : TPrinterPorts; const GraphiFilename : String; var TrackingNr, TrackingURL, ErrText : String) : Integer;

function GetIDBarcode          (const RefTyp : Char; const ID : String) : String;

implementation

uses Windows, WinSpool, Classes, ADODB, DB, SysUtils, Printers, Graphics, StringUtils,
     DatenModul, EAN128Utils, VerlaufDLG, DateUtils, Forms, ImageDLG,
     JPEG,
     GR32,
     GR32_PortableNetworkGraphic,
     GR32_PNG,
     PNGImage,

     ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetLELPCheckChar (const Line : String) : AnsiChar;
var
  i,
  summe,
  faktor : Integer;
begin
  i := Length (Line);
  faktor := 4;
  summe := 0;

  while (i > 0) do begin
    if (line [i] >= '0') and (line [i] <= '9') then
      summe := summe + ((Ord (line [i]) - Ord ('0')) * faktor)
    else
      summe := summe + (Ord (line [i]) * faktor);

    if (faktor = 4) then
      faktor := 9
    else faktor := 4;

    Dec (i);
  end;

  Result := AnsiChar (Ord ('0') + (summe mod 10));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetRefBarcode (const RefTyp : Char; const Ref : Integer) : String;
var
  barstr : String;
begin
  barstr := IntToStr (Ref);

  if ((Length (barstr) mod 2) = 1) then
    barstr := '0' + barstr;

  barstr := '0'+RefTyp+barstr;

  Result := barstr + GetLELPCheckChar (barstr);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetIDBarcode (const RefTyp : Char; const ID : String) : String;
var
  barstr : String;
begin
  barstr := '0'+RefTyp+ID;

  Result := barstr + GetLELPCheckChar (barstr);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure SplitArtikelText (const ArtikelText : String; const MaxLen : Integer; var ParamArray : array of AnsiString; var ParamIndex : Integer);
var
  strpos : Integer;
  artextstr : String;
begin
  artextstr := ArtikelText;

  if (Length (artextstr) <= MaxLen) then begin
    ParamArray [ParamIndex] := 'AR_TEXT_1:'+artextstr;
    artextstr := '';
  end else begin
    strpos := MaxLen;

    while (strpos > (MaxLen div 2)) and (artextstr [strpos] in ['a'..'z','A'..'Z','0'..'9']) do
      Dec (strpos);

    paramarray [ParamIndex] := 'AR_TEXT_1:'+Copy (artextstr, 1, strpos);
    artextstr := TrailingStripString (Copy (artextstr, strpos + 1));
  end;
  Inc (ParamIndex);

  if (Length (artextstr) <= MaxLen) then begin
    ParamArray [ParamIndex] := 'AR_TEXT_2:'+artextstr;
    artextstr := '';
  end else begin
    strpos := MaxLen;

    while (strpos > (MaxLen div 2)) and (artextstr [strpos] in ['a'..'z','A'..'Z','0'..'9']) do
      Dec (strpos);

    ParamArray [ParamIndex] := 'AR_TEXT_2:'+Copy (artextstr, 1, strpos);
    artextstr := TrailingStripString (Copy (artextstr, strpos + 1));
  end;
  Inc (ParamIndex);

  ParamArray [ParamIndex]  := 'AR_TEXT_3:'+Copy (artextstr, 1, MaxLen);
  Inc (ParamIndex);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintLELabel (const PrinterModel : String; const Anzahl : Integer; const Daten : TLEDaten) : Integer;
var
  res     : Integer;
  errtext : String;
begin
  res := BeginPrinting ('LE-Label', errtext);

  if (res = 0) then begin
    if (PrinterModel = 'TPL2844') then begin
      res := PrintLine (CRLF+'N'+CRLF+'I8,1,049'+CRLF, errtext);
      if (res = 0) then res := PrintLine ('A100,20,0,5,1,1,N,"'+Daten.MandantKopfText+'"'+CRLF, errtext);
      if (res = 0) then res := PrintLine ('A100,90,0,3,2,2,N,"'+Daten.ArtikelNr+'"'+CRLF, errtext);
      if (res = 0) then res := PrintLine ('A300,100,0,3,1,1,N,"'+Daten.ArtikelText+'"'+CRLF, errtext);
      if (res = 0) then res := PrintLine ('A100,160,0,2,1,1,N,"MHD:"'+CRLF, errtext);
      if (res = 0) then res := PrintLine ('A200,160,0,3,1,1,N,"'+Daten.MHD+'"'+CRLF, errtext);
      if (res = 0) then res := PrintLine ('A100,190,0,2,1,1,N,"Charge:"'+CRLF, errtext);
      if (res = 0) then res := PrintLine ('A200,190,0,3,1,1,N,"'+Daten.Charge+'"'+CRLF, errtext);
      if (res = 0) then res := PrintLine ('A400,160,0,2,1,1,N,"Menge:"'+CRLF, errtext);
      if (res = 0) then res := PrintLine ('A600,160,0,3,1,1,N,"'+IntToStr (Daten.Menge)+' '+Daten.Einheit+'"'+CRLF, errtext);
      if (res = 0) then res := PrintLine ('B100,240,0,2,8,16,300,N,"'+FormatStr (Daten.LENummer, -8, '0')+'"'+CRLF, errtext);
      if (res = 0) then res := PrintLine ('A300,560,0,4,1,1,N,"LE '+FormatStr (Daten.LENummer, -8, '0')+'"'+CRLF, errtext);

      if (res = 0) then res := PrintLine ('P'+IntToStr (Anzahl)+CRLF, errtext);
    end else if (PrinterModel = 'SATO') then begin
      if (res = 0) then res := PrintLine (ESC+'A', errtext);
      if (res = 0) then res := PrintLine (ESC+'A3H61V0000', errtext);
      if (res = 0) then res := PrintLine (ESC+'%3', errtext);
      if (res = 0) then res := PrintLine (ESC+'A0', errtext);

      if (res = 0) then res := PrintLine (ESC+'H200'+ESC+'V100'+ESC+'B205300'+FormatStr (Daten.LENummer, -8, '0'), errtext);
      if (res = 0) then res := PrintLine (ESC+'L0101', errtext);
      if (res = 0) then res := PrintLine (ESC+'%1'+ESC+'H10'+ESC+'V400'+ESC+'WL0'+Daten.MandantKopfText, errtext);
      if (res = 0) then res := PrintLine (ESC+'%1'+ESC+'H70'+ESC+'V350'+ESC+'WL0'+Daten.ArtikelNr, errtext);
      if (res = 0) then res := PrintLine (ESC+'%1'+ESC+'H130'+ESC+'V470'+ESC+'M'+Daten.ArtikelText, errtext);

      if (res = 0) then res := PrintLine (ESC+'%1'+ESC+'H650'+ESC+'V400'+ESC+'M'+DateToStr (Now)+' '+TimeToStr (Now), errtext);
      if (res = 0) then res := PrintLine (ESC+'%1'+ESC+'H675'+ESC+'V350'+ESC+'MLE '+FormatStr (Daten.LENummer, -8, '0'), errtext);
      if (res = 0) then res := PrintLine (ESC+'L0101', errtext);
      if (res = 0) then res := PrintLine (ESC+'%1'+ESC+'H700'+ESC+'V450'+ESC+'WL0'+Daten.ArtikelNr+' / '+IntToStr (Daten.Menge)+ ' ' +Daten.Einheit, errtext);

      if (res = 0) then res := PrintLine (ESC+'L0101', errtext);
      if (res = 0) then res := PrintLine (ESC+'%2'+ESC+'H550'+ESC+'V60'+ESC+'WL0LE '+FormatStr (Daten.LENummer, -8, '0'), errtext);
      if (res = 0) then res := PrintLine (ESC+'%2'+ESC+'H600'+ESC+'V450'+ESC+'MFrost & Fresh', errtext);

      if (res = 0) then res := PrintLine (ESC+'Q'+IntToStr (Anzahl), errtext);
      if (res = 0) then res := PrintLine (ESC+'Z', errtext);
    end;

    EndPrinting;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintLEEinlagerLabel  (const  PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefLE : Integer; var ErrText : String) : Integer;
var
  prtres     : Integer;
  fname,
  lestr      : String;
  checkch    : AnsiChar;
  infowin    : TVerlaufForm;
  paramarray : array [0..31] of AnsiString;
  query      : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from V_LE where REF=:RefLE');
    query.Parameters.ParamByName('RefLE').Value := RefLE;

    query.Open;

    if (query.RecordCount = 0) then begin
      prtres  := 10;
      ErrText := FormatMessageText (1410, []);
    end else begin
      infowin := TVerlaufForm.Create (Nil);

      infowin.Caption := FormatResourceText (1525, [GetResourceText (1523)]);
      infowin.Label1.Visible := True;
      infowin.Label1.Caption := FormatMessageText (1412, [GetResourceText (1523)]);
      infowin.ProgressBar1.Max := query.RecordCount;

      infowin.BeginShowModal;

      with prtdaten do
        prtres := OpenPrinterPort(Port, Model, FileOutput, User, Passwd, fname, errtext);

      if (prtres = 0) then begin
        while not (query.Eof) and (prtres = 0) and not (infowin.AbortFlag) do begin
          prtres := BeginPrinting ('LEEIN-Labels', errtext);

          if (prtres = 0) then begin
            lestr := FormatStr (query.FieldByName('LE_NR').AsString,-9,'0');
            checkch := GetLELPCheckChar ('1'+lestr);

            paramarray [2] := 'LAGER:'+query.FieldByName('LAGER').AsString;
            paramarray [3] := 'BARCODE_LE:' + '1'+lestr+checkch;
            paramarray [4] := 'BARCODE_LE_TEXT:' + '1 '+lestr+' '+checkch;
            paramarray [5] := 'KOPIEN:1';

            prtres := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramarray, errtext);

            EndPrinting;
          end;

          infowin.ProgressBar1.StepIt;
          infowin.UpdateModal;

          query.Next;
        end;

        ClosePrinterPort;
      end;

      infowin.EndShowModal;
      infowin.Release;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := prtres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintWEPalettenLabels (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefWE, RefWEPos : Integer; const ShowProgress : Boolean; var ErrText : String) : Integer;
var
  prtres,
  paramidx,
  artextlen,
  oldbesref  : Integer;
  fname,
  barstr,
  lestr,
  barstr_2,
  bartextstr_2,
  bartextstr : String;
  checkch    : AnsiChar;
  infowin    : TVerlaufForm;
  paramarray : array [0..60] of AnsiString;
  bardata    : TEANBarcode;
  query      : TADOQuery;
  subquery   : TADOQuery;
  stop       : Boolean;
begin
  infowin := Nil;

  query    := TADOQuery.Create (Nil);
  subquery := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;
    subquery.LockType := ltReadOnly;
    subquery.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select e.*,k.LP_NR,k.LP_DISP,k.LAGER,r.LP_NR as PLAN_LP_NR,r.LP_NR as PLAN_LP_NR,ar.ARTIKEL_NR_HERSTELLER,ar.ARTIKEL_NR_LIEFERANT,ar.BRAND,ar.ARTIKEL_NR_MANDANT,'+
                   'le.WE_NVE,we.LIEFER_NR,bes.LAST_PICK,lp.BEREICH as LB_NAME,lp.LP_NR,lp.NAME as LP_NAME,lp.LP_DISP,lf.LIEFERANT_NAME'+
                   ' from V_LE_INHALT e'+
                   ' inner join V_BES bes on (bes.REF=e.REF_BES)'+
                   ' inner join VQ_LAGER_BESTAND qbes on (qbes.REF=e.REF_BES)'+
                   ' inner join V_LE le on (le.REF=e.REF_LE)'+
                   ' inner join V_ARTIKEL ar on (ar.REF=e.REF_AR)'+
                   ' left outer join V_LP lp on (lp.REF=le.REF_LP)'+
                   ' left outer join V_WE_POS wep on (wep.REF=e.REF_LE_WE_POS)'+
                   ' left outer join V_WARENEINGANG we on (we.REF=wep.REF_WE)'+
                   ' left outer join V_LIEFERANTEN lf on (lf.REF=qbes.REF_LIEFERANT)'+
                   ' left outer join (select REF_LAGER, REF_AR, REF_EINHEIT, LAGER, LP_NR, LP_DISP'+
                   ' from (select lz.REF_LAGER, lz.REF_AR, lz.REF_EINHEIT, lz.LAGER, lz.LP_NR, lp.LP_DISP from V_KOMM_LP_ZUORDNUNG lz inner join V_LP lp on (lp.REF=lz.REF_LP)'+
                   ' group by lz.REF_LAGER, lz.LAGER, lz.REF_AR, lz.REF_EINHEIT, lz.LP_NR, lp.LP_DISP)) k'+
                   ' on (k.REF_LAGER=e.REF_LAGER and k.REF_AR=e.REF_AR and k.REF_EINHEIT=e.REF_EINHEIT)'+
                   ' left outer join (select REF_LE, LP_NR from (select max (REF), REF_LE, LP_NR from V_LP_RES where STATUS=''ANG'' and VORGANG=''EIN'' group by REF_LE, LP_NR)) r on (r.REF_LE=e.REF_LE)'
                  );

    if (RefWEPos <> -1) then begin
      query.SQL.Add ('where e.REF_LE_WE_POS=:RefWEPos');
      query.Parameters.ParamByName('RefWEPos').Value := RefWEPos;
    end else begin
      query.SQL.Add ('where e.REF_LE_WE=:RefWE');
      query.Parameters.ParamByName('RefWE').Value := RefWE;
    end;

    query.Open;

    if (query.RecordCount = 0) then begin
      prtres  := 10;
      ErrText := FormatMessageText (1411, []);
    end else begin
      if (ShowProgress) then begin
        infowin := TVerlaufForm.Create (Nil);

        infowin.Caption := FormatResourceText (1525, [GetResourceText (1524)]);
        infowin.Label1.Visible := True;
        infowin.Label1.Caption := FormatMessageText (1412, [GetResourceText (1524)]);
        infowin.ProgressBar1.Max := query.RecordCount;

        infowin.BeginShowModal;
      end;

      with prtdaten do
        prtres := OpenPrinterPort(Port, Model, FileOutput, User, Passwd, fname, errtext);

      if (prtres = 0) then begin
        GetNumParam ('ARTEXTLEN:', FormInfo.Options, artextlen, 30);

        stop := False;

        oldbesref := -1;

        subquery.SQL.Clear;
        subquery.SQL.Add ('select * from VQ_LAGER_BESTAND where REF=:ref');

        while not (query.Eof) and (prtres = 0) and not (stop) do begin
          //Verhindern, dass der Hinhalt wegen mehren Komm-LPs mehrfach gedruckt wird
          if (oldbesref = -1) or (oldbesref <> query.FieldByName('REF_BES').AsInteger) then begin
            oldbesref := query.FieldByName('REF_BES').AsInteger;

            subquery.Parameters.ParamByName('ref').Value := query.FieldByName('REF_BES').AsInteger;

            subquery.Open;

            prtres := BeginPrinting ('LE-Labels', errtext);

            if (prtres = 0) then begin
              if query.FieldByName('EAN').IsNull then begin
                barstr := '';
                bartextstr := '';
              end else begin
                bardata.EAN := '';

                bardata.InhaltEAN        := query.FieldByName('EAN').AsString;
                bardata.Einheiten        := query.FieldByName('MENGE_FREI').AsInteger;
                bardata.MHD              := query.FieldByName('MHD').AsString;

                if Assigned (query.FindField ('HERSTELL_DATUM')) then
                  bardata.HerstellungDatum := query.FieldByName('HERSTELL_DATUM').AsString;

                bardata.Charge           := query.FieldByName('CHARGE').AsString;

                if (query.FieldByName('OPT_GEWICHT').AsString = '1') then
                  bardata.NettoGewicht := Round (query.FieldByName('NETTO_GEWICHT').AsFloat * 1000)
                else
                  bardata.NettoGewicht := 0;

                BuildEAN128Barcode (bardata, barstr, bartextstr);
              end;

              (*
              if not (query.FieldByName('LE_NR').IsNull) then begin
                nrstr := query.FieldByName('LE_NR').AsString;

                nr := 0;
                for i:= 1 to Length (nrstr) do
                  nr := (nr + Ord (nrstr [i]) * i) mod $10000;

                barstr := barstr + DC1 + '90' + IntToStr (nr);
                bartextstr := bartextstr + '(90)' + IntToStr (nr);
              end;
              *)

              lestr := FormatStr (query.FieldByName('LE_NR').AsString,-9,'0');
              checkch := GetLELPCheckChar ('1'+lestr);

              if (Length (query.FieldByName('LP_NR').AsString) > 5) then begin
                paramarray [0] := 'KOMM_LP_NR:';
                paramarray [1] := 'KOMM_LP_NR_LANG:'+ query.FieldByName('LP_NR').AsString
              end else begin
                paramarray [0] := 'KOMM_LP_NR:'+ query.FieldByName('LP_NR').AsString;
                paramarray [1] := 'KOMM_LP_NR_LANG:';
              end;

              if (Length (query.FieldByName('PLAN_LP_NR').AsString) > 5) then begin
                paramarray [2] := 'PLAN_LP_NR:';
                paramarray [3] := 'PLAN_LP_NR_LANG:'+ query.FieldByName('PLAN_LP_NR').AsString
              end else begin
                paramarray [2] := 'PLAN_LP_NR:'+ query.FieldByName('PLAN_LP_NR').AsString;
                paramarray [3] := 'PLAN_LP_NR_LANG:';
              end;

              paramarray [4] := 'MANDANT:'+query.FieldByName('MANDANT').AsString;
              paramarray [5] := 'AR_NR:'+query.FieldByName('ARTIKEL_NR').AsString;
              paramarray [6] := 'AR_TEXT:'+query.FieldByName('ARTIKEL_TEXT').AsString;

              paramidx := 7;
              SplitArtikelText (query.FieldByName('ARTIKEL_TEXT').AsString, artextlen, paramarray, paramidx);

              paramarray [10] := 'MHD:'+query.FieldByName('MHD').AsString;
              paramarray [11] := 'MHD_KURZ:'+copy (bardata.MHD,1,6)+copy (bardata.MHD,9,2);
              paramarray [12] := 'CHARGE:'+query.FieldByName('CHARGE').AsString;
              paramarray [13] := 'NETTO_GEWICHT:'+Format ('%8.2f kg', [query.FieldByName('NETTO_GEWICHT').AsFloat]);
              paramarray [14] := 'MENGE:'+IntToStr (query.FieldByName('MENGE_FREI').AsInteger + query.FieldByName('MENGE_SPERR').AsInteger);
              paramarray [15] := 'EINHEIT:'+query.FieldByName('EINHEIT').AsString;
              paramarray [16] := 'BARCODE:'+barstr;
              paramarray [17] := 'BARCODE_TEXT:'+bartextstr;
              paramarray [18] := 'LIEFERANT:'+query.FieldByName('LIEFERANT_NAME').AsString;
              paramarray [19] := 'LE_NR:'+query.FieldByName('LE_NR').AsString;

              paramarray [20] := 'LE_NAME:';
              if Assigned (query.FindField ('LE_NAME')) then
                paramarray [20] := paramarray [20] + query.FieldByName ('LE_NAME').AsString;

              paramarray [21] := 'LT_NAME:'+query.FieldByName('LE_TYPE').AsString;
              paramarray [22] := 'BARCODE_LE:' + '1'+lestr+checkch;
              paramarray [23] := 'BARCODE_LE_TEXT:' + '1 '+lestr+' '+checkch;
              paramarray [24] := 'NOW:'+DateToStr (Now)+' '+TimeToStr (Now);
              paramarray [25] := 'LAGER:'+query.FieldByName('LAGER').AsString;
              paramarray [26] := 'BARCODE_EAN:'+query.FieldByName('EAN').AsString;
              paramarray [27] := 'COLOR:'+query.FieldByName('COLOR_TEXT').AsString;
              paramarray [28] := 'SIZE:'+query.FieldByName('SIZE_TEXT').AsString;
              paramarray [29] := 'MANF_AR_NR:'+query.FieldByName('ARTIKEL_NR_HERSTELLER').AsString;
              paramarray [30] := 'LIEF_AR_NR:'+query.FieldByName('ARTIKEL_NR_LIEFERANT').AsString;
              paramarray [31] := 'WE_NVE:'+query.FieldByName('WE_NVE').AsString;
              paramarray [32] := 'LS_NR:'+query.FieldByName('LIEFER_NR').AsString;
              paramarray [33] := 'SUB_MANDANT:'+query.FieldByName('SUB_MANDANT').AsString;
              paramarray [34] := 'LB_NAME:'+query.FieldByName('LB_NAME').AsString;
              paramarray [35] := 'LP_NR:'+query.FieldByName('LP_NR').AsString;
              paramarray [36] := 'LP_NAME:'+query.FieldByName('LP_NAME').AsString;
              paramarray [37] := 'LP_DISP:'+query.FieldByName('LP_DISP').AsString;
              paramarray [38] := 'SUB_MANDANT:'+query.FieldByName('SUB_MANDANT').AsString;
              paramarray [39] := 'BARCODE_2:'+barstr_2;
              paramarray [40] := 'BARCODE_TEXT_2:'+bartextstr_2;

              if not (query.FieldByName('LAST_PICK').IsNull) then
                paramarray [41] := 'WE_MENGE:'
              else if not (query.FieldByName('MENGE_SPERR').IsNull) then
                paramarray [41] := 'WE_MENGE:'+query.FieldByName('MENGE_SPERR').AsString
              else
                paramarray [41] := 'WE_MENGE:'+query.FieldByName('MENGE_FREI').AsString;

              paramarray [42] := 'KOMM_LP_DISP:'+query.FieldByName('LP_DISP').AsString;

              paramarray [43] := 'WE_DATUM:'+FormatDateTime ('dd.mm.yyyy', query.FieldByName('WE_DATUM').AsDateTime);
              paramarray [44] := 'WOCHE:'+IntToStr (WeekOfTheYear (query.FieldByName('WE_DATUM').AsDateTime));
              paramarray [45] := 'JAHR:'+IntToStr (YearOf (query.FieldByName('WE_DATUM').AsDateTime));
              paramarray [46] := 'MONAT:'+IntToStr (MonthOfTheYear (query.FieldByName('WE_DATUM').AsDateTime));
              paramarray [47] := 'TAG:'+IntToStr (DayOfTheMonth (query.FieldByName('WE_DATUM').AsDateTime));

              paramarray [48] := 'PROJECT_ID:';

              if Assigned (subquery.FindField('PROJECT_ID')) then
                paramarray [48] := paramarray [48] + subquery.FieldByName('PROJECT_ID').AsString;

              paramarray [49] := 'BRAND:'+query.FieldByName('BRAND').AsString;

              paramarray [50] := 'ID_BARCODE:';
              paramarray [51] := 'BESTAND_ID:';
              if Assigned (query.FindField ('BESTAND_ID')) then begin
                paramarray [50] := paramarray [50]+GetIDBarcode ('4', subquery.FieldByName('BESTAND_ID').AsString);
                paramarray [51] := paramarray [51]+subquery.FieldByName('BESTAND_ID').AsString;
              end;

              paramarray[52] := 'MAND_AR_NR:'+query.FieldByName('ARTIKEL_NR_MANDANT').AsString;

              paramarray [53] := 'KOPIEN:1';

              prtres := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramarray, errtext);

              EndPrinting;
            end;

            if Assigned (infowin) then begin
              infowin.ProgressBar1.StepIt;
              infowin.UpdateModal;

              if infowin.AbortFlag then
                stop := True;
            end;

            subquery.Close;
          end;

          query.Next;
        end;

        ClosePrinterPort;
      end;

      if Assigned (infowin) then begin
        infowin.EndShowModal;
        infowin.Release;
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := prtres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintPalettenHULabels (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefLE, RefBes : Integer; var ErrText : String) : Integer;
var
  res,
  prtres,
  paramidx,
  artextlen  : Integer;
  fname,
  barstr,
  barstr_2,
  lestr,
  formname,
  bartextstr,
  bartextstr_2 : String;
  barstr_hu,
  bartextstr_hu  : String;
  checkch    : AnsiChar;
  infowin    : TVerlaufForm;
  paramarray : array [0..49] of AnsiString;
  bardata    : TEANBarcode;
  query      : TADOQuery;
  barforminfo : TFormInfos;
begin
  query := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select'+
                  ' le.*,e.*,ar.ARTIKEL_NR_HERSTELLER,ar.ARTIKEL_NR_LIEFERANT,we.LIEFER_NR,bes.LAST_PICK,lp.BEREICH as LB_NAME,lp.LP_NR,lp.NAME as LP_NAME,lp.LP_DISP'+
                  ',(select nvl (BESCHREIBUNG, NAME) from V_MANDANT where REF=e.REF_MAND) as MANDANT_TEXT'+
                  ',(select LP_NR from V_KOMM_LP_ZUORDNUNG where REF_LAGER=e.REF_LAGER and REF_AR=e.REF_AR and REF_EINHEIT=e.REF_EINHEIT and ROWNUM=1) as KOMM_LP_NR'+
                  ',(select lp.LP_DISP from V_KOMM_LP_ZUORDNUNG lz inner join V_LP lp on (lp.REF=lz.REF_LP) where lz.REF_LAGER=bes.REF_LAGER and lz.REF_AR=bes.REF_AR and lz.REF_EINHEIT=bes.REF_EINHEIT and ROWNUM=1) as KOMM_LP_DISP'+
                  ' from' +
                  ' V_LE le'+
                  ' left outer join V_LE_INHALT e on (e.REF_LE=le.REF)'+
                  ' left outer join V_BES bes on (bes.REF=e.REF_BES)'+
                  ' left outer join V_ARTIKEL ar on (ar.REF=e.REF_AR)'+
                  ' left outer join V_LP lp on (lp.REF=le.REF_LP)'+
                  ' left outer join V_WE_POS wep on (wep.REF=e.REF_LE_WE_POS)'+
                  ' left outer join V_WARENEINGANG we on (we.REF=wep.REF_WE)'+
                  ' where le.REF=:RefLE');
    query.Parameters.ParamByName('RefLE'). Value := RefLE;

    if (RefBes <> -1) then begin
      query.SQL.Add ('and e.REF_BES=:RefBes');
      query.Parameters.ParamByName('RefBes'). Value := RefBes;
    end;

    try
      query.Open;

      if (query.RecordCount = 0) then begin
        prtres  := 10;
        ErrText := FormatMessageText (1413, []);
      end else begin
        infowin := TVerlaufForm.Create (Nil);

        try
          infowin.Caption := FormatResourceText (1525, [GetResourceText (1524)]);
          infowin.Label1.Visible := True;
          infowin.Label1.Caption := FormatMessageText (1412, [GetResourceText (1524)]);
          infowin.ProgressBar1.Max := query.RecordCount;

          infowin.BeginShowModal;

          with prtdaten do
            prtres := OpenPrinterPort(Port, Model, FileOutput, User, Passwd, fname, errtext);

          if (prtres = 0) then begin
            barstr        := '';
            barstr_2      := '';
            bartextstr    := '';
            bartextstr_2  := '';
            barstr_hu     := '';
            bartextstr_hu := '';

            GetNumParam ('ARTEXTLEN:', FormInfo.Options, artextlen, 30);

            while not (query.Eof) and (prtres = 0) and not (infowin.AbortFlag) do begin
              prtres := BeginPrinting ('HU-Labels', errtext);

              if (prtres = 0) then begin
                if (query.FieldByName('MHD').IsNull) then
                  bardata.MHD := ''
                else
                  bardata.MHD := query.FieldByName('MHD').AsString;

                if Assigned (query.FindField ('HERSTELL_DATUM')) then begin
                  if (query.FieldByName('HERSTELL_DATUM').IsNull) then
                    bardata.HerstellungDatum := ''
                  else
                    bardata.HerstellungDatum := query.FieldByName('HERSTELL_DATUM').AsString;
                end;

                if (query.FieldByName('CHARGE').IsNull) then
                  bardata.Charge := ''
                else
                  bardata.Charge := query.FieldByName('CHARGE').AsString;

                if (query.FieldByName('EAN').IsNull) then
                  formname := FormInfo.FormularName
                else begin
                  bardata.EAN := '';

                  bardata.InhaltEAN := query.FieldByName('EAN').AsString;
                  bardata.Einheiten := query.FieldByName('MENGE_FREI').AsInteger;

                  if (query.FieldByName('OPT_GEWICHT').AsString = '1') then
                    bardata.NettoGewicht := Round (query.FieldByName('NETTO_GEWICHT').AsFloat * 1000)
                  else
                    bardata.NettoGewicht := 0;

                  if (Length (bardata.Charge) <= 10) then begin
                    formname := FormInfo.FormularName;

                    BuildEAN128Barcode (bardata, barstr, bartextstr)
                  end else begin
                    res := DetectFormular (query.FieldByName('REF_MAND').AsInteger, query.FieldByName('REF_LAGER').AsInteger, '', '', FormInfo.Generator, FormInfo.ReportName+ '_2', barforminfo);

                    if (res <> 0) then begin
                      prtres := res;
                      ErrText := 'Kein passendes 2-Barcode Druckformular ('+FormInfo.Generator+'/'+FormInfo.ReportName+') für die Palettenlabels vorhanden';
                    end else begin
                      formname := barforminfo.FormularName;

                      BuildEAN128Barcode (bardata, barstr, bartextstr, barstr_2, bartextstr_2);
                    end;
                  end;
                end;

                if (prtres = 0) then begin
                  lestr := FormatStr (query.FieldByName('LE_NR').AsString,-9,'0');
                  checkch := GetLELPCheckChar ('1'+lestr);

                  if Assigned (query.FindField ('WE_HU_NR')) and not (query.FieldByName('WE_HU_NR').IsNull) then begin
                    barstr_hu     := '6JUN1111111'+StripLeadingZeros (query.FieldByName('WE_HU_NR').AsString);
                    bartextstr_hu := '(6J) UN 1111111 '+StripLeadingZeros (query.FieldByName('WE_HU_NR').AsString);
                  end;

                  if (Length (query.FieldByName('KOMM_LP_NR').AsString) > 5) then begin
                    paramarray [0] := 'KOMM_LP_NR:';
                    paramarray [1] := 'KOMM_LP_NR_LANG:'+ query.FieldByName('KOMM_LP_NR').AsString
                  end else begin
                    paramarray [0] := 'KOMM_LP_NR:'+ query.FieldByName('KOMM_LP_NR').AsString;
                    paramarray [1] := 'KOMM_LP_NR_LANG:';
                  end;

                  paramarray [2] := 'PLAN_LP_NR:';
                  paramarray [3] := 'PLAN_LP_NR_LANG:';

                  paramarray [4] := 'AR_NR:'+query.FieldByName('ARTIKEL_NR').AsString;
                  paramarray [5] := 'AR_TEXT:'+query.FieldByName('ARTIKEL_TEXT').AsString;

                  paramidx := 6;
                  SplitArtikelText (query.FieldByName('ARTIKEL_TEXT').AsString, artextlen, paramarray, paramidx);

                  paramarray [9] := 'MHD:'+bardata.MHD;
                  paramarray [10] := 'MHD_KURZ:'+copy (bardata.MHD,1,6)+copy (bardata.MHD,9,2);
                  paramarray [11] := 'CHARGE:'+bardata.Charge;

                  if (query.FieldByName('NETTO_GEWICHT').IsNull) then
                    paramarray [12] := 'NETTO_GEWICHT:'
                  else
                    paramarray [12] := 'NETTO_GEWICHT:'+Format ('%8.2f kg', [query.FieldByName('NETTO_GEWICHT').AsFloat]);

                  paramarray [13] := 'MENGE:'+IntToStr (query.FieldByName('MENGE_FREI').AsInteger + query.FieldByName('MENGE_SPERR').AsInteger);
                  paramarray [14] := 'EINHEIT:'+query.FieldByName('EINHEIT').AsString;
                  paramarray [15] := 'AR_VARIANTE:'+query.FieldByName('AR_VARIANTE').AsString;
                  paramarray [16] := 'COLOR:'+query.FieldByName('COLOR_TEXT').AsString;
                  paramarray [17] := 'SIZE:'+query.FieldByName('SIZE_TEXT').AsString;
                  paramarray [18] := 'BARCODE_EAN:'+query.FieldByName('EAN').AsString;;
                  paramarray [19] := 'BARCODE:'+barstr;
                  paramarray [20] := 'BARCODE_TEXT:'+bartextstr;
                  paramarray [21] := 'WE_DATUM:'+DateToStr (query.FieldByName('WE_DATUM').AsDateTime);
                  paramarray [22] := 'WOCHE:'+IntToStr (WeekOfTheYear (query.FieldByName('WE_DATUM').AsDateTime));
                  paramarray [23] := 'LE_NR:'+query.FieldByName('LE_NR').AsString;

                  paramarray [24] := 'LE_NAME:';
                  if Assigned (query.FindField ('LE_NAME')) then
                    paramarray [24] := paramarray [24] + query.FieldByName ('LE_NAME').AsString;

                  paramarray [25] := 'LT_NAME:'+query.FieldByName('LE_TYPE').AsString;
                  paramarray [26] := 'BARCODE_LE:' + '1'+lestr+checkch;
                  paramarray [27] := 'BARCODE_LE_TEXT:' + '1 '+lestr+' '+checkch;
                  paramarray [28] := 'MANDANT:' + query.FieldByName('MANDANT_TEXT').AsString;
                  paramarray [29] := 'NOW:'+DateToStr (Now)+' '+TimeToStr (Now);
                  paramarray [30] := 'MANF_AR_NR:'+query.FieldByName('ARTIKEL_NR_HERSTELLER').AsString;
                  paramarray [31] := 'LIEF_AR_NR:'+query.FieldByName('ARTIKEL_NR_LIEFERANT').AsString;
                  paramarray [32] := 'WE_NVE:'+query.FieldByName('WE_NVE').AsString;
                  paramarray [33] := 'WE_HU_NR:'+query.FieldByName('WE_HU_NR').AsString;
                  paramarray [34] := 'LS_NR:'+query.FieldByName('LIEFER_NR').AsString;
                  paramarray [35] := 'SUB_MANDANT:'+query.FieldByName('SUB_MANDANT').AsString;
                  paramarray [36] := 'LB_NAME:'+query.FieldByName('LB_NAME').AsString;
                  paramarray [37] := 'LP_NR:'+query.FieldByName('LP_NR').AsString;
                  paramarray [38] := 'LP_NAME:'+query.FieldByName('LP_NAME').AsString;
                  paramarray [39] := 'LP_DISP:'+query.FieldByName('LP_DISP').AsString;
                  paramarray [40] := 'BARCODE_2:'+barstr_2;
                  paramarray [41] := 'BARCODE_TEXT_2:'+bartextstr_2;
                  paramarray [42] := 'BARCODE_HU:'+barstr_hu;
                  paramarray [43] := 'BARCODE_HU_TEXT:'+bartextstr_hu;

                  if not (query.FieldByName('LAST_PICK').IsNull) then
                    paramarray [44] := 'WE_MENGE:'
                  else if not (query.FieldByName('MENGE_SPERR').IsNull) then
                    paramarray [44] := 'WE_MENGE:'+query.FieldByName('MENGE_SPERR').AsString
                  else
                    paramarray [44] := 'WE_MENGE:'+query.FieldByName('MENGE_FREI').AsString;

                  paramarray [45] := 'KOMM_LP_DISP:'+query.FieldByName('KOMM_LP_DISP').AsString;
                  paramarray [46] := 'JAHR:'+IntToStr (YearOf (query.FieldByName('WE_DATUM').AsDateTime));

                  paramarray [47] := 'KOPIEN:1';

                  prtres := PrintTemplate (LabelTemplatePath+formname, paramarray, errtext);
                end;

                EndPrinting;
              end;

              infowin.ProgressBar1.StepIt;
              infowin.UpdateModal;

              query.Next;

              //Nur ein Etikett drucken, wenn darauf keine Artikelinfos ausgegeben werden
              if (Pos ('NO_AR_INFO', FormInfo.Options) > 0) then
                break;
            end;

            ClosePrinterPort;
          end;
        finally
          infowin.EndShowModal;
          infowin.Release;
        end;
      end;
    except
      prtres  := -9;
      ErrText := FormatMessageText (1150, []);
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := prtres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintPalettenLabels (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefLE, RefBes : Integer; var ErrText : String) : Integer;
var
  res,
  prtres,
  paramidx,
  artextlen  : Integer;
  fname,
  barstr,
  barstr_2,
  lestr,
  formname,
  bartextstr,
  bartextstr_2 : String;
  checkch     : AnsiChar;
  infowin     : TVerlaufForm;
  paramarray  : array [0..59] of AnsiString;
  bardata     : TEANBarcode;
  query       : TADOQuery;
  subquery    : TADOQuery;
  barforminfo : TFormInfos;
begin
  query := TADOQuery.Create (Nil);
  subquery := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;
    subquery.LockType := ltReadOnly;
    subquery.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select'+
                  ' le.*,e.*,ar.ARTIKEL_NR_HERSTELLER,ar.ARTIKEL_NR_LIEFERANT,ar.BRAND,we.LIEFER_NR,bes.LAST_PICK,lp.BEREICH as LB_NAME,lp.LP_NR,lp.NAME as LP_NAME,lp.LP_DISP'+
                  ',(select nvl (BESCHREIBUNG, NAME) from V_MANDANT where REF=e.REF_MAND) as MANDANT_TEXT'+
                  ',(select LP_NR from V_KOMM_LP_ZUORDNUNG where REF_LAGER=e.REF_LAGER and REF_AR=e.REF_AR and REF_EINHEIT=e.REF_EINHEIT and ROWNUM=1) as KOMM_LP_NR'+
                  ',(select lp.LP_DISP from V_KOMM_LP_ZUORDNUNG lz inner join V_LP lp on (lp.REF=lz.REF_LP) where lz.REF_LAGER=bes.REF_LAGER and lz.REF_AR=bes.REF_AR and lz.REF_EINHEIT=bes.REF_EINHEIT and ROWNUM=1) as KOMM_LP_DISP'+
                  ' from' +
                  ' V_LE le'+
                  ' left outer join V_LE_INHALT e on (e.REF_LE=le.REF)'+
                  ' left outer join V_BES bes on (bes.REF=e.REF_BES)'+
                  ' left outer join V_ARTIKEL ar on (ar.REF=e.REF_AR)'+
                  ' left outer join V_LP lp on (lp.REF=le.REF_LP)'+
                  ' left outer join V_WE_POS wep on (wep.REF=e.REF_LE_WE_POS)'+
                  ' left outer join V_WARENEINGANG we on (we.REF=wep.REF_WE)'+
                  ' where le.REF=:RefLE');
    query.Parameters.ParamByName('RefLE'). Value := RefLE;

    if (RefBes <> -1) then begin
      query.SQL.Add ('and e.REF_BES=:RefBes');
      query.Parameters.ParamByName('RefBes'). Value := RefBes;
    end;

    try
      query.Open;

      if (query.RecordCount = 0) then begin
        prtres  := 10;
        ErrText := FormatMessageText (1413, []);
      end else begin
        infowin := TVerlaufForm.Create (Nil);

        try
          infowin.Caption := FormatResourceText (1525, [GetResourceText (1524)]);
          infowin.Label1.Visible := True;
          infowin.Label1.Caption := FormatMessageText (1412, [GetResourceText (1524)]);
          infowin.ProgressBar1.Max := query.RecordCount;

          infowin.BeginShowModal;

          with prtdaten do
            prtres := OpenPrinterPort(Port, Model, FileOutput, User, Passwd, fname, errtext);

          if (prtres = 0) then begin
            barstr       := '';
            barstr_2     := '';
            bartextstr   := '';
            bartextstr_2 := '';

            GetNumParam ('ARTEXTLEN:', FormInfo.Options, artextlen, 30);

            while not (query.Eof) and (prtres = 0) and not (infowin.AbortFlag) do begin
              prtres := BeginPrinting ('LE-Labels', errtext);

              if (prtres = 0) then begin
                if (query.FieldByName('MHD').IsNull) then
                  bardata.MHD := ''
                else
                  bardata.MHD := query.FieldByName('MHD').AsString;

                if Assigned (query.FindField ('HERSTELL_DATUM')) then begin
                  if (query.FieldByName('HERSTELL_DATUM').IsNull) then
                    bardata.HerstellungDatum := ''
                  else
                    bardata.HerstellungDatum := query.FieldByName('HERSTELL_DATUM').AsString;
                end;

                if (query.FieldByName('CHARGE').IsNull) then
                  bardata.Charge := ''
                else
                  bardata.Charge := query.FieldByName('CHARGE').AsString;

                if (query.FieldByName('EAN').IsNull) then
                  formname := FormInfo.FormularName
                else begin
                  bardata.EAN := '';

                  bardata.InhaltEAN := query.FieldByName('EAN').AsString;
                  bardata.Einheiten := query.FieldByName('MENGE_FREI').AsInteger;

                  if (query.FieldByName('OPT_GEWICHT').AsString = '1') then
                    bardata.NettoGewicht := Round (query.FieldByName('NETTO_GEWICHT').AsFloat * 1000)
                  else
                    bardata.NettoGewicht := 0;

                  if (Length (bardata.Charge) <= 10) then begin
                    formname := FormInfo.FormularName;

                    BuildEAN128Barcode (bardata, barstr, bartextstr)
                  end else begin
                    res := DetectFormular (query.FieldByName('REF_MAND').AsInteger, query.FieldByName('REF_LAGER').AsInteger, '', '', FormInfo.Generator, FormInfo.ReportName+ '_2', barforminfo);

                    if (res <> 0) then begin
                      prtres := res;
                      ErrText := 'Kein passendes 2-Barcode Druckformular ('+FormInfo.Generator+') für die Palettenlabels vorhanden';
                    end else begin
                      formname := barforminfo.FormularName;

                      BuildEAN128Barcode (bardata, barstr, bartextstr, barstr_2, bartextstr_2);
                    end;
                  end;
                end;

                if (prtres = 0) then begin
                  lestr := FormatStr (query.FieldByName('LE_NR').AsString,-9,'0');
                  checkch := GetLELPCheckChar ('1'+lestr);

                  if (Length (query.FieldByName('KOMM_LP_NR').AsString) > 5) then begin
                    paramarray [0] := 'KOMM_LP_NR:';
                    paramarray [1] := 'KOMM_LP_NR_LANG:'+ query.FieldByName('KOMM_LP_NR').AsString
                  end else begin
                    paramarray [0] := 'KOMM_LP_NR:'+ query.FieldByName('KOMM_LP_NR').AsString;
                    paramarray [1] := 'KOMM_LP_NR_LANG:';
                  end;

                  paramarray [2] := 'PLAN_LP_NR:';
                  paramarray [3] := 'PLAN_LP_NR_LANG:';

                  paramarray [4] := 'AR_NR:'+query.FieldByName('ARTIKEL_NR').AsString;
                  paramarray [5] := 'AR_TEXT:'+query.FieldByName('ARTIKEL_TEXT').AsString;

                  paramidx := 6;
                  SplitArtikelText (query.FieldByName('ARTIKEL_TEXT').AsString, artextlen, paramarray, paramidx);

                  paramarray [9] := 'MHD:'+bardata.MHD;
                  paramarray [10] := 'MHD_KURZ:'+copy (bardata.MHD,1,6)+copy (bardata.MHD,9,2);
                  paramarray [11] := 'CHARGE:'+bardata.Charge;

                  if (query.FieldByName('NETTO_GEWICHT').IsNull) then
                    paramarray [12] := 'NETTO_GEWICHT:'
                  else
                    paramarray [12] := 'NETTO_GEWICHT:'+Format ('%8.2f kg', [query.FieldByName('NETTO_GEWICHT').AsFloat]);

                  paramarray [13] := 'MENGE:'+IntToStr (query.FieldByName('MENGE_FREI').AsInteger + query.FieldByName('MENGE_SPERR').AsInteger);
                  paramarray [14] := 'EINHEIT:'+query.FieldByName('EINHEIT').AsString;
                  paramarray [15] := 'AR_VARIANTE:'+query.FieldByName('AR_VARIANTE').AsString;
                  paramarray [16] := 'COLOR:'+query.FieldByName('COLOR_TEXT').AsString;
                  paramarray [17] := 'SIZE:'+query.FieldByName('SIZE_TEXT').AsString;
                  paramarray [18] := 'BARCODE_EAN:'+query.FieldByName('EAN').AsString;;
                  paramarray [19] := 'BARCODE:'+barstr;
                  paramarray [20] := 'BARCODE_TEXT:'+bartextstr;
                  paramarray [21] := 'WE_DATUM:'+DateToStr (query.FieldByName('WE_DATUM').AsDateTime);
                  paramarray [22] := 'WOCHE:'+IntToStr (WeekOfTheYear (query.FieldByName('WE_DATUM').AsDateTime));
                  paramarray [23] := 'LE_NR:'+query.FieldByName('LE_NR').AsString;

                  paramarray [24] := 'LE_NAME:';
                  if Assigned (query.FindField ('LE_NAME')) then
                    paramarray [24] := paramarray [24] + query.FieldByName ('LE_NAME').AsString;

                  paramarray [25] := 'LT_NAME:'+query.FieldByName('LE_TYPE').AsString;
                  paramarray [26] := 'BARCODE_LE:' + '1'+lestr+checkch;
                  paramarray [27] := 'BARCODE_LE_TEXT:' + '1 '+lestr+' '+checkch;
                  paramarray [28] := 'MANDANT:' + query.FieldByName('MANDANT_TEXT').AsString;
                  paramarray [29] := 'NOW:'+DateToStr (Now)+' '+TimeToStr (Now);
                  paramarray [30] := 'MANF_AR_NR:'+query.FieldByName('ARTIKEL_NR_HERSTELLER').AsString;
                  paramarray [31] := 'LIEF_AR_NR:'+query.FieldByName('ARTIKEL_NR_LIEFERANT').AsString;
                  paramarray [32] := 'WE_NVE:'+query.FieldByName('WE_NVE').AsString;
                  paramarray [33] := 'LS_NR:'+query.FieldByName('LIEFER_NR').AsString;
                  paramarray [34] := 'SUB_MANDANT:'+query.FieldByName('SUB_MANDANT').AsString;
                  paramarray [35] := 'LB_NAME:'+query.FieldByName('LB_NAME').AsString;
                  paramarray [36] := 'LP_NR:'+query.FieldByName('LP_NR').AsString;
                  paramarray [37] := 'LP_NAME:'+query.FieldByName('LP_NAME').AsString;
                  paramarray [38] := 'LP_DISP:'+query.FieldByName('LP_DISP').AsString;
                  paramarray [39] := 'BARCODE_2:'+barstr_2;
                  paramarray [40] := 'BARCODE_TEXT_2:'+bartextstr_2;
                  paramarray [41] := 'BARCODE_2:'+barstr_2;
                  paramarray [42] := 'BARCODE_TEXT_2:'+bartextstr_2;

                  if not (query.FieldByName('LAST_PICK').IsNull) then
                    paramarray [43] := 'WE_MENGE:'
                  else if not (query.FieldByName('MENGE_SPERR').IsNull) then
                    paramarray [43] := 'WE_MENGE:'+query.FieldByName('MENGE_SPERR').AsString
                  else
                    paramarray [43] := 'WE_MENGE:'+query.FieldByName('MENGE_FREI').AsString;

                  paramarray [44] := 'KOMM_LP_DISP:'+query.FieldByName('KOMM_LP_DISP').AsString;
                  paramarray [45] := 'JAHR:'+IntToStr (YearOf (query.FieldByName('WE_DATUM').AsDateTime));

                  paramarray [46] := 'BRAND:'+query.FieldByName('BRAND').AsString;

                  subquery.SQL.Clear;
                  subquery.SQL.Add ('select * from VQ_LAGER_BESTAND where REF=:ref');
                  subquery.Parameters.ParamByName('ref').Value := query.FieldByName('REF_BES').AsInteger;

                  subquery.Open;

                  paramarray [47] := 'PROJECT_ID:';

                  if Assigned (subquery.FindField('PROJECT_ID')) then
                    paramarray [47] := paramarray [47] + subquery.FieldByName('PROJECT_ID').AsString;

                  paramarray [48] := 'ID_BARCODE:';
                  paramarray [49] := 'BESTAND_ID:';
                  if Assigned (subquery.FindField ('BESTAND_ID')) then begin
                    paramarray [48] := paramarray [48]+GetIDBarcode ('4', subquery.FieldByName('BESTAND_ID').AsString);
                    paramarray [49] := paramarray [49]+subquery.FieldByName('BESTAND_ID').AsString;
                  end;

                  subquery.Close;

                  paramarray [50] := 'KOPIEN:1';

                  prtres := PrintTemplate (LabelTemplatePath+formname, paramarray, errtext);
                end;

                EndPrinting;
              end;

              infowin.ProgressBar1.StepIt;
              infowin.UpdateModal;

              query.Next;

              //Nur ein Etikett drucken, wenn darauf keine Artikelinfos ausgegeben werden
              if (Pos ('NO_AR_INFO', FormInfo.Options) > 0) then
                break;
            end;

            ClosePrinterPort;
          end;
        finally
          infowin.EndShowModal;
          infowin.Release;
        end;
      end;
    except
      prtres  := -9;
      ErrText := FormatMessageText (1150, []);
    end;

    query.Close;
  finally
    query.Free;
    subquery.Free;
  end;

  Result := prtres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintBestandLabel (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefWE, RefWEPos : Integer; var ErrText : String) : Integer;
var
  prtres     : Integer;
  query      : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select REF from VQ_LAGER_BESTAND where REF_WE_POS=:RefWEPos');
    query.Parameters.ParamByName('RefWEPos').Value := RefWEPos;

    query.Open;

    if (query.RecordCount = 0) then begin
      prtres  := 10;
      ErrText := FormatMessageText (1414, []);
    end else begin
      prtres  := 0;

      while not (query.Eof) and (prtres = 0) do begin
        prtres := PrintBestandLabel (PrtDaten, FormInfo, query.Fields [0].AsInteger, ErrText);

        query.Next;
      end;
    end;
  finally
    query.Free;
  end;

  Result := prtres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintBestandLabel (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefBes : Integer; var ErrText : String) : Integer;
var
  prtres,
  paramidx,
  artextlen  : Integer;
  fname,
  barstr,
  lestr,
  barlestr,
  barletext,
  bartextstr : String;
  checkch    : AnsiChar;
  paramarray : array [0..50] of AnsiString;
  bardata    : TEANBarcode;
  query      : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select');
    query.SQL.Add ('bes.*,ar.ARTIKEL_NR_HERSTELLER,ar.ARTIKEL_NR_LIEFERANT,ar.BRAND');
    query.SQL.Add (',(select nvl (BESCHREIBUNG, NAME) from V_MANDANT where REF=bes.REF_MAND) as MANDANT_TEXT');
    query.SQL.Add (',(select NAME from V_MANDANT where REF=bes.REF_SUB_MAND) as SUB_MANDANT');
    query.SQL.Add (',ae.OPT_GEWICHT,we.LIEFER_NR,lp.BEREICH as LB_NAME,lp.LP_NR,lp.NAME as LP_NAME,lp.LP_DISP');
    query.SQL.Add (',(select LP_NR from V_KOMM_LP_ZUORDNUNG where REF_LAGER=bes.REF_LAGER and REF_AR=bes.REF_AR and REF_EINHEIT=bes.REF_EINHEIT and ROWNUM=1) as KOMM_LP_NR');
    query.SQL.Add (',(select lp.LP_DISP from V_KOMM_LP_ZUORDNUNG lz inner join V_LP lp on (lp.REF=lz.REF_LP) where lz.REF_LAGER=bes.REF_LAGER and lz.REF_AR=bes.REF_AR and lz.REF_EINHEIT=bes.REF_EINHEIT and ROWNUM=1) as KOMM_LP_DISP');
    query.SQL.Add (',cat.NAME as BES_CATEGORY,cat.DEFINITION as BES_CATEGORY_DEF');
    query.SQL.Add ('from V_BES bes inner join V_ARTIKEL_EINHEIT ae on (ae.REF=bes.REF_AR_EINHEIT) inner join V_ARTIKEL ar on (ar.REF=bes.REF_AR)');
    query.SQL.Add ('left outer join V_LE le on (le.REF=bes.REF_LE)');
    query.SQL.Add ('left outer join V_LP lp on ((bes.REF_LP is not null and lp.REF=bes.REF_LP) or (le.REF_LP is not null and lp.REF=le.REF_LP))');
    query.SQL.Add ('left outer join V_WE_POS wep on (wep.REF=bes.REF_WE_POS)');
    query.SQL.Add ('left outer join V_WARENEINGANG we on (we.REF=wep.REF_WE)');
    query.SQL.Add ('left outer join V_LAGER_BESTAND_CATEGORY cat on (cat.REF=bes.REF_CATEGORY)');
    query.SQL.Add ('where  bes.REF=:RefBes');
    query.Parameters.ParamByName('RefBes').Value := RefBes;

    try
      query.Open;

      if (query.RecordCount = 0) then begin
        prtres  := 10;
        ErrText := FormatMessageText (1415, []);
      end else begin
        with prtdaten do
          prtres := OpenPrinterPort(Port, Model, FileOutput, User, Passwd, fname, errtext);

        if (prtres = 0) then begin
          GetNumParam ('ARTEXTLEN:', FormInfo.Options, artextlen, 30);

          prtres := BeginPrinting ('BES-Labels', errtext);

          if (prtres = 0) then begin
            if Assigned (query.FindField ('HERSTELL_DATUM')) then begin
              if (query.FieldByName('HERSTELL_DATUM').IsNull) then
                bardata.HerstellungDatum := ''
              else
                bardata.HerstellungDatum := query.FieldByName('HERSTELL_DATUM').AsString;
            end;

            if (query.FieldByName('MHD').IsNull) then
              bardata.MHD := ''
            else
              bardata.MHD := query.FieldByName('MHD').AsString;

            if (query.FieldByName('CHARGE').IsNull) then
              bardata.Charge := ''
            else
              bardata.Charge := query.FieldByName('CHARGE').AsString;

            if query.FieldByName('EAN').IsNull then begin
              barstr := '';
              bartextstr := '';
            end else begin
              bardata.EAN := '';

              bardata.InhaltEAN := query.FieldByName('EAN').AsString;
              bardata.Einheiten := query.FieldByName('MENGE_FREI').AsInteger;

              if (query.FieldByName('OPT_GEWICHT').AsString = '1') then
                bardata.NettoGewicht := query.FieldByName('NETTO_GEWICHT').AsInteger
              else
                bardata.NettoGewicht := 0;

              BuildEAN128Barcode (bardata, barstr, bartextstr);
            end;

            if query.FieldByName('LE_NR').IsNull then begin
              barlestr  := '';
              barletext := '';
            end else begin
              lestr   := FormatStr (query.FieldByName('LE_NR').AsString,-9,'0');
              checkch := GetLELPCheckChar ('1'+lestr);

              barlestr  := '1' + lestr + checkch;
              barletext := '1 ' + lestr + ' ' + checkch;
            end;

            if (Length (query.FieldByName('KOMM_LP_NR').AsString) > 5) then begin
              paramarray [0] := 'KOMM_LP_NR:';
              paramarray [1] := 'KOMM_LP_NR_LANG:'+ query.FieldByName('KOMM_LP_NR').AsString
            end else begin
              paramarray [0] := 'KOMM_LP_NR:'+ query.FieldByName('KOMM_LP_NR').AsString;
              paramarray [1] := 'KOMM_LP_NR_LANG:';
            end;

            paramarray [2] := 'PLAN_LP_NR:';
            paramarray [3] := 'PLAN_LP_NR_LANG:';

            paramarray [4] := 'AR_NR:'+query.FieldByName('ARTIKEL_NR').AsString;
            paramarray [5] := 'AR_TEXT:'+query.FieldByName('ARTIKEL_TEXT').AsString;

            paramidx := 6;
            SplitArtikelText (query.FieldByName('ARTIKEL_TEXT').AsString, artextlen, paramarray, paramidx);

            paramarray [9]  := 'MHD:'+bardata.MHD;
            paramarray [10] := 'MHD_KURZ:'+copy (bardata.MHD,1,6)+copy (bardata.MHD,9,2);
            paramarray [11] := 'CHARGE:'+bardata.Charge;
            paramarray [12] := 'NETTO_GEWICHT:'+Format ('%8.2f kg', [query.FieldByName('NETTO_GEWICHT').AsInteger/1000]);
            paramarray [13] := 'MENGE:'+IntToStr (query.FieldByName('MENGE_FREI').AsInteger + query.FieldByName('MENGE_SPERR').AsInteger);
            paramarray [14] := 'EINHEIT:'+query.FieldByName('EINHEIT').AsString;
            paramarray [15] := 'AR_VARIANTE:'+query.FieldByName('AR_VARIANTE').AsString;
            paramarray [16] := 'COLOR:'+query.FieldByName('COLOR_TEXT').AsString;
            paramarray [17] := 'SIZE:'+query.FieldByName('SIZE_TEXT').AsString;
            paramarray [18] := 'BARCODE_EAN:'+query.FieldByName('EAN').AsString;;
            paramarray [19] := 'BARCODE:'+barstr;
            paramarray [20] := 'BARCODE_TEXT:'+bartextstr;
            paramarray [21] := 'WE_DATUM:'+DateToStr (query.FieldByName('EINGANG_DATUM').AsDateTime);
            paramarray [22] := 'WOCHE:'+IntToStr (WeekOfTheYear (query.FieldByName('EINGANG_DATUM').AsDateTime));
            paramarray [23] := 'LE_NR:'+query.FieldByName('LE_NR').AsString;
            paramarray [24] := 'LT_NAME:';
            paramarray [25] := 'BARCODE_LE:' + barlestr;
            paramarray [26] := 'BARCODE_LE_TEXT:' + barletext;
            paramarray [27] := 'MANDANT:' + query.FieldByName('MANDANT_TEXT').AsString;
            paramarray [28] := 'NOW:'+DateToStr (Now)+' '+TimeToStr (Now);
            paramarray [29] := 'MANF_AR_NR:'+query.FieldByName('ARTIKEL_NR_HERSTELLER').AsString;
            paramarray [30] := 'LIEF_AR_NR:'+query.FieldByName('ARTIKEL_NR_LIEFERANT').AsString;
            paramarray [31] := 'WE_NVE:';

            if Assigned (query.FindField('WE_NVE')) then
              paramarray [31] := paramarray [31]+query.FieldByName('WE_NVE').AsString;

            paramarray [32] := 'LS_NR:'+query.FieldByName('LIEFER_NR').AsString;
            paramarray [33] := 'SUB_MANDANT:'+query.FieldByName('SUB_MANDANT').AsString;
            paramarray [34] := 'LB_NAME:'+query.FieldByName('LB_NAME').AsString;
            paramarray [35] := 'LP_NR:'+query.FieldByName('LP_NR').AsString;
            paramarray [36] := 'LP_NAME:'+query.FieldByName('LP_NAME').AsString;
            paramarray [37] := 'LP_DISP:'+query.FieldByName('LP_DISP').AsString;

            if not (query.FieldByName('LAST_PICK').IsNull) then
              paramarray [38] := 'WE_MENGE:'
            else if not (query.FieldByName('MENGE_SPERR').IsNull) then
              paramarray [38] := 'WE_MENGE:'+query.FieldByName('MENGE_SPERR').AsString
            else
              paramarray [38] := 'WE_MENGE:'+query.FieldByName('MENGE_FREI').AsString;

            paramarray [39] := 'KOMM_LP_DISP:'+query.FieldByName('KOMM_LP_DISP').AsString;
            paramarray [40] := 'JAHR:'+IntToStr (YearOf (query.FieldByName('EINGANG_DATUM').AsDateTime));

            paramarray [41] := 'BES_CATEGORY:'+query.FieldByName ('BES_CATEGORY').AsString;
            paramarray [42] := 'BES_CATEGORY_DEF:'+query.FieldByName ('BES_CATEGORY_DEF').AsString;

            paramarray [43] := 'ID_BARCODE:';
            paramarray [44] := 'BESTAND_ID:';
            if Assigned (query.FindField ('BESTAND_ID')) then begin
              paramarray [43] := paramarray [43]+GetIDBarcode ('4', query.FieldByName('BESTAND_ID').AsString);
              paramarray [44] := paramarray [44]+query.FieldByName('BESTAND_ID').AsString;
            end;

            paramarray [45] := 'PROJECT_ID:';

            if Assigned (query.FindField('PROJECT_ID')) then
              paramarray [45] := paramarray [45] + query.FieldByName('PROJECT_ID').AsString;

            paramarray [46] := 'BRAND:'+query.FieldByName('BRAND').AsString;
            paramarray [47] := 'LAGER:'+query.FieldByName('LAGER').AsString;


            paramarray [48] := 'KOPIEN:1';

            prtres := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramarray, errtext);

            EndPrinting;
          end;

          ClosePrinterPort;
        end;
      end;
    except
      prtres  := -9;
      ErrText := FormatMessageText (1150, []);
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := prtres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintBestandLabel (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const LENr, NVENr : String; const RefArtikel, RefEinheit, Menge, NettoGewicht : Integer; const HSD,MHD : TDateTime; const ARVariante, Charge : String; var ErrText : String) : Integer;
var
  prtres,
  paramidx,
  artextlen  : Integer;
  fname,
  barstr,
  lestr,
  barlestr,
  barletext,
  bartextstr : String;
  checkch    : AnsiChar;
  paramarray : array [0..40] of AnsiString;
  bardata    : TEANBarcode;
  query      : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select');
    query.SQL.Add ('ae.EAN,ae.OPT_GEWICHT,ar.ARTIKEL_NR,ar.ARTIKEL_TEXT,ae.EINHEIT,ae.COLOR_TEXT,ae.SIZE_TEXT,ar.ARTIKEL_NR_HERSTELLER,ar.ARTIKEL_NR_LIEFERANT');
    query.SQL.Add (',(select nvl (BESCHREIBUNG, NAME) from V_MANDANT where REF=ar.REF_MAND) as MANDANT_TEXT');
    query.SQL.Add (',ae.OPT_GEWICHT');
    query.SQL.Add ('from V_ARTIKEL_EINHEIT ae, V_ARTIKEL ar');
    query.SQL.Add ('where ar.REF=ae.REF_AR and ae.STATUS=''AKT'' and OPT_MASTER=''1'' and ae.REF_AR=:RefArtikel and REF_EINHEIT=:RefEinheit');
    query.Parameters.ParamByName('RefArtikel').Value := RefArtikel;
    query.Parameters.ParamByName('RefEinheit').Value := RefEinheit;

    try
      query.Open;

      if (query.RecordCount = 0) then begin
        prtres  := 10;
        ErrText := FormatMessageText (1416, []);
      end else begin
        with prtdaten do
          prtres := OpenPrinterPort(Port, Model, FileOutput, User, Passwd, fname, errtext);

        if (prtres = 0) then begin
          GetNumParam ('ARTEXTLEN:', FormInfo.Options, artextlen, 30);

          prtres := BeginPrinting ('BES-Labels', errtext);

          if (prtres = 0) then begin
            if (HSD <= 0) then
              bardata.HerstellungDatum := ''
            else
              bardata.HerstellungDatum := DateToStr (HSD);

            if (MHD <= 0) then
              bardata.MHD := ''
            else
              bardata.MHD := DateToStr (MHD);

            bardata.Charge := Charge;

            if query.FieldByName('EAN').IsNull then begin
              barstr := '';
              bartextstr := '';
            end else begin
              bardata.EAN := '';

              bardata.InhaltEAN := query.FieldByName('EAN').AsString;
              bardata.Einheiten := Menge;

              if (query.FieldByName('OPT_GEWICHT').AsString = '1') then
                bardata.NettoGewicht := NettoGewicht
              else
                bardata.NettoGewicht := 0;

              BuildEAN128Barcode (bardata, barstr, bartextstr);
            end;

            if (Length (LENr) = 0) then begin
              barlestr  := '';
              barletext := '';
            end else begin
              lestr   := FormatStr (LENr,-9,'0');
              checkch := GetLELPCheckChar ('1'+lestr);

              barlestr  := '1' + lestr + checkch;
              barletext := '1 ' + lestr + ' ' + checkch;
            end;

            paramarray [0] := 'KOMM_LP_NR:';
            paramarray [1] := 'KOMM_LP_NR_LANG:';

            paramarray [2] := 'PLAN_LP_NR:';
            paramarray [3] := 'PLAN_LP_NR_LANG:';

            paramarray [4] := 'AR_NR:'+query.FieldByName('ARTIKEL_NR').AsString;
            paramarray [5] := 'AR_TEXT:'+query.FieldByName('ARTIKEL_TEXT').AsString;

            paramidx := 6;
            SplitArtikelText (query.FieldByName('ARTIKEL_TEXT').AsString, artextlen, paramarray, paramidx);

            paramarray [9] := 'MHD:'+bardata.MHD;
            paramarray [10] := 'MHD_KURZ:'+copy (bardata.MHD,1,6)+copy (bardata.MHD,9,2);
            paramarray [11] := 'CHARGE:'+bardata.Charge;
            paramarray [12] := 'NETTO_GEWICHT:'+Format ('%8.2f kg', [NettoGewicht/1000]);
            paramarray [13] := 'MENGE:'+IntToStr (Menge);
            paramarray [14] := 'EINHEIT:'+query.FieldByName('EINHEIT').AsString;
            paramarray [15] := 'AR_VARIANTE:'+ARVariante;
            paramarray [16] := 'COLOR:'+query.FieldByName('COLOR_TEXT').AsString;
            paramarray [17] := 'SIZE:'+query.FieldByName('SIZE_TEXT').AsString;
            paramarray [18] := 'BARCODE_EAN:'+query.FieldByName('EAN').AsString;;
            paramarray [19] := 'BARCODE:'+barstr;
            paramarray [20] := 'BARCODE_TEXT:'+bartextstr;
            paramarray [21] := 'WE_DATUM:'+DateToStr (query.FieldByName('WE_DATUM').AsDateTime);
            paramarray [22] := 'WOCHE:'+IntToStr (WeekOfTheYear (Now));
            paramarray [23] := 'LE_NR:'+LENr;
            paramarray [24] := 'LT_NAME:';
            paramarray [25] := 'BARCODE_LE:' + barlestr;
            paramarray [26] := 'BARCODE_LE_TEXT:' + barletext;
            paramarray [27] := 'MANDANT:' + query.FieldByName('MANDANT_TEXT').AsString;
            paramarray [28] := 'NOW:'+DateToStr (Now)+' '+TimeToStr (Now);
            paramarray [29] := 'MANF_AR_NR:'+query.FieldByName('ARTIKEL_NR_HERSTELLER').AsString;
            paramarray [30] := 'LIEF_AR_NR:'+query.FieldByName('ARTIKEL_NR_LIEFERANT').AsString;
            paramarray [31] := 'JAHR:'+IntToStr (YearOf (Now));
            paramarray [32] := 'KOPIEN:1';

            prtres := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramarray, errtext);

            EndPrinting;
          end;

          ClosePrinterPort;
        end;
      end;
    except
      prtres  := -9;
      ErrText := FormatMessageText (1150, []);
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := prtres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.01.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintLELabel (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const LENr : String; const RefLT : Integer; var ErrText : String) : Integer;
var
  i,
  res,
  prtres     : Integer;
  fname,
  lestr      : String;
  checkch    : AnsiChar;
  infowin    : TVerlaufForm;
  paramarray : array [0..31] of AnsiString;
  paramstru  : array [0..31] of String;
  query      : TADOQuery;
begin
  prtres := 0;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from V_LT_TYPEN where REF=:ref');
    query.Parameters [0].Value := RefLT;

    query.Open;

    if (query.RecordCount = 0) then begin
      prtres  := 10;
      ErrText := FormatMessageText (1410, []);
    end else begin
      infowin := TVerlaufForm.Create (Nil);

      infowin.Caption := FormatResourceText (1525, [GetResourceText (1524)]);
      infowin.Label1.Visible := True;
      infowin.Label1.Caption := FormatMessageText (1412, [GetResourceText (1524)]);
      infowin.ProgressBar1.Max := query.RecordCount;

      infowin.BeginShowModal;

      lestr := FormatStr (LENr,-9,'0');
      checkch := GetLELPCheckChar ('1'+lestr);

      paramarray [0] := 'LT_NAME:'+query.FieldByName('NAME').AsString;
      paramarray [1] := 'LE_NR:'+lestr;

      paramarray [2] := 'LE_NAME:';
      paramarray [3] := 'BARCODE:' + '1'+lestr+checkch;
      paramarray [4] := 'BARCODE_TEXT:' + '1 '+lestr+' '+checkch;
      paramarray [5] := 'BARCODE_LE:' + '1'+lestr+checkch;
      paramarray [6] := 'BARCODE_LE_TEXT:' + '1 '+lestr+' '+checkch;
      paramarray [7] := 'KOPIEN:1';


      if (forminfo.Generator = 'CR') then begin
        for i := Low (paramarray) to High (paramarray) do
          paramstru [i] := paramarray [i];

        res := PrintModule.PrintFormular ('', prtdaten.Port, '', -1, -1, forminfo.FormularName, forminfo, paramstru, False, errtext);
      end else begin
        with prtdaten do
          prtres := OpenPrinterPort(Port, Model, FileOutput, User, Passwd, fname, ErrText);

        if (prtres = 0) then begin
            prtres := BeginPrinting ('LE-Labels', errtext);

          if (prtres = 0) then begin
            prtres := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramarray, ErrText);

            EndPrinting;
          end;

          infowin.ProgressBar1.StepIt;
          infowin.UpdateModal;

          ClosePrinterPort;
        end;
      end;

      infowin.EndShowModal;
      infowin.Release;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := prtres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintLELabel (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const RefLE : Integer; var ErrText : String) : Integer;
var
  prtres     : Integer;
  fname,
  lestr      : String;
  checkch    : AnsiChar;
  infowin    : TVerlaufForm;
  paramarray : array [0..31] of AnsiString;
  query      : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from V_LE where REF=:RefLE');
    query.Parameters.ParamByName('RefLE').Value := RefLE;

    query.Open;

    if (query.RecordCount = 0) then begin
      prtres  := 10;
      ErrText := FormatMessageText (1410, []);
    end else begin
      infowin := TVerlaufForm.Create (Nil);

      infowin.Caption := FormatResourceText (1525, [GetResourceText (1524)]);
      infowin.Label1.Visible := True;
      infowin.Label1.Caption := FormatMessageText (1412, [GetResourceText (1524)]);
      infowin.ProgressBar1.Max := query.RecordCount;

      infowin.BeginShowModal;

      if (forminfo.Generator = 'CR') then begin
        prtres := PrintModule.PrintFormular ('', PrtDaten.Port, '', -1, query.FieldByName('REF_LAGER').AsInteger, '', forminfo, ['REF:'+IntToStr (RefLE)], False, errtext);
      end else begin
        with prtdaten do
          prtres := OpenPrinterPort(Port, Model, FileOutput, User, Passwd, fname, errtext);

        if (prtres = 0) then begin
            prtres := BeginPrinting ('LE-Labels', errtext);

          if (prtres = 0) then begin
            lestr := FormatStr (query.FieldByName('LE_NR').AsString,-9,'0');
            checkch := GetLELPCheckChar ('1'+lestr);

            paramarray [0] := 'LT_NAME:'+query.FieldByName('LE_TYPE').AsString;
            paramarray [1] := 'LE_NR:'+lestr;

            paramarray [2] := 'LE_NAME:';
            if Assigned (query.FindField ('LE_NAME')) then
              paramarray [2] := paramarray [2] + query.FieldByName ('LE_NAME').AsString;

            paramarray [3] := 'BARCODE:' + '1'+lestr+checkch;
            paramarray [4] := 'BARCODE_TEXT:' + '1 '+lestr+' '+checkch;
            paramarray [5] := 'BARCODE_LE:' + '1'+lestr+checkch;
            paramarray [6] := 'BARCODE_LE_TEXT:' + '1 '+lestr+' '+checkch;
            paramarray [7] := 'KOPIEN:1';

            prtres := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramarray, errtext);

            EndPrinting;
          end;

          infowin.ProgressBar1.StepIt;
          infowin.UpdateModal;

          ClosePrinterPort;
        end;
      end;

      infowin.EndShowModal;
      infowin.Release;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := prtres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintDOCID (const PrtDaten : TPrinterPorts; const FormInfo : TFormInfos; const DocIDText : String; const DocIDStr : String; var ErrText : String) : Integer;
var
  pcount,
  prtres     : Integer;
  lestr,
  fname      : String;
  checkch    : AnsiChar;
  paramstr   : array [0..31] of AnsiString;
begin
  with prtdaten do
    prtres := OpenPrinterPort(Port, Model, FileOutput, User, Passwd, fname, errtext);

  if (prtres = 0) then begin
    prtres := BeginPrinting ('DocID Labels', errtext);

    if (prtres = 0) then begin
      lestr := FormatStr (DocIDStr, -8,'0');
      checkch := GetLELPCheckChar ('51'+lestr);

      pcount := 0;

      paramstr [pcount] := 'DOCID_TEXT:'+DocIDText;
      Inc (pcount);
      paramstr [pcount] := 'DOCID_NR:'+DocIDStr;
      Inc (pcount);
      paramstr [pcount] := 'BARCODE:' + '50'+lestr+checkch;
      Inc (pcount);
      paramstr [pcount] := 'BARCODE_TEXT:' + '50 '+lestr+' '+checkch;

      prtres := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

      EndPrinting;
    end;
  end;

  Result := prtres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintAdressLabel (const PrtDaten : TPrinterPorts; const RefAuf : Integer; var ErrText : String) : Integer;
var
  res       : Integer;
  forminfo  : TFormInfos;
  paramstr  : array [0..31] of AnsiString;
  fname     : String;
  query     : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select a.REF_MAND, a.REF_LAGER, m.ADR_ZUSATZ, m.STRASSE, m.LAND, m.PLZ, m.ORT');
    query.SQL.Add (',adr.NAME1 as ABS_NAME, adr.STRASSE as ABS_STRASSE, adr.LAND as ABS_LAND, adr.PLZ as ABS_PLZ, adr.ORT as ABS_ORT');
    query.SQL.Add (',ret.RETOUREN_NR');
    query.SQL.Add ('from V_AUFTRAG a inner join V_MANDANT m on (m.REF=nvl (a.REF_SUB_MAND, a.REF_MAND)) inner join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR) left outer join V_RETOUREN_NR ret on (ret.REF_AUF_KOPF=a.REF) where a.REF=:RefAuf');
    query.Parameters.ParamByName('RefAuf').Value := RefAuf;

    try
      query.Open;

      res := DetectFormular (query.FieldByName ('REF_MAND').AsInteger, query.FieldByName ('REF_LAGER').AsInteger, '', '', PrtDaten.Model, 'RETOUREN_ADR', forminfo);
      if (res <> 0) Then
        ErrText := FormatMessageText (1320, [PrtDaten.Model, 'RETOUREN_ADR'])
      else begin
        res := OpenPrinterPort (PrtDaten.Port, PrtDaten.Model, PrtDaten.FileOutput, PrtDaten.User, PrtDaten.Passwd, fname, ErrText);

        if (res = 0) then begin
          res := BeginPrinting ('RETOUREN_ADR',ErrText);

          if (res = 0) then begin
            paramstr [0] := 'ABS_NAME:'+query.FieldByName ('ABS_NAME').AsString;
            paramstr [1] := 'ABS_STRASSE:'+query.FieldByName ('ABS_STRASSE').AsString;

            if (query.FieldByName ('ABS_LAND').IsNull) then
              paramstr [2] := 'ABS_ORT:'+query.FieldByName ('ABS_PLZ').AsString+ ' ' + query.FieldByName ('ABS_ORT').AsString
            else
              paramstr [2] := 'ABS_ORT:'+query.FieldByName ('ABS_LAND').AsString+ '-' + query.FieldByName ('ABS_PLZ').AsString+ ' ' + query.FieldByName ('ABS_ORT').AsString;;

            paramstr [3] := 'EMPFAENGER:'+query.FieldByName ('ADR_ZUSATZ').AsString;
            paramstr [4] := 'EMPF_STR:'+query.FieldByName ('STRASSE').AsString;

            if (query.FieldByName ('LAND').IsNull) then
              paramstr [5] := 'EMPF_ORT:'+query.FieldByName ('PLZ').AsString+ ' ' + query.FieldByName ('ORT').AsString
            else if (query.FieldByName ('ABS_LAND').AsString <> query.FieldByName ('LAND').AsString) then
              paramstr [5] := 'EMPF_ORT:'+query.FieldByName ('LAND').AsString+ '-' + query.FieldByName ('PLZ').AsString+ ' ' + query.FieldByName ('ORT').AsString;

            if query.FieldByName ('RETOUREN_NR').IsNull then
              paramstr [6] := 'BARCODE:'
            else
              paramstr [6] := 'BARCODE:'+'401'+query.FieldByName ('RETOUREN_NR').AsString;

            res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

            EndPrinting;
          end;

          ClosePrinterPort;
        end;
      end;
    except
      res := -9;
      ErrText := FormatMessageText (1150, []);
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintRetourenLabel (const PrtDaten : TPrinterPorts; const RefAuf : Integer; var ErrText : String) : Integer;
var
  res       : Integer;
  forminfo  : TFormInfos;
  paramstr  : array [0..31] of AnsiString;
  fname     : String;
  query     : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select a.REF_MAND, a.REF_LAGER, m.ADR_ZUSATZ, m.STRASSE, m.LAND, m.PLZ, m.ORT');
    query.SQL.Add (',adr.NAME1 as ABS_NAME, adr.STRASSE as ABS_STRASSE, adr.LAND as ABS_LAND, adr.PLZ as ABS_PLZ, adr.ORT as ABS_ORT');
    query.SQL.Add (',ret.RETOUREN_NR');
    query.SQL.Add ('from V_AUFTRAG a inner join V_MANDANT m on (m.REF=nvl (a.REF_SUB_MAND, a.REF_MAND)) inner join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR) left outer join V_RETOUREN_NR ret on (ret.REF_AUF_KOPF=a.REF) where a.REF=:RefAuf');
    query.Parameters.ParamByName('RefAuf').Value := RefAuf;

    try
      query.Open;

      res := DetectFormular (query.FieldByName ('REF_MAND').AsInteger, query.FieldByName ('REF_LAGER').AsInteger, '', '', PrtDaten.Model, 'RETOUREN_ADR', forminfo);
      if (res <> 0) Then
        ErrText := FormatMessageText (1320, [PrtDaten.Model, 'RETOUREN_ADR'])
      else begin
        res := OpenPrinterPort (PrtDaten.Port, PrtDaten.Model, PrtDaten.FileOutput, PrtDaten.User, PrtDaten.Passwd, fname, ErrText);

        if (res = 0) then begin
          res := BeginPrinting ('RETOUREN_ADR',ErrText);

          if (res = 0) then begin
            paramstr [0] := 'ABS_NAME:'+query.FieldByName ('ABS_NAME').AsString;
            paramstr [1] := 'ABS_STRASSE:'+query.FieldByName ('ABS_STRASSE').AsString;

            if (query.FieldByName ('ABS_LAND').IsNull) then
              paramstr [2] := 'ABS_ORT:'+query.FieldByName ('ABS_PLZ').AsString+ ' ' + query.FieldByName ('ABS_ORT').AsString
            else
              paramstr [2] := 'ABS_ORT:'+query.FieldByName ('ABS_LAND').AsString+ '-' + query.FieldByName ('ABS_PLZ').AsString+ ' ' + query.FieldByName ('ABS_ORT').AsString;;

            paramstr [3] := 'EMPFAENGER:'+query.FieldByName ('ADR_ZUSATZ').AsString;
            paramstr [4] := 'EMPF_STR:'+query.FieldByName ('STRASSE').AsString;

            if (query.FieldByName ('LAND').IsNull) then
              paramstr [5] := 'EMPF_ORT:'+query.FieldByName ('PLZ').AsString+ ' ' + query.FieldByName ('ORT').AsString
            else if (query.FieldByName ('ABS_LAND').AsString <> query.FieldByName ('LAND').AsString) then
              paramstr [5] := 'EMPF_ORT:'+query.FieldByName ('LAND').AsString+ '-' + query.FieldByName ('PLZ').AsString+ ' ' + query.FieldByName ('ORT').AsString;

            if query.FieldByName ('RETOUREN_NR').IsNull then
              paramstr [6] := 'BARCODE:'
            else
              paramstr [6] := 'BARCODE:'+'401'+query.FieldByName ('RETOUREN_NR').AsString;

            res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

            EndPrinting;
          end;

          ClosePrinterPort;
        end;
      end;
    except
      res := -9;
      ErrText := FormatMessageText (1150, []);
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintZusatzLabel (const PrtDaten : TPrinterPorts; const LableName : String; const RefAuf : Integer; var ErrText : String) : Integer;
var
  res       : Integer;
  forminfo  : TFormInfos;
  paramstr  : array [0..31] of AnsiString;
  fname     : String;
  query     : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ( 'select a.REF_MAND, a.REF_LAGER, m.ADR_ZUSATZ, m.STRASSE, m.LAND, m.PLZ, m.ORT,a.AUFTRAG_NR,a.AUF_REFERENZ,a.KD_BESTELL_NR,a.LIEFERSCHEIN_NR'
                   +',adr.NAME1 as ABS_NAME, adr.STRASSE as ABS_STRASSE, adr.LAND as ABS_LAND, adr.PLZ as ABS_PLZ, adr.ORT as ABS_ORT,ret.RETOUREN_NR'
                   +' from V_AUFTRAG a inner join V_MANDANT m on (m.REF=nvl (a.REF_SUB_MAND, a.REF_MAND)) inner join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR) left outer join V_RETOUREN_NR ret on (ret.REF_AUF_KOPF=a.REF) where a.REF=:RefAuf');
    query.Parameters.ParamByName('RefAuf').Value := RefAuf;

    try
      query.Open;

      res := DetectFormular (query.FieldByName ('REF_MAND').AsInteger, query.FieldByName ('REF_LAGER').AsInteger, '', '', PrtDaten.Model, LableName, forminfo);
      if (res <> 0) Then
        ErrText := FormatMessageText (1320, [PrtDaten.Model, 'RETOUREN_ADR'])
      else begin
        res := OpenPrinterPort (PrtDaten.Port, PrtDaten.Model, PrtDaten.FileOutput, PrtDaten.User, PrtDaten.Passwd, fname, ErrText);

        if (res = 0) then begin
          res := BeginPrinting (LableName, ErrText);

          if (res = 0) then begin
            paramstr [0] := 'AUFTRAG_NR:'+query.FieldByName ('AUFTRAG_NR').AsString;
            paramstr [1] := 'AUF_REFERENZ:'+query.FieldByName ('AUF_REFERENZ').AsString;
            paramstr [2] := 'BESTELL_NR:'+query.FieldByName ('KD_BESTELL_NR').AsString;
            paramstr [3] := 'LIEFERSCHEIN_NR:'+query.FieldByName ('LIEFERSCHEIN_NR').AsString;
            paramstr [4] := 'KD_KOMM_NR:'+query.FieldByName ('ABS_STRASSE').AsString;
            paramstr [5] := 'ABS_NAME:'+query.FieldByName ('ABS_NAME').AsString;
            paramstr [6] := 'ABS_STRASSE:'+query.FieldByName ('ABS_STRASSE').AsString;

            if (query.FieldByName ('ABS_LAND').IsNull) then
              paramstr [7] := 'ABS_ORT:'+query.FieldByName ('ABS_PLZ').AsString+ ' ' + query.FieldByName ('ABS_ORT').AsString
            else
              paramstr [7] := 'ABS_ORT:'+query.FieldByName ('ABS_LAND').AsString+ '-' + query.FieldByName ('ABS_PLZ').AsString+ ' ' + query.FieldByName ('ABS_ORT').AsString;;

            paramstr [8] := 'EMPFAENGER:'+query.FieldByName ('ADR_ZUSATZ').AsString;
            paramstr [8] := 'EMPF_STR:'+query.FieldByName ('STRASSE').AsString;

            if (query.FieldByName ('LAND').IsNull) then
              paramstr [9] := 'EMPF_ORT:'+query.FieldByName ('PLZ').AsString+ ' ' + query.FieldByName ('ORT').AsString
            else if (query.FieldByName ('ABS_LAND').AsString <> query.FieldByName ('LAND').AsString) then
              paramstr [9] := 'EMPF_ORT:'+query.FieldByName ('LAND').AsString+ '-' + query.FieldByName ('PLZ').AsString+ ' ' + query.FieldByName ('ORT').AsString;

            if query.FieldByName ('RETOUREN_NR').IsNull then
              paramstr [10] := 'BARCODE:'
            else
              paramstr [10] := 'BARCODE:'+'401'+query.FieldByName ('RETOUREN_NR').AsString;

            res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

            EndPrinting;
          end;
        end;

        ClosePrinterPort;
      end;
    except
      res := -9;
      ErrText := FormatMessageText (1150, []);
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintNVEZusatzLabel   (const PrtDaten : TPrinterPorts; const LableName : String; const RefNVE : Integer; var ErrText : String) : Integer;
var
  res       : Integer;
  forminfo  : TFormInfos;
  paramstr  : array [0..31] of AnsiString;
  fname     : String;
  query     : TADOQuery;
  cfgquery  : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ( 'select nvl (a.REF_SUB_MAND, a.REF_MAND) as REF_MAND,a.REF_LAGER,a.REF as REF_AUF_KOPF,a.AUFTRAG_NR,a.AUF_REFERENZ,a.KD_BESTELL_NR,a.LIEFERSCHEIN_NR'
                   +',adr.NAME1 as EMPF_NAME, adr.STRASSE as EMPF_STRASSE, adr.LAND as EMPF_LAND, adr.PLZ as EMPF_PLZ, adr.ORT as EMPF_ORT,ret.RETOUREN_NR,a.KD_KOMM_NR'
                   +',nve.NVE_NR,nve.NETTO_GEWICHT,nve.BRUTTO_GEWICHT,nve.GESAMT_VPE,nve.PACKAGE_NR,at.VERSAND_HINWEIS,at.LIEFER_TEXT'
                   +',av.SHIPPING_UNITS,(select count (REF) from V_NVE_01 where STATUS<>''DEL'' and REF_MASTER_NVE is null and REF_AUF_KOPF=nve.REF_AUF_KOPF) as NVE_COUNT'
                   +' from'
                   +' V_NVE_01 nve'
                   +' inner join V_AUFTRAG a on (a.REF=nve.REF_AUF_KOPF)'
                   +' inner join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=nve.REF_AUF_KOPF)'
                   +' inner join V_AUFTRAG_TEXTE at on (at.REF_AUF_KOPF=nve.REF_AUF_KOPF)'
                   +' inner join V_MANDANT m on (m.REF=nvl (a.REF_SUB_MAND, a.REF_MAND))'
                   +' inner join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR)'
                   +' left outer join V_RETOUREN_NR ret on (ret.REF_AUF_KOPF=a.REF)'
                   +' where nve.REF=:RefNVE');
    query.Parameters.ParamByName('RefNVE').Value := RefNVE;

    try
      query.Open;

      res := DetectFormular (query.FieldByName ('REF_MAND').AsInteger, query.FieldByName ('REF_LAGER').AsInteger, '', '', PrtDaten.Model, LableName, forminfo);
      if (res <> 0) Then
        ErrText := FormatMessageText (1320, [PrtDaten.Model, LableName])
      else begin
        res := OpenPrinterPort (PrtDaten.Port, PrtDaten.Model, PrtDaten.FileOutput, PrtDaten.User, PrtDaten.Passwd, fname, ErrText);

        if (res = 0) then begin
          res := BeginPrinting (LableName, ErrText);

          if (res = 0) then begin
            paramstr [0] := 'AUFTRAG_NR:'+query.FieldByName ('AUFTRAG_NR').AsString;
            paramstr [1] := 'AUF_REFERENZ:'+query.FieldByName ('AUF_REFERENZ').AsString;
            paramstr [2] := 'BESTELL_NR:'+query.FieldByName ('KD_BESTELL_NR').AsString;
            paramstr [3] := 'LIEFERSCHEIN_NR:'+query.FieldByName ('LIEFERSCHEIN_NR').AsString;
            paramstr [4] := 'KD_KOMM_NR:'+query.FieldByName ('KD_KOMM_NR').AsString;

            //Absender Adresse übergeben
            cfgquery  := TADOQuery.Create (Nil);

            try
              cfgquery.LockType := ltReadOnly;
              cfgquery.Connection := LVSDatenModul.MainADOConnection;

              //Bei Dropship wird die Traderadresse angezeigt
              cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                                 +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                                 +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                                 +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                                 +'coalesce (translate (tadr.LAND using CHAR_CS),loc.LAND,''DE'') as LAND,'
                                 +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON'
                                +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                                +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                                +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                                +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                +' where auf.REF=:ref_auf'
                                );

              cfgquery.Parameters.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

              cfgquery.Open;

              if not (cfgquery.FieldByName('REF').IsNull) then begin
                paramstr [5] := 'ABS_NAME:'+cfgquery.FieldByName ('NAME1').AsString;
                paramstr [6] := 'ABS_STRASSE:'+cfgquery.FieldByName ('STRASSE').AsString;

                if (cfgquery.FieldByName ('LAND').IsNull) then
                  paramstr [7] := 'ABS_ORT:'+cfgquery.FieldByName ('PLZ').AsString+ ' ' + cfgquery.FieldByName ('ORT').AsString
                else
                  paramstr [7] := 'ABS_ORT:'+cfgquery.FieldByName ('LAND').AsString+ '-' + cfgquery.FieldByName ('PLZ').AsString+ ' ' + cfgquery.FieldByName ('ORT').AsString;;
              end else begin
                //Ansonsten die vom Lager
                cfgquery.Close;

                cfgquery.SQL.Clear;

                cfgquery.SQL.Add ( 'select coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as NAME1,ml.ABSENDER_ZUSATZ,nvl (ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE'
                                  +',nvl (ml.ABSENDER_PLZ,loc.PLZ) as PLZ,nvl (ml.ABSENDER_ORT,loc.ORT) as ORT,nvl (loc.LAND,''DE'') as LAND,loc.TELEFON'
                                  +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                  +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                  +' where m.REF=:ref_mand'
                                  );

                cfgquery.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

                if query.FieldByName('REF_SUB_MAND').IsNull then
                  cfgquery.Parameters.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
                else cfgquery.Parameters.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

                cfgquery.Open;

                paramstr [5] := 'ABS_NAME:'+cfgquery.FieldByName ('NAME1').AsString;
                paramstr [6] := 'ABS_STRASSE:'+cfgquery.FieldByName ('STRASSE').AsString;

                if (cfgquery.FieldByName ('LAND').IsNull) then
                  paramstr [7] := 'ABS_ORT:'+cfgquery.FieldByName ('PLZ').AsString+ ' ' + cfgquery.FieldByName ('ORT').AsString
                else
                  paramstr [7] := 'ABS_ORT:'+cfgquery.FieldByName ('LAND').AsString+ '-' + cfgquery.FieldByName ('PLZ').AsString+ ' ' + cfgquery.FieldByName ('ORT').AsString;;
              end;

              cfgquery.Close;
            finally
              cfgquery.Free;
            end;

            paramstr [8] := 'EMPFAENGER:'+query.FieldByName ('EMPF_NAME').AsString;
            paramstr [9] := 'EMPF_STR:'+query.FieldByName ('STRASSE').AsString;

            if (query.FieldByName ('LAND').IsNull) then
              paramstr [10] := 'EMPF_ORT:'+query.FieldByName ('PLZ').AsString+ ' ' + query.FieldByName ('ORT').AsString
            else if (query.FieldByName ('ABS_LAND').AsString <> query.FieldByName ('LAND').AsString) then
              paramstr [10] := 'EMPF_ORT:'+query.FieldByName ('LAND').AsString+ '-' + query.FieldByName ('PLZ').AsString+ ' ' + query.FieldByName ('ORT').AsString;

            if query.FieldByName ('RETOUREN_NR').IsNull then
              paramstr [11] := 'BARCODE:'
            else
              paramstr [11] := 'BARCODE:'+'401'+query.FieldByName ('RETOUREN_NR').AsString;

            paramstr [12] := 'ANZ_VPE:'+query.FieldByName ('GESAMT_VPE').AsString;
            paramstr [13] := 'PACKAGE_NR:'+query.FieldByName ('PACKAGE_NR').AsString;

            if not (query.FieldByName ('SHIPPING_UNITS').IsNUll) then
              paramstr [14] := 'ANZ_PACKAGE:'+query.FieldByName ('SHIPPING_UNITS').AsString
            else
              paramstr [14] := 'ANZ_PACKAGE:'+query.FieldByName ('NVE_COUNT').AsString;

            paramstr [15] := 'NVE_NR:'+query.FieldByName ('NVE_NR').AsString;
            paramstr [16] := 'NETTO_GEWICHT:'+Format ('%8.2f kg', [query.FieldByName('NETTO_GEWICHT').AsInteger/1000]);
            paramstr [17] := 'BRUTTO_GEWICHT:'+Format ('%8.2f kg', [query.FieldByName('BRUTTO_GEWICHT').AsInteger/1000]);
            paramstr [18] := 'LIEFER_TEXT:'+query.FieldByName ('LIEFER_TEXT').AsString;
            paramstr [19] := 'VERSAND_HINWEIS:'+query.FieldByName ('VERSAND_HINWEIS').AsString;

            res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

            EndPrinting;
          end;
        end;

        ClosePrinterPort;
      end;
    except
      res := -9;
      ErrText := FormatMessageText (1150, []);
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.03.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintRetourenPosLabel (const RefRetPos : Integer; var ErrorMsg : String) : Integer; overload;
var
  prtinfo    : TPrinterPorts;
begin
  prtinfo.Port := '';

  Result := PrintRetourenPosLabel (prtinfo, RefRetPos, ErrorMsg);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintRetourenPosLabel (const PrtDaten : TPrinterPorts; const RefRetPos : Integer; var ErrorMsg : String) : Integer;
var
  res        : Integer;
  fname,
  lestr,
  eanstr,
  barstr,
  eancode,
  errtext,
  namestr,
  bartextstr : String;
  checkch    : AnsiChar;
  prtinfo    : TPrinterPorts;
  forminfo   : TFormInfos;
  query      : TADOQuery;
  paramarray : array [0..50] of AnsiString;
  bardata    : TEANBarcode;
begin
  res := 0;

  if (Length (PrtDaten.Port) > 0) then
    prtinfo := PrtDaten
  else if (PrintModule.VPELabelPrinter.Ref = -1) and (Length (PrintModule.VPELabelPrinter.Name) = 0) then begin
    res := -1;
    ErrorMsg := FormatMessageText (1417, []);
  end else begin
    if (PrintModule.VPELabelPrinter.Ref > 0) then
      res := PrintModule.LoadPrinter (-1, PrintModule.VPELabelPrinter.Ref, PrintModule.VPELabelPrinter.Name, prtinfo)
    else begin
      res := 0;

      prtinfo.Model := 'ZPL';
      prtinfo.Port  := PrintModule.VPELabelPrinter.Port;
    end;
  end;

  if (res <> 0) then
    ErrorMsg := FormatMessageText (1418, [PrintModule.VPELabelPrinter.Name])
  else begin
    query := TADOQuery.Create (Nil);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select wp.*,r.ORIG_AUFTRAG_NR,r.RETOUREN_NR,r.KUNDEN_NR,nvl (cat.LABEL_TYPE, ''RET_POS_LABEL'') as LABEL_TYPE,ae.EAN,ar.PRODUCER,ae.OPT_IS_COLLI,ae.COLLI_NR,sku.ANZAHL_PACKEINHEIT,ae.OPT_GEWICHT,l.ID_IFC'+
                     ',cat.NAME as BES_CATEGORY,cat.DEFINITION as BES_CATEGORY_DEF,av.RETOUREN_AVIS_NR,l.NAME as LAGER'+
                     ',(select ANNAHME_INFO from VQ_WE_POS where REF=wp.REF) as ANNAHME_INFO'+
                     ',(select LP_NR from V_KOMM_LP_ZUORDNUNG where REF_LAGER=we.REF_LAGER and REF_AR=wp.REF_AR and REF_EINHEIT=ae.REF_EINHEIT and ROWNUM=1) as KOMM_LP_NR'+
                     ',(select lp.LP_DISP from V_KOMM_LP_ZUORDNUNG lz inner join V_LP lp on (lp.REF=lz.REF_LP) where lz.REF_LAGER=we.REF_LAGER and lz.REF_AR=wp.REF_AR and lz.REF_EINHEIT=ae.REF_EINHEIT and ROWNUM=1) as KOMM_LP_DISP'+
                     ' from V_WE_RET_POS wp'+
                     ' inner join V_WARENEINGANG we on (we.REF=wp.REF_WE)'+
                     ' inner join V_LAGER l on (l.REF=we.REF_LAGER)'+
                     ' inner join V_RETOUREN r on (r.REF=we.REF_RETOURE)'+
                     ' inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=wp.REF_AR_EINHEIT)'+
                     ' inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'+
                     ' left outer join V_RETOUREN_AVIS_POS avp on (avp.REF=wp.REF_RET_AVIS_POS)'+
                     ' left outer join V_RETOUREN_AVIS av on (av.REF=avp.REF_RETOUREN_AVIS)'+
                     ' left outer join VQ_ARTIKEL_EINHEIT sku on (ae.OPT_IS_COLLI =''1'' and sku.REF=(select REF_MASTER_AR_EINHEIT from V_ARTIKEL_EINHEIT_COLLI where REF_SET_AR_EINHEIT=ae.REF))'+
                     ' left outer join V_LAGER_BESTAND_CATEGORY cat on (cat.REF=wp.REF_BESTAND_CATEGORY) where wp.REF=:RefRetPos');
      query.Parameters.ParamByName('RefRetPos').Value := RefRetPos;

      try
        query.Open;

        if not (query.FieldByName ('LABEL_TYPE').IsNull) then begin
          with prtinfo do
            res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);

          if (res <> 0) then
            ErrorMsg := FormatMessageText (1360, [errtext])
          else begin
            res := BeginPrinting('RET-Label',errtext);

            if (res <> 0) then
              ErrorMsg := FormatMessageText (1360, [errtext])
            else begin
              namestr := 'VE-'+query.FieldByName ('LABEL_TYPE').AsString+'-RET';

              with prtinfo do
                res := DetectFormular (query.FieldByName ('REF_MAND').AsInteger, query.FieldByName ('REF_LAGER').AsInteger, '', '', Model, namestr, forminfo);

              if (res <> 0) then begin
                ErrorMsg := FormatMessageText (1320, [prtinfo.Model, namestr]);
              end else begin
                lestr      := '';
                checkch    := #0;
                barstr     := '';
                bartextstr := '';

                if not (query.FieldByName('WE_LE_NR').IsNull) then begin
                  lestr := FormatStr (query.FieldByName('WE_LE_NR').AsString,-9,'0');
                  checkch := GetLELPCheckChar ('1'+lestr);
                end;

                if (query.FieldByName('MHD').IsNull) then
                  bardata.MHD := ''
                else
                  bardata.MHD := query.FieldByName('MHD').AsString;

                if Assigned (query.FindField ('HERSTELL_DATUM')) then begin
                  if (query.FieldByName('HERSTELL_DATUM').IsNull) then
                    bardata.HerstellungDatum := ''
                  else
                    bardata.HerstellungDatum := query.FieldByName('HERSTELL_DATUM').AsString;
                end;

                if (query.FieldByName('CHARGE').IsNull) then
                  bardata.Charge := ''
                else
                  bardata.Charge := query.FieldByName('CHARGE').AsString;

                bardata.EAN := '';

                bardata.InhaltEAN := query.FieldByName('EAN').AsString;
                bardata.Einheiten := query.FieldByName('MENGE').AsInteger;

                if (query.FieldByName('OPT_GEWICHT').AsString = '1') then
                  bardata.NettoGewicht := Round (query.FieldByName('GEWICHT').AsFloat * 1000)
                else
                  bardata.NettoGewicht := 0;

                BuildEAN128Barcode (bardata, barstr, bartextstr);

                eancode := query.FieldByName ('EAN').AsString;

                //Prüfziffer entfernen
                eanstr := copy (eancode,1,Length (eancode) - 1);

                if (Length (eanstr) < 13) then begin
                  while (Length (eanstr) < 12) do eanstr := '0' + eanstr;
                end;

                eanstr := eanstr + GetEANCheckDigit (eanstr);

                paramarray [2] := 'AR_NR:'+query.FieldByName ('ARTIKEL_NR').AsString;
                paramarray [3] := 'AR_MNF_NR:'+query.FieldByName ('ARTIKEL_NR_HERSTELLER').AsString;
                paramarray [4] := 'AR_TEXT:'+query.FieldByName ('ARTIKEL_TEXT').AsString;
                paramarray [5] := 'AR_TEXT_INFO:';
                ParamArray [6] := 'AR_COLLI_NAME:'+query.FieldByName ('COLLI_NAME').AsString;

                if (query.FieldByName ('OPT_IS_COLLI').AsString = '1') then
                  ParamArray [7] := 'AR_COLLI_NR:'+query.FieldByName ('COLLI_NR').AsString+'/'+query.FieldByName ('ANZAHL_PACKEINHEIT').AsString
                else
                  ParamArray [7] := 'AR_COLLI_NR:1/1';

                if (query.FieldByName('GEWICHT').IsNull) then
                  paramarray [8] := 'NETTO_GEWICHT:'
                else
                  paramarray [8] := 'NETTO_GEWICHT:'+Format ('%8.2f kg', [query.FieldByName('GEWICHT').AsFloat]);

                paramarray [9] := 'MENGE:'+query.FieldByName('MENGE').AsString;
                paramarray [10] := 'EINHEIT:'+query.FieldByName('EINHEIT').AsString;
                paramarray [11] := 'AR_COLOR:';
                paramarray [12] := 'AR_SIZE:';
                paramarray [13] := 'AR_EAN:'+eanstr;
                paramarray [14] := 'MHD:'+query.FieldByName ('MHD').AsString;
                paramarray [15] := 'MHD_KURZ:'+copy (bardata.MHD,1,6)+copy (bardata.MHD,9,2);
                paramarray [16] := 'CHARGE:'+query.FieldByName ('CHARGE').AsString;
                paramarray [18] := 'MANUFACTURER:'+query.FieldByName ('PRODUCER').AsString;
                paramarray [19] := 'RETOUREN_NR:'+query.FieldByName ('RETOUREN_NR').AsString;
                paramarray [20] := 'KUNDEN_NR:'+query.FieldByName ('KUNDEN_NR').AsString;
                paramarray [21] := 'AUFTRAG_NR:'+query.FieldByName ('ORIG_AUFTRAG_NR').AsString;
                paramarray [22] := 'ZUSTAND:'+query.FieldByName ('RET_ZUSTAND_CAT').AsString;
                paramarray [23] := 'RET_GRUND:'+query.FieldByName ('RET_GRUND').AsString;
                paramarray [24] := 'QS_ZUSTAND:'+query.FieldByName ('RET_ZUSTAND').AsString;
                paramarray [25] := 'BES_CATEGORY:'+query.FieldByName ('BES_CATEGORY').AsString;
                paramarray [26] := 'BES_CATEGORY_DEF:'+query.FieldByName ('BES_CATEGORY_DEF').AsString;
                paramarray [27] := 'REF_BARCODE:'+GetRefBarcode ('1', RefRetPos);
                paramarray [28] := 'LE_NR:'+lestr;
                paramarray [29] := 'BARCODE_EAN:'+query.FieldByName('EAN').AsString;
                paramarray [30] := 'BARCODE:'+barstr;
                paramarray [31] := 'BARCODE_TEXT:'+bartextstr;
                paramarray [32] := 'WOCHE:';

                paramarray [33] := 'ID_BARCODE:';
                paramarray [34] := 'BESTAND_ID:';
                if Assigned (query.FindField ('BESTAND_ID')) then begin
                  paramarray [33] := paramarray [33]+GetIDBarcode ('4', query.FieldByName('BESTAND_ID').AsString);
                  paramarray [34] := paramarray [34]+query.FieldByName('BESTAND_ID').AsString;
                end;

                paramarray [35] := 'SERIAL:';
                if Assigned (query.FindField ('SERIAL_NR')) then
                  paramarray [35] := paramarray [35]+query.FieldByName('SERIAL_NR').AsString;

                if (Length (lestr) = 0) then begin
                  paramarray [36] := 'BARCODE_LE:';
                  paramarray [37] := 'BARCODE_LE_TEXT:';
                end else begin
                  paramarray [36] := 'BARCODE_LE:' + '1'+lestr+checkch;
                  paramarray [37] := 'BARCODE_LE_TEXT:' + '1 '+lestr+' '+checkch;
                end;

                paramarray [38] := 'KOMM_LP_NR:'+ query.FieldByName('KOMM_LP_NR').AsString;
                paramarray [39] := 'KOMM_LP_DISP:'+ query.FieldByName('KOMM_LP_DISP').AsString;
                paramarray [40] := 'PLAN_LP_NR:';
                paramarray [41] := 'JAHR:';
                paramarray [42] := 'QS_HINWEIS:'+query.FieldByName ('QS_HINWEIS').AsString;
                paramarray [43] := 'RETOUREN_AVIS_NR:'+query.FieldByName ('RETOUREN_AVIS_NR').AsString;

                paramarray [44] := 'LAGER:'+query.FieldByName('LAGER').AsString;

                if (query.FieldByName ('MENGE').IsNull) then
                  paramarray [45] := 'KOPIEN:'+GetCopySequenz (1 + forminfo.Kopien)
                else if (query.FieldByName ('MENGE').AsInteger < 20) then
                  paramarray [45] := 'KOPIEN:'+GetCopySequenz (query.FieldByName ('MENGE').AsInteger * (1 + forminfo.Kopien))
                else
                  paramarray [45] := 'KOPIEN:'+GetCopySequenz (20 * (1 + forminfo.Kopien));

                paramarray[46] := 'LAGER_ID_IFC:'+query.FieldByName('ID_IFC').AsString;

                namestr := 'VE-'+query.FieldByName ('LABEL_TYPE').AsString+'-RET';

                res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramarray, ErrorMsg);
              end;

              EndPrinting;
            end;

            ClosePrinterPort;
          end;
        end;

        query.Close;
      except
        res := -9;
        ErrorMsg := FormatMessageText (1150, []);
      end;
    finally
      query.Free;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.12.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintRetourenBestandLabel (const PrtDaten : TPrinterPorts; const RefBestand : Integer; var ErrorMsg : String) : Integer;
var
  res        : Integer;
  fname,
  lestr,
  eanstr,
  barstr,
  eancode,
  errtext,
  namestr,
  bartextstr : String;
  prtinfo    : TPrinterPorts;
  checkch    : AnsiChar;
  forminfo   : TFormInfos;
  query      : TADOQuery;
  paramarray : array [0..50] of AnsiString;
  bardata    : TEANBarcode;
begin
  res := 0;

  if (Length (PrtDaten.Port) > 0) then
    prtinfo := PrtDaten
  else if (PrintModule.VPELabelPrinter.Ref = -1) and (Length (PrintModule.VPELabelPrinter.Name) = 0) then begin
    res := -1;
    ErrorMsg := FormatMessageText (1417, []);
  end else begin
    if (PrintModule.VPELabelPrinter.Ref > 0) then
      res := PrintModule.LoadPrinter (-1, PrintModule.VPELabelPrinter.Ref, PrintModule.VPELabelPrinter.Name, prtinfo)
    else begin
      res := 0;

      prtinfo.Model := 'ZPL';
      prtinfo.Port  := PrintModule.VPELabelPrinter.Port;
    end;
  end;

  if (res <> 0) then
    ErrorMsg := FormatMessageText (1418, [PrintModule.VPELabelPrinter.Name])
  else begin
    query := TADOQuery.Create (Nil);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select bes.*,nvl (bes.MENGE_FREI,0)+nvl (bes.MENGE_SPERR,0) as MENGE, r.ORIG_AUFTRAG_NR,r.RETOUREN_NR,r.KUNDEN_NR,nvl (cat.LABEL_TYPE, ''RET_POS_LABEL'') as LABEL_TYPE'+
                     ',ae.EAN,ar.PRODUCER,ae.OPT_IS_COLLI,ae.COLLI_NR,sku.ANZAHL_PACKEINHEIT,ae.OPT_GEWICHT,nvl (wp.WE_LE_NR,bes.LE_NR) as WE_LE_NR,ar.ARTIKEL_NR_HERSTELLER,ae.COLLI_NAME'+
                     ',wp.RET_ZUSTAND,wp.RET_ZUSTAND_CAT,wp.RET_GRUND,wp.QS_HINWEIS'+
                     ',cat.NAME as BES_CATEGORY,cat.DEFINITION as BES_CATEGORY_DEF'+
                     ',(select ANNAHME_INFO from VQ_WE_POS where REF=wp.REF) as ANNAHME_INFO'+
                     ',(select LP_NR from V_KOMM_LP_ZUORDNUNG where REF_LAGER=bes.REF_LAGER and REF_AR=ae.REF_AR and REF_EINHEIT=ae.REF_EINHEIT and ROWNUM=1) as KOMM_LP_NR'+
                     ',(select lp.LP_DISP from V_KOMM_LP_ZUORDNUNG lz inner join V_LP lp on (lp.REF=lz.REF_LP) where lz.REF_LAGER=bes.REF_LAGER and lz.REF_AR=ae.REF_AR and lz.REF_EINHEIT=ae.REF_EINHEIT and ROWNUM=1) as KOMM_LP_DISP'+
                     ' from V_BES bes'+
                     ' inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=bes.REF_AR_EINHEIT)'+
                     ' inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'+
                     ' left outer join V_WE_RET_POS wp on (wp.REF=bes.REF_WE_POS)'+
                     ' left outer join V_RETOUREN r on (r.REF=(select REF_RETOURE from VQ_WARENEINGANG where REF=wp.REF_WE))'+
                     ' left outer join VQ_ARTIKEL_EINHEIT sku on (ae.OPT_IS_COLLI =''1'' and sku.REF=(select REF_MASTER_AR_EINHEIT from V_ARTIKEL_EINHEIT_COLLI where REF_SET_AR_EINHEIT=ae.REF))'+
                     ' left outer join V_LAGER_BESTAND_CATEGORY cat on ((wp.REF is not null and cat.REF=wp.REF_BESTAND_CATEGORY) or (wp.REF is null and cat.REF=bes.REF_CATEGORY)) where bes.REF=:RefBestand');
      query.Parameters.ParamByName('RefBestand').Value := RefBestand;

      try
        query.Open;

        if not (query.FieldByName ('LABEL_TYPE').IsNull) then begin
          with prtinfo do
            res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);

          if (res <> 0) then
            ErrorMsg := FormatMessageText (1360, [errtext])
          else begin
            res := BeginPrinting('RET-Label',errtext);

            if (res <> 0) then
              ErrorMsg := FormatMessageText (1360, [errtext])
            else begin
              namestr := 'VE-'+query.FieldByName ('LABEL_TYPE').AsString+'-RET';

              with prtinfo do
                res := DetectFormular (query.FieldByName ('REF_MAND').AsInteger, query.FieldByName ('REF_LAGER').AsInteger, '', '', Model, namestr, forminfo);

              if (res <> 0) then begin
                ErrorMsg := FormatMessageText (1320, [prtinfo.Model, namestr]);
              end else begin
                lestr      := '';
                checkch    := #0;
                barstr     := '';
                bartextstr := '';

                if not (query.FieldByName('WE_LE_NR').IsNull) then begin
                  lestr := FormatStr (query.FieldByName('WE_LE_NR').AsString,-9,'0');
                  checkch := GetLELPCheckChar ('1'+lestr);
                end;

                if (query.FieldByName('MHD').IsNull) then
                  bardata.MHD := ''
                else
                  bardata.MHD := query.FieldByName('MHD').AsString;

                if Assigned (query.FindField ('HERSTELL_DATUM')) then begin
                  if (query.FieldByName('HERSTELL_DATUM').IsNull) then
                    bardata.HerstellungDatum := ''
                  else
                    bardata.HerstellungDatum := query.FieldByName('HERSTELL_DATUM').AsString;
                end;

                if (query.FieldByName('CHARGE').IsNull) then
                  bardata.Charge := ''
                else
                  bardata.Charge := query.FieldByName('CHARGE').AsString;

                bardata.EAN := '';

                bardata.InhaltEAN := query.FieldByName('EAN').AsString;
                bardata.Einheiten := query.FieldByName('MENGE').AsInteger;

                if (query.FieldByName('OPT_GEWICHT').AsString = '1') then
                  bardata.NettoGewicht := Round (query.FieldByName('GEWICHT').AsFloat * 1000)
                else
                  bardata.NettoGewicht := 0;

                BuildEAN128Barcode (bardata, barstr, bartextstr);

                eancode := query.FieldByName ('EAN').AsString;

                //Prüfziffer entfernen
                eanstr := copy (eancode,1,Length (eancode) - 1);

                if (Length (eanstr) < 13) then begin
                  while (Length (eanstr) < 12) do eanstr := '0' + eanstr;
                end;

                eanstr := eanstr + GetEANCheckDigit (eanstr);

                paramarray [2] := 'AR_NR:'+query.FieldByName ('ARTIKEL_NR').AsString;
                paramarray [3] := 'AR_MNF_NR:'+query.FieldByName ('ARTIKEL_NR_HERSTELLER').AsString;
                paramarray [4] := 'AR_TEXT:'+query.FieldByName ('ARTIKEL_TEXT').AsString;
                paramarray [5] := 'AR_TEXT_INFO:';
                ParamArray [6] := 'AR_COLLI_NAME:'+query.FieldByName ('COLLI_NAME').AsString;

                if (query.FieldByName ('OPT_IS_COLLI').AsString = '1') then
                  ParamArray [7] := 'AR_COLLI_NR:'+query.FieldByName ('COLLI_NR').AsString+'/'+query.FieldByName ('ANZAHL_PACKEINHEIT').AsString
                else
                  ParamArray [7] := 'AR_COLLI_NR:1/1';

                if (query.FieldByName('NETTO_GEWICHT').IsNull) then
                  paramarray [8] := 'NETTO_GEWICHT:'
                else
                  paramarray [8] := 'NETTO_GEWICHT:'+Format ('%8.2f kg', [query.FieldByName('NETTO_GEWICHT').AsFloat]);

                paramarray [9] := 'MENGE:'+query.FieldByName('MENGE').AsString;
                paramarray [10] := 'EINHEIT:'+query.FieldByName('EINHEIT').AsString;
                paramarray [11] := 'AR_COLOR:';
                paramarray [12] := 'AR_SIZE:';
                paramarray [13] := 'AR_EAN:'+eanstr;
                paramarray [14] := 'MHD:'+query.FieldByName ('MHD').AsString;
                paramarray [15] := 'MHD_KURZ:'+copy (bardata.MHD,1,6)+copy (bardata.MHD,9,2);
                paramarray [16] := 'CHARGE:'+query.FieldByName ('CHARGE').AsString;
                paramarray [18] := 'MANUFACTURER:'+query.FieldByName ('PRODUCER').AsString;
                paramarray [19] := 'RETOUREN_NR:'+query.FieldByName ('RETOUREN_NR').AsString;
                paramarray [20] := 'KUNDEN_NR:'+query.FieldByName ('KUNDEN_NR').AsString;
                paramarray [21] := 'AUFTRAG_NR:'+query.FieldByName ('ORIG_AUFTRAG_NR').AsString;
                paramarray [22] := 'ZUSTAND:'+query.FieldByName ('RET_ZUSTAND_CAT').AsString;
                paramarray [23] := 'RET_GRUND:'+query.FieldByName ('RET_GRUND').AsString;
                paramarray [24] := 'QS_ZUSTAND:'+query.FieldByName ('RET_ZUSTAND').AsString;
                paramarray [25] := 'BES_CATEGORY:'+query.FieldByName ('BES_CATEGORY').AsString;
                paramarray [26] := 'BES_CATEGORY_DEF:'+query.FieldByName ('BES_CATEGORY_DEF').AsString;
                paramarray [27] := 'REF_BARCODE:'+GetRefBarcode ('2', RefBestand);
                paramarray [28] := 'LE_NR:'+lestr;
                paramarray [29] := 'BARCODE_EAN:'+query.FieldByName('EAN').AsString;
                paramarray [30] := 'BARCODE:'+barstr;
                paramarray [31] := 'BARCODE_TEXT:'+bartextstr;
                paramarray [32] := 'WOCHE:';

                paramarray [33] := 'ID_BARCODE:';
                paramarray [34] := 'BESTAND_ID:';
                if Assigned (query.FindField ('BESTAND_ID')) then begin
                  paramarray [33] := paramarray [33]+GetIDBarcode ('4', query.FieldByName('BESTAND_ID').AsString);
                  paramarray [34] := paramarray [34]+query.FieldByName('BESTAND_ID').AsString;
                end;

                paramarray [35] := 'SERIAL:';
                if Assigned (query.FindField ('SERIAL_NR')) then
                  paramarray [35] := paramarray [35]+query.FieldByName('SERIAL_NR').AsString;

                paramarray [36] := 'BARCODE_LE:';
                paramarray [37] := 'BARCODE_LE_TEXT:';
                if (Length (lestr) > 0) then begin
                  paramarray [36] := paramarray [36] + '1'+lestr+checkch;
                  paramarray [37] := paramarray [37] + '1 '+lestr+' '+checkch;
                end;

                paramarray [38] := 'KOMM_LP_NR:'+ query.FieldByName('KOMM_LP_NR').AsString;
                paramarray [39] := 'KOMM_LP_DISP:'+ query.FieldByName('KOMM_LP_DISP').AsString;
                paramarray [40] := 'PLAN_LP_NR:';
                paramarray [41] := 'JAHR:';
                paramarray [42] := 'QS_HINWEIS:'+query.FieldByName ('QS_HINWEIS').AsString;
                paramarray [43] := 'LAGER:'+query.FieldByName ('LAGER').AsString;

                if (query.FieldByName ('MENGE').IsNull) then
                  paramarray [44] := 'KOPIEN:'+GetCopySequenz (1 + forminfo.Kopien)
                else if (query.FieldByName ('MENGE').AsInteger < 20) then
                  paramarray [44] := 'KOPIEN:'+GetCopySequenz (query.FieldByName ('MENGE').AsInteger * (1 + forminfo.Kopien))
                else
                  paramarray [44] := 'KOPIEN:'+GetCopySequenz (20 * (1 + forminfo.Kopien));

                namestr := 'VE-'+query.FieldByName ('LABEL_TYPE').AsString+'-RET';

                res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramarray, ErrorMsg);
              end;

              EndPrinting;
            end;

            ClosePrinterPort;
          end;
        end;

        query.Close;
      except
        res := -9;
        ErrorMsg := FormatMessageText (1150, []);
      end;
    finally
      query.Free;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 27.04.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :V
//******************************************************************************
function PrintKommBestandLabel (const PrtDaten : TPrinterPorts; const RefAKP : Integer; var ErrorMsg : String) : Integer;
var
  res        : Integer;
  fname,
  lestr,
  eanstr,
  barstr,
  eancode,
  errtext,
  namestr,
  bartextstr : String;
  prtinfo    : TPrinterPorts;
  checkch    : AnsiChar;
  forminfo   : TFormInfos;
  query      : TADOQuery;
  paramarray : array [0..50] of AnsiString;
  bardata    : TEANBarcode;
begin
  res := 0;

  if (Length (PrtDaten.Port) > 0) then
    prtinfo := PrtDaten
  else if (PrintModule.VPELabelPrinter.Ref = -1) and (Length (PrintModule.VPELabelPrinter.Name) = 0) then begin
    res := -1;
    ErrorMsg := FormatMessageText (1417, []);
  end else begin
    if (PrintModule.VPELabelPrinter.Ref > 0) then
      res := PrintModule.LoadPrinter (-1, PrintModule.VPELabelPrinter.Ref, PrintModule.VPELabelPrinter.Name, prtinfo)
    else begin
      res := 0;

      prtinfo.Model := 'ZPL';
      prtinfo.Port  := PrintModule.VPELabelPrinter.Port;
    end;
  end;

  if (res <> 0) then
    ErrorMsg := FormatMessageText (1418, [PrintModule.VPELabelPrinter.Name])
  else begin
    query := TADOQuery.Create (Nil);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select bes.*,bes.MENGE_IST as MENGE'+
                     ',auf.REF_MAND,auf.REF_LAGER,auf.MANDANT,auf.LAGER,auf.AUFTRAG_NR,auf.KUNDEN_NR'+
                     ',nvl (cat.LABEL_TYPE, ''BES_LABEL'') as LABEL_TYPE'+
                     ',ae.EAN,ar.PRODUCER,ae.OPT_IS_COLLI,ae.COLLI_NR,sku.ANZAHL_PACKEINHEIT,ae.OPT_GEWICHT,ar.ARTIKEL_NR_HERSTELLER,ae.COLLI_NAME'+
                     ',cat.NAME as BES_CATEGORY,cat.DEFINITION as BES_CATEGORY_DEF'+
                     ' from V_AUFTRAG_KOMM_POS bes'+
                     ' inner join V_AUFTRAG auf on (auf.REF=bes.REF_AUF_KOPF)'+
                     ' inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=bes.REF_AR_EINHEIT_PICK)'+
                     ' inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'+
                     ' left outer join VQ_ARTIKEL_EINHEIT sku on (ae.OPT_IS_COLLI =''1'' and sku.REF=(select REF_MASTER_AR_EINHEIT from V_ARTIKEL_EINHEIT_COLLI where REF_SET_AR_EINHEIT=ae.REF))'+
                     ' left outer join V_LAGER_BESTAND_CATEGORY cat on (cat.REF=bes.REF_CATEGORY)'+
                     ' where bes.REF=:RefBestand');
      query.Parameters.ParamByName('RefBestand').Value := RefAKP;

      try
        query.Open;

        if not (query.FieldByName ('LABEL_TYPE').IsNull) then begin
          with prtinfo do
            res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);

          if (res <> 0) then
            ErrorMsg := FormatMessageText (1360, [errtext])
          else begin
            res := BeginPrinting('RET-Label',errtext);

            if (res <> 0) then
              ErrorMsg := FormatMessageText (1360, [errtext])
            else begin
              namestr := 'VE-'+query.FieldByName ('LABEL_TYPE').AsString+'-KOMM';

              with prtinfo do
                res := DetectFormular (query.FieldByName ('REF_MAND').AsInteger, query.FieldByName ('REF_LAGER').AsInteger, '', '', Model, namestr, forminfo);

              if (res <> 0) then begin
                ErrorMsg := FormatMessageText (1320, [prtinfo.Model, namestr]);
              end else begin
                lestr      := '';
                checkch    := #0;
                barstr     := '';
                bartextstr := '';

                if (query.FieldByName('MHD').IsNull) then
                  bardata.MHD := ''
                else
                  bardata.MHD := query.FieldByName('MHD').AsString;

                if Assigned (query.FindField ('HERSTELL_DATUM')) then begin
                  if (query.FieldByName('HERSTELL_DATUM').IsNull) then
                    bardata.HerstellungDatum := ''
                  else
                    bardata.HerstellungDatum := query.FieldByName('HERSTELL_DATUM').AsString;
                end;

                if (query.FieldByName('CHARGE').IsNull) then
                  bardata.Charge := ''
                else
                  bardata.Charge := query.FieldByName('CHARGE').AsString;

                bardata.EAN := '';

                bardata.InhaltEAN := query.FieldByName('EAN').AsString;
                bardata.Einheiten := query.FieldByName('MENGE').AsInteger;

                if (query.FieldByName('OPT_GEWICHT').AsString = '1') then
                  bardata.NettoGewicht := Round (query.FieldByName('GEWICHT_IST').AsFloat * 1000)
                else
                  bardata.NettoGewicht := 0;

                BuildEAN128Barcode (bardata, barstr, bartextstr);

                eancode := query.FieldByName ('EAN').AsString;

                //Prüfziffer entfernen
                eanstr := copy (eancode,1,Length (eancode) - 1);

                if (Length (eanstr) < 13) then begin
                  while (Length (eanstr) < 12) do eanstr := '0' + eanstr;
                end;

                eanstr := eanstr + GetEANCheckDigit (eanstr);

                paramarray [2] := 'AR_NR:'+query.FieldByName ('ARTIKEL_NR').AsString;
                paramarray [3] := 'AR_MNF_NR:'+query.FieldByName ('ARTIKEL_NR_HERSTELLER').AsString;
                paramarray [4] := 'AR_TEXT:'+query.FieldByName ('ARTIKEL_TEXT').AsString;
                paramarray [5] := 'AR_TEXT_INFO:';
                ParamArray [6] := 'AR_COLLI_NAME:'+query.FieldByName ('COLLI_NAME').AsString;

                if (query.FieldByName ('OPT_IS_COLLI').AsString = '1') then
                  ParamArray [7] := 'AR_COLLI_NR:'+query.FieldByName ('COLLI_NR').AsString+'/'+query.FieldByName ('ANZAHL_PACKEINHEIT').AsString
                else
                  ParamArray [7] := 'AR_COLLI_NR:1/1';

                if (query.FieldByName('GEWICHT_IST').IsNull) then
                  paramarray [8] := 'NETTO_GEWICHT:'
                else
                  paramarray [8] := 'NETTO_GEWICHT:'+Format ('%8.2f kg', [query.FieldByName('GEWICHT_IST').AsFloat]);

                paramarray [9] := 'MENGE:'+query.FieldByName('MENGE').AsString;
                paramarray [10] := 'EINHEIT:'+query.FieldByName('EINHEIT').AsString;
                paramarray [11] := 'AR_COLOR:';
                paramarray [12] := 'AR_SIZE:';
                paramarray [13] := 'AR_EAN:'+eanstr;
                paramarray [14] := 'MHD:'+query.FieldByName ('MHD').AsString;
                paramarray [15] := 'MHD_KURZ:'+copy (bardata.MHD,1,6)+copy (bardata.MHD,9,2);
                paramarray [16] := 'CHARGE:'+query.FieldByName ('CHARGE').AsString;
                paramarray [18] := 'MANUFACTURER:'+query.FieldByName ('PRODUCER').AsString;
                paramarray [20] := 'KUNDEN_NR:'+query.FieldByName ('KUNDEN_NR').AsString;
                paramarray [21] := 'AUFTRAG_NR:'+query.FieldByName ('AUFTRAG_NR').AsString;
                paramarray [25] := 'BES_CATEGORY:'+query.FieldByName ('BES_CATEGORY').AsString;
                paramarray [26] := 'BES_CATEGORY_DEF:'+query.FieldByName ('BES_CATEGORY_DEF').AsString;
                paramarray [29] := 'BARCODE_EAN:'+query.FieldByName('EAN').AsString;
                paramarray [30] := 'BARCODE:'+barstr;
                paramarray [31] := 'BARCODE_TEXT:'+bartextstr;
                paramarray [32] := 'WOCHE:';

                paramarray [33] := 'ID_BARCODE:';
                paramarray [34] := 'BESTAND_ID:';
                if Assigned (query.FindField ('BESTAND_ID')) then begin
                  paramarray [33] := paramarray [33]+GetIDBarcode ('4', query.FieldByName('BESTAND_ID').AsString);
                  paramarray [34] := paramarray [34]+query.FieldByName('BESTAND_ID').AsString;
                end;

                paramarray [35] := 'SERIAL:';
                if Assigned (query.FindField ('SERIAL_NR')) then
                  paramarray [35] := paramarray [35]+query.FieldByName('SERIAL_NR').AsString;

                paramarray [41] := 'JAHR:';
                paramarray [43] := 'LAGER:'+query.FieldByName ('LAGER').AsString;

                if (query.FieldByName ('MENGE').IsNull) then
                  paramarray [44] := 'KOPIEN:'+GetCopySequenz (1 + forminfo.Kopien)
                else if (query.FieldByName ('MENGE').AsInteger < 20) then
                  paramarray [44] := 'KOPIEN:'+GetCopySequenz (query.FieldByName ('MENGE').AsInteger * (1 + forminfo.Kopien))
                else
                  paramarray [44] := 'KOPIEN:'+GetCopySequenz (20 * (1 + forminfo.Kopien));

                res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramarray, ErrorMsg);
              end;

              EndPrinting;
            end;

            ClosePrinterPort;
          end;
        end;

        query.Close;
      except
        res := -9;
        ErrorMsg := FormatMessageText (1150, []);
      end;
    finally
      query.Free;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintGraphiLabel (const PrtDaten : TPrinterPorts; const GraphiFilename : String; var TrackingNr, TrackingURL, ErrText : String) : Integer;
type
  TRGBQuadArray = array[0..MaxInt div 4 - 1] of TRGBQuad;
  PRGBQuadArray = ^TRGBQuadArray;
var
  res     : Integer;
  png     : {$ifdef UsePNGObject}TPNGObject{$else}TPNGGraphic{$endif};
  //png     : TPortableNetworkGraphic32;
  jpg     : TJPEGImage;
  fname,
  extstr  : String;
  dc      : HDC;
  canvas  : TCanvas;
  docinfo : TDocInfo;
  devsize : Longint;
  devmode : TDeviceMode;
  pdevmode: PDeviceMode;
  hmode   : THandle;
  hprt    : THandle;
  drawrect: TRect;
  csvlist : TStringList;
  csvfile : TStringList;
  hres,
  vres,
  pngh,
  pngw,
  hsize,
  vsize   : Integer;
  wert    : DWORD;
  X, Y    : Integer;
  Row     : PRGBQuadArray;
begin
  res := 0;

  ErrText := '';

  extstr := LowerCase (ExtractFileExt (GraphiFilename));

  if (extstr = '.png') then begin
    png := {$ifdef UsePNGObject}TPNGObject{$else}TPNGGraphic{$endif}.Create;
    //png := TPortableNetworkGraphic32.Create;

    try
      try
        png.LoadFromFile (GraphiFilename);

        {$ifdef UsePNGObject}
          pngw := png.Header.Width;
          pngh := png.Header.Height;
        {$endif}

        (*
          for Y := png.Height - 1 downto 0 do begin
            Row := png.ScanLine[Y];

            try
              if Assigned (Row) then begin
                for X := png.Width - 1 downto 0 do begin
                  wert := 255 + (Row[X].rgbBlue - 255) * Row[X].rgbReserved div 255;
                  Row[X].rgbBlue  := wert;
                  wert := 255 + (Row[X].rgbGreen - 255) * Row[X].rgbReserved div 255;
                  Row[X].rgbGreen := wert;
                  wert := 255 + (Row[X].rgbRed - 255) * Row[X].rgbReserved div 255;
                  Row[X].rgbRed   := wert;
                  Row[X].rgbReserved := 0;
                end;
              end;
            except
            end;
          end;
        *)

        fname := '';

        if not (OpenPrinter (PChar (PrtDaten.Port), hprt, Nil)) then begin
          res := -10;
          ErrText := FormatMessageText (1360, [])
        end else begin
          devsize := DocumentProperties(0, hprt, PChar (PrtDaten.Port), devmode, devmode, 0);

          hmode := GlobalAlloc(GHND, devsize);
          if hmode <> 0 then begin
            pdevmode := GlobalLock (hmode);

            if DocumentProperties(0, hprt, PChar (PrtDaten.Port), pdevmode^, pdevmode^, DM_OUT_BUFFER) > 0 then begin
              dc := CreateDC ('WINSPOOL', PChar(PrtDaten.Port), nil, pdevmode);

              hres := GetDeviceCaps (dc, HORZRES);
              vres := GetDeviceCaps (dc, VERTRES);
              hsize := GetDeviceCaps (dc, HORZSIZE);
              vsize := GetDeviceCaps (dc, VERTSIZE);

              FillChar(docinfo, sizeof(docinfo), #0);
              docinfo.cbSize := SizeOf(docinfo);

              StartDoc(dc, docinfo);
              StartPage(dc);

              SetBkColor (dc, 0);

              {$ifndef UsePNGObject}
                if (pdevmode^.dmPrintQuality = 203) then
                  BltTBitmapAsDib (dc, 0, 0, 840, 1200, png)
                else if (pdevmode^.dmPrintQuality = 300) then
                  BltTBitmapAsDib (dc, 0, 0, 1240, 1770, png)
                else
                  BltTBitmapAsDib (dc, 0, 0, pdevmode^.dmPaperWidth, pdevmode^.dmPaperLength, png);
              {$else}
                canvas := TCanvas.Create;

                try
                  canvas.Handle := dc;

                  if (pdevmode^.dmPrintQuality = 203) then begin
                    drawrect.Top := 0;
                    drawrect.Left := 0;
                    drawrect.Right := 840;
                    drawrect.Bottom := 1200;
                  end else if (pdevmode^.dmPrintQuality = 300) then begin
                    drawrect.Top := 0;
                    drawrect.Left := 0;
                    drawrect.Right := 1240;
                    drawrect.Bottom := 1770;
                  end else begin
                    drawrect.Top := 0;
                    drawrect.Left := 0;
                    drawrect.Right := pdevmode^.dmPaperWidth;
                    drawrect.Bottom := pdevmode^.dmPaperLength;
                  end;

                  png.Draw (canvas, drawrect);
                finally
                  canvas.Free;
                end;
              {$endif}

              EndPage(dc);
              EndDoc(dc);

              if (dc <> 0) then
                deletedc (dc);
            end;

            GlobalUnlock(hmode);
            GlobalFree(hmode);
            hmode := 0;
          end;

          ClosePrinter (hprt);
        end;

        if (res = 0) then begin
          if FileExists (ChangeFileExt (GraphiFilename, '.csv')) then begin
            csvlist := TStringList.Create;
            csvfile := TStringList.Create;

            try
              csvfile.LoadFromFile (ChangeFileExt (GraphiFilename, '.csv'));

              if (csvfile.Count = 2) then begin
                csvlist.StrictDelimiter := True;
                csvlist.Delimiter := ';';
                csvlist.DelimitedText := csvfile [1];

                if (csvlist.Count > 1) then begin
                  TrackingNr  := csvlist [1];
                  TrackingURL := 'https://tracking.dpd.de/status/de_DE/parcel/'+csvlist [1];
                end;
              end;
            finally
              csvlist.Free;
              csvfile.Free;
            end;
          end;
        end;
      except
        res := -11;
        ErrText := 'Fehler beim Lesen der Labeldaten';
      end;
    finally
      png.Free;
    end;
  end;

  Result := res;
end;

end.
