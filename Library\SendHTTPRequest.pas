unit SendHTTPRequest;

interface

uses
   Windows, SysUtils, Classes,
   WinHttp, // siehe: http://www.tek-tips.com/faqs.cfm?fid=7493
   JwaWinCrypt; // siehe: http://jedi-apilib.sourceforge.net

function SendRequest (Host : String;
                      Port : Integer;
                      Service : String;
                      Methode : String;
                      Proxy : String;
                      UserName, Password: WideString;
                      Action : String;
                      ContentType : UTF8String;
                      AddHeader : array of UTF8String;
                      RequestData : UTF8String;
                      FName : String;
                      var ErrCode : Integer;
                      var ErrText : String):Boolean; overload;

function SendRequest (Host : String;
                      Port : Integer;
                      Service : String;
                      Methode : String;
                      Proxy : String;
                      UserName, Password: WideString;
                      Action : String;
                      ContentType : UTF8String;
                      AddHeader : array of UTF8String;
                      RequestData : UTF8String;
                      var ResponseHeader : String;
                      ResponseStream : TStream;
                      var ErrCode : Integer;
                      var ErrText : String):Boolean; overload;

function SendRequest (Host : String;
                      Port : Integer;
                      Service : String;
                      Methode : String;
                      Proxy : String;
                      UserName, Password: WideString;
                      Action : String;
                      ContentType : UTF8String;
                      AddHeader : array of UTF8String;
                      RequestKey: string;
                      RequestData : UTF8String;
                      var ResponseHeader : String;
                      ResponseStream : TStream;
                      var ErrCode : Integer;
                      var ErrText : String):Boolean;   overload;

implementation
function SendRequest (Host : String;
                      Port : Integer;
                      Service : String;
                      Methode : String;
                      Proxy : String;
                      UserName, Password: WideString;
                      Action : String;
                      ContentType : UTF8String;
                      AddHeader : array of UTF8String;
                      RequestKey: string;
                      RequestData : UTF8String;
                      var ResponseHeader : String;
                      ResponseStream : TStream;
                      var ErrCode : Integer;
                      var ErrText : String):Boolean;   overload;
var
  hConnect, hRequest, hSession : HInternet;
  UserAgent, HostStr : WideString;
  Header : WideString;
  I, dwContext, BytesRead, BytesWritten, HeaderLen, OptionLen : Cardinal;
  szHeader : array[1..2048] of AnsiChar;
  szBuffer : array[1..128000] of AnsiChar;
  DownloadSize : Int64;
  dwsize : DWORD;
    Secure: Boolean;

      procedure CleanUp;
  begin
    if hSession <> nil then WinHttpCloseHandle(hSession);
    if hConnect <> nil then WinHttpCloseHandle(hConnect);
    if hRequest <> nil then WinHttpCloseHandle(hRequest);
  end;

begin
  Result := False;
  ErrCode := 0;
  ErrText := '';
  ResponseHeader := '';

  hConnect := nil;
  hRequest := nil;
  UserAgent := 'Mozilla/5.0 (Windows NT 6.1; Trident/7.0; rv:11.0) like Gecko';

  if (copy(lowercase(Host), 1, 7) = 'http://') then
  begin
    secure := False;
    hoststr := copy(Host, 8);
  end else if (copy(lowercase(Host), 1, 8) = 'https://') then
  begin
    secure := True;
    hoststr := copy(Host, 9);
  end else begin
    secure := True;
    hoststr := Host;
  end;

  if (Length(Proxy) > 0) then
    hSession := WinHttpOpen(PWideChar(UserAgent),
                            WINHTTP_ACCESS_TYPE_NAMED_PROXY,
                            PWideChar(WideString(Proxy)),
                            WINHTTP_NO_PROXY_BYPASS,
                            0)
  else
    hSession := WinHttpOpen(PWideChar(UserAgent),
                            WINHTTP_ACCESS_TYPE_DEFAULT_PROXY,
                            WINHTTP_NO_PROXY_NAME,
                            WINHTTP_NO_PROXY_BYPASS,
                            0);

  if (hSession = NIL) then
  begin
    ErrCode := 1004;
    ErrText := WinHttpSysErrorMessage(GetLastError);
    Exit;
  end;

  if (Port > 0) then
    hConnect := WinHttpConnect(hSession, PWideChar(WideString(hoststr)), Port, 0)
  else
    hConnect := WinHttpConnect(hSession, PWideChar(WideString(hoststr)), INTERNET_DEFAULT_HTTPS_PORT, 0);

  if (hConnect = NIL) then
  begin
    ErrCode := 1005;
    ErrText := WinHttpSysErrorMessage(GetLastError);
    CleanUp;
    Exit;
  end;

  if secure then
    hRequest := WinHttpOpenRequest(hConnect, PWideChar(WideString(Methode)),
                                    PWideChar(WideString(Service)), nil,
                                    WINHTTP_NO_REFERER, WINHTTP_DEFAULT_ACCEPT_TYPES, WINHTTP_FLAG_SECURE)
  else
    hRequest := WinHttpOpenRequest(hConnect, PWideChar(WideString(Methode)),
                                    PWideChar(WideString(Service)), nil,
                                    WINHTTP_NO_REFERER, WINHTTP_DEFAULT_ACCEPT_TYPES, 0);

  if (hRequest = NIL) then
  begin
    ErrCode := 1006;
    ErrText := WinHttpSysErrorMessage(GetLastError);
    CleanUp;
    Exit;
  end;

  if (Length(UserName) > 0) then
  begin
    if not WinHttpSetCredentials(hRequest, WINHTTP_AUTH_TARGET_SERVER, WINHTTP_AUTH_SCHEME_BASIC,
                                 PWideChar(WideString(UserName)), PWideChar(WideString(Password)), nil) then
    begin
      ErrCode := 1007;
      ErrText := WinHttpSysErrorMessage(GetLastError);
      CleanUp;
      Exit;
    end;
  end;

  dwContext := 0;
  FillChar(szBuffer, SizeOf(szBuffer), 0);
  I := 1; OptionLen := Length(RequestData);
  repeat
    szBuffer[I] := AnsiChar(RequestData[I]);
    Inc(I);
    szBuffer[I] := #0;
  until (I > OptionLen);

  if Length(ContentType) = 0 then
    Header := AnsiToUtf8('Content-Type: application/x-www-form-urlencoded;charset=UTF-8' + #13 + #10)
  else
    Header := AnsiToUtf8('Content-Type: ' + ContentType + #13 + #10);

  if Length(AddHeader) > 0 then
  begin
    for I := 0 to High(AddHeader) do
    begin
      if Length(AddHeader[I]) > 0 then
        Header := Header + AnsiToUtf8(AddHeader[I] + #13 + #10);
    end;
  end;

  Header := Header + #0;
  if not WinHttpAddRequestHeaders(hRequest, PWideChar(Header), $ffffffff, WINHTTP_ADDREQ_FLAG_ADD) then
  begin
    ErrCode := 1008;
    ErrText := WinHttpSysErrorMessage(GetLastError);
    CleanUp;
    Exit;
  end;

  if not WinHttpSendRequest(hRequest, WINHTTP_NO_ADDITIONAL_HEADERS, 0, @szBuffer, OptionLen, OptionLen, dwContext) then
  begin
    ErrCode := 1009;
    ErrText := WinHttpSysErrorMessage(GetLastError);
    CleanUp;
    Exit;
  end;

  if not WinHttpReceiveResponse(hRequest, nil) then
  begin
    ErrCode := 1010;
    ErrText := WinHttpSysErrorMessage(GetLastError);
    CleanUp;
    Exit;
  end;

  FillChar(szBuffer, SizeOf(szBuffer), 0);
  dwsize := SizeOf(szBuffer);
  if not WinHttpQueryHeaders(hRequest, WINHTTP_QUERY_RAW_HEADERS_CRLF, WINHTTP_HEADER_NAME_BY_INDEX, @szBuffer, dwsize, WINHTTP_NO_HEADER_INDEX) then
  begin
    ErrCode := 1012;
    ErrText := WinHttpSysErrorMessage(GetLastError);
    CleanUp;
    Exit;
  end;

  UnicodeToUtf8(PAnsiChar(@szHeader), PWideChar(@szBuffer), SizeOf(szHeader));
  ResponseHeader := szHeader;

  FillChar(szBuffer, SizeOf(szBuffer), 0);
  if not WinHttpReadData(hRequest, @szBuffer, SizeOf(szBuffer), BytesRead) then
  begin
    ErrCode := 1011;
    ErrText := WinHttpSysErrorMessage(GetLastError);
    CleanUp;
    Exit;
  end;

  while (BytesRead > 0) do
  begin
    ResponseStream.Write(szBuffer, BytesRead);
    FillChar(szBuffer, SizeOf(szBuffer), 0);
    if not WinHttpReadData(hRequest, @szBuffer, SizeOf(szBuffer), BytesRead) then
    begin
      ErrCode := 1012;
      ErrText := WinHttpSysErrorMessage(GetLastError);
      CleanUp;
      Exit;
    end;
  end;

  CleanUp;
  Result := True;
end;


function SendRequest (Host : String;
                      Port : Integer;
                      Service : String;
                      Methode : String;
                      Proxy : String;
                      UserName, Password: WideString;
                      Action : String;
                      ContentType : UTF8String;
                      AddHeader : array of UTF8String;
                      RequestData : UTF8String;
                      FName : String;
                      var ErrCode : Integer;
                      var ErrText : String):Boolean;
var
  respstr    : String;
  filestream : TFileStream;
begin
  filestream := TFileStream.Create (FName, fmCreate);

  Result := SendRequest (Host, Port, Service, Methode, Proxy, UserName, Password, Action, ContentType, AddHeader, RequestData, respstr, filestream, ErrCode, ErrText);

  filestream.Free;
end;

function SendRequest (Host : String;
                      Port : Integer;
                      Service : String;
                      Methode : String;
                      Proxy : String;
                      UserName, Password: WideString;
                      Action : String;
                      ContentType : UTF8String;
                      AddHeader : array of UTF8String;
                      RequestData : UTF8String;
                      var ResponseHeader : String;
                      ResponseStream : TStream;
                      var ErrCode : Integer;
                      var ErrText : String):Boolean;
  var
      hConnect, hRequest, hSession : HInternet;
      UserAgent : WideString;
      Header : WideString;
      I, dwContext, BytesRead, BytesWritten, HeaderLen, OptionLen : Cardinal;
      line : String;
      szHeader : array[1..2048] of AnsiChar;
      szBuffer : array[1..128000] of AnsiChar;
      DownloadSize : Int64;
      FHandle : Integer;
      FSizeRead : DWord;
      hStore : HCERTSTORE;
      pCert : PCertContext;
      dwstatcode : DWORD;
      dwsize : DWORD;
      dwanz : DWORD;
      dwsupport : DWORD;
      dwfirst : DWORD;
      dwtarget : DWORD;
      dwproto : DWORD;
      dwIndex : DWORD;
      ok : boolean;
      hoststr : String;
      secure : boolean;
      //S : String;

   procedure CleanUp;
   begin
     if hSession <> nil then WinHttpCloseHandle(hSession);
     if hConnect <> nil then WinHttpCloseHandle(hConnect);
     if hRequest <> nil then WinHttpCloseHandle(hRequest);
     CloseHandle(FHandle);
   end;

begin
   Result:=False; ErrCode:=0; ErrText:=''; ResponseHeader := '';
   hConnect := nil;
   hRequest := nil;
   UserAgent:='Mozilla/5.0 (Windows NT 6.1; Trident/7.0; rv:11.0) like Gecko';
   //UserAgent:='';

   if (copy (lowercase (Host), 1, 7) = 'http://') then begin
     secure  := false;
     hoststr := copy (Host, 8);
   end else if (copy (lowercase (Host), 1, 8) = 'https://') then begin
     secure  := true;
     hoststr := copy (Host, 9);
   end else begin
     secure  := true;
     hoststr := Host;
   end;

   if (Length (Proxy) > 0) then
     hSession := WinHttpOpen(PWideChar(UserAgent),
                             WINHTTP_ACCESS_TYPE_NAMED_PROXY,
                             PWideChar(WideString(Proxy)),
                             WINHTTP_NO_PROXY_BYPASS,
                             0)
   else
     hSession := WinHttpOpen(PWideChar(UserAgent),
                             WINHTTP_ACCESS_TYPE_DEFAULT_PROXY,
                             WINHTTP_NO_PROXY_NAME,
                             WINHTTP_NO_PROXY_BYPASS,
                             0);

   if (hSession = NIL) then
    begin
      ErrCode:=1004;
      ErrText:=WinHttpSysErrorMessage(GetLastError);
      Exit;
    end;

   if (Port > 0) then
     hConnect:=WinHttpConnect(hSession,
                              PWideChar(WideString(hoststr)),
                              Port,
                              0)
   else
     hConnect:=WinHttpConnect(hSession,
                              PWideChar(WideString(hoststr)),
                              INTERNET_DEFAULT_HTTPS_PORT,
                              0);

   if (hConnect = NIL) then
    begin
      ErrCode:= 1005;
      ErrText:=WinHttpSysErrorMessage(GetLastError);
      CleanUp; Exit;
    end;

   if secure then
     hRequest:=WinHttpOpenRequest(hConnect,
                                  PWideChar(WideString(Methode)),
                                  PWideChar(WideString(Service)),
                                  nil,
                                  WINHTTP_NO_REFERER,
                                  WINHTTP_DEFAULT_ACCEPT_TYPES,
                                  WINHTTP_FLAG_SECURE)
   else
     hRequest:=WinHttpOpenRequest(hConnect,
                                  PWideChar(WideString(Methode)),
                                  PWideChar(WideString(Service)),
                                  nil,
                                  WINHTTP_NO_REFERER,
                                  WINHTTP_DEFAULT_ACCEPT_TYPES,
                                  0);
   if (hRequest = NIL) then
    begin
      ErrCode:=1006;
      ErrText:=WinHttpSysErrorMessage(GetLastError);
      CleanUp; Exit;
    end;

   if (Length (UserName) > 0) then begin
     if not WinHttpSetCredentials(hRequest,
                                  WINHTTP_AUTH_TARGET_SERVER,
                                  WINHTTP_AUTH_SCHEME_BASIC,
                                  PWideChar(WideString(UserName)),
                                  PWideChar(WideString(Password)),
                                  nil) then
      begin
        ErrCode:=1007;
        ErrText:=WinHttpSysErrorMessage(GetLastError);
        CleanUp; Exit;
      end;
   end;

   if (Length (RequestData) > 0) then begin
     // Requestdaten vorbereiten...
     dwContext:=0;
     FillChar(szBuffer, SizeOf(szBuffer), 0);
     I:=1; OptionLen:=Length(RequestData);
     repeat
       szBuffer[I]:= AnsiChar (RequestData[I]);
       Inc(I);
       szBuffer[I]:= #0;
     until (I > OptionLen);
   end;

   if (Length (ContentType) = 0) then begin
     Header := AnsiToUtf8 ('Content-Type: application/x-www-form-urlencoded;charset=UTF-8'+#13+#10);
   end else begin
     Header := AnsiToUtf8 ('Content-Type: '+ContentType+#13+#10);
   end;

   if (High(AddHeader) >= 0) then begin
     for i:= 0 to High(AddHeader) do begin
       if (Length (AddHeader [i]) > 0) then
         Header := Header + AnsiToUtf8 (AddHeader[i]+#13+#10);
     end;
  end;

   Header := Header + #0;

   if not WinHttpAddRequestHeaders(hRequest,
                                   PWideChar(Header),
                                   $ffffffff,
                                   WINHTTP_ADDREQ_FLAG_ADD) Then

      begin
        ErrCode:=1008;
        ErrText:=WinHttpSysErrorMessage(GetLastError);
        CleanUp; Exit;
      end;

   HeaderLen := Length (Header);
   OptionLen := Length (RequestData);

   dwContext := 0;

   // und abschicken...
   if not WinHttpSendRequest(hRequest,
                             WINHTTP_NO_ADDITIONAL_HEADERS,
                             0,
                             @szBuffer,
                             OptionLen,
                             OptionLen,
                             dwContext) then
    begin
      ErrCode:=1009;
      ErrText:=WinHttpSysErrorMessage(GetLastError);
      CleanUp; Exit;
    end;

   // Antwort holen...
   if not WinHttpReceiveResponse(hRequest, nil) then
    begin
      ErrCode:=1010;
      ErrText:=WinHttpSysErrorMessage(GetLastError);
      CleanUp; Exit;
    end;

   FillChar(szBuffer, SizeOf(szBuffer), 0);

   dwsize := sizeof(szBuffer);

   // Header holen...
   if not WinHttpQueryHeaders (hRequest, WINHTTP_QUERY_RAW_HEADERS_CRLF, WINHTTP_HEADER_NAME_BY_INDEX, @szBuffer, dwsize, WINHTTP_NO_HEADER_INDEX) then
    begin
      ErrCode:=1012;
      ErrText:=WinHttpSysErrorMessage(GetLastError);
      CleanUp; Exit;
    end;

   UnicodeToUtf8(PAnsiChar (@szHeader), PWideChar (@szBuffer), sizeof(szHeader));

   {$ifdef UNICODE}
     ResponseHeader := szHeader;
   {$else}
     ResponseHeader := PChar (@szHeader);
   {$endif}

   FillChar(szBuffer, SizeOf(szBuffer), 0);

   // und lesen...
   if not WinHttpReadData(hRequest, @szbuffer, sizeof(szbuffer), BytesRead) then
    begin
      ErrCode:=1011;
      ErrText:=WinHttpSysErrorMessage(GetLastError);
      CleanUp; Exit;
    end;

   // und in Ausgabedatei schreiben...
   while (BytesRead > 0) do begin
      ResponseStream.Write (szBuffer, BytesRead);

      FillChar(szBuffer, SizeOf(szBuffer), 0);

      if not WinHttpReadData(hRequest, @szbuffer, sizeof(szbuffer), BytesRead) then
       begin
         ErrCode:=1012;
         ErrText:=WinHttpSysErrorMessage(GetLastError);
         CleanUp; Exit;
       end;
    end;

   CleanUp;
   Result:=True;
end; { SendRequest }

end.
