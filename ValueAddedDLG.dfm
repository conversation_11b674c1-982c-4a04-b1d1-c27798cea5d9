object ValueAddedForm: TValueAddedForm
  Left = 0
  Top = 0
  Caption = 'Infos zu den Values Added Services'
  ClientHeight = 396
  ClientWidth = 896
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 896
    Height = 76
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      896
      76)
    object Label1: TLabel
      Left = 16
      Top = 16
      Width = 41
      Height = 13
      Caption = 'Auftrag:'
    end
    object Label2: TLabel
      Left = 16
      Top = 35
      Width = 34
      Height = 13
      Caption = 'Kunde:'
    end
    object AuftragLabel: TLabel
      Left = 88
      Top = 16
      Width = 62
      Height = 13
      Caption = 'AuftragLabel'
    end
    object KundeLabel: TLabel
      Left = 88
      Top = 35
      Width = 55
      Height = 13
      Caption = 'KundeLabel'
    end
    object Bevel1: TBevel
      Left = 7
      Top = 70
      Width = 886
      Height = 8
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 79
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 350
    Width = 896
    Height = 46
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      896
      46)
    object OkButton: TButton
      Left = 734
      Top = 13
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Ok'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
    object AbortButton: TButton
      Left = 818
      Top = 13
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
    object PrintButton: TButton
      Left = 8
      Top = 13
      Width = 163
      Height = 25
      Caption = 'Liste drucken ...'
      TabOrder = 2
      OnClick = PrintButtonClick
    end
  end
  object ScrollBox1: TScrollBox
    Left = 0
    Top = 76
    Width = 896
    Height = 274
    Align = alClient
    BevelInner = bvNone
    BevelOuter = bvNone
    BorderStyle = bsNone
    TabOrder = 2
  end
end
