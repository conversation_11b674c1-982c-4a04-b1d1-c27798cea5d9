object TourRelationForm: TTourRelationForm
  Left = 375
  Top = 239
  BorderIcons = [biSystemMenu]
  Caption = 'Touren und Warenausgangs-Relationen'
  ClientHeight = 566
  ClientWidth = 707
  Color = clBtnFace
  Constraints.MinHeight = 593
  Constraints.MinWidth = 600
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnKeyDown = FormKeyDown
  OnShow = FormShow
  DesignSize = (
    707
    566)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 292
    Width = 51
    Height = 13
    Caption = 'Relationen'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 527
    Width = 695
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 400
    ExplicitWidth = 570
  end
  object Bevel2: TBevel
    Left = 4
    Top = 288
    Width = 695
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 667
  end
  object Label2: TLabel
    Left = 8
    Top = 8
    Width = 98
    Height = 13
    Caption = 'Zugeordnete Touren'
  end
  object WARelDBGrid: TDBGridPro
    Left = 8
    Top = 311
    Width = 564
    Height = 210
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = WARelDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 4
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDblClick = WARelDBGridDblClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 580
    Top = 536
    Width = 122
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 10
  end
  object RelNewButton: TButton
    Left = 580
    Top = 311
    Width = 122
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Neu...'
    TabOrder = 5
    OnClick = RelNewButtonClick
  end
  object RelDelButton: TButton
    Left = 580
    Top = 383
    Width = 122
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'L'#246'schen...'
    TabOrder = 7
    OnClick = RelDelButtonClick
  end
  object TourDBGrid: TDBGridPro
    Left = 8
    Top = 24
    Width = 564
    Height = 249
    Anchors = [akLeft, akTop, akRight]
    DataSource = TourDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDblClick = TourChangeButtonClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
    OnColumnSort = TourDBGridColumnSort
  end
  object TourNewButton: TButton
    Left = 580
    Top = 24
    Width = 122
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Neu..'
    TabOrder = 1
    OnClick = TourNewButtonClick
  end
  object TourDelButton: TButton
    Left = 580
    Top = 96
    Width = 122
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'L'#246'schen...'
    TabOrder = 3
    OnClick = TourDelButtonClick
  end
  object TourChangeButton: TButton
    Left = 580
    Top = 55
    Width = 122
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Bearbeiten...'
    TabOrder = 2
    OnClick = TourChangeButtonClick
  end
  object RelChangeButton: TButton
    Left = 580
    Top = 343
    Width = 122
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Bearbeiten...'
    TabOrder = 6
    OnClick = WARelDBGridDblClick
  end
  object RelPrtPalButton: TButton
    Left = 580
    Top = 496
    Width = 122
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Relationslabel drucken'
    TabOrder = 8
    OnClick = RelPrtPalButtonClick
  end
  object RelPrtLPButton: TButton
    Left = 580
    Top = 465
    Width = 122
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'WA-Platzlabel drucken'
    TabOrder = 9
    OnClick = RelPrtPalButtonClick
  end
  object PrintButton: TButton
    Left = 8
    Top = 536
    Width = 122
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = #220'bersicht drucken...'
    TabOrder = 11
    OnClick = PrintButtonClick
  end
  object WARelADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 448
    Top = 264
  end
  object WARelDataSource: TDataSource
    DataSet = WARelADOQuery
    OnDataChange = WARelDataSourceDataChange
    Left = 408
    Top = 264
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 536
    Top = 360
  end
  object TourDataSource: TDataSource
    DataSet = TourADOQuery
    OnDataChange = TourDataSourceDataChange
    Left = 112
    Top = 88
  end
  object TourADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 152
    Top = 88
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 384
    Top = 56
  end
end
