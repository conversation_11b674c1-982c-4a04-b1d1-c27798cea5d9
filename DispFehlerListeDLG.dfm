object DispFehlerListeForm: TDispFehlerListeForm
  Left = 276
  Top = 284
  Caption = 'DispFehlerListeForm'
  ClientHeight = 334
  ClientWidth = 988
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 284
    Width = 988
    Height = 50
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      988
      50)
    object Button1: TButton
      Left = 912
      Top = 16
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
  end
  object DBGridPro1: TDBGridPro
    Left = 8
    Top = 73
    Width = 972
    Height = 211
    Align = alClient
    DataSource = DataSource1
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object Panel2: TPanel
    Left = 0
    Top = 0
    Width = 988
    Height = 73
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    object FehlerStatusLabel: TLabel
      Left = 16
      Top = 7
      Width = 85
      Height = 13
      Caption = 'FehlerStatusLabel'
    end
    object FehlerTextLabel: TLabel
      Left = 16
      Top = 28
      Width = 76
      Height = 13
      Caption = 'FehlerTextLabel'
    end
    object CheckZeitLabel: TLabel
      Left = 16
      Top = 50
      Width = 75
      Height = 13
      Caption = 'CheckZeitLabel'
    end
  end
  object Panel3: TPanel
    Left = 0
    Top = 73
    Width = 8
    Height = 211
    Align = alLeft
    BevelOuter = bvNone
    TabOrder = 3
  end
  object Panel4: TPanel
    Left = 980
    Top = 73
    Width = 8
    Height = 211
    Align = alRight
    BevelOuter = bvNone
    TabOrder = 4
  end
  object DataSource1: TDataSource
    DataSet = FehlerQuery
    Left = 136
    Top = 40
  end
  object FehlerQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 176
    Top = 40
  end
end
