object ArtikelIdentInputForm: TArtikelIdentInputForm
  Left = 0
  Top = 0
  Caption = '<PERSON><PERSON><PERSON><PERSON> von zu<PERSON>'#228'tzlichen Infos zum Artikel'
  ClientHeight = 301
  ClientWidth = 635
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 635
    Height = 85
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      635
      85)
    object Label1: TLabel
      Left = 16
      Top = 40
      Width = 41
      Height = 13
      Caption = 'Auftrag:'
    end
    object Label2: TLabel
      Left = 16
      Top = 59
      Width = 34
      Height = 13
      Caption = 'Kunde:'
    end
    object AuftragLabel: TLabel
      Left = 88
      Top = 40
      Width = 62
      Height = 13
      Caption = 'AuftragLabel'
    end
    object KundeLabel: TLabel
      Left = 88
      Top = 59
      Width = 55
      Height = 13
      Caption = 'KundeLabel'
    end
    object Label3: TLabel
      Left = 16
      Top = 8
      Width = 46
      Height = 19
      Caption = 'Artikel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object ArtikelLabel: TLabel
      Left = 88
      Top = 8
      Width = 83
      Height = 19
      Caption = 'ArtikelLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object Bevel1: TBevel
      Left = 4
      Top = 29
      Width = 627
      Height = 8
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
  end
  object PageControl1: TPageControl
    Left = 0
    Top = 85
    Width = 635
    Height = 170
    ActivePage = TabSheet1
    Align = alClient
    TabOrder = 1
    ExplicitTop = 49
    ExplicitHeight = 206
    object TabSheet1: TTabSheet
      Caption = 'TabSheet1'
      ExplicitLeft = 0
      ExplicitTop = 0
      ExplicitWidth = 0
      ExplicitHeight = 178
      object ScrollBox1: TScrollBox
        Left = 0
        Top = 0
        Width = 627
        Height = 142
        Align = alClient
        BorderStyle = bsNone
        TabOrder = 0
        ExplicitHeight = 178
      end
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 255
    Width = 635
    Height = 46
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      635
      46)
    object OkButton: TButton
      Left = 473
      Top = 13
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Ok'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
    object AbortButton: TButton
      Left = 557
      Top = 13
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
  end
end
