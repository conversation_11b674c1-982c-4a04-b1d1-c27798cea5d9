program SprachEditor;

uses
  Forms,
  SprachEditorMain in 'SprachEditorMain.pas' {SprachEditorForm},
  SprachModul in 'SprachModul.pas' {LVSSprachModul: TDataModule},
  CompTranslateSearch in 'Library\CompTranslateSearch.pas' {CompTranslateSearchForm},
  CompTranslateSelLang in 'Library\CompTranslateSelLang.pas' {TranslateSelLangForm};

{$R *.res}

begin
  Application.Initialize;
  //Application.MainFormOnTaskbar := True;
  Application.CreateForm(TLVSSprachModul, LVSSprachModul);

  LVSSprachModul.ShowEditor (Nil);

  Application.Run;
end.
