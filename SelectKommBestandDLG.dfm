object SelectKommBestandForm: TSelectKommBestandForm
  Left = 0
  Top = 0
  Caption = 'Zu kommissionierender Bestand ausw'#228'hlen'
  ClientHeight = 345
  ClientWidth = 827
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    827
    345)
  PixelsPerInch = 96
  TextHeight = 13
  object LEBestandDBGrid: TDBGridPro
    Left = 8
    Top = 72
    Width = 811
    Height = 225
    Anchors = [akLeft, akTop, akRight, akBottom]
    Constraints.MinHeight = 100
    DataSource = LEBestandDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgCol<PERSON>ines, dgRow<PERSON>ines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit, dgMultiSelect]
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    OnDblClick = LEBestandDBGridDblClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoCheckBoxSelect, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 29
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object OkButton: TButton
    Left = 656
    Top = 312
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    ModalResult = 1
    TabOrder = 1
  end
  object AbortButton: TButton
    Left = 744
    Top = 312
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 2
  end
  object LEBestandDataSource: TDataSource
    DataSet = LEBestandDataSet
    Left = 696
    Top = 16
  end
  object LEBestandDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 728
    Top = 16
  end
end
