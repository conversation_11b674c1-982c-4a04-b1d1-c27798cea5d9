object SwitchArtikelForm: TSwitchArtikelForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Artikel des Bestandes wechseln'
  ClientHeight = 275
  ClientWidth = 443
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    443
    275)
  PixelsPerInch = 96
  TextHeight = 13
  object Label14: TLabel
    Left = 8
    Top = 254
    Width = 37
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Label14'
    ExplicitTop = 222
  end
  object Label13: TLabel
    Left = 8
    Top = 194
    Width = 79
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Grund '#196'nderung'
    ExplicitTop = 172
  end
  object Label3: TLabel
    Left = 8
    Top = 144
    Width = 83
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Vorgangsnummer'
  end
  object ArtikelPanel: TPanel
    Left = 0
    Top = 41
    Width = 443
    Height = 96
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      443
      96)
    object Label1: TLabel
      Left = 8
      Top = 11
      Width = 30
      Height = 13
      Caption = 'Artikel'
    end
    object Label11: TLabel
      Left = 8
      Top = 41
      Width = 56
      Height = 13
      Caption = 'Ausf'#252'hrung'
    end
    object Bevel3: TBevel
      Left = 5
      Top = 90
      Width = 430
      Height = 10
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 80
    end
    object ArtikelComboBox: TComboBoxPro
      Left = 184
      Top = 8
      Width = 251
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 1
      OnCloseUp = ArtikelComboBoxCloseUp
      OnDropDown = ArtikelComboBoxDropDown
    end
    object VarianteEdit: TEdit
      Left = 80
      Top = 38
      Width = 355
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 128
      TabOrder = 2
      Text = 'VarianteEdit'
    end
    object ArNrEdit: TEdit
      Left = 80
      Top = 8
      Width = 98
      Height = 21
      TabOrder = 0
      Text = 'ArNrEdit'
    end
    object ListedCheckBox: TCheckBox
      Left = 80
      Top = 68
      Width = 343
      Height = 17
      Anchors = [akLeft, akBottom]
      Caption = 'nicht gelistete Artikel auch auff'#252'hren'
      TabOrder = 3
      OnClick = ListedCheckBoxClick
    end
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 443
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      443
      41)
    object Bevel1: TBevel
      Left = 5
      Top = 38
      Width = 430
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label2: TLabel
      Left = 8
      Top = 13
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandComboBox: TComboBoxPro
      Left = 80
      Top = 10
      Width = 355
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
    end
  end
  object OkButton: TButton
    Left = 273
    Top = 242
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 4
  end
  object AbortButton: TButton
    Left = 360
    Top = 242
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
  object GrundComboBox: TComboBox
    Left = 8
    Top = 210
    Width = 427
    Height = 21
    Hint = 'Grund der manuellen Anlage'
    Style = csDropDownList
    Anchors = [akLeft, akBottom]
    ItemHeight = 0
    MaxLength = 64
    TabOrder = 3
    OnChange = GrundComboBoxChange
  end
  object VorgangEdit: TEdit
    Left = 8
    Top = 160
    Width = 121
    Height = 21
    Anchors = [akLeft, akBottom]
    MaxLength = 32
    TabOrder = 2
    Text = 'VorgangEdit'
  end
end
