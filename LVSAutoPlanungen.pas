unit LVSAutoPlanungen;

interface

uses <PERSON><PERSON>, DB, ADODB;

function CreateBacktransporte (const RefLager, RefSourceLB, RefDestLB, Anzahl : Integer; var Ergebnis : Integer) : Integer;

implementation

uses
  <PERSON>ysUtils, DatenModul, Variants;

//******************************************************************************
//* Function Name:
//* Author       : <PERSON>
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateBacktransporte (const RefLager, RefSourceLB, RefDestLB, Anzahl : Integer; var Ergebnis : Integer) : Integer;
var
  dbres  : Integer;
  StoredProcedure : TADOStoredProc;
begin
  Ergebnis := -1;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'RUECKLAGERUNG_PLANEN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, RefLager);
    Parameters.CreateParameter('pRefSourceLB',ftString,pdInput, 128, RefSourceLB);
    Parameters.CreateParameter('pRefDestLB',ftInteger,pdInput, 12, RefDestLB);
    Parameters.CreateParameter('pAnzahl',ftInteger,pdInput, 12, Anzahl);
    Parameters.CreateParameter('oAnzErzeugt', ftInteger, pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if not (StoredProcedure.Parameters.ParamValues ['oAnzErzeugt'] = NULL) then
      Ergebnis := StoredProcedure.Parameters.ParamValues ['oAnzErzeugt'];
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

end.
