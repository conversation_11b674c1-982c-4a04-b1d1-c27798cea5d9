unit LEUmlagernDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, DB, ADODB, ExtCtrls, ComboBoxPro, ComCtrls, BarCodeScanner,
  Menus, Grids, DBGrids, SMDBGrid, DBGridPro;

type
  TUmlagerForm = class(TForm)
    ADOQuery1: TADOQuery;
    OkButton: TButton;
    Button1: TButton;
    PageControl1: TPageControl;
    LocalTabSheet: TTabSheet;
    ExternTabSheet: TTabSheet;
    Label3: TLabel;
    Label4: TLabel;
    LBComboBox: TComboBoxPro;
    LPListBox: TListBox;
    LocComboBox: TComboBoxPro;
    LagerComboBox: TComboBoxPro;
    Label8: TLabel;
    Label9: TLabel;
    InhousGroupBox: TGroupBox;
    Label10: TLabel;
    InHousLBComboBox: TComboBoxPro;
    InhousLPListBox: TListBox;
    Label11: TLabel;
    Label12: TLabel;
    InHousLBZoneComboBox: TComboBoxPro;
    Label13: TLabel;
    LBZoneComboBox: TComboBoxPro;
    AuslagernTabSheet: TTabSheet;
    Label14: TLabel;
    Label15: TLabel;
    GrundEdit: TEdit;
    Label1: TLabel;
    StaticText1: TStaticText;
    Label5: TLabel;
    StaticText2: TStaticText;
    Label6: TLabel;
    StaticText3: TStaticText;
    Label7: TLabel;
    StaticText4: TStaticText;
    Bevel1: TBevel;
    ArtikelListBox: TListBox;
    Label2: TLabel;
    AuslagerRelComboBox: TComboBoxPro;
    LPListBoxPopupMenu: TPopupMenu;
    LagerSuchenLPMenuItem: TMenuItem;
    InhousLPListBoxPopupMenu: TPopupMenu;
    InhousLPSuchMenuItem: TMenuItem;
    Label16: TLabel;
    KundenDBGrid: TDBGridPro;
    KundenQuery: TADOQuery;
    KundenDataSource: TDataSource;
    KundenCheckBox: TCheckBox;
    Label17: TLabel;
    SpedComboBox: TComboBoxPro;
    procedure FormShow(Sender: TObject);
    procedure LBComboBoxChange(Sender: TObject);
    procedure ListBoxDrawItem(Control: TWinControl; Index: Integer;
      Rect: TRect; State: TOwnerDrawState);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure ExternTabSheetShow(Sender: TObject);
    procedure LocComboBoxChange(Sender: TObject);
    procedure LagerComboBoxChange(Sender: TObject);
    procedure InHousLBComboBoxChange(Sender: TObject);
    procedure InHousLBZoneComboBoxChange(Sender: TObject);
    procedure LBZoneComboBoxChange(Sender: TObject);
    procedure AuslagernTabSheetShow(Sender: TObject);
    procedure InhousLPSuchMenuItemClick(Sender: TObject);
    procedure FormPaint(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure KundenCheckBoxClick(Sender: TObject);
  private
    fLPEmptyAllowd : Boolean;
    fLPInUseAllowd : Boolean;

    fRefLager : Integer;
    fRefMand  : Integer;

    fLPSortType : Integer;

    fRefWarenEmpf : Integer;
    fRefLieferAdr : Integer;

    fMoveArt : String;
    fRefZielLE   : Integer;

    procedure UpdateLPListe (const LBRef, LBZoneRef : Integer; LPListBox : TListBox);
    procedure ScannerErfassung (var Message: TMessage); message WM_USER + SCANNER_DETECT;
  public
    Lager       : String;
    LEReferenz  : Integer;
    NVEReferenz : Integer;
    LBReferenz  : Integer;

    property LPEmptyAllowd : Boolean read fLPEmptyAllowd write fLPEmptyAllowd;
    property LPInUseAllowd : Boolean read fLPInUseAllowd write fLPInUseAllowd;

    property MoveArt   : String read fMoveArt;
    property RefZielLE : integer read fRefZielLE;

    property RefWarenEmpf : Integer read fRefWarenEmpf write fRefWarenEmpf;
    property RefLieferAdr : Integer read fRefLieferAdr write fRefLieferAdr;

    procedure PrepareRes (const RefMand, RefLPRes : Integer);
    procedure Prepare    (const RefMand, RefLager : Integer);
  end;

implementation

uses
  VCLUtilitys, StringUtils, DatenModul, FrontEndUtils, DBGridUtilModule,
  SprachModul, LVSDatenInterface, ResourceText;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.PrepareRes (const RefMand, RefLPRes : Integer);
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from V_LP_RES where REF=:ref');
    query.Parameters.ParamByName('ref').Value := RefLPRes;

    query.Open;

    fRefLager := query.FieldByName ('REF_LAGER').AsInteger;

    Prepare (RefMand, fRefLager);

    LBComboBox.ItemIndex := FindComboboxRef(LBComboBox, query.FieldByName ('REF_LB').AsInteger);
    LBComboBoxChange (Nil);

    if not (query.FieldByName ('REF_LB_ZONE').IsNull) then begin
      LBZoneComboBox.ItemIndex := FindComboboxRef(LBZoneComboBox, query.FieldByName ('REF_LB_ZONE').AsInteger);
      LBZoneComboBoxChange (Nil);
    end;

    if not (query.FieldByName ('REF_LP').IsNull) then
      LPListBox.ItemIndex := FindListBoxRef (LPListBox, query.FieldByName ('REF_LP').AsInteger);
    

    query.Close;
  finally
    query.Free;
  end;
end;
//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.Prepare (const RefMand, RefLager : Integer);
var
  dbres     : Integer;
begin
  fRefMand  := RefMand;
  fRefLager := RefLager;

  //Wenn der Mandant nicht eindeutig zuordenbar ist, kann die LE nur innerhalb des Lager umgelagert werden
  if (fRefMand = -1) then
    ExternTabSheet.TabVisible := false;

  LocalTabSheet.Caption := StringReplace (LocalTabSheet.Caption, '$0', Lager, [rfReplaceAll]);

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF, NAME, BESCHREIBUNG from V_LB where REF_LAGER=:ref_lager order by NAME');
  ADOQuery1.Parameters.ParamByName('ref_lager').Value := RefLager;

  ClearListBoxObjects (LPListBox);
  ClearComboBoxObjects (LBComboBox);

  try
    ADOQuery1.Open;

    while not (ADOQuery1.EOF) do begin
      LBComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;
  except
    dbres := -9;
  end;

  LBComboBox.ItemIndex := 0;
  LBComboBoxChange (Nil);

  KundenQuery.SQL.Add ('select * from V_WARENEMPF_LIEFER_ADR where STATUS=''AKT'' and REF_MAND=:ref_mand');
  KundenQuery.SQL.Add ('and REF in (select REF_WARENEMPF from V_WARENEMPF_REL_KOMM where REF_LOCATION=(select REF_LOCATION from V_LOCATION_REL_LAGER where REF_LAGER=:ref_lager) and OPT_AUSLAGERUNG=''1'')');
  KundenQuery.Parameters.ParamByName('ref_mand').Value := fRefMand;
  KundenQuery.Parameters.ParamByName('ref_lager').Value := fRefLager;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.FormShow(Sender: TObject);
var
  len,
  idx,
  dbres   : Integer;
  sl      : DWORD;
  tab     : array [0..4] of Integer;
  strlist : TStringList;
begin
  dbres := 0;

  StaticText1.Caption := '';
  StaticText2.Caption := '';
  StaticText3.Caption := '';
  StaticText4.Caption := '';

  ArtikelListBox.Clear;

  if (LEReferenz <> -1) then begin
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF_LAGER, LE_NR, LE_TYPE, LB_NR, LP_NR, HOEHEN_KLASSE_STR from V_LE where REF=:ref_le');
    ADOQuery1.Parameters.ParamByName('ref_le').Value := LEReferenz;

    try
      ADOQuery1.Open;

      StaticText1.Caption := ADOQuery1.Fields [1].AsString;

      if (ADOQuery1.Fields [5].IsNull) then
        StaticText2.Caption := ADOQuery1.Fields [2].AsString
      else StaticText2.Caption := ADOQuery1.Fields [2].AsString + ' / ' + ADOQuery1.Fields [5].AsString;

      StaticText3.Caption := ADOQuery1.Fields [3].AsString;
      StaticText4.Caption := ADOQuery1.Fields [4].AsString;

      ADOQuery1.Close;

      ArtikelListBox.Clear;

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select ARTIKEL_NR,coalesce (MENGE_BEW,MENGE_SPERR,MENGE_FREI),ARTIKEL_TEXT,MHD from V_BES_LE where REF_LE=:ref_le');
      ADOQuery1.Parameters.ParamByName('ref_le').Value := LEReferenz;

      strlist := TStringList.Create;

      try
        ADOQuery1.Open;

        tab [0] := 5;
        tab [1] := 5;
        tab [2] := 32;

        while (dbres = 0) and not (ADOQuery1.EOF) do begin
          len := Length (ADOQuery1.Fields [0].AsString);
          if (len > tab [0]) then tab [0] := len;

          len := Length (ADOQuery1.Fields [2].AsString);
          if (len > tab [2]) then tab [2] := len;

          strlist.Add (ADOQuery1.Fields [0].AsString + #9 + ADOQuery1.Fields [1].AsString + #9 + ADOQuery1.Fields [2].AsString + #9 + ADOQuery1.Fields [3].AsString);

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;

        tab [0] := tab [0] * 4;
        tab [1] := tab [1] * 4 + tab [0];
        tab [2] := tab [2] * 4 + tab [1];

        ArtikelListBox.TabWidth := 1;
        { Set the Tabulators / Tabulatoren setzen }
        sl:=GetWindowLong(ArtikelListBox.Handle,GWL_STYLE);
        sl:=sl or LBS_USETABSTOPS;
        SetWindowLong(ArtikelListBox.Handle,GWL_STYLE,sl);

        SendMessage(ArtikelListBox.Handle, LB_SETTABSTOPS, 3, Longint(@tab));

        idx := 0;
        while (idx < strlist.Count) do begin
          ArtikelListBox.Items.Add (strlist [idx]);

          Inc (idx);
        end;
      except
        dbres := -9;
      end;

      strlist.Free;
    except
      dbres := -9;
    end;
  end else if (NVEReferenz <> -1) then begin
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF_LAGER, NVE_NR, NVE_TYPE, LB_NR, LP_NR from V_NVE where REF=:ref_nve');
    ADOQuery1.Parameters.ParamByName('ref_nve').Value := NVEReferenz;

    try
      ADOQuery1.Open;

      StaticText1.Caption := ADOQuery1.Fields [1].AsString;
      StaticText2.Caption := ADOQuery1.Fields [2].AsString;
      StaticText3.Caption := ADOQuery1.Fields [3].AsString;
      StaticText4.Caption := ADOQuery1.Fields [4].AsString;

      ADOQuery1.Close;

      ArtikelListBox.Clear;

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select ARTIKEL_NR,ARTIKEL_TEXT,MHD from V_NVE_INHALT where REF_NVE=:ref_nve');
      ADOQuery1.Parameters.ParamByName('ref_nve').Value := NVEReferenz;

      try
        ADOQuery1.Open;

        tab [0] := 50;
        tab [1] := 100;

        while (dbres = 0) and not (ADOQuery1.EOF) do begin
          len := ArtikelListBox.Canvas.TextWidth (ADOQuery1.Fields [0].AsString);
          if (len > tab [0]) then tab [0] := len;

          len := ArtikelListBox.Canvas.TextWidth (ADOQuery1.Fields [1].AsString);
          if (len > tab [1]) then tab [1] := len;

          ArtikelListBox.Items.Add (ADOQuery1.Fields [0].AsString + #9 + ADOQuery1.Fields [1].AsString + #9 + ADOQuery1.Fields [2].AsString);

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;

        ArtikelListBox.TabWidth := 1;
        { Set the Tabulators / Tabulatoren setzen }
        SendMessage(ArtikelListBox.Handle, LB_SETTABSTOPS, 2, Longint(@tab));
      except
        dbres := -9;
      end;
    except
      dbres := -9;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.InHousLBComboBoxChange(Sender: TObject);
begin
  LoadLBZoneCombobox (InHousLBZoneComboBox, GetComboBoxRef(InHousLBComboBox));

  if (InHousLBZoneComboBox.Items.Count > 0) then
    InHousLBZoneComboBox.Enabled := True
  else begin
    InHousLBZoneComboBox.Enabled := False;
    UpdateLPListe (GetComboBoxRef (InHousLBComboBox), -1, InhousLPListBox);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.InHousLBZoneComboBoxChange(Sender: TObject);
begin
  UpdateLPListe (GetComboBoxRef (InHousLBComboBox), GetComboBoxRef (InHousLBZoneComboBox), InhousLPListBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.InhousLPSuchMenuItemClick(Sender: TObject);
var
  idx,
  reflb,
  reflager : Integer;
  instr    : String;
  query    : TADOQuery;
  list     : TListBox;
begin
  if (Sender = LagerSuchenLPMenuItem) then begin
    list     := LPListBox;
    reflb    := GetComboBoxRef (LBComboBox);
    reflager := fRefLager;
  end else begin
    list     := InhousLPListBox;
    reflb    := GetComboBoxRef (InHousLBComboBox);
    reflager := GetComboBoxRef (LagerComboBox);
  end;

  instr := '';

  if (fLPSortType = 2) then begin
    if (InputQuery (GetResourceText (1291), GetResourceText (1292), instr)) then begin
      query := TADOQuery.Create (Self);
      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        if (reflb = -1) then begin
          query.SQL.Add ('select REF from V_LP where REF_LAGER=:reflager and NAME=:nr');
          query.Parameters.ParamByName('reflager').Value := reflager;
        end else begin
          query.SQL.Add ('select REF from V_LP where REF_LB=:LBRef and NAME=:nr');
          query.Parameters.ParamByName('LBRef').Value := reflb;
        end;
        query.Parameters.ParamByName('nr').Value := instr;

        query.Open;

        if query.Fields [0].IsNull then begin
          if (reflb = -1) then
            MessageDLG (FormatMessageText (1097, []), mtError, [mbOk], 0)
          else MessageDLG (FormatMessageText (1098, []), mtError, [mbOk], 0)
        end else begin
          idx := FindListBoxRef (list, query.Fields [0].AsInteger);

          if (idx = -1) then
            MessageDLG (FormatMessageText (1099, []), mtError, [mbOk], 0)
          else
            list.ItemIndex := idx;
        end;

        query.Close;
      finally
        query.Free;
      end;
    end;
  end else if (fLPSortType = 0) then begin
    if (InputQuery (GetResourceText (1291), GetResourceText (1293), instr)) then begin
      query := TADOQuery.Create (Self);
      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        if (reflb = -1) then begin
          query.SQL.Add ('select REF from V_LP where REF_LAGER=:reflager and LP_NR=:nr');
          query.Parameters.ParamByName('reflager').Value := reflager;
        end else begin
          query.SQL.Add ('select REF from V_LP where REF_LB=:LBRef and LP_NR=:nr');
          query.Parameters.ParamByName('LBRef').Value := reflb;
        end;
        query.Parameters.ParamByName('nr').Value := instr;

        query.Open;

        if query.Fields [0].IsNull then begin
          if (reflb = -1) then
            MessageDLG (FormatMessageText (1097, []), mtError, [mbOk], 0)
          else MessageDLG (FormatMessageText (1098, []), mtError, [mbOk], 0)
        end else begin
          idx := FindListBoxRef (list, query.Fields [0].AsInteger);

          if (idx = -1) then
            MessageDLG (FormatMessageText (1099, []), mtError, [mbOk], 0)
          else
            list.ItemIndex := idx;
        end;

        query.Close;
      finally
        query.Free;
      end;
    end;
  end else begin
    if (InputQuery (GetResourceText (1291), GetResourceText (1294), instr)) then begin
      query := TADOQuery.Create (Self);
      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        if (reflb = -1) then begin
          query.SQL.Add ('select REF from V_LP where REF_LAGER=:reflager and LP_KOOR=:koord');
          query.Parameters.ParamByName('reflager').Value := reflager;
        end else begin
          query.SQL.Add ('select REF from V_LP where REF_LB=:LBRef and LP_KOOR=:koord');
          query.Parameters.ParamByName('LBRef').Value := reflb;
        end;
        query.Parameters.ParamByName('koord').Value := instr;

        query.Open;

        if query.Fields [0].IsNull then begin
          if (reflb = -1) then
            MessageDLG (FormatMessageText (1097, []), mtError, [mbOk], 0)
          else MessageDLG (FormatMessageText (1098, []), mtError, [mbOk], 0)
        end else begin
          idx := FindListBoxRef (list, query.Fields [0].AsInteger);

          if (idx = -1) then
            MessageDLG (FormatMessageText (1099, []), mtError, [mbOk], 0)
          else
            list.ItemIndex := idx;
        end;

        query.Close;
      finally
        query.Free;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.LagerComboBoxChange(Sender: TObject);
begin
  if InhousGroupBox.Enabled then begin
    ClearListBoxObjects (InhousLPListBox);
    ClearComboBoxObjects (InHousLBComboBox);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF, NAME, BESCHREIBUNG from V_LB where REF_LAGER=:ref_lager order by NAME');
    ADOQuery1.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);

    try
      ADOQuery1.Open;

      while not (ADOQuery1.EOF) do begin
        InHousLBComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

        ADOQuery1.Next;
      end;

      ADOQuery1.Close;
    except
    end;

    InHousLBComboBox.ItemIndex := 0;

    if (LBComboBox.ItemIndex >= 0) Then
      UpdateLPListe (GetComboBoxRef (InHousLBComboBox), -1, InhousLPListBox);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.LBComboBoxChange(Sender: TObject);
begin
  LoadLBZoneCombobox (LBZoneComboBox, GetComboBoxRef(LBComboBox));

  if (LBZoneComboBox.Items.Count > 0) then begin
    LBZoneComboBox.Enabled := True;

    LBZoneComboBox.Items.Insert (0, '');
    LBZoneComboBox.ItemIndex := 0;

    UpdateLPListe (GetComboBoxRef (LBComboBox), GetComboBoxRef (LBZoneComboBox), LPListBox);
  end else begin
    LBZoneComboBox.Enabled := False;

    UpdateLPListe (GetComboBoxRef (LBComboBox), -1, LPListBox);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.LBZoneComboBoxChange(Sender: TObject);
begin
  UpdateLPListe (GetComboBoxRef (LBComboBox), GetComboBoxRef (LBZoneComboBox), LPListBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.UpdateLPListe (const LBRef, LBZoneRef : Integer; LPListBox : TListBox);
var
  dbres    : Integer;
  tabsize1,
  tabsize2 : Integer;
  belflag  : String;
  tabs     : array[0..4] of Integer;
begin
  dbres := 0;

  if (UserReg.ReadRegValue ('SortLP', fLPSortType) <> 0) then
    fLPSortType := 1;

  ClearListBoxObjects (LPListBox);

  if (fLPEmptyAllowd) then
    LPListBox.Items.AddObject ('', TListBoxRef.Create (-1));

  ADOQuery1.SQL.Clear;

  if (fLPSortType = 2) then
    ADOQuery1.SQL.Add ('select REF,LP_DISP,LP_KOOR,LT_COUNT,LT_CAPACITY,HOEHEN_KLASSE_STR,STATUS from V_LP where STATUS<>''SPERR''')
  else if (fLPSortType = 1) then
    ADOQuery1.SQL.Add ('select REF,LP_KOOR,LP_NR,LT_COUNT,LT_CAPACITY,HOEHEN_KLASSE_STR,STATUS from V_LP where STATUS<>''SPERR''')
  else
    ADOQuery1.SQL.Add ('select REF,LP_NR,LP_KOOR,LT_COUNT,LT_CAPACITY,HOEHEN_KLASSE_STR,STATUS from V_LP where STATUS<>''SPERR''');

  if not (fLPInUseAllowd) then
    ADOQuery1.SQL.Add ('and ((STATUS<>''UEB'') and (LT_COUNT is null or (LT_COUNT < LT_CAPACITY)))');

  if (LBZoneRef = -1) then begin
    ADOQuery1.SQL.Add ('and REF_LB=:ref_lb');
    ADOQuery1.Parameters.ParamByName('ref_lb').Value := LBRef;
  end else begin
    ADOQuery1.SQL.Add ('and REF_LB_ZONE=:ref_zone');
    ADOQuery1.Parameters.ParamByName('ref_zone').Value := LBZoneRef;
  end;

  if (fLPSortType = 2) then
    ADOQuery1.SQL.Add ('order by LP_DISP')
  else if (fLPSortType = 1) then
    ADOQuery1.SQL.Add ('order by LP_KOOR')
  else
    ADOQuery1.SQL.Add ('order by LP_NR');

  tabsize1 := -1;
  tabsize2 := -1;

  Screen.Cursor := crSQLWait;
  LPListBox.Items.BeginUpdate;

  try
    try
      ADOQuery1.Open;

      while (dbres = 0) and not (ADOQuery1.EOF) do begin
        belflag := 'F';

        if (ADOQuery1.Fields [6].AsString = 'UEB') then
          belflag := 'U'
        else if not (ADOQuery1.Fields [3].IsNull) and (ADOQuery1.Fields [3].AsInteger > 0) and not (ADOQuery1.Fields [4].IsNull) and (ADOQuery1.Fields [3].AsInteger >= ADOQuery1.Fields [4].AsInteger) then
          belflag := 'B';

        if (ADOQuery1.Fields [1].IsNull) then
          LPListBox.Items.AddObject (#9+ADOQuery1.Fields [2].AsString+#9+belflag+#9+ADOQuery1.Fields [5].AsString, TListBoxRef.Create (ADOQuery1.Fields [0].AsInteger))
        else begin
          LPListBox.Items.AddObject (ADOQuery1.Fields [1].AsString+#9+ADOQuery1.Fields [2].AsString+#9+belflag+#9+ADOQuery1.Fields [5].AsString, TListBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

          if (Length (ADOQuery1.Fields [1].AsString) > tabsize1) then
            tabsize1 := Length (ADOQuery1.Fields [1].AsString);

          if (Length (ADOQuery1.Fields [2].AsString) > tabsize2) then
            tabsize2 := Length (ADOQuery1.Fields [2].AsString);
        end;

        ADOQuery1.Next;
      end;

      ADOQuery1.Close;
    except
    end;

    tabs[0] := tabsize1;
    tabs[1] := tabsize2;
    LPListBox.TabWidth := 1;
    { Set the Tabulators / Tabulatoren setzen }
    SendMessage(LPListBox.Handle, LB_SETTABSTOPS, 2, Longint(@tabs));
  finally
    LPListBox.Items.EndUpdate;

    Screen.Cursor := crDefault;
  end;

  if (fLPEmptyAllowd) then
    LPListBox.ItemIndex := 0
  else
    LPListBox.ItemIndex := -1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.ListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
var
  line : String;
  strpos : Integer;
begin
  line := (Control as TListBox).Items [Index];

  with (Control as TListBox).Canvas do begin
    FillRect(Rect);

    strpos := Pos ('|', line);
    if (strpos = 0) then
      TextOut (Rect.Left, Rect.Top, line)
    else begin
      TextOut (Rect.Left, Rect.Top, Copy (line,1,strpos - 1));

      Delete (line, 1, strpos);

      strpos := Pos ('|', line);
      if (strpos = 0) then
        TextOut (Rect.Left + 50, Rect.Top, line)
      else begin
        TextOut (Rect.Left + 50, Rect.Top, Copy (line,1,strpos - 1));
        TextOut (Rect.Left + 200, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.LocComboBoxChange(Sender: TObject);
begin
  if (GetComboBoxRef (LocComboBox) = LVSDatenModul.AktLocationRef) then
    InhousGroupBox.Enabled := True
  else begin
    InhousGroupBox.Enabled := False;

    ClearComboBoxObjects (InHousLBComboBox);
    ClearListBoxObjects  (InhousLPListBox);
  end;

  LoadLagerCombobox (LagerComboBox, GetComboBoxRef (LocComboBox), fRefMand, -1);

  if (LagerComboBox.Items.Count = 1) then begin
    LagerComboBox.Enabled := false;
    LagerComboBox.ItemIndex := 0;
    
    LagerComboBoxChange (Sender);
  end else if (GetComboBoxRef (LocComboBox) = LVSDatenModul.AktLocationRef) then begin
    LagerComboBox.Enabled   := True;
    LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, fRefLager);
  end else begin
    LagerComboBox.Enabled   := True;
    LagerComboBox.ItemIndex := -1;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 31.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.AuslagernTabSheetShow(Sender: TObject);
var
  query : TADOQuery;
begin
  LoadRelationen (AuslagerRelComboBox, 'WA', fRefLager);
  AuslagerRelComboBox.Items.Insert (0, '');

  query    := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQl.Add ('SELECT * FROM V_SPEDITIONEN where STATUS=''ANG'' and ((REF_LAGER is not null and REF_LAGER=:ref_lager) or (REF_LAGER is null and (REF_LOCATION is null or REF_LOCATION=:ref_loc)))');
    query.SQl.Add ('order by REF_MAND nulls last, upper (NAME)');

    if (LVSDatenModul.AktLagerRef > 0) then
      query.Parameters.ParamByName ('ref_lager').Value := LVSDatenModul.AktLagerRef
    else begin
      query.Parameters.ParamByName ('ref_lager').DataType := ftInteger;
      query.Parameters.ParamByName ('ref_lager').Value := NULL;
    end;

    query.Parameters.ParamByName ('ref_loc').Value   := LVSDatenModul.AktLocationRef;

    ClearComboBoxObjects (SpedComboBox);

    try
      query.Open;

      while not (query.Eof) do begin
        SpedComboBox.AddItemIndex (query.FieldByName('NAME').AsString+'|'+query.FieldByName('BESCHREIBUNG').AsString, TComboBoxRef.Create (query.FieldByName('REF').AsInteger));

        query.Next;
      end;

      query.Close;
    except
    end;

    SpedComboBox.Items.Insert(0, GetResourceText (1020));
    SpedComboBox.ItemIndex := 0;
  finally
    query.Free;
  end;

  if (KundenCheckBox.Checked) then begin
    KundenQuery.Close;

    KundenQuery.Open;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.KundenCheckBoxClick(Sender: TObject);
begin
  if (KundenCheckBox.Checked) then begin
    KundenDBGrid.Enabled := True;
    KundenQuery.Open;
  end else begin
    KundenQuery.Close;
    KundenDBGrid.Enabled := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.ExternTabSheetShow(Sender: TObject);
begin
  LoadLocationCombobox (LocComboBox, fRefMand);

  if (LocComboBox.Items.Count = 1) then begin
    LocComboBox.Enabled   := False;
    LocComboBox.ItemIndex := 0;
  end else if (LVSDatenModul.AktLocationRef <> -1) then begin
    LocComboBox.Enabled   := True;
    LocComboBox.ItemIndex := FindComboboxRef (LocComboBox, LVSDatenModul.AktLocationRef);
  end else begin
    LocComboBox.Enabled   := True;
    LocComboBox.ItemIndex := 0;
  end;

  LocComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  KundenQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
 artab   : String;
 res,
 refleab : Integer;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    res := 0;

    refleab := -1;

    if (PageControl1.ActivePage = LocalTabSheet) then begin
      if (LEReferenz > 0) and (GetListBoxRef(LPListBox) > 0) then
        res := CheckLEAbstellen (-1, LEReferenz, GetListBoxRef(LPListBox), fMoveArt, refleab)
    end else if (PageControl1.ActivePage = LocalTabSheet) then begin
      if (LEReferenz > 0) and (GetListBoxRef(InhousLPListBox) > 0) then
        res := CheckLEAbstellen (-1, LEReferenz, GetListBoxRef(InhousLPListBox), fMoveArt, refleab);
    end;

    if (res <> 0) then begin
      CanClose := False;

      MessageDLG (FormatMessageText (1096, [])+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOk], 0);
    end else begin
      CanClose := True;

      fRefZielLE := refleab;

      if (KundenDBGrid.Visible) and (KundenQuery.Active and (KundenQuery.RecNo > 0)) then begin
        fRefWarenEmpf := KundenQuery.FieldByName('REF').AsInteger;
        fRefLieferAdr := KundenQuery.FieldByName('REF_LIEFER_ADR').AsInteger;
      end else begin
        fRefWarenEmpf := -1;
        fRefLieferAdr := -1;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.FormCreate(Sender: TObject);
begin
  fRefZielLE := -1;
  fMoveArt   := '';
  
  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  fRefWarenEmpf := -1;
  fRefLieferAdr := -1;

  fLPEmptyAllowd := False;

  LEReferenz := -1;
  NVEReferenz := -1;

  GrundEdit.Text := '';

  PageControl1.ActivePage := LocalTabSheet;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (LBComboBox);
  ClearComboBoxObjects (LBZoneComboBox);

  ClearComboBoxObjects (InHousLBComboBox);
  ClearComboBoxObjects (InHousLBZoneComboBox);

  ClearComboBoxObjects (AuslagerRelComboBox);

  ClearListBoxObjects (LPListBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.FormPaint(Sender: TObject);
{$ifdef Debug}
  var
    i : Integer;
{$endif}
begin
{$ifdef Debug}
  i := ArtikelListBox.Left;

  while (i < ArtikelListBox.Left + ArtikelListBox.Width) do begin
    Canvas.MoveTo (i,ArtikelListBox.Top - 15);
    Canvas.LineTo (i,ArtikelListBox.Top - 5);

    Inc (i, 20);
  end;
{$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUmlagerForm.ScannerErfassung (var Message: TMessage);
var
  lpstr   : String;
  query   : TADOQuery;
begin
  if CheckLPNrBarcode (ScanCode, lpstr) then begin
    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;
      query.SQL.Add ('select * from V_LP where REF_LAGER=:ref_lager and LP_NR=:lp_nr');
      query.Parameters.ParamByName ('ref_lager').Value := fRefLager;
      query.Parameters.ParamByName ('lp_nr').Value := lpstr;

      query.Open;

      if (query.FieldByName ('REF').IsNull) then
        MessageDlg(FormatMessageText (1100, []), mtError, [mbOk], 0)
      else begin
        LBComboBox.ItemIndex := FindComboboxRef (LBComboBox, query.FieldByName ('REF_LB').AsInteger);
        LBComboBoxChange (Nil);

        LPListBox.ItemIndex := FindListBoxRef(LPListBox, query.FieldByName ('REF').AsInteger)
      end;

      query.Close;
    finally
      query.Free;
    end;
  end;
end;

end.
