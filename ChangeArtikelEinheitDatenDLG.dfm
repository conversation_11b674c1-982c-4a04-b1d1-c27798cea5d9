object ChangeArtikelEinheitDatenForm: TChangeArtikelEinheitDatenForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Artikelstammdaten erg'#228'nzen'
  ClientHeight = 625
  ClientWidth = 363
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object KopfPanel: TPanel
    Left = 0
    Top = 0
    Width = 363
    Height = 69
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      363
      69)
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 48
      Height = 13
      Caption = 'Artikelnr.:'
    end
    object Label3: TLabel
      Left = 8
      Top = 44
      Width = 36
      Height = 13
      Caption = 'Einheit:'
    end
    object ArNrLabel: TLabel
      Left = 104
      Top = 8
      Width = 47
      Height = 13
      Caption = 'ArNrLabel'
    end
    object EinheitLabel: TLabel
      Left = 104
      Top = 44
      Width = 57
      Height = 13
      Caption = 'EinheitLabel'
    end
    object Bevel2: TBevel
      Left = 8
      Top = 64
      Width = 347
      Height = 8
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 49
    end
    object ArTextLabel: TLabel
      Left = 104
      Top = 24
      Width = 58
      Height = 13
      Caption = 'ArTextLabel'
    end
  end
  object AbmessungPanel: TPanel
    Left = 0
    Top = 69
    Width = 363
    Height = 112
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      363
      112)
    object Label15: TLabel
      Left = 168
      Top = 28
      Width = 55
      Height = 13
      Caption = 'in Millimeter'
    end
    object Label9: TLabel
      Left = 120
      Top = 9
      Width = 25
      Height = 13
      Caption = 'H'#246'he'
    end
    object Label8: TLabel
      Left = 64
      Top = 9
      Width = 28
      Height = 13
      Caption = 'Breite'
    end
    object Label7: TLabel
      Left = 8
      Top = 9
      Width = 29
      Height = 13
      Caption = 'L'#228'nge'
    end
    object Label13: TLabel
      Left = 85
      Top = 76
      Width = 22
      Height = 13
      Caption = 'in kg'
    end
    object Label14: TLabel
      Left = 197
      Top = 76
      Width = 22
      Height = 13
      Caption = 'in kg'
    end
    object Label6: TLabel
      Left = 120
      Top = 57
      Width = 67
      Height = 13
      Caption = 'Bruttogewicht'
    end
    object Label4: TLabel
      Left = 8
      Top = 57
      Width = 64
      Height = 13
      Caption = 'Nettogewicht'
    end
    object Bevel5: TBevel
      Left = 8
      Top = 107
      Width = 347
      Height = 8
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object HoeheEdit: TEdit
      Left = 120
      Top = 25
      Width = 40
      Height = 21
      MaxLength = 4
      TabOrder = 1
      Text = 'HoeheEdit'
      OnKeyPress = DimEditKeyPress
    end
    object BreiteEdit: TEdit
      Left = 64
      Top = 25
      Width = 40
      Height = 21
      MaxLength = 4
      TabOrder = 2
      Text = 'BreiteEdit'
    end
    object LaengeEdit: TEdit
      Left = 8
      Top = 25
      Width = 40
      Height = 21
      MaxLength = 4
      TabOrder = 0
      Text = 'LaengeEdit'
      OnKeyPress = DimEditKeyPress
    end
    object BruttoEdit: TEdit
      Left = 120
      Top = 73
      Width = 70
      Height = 21
      TabOrder = 4
      Text = 'BruttoEdit'
      OnExit = GewichtEditExit
    end
    object NettoEdit: TEdit
      Left = 8
      Top = 73
      Width = 70
      Height = 21
      TabOrder = 3
      Text = 'NettoEdit'
      OnExit = GewichtEditExit
      OnKeyPress = FloatEditKeyPress
    end
  end
  object PalPanel: TPanel
    Left = 0
    Top = 181
    Width = 363
    Height = 61
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      363
      61)
    object Bevel3: TBevel
      Left = 8
      Top = 56
      Width = 347
      Height = 8
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label21: TLabel
      Left = 114
      Top = 5
      Width = 69
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'Paletten-H'#246'he'
    end
    object Label2: TLabel
      Left = 179
      Top = 24
      Width = 27
      Height = 13
      Caption = 'in mm'
    end
    object Label11: TLabel
      Left = 8
      Top = 5
      Width = 100
      Height = 13
      Caption = 'Einheiten pro Palette'
    end
    object PalHeightEdit: TEdit
      Left = 121
      Top = 21
      Width = 50
      Height = 21
      Anchors = [akTop, akRight]
      MaxLength = 4
      TabOrder = 1
      Text = 'PalHeightEdit'
      OnKeyPress = IntEditKeyPress
    end
    object PalFaktorEdit: TEdit
      Left = 8
      Top = 21
      Width = 57
      Height = 21
      MaxLength = 5
      TabOrder = 0
      Text = 'PalFaktorEdit'
      OnKeyPress = IntEditKeyPress
    end
  end
  object AttrPanel: TPanel
    Left = 0
    Top = 242
    Width = 363
    Height = 82
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      363
      82)
    object Bevel4: TBevel
      Left = 8
      Top = 77
      Width = 347
      Height = 8
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object ReadyShipCheckBox: TCheckBox
      Left = 8
      Top = 28
      Width = 340
      Height = 17
      Caption = 'Die Einheit ist versandf'#228'hig verpackt'
      TabOrder = 1
    end
    object SperrgutCheckBox: TCheckBox
      Left = 8
      Top = 51
      Width = 340
      Height = 17
      Caption = 'Die Einheit muss als Sperrgut verschickt werden'
      TabOrder = 2
    end
    object BigItemCheckBox: TCheckBox
      Left = 8
      Top = 6
      Width = 340
      Height = 17
      Caption = 'Big Item'
      TabOrder = 0
    end
  end
  object BarcodePanel: TPanel
    Left = 0
    Top = 324
    Width = 363
    Height = 53
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      363
      53)
    object Label5: TLabel
      Left = 8
      Top = 1
      Width = 20
      Height = 13
      Caption = 'EAN'
    end
    object Label22: TLabel
      Left = 120
      Top = 2
      Width = 39
      Height = 13
      Caption = 'Barcode'
    end
    object Bevel6: TBevel
      Left = 8
      Top = 47
      Width = 347
      Height = 8
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 61
    end
    object EANEdit: TEdit
      Left = 8
      Top = 17
      Width = 96
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = '99999999999999'
      OnExit = EANEditExit
      OnKeyPress = EANEditKeyPress
    end
    object BarcodeEdit: TEdit
      Left = 120
      Top = 17
      Width = 235
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 1
      Text = 'Edit'
    end
  end
  object ShipperPanel: TPanel
    Left = 0
    Top = 377
    Width = 363
    Height = 53
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      363
      53)
    object Bevel1: TBevel
      Left = 8
      Top = 48
      Width = 347
      Height = 12
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label56: TLabel
      Left = 8
      Top = 3
      Width = 44
      Height = 13
      Caption = 'Spedition'
    end
    object VersandSpedComboBox: TComboBoxPro
      Left = 8
      Top = 19
      Width = 347
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 0
    end
  end
  object FussPanel: TPanel
    Left = 0
    Top = 584
    Width = 363
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 7
    ExplicitTop = 530
    DesignSize = (
      363
      41)
    object OkButton: TButton
      Left = 199
      Top = 8
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = #220'bernehmen'
      ModalResult = 1
      TabOrder = 0
    end
    object CancleButton: TButton
      Left = 280
      Top = 8
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 2
      TabOrder = 1
    end
  end
  object GefahrstoffPanel: TPanel
    Left = 0
    Top = 483
    Width = 363
    Height = 101
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 6
    ExplicitTop = 430
    DesignSize = (
      363
      101)
    object Bevel7: TBevel
      Left = 8
      Top = 99
      Width = 347
      Height = 12
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 139
    end
    object Label41: TLabel
      Left = 8
      Top = 7
      Width = 83
      Height = 13
      Caption = 'Gefahrgut-Stoffe'
    end
    object GefahrStoffeCheckListBox: TCheckListBox
      Left = 8
      Top = 23
      Width = 347
      Height = 70
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 13
      Style = lbOwnerDrawFixed
      TabOrder = 0
      TabWidth = 100
      OnDrawItem = IdentListBoxDrawItem
    end
  end
  object KlassePanel: TPanel
    Left = 0
    Top = 430
    Width = 363
    Height = 53
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 8
    ExplicitLeft = -8
    ExplicitTop = 423
    DesignSize = (
      363
      53)
    object Bevel8: TBevel
      Left = 8
      Top = 48
      Width = 347
      Height = 12
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label10: TLabel
      Left = 8
      Top = 3
      Width = 64
      Height = 13
      Caption = 'Artikel-Klasse'
    end
    object KlasseComboBox: TComboBoxPro
      Left = 8
      Top = 19
      Width = 347
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 0
    end
  end
end
