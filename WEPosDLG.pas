//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : WEPosDLG
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /Zimbo/LVS/FrontEnd/WEPosDLG.pas $
// $Revision: 5 $
// $Modtime: 30.09.04 9:52 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Anzeigen der Wareneingangspositionen
//*****************************************************************************
unit WEPosDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, Grids, DBGrids, SMDBGrid, DBGridPro, ExtCtrls,
  StdCtrls, CompTranslate;

type
  TWEPosForm = class(TForm)
    Panel1: TPanel;
    Panel2: TPanel;
    DBGridPro1: TDBGridPro;
    ADOQuery1: TADOQuery;
    DataSource1: TDataSource;
    Button1: TButton;
    CompTranslateForm1: TCompTranslateForm;
    Label1: TLabel;
    HACCPDBGrid: TDBGridPro;
    GroupBox1: TGroupBox;
    HACCPMemo: TMemo;
    HACCPEdit: TEdit;
    ADOQuery2: TADOQuery;
    DataSource2: TDataSource;
    HACCPCheckBox: TCheckBox;
    HACCPRadioGroup: TRadioGroup;
    HACCPLabel: TLabel;
    Label2: TLabel;
    procedure FormShow(Sender: TObject);
    procedure FormHide(Sender: TObject);
    procedure FormKeyPress(Sender: TObject; var Key: Char);
    procedure DataSource2DataChange(Sender: TObject; Field: TField);
  private
    { Private-Deklarationen }
  public
    WEReferenz : Integer;
  end;

var
  WEPosForm: TWEPosForm;

implementation

uses StringUtils, DatenModul, SprachModul, ConfigModul;

{$R *.dfm}

procedure TWEPosForm.FormShow(Sender: TObject);
begin
  LVSConfigModul.RestoreFormInfo (Self);

  HACCPLabel.Caption := '';
  HACCPRadioGroup.Visible := False;
  HACCPCheckBox.Visible := False;
  HACCPEdit.Visible := False;
  HACCPMemo.Visible := False;

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select * from v_we_pos where REF_WE='+IntToStr (WEReferenz));
  ADOQuery1.Open;

  ADOQuery1.FieldByName ('REF').Visible := False;
  ADOQuery1.FieldByName ('REF_WE').Visible := False;

  ADOQuery2.SQL.Clear;
  ADOQuery2.SQL.Add ('select REF, TESTER, VORGANG, BEZEICHNUNG, RESULT, TEXT, ERGEBNIS from V_HACCP_ERGEBNISSE where REF_DATEN='+#39+IntToStr (WEReferenz)+#39);
  ADOQuery2.Open;

  ADOQuery2.FieldByName ('REF').Visible := False;
  ADOQuery2.FieldByName ('RESULT').Visible := False;
  ADOQuery2.FieldByName ('TEXT').Visible := False;
  ADOQuery2.FieldByName ('ERGEBNIS').Visible := False;
end;

procedure TWEPosForm.FormHide(Sender: TObject);
begin
  ADOQuery2.Close;
  ADOQuery1.Close;

  LVSConfigModul.SaveFormInfo (Self);
end;

procedure TWEPosForm.FormKeyPress(Sender: TObject; var Key: Char);
begin
  if (Key = Chr (VK_ESCAPE)) then
    ModalResult := mrAbort;
end;

procedure TWEPosForm.DataSource2DataChange(Sender: TObject; Field: TField);
begin
  HACCPRadioGroup.Visible := False;
  HACCPCheckBox.Visible := False;
  HACCPEdit.Visible := False;
  HACCPMemo.Visible := False;

  if (ADOQuery2.Active) then begin
    HACCPLabel.Caption := StrConvertControlChars (ADOQuery2.FieldByName('TEXT').AsString);

    if (ADOQuery2.FieldByName('RESULT').AsString = 'CHECK') then begin
      HACCPCheckBox.Visible := True;
      HACCPCheckBox.Checked := ADOQuery2.FieldByName('ERGEBNIS').AsString = '1';
    end else if (ADOQuery2.FieldByName('RESULT').AsString = 'YESNO') then begin
      HACCPRadioGroup.Visible := True;
      if (ADOQuery2.FieldByName('ERGEBNIS').AsString = 'Y') then
        HACCPRadioGroup.ItemIndex := 0
      else HACCPRadioGroup.ItemIndex := 1;
    end else if (ADOQuery2.FieldByName('RESULT').AsString = 'TEXT') then begin
      HACCPEdit.Visible := True;
      HACCPEdit.Text := ADOQuery2.FieldByName('ERGEBNIS').AsString;
    end else if (ADOQuery2.FieldByName('RESULT').AsString = 'MEMO') then begin
      HACCPMemo.Visible := True;
      HACCPMemo.Text := ADOQuery2.FieldByName('ERGEBNIS').AsString;
    end;
  end;
end;

end.
