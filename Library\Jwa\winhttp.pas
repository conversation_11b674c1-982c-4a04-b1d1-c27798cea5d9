
unit winhttp;
// implementation of Winhttp.  Written by <PERSON><PERSON><PERSON>9<PERSON> at tek-tips.com

interface
  uses windows;
  const
    winhttpapi = 'WINHTTP.DLL';
   // constant strings for WinHTTP specific error messages, used with WinHttpSysErrorMessage.
    err_12001 = 'Out of handles.';
    err_12002 = 'Time out.';
    err_12004 = 'Internal error.';
    err_12005 = 'Invalid URL.';
    err_12006 = 'Unrecognized Scheme.';
    err_12007 = 'Name not resolved.';
    err_12009 = 'Invalid option.';
    err_12011 = 'Option not settable.';
    err_12012 = 'Shutdown.';
    err_12015 = 'Login failure.';
    err_12017 = 'Operation cancelled.';
    err_12018 = 'Incorrect handle type.';
    err_12019 = 'Incorrect handle state.';
    err_12029 = 'Can not connect.';
    err_12030 = 'Connection error.';
    err_12032 = 'Resend request.';
    err_12044 = 'Client auth cert needed.';
    err_12100 = 'Can not call before open.';
    err_12101 = 'Can not call before send.';
    err_12102 = 'Can not call after send.';
    err_12103 = 'Can not call after open.';
    err_12150 = 'Header not found.';
    err_12152 = 'Invalid Server Response.';
    err_12154 = 'Invalid query request.';
    err_12155 = 'Header already exists.';
    err_12156 = 'Redirect failed.';
    err_12178 = 'Auto proxy service error.';
    err_12166 = 'Bad auto proxy script.';
    err_12167 = 'Unable to download Script.';
    err_12172 = 'Not initialized.';
    err_12175 = 'Secure Failure.';
    err_12037 = 'Secure Cert Date Invalid.';
    err_12038 = 'Secure Cert CN Invalid.';
    err_12045 = 'Secure Invalid CA.';
    err_12057 = 'Secure Cert Rev Failed.';
    err_12157 = 'Secure Channel Error.';
    err_12169 = 'Secure Invalid Cert.';
    err_12170 = 'Secure Cert Revoked.';
    err_12179 = 'Secure Cert Wrong Usage.';
    err_12180 = 'Auto Detection Failed.';
    err_12181 = 'Header Count Exceeded.';
    err_12182 = 'Header Size Overflow.';
    err_12183 = 'Chunked Encoding Header Size Overflow.';
    err_12184 = 'Response Drain Overflow.';
//-----------------------------------------------------------------------------
   // values for dwModifiers parameter of WinHttpAddRequestHeaders()
    WINHTTP_ADDREQ_INDEX_MASK = $0000FFFF;
    WINHTTP_ADDREQ_FLAGS_MASK = $FFFF0000;
    WINHTTP_ADDREQ_FLAG_ADD_IF_NEW = $10000000;
    WINHTTP_ADDREQ_FLAG_ADD = $20000000;
    WINHTTP_ADDREQ_FLAG_COALESCE_WITH_COMMA = $40000000;
    WINHTTP_ADDREQ_FLAG_COALESCE_WITH_SEMICOLON = $01000000;
    WINHTTP_ADDREQ_FLAG_COALESCE = WINHTTP_ADDREQ_FLAG_COALESCE_WITH_COMMA;
    WINHTTP_ADDREQ_FLAG_REPLACE = $80000000;

    // WinHttpSendRequest prettifiers for optional parameters.
    WINHTTP_NO_ADDITIONAL_HEADERS = nil;
    WINHTTP_NO_REQUEST_DATA = nil;
    // WinHttpQueryHeaders prettifiers for optional parameters.
    WINHTTP_HEADER_NAME_BY_INDEX = nil;
    WINHTTP_NO_OUTPUT_BUFFER = nil;
    WINHTTP_NO_HEADER_INDEX = nil;
    // WinHttpOpenRequest prettifers for optional parameters
    WINHTTP_NO_REFERER = nil;
    WINHTTP_DEFAULT_ACCEPT_TYPES = nil;

   // WinHttp API error returns
    WINHTTP_ERROR_BASE = 12000;
    ERROR_WINHTTP_OUT_OF_HANDLES = (WINHTTP_ERROR_BASE + 1);
    ERROR_WINHTTP_TIMEOUT = (WINHTTP_ERROR_BASE + 2);
    ERROR_WINHTTP_INTERNAL_ERROR = (WINHTTP_ERROR_BASE + 4);
    ERROR_WINHTTP_INVALID_URL = (WINHTTP_ERROR_BASE + 5);
    ERROR_WINHTTP_UNRECOGNIZED_SCHEME = (WINHTTP_ERROR_BASE + 6);
    ERROR_WINHTTP_NAME_NOT_RESOLVED = (WINHTTP_ERROR_BASE + 7);
    ERROR_WINHTTP_INVALID_OPTION = (WINHTTP_ERROR_BASE + 9);
    ERROR_WINHTTP_OPTION_NOT_SETTABLE = (WINHTTP_ERROR_BASE + 11);
    ERROR_WINHTTP_SHUTDOWN = (WINHTTP_ERROR_BASE + 12);

    ERROR_WINHTTP_LOGIN_FAILURE = (WINHTTP_ERROR_BASE + 15);
    ERROR_WINHTTP_OPERATION_CANCELLED = (WINHTTP_ERROR_BASE + 17);
    ERROR_WINHTTP_INCORRECT_HANDLE_TYPE = (WINHTTP_ERROR_BASE + 18);
    ERROR_WINHTTP_INCORRECT_HANDLE_STATE = (WINHTTP_ERROR_BASE + 19);
    ERROR_WINHTTP_CANNOT_CONNECT = (WINHTTP_ERROR_BASE + 29);
    ERROR_WINHTTP_CONNECTION_ERROR = (WINHTTP_ERROR_BASE + 30);
    ERROR_WINHTTP_RESEND_REQUEST = (WINHTTP_ERROR_BASE + 32);

    ERROR_WINHTTP_CLIENT_AUTH_CERT_NEEDED = (WINHTTP_ERROR_BASE + 44);

   // WinHttpRequest Component errors
    ERROR_WINHTTP_CANNOT_CALL_BEFORE_OPEN = (WINHTTP_ERROR_BASE + 100);
    ERROR_WINHTTP_CANNOT_CALL_BEFORE_SEND = (WINHTTP_ERROR_BASE + 101);
    ERROR_WINHTTP_CANNOT_CALL_AFTER_SEND = (WINHTTP_ERROR_BASE + 102);
    ERROR_WINHTTP_CANNOT_CALL_AFTER_OPEN = (WINHTTP_ERROR_BASE + 103);

    // HTTP API errors
    ERROR_WINHTTP_HEADER_NOT_FOUND = (WINHTTP_ERROR_BASE + 150);
    ERROR_WINHTTP_INVALID_SERVER_RESPONSE = (WINHTTP_ERROR_BASE + 152);
    ERROR_WINHTTP_INVALID_QUERY_REQUEST = (WINHTTP_ERROR_BASE + 154);
    ERROR_WINHTTP_HEADER_ALREADY_EXISTS = (WINHTTP_ERROR_BASE + 155);
    ERROR_WINHTTP_REDIRECT_FAILED = (WINHTTP_ERROR_BASE + 156);

    // additional WinHttp API error codes
    ERROR_WINHTTP_AUTO_PROXY_SERVICE_ERROR = (WINHTTP_ERROR_BASE + 178);
    ERROR_WINHTTP_BAD_AUTO_PROXY_SCRIPT = (WINHTTP_ERROR_BASE + 166);
    ERROR_WINHTTP_UNABLE_TO_DOWNLOAD_SCRIPT = (WINHTTP_ERROR_BASE + 167);

    ERROR_WINHTTP_NOT_INITIALIZED = (WINHTTP_ERROR_BASE + 172);
    ERROR_WINHTTP_SECURE_FAILURE = (WINHTTP_ERROR_BASE + 175);

    ERROR_WINHTTP_SECURE_CERT_DATE_INVALID = (WINHTTP_ERROR_BASE + 37);
    ERROR_WINHTTP_SECURE_CERT_CN_INVALID = (WINHTTP_ERROR_BASE + 38);
    ERROR_WINHTTP_SECURE_INVALID_CA = (WINHTTP_ERROR_BASE + 45);
    ERROR_WINHTTP_SECURE_CERT_REV_FAILED = (WINHTTP_ERROR_BASE + 57);
    ERROR_WINHTTP_SECURE_CHANNEL_ERROR = (WINHTTP_ERROR_BASE + 157);
    ERROR_WINHTTP_SECURE_INVALID_CERT = (WINHTTP_ERROR_BASE + 169);
    ERROR_WINHTTP_SECURE_CERT_REVOKED = (WINHTTP_ERROR_BASE + 170);
    ERROR_WINHTTP_SECURE_CERT_WRONG_USAGE = (WINHTTP_ERROR_BASE + 179);

    ERROR_WINHTTP_AUTODETECTION_FAILED = (WINHTTP_ERROR_BASE + 180);
    ERROR_WINHTTP_HEADER_COUNT_EXCEEDED = (WINHTTP_ERROR_BASE + 181);
    ERROR_WINHTTP_HEADER_SIZE_OVERFLOW = (WINHTTP_ERROR_BASE + 182);
    ERROR_WINHTTP_CHUNKED_ENCODING_HEADER_SIZE_OVERFLOW = (WINHTTP_ERROR_BASE + 183);
    ERROR_WINHTTP_RESPONSE_DRAIN_OVERFLOW = (WINHTTP_ERROR_BASE + 184);

    WINHTTP_ERROR_LAST = (WINHTTP_ERROR_BASE + 184);

    INTERNET_DEFAULT_PORT =  0 ;          // use the protocol-specific default
    INTERNET_DEFAULT_HTTP_PORT = 80;      //    "     "  HTTP   "
    INTERNET_DEFAULT_HTTPS_PORT = 443;    //    "     "  HTTPS  "

    // flags for WinHttpOpen():
    WINHTTP_FLAG_ASYNC = $10000000;   // this session is asynchronous (where supported)

    // flags for WinHttpOpenRequest():
    WINHTTP_FLAG_SECURE = $00800000;  // use SSL if applicable (HTTPS)
    WINHTTP_FLAG_ESCAPE_PERCENT = $00000004;  // if escaping enabled, escape percent as well
    WINHTTP_FLAG_NULL_CODEPAGE = $00000008; // assume all symbols are ASCII, use fast convertion
    WINHTTP_FLAG_BYPASS_PROXY_CACHE = $00000100; // add "pragma: no-cache" request header
    WINHTTP_FLAG_REFRESH = WINHTTP_FLAG_BYPASS_PROXY_CACHE;
    WINHTTP_FLAG_ESCAPE_DISABLE = $00000040;  // disable escaping
    WINHTTP_FLAG_ESCAPE_DISABLE_QUERY = $00000080;  // if escaping enabled escape path part, but do not escape query

    SECURITY_FLAG_IGNORE_UNKNOWN_CA = $00000100;
    SECURITY_FLAG_IGNORE_CERT_DATE_INVALID = $00002000; // expired X509 Cert.
    SECURITY_FLAG_IGNORE_CERT_CN_INVALID = $00001000; // bad common name in X509 Cert.
    SECURITY_FLAG_IGNORE_CERT_WRONG_USAGE = $00000200;

    WINHTTP_AUTOPROXY_AUTO_DETECT = $00000001;
    WINHTTP_AUTOPROXY_CONFIG_URL = $00000002;
    WINHTTP_AUTOPROXY_RUN_INPROCESS = $00010000;
    WINHTTP_AUTOPROXY_RUN_OUTPROCESS_ONLY = $00020000;

    // Flags for dwAutoDetectFlags
    WINHTTP_AUTO_DETECT_TYPE_DHCP = $00000001;
    WINHTTP_AUTO_DETECT_TYPE_DNS_A = $00000002;

    // constants for WinHttpTimeFromSystemTime
    WINHTTP_TIME_FORMAT_BUFSIZE = 62;

    // flags for CrackUrl() and CombineUrl()
    ICU_NO_ENCODE = $20000000;  // Don't convert unsafe characters to escape sequence
    ICU_DECODE = $10000000;  // Convert %XX escape sequences to characters
    ICU_NO_META = $08000000;  // Don't convert .. etc. meta path sequences
    ICU_ENCODE_SPACES_ONLY = $04000000;  // Encode spaces only
    ICU_BROWSER_MODE = $02000000; // Special encode/decode rules for browser
    ICU_ENCODE_PERCENT = $00001000;      // Encode any percent (ASCII25)
        // signs encountered, default is to not encode percent.

    // flags for WinHttpCrackUrl() and WinHttpCreateUrl()
    ICU_ESCAPE = $80000000;  // (un)escape URL characters

    // WinHttpOpen dwAccessType values (also for WINHTTP_PROXY_INFO::dwAccessType)
    WINHTTP_ACCESS_TYPE_DEFAULT_PROXY = 0;
    WINHTTP_ACCESS_TYPE_NO_PROXY = 1;
    WINHTTP_ACCESS_TYPE_NAMED_PROXY = 3;

    // WinHttpOpen prettifiers for optional parameters
    WINHTTP_NO_PROXY_NAME = nil;
    WINHTTP_NO_PROXY_BYPASS = nil;

    // options manifests for WinHttp{Query|Set}Option
    WINHTTP_OPTION_CALLBACK = 1;
    WINHTTP_FIRST_OPTION = WINHTTP_OPTION_CALLBACK;
    WINHTTP_OPTION_RESOLVE_TIMEOUT = 2;
    WINHTTP_OPTION_CONNECT_TIMEOUT = 3;
    WINHTTP_OPTION_CONNECT_RETRIES = 4;
    WINHTTP_OPTION_SEND_TIMEOUT = 5;
    WINHTTP_OPTION_RECEIVE_TIMEOUT = 6;
    WINHTTP_OPTION_RECEIVE_RESPONSE_TIMEOUT = 7;
    WINHTTP_OPTION_HANDLE_TYPE = 9;
    WINHTTP_OPTION_READ_BUFFER_SIZE = 12;
    WINHTTP_OPTION_WRITE_BUFFER_SIZE = 13;
    WINHTTP_OPTION_PARENT_HANDLE = 21;
    WINHTTP_OPTION_EXTENDED_ERROR = 24;
    WINHTTP_OPTION_SECURITY_FLAGS = 31;
    WINHTTP_OPTION_SECURITY_CERTIFICATE_STRUCT = 32;
    WINHTTP_OPTION_URL = 34;
    WINHTTP_OPTION_SECURITY_KEY_BITNESS = 36;
    WINHTTP_OPTION_PROXY = 38;

    WINHTTP_OPTION_USER_AGENT = 41;
    WINHTTP_OPTION_CONTEXT_VALUE = 45;
    WINHTTP_OPTION_CLIENT_CERT_CONTEXT = 47;
    WINHTTP_OPTION_REQUEST_PRIORITY = 58;
    WINHTTP_OPTION_HTTP_VERSION = 59;
    WINHTTP_OPTION_DISABLE_FEATURE = 63;
    WINHTTP_OPTION_CODEPAGE = 68;
    WINHTTP_OPTION_MAX_CONNS_PER_SERVER = 73;
    WINHTTP_OPTION_MAX_CONNS_PER_1_0_SERVER = 74;
    WINHTTP_OPTION_AUTOLOGON_POLICY = 77;
    WINHTTP_OPTION_SERVER_CERT_CONTEXT = 78;
    WINHTTP_OPTION_ENABLE_FEATURE = 79;
    WINHTTP_OPTION_WORKER_THREAD_COUNT = 80;
    WINHTTP_OPTION_PASSPORT_COBRANDING_TEXT = 81;
    WINHTTP_OPTION_PASSPORT_COBRANDING_URL = 82;
    WINHTTP_OPTION_CONFIGURE_PASSPORT_AUTH = 83;
    WINHTTP_OPTION_SECURE_PROTOCOLS = 84;
    WINHTTP_OPTION_ENABLETRACING = 85;
    WINHTTP_OPTION_PASSPORT_SIGN_OUT = 86;
    WINHTTP_OPTION_PASSPORT_RETURN_URL = 87;
    WINHTTP_OPTION_REDIRECT_POLICY = 88;
    WINHTTP_OPTION_MAX_HTTP_AUTOMATIC_REDIRECTS =  89;
    WINHTTP_OPTION_MAX_HTTP_STATUS_CONTINUE = 90;
    WINHTTP_OPTION_MAX_RESPONSE_HEADER_SIZE = 91;
    WINHTTP_OPTION_MAX_RESPONSE_DRAIN_SIZE = 92;
    WINHTTP_LAST_OPTION = WINHTTP_OPTION_MAX_RESPONSE_DRAIN_SIZE;

    WINHTTP_OPTION_USERNAME = $1000;
    WINHTTP_OPTION_PASSWORD = $1001;
    WINHTTP_OPTION_PROXY_USERNAME = $1002;
    WINHTTP_OPTION_PROXY_PASSWORD = $1003;

    // manifest value for WINHTTP_OPTION_MAX_CONNS_PER_SERVER and
    // WINHTTP_OPTION_MAX_CONNS_PER_1_0_SERVER
    WINHTTP_CONNS_PER_SERVER_UNLIMITED = $FFFFFFFF;

    // values for WINHTTP_OPTION_AUTOLOGON_POLICY
    WINHTTP_AUTOLOGON_SECURITY_LEVEL_MEDIUM = 0;
    WINHTTP_AUTOLOGON_SECURITY_LEVEL_LOW = 1;
    WINHTTP_AUTOLOGON_SECURITY_LEVEL_HIGH = 2;
    WINHTTP_AUTOLOGON_SECURITY_LEVEL_DEFAULT = WINHTTP_AUTOLOGON_SECURITY_LEVEL_MEDIUM;

    // values for WINHTTP_OPTION_REDIRECT_POLICY
    WINHTTP_OPTION_REDIRECT_POLICY_NEVER = 0;
    WINHTTP_OPTION_REDIRECT_POLICY_DISALLOW_HTTPS_TO_HTTP = 1;
    WINHTTP_OPTION_REDIRECT_POLICY_ALWAYS = 2;
    WINHTTP_OPTION_REDIRECT_POLICY_LAST = WINHTTP_OPTION_REDIRECT_POLICY_ALWAYS;
    WINHTTP_OPTION_REDIRECT_POLICY_DEFAULT =
                       WINHTTP_OPTION_REDIRECT_POLICY_DISALLOW_HTTPS_TO_HTTP;

    WINHTTP_DISABLE_PASSPORT_AUTH = $00000000;
    WINHTTP_ENABLE_PASSPORT_AUTH  = $10000000;
    WINHTTP_DISABLE_PASSPORT_KEYRING = $20000000;
    WINHTTP_ENABLE_PASSPORT_KEYRING = $40000000;

    // values for WINHTTP_OPTION_DISABLE_FEATURE
    WINHTTP_DISABLE_COOKIES = $00000001;
    WINHTTP_DISABLE_REDIRECTS = $00000002;
    WINHTTP_DISABLE_AUTHENTICATION = $00000004;
    WINHTTP_DISABLE_KEEP_ALIVE = $00000008;

    // values for WINHTTP_OPTION_ENABLE_FEATURE
    WINHTTP_ENABLE_SSL_REVOCATION = $00000001;
    WINHTTP_ENABLE_SSL_REVERT_IMPERSONATION = $00000002;

    // winhttp handle types
    WINHTTP_HANDLE_TYPE_SESSION = 1;
    WINHTTP_HANDLE_TYPE_CONNECT = 2;
    WINHTTP_HANDLE_TYPE_REQUEST = 3;

    // values for auth schemes
    WINHTTP_AUTH_SCHEME_BASIC = $00000001;
    WINHTTP_AUTH_SCHEME_NTLM = $00000002;
    WINHTTP_AUTH_SCHEME_PASSPORT = $00000004;
    WINHTTP_AUTH_SCHEME_DIGEST = $00000008;
    WINHTTP_AUTH_SCHEME_NEGOTIATE = $00000010;

    // WinHttp supported Authentication Targets
    WINHTTP_AUTH_TARGET_SERVER = $00000000;
    WINHTTP_AUTH_TARGET_PROXY = $00000001;

    // values for WINHTTP_OPTION_SECURITY_FLAGS
    // query only
    SECURITY_FLAG_SECURE = $00000001; // can query only
    SECURITY_FLAG_STRENGTH_WEAK = $10000000;
    SECURITY_FLAG_STRENGTH_MEDIUM = $40000000;
    SECURITY_FLAG_STRENGTH_STRONG = $20000000;

    // Secure connection error status flags
    WINHTTP_CALLBACK_STATUS_FLAG_CERT_REV_FAILED = $00000001;
    WINHTTP_CALLBACK_STATUS_FLAG_INVALID_CERT = $00000002;
    WINHTTP_CALLBACK_STATUS_FLAG_CERT_REVOKED = $00000004;
    WINHTTP_CALLBACK_STATUS_FLAG_INVALID_CA = $00000008;
    WINHTTP_CALLBACK_STATUS_FLAG_CERT_CN_INVALID = $00000010;
    WINHTTP_CALLBACK_STATUS_FLAG_CERT_DATE_INVALID = $00000020;
    WINHTTP_CALLBACK_STATUS_FLAG_CERT_WRONG_USAGE = $00000040;
    WINHTTP_CALLBACK_STATUS_FLAG_SECURITY_CHANNEL_ERROR = $80000000;

    WINHTTP_FLAG_SECURE_PROTOCOL_SSL2 = $00000008;
    WINHTTP_FLAG_SECURE_PROTOCOL_SSL3 = $00000020;
    WINHTTP_FLAG_SECURE_PROTOCOL_TLS1 = $00000080;
    WINHTTP_FLAG_SECURE_PROTOCOL_ALL =
      WINHTTP_FLAG_SECURE_PROTOCOL_SSL2 or WINHTTP_FLAG_SECURE_PROTOCOL_SSL3
         or WINHTTP_FLAG_SECURE_PROTOCOL_TLS1;

    // status manifests for WinHttp status callback
    WINHTTP_CALLBACK_STATUS_RESOLVING_NAME = $00000001;
    WINHTTP_CALLBACK_STATUS_NAME_RESOLVED = $00000002;
    WINHTTP_CALLBACK_STATUS_CONNECTING_TO_SERVER = $00000004;
    WINHTTP_CALLBACK_STATUS_CONNECTED_TO_SERVER = $00000008;
    WINHTTP_CALLBACK_STATUS_SENDING_REQUEST = $00000010;
    WINHTTP_CALLBACK_STATUS_REQUEST_SENT = $00000020;
    WINHTTP_CALLBACK_STATUS_RECEIVING_RESPONSE = $00000040;
    WINHTTP_CALLBACK_STATUS_RESPONSE_RECEIVED = $00000080;
    WINHTTP_CALLBACK_STATUS_CLOSING_CONNECTION = $00000100;
    WINHTTP_CALLBACK_STATUS_CONNECTION_CLOSED = $00000200;
    WINHTTP_CALLBACK_STATUS_HANDLE_CREATED = $00000400;
    WINHTTP_CALLBACK_STATUS_HANDLE_CLOSING = $00000800;
    WINHTTP_CALLBACK_STATUS_DETECTING_PROXY = $00001000;
    WINHTTP_CALLBACK_STATUS_REDIRECT = $00004000;
    WINHTTP_CALLBACK_STATUS_INTERMEDIATE_RESPONSE = $00008000;
    WINHTTP_CALLBACK_STATUS_SECURE_FAILURE = $00010000;
    WINHTTP_CALLBACK_STATUS_HEADERS_AVAILABLE = $00020000;
    WINHTTP_CALLBACK_STATUS_DATA_AVAILABLE = $00040000;
    WINHTTP_CALLBACK_STATUS_READ_COMPLETE = $00080000;
    WINHTTP_CALLBACK_STATUS_WRITE_COMPLETE = $00100000;
    WINHTTP_CALLBACK_STATUS_REQUEST_ERROR = $00200000;
    WINHTTP_CALLBACK_STATUS_SENDREQUEST_COMPLETE = $00400000;

    WINHTTP_CALLBACK_FLAG_RESOLVE_NAME =
     (WINHTTP_CALLBACK_STATUS_RESOLVING_NAME or WINHTTP_CALLBACK_STATUS_NAME_RESOLVED);
    WINHTTP_CALLBACK_FLAG_CONNECT_TO_SERVER =
     (WINHTTP_CALLBACK_STATUS_CONNECTING_TO_SERVER or
      WINHTTP_CALLBACK_STATUS_CONNECTED_TO_SERVER);
    WINHTTP_CALLBACK_FLAG_SEND_REQUEST =
     (WINHTTP_CALLBACK_STATUS_SENDING_REQUEST or
      WINHTTP_CALLBACK_STATUS_REQUEST_SENT);
    WINHTTP_CALLBACK_FLAG_RECEIVE_RESPONSE =
     (WINHTTP_CALLBACK_STATUS_RECEIVING_RESPONSE or
      WINHTTP_CALLBACK_STATUS_RESPONSE_RECEIVED);
    WINHTTP_CALLBACK_FLAG_CLOSE_CONNECTION =
     (WINHTTP_CALLBACK_STATUS_CLOSING_CONNECTION or
      WINHTTP_CALLBACK_STATUS_CONNECTION_CLOSED);
    WINHTTP_CALLBACK_FLAG_HANDLES =
     (WINHTTP_CALLBACK_STATUS_HANDLE_CREATED or
      WINHTTP_CALLBACK_STATUS_HANDLE_CLOSING);
    WINHTTP_CALLBACK_FLAG_DETECTING_PROXY = WINHTTP_CALLBACK_STATUS_DETECTING_PROXY;
    WINHTTP_CALLBACK_FLAG_REDIRECT = WINHTTP_CALLBACK_STATUS_REDIRECT;
    WINHTTP_CALLBACK_FLAG_INTERMEDIATE_RESPONSE =  WINHTTP_CALLBACK_STATUS_INTERMEDIATE_RESPONSE;
    WINHTTP_CALLBACK_FLAG_SECURE_FAILURE = WINHTTP_CALLBACK_STATUS_SECURE_FAILURE;
    WINHTTP_CALLBACK_FLAG_SENDREQUEST_COMPLETE = WINHTTP_CALLBACK_STATUS_SENDREQUEST_COMPLETE;
    WINHTTP_CALLBACK_FLAG_HEADERS_AVAILABLE = WINHTTP_CALLBACK_STATUS_HEADERS_AVAILABLE;
    WINHTTP_CALLBACK_FLAG_DATA_AVAILABLE = WINHTTP_CALLBACK_STATUS_DATA_AVAILABLE;
    WINHTTP_CALLBACK_FLAG_READ_COMPLETE = WINHTTP_CALLBACK_STATUS_READ_COMPLETE;
    WINHTTP_CALLBACK_FLAG_WRITE_COMPLETE = WINHTTP_CALLBACK_STATUS_WRITE_COMPLETE;
    WINHTTP_CALLBACK_FLAG_REQUEST_ERROR = WINHTTP_CALLBACK_STATUS_REQUEST_ERROR;

    WINHTTP_CALLBACK_FLAG_ALL_COMPLETIONS =
        (WINHTTP_CALLBACK_STATUS_SENDREQUEST_COMPLETE
       or WINHTTP_CALLBACK_STATUS_HEADERS_AVAILABLE
       or WINHTTP_CALLBACK_STATUS_DATA_AVAILABLE
       or WINHTTP_CALLBACK_STATUS_READ_COMPLETE
       or WINHTTP_CALLBACK_STATUS_WRITE_COMPLETE
       or WINHTTP_CALLBACK_STATUS_REQUEST_ERROR);
    WINHTTP_CALLBACK_FLAG_ALL_NOTIFICATIONS = $ffffffff;

//
// if the following value is returned by WinHttpSetStatusCallback, then
// probably an invalid (non-code) address was supplied for the callback
//
    WINHTTP_INVALID_STATUS_CALLBACK = -1;


//
// WinHttpQueryHeaders info levels. Generally, there is one info level
// for each potential RFC822/HTTP/MIME header that an HTTP server
// may send as part of a request response.
//
// The WINHTTP_QUERY_RAW_HEADERS info level is provided for clients
// that choose to perform their own header parsing.
//

    WINHTTP_QUERY_MIME_VERSION = 0;
    WINHTTP_QUERY_CONTENT_TYPE = 1;
    WINHTTP_QUERY_CONTENT_TRANSFER_ENCODING = 2;
    WINHTTP_QUERY_CONTENT_ID = 3;
    WINHTTP_QUERY_CONTENT_DESCRIPTION = 4;
    WINHTTP_QUERY_CONTENT_LENGTH = 5;
    WINHTTP_QUERY_CONTENT_LANGUAGE = 6;
    WINHTTP_QUERY_ALLOW = 7;
    WINHTTP_QUERY_PUBLIC = 8;
    WINHTTP_QUERY_DATE = 9;
    WINHTTP_QUERY_EXPIRES = 10;
    WINHTTP_QUERY_LAST_MODIFIED = 11;
    WINHTTP_QUERY_MESSAGE_ID = 12;
    WINHTTP_QUERY_URI = 13;
    WINHTTP_QUERY_DERIVED_FROM = 14;
    WINHTTP_QUERY_COST = 15;
    WINHTTP_QUERY_LINK = 16;
    WINHTTP_QUERY_PRAGMA = 17;
    WINHTTP_QUERY_VERSION = 18; // special: part of status line
    WINHTTP_QUERY_STATUS_CODE =  19;  // special: part of status line
    WINHTTP_QUERY_STATUS_TEXT = 20;  // special: part of status line
    WINHTTP_QUERY_RAW_HEADERS = 21; // special: all headers as ASCIIZ
    WINHTTP_QUERY_RAW_HEADERS_CRLF = 22;  // special: all headers
    WINHTTP_QUERY_CONNECTION = 23;
    WINHTTP_QUERY_ACCEPT = 24;
    WINHTTP_QUERY_ACCEPT_CHARSET = 25;
    WINHTTP_QUERY_ACCEPT_ENCODING = 26;
    WINHTTP_QUERY_ACCEPT_LANGUAGE = 27;
    WINHTTP_QUERY_AUTHORIZATION = 28;
    WINHTTP_QUERY_CONTENT_ENCODING = 29;
    WINHTTP_QUERY_FORWARDED = 30;
    WINHTTP_QUERY_FROM = 31;
    WINHTTP_QUERY_IF_MODIFIED_SINCE = 32;
    WINHTTP_QUERY_LOCATION = 33;
    WINHTTP_QUERY_ORIG_URI = 34;
    WINHTTP_QUERY_REFERER = 35;
    WINHTTP_QUERY_RETRY_AFTER = 36;
    WINHTTP_QUERY_SERVER = 37;
    WINHTTP_QUERY_TITLE = 38;
    WINHTTP_QUERY_USER_AGENT = 39;
    WINHTTP_QUERY_WWW_AUTHENTICATE = 40;
    WINHTTP_QUERY_PROXY_AUTHENTICATE = 41;
    WINHTTP_QUERY_ACCEPT_RANGES = 42;
    WINHTTP_QUERY_SET_COOKIE = 43;
    WINHTTP_QUERY_COOKIE = 44;
    WINHTTP_QUERY_REQUEST_METHOD = 45;  // special: GET/POST etc.
    WINHTTP_QUERY_REFRESH = 46;
    WINHTTP_QUERY_CONTENT_DISPOSITION = 47;

   // HTTP 1.1 defined headers
    WINHTTP_QUERY_AGE = 48;
    WINHTTP_QUERY_CACHE_CONTROL = 49;
    WINHTTP_QUERY_CONTENT_BASE = 50;
    WINHTTP_QUERY_CONTENT_LOCATION = 51;
    WINHTTP_QUERY_CONTENT_MD5 = 52;
    WINHTTP_QUERY_CONTENT_RANGE = 53;
    WINHTTP_QUERY_ETAG = 54;
    WINHTTP_QUERY_HOST = 55;
    WINHTTP_QUERY_IF_MATCH = 56;
    WINHTTP_QUERY_IF_NONE_MATCH = 57;
    WINHTTP_QUERY_IF_RANGE = 58;
    WINHTTP_QUERY_IF_UNMODIFIED_SINCE = 59;
    WINHTTP_QUERY_MAX_FORWARDS = 60;
    WINHTTP_QUERY_PROXY_AUTHORIZATION = 61;
    WINHTTP_QUERY_RANGE = 62;
    WINHTTP_QUERY_TRANSFER_ENCODING = 63;
    WINHTTP_QUERY_UPGRADE = 64;
    WINHTTP_QUERY_VARY = 65;
    WINHTTP_QUERY_VIA = 66;
    WINHTTP_QUERY_WARNING = 67;
    WINHTTP_QUERY_EXPECT = 68;
    WINHTTP_QUERY_PROXY_CONNECTION = 69;
    WINHTTP_QUERY_UNLESS_MODIFIED_SINCE = 70;

    WINHTTP_QUERY_PROXY_SUPPORT = 75;
    WINHTTP_QUERY_AUTHENTICATION_INFO = 76;
    WINHTTP_QUERY_PASSPORT_URLS = 77;
    WINHTTP_QUERY_PASSPORT_CONFIG = 78;
    WINHTTP_QUERY_MAX = 78;

//
// WINHTTP_QUERY_CUSTOM - if this special value is supplied as the dwInfoLevel
// parameter of WinHttpQueryHeaders() then the lpBuffer parameter contains the name
// of the header we are to query
//

    WINHTTP_QUERY_CUSTOM = 65535;

//
// WINHTTP_QUERY_FLAG_REQUEST_HEADERS - if this bit is set in the dwInfoLevel
// parameter of WinHttpQueryHeaders() then the request headers will be queried for the
// request information
//

    WINHTTP_QUERY_FLAG_REQUEST_HEADERS = $80000000;

//
// WINHTTP_QUERY_FLAG_SYSTEMTIME - if this bit is set in the dwInfoLevel parameter
// of WinHttpQueryHeaders() AND the header being queried contains date information,
// e.g. the "Expires:" header then lpBuffer will contain a SYSTEMTIME structure
// containing the date and time information converted from the header string
//

    WINHTTP_QUERY_FLAG_SYSTEMTIME = $40000000;

//
// WINHTTP_QUERY_FLAG_NUMBER - if this bit is set in the dwInfoLevel parameter of
// HttpQueryHeader(), then the value of the header will be converted to a number
// before being returned to the caller, if applicable
//

    WINHTTP_QUERY_FLAG_NUMBER = $20000000;

   // HTTP Response Status Codes:
    HTTP_STATUS_CONTINUE =  100; // OK to continue with request
    HTTP_STATUS_SWITCH_PROTOCOLS = 101; // server has switched protocols in upgrade header

    HTTP_STATUS_OK = 200; // request completed
    HTTP_STATUS_CREATED = 201; // object created, reason = new URI
    HTTP_STATUS_ACCEPTED = 202; // async completion (TBS)
    HTTP_STATUS_PARTIAL = 203; // partial completion
    HTTP_STATUS_NO_CONTENT = 204; // no info to return
    HTTP_STATUS_RESET_CONTENT = 205; // request completed, but clear form
    HTTP_STATUS_PARTIAL_CONTENT = 206; // partial GET fulfilled
    HTTP_STATUS_WEBDAV_MULTI_STATUS = 207; // WebDAV Multi-Status

    HTTP_STATUS_AMBIGUOUS = 300; // server couldn't decide what to return
    HTTP_STATUS_MOVED = 301; // object permanently moved
    HTTP_STATUS_REDIRECT = 302; // object temporarily moved
    HTTP_STATUS_REDIRECT_METHOD = 303; // redirection w/ new access method
    HTTP_STATUS_NOT_MODIFIED = 304; // if-modified-since was not modified
    HTTP_STATUS_USE_PROXY = 305; // redirection to proxy, location header specifies proxy to use
    HTTP_STATUS_REDIRECT_KEEP_VERB = 307; // HTTP/1.1: keep same verb

    HTTP_STATUS_BAD_REQUEST = 400; // invalid syntax
    HTTP_STATUS_DENIED = 401; // access denied
    HTTP_STATUS_PAYMENT_REQ = 402; // payment required
    HTTP_STATUS_FORBIDDEN = 403; // request forbidden
    HTTP_STATUS_NOT_FOUND = 404; // object not found
    HTTP_STATUS_BAD_METHOD = 405; // method is not allowed
    HTTP_STATUS_NONE_ACCEPTABLE = 406; // no response acceptable to client found
    HTTP_STATUS_PROXY_AUTH_REQ = 407; // proxy authentication required
    HTTP_STATUS_REQUEST_TIMEOUT = 408; // server timed out waiting for request
    HTTP_STATUS_CONFLICT = 409; // user should resubmit with more info
    HTTP_STATUS_GONE = 410; // the resource is no longer available
    HTTP_STATUS_LENGTH_REQUIRED = 411; // the server refused to accept request w/o a length
    HTTP_STATUS_PRECOND_FAILED = 412; // precondition given in request failed
    HTTP_STATUS_REQUEST_TOO_LARGE = 413; // request entity was too large
    HTTP_STATUS_URI_TOO_LONG = 414; // request URI too long
    HTTP_STATUS_UNSUPPORTED_MEDIA = 415; // unsupported media type
    HTTP_STATUS_RETRY_WITH = 449; // retry after doing the appropriate action.

    HTTP_STATUS_SERVER_ERROR = 500; // internal server error
    HTTP_STATUS_NOT_SUPPORTED = 501; // required not supported
    HTTP_STATUS_BAD_GATEWAY = 502; // error response received from gateway
    HTTP_STATUS_SERVICE_UNAVAIL = 503; // temporarily overloaded
    HTTP_STATUS_GATEWAY_TIMEOUT = 504; // timed out waiting for gateway
    HTTP_STATUS_VERSION_NOT_SUP = 505; // HTTP version not supported

    HTTP_STATUS_FIRST = HTTP_STATUS_CONTINUE;
    HTTP_STATUS_LAST = HTTP_STATUS_VERSION_NOT_SUP;


  type
    HInternet = pointer;
{    // API Enums for WINHTTP_CALLBACK_STATUS_REQUEST_ERROR:
    WINHTTP_CALLBACK_STATUS_REQUEST_ERROR = (API_NONE, API_RECEIVE_RESPONSE,
      API_QUERY_DATA_AVAILABLE, API_READ_DATA, API_WRITE_DATA, API_SEND_REQUEST);
}
    INTERNET_SCHEME = (INTERNET_SCHEME_FILLER, INTERNET_SCHEME_HTTP, INTERNET_SCHEME_HTTPS);
    INTERNET_PORT = word;
    URL_COMPONENTS = record
      dwStructSize: DWord;      // size of this structure. Used in version check
      lpszScheme: LPWSTR;       // pointer to scheme name
      dwSchemeLength: DWord;    // length of scheme name
      nScheme: INTERNET_SCHEME; // enumerated scheme type (if known)
      lpszHostName: LPWSTR;     // pointer to host name
      dwHostNameLength: DWord;  // length of host name
      nPort: INTERNET_PORT;     // converted port number
      lpszUserName: LPWSTR;     // pointer to user name
      dwUserNameLength: DWord;  // length of user name
      lpszPassword: LPWSTR;     // pointer to password
      dwPasswordLength: DWord;  // length of password
      lpszUrlPath: LPWSTR;      // pointer to URL-path
      dwUrlPathLength: DWord;   // length of URL-path
      lpszExtraInfo: LPWSTR;    // pointer to extra information (e.g. ?foo or #foo)
      dwExtraInfoLength: DWord; // length of extra information
    end;
    LPURL_COMPONENTS = ^URL_COMPONENTS;
    WINHTTP_PROXY_INFO = record
      dwAccessType: DWord;
      lpszProxy: LPWSTR;
      lpszProxyBypass: LPWSTR;
    end;
    LPWINHTTP_PROXY_INFO = ^WINHTTP_PROXY_INFO;
    WINHTTP_AUTOPROXY_OPTIONS = record
      dwFlags: DWord;
      dwAutoDetectFlags: DWord;
      lpszAutoConfigUrl: LPCWSTR;
      lpvReserved: Pointer;
      dwReserved: DWord;
      fAutoLogonIfChallenged: BOOL;
    end;
    WINHTTP_CURRENT_USER_IE_PROXY_CONFIG = record
      fAutoDetect: BOOL;
      lpszAutoConfigUrl: LPWSTR;
      lpszProxy: LPWSTR;
      lpszProxyBypass: LPWSTR;
    end;
    WINHTTP_STATUS_CALLBACK = procedure(hInternet: HInternet;
      var dwContext: DWord; dwInternetStatus: DWord;
      lpvStatusInformation: Pointer; dwStatusInformationLength: DWord);
    WINHTTP_ASYNC_RESULT = record
      dwResult: ^DWord;
      dwError: DWord;
    end;
    LPWINHTTP_ASYNC_RESULT = ^WINHTTP_ASYNC_RESULT;
    HTTP_VERSION_INFO = record
      dwMajorVersion: DWord;
      dwMinorVersion: DWord;
    end;
    LPHTTP_VERSION_INFO = ^HTTP_VERSION_INFO;
    WINHTTP_CERTIFICATE_INFO = record
      // ftExpiry - date the certificate expires.
      ftExpiry: TFILETIME;
      // ftStart - date the certificate becomes valid.
      ftStart: TFILETIME;
      // lpszSubjectInfo - the name of organization, site, and server the cert. was issued for.
      lpszSubjectInfo: LPWSTR;
      // lpszIssuerInfo - the name of orgainzation, site, and server the cert. was issued by.
      lpszIssuerInfo: LPWSTR;
      // lpszProtocolName - the name of the protocol used to provide the secure connection.
      lpszProtocolName: LPWSTR;
      // lpszSignatureAlgName - the name of the algorithm used for signing the certificate.
      lpszSignatureAlgName: LPWSTR;
      // lpszEncryptionAlgName - the name of the algorithm used for doing
      // encryption over the secure channel (SSL) connection.
      lpszEncryptionAlgName: LPWSTR;
      // dwKeySize - size of the key.
      dwKeySize: DWord;
    end;

  function WinHttpAddRequestHeaders(hRequest: HInternet;
    pwszHeaders: PWideChar; dwHeadersLength, dwModifiers: DWord): BOOL;
    stdcall; external winhttpapi;

  function WinHttpCheckPlatform: BOOL; stdcall; external winhttpapi;

  function WinHttpCloseHandle(hInternet: HInternet): BOOL; stdcall; external winhttpapi;

  function WinHttpConnect(hSession: HInternet; pwszServerName: LPCWSTR;
        nServerPort: INTERNET_PORT; dwReserved: DWord): HInternet; stdcall;
        external winhttpapi;

  function WinHttpCrackUrl(pwszURL: LPCWSTR; dwUrlLength, dwFlags: DWord;
      var URLComponents: URL_COMPONENTS): BOOL; stdcall; external winhttpapi;

  function WinHttpCreateUrl(lpURLComponents: LPURL_COMPONENTS; dwFlags: DWord;
      pwszUrl: LPWSTR; var lpdwURLLength: DWord): BOOL; stdcall; external winhttpapi;

  function WinHttpDetectAutoProxyConfigUrl(dwAutoDetectFlags: DWord;
      ppwszAutoConfigURL: LPWSTR): BOOL; stdcall; external winhttpapi;

  function WinHttpGetDefaultProxyConfiguration(var pProxyInfo:
      WINHTTP_PROXY_INFO): BOOL; stdcall; external winhttpapi;

  function WinHttpGetIEProxyConfigForCurrentUser(var pProxyConfig:
      WINHTTP_CURRENT_USER_IE_PROXY_CONFIG): BOOL; stdcall; external winhttpapi;

  function WinHttpGetProxyForUrl(hSession: HInternet; lpcwszUrl: LPCWSTR;
      pAutoProxyOptions: WINHTTP_AUTOPROXY_OPTIONS; var pProxyInfo:
      WINHTTP_PROXY_INFO): BOOL; stdcall; external winhttpapi;

  function WinHttpOpen(pwszUserAgent: LPCWSTR; dwAccessType: DWord;
      pwszProxyName, pwszProxyBypass: LPCWSTR; dwFlags: DWord): HInternet;
      stdcall; external winhttpapi;

  function WinHttpOpenRequest(hConnect: HInternet; pwszVerb, pwszObjectName,
      pwszVersion, pwszReferrer, ppwszAcceptTypes: LPCWSTR; dwFlags: DWord):
      HInternet; stdcall; external winhttpapi;

  function WinHttpQueryAuthSchemes(hRequest: HInternet; var lpdwSupportedSchemes,
      lpdwFirstScheme, pdwAuthTarget: DWord): BOOL; stdcall; external winhttpapi;

  function WinHttpQueryDataAvailable(hRequest: HInternet;
    var lpdwNumberofBytesAvailable): BOOL; stdcall; external winhttpapi;

  function WinHttpQueryHeaders(hRequest: HInternet; dwInfoLevel: DWord;
    pwszName: LPCWSTR; lpBuffer: Pointer; var lpdwBufferLength: DWord; lpdwIndex: PDWord):
    BOOL; stdcall; external winhttpapi;

  function WinHttpQueryOption(hInternet: HInternet; dwOption: DWord;
    lpvoid: Pointer; var lpdwBufferLength: DWord): BOOL; stdcall;
    external winhttpapi;

  function WinHttpReadData(hRequest: HINTERNET; lpBuffer: Pointer;
    dwNumberofBytesToRead: DWord; var lpdwNumberOfBytesRead: DWord): BOOL;
    stdcall; external winhttpapi;

  function WinHttpReceiveResponse(hRequest: HInternet; lpReserved: Pointer): BOOL;
    stdcall; external winhttpapi;

  function WinHttpSendRequest(hRequest: HInternet; pwszHeaders: LPCWSTR;
    dwHeadersLength: DWord; lpOptional: Pointer; dwOptionalLength: DWord;
    dwTotalLength: DWord; var dwContext: DWord): BOOL; stdcall;
    external winhttpapi;

  function WinHttpSetCredentials(hRequest: HInternet; AuthTargets,
    AuthScheme: DWord; pwszUserName, pwszPassWord: LPCWSTR;
    pAuthParams: Pointer): BOOL; stdcall; external winhttpapi;

  function WinHttpSetDefaultProxyConfiguration(pProxyInfo: WINHTTP_PROXY_INFO):
    BOOL; stdcall; external winhttpapi;

  function WinHttpSetOption(hInternet: HInternet; dwOption: DWord;
    lpBuffer: Pointer; dwBufferLength: DWord): BOOL; stdcall; external winhttpapi;

  function WinHttpSetStatusCallback(hInternet: HInternet;
    lpfnInternetCallBack: WINHTTP_STATUS_CALLBACK; dwNotificationFlags: DWord;
    var dwReserved: DWord): WINHTTP_STATUS_CALLBACK; stdcall; external winhttpapi;

  function WinHttpSetTimeouts(hInternet: HInternet; dwResolveTimeOut, dwConnectTimeout,
    dwSendTimeout, dwReceiveTimeout: DWord): BOOL; stdcall; external winhttpapi;

  function WinHttpTimeFromSystemTime(pst: TSystemTime; pwszTime: LPWSTR):
    BOOL; stdcall; external winhttpapi;

  function WinHttpTimeToSystemTime(pwszTime: LPCWSTR; pst: TSystemTime):
    BOOL; stdcall; external winhttpapi;

  function WinHttpWriteData(hRequest: HInternet; lpBuffer: Pointer;
    dwNumberofBytesToWrite: DWord; var lpdwNumberOfBytesWritten: DWord):
    BOOL; stdcall; external winhttpapi;


  function WinHttpSysErrorMessage(inerror: integer): String;

implementation
  uses sysutils;

function WinHttpSysErrorMessage(inerror: integer): String;
{
SysErrorMessage does not return word values for the WinHTTP errors.  This will.
SysErrorMessage is still called because WinHTTP will return *SOME* standard
Windows API errors.
}

  begin
    if (inerror >= WINHTTP_ERROR_BASE) and (inerror <= WINHTTP_ERROR_LAST) then
      case inerror of
        ERROR_WINHTTP_OUT_OF_HANDLES: Result := err_12001;
        ERROR_WINHTTP_TIMEOUT: Result := err_12002;
        ERROR_WINHTTP_INTERNAL_ERROR: Result := err_12004;
        ERROR_WINHTTP_INVALID_URL: Result := err_12005;
        ERROR_WINHTTP_UNRECOGNIZED_SCHEME: Result := err_12006;
        ERROR_WINHTTP_NAME_NOT_RESOLVED: Result := err_12007;
        ERROR_WINHTTP_INVALID_OPTION: Result := err_12009;
        ERROR_WINHTTP_OPTION_NOT_SETTABLE: Result := err_12011;
        ERROR_WINHTTP_SHUTDOWN: Result := err_12012;
        ERROR_WINHTTP_LOGIN_FAILURE: Result := err_12015;
        ERROR_WINHTTP_OPERATION_CANCELLED: Result := err_12017;
        ERROR_WINHTTP_INCORRECT_HANDLE_TYPE: Result := err_12018;
        ERROR_WINHTTP_INCORRECT_HANDLE_STATE: Result := err_12019;
        ERROR_WINHTTP_CANNOT_CONNECT: Result := err_12029;
        ERROR_WINHTTP_CONNECTION_ERROR: Result := err_12030;
        ERROR_WINHTTP_RESEND_REQUEST: Result := err_12032;
        ERROR_WINHTTP_CLIENT_AUTH_CERT_NEEDED: Result := err_12044;
        ERROR_WINHTTP_CANNOT_CALL_BEFORE_OPEN: Result := err_12100;
        ERROR_WINHTTP_CANNOT_CALL_BEFORE_SEND: Result := err_12101;
        ERROR_WINHTTP_CANNOT_CALL_AFTER_SEND: Result := err_12102;
        ERROR_WINHTTP_CANNOT_CALL_AFTER_OPEN: Result := err_12103;
        ERROR_WINHTTP_HEADER_NOT_FOUND: Result := err_12150;
        ERROR_WINHTTP_INVALID_SERVER_RESPONSE: Result := err_12152;
        ERROR_WINHTTP_INVALID_QUERY_REQUEST: Result := err_12154;
        ERROR_WINHTTP_HEADER_ALREADY_EXISTS: Result := err_12155;
        ERROR_WINHTTP_REDIRECT_FAILED: Result := err_12156;
        ERROR_WINHTTP_AUTO_PROXY_SERVICE_ERROR: Result := err_12178;
        ERROR_WINHTTP_BAD_AUTO_PROXY_SCRIPT: Result := err_12166;
        ERROR_WINHTTP_UNABLE_TO_DOWNLOAD_SCRIPT: Result := err_12167;
        ERROR_WINHTTP_NOT_INITIALIZED: Result := err_12172;
        ERROR_WINHTTP_SECURE_FAILURE: Result := err_12175;
        ERROR_WINHTTP_SECURE_CERT_DATE_INVALID: Result := err_12037;
        ERROR_WINHTTP_SECURE_CERT_CN_INVALID: Result := err_12038;
        ERROR_WINHTTP_SECURE_INVALID_CA: Result := err_12045;
        ERROR_WINHTTP_SECURE_CERT_REV_FAILED: Result := err_12057;
        ERROR_WINHTTP_SECURE_CHANNEL_ERROR: Result := err_12157;
        ERROR_WINHTTP_SECURE_INVALID_CERT: Result := err_12169;
        ERROR_WINHTTP_SECURE_CERT_REVOKED: Result := err_12170;
        ERROR_WINHTTP_SECURE_CERT_WRONG_USAGE: Result := err_12179;
        ERROR_WINHTTP_AUTODETECTION_FAILED: Result := err_12180;
        ERROR_WINHTTP_HEADER_COUNT_EXCEEDED: Result := err_12181;
        ERROR_WINHTTP_HEADER_SIZE_OVERFLOW: Result := err_12182;
        ERROR_WINHTTP_CHUNKED_ENCODING_HEADER_SIZE_OVERFLOW: Result := err_12183;
        ERROR_WINHTTP_RESPONSE_DRAIN_OVERFLOW: Result := err_12184;
      else
        Result := 'Unspecified error.';
      end
    else
      Result := SysErrorMessage(inerror);
  end;

end.

