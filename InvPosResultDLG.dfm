object InvPosResultForm: TInvPosResultForm
  Left = 397
  Top = 186
  Caption = 'Inventurz'#228'hlungen zur'#252'ckmelden'
  ClientHeight = 579
  ClientWidth = 815
  Color = clBtnFace
  Constraints.MinHeight = 613
  Constraints.MinWidth = 823
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poDesigned
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 8
    Height = 579
    Align = alLeft
    BevelOuter = bvNone
    TabOrder = 0
  end
  object Panel2: TPanel
    Left = 807
    Top = 0
    Width = 8
    Height = 579
    Align = alRight
    BevelOuter = bvNone
    TabOrder = 1
  end
  object Panel3: TPanel
    Left = 8
    Top = 0
    Width = 799
    Height = 579
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      799
      579)
    object Label4: TLabel
      Left = 0
      Top = 152
      Width = 165
      Height = 13
      Caption = 'Bereits erfasste Inventurergebnisse'
    end
    object Bevel1: TBevel
      Left = 0
      Top = 144
      Width = 801
      Height = 1
      Anchors = [akLeft, akTop, akRight]
    end
    object Label1: TLabel
      Left = 0
      Top = 0
      Width = 87
      Height = 13
      Caption = 'Inventurpositionen'
    end
    object Panel5: TPanel
      Left = 0
      Top = 313
      Width = 799
      Height = 266
      Align = alBottom
      Anchors = [akLeft, akTop, akRight, akBottom]
      BevelOuter = bvNone
      TabOrder = 0
      DesignSize = (
        799
        266)
      object GroupBox1: TGroupBox
        Left = 0
        Top = 16
        Width = 799
        Height = 212
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Inventurergebnis'
        TabOrder = 0
        DesignSize = (
          799
          212)
        object Label3: TLabel
          Left = 496
          Top = 82
          Width = 47
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'Menge Ist'
        end
        object Label6: TLabel
          Left = 648
          Top = 82
          Width = 53
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'Gewicht Ist'
        end
        object Label7: TLabel
          Left = 496
          Top = 131
          Width = 39
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'MHD Ist'
        end
        object Label10: TLabel
          Left = 648
          Top = 131
          Width = 48
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'Charge Ist'
        end
        object Label12: TLabel
          Left = 496
          Top = 32
          Width = 32
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'Einheit'
        end
        object Label13: TLabel
          Left = 776
          Top = 103
          Width = 12
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'kg'
        end
        object Label2: TLabel
          Left = 648
          Top = 32
          Width = 13
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'LE'
        end
        object MengeEdit: TEdit
          Left = 496
          Top = 98
          Width = 105
          Height = 21
          Anchors = [akTop, akRight]
          MaxLength = 22
          TabOrder = 2
          Text = '0'
          OnChange = MengeEditChange
          OnKeyPress = MengeEditKeyPress
        end
        object UpdateButton: TButton
          Left = 608
          Top = 179
          Width = 75
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = #220'bernehmen'
          TabOrder = 7
          OnClick = UpdateButtonClick
        end
        object GewichtEdit: TEdit
          Left = 648
          Top = 98
          Width = 121
          Height = 21
          Anchors = [akTop, akRight]
          MaxLength = 12
          TabOrder = 4
          Text = 'GewichtEdit'
          OnChange = DataChange
          OnExit = GewichtEditExit
        end
        object MengeUpDown: TIntegerUpDown
          Left = 601
          Top = 98
          Width = 15
          Height = 21
          Anchors = [akTop, akRight]
          Associate = MengeEdit
          Max = 1000
          TabOrder = 3
        end
        object MHDEdit: TEdit
          Left = 496
          Top = 147
          Width = 121
          Height = 21
          Anchors = [akTop, akRight]
          MaxLength = 10
          TabOrder = 5
          Text = 'MHDEdit'
          OnChange = DataChange
          OnExit = MHDEditExit
          OnKeyPress = MHDEditKeyPress
        end
        object ChargeEdit: TEdit
          Left = 648
          Top = 147
          Width = 121
          Height = 21
          Anchors = [akTop, akRight]
          MaxLength = 32
          TabOrder = 6
          Text = 'ChargeEdit'
          OnChange = DataChange
        end
        object ClearButton: TButton
          Left = 695
          Top = 179
          Width = 75
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = 'Verwerfen'
          TabOrder = 8
          OnClick = ClearButtonClick
        end
        object EinheitComboBox: TComboBoxPro
          Left = 496
          Top = 48
          Width = 121
          Height = 22
          Style = csOwnerDrawFixed
          Anchors = [akTop, akRight]
          ItemHeight = 16
          TabOrder = 0
          OnChange = DataChange
        end
        object LEEdit: TEdit
          Left = 648
          Top = 48
          Width = 121
          Height = 21
          Anchors = [akTop, akRight]
          MaxLength = 22
          TabOrder = 1
          Text = 'LEEdit'
        end
      end
      object AbortButton: TButton
        Left = 723
        Top = 236
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Cancel = True
        Caption = 'Abbrechen'
        ModalResult = 3
        TabOrder = 1
      end
      object OkButton: TButton
        Left = 635
        Top = 236
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'OK'
        Default = True
        ModalResult = 1
        TabOrder = 2
      end
      object ArtikelPanel: TPanel
        Left = 8
        Top = 32
        Width = 441
        Height = 185
        BevelOuter = bvNone
        Caption = 'ArtikelPanel'
        TabOrder = 3
        object Label11: TLabel
          Left = 2
          Top = 3
          Width = 29
          Height = 13
          Caption = 'Artikel'
        end
        object ArtikelDBGridPro: TDBGridPro
          Left = 0
          Top = 17
          Width = 441
          Height = 165
          DataSource = ArtLPDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'MS Sans Serif'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\Scalabium'
          RegistrySection = 'SMDBGrid'
          WidthOfIndicator = 11
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
          OnColumnSort = ArtikelDBGridProColumnSort
        end
      end
      object LagerplatzPanel: TPanel
        Left = 8
        Top = 32
        Width = 481
        Height = 185
        Anchors = [akLeft, akTop, akRight]
        BevelOuter = bvNone
        Caption = 'LagerplatzPanel'
        TabOrder = 4
        DesignSize = (
          481
          185)
        object Label8: TLabel
          Left = 0
          Top = 3
          Width = 49
          Height = 13
          Caption = 'Lagerplatz'
        end
        object LagerplatzDBGridPro: TDBGridPro
          Left = 0
          Top = 17
          Width = 473
          Height = 165
          Anchors = [akLeft, akTop, akRight]
          DataSource = ArtLPDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'MS Sans Serif'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\Scalabium'
          RegistrySection = 'SMDBGrid'
          WidthOfIndicator = 11
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
        end
      end
    end
    object ErgebnisseStringGrid: TStringGrid
      Left = 0
      Top = 168
      Width = 799
      Height = 120
      Anchors = [akLeft, akTop, akRight]
      ColCount = 10
      DefaultRowHeight = 18
      FixedCols = 0
      RowCount = 2
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goColSizing, goRowSelect]
      PopupMenu = InvErgPopupMenu
      TabOrder = 1
    end
    object InvPosDBGridPro: TDBGridPro
      Left = 0
      Top = 16
      Width = 799
      Height = 120
      Anchors = [akLeft, akTop, akRight]
      DataSource = InvPosDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      TabOrder = 2
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
    object InvDelButton: TButton
      Left = 0
      Top = 296
      Width = 153
      Height = 25
      Caption = 'Inventurergebnis l'#246'schen...'
      TabOrder = 3
      OnClick = InvDelButtonOnClick
    end
  end
  object KommPosDataSource: TDataSource
    OnDataChange = KommPosDataSourceDataChange
    Left = 624
    Top = 112
  end
  object InvPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 648
    Top = 40
  end
  object ADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 752
    Top = 144
  end
  object ArtLPBetterADODataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 136
    Top = 280
  end
  object ArtLPDataSource: TDataSource
    DataSet = ArtLPBetterADODataSet
    OnDataChange = ArtLPDataSourceDataChange
    Left = 184
    Top = 280
  end
  object ErgebnisseBetterADODataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 512
    Top = 112
  end
  object LEBetterADODataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 736
    Top = 256
  end
  object InvPosDataSource: TDataSource
    DataSet = InvPosDataSet
    OnDataChange = InvPosOnDataChange
    Left = 736
    Top = 56
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 400
    Top = 272
  end
  object InvErgPopupMenu: TPopupMenu
    Left = 472
    Top = 152
    object InvErgDelPopUpMenuItem: TMenuItem
      Caption = 'Inventurergebnis l'#246'schen...'
      OnClick = InvDelButtonOnClick
    end
  end
end
