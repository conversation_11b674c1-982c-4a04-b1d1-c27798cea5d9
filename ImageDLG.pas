unit ImageDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, GR32_Image;

type
  TImageForm = class(TForm)
    Image1: TImage32;
    procedure FormShow(Sender: TObject);
    procedure FormKeyPress(Sender: TObject; var Key: Char);
  private
    fFileName : String;
  public
    property FileName : String read fFileName write fFileName;
  end;

implementation

{$R *.dfm}

uses
  FrontendUtils;

procedure TImageForm.FormKeyPress(Sender: TObject; var Key: Char);
begin
  if (Key = #27) then
    Close;
end;

procedure TImageForm.FormShow(Sender: TObject);
begin
  ShowArtikelPicture (Image1, Filename);
end;

end.
