unit CmdLineUtils;

interface

function GetParamVal(const TaggedParm : string; IgnoreCase : boolean = true) : string;

implementation

uses
  SysUtils;

function GetParamVal(const TaggedParm : string; IgnoreCase : boolean = true) : string;
var Retvar : string;
    i,Len : integer;
    Comp1,Comp2 : string;
begin
  RetVar := '';
  Comp1 := TaggedParm + '=';
  if IgnoreCase then Comp1 := UpperCase(Comp1);
  Len := length(Comp1);

  for i := 1 to ParamCount do begin
      Comp2 := copy(ParamStr(i),1,Len);
      if IgnoreCase then Comp2 := UpperCase(Comp2);
      if (Comp1 = Comp2) then begin
         RetVar := copy(ParamStr(i),Len + 1,length(ParamStr(i)));
         break;
      end;
  end;

  Result := RetVar;
end;

end.
