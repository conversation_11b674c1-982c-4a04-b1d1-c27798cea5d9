object ZollAbwicklungForm: TZollAbwicklungForm
  Left = 0
  Top = 0
  Caption = 'Zoll Abwicklung'
  ClientHeight = 491
  ClientWidth = 852
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 60
    Width = 852
    Height = 45
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      852
      45)
    object Label1: TLabel
      Left = 8
      Top = 29
      Width = 61
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Verzollungen'
    end
  end
  object Panel3: TPanel
    Left = 0
    Top = 450
    Width = 852
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      852
      41)
    object CloseButton: TButton
      Left = 768
      Top = 9
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 0
    end
  end
  object VerzollDBGrid: TDBGridPro
    Left = 0
    Top = 105
    Width = 852
    Height = 345
    Align = alClient
    DataSource = VerzollDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    TabOrder = 2
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object MandPanel: TPanel
    Left = 0
    Top = 0
    Width = 852
    Height = 32
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      852
      32)
    object Label2: TLabel
      Left = 8
      Top = 11
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 88
      Top = 8
      Width = 755
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 32
    Width = 852
    Height = 28
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      852
      28)
    object Label11: TLabel
      Left = 8
      Top = 11
      Width = 69
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 88
      Top = 4
      Width = 755
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object VerzollDataSource: TDataSource
    DataSet = VerzollDataSet
    Left = 352
    Top = 296
  end
  object VerzollDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 248
    Top = 296
  end
end
