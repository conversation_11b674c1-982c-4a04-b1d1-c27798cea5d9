unit LPStatistikFrame;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms, 
  Dialogs, AdvSmoothCapacityBar, ExtCtrls, StdCtrls;

type
  TLagerBereichStatistikFrame = class(TFrame)
    LPCapacityBar: TAdvSmoothCapacityBar;
    CaptionPanel: TPanel;
    NameLabel: TLabel;
    BestandLabel: TLabel;
    LECountLabel: TLabel;
    Label1: TLabel;
    Label2: TLabel;
  private
    { Private-Deklarationen }
  public
    constructor Create(AOwner: TComponent); override;
  end;

implementation

{$R *.dfm}

uses
  SprachModul, ResourceText;

constructor TLagerBereichStatistikFrame.Create(AOwner: TComponent);
begin
  inherited Create (AOwner);

  LVSSprachModul.InitFrame (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, NameLabel);
    LVSSprachModul.SetNoTranslate (Self, LECountLabel);
    LVSSprachModul.SetNoTranslate (Self, BestandLabel);
  {$endif}

  LPCapacityBar.CapacityDescription := GetResourceText(1765);
  LPCapacityBar.Items [0].Description := GetResourceText(1766);
  LPCapacityBar.Items [1].Description := GetResourceText(1767);
  LPCapacityBar.Items [2].Description := GetResourceText(1768);
  LPCapacityBar.FreeDescription := GetResourceText(1769);
end;

end.
