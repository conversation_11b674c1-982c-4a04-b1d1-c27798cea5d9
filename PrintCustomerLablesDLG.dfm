object PrintCustomerLablesForm: TPrintCustomerLablesForm
  Left = 0
  Top = 0
  Caption = 'Kundenspezifische Etiketten drucken'
  ClientHeight = 397
  ClientWidth = 699
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  DesignSize = (
    699
    397)
  TextHeight = 13
  object LabelStringGrid: TStringGridPro
    Left = 8
    Top = 72
    Width = 683
    Height = 260
    Anchors = [akLeft, akTop, akRight, akBottom]
    ColCount = 8
    DefaultColWidth = 16
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
    PopupMenu = LabelStringGridPopupMenu
    TabOrder = 0
    TitelTexte.Strings = (
      ''
      'Artikelnr.'
      'Kunden Artikelnr.'
      'Menge'
      'MHD'
      'Charge'
      'Generator'
      'Formular')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'Tahoma'
    TitelFont.Style = []
    ColWidths = (
      16
      76
      101
      42
      59
      58
      53
      241)
  end
  object Panel2: TPanel
    Left = 0
    Top = 338
    Width = 699
    Height = 59
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      699
      59)
    object Label8: TLabel
      Left = 8
      Top = 6
      Width = 37
      Height = 13
      Caption = 'Drucker'
    end
    object PrinterComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 284
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 160
      ItemHeight = 15
      TabOrder = 0
    end
    object PrintButton: TButton
      Left = 307
      Top = 20
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Drucken'
      Default = True
      TabOrder = 1
      OnClick = PrintButtonClick
    end
    object CloseButton: TButton
      Left = 616
      Top = 20
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 2
    end
  end
  object LabelStringGridPopupMenu: TPopupMenu
    OnPopup = LabelStringGridPopupMenuPopup
    Left = 280
    Top = 200
    object PrintSingleLableMenuItem: TMenuItem
      Caption = 'Einzelne Etiketten nachdrucken...'
      OnClick = PrintSingleLableMenuItemClick
    end
  end
end
