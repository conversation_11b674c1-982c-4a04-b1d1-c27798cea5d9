object AuftragVerpackenForm: TAuftragVerpackenForm
  Left = 0
  Top = 0
  Caption = 'Auftrag Verpacken'
  ClientHeight = 626
  ClientWidth = 913
  Color = clBtnFace
  Constraints.MinHeight = 450
  Constraints.MinWidth = 500
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnResize = FormResize
  OnShow = FormShow
  TextHeight = 13
  object ButtonPanel: TPanel
    Left = 0
    Top = 546
    Width = 913
    Height = 80
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      913
      80)
    object Bevel2: TBevel
      Left = 6
      Top = 1
      Width = 901
      Height = 13
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 542
    end
    object AbortButton: TButton
      Left = 757
      Top = 23
      Width = 150
      Height = 50
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ModalResult = 3
      ParentFont = False
      TabOrder = 1
    end
    object OkButton: TButton
      Left = 8
      Top = 23
      Width = 150
      Height = 50
      Anchors = [akLeft, akBottom]
      Caption = 'Verpacken'
      Default = True
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ModalResult = 1
      ParentFont = False
      TabOrder = 0
    end
  end
  object Panel1: TPanel
    Left = 0
    Top = 371
    Width = 913
    Height = 175
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      913
      175)
    object Label4: TLabel
      Left = 8
      Top = 147
      Width = 67
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Bruttogewicht'
      ExplicitTop = 103
    end
    object Label5: TLabel
      Left = 154
      Top = 147
      Width = 11
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'kg'
      ExplicitTop = 103
    end
    object VersandKartonLabel: TLabel
      Left = 8
      Top = 29
      Width = 140
      Height = 19
      Caption = 'VersandKartonLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object VPEAnzLabel: TLabel
      Left = 877
      Top = 8
      Width = 30
      Height = 19
      Alignment = taRightJustify
      Anchors = [akTop, akRight]
      Caption = '999'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
      ExplicitLeft = 518
    end
    object VerpackungLabel: TLabel
      Left = 8
      Top = 7
      Width = 120
      Height = 19
      Caption = 'VerpackungLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      ShowAccelChar = False
    end
    object PresentLabel: TLabel
      Left = 8
      Top = 59
      Width = 900
      Height = 25
      Alignment = taCenter
      Anchors = [akLeft, akRight, akBottom]
      AutoSize = False
      Caption = 'PresentLabel'
      Color = clBtnFace
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clRed
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentColor = False
      ParentFont = False
      ShowAccelChar = False
    end
    object PackHinweisLabel: TLabel
      Left = 8
      Top = 76
      Width = 900
      Height = 25
      Alignment = taCenter
      Anchors = [akLeft, akRight, akBottom]
      AutoSize = False
      Caption = 'PackHinweisLabel'
      Color = clBtnFace
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clGreen
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentColor = False
      ParentFont = False
      ShowAccelChar = False
    end
    object PackHintLabel: TLabel
      Left = 8
      Top = 98
      Width = 900
      Height = 25
      Alignment = taCenter
      Anchors = [akLeft, akRight, akBottom]
      AutoSize = False
      Caption = 'PackHintLabel'
      Color = clBtnFace
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlue
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentColor = False
      ParentFont = False
      ShowAccelChar = False
    end
    object BruttoGewichtEdit: TEdit
      Left = 84
      Top = 144
      Width = 61
      Height = 21
      Anchors = [akLeft, akBottom]
      TabOrder = 0
      OnChange = BruttoGewichtEditChange
      OnKeyPress = BruttoGewichtEditKeyPress
    end
    object WaagePanel: TPanel
      Left = 200
      Top = 136
      Width = 297
      Height = 36
      Anchors = [akLeft, akBottom]
      TabOrder = 1
      object WaageLabel: TLabel
        Left = 40
        Top = 13
        Width = 59
        Height = 13
        Caption = 'WaageLabel'
      end
      object WaageGewichtLabel: TLabel
        Left = 184
        Top = 13
        Width = 97
        Height = 13
        Alignment = taRightJustify
        Caption = 'WaageGewichtLabel'
      end
      object WaagePaintBox: TPaintBox
        Left = 5
        Top = 6
        Width = 24
        Height = 24
      end
      object WaageLEDPanel: TPanel
        Left = 136
        Top = 10
        Width = 16
        Height = 16
        Color = clMaroon
        ParentBackground = False
        TabOrder = 0
      end
    end
    object AbmessungPanel: TPanel
      Left = 513
      Top = 136
      Width = 337
      Height = 36
      Anchors = [akLeft, akBottom]
      TabOrder = 2
      object Label11: TLabel
        Left = 170
        Top = 13
        Width = 6
        Height = 13
        Caption = 'x'
      end
      object Label12: TLabel
        Left = 240
        Top = 13
        Width = 6
        Height = 13
        Caption = 'x'
      end
      object Label13: TLabel
        Left = 40
        Top = 13
        Width = 55
        Height = 13
        Caption = 'Abmessung'
      end
      object Label14: TLabel
        Left = 311
        Top = 13
        Width = 13
        Height = 13
        Caption = 'cm'
      end
      object PaintBox2: TPaintBox
        Left = 8
        Top = 6
        Width = 24
        Height = 24
      end
      object LTLength: TEdit
        Left = 114
        Top = 10
        Width = 50
        Height = 21
        MaxLength = 4
        TabOrder = 0
        Text = 'LTLength'
      end
      object LTWidth: TEdit
        Left = 184
        Top = 10
        Width = 50
        Height = 21
        MaxLength = 4
        TabOrder = 1
        Text = 'LTWidth'
      end
      object LTHeigth: TEdit
        Left = 254
        Top = 10
        Width = 50
        Height = 21
        MaxLength = 4
        TabOrder = 2
        Text = 'LTHeigth'
      end
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 0
    Width = 913
    Height = 67
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      913
      67)
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 51
      Height = 13
      Caption = 'Auftragnr.'
    end
    object AuftragNrLabel: TLabel
      Left = 80
      Top = 8
      Width = 85
      Height = 13
      Caption = 'AuftragNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label3: TLabel
      Left = 8
      Top = 24
      Width = 52
      Height = 13
      Caption = 'Empf'#228'nger'
    end
    object WarenempfLabel: TLabel
      Left = 80
      Top = 24
      Width = 321
      Height = 13
      AutoSize = False
      Caption = 'WarenempfLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LabelLENVE: TLabel
      Left = 8
      Top = 40
      Width = 38
      Height = 13
      Caption = 'NVE-Nr.'
    end
    object NVELabel: TLabel
      Left = 80
      Top = 40
      Width = 50
      Height = 13
      Caption = 'NVELabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Bevel1: TBevel
      Left = 6
      Top = 61
      Width = 901
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 80
      ExplicitWidth = 542
    end
  end
  object InhaltPanel: TPanel
    Left = 0
    Top = 127
    Width = 913
    Height = 194
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 3
    OnResize = InhaltPanelResize
    DesignSize = (
      913
      194)
    object Positionen: TLabel
      Left = 8
      Top = 8
      Width = 49
      Height = 13
      Caption = 'Positionen'
    end
    object Bevel3: TBevel
      Left = 6
      Top = 4
      Width = 901
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 730
    end
    object ArtikelImagePanel: TPanel
      Left = 503
      Top = 19
      Width = 161
      Height = 161
      BevelOuter = bvSpace
      Color = clWhite
      TabOrder = 0
      object ArtikelImage: TImage32
        Left = 1
        Top = 1
        Width = 159
        Height = 159
        Align = alClient
        Bitmap.ResamplerClassName = 'TDraftResampler'
        BitmapAlign = baCenter
        Scale = 1.000000000000000000
        ScaleMode = smOptimal
        TabOrder = 0
      end
    end
    object InhaltListView: TListView
      Left = 8
      Top = 27
      Width = 369
      Height = 152
      Columns = <>
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'Tahoma'
      Font.Style = []
      GridLines = True
      MultiSelect = True
      ReadOnly = True
      RowSelect = True
      ParentFont = False
      TabOrder = 1
      ViewStyle = vsReport
      OnChange = InhaltListViewChange
      OnCustomDrawSubItem = InhaltListViewCustomDrawSubItem
    end
  end
  object FehlerPanel: TPanel
    Left = 0
    Top = 321
    Width = 913
    Height = 50
    Align = alBottom
    BevelOuter = bvNone
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 4
    DesignSize = (
      913
      50)
    object FehlerButton: TButton
      Left = 713
      Top = 6
      Width = 195
      Height = 38
      Anchors = [akTop, akRight, akBottom]
      Caption = 'Best'#228'tigen (F8)'
      TabOrder = 0
    end
  end
  object SpedPanel: TPanel
    AlignWithMargins = True
    Left = 8
    Top = 67
    Width = 897
    Height = 60
    Margins.Left = 8
    Margins.Top = 0
    Margins.Right = 8
    Margins.Bottom = 0
    Align = alTop
    BevelOuter = bvNone
    ParentBackground = False
    TabOrder = 5
    DesignSize = (
      897
      60)
    object Label6: TLabel
      Left = 0
      Top = 23
      Width = 48
      Height = 13
      Caption = 'Spedition:'
    end
    object SpedLabel: TLabel
      Left = 288
      Top = 14
      Width = 107
      Height = 25
      Caption = 'SpedLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
      ShowAccelChar = False
    end
    object SpedImage: TImage
      Left = 64
      Top = 6
      Width = 183
      Height = 50
      Center = True
      Proportional = True
      Stretch = True
    end
    object ChangeSpedButton: TButton
      Left = 693
      Top = 13
      Width = 195
      Height = 34
      Anchors = [akTop, akRight]
      Caption = 'Spedition wechseln'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      OnClick = ChangeSpedButtonClick
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 496
    Top = 16
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 376
    Top = 48
  end
end
