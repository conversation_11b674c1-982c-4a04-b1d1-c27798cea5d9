@echo off
setlocal enabledelayedexpansion
for /f "delims=" %%i in ('git describe') do set GIT_VERSION=%%i

:: Define the application details
set "COMPANY_NAME=common-solutions"
set "FILE_DESCRIPTION=storelogix manager"
set "PRODUCT_NAME=storelogix"
set "ORIGINAL_FILENAME=SLManager.exe"
:: Define output file
set "RC_FILE=aVersionInfo.rc"


:: Split in parts
for /f "tokens=1-3 delims=-" %%a in ("%GIT_VERSION%") do (
  set "PRODUCT_VERSION=%%a"
  set "COMMIT_ANZ=%%b"
  set "COMMIT_HASH=%%c"
)

:: Check for letter at start of tag
SET "var="&for /f "delims=0123456789" %%i in ("!PRODUCT_VERSION:~0,1!") do set var=%%i
if defined var (set "PRODUCT_VERSION_C=!PRODUCT_VERSION:~1!") else (set "PRODUCT_VERSION_C=%PRODUCT_VERSION%")

for /f "tokens=1-4 delims=." %%a in ("%PRODUCT_VERSION_C%") do (
  set "MAJORVERSION=%%a"
  set "MINORVERSION=%%b"
  set "REVISIONNUMBER=%%c"
  set "BUILDNUMBER=%%d"
)
:: echo. VALUE "ProductVersion", "%PRODUCT_VERSION%" >> aVersionInfo.rc
(
    echo VS_VERSION_INFO VERSIONINFO
    echo FILEVERSION %MAJORVERSION%,%MINORVERSION%,%REVISIONNUMBER%,%BUILDNUMBER%
    echo PRODUCTVERSION %MAJORVERSION%,%MINORVERSION%,%REVISIONNUMBER%,%BUILDNUMBER%
    echo BEGIN
    echo     BLOCK "StringFileInfo"
    echo     BEGIN
    echo         BLOCK "040904E4"
    echo         BEGIN
    echo             VALUE "CompanyName", "%COMPANY_NAME%\0"
    echo             VALUE "FileDescription", "%FILE_DESCRIPTION%\0"
    echo             VALUE "FileVersion", "%PRODUCT_VERSION_C%\0"
    echo             VALUE "ProductVersion", "%GIT_VERSION%\0"
    echo             VALUE "OriginalFilename", "%ORIGINAL_FILENAME%\0"
    echo             VALUE "ProductName", "%PRODUCT_NAME%\0"
    echo             VALUE "LegalCopyright", "Copyright 2004-2024 %COMPANY_NAME%\0"
    echo         END
    echo     END
    echo     BLOCK "VarFileInfo"
    echo     BEGIN
    echo         VALUE "Translation", 0x407, 1252
    echo     END
    echo END
) > %RC_FILE%

brcc32 %RC_FILE%

endlocal