object LeistungsReportForm: TLeistungsReportForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Leistungsauswertung'
  ClientHeight = 569
  ClientWidth = 273
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    273
    569)
  PixelsPerInch = 96
  TextHeight = 13
  object Label3: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Label2: TLabel
    Left = 8
    Top = 56
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object DatumLabel: TLabel
    Left = 8
    Top = 104
    Width = 52
    Height = 13
    Caption = 'Von Datum'
  end
  object BisDatumLabel: TLabel
    Left = 154
    Top = 104
    Width = 47
    Height = 13
    Caption = 'Bis Datum'
  end
  object Label1: TLabel
    Left = 8
    Top = 207
    Width = 76
    Height = 13
    Caption = 'Kommissionierer'
  end
  object Bevel3: TBevel
    Left = 4
    Top = 526
    Width = 266
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 525
  end
  object Bevel1: TBevel
    Left = 4
    Top = 149
    Width = 266
    Height = 4
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 232
  end
  object Bevel2: TBevel
    Left = 4
    Top = 201
    Width = 266
    Height = 4
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object MandantComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 259
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 60
    Enabled = False
    ItemHeight = 16
    TabOrder = 0
    OnChange = MandantComboBoxChange
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 72
    Width = 259
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 60
    ItemHeight = 16
    TabOrder = 1
  end
  object DatumDateTimePicker: TDateTimePicker
    Left = 8
    Top = 120
    Width = 113
    Height = 21
    Date = 38275.496297569450000000
    Time = 38275.496297569450000000
    TabOrder = 2
  end
  object BisDatumDateTimePicker: TDateTimePicker
    Left = 154
    Top = 120
    Width = 113
    Height = 21
    Date = 38275.496297569450000000
    Time = 38275.496297569450000000
    TabOrder = 3
    OnChange = BisDatumDateTimePickerChange
  end
  object ShowButton: TButton
    Left = 8
    Top = 536
    Width = 180
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Bericht anzeigen...'
    Default = True
    TabOrder = 11
    OnClick = ShowButtonClick
  end
  object AbortButton: TButton
    Left = 194
    Top = 536
    Width = 73
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 12
  end
  object KommUserListBox: TCheckListBox
    Left = 8
    Top = 304
    Width = 259
    Height = 192
    Anchors = [akLeft, akTop, akRight, akBottom]
    ItemHeight = 16
    PopupMenu = PopupMenu1
    Style = lbOwnerDrawFixed
    TabOrder = 9
    OnDrawItem = KommUserListBoxDrawItem
  end
  object VorschauCheckBox: TCheckBox
    Left = 8
    Top = 502
    Width = 217
    Height = 17
    Anchors = [akLeft, akBottom]
    Caption = 'Mit Druckvorschau'
    Checked = True
    State = cbChecked
    TabOrder = 10
  end
  object STVerdichtungCheckBox: TCheckBox
    Left = 8
    Top = 160
    Width = 223
    Height = 17
    Caption = 'St'#252'ckkommissionierung zusammenfassen'
    TabOrder = 4
    Visible = False
  end
  object ShowDelUserCheckBox: TCheckBox
    Left = 8
    Top = 226
    Width = 223
    Height = 17
    Caption = 'Gel'#246'schte auch anzeigen'
    TabOrder = 6
    OnClick = ShowDelUserCheckBoxClick
  end
  object UserFilterEdit: TEdit
    Left = 8
    Top = 272
    Width = 259
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 8
    Text = 'UserFilterEdit'
    Visible = False
    OnChange = UserFilterEditChange
  end
  object PrintBenCheckBox: TCheckBox
    Left = 8
    Top = 176
    Width = 223
    Height = 17
    Caption = 'Benutzernamen mit ausgeben'
    TabOrder = 5
  end
  object ShowNNCheckBox: TCheckBox
    Left = 8
    Top = 242
    Width = 223
    Height = 17
    Caption = 'Nicht benannte auch anzeigen'
    Checked = True
    State = cbChecked
    TabOrder = 7
    OnClick = ShowDelUserCheckBoxClick
  end
  object PopupMenu1: TPopupMenu
    Left = 48
    Top = 232
    object Allemarkieren1: TMenuItem
      Caption = 'Alle markieren'
      OnClick = Allemarkieren1Click
    end
    object Markierungenaufheben1: TMenuItem
      Caption = 'Markierungen aufheben'
      OnClick = Markierungenaufheben1Click
    end
  end
end
