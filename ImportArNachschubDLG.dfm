object ImportArNachschubForm: TImportArNachschubForm
  Left = 0
  Top = 0
  Caption = 'Impo<PERSON> <PERSON>'
  ClientHeight = 464
  ClientWidth = 815
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  DesignSize = (
    815
    464)
  TextHeight = 15
  object Bevel3: TBevel
    Left = 8
    Top = 379
    Width = 277
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 408
  end
  object Label1: TLabel
    Left = 10
    Top = 8
    Width = 29
    Height = 15
    Caption = 'Lager'
  end
  object ImportStringGrid: TStringGridPro
    Left = 8
    Top = 139
    Width = 799
    Height = 278
    Anchors = [akLeft, akTop, akRight, akBottom]
    ColCount = 7
    DefaultColWidth = 40
    DefaultRowHeight = 16
    RowCount = 6
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goFixedRowDefAlign]
    TabOrder = 1
    TitelTexte.Strings = (
      ''
      'Bereich'
      'Zone'
      'Lagerplatz'
      'Artikel-Nr.'
      'Min. Bestand'
      'Max. Bestand')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'Tahoma'
    TitelFont.Style = []
    ExplicitWidth = 968
    ExplicitHeight = 255
    ColWidths = (
      32
      143
      100
      130
      87
      101
      90)
  end
  object ReadButton: TButton
    Left = 8
    Top = 431
    Width = 75
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Einlesen'
    TabOrder = 2
    OnClick = ReadButtonClick
    ExplicitTop = 408
  end
  object CreateButton: TButton
    Left = 542
    Top = 431
    Width = 265
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Erzeugen'
    TabOrder = 3
    OnClick = CreateButtonClick
    ExplicitLeft = 711
    ExplicitTop = 408
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 799
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 0
  end
  object OpenDialog1: TOpenDialog
    DefaultExt = '*.csv'
    Filter = 'CSV-Dateien|*.csv'
    Left = 904
    Top = 29
  end
end
