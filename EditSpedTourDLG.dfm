object EditSpedTourForm: TEditSpedTourForm
  Left = 561
  Top = 277
  Caption = 'EditSpedTourForm'
  ClientHeight = 416
  ClientWidth = 599
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    599
    416)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 104
    Width = 39
    Height = 13
    Caption = 'Tour-Nr.'
  end
  object Label2: TLabel
    Left = 8
    Top = 136
    Width = 65
    Height = 13
    Caption = 'Beschreibung'
  end
  object Label3: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Bevel2: TBevel
    Left = 7
    Top = 94
    Width = 584
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label13: TLabel
    Left = 8
    Top = 37
    Width = 63
    Height = 13
    Caption = 'Auslieferlager'
  end
  object Label14: TLabel
    Left = 8
    Top = 66
    Width = 29
    Height = 13
    Caption = 'Depot'
  end
  object Label9: TLabel
    Left = 311
    Top = 104
    Width = 65
    Height = 13
    Caption = 'Untertour von'
  end
  object Bevel1: TBevel
    Left = 7
    Top = 160
    Width = 584
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object OkButton: TButton
    Left = 424
    Top = 383
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 7
  end
  object AbortButton: TButton
    Left = 516
    Top = 383
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 8
  end
  object TourNrEdit: TEdit
    Left = 103
    Top = 101
    Width = 200
    Height = 21
    MaxLength = 32
    TabOrder = 3
    Text = 'TourNrEdit'
  end
  object BezEdit: TEdit
    Left = 103
    Top = 133
    Width = 488
    Height = 21
    MaxLength = 64
    TabOrder = 5
    Text = 'BezEdit'
  end
  object MandantComboBox: TComboBoxPro
    Left = 103
    Top = 5
    Width = 488
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 120
    AutoPrepare = False
    ColumeCount = 1
    ItemHeight = 16
    TabOrder = 0
    OnChange = MandantComboBoxChange
  end
  object LiefLagerComboBox: TComboBoxPro
    Left = 103
    Top = 34
    Width = 488
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 120
    ItemHeight = 16
    TabOrder = 1
    OnChange = LiefLagerComboBoxChange
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 175
    Width = 583
    Height = 201
    ActivePage = TabSheet1
    Anchors = [akLeft, akBottom]
    PopupMenu = WochentagPopupMenu
    TabOrder = 6
    TabWidth = 70
    object TabSheet1: TTabSheet
      Caption = 'Alle Tage'
      OnShow = TabSheet1Show
      inline DefaultTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet2: TTabSheet
      Caption = 'Montag'
      ImageIndex = 1
      inline MoTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet3: TTabSheet
      Caption = 'Dienstag'
      ImageIndex = 2
      inline DiTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet4: TTabSheet
      Caption = 'Mittwoch'
      ImageIndex = 3
      inline MiTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet5: TTabSheet
      Caption = 'Donnerstag'
      ImageIndex = 4
      inline DoTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet6: TTabSheet
      Caption = 'Freitag'
      ImageIndex = 5
      inline FrTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet7: TTabSheet
      Caption = 'Samstag'
      ImageIndex = 6
      inline SaTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet8: TTabSheet
      Caption = 'Sonntag'
      ImageIndex = 7
      inline SoTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
  end
  object DepotComboBox: TComboBoxPro
    Left = 103
    Top = 63
    Width = 488
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 120
    AutoPrepare = False
    ColumeCount = 1
    ItemHeight = 16
    TabOrder = 2
  end
  object SubTourComboBox: TComboBoxPro
    Left = 391
    Top = 101
    Width = 200
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 80
    ItemHeight = 16
    TabOrder = 4
    OnChange = SubTourComboBoxChange
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 304
    Top = 120
  end
  object WochentagPopupMenu: TPopupMenu
    Left = 376
    Top = 496
    object Montag1: TMenuItem
      AutoCheck = True
      Caption = 'Montag'
      OnClick = WochentagClick
    end
    object Dienstag1: TMenuItem
      AutoCheck = True
      Caption = 'Dienstag'
      GroupIndex = 1
      OnClick = WochentagClick
    end
    object Mittwoch1: TMenuItem
      AutoCheck = True
      Caption = 'Mittwoch'
      GroupIndex = 1
      OnClick = WochentagClick
    end
    object Donnerstag1: TMenuItem
      AutoCheck = True
      Caption = 'Donnerstag'
      GroupIndex = 1
      OnClick = WochentagClick
    end
    object Freitag1: TMenuItem
      AutoCheck = True
      Caption = 'Freitag'
      GroupIndex = 1
      OnClick = WochentagClick
    end
    object Samstag1: TMenuItem
      AutoCheck = True
      Caption = 'Samstag'
      GroupIndex = 1
      OnClick = WochentagClick
    end
    object Sonntag1: TMenuItem
      AutoCheck = True
      Caption = 'Sonntag'
      GroupIndex = 1
      OnClick = WochentagClick
    end
  end
end
