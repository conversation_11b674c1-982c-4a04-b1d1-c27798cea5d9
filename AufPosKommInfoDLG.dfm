object AufPosKommInfoForm: TAufPosKommInfoForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'AufPosKommInfoForm'
  ClientHeight = 264
  ClientWidth = 695
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  DesignSize = (
    695
    264)
  TextHeight = 13
  object GroupBox1: TGroupBox
    Left = 8
    Top = 15
    Width = 679
    Height = 210
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Zusatzinfos'
    TabOrder = 0
    ExplicitTop = 13
    ExplicitWidth = 677
    DesignSize = (
      679
      210)
    object InfoArtComboBox: TComboBoxPro
      Left = 8
      Top = 20
      Width = 377
      Height = 21
      Style = csDropDownList
      TabOrder = 0
      OnChange = InfoArtComboBoxChange
    end
    object InfoMemo: TMemo
      Left = 8
      Top = 78
      Width = 663
      Height = 123
      Anchors = [akLeft, akTop, akRight, akBottom]
      Lines.Strings = (
        'Memo1')
      TabOrder = 1
      ExplicitWidth = 661
    end
  end
  object CloseButton: TButton
    Left = 612
    Top = 231
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 1
    ExplicitLeft = 610
    ExplicitTop = 229
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 16
    Top = 56
  end
end
