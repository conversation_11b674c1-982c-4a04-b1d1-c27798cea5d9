unit ShowAuftragChangesDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, BetterADODataSet, StdCtrls, Grids, DBGrids, SMDBGrid,
  DBGridPro, Menus, ExtCtrls;

type
  TShowAuftragChangesForm = class(TForm)
    KopfPanel: TPanel;
    Label1: TLabel;
    Label3: TLabel;
    AuftragNrLabel: TLabel;
    WarenempfLabel: TLabel;
    AufChangeDBGrid: TDBGridPro;
    AufPosChangesDataSet: TBetterADODataSet;
    AuftragPosDataSource: TDataSource;
    AufChangeDataSet: TBetterADODataSet;
    AufChangeDataSource: TDataSource;
    PosPanel: TPanel;
    AuftPosChangesDBGrid: TDBGridPro;
    Label2: TLabel;
    FussPanel: TPanel;
    CloseButton: TButton;
    Splitter1: TSplitter;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    function AuftPosChangesDBGridColumnSort(Sender: TCustomDBGridPro;
      const ColumnName: string): string;
  private
  public
    procedure Prepare (const RefAuf : Integer);
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, LVSConst, DatenModul, DBGridUtilModule, FrontendUtils, ConfigModul, SperrGrundDLG, LVSDatenInterface,
  SprachModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TShowAuftragChangesForm.AuftPosChangesDBGridColumnSort(Sender: TCustomDBGridPro; const ColumnName: string): string;
begin
  if (ColumnName = 'CHANGE_DATE') then
    Result := 'REF'
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowAuftragChangesForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  AufChangeDataSet.Close;
  AufPosChangesDataSet.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
  LVSConfigModul.SaveFormParameter(Self, 'Splitter', KopfPanel.Height);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowAuftragChangesForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AuftragNrLabel);
    LVSSprachModul.SetNoTranslate (Self, WarenempfLabel);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowAuftragChangesForm.FormShow(Sender: TObject);
var
  wert : Integer;
begin
  LVSConfigModul.RestoreFormInfo (Self);

  if (LVSConfigModul.ReadFormParameter(Self, 'Splitter', wert, KopfPanel.Height) = 0) and (wert > 0) and (wert < (Height - 100)) then
    KopfPanel.Height := wert;

  AufChangeDataSet.Open;

  AufChangeDBGrid.SetColumnVisible('AUFTRAG_NR', False);
  //AufChangeDBGrid.SetColumnVisible('OLD_TOUR_NR', LVSConfigModul.UseSpedTour);
  //AufChangeDBGrid.SetColumnVisible('NEW_TOUR_NR', LVSConfigModul.UseSpedTour);

  AufPosChangesDataSet.Open;

  AuftPosChangesDBGrid.SetColumnVisible('CHANGE_MHD_FIX', LVSConfigModul.UseMHD);
  AuftPosChangesDBGrid.SetColumnVisible('CHANGE_MHD_MIN', LVSConfigModul.UseMHD);

  AuftPosChangesDBGrid.SetColumnVisible('CHANGE_GEWICHT',        LVSConfigModul.UseGewicht);
  AuftPosChangesDBGrid.SetColumnVisible('CHANGE_GESAMT_GEWICHT', LVSConfigModul.UseGewicht);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowAuftragChangesForm.Prepare (const RefAuf : Integer);
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select a.*,adr.LAND,adr.LAND_ISO,lt.NAME as LT_NAME from V_AUFTRAG a, V_AUFTRAG_ADR adr, V_LT_TYPEN lt where adr.REF=a.REF_LIEFER_ADR and lt.REF(+)=a.REF_LT and a.REF=:ref_auf');
    query.Parameters.ParamByName('ref_auf').Value := RefAuf;

    query.Open;

    AuftragNrLabel.Caption := query.FieldByName('AUFTRAG_NR').AsString;

    if (query.FieldByName('KUNDEN_NR').IsNull) then
      WarenempfLabel.Caption := ''
    else
      WarenempfLabel.Caption := query.FieldByName('KUNDEN_NR').AsString + ' / ';

    WarenempfLabel.Caption := WarenempfLabel.Caption + query.FieldByName('KUNDEN_NAME').AsString + ' / ' + query.FieldByName('LAND').AsString + ' ('+query.FieldByName('LAND_ISO').AsString+')';

    query.Close;
  finally
    query.Free;
  end;


  AufChangeDataSet.CommandText := 'select * from V_AUFTRAG_CHANGE_LOG where REF_AUF_KOPF=:ref_auf';
  AufChangeDataSet.Parameters.ParamByName('ref_auf').Value := RefAuf;

  AufPosChangesDataSet.CommandText := 'select * from V_AUFTRAG_POS_CHANGE_LOG where REF_AUF_KOPF=:ref_auf';
  AufPosChangesDataSet.Parameters.ParamByName('ref_auf').Value := RefAuf;
end;

end.
