unit ArtikelIdentInputFRM;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms, 
  Dialogs, StdCtrls, ExtCtrls, ComboBoxPro;

type
  TArtikelIdentInputFrame = class(TFrame)
    Panel1: TPanel;
    InfoLabel: TLabel;
    InfoEdit: TEdit;
    InfoComboBox: TComboBoxPro;
    InfoCheckBox: TCheckBox;
    InfoMemo: TMemo;
    procedure InfoEditChange(Sender: TObject);
  private
    fChanged     : Boolean;
    fNotNull     : Boolean;
    fIdentName   : String;
  public
    property IdentName       : String  read fIdentName   write fIdentName;
    property NotNull         : Boolean read fNotNull     write fNotNull;
    property IsChanged       : Boolean read fChanged     write fChanged;

    constructor Create(AOwner: TComponent); override;
  end;

implementation

{$R *.dfm}

constructor TArtikelIdentInputFrame.Create(AOwner: TComponent);
begin
  inherited Create (AOwner);

  fIdentName := '';

  InfoEdit.Text := '';
  InfoMemo.Text := '';
end;

procedure TArtikelIdentInputFrame.InfoEditChange(Sender: TObject);
begin
  fChanged := True;

  if (Sender is TEdit) then
    (Sender as TEdit).Color := clWindow
  else if (Sender is TMemo) then
    (Sender as TMemo).Color := clWindow
  else if (Sender is TComboBoxPro) then
    (Sender as TComboBoxPro).Color := clWindow;
end;

end.
