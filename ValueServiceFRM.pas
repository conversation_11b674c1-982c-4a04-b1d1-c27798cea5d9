unit ValueServiceFRM;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms, 
  Dialogs, StdCtrls, ExtCtrls;

type
  TValueServiceFrame = class(TFrame)
    ServiceLabel: TLabel;
    ServiceInfoLabel: TLabel;
    DoneCheckBox: TCheckBox;
    DoneButton: TButton;
    Bevel1: TBevel;
    Bevel2: TBevel;
    Label3: TLabel;
    ArtikelLabel: TLabel;
    procedure DoneButtonClick(Sender: TObject);
  private
    fRef : Integer;
  public
    property REf : Integer read fRef write fRef;

    constructor Create(AOwner: TComponent); override;
  end;

implementation

{$R *.dfm}

uses
  SprachModul, ResourceText;

constructor TValueServiceFrame.Create(AOwner: TComponent);
begin
  inherited Create(AOwner);

  fRef := -1;

  ServiceLabel.Caption := '';
  ServiceInfoLabel.Caption := '';

  LVSSprachModul.InitFrame (Self);
end;

procedure TValueServiceFrame.DoneButtonClick(Sender: TObject);
begin
  DoneCheckBox.Checked := True;
end;

end.
