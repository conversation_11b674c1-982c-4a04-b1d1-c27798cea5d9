unit NewCfgParamDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, ExtCtrls, StdCtrls, ComboBoxPro;

type
  TNewParamForm = class(TForm)
    MandantComboBox: TComboBoxPro;
    LagerComboBox: TComboBoxPro;
    ParamComboBox: TComboBoxPro;
    AbortButton: TButton;
    Label1: TLabel;
    Label2: TLabel;
    Bevel1: TBevel;
    Label3: TLabel;
    OkButton: TButton;
    LocationComboBox: TComboBoxPro;
    Label4: TLabel;
    procedure LocationComboBoxChange(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure MandantComboBoxChange(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    fRefMand  : Integer;
    fRefLoc   : Integer;
    fRefLager : Integer;
  public
    procedure Prepare (const RefMand, RefLoc, RefLager : Integer);
  end;

implementation

{$R *.dfm}

uses <PERSON><PERSON><PERSON><PERSON>s, DatenModul, FrontendUtils, ConfigModul, SprachModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TNewParamForm.Prepare (const RefMand, RefLoc, RefLager : Integer);
var
  idx,
  selidx   : Integer;
  desc     : String;
  query    : TADOQuery;
  subquery : TADOQuery;
begin
  fRefMand := RefMand;
  fRefLoc  := RefLoc;
  fRefLager:= RefLager;

  query := TADOQuery.Create (Nil);
  subquery := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    subquery.LockType := ltReadOnly;
    subquery.Connection := LVSDatenModul.MainADOConnection;

    if (fRefLager <> -1) then begin
      query.SQL.Clear;
      query.SQL.Add ('select REF_LOCATION from V_LOCATION_REL_LAGER where REF_LAGER='+IntToStr (fRefLager));

      query.Open;

      if (query.RecordCount > 0) then
        fRefLoc := query.Fields [0].AsInteger;

      query.Close;
    end;

    MandantComboBox.Items.Insert (0, '');

    query.SQL.Clear;
    query.SQL.Add ('select REF,NAME,BESCHREIBUNG from V_PCD_MANDANT');

    if (fRefMand <> -1) then
      query.SQL.Add ('where REF='+IntToStr (fRefMand));

    query.SQL.Add ('order by upper (NAME)');

    try
      query.Open;

      while not (query.Eof) do begin
        MandantComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        if (LVSConfigModul.UseSubMandanten) then begin
          subquery.SQL.Clear;
          subquery.SQL.Add ('select REF,NAME,BESCHREIBUNG from V_MANDANT where REF_MASTER_MAND='+query.Fields [0].AsString+' order by upper (NAME)');

          try
            subquery.Open;

            while not (subquery.Eof) do begin
              MandantComboBox.Items.AddObject (query.Fields [1].AsString+':'+subquery.Fields [1].AsString+'|'+subquery.Fields [2].AsString, TComboBoxRef.Create (subquery.Fields [0].AsInteger));

              subquery.Next;
            end;

            subquery.Close;
          except
          end;
        end;

        query.Next;
      end;

      query.Close;
    except
    end;

    if (fRefMand = -1) then
      MandantComboBox.ItemIndex := 0
    else begin
      MandantComboBox.Enabled := (MandantComboBox.Items.Count > 2);
      MandantComboBox.ItemIndex := FindComboboxRef (MandantComboBox, fRefMand)
    end;

    if (RefLoc <> -1) or (RefLager <> -1) then begin
      MandantComboBoxChange (Nil)
    end else begin
      LocationComboBox.Enabled := False;
      LagerComboBox.Enabled := False;
    end;

    selidx := -1;

    ClearComboBoxObjects (ParamComboBox);

    query.SQL.Clear;
    query.SQL.Add ('select * from V_INF_WERTE where TABELLE=''SYS_CONFIG''');

    if (RefMand <> -1) then
      query.SQL.Add ('and SPALTE in (''LAGER_PARAM'', ''MAND_PARAM'')')
    else
      query.SQL.Add ('and SPALTE=''LAGER_PARAM''');

    if (RefLager <> -1) then begin
      if (RefMand = -1) then
        query.SQL.Add ('and WERT not in (select NAME from V_SYS_CONFIG where REF_MAND is null and REF_LAGER='+IntToStr (RefLager)+')')
      else
        query.SQL.Add ('and WERT not in (select NAME from V_SYS_CONFIG where REF_MAND='+IntToStr (fRefMand)+' and REF_LAGER='+IntToStr (RefLager)+')')
    end else if (RefLoc <> -1) then begin
      if (RefMand = -1) then
        query.SQL.Add ('and WERT not in (select NAME from V_SYS_CONFIG where REF_MAND is null and REF_LOCATION='+IntToStr (RefLoc)+')')
      else
        query.SQL.Add ('and WERT not in (select NAME from V_SYS_CONFIG where REF_MAND='+IntToStr (fRefMand)+' and REF_LOCATION='+IntToStr (RefLoc)+')');
    end else if (RefMand <> -1) then begin
      query.SQL.Add ('and WERT not in (select NAME from V_SYS_CONFIG where REF_MAND='+IntToStr (fRefMand)+')');
    end;

    query.SQL.Add ('order by REIHEN_FOLGE, WERT');

    Screen.Cursor := crSQLWait;

    try
      query.Open;

      while not (query.Eof) do begin
        if Assigned (query.FindField ('STR_WERT')) then
          desc := query.FieldByName ('STR_WERT').AsString
        else
          desc := query.FieldByName ('WERT').AsString;

        idx := ParamComboBox.AddItemIndex (desc + '|' + query.FieldByName ('TEXT').AsString,
 TDBItemsDaten.Create (query.FieldByName ('SPALTE').AsString, query.FieldByName ('WERT').AsString, query.FieldByName ('TEXT').AsString,
      query.FieldByName ('DEFAULT_SPALTE').AsString, query.FieldByName ('ART').AsString));

        if not (query.FieldByName ('DEFAULT_SPALTE').IsNull) and (query.FieldByName ('DEFAULT_SPALTE').AsInteger = 1) then
          selidx := idx;

        query.Next;
      end;

      query.Close;
    except
    end;

    Screen.Cursor := crDefault;
  finally
    subquery.Free;
    query.Free;
  end;

  if (selidx <> -1) then
    ParamComboBox.ItemIndex := selidx;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.01.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TNewParamForm.FormCreate(Sender: TObject);
begin
  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, MandantComboBox);
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
    LVSSprachModul.SetNoTranslate (Self, LocationComboBox);
    LVSSprachModul.SetNoTranslate (Self, ParamComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TNewParamForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (MandantComboBox);
  ClearComboBoxObjects (LocationComboBox);
  ClearComboBoxObjects (LagerComboBox);
  ClearComboBoxObjects (ParamComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TNewParamForm.FormShow(Sender: TObject);
begin
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TNewParamForm.LocationComboBoxChange(Sender: TObject);
begin
  ClearComboBoxObjects (LagerComboBox);

  if (GetComboBoxRef (LocationComboBox) = -1) then begin
    LagerComboBox.Enabled := False;
    LagerComboBox.ItemIndex := -1;
    LagerComboBox.Text := '';
  end else if (fRefLager = -1) then begin
    LagerComboBox.Enabled := False;
    LagerComboBox.ItemIndex := -1;
    LagerComboBox.Text := '';
  end else begin
    LagerComboBox.Enabled := False;
    LoadLagerCombobox (LagerComboBox, GetComboBoxRef (LocationComboBox), GetComboBoxRef (MandantComboBox));
    LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, fRefLager);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TNewParamForm.MandantComboBoxChange(Sender: TObject);
begin
  LoadLocationCombobox (LocationComboBox, GetComboBoxRef (MandantComboBox));
  LocationComboBox.Items.Insert (0, '');

  if (fRefLoc = -1) then
    LocationComboBox.ItemIndex := 0
  else begin
    LocationComboBox.Enabled := False;
    LocationComboBox.ItemIndex := FindComboboxRef (LocationComboBox, fRefLoc);
  end;

  LocationComboBoxChange (Sender);
end;

end.
