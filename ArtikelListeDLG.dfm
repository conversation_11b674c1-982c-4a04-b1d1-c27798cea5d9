object ArtikelListeForm: TArtikelListeForm
  Left = 332
  Top = 406
  BorderIcons = [biSystemMenu]
  Caption = 'ArtikelListeForm'
  ClientHeight = 382
  ClientWidth = 880
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnHide = FormHide
  OnKeyDown = FormKeyDown
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 70
    Width = 880
    Height = 29
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      880
      29)
    object Label1: TLabel
      Left = 8
      Top = 12
      Width = 47
      Height = 13
      Caption = 'Artikelliste'
    end
    object Bevel1: TBevel
      Left = 6
      Top = 2
      Width = 867
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
  end
  object FussPanel: TPanel
    Left = 0
    Top = 291
    Width = 880
    Height = 91
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 6
    DesignSize = (
      880
      91)
    object OkButton: TButton
      Left = 705
      Top = 59
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Ok'
      Default = True
      ModalResult = 1
      TabOrder = 5
    end
    object AbortButton: TButton
      Left = 796
      Top = 59
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 6
    end
    object INACheckBox: TCheckBox
      Left = 8
      Top = 8
      Width = 353
      Height = 17
      Caption = 'deaktivierte Artikel auch anzeigen'
      TabOrder = 0
      OnClick = CheckBoxUpdateClick
    end
    object BeschaffungCheckBox: TCheckBox
      Left = 8
      Top = 39
      Width = 321
      Height = 17
      Caption = 'Nur Beschaffungsartikel anzeigen'
      TabOrder = 2
      OnClick = CheckBoxUpdateClick
    end
    object LieferantCheckBox: TCheckBox
      Left = 8
      Top = 54
      Width = 329
      Height = 17
      Caption = 'Nur Artikel des Lieferanten anzeigen'
      TabOrder = 3
      OnClick = CheckBoxUpdateClick
    end
    object BestandCheckBox: TCheckBox
      Left = 8
      Top = 70
      Width = 329
      Height = 17
      Caption = 'Nur Artikel mit Bestand anzeigen'
      TabOrder = 4
      Visible = False
      OnClick = CheckBoxUpdateClick
    end
    object ListedCheckBox: TCheckBox
      Left = 8
      Top = 24
      Width = 353
      Height = 17
      Caption = 'nicht gelistete Artikel auch anzeigen'
      TabOrder = 1
      OnClick = CheckBoxUpdateClick
    end
  end
  object Panel3: TPanel
    Left = 0
    Top = 99
    Width = 8
    Height = 192
    Align = alLeft
    BevelOuter = bvNone
    TabOrder = 3
  end
  object Panel4: TPanel
    Left = 872
    Top = 99
    Width = 8
    Height = 192
    Align = alRight
    BevelOuter = bvNone
    TabOrder = 4
  end
  object ArtikelDBGrid: TDBGridPro
    Left = 8
    Top = 99
    Width = 864
    Height = 192
    Align = alClient
    DataSource = ArtikelDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = ArtikelDBGridPopupMenu
    ReadOnly = True
    TabOrder = 5
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDblClick = ArtikelDBGridDblClick
    OnKeyPress = ArtikelDBGridKeyPress
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
    OnColumnSort = ArtikelDBGridColumnSort
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 0
    Width = 880
    Height = 35
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    Visible = False
    DesignSize = (
      880
      35)
    object Label11: TLabel
      Left = 8
      Top = 11
      Width = 67
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 104
      Top = 8
      Width = 768
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      ItemHeight = 16
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object ARGrpPanel: TPanel
    Left = 0
    Top = 35
    Width = 880
    Height = 35
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      880
      35)
    object Label2: TLabel
      Left = 8
      Top = 11
      Width = 62
      Height = 13
      Caption = 'Artikelgruppe'
    end
    object ARGrpComboBox: TComboBoxPro
      Left = 104
      Top = 8
      Width = 768
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      ItemHeight = 16
      TabOrder = 0
      OnChange = ARGrpComboBoxChange
    end
  end
  object ArtikelQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    AfterClose = ArtikelQueryAfterClose
    Parameters = <>
    Left = 248
    Top = 128
  end
  object ArtikelDataSource: TDataSource
    DataSet = ArtikelQuery
    Left = 64
    Top = 136
  end
  object ArtikelDBGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = ArtikelDBGridPopupMenuPopup
    Left = 360
    Top = 72
    object ShowArtikelPictureMenuItem: TMenuItem
      Caption = 'Artikelbild anzeigen...'
      ImageIndex = 24
      OnClick = ShowArtikelPictureMenuItemClick
    end
  end
end
