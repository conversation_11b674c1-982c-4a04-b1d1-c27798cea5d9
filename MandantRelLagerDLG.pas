unit MandantRelLagerDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, CheckLst, Menus;

type
  TMandantRelLagerForm = class(TForm)
    MandantCheckListBox: TCheckListBox;
    OkButton: TButton;
    AbortButton: TButton;
    ListBoxPopupMenu: TPopupMenu;
    Konfigurationprfen1: TMenuItem;
    procedure MandantCheckListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
    procedure Konfigurationprfen1Click(Sender: TObject);
  private
    fRefMand : Integer;
  public
    property RefMand : Integer read fRefMand write fRefMand;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, CheckConfigDLG;

procedure TMandantRelLagerForm.Konfigurationprfen1Click(Sender: TObject);
var
  checkform : TCheckConfigForm;
begin
  if (GetListBoxRef (MandantCheckListBox) <> -1) then begin
    checkform := TCheckConfigForm.Create (Self);

    checkform.RefMand  := fRefMand;
    checkform.RefLager := GetListBoxRef (MandantCheckListBox);

    checkform.ShowModal;

    checkform.Release;
  end;
end;

procedure TMandantRelLagerForm.MandantCheckListBoxDrawItem(
  Control: TWinControl; Index: Integer; Rect: TRect;
  State: TOwnerDrawState);
var
  line : String;
  strpos : Integer;
begin
  if (Control is TListBox) then begin
    line := (Control as TListBox).Items [Index];
    strpos := Pos ('|', line);

    with (Control as TListBox).Canvas do begin
      FillRect(Rect);

      TextOut (Rect.Left, Rect.Top, Copy (line,1,strpos - 1));
      TextOut (Rect.Left + 80, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
    end;
  end else if (Control is TCheckListBox) then begin
    line := (Control as TCheckListBox).Items [Index];
    strpos := Pos ('|', line);

    with (Control as TCheckListBox).Canvas do begin
      FillRect(Rect);

      TextOut (Rect.Left, Rect.Top, Copy (line,1,strpos - 1));
      TextOut (Rect.Left + 100, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
    end;
  end;
end;

end.
