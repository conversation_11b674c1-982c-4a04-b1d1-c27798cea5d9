object WarenempfForm: TWarenempfForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Daten der Warenempf'#228'nger'
  ClientHeight = 545
  ClientWidth = 928
  Color = clBtnFace
  Constraints.MinHeight = 400
  Constraints.MinWidth = 700
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnKeyUp = FormKeyUp
  OnShow = FormShow
  TextHeight = 13
  object WarenempfPanel: TPanel
    Left = 0
    Top = 46
    Width = 928
    Height = 154
    Align = alClient
    BevelOuter = bvNone
    Caption = 'WarenempfPanel'
    TabOrder = 1
    DesignSize = (
      928
      154)
    object Label7: TLabel
      Left = 8
      Top = 8
      Width = 84
      Height = 13
      Caption = 'Warenempf'#228'nger'
    end
    object WarenempfDBGrid: TDBGridPro
      Left = 8
      Top = 24
      Width = 912
      Height = 128
      Hint = 
        'Order with the left mouse button, searching with the right mouse' +
        ' button'
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = WarenempfDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      PopupMenu = WarenempfDBGridPopupMenu
      ReadOnly = True
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      OnDblClick = EditButtonClick
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'Tahoma'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
      OnColumnSort = WarenempfDBGridColumnSort
    end
    object ShowDelCheckBox: TCheckBox
      Left = 799
      Top = 4
      Width = 121
      Height = 17
      Anchors = [akTop, akRight]
      Caption = 'Gel'#246'schte anzeigen'
      TabOrder = 1
    end
  end
  object ChangeButtonPanel: TPanel
    Left = 0
    Top = 200
    Width = 928
    Height = 31
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      928
      31)
    object NewButton: TButton
      Left = 8
      Top = 4
      Width = 100
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'Neue...'
      TabOrder = 0
      OnClick = NewButtonClick
    end
    object EditButton: TButton
      Left = 123
      Top = 4
      Width = 100
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'Bearbeiten...'
      TabOrder = 1
      OnClick = EditButtonClick
    end
    object DeleteButton: TButton
      Left = 237
      Top = 4
      Width = 100
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'L'#246'schen...'
      Enabled = False
      TabOrder = 2
      OnClick = DeleteButtonClick
    end
  end
  object BottomPanel: TPanel
    Left = 0
    Top = 231
    Width = 928
    Height = 314
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      928
      314)
    object LogiDataGroupBox: TGroupBox
      Left = 8
      Top = 11
      Width = 912
      Height = 264
      Anchors = [akLeft, akTop, akRight, akBottom]
      Caption = 'Daten f'#252'r die Lagerabwicklung'
      TabOrder = 0
      DesignSize = (
        912
        264)
      object UpdateButton: TButton
        Left = 736
        Top = 231
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = #220'bernehmen'
        TabOrder = 1
        OnClick = UpdateButtonClick
      end
      object QuashButton: TButton
        Left = 825
        Top = 231
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Verwerfen'
        TabOrder = 2
        OnClick = QuashButtonClick
      end
      object PageControl1: TPageControl
        Left = 11
        Top = 24
        Width = 889
        Height = 201
        ActivePage = TourTabSheet
        Anchors = [akLeft, akTop, akRight, akBottom]
        TabOrder = 0
        object TourTabSheet: TTabSheet
          Caption = 'Planung'
          DesignSize = (
            881
            173)
          object Label10: TLabel
            Left = 8
            Top = 19
            Width = 27
            Height = 13
            Caption = 'Lager'
          end
          object LagerComboBox: TComboBoxPro
            Left = 80
            Top = 16
            Width = 794
            Height = 22
            Style = csOwnerDrawFixed
            Anchors = [akLeft, akTop, akRight]
            ColWidth = 120
            TabOrder = 0
            OnChange = LagerComboBoxChange
          end
          object AssignGroupBox: TGroupBox
            Left = 3
            Top = 60
            Width = 871
            Height = 109
            Anchors = [akLeft, akTop, akRight]
            TabOrder = 1
            object Label9: TLabel
              Left = 8
              Top = 25
              Width = 38
              Height = 13
              Caption = 'Priorit'#228't'
            end
            object Spedition: TLabel
              Left = 8
              Top = 52
              Width = 44
              Height = 13
              Caption = 'Spedition'
            end
            object Label3: TLabel
              Left = 8
              Top = 81
              Width = 37
              Height = 13
              Caption = 'Tour-Nr'
            end
            object Label4: TLabel
              Left = 214
              Top = 81
              Width = 76
              Height = 13
              Caption = 'Tourreihenfolge'
            end
            object Label11: TLabel
              Left = 376
              Top = 53
              Width = 60
              Height = 13
              Caption = 'WA-Relation'
            end
            object PrioEdit: TEdit
              Left = 80
              Top = 22
              Width = 105
              Height = 21
              TabOrder = 0
              Text = 'PrioEdit'
              OnChange = ChangeLagerInfoData
              OnKeyPress = PrioEditKeyPress
            end
            object TourEdit: TEdit
              Left = 80
              Top = 78
              Width = 105
              Height = 21
              MaxLength = 8
              TabOrder = 3
              Text = 'TourEdit'
              OnChange = ChangeLagerInfoData
            end
            object TourIndexEdit: TEdit
              Left = 296
              Top = 78
              Width = 41
              Height = 21
              TabOrder = 4
              Text = '0'
              OnChange = ChangeLagerInfoData
              OnKeyPress = TourIndexEditKeyPress
            end
            object TourIndexUpDown: TUpDown
              Left = 337
              Top = 78
              Width = 16
              Height = 21
              Associate = TourIndexEdit
              Max = 999
              TabOrder = 5
            end
            object SpedComboBox: TComboBoxPro
              Left = 80
              Top = 50
              Width = 273
              Height = 22
              Style = csOwnerDrawFixed
              TabOrder = 1
              OnChange = ChangeLagerInfoData
            end
            object WARelComboBox: TComboBoxPro
              Left = 448
              Top = 50
              Width = 273
              Height = 22
              Style = csOwnerDrawFixed
              TabOrder = 2
              OnChange = ChangeLagerInfoData
            end
          end
          object AssignCheckBox: TCheckBox
            Left = 11
            Top = 56
            Width = 129
            Height = 17
            Caption = 'In diesem Lager aktiv'
            TabOrder = 2
            OnClick = AssignCheckBoxClick
          end
        end
        object KommTabSheet: TTabSheet
          Caption = 'Kommissionierung'
          ImageIndex = 1
          DesignSize = (
            881
            173)
          object Label2: TLabel
            Left = 8
            Top = 17
            Width = 147
            Height = 13
            Caption = 'Abweichende Kommissionierart'
          end
          object KommUserPanel: TPanel
            Left = 313
            Top = 16
            Width = 565
            Height = 37
            Anchors = [akLeft, akTop, akRight]
            BevelOuter = bvNone
            TabOrder = 1
            DesignSize = (
              565
              37)
            object Label1: TLabel
              Left = 0
              Top = 1
              Width = 210
              Height = 13
              Caption = 'Vorgesehener Kommissionierer bzw. Gruppe'
            end
            object KommUserComboBox: TComboBoxPro
              Left = 0
              Top = 16
              Width = 565
              Height = 21
              Style = csOwnerDrawFixed
              Anchors = [akLeft, akTop, akRight]
              ColWidth = 60
              ItemHeight = 15
              TabOrder = 0
              OnChange = ChangeLogiData
              OnDrawItem = KommUserComboBoxDrawItem
            end
          end
          object KommArtComboBox: TComboBoxPro
            Left = 8
            Top = 32
            Width = 278
            Height = 21
            Style = csOwnerDrawFixed
            ColWidth = 60
            ItemHeight = 15
            TabOrder = 0
            OnClick = ChangeLogiData
          end
          object KommOptCheckBox: TCheckBox
            Left = 8
            Top = 104
            Width = 263
            Height = 17
            Caption = 'Kommissionierverdichtung f'#252'r St'#252'ckartikel zul'#228'ssig'
            TabOrder = 4
            OnClick = ChangeLogiData
          end
          object AutoKommCheckBox: TCheckBox
            Left = 8
            Top = 64
            Width = 147
            Height = 17
            Caption = 'Automatisch planen'
            TabOrder = 2
            OnClick = ChangeLogiData
          end
          object PacklisteCheckBox: TCheckBox
            Left = 8
            Top = 84
            Width = 202
            Height = 17
            Caption = 'Einzelne Packzettel bilden'
            TabOrder = 3
            OnClick = ChangeLogiData
          end
        end
        object WATabSheet: TTabSheet
          Caption = 'Warenausgang'
          ImageIndex = 2
          DesignSize = (
            881
            173)
          object Label5: TLabel
            Left = 8
            Top = 7
            Width = 95
            Height = 13
            Caption = 'NVE-Label Optionen'
          end
          object Label6: TLabel
            Left = 317
            Top = 7
            Width = 105
            Height = 13
            Caption = 'Text f'#252'r Zusatzetikett'
          end
          object Label8: TLabel
            Left = 8
            Top = 120
            Width = 76
            Height = 13
            Caption = 'Lademittelkonto'
          end
          object NVEOptComboBox: TComboBoxPro
            Left = 8
            Top = 23
            Width = 284
            Height = 21
            Style = csDropDownList
            ItemIndex = 0
            TabOrder = 0
            Text = 'Standard'
            OnClick = ChangeLogiData
            Items.Strings = (
              'Standard'
              'Bruttogewicht im 2.Barcode'
              'EDEKA-Format'
              'SPAR-Format'
              'dm-Format'
              'REWE-Format')
          end
          object LabelMemo: TMemo
            Left = 317
            Top = 23
            Width = 555
            Height = 134
            Anchors = [akLeft, akTop, akRight]
            Lines.Strings = (
              'LabelMemo')
            MaxLength = 120
            TabOrder = 5
            OnClick = ChangeLogiData
          end
          object LagerVerkaufCheckBox: TCheckBox
            Left = 8
            Top = 56
            Width = 214
            Height = 17
            Caption = 'Lagerverkauf zul'#228'ssig'
            TabOrder = 1
            OnClick = ChangeLogiData
          end
          object SammelLSCheckBox: TCheckBox
            Left = 8
            Top = 76
            Width = 202
            Height = 17
            Caption = 'Sammellieferschein erlaubt'
            TabOrder = 2
            OnClick = ChangeLogiData
          end
          object LeerComboBox: TComboBoxPro
            Left = 8
            Top = 136
            Width = 284
            Height = 21
            Style = csOwnerDrawFixed
            ItemHeight = 15
            PopupMenu = LeerComboxPopupMenu
            TabOrder = 4
            OnClick = ChangeLogiData
          end
          object AuslagerCheckBox: TCheckBox
            Left = 8
            Top = 96
            Width = 202
            Height = 17
            Caption = 'Auslagerungen erlaubt'
            TabOrder = 3
            OnClick = ChangeLogiData
          end
        end
      end
    end
    object CloseButton: TButton
      Left = 845
      Top = 281
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 1
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 0
    Width = 928
    Height = 46
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      928
      46)
    object Label12: TLabel
      Left = 8
      Top = 4
      Width = 69
      Height = 13
      Alignment = taRightJustify
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 912
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object WarenempfDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 728
    Top = 16
  end
  object WarenempfDataSource: TDataSource
    DataSet = WarenempfDataSet
    OnDataChange = WarenempfDataSourceDataChange
    Left = 696
    Top = 16
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 664
    Top = 16
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 624
    Top = 56
  end
  object LeerComboxPopupMenu: TPopupMenu
    Left = 496
    Top = 328
    object NewLeerKontoMenuItem: TMenuItem
      Caption = 'Neues Lademittelkonto anlegen...'
      OnClick = NewLeerKontoMenuItemClick
    end
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 680
    Top = 56
  end
  object WarenempfDBGridPopupMenu: TPopupMenu
    Left = 336
    Top = 64
    object ImportEmpfMenuItem: TMenuItem
      Caption = '&Import Warenempf'#228'nger...'
      OnClick = ImportEmpfMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object AlleWarenempfngerdrucken1: TMenuItem
      Caption = '&Alle Warenempf'#228'nger drucken...'
      OnClick = AlleWarenempfngerdrucken1Click
    end
  end
end
