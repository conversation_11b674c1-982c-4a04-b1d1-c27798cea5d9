unit DisplayVerteilPlanDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, Grids, DBGrids, SMDBGrid, DBGridPro, StdCtrls;

type
  TDisplayVerteilPlanForm = class(TForm)
    CloseButton: TButton;
    VTLDBGrid: TDBGridPro;
    VTLDataSource: TDataSource;
    VTLQuery: TADOQuery;
    PrintButton: TButton;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure PrintButtonClick(Sender: TObject);
  private
    fRefBatch : Integer;
  public
    property RefBatch : Integer read fRefBatch write fRefBatch;
  end;

implementation

{$R *.dfm}

uses
  DatenModul, DBGridUtilModule, ConfigModul, PrintModul, SprachModul, ResourceText;

procedure TDisplayVerteilPlanForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  VTLQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

procedure TDisplayVerteilPlanForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, AuftragNrLabel);
  {$endif}
end;

procedure TDisplayVerteilPlanForm.FormShow(Sender: TObject);
var
  query : TADOQuery;
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.RestoreFormInfo (Self);

  if (fRefBatch <> -1) then begin
    query := TADOQuery.Create (Nil);
    try
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Clear;
      query.SQL.Add ('select * from V_AUFTRAG_BATCHLAUF where REF='+IntToStr (fRefBatch));

      try
        query.Open;

        Caption := FormatResourceText (1791, [query.FieldByName ('BATCH_NR').AsString]);

        query.Close;
      except
      end;
    finally
      query.Free;
    end;

    VTLQuery.SQL.Add ('select av.REF, av.STATUS,av.FACH_NR,av.LT_NR,a.AUFTRAG_NR,a.KUNDEN_NR,a.KUNDEN_NAME,a.LIEFERSCHEIN_NR,av.CLOSE_DATE from V_AUFTRAG_VERTEILUNG av, V_AUFTRAG a where a.REF=av.REF_AUF_KOPF and av.REF_BATCHLAUF='+IntToStr (fRefBatch));
    VTLQuery.SQL.Add ('order by FACH_NR');
    
    VTLQuery.Open;
  end;
end;

procedure TDisplayVerteilPlanForm.PrintButtonClick(Sender: TObject);
var
  res    : Integer;
  errtxt : String;
begin
  res := PrintModule.PreparePreview;

  if (res = 0) then begin
    res := PrintModule.PrintReport('', PrintModule.StdLaserPrinter.Port, '', -1, -1, '', 'BATCHVERTEILPLAN', '', ['REF:' + IntToStr (fRefBatch)], errtxt, True);

    PrintModule.BeginPreview;
  end;
end;

end.
