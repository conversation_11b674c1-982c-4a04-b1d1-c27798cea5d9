unit DMSContenInfoFRM;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms, 
  Dialogs, StdCtrls;

type
  TDMSContenInfoFrame = class(TFrame)
    DescMemo: TMemo;
    Label1: TLabel;
    Label2: TLabel;
    CreateAtLabel: TLabel;
    CreateFromLabel: TLabel;
  private
    { Private-Deklarationen }
  public
    constructor Create(AOwner: TComponent); override;
  end;

implementation

{$R *.dfm}

constructor TDMSContenInfoFrame.Create(AOwner: TComponent);
begin
  inherited Create (AOwner);

  CreateAtLabel.Caption := '';
  CreateFromLabel.Caption := '';
  DescMemo.Text := '';
end;


end.
