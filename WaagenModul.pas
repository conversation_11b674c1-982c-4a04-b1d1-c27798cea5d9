unit WaagenModul;

interface

uses
  Classes, OverbyteIcsWSocket, OverbyteIcsWndControl;

type
  TWiegeThread = class (TThread)
  private
    fWeight      : Integer;
    fWaageType   : String;
    fWaageIP     : String;
    fWaagePort   : Integer;
    fWaageOnline : Boolean;

    fWaageSocket : TWSocket;
  protected
    procedure Execute; override;
    destructor Destroy; override;
  public
    constructor Create(CreateSuspended: Boolean); overload;

    property WaageOnline : Boolean   read fWaageOnline;
    property Weight      : Integer   read fWeight;

    property WaageType  : String    read fWaageType write fWaageType;
    property WaageIP    : String    read fWaageIP   write fWaageIP;
    property WaagePort  : Integer   read fWaagePort write fWaagePort;
  end;


implementation

uses
  Windows, Forms, Sysutils, ErrorTracking;

constructor TWiegeThread.Create(CreateSuspended: Boolean);
begin
  inherited Create(CreateSuspended);

  fWeight    := -1;
  fWaageIP   := '';
  WaageType  := '';
  fWaagePort := -1;
  fWaageOnline := false;

  fWaageSocket := TWSocket.Create (Nil);
  fWaageSocket.ReuseAddr := true;
  fWaageSocket.MultiThreaded := true;
  //fWaageSocket.LocalAddr := fWaageSocket.GetXAddr;
end;

destructor TWiegeThread.Destroy;
begin
  try
    fWaageSocket.Close;

    FreeAndNil (fWaageSocket);
  except
    fWaageSocket := Nil;
  end;

  inherited Destroy;
end;

procedure TWiegeThread.Execute;
const
  dotfrm  : TFormatSettings = (DecimalSeparator: '.');
var
  gwstr,
  outstr  : AnsiString;
  fwert   : Double;
  count   : Integer;
begin
  fWaageOnline := false;

  try
    while not (Terminated) do begin
      Sleep (1000);

      if not Assigned (fWaageSocket) then
        break;

      if (Length (fWaageIP) > 0) and (fWaagePort > 0) then begin
        if (fWaageSocket.State <> wsConnected) then begin
          fWeight := -1;
          fWaageOnline  := false;

          fWaageSocket.Proto   := 'tcp';
          fWaageSocket.Addr    := fWaageIP;
          fWaageSocket.Port    := IntToStr (fWaagePort);
          //fWaageSocket.ReuseAddr := true;
          fWaageSocket.MultiThreaded := true;

          try
            fWaageSocket.Connect;

            if not Assigned (fWaageSocket) then
              break;

            count := 500;

            while Assigned (fWaageSocket) and not (fWaageSocket.State = wsConnected) and (count > 0) and not (Terminated) do begin
              Application.ProcessMessages;

              if not Assigned (fWaageSocket) then
                break;

              Sleep (10);
              Dec (count);
            end;

            if Assigned (fWaageSocket) and (fWaageSocket.State <> wsConnected) then
              fWaageSocket.Abort;
          except
            on  E: Exception do begin
              fWaageSocket.Abort;

              ErrorTrackingModule.WriteErrorLog ('TWiegeThread.Execute', fWaageIP+':'+IntToStr (fWaagePort)+e.ClassName + ' > ' + e.Message);
            end;
          end;

          if not Assigned (fWaageSocket) then
            break;
        end;

        if not Assigned (fWaageSocket) then
          break;

        if not (Terminated) and (fWaageSocket.State = wsConnected) then begin
          fWaageOnline := true;

          outstr := '<GB1>'+#13;
          fWaageSocket.SendStr (outstr);

          fWaageSocket.ReadLine (-500, gwstr);

          if (Length (gwstr) > 0) then begin
            if TryStrToFloat (trim (copy (gwstr, 1, 8)), fwert, dotfrm) then
              fWeight := round (fwert * 1000.0);
          end;
        end else begin
          fWeight       := -1;
          fWaageOnline  := false;
        end;
      end;
    end;
  finally
    if Assigned (fWaageSocket) then
      fWaageSocket.Close;
  end;
end;

end.
