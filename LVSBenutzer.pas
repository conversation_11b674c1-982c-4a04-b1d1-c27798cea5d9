﻿//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : LVSBenutzer
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/LVSBenutzer.pas $
// $Revision: 55 $
// $Modtime: 11.03.23 10:11 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Benutzer- und Rechtverwaltung ein der Datenbank
//*****************************************************************************
unit LVSBenutzer;

interface

uses
  DB, ADODB, ACOList, LVSSecurity;

var
  AktMandantRecht : TLVSRechte;

function CheckUserExists (const Username : String; var Found, Deleted : Boolean; var UserRef : Integer) : Integer;

function CheckOrcaleUserExists (const Username : String; var Found : Boolean) : Integer;

function CreateUser (const RefFirma : Integer; const UserID, UserShortName, UserNumID, UserName, DBUserName, UserTitle, Password, DomName, DomUser, Email : String; const AdminFlag : Boolean; var UserRef : Integer) : Integer;
function ChangeUser (const Ref, RefFirma : Integer; const UserID, UserShortName, UserNumID, UserName, DBUserName, UserTitle, Sprache, Password, DomName, DomUser, Email : String; const AdminFlag : Boolean) : Integer;

function ActivateUser   (const UserRef : Integer) : Integer;
function DeactivateUser (const UserRef : Integer) : Integer;
function DeleteUser     (const UserRef : Integer) : Integer;

function CreateUserGroupe (const GrpArt, GrpID, GrpName : String; const RefLoc : Integer; const AdminFlag : Boolean) : Integer;
function ChangeUserGroupe (const Ref : Integer; const GrpID, GrpName : String; const RefLoc : Integer; const AdminFlag : Boolean) : Integer;
function DeleteUserGroupe (const Ref : Integer) : Integer;

function ChangeOwnPassword  (const UserID, DBUser, OldPasswd, NewPasswd : String; var ErrorText : String) : Integer;
function ChangeUserPassword (const UserID, DBUser, OldPasswd, NewPasswd : String; var ErrorText : String) : Integer;
function ResetUserPassword  (const UserID, DBUser, NewPasswd : String) : Integer;
function UnlockUser         (const DBUser : String; var ErrorText : String) : Integer;

function CheckExecuteRecht (const UserName, RechtName : String) : Boolean;
function CheckReadRecht    (const UserName, RechtName : String) : Boolean;
function CheckWriteRecht   (const UserName, RechtName : String) : Boolean;
function CheckAdminRecht   (const UserName, RechtName : String) : Boolean; overload;
function CheckAdminRecht   (const UserName : String; const ACORef : Integer) : Boolean; overload;

function GetRechte (const UserName, RechtName : String; var Recht : TLVSRechte) : Integer; overload;
function GetRechte (const BenRef, ACORef : Integer; var Recht : TLVSRechte) : Integer; overload;

function GetUserRecht     (const UserRef, ACORef : Integer; var Recht : TLVSRechte) : Integer; overload;

function GetUserRechte    (const UserName, RechtName : String; var Recht : TLVSRechte) : Integer; overload;
function GetUserRechte    (const UserName : String; const ACORef : Integer; var Recht : TLVSRechte) : Integer; overload;
function GetGruppenRechte (const GrpName, RechtName : String; var Recht : TLVSRechte) : Integer; overload;
function GetGruppenRechte (const GrpName : String; const ACORef : Integer; var Recht : TLVSRechte) : Integer; overload;

function CheckACO   (const ACOName : String; var ACORef : Integer) : Integer; overload;
function CheckACO   (const ACOID : Integer; ACOInfo : TACOListEntry) : Integer; overload;
function CheckACO   (Query : TADOQuery; const ACOName : String; var ACORef : Integer) : Integer; overload;
function CheckACO   (Query : TADOQuery; const ACOID : Integer; ACOInfo : TACOListEntry) : Integer; overload;

function InsertACO  (const ACOType, AppName, ACOName, Bezeichnung, Hinweis : String) : Integer; overload;
function InsertACO  (const ADCOID : Integer; const ACOType, AppName, ACOObject, ACOGruppe, ACOName, Hinweis : String) : Integer; overload;
function InsertACO  (const ADCOID : Integer; const ACOType, AppName, ACOObject, ACOGruppe, ACOName, Hinweis, ComponentPath : String) : Integer; overload;

function UpdateACO  (const Ref, ADCOID : Integer; const ACOObject, ACOName, Hinweis : String) : Integer; overload;
function UpdateACO  (const Ref, ADCOID : Integer; const ACOObject, ACOGruppe, ACOName, Hinweis : String) : Integer; overload;
function UpdateACO  (const Ref, ADCOID : Integer; const ACOObject, ACOGruppe, ACOName, Hinweis, ComponentPath : String) : Integer; overload;

function SetUserRechte       (const UserName, AppName, ACOName : String; Recht : TLVSRechte) : Integer; overload;
function SetUserRechte       (const UserName : String; const ACORef : Integer; Recht : TLVSRechte) : Integer; overload;
function SetGruppenRechte    (const GrpName, AppName, ACOName : String; Recht : TLVSRechte) : Integer; overload;
function SetGruppenRechte    (const GrpRef, ACORef : Integer; Recht : TLVSRechte) : Integer; overload;

function DropUserRechte    (GrpName : String; ACORef : Integer) : Integer; overload;
function DropUserRechte    (const GrpName, AppName, ACOName : String) : Integer; overload;

function DropGruppenRechte (GrpName, ACOName : String) : Integer; overload;
function DropGruppenRechte (GrpName : String; ACORef : Integer) : Integer; overload;

function AddUserToGroup      (UserName, GrpName : String) : Integer;
function RemoveUserFromGroup (UserName, GrpName : String) : Integer;

function UserIsAdmin (UserID : String) : Boolean;

implementation

uses
  SysUtils, DatenModul, Variants, Ora, OraclePruefUnit, ErrorTracking;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckExecuteRecht (const UserName, RechtName : String) : Boolean;
var
  recht : TLVSRechte;
begin
  if (GetRechte (UserName, RechtName, recht) = 0) then
    Result := (Exec in recht)
  else Result := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckReadRecht (const UserName, RechtName : String) : Boolean;
var
  recht : TLVSRechte;
begin
  if (GetRechte (UserName, RechtName, recht) = 0) then
    Result := (Read in recht)
  else Result := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckWriteRecht   (const UserName, RechtName : String) : Boolean;
var
  recht : TLVSRechte;
begin
  if (GetRechte (UserName, RechtName, recht) = 0) then
    Result := (Write in recht)
  else Result := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckAdminRecht   (const UserName, RechtName : String) : Boolean;
var
  recht : TLVSRechte;
begin
  if (GetRechte (UserName, RechtName, recht) = 0) then
    Result := (Admin in recht)
  else Result := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckAdminRecht   (const UserName : String; const ACORef : Integer) : Boolean;
var
  recht : TLVSRechte;
begin
  if (GetUserRechte (UserName, ACORef, recht) = 0) then
    Result := (Admin in recht)
  else Result := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetUserRechte (const UserName, RechtName : String; var Recht : TLVSRechte) : Integer;
var
  dbres           : Integer;
  strwert         : String;
  StoredProcedure : TADOStoredProc;
begin
  Recht := [];

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.GET_USER_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, UserName);
    Parameters.CreateParameter('pObjectName',ftString,pdInput, 256, RechtName);
    Parameters.CreateParameter('oRecht',ftString,pdOutput, 8, '');
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres <> 0) then
    strwert := ''
  else begin
    if VarIsNull (StoredProcedure.Parameters.FindParam('oRecht').Value) then
      strwert := ''
    else if (StoredProcedure.Parameters.ParamValues ['oRecht'] = Null) then
      strwert := ''
    else strwert := StoredProcedure.Parameters.ParamValues ['oRecht'];
  end;

  StoredProcedure.Free;

  if (Pos ('E', strwert) <> 0) then Recht := Recht + [Exec];
  if (Pos ('R', strwert) <> 0) then Recht := Recht + [Read];
  if (Pos ('W', strwert) <> 0) then Recht := Recht + [Write];
  if (Pos ('A', strwert) <> 0) then Recht := Recht + [Admin];
  if (Pos ('G', strwert) <> 0) then Recht := Recht + [Grant];

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetUserRechte (const UserName : String; const ACORef : Integer; var Recht : TLVSRechte) : Integer;
var
  dbres           : Integer;
  strwert         : String;
  StoredProcedure : TADOStoredProc;
begin
  Recht := [];

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.GET_USER_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, UserName);
    Parameters.CreateParameter('pObjectRef',ftInteger,pdInput, 12, ACORef);
    Parameters.CreateParameter('oRecht',ftString,pdOutput, 8, '');
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres <> 0) then
    strwert := ''
  else begin
    if VarIsNull (StoredProcedure.Parameters.FindParam('oRecht').Value) then
      strwert := ''
    else if (StoredProcedure.Parameters.ParamValues ['oRecht'] = Null) then
      strwert := ''
    else strwert := StoredProcedure.Parameters.ParamValues ['oRecht'];
  end;

  StoredProcedure.Free;

  if (Pos ('E', strwert) <> 0) then Recht := Recht + [Exec];
  if (Pos ('R', strwert) <> 0) then Recht := Recht + [Read];
  if (Pos ('W', strwert) <> 0) then Recht := Recht + [Write];
  if (Pos ('A', strwert) <> 0) then Recht := Recht + [Admin];
  if (Pos ('G', strwert) <> 0) then Recht := Recht + [Grant];

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetGruppenRechte (const GrpName, RechtName : String; var Recht : TLVSRechte) : Integer;
var
  dbres           : Integer;
  strwert         : String;
  StoredProcedure : TADOStoredProc;
begin
  Recht := [];

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.GET_GRP_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pGroupeID',ftString,pdInput, 32, GrpName);
    Parameters.CreateParameter('pObjectName',ftString,pdInput, 256, RechtName);
    Parameters.CreateParameter('oRecht',ftString,pdOutput, 8, '');
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres <> 0) then
    strwert := ''
  else begin
    if VarIsNull (StoredProcedure.Parameters.FindParam('oRecht').Value) then
      strwert := ''
    else if (StoredProcedure.Parameters.ParamValues ['oRecht'] = Null) then
      strwert := ''
    else strwert := StoredProcedure.Parameters.ParamValues ['oRecht'];
  end;

  StoredProcedure.Free;

  if (Pos ('E', strwert) <> 0) then Recht := Recht + [Exec];
  if (Pos ('R', strwert) <> 0) then Recht := Recht + [Read];
  if (Pos ('W', strwert) <> 0) then Recht := Recht + [Write];
  if (Pos ('A', strwert) <> 0) then Recht := Recht + [Admin];
  if (Pos ('G', strwert) <> 0) then Recht := Recht + [Grant];

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetGruppenRechte (const GrpName : String; const ACORef : Integer; var Recht : TLVSRechte) : Integer;
var
  dbres           : Integer;
  strwert         : String;
  StoredProcedure : TADOStoredProc;
begin
  Recht := [];

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.GET_GRP_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pGroupeID',ftString, pdInput, 32, GrpName);
    Parameters.CreateParameter('pObjectRef',ftInteger,pdInput, 12, ACORef);
    Parameters.CreateParameter('oRecht',ftString,pdOutput, 8, '');
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres <> 0) then
    strwert := ''
  else begin
    if VarIsNull (StoredProcedure.Parameters.FindParam('oRecht').Value) then
      strwert := ''
    else if (StoredProcedure.Parameters.ParamValues ['oRecht'] = Null) then
      strwert := ''
    else strwert := StoredProcedure.Parameters.ParamValues ['oRecht'];
  end;

  StoredProcedure.Free;

  if (Pos ('E', strwert) <> 0) then Recht := Recht + [Exec];
  if (Pos ('R', strwert) <> 0) then Recht := Recht + [Read];
  if (Pos ('W', strwert) <> 0) then Recht := Recht + [Write];
  if (Pos ('A', strwert) <> 0) then Recht := Recht + [Admin];
  if (Pos ('G', strwert) <> 0) then Recht := Recht + [Grant];

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetRechte (const UserName, RechtName : String; var Recht : TLVSRechte) : Integer;
var
  dbres           : Integer;
  strwert         : String;
  found           : Boolean;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  Recht := [];

  if (LVSSecurityModule.SecurityDataSet.Active) and (UserName = LVSDatenModul.AktUser) then begin
    found := LVSSecurityModule.SecurityDataSet.Locate ('ObjectName', RechtName, []);

    if not (found) then
      strwert := ''
    else begin
      Recht := Recht + [Def];
      strwert := LVSSecurityModule.SecurityDataSet.FieldByName ('RECHTE').AsString;
    end;
  end else begin
    StoredProcedure := TADOStoredProc.Create (Nil);

    with StoredProcedure do begin
      ProcedureName := 'PA_SYS_BENUTZER.GET_OBJECT_RECHTE';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
      Parameters.CreateParameter('pUserID',ftString,pdInput, 32, UserName);

      if Assigned (LVSSecurityModule.ACOModul) then
        Parameters.CreateParameter('pApp',ftString,pdInput, 32, LVSSecurityModule.ACOModul.ApplicationID)
      else Parameters.CreateParameter('pApp',ftString,pdInput, 32, NULL);

      Parameters.CreateParameter('pObjectName',ftString,pdInput, 256, RechtName);
      Parameters.CreateParameter('oRecht',ftString,pdOutput, 8, '');
      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

    if (dbres <> 0) then
      strwert := ''
    else begin
      if VarIsNull (StoredProcedure.Parameters.FindParam('oRecht').Value) then
        strwert := ''
      else if (StoredProcedure.Parameters.ParamValues ['oRecht'] = Null) then
        strwert := ''
      else strwert := StoredProcedure.Parameters.ParamValues ['oRecht'];
    end;

    StoredProcedure.Free;
  end;

  if (Length (strwert) > 0) Then begin
    if (Pos ('E', strwert) <> 0) then Recht := Recht + [Exec];
    if (Pos ('R', strwert) <> 0) then Recht := Recht + [Read];
    if (Pos ('W', strwert) <> 0) then Recht := Recht + [Write];
    if (Pos ('A', strwert) <> 0) then Recht := Recht + [Admin];
    if (Pos ('G', strwert) <> 0) then Recht := Recht + [Grant];
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetRechte (const BenRef, ACORef : Integer; var Recht : TLVSRechte) : Integer;
var
  dbres           : Integer;
  strwert         : String;
  StoredProcedure : TADOStoredProc;
begin
  Recht := [];

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.GET_OBJECT_RECHTE_PCD';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pBenRef',ftInteger,pdInput, 12, BenRef);
    Parameters.CreateParameter('pACORef',ftInteger,pdInput, 12, ACORef);
    Parameters.CreateParameter('oRecht',ftString,pdOutput, 8, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres <> 0) then
    strwert := ''
  else begin
    if VarIsNull (StoredProcedure.Parameters.FindParam('oRecht').Value) then
      strwert := ''
    else if (StoredProcedure.Parameters.ParamValues ['oRecht'] = Null) then
      strwert := ''
    else strwert := StoredProcedure.Parameters.ParamValues ['oRecht'];
  end;

  StoredProcedure.Free;

  if (Pos ('E', strwert) <> 0) then Recht := Recht + [Exec];
  if (Pos ('R', strwert) <> 0) then Recht := Recht + [Read];
  if (Pos ('W', strwert) <> 0) then Recht := Recht + [Write];
  if (Pos ('A', strwert) <> 0) then Recht := Recht + [Admin];
  if (Pos ('G', strwert) <> 0) then Recht := Recht + [Grant];

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckACO (const ACOName : String; var ACORef : Integer) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType   := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select REF from SYS_ACO where OBJECT_NAME=:name');
    query.Parameters.Items [0].Value := ACOName;

    res := 0;

    try
      query.Open;

      if (query.Fields [0].IsNull) then
        ACORef := -1
      else ACORef := query.Fields [0].AsInteger;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  CheckACO := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.11.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckACO (const ACOID : Integer; ACOInfo : TACOListEntry) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  ACOInfo.ACORef := -1;

  query := TADOQuery.Create (Nil);

  try
    query.LockType   :=ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from SYS_ACO where OBJECT_ID=:id');
    query.Parameters.Items [0].Value := ACOID;

    res := 0;

    try
      query.Open;

      if (query.RecordCount > 0) then begin
        ACOInfo.ACORef   := query.FieldByName ('REF').AsInteger;
        ACOInfo.ACOID    := query.FieldByName ('OBJECT_ID').AsInteger;

        ACOInfo.ACOType  := query.FieldByName ('TYP').AsString;
        ACOInfo.ACOObject:= query.FieldByName ('OBJECT_NAME').AsString;
        ACOInfo.ACOName  := query.FieldByName ('NAME').AsString;
        ACOInfo.ACOText  := query.FieldByName ('BESCHREIBUNG').AsString;

        if not Assigned (query.FindField ('OBJECT_GROUP')) then
          ACOInfo.ACOVersion := 1
        else begin
          ACOInfo.ACOVersion := 2;
          ACOInfo.ACOGroup := query.FieldByName ('OBJECT_GROUP').AsString;
        end;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  CheckACO := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckACO (Query : TADOQuery; const ACOName : String; var ACORef : Integer) : Integer;
var
  res    : Integer;
  sqlstr : String;
begin
  sqlstr := 'select REF from SYS_ACO where OBJECT_NAME=:name';

  if (Query.SQL.Text <> sqlstr) then
    Query.SQL.Text := sqlstr;

  Query.Parameters.Items [0].Value := ACOName;

  res := 0;

  try
    Query.Open;

    if (Query.Fields [0].IsNull) then
      ACORef := -1
    else ACORef := Query.Fields [0].AsInteger;
  except
    res := -9;
  end;

  Query.Close;

  CheckACO := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.11.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckACO (Query : TADOQuery; const ACOID : Integer; ACOInfo : TACOListEntry) : Integer; overload;
var
  res    : Integer;
  sqlstr : String;
begin
  ACOInfo.ACORef := -1;

  sqlstr := 'select * from SYS_ACO where OBJECT_ID=:id';

  if (Query.SQL.Text <> sqlstr) then
    Query.SQL.Text := sqlstr;

  Query.Parameters.Items [0].Value := ACOID;

  res := 0;

  try
    Query.Open;

    if (query.RecordCount > 0) then begin
      ACOInfo.ACORef   := Query.FieldByName ('REF').AsInteger;
      ACOInfo.ACOID    := Query.FieldByName ('OBJECT_ID').AsInteger;

      ACOInfo.ACOType  := Query.FieldByName ('TYP').AsString;
      ACOInfo.ACOObject:= Query.FieldByName ('OBJECT_NAME').AsString;
      ACOInfo.ACOName  := Query.FieldByName ('NAME').AsString;
      ACOInfo.ACOText  := Query.FieldByName ('BESCHREIBUNG').AsString;

      if not Assigned (query.FindField ('OBJECT_GROUP')) then
        ACOInfo.ACOVersion := 1
      else begin
        ACOInfo.ACOVersion := 2;
        ACOInfo.ACOGroup := Query.FieldByName ('OBJECT_GROUP').AsString;
      end;
    end;
  except
    res := -9;
  end;

  Query.Close;

  CheckACO := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertACO  (const ACOType, AppName, ACOName, Bezeichnung, Hinweis : String) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.Connection    := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('insert into SYS_ACO (APPLICATION, OBJECT_NAME, TYP, NAME, BESCHREIBUNG) VALUES ('+#39+AppName+#39+','+#39+ACOName+#39+','+#39+ACOType+#39+','+#39+Bezeichnung+#39+','+#39+Hinweis+#39+')');

    res := 0;

    try
      query.ExecSQL;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  InsertACO := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.11.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertACO  (const ADCOID : Integer; const ACOType, AppName, ACOObject, ACOGruppe, ACOName, Hinweis : String) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.Connection    := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('insert into SYS_ACO (APPLICATION, OBJECT_ID, OBJECT_GROUP, OBJECT_NAME, TYP, NAME, BESCHREIBUNG) VALUES (:app, :obj_id, :obj_group, :obj_name, :typ, :name, :hint)');
    query.Parameters.ParamByName ('app').Value := AppName;
    query.Parameters.ParamByName ('obj_id').Value := ADCOID;
    query.Parameters.ParamByName ('obj_group').Value := ACOGruppe;
    query.Parameters.ParamByName ('obj_name').Value := ACOObject;
    query.Parameters.ParamByName ('typ').Value := ACOType;
    query.Parameters.ParamByName ('name').Value := ACOName;
    query.Parameters.ParamByName ('hint').Value := Hinweis;

    res := 0;

    try
      query.ExecSQL;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  InsertACO := res;
end;


function InsertACO  (const ADCOID : Integer; const ACOType, AppName, ACOObject, ACOGruppe, ACOName, Hinweis, ComponentPath : String) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.Connection    := LVSDatenModul.MainADOConnection;

    try
      query.SQL.Add ('insert into SYS_ACO (APPLICATION, OBJECT_ID, OBJECT_GROUP, OBJECT_NAME, TYP, NAME, BESCHREIBUNG, COMPONENT_PATH) VALUES (:app, :obj_id, :obj_group, :obj_name, :typ, :name, :hint, :comp_path)');
      query.Parameters.ParamByName ('app').Value := AppName;
      query.Parameters.ParamByName ('obj_id').Value := ADCOID;
      query.Parameters.ParamByName ('obj_group').Value := ACOGruppe;
      query.Parameters.ParamByName ('obj_name').Value := ACOObject;
      query.Parameters.ParamByName ('typ').Value := ACOType;
      query.Parameters.ParamByName ('name').Value := ACOName;
      query.Parameters.ParamByName ('hint').Value := Hinweis;
      query.Parameters.ParamByName ('comp_path').Value := ComponentPath;
    except
      query.SQL.Clear;
      query.SQL.Add ('insert into SYS_ACO (APPLICATION, OBJECT_ID, OBJECT_GROUP, OBJECT_NAME, TYP, NAME, BESCHREIBUNG) VALUES (:app, :obj_id, :obj_group, :obj_name, :typ, :name, :hint)');
      query.Parameters.ParamByName ('app').Value := AppName;
      query.Parameters.ParamByName ('obj_id').Value := ADCOID;
      query.Parameters.ParamByName ('obj_group').Value := ACOGruppe;
      query.Parameters.ParamByName ('obj_name').Value := ACOObject;
      query.Parameters.ParamByName ('typ').Value := ACOType;
      query.Parameters.ParamByName ('name').Value := ACOName;
      query.Parameters.ParamByName ('hint').Value := Hinweis;
    end;

    res := 0;

    try
      query.ExecSQL;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  InsertACO := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function UpdateACO  (const Ref, ADCOID : Integer; const ACOObject, ACOName, Hinweis : String) : Integer; overload;
var
  res   : Integer;
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('update SYS_ACO set OBJECT_ID=:obj_id, OBJECT_NAME=:obj_name, NAME=:name, BESCHREIBUNG=:hint where REF=:ref');
    query.Parameters.ParamByName ('ref').Value := Ref;
    query.Parameters.ParamByName ('obj_id').Value := ADCOID;
    query.Parameters.ParamByName ('obj_name').Value := ACOObject;
    query.Parameters.ParamByName ('name').Value := ACOName;
    query.Parameters.ParamByName ('hint').Value := Hinweis;

    res := 0;

    try
      query.ExecSQL;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  UpdateACO := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.11.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function UpdateACO  (const Ref, ADCOID : Integer; const ACOObject, ACOGruppe, ACOName, Hinweis : String) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('update SYS_ACO set OBJECT_ID=:obj_id, OBJECT_GROUP=:obj_group, OBJECT_NAME=:obj_name, NAME=:name, BESCHREIBUNG=:hint where REF=:ref');
    query.Parameters.ParamByName ('ref').Value := Ref;
    query.Parameters.ParamByName ('obj_id').Value := ADCOID;
    query.Parameters.ParamByName ('obj_group').Value := ACOGruppe;
    query.Parameters.ParamByName ('obj_name').Value := ACOObject;
    query.Parameters.ParamByName ('name').Value := ACOName;
    query.Parameters.ParamByName ('hint').Value := Hinweis;

    res := 0;

    try
      query.ExecSQL;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  UpdateACO := res;
end;

function UpdateACO  (const Ref, ADCOID : Integer; const ACOObject, ACOGruppe, ACOName, Hinweis, ComponentPath : String) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.Connection := LVSDatenModul.MainADOConnection;

    try
      query.SQL.Add ('update SYS_ACO set OBJECT_ID=:obj_id, OBJECT_GROUP=:obj_group, OBJECT_NAME=:obj_name, NAME=:name, BESCHREIBUNG=:hint, COMPONENT_PATH=:comp_path where REF=:ref');
      query.Parameters.ParamByName ('ref').Value := Ref;
      query.Parameters.ParamByName ('obj_id').Value := ADCOID;
      query.Parameters.ParamByName ('obj_group').Value := ACOGruppe;
      query.Parameters.ParamByName ('obj_name').Value := ACOObject;
      query.Parameters.ParamByName ('name').Value := ACOName;
      query.Parameters.ParamByName ('hint').Value := Hinweis;
      query.Parameters.ParamByName ('comp_path').Value := ComponentPath;
    except
      query.SQL.Clear;
      query.SQL.Add ('update SYS_ACO set OBJECT_ID=:obj_id, OBJECT_GROUP=:obj_group, OBJECT_NAME=:obj_name, NAME=:name, BESCHREIBUNG=:hint where REF=:ref');
      query.Parameters.ParamByName ('ref').Value := Ref;
      query.Parameters.ParamByName ('obj_id').Value := ADCOID;
      query.Parameters.ParamByName ('obj_group').Value := ACOGruppe;
      query.Parameters.ParamByName ('obj_name').Value := ACOObject;
      query.Parameters.ParamByName ('name').Value := ACOName;
      query.Parameters.ParamByName ('hint').Value := Hinweis;
    end;

    res := 0;

    try
      query.ExecSQL;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  UpdateACO := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetUserRechte (const UserName, AppName, ACOName : String; Recht : TLVSRechte) : Integer;
var
  dbres  : Integer;
  strwert : String;
  StoredProcedure : TADOStoredProc;
begin
  strwert := '';
  if (Exec in Recht) then strwert := strwert + 'E';
  if (Read in Recht) then strwert := strwert + 'R';
  if (Write in Recht) then strwert := strwert + 'W';
  if (Admin in Recht) then strwert := strwert + 'A';
  if (Grant in Recht) then strwert := strwert + 'G';

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.SET_USER_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, UserName);
    Parameters.CreateParameter('pApp',ftString,pdInput, 32, AppName);
    Parameters.CreateParameter('pObjectName',ftString,pdInput, 256, ACOName);
    Parameters.CreateParameter('pRecht',ftString,pdInput, 16, strwert);
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetUserRechte (const UserName : String; const ACORef : Integer; Recht : TLVSRechte) : Integer;
var
  dbres  : Integer;
  strwert : String;
  StoredProcedure : TADOStoredProc;
begin
  strwert := '';
  if (Exec in Recht) then strwert := strwert + 'E';
  if (Read in Recht) then strwert := strwert + 'R';
  if (Write in Recht) then strwert := strwert + 'W';
  if (Admin in Recht) then strwert := strwert + 'A';
  if (Grant in Recht) then strwert := strwert + 'G';

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.SET_USER_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, UserName);
    Parameters.CreateParameter('pObjectRef',ftInteger,pdInput, 12, ACORef);
    Parameters.CreateParameter('pRecht',ftString,pdInput, 16, strwert);
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DropUserRechte    (GrpName : String; ACORef : Integer) : Integer;
var
  dbres  : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.DROP_USER_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pGrpID',ftString,pdInput, 32, GrpName);
    Parameters.CreateParameter('pObjectRef',ftInteger,pdInput, 256, ACORef);
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DropUserRechte (const GrpName, AppName, ACOName : String) : Integer;
var
  dbres  : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.DROP_USER_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pGrpID',ftString,pdInput, 32, GrpName);
    Parameters.CreateParameter('pApp',ftString,pdInput, 32, AppName);
    Parameters.CreateParameter('pObjectName',ftString,pdInput, 256, ACOName);
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetGruppenRechte (const GrpName, AppName, ACOName : String; Recht : TLVSRechte) : Integer;
var
  dbres  : Integer;
  strwert : String;
  StoredProcedure : TADOStoredProc;
begin
  strwert := '';
  if (Exec in Recht) then strwert := strwert + 'E';
  if (Read in Recht) then strwert := strwert + 'R';
  if (Write in Recht) then strwert := strwert + 'W';
  if (Admin in Recht) then strwert := strwert + 'A';
  if (Grant in Recht) then strwert := strwert + 'G';

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.SET_GRP_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pGrpID',ftString,pdInput, 32, GrpName);
    Parameters.CreateParameter('pAppName',ftString,pdInput, 32, AppName);
    Parameters.CreateParameter('pObjectName',ftString,pdInput, 256, ACOName);
    Parameters.CreateParameter('pRecht',ftString,pdInput, 16, strwert);
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetGruppenRechte (const GrpRef, ACORef : Integer; Recht : TLVSRechte) : Integer;
var
  dbres  : Integer;
  strwert : String;
  StoredProcedure : TADOStoredProc;
begin
  strwert := '';
  if (Exec in Recht) then strwert := strwert + 'E';
  if (Read in Recht) then strwert := strwert + 'R';
  if (Write in Recht) then strwert := strwert + 'W';
  if (Admin in Recht) then strwert := strwert + 'A';
  if (Grant in Recht) then strwert := strwert + 'G';

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.SET_GRP_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pGrpRef',ftString,pdInput, 12, GrpRef);
    Parameters.CreateParameter('pObjectRef',ftInteger,pdInput, 12, ACORef);
    Parameters.CreateParameter('pRecht',ftString,pdInput, 16, strwert);
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DropGruppenRechte (GrpName : String; ACORef : Integer) : Integer;
var
  dbres  : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.DROP_GRP_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pGrpID',ftString,pdInput, 32, GrpName);
    Parameters.CreateParameter('pObjectRef',ftInteger,pdInput, 256, ACORef);
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DropGruppenRechte (GrpName, ACONAme : String) : Integer;
var
  dbres  : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.DROP_GRP_OBJECT_RECHTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pGrpID',ftString,pdInput, 32, GrpName);
    Parameters.CreateParameter('pObjectName',ftString,pdInput, 256, ACOName);
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckUserExists (const Username : String; var Found, Deleted : Boolean; var UserRef : Integer) : Integer;
var
  res      : Integer;
  adoquery : TADOQuery;
begin
  res := 0;

  Found   := False;
  Deleted := False;
  UserRef := -1;

  adoquery := TADOQuery.Create (Nil);

  try
    adoquery.Connection := LVSDatenModul.MainADOConnection;

    //Pr�fen ob der User in der DB schon angelegt ist
    adoquery.SQL.Add ('select REF,STATUS from V_SYS_BEN where upper (USER_ID)=:ID');
    adoquery.Parameters.ParamByName('ID').Value := UpperCase (UserName);

    try
      adoquery.Open;

      if (adoquery.RecordCount > 0) then begin
        Found := true;

        UserRef := adoquery.Fields [0].AsInteger;
        Deleted := (adoquery.Fields [1].AsString = 'DEL');
      end;

      adoquery.Close;
    except
      res := -9;
    end;
  finally
    adoquery.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckOrcaleUserExists (const Username : String; var Found : Boolean) : Integer;
var
  res      : Integer;
  adoquery : TADOQuery;
begin
  res := 0;

  adoquery := TADOQuery.Create (Nil);

  try
    adoquery.LockType := ltReadOnly;
    adoquery.Connection := LVSDatenModul.MainADOConnection;

    //Pr�fen ob der User in der DB schon angelegt ist
    adoquery.SQL.Add ('select USER_ID from SYS.ALL_USERS where USERNAME=:usr_namer');
    adoquery.Parameters [0].Value := UpperCase (UserName);

    try
      adoquery.Open;

      Found := (adoquery.RecordCount > 0);

      adoquery.Close;
    except
      res := -9;
    end;
  finally
    adoquery.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckUserRole (const UserName, RoleName : String) : Boolean;
begin
  Result := True;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateOracleUser (const UserName, Password : String; const AdminFlag, DomFlag : Boolean) : Integer;
var
  res      : Integer;
  adocmd   : TADOCommand;
  found    : Boolean;
  errstr   : String;
begin
  adocmd := TADOCommand.Create (Nil);
  adocmd.Connection := LVSDatenModul.MainADOConnection;

  res := CheckOrcaleUserExists (UserName, found);

  if (res = 0) and not (found) then begin
    if (DomFlag) then
      adocmd.CommandText := 'CREATE USER "'+AnsiUpperCase (UserName)+'" IDENTIFIED BY EXTERNAL'
    else adocmd.CommandText := 'CREATE USER "'+AnsiUpperCase (UserName)+'" IDENTIFIED BY "'+Password+'"';

    try
      LVSDatenModul.TraceSQL (adocmd.CommandText);
      adocmd.Execute;
    except
      on E: Exception do begin
        errstr := E.Message;

        res := -9;
      end;
    end;
  end;

  if (res = 0) Then begin
    //Die Rolle CONNECT zuweisen, sonst kann man sich gar nicht anmelden
    adocmd.CommandText := 'GRANT "CONNECT" TO "'+AnsiUpperCase (UserName)+'"';
    try
      LVSDatenModul.TraceSQL (adocmd.CommandText);
      adocmd.Execute;
    except
      on E: Exception do begin
        errstr := E.Message;

        res := -9;
      end;
    end;
  end;

  //Nun noch die passenden Zugriffsrechte �ber eine Rolle zuweisen
  if (res = 0) Then begin
    adocmd.CommandText := 'GRANT "'+LVSDatenModul.Schema+'_USER" TO "'+AnsiUpperCase (UserName)+'"';

    try
      LVSDatenModul.TraceSQL (adocmd.CommandText);
      adocmd.Execute;
    except
      on E: Exception do begin
        errstr := E.Message;

        res := -9;
      end;
    end;

    if (res = 0) then begin
      if (AnsiUpperCase(LVSDatenModul.AktUser) = AnsiUpperCase(LVSDatenModul.Schema)) then begin
        if (AdminFlag) then begin
          adocmd.CommandText := 'GRANT "'+LVSDatenModul.Schema+'_ADMIN" TO "'+AnsiUpperCase (UserName)+'"';

          try
            LVSDatenModul.TraceSQL (adocmd.CommandText);
            adocmd.Execute;
          except
            on E: Exception do begin
              errstr := E.Message;

              res := -9;
            end;
          end;
        end else if (found) then begin
          if (CheckUserRole (AnsiUpperCase (UserName), LVSDatenModul.Schema+'_ADMIN')) Then begin
            adocmd.CommandText := 'REVOKE "'+LVSDatenModul.Schema+'_ADMIN" FROM "'+AnsiUpperCase (UserName)+'"';

            try
              LVSDatenModul.TraceSQL (adocmd.CommandText);
              adocmd.Execute;
            except
              res := 0;
            end;
          end;
        end;
      end;
    end;
  end;

  adocmd.Free;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateUser (const RefFirma : Integer; const UserID, UserShortName, UserNumID, UserName, DBUserName, UserTitle, Password, DomName, DomUser, Email : String; const AdminFlag : Boolean; var UserRef : Integer) : Integer;
var
  res      : Integer;
  adocmd   : TADOCommand;
  adoquery : TADOQuery;
  uname    : String;
  found    : Boolean;
begin
  adocmd := TADOCommand.Create (Nil);
  adocmd.Connection := LVSDatenModul.MainADOConnection;

  adoquery := TADOQuery.Create (Nil);
  adoquery.Connection := LVSDatenModul.MainADOConnection;

  res := CreateOracleUser (DBUserName, Password, AdminFlag, False);

  if (res = 0) and (Length (DomName) > 0) and (Length (DomUser) > 0) Then begin
    uname := 'OPS$'+DomName+'\'+DomUser;

    res := CreateOracleUser (uname, '', AdminFlag, True);

    (*
    if (res = 0) Then begin
      adocmd.CommandText := 'alter user "'+UserID+'" grant connect through "'+AnsiUpperCase (uname)+'"';

      try
        LVSDatenModul.TraceSQL (adocmd.CommandText);
        adocmd.Execute;
      except
        on E: Exception do begin
//          errstr := E.Message;

          res := -9;
        end;
      end;
    end;
    *)
  end;

  if (res = 0) then begin
    //Pr�fen ob der User im LVS schon vorhanden ist
    adoquery.SQL.Clear;
    adoquery.SQL.Add ('select REF from SYS_BEN where USER_ID=:id');
    adoquery.Parameters [0].Value := UpperCase (UserID);
    adoquery.Open;

    found := (adoquery.RecordCount > 0);

    adoquery.Close;

    if not (found) then begin
      adoquery.SQL.Clear;
      adoquery.SQL.Add ('select SEQ_ACL.NextVal from DUAL');
      try
        adoquery.Open;
        UserRef := adoquery.Fields [0].AsInteger;
        adoquery.Close;
      except
        res := -9;
        UserRef := -1;
      end;

      if (res = 0) and (UserRef <> -1) then begin
        adocmd.CommandText := 'insert into SYS_BEN (REF,USER_NAME,USER_ID,USER_SHORT_NAME,USER_NUM_ID,USER_TITLE,NTS_DOMAINE,NTS_USER,EMAIL,REF_FIRMA,IS_ADMIN,RESET_PASSWORD)';
        adocmd.CommandText := adocmd.CommandText + ' VALUES ('+IntToStr (UserRef)+','+#39+UserName+#39+','+#39+AnsiUpperCase (UserID)+#39+','+#39+UserShortName+#39+','+#39+UserNumID+#39+','+#39+BuildSQLString (copy (UserTitle,1,32))+#39+','+#39+DomName+#39+','+#39+DomUser+#39+','+#39+BuildSQLString (Email)+#39;

        if (RefFirma = -1) then
          adocmd.CommandText := adocmd.CommandText +',null'
        else
          adocmd.CommandText := adocmd.CommandText +','+IntToStr (RefFirma);

        if AdminFlag then
          adocmd.CommandText := adocmd.CommandText + ',''1'''
        else
          adocmd.CommandText := adocmd.CommandText + ',''0''';

        adocmd.CommandText := adocmd.CommandText + ',''1'')';

        try
          adocmd.Execute;
        except
          res := -9;
        end;
      end;
    end;
  end;

  adoquery.Free;
  adocmd.Free;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeUser (const Ref, RefFirma : Integer; const UserID, UserShortName, UserNumID, UserName, DBUserName, UserTitle, Sprache, Password, DomName, DomUser, Email : String; const AdminFlag : Boolean) : Integer;
var
  res      : Integer;
  adocmd   : TADOCommand;
  adoquery : TADOQuery;
  uname    : String;
begin
  adocmd := TADOCommand.Create (Nil);
  adocmd.Connection := LVSDatenModul.MainADOConnection;

  adoquery := TADOQuery.Create (Nil);
  adoquery.Connection := LVSDatenModul.MainADOConnection;

  res := CreateOracleUser (DBUserName, Password, AdminFlag, False);

  if (res = 0) and (Length (DomName) > 0) and (Length (DomUser) > 0) Then begin
    uname := 'OPS$'+DomName+'\'+DomUser;

    res := CreateOracleUser (uname, '', AdminFlag, True);
  end;

  if (res = 0) then begin
    adocmd.CommandText := 'update SYS_BEN set USER_ID=upper ('+#39+UserID+#39+'), USER_SHORT_NAME='+#39+UserShortName+#39+', USER_NUM_ID='+#39+UserNumID+#39+', USER_NAME='+#39+UserName+#39+',USER_TITLE='+#39+BuildSQLString (UserTitle)+#39;
    adocmd.CommandText := adocmd.CommandText + ',NTS_DOMAINE='+#39+DomName+#39+',NTS_USER='+#39+DomUser+#39;
    adocmd.CommandText := adocmd.CommandText + ',REF_FIRMA='+IntToStr (RefFirma);

    if (Length (Email) = 0) then
      adocmd.CommandText := adocmd.CommandText + ',EMAIL=null'
    else
      adocmd.CommandText := adocmd.CommandText + ',EMAIL='+#39+BuildSQLString (Email)+#39;

    if (Length (Sprache) = 0) then
      adocmd.CommandText := adocmd.CommandText + ',SPRACHE=null'
    else
      adocmd.CommandText := adocmd.CommandText + ',SPRACHE='+#39+BuildSQLString (Sprache)+#39;

    if AdminFlag then
      adocmd.CommandText := adocmd.CommandText + ',IS_ADMIN=''1'''
    else adocmd.CommandText := adocmd.CommandText + ',IS_ADMIN=''0''';

    adocmd.CommandText := adocmd.CommandText + ' where REF='+IntToStr (Ref);

    try
      LVSDatenModul.TraceSQL (adocmd.CommandText);
      adocmd.Execute;
    except
      res := -9;
    end;
  end;

  adoquery.Free;
  adocmd.Free;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeOwnPassword (const UserID, DBUser, OldPasswd, NewPasswd : String; var ErrorText : String) : Integer;
var
  res      : Integer;
  logstr   : String;
  odaccmd  : TOraSQL;
begin
  res := 0;

  ErrorText := '';

  odaccmd := TOraSQL.Create (Nil);

  try
    odaccmd.Session := LVSDatenModul.OraMainSession;

    odaccmd.SQL.Add ('alter user "'+DBUser+'" identified by "'+NewPasswd+'" replace "'+OldPasswd+'"');

    odaccmd.Session.StartTransaction;

    try
      LVSDatenModul.TraceSQL (odaccmd.SQL.Text);
      odaccmd.Execute;

      odaccmd.SQL.Clear;
      odaccmd.SQL.Add ('begin PA_SYS_BENUTZER.PASSWORD_CHANGED ('+#39+UserID+#39+');end;');

      LVSDatenModul.TraceSQL (odaccmd.SQL.Text);
      odaccmd.Execute;

      odaccmd.Session.Commit;

      LVSDatenModul.ReadOraDBMSOutput ('PA_SYS_BENUTZER.PASSWORD_CHANGED', '', 0);
    except
      on e: Exception do begin
        res := -9;
        ErrorText := e.Message;

        odaccmd.Session.Rollback;

        logstr := odaccmd.SQL.Text;
        logstr := StringReplace (logstr, OldPasswd, 'xxx', [rfReplaceAll, rfIgnoreCase]);
        logstr := StringReplace (logstr, NewPasswd, 'xxx', [rfReplaceAll, rfIgnoreCase]);

        ErrorTrackingModule.WriteErrorLog ('ChangeOwnPassword', e.ClassName + ' : ' + e.Message+#13+logstr);
      end;
    end;
  finally
    odaccmd.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeUserPassword (const UserID, DBUser, OldPasswd, NewPasswd : String; var ErrorText : String) : Integer;
var
  res      : Integer;
  logstr   : String;
  adocmd   : TADOCommand;
begin
  res := 0;
  ErrorText := '';

  adocmd := TADOCommand.Create (Nil);
  adocmd.Connection := LVSDatenModul.MainADOConnection;

  adocmd.CommandText := 'alter user "'+DBUser+'" identified by "'+NewPasswd+'"';

  adocmd.Connection.BeginTrans;

  try
    LVSDatenModul.TraceSQL (adocmd.CommandText);
    adocmd.Execute;

    adocmd.CommandText := 'begin PA_SYS_BENUTZER.PASSWORD_CHANGED ('+#39+UserID+#39+');end;';
    adocmd.Execute;

    adocmd.Connection.CommitTrans;

    LVSDatenModul.ReadDBMSOutput ('PA_SYS_BENUTZER.PASSWORD_CHANGED', '', 0);
  except
    on e: Exception do begin
      res := -9;
      ErrorText := e.Message;

      adocmd.Connection.RollbackTrans;

      logstr := adocmd.CommandText;
      logstr := StringReplace (logstr, OldPasswd, 'xxx', [rfReplaceAll, rfIgnoreCase]);
      logstr := StringReplace (logstr, NewPasswd, 'xxx', [rfReplaceAll, rfIgnoreCase]);

      ErrorTrackingModule.WriteErrorLog ('ChangeOwnPassword', e.ClassName + ' : ' + e.Message+#13+logstr);
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ResetUserPassword (const UserID, DBUser, NewPasswd : String) : Integer;
var
  res      : Integer;
  adocmd   : TADOCommand;
begin
  res := 0;

  adocmd := TADOCommand.Create (Nil);
  adocmd.Connection := LVSDatenModul.MainADOConnection;

  adocmd.CommandText := 'alter user "'+DBUser+'" ACCOUNT UNLOCK';

  LVSDatenModul.TraceSQL (adocmd.CommandText);

  try
    adocmd.Execute;
  except
  end;

  if (res = 0) then begin
    adocmd.Connection.BeginTrans;

    try
      adocmd.CommandText := 'alter user "'+DBUser+'" identified by "'+NewPasswd+'"';

      LVSDatenModul.TraceSQL ('alter user "'+DBUser+'" identified by "*********"');
      adocmd.Execute;

      adocmd.CommandText := 'begin PA_SYS_BENUTZER.PASSWORD_RESET ('+#39+UserID+#39+');end;';

      LVSDatenModul.TraceSQL (adocmd.CommandText);
      adocmd.Execute;

      adocmd.Connection.CommitTrans;
    except
      adocmd.Connection.RollbackTrans;

      res := -9;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function AddUserToGroup (UserName, GrpName : String) : Integer;
var
  dbres  : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.ADD_USER_TO_GROUPE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, UserName);
    Parameters.CreateParameter('pGroupID',ftString,pdInput, 256, GrpName);
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function RemoveUserFromGroup (UserName, GrpName : String) : Integer;
var
  dbres  : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.REMOVE_USER_FROM_GROUPE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, UserName);
    Parameters.CreateParameter('pGroupID',ftString,pdInput, 256, GrpName);
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateUserGroupe (const GrpArt, GrpID, GrpName : String; const RefLoc : Integer; const AdminFlag : Boolean) : Integer;
var
  dbres  : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.CREATE_USER_GROUPE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pGrpArt',ftString,pdInput, 8, GrpArt);
    Parameters.CreateParameter('pGroupID',ftString,pdInput, 32, GrpID);
    Parameters.CreateParameter('pGrpBez',ftString,pdInput, 64, GrpName);

    if (RefLoc = -1) then
      Parameters.CreateParameter('pRefLoc',ftInteger, pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pRefLoc',ftInteger, pdInput, 12, RefLoc);

    if (AdminFlag) then
      Parameters.CreateParameter('pAdminFlag',ftInteger, pdInput, 1, '1')
    else
      Parameters.CreateParameter('pAdminFlag',ftInteger, pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeUserGroupe (const Ref : Integer; const GrpID, GrpName : String; const RefLoc : Integer; const AdminFlag : Boolean) : Integer;
var
  dbres  : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.CHANGE_USER_GROUPE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pGroupRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pGroupID',ftString,pdInput, 32, GrpID);
    Parameters.CreateParameter('pGrpBez',ftString,pdInput, 64, GrpName);

    if (RefLoc = -1) then
      Parameters.CreateParameter('pRefLoc',ftInteger, pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pRefLoc',ftInteger, pdInput, 12, RefLoc);

    if (AdminFlag) then
      Parameters.CreateParameter('pAdminFlag',ftInteger, pdInput, 1, '1')
    else
      Parameters.CreateParameter('pAdminFlag',ftInteger, pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteUserGroupe (const Ref : Integer) : Integer;
var
  dbres  : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SYS_BENUTZER.DELETE_USER_GROUPE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pGroupRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ActivateUser (const UserRef : Integer) : Integer;
var
  res      : Integer;
  adocmd   : TADOCommand;
begin
  res := 0;

  adocmd := TADOCommand.Create (Nil);
  adocmd.Connection := LVSDatenModul.MainADOConnection;

  adocmd.CommandText := 'update SYS_BEN set STATUS=''AKT''';
  adocmd.CommandText := adocmd.CommandText + ' where REF='+IntToStr (UserRef);

  adocmd.Connection.BeginTrans;

  try
    LVSDatenModul.TraceSQL (adocmd.CommandText);
    adocmd.Execute;

    adocmd.Connection.CommitTrans;
  except
    adocmd.Connection.RollbackTrans;

    res := -9;
  end;
  adocmd.Free;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function UnlockUser (const DBUser : String; var ErrorText : String) : Integer;
var
  res      : Integer;
  adocmd   : TADOCommand;
begin
  res := 0;

  adocmd := TADOCommand.Create (Nil);

  try
    adocmd.Connection := LVSDatenModul.MainADOConnection;

    adocmd.CommandText := 'alter user "'+DBUser+'" ACCOUNT UNLOCK';

    try
      LVSDatenModul.TraceSQL (adocmd.CommandText);

      adocmd.Execute;
    except
      on e: Exception do begin
        res := -9;
        ErrorText := e.Message;
      end;
    end;
  finally
    adocmd.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeactivateUser (const UserRef : Integer) : Integer;
var
  res      : Integer;
  adocmd   : TADOCommand;
begin
  res := 0;

  adocmd := TADOCommand.Create (Nil);
  adocmd.Connection := LVSDatenModul.MainADOConnection;

  adocmd.CommandText := 'update SYS_BEN set STATUS=''ANG''';
  adocmd.CommandText := adocmd.CommandText + ' where REF='+IntToStr (UserRef);

  adocmd.Connection.BeginTrans;

  try
    LVSDatenModul.TraceSQL (adocmd.CommandText);
    adocmd.Execute;

    adocmd.Connection.CommitTrans;
  except
    adocmd.Connection.RollbackTrans;
    res := -9;
  end;
  adocmd.Free;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteUser (const UserRef : Integer) : Integer;
var
  res      : Integer;
  adocmd   : TADOCommand;
begin
  res := 0;

  adocmd := TADOCommand.Create (Nil);
  adocmd.Connection := LVSDatenModul.MainADOConnection;

  adocmd.CommandText := 'update SYS_BEN set STATUS=''DEL''';
  adocmd.CommandText := adocmd.CommandText + ' where REF='+IntToStr (UserRef);

  adocmd.Connection.BeginTrans;

  try
    LVSDatenModul.TraceSQL (adocmd.CommandText);
    adocmd.Execute;

    adocmd.Connection.CommitTrans;
  except
    adocmd.Connection.RollbackTrans;

    res := -9;
  end;
  adocmd.Free;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetUserRecht (const UserRef, ACORef : Integer; var Recht : TLVSRechte) : Integer; overload;
var
  res     : integer;
  query   : TADOQuery;
  strwert : String;
begin
  res := 0;

  Recht := [];

  query := TADOQuery.Create (Nil);
  try
    query.Connection    := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select RECHTE_FLAGS from V_SYS_ACO_LIST where REF_GRP is null and REF_BEN=:UserRef and REF_ACO=:ACORef');
    query.Parameters.ParamByName('UserRef').Value := UserRef;
    query.Parameters.ParamByName('ACORef').Value := ACORef;

    try
      query.Open;

      if not (query.Fields [0].IsNull) then begin
        strwert := query.Fields [0].AsString;

        if (Pos ('E', strwert) <> 0) then Recht := Recht + [Exec];
        if (Pos ('R', strwert) <> 0) then Recht := Recht + [Read];
        if (Pos ('W', strwert) <> 0) then Recht := Recht + [Write];
        if (Pos ('A', strwert) <> 0) then Recht := Recht + [Admin];
        if (Pos ('G', strwert) <> 0) then Recht := Recht + [Grant];
      end;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function UserIsAdmin (UserID : String) : Boolean;
var
  flag     : Boolean;
  adoquery : TADOQuery;
begin
  flag := False;

  adoquery := TADOQuery.Create (Nil);
  try
    adoquery.Connection := LVSDatenModul.MainADOConnection;

    //Pr�fen ob der User in der DB schon angelegt ist
    adoquery.SQL.Clear;
    adoquery.SQL.Add ('select IS_ADMIN from V_SYS_BEN where upper (USER_ID)=:UserID');
    adoquery.Parameters.ParamByName('UserID').Value := UpperCase (UserID);

    try
      adoquery.Open;

      flag := (adoquery.RecordCount > 0) and (adoquery.Fields [0].AsString = '1');

      adoquery.Close;
    except
    end;
  finally
    adoquery.Free;
  end;

  Result := flag;
end;

end.
