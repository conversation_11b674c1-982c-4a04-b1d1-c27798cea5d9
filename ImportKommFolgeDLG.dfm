object ImportKommFolgeForm: TImportKommFolgeForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Import der Kommissionierfolge'
  ClientHeight = 421
  ClientWidth = 678
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    678
    421)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 56
    Width = 62
    Height = 13
    Caption = 'Lagerbereich'
    Enabled = False
  end
  object Label3: TLabel
    Left = 8
    Top = 102
    Width = 269
    Height = 13
    Caption = 'Importierte Liste der Laufreihenfolge f'#252'r die Lagerpl'#228'tze'
  end
  object Label4: TLabel
    Left = 9
    Top = 13
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object ResultLabel: TLabel
    Left = 104
    Top = 394
    Width = 55
    Height = 13
    Caption = 'ResultLabel'
  end
  object CreateButton: TButton
    Left = 395
    Top = 389
    Width = 275
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Folgenummern neu setzen'
    TabOrder = 4
    OnClick = CreateButtonClick
  end
  object ImportStringGrid: TStringGridPro
    Left = 8
    Top = 118
    Width = 661
    Height = 265
    Anchors = [akLeft, akTop, akRight, akBottom]
    ColCount = 11
    DefaultColWidth = 10
    DefaultRowHeight = 16
    TabOrder = 2
    TitelTexte.Strings = (
      ''
      'Bereich'
      'Zone'
      'Platznr.'
      'Reihe'
      'Feld'
      'Platz'
      'Ebene'
      'Neue Folgenr.'
      'Platzname'
      'Barcode')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'Tahoma'
    TitelFont.Style = []
    ColWidths = (
      10
      118
      74
      60
      48
      40
      40
      40
      75
      78
      53)
  end
  object ReadButton: TButton
    Left = 8
    Top = 389
    Width = 75
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Einlesen'
    TabOrder = 3
    OnClick = ReadButtonClick
  end
  object LBComboBox: TComboBoxPro
    Left = 8
    Top = 72
    Width = 663
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    Enabled = False
    ItemHeight = 15
    TabOrder = 1
    OnChange = LBComboBoxChange
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 29
    Width = 663
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 0
    OnChange = LBComboBoxChange
  end
  object OpenDialog1: TOpenDialog
    DefaultExt = '*.csv'
    Filter = 'CSV-Dateien|*.csv'
    Left = 360
    Top = 8
  end
end
