unit EditVPEDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, DB, ADODB;

type
  TEditVPEForm = class(TForm)
    KurzEdit: TEdit;
    LangEdit: TEdit;
    OkButton: TButton;
    AbortButton: TButton;
    Label1: TLabel;
    Label2: TLabel;
    Bevel1: TBevel;
    ADOQuery1: TADOQuery;
    Label3: TLabel;
    MDEDescEdit: TEdit;
    Label4: TLabel;
    MandDescEdit: TEdit;
    Bevel2: TBevel;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    RefMand    : Integer;
    RefSubMand : Integer;
    Referenz   : Integer;
  end;

implementation

uses
  DatenModul, LVSArtikelInterface, FrontendUtils, SprachModul, ResourceText;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditVPEForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res : Integer;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    if (Length (KurzEdit.Text) = 0) then begin
      CanClose := False;
      KurzEdit.SetFocus;
    end else if (Referenz = -1) then begin
      if (RefMand = -1) then
        RefMand := LVSDatenModul.AktMandantRef;

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select REF from V_ARTIKEL_VPE where REF_MAND='+IntToStr (RefMand)+' and KURZ_BEZEICHNUNG='+#39+KurzEdit.Text+#39);

      try
        ADOQuery1.Open;

        CanClose := ADOQuery1.RecordCount = 0;

        ADOQuery1.Close;

        if not (CanClose) then
          MessageDLG (FormatMessageText (1509, []), mtError, [mbOK], 0)
        else begin
          if (Length (MDEDescEdit.Text) = 0) then
            res := InsertVPE (RefMand, RefSubMand, LangEdit.Text, KurzEdit.Text, Referenz)
          else res := InsertVPE (RefMand, RefSubMand, LangEdit.Text, KurzEdit.Text, MDEDescEdit.Text, Referenz);

          if (res = 0) then
            CanClose := True
          else MessageDLG (FormatMessageText (1510, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
        end;
      except
        MessageDLG (FormatMessageText (1510, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
        CanClose := False;
      end;
    end else begin
      if (Length (MDEDescEdit.Text) = 0) then
        res := ChangeVPE (Referenz, LangEdit.Text, KurzEdit.Text)
      else res := ChangeVPE (Referenz, LangEdit.Text, KurzEdit.Text, MDEDescEdit.Text);

      if (res = 0) then
        CanClose := True
      else MessageDLG (FormatMessageText (1511, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditVPEForm.FormCreate(Sender: TObject);
begin
  RefMand    := -1;
  RefSubMand := -1;
  Referenz   := -1;

  MDEDescEdit.Text := '';
  KurzEdit.Text    := '';
  LangEdit.Text    := '';
  MandDescEdit.Text:= '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditVPEForm.FormShow(Sender: TObject);
begin
  if (Referenz = -1) then begin
    KurzEdit.Text    := '';
    MDEDescEdit.Text := '';
    LangEdit.Text    := '';

    KurzEdit.Enabled := True;
    KurzEdit.SetFocus;
  end else begin
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_ARTIKEL_VPE where REF='+IntToStr (Referenz));

    try
      ADOQuery1.Open;

      RefMand      := ADOQuery1.FieldByName ('REF_MAND').AsInteger;
      RefSubMand   := DBGetReferenz (ADOQuery1.FieldByName ('REF_SUB_MAND'));

      KurzEdit.Text    := ADOQuery1.FieldByName ('KURZ_BEZEICHNUNG').AsString;
      MDEDescEdit.Text := ADOQuery1.FieldByName ('MDE_BEZEICHNUNG').AsString;
      MandDescEdit.Text := ADOQuery1.FieldByName ('MANDANT_EINHEIT').AsString;
      LangEdit.Text    := ADOQuery1.FieldByName ('BEZEICHNUNG').AsString;

      ADOQuery1.Close;
    except
    end;

    KurzEdit.Enabled := False;
    LangEdit.SetFocus;
  end;
end;

end.
