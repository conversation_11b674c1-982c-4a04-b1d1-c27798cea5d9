package cslibrary;

{$R *.res}
{$R 'BetterADODataSet.dcr'}
{$R 'CompTranslate.dcr'}
{$R 'DBGridPro.dcr'}
{$R 'ExtColorCombo.dcr'}
{$R 'ACOList.dcr'}
{$IFDEF IMPLICITBUILDING This IFDEF should not be used by users}
{$ALIGN 8}
{$ASSERTIONS ON}
{$BOOLEVAL OFF}
{$DEBUGINFO OFF}
{$EXTENDEDSYNTAX ON}
{$IMPORTEDDATA ON}
{$IOCHECKS OFF}
{$LOCALSYMBOLS OFF}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION ON}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO OFF}
{$SAFEDIVIDE OFF}
{$STACKFRAMES OFF}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST ON}
{$MINENUMSIZE 1}
{$IMAGEBASE $51000000}
{$ENDIF IMPLICITBUILDING}
{$DESCRIPTION 'common solutions Library'}
{$IMPLICITBUILD OFF}

requires
  rtl,
  vcl,
  dbrtl,
  adortl,
  vcldb,
  designide,
  vclactnband,
  vclx,
  madBasic_,
  madDisAsm_,
  madExcept_;

contains
  BetterADODataSet in 'BetterADODataSet.PAS',
  CompTranslate in 'CompTranslate.pas',
  DBGridPro in 'DBGridPro.pas',
  ExtColorCombo in 'ExtColorCombo.pas',
  CompTranslateEditProp in 'CompTranslateEditProp.pas',
  StringGridPro in 'StringGridPro.pas',
  ACOList in 'ACOList.pas',
  IntegerUpDown in 'IntegerUpDown.pas',
  ComboBoxPro in 'ComboBoxPro.pas',
  ACOListEditProp in 'ACOListEditProp.pas',
  CompTranslateEdit in 'CompTranslateEdit.pas' {CompTranslateEditForm},
  NoBoardPageControl in 'NoBoardPageControl.pas',
  ColorButton in 'ColorButton.pas',
  CustomPanelButton in 'CustomPanelButton.pas',
  Led in 'Led.pas',
  TabListBox in 'TabListBox.pas';

end.
