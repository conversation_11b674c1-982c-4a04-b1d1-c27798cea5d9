object MoveHistoryForm: TMoveHistoryForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Bewegungshistorie'
  ClientHeight = 374
  ClientWidth = 672
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 672
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label1: TLabel
      Left = 16
      Top = 16
      Width = 31
      Height = 13
      Caption = 'Label1'
    end
    object Label2: TLabel
      Left = 78
      Top = 16
      Width = 31
      Height = 13
      Caption = 'Label2'
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 333
    Width = 672
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      672
      41)
    object CloseButton: TButton
      Left = 592
      Top = 9
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 0
    end
  end
  object MoveDBGrid: TDBGridPro
    Left = 0
    Top = 82
    Width = 672
    Height = 251
    Align = alClient
    DataSource = Date
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 2
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\CS'
    RegistrySection = 'DBGrids'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
    OnColumnSort = MoveDBGridColumnSort
  end
  object DatumPanel: TPanel
    Left = 0
    Top = 41
    Width = 672
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    object Label6: TLabel
      Left = 16
      Top = 14
      Width = 52
      Height = 13
      Caption = 'Von Datum'
    end
    object Label7: TLabel
      Left = 187
      Top = 14
      Width = 47
      Height = 13
      Alignment = taRightJustify
      Caption = 'Bis Datum'
    end
    object VonDateTimePicker: TDateTimePicker
      Left = 78
      Top = 10
      Width = 91
      Height = 21
      Date = 42182.962714189820000000
      Time = 42182.962714189820000000
      TabOrder = 0
      OnCloseUp = DateTimePickerChange
      OnChange = DateTimePickerChange
    end
    object BisDateTimePicker: TDateTimePicker
      Left = 246
      Top = 10
      Width = 91
      Height = 21
      Date = 42182.962714189820000000
      Time = 42182.962714189820000000
      TabOrder = 1
      OnCloseUp = DateTimePickerChange
      OnChange = DateTimePickerChange
    end
  end
  object MoveQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 544
    Top = 40
  end
  object Date: TDataSource
    DataSet = MoveQuery
    Left = 584
    Top = 40
  end
end
