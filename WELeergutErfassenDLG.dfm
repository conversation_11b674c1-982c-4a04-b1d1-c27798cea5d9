object WELeergutErfassenForm: TWELeergutErfassenForm
  Left = 383
  Top = 161
  BorderIcons = [biSystemMenu]
  BorderStyle = bsDialog
  Caption = 'WELeergutErfassenForm'
  ClientHeight = 391
  ClientWidth = 388
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poMainFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object KopfPanel: TPanel
    Left = 0
    Top = 0
    Width = 388
    Height = 153
    Align = alTop
    Anchors = [akLeft]
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      388
      153)
    object Label1: TLabel
      Left = 8
      Top = 37
      Width = 32
      Height = 13
      Caption = 'Label1'
    end
    object Label2: TLabel
      Left = 8
      Top = 131
      Width = 82
      Height = 13
      Anchors = [akLeft]
      Caption = 'Ladungstr'#228'ger'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object IOLabel: TLabel
      Left = 114
      Top = 131
      Width = 54
      Height = 13
      Anchors = [akLeft]
      Caption = 'Anzahl i. O.'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
    end
    object NIOLabel: TLabel
      Left = 182
      Top = 131
      Width = 71
      Height = 13
      Anchors = [akLeft]
      Caption = 'Anzahl defekte'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
    end
    object KontoLabel: TLabel
      Left = 80
      Top = 8
      Width = 65
      Height = 13
      Caption = 'KontoLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object HinweisLabel: TLabel
      Left = 8
      Top = 61
      Width = 372
      Height = 16
      Alignment = taCenter
      Anchors = [akLeft, akTop, akRight]
      AutoSize = False
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object RETLabel: TLabel
      Left = 284
      Top = 131
      Width = 82
      Height = 13
      Anchors = [akLeft]
      Caption = 'Anzahl getauscht'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
    end
    object Label3: TLabel
      Left = 8
      Top = 8
      Width = 28
      Height = 13
      Caption = 'Konto'
    end
    object Label5: TLabel
      Left = 8
      Top = 68
      Width = 44
      Height = 13
      Caption = 'Spedition'
    end
    object Label4: TLabel
      Left = 114
      Top = 111
      Width = 141
      Height = 13
      Alignment = taCenter
      Anchors = [akLeft]
      AutoSize = False
      Caption = #220'bernommen'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label7: TLabel
      Left = 284
      Top = 112
      Width = 82
      Height = 13
      Alignment = taCenter
      Anchors = [akLeft]
      AutoSize = False
      Caption = 'Getauscht'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object SpedCombobox: TComboBoxPro
      Left = 8
      Top = 84
      Width = 375
      Height = 21
      ItemHeight = 0
      TabOrder = 0
      Text = 'SpedCombobox'
      Visible = False
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 277
    Width = 388
    Height = 114
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      388
      114)
    object Label6: TLabel
      Left = 8
      Top = 0
      Width = 56
      Height = 13
      Caption = 'Kommentar:'
    end
    object Button1: TButton
      Left = 224
      Top = 82
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Ok'
      Default = True
      ModalResult = 1
      TabOrder = 1
    end
    object Button2: TButton
      Left = 305
      Top = 82
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 2
    end
    object CommentMemo: TMemo
      Left = 8
      Top = 16
      Width = 372
      Height = 57
      Anchors = [akLeft, akTop, akRight]
      Lines.Strings = (
        'CommentMemo')
      TabOrder = 0
    end
  end
  object LTScrollBox: TScrollBox
    Left = 0
    Top = 153
    Width = 388
    Height = 89
    Align = alClient
    BevelInner = bvNone
    BevelOuter = bvNone
    BorderStyle = bsNone
    TabOrder = 3
  end
  object BestandPanel: TPanel
    Left = 0
    Top = 242
    Width = 388
    Height = 35
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    Visible = False
    DesignSize = (
      388
      35)
    object LTBestandBuchenCheckBox: TCheckBox
      Left = 8
      Top = 6
      Width = 372
      Height = 17
      Anchors = [akLeft, akTop, akRight]
      Caption = 'LT Bestand buchen'
      Checked = True
      State = cbChecked
      TabOrder = 0
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 264
    Top = 16
  end
end
