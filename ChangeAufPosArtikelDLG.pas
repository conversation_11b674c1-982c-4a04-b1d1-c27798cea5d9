unit ChangeAufPosArtikelDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, DB, ADODB, ComboBoxPro, ComCtrls, ArtikelListeDLG, BarCodeScanner;

type
  TChangeAufPosArtikelForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    Label2: TLabel;
    AufNrLabel: TLabel;
    Label3: TLabel;
    KundeLabel: TLabel;
    Bevel3: TBevel;
    Label4: TLabel;
    Label5: TLabel;
    ArtikelLabel: TLabel;
    MengeLabel: TLabel;
    Bevel2: TBevel;
    ArtikelComboBox: TComboBoxPro;
    Label6: TLabel;
    ADOQuery1: TADOQuery;
    MengeEdit: TEdit;
    Label7: TLabel;
    Label8: TLabel;
    GrundEdit: TEdit;
    Bevel1: TBevel;
    Label1: TLabel;
    ARGrpComboBox: TComboBoxPro;
    ArNrEdit: TEdit;
    MengeUpDown: TUpDown;
    ListArtikelButton: TButton;
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure MengeEditKeyPress(Sender: TObject; var Key: Char);
    procedure ARGrpComboBoxChange(Sender: TObject);
    procedure ArtikelComboBoxDropDown(Sender: TObject);
    procedure ArNrEditChange(Sender: TObject);
    procedure ListArtikelButtonClick(Sender: TObject);
    procedure ArNrEditExit(Sender: TObject);
  private
    fRefAR      : Integer;
    fRefMand    : Integer;
    fRefSubMand : Integer;
    fRefLager   : Integer;

    fOldArNrEdit : string;
    fArtikelComboxLoaded : boolean;
    
    fBlankCommendAllowed : Boolean;

    fArtikelListeForm : TArtikelListeForm;

    function ReloadArtikel (const RefAR : Integer) : Integer;

    procedure ScannerErfassung (var Message: TMessage); message WM_USER + SCANNER_DETECT;
  public
    property BlankCommendAllowed : Boolean read fBlankCommendAllowed write fBlankCommendAllowed;

    function PrepareKommPos (const RefKommPos : Integer) : Integer;
    function PrepareAufPos  (const RefAufPos : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, LVSConst, DatenModul, FrontendUtils, ResourceText, SprachModul,
  ConfigModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function TChangeAufPosArtikelForm.ReloadArtikel (const RefAR : Integer) : Integer;
var
  idx,
  selidx,
  dlgres     : Integer;
begin
  selidx := -1;

  Screen.Cursor := crSQLWait;

  try
    ADOQuery1.SQL.Clear;

    ADOQuery1.SQL.Add ('select ar.REF_AR_EINHEIT,nvl (qar.ARTIKEL_NR_MANDANT, ar.ARTIKEL_NR),ar.ARTIKEL_TEXT,ar.EINHEIT,ar.REF from V_ARTIKEL_SUCHE ar, VQ_ARTIKEL qar where qar.REF=ar.REF and');

    if (RefAR > 0) then begin
      ADOQuery1.SQL.Add ('ar.REF=:ref_ar');
      ADOQuery1.Parameters.ParamByName ('ref_ar').Value := RefAR;
    end else begin
      if (Length (ArNrEdit.Text) > 0) then begin
        ADOQuery1.SQL.Add ('((ar.ARTIKEL_NR like :ar_nr) or (qar.ARTIKEL_NR_MANDANT like :ar_mand_nr))');
        ADOQuery1.Parameters.ParamByName('ar_nr').Value := ArNrEdit.Text+'%';
        ADOQuery1.Parameters.ParamByName('ar_mand_nr').Value := ArNrEdit.Text+'%';

        if (fRefSubMand > 0) then begin
          ADOQuery1.SQL.Add ('and ar.REF_SUB_MAND=:ref_mand');
          ADOQuery1.Parameters.ParamByName ('ref_mand').Value := fRefSubMand;
        end else begin
          ADOQuery1.SQL.Add ('and ar.REF_MAND=:ref_mand');
          ADOQuery1.Parameters.ParamByName ('ref_mand').Value := fRefMand;
        end;
      end else if (GetComboBoxRef(ARGrpComboBox) > 0) then begin
        ADOQuery1.SQL.Add ('qar.REF_GRUPPE=:ref_grp');
        ADOQuery1.Parameters.ParamByName ('ref_grp').Value := GetComboBoxRef(ARGrpComboBox);
      end else if (fRefSubMand > 0) then begin
        ADOQuery1.SQL.Add ('ar.REF_SUB_MAND=:ref_mand');
        ADOQuery1.Parameters.ParamByName ('ref_mand').Value := fRefSubMand;
      end else begin
        ADOQuery1.SQL.Add ('ar.REF_MAND=:ref_mand');
        ADOQuery1.Parameters.ParamByName ('ref_mand').Value := fRefMand;
      end;

      if LVSConfigModul.UseLocationListing then begin
        ADOQuery1.SQL.Add ('and ar.REF in (select REF_AR from V_ARTIKEL_REL_LOCATION where STATUS=''AKT'' and REF_LOCATION=:ref_loc)');
        ADOQuery1.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
      end;
    end;
    ADOQuery1.SQL.Add ('order by LPAD (ar.ARTIKEL_NR,32,'' '')');

    try
      ADOQuery1.Open;

      if (ADOQuery1.RecordCount < 10000) then
        dlgres := mrYes
      else
        dlgres := MessageDLG (FormatMessageText (1135, [IntToStr (ADOQuery1.RecordCount)]), mtConfirmation, [mbYes, mbNo, mbCancel], 0);

      if (dlgres = mrYes) then begin
        Screen.Cursor := crSQLWait;

        ArtikelComboBox.Items.BeginUpdate;

        try
          ClearComboBoxObjects (ArtikelComboBox);

          while not (ADOQuery1.Eof) do begin
            idx := ArtikelComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString + '|' + ADOQuery1.Fields [3].AsString + '|'+ADOQuery1.Fields [2].AsString, TComboBoxArtikelRef.Create (ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [4].AsInteger));

            if (ADOQuery1.Fields [4].AsInteger = fRefAR) then
              selidx := idx;

            ADOQuery1.Next;
          end;
        finally
          ArtikelComboBox.Items.EndUpdate;
        end;

        fOldArNrEdit := ArNrEdit.Text;
      end;

      ADOQuery1.Close;
    except
    end;

    if (selidx > 0) then
      ArtikelComboBox.ItemIndex := selidx
    else ArtikelComboBox.ItemIndex := 0;
  finally
    Screen.Cursor := crDefault;
  end;

  //Label14.Caption := IntToStr (ArtikelComboBox.Items.Count);

  fArtikelComboxLoaded := True;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeAufPosArtikelForm.ARGrpComboBoxChange(Sender: TObject);
begin
  ReloadArtikel (-1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 15.06.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeAufPosArtikelForm.ArNrEditChange(Sender: TObject);
begin
  ARGrpComboBox.Enabled := (Length (ArNrEdit.Text) = 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 08.07.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeAufPosArtikelForm.ArNrEditExit(Sender: TObject);
begin
  if not (fArtikelComboxLoaded) or (fOldArNrEdit <> ArNrEdit.Text) then
    ReloadArtikel (-1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeAufPosArtikelForm.ArtikelComboBoxDropDown(Sender: TObject);
begin
  if not (fArtikelComboxLoaded) or (fOldArNrEdit <> ArNrEdit.Text) then
    ReloadArtikel (-1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeAufPosArtikelForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    if (GetComboBoxRef (ArtikelComboBox) = -1) then begin
      MessageDLG (FormatMessageText (1373, []), mtError, [mbOk], 0);
      ArtikelComboBox.SetFocus;
      CanClose := False;
    end else if not (fBlankCommendAllowed) and (Length (GrundEdit.Text) = 0) then begin
      MessageDLG (FormatMessageText (1571, []), mtError, [mbOk], 0);
      GrundEdit.SetFocus;
      CanClose := False;
    end else begin
      CanClose := True
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeAufPosArtikelForm.FormCreate(Sender: TObject);
begin
  fArtikelListeForm := Nil;

  fBlankCommendAllowed := False;
  fOldArNrEdit         := '';
  fArtikelComboxLoaded := False;

  AufNrLabel.Caption := '';
  KundeLabel.Caption := '';
  ArtikelLabel.Caption := '';
  MengeLabel.Caption := '';

  ArNrEdit.Text  := '';
  MengeEdit.Text := '';
  GrundEdit.Text := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, ARGrpComboBox);
    LVSSprachModul.SetNoTranslate (Self, ArtikelComboBox);
    LVSSprachModul.SetNoTranslate (Self, GrundEdit);
    LVSSprachModul.SetNoTranslate (Self, MengeEdit);
    LVSSprachModul.SetNoTranslate (Self, ArNrEdit);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 03.07.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeAufPosArtikelForm.ListArtikelButtonClick(Sender: TObject);
begin
  if not Assigned (fArtikelListeForm) then begin
    fArtikelListeForm := TArtikelListeForm.Create (Self);
    fArtikelListeForm.ListeTyp := 'AUFPOS_';

    fArtikelListeForm.ShowCollis      := False;
    fArtikelListeForm.ShowMultiCollis := True;

    fArtikelListeForm.RefMand      := fRefMand;
    fArtikelListeForm.RefSubMand   := fRefSubMand;
    fArtikelListeForm.RefLager     := fRefLager;

    fArtikelListeForm.SubMandPanel.Visible := LVSConfigModul.UseSubMandanten;

    fArtikelListeForm.BeschaffungCheckBox.Checked := False;
    fArtikelListeForm.BeschaffungCheckBox.Enabled := True;

    fArtikelListeForm.BestandCheckBox.Visible := False
  end;

  fArtikelListeForm.ArtikelNr := ArNrEdit.Text;

  if (fArtikelListeForm.ShowModal = mrOK) then begin
    ArNrEdit.Text := fArtikelListeForm.ArtikelQuery.FieldByName ('ARTIKEL_NR').AsString;

    ClearComboBoxObjects(ArtikelComboBox);
    ArtikelComboBox.ItemIndex := ArtikelComboBox.Items.AddObject (fArtikelListeForm.ArtikelQuery.FieldByName ('ARTIKEL_NR').AsString + '|' + fArtikelListeForm.ArtikelQuery.FieldByName ('EINHEIT').AsString + '|'+fArtikelListeForm.ArtikelQuery.FieldByName ('ARTIKEL_TEXT').AsString, TComboBoxRef.Create (fArtikelListeForm.ArtikelQuery.FieldByName ('REF_AR_EINHEIT').AsInteger));
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeAufPosArtikelForm.MengeEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (CharInSet (Key, [#8,^C,^V,'0'..'9'])) then begin
    Beep;
    Key := #0;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TChangeAufPosArtikelForm.PrepareKommPos (const RefKommPos : Integer) : Integer;
var
  i,
  res,
  idx     : Integer;
  refgrp  : Integer;
  blankstr : String;
begin
  res := 0;

  Screen.Cursor := crSQLWait;

  try
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select ap.REF,a.REF_MAND,a.REF_SUB_MAND,a.REF_LAGER,ap.REF_AR,ar.REF_GRUPPE,a.AUFTRAG_NR,a.KUNDEN_NR,a.KUNDEN_NAME,ap.AUFTRAG_ARTIKEL_NR'
                      +',GetArtikelText (ar.REF,PA_SESSION_DATEN.GetSprache) as AUFTRAG_ARTIKEL_TEXT,ap.MENGE_BESTELLT,ap.AUFTRAG_EINHEIT,ap.MENGE_SOLL,ap.MENGE_GESAMT'
                      +'from VQ_KOMM_POS kp inner join VQ_AUFTRAG_POS ap on (ap.REF=kp.REF_AUF_POS) inner join V_AUFTRAG a on (a.REF=ap.REF_AUF_KOPF)'
                      +'inner join V_ARTIKEL ar on (ar.REF=ap.REF_AR) where kp.REF=:ref');
    ADOQuery1.Parameters [0].Value := IntToStr (RefKommPos);

    ADOQuery1.Open;

    if (ADOQuery1.FieldByName ('REF').IsNull) then
      res := -1
    else begin
      fRefAR       := ADOQuery1.FieldByName ('REF_AR').AsInteger;
      refgrp       := DBGetReferenz (ADOQuery1.FieldByName ('REF_GRUPPE'));
      fRefMand     := ADOQuery1.FieldByName ('REF_MAND').AsInteger;
      fRefSubMand  := DBGetReferenz (ADOQuery1.FieldByName ('REF_SUB_MAND'));
      fRefLager    := ADOQuery1.FieldByName ('REF_LAGER').AsInteger;

      AufNrLabel.Caption := ADOQuery1.FieldByName('AUFTRAG_NR').AsString;
      KundeLabel.Caption := ADOQuery1.FieldByName('KUNDEN_NR').AsString + ' / '+ ADOQuery1.FieldByName('KUNDEN_NAME').AsString;

      ArtikelLabel.Caption := ADOQuery1.FieldByName ('AUFTRAG_ARTIKEL_NR').AsString + ' / ' + ADOQuery1.FieldByName ('AUFTRAG_ARTIKEL_TEXT').AsString;
      MengeLabel.Caption   := ADOQuery1.FieldByName ('MENGE_BESTELLT').AsString + ' ' + ADOQuery1.FieldByName ('AUFTRAG_EINHEIT').AsString;

      MengeUpDown.Position := ADOQuery1.FieldByName ('MENGE_SOLL').AsInteger - ADOQuery1.FieldByName ('MENGE_GESAMT').AsInteger;

      ADOQuery1.Close;

      ClearComboBoxObjects (ARGrpComboBox);
      ARGrpComboBox.Items.Add (GetResourceText (1020));
      ARGrpComboBox.ItemIndex := 0;

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select LEVEL, REF, NAME, BEZEICHNUNG from V_ARTIKEL_GRUPPE where');
      if (fRefSubMand > 0) then begin
        ADOQuery1.SQL.Add ('REF_SUB_MAND=:ref_mand');
        ADOQuery1.Parameters.ParamByName ('ref_mand').Value := fRefSubMand;
      end else begin
        ADOQuery1.SQL.Add ('REF_MAND=:ref_mand');
        ADOQuery1.Parameters.ParamByName ('ref_mand').Value := fRefMand;
      end;

      ADOQuery1.SQL.Add ('start with REF_PARENT is null connect by prior REF=REF_PARENT');
      ADOQuery1.SQL.Add ('order siblings by case when (substr (NAME,1,1) between ''0'' and ''9'') then lpad (NAME,9,'' '') else upper (NAME) end');

      ARGrpComboBox.Items.BeginUpdate;

      try
        try
          ADOQuery1.Open;

          while not (ADOQuery1.EOF) do begin
            blankstr := '';

            for i:= 0 to ADOQuery1.Fields [0].AsInteger do
              blankstr := blankstr + ' ';

            idx := ARGrpComboBox.Items.AddObject (blankstr+ADOQuery1.Fields [2].AsString+'|'+ADOQuery1.Fields [3].AsString, TComboBoxRef.Create (ADOQuery1.Fields [1].AsInteger));

            if ((refgrp > 0) and (ADOQuery1.Fields [1].AsInteger = refgrp)) then
              ARGrpComboBox.ItemIndex := idx;

            ADOQuery1.Next;
          end;

          ADOQuery1.Close;
        except
        end;
      finally
        ARGrpComboBox.Items.EndUpdate;
      end;

      ARGrpComboBox.Enabled := (ARGrpComboBox.Items.Count > 1);

      if (ARGrpComboBox.Enabled) and (GetComboBoxRef (ARGrpComboBox) > 0) then
        ARGrpComboBoxChange (Nil);
    end;
  finally
    Screen.Cursor := crDefault;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TChangeAufPosArtikelForm.PrepareAufPos (const RefAufPos : Integer) : Integer;
var
  i,
  res,
  idx     : Integer;
  refgrp  : Integer;
  blankstr : String;
begin
  res := 0;

  Screen.Cursor := crSQLWait;

  try
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select'
                      +' ap.REF,a.REF_MAND,a.REF_SUB_MAND,a.REF_LAGER,ap.REF_AR,ar.REF_GRUPPE,a.AUFTRAG_NR,a.KUNDEN_NR,a.KUNDEN_NAME,ap.AUFTRAG_ARTIKEL_NR'
                      +',GetArtikelText (ar.REF,PA_SESSION_DATEN.GetSprache) as AUFTRAG_ARTIKEL_TEXT,ap.MENGE_BESTELLT,ap.AUFTRAG_EINHEIT,ap.MENGE_SOLL'
                      +' from'
                      +' VQ_AUFTRAG_POS ap'
                      +'   inner join V_AUFTRAG a on (a.REF=ap.REF_AUF_KOPF)'
                      +'   inner join V_ARTIKEL ar on (ar.REF=ap.REF_AR)'
                      +' where ap.REF=:ref');
    ADOQuery1.Parameters [0].Value := RefAufPos;

    ADOQuery1.Open;

    if (ADOQuery1.FieldByName ('REF').IsNull) then
      res := -1
    else begin
      fRefAR       := ADOQuery1.FieldByName ('REF_AR').AsInteger;
      refgrp       := DBGetReferenz (ADOQuery1.FieldByName ('REF_GRUPPE'));
      fRefMand     := ADOQuery1.FieldByName ('REF_MAND').AsInteger;
      fRefSubMand  := DBGetReferenz (ADOQuery1.FieldByName ('REF_SUB_MAND'));
      fRefLager    := ADOQuery1.FieldByName ('REF_LAGER').AsInteger;

      AufNrLabel.Caption := ADOQuery1.FieldByName('AUFTRAG_NR').AsString;
      KundeLabel.Caption := ADOQuery1.FieldByName('KUNDEN_NR').AsString + ' / '+ ADOQuery1.FieldByName('KUNDEN_NAME').AsString;

      ArtikelLabel.Caption := ADOQuery1.FieldByName ('AUFTRAG_ARTIKEL_NR').AsString + ' / ' + ADOQuery1.FieldByName ('AUFTRAG_ARTIKEL_TEXT').AsString;
      MengeLabel.Caption   := ADOQuery1.FieldByName ('MENGE_BESTELLT').AsString + ' ' + ADOQuery1.FieldByName ('AUFTRAG_EINHEIT').AsString;

      MengeEdit.Text := ADOQuery1.FieldByName ('MENGE_SOLL').AsString;

      ADOQuery1.Close;

      ClearComboBoxObjects (ARGrpComboBox);
      ARGrpComboBox.Items.Add (GetResourceText (1020));
      ARGrpComboBox.ItemIndex := 0;

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select LEVEL, REF, NAME, BEZEICHNUNG from V_ARTIKEL_GRUPPE where');
      if (fRefSubMand > 0) then begin
        ADOQuery1.SQL.Add ('REF_SUB_MAND=:ref_mand');
        ADOQuery1.Parameters.ParamByName ('ref_mand').Value := fRefSubMand;
      end else begin
        ADOQuery1.SQL.Add ('REF_MAND=:ref_mand');
        ADOQuery1.Parameters.ParamByName ('ref_mand').Value := fRefMand;
      end;

      ADOQuery1.SQL.Add ('start with REF_PARENT is null connect by prior REF=REF_PARENT');
      ADOQuery1.SQL.Add ('order siblings by case when (substr (NAME,1,1) between ''0'' and ''9'') then lpad (NAME,9,'' '') else upper (NAME) end');

      try
        ADOQuery1.Open;

        while not (ADOQuery1.EOF) do begin
          blankstr := '';

          for i:= 0 to ADOQuery1.Fields [0].AsInteger do
            blankstr := blankstr + ' ';

          idx := ARGrpComboBox.Items.AddObject (blankstr+ADOQuery1.Fields [2].AsString+'|'+ADOQuery1.Fields [3].AsString, TComboBoxRef.Create (ADOQuery1.Fields [1].AsInteger));

          if ((refgrp > 0) and (ADOQuery1.Fields [1].AsInteger = refgrp)) then
            ARGrpComboBox.ItemIndex := idx;

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      except
      end;

      ARGrpComboBox.Enabled := (ARGrpComboBox.Items.Count > 1);
      ARGrpComboBoxChange (Nil);
    end;
  finally
    Screen.Cursor := crDefault;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeAufPosArtikelForm.ScannerErfassung (var Message: TMessage);
var
  res,
  refar,
  refae      : Integer;
  arnr,
  einh,
  errmsg     : String;
  arinfo     : TArtikelInfo;
begin
  res := DetectArtikelBarcode (fRefMand, refar, refae, arnr, einh, errmsg);

  if (res = 0) and (Length (errmsg) = 0) then begin
    arinfo := TArtikelInfo.Create;

    try
      GetArtikelInfos (refar, refae, arinfo);

      if (Length (arinfo.MandArtikelNr) > 0) then
        ArNrEdit.Text := arinfo.MandArtikelNr
      else
        ArNrEdit.Text := arinfo.ArtikelNr;

      ReloadArtikel (refar);
    finally
      arinfo.Free;
    end;
  end;

  if (Length (errmsg) > 0) then
    MessageDLG (errmsg, mtInformation	, [mbOk], 0);
end;

end.
