object KommGewichtAbschlussForm: TKommGewichtAbschlussForm
  Left = 152
  Top = 184
  BorderIcons = [biSystemMenu]
  Caption = 'Kommissioniere Gewicht zur'#252'ckmelden'
  ClientHeight = 537
  ClientWidth = 864
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnHide = FormHide
  OnKeyDown = FormKeyDown
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 8
    Height = 537
    Align = alLeft
    BevelOuter = bvNone
    TabOrder = 0
  end
  object Panel2: TPanel
    Left = 856
    Top = 0
    Width = 8
    Height = 537
    Align = alRight
    BevelOuter = bvNone
    TabOrder = 1
  end
  object Panel3: TPanel
    Left = 8
    Top = 0
    Width = 848
    Height = 537
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 2
    object Panel4: TPanel
      Left = 0
      Top = 0
      Width = 848
      Height = 57
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 0
      DesignSize = (
        848
        57)
      object Label1: TLabel
        Left = 0
        Top = 8
        Width = 79
        Height = 13
        Caption = 'Auftrag-Nummer:'
      end
      object AufNrLabel: TLabel
        Left = 96
        Top = 8
        Width = 64
        Height = 13
        Caption = 'AufNrLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label5: TLabel
        Left = 0
        Top = 32
        Width = 87
        Height = 13
        Caption = 'Kommissionierung:'
      end
      object KommNrLabel: TLabel
        Left = 97
        Top = 32
        Width = 78
        Height = 13
        Caption = 'KommNrLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label85: TLabel
        Left = 476
        Top = 32
        Width = 119
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'Aktueller Kommissionierer'
      end
      object KommUserComboBox: TComboBoxPro
        Left = 601
        Top = 29
        Width = 248
        Height = 22
        Style = csOwnerDrawFixed
        ItemHeight = 16
        TabOrder = 0
      end
    end
    object Panel5: TPanel
      Left = 0
      Top = 232
      Width = 848
      Height = 305
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 1
      DesignSize = (
        848
        305)
      object GroupBox1: TGroupBox
        Left = 6
        Top = 39
        Width = 849
        Height = 228
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Kommissionierposition'
        TabOrder = 0
        DesignSize = (
          849
          228)
        object Label4: TLabel
          Left = 8
          Top = 16
          Width = 46
          Height = 13
          Caption = 'Artikel-Nr:'
        end
        object ArtNrLabel: TLabel
          Left = 80
          Top = 16
          Width = 61
          Height = 13
          Caption = 'ArtNrLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object SollGewichtLabel: TLabel
          Left = 80
          Top = 40
          Width = 99
          Height = 13
          Caption = 'SollGewichtLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object Bevel1: TBevel
          Left = 0
          Top = 60
          Width = 850
          Height = 5
          Anchors = [akLeft, akTop, akRight]
          Shape = bsTopLine
          ExplicitWidth = 849
        end
        object Label6: TLabel
          Left = 8
          Top = 64
          Width = 53
          Height = 13
          Caption = 'Gewicht Ist'
        end
        object Label7: TLabel
          Left = 8
          Top = 120
          Width = 59
          Height = 13
          Caption = 'Neues MHD'
        end
        object Label8: TLabel
          Left = 232
          Top = 16
          Width = 28
          Height = 13
          Caption = 'MHD:'
        end
        object Label9: TLabel
          Left = 232
          Top = 40
          Width = 37
          Height = 13
          Caption = 'Charge:'
        end
        object MHDLabel: TLabel
          Left = 296
          Top = 16
          Width = 60
          Height = 13
          Caption = 'MHDLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object ChargeLabel: TLabel
          Left = 296
          Top = 40
          Width = 72
          Height = 13
          Caption = 'ChargeLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object Label10: TLabel
          Left = 160
          Top = 120
          Width = 34
          Height = 13
          Caption = 'Charge'
        end
        object Bevel2: TBevel
          Left = 8
          Top = 112
          Width = 276
          Height = 9
          Shape = bsTopLine
        end
        object NVELabel: TLabel
          Left = 520
          Top = 16
          Width = 57
          Height = 13
          Caption = 'NVELabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
          Visible = False
        end
        object Label12: TLabel
          Left = 112
          Top = 83
          Width = 12
          Height = 13
          Caption = 'kg'
        end
        object Label2: TLabel
          Left = 8
          Top = 40
          Width = 59
          Height = 13
          Caption = 'Gewicht Soll'
        end
        object Label3: TLabel
          Left = 160
          Top = 64
          Width = 81
          Height = 13
          Caption = 'Gewicht gebucht'
        end
        object Label13: TLabel
          Left = 264
          Top = 83
          Width = 12
          Height = 13
          Caption = 'kg'
        end
        object Label11: TLabel
          Left = 424
          Top = 16
          Width = 83
          Height = 13
          Caption = 'Aktuelle NVE-Nr.:'
        end
        object UpdateButton: TButton
          Left = 209
          Top = 169
          Width = 75
          Height = 25
          Anchors = [akLeft, akBottom]
          Caption = #220'bernehmen'
          TabOrder = 7
          OnClick = UpdateButtonClick
        end
        object GewichtEdit: TEdit
          Left = 8
          Top = 80
          Width = 100
          Height = 21
          TabOrder = 0
          Text = 'GewichtEdit'
          OnEnter = GewichtEditEnter
          OnExit = GewichtEditExit
          OnKeyPress = GewichtEditKeyPress
        end
        object DatumEdit: TEdit
          Left = 8
          Top = 136
          Width = 121
          Height = 21
          TabOrder = 2
          Text = 'DatumEdit'
          OnChange = DataChange
          OnExit = DatumEditExit
          OnKeyPress = DatumEditKeyPress
        end
        object ChargeEdit: TEdit
          Left = 160
          Top = 136
          Width = 121
          Height = 21
          TabOrder = 3
          Text = 'ChargeEdit'
          OnChange = DataChange
          OnKeyPress = ChargeEditKeyPress
        end
        object ClearButton: TButton
          Left = 128
          Top = 169
          Width = 75
          Height = 25
          Anchors = [akLeft, akBottom]
          Caption = 'Verwerfen'
          TabOrder = 6
          OnClick = ClearButtonClick
        end
        object ErfasstStringGrid: TStringGridPro
          Left = 296
          Top = 71
          Width = 547
          Height = 150
          Anchors = [akLeft, akTop, akRight]
          ColCount = 7
          DefaultColWidth = 20
          DefaultRowHeight = 20
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = []
          Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
          ParentFont = False
          PopupMenu = ErfassenGridPopupMenu
          TabOrder = 8
          OnPostDrawCell = ErfasstStringGridDrawCell
          TitelTexte.Strings = (
            ''
            'Menge'
            'Gewicht'
            'MHD'
            'Charge'
            'NVE')
          TitelFont.Charset = DEFAULT_CHARSET
          TitelFont.Color = clWindowText
          TitelFont.Height = -11
          TitelFont.Name = 'Tahoma'
          TitelFont.Style = []
          ColWidths = (
            20
            48
            54
            68
            76
            116
            145)
        end
        object FehlButton: TButton
          Left = 8
          Top = 196
          Width = 95
          Height = 25
          Anchors = [akLeft, akBottom]
          Caption = 'Fehlmenge (F2)'
          TabOrder = 5
          OnClick = FehlButtonClick
        end
        object GwBuchEdit: TEdit
          Left = 160
          Top = 80
          Width = 100
          Height = 21
          TabOrder = 1
          Text = 'GwBuchEdit'
          OnEnter = GewichtEditEnter
          OnExit = GewichtEditExit
          OnKeyPress = GewichtEditKeyPress
        end
        object NVEButton: TButton
          Left = 8
          Top = 169
          Width = 95
          Height = 25
          Caption = 'Neue NVE (F7)'
          TabOrder = 4
          OnClick = NVEButtonClick
        end
      end
      object AbortButton: TButton
        Left = 773
        Top = 273
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Cancel = True
        Caption = 'Abbrechen'
        ModalResult = 3
        TabOrder = 1
      end
      object OkButton: TButton
        Left = 680
        Top = 273
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Ok'
        ModalResult = 1
        TabOrder = 2
      end
      object FehlerLabel: TPanel
        Left = 0
        Top = 0
        Width = 848
        Height = 24
        Align = alTop
        BevelOuter = bvNone
        Caption = 'FehlerLabel'
        Color = clRed
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentBackground = False
        ParentFont = False
        TabOrder = 3
      end
    end
    object KommPosDBGrid: TDBGridPro
      Left = 0
      Top = 57
      Width = 848
      Height = 175
      Align = alClient
      DataSource = KommPosDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      ReadOnly = True
      TabOrder = 2
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      OnDrawColumnCell = KommPosDBGridDrawColumnCell
      OnDblClick = KommPosDBGridDblClick
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
  end
  object KommPosDataSource: TDataSource
    DataSet = KommPosDataSet
    OnDataChange = KommPosDataSourceDataChange
    Left = 272
    Top = 72
  end
  object KommPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    IndexDefs = <>
    Left = 312
    Top = 72
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 728
    Top = 288
  end
  object ErfassenGridPopupMenu: TPopupMenu
    OnPopup = ErfassenGridPopupMenuPopup
    Left = 480
    Top = 400
    object ErfassenDelMenuItem: TMenuItem
      Caption = 'L'#246'schen'
      OnClick = ErfassenDelMenuItemClick
    end
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 776
    Top = 288
  end
end
