unit FrontendDatasets;

interface

uses
  SysUtils, Classes, DB, ADODB, BetterADODataSet;

type
  TFrontendDataModule = class(TDataModule)
    KommPosDataSet: TBetterADODataSet;
    AufPosDataSet: TBetterADODataSet;
    LagerLPDataSet: TBetterADODataSet;
    LagerResDataSet: TBetterADODataSet;
    LagerLEDataSet: TBetterADODataSet;
    BestPosDataSet: TBetterADODataSet;
    BestBestDataSet: TBetterADODataSet;
    LagerNVEDataSet: TBetterADODataSet;
    WANVEDataSet: TBetterADODataSet;
    LagerTAKopfDataSet: TBetterADODataSet;
    LagerTAPosDataSet: TBetterADODataSet;
    BestBesWEDataSet: TBetterADODataSet;
    WEDataSet: TBetterADODataSet;
    KommLPDataSet: TBetterADODataSet;
    VersDataSet: TBetterADODataSet;
    KommARLPDataSet: TBetterADODataSet;
    BenDataSet: TBetterADODataSet;
    WEBesPosDataSet: TBetterADODataSet;
    BenSessionDataSet: TBetterADODataSet;
    SessionDataSet: TBetterADODataSet;
    WarenBesDataSet: TBetterADODataSet;
    WarenBesBesDataSet: TBetterADODataSet;
    ManLiefDataSet: TBetterADODataSet;
    ManLiefPosDataSet: TBetterADODataSet;
    KommARBesDataSet: TBetterADODataSet;
    WarenBesPoolDataSet: TBetterADODataSet;
    WADataSet: TBetterADODataSet;
    LieferungDataSet: TBetterADODataSet;
    LiefNVEDataSet: TBetterADODataSet;
    VersAufDataSet: TBetterADODataSet;
    LEInhaltDataSet: TBetterADODataSet;
    NVEInhaltDataSet: TBetterADODataSet;
    InvDataSet: TBetterADODataSet;
    InvPosDataSet: TBetterADODataSet;
    InvDiffDataSet: TBetterADODataSet;
    IFCErrorDataSet: TBetterADODataSet;
    InvResDataSet: TBetterADODataSet;
    PackKopfDataSet: TBetterADODataSet;
    PackPosDataSet: TBetterADODataSet;
    VerladeDataSet: TBetterADODataSet;
    VerladePosDataSet: TBetterADODataSet;
    MDEDataSet: TBetterADODataSet;
    KommGrpDataSet: TBetterADODataSet;
    KommGrpAufDataSet: TBetterADODataSet;
    KommGrpAufPosDataSet: TBetterADODataSet;
    LiefRetDataSet: TBetterADODataSet;
    LiefRetPosDataSet: TBetterADODataSet;
    BatchKopfDataSet: TBetterADODataSet;
    BatchPosDataSet: TBetterADODataSet;
    QualDataSet: TBetterADODataSet;
    WEAvisDataSet: TBetterADODataSet;
    WEAvisPosDataSet: TBetterADODataSet;
    WATourDataSet: TBetterADODataSet;
    WATourAufDataSet: TBetterADODataSet;
    LagerLPLEDataSet: TBetterADODataSet;
    LagerLPLEBesDataSet: TBetterADODataSet;
    LagerLPBesDataSet: TBetterADODataSet;
    WASpedAufDataSet: TBetterADODataSet;
    BatchKommPosDataSet: TBetterADODataSet;
    NachKopfDataSet: TBetterADODataSet;
    NachPosDataSet: TBetterADODataSet;
    NachPosResDataSet: TBetterADODataSet;
    NachPosPlanDataSet: TBetterADODataSet;
    VorplanDataSet: TBetterADODataSet;
    LifeObjDataSet: TBetterADODataSet;
    LifeArtikelDataSet: TBetterADODataSet;
    WELTPosDataSet: TBetterADODataSet;
    AufLTPosDataSet: TBetterADODataSet;
    VerpackenPosDataSet: TBetterADODataSet;
    VerpackenAufDataSet: TBetterADODataSet;
    ForcastDataSet: TBetterADODataSet;
    LEKommInhaltDataSet: TBetterADODataSet;
    InvPlanPosDataSet: TBetterADODataSet;
    InvPosSollDataSet: TBetterADODataSet;
    InvPosIstDataSet: TBetterADODataSet;
    KommLoadKopfDataSet: TBetterADODataSet;
    KommLoadPosDataSet: TBetterADODataSet;
    AufTourDataSet: TBetterADODataSet;
    AufTourAufDataSet: TBetterADODataSet;
    AufTourAufposDataSet: TBetterADODataSet;
    RetourePosDataSet: TBetterADODataSet;
    RetoureAvisPosDataSet: TBetterADODataSet;
    RetoureLEDataSet: TBetterADODataSet;
    RetoureLEInhaltDataSet: TBetterADODataSet;
    RetoureBestandDataSet: TBetterADODataSet;
    WANVEBestandDataSet: TBetterADODataSet;
    AufSpedTourDataSet: TBetterADODataSet;
    AufSpedTourPosDataSet: TBetterADODataSet;
    procedure KommLPDataSetBeforeClose(DataSet: TDataSet);
    procedure InvDataSetBeforeClose(DataSet: TDataSet);
    procedure VersDataSetBeforeClose(DataSet: TDataSet);
    procedure WarenBesDataSetBeforeClose(DataSet: TDataSet);
    procedure WarenBesPoolDataSetBeforeClose(DataSet: TDataSet);
    procedure BestBesWEDataSetFetchProgress(DataSet: TCustomADODataSet;
      Progress, MaxProgress: Integer; var EventStatus: TEventStatus);
    procedure LagerTAKopfDataSetBeforeClose(DataSet: TDataSet);
    procedure KommKopfDataSetBeforeClose(DataSet: TDataSet);
    procedure AufKopfDataSetBeforeClose(DataSet: TDataSet);
    procedure RemoveOnText(DataSet: TDataSet);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

var
  FrontendDataModule: TFrontendDataModule;

implementation

{$R *.dfm}

uses
  {$IFDEF TRACE}
    Trace,
  {$ENDIF}

  Forms, DatenModul;

procedure TFrontendDataModule.KommKopfDataSetBeforeClose(DataSet: TDataSet);
begin
  KommPosDataSet.Close;

  RemoveOnText (DataSet);
end;

procedure TFrontendDataModule.KommLPDataSetBeforeClose(DataSet: TDataSet);
begin
  KommARBesDataSet.Close;
  KommARLPDataSet.Close;

  RemoveOnText (DataSet);
end;

procedure TFrontendDataModule.LagerTAKopfDataSetBeforeClose(DataSet: TDataSet);
begin
  LagerTAPosDataSet.Close;

  RemoveOnText (DataSet);
end;

procedure TFrontendDataModule.RemoveOnText(DataSet: TDataSet);
var
  idx : Integer;
begin
  {$ifdef Trace}
    ProcedureStart ('TFrontendDataModule.RemoveOnText');
    TraceParameter ('Dataset', DataSet.ClassName);
  {$endif}

  idx := 0;
  
  while (idx < DataSet.FieldCount) do begin
    if Assigned (DataSet.Fields [idx].OnGetText) then
      DataSet.Fields [idx].OnGetText := Nil;

    Inc (idx);
  end;

  {$ifdef Trace}
    ProcedureStop;
  {$endif}
end;

procedure TFrontendDataModule.AufKopfDataSetBeforeClose(DataSet: TDataSet);
begin
  AufPosDataSet.Close;

  RemoveOnText (DataSet);
end;

procedure TFrontendDataModule.BestBesWEDataSetFetchProgress(
  DataSet: TCustomADODataSet; Progress, MaxProgress: Integer;
  var EventStatus: TEventStatus);
begin
  Application.ProcessMessages;
end;

procedure TFrontendDataModule.InvDataSetBeforeClose(DataSet: TDataSet);
begin
  InvPosDataSet.Close;

  RemoveOnText (DataSet);
end;

procedure TFrontendDataModule.VersDataSetBeforeClose(DataSet: TDataSet);
begin
  VersAufDataSet.Close;
  LiefNVEDataSet.Close;

  RemoveOnText (DataSet);
end;

procedure TFrontendDataModule.WarenBesDataSetBeforeClose(DataSet: TDataSet);
begin
  WarenBesBesDataSet.Close;

  RemoveOnText (DataSet);
end;

procedure TFrontendDataModule.WarenBesPoolDataSetBeforeClose(
  DataSet: TDataSet);
begin
  WarenBesBesDataSet.Close;

  RemoveOnText (DataSet);
end;

end.
