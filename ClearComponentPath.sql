-- ******************************************************************************
-- * Script Name: ClearComponentPath.sql
-- * Author: Generated Script
-- * Date: 2025-08-19
-- ******************************************************************************
-- * Description: Löscht alle ComponentPath-Einträge aus der SYS_ACO Tabelle
-- *              Setzt alle COMPONENT_PATH Felder auf NULL zurück
-- ******************************************************************************

-- Backup-A<PERSON><PERSON><PERSON> um zu sehen, welche Einträge betroffen sind
-- (Kommentar entfernen um vor dem Löschen zu prüfen)
/*
SELECT 
    OBJECT_NAME,
    COMPONENT_PATH,
    ACO_TYPE,
    ACO_NAME,
    ACO_TEXT
FROM SYS_ACO 
WHERE COMPONENT_PATH IS NOT NULL 
  AND COMPONENT_PATH <> ''
ORDER BY OBJECT_NAME;
*/

-- Anzahl der betroffenen Datensätze anzeigen
SELECT COUNT(*) AS 'Anzahl Datensätze mit ComponentPath'
FROM SYS_ACO 
WHERE COMPONENT_PATH IS NOT NULL 
  AND COMPONENT_PATH <> '';

-- Hauptabfrage: Alle ComponentPath-Einträge löschen
UPDATE SYS_ACO 
SET COMPONENT_PATH = NULL
WHERE COMPONENT_PATH IS NOT NULL 
  AND COMPONENT_PATH <> '';

-- Bestätigung: Anzahl der aktualisierten Datensätze
SELECT @@ROWCOUNT AS 'Anzahl aktualisierte Datensätze';

-- Verifikation: Prüfen ob alle ComponentPath-Einträge gelöscht wurden
SELECT COUNT(*) AS 'Verbleibende Datensätze mit ComponentPath'
FROM SYS_ACO 
WHERE COMPONENT_PATH IS NOT NULL 
  AND COMPONENT_PATH <> '';

-- Optional: Alle Datensätze anzeigen um das Ergebnis zu überprüfen
/*
SELECT 
    OBJECT_NAME,
    COMPONENT_PATH,
    ACO_TYPE,
    ACO_NAME,
    ACO_TEXT
FROM SYS_ACO 
ORDER BY OBJECT_NAME;
*/
