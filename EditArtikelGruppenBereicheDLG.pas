unit EditArtikelGruppenBereicheDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComboBoxPro;

type
  TEditArtikelGruppenBereicheForm = class(TForm)
    Label6: TLabel;
    LagerComboBox: TComboBoxPro;
    Bevel1: TBevel;
    Bevel2: TBevel;
    Bevel3: TBevel;
    OkButton: TButton;
    AbortButton: TButton;
    Label8: TLabel;
    BereichComboBox: TComboBoxPro;
    Label1: TLabel;
    ComboBoxPro1: TComboBoxPro;
    Label2: TLabel;
    ComboBoxPro2: TComboBoxPro;
    Label3: TLabel;
    ComboBoxPro3: TComboBoxPro;
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

end.
