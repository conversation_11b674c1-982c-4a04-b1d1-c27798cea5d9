object VEPDisplayForm: TVEPDisplayForm
  Left = 302
  Top = 283
  BorderStyle = bsDialog
  Caption = #220'bersicht '#252'ber die Verpackungsarten'
  ClientHeight = 362
  ClientWidth = 553
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    553
    362)
  PixelsPerInch = 96
  TextHeight = 13
  object Bevel1: TBevel
    Left = 8
    Top = 324
    Width = 540
    Height = 26
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitWidth = 433
  end
  object Label1: TLabel
    Left = 8
    Top = 68
    Width = 87
    Height = 13
    Caption = 'Verpackungsarten'
  end
  object Label2: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Bevel2: TBevel
    Left = 8
    Top = 54
    Width = 540
    Height = 11
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 433
  end
  object VPEDBGrid: TDBGridPro
    Left = 8
    Top = 87
    Width = 444
    Height = 225
    Anchors = [akLeft, akTop, akRight]
    DataSource = DataSource1
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDblClick = VPEDBGridDblClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object Button1: TButton
    Left = 471
    Top = 332
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 2
  end
  object Button2: TButton
    Left = 467
    Top = 88
    Width = 79
    Height = 25
    Anchors = [akTop, akRight]
    Caption = '&Neu...'
    TabOrder = 3
    OnClick = Button2Click
  end
  object Button3: TButton
    Left = 467
    Top = 120
    Width = 79
    Height = 25
    Anchors = [akTop, akRight]
    Caption = '&Bearbeiten...'
    TabOrder = 4
    OnClick = Button3Click
  end
  object Button4: TButton
    Left = 467
    Top = 168
    Width = 79
    Height = 25
    Anchors = [akTop, akRight]
    Caption = '&L'#246'schen...'
    TabOrder = 5
    Visible = False
  end
  object MandantComboBox: TComboBoxPro
    Left = 8
    Top = 26
    Width = 538
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 120
    ItemHeight = 16
    TabOrder = 0
    OnChange = MandantComboBoxChange
  end
  object DataSource1: TDataSource
    DataSet = ADOQuery1
    OnDataChange = DataSource1DataChange
    Left = 24
    Top = 144
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    AfterClose = ADOQuery1AfterClose
    Parameters = <>
    Left = 56
    Top = 144
  end
end
