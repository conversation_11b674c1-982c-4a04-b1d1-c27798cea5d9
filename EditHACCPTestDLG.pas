unit EditHACCPTestDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComboBoxPro, DB, ADODB, ComCtrls;

type
  TEditHACCPTestForm = class(TForm)
    BereichComboBox: TComboBoxPro;
    VorgangComboBox: TComboBoxPro;
    Label1: TLabel;
    Label2: TLabel;
    TextEdit: TEdit;
    Label3: TLabel;
    Bevel1: TBevel;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel3: TBevel;
    Label4: TLabel;
    ArtGroupBox: TGroupBox;
    SelTextComboBox: TComboBox;
    Label5: TLabel;
    OptResCheckBox: TCheckBox;
    DefResEdit: TEdit;
    Label6: TLabel;
    Bevel2: TBevel;
    ArtComboBox: TComboBoxPro;
    ADOQuery1: TADOQuery;
    DefSelComboBox: TComboBoxPro;
    ReihenfolgeEdit: TEdit;
    ReihenfolgeUpDown: TUpDown;
    Label7: TLabel;
    Bevel4: TBevel;
    MandantComboBox: TComboBoxPro;
    Label8: TLabel;
    Bevel5: TBevel;
    GroupBox1: TGroupBox;
    AnalyseCheckBox: TCheckBox;
    Label9: TLabel;
    BadSelComboBox: TComboBoxPro;
    AnalyseNegRadioButton: TRadioButton;
    AnalyseAllRadioButton: TRadioButton;
    BadPointsEdit: TEdit;
    Label10: TLabel;
    Label11: TLabel;
    DescEdit: TEdit;
    IDEdit: TEdit;
    Label12: TLabel;
    procedure ArtComboBoxChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure SelTextComboBoxChange(Sender: TObject);
    procedure DefSelComboBoxChange(Sender: TObject);
    procedure BadPointsEditKeyPress(Sender: TObject; var Key: Char);
    procedure FormDestroy(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    procedure Prepare (const Ref     : Integer); overload;
    procedure Prepare (const NewFlag : Boolean); overload;
  end;

implementation

{$R *.dfm}

uses VCLUtilitys, DatenModul, FrontendUtils;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditHACCPTestForm.Prepare (const Ref : Integer);
var
  intwert : Integer;
begin
  if (Ref = -1) then
    Prepare (True)
  else begin
    Prepare (False);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_HACCP_TEST where REF='+IntToStr (Ref));

    ADOQuery1.Open;

    MandantComboBox.ItemIndex := FindComboboxRef (MandantComboBox, ADOQuery1.FieldByName ('REF_MAND').AsInteger);

    BereichComboBox.ItemIndex := BereichComboBox.IndexOf (ADOQuery1.FieldByName ('BEREICH').AsString);
    VorgangComboBox.ItemIndex := VorgangComboBox.IndexOf (ADOQuery1.FieldByName ('VORGANG').AsString);
    ArtComboBox.ItemIndex     := ArtComboBox.IndexOf (ADOQuery1.FieldByName ('RESULT').AsString);

    ArtComboBoxChange (ArtComboBox);
    SelTextComboBox.ItemIndex := FindComboboxRef (SelTextComboBox, ADOQuery1.FieldByName ('REF_RESULT_TEXT').AsInteger, 0);

    DescEdit.Text          := ADOQuery1.FieldByName ('BEZEICHNUNG').AsString;
    IDEdit.Text            := ADOQuery1.FieldByName ('TEST_ID').AsString;
    TextEdit.Text          := ADOQuery1.FieldByName ('TEXT').AsString;
    OptResCheckBox.Checked := ADOQuery1.FieldByName ('RESULT_OPTIONAL').AsString = '1';

    ReihenfolgeUpDown.Position := ADOQuery1.FieldByName ('REIHENFOLGE').AsInteger;

    DefResEdit.Text := ADOQuery1.FieldByName ('DEFAULT_RESULT').AsString;
    if (ArtComboBox.GetItemText = 'SELECT') or (ArtComboBox.GetItemText = 'CHECK') then begin
      if (ADOQuery1.FieldByName ('DEFAULT_RESULT').IsNull) then
        DefSelComboBox.ItemIndex := 0
      else if not (TryStrToInt (ADOQuery1.FieldByName ('DEFAULT_RESULT').AsString, intwert)) then
        DefSelComboBox.ItemIndex := 0
      else DefSelComboBox.ItemIndex := FindComboboxRef (DefSelComboBox, intwert, 0);

      if (ADOQuery1.FieldByName ('BAD_RESULT_INDEX').IsNull) then
        BadSelComboBox.ItemIndex := 0
      else if not (TryStrToInt (ADOQuery1.FieldByName ('BAD_RESULT_INDEX').AsString, intwert)) then
        BadSelComboBox.ItemIndex := 0
      else BadSelComboBox.ItemIndex := FindComboboxRef (BadSelComboBox, intwert, 0);

      if (ADOQuery1.FieldByName ('BAD_POINTS').IsNull) then
        BadPointsEdit.Text := ''
      else
        BadPointsEdit.Text := ADOQuery1.FieldByName ('BAD_POINTS').AsString;
    end;

    AnalyseCheckBox.Checked := ADOQuery1.FieldByName ('USE_FOR_ANALYSE').AsString = '1';

    if (ADOQuery1.FieldByName ('ANALYSE_TYPE').AsInteger = 1) then
      AnalyseAllRadioButton.Checked := True
    else if (ADOQuery1.FieldByName ('ANALYSE_TYPE').AsInteger = 2) then
      AnalyseNegRadioButton.Checked := True;

    ADOQuery1.Close;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditHACCPTestForm.Prepare (const NewFlag : Boolean);
begin
  LoadMandantCombobox (MandantComboBox);
  MandantComboBox.Items.Insert(0, '');

  if (LVSDatenModul.AktMandantRef <> -1) then
    MandantComboBox.ItemIndex := FindComboboxRef (MandantComboBox, LVSDatenModul.AktMandantRef)
  else MandantComboBox.ItemIndex := 0;

  LoadComboxDBItems (BereichComboBox, 'HACCP_TEST', 'BEREICH');
  if (BereichComboBox.ItemIndex = -1) then BereichComboBox.ItemIndex := 0;

  LoadComboxDBItems (VorgangComboBox, 'HACCP_TEST', 'VORGANG');
  if (VorgangComboBox.ItemIndex = -1) then VorgangComboBox.ItemIndex := 0;

  LoadComboxDBItems (ArtComboBox, 'HACCP_TEST', 'RESULT');
  if (ArtComboBox.ItemIndex = -1) then ArtComboBox.ItemIndex := 0;

  BereichComboBox.Enabled := NewFlag;
  VorgangComboBox.Enabled := NewFlag;
  ArtComboBox.Enabled     := NewFlag;

  TextEdit.Text := '';
  DefResEdit.Text := '';
  BadPointsEdit.Text := '';

  ArtComboBoxChange (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditHACCPTestForm.ArtComboBoxChange(Sender: TObject);
var
  oldref,
  lastref : Integer;
  seltext : String;
  query   : TADOQuery;
begin
  if (ArtComboBox.GetItemText = 'SELECT') or (ArtComboBox.GetItemText = 'CHECK') then begin
    oldref := GetComboBoxRef (SelTextComboBox);

    ArtGroupBox.Enabled := True;

    lastref := -1;
    seltext := '';

    SelTextComboBox.Clear;

    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select REF, SEL, TEXT from V_SYS_TEXTE where AREA=''CHECK_SEL'' and SEL<>0 order by REF, SEL');

      query.Open;

      while not (query.Eof) do begin
        if (lastref <> query.Fields [0].AsInteger) then begin
          if (lastref <> -1) then begin
            SelTextComboBox.Items.AddObject(seltext, TComboBoxRef.Create (lastref));
          end;

          lastref  := query.Fields [0].AsInteger;
          seltext := '>' + query.Fields [2].AsString + '<';
        end else begin
          seltext := seltext + ' / >' + query.Fields [2].AsString+'<';
        end;

        query.Next;
      end;

      query.Close;
    finally
      query.Free;
    end;

    if (lastref <> -1) then begin
      SelTextComboBox.Items.AddObject(seltext, TComboBoxRef.Create (lastref));
    end;

    if (ArtComboBox.GetItemText = 'CHECK') then
      SelTextComboBox.Items.Insert (0, '');

    if (oldref = -1) then
      SelTextComboBox.ItemIndex := 0
    else begin
      SelTextComboBox.ItemIndex := FindComboboxRef (SelTextComboBox, oldref);
      if (SelTextComboBox.ItemIndex = -1) then SelTextComboBox.ItemIndex := 0;
    end;

    DefResEdit.Visible := False;

    DefSelComboBox.Visible := True;
    SelTextComboBoxChange (Sender);
  end else begin
    DefSelComboBox.Clear;
    DefSelComboBox.Visible := False;

    DefResEdit.Visible := True;

    SelTextComboBox.Clear;
    SelTextComboBox.Text := '';

    ArtGroupBox.Enabled := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 03.11.2016
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditHACCPTestForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (SelTextComboBox);
  ClearComboBoxObjects (BadSelComboBox);
  ClearComboBoxObjects (DefSelComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditHACCPTestForm.FormShow(Sender: TObject);
begin
  ArtComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditHACCPTestForm.SelTextComboBoxChange(Sender: TObject);
var
  selstr,
  badstr : String;
  query  : TADOQuery;
begin
  selstr := DefSelComboBox.GetItemText;
  badstr := BadSelComboBox.GetItemText;

  DefSelComboBox.Clear;
  DefSelComboBox.Items.AddObject ('', TComboBoxRef.Create (0));

  BadSelComboBox.Clear;
  BadSelComboBox.Items.AddObject ('', Nil);

  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select SEL,TEXT from V_SYS_TEXTE where REF='+IntToStr (GetComboBoxRef (SelTextComboBox)) + ' order by SEL');

    query.Open;

    while not (query.Eof) do begin
      BadSelComboBox.Items.AddObject(query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));
      DefSelComboBox.Items.AddObject(query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

      query.Next;
    end;

    query.Close;
  finally
    query.Free;
  end;

  DefSelComboBox.ItemIndex := DefSelComboBox.IndexOf (selstr);
  if (DefSelComboBox.ItemIndex = -1) then DefSelComboBox.ItemIndex := 0;

  BadSelComboBox.ItemIndex := BadSelComboBox.IndexOf (badstr);
  if (BadSelComboBox.ItemIndex = -1) then BadSelComboBox.ItemIndex := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditHACCPTestForm.BadPointsEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in ['0'..'9', #8,^C,^V])  then begin
    Beep;
    Key := #0;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditHACCPTestForm.DefSelComboBoxChange(Sender: TObject);
begin
  if (GetComboBoxRef (DefSelComboBox) = 0) then
    DefResEdit.Text := ''
  else DefResEdit.Text := IntToStr (GetComboBoxRef (DefSelComboBox));
end;

end.
