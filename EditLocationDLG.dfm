object EditLocationForm: TEditLocationForm
  Left = 428
  Top = 420
  BorderStyle = bsDialog
  Caption = 'Angaben zur Niederlassung '#228'nderen'
  ClientHeight = 541
  ClientWidth = 779
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    779
    541)
  TextHeight = 13
  object OkButton: TButton
    Left = 603
    Top = 511
    Width = 75
    Height = 26
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 0
    ExplicitLeft = 518
  end
  object AbortButton: TButton
    Left = 690
    Top = 511
    Width = 75
    Height = 26
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = '&Abbrechen'
    ModalResult = 3
    TabOrder = 1
    ExplicitLeft = 605
  end
  object PageControl1: TPageControl
    Left = 0
    Top = 0
    Width = 779
    Height = 493
    ActivePage = DatenTabSheet
    Align = alTop
    TabOrder = 2
    ExplicitWidth = 694
    object DatenTabSheet: TTabSheet
      Caption = 'Daten'
      DesignSize = (
        771
        465)
      object Label1: TLabel
        Left = 8
        Top = 12
        Width = 98
        Height = 13
        Caption = 'Niederlassungsname'
      end
      object Label2: TLabel
        Left = 8
        Top = 56
        Width = 65
        Height = 13
        Caption = 'Beschreibung'
      end
      object Bevel3: TBevel
        Left = 7
        Top = 100
        Width = 754
        Height = 9
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
        ExplicitWidth = 676
      end
      object Label5: TLabel
        Left = 8
        Top = 106
        Width = 62
        Height = 13
        Caption = 'Adresszusatz'
      end
      object Label6: TLabel
        Left = 8
        Top = 150
        Width = 108
        Height = 13
        Caption = 'Strasse / Hausnummer'
      end
      object Label7: TLabel
        Left = 8
        Top = 196
        Width = 53
        Height = 13
        Caption = 'Postleitzahl'
      end
      object Label8: TLabel
        Left = 108
        Top = 196
        Width = 14
        Height = 13
        Caption = 'Ort'
      end
      object Bevel2: TBevel
        Left = 4
        Top = 238
        Width = 757
        Height = 2
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
        ExplicitWidth = 679
      end
      object Label3: TLabel
        Left = 8
        Top = 244
        Width = 36
        Height = 13
        Caption = 'Telefon'
      end
      object Label4: TLabel
        Left = 145
        Top = 244
        Width = 17
        Height = 13
        Caption = 'Fax'
      end
      object Label9: TLabel
        Left = 283
        Top = 244
        Width = 78
        Height = 13
        Caption = 'Ansprechpartner'
      end
      object Label10: TLabel
        Left = 8
        Top = 331
        Width = 25
        Height = 13
        Caption = 'Email'
      end
      object Label21: TLabel
        Left = 685
        Top = 196
        Width = 24
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'Land'
        ExplicitLeft = 607
      end
      object Label12: TLabel
        Left = 615
        Top = 12
        Width = 56
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'Interface ID'
        ExplicitLeft = 534
      end
      object NameEdit: TEdit
        Left = 8
        Top = 28
        Width = 590
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 0
        Text = 'NameEdit'
        ExplicitWidth = 505
      end
      object BeschreibungEdit: TEdit
        Left = 8
        Top = 72
        Width = 753
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 2
        Text = 'BeschreibungEdit'
        ExplicitWidth = 668
      end
      object AdrEdit: TEdit
        Left = 8
        Top = 122
        Width = 753
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 3
        Text = 'AdrEdit'
        ExplicitWidth = 668
      end
      object RoadEdit: TEdit
        Left = 8
        Top = 166
        Width = 753
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 4
        Text = 'Edit1'
        ExplicitWidth = 668
      end
      object PLZEdit: TEdit
        Left = 8
        Top = 212
        Width = 85
        Height = 21
        MaxLength = 12
        TabOrder = 5
        Text = 'PLZEdit'
      end
      object OrtEdit: TEdit
        Left = 108
        Top = 212
        Width = 566
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 6
        Text = 'OrtEdit'
        ExplicitWidth = 481
      end
      object FonEdit: TEdit
        Left = 8
        Top = 260
        Width = 121
        Height = 21
        MaxLength = 32
        TabOrder = 8
        Text = 'FonEdit'
      end
      object FaxEdit: TEdit
        Left = 145
        Top = 260
        Width = 121
        Height = 21
        MaxLength = 32
        TabOrder = 9
        Text = 'FaxEdit'
      end
      object ContactEdit: TEdit
        Left = 283
        Top = 260
        Width = 478
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 10
        Text = 'ContactEdit'
        ExplicitWidth = 393
      end
      object MailEdit: TEdit
        Left = 8
        Top = 347
        Width = 753
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 11
        Text = 'MailEdit'
        ExplicitWidth = 668
      end
      object LandEdit: TEdit
        Left = 685
        Top = 212
        Width = 76
        Height = 21
        Anchors = [akTop, akRight]
        MaxLength = 16
        TabOrder = 7
        Text = 'LandEdit'
        ExplicitLeft = 600
      end
      object IFCIDEdit: TEdit
        Left = 615
        Top = 28
        Width = 146
        Height = 21
        Anchors = [akTop, akRight]
        MaxLength = 32
        TabOrder = 1
        Text = 'IFCIDEdit'
        ExplicitLeft = 530
      end
    end
    object ConfigTabSheet: TTabSheet
      Caption = 'Konfiguration'
      ImageIndex = 1
      DesignSize = (
        771
        465)
      object OptionPageControl: TPageControl
        Left = 8
        Top = 128
        Width = 755
        Height = 334
        ActivePage = TabSheet4
        Anchors = [akLeft, akTop, akRight, akBottom]
        TabOrder = 1
        ExplicitWidth = 670
        object TabSheet4: TTabSheet
          Caption = 'Konfiguration'
          ImageIndex = 9
          object SubMandCheckBox: TCheckBox
            Left = 16
            Top = 16
            Width = 249
            Height = 17
            Caption = 'Untermandanten zul'#228'ssig'
            TabOrder = 0
          end
          object MHDCheckBox: TCheckBox
            Left = 16
            Top = 48
            Width = 249
            Height = 17
            Caption = 'MHD Verwaltung aktiv'
            TabOrder = 1
          end
          object ChargeCheckBox: TCheckBox
            Left = 16
            Top = 72
            Width = 249
            Height = 17
            Caption = 'Chargen Verwaltung aktiv'
            TabOrder = 2
          end
          object ColliCheckBox: TCheckBox
            Left = 16
            Top = 104
            Width = 249
            Height = 17
            Caption = 'Artikel Colli Verwaltung aktiv'
            TabOrder = 3
          end
        end
        object TabSheet2: TTabSheet
          Caption = 'Auftr'#228'ge'
          ImageIndex = 6
          object AufEnabledCheckBox: TCheckBox
            Left = 12
            Top = 17
            Width = 285
            Height = 17
            Caption = 'Das Anlegen von Auftr'#228'gen ist zul'#228'ssig'
            TabOrder = 0
          end
          object AufEmpfSelectCheckBox: TCheckBox
            Left = 12
            Top = 56
            Width = 285
            Height = 17
            Caption = 'Auswahl der Warenempf'#228'nger immer anzeigen'
            TabOrder = 1
          end
          object AufAutoAddPosCheckBox: TCheckBox
            Left = 12
            Top = 79
            Width = 285
            Height = 17
            Caption = 'Positionserfassung f'#252'r neue Artikel ge'#246'ffnet lassen'
            TabOrder = 2
          end
          object AufAdrCorrStrCheckBox: TCheckBox
            Left = 333
            Top = 65
            Width = 250
            Height = 17
            Caption = 'Automatisch Korrektur Strasse'
            TabOrder = 6
          end
          object AufCheckAdrCheckBox: TCheckBox
            Left = 317
            Top = 12
            Width = 250
            Height = 17
            Caption = 'Automatisch Adresspr'#252'fung'
            TabOrder = 3
            OnClick = AufCheckAdrCheckBoxClick
          end
          object AufAdrCorrCityCheckBox: TCheckBox
            Left = 333
            Top = 48
            Width = 250
            Height = 17
            Caption = 'Automatisch Korrektur des Ortes'
            TabOrder = 5
          end
          object AufAdrCorrCheckBox: TCheckBox
            Left = 333
            Top = 31
            Width = 317
            Height = 17
            Caption = 'Automatisch Korrektur von Schreibweisen'
            TabOrder = 4
          end
        end
        object TabSheet3: TTabSheet
          Caption = 'Warenerwartung'
          ImageIndex = 7
          object BestEnabledCheckBox: TCheckBox
            Left = 12
            Top = 17
            Width = 485
            Height = 17
            Caption = 'Das Anlegen von Warenerwartungen ist zul'#228'ssig'
            TabOrder = 0
          end
          object BestAutoAddPosCheckBox: TCheckBox
            Left = 12
            Top = 79
            Width = 485
            Height = 17
            Caption = 'Positionserfassung f'#252'r neue Artikel ge'#246'ffnet lassen'
            TabOrder = 1
            Visible = False
          end
        end
        object QSTabSheet: TTabSheet
          Caption = 'Qualit'#228'tskontrolle'
          object Label19: TLabel
            Left = 3
            Top = 13
            Width = 45
            Height = 13
            Caption = 'Annahme'
          end
          object Label20: TLabel
            Left = 3
            Top = 40
            Width = 38
            Height = 13
            Caption = 'Retoure'
          end
          object QSWEComboBox: TComboBoxPro
            Left = 63
            Top = 10
            Width = 145
            Height = 21
            Style = csOwnerDrawFixed
            ItemHeight = 15
            TabOrder = 0
          end
          object QSRETComboBox: TComboBoxPro
            Left = 63
            Top = 37
            Width = 145
            Height = 21
            Style = csOwnerDrawFixed
            ItemHeight = 15
            TabOrder = 1
          end
        end
        object WETabSheet: TTabSheet
          Caption = 'Wareneingang'
          ImageIndex = 3
          object WEPicCheckBox: TCheckBox
            Left = 8
            Top = 16
            Width = 433
            Height = 17
            AllowGrayed = True
            Caption = 'Artikelbilder im WE'
            TabOrder = 0
          end
          object WELastMHDCheckBox: TCheckBox
            Left = 8
            Top = 36
            Width = 433
            Height = 17
            AllowGrayed = True
            Caption = 
              'Pr'#252'fen, ob MHD geringer ist, als das bei der letzten Warenannahm' +
              'e '
            TabOrder = 1
          end
          object WEQSGrundCheckBox: TCheckBox
            Left = 8
            Top = 66
            Width = 433
            Height = 17
            AllowGrayed = True
            Caption = 'QS-Auswahl bei der WE-Erfassung'
            TabOrder = 2
          end
          object WEArEinheitOptCheckBox: TCheckBox
            Left = 8
            Top = 92
            Width = 433
            Height = 17
            AllowGrayed = True
            Caption = 'Sperrgut bzw. Big itme im WE festlegen'
            TabOrder = 3
          end
          object WEBedarfCheckBox: TCheckBox
            Left = 8
            Top = 180
            Width = 161
            Height = 17
            AllowGrayed = True
            Caption = 'Bedarfsmenge anzeigen'
            TabOrder = 6
          end
          object WEKommBestandCheckBox: TCheckBox
            Left = 183
            Top = 180
            Width = 161
            Height = 17
            AllowGrayed = True
            Caption = 'Kommbestand anzeigen'
            TabOrder = 7
          end
          object WENachsBestandCheckBox: TCheckBox
            Left = 325
            Top = 180
            Width = 161
            Height = 17
            AllowGrayed = True
            Caption = 'Nachschubbestand anzeigen'
            TabOrder = 8
          end
          object WESerialCheckBox: TCheckBox
            Left = 8
            Top = 122
            Width = 225
            Height = 17
            Caption = 'Seriennummer erfassen'
            TabOrder = 4
          end
          object WESerialAllCheckBox: TCheckBox
            Left = 24
            Top = 142
            Width = 225
            Height = 17
            Caption = 'Bei allen Artikel'
            TabOrder = 5
          end
        end
        object LiefRetTabSheet: TTabSheet
          Caption = 'R'#252'cksendungen'
          ImageIndex = 2
          DesignSize = (
            747
            306)
          object LiefRetBesCheckBox: TCheckBox
            Left = 8
            Top = 8
            Width = 595
            Height = 17
            AllowGrayed = True
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Lieferantenretouren mit Bestandsanpassungen'
            TabOrder = 0
            ExplicitWidth = 510
          end
          object LiefRetWECheckBox: TCheckBox
            Left = 8
            Top = 30
            Width = 595
            Height = 17
            AllowGrayed = True
            Anchors = [akLeft, akTop, akRight]
            Caption = 'WE-Kontrolle bei Lieferantenretouren'
            TabOrder = 1
            ExplicitWidth = 510
          end
          object RetQSGrundCheckBox: TCheckBox
            Left = 259
            Top = 139
            Width = 238
            Height = 17
            AllowGrayed = True
            Caption = 'QS-Auswahl bei Retouren'
            TabOrder = 8
          end
          object RetCatAuswahlCheckBox: TCheckBox
            Left = 8
            Top = 179
            Width = 238
            Height = 17
            AllowGrayed = True
            Caption = 'Kategorieauswahl bei Retouren'
            TabOrder = 7
          end
          object RetSelZustandCheckBox: TCheckBox
            Left = 8
            Top = 159
            Width = 238
            Height = 17
            AllowGrayed = True
            Caption = 'Zustandsauswahl bei Retouren'
            TabOrder = 6
          end
          object RetSelGrundCheckBox: TCheckBox
            Left = 8
            Top = 139
            Width = 238
            Height = 17
            AllowGrayed = True
            Caption = 'Auswahl des Retourengrundes'
            TabOrder = 5
          end
          object RetAvsiParallelWECheckBox: TCheckBox
            Left = 8
            Top = 262
            Width = 515
            Height = 17
            Caption = 'Mehrere parallele Retourenannahmen f'#252'r Avis zul'#228'ssig'
            TabOrder = 12
          end
          object RetAvsiParallelAnnahmeCheckBox: TCheckBox
            Left = 8
            Top = 283
            Width = 515
            Height = 17
            Caption = 'Gleichzeitige Annahmungen von Ware f'#252'r ein Avis zul'#228'ssig'
            TabOrder = 13
          end
          object RetAutoPrintCheckBox: TCheckBox
            Left = 8
            Top = 52
            Width = 595
            Height = 17
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Automatischer Druck des Retourenbeleges'
            TabOrder = 2
            ExplicitWidth = 510
          end
          object RetPrintLableCheckBox: TCheckBox
            Left = 8
            Top = 74
            Width = 510
            Height = 17
            AllowGrayed = True
            Caption = 'Retourenlabel ausdrucken'
            TabOrder = 3
          end
          object RetZustandCommentCheckBox: TCheckBox
            Left = 259
            Top = 159
            Width = 238
            Height = 17
            Caption = 'Zustands-Beschreibung'
            TabOrder = 9
          end
          object RetSerialCheckBox: TCheckBox
            Left = 8
            Top = 207
            Width = 225
            Height = 17
            Caption = 'Seriennummer erfassen'
            TabOrder = 10
          end
          object RetSerialAllCheckBox: TCheckBox
            Left = 24
            Top = 230
            Width = 225
            Height = 17
            Caption = 'Bei allen Artikel'
            TabOrder = 11
          end
          object RetVASCheckBox: TCheckBox
            Left = 8
            Top = 96
            Width = 510
            Height = 17
            Caption = 'Value Added Services m'#252'ssen erfasst werden'
            TabOrder = 4
          end
        end
        object TabSheet1: TTabSheet
          Caption = 'Kommissionieren'
          ImageIndex = 5
          object SelKommCheckBox: TCheckBox
            Left = 8
            Top = 16
            Width = 265
            Height = 17
            Caption = 'Kommissioinier muss ausgew'#228'hlt werden'
            TabOrder = 0
          end
          object SelKommResetCheckBox: TCheckBox
            Left = 8
            Top = 36
            Width = 475
            Height = 17
            Caption = 'Kommissioniereauswahl nach jedem Abschluss zur'#252'cksetzen'
            TabOrder = 1
          end
        end
        object PackTabSheet: TTabSheet
          Caption = 'Verpacken'
          ImageIndex = 5
          object VerteilPicCheckBox: TCheckBox
            Left = 8
            Top = 79
            Width = 433
            Height = 17
            AllowGrayed = True
            Caption = 'Artikelbilder im Verteilen'
            TabOrder = 2
          end
          object SelPackerCheckBox: TCheckBox
            Left = 8
            Top = 16
            Width = 265
            Height = 17
            Caption = 'Packer muss ausgew'#228'hlt werden'
            TabOrder = 0
          end
          object SelPackerResetCheckBox: TCheckBox
            Left = 8
            Top = 33
            Width = 475
            Height = 17
            Caption = 'Packerauswahl nach jedem Abschluss zur'#252'cksetzen'
            TabOrder = 1
          end
        end
        object InvTabSheet: TTabSheet
          Caption = 'Inventur'
          ImageIndex = 1
          object InvEinzelScanCheckBox: TCheckBox
            Left = 8
            Top = 16
            Width = 414
            Height = 17
            AllowGrayed = True
            Caption = 'Artikel einzeln scannen und z'#228'hlen'
            TabOrder = 0
          end
          object InvBesAbgleichOldMHDCheckBox: TCheckBox
            Left = 8
            Top = 56
            Width = 414
            Height = 17
            AllowGrayed = True
            Caption = 'Bestandsabgleich auch f'#252'r abgelaufene Ware'
            TabOrder = 2
          end
          object InvAssignPosCheckBox: TCheckBox
            Left = 8
            Top = 36
            Width = 414
            Height = 17
            AllowGrayed = True
            Caption = 'Von Platz zu Platz f'#252'hren'
            TabOrder = 1
          end
        end
        object TextTabSheet: TTabSheet
          Caption = 'Texte'
          ImageIndex = 8
          object Label23: TLabel
            Left = 8
            Top = 12
            Width = 156
            Height = 13
            Caption = 'BIO-Kontrollnr. der Niederlassung'
          end
          object BioKontrollNrEdit: TEdit
            Left = 8
            Top = 31
            Width = 121
            Height = 21
            MaxLength = 32
            TabOrder = 0
            Text = 'BioKontrollNrEdit'
          end
        end
      end
      object LoginGroupBox: TGroupBox
        Left = 8
        Top = 8
        Width = 755
        Height = 105
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Lizenzverwaltung'
        TabOrder = 0
        ExplicitWidth = 670
        object Label11: TLabel
          Left = 8
          Top = 24
          Width = 241
          Height = 13
          Caption = 'Max. zul'#228'ssige Sessions, leer=keine Beschr'#228'nkung'
        end
        object MaxSessionEdit: TEdit
          Left = 8
          Top = 40
          Width = 57
          Height = 21
          MaxLength = 4
          TabOrder = 0
          Text = 'MaxSessionEdit'
          OnKeyPress = MaxSessionEditKeyPress
        end
      end
    end
  end
end
