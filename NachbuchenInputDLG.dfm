object NachbuchenInputForm: TNachbuchenInputForm
  Left = 432
  Top = 188
  BorderStyle = bsDialog
  Caption = 'Gewicht nachbuchen'
  ClientHeight = 149
  ClientWidth = 224
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  DesignSize = (
    224
    149)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 33
    Height = 13
    Caption = 'Menge'
  end
  object Label2: TLabel
    Left = 8
    Top = 56
    Width = 39
    Height = 13
    Caption = 'Gewicht'
  end
  object Label3: TLabel
    Left = 136
    Top = 78
    Width = 12
    Height = 13
    Caption = 'kg'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 104
    Width = 209
    Height = 9
    Shape = bsTopLine
  end
  object OkButton: TButton
    Left = 52
    Top = 117
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 2
  end
  object AbortButton: TButton
    Left = 140
    Top = 117
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 3
  end
  object MengeEdit: TEdit
    Left = 8
    Top = 24
    Width = 121
    Height = 21
    TabOrder = 0
    Text = 'MengeEdit'
    OnKeyPress = MengeEditKeyPress
  end
  object GewichtEdit: TEdit
    Left = 8
    Top = 72
    Width = 121
    Height = 21
    TabOrder = 1
    Text = 'GewichtEdit'
    OnKeyPress = GewichtEditKeyPress
  end
end
