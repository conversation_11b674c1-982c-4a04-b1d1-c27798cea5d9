unit ChangeAufTextDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, StdCtrls, ExtCtrls;

type
  TChangeAufTextForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    ADOQuery1: TADOQuery;
    KopfPanel: TPanel;
    AufTextLabel: TLabel;
    Label3: TLabel;
    KundeNrLabel: TLabel;
    AufNrLabel: TLabel;
    Bevel1: TBevel;
    HintPanel: TPanel;
    Label1: TLabel;
    KommTextEdit: TEdit;
    WarningPanel: TPanel;
    Label2: TLabel;
    WarnTextEdit: TEdit;
    Bevel2: TBevel;
    PackPanel: TPanel;
    Label4: TLabel;
    PackTextEdit: TEdit;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
    fRefAuf     : Integer;
    fRefAufPos  : Integer;
    fRefKomm    : Integer;
    fRefRet     : Integer;
    fRefRetAvis : Integer;
    fRefAvisPos : Integer;
  public
    property RefAuf     : Integer read fRefAuf     write fRefAuf;
    property RefAufPos  : Integer read fRefAufPos  write fRefAufPos;
    property RefKomm    : Integer read fRefKomm    write fRefKomm;
    property RefRet     : Integer read fRefRet     write fRefRet;
    property RefRetAvis : Integer read fRefRetAvis write fRefRetAvis;
    property RefAvisPos : Integer read fRefAvisPos write fRefAvisPos;
  end;

implementation

{$R *.dfm}

uses
  DatenModul, LVSDatenInterface, SprachModul, FrontendMessages, ResourceText;

procedure TChangeAufTextForm.FormCloseQuery (Sender: TObject; var CanClose: Boolean);
var
  res : Integer;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    if (fRefAufPos > 0) then begin
      res := ChangeAuftragPosKommText (fRefAufPos, KommTextEdit.Text);

      if (res = 0) then
        CanClose := True
      else begin
        CanClose := False;

        FrontendMessages.MessageDLG (FormatMessageText (1576, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end;
    end else if (fRefAuf > 0) then begin
      res := ChangeAuftragKommText (fRefAuf, KommTextEdit.Text);

      if ((res = 0) and PackPanel.Visible) then
        res := ChangeAuftragPackText (fRefAuf, PackTextEdit.Text);

      if (res = 0) then
        CanClose := True
      else begin
        CanClose := False;

        FrontendMessages.MessageDLG (FormatMessageText (1576, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end;
    end else if (fRefKomm > 0) then begin
      res := ChangeKomKopfKommText (fRefKomm, KommTextEdit.Text);

      if (res = 0) then
        CanClose := True
      else begin
        CanClose := False;

        FrontendMessages.MessageDLG (FormatMessageText (1576, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end;
    end else if (fRefRet > 0) then begin
      res := ChangeRetoureHintText (fRefRet, KommTextEdit.Text);

      if (res = 0) then
        CanClose := True
      else begin
        CanClose := False;

        FrontendMessages.MessageDLG (FormatMessageText (1785, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end;
    end else if (fRefRetAvis > 0) then begin
      if WarningPanel.Visible then
        res := ChangeRetAvisHintText (fRefRetAvis, KommTextEdit.Text, WarnTextEdit.Text)
      else
        res := ChangeRetAvisHintText (fRefRetAvis, KommTextEdit.Text);

      if (res = 0) then
        CanClose := True
      else begin
        CanClose := False;

        FrontendMessages.MessageDLG (FormatMessageText (1785, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end;
    end else if (fRefAvisPos > 0) then begin
      res := ChangeRetAvisPosHintText (fRefAvisPos, KommTextEdit.Text);


      if (res = 0) then
        CanClose := True
      else begin
        CanClose := False;

        FrontendMessages.MessageDLG (FormatMessageText (1785, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end;
    end;
  end;
end;

procedure TChangeAufTextForm.FormCreate(Sender: TObject);
begin
  AufNrLabel.Caption   := '';
  KundeNrLabel.Caption := '';
  KommTextEdit.Text := '';
  WarnTextEdit.Text := '';
  
  fRefAuf     := -1;
  fRefRet     := -1;
  fRefAufPos  := -1;
  fRefKomm    := -1;
  fRefAvisPos := -1;
  fRefRetAvis := -1;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AufNrLabel);
    LVSSprachModul.SetNoTranslate (Self, KundeNrLabel);
  {$endif}
end;

procedure TChangeAufTextForm.FormShow(Sender: TObject);
begin
  if (fRefAufPos > 0) then begin
    PackPanel.Visible    := false;
    WarningPanel.Visible := false;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select auf.AUFTRAG_NR,auf.KUNDEN_NR,auf.KUNDEN_NAME,pos.KOMM_TEXT from V_AUFTRAG auf, V_AUFTRAG_POS pos where auf.REF=pos.REF_AUF_KOPF and pos.REF='+IntToStr (fRefAufPos));

    ADOQuery1.Open;

    AufNrLabel.Caption   := ADOQuery1.FieldByName ('AUFTRAG_NR').AsString;

    if (ADOQuery1.FieldByName ('KUNDEN_NR').IsNUll) then
      KundeNrLabel.Caption := ADOQuery1.FieldByName ('KUNDEN_NAME').AsString
    else
      KundeNrLabel.Caption := ADOQuery1.FieldByName ('KUNDEN_NR').AsString + ' : ' + ADOQuery1.FieldByName ('KUNDEN_NAME').AsString;

    KommTextEdit.Text    := ADOQuery1.FieldByName ('KOMM_TEXT').AsString;

    ADOQuery1.Close;
  end else if (fRefAuf > 0) then begin
    WarningPanel.Visible := false;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_AUFTRAG where REF='+IntToStr (fRefAuf));

    ADOQuery1.Open;

    AufNrLabel.Caption   := ADOQuery1.FieldByName ('AUFTRAG_NR').AsString;

    if (ADOQuery1.FieldByName ('KUNDEN_NR').IsNUll) then
      KundeNrLabel.Caption := ADOQuery1.FieldByName ('KUNDEN_NAME').AsString
    else
      KundeNrLabel.Caption := ADOQuery1.FieldByName ('KUNDEN_NR').AsString + ' : ' + ADOQuery1.FieldByName ('KUNDEN_NAME').AsString;

    KommTextEdit.Text    := ADOQuery1.FieldByName ('KOMM_TEXT').AsString;

    if not Assigned (ADOQuery1.FindField ('PACK_TEXT')) then
      PackPanel.Visible := false
    else
      PackTextEdit.Text := ADOQuery1.FieldByName ('PACK_TEXT').AsString;

    ADOQuery1.Close;
  end else if (fRefKomm > 0) then begin
    PackPanel.Visible    := false;
    WarningPanel.Visible := false;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_KOMM_KOPF where REF='+IntToStr (fRefKomm));

    ADOQuery1.Open;

    AufNrLabel.Caption   := ADOQuery1.FieldByName ('AUFTRAG_NR').AsString;
    KundeNrLabel.Caption := '';
    KommTextEdit.Text    := ADOQuery1.FieldByName ('KOMM_HINWEIS').AsString;

    ADOQuery1.Close;
  end else if (fRefRet > 0) then begin
    Caption := GetResourceText(1832);
    AufTextLabel.Caption := GetResourceText(1831);
    Label1.Caption := GetResourceText(1831);

    PackPanel.Visible    := false;
    WarningPanel.Visible := false;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_RETOUREN where REF='+IntToStr (fRefRet));

    ADOQuery1.Open;

    AufNrLabel.Caption   := ADOQuery1.FieldByName ('RETOUREN_NR').AsString;

    if (ADOQuery1.FieldByName ('KUNDEN_NR').IsNull) then
      KundeNrLabel.Caption := ADOQuery1.FieldByName ('KUNDEN_NAME').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_PLZ').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_ORT').AsString
    else
     KundeNrLabel.Caption := ADOQuery1.FieldByName ('KUNDEN_NR').AsString + ' : ' + ADOQuery1.FieldByName ('KUNDEN_NAME').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_PLZ').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_ORT').AsString;

    KommTextEdit.Text    := ADOQuery1.FieldByName ('HINWEIS').AsString;

    ADOQuery1.Close;
  end else if (fRefRetAvis > 0) then begin
    PackPanel.Visible    := false;

    Caption := GetResourceText(1832);
    AufTextLabel.Caption := GetResourceText(1830);
    Label1.Caption := GetResourceText(1831);
    Label2.Caption := GetResourceText(1833);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_PCD_RETOUREN_AVIS where REF='+IntToStr (fRefRetAvis));

    ADOQuery1.Open;

    AufNrLabel.Caption   := ADOQuery1.FieldByName ('RETOUREN_AVIS_NR').AsString;

    if (ADOQuery1.FieldByName ('KUNDEN_NR').IsNUll) then
      KundeNrLabel.Caption := ADOQuery1.FieldByName ('KUNDEN_NAME').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_PLZ').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_ORT').AsString
    else
     KundeNrLabel.Caption := ADOQuery1.FieldByName ('KUNDEN_NR').AsString + ' : ' + ADOQuery1.FieldByName ('KUNDEN_NAME').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_PLZ').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_ORT').AsString;

    if not Assigned (ADOQuery1.FindField('WARN_HINWEIS')) then
      WarningPanel.Visible := false
    else begin
      WarningPanel.Visible :=  true;
      WarnTextEdit.Text    := ADOQuery1.FieldByName ('WARN_HINWEIS').AsString;
    end;

    KommTextEdit.Text    := ADOQuery1.FieldByName ('HINWEIS').AsString;

    ADOQuery1.Close;
  end else if (RefAvisPos > 0) then begin
    PackPanel.Visible    := false;
    WarningPanel.Visible := false;

    Caption := GetResourceText(1832);
    AufTextLabel.Caption := GetResourceText(1830);
    Label1.Caption := GetResourceText(1831);
    Label2.Caption := GetResourceText(1833);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select avis.KUNDEN_NR,avis.RETOUREN_AVIS_NR,avis.KUNDEN_NAME,avis.KUNDEN_PLZ,avis.KUNDEN_ORT,pos.WARN_HINWEIS from V_RETOUREN_AVIS_POS pos, V_PCD_RETOUREN_AVIS avis where avis.REF=pos.REF_RETOUREN_AVIS and pos.REF='+IntToStr (RefAvisPos));

    ADOQuery1.Open;

    AufNrLabel.Caption   := ADOQuery1.FieldByName ('RETOUREN_AVIS_NR').AsString;

    if (ADOQuery1.FieldByName ('KUNDEN_NR').IsNUll) then
      KundeNrLabel.Caption := ADOQuery1.FieldByName ('KUNDEN_NAME').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_PLZ').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_ORT').AsString
    else
     KundeNrLabel.Caption := ADOQuery1.FieldByName ('KUNDEN_NR').AsString + ' : ' + ADOQuery1.FieldByName ('KUNDEN_NAME').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_PLZ').AsString+', '+ADOQuery1.FieldByName ('KUNDEN_ORT').AsString;

    KommTextEdit.Text    := ADOQuery1.FieldByName ('HINWEIS').AsString;

    ADOQuery1.Close;
  end;

  if not WarningPanel.Visible then
    Height := Height - WarningPanel.Height;

  if not PackPanel.Visible then
    Height := Height - PackPanel.Height;
end;

end.
