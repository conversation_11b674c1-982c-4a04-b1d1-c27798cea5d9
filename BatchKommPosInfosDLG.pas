unit BatchKommPosInfosDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TBatchKommPosInfoForm = class(TForm)
    CloseButton: TButton;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    Label5: TLabel;
    Label6: TLabel;
    AufNrLabel: TLabel;
    ArtikelLabel: TLabel;
    KommNrLabel: TLabel;
    KommLPLabel: TLabel;
    KommLELabel: TLabel;
    KommBenLabel: TLabel;
    Bevel1: TBevel;
    procedure FormShow(Sender: TObject);
  private
    fRefKommPos    : Integer;
    fRefAufKommPos : Integer;
  public
    property RefKommPos    : Integer read fRefKommPos    write fRefKommPos;
    property RefAufKommPos : Integer read fRefAufKommPos write fRefAufKommPos;
  end;

implementation

{$R *.dfm}

uses
  DB, ADODB, DatenModul;

procedure TBatchKommPosInfoForm.FormShow(Sender: TObject);
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Self);
  try
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select kk.KOMM_NR,kp.ARTIKEL_NR,kp.ARTIKEL_TEXT,ae.EAN,kp.AUFTRAG_NR,kp.LP_NR,kp.LP_KOOR,kp.LE_NR from V_KOMM_KOPF kk, V_KOMM_POS kp, VQ_ARTIKEL_EINHEIT ae where kk.REF=kp.REF_KOMM_KOPF and ae.REF=kp.REF_AR_EINHEIT and kp.REF='+IntToStr (RefKommPos));

    try
      query.Open;

      AufNrLabel.Caption   := query.FieldByName ('AUFTRAG_NR').AsString;
      ArtikelLabel.Caption := query.FieldByName ('ARTIKEL_TEXT').AsString + ' / ' + query.FieldByName ('ARTIKEL_NR').AsString;

      KommNrLabel.Caption   := query.FieldByName ('KOMM_NR').AsString;
      KommLPLabel.Caption   := query.FieldByName ('LP_KOOR').AsString;
      KommLELabel.Caption   := query.FieldByName ('LE_NR').AsString;

      query.Close;
    except
    end;

    query.SQL.Clear;
    query.SQL.Add ('select KOMM_BENUTZER from V_AUFTRAG_KOMM_POS where REF='+IntToStr (RefAufKommPos));

    try
      query.Open;

      KommBenLabel.Caption   := query.FieldByName ('KOMM_BENUTZER').AsString;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;
end;

end.
