object VASInputFrame: TVASInputFrame
  Left = 0
  Top = 0
  Width = 451
  Height = 96
  Align = alTop
  TabOrder = 0
  object CheckboxPanel: TPanel
    Left = 0
    Top = 0
    Width = 451
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    Visible = False
    ExplicitLeft = 128
    ExplicitTop = 64
    ExplicitWidth = 185
    DesignSize = (
      451
      41)
    object VASCheckBox: TCheckBox
      Left = 64
      Top = 8
      Width = 369
      Height = 17
      Anchors = [akLeft, akTop, akRight]
      Caption = 'VASCheckBox'
      TabOrder = 0
    end
  end
  object EditPanel: TPanel
    Left = 0
    Top = 41
    Width = 451
    Height = 48
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    Visible = False
    ExplicitTop = 39
    object VASEditLabel: TLabel
      Left = 64
      Top = 4
      Width = 68
      Height = 15
      Caption = 'VASEditLabel'
    end
    object VASQuantityLabel: TLabel
      Left = 263
      Top = 4
      Width = 94
      Height = 15
      Caption = 'VASQuantityLabel'
    end
    object VASEdit: TEdit
      Left = 64
      Top = 21
      Width = 193
      Height = 23
      MaxLength = 64
      TabOrder = 0
      Text = 'VasEdit'
    end
    object VASQuantityEdit: TEdit
      Left = 263
      Top = 21
      Width = 82
      Height = 23
      MaxLength = 9
      TabOrder = 1
      Text = '0'
    end
    object VASQuantityUpDown: TIntegerUpDown
      Left = 345
      Top = 21
      Width = 16
      Height = 23
      Associate = VASQuantityEdit
      TabOrder = 2
    end
  end
end
