object ShowBestandResForm: TShowBestandResForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Reservierungen'
  ClientHeight = 332
  ClientWidth = 590
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    590
    332)
  PixelsPerInch = 96
  TextHeight = 13
  object BestandResDBGrid: TDBGridPro
    Left = 8
    Top = 16
    Width = 574
    Height = 273
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = BestandResDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = BestandResDBGridPopupMenu
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 507
    Top = 299
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 1
    ExplicitLeft = 552
    ExplicitTop = 267
  end
  object BestandResDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 464
    Top = 56
  end
  object BestandResDataSource: TDataSource
    DataSet = BestandResDataSet
    Left = 512
    Top = 56
  end
  object BestandResDBGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = BestandResDBGridPopupMenuPopup
    Left = 200
    Top = 112
    object DelBesResMenuItem: TMenuItem
      Caption = 'Reservierung l'#246'schen...'
      OnClick = DelBesResMenuItemClick
    end
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 80
    Top = 72
  end
end
