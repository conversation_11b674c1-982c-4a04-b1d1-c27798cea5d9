unit VPEDisplayDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, Grids, DBGrids, SMDBGrid, DBGridPro, ExtCtrls,
  StdCtrls, ConfigModul, ComboBoxPro;

type
  TVEPDisplayForm = class(TForm)
    VPEDBGrid: TDBGridPro;
    DataSource1: TDataSource;
    ADOQuery1: TADOQuery;
    Button1: TButton;
    Button2: TButton;
    Button3: TButton;
    Button4: TButton;
    Bevel1: TBevel;
    Label1: TLabel;
    MandantComboBox: TComboBoxPro;
    Label2: TLabel;
    Bevel2: TBevel;
    procedure FormShow(Sender: TObject);
    procedure VPEDBGridDblClick(Sender: TObject);
    procedure Button3Click(Sender: TObject);
    procedure Button2Click(Sender: TObject);
    procedure MandantComboBoxChange(Sender: TObject);
    procedure ADOQuery1AfterClose(DataSet: TDataSet);
    procedure DataSource1DataChange(Sender: TObject; Field: TField);
    procedure FormCreate(Sender: TObject);
  private
    fMandantConfig : TMandantConfig;

    procedure UpdateQuery;
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses VCLUtilitys, DatenModul, EditVPEDLG, FrontendUtils, DBGridUtilModule,
  SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.09.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TVEPDisplayForm.FormCreate(Sender: TObject);
begin
  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, MandantComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TVEPDisplayForm.FormShow(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  LoadMandantCombobox(MandantComboBox);
  if (LVSDatenModul.AktMandantRef = -1) Then
    MandantComboBox.ItemIndex := 0
  else MandantComboBox.ItemIndex := FindComboboxRef (MandantComboBox, LVSDatenModul.AktMandantRef);

  MandantComboBox.OnChange (MandantComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TVEPDisplayForm.UpdateQuery;
begin
  ADOQuery1.Close;
  ADOQuery1.SQL.Clear;

  ADOQuery1.SQL.Add ('select * from V_ARTIKEL_VPE');

  if (GetComboBoxRef (MandantComboBox) = -1) then
    ADOQuery1.SQL.Add ('where REF_MAND in (select REF from V_PCD_MANDANT)')
  else ADOQuery1.SQL.Add ('where REF_MAND='+IntToStr (GetComboboxRef (MandantComboBox)));

  try
    ADOQuery1.Open;

    VPEDBGrid.SetColumnVisible ('REF', False);
  except
  end;

end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TVEPDisplayForm.VPEDBGridDblClick(Sender: TObject);
var
  editform : TEditVPEForm;
begin
  if (ADOQuery1.Active) and (ADOQuery1.RecordCount > 0) then begin
    editform := TEditVPEForm.Create (Self);

    try
      editform.Caption := GetResourceText (1393);

      editform.Referenz := ADOQuery1.FieldByName ('REF').AsInteger;

      if (editform.ShowModal = mrOk) Then begin
        VPEDBGrid.Reload;
      end;
    finally
      editform.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TVEPDisplayForm.Button3Click(Sender: TObject);
begin
  VPEDBGridDblClick (Sender);
end;

procedure TVEPDisplayForm.DataSource1DataChange(Sender: TObject; Field: TField);
begin
  Button3.Enabled := (ADOQuery1.Active) and (ADOQuery1.RecordCount > 0);
  Button4.Enabled := (ADOQuery1.Active) and (ADOQuery1.RecordCount > 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TVEPDisplayForm.Button2Click(Sender: TObject);
var
  editform : TEditVPEForm;
begin
  editform := TEditVPEForm.Create (Self);

  try
    editform.Caption := GetResourceText (1392);

    editform.Referenz := -1;

    if (editform.ShowModal = mrOk) Then begin
      VPEDBGrid.Reload;
    end;
  finally
    editform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TVEPDisplayForm.MandantComboBoxChange(Sender: TObject);
begin
  if (GetComboBoxRef (MandantComboBox) = -1) then begin
    fMandantConfig.RefMandant := -1;

    Button2.Enabled := false;
  end else begin
    LVSConfigModul.ReadMandantConfig (MandantComboBox.GetItemText, fMandantConfig);

    Button2.Enabled := fMandantConfig.FlagChangeArtikelStammdaten;
  end;

  UpdateQuery;
end;

procedure TVEPDisplayForm.ADOQuery1AfterClose(DataSet: TDataSet);
begin
  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);
end;

end.
