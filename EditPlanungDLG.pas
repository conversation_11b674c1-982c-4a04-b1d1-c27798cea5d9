unit EditPlanungDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComCtrls, ComboBoxPro, ExtCtrls;

type
  TEditPlanungForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    ArtComboBox: TComboBoxPro;
    NameEdit: TEdit;
    DescEdit: TEdit;
    ValideDateTimePicker: TDateTimePicker;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    MandantComboBox: TComboBoxPro;
    Label5: TLabel;
    Label6: TLabel;
    LagerComboBox: TComboBoxPro;
    Bevel1: TBevel;
    Bevel2: TBevel;
    Bevel3: TBevel;
    Label7: TLabel;
    VorgangComboBox: TComboBoxPro;
    Bevel4: TBevel;
    Label8: TLabel;
    BereichComboBox: TComboBoxPro;
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure LagerComboBoxChange(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses VCLUtilitys, FrontendUtils, CommCtrl, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditPlanungForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  systime : TSystemTime;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    if  ValideDateTimePicker.Checked and not (DateTime_GetSystemTime (ValideDateTimePicker.Handle, systime) = GDT_VALID) then
      ValideDateTimePicker.Checked := False;

    CanClose := False;

    if LagerComboBox.Enabled and (LagerComboBox.ItemIndex = -1) then
      LagerComboBox.SetFocus
    else if (Length (NameEdit.Text) = 0) then
      NameEdit.SetFocus
    else if ArtComboBox.Enabled and (ArtComboBox.ItemIndex = -1) then
      ArtComboBox.SetFocus
    else
      CanClose := True
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditPlanungForm.FormCreate(Sender: TObject);
begin
  NameEdit.Text := '';
  DescEdit.Text := '';
  
  ValideDateTimePicker.DateTime := Now + 1;
  ValideDateTimePicker.Checked  := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditPlanungForm.LagerComboBoxChange(Sender: TObject);
begin
  LoadLBComboboxNullEntry (BereichComboBox, '', GetResourceText (rsComboboxAlle), GetComboBoxRef(LagerComboBox));

  BereichComboBox.Items.Insert (0, '');
  BereichComboBox.ItemIndex := 0;
end;

end.
