﻿//******************************************************************************
//* Modul Name: StringUtils
//* Author    : <PERSON> Graf
//******************************************************************************
//* $Revision: 13 $
//* $Date: 14.08.23 23:08 $
//* $Id: StringUtils.pas,v 1.4 2003/08/10 13:46:15 graf Exp graf $
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
unit StringUtils;

interface
uses
  Windows, Classes;

type
  TTrennzeichen = set of char;

function FormatIntToStr (const Wert,Stellen : Integer) : String;
function FormatStr      (const TextStr : String; const Stellen : Integer; const Zeichen : Char = ' ') : String; overload;
function TickToStr      (const Ticks : DWORD) : String;

function GetParameter    (const ParamLine : String; var strpos : Integer; const TrennChar : Char) : String; overload;
function GetParameterInt (const ParamLine : String; var strpos : Integer; const TrennChar : Char) : Integer; overload;

function StripString         (const Line : String) : String;
function TrailingStripString (const Line : String) : String;
function TrimLeadingZeros    (const Line: string): string;
function StripLeadingZeros   (const Line: string): string;

function StrConvertControlChars (line : String) : String;
function StrRemoveControlChars  (line : String) : String;

function BuildStringSet (const line : String; const TrennChars : TTrennzeichen = [',',';',':',' ']) : String;

function AddBackSlash (const PathName : String) : String;

function GetNumParam (const ParamName, ParamLine : String; var IntWert : Integer; const DefaultWert : Integer) : Boolean;

function MemoryStreamToString (stream: TMemoryStream): string;

function UTFToAnsiString (const Line : String) : AnsiString;
function StringToUTF  (const Line : AnsiString) : String;

function GetCP850AnsiChar (const Code : Byte) : AnsiChar;

implementation

uses SysUtils;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TickToStr (const Ticks : DWORD) : String;
var line : String;
begin
  line := FormatIntToStr (Ticks mod 60, 2);
  line := FormatIntToStr ((Ticks div 60) mod 60, 2) + ':' + line;
  line := FormatIntToStr ((Ticks div 3600) mod 24, 2) + ':' + line;

  TickToStr := line;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FormatStr (const TextStr : String; const Stellen : Integer; const Zeichen : Char) : String;
var line : String;
begin
  if (Stellen < 0) Then begin
    line := '';
    while (Length (line) < ((Stellen * -1) - Length (TextStr))) do line := line + Zeichen;
    line := line + TextStr;
  end else begin
    line := TextStr;
    while (Length (line) < Stellen) do line := line + Zeichen;
  end;

  FormatStr := line;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FormatIntToStr (const Wert,Stellen : Integer) : String;
var line : String;
begin
  line := IntToStr (Wert);
  while (Length (line) < Stellen) do line := '0' + line;

  FormatIntToStr := line;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetParameter (const ParamLine : String; var strpos : Integer; const TrennChar : Char) : String;
var line : String;
begin
  line := '';

  if (strpos = 0) Then strpos := 1;

  while (strpos <= Length (ParamLine)) and (ParamLine [strpos] <> TrennChar) do begin
    line := line + ParamLine [strpos];
    Inc (strpos);
  end;

  if (strpos <= Length (ParamLine)) and (ParamLine [strpos] = TrennChar) then
    Inc (strpos);

  GetParameter := line;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetParameterInt (const ParamLine : String; var strpos : Integer; const TrennChar : Char) : Integer;
var wert : Integer;
begin
  wert := 0;

  if (strpos = 0) Then strpos := 1;

  while (strpos <= Length (ParamLine)) and (ParamLine [strpos] <> TrennChar) do begin
    if ((ParamLine [strpos] >= '0') and (ParamLine [strpos] <= '9')) then
      wert := wert + (Ord (ParamLine [strpos]) - Ord ('0'));

    Inc (strpos);
  end;

  if (strpos <= Length (ParamLine)) and (ParamLine [strpos] = TrennChar) then
    Inc (strpos);

  GetParameterInt := wert;
end;


//******************************************************************************
//* Function Name: StripString
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Schneidet alle überflüssigen Blanks am Ende der Zeile ab
//******************************************************************************
//* Return Value :
//******************************************************************************
function StripString (const Line : String) : String;
var
  i      : Integer;
  stop   : Boolean;
begin
  i:= Length (Line);
  stop := false;

  while (i > 0) and not (stop) do begin
    if (Line [i] <> ' ') then
      stop := true
    else Dec (i);
  end;

  StripString := Copy (Line, 1, i);
end;

//******************************************************************************
//* Function Name: TrailingStripString
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Schneidet alle überflüssigen Blanks am Anfang der Zeile ab
//******************************************************************************
//* Return Value :
//******************************************************************************
function TrailingStripString (const Line : String) : String;
var
  i      : Integer;
  stop   : Boolean;
begin
  i:= 1;
  stop := false;

  while (i <= Length (Line)) and not (stop) do begin
    if (Line [i] <> ' ') then
      stop := true
    else Inc (i);
  end;

  TrailingStripString := Copy (Line, i, Length (Line) - i + 1);
end;

//******************************************************************************
//* Function Name: StripString
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Schneidet alle überflüssigen Blanks am Ende der Zeile ab
//******************************************************************************
//* Return Value :
//******************************************************************************
function StrConvertControlChars (line : String) : String;
var
  idx    : Integer;
  outstr : String;
begin
  outstr := '';
  idx := 1;

  while (idx <= Length (Line)) do begin
    if (Line [idx] <> '^') Then
      outstr := outstr + Line [idx]
    else begin
      Inc (idx);

      if (idx <= Length (Line)) then
        outstr := outstr + chr (Ord (upcase (Line [idx])) - Ord ('@'));
    end;

    Inc (idx);
  end;

  Result := outstr;
end;

//******************************************************************************
//* Function Name: StripString
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Schneidet alle überflüssigen Blanks am Ende der Zeile ab
//******************************************************************************
//* Return Value :
//******************************************************************************
function StrRemoveControlChars (line : String) : String;
var
  idx    : Integer;
  outstr : String;
begin
  outstr := '';
  idx := 1;

  while (idx <= Length (Line)) do begin
    if (Line [idx] >= ' ') Then
      outstr := outstr + Line [idx];

    Inc (idx);
  end;

  Result := outstr;
end;

//******************************************************************************
//* Function Name: StripString
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Schneidet alle überflüssigen Blanks am Ende der Zeile ab
//******************************************************************************
//* Return Value :
//******************************************************************************
function BuildStringSet (const line : String; const TrennChars : TTrennzeichen) :String;
var
  strpos : Integer;
  outstr : String;
begin
  strpos := 1;

  if (Length (line) = 0) then
    outstr := ''
  else begin
    outstr := '''';
    while (strpos <= Length (line)) do begin
      if (line [strpos] in TrennChars) then begin
        outstr := outstr + '''' + line [strpos] + '''';
      end else begin
        outstr := outstr + line [strpos];
      end;

      Inc (strpos);
    end;

    outstr := outstr + '''';
  end;

  Result := outstr;
end;

//******************************************************************************
//* Function Name: AddBackSlash
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Hänge ggf. an den Verzeichnisnamen eine \ an
//******************************************************************************
//* Return Value : Verzeichnisnamen mit \
//******************************************************************************
function AddBackSlash (const PathName : String) : String;
begin
  if (Length (PathName) = 0) then
    Result := ''
  else if (PathName [Length (PathName)] <> '\') then
    Result := PathName + '\'
  else Result := PathName;
end;


//******************************************************************************
//* Function Name: GetNumParam
//* Author       : Stefan Graf
//* Datum        : 18.09.2014
//******************************************************************************
//* Description  : Sucht in <ParamLine> nach dem Parameter <ParamName> und liefert
//*                den Wert dahinter zurück
//******************************************************************************
//* Return Value : Wert hinter <ParamName>
//******************************************************************************
function GetNumParam (const ParamName, ParamLine : String; var IntWert : Integer; const DefaultWert : Integer) : Boolean;
var
  strpos : Integer;
  numstr : String;
begin
  Result := False;
  IntWert := DefaultWert;

  strpos := Pos (ParamName, ParamLine);

  if (strpos > 0) then begin
    numstr := '';
    strpos := strpos + Length (ParamName);

    while ((strpos <= Length (ParamLine)) and (ParamLine [strpos] in ['0'..'9'])) do begin
      numstr := numstr +  ParamLine [strpos];

      Inc (strpos);
    end;

    if (Length (numstr) > 0) then begin
      if TryStrToInt (numstr, IntWert) then
        Result := True;
    end;
  end;
end;

//******************************************************************************
//* Function Name: TrimLeadingZeros
//* Author       : Stefan Graf
//* Datum        : 20.01.2020
//******************************************************************************
//* Description  : Führende Nullen abschneiden
//******************************************************************************
//* Return Value : String ohne führende Nullen
//******************************************************************************
function StripLeadingZeros (const Line: string): string; inline;
begin
  Result := TrimLeadingZeros (Line);
end;

function TrimLeadingZeros(const Line: string): string;
var
  i, l: Integer;
begin
  l:= Length(Line);
  i:= 1;
  while (i < l) and (Line[i] = '0') do Inc(i);
  Result:= Copy(Line, i);
end;

//******************************************************************************
//* Function Name: MemoryStreamToString
//* Author       : Stefan Graf
//* Datum        : 26.01.2023
//******************************************************************************
//* Description  : Inhalt eines TMemoryStream in einen String kopieren
//******************************************************************************
//* Return Value : Inhalt des TMemoryStream
//******************************************************************************
function MemoryStreamToString (stream: TMemoryStream): string;
begin
  SetString(Result, PChar(stream.Memory), stream.Size div SizeOf(Char));
end;

function UTFToAnsiString (const Line : String) : AnsiString;
var
  i      : Integer;
  outstr : AnsiString;
begin
  outstr := '';

  i := 1;

  while (i <= Length (Line)) do begin
    if (Ord (Line [i]) < $80) then //ACII
      outstr := outstr + Line [i]
    else if ((Ord (Line [i]) and $F8) = $F0) then begin
      if not (((i + 3) <= Length (Line)) and ((Ord (Line [i+1]) and $80) = $80) and ((Ord (Line [i+2]) and $80) = $80) and ((Ord (Line [i+3]) and $80) = $80)) then
        outstr := outstr + Chr ($C0 or (Ord (Line [i]) and $C0) shr 6) + Chr ($80 or (Ord (Line [i]) and $3f))
      else begin
        outstr := outstr + Line [i] + Line [i + 1] + Line [i + 2] + Line [i + 3];
        Inc (i, 3);
      end;
    end else if ((Ord (Line [i]) and $F0) = $E0) then begin
      if not (((i + 2) <= Length (Line)) and ((Ord (Line [i+1]) and $80) = $80) and ((Ord (Line [i+2]) and $80) = $80)) then
        outstr := outstr + Chr ($C0 or (Ord (Line [i]) and $C0) shr 6) + Chr ($80 or (Ord (Line [i]) and $3f))
      else begin
        outstr := outstr + Line [i] + Line [i + 1] + Line [i + 2];
        Inc (i, 2);
      end;
    end else if ((Ord (Line [i]) and $C0) = $C0) then begin
      if not (((i + 1) <= Length (Line)) and ((Ord (Line [i+1]) and $80) = $80)) then begin
        outstr := outstr + Line [i];
      end else begin
        if (Ord (Line [i]) = $C2) then
          outstr := outstr + Line [i + 1]
        else if (Ord (Line [i]) = $C3) then begin
          outstr := outstr + Chr (Ord (Line [i + 1]) + $40);
        end else if (Ord (Line [i]) = $C4) then begin
          if (Ord (Line [i + 1]) = $88) then
            outstr := outstr + 'C'
          else if (Ord (Line [i + 1]) = $8A) then
            outstr := outstr + 'C'
          else if (Ord (Line [i + 1]) = $8C) then
            outstr := outstr + 'C'
          else if (Ord (Line [i + 1]) = $8E) then
            outstr := outstr + 'D'
          else if (Ord (Line [i + 1]) = $90) then
            outstr := outstr + 'D'
          else if (Ord (Line [i + 1]) = $91) then
            outstr := outstr + 'd'
          else if (Ord (Line [i + 1]) = $93) then
            outstr := outstr + 'e'
          else if (Ord (Line [i + 1]) = $95) then
            outstr := outstr + 'e'
          else if (Ord (Line [i + 1]) = $97) then
            outstr := outstr + 'e'
          else if (Ord (Line [i + 1]) = $99) then
            outstr := outstr + 'e'
          else if (Ord (Line [i + 1]) = $9B) then
            outstr := outstr + 'e'
          else if (Ord (Line [i + 1]) = $8D) then
            outstr := outstr + 'c'
          else if (Ord (Line [i + 1]) = $8F) then
            outstr := outstr + 'd'
          else
            outstr := outstr + '?';
        end else if (Ord (Line [i]) = $C5) then begin
          if (Ord (Line [i + 1]) = $99) then
            outstr := outstr + 'r'
          else if (Ord (Line [i + 1]) = $88) then
            outstr := outstr + 'n'
          else if (Ord (Line [i + 1]) = $89) then
            outstr := outstr + 'n'
          else if (Ord (Line [i + 1]) = $94) then
            outstr := outstr + 'R'
          else if (Ord (Line [i + 1]) = $95) then
            outstr := outstr + 'r'
          else if (Ord (Line [i + 1]) = $96) then
            outstr := outstr + 'R'
          else if (Ord (Line [i + 1]) = $97) then
            outstr := outstr + 'r'
          else if (Ord (Line [i + 1]) = $98) then
            outstr := outstr + 'R'
          else if (Ord (Line [i + 1]) = $99) then
            outstr := outstr + 'r'
          else if (Ord (Line [i + 1]) = $9A) then
            outstr := outstr + 'S'
          else if (Ord (Line [i + 1]) = $9b) then
            outstr := outstr + 's'
          else if (Ord (Line [i + 1]) = $9c) then
            outstr := outstr + 'S'
          else if (Ord (Line [i + 1]) = $9e) then
            outstr := outstr + 'S'
          else if (Ord (Line [i + 1]) = $9f) then
            outstr := outstr + 's'
          else if (Ord (Line [i + 1]) = $A0) then
            outstr := outstr + 'S'
          else if (Ord (Line [i + 1]) = $A1) then
            outstr := outstr + 's'
          else if (Ord (Line [i + 1]) = $A2) then
            outstr := outstr + 'T'
          else if (Ord (Line [i + 1]) = $A3) then
            outstr := outstr + 't'
          else if (Ord (Line [i + 1]) = $A4) then
            outstr := outstr + 'T'
          else if (Ord (Line [i + 1]) = $A5) then
            outstr := outstr + 't'
          else if (Ord (Line [i + 1]) = $A6) then
            outstr := outstr + 'T'
          else if (Ord (Line [i + 1]) = $A7) then
            outstr := outstr + 't'
          else if (Ord (Line [i + 1]) = $AD) then
            outstr := outstr + 'u'
          else if (Ord (Line [i + 1]) = $AF) then
            outstr := outstr + 'u'
          else if (Ord (Line [i + 1]) = $B6) then
            outstr := outstr + 'Y'
          else if (Ord (Line [i + 1]) = $B7) then
            outstr := outstr + 'y'
          else if (Ord (Line [i + 1]) = $B8) then
            outstr := outstr + 'Y'
          else if (Ord (Line [i + 1]) = $B9) then
            outstr := outstr + 'Z'
          else if (Ord (Line [i + 1]) = $BA) then
            outstr := outstr + 'z'
          else if (Ord (Line [i + 1]) = $BB) then
            outstr := outstr + 'Z'
          else if (Ord (Line [i + 1]) = $BC) then
            outstr := outstr + 'z'
          else if (Ord (Line [i + 1]) = $BD) then
            outstr := outstr + 'Z'
          else if (Ord (Line [i + 1]) = $BE) then
            outstr := outstr + 'z'
          else
            outstr := outstr + '§';
        end;

        Inc (i);
      end;
    end;

    Inc (i);
  end;

  UTFToAnsiString := outstr;
end;

//******************************************************************************
//* Function Name: StringToUTF
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Der String wird passen für UTF-8 konvertiert
//*                Dabei werden Ansi-Umlaute korrekt erkannt und umgesetzt und
//*                UTF8 Umlaute bleiben bestehen
//*
//*                !! Bei aufeinanderfolgend Ansi-Zeichen kommt es aber zu Fehlern!!
//******************************************************************************
//* Return Value :
//******************************************************************************
function StringToUTF (const Line : AnsiString) : String;
var
  i      : Integer;
  outstr : String;
begin
  outstr := '';

  i := 1;

  while (i <= Length (Line)) do begin
    if (Ord (Line [i]) < $80) then //ACII
      outstr := outstr + Line [i]
    else if ((Ord (Line [i]) and $F8) = $F0) then begin
      if not (((i + 3) <= Length (Line)) and ((Ord (Line [i+1]) and $80) = $80) and ((Ord (Line [i+2]) and $80) = $80) and ((Ord (Line [i+3]) and $80) = $80)) then
        outstr := outstr + Chr ($C0 or (Ord (Line [i]) and $C0) shr 6) + Chr ($80 or (Ord (Line [i]) and $3f))
      else begin
        outstr := outstr + Line [i] + Line [i + 1] + Line [i + 2] + Line [i + 3];
        Inc (i, 3);
      end;
    end else if ((Ord (Line [i]) and $F0) = $E0) then begin
      if not (((i + 2) <= Length (Line)) and ((Ord (Line [i+1]) and $80) = $80) and ((Ord (Line [i+2]) and $80) = $80)) then
        outstr := outstr + Chr ($C0 or (Ord (Line [i]) and $C0) shr 6) + Chr ($80 or (Ord (Line [i]) and $3f))
      else begin
        outstr := outstr + Line [i] + Line [i + 1] + Line [i + 2];
        Inc (i, 2);
      end;
    end else if ((Ord (Line [i]) and $C0) = $C0) then begin
      if not (((i + 1) <= Length (Line)) and ((Ord (Line [i+1]) and $80) = $80)) then
        outstr := outstr + Chr ($C0 or (Ord (Line [i]) and $C0) shr 6) + Chr ($80 or (Ord (Line [i]) and $3f))
      else begin
        outstr := outstr + Line [i] + Line [i + 1];
        Inc (i);
      end;
    end;

    Inc (i);
  end;

  StringToUTF := outstr;
end;

function GetCP850AnsiChar (const Code : Byte) : AnsiChar;
var
  ch : AnsiChar;
begin
  case Code of
    128 : ch := 'Ç';
    129 : ch := 'ü';
    130 : ch := 'é';
    131 : ch := 'â';
    132 : ch := 'ä';
    133 : ch := 'à';
    134 : ch := 'å';
    135 : ch := 'ç';
    136 : ch := 'ê';
    137 : ch := 'ë';
    138 : ch := 'è';
    139 : ch := 'ï';
    140 : ch := 'î';
    141 : ch := 'ì';
    142 : ch := 'Ä';
    143 : ch := 'Å';
    144 : ch := 'É';
    145 : ch := 'æ';
    146 : ch := 'Æ';
    147 : ch := 'ô';
    148 : ch := 'ö';
    149 : ch := 'ò';
    150 : ch := 'û';
    151 : ch := 'ù';
    152 : ch := 'ÿ';
    153 : ch := 'Ö';
    154 : ch := 'Ü';
    155 : ch := 'ø';
    156 : ch := '£';
    157 : ch := 'Ø';
    158 : ch := '×';
    159 : ch := 'ƒ';
    160 : ch := 'á';
    161 : ch := 'í';
    162 : ch := 'ó';
    163 : ch := 'ú';
    164 : ch := 'ñ';
    165 : ch := 'Ñ';
    166 : ch := 'ª';
    167 : ch := 'º';
    168 : ch := '¿';
    169 : ch := '®';
    170 : ch := '¬';
    171 : ch := '½';
    172 : ch := '¼';
    173 : ch := '¡';
    174 : ch := '«';
    175 : ch := '»';
    176 : ch := '░';
    177 : ch := '▒';
    178 : ch := '▓';
    179 : ch := '│';
    180 : ch := '┤';
    181 : ch := 'Á';
    182 : ch := 'Â';
    183 : ch := 'À';
    184 : ch := '©';
    189 : ch := '¢';
    190 : ch := '¥';
    191 : ch := '┐';
    192 : ch := '└';
    193 : ch := '┴';
    194 : ch := '┬';
    195 : ch := '├';
    196 : ch := '─';
    197 : ch := '┼';
    198 : ch := 'ã';
    199 : ch := 'Ã';
    208 : ch := 'ð';
    209 : ch := 'Ð';
    210 : ch := 'Ê';
    211 : ch := 'Ë';
    212 : ch := 'È';
    213 : ch := 'ı';
    214 : ch := 'Í';
    215 : ch := 'Î';
    216 : ch := 'Ï';
    217 : ch := '┘';
    218 : ch := '┌';
    219 : ch := '█';
    220 : ch := '▄';
    221 : ch := '¦';
    222 : ch := 'Ì';
    223 : ch := '▀';
    224 : ch := 'Ó';
    225 : ch := 'ß';
    226 : ch := 'Ô';
    227 : ch := 'Ò';
    228 : ch := 'õ';
    229 : ch := 'Õ';
    230 : ch := 'µ';
    231 : ch := 'þ';
    232 : ch := 'Þ';
    233 : ch := 'Ú';
    234 : ch := 'Û';
    235 : ch := 'Ù';
    236 : ch := 'ý';
    237 : ch := 'Ý';
    238 : ch := '¯';
    239 : ch := '´';
    241 : ch := '±';
    242 : ch := '‗';
    243 : ch := '¾';
    244 : ch := '¶';
    245 : ch := '§';
    246 : ch := '÷';
    247 : ch := '¸';
    248 : ch := '°';
    250 : ch := '¨';
    251 : ch := '¹';
    252 : ch := '³';
    253 : ch := '²';
    254 : ch := '■';
    else
      ch := '?';
  end;

  Result := ch;
end;

end.
