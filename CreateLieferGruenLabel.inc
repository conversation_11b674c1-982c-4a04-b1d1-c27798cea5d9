﻿  //******************************************************************************
  //* Function Name: CreateFairSendenLabel
  //* Author       : <PERSON>
  //* Datum        : 20.02.2023
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateLieferGruenLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    idx,
    res,
    keyidx,
    errcode  : Integer;
    errtext  : String;
    xmlstr   : String;
    urlparam : String;
    body     : String;
    resp     : String;
    hdr      : String;
    nvestr   : String;
    labelurl : String;
    nrstr    : String;
    orderid  : String;
    timestr  : String;
    keystr,
    streetstr,
    vorstr,
    nachstr  : String;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    js       : TlkJSONobject;
    fs,
    us,
    errfs    : TlkJSONbase;
    cfgquery : TSmartQuery;
    olddec   : Char;
    found,
    testflag : boolean;
    urlstr,
    deftelstr,
    defmailstr,
    authurlstr,
    apiurlstr,
    prodstr,
    idstr,
    cfgstr,
    userid,
    usersec   : String;
  begin
    res := 0;

    LabelImage.Clear;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      ErrorText := '';

      urlstr  := '';
      testflag := false;

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select cfg.REST_URL as SPED_REST_URL, g.* from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else begin
          keystr     := cfgquery.FieldByName('API_KEY').AsString;
          prodstr    := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;
          deftelstr  := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;
          cfgstr     := cfgquery.FieldByName('SENDIT_CONFIG').AsString;
          defmailstr := cfgquery.FieldByName('DEFAULT_EMAIL_ADRESS').AsString;

          if Assigned (cfgquery.FindField ('OPT_TEST')) then
            testflag := cfgquery.FieldByName('OPT_TEST').AsString > '0';

          if Assigned (cfgquery.FindField ('REST_URL')) then
            urlstr  := cfgquery.FieldByName('REST_URL').AsString;

          if (Length (urlstr) = 0) then
            urlstr  := cfgquery.FieldByName('SPED_REST_URL').AsString;

          if (Length (urlstr) = 0) then
            ErrorText := 'Not REST url defined';
        end;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        sdata := TMemoryStream.Create;

        try
          hdr := 'Authorization: Bearer '+keystr;

          nvestr := query.FieldByName('NVE_NR').AsString;

          if (query.FieldByName('COUNT_FREIGABE').AsInteger > 1) then
            orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('COUNT_FREIGABE').AsString
          else if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
            orderid := query.FieldByName('AUFTRAG_NR').AsString
          else
            orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;

          if Assigned (SendITLog) then begin
            SendITLog.Logging (clNormal, 'Versand: LieferGruen, RefNVE: '+query.FieldByName('REF_NVE').AsString+', OrderID: '+orderid+', NVE: '+nvestr+CR+LF);
          end;

          urlparam := '';
          body := '';

          if testflag then
            body := body + '"test": true';

          nrstr  := '';
          streetstr := liefquery.FieldByName ('STRASSE').AsString;

          idx := Length (streetstr);
          while (idx > 1) and (streetstr [idx] <> ' ') do
            Dec (idx);

          if (idx > 1) then begin
            nrstr := copy (streetstr, idx + 1);
            streetstr := Copy (streetstr, 1, idx - 1);
          end;

          if not (liefquery.FieldByName('COMPANY').IsNull) then begin
            if (Length (body) > 0) then body := body + ',';
            body := body + '"dropoff_company": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('COMPANY').AsString, ';', ',', [rfReplaceAll]))+'"';
          end else if not (liefquery.FieldByName('NAME2').IsNull) then begin
            if (Length (body) > 0) then body := body + ',';
            body := body + '"dropoff_company": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('NAME2').AsString, ';', ',', [rfReplaceAll]))+'"';
          end;

          if (kepemail) and not adrquery.FieldByName('EMAIL').IsNull then begin
            if (Length (body) > 0) then body := body + ',';
            body := body + '"dropoff_email": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll]))+'"'
          end else if (Length (defmailstr) > 0) then begin
            if (Length (body) > 0) then body := body + ',';
            body := body + '"dropoff_email": "'+ConvertJSONSonderzeichen (StringReplace (defmailstr, ';', ',', [rfReplaceAll]))+'"'
          end;

          if not (adrquery.FieldByName('TELEFON').IsNull) then begin
            if (Length (body) > 0) then body := body + ',';
            body := body + '  "dropoff_phone": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll]))+'"';
          end else if (Length (deftelstr) > 0) then begin
            if (Length (body) > 0) then body := body + ',';
            body := body + '"dropoff_phone": "'+ConvertJSONSonderzeichen (StringReplace (deftelstr, ';', ',', [rfReplaceAll]))+'"'
          end;

          ExtractVorUndNachname (vorstr, nachstr);

          if (Length (body) > 0) then body := body + ',';
          body := body + '"dropoff_firstname": "'+ConvertJSONSonderzeichen (StringReplace (vorstr, ';', ',', [rfReplaceAll]))+'"';
          body := body + ',"dropoff_lastname": "'+ConvertJSONSonderzeichen (StringReplace (nachstr, ';', ',', [rfReplaceAll]))+'"';

          body := body + ',"dropoff_streetname": "'+ConvertJSONSonderzeichen (StringReplace (streetstr, ';', ',', [rfReplaceAll]))+'"';
          body := body + ',"dropoff_streetnumber": "'+ConvertJSONSonderzeichen (StringReplace (nrstr, ';', ',', [rfReplaceAll]))+'"';

          body := body + ',"dropoff_postalcode": "'+liefquery.FieldByName('PLZ').AsString+'"'+
                         ',"dropoff_city": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('ORT').AsString, ';', ',', [rfReplaceAll]))+'"'+
                         ',"dropoff_country": "'+landstr+'"';

          body := body + ',"sender_external_id": "'+orderid+'"'+
                         ',"shipment_id":"'+nvestr+'"'+
                         ',"quantity":"'+'1'+'"';

          (*
          body := body + ',"sender_external_id": "'+nvestr+'"'+
                         ',"shipment_id":"'+orderid+'"'+
                         ',"quantity":"'+'1'+'"';
          *)
          if (Length (prodstr) > 0) then
            body := body + ',"service_type":"'+prodstr+'",';

          body := body + ',"weight": "'+FormatFloat ('0.#', query.FieldByName('BRUTTO_GEWICHT').AsFloat)+'"';

          body := '{' + body + '}';

          ForceDirectories(DatenPath + RESTDumpDir+'LieferGruen\'+FormatDateTime ('yyyymmdd', Now));

          StrToFile (DatenPath + RESTDumpDir+'LieferGruen\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_'+nvestr+'.json', body);

          resp := '';
          sdata.Clear;

          if SendRequest(urlstr, // Host,
                          -1, //Port
                          'standard/delivery', // Service
                          'POST', //Methode
                          '', // Proxy,
                          '', '', // User , PW
                          '', //Action
                          'application/json', //ContentType
                          [hdr], //AddHeader
                          StringUtils.StringToUTF (body),         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            StrToFile (DatenPath + RESTDumpDir+'LieferGruen\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_resp_'+nvestr+'.txt', resp);

            labelurl := '';

            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+'LieferGruen\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_resp_'+nvestr+'.json');

            sdata.Position := 0;
            js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;

            if not Assigned (js) then begin
              res := 11;
              ErrorText := 'API error message';
            end else begin
              fs := js.Field['status'];
              if not Assigned (fs) then begin
                res := 15;

                ErrorText := 'api error';

                fs := js.Field['error'];
                if Assigned (fs) then
                  ErrorText := ErrorText + ': ' + fs.Value
                else
              end else begin
                if (fs.Value <> 'ok') then begin
                  res := 15;
                  ErrorText := 'not ok';
                end else begin
                  fs := js.Field['delivery'];
                  if not Assigned (fs) then begin
                    res := 15;
                    ErrorText := 'delivery code error';
                  end else begin
                    SendungsID := nvestr;

                    us := fs.Field['id'];

                    if Assigned (us) and not (us.Value = NULL) then begin
                      Barcode    := us.Value;
                      SendungsNr := us.Value;

                      us := fs.Field['public_link'];

                      if not Assigned (us) or (us.Value = NULL) then begin
                        res := 16;
                        ErrorText := 'id error'
                      end else begin
                        TrackUrl := us.Value;
                      end;
                    end;
                  end;
                end;
              end;
            end;
          end else begin
            res := 11;

            ErrorText := 'Post delivery data error '+IntToStr (errcode)+' ('+errtext;
          end;

          if (res = 0) and (Length (SendungsNr) = 0) then begin
            body := '';
            resp := '';
            sdata.Clear;

            urlparam := 'id='+nvestr;
            //urlparam := 'sender_external_id='+nvestr;

            if SendRequest(urlstr, // Host,
                            -1, //Port
                            'standard/delivery?'+urlparam, // Service
                            'GET', //Methode
                            '', // Proxy,
                            '', '', // User , PW
                            '', //Action
                            'application/json', //ContentType
                            [hdr], //AddHeader
                            body,         // RequestData
                            resp,
                            sdata, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
            then begin
              StrToFile (DatenPath + RESTDumpDir+'LieferGruen\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_resp_'+nvestr+'.txt', resp);

              sdata.Position := 0;
              sdata.SaveToFile(DatenPath + RESTDumpDir+'LieferGruen\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nvestr+'.json');

              if (Pos ('404', resp) > 0) then begin
              end else begin
                sdata.Position := 0;
                js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;

                if not Assigned (js) then begin
                  res := 11;
                  ErrorText := 'API error message';
                end else begin
                  fs := js.Field['status'];
                  if not Assigned (fs) then begin
                    res := 15;
                    ErrorText := 'tracking_number code error';
                  end else begin
                    if (fs.Value <> 'ok') then begin
                      res := 15;
                      ErrorText := 'not ok';
                    end else begin
                      fs := js.Field['delivery'];
                      if not Assigned (fs) then begin
                        res := 15;
                        ErrorText := 'delivery code error';
                      end else begin
                        SendungsID := nvestr;

                        us := fs.Field['id'];

                        if Assigned (us) and not (us.Value = NULL) then begin
                          Barcode    := us.Value;
                          SendungsNr := us.Value;

                          us := fs.Field['public_link'];

                          if not Assigned (us) or (us.Value = NULL) then begin
                            res := 16;
                            ErrorText := 'id error'
                          end else begin
                            TrackUrl := us.Value;
                          end;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end else begin
              res := 11;

              ErrorText := 'Get delivery data error '+IntToStr (errcode)+' ('+errtext;
            end;
          end;

          if (res = 0) and (Length (SendungsNr) > 0) then begin
            body := '';

            urlparam := 'id='+SendungsNr;
            //urlparam := 'sender_external_id='+nvestr;

            if SendRequest(urlstr, // Host,
                            -1, //Port
                            'standard/label?'+urlparam, // Service
                            'GET', //Methode
                            '', // Proxy,
                            '', '', // User , PW
                            '', //Action
                            'application/json', //ContentType
                            [hdr], //AddHeader
                            body,         // RequestData
                            resp,
                            LabelImage, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
            then begin
              StrToFile (DatenPath + RESTDumpDir+'LieferGruen\'+FormatDateTime ('yyyymmdd', Now)+'\label_resp_'+nvestr+'.txt', resp);
              if (Pos ('400', resp) > 0) then begin
                res := 26;
                ErrorText := 'Error while load label';
              end else if (Pos ('404', resp) > 0) then begin
                res := 26;
                ErrorText := 'No label found';
              end else begin
                if (Pos ('application/zpl', resp) > 0) then
                  LabelFormat := 'zpl'
                else if (Pos ('application/pdf', resp) > 0)  then
                  LabelFormat := 'pdf'
                else
                  LabelFormat := '###';

                ForceDirectories(DatenPath + LabelDumpDir + 'LieferGruen');

                LabelImage.Position := 0;
                
                try
                  LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'LieferGruen\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                except
                end;

                if Assigned (SendITLog) then begin
                  SendITLog.Logging (clNormal, 'Versand: LieferGruen, RefNVE: '+query.FieldByName('REF_NVE').AsString+', Versender: '+Versender+', SendungsNr: '+SendungsNr);
                end;
              end;
            end else begin
              res := 25;

              ErrorText := 'Download error '+IntToStr (errcode)+' ('+errtext;
            end;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: LieferGruen, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

  //******************************************************************************
  //* Function Name: GetLieferGruenLabel
  //* Author       : Stefan Graf
  //* Datum        : 23.02.2023
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function GetLieferGruenLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    res,
    errcode  : Integer;
    errtext  : String;
    urlparam : String;
    body     : String;
    resp     : String;
    hdr      : String;
    keystr   : String;
    sdata    : TMemoryStream;
    js       : TlkJSONobject;
    fs,
    us       : TlkJSONbase;
    cfgquery : TSmartQuery;
    olddec   : Char;
    testflag : boolean;
    urlstr,
    prodstr,
    telstr,
    idstr,
    cfgstr    : String;
  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      ErrorText := '';

      urlstr  := '';

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select cfg.REST_URL as SPED_REST_URL, g.* from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else begin
          keystr  := cfgquery.FieldByName('API_KEY').AsString;
          prodstr := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;
          telstr  := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;
          cfgstr  := cfgquery.FieldByName('SENDIT_CONFIG').AsString;

          if Assigned (cfgquery.FindField ('OPT_TEST')) then
            testflag := cfgquery.FieldByName('OPT_TEST').AsString > '0';

          if Assigned (cfgquery.FindField ('REST_URL')) then
            urlstr  := cfgquery.FieldByName('REST_URL').AsString;

          if (Length (urlstr) = 0) then
            urlstr  := cfgquery.FieldByName('SPED_REST_URL').AsString;

          if (Length (urlstr) = 0) then
            ErrorText := 'Not REST url defined';
        end;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        sdata := TMemoryStream.Create;

        try
          hdr := 'Authorization: Bearer '+keystr;

          idstr := SendungsID;

          body := '';
          resp := '';
          sdata.Clear;

          urlparam := 'id='+idstr;
          //urlparam := 'sender_external_id='+idstr;

          if SendRequest(urlstr, // Host,
                          -1, //Port
                          'standard/delivery?'+urlparam, // Service
                          'GET', //Methode
                          '', // Proxy,
                          '', '', // User , PW
                          '', //Action
                          'application/json', //ContentType
                          [hdr], //AddHeader
                          body,         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
          then begin
            StrToFile (DatenPath + RESTDumpDir+'LieferGruen\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_resp_'+idstr+'.txt', resp);

            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+'LieferGruen\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+idstr+'.json');

            if (Pos ('404', resp) > 0) then begin
            end else begin
              sdata.Position := 0;
              js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;

              if not Assigned (js) then begin
                res := 11;
                ErrorText := 'API error message';
              end else begin
                fs := js.Field['status'];
                if not Assigned (fs) then begin
                  res := 15;
                  ErrorText := 'tracking_number code error';
                end else begin
                  if (fs.Value <> 'ok') then begin
                    res := 15;
                    ErrorText := 'not ok';
                  end else begin
                    fs := js.Field['delivery'];
                    if not Assigned (fs) then begin
                      res := 15;
                      ErrorText := 'delivery code error';
                    end else begin
                      us := fs.Field['id'];

                      if Assigned (us) and not (us.Value = NULL) then begin
                        Barcode    := us.Value;
                        SendungsNr := us.Value;

                        us := fs.Field['public_link'];

                        if Assigned (us) then begin
                          if (us.Value = NULL) then
                            TrackUrl := ''
                          else
                            TrackUrl := us.Value;
                        end else begin
                          res := 16;
                          ErrorText := 'public_link error'
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;
          end else begin
            res := 11;

            ErrorText := 'Get delivery data error '+IntToStr (errcode)+' ('+errtext;
          end;

          if (res = 0) and (Length (SendungsNr) > 0) then begin
            LabelImage.Clear;

            body := '';
            urlparam := 'id='+idstr;
            //urlparam := 'sender_external_id='+idstr;

            if SendRequest(urlstr, // Host,
                            -1, //Port
                            'standard/label?'+urlparam, // Service
                            'GET', //Methode
                            '', // Proxy,
                            '', '', // User , PW
                            '', //Action
                            'application/json', //ContentType
                            [hdr], //AddHeader
                            body,         // RequestData
                            resp,
                            LabelImage, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
            then begin
              StrToFile (DatenPath + RESTDumpDir + 'LieferGruen\'+FormatDateTime ('yyyymmdd', Now)+'\label_resp_'+idstr+'.txt', resp);

              if (Pos ('400', resp) > 0) then begin
                res := 26;
                ErrorText := 'Error while load label';
              end else if (Pos ('404', resp) > 0) then begin
                res := 26;
                ErrorText := 'No label found';
              end else begin
                if (Pos ('application/zpl', resp) > 0) then
                  LabelFormat := 'zpl'
                else if (Pos ('application/pdf', resp) > 0)  then
                  LabelFormat := 'pdf'
                else
                  LabelFormat := '###';

                ForceDirectories(DatenPath + LabelDumpDir + 'LieferGruen');

                LabelImage.Position := 0;

                try
                  LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'LieferGruen\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                except
                end;

                if Assigned (SendITLog) then begin
                  SendITLog.Logging (clNormal, 'Versand: LieferGruen, RefNVE: '+query.FieldByName('REF_NVE').AsString+', Versender: '+Versender+', SendungsNr: '+SendungsNr);
                end;
              end;
            end else begin
              res := 25;

              ErrorText := 'Download error '+IntToStr (errcode)+' ('+errtext;
            end;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: LieferGruen, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;
