unit AbverkaufVerrechnenDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, DB, ADODB, BetterADODataSet, Grids, DBGrids,
  SMDBGrid, DBGridPro, ExtCtrls, ComCtrls;

type
  TAbverkaufVerrechnenForm = class(TForm)
    Panel3: TPanel;
    CloseButton: TButton;
    OpenBillingDataSource: TDataSource;
    OpenBillingDataSet: TBetterADODataSet;
    MandPanel: TPanel;
    Label2: TLabel;
    MandantComboBox: TComboBoxPro;
    SubMandPanel: TPanel;
    Label11: TLabel;
    SubMandComboBox: TComboBoxPro;
    Panel2: TPanel;
    Label3: TLabel;
    ComboBoxPro1: TComboBoxPro;
    BillingPageControl: TPageControl;
    OpenBillingTabSheet: TTabSheet;
    BillingTabSheet: TTabSheet;
    OpenBillingDBGrid: TDBGridPro;
    BillingDBGrid: TDBGridPro;
    Panel1: TPanel;
    DBGridPro2: TDBGridPro;
    Panel4: TPanel;
    BillingDataSource: TDataSource;
    BillingDataSet: TBetterADODataSet;
    BillingPosDataSource: TDataSource;
    BillingPosDataSet: TBetterADODataSet;
    Splitter1: TSplitter;
    Panel5: TPanel;
    AbrechnenButton: TButton;
    procedure MandantComboBoxChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure SubMandComboBoxChange(Sender: TObject);
    procedure OpenBillingTabSheetShow(Sender: TObject);
    procedure BillingTabSheetShow(Sender: TObject);
    procedure BillingDataSourceDataChange(Sender: TObject; Field: TField);
    procedure AbrechnenButtonClick(Sender: TObject);
  private
    procedure UpdateBillingQuery;
    procedure UpdateInvoiceQuery;
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, FrontEndUtils, DBGridUtilModule, DatenModul, ConfigModul, LVSInvoiceInterface;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAbverkaufVerrechnenForm.UpdateBillingQuery;
begin
  OpenBillingDataSet.Close;

  OpenBillingDataSet.CommandText := 'select * from V_PCD_BESTAND_BILLING where REF_INVOICE_POS is null and REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc)';
  OpenBillingDataSet.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

  if (SubMandPanel.Visible and (GetComboBoxRef (SubMandComboBox) > 0)) then begin
    OpenBillingDataSet.CommandText := OpenBillingDataSet.CommandText + ' and REF_SUB_MAND=:ref_sub_mand';
    OpenBillingDataSet.Parameters.ParamByName('ref_sub_mand').Value := GetComboBoxRef (SubMandComboBox);
  end else if (GetComboBoxRef (MandantComboBox) > 0) then begin
    OpenBillingDataSet.CommandText := OpenBillingDataSet.CommandText + ' and REF_MAND=:ref_mand';
    OpenBillingDataSet.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandantComboBox);
  end;

  OpenBillingDataSet.Open;

  DBGridUtils.SetGewichtDisplayFunctions(OpenBillingDBGrid.DataSource.DataSet, 'NETTO_GEWICHT');
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 29.08.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAbverkaufVerrechnenForm.BillingDataSourceDataChange(Sender: TObject; Field: TField);
begin
  BillingPosDataSet.Close;

  if (BillingDataSet.Active and (BillingDataSet.RecNo > 0)) then begin
    BillingPosDataSet.CommandText := 'select * from V_LAGER_INVOICE_POS where REF_INVOICE=:ref';
    BillingPosDataSet.Parameters.ParamByName('ref').Value := BillingDataSet.FieldByName ('REF').AsInteger;

    BillingPosDataSet.Open;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 29.08.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAbverkaufVerrechnenForm.BillingTabSheetShow(Sender: TObject);
begin
  UpdateInvoiceQuery;
end;

procedure TAbverkaufVerrechnenForm.AbrechnenButtonClick(Sender: TObject);
var
  res,
  ref   : Integer;
  query : TADOQuery;
begin
  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;
    query.SQL.Add ('select distinct bill.REF_MAND, bill.REF_SUB_MAND, bill.REF_LAGER, bill.REF_BILLING_MAND');
    query.SQL.Add ('from V_PCD_BESTAND_BILLING bill where bill.REF_INVOICE_POS is null and bill.REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc)');
    query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    if (SubMandComboBox.Visible and (GetComboBoxRef (SubMandComboBox) > 0)) then begin
      query.SQL.Add (' and REF_SUB_MAND=:ref_sub_mand');
      query.Parameters.ParamByName('ref_sub_mand').Value := GetComboBoxRef (SubMandComboBox);
    end else if (GetComboBoxRef (MandantComboBox) > 0) then begin
      query.SQL.Add ('and REF_MAND=:ref_mand');
      query.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandantComboBox);
    end;

    res := 0;

    query.Open;

    while not (query.Eof) and (res = 0) do begin
      res := BillingAbrechnen (query.Fields [0].AsInteger, query.Fields [1].AsInteger, DBGetReferenz (query.Fields [2]), query.Fields [3].AsInteger, ref);

      query.Next;
    end;

    OpenBillingDBGrid.Reload;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 29.08.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAbverkaufVerrechnenForm.UpdateInvoiceQuery;
begin
  BillingDataSet.Close;

  BillingDataSet.CommandText := 'select * from V_PCD_LAGER_INVOICE where REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc)';
  BillingDataSet.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

  if (SubMandComboBox.Enabled and (GetComboBoxRef (SubMandComboBox) > 0)) then begin
    BillingDataSet.CommandText := BillingDataSet.CommandText + ' and REF_SUB_MAND=:ref_sub_mand';
    BillingDataSet.Parameters.ParamByName('ref_sub_mand').Value := GetComboBoxRef (SubMandComboBox);
  end else begin
    BillingDataSet.CommandText := BillingDataSet.CommandText + ' and REF_MAND=:ref_mand';
    BillingDataSet.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandantComboBox);
  end;

  BillingDataSet.Open;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 29.08.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAbverkaufVerrechnenForm.FormClose(Sender: TObject;
  var Action: TCloseAction);
begin
  OpenBillingDataSet.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAbverkaufVerrechnenForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAbverkaufVerrechnenForm.FormShow(Sender: TObject);
begin
  LoadMandantCombobox (MandantComboBox);

  if (LVSDatenModul.AktMandantRef > 0) then
    MandantComboBox.ItemIndex := FindComboboxRef(MandantComboBox, LVSDatenModul.AktMandantRef);

  MandantComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAbverkaufVerrechnenForm.MandantComboBoxChange(Sender: TObject);
begin
  if (SubMandPanel.Visible) then begin
    ClearComboBoxObjects (SubMandComboBox);

    LoadSubMandantCombobox (SubMandComboBox, LVSDatenModul.AktLocationRef, GetComboBoxRef(MandantComboBox));

    if (SubMandComboBox.Items.Count = 0) then
      SubMandComboBox.Enabled := False
    else begin
      SubMandComboBox.Enabled := True;

      SubMandComboBox.Items.Insert (0, '<alle>');
      SubMandComboBox.ItemIndex := 0;
    end;
  end;

  if (BillingPageControl.ActivePage = OpenBillingTabSheet) then
    UpdateBillingQuery
  else
    UpdateInvoiceQuery;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAbverkaufVerrechnenForm.SubMandComboBoxChange(Sender: TObject);
begin
  if (BillingPageControl.ActivePage = OpenBillingTabSheet) then
    UpdateBillingQuery
  else
    UpdateInvoiceQuery
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 29.08.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAbverkaufVerrechnenForm.OpenBillingTabSheetShow(Sender: TObject);
begin
  UpdateBillingQuery;
end;

end.
