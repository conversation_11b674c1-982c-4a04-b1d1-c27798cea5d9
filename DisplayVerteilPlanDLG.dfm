object DisplayVerteilPlanForm: TDisplayVerteilPlanForm
  Left = 0
  Top = 0
  Caption = 'DisplayVerteilPlanForm'
  ClientHeight = 366
  ClientWidth = 635
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    635
    366)
  PixelsPerInch = 96
  TextHeight = 13
  object CloseButton: TButton
    Left = 552
    Top = 333
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 0
  end
  object VTLDBGrid: TDBGridPro
    Left = 8
    Top = 16
    Width = 619
    Height = 309
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = VTLDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgCol<PERSON>ines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object PrintButton: TButton
    Left = 8
    Top = 333
    Width = 75
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Drucken...'
    TabOrder = 2
    OnClick = PrintButtonClick
  end
  object VTLDataSource: TDataSource
    DataSet = VTLQuery
    Left = 384
    Top = 192
  end
  object VTLQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 416
    Top = 192
  end
end
