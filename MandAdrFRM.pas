unit MandAdrFRM;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes,
  Graphics, Controls, Forms, Dialogs, StdCtrls;

type
  TMandAdrFrame = class(TFrame)
    Label26: TLabel;
    NameEdit: TEdit;
    Label27: TLabel;
    RoadEdit: TEdit;
    Label28: TLabel;
    PLZEdit: TEdit;
    Label29: TLabel;
    OrtEdit: TEdit;
    Label30: TLabel;
    LandEdit: TEdit;
    NameAddEdit: TEdit;
    Label1: TLabel;
    Label2: TLabel;
    ContactEdit: TEdit;
    Label3: TLabel;
    PhoneEdit: TEdit;
    Label4: TLabel;
    MailEdit: TEdit;
  private
    { Private-Deklarationen }
  public
    constructor Create (AOwner: TComponent); override;
  end;

implementation

{$R *.dfm}

constructor TMandAdrFrame.Create (AOwner: TComponent);
begin
  inherited;

  NameEdit.Text := '';
  RoadEdit.Text := '';
  PLZEdit.Text := '';
  OrtEdit.Text := '';
  LandEdit.Text := '';
  NameAddEdit.Text := '';
  ContactEdit.Text := '';
  PhoneEdit.Text := '';
  MailEdit.Text := '';
end;

end.
