unit DispRefValuesDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, Grids, StdCtrls, ExtCtrls, Menus, DBCtrls, DB, DBGrids;

type
  TDispRefValuesForm = class(TForm)
    Panel1: TPanel;
    Button1: TButton;
    StringGrid1: TStringGrid;
    Memo1: TMemo;
    Splitter1: TSplitter;
    DBNavigator1: TDBNavigator;
    DataSource1: TDataSource;
    PopupMenu1: TPopupMenu;
    Kopieren1: TMenuItem;
    MemoPopupMenu: TPopupMenu;
    CopyQueryMenuItem: TMenuItem;
    CopyQueryParamMenuItem: TMenuItem;
    procedure Kopieren1Click(Sender: TObject);
    procedure StringGrid1SelectCell(Sender: TObject; ACol, ARow: Integer;
      var CanSelect: Boolean);
    procedure FormShow(Sender: TObject);
    procedure DataSource1DataChange(Sender: TObject; Field: TField);
    procedure PopupMenu1Popup(Sender: TObject);
    procedure CopyQueryParamMenuItemClick(Sender: TObject);
    procedure CopyQueryMenuItemClick(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    Grid : TDBGrid;
  end;

implementation

{$R *.dfm}

uses
  ADODB, BetterADODataSet,

  {$ifdef UseODAC}
    Ora, OraSmart,
  {$endif}

  Clipbrd;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispRefValuesForm.Kopieren1Click(Sender: TObject);
begin
  Clipboard.SetTextBuf (PChar (StringGrid1.Cells [2, StringGrid1.Row]));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispRefValuesForm.CopyQueryMenuItemClick(Sender: TObject);
var
  querystr : String;
begin
  querystr := '';

  if Assigned (Grid) then begin
    if (Grid.DataSource.DataSet is TADOQuery) Then begin
      querystr := (Grid.DataSource.DataSet as TADOQuery).SQL.Text;
    end else if (Grid.DataSource.DataSet is TBetterADODataSet) Then begin
      querystr := (Grid.DataSource.DataSet as TBetterADODataSet).CommandText;
    {$ifdef UseODAC}
      end else if (Grid.DataSource.DataSet is TSmartQuery) Then begin
        querystr := (Grid.DataSource.DataSet as TSmartQuery).SQL.Text;
      end else if (Grid.DataSource.DataSet is TOraQuery) Then begin
        querystr := (Grid.DataSource.DataSet as TOraQuery).SQL.Text;
    {$endif}
    end;
  end;

  if (Length (querystr) > 0) then
    Clipboard.SetTextBuf (PChar (querystr));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispRefValuesForm.StringGrid1SelectCell(Sender: TObject; ACol, ARow: Integer; var CanSelect: Boolean);
begin
  CanSelect := (ACol = 2);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispRefValuesForm.FormShow(Sender: TObject);
begin
  StringGrid1.Row := StringGrid1.FixedRows;
  StringGrid1.Col := 2;

  Memo1.Clear;

  if Assigned (Grid) then begin
    if (Grid.DataSource.DataSet is TADOQuery) Then
      Memo1.Text := (Grid.DataSource.DataSet as TADOQuery).SQL.Text
    else if (Grid.DataSource.DataSet is TBetterADODataSet) Then
      Memo1.Text := (Grid.DataSource.DataSet as TBetterADODataSet).CommandText
    {$ifdef UseODAC}
      else if (Grid.DataSource.DataSet is TSmartQuery) Then
        Memo1.Text := (Grid.DataSource.DataSet as TSmartQuery).SQL.Text
      else if (Grid.DataSource.DataSet is TOraQuery) Then
        Memo1.Text := (Grid.DataSource.DataSet as TOraQuery).SQL.Text;
    {$endif}
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispRefValuesForm.DataSource1DataChange(Sender: TObject; Field: TField);
var
  idx,
  grididx : Integer;
begin
  if Assigned (Grid) then begin
    idx := 0;
    grididx := 0;

    while (idx < Grid.Columns.Count) do begin
      if not (Grid.Columns [idx].Visible) then begin
        with StringGrid1 do begin
          Cells [1, FixedRows + grididx] := Grid.Columns [idx].Field.FieldName;
          Cells [2, FixedRows + grididx] := Grid.Columns [idx].Field.AsString;
        end;

        Inc (grididx);
      end;

      Inc (idx);
    end;

    with StringGrid1 do begin
      if (grididx > 0) then
        RowCount := FixedRows + grididx
      else begin
        RowCount := FixedRows + 1;
        Rows [FixedRows].Clear;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispRefValuesForm.PopupMenu1Popup(Sender: TObject);
begin
  Kopieren1.Enabled := (DataSource1.DataSet.Active);
end;

procedure TDispRefValuesForm.CopyQueryParamMenuItemClick(Sender: TObject);
var
  i        : Integer;
  querystr : String;
begin
  querystr := '';

  if Assigned (Grid) then begin
    if (Grid.DataSource.DataSet is TADOQuery) Then begin
      querystr := (Grid.DataSource.DataSet as TADOQuery).SQL.Text;

      for i:=0 to (Grid.DataSource.DataSet as TADOQuery).Parameters.Count - 1 do begin
        if ((Grid.DataSource.DataSet as TADOQuery).Parameters [i].DataType = ftString) then
          querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TADOQuery).Parameters [i].Name, #39+(Grid.DataSource.DataSet as TADOQuery).Parameters [i].Value+#39, [rfReplaceAll, rfIgnoreCase])
        else if ((Grid.DataSource.DataSet as TADOQuery).Parameters [i].DataType = ftDateTime) then
          querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TADOQuery).Parameters [i].Name, 'to_date('''+FormatDateTime ('dd.mm.yyyy hh:nn:ss', (Grid.DataSource.DataSet as TADOQuery).Parameters [i].Value)+''',''dd.mm.yyyy hh24:mi:ss'')', [rfReplaceAll, rfIgnoreCase])
        else
          querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TADOQuery).Parameters [i].Name, (Grid.DataSource.DataSet as TADOQuery).Parameters [i].Value, [rfReplaceAll, rfIgnoreCase]);
      end;
    end else if (Grid.DataSource.DataSet is TBetterADODataSet) Then begin
      querystr := (Grid.DataSource.DataSet as TBetterADODataSet).CommandText;

      for i:=0 to (Grid.DataSource.DataSet as TBetterADODataSet).Parameters.Count - 1 do begin
        if ((Grid.DataSource.DataSet as TBetterADODataSet).Parameters [i].DataType = ftString) then
          querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TBetterADODataSet).Parameters [i].Name, #39+(Grid.DataSource.DataSet as TBetterADODataSet).Parameters [i].Value+#39, [rfReplaceAll, rfIgnoreCase])
        else if ((Grid.DataSource.DataSet as TBetterADODataSet).Parameters [i].DataType = ftDateTime) then
          querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TBetterADODataSet).Parameters [i].Name, 'to_date('''+FormatDateTime ('dd.mm.yyyy hh:nn:ss', (Grid.DataSource.DataSet as TBetterADODataSet).Parameters [i].Value)+''',''dd.mm.yyyy hh24:mi:ss'')', [rfReplaceAll, rfIgnoreCase])
        else
          querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TBetterADODataSet).Parameters [i].Name, (Grid.DataSource.DataSet as TBetterADODataSet).Parameters [i].Value, [rfReplaceAll, rfIgnoreCase]);
      end;
    {$ifdef UseODAC}
      end else if (Grid.DataSource.DataSet is TSmartQuery) Then begin
        querystr := (Grid.DataSource.DataSet as TSmartQuery).SQL.Text;

        for i:=0 to (Grid.DataSource.DataSet as TSmartQuery).Params.Count - 1 do begin
          if ((Grid.DataSource.DataSet as TSmartQuery).Params [i].DataType = ftString) then
            querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TSmartQuery).Params [i].Name, #39+(Grid.DataSource.DataSet as TSmartQuery).Params [i].Value+#39, [rfReplaceAll, rfIgnoreCase])
          else if ((Grid.DataSource.DataSet as TSmartQuery).Params [i].DataType = ftDate) then
            querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TSmartQuery).Params [i].Name, 'to_date('''+FormatDateTime ('dd.mm.yyyy', (Grid.DataSource.DataSet as TSmartQuery).Params [i].Value)+''',''dd.mm.yyyy'')', [rfReplaceAll, rfIgnoreCase])
          else if ((Grid.DataSource.DataSet as TSmartQuery).Params [i].DataType = ftDateTime) then
            querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TSmartQuery).Params [i].Name, 'to_date('''+FormatDateTime ('dd.mm.yyyy hh:nn:ss', (Grid.DataSource.DataSet as TSmartQuery).Params [i].Value)+''',''dd.mm.yyyy hh24:mi:ss'')', [rfReplaceAll, rfIgnoreCase])
          else
            querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TSmartQuery).Params [i].Name, (Grid.DataSource.DataSet as TSmartQuery).Params [i].Value, [rfReplaceAll, rfIgnoreCase]);
        end;
      end else if (Grid.DataSource.DataSet is TOraQuery) Then begin
        querystr := (Grid.DataSource.DataSet as TOraQuery).SQL.Text;

        for i:=0 to (Grid.DataSource.DataSet as TOraQuery).Params.Count - 1 do begin
          if ((Grid.DataSource.DataSet as TOraQuery).Params [i].DataType = ftString) then
            querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TOraQuery).Params [i].Name, #39+(Grid.DataSource.DataSet as TOraQuery).Params [i].Value+#39, [rfReplaceAll, rfIgnoreCase])
          else if ((Grid.DataSource.DataSet as TOraQuery).Params [i].DataType = ftDate) then
            querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TOraQuery).Params [i].Name, 'to_date('''+FormatDateTime ('dd.mm.yyyy', (Grid.DataSource.DataSet as TOraQuery).Params [i].Value)+''',''dd.mm.yyyy'')', [rfReplaceAll, rfIgnoreCase])
          else if ((Grid.DataSource.DataSet as TOraQuery).Params [i].DataType = ftDateTime) then
            querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TOraQuery).Params [i].Name, 'to_date('''+FormatDateTime ('dd.mm.yyyy hh:nn:ss', (Grid.DataSource.DataSet as TOraQuery).Params [i].Value)+''',''dd.mm.yyyy hh24:mi:ss'')', [rfReplaceAll, rfIgnoreCase])
          else
            querystr := StringReplace (querystr, ':'+(Grid.DataSource.DataSet as TOraQuery).Params [i].Name, (Grid.DataSource.DataSet as TOraQuery).Params [i].Value, [rfReplaceAll, rfIgnoreCase]);
        end;
    {$endif}
    end;
  end;

  if (Length (querystr) > 0) then
    Clipboard.SetTextBuf (PChar (querystr));
end;

end.
