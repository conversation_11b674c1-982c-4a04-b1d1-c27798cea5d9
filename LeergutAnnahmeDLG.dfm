object LeergutAnnahmeForm: TLeergutAnnahmeForm
  Left = 0
  Top = 0
  Caption = 'Lademittel R'#252'ckerfassung'
  ClientHeight = 697
  ClientWidth = 825
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  TextHeight = 13
  object KopfPanel: TPanel
    Left = 0
    Top = 0
    Width = 825
    Height = 217
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Kunde: TLabel
      Left = 16
      Top = 56
      Width = 54
      Height = 23
      Caption = 'Kunde'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object KundenLabel: TLabel
      Left = 176
      Top = 56
      Width = 123
      Height = 23
      Caption = 'KundenLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Auftrag: TLabel
      Left = 16
      Top = 80
      Width = 62
      Height = 23
      Caption = 'Auftrag'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object AuftragLabel: TLabel
      Left = 176
      Top = 80
      Width = 122
      Height = 23
      Caption = 'AuftragLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Lieferdatum: TLabel
      Left = 16
      Top = 104
      Width = 100
      Height = 23
      Caption = 'Lieferdatum'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object LieferDatumLabel: TLabel
      Left = 176
      Top = 104
      Width = 168
      Height = 23
      Caption = 'LieferDatumLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label1: TLabel
      Left = 16
      Top = 16
      Width = 137
      Height = 25
      Caption = 'Lieferscheinnr.'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object LSNrLabel: TLabel
      Left = 176
      Top = 16
      Width = 106
      Height = 25
      Caption = 'LSNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LTNameLabel: TLabel
      Left = 16
      Top = 186
      Width = 118
      Height = 20
      Caption = 'Ladungstr'#228'ger'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LTAnzahlIOLabel: TLabel
      Left = 554
      Top = 186
      Width = 80
      Height = 20
      Caption = 'Anzahl IO'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LTAnzahlNIOLabel: TLabel
      Left = 690
      Top = 186
      Width = 92
      Height = 20
      Caption = 'Anzahl NIO'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label2: TLabel
      Left = 16
      Top = 144
      Width = 132
      Height = 23
      Caption = 'Lademittelkonto'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object KontoLabel: TLabel
      Left = 176
      Top = 144
      Width = 108
      Height = 23
      Caption = 'KontoLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object FussPanel: TPanel
    Left = 0
    Top = 656
    Width = 825
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      825
      41)
    object Bevel1: TBevel
      Left = 9
      Top = 0
      Width = 811
      Height = 8
      Shape = bsTopLine
    end
    object CloseButton: TButton
      Left = 745
      Top = 8
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 0
    end
  end
  object FehlerPanel: TPanel
    Left = 0
    Top = 544
    Width = 825
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 2
  end
  object LTScrollBox: TScrollBox
    Left = 0
    Top = 217
    Width = 825
    Height = 327
    Align = alClient
    TabOrder = 3
  end
  object Panel1: TPanel
    Left = 0
    Top = 585
    Width = 825
    Height = 71
    Align = alBottom
    Anchors = [akRight, akBottom]
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      825
      71)
    object BuchenButton: TButton
      Left = 16
      Top = 8
      Width = 793
      Height = 57
      Anchors = [akLeft, akTop, akRight]
      Caption = 'Lademittel auf das Konto buchen'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -27
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      OnClick = BuchenButtonClick
    end
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 1064
    Top = 24
  end
end
