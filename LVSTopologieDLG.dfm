object LagerTopologieForm: TLagerTopologieForm
  Left = 342
  Top = 190
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Konfiguration der Lager'
  ClientHeight = 697
  ClientWidth = 712
  Color = clBtnFace
  Constraints.MinHeight = 580
  Constraints.MinWidth = 700
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poMainFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnKeyDown = FormKeyDown
  OnShow = FormShow
  DesignSize = (
    712
    697)
  TextHeight = 13
  object PageControl1: TPageControl
    Left = 8
    Top = 8
    Width = 696
    Height = 648
    ActivePage = LPTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 0
    OnChanging = PageControl1Changing
    object TabSheet5: TTabSheet
      Caption = 'Niederlassungen'
      ImageIndex = 4
      OnResize = TabSheet5Resize
      OnShow = TabSheet5Show
      DesignSize = (
        688
        620)
      object LocStringGrid: TStringGridPro
        Left = 8
        Top = 17
        Width = 566
        Height = 600
        Anchors = [akLeft, akTop, akRight, akBottom]
        ColCount = 3
        DefaultColWidth = 20
        DefaultRowHeight = 20
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
        TabOrder = 0
        OnDblClick = LocStringGridDblClick
        GridStyle.OddColor = clInfoBk
        TitelTexte.Strings = (
          ''
          'NL'
          'Beschreibung')
        TitelFont.Charset = DEFAULT_CHARSET
        TitelFont.Color = clWindowText
        TitelFont.Height = -11
        TitelFont.Name = 'MS Sans Serif'
        TitelFont.Style = []
        ColWidths = (
          20
          102
          411)
        RowHeights = (
          20
          20
          20
          20
          20)
      end
      object NewLocButton: TButton
        Left = 585
        Top = 17
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Neu...'
        TabOrder = 1
        OnClick = NewLocButtonClick
      end
      object ChangeLocButton: TButton
        Left = 585
        Top = 129
        Width = 97
        Height = 25
        Anchors = [akRight]
        Caption = 'Bearbeiten...'
        TabOrder = 2
        OnClick = LocStringGridDblClick
      end
      object CopyLocButton: TButton
        Left = 585
        Top = 54
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Kopieren...'
        TabOrder = 3
        OnClick = CopyLocButtonClick
      end
    end
    object LagerTabSheet: TTabSheet
      Caption = 'Lager'
      OnResize = LagerTabSheetResize
      OnShow = LagerTabSheetShow
      DesignSize = (
        688
        620)
      object Label3: TLabel
        Left = 8
        Top = 56
        Width = 27
        Height = 13
        Caption = 'Lager'
      end
      object Label10: TLabel
        Left = 8
        Top = 8
        Width = 67
        Height = 13
        Caption = 'Niederlassung'
      end
      object LagerNeuButton: TButton
        Left = 585
        Top = 81
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = '&Neu...'
        TabOrder = 0
        OnClick = LagerNeuButtonClick
      end
      object LagerDelButton: TButton
        Left = 585
        Top = 201
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = '&L'#246'schen'
        Enabled = False
        TabOrder = 1
        Visible = False
      end
      object LagerStringGrid: TStringGridPro
        Left = 8
        Top = 72
        Width = 566
        Height = 536
        Anchors = [akLeft, akTop, akRight, akBottom]
        ColCount = 7
        DefaultColWidth = 20
        DefaultRowHeight = 20
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
        PopupMenu = LagerPopupMenu
        TabOrder = 2
        OnClick = LagerStringGridClick
        OnDblClick = LagerStringGridDblClick
        GridStyle.OddColor = clInfoBk
        TitelTexte.Strings = (
          ''
          'NL'
          'Lagername'
          'Art'
          'Beschreibung'
          'Betriebs-Nr.'
          'Sperrlager')
        TitelFont.Charset = DEFAULT_CHARSET
        TitelFont.Color = clWindowText
        TitelFont.Height = -11
        TitelFont.Name = 'MS Sans Serif'
        TitelFont.Style = []
        ColWidths = (
          20
          88
          83
          43
          116
          87
          86)
        RowHeights = (
          20
          20
          20
          20
          20)
      end
      object LagerCfgButton: TButton
        Left = 585
        Top = 249
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Konfiguration...'
        TabOrder = 3
        OnClick = LagerCfgButtonClick
      end
      object LagerCommButton: TButton
        Left = 585
        Top = 289
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Kommunikation...'
        TabOrder = 4
        OnClick = LagerCommButtonClick
      end
      object LocComboBox: TComboBoxPro
        Left = 8
        Top = 24
        Width = 566
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 5
        OnChange = LocComboBoxChange
      end
      object LagerMandButton: TButton
        Left = 585
        Top = 328
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Mandanten...'
        TabOrder = 6
        OnClick = LagerMandButtonClick
      end
      object LagerEditButton: TButton
        Left = 585
        Top = 154
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Bearbeiten...'
        TabOrder = 7
        OnClick = LagerStringGridDblClick
      end
      object LagerCopyButton: TButton
        Left = 585
        Top = 112
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Kopieren...'
        TabOrder = 8
        OnClick = LagerCopyButtonClick
      end
    end
    object LBTabSheet: TTabSheet
      Caption = 'Lagerbereiche'
      ImageIndex = 1
      OnResize = LBTabSheetResize
      OnShow = LBTabSheetShow
      DesignSize = (
        688
        620)
      object Label4: TLabel
        Left = 8
        Top = 8
        Width = 27
        Height = 13
        Caption = 'Lager'
      end
      object Label5: TLabel
        Left = 8
        Top = 56
        Width = 42
        Height = 13
        Caption = 'Bereiche'
      end
      object Label11: TLabel
        Left = 8
        Top = 431
        Width = 104
        Height = 13
        Anchors = [akLeft, akBottom]
        Caption = 'Zonen in dem Bereich'
        ExplicitTop = 421
      end
      object LBNeuButton: TButton
        Left = 585
        Top = 72
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = '&Neu...'
        TabOrder = 1
        OnClick = LBNeuButtonClick
      end
      object LagerComboBox1: TComboBoxPro
        Left = 8
        Top = 24
        Width = 561
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 0
        OnChange = LagerComboBox1Change
      end
      object LBCopyButton: TButton
        Left = 585
        Top = 104
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = '&Kopieren...'
        TabOrder = 2
        OnClick = LBCopyButtonClick
      end
      object DelLBButton: TButton
        Left = 585
        Top = 394
        Width = 97
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = '&L'#246'schen'
        TabOrder = 3
        OnClick = DelLBButtonClick
      end
      object LBDBGrid: TDBGridPro
        Left = 8
        Top = 72
        Width = 566
        Height = 347
        Hint = 
          'Order with the left mouse button, searching with the right mouse' +
          ' button'
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = LBDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
        PopupMenu = LBDBGridPopupMenu
        ReadOnly = True
        TabOrder = 4
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        OnDblClick = StringGrid2DblClick
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'MS Sans Serif'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object EditLBButton: TButton
        Left = 585
        Top = 152
        Width = 97
        Height = 25
        Anchors = [akTop, akRight]
        Caption = '&Bearbeiten...'
        TabOrder = 5
        OnClick = StringGrid2DblClick
      end
      object LBZoneDBGrid: TDBGridPro
        Left = 8
        Top = 450
        Width = 566
        Height = 160
        Anchors = [akLeft, akRight, akBottom]
        DataSource = LBZoneDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
        PopupMenu = LBZoneDBGridPopupMenu
        ReadOnly = True
        TabOrder = 6
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        OnDblClick = LBZOneEditButtonClick
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'MS Sans Serif'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object LBZoneNewButton: TButton
        Left = 585
        Top = 450
        Width = 97
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = '&Neu...'
        TabOrder = 7
        OnClick = LBZoneNewButtonClick
      end
      object LBZOneEditButton: TButton
        Left = 585
        Top = 481
        Width = 97
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = '&Bearbeiten...'
        TabOrder = 8
        OnClick = LBZOneEditButtonClick
      end
      object LBZoneDelButton: TButton
        Left = 585
        Top = 585
        Width = 97
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = '&L'#246'schen'
        TabOrder = 9
        OnClick = LBZoneDelButtonClick
      end
    end
    object LPTabSheet: TTabSheet
      Caption = 'Lagerpl'#228'tze'
      ImageIndex = 2
      OnResize = LPTabSheetResize
      OnShow = LPTabSheetShow
      object LPTabSplitter: TSplitter
        Left = 0
        Top = 497
        Width = 688
        Height = 3
        Cursor = crVSplit
        Align = alBottom
        ExplicitTop = 499
        ExplicitWidth = 691
      end
      object LPArtikelPanel: TPanel
        Left = 0
        Top = 500
        Width = 688
        Height = 120
        Align = alBottom
        BevelOuter = bvNone
        Constraints.MinHeight = 120
        TabOrder = 0
        DesignSize = (
          688
          120)
        object Label12: TLabel
          Left = 8
          Top = 7
          Width = 93
          Height = 13
          Caption = 'Zugeordnete Artikel'
        end
        object LPArtikelDBGrid: TDBGridPro
          Left = 8
          Top = 24
          Width = 566
          Height = 88
          Anchors = [akLeft, akTop, akRight, akBottom]
          DataSource = LPArtikelDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
          PopupMenu = LPArtikelDBGridPopupMenu
          ReadOnly = True
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          OnDrawColumnCell = DBGridDrawColumnCell
          OnKeyPress = DBGridHotTrackKeyPress
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'MS Sans Serif'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
          RegistryKey = 'Software\Scalabium'
          RegistrySection = 'SMDBGrid'
          WidthOfIndicator = 11
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
        end
        object LPArAddButton: TButton
          Left = 585
          Top = 24
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Hinzuf'#252'gen...'
          TabOrder = 1
          OnClick = LPArAddButtonClick
        end
        object LPARDelButton: TButton
          Left = 585
          Top = 87
          Width = 97
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = '&L'#246'schen'
          TabOrder = 2
          OnClick = LPARDelButtonClick
        end
      end
      object LPDatenPanel: TPanel
        Left = 0
        Top = 0
        Width = 688
        Height = 497
        Align = alClient
        BevelOuter = bvNone
        Constraints.MinHeight = 480
        TabOrder = 1
        DesignSize = (
          688
          497)
        object Label1: TLabel
          Left = 8
          Top = 8
          Width = 27
          Height = 13
          Caption = 'Lager'
        end
        object Label2: TLabel
          Left = 240
          Top = 8
          Width = 36
          Height = 13
          Caption = 'Bereich'
        end
        object Label6: TLabel
          Left = 8
          Top = 56
          Width = 55
          Height = 13
          Caption = 'Lagerpl'#228'tze'
        end
        object LagerComboBox2: TComboBoxPro
          Left = 8
          Top = 24
          Width = 209
          Height = 21
          Style = csOwnerDrawFixed
          ItemHeight = 15
          TabOrder = 0
          OnChange = LagerComboBox2Change
        end
        object LBComboBox1: TComboBoxPro
          Left = 240
          Top = 24
          Width = 329
          Height = 21
          Style = csOwnerDrawFixed
          ItemHeight = 15
          TabOrder = 1
          OnChange = LBComboBoxChange
        end
        object DrawLPButton: TButton
          Left = 585
          Top = 22
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Zeichnen...'
          TabOrder = 2
          OnClick = DrawLPButtonClick
        end
        object NeuLPButton: TButton
          Left = 585
          Top = 73
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = '&Neuer Platz...'
          Enabled = False
          TabOrder = 3
          OnClick = NeuLPButtonClick
        end
        object NeuLPSerieButton: TButton
          Left = 585
          Top = 104
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Neue &Serie...'
          TabOrder = 4
          OnClick = NeuLPSerieButtonClick
        end
        object EditLPButton: TButton
          Left = 585
          Top = 136
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Bearbeiten...'
          TabOrder = 5
          OnClick = EditLPButtonClick
        end
        object ChangeLPLBButton: TButton
          Left = 585
          Top = 168
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Bereich '#228'ndern...'
          TabOrder = 6
          OnClick = ChangeLPLBButtonClick
        end
        object SperrenLPButton: TButton
          Left = 585
          Top = 209
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Sperren...'
          TabOrder = 7
          OnClick = SperrenLPButtonClick
        end
        object FreigebenLPButton: TButton
          Left = 585
          Top = 240
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Freigeben...'
          TabOrder = 8
          OnClick = FreigebenLPButtonClick
        end
        object ChangeTypenButton: TButton
          Left = 585
          Top = 280
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'LP Typen...'
          TabOrder = 9
          OnClick = ChangeTypenButtonClick
        end
        object PrintLPButton: TButton
          Left = 585
          Top = 312
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Auszeichnen...'
          TabOrder = 10
          OnClick = PrintLPButtonClick
        end
        object RegalButton: TButton
          Left = 585
          Top = 343
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Regal Daten...'
          TabOrder = 11
          OnClick = RegalButtonClick
        end
        object KommFolgeButton: TButton
          Left = 585
          Top = 384
          Width = 97
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Komm-Folge...'
          TabOrder = 12
          OnClick = KommFolgeButtonClick
        end
        object DelLPButton: TButton
          Left = 585
          Top = 466
          Width = 97
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = '&L'#246'schen'
          TabOrder = 13
          OnClick = DelLPButtonClick
        end
        object LPDBGrid: TDBGridPro
          Left = 8
          Top = 72
          Width = 566
          Height = 419
          Anchors = [akLeft, akTop, akRight, akBottom]
          DataSource = LPDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit, dgMultiSelect]
          PopupMenu = LPDBGridPopupMenu
          ReadOnly = True
          TabOrder = 14
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          OnDrawColumnCell = DBGridDrawColumnCell
          OnDblClick = LPDBGridDblClick
          OnKeyPress = DBGridHotTrackKeyPress
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'MS Sans Serif'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoCheckBoxSelect, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
          RegistryKey = 'Software\Scalabium'
          RegistrySection = 'SMDBGrid'
          WidthOfIndicator = 29
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
          OnColumnSort = LPDBGridColumnSort
        end
      end
    end
    object KommTabSheet: TTabSheet
      Caption = 'Kommissionier-Pl'#228'tze'
      ImageIndex = 3
      OnResize = KommTabSheetResize
      OnShow = KommTabSheetShow
      DesignSize = (
        688
        620)
      object Label7: TLabel
        Left = 8
        Top = 8
        Width = 27
        Height = 13
        Caption = 'Lager'
      end
      object Label8: TLabel
        Left = 240
        Top = 8
        Width = 36
        Height = 13
        Caption = 'Bereich'
      end
      object Label9: TLabel
        Left = 8
        Top = 56
        Width = 55
        Height = 13
        Caption = 'Lagerpl'#228'tze'
      end
      object LagerComboBox3: TComboBoxPro
        Left = 8
        Top = 24
        Width = 209
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 0
        OnChange = LagerComboBox3Change
      end
      object LBComboBox2: TComboBoxPro
        Left = 240
        Top = 24
        Width = 281
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 1
        OnChange = LBComboBoxChange
      end
      object LPListBox: TListBox
        Left = 8
        Top = 72
        Width = 145
        Height = 536
        Style = lbOwnerDrawFixed
        Anchors = [akLeft, akTop, akBottom]
        DragMode = dmAutomatic
        ItemHeight = 13
        MultiSelect = True
        TabOrder = 2
        OnDragOver = LPListBoxDragOver
        OnDrawItem = LPListBoxDrawItem
      end
      object AddKommLPButton: TButton
        Left = 168
        Top = 80
        Width = 41
        Height = 25
        Caption = '>>'
        TabOrder = 3
        OnClick = AddKommLPButtonClick
      end
      object KommDBGrid: TDBGridPro
        Left = 224
        Top = 72
        Width = 456
        Height = 507
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = KommDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit, dgMultiSelect]
        PopupMenu = KommDBGridPopupMenu
        ReadOnly = True
        TabOrder = 5
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        OnDrawColumnCell = DBGridDrawColumnCell
        OnDblClick = KommDBGridDblClick
        OnDragDrop = KommDBGridDragDrop
        OnDragOver = KommDBGridDragOver
        OnKeyPress = DBGridHotTrackKeyPress
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'MS Sans Serif'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoCheckBoxSelect, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 29
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
        OnColumnSort = KommDBGridColumnSort
      end
      object RemoveKommLPButton: TButton
        Left = 169
        Top = 128
        Width = 41
        Height = 25
        Caption = '<<'
        TabOrder = 4
        OnClick = RemoveKommLPButtonClick
      end
      object KommLPPrintButton: TButton
        Left = 605
        Top = 590
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Drucken...'
        TabOrder = 6
        OnClick = KommLPPrintButtonClick
      end
    end
  end
  object Button1: TButton
    Left = 628
    Top = 663
    Width = 73
    Height = 26
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 1
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 580
    Top = 8
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    OnChangeLanguage = CompTranslateForm1ChangeLanguage
    Left = 548
    Top = 8
  end
  object LPDataSource: TDataSource
    DataSet = LPOraQuery
    OnDataChange = LPDataSourceDataChange
    Left = 516
    Top = 8
  end
  object KommADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 612
    Top = 8
  end
  object KommDataSource: TDataSource
    DataSet = KommADOQuery
    Left = 484
    Top = 8
  end
  object LBQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 452
    Top = 48
  end
  object LBDataSource: TDataSource
    DataSet = LBQuery
    OnDataChange = LBDataSourceDataChange
    Left = 412
    Top = 48
  end
  object KommDBGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    Left = 672
    Top = 8
    object Einlagerplatzanlegen1: TMenuItem
      Caption = 'Einlagerplatz anlegen...'
      OnClick = Einlagerplatzanlegen1Click
    end
    object N3: TMenuItem
      Caption = '-'
    end
  end
  object LagerPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = LagerPopupMenuPopup
    Left = 256
    Top = 168
    object LagerCopyColMenuItem: TMenuItem
      Caption = 'Kopieren'
      OnClick = StringGridCopyColMenuItemClick
    end
    object LagerColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      OnClick = StringGridColOptimalMenuItemClick
    end
    object N4: TMenuItem
      Caption = '-'
    end
    object LagerCheckConfigMenuItem: TMenuItem
      Caption = 'Konfiguration '#252'berpr'#252'fen...'
      OnClick = LagerCheckConfigMenuItemClick
    end
  end
  object LBZoneDataSource: TDataSource
    DataSet = LBZoneQuery
    OnDataChange = LBZoneDataSourceDataChange
    Left = 412
    Top = 328
  end
  object LBZoneQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 452
    Top = 328
  end
  object LBZoneDBGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = LBZoneDBGridPopupMenuPopup
    Left = 264
    Top = 352
    object PrintLBZonenLabelMenuItem: TMenuItem
      Caption = 'Bezeichnung drucken...'
      ImageIndex = 3
      OnClick = PrintLBZonenLabelMenuItemClick
    end
  end
  object LPDBGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = LPDBGridPopupMenuPopup
    Left = 112
    Top = 232
    object ImportLPDataImportMenuItem: TMenuItem
      Caption = 'Import...'
      OnClick = ImportLPDataImportMenuItemClick
    end
    object ImportLPKommFolgeMenuItem: TMenuItem
      Caption = 'Import Komm-Folge...'
      OnClick = ImportLPKommFolgeMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object ExportLPDataMenuItem: TMenuItem
      Caption = 'Export...'
      ImageIndex = 12
      OnClick = ExportLPDataMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object LPCreateSameMenuItem: TMenuItem
      Caption = #196'hnlicher Platz anlegen...'
      OnClick = NeuLPButtonClick
    end
    object N5: TMenuItem
      Caption = '-'
    end
    object LPNotUniqueMenuItem: TMenuItem
      AutoCheck = True
      Caption = 'Nicht eindeutige Koordinaten'
      OnClick = LPNotUniqueMenuItemClick
    end
  end
  object LBDBGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    Left = 328
    Top = 160
    object PrintLBLabelMenuItem: TMenuItem
      Caption = 'Bezeichnung drucken...'
      ImageIndex = 3
      OnClick = PrintLBLabelMenuItemClick
    end
  end
  object LPOraQuery: TSmartQuery
    ReadOnly = True
    Left = 408
    Top = 216
  end
  object LPADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 644
    Top = 8
  end
  object LPArtikelDataSource: TDataSource
    DataSet = LPArtikelQuery
    OnDataChange = LBZoneDataSourceDataChange
    Left = 412
    Top = 360
  end
  object LPArtikelQuery: TSmartQuery
    ReadOnly = True
    Left = 448
    Top = 360
  end
  object LPArtikelDBGridPopupMenu: TPopupMenu
    Left = 440
    Top = 560
    object LPARNachschubMenuItem: TMenuItem
      Caption = 'Nachschub-Parameter'
      OnClick = LPARNachschubMenuItemClick
    end
  end
end
