object ValueServiceFrame: TValueServiceFrame
  Left = 0
  Top = 0
  Width = 482
  Height = 150
  Anchors = [akLeft, akBottom]
  TabOrder = 0
  DesignSize = (
    482
    150)
  object ServiceLabel: TLabel
    Left = 8
    Top = 48
    Width = 103
    Height = 19
    Caption = 'ServiceLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object ServiceInfoLabel: TLabel
    Left = 8
    Top = 80
    Width = 80
    Height = 13
    Caption = 'ServiceInfoLabel'
  end
  object Bevel1: TBevel
    Left = 4
    Top = 32
    Width = 476
    Height = 8
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel2: TBevel
    Left = 3
    Top = 145
    Width = 476
    Height = 8
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object Label3: TLabel
    Left = 8
    Top = 8
    Width = 46
    Height = 19
    Caption = 'Artikel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentFont = False
  end
  object ArtikelLabel: TLabel
    Left = 104
    Top = 8
    Width = 83
    Height = 19
    Caption = 'ArtikelLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentFont = False
  end
  object DoneCheckBox: TCheckBox
    Left = 8
    Top = 111
    Width = 249
    Height = 17
    Anchors = [akLeft, akBottom]
    Caption = 'Erledigt'
    TabOrder = 0
  end
  object DoneButton: TButton
    Left = 400
    Top = 107
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Erledigt'
    TabOrder = 1
    OnClick = DoneButtonClick
  end
end
