object EditArtikelListungForm: TEditArtikelListungForm
  Left = 617
  Top = 239
  BorderStyle = bsDialog
  Caption = 'Artikellistung bearbeiten'
  ClientHeight = 595
  ClientWidth = 362
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    362
    595)
  TextHeight = 13
  object Label2: TLabel
    Left = 8
    Top = 52
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Bevel1: TBevel
    Left = 6
    Top = 104
    Width = 349
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 32
    Height = 13
    Caption = 'Artikel:'
  end
  object Bevel2: TBevel
    Left = 5
    Top = 40
    Width = 349
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object ArtikelLabel: TLabel
    Left = 56
    Top = 8
    Width = 55
    Height = 13
    Caption = 'ArtikelLabel'
  end
  object Bevel5: TBevel
    Left = 5
    Top = 153
    Width = 349
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object OkButton: TButton
    Left = 190
    Top = 565
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 1
  end
  object AbortButton: TButton
    Left = 278
    Top = 565
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 2
    TabOrder = 2
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 68
    Width = 345
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 0
    OnChange = LagerComboBoxChange
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 168
    Width = 346
    Height = 389
    ActivePage = NachschubTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 3
    Visible = False
    object LagerungTabSheet: TTabSheet
      Caption = 'Lagerung'
      DesignSize = (
        338
        361)
      object Label5: TLabel
        Left = 8
        Top = 56
        Width = 79
        Height = 13
        Caption = 'Art der Lagerung'
      end
      object Label3: TLabel
        Left = 8
        Top = 313
        Width = 110
        Height = 13
        Anchors = [akLeft, akBottom]
        Caption = 'Automatisch LEs bilden'
        ExplicitTop = 156
      end
      object Label4: TLabel
        Left = 8
        Top = 168
        Width = 49
        Height = 13
        Caption = 'Lagerplatz'
      end
      object Label6: TLabel
        Left = 8
        Top = 124
        Width = 62
        Height = 13
        Caption = 'Lagerbereich'
      end
      object Bevel3: TBevel
        Left = 5
        Top = 112
        Width = 330
        Height = 9
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object Bevel4: TBevel
        Left = 6
        Top = 298
        Width = 330
        Height = 9
        Anchors = [akLeft, akRight, akBottom]
        Shape = bsTopLine
        ExplicitTop = 718
      end
      object StoreArtComboBox: TComboBoxPro
        Left = 8
        Top = 72
        Width = 325
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 0
        OnChange = StoreArtComboBoxChange
      end
      object AutoLTComboBox: TComboBoxPro
        Left = 8
        Top = 329
        Width = 325
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akRight, akBottom]
        ItemHeight = 15
        TabOrder = 3
      end
      object LPListBox: TListBox
        Left = 8
        Top = 184
        Width = 325
        Height = 123
        Anchors = [akLeft, akTop, akRight, akBottom]
        Enabled = False
        ItemHeight = 13
        TabOrder = 2
        TabWidth = 1
      end
      object LBComboBox: TComboBoxPro
        Left = 8
        Top = 140
        Width = 325
        Height = 22
        Style = csOwnerDrawFixed
        Enabled = False
        TabOrder = 1
        OnChange = LBComboBoxChange
      end
    end
    object NachschubTabSheet: TTabSheet
      Caption = 'Nachschub'
      ImageIndex = 1
      OnShow = NachschubTabSheetShow
      DesignSize = (
        338
        361)
      object Label7: TLabel
        Left = 8
        Top = 8
        Width = 96
        Height = 13
        Caption = 'Art des Nachschubs'
      end
      object NachArtComboBox: TComboBoxPro
        Left = 8
        Top = 24
        Width = 325
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ColumeCount = 1
        ItemHeight = 15
        ItemIndex = 0
        TabOrder = 0
        Text = 'Palette'
        Items.Strings = (
          'Palette'
          'VPE'
          'VE')
      end
      object NachsGroupBox: TGroupBox
        Left = 10
        Top = 51
        Width = 325
        Height = 123
        Anchors = [akLeft, akTop, akRight]
        Caption = 'NachsGroupBox'
        TabOrder = 2
        object Label8: TLabel
          Left = 8
          Top = 33
          Width = 135
          Height = 13
          Caption = 'Min. Bestand f'#252'r Nachschub'
        end
        object Label9: TLabel
          Left = 8
          Top = 75
          Width = 152
          Height = 13
          Caption = 'Min. Anzahl VPE f'#252'r Nachschub'
        end
        object Label10: TLabel
          Left = 8
          Top = 101
          Width = 155
          Height = 13
          Caption = 'Max. Anzahl VPE f'#252'r Nachschub'
        end
        object BesMengeEdit: TEdit
          Left = 186
          Top = 30
          Width = 60
          Height = 21
          TabOrder = 0
          Text = 'BesMengeEdit'
          OnKeyPress = IntEditKeyPress
        end
        object MinNachMengeEdit: TEdit
          Left = 186
          Top = 72
          Width = 60
          Height = 21
          TabOrder = 1
          Text = 'MinNachMengeEdit'
          OnKeyPress = IntEditKeyPress
        end
        object MaxNachMengeEdit: TEdit
          Left = 186
          Top = 98
          Width = 60
          Height = 21
          TabOrder = 2
          Text = 'MaxNachMengeEdit'
          OnKeyPress = IntEditKeyPress
        end
      end
      object AutoNachCheckBox: TCheckBox
        Left = 16
        Top = 51
        Width = 193
        Height = 17
        AllowGrayed = True
        Caption = 'Automatische Nachschubsteuerung'
        TabOrder = 1
        OnClick = AutoNachCheckBoxClick
      end
    end
  end
  object LeergutCheckBox: TCheckBox
    Left = 8
    Top = 119
    Width = 97
    Height = 17
    Caption = 'Ist Leergut'
    TabOrder = 4
  end
end
