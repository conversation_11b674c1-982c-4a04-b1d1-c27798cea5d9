object ConfirmForm: TConfirmForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'ConfirmForm'
  ClientHeight = 368
  ClientWidth = 428
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    428
    368)
  PixelsPerInch = 96
  TextHeight = 13
  object Label7: TLabel
    Left = 8
    Top = 288
    Width = 98
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Grund der '#196'nderung'
  end
  object Button1: TButton
    Left = 166
    Top = 338
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Button1'
    TabOrder = 5
  end
  object Button2: TButton
    Left = 254
    Top = 338
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Button2'
    TabOrder = 6
  end
  object AbortButton: TButton
    Left = 343
    Top = 338
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 7
  end
  object LabelPanel: TPanel
    Left = 0
    Top = 41
    Width = 428
    Height = 82
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    Visible = False
    DesignSize = (
      428
      82)
    object ConfirmLabel: TLabel
      Left = 8
      Top = 16
      Width = 410
      Height = 57
      Alignment = taCenter
      Anchors = [akLeft, akTop, akRight]
      AutoSize = False
      Caption = 'ConfirmLabel'
      ExplicitWidth = 620
    end
    object Bevel4: TBevel
      Left = 8
      Top = 77
      Width = 410
      Height = 6
      Shape = bsTopLine
    end
  end
  object EditPanel: TPanel
    Left = 0
    Top = 123
    Width = 428
    Height = 54
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    object Label1: TLabel
      Left = 8
      Top = 3
      Width = 32
      Height = 13
      Caption = 'Menge'
    end
    object Label2: TLabel
      Left = 176
      Top = 2
      Width = 38
      Height = 13
      Caption = 'Gewicht'
    end
    object EinheitLabel: TLabel
      Left = 99
      Top = 22
      Width = 57
      Height = 13
      Caption = 'EinheitLabel'
    end
    object Label3: TLabel
      Left = 264
      Top = 22
      Width = 11
      Height = 13
      Caption = 'kg'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 46
      Width = 410
      Height = 6
      Shape = bsTopLine
    end
    object MengeEdit: TEdit
      Left = 8
      Top = 19
      Width = 55
      Height = 21
      TabOrder = 0
      Text = '0'
      OnChange = MengeEditChange
      OnExit = MengeEditExit
    end
    object GewichtEdit: TEdit
      Left = 176
      Top = 19
      Width = 81
      Height = 21
      TabOrder = 1
      Text = 'GewichtEdit'
    end
    object MengeUpDown: TIntegerUpDown
      Left = 63
      Top = 19
      Width = 16
      Height = 21
      Associate = MengeEdit
      TabOrder = 2
    end
  end
  object ArtikelPanel: TPanel
    Left = 0
    Top = 0
    Width = 428
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object ArtikelLabel: TLabel
      Left = 8
      Top = 8
      Width = 55
      Height = 13
      Caption = 'ArtikelLabel'
    end
    object Bevel2: TBevel
      Left = 8
      Top = 26
      Width = 410
      Height = 6
      Shape = bsTopLine
    end
  end
  object MHDPanel: TPanel
    Left = 0
    Top = 177
    Width = 428
    Height = 54
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    Visible = False
    object Label4: TLabel
      Left = 8
      Top = 3
      Width = 22
      Height = 13
      Caption = 'MHD'
    end
    object Label5: TLabel
      Left = 176
      Top = 2
      Width = 35
      Height = 13
      Caption = 'Charge'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 51
      Width = 410
      Height = 6
      Shape = bsTopLine
    end
    object Label6: TLabel
      Left = 89
      Top = 3
      Width = 66
      Height = 13
      Caption = 'Herstelldatum'
    end
    object MHDEdit: TEdit
      Left = 8
      Top = 19
      Width = 72
      Height = 21
      TabOrder = 0
      Text = 'MHDEdit'
      OnExit = MHDEditExit
    end
    object ChargeEdit: TEdit
      Left = 176
      Top = 19
      Width = 153
      Height = 21
      TabOrder = 2
      Text = 'ChargeEdit'
    end
    object HDTEdit: TEdit
      Left = 89
      Top = 19
      Width = 72
      Height = 21
      TabOrder = 1
      Text = 'HDTEdit'
      OnExit = HDTEditExit
    end
  end
  object CategoryPanel: TPanel
    Left = 0
    Top = 231
    Width = 428
    Height = 52
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      428
      52)
    object Label8: TLabel
      Left = 8
      Top = 6
      Width = 101
      Height = 13
      Caption = 'Bestandsqualifikation'
    end
    object CategoryComboBox: TComboBox
      Left = 8
      Top = 22
      Width = 410
      Height = 21
      Style = csDropDownList
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 0
      TabOrder = 0
    end
  end
  object GrundComboBox: TComboBox
    Left = 8
    Top = 304
    Width = 410
    Height = 21
    Style = csDropDownList
    Anchors = [akLeft, akRight, akBottom]
    ItemHeight = 13
    MaxLength = 64
    TabOrder = 8
    OnChange = GrundComboBoxChange
    Items.Strings = (
      'Ware mangelhaft')
  end
end
