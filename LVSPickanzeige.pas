unit LVSPickanzeige;

interface

uses Classes, DB, ADODB;

function SetPickAnzeigeAus   (const Zone : String; const Farbe : Integer = -1; const FachNr : Integer = -1) : Integer;
function SetPickAnzeigeEin   (const Zone : String; const Farbe : Integer = -1; const FachNr : Integer = -1) : Integer;
function SetPickAnzeigeBlink (const Zone : String; const Farbe : Integer = -1; const FachNr : Integer = -1) : Integer;

implementation

uses  SysUtils, DatenModul, Variants;

//******************************************************************************
//* Function Name: ChangeAuftragKommText
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetPickAnzeigeAus (const Zone : String; const Farbe : Integer; const FachNr : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PICKANZEIGE.SET_FACH_AUS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pZone',ftString,pdInput, 16, Zone);

    if (FachNr = -1) then
      Parameters.CreateParameter('pFachNr',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pFachNr',ftInteger,pdInput, 12, FachNr);

    if (Farbe = -1) then
      Parameters.CreateParameter('pFarbe',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pFarbe',ftInteger,pdInput, 12, Farbe);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: ChangeAuftragKommText
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetPickAnzeigeEin (const Zone : String; const Farbe : Integer = -1; const FachNr : Integer = -1) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PICKANZEIGE.SET_FACH_EIN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pZone',ftString,pdInput, 16, Zone);

    if (FachNr = -1) then
      Parameters.CreateParameter('pFachNr',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pFachNr',ftInteger,pdInput, 12, FachNr);

    if (Farbe = -1) then
      Parameters.CreateParameter('pFarbe',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pFarbe',ftInteger,pdInput, 12, Farbe);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;
//******************************************************************************
//* Function Name: ChangeAuftragKommText
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetPickAnzeigeBlink (const Zone : String; const Farbe : Integer = -1; const FachNr : Integer = -1) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PICKANZEIGE.SET_FACH_BLINK';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pZone',ftString,pdInput, 16, Zone);

    if (FachNr = -1) then
      Parameters.CreateParameter('pFachNr',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pFachNr',ftInteger,pdInput, 12, FachNr);

    if (Farbe = -1) then
      Parameters.CreateParameter('pFarbe',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pFarbe',ftInteger,pdInput, 12, Farbe);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

end.
