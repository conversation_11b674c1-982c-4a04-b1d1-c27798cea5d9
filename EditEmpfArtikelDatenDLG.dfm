object EditEmpfArtikelDatenDLGForm: TEditEmpfArtikelDatenDLGForm
  Left = 0
  Top = 0
  Caption = 'EditEmpfArtikelDatenDLG'
  ClientHeight = 555
  ClientWidth = 727
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object EmpfPanel: TPanel
    Left = 0
    Top = 41
    Width = 727
    Height = 254
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    ExplicitHeight = 257
    object EmpfKopfPanel: TPanel
      Left = 0
      Top = 0
      Width = 727
      Height = 49
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 0
      ExplicitWidth = 796
      DesignSize = (
        727
        49)
      object Label12: TLabel
        Left = 8
        Top = 17
        Width = 82
        Height = 13
        Caption = 'Warenempf. Nr.:'
      end
      object Bevel1: TBevel
        Left = 8
        Top = 2
        Width = 710
        Height = 4
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
        ExplicitWidth = 601
      end
      object EmpfNameLabel: TLabel
        Left = 224
        Top = 17
        Width = 76
        Height = 13
        Caption = 'EmpfNameLabel'
      end
      object EmpfNrEdit: TEdit
        Left = 96
        Top = 15
        Width = 121
        Height = 21
        TabOrder = 0
        Text = 'EmpfNrEdit'
      end
      object ShowEmpfGridButton: TButton
        Left = 518
        Top = 12
        Width = 200
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Warenempf'#228'nger anzeigen...'
        TabOrder = 1
        TabStop = False
        OnClick = ShowEmpfGridButtonClick
        ExplicitLeft = 587
      end
    end
    object EmpfGridPanel: TPanel
      Left = 0
      Top = 49
      Width = 727
      Height = 205
      Align = alClient
      BevelOuter = bvNone
      Color = clSkyBlue
      ParentBackground = False
      TabOrder = 1
      Visible = False
      ExplicitWidth = 796
      ExplicitHeight = 172
      DesignSize = (
        727
        205)
      object Label1: TLabel
        Left = 8
        Top = 10
        Width = 84
        Height = 13
        Caption = 'Warenempf'#228'nger'
      end
      object AllEmpfCheckBox: TCheckBox
        Left = 543
        Top = 7
        Width = 175
        Height = 17
        Anchors = [akTop, akRight]
        BiDiMode = bdLeftToRight
        Caption = 'Alle Warenempf'#228'nger anzeigen'
        ParentBiDiMode = False
        TabOrder = 0
        ExplicitLeft = 612
      end
      object KundenDBGrid: TDBGridPro
        Left = 8
        Top = 29
        Width = 711
        Height = 173
        Anchors = [akLeft, akTop, akRight, akBottom]
        BiDiMode = bdLeftToRight
        DataSource = KundenDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ParentBiDiMode = False
        ReadOnly = True
        TabOrder = 1
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'MS Sans Serif'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
    end
  end
  object GroupBox1: TGroupBox
    AlignWithMargins = True
    Left = 6
    Top = 301
    Width = 715
    Height = 203
    Margins.Left = 6
    Margins.Top = 6
    Margins.Right = 6
    Margins.Bottom = 6
    Align = alBottom
    Caption = 'Artikelinfos f'#252'r diesen Warenempf'#228'nger'
    TabOrder = 1
    DesignSize = (
      715
      203)
    object Label2: TLabel
      Left = 16
      Top = 104
      Width = 44
      Height = 13
      Caption = 'Artikelnr.'
    end
    object Label3: TLabel
      Left = 160
      Top = 104
      Width = 32
      Height = 13
      Caption = 'Einheit'
    end
    object Label4: TLabel
      Left = 16
      Top = 152
      Width = 50
      Height = 13
      Caption = 'Artikeltext'
    end
    object ArtikelNrEdit: TEdit
      Left = 16
      Top = 120
      Width = 121
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'ArtikelNrEdit'
    end
    object ArtikelEinheitEdit: TEdit
      Left = 160
      Top = 120
      Width = 57
      Height = 21
      MaxLength = 16
      TabOrder = 1
      Text = 'ArtikelEinheitEdit'
    end
    object ArtikelTextEdit: TEdit
      Left = 16
      Top = 168
      Width = 688
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 2
      Text = 'ArtikelTextEdit'
    end
  end
  object FussPanel: TPanel
    Left = 0
    Top = 510
    Width = 727
    Height = 45
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    ExplicitTop = 542
    ExplicitWidth = 796
    DesignSize = (
      727
      45)
    object OkButton: TButton
      Left = 518
      Top = 12
      Width = 112
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      Default = True
      ModalResult = 1
      TabOrder = 0
      ExplicitLeft = 587
    end
    object AbortButton: TButton
      Left = 643
      Top = 12
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
      ExplicitLeft = 712
    end
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 727
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    object Label5: TLabel
      Left = 8
      Top = 8
      Width = 34
      Height = 13
      Caption = 'Artikel:'
    end
    object ArtikelLabel: TLabel
      Left = 80
      Top = 8
      Width = 55
      Height = 13
      Caption = 'ArtikelLabel'
    end
  end
  object KundenQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 112
    Top = 704
  end
  object KundenDataSource: TDataSource
    DataSet = KundenQuery
    OnDataChange = KundenDataSourceDataChange
    Left = 152
    Top = 704
  end
end
