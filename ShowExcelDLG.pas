unit ShowExcelDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, nExcel, Grids, DBGrids, DBGrid2Excel, StringGridPro, Menus;

type
  TShowExcelForm = class(TForm)
    ExcelStringGrid: TStringGridPro;
    PopupMenu1: TPopupMenu;
    procedure FormShow(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure ExcelStringGridDrawCell(Sender: TObject; ACol, ARow: Integer;
      Rect: TRect; State: TGridDrawState);
  private
    fFileName : String;
    fWorkBook : IXLSWorkbook;
  public
    property FileName : String read fFileName write fFileName;
  end;

implementation

{$R *.dfm}

uses
  ConfigModul;

type
 TCellParam = class (TPersistent)
 public
   FontName   : String;
   FontSize   : Integer;
   IsBold     : Boolean;
   HAlignment : XlHAlign;
   ValueType  : TVarType;
 end;

procedure TShowExcelForm.ExcelStringGridDrawCell(Sender: TObject; ACol, ARow: Integer; Rect: TRect; State: TGridDrawState);
var
  al   : XlHAlign;
  vofs,
  hofs : Integer;
begin
  if Assigned (ExcelStringGrid.Objects [ACol, ARow]) then begin
    ExcelStringGrid.Canvas.Brush.Color := ExcelStringGrid.Color;
    ExcelStringGrid.Canvas.FillRect(Rect);

    ExcelStringGrid.Canvas.Font.Name := TCellParam (ExcelStringGrid.Objects [ACol, ARow]).FontName;
    ExcelStringGrid.Canvas.Font.Size := TCellParam (ExcelStringGrid.Objects [ACol, ARow]).FontSize;

    if (TCellParam (ExcelStringGrid.Objects [ACol, ARow]).IsBold) then
      ExcelStringGrid.Canvas.Font.Style := ExcelStringGrid.Canvas.Font.Style + [fsBold]
    else
      ExcelStringGrid.Canvas.Font.Style := ExcelStringGrid.Canvas.Font.Style - [fsBold];

    case TCellParam (ExcelStringGrid.Objects [ACol, ARow]).ValueType of
        varByte,
        {$IFNDEF D45}
        varWord,
        varLongWord,
        varInt64,
        varShortInt,
        {$ENDIF}
        {$IFDEF D2009}
        varUInt64,
        {$ENDIF}
        varSmallint,
        varInteger,
        varSingle,
        varDouble,
        varCurrency :  al := xlHAlignRight;
      else
        al := TCellParam (ExcelStringGrid.Objects [ACol, ARow]).HAlignment;
    end;

    hofs := Rect.Bottom - Rect.Top - ExcelStringGrid.Canvas.TextHeight ('A');

    if (al = xlHAlignRight) then
      vofs := (Rect.Right - Rect.Left) - 2 - ExcelStringGrid.Canvas.TextWidth (ExcelStringGrid.Cells[ACol, ARow])
    else if (al = xlHAlignCenter) then
      vofs := (Rect.Right - Rect.Left - ExcelStringGrid.Canvas.TextWidth (ExcelStringGrid.Cells[ACol, ARow])) div 2
    else
      vofs := 2;

    ExcelStringGrid.Canvas.TextRect(Rect, Rect.Left + vofs, Rect.Top + hofs, ExcelStringGrid.Cells[ACol, ARow]);
  end else if (ACol = 0) then begin
    ExcelStringGrid.Canvas.Font.Name := 'Calibri';
    ExcelStringGrid.Canvas.Font.Size := 11;

    hofs := Rect.Bottom - Rect.Top - ExcelStringGrid.Canvas.TextHeight ('A');
    vofs := (Rect.Right - Rect.Left - ExcelStringGrid.Canvas.TextWidth (ExcelStringGrid.Cells[ACol, ARow])) div 2;

    ExcelStringGrid.Canvas.TextRect(Rect, Rect.Left + vofs, Rect.Top + hofs, ExcelStringGrid.Cells[ACol, ARow])
  end else if (ARow = 0) then begin
    ExcelStringGrid.Canvas.Font.Name := 'Calibri';
    ExcelStringGrid.Canvas.Font.Size := 11;

    hofs := (Rect.Bottom - Rect.Top - ExcelStringGrid.Canvas.TextHeight ('A')) div 2;
    vofs := (Rect.Right - Rect.Left - ExcelStringGrid.Canvas.TextWidth (ExcelStringGrid.Cells[ACol, ARow])) div 2;

    ExcelStringGrid.Canvas.TextRect(Rect, Rect.Left + vofs, Rect.Top + hofs, ExcelStringGrid.Cells[ACol, ARow]);
  end else begin
    ExcelStringGrid.Font.Name := 'Courier';
    ExcelStringGrid.Font.Size := 10;

    ExcelStringGrid.Canvas.TextRect(Rect, Rect.Left + 2, Rect.Top + 2, ExcelStringGrid.Cells[ACol, ARow]);
  end;
end;

procedure TShowExcelForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

procedure TShowExcelForm.FormCreate(Sender: TObject);
begin
  fWorkBook := Nil;
  fFileName := '';
end;

procedure TShowExcelForm.FormDestroy(Sender: TObject);
begin
  if Assigned (fWorkBook) then begin
    fWorkBook.Close;
  end;
end;

procedure TShowExcelForm.FormShow(Sender: TObject);
var
  x,
  y,
  lastheight : Integer;
  fext       : String;
  p          : TCellParam;
  c          : IXLSRange;
  csvstream  : TFileStream;
  eol,
  stop       : boolean;
  csvline    : String;
  ch         : Char;
  readsize   : Integer;
  valuelist  : TStringList;
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.RestoreFormInfo (Self);

  Caption := fFileName;

  if (Length (fFileName) > 0) and FileExists (fFileName) then begin
    fext := lowercase (ExtractFileExt (fFileName));

    Screen.Cursor := crHourGlass;

    try
      if (fext = '.csv') then begin
        try
          csvstream := TFileStream.Create (fFileName, fmOpenRead or fmShareDenyWrite);
        except
          csvstream := Nil;
        end;

        if not Assigned (csvstream) then
        else begin
          valuelist := TStringList.Create;

          valuelist.StrictDelimiter := True;
          valuelist.Delimiter := ';';

          y := ExcelStringGrid.FixedRows;
          ExcelStringGrid.ColCount := ExcelStringGrid.FixedCols + 1;

          stop := False;

          repeat
            eol := False;
            csvline := '';

            repeat
              readsize := csvstream.Read (ch, 1);

              if(ch = #13) then
                eol := true
              else if (ch = #10) then
              else
                csvline := csvline + ch;
            until (eol or (readsize = 0));

            valuelist.DelimitedText := csvline;

            if (ExcelStringGrid.ColCount < ExcelStringGrid.FixedCols + valuelist.Count) then
              ExcelStringGrid.ColCount := ExcelStringGrid.FixedCols + valuelist.Count;

            for x := 0 to valuelist.Count - 1 do
              ExcelStringGrid.Cells [ExcelStringGrid.FixedCols + x, y] := valuelist [x];

            Inc (y);
          until (readsize = 0);

          ExcelStringGrid.RowCount := y;

          csvstream.Free;

          ExcelStringGrid.HeaderRows := 1;

          for x := 1 to ExcelStringGrid.ColCount - 1 do
            ExcelStringGrid.SetOptimalColWidth (x);
        end;
      end else if (fext = '.xls') or (fext = '.xlsx') then begin
        fWorkBook := TXLSWorkBook.Create;

        try
          fWorkBook.Open (fFileName);

          if Assigned (fWorkBook.ActiveSheet) then begin
            ExcelStringGrid.RowCount := fWorkBook.ActiveSheet.UsedRange.Rows.Count + 1;
            ExcelStringGrid.ColCount := fWorkBook.ActiveSheet.UsedRange.Columns.Count + 1;

            ExcelStringGrid.Font.Size := 10;

            if (fWorkBook.ActiveSheet.UsedRange.Rows.Count > 100000) then
              ExcelStringGrid.ColWidths [0] := 8+ExcelStringGrid.Canvas.TextWidth ('999999')
            else if (fWorkBook.ActiveSheet.UsedRange.Rows.Count > 10000) then
              ExcelStringGrid.ColWidths [0] := 8+ExcelStringGrid.Canvas.TextWidth ('99999')
            else if (fWorkBook.ActiveSheet.UsedRange.Rows.Count > 1000) then
              ExcelStringGrid.ColWidths [0] := 8+ExcelStringGrid.Canvas.TextWidth ('9999')
            else if (fWorkBook.ActiveSheet.UsedRange.Rows.Count > 100) then
              ExcelStringGrid.ColWidths [0] := 8+ExcelStringGrid.Canvas.TextWidth ('999')
            else
              ExcelStringGrid.ColWidths [0]  := 8+ExcelStringGrid.Canvas.TextWidth ('99');

            ExcelStringGrid.RowHeights [0] := Round (fWorkBook.ActiveSheet.StandardHeight * 96) div 72;

            for y := 1 to fWorkBook.ActiveSheet.UsedRange.Rows.Count do begin
              ExcelStringGrid.Cells [0, y] := IntToStr (y);
            end;

            for x := 1 to fWorkBook.ActiveSheet.UsedRange.Columns.Count do begin
              if (x < (ord ('Z') - ord ('A'))) then
                ExcelStringGrid.Cells [x, 0] := chr ((x - 1) + ord ('A'))
              else
                ExcelStringGrid.Cells [x, 0] := 'A'+chr ((x - 1 - (ord ('Z') - ord ('A'))) + ord ('A'));

              ExcelStringGrid.ColWidths [x] := fWorkBook.ActiveSheet.UsedRange.Columns.Item [x].WidthInPixels;
            end;

            lastheight := ExcelStringGrid.DefaultRowHeight;

            for y := 1 to fWorkBook.ActiveSheet.UsedRange.Rows.Count do begin
              if (fWorkBook.ActiveSheet.UsedRange [y, 1].Height > 0) then
                ExcelStringGrid.RowHeights [y] := Round (fWorkBook.ActiveSheet.UsedRange [y, 1].Height * 96) div 72
              else ExcelStringGrid.RowHeights [y] := Round (fWorkBook.ActiveSheet.StandardHeight * 96) div 72;

              for x := 1 to fWorkBook.ActiveSheet.UsedRange.Columns.Count do begin
                if not VarIsNull (fWorkBook.ActiveSheet.UsedRange [y, x].Value) then begin
                  if (ExcelStringGrid.RowHeights [y] < Round (fWorkBook.ActiveSheet.UsedRange [y,x].Font.Size * 96) div 72) then
                    ExcelStringGrid.RowHeights [y] := Round (fWorkBook.ActiveSheet.UsedRange [y,x].Font.Size * 96) div 72 + 8;

                  c := fWorkBook.ActiveSheet.UsedRange [y, x];

                  ExcelStringGrid.Cells [x, y] := fWorkBook.ActiveSheet.UsedRange [y, x].Value;

                  p:= TCellParam.Create;
                  p.FontName   := fWorkBook.ActiveSheet.UsedRange [y, x].Font.Name;
                  p.FontSize   := Round (fWorkBook.ActiveSheet.UsedRange [y, x].Font.Size);
                  p.IsBold     := fWorkBook.ActiveSheet.UsedRange [y, x].Font.Bold;
                  p.HAlignment := fWorkBook.ActiveSheet.UsedRange [y, x].HorizontalAlignment;
                  p.ValueType  := VarType (fWorkBook.ActiveSheet.UsedRange [y, x].Value);

                  ExcelStringGrid.Objects [x, y] := p;
                end;
              end;
            end;
          end;

          ExcelStringGrid.HeaderRows := 1;
        finally
          fWorkBook.Close;
        end;
      end;
    finally
      Screen.Cursor := crDefault;
    end;
  end;
end;

end.
