object InputLHMSerialForm: TInputLHMSerialForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Beh'#228'lter Seriennummer erfassen'
  ClientHeight = 352
  ClientWidth = 437
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    437
    352)
  TextHeight = 13
  object OkButton: TButton
    Left = 266
    Top = 319
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    Default = True
    ModalResult = 1
    TabOrder = 4
    ExplicitTop = 211
  end
  object AbortButton: TButton
    Left = 354
    Top = 319
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
    ExplicitTop = 211
  end
  object KopfPanel: TPanel
    Left = 0
    Top = 0
    Width = 437
    Height = 102
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      437
      102)
    object Bevel1: TBevel
      Left = 6
      Top = 96
      Width = 425
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 36
      Height = 13
      Caption = 'Kunden'
    end
    object Label2: TLabel
      Left = 8
      Top = 28
      Width = 37
      Height = 13
      Caption = 'Auftrag'
    end
    object Label7: TLabel
      Left = 8
      Top = 48
      Width = 79
      Height = 13
      Caption = 'Versandbeh'#228'lter'
    end
    object Label3: TLabel
      Left = 8
      Top = 68
      Width = 49
      Height = 13
      Caption = 'Versender'
    end
    object SpedLabel: TLabel
      Left = 104
      Top = 68
      Width = 58
      Height = 13
      Caption = 'SpedLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LTTypLabel: TLabel
      Left = 104
      Top = 48
      Width = 64
      Height = 13
      Caption = 'LTTypLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object AuftragLabel: TLabel
      Left = 104
      Top = 28
      Width = 73
      Height = 13
      Caption = 'AuftragLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object KundenLabel: TLabel
      Left = 104
      Top = 8
      Width = 72
      Height = 13
      Caption = 'KundenLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object NrPanel: TPanel
    Left = 0
    Top = 209
    Width = 437
    Height = 105
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      437
      105)
    object Bevel2: TBevel
      Left = 6
      Top = 97
      Width = 425
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label4: TLabel
      Left = 8
      Top = 50
      Width = 83
      Height = 13
      Caption = 'Beh'#228'lter-Nummer'
    end
    object InputLabel: TLabel
      Left = 8
      Top = 4
      Width = 83
      Height = 13
      Caption = 'Beh'#228'lter-Nummer'
    end
    object TrackingNrEdit: TEdit
      Left = 8
      Top = 66
      Width = 421
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 1
      Text = 'TrackingNrEdit'
    end
    object LHMSerialEdit: TEdit
      Left = 8
      Top = 20
      Width = 421
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      Text = 'LHMSerialEdit'
    end
  end
  object SpedPanel: TPanel
    Left = 0
    Top = 102
    Width = 437
    Height = 66
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    Visible = False
    DesignSize = (
      437
      66)
    object Label5: TLabel
      Left = 8
      Top = 4
      Width = 44
      Height = 13
      Caption = 'Spedition'
    end
    object Label8: TLabel
      Left = 240
      Top = 4
      Width = 37
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'Produkt'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 60
      Width = 423
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 76
    end
    object SpedComboBox: TComboBoxPro
      Left = 8
      Top = 20
      Width = 209
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 0
      OnChange = SpedComboBoxChange
    end
    object SpedProduktComboBox: TComboBoxPro
      Left = 240
      Top = 20
      Width = 189
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akTop, akRight]
      ItemHeight = 15
      TabOrder = 1
    end
  end
  object VersenderPanel: TPanel
    Left = 0
    Top = 168
    Width = 437
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      437
      41)
    object Label6: TLabel
      Left = 8
      Top = 0
      Width = 49
      Height = 13
      Caption = 'Versender'
    end
    object VersenderEdit: TEdit
      Left = 8
      Top = 16
      Width = 421
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      Text = 'VersenderEdit'
    end
  end
end
