unit SelectSpeditionTouchDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls;

type
  TSelectSpeditionTouchForm = class(TForm)
    BottonPanel: TPanel;
    TopPanel: TPanel;
    AbortButton: TButton;
    ListPanel: TPanel;
    TestPanel: TPanel;
    Bevel1: TBevel;
    Bevel2: TBevel;
    procedure FormShow(Sender: TObject);
    procedure PanelSpedClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    fRefMand      : Integer;
    fRefLager     : Integer;
    fRefSped      : Integer;
    fRefPackplatz : Integer;
    fSpedName     : String;
  public
    property RefSped      : Integer read fRefSped      write fRefSped;
    property RefMand      : Integer read fRefMand      write fRefMand;
    property RefLager     : Integer read fRefLager     write fRefLager;
    property RefPackplatz : Integer read fRefPackplatz write fRefPackplatz;
    property SpedName     : String read fSpedName;
  end;

implementation

{$R *.dfm}

uses
  DB, ADODB, VCLUtilitys, FrontendUtils, ConfigModul, DatenModul, LVSGlobalDaten,
  ErrorTracking, SprachModul, ResourceText;

procedure TSelectSpeditionTouchForm.FormCreate(Sender: TObject);
begin
  fSpedName     := '';
  fRefMand      := -1;
  fRefSped      := -1;
  fRefLager     := -1;
  fRefPackplatz := -1;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, TestPanel);
  {$endif}
end;

procedure TSelectSpeditionTouchForm.FormShow(Sender: TObject);
var
  formsize : Integer;
  panel : TPanel;
  query : TADOQuery;
begin
  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (RefPackplatz > 0) then begin
      query.SQL.Add ('select sped.* from V_SPEDITIONEN sped, V_WA_PACKPLATZ_REL_SPED rel where sped.STATUS in (''ANG'',''AKT'') and sped.REF_MAND=:ref_mand and sped.REF=rel.REF_SPED and rel.REF_PACKPLATZ=:ref_pack order by rel.REIHENFOLGE');
      query.Parameters.ParamByName('ref_mand').Value := RefMand;
      query.Parameters.ParamByName('ref_pack').Value := RefPackplatz;
    end else if (fRefLager > 0) then begin
      query.SQL.Add ( 'select sped.* from V_SPEDITIONEN sped where sped.STATUS in (''ANG'',''AKT'') and sped.REF_MAND=:ref_mand and (sped.REF_LAGER=:ref_lager_1 or (sped.REF_LAGER is null'
                     +'and sped.REF_LOCATION=(select REF_LOCATION from V_LOCATION_REL_LAGER where REF_LAGER=:ref_lager_2))) order by sped.NAME'
                    );
      query.Parameters.ParamByName('ref_mand').Value := RefMand;
      query.Parameters.ParamByName('ref_lager_1').Value := fRefLager;
      query.Parameters.ParamByName('ref_lager_2').Value := fRefLager;
    end else begin
      query.SQL.Add ('select sped.* from V_SPEDITIONEN sped where sped.STATUS in (''ANG'',''AKT'') and sped.REF_MAND=:ref_mand and sped.REF_LOCATION=:ref_loc order by sped.NAME');
      query.Parameters.ParamByName('ref_mand').Value := RefMand;
      query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
    end;

    query.Open;

    formsize := 0;

    while not (query.Eof) do begin
      panel := TPanel.Create (Self);
      panel.Parent := ListPanel;
      panel.Align := alTop;

      panel.Height     := TestPanel.Height;
      panel.BevelInner := TestPanel.BevelInner;
      panel.BevelKind  := TestPanel.BevelKind;
      panel.BevelOuter := TestPanel.BevelOuter;

      panel.Font.Assign (TestPanel.Font);

      if (fRefSped > 0) and (fRefSped = query.FieldByName('REF').AsInteger) then begin
        fSpedName := query.FieldByName('NAME').AsString;

        panel.ParentBackground := False;
        panel.Color := clLime;
        panel.Font.Style := panel.Font.Style + [fsBold];
      end;

      panel.Tag     := query.FieldByName('REF').AsInteger;
      panel.Caption := query.FieldByName('NAME').AsString;
      panel.OnClick := PanelSpedClick;

      formsize := formsize + panel.Height;

      query.Next;
    end;

    query.Close;

    ClientHeight := TopPanel.Height + BottonPanel.Height + formsize;
  finally
    query.Free;
  end;
end;

procedure TSelectSpeditionTouchForm.PanelSpedClick(Sender: TObject);
begin
  fSpedName := (Sender as TPanel).Caption;
  RefSped   := (Sender as TPanel).Tag;

  ModalResult := mrOk;
end;

end.
