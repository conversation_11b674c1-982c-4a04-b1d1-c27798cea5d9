object TouchPickStationForm: TTouchPickStationForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Pickstation'
  ClientHeight = 846
  ClientWidth = 1441
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnShow = FormShow
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 1441
    Height = 89
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label1: TLabel
      Left = 16
      Top = 16
      Width = 96
      Height = 25
      Caption = 'Auftragnr.'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object Label2: TLabel
      Left = 16
      Top = 48
      Width = 59
      Height = 25
      Caption = 'Kunde'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object WarenempfLabel: TLabel
      Left = 160
      Top = 48
      Width = 158
      Height = 25
      Caption = 'WarenempfLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object AuftragNrLabel: TLabel
      Left = 160
      Top = 16
      Width = 141
      Height = 25
      Caption = 'AuftragNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 768
    Width = 1441
    Height = 78
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      1441
      78)
    object CloseButton: TButton
      Left = 1240
      Top = 16
      Width = 191
      Height = 49
      Anchors = [akTop, akRight]
      Caption = 'Schlie'#223'en'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ModalResult = 1
      ParentFont = False
      TabOrder = 0
    end
  end
  object ScrollBox1: TScrollBox
    Left = 0
    Top = 89
    Width = 1441
    Height = 638
    Align = alClient
    BevelInner = bvNone
    BevelOuter = bvNone
    TabOrder = 2
  end
  object FehlerPanel: TPanel
    Left = 0
    Top = 727
    Width = 1441
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 3
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 1224
    Top = 40
  end
end
