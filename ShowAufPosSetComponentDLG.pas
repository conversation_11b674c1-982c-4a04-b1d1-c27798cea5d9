unit ShowAufPosSetComponentDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, BetterADODataSet, ExtCtrls, StdCtrls, Grids, DBGrids,
  SMDBGrid, DBGridPro;

type
  TShowAufPosSetComponentForm = class(TForm)
    AufTextLabel: TLabel;
    AufPosLabel: TLabel;
    Label3: TLabel;
    ArtikelLabel: TLabel;
    Label5: TLabel;
    SetComponentDBGrid: TDBGridPro;
    CloseButton: TButton;
    Bevel1: TBevel;
    SetComponentDataSource: TDataSource;
    SetComponentDataSet: TBetterADODataSet;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
  private
    fRefAufPos : Integer;
  public
    property RefAufPos : Integer read fRefAufPos write fRefAufPos;
  end;

implementation

uses <PERSON>figModul, DBGridUtilModule, SprachModul, DatenModul;

{$R *.dfm}

//****************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 30.05.2017
//****************************************************************************
//* Description
//****************************************************************************
//* Return Value :
//****************************************************************************
procedure TShowAufPosSetComponentForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  SetComponentDataSet.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
end;

//****************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 30.05.2017
//****************************************************************************
//* Description
//****************************************************************************
//* Return Value :
//****************************************************************************
procedure TShowAufPosSetComponentForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  fRefAufPos := -1;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AufPosLabel);
    LVSSprachModul.SetNoTranslate (Self, ArtikelLabel);
  {$endif}
end;

//****************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 30.05.2017
//****************************************************************************
//* Description
//****************************************************************************
//* Return Value :
//****************************************************************************
procedure TShowAufPosSetComponentForm.FormShow(Sender: TObject);
var
  query : TADOQuery;
begin
  LVSConfigModul.RestoreFormInfo (Self);

  if (fRefAufPos > 0) then begin
    query := TADOQuery.Create (Self);
    try
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select a.AUFTRAG_NR,ap.AUF_POS_NR,ap.MENGE_BESTELLT,ap.ARTIKEL_NR,ap.ARTIKEL_TEXT from V_AUFTRAG_POS ap, V_AUFTRAG a where a.REF=ap.REF_AUF_KOPF and ap.REF=:ref');
      query.Parameters [0].Value := fRefAufPos;

      query.Open;

      AufPosLabel.Caption  := query.FieldByName('AUFTRAG_NR').AsString + ' / ' + query.FieldByName('AUF_POS_NR').AsString;
      ArtikelLabel.Caption := query.FieldByName('MENGE_BESTELLT').AsString + ' * ' + query.FieldByName('ARTIKEL_NR').AsString + '  ' + query.FieldByName('ARTIKEL_TEXT').AsString;;

      query.Close;
    finally
      query.Free;
    end;

    SetComponentDataSet.CommandText := 'select * from V_AUFTRAG_POS_ARTIKEL_SET where REF_AUF_POS=:ref';
    SetComponentDataSet.Parameters [0].Value := fRefAufPos;

    SetComponentDataSet.Open;
  end;
end;

end.
