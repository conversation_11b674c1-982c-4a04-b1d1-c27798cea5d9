unit EditPackplatzDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls, CheckLst, ComCtrls;

type
  TEditPackplatzForm = class(TForm)
    Label1: TLabel;
    NameEdit: TEdit;
    OkButton: TButton;
    AbortButton: TButton;
    LagerComboBox: TComboBoxPro;
    Label2: TLabel;
    Bevel1: TBevel;
    Label3: TLabel;
    DescEdit: TEdit;
    Label4: TLabel;
    WAComboBox: TComboBoxPro;
    PageControl1: TPageControl;
    TabSheet1: TTabSheet;
    TabSheet2: TTabSheet;
    SperrgutCheckBox: TCheckBox;
    SingleCheckBox: TCheckBox;
    SelectSpedCheckListBox: TCheckListBox;
    ClearingCheckBox: TCheckBox;
    TabSheet3: TTabSheet;
    PackSpedCheckListBox: TCheckListBox;
    TabSheet4: TTabSheet;
    IncCountryEdit: TEdit;
    Label5: TLabel;
    Label6: TLabel;
    ExcCountryEdit: TEdit;
    BatchTabSheet: TTabSheet;
    PtoLComboBox: TComboBoxPro;
    Label7: TLabel;
    BatchVerteilCheckBox: TCheckBox;
    TabSheet5: TTabSheet;
    SelectVPECheckBox: TCheckBox;
    SelPackmittelCheckBox: TCheckBox;
    NoFehlPackCheckBox: TCheckBox;
    FullPackCheckBox: TCheckBox;
    PackAutoCloseCheckBox: TCheckBox;
    GroupPosCheckBox: TCheckBox;
    DimPackmittelCheckBox: TCheckBox;
    ErrorConfirmedCheckBox: TCheckBox;
    ArtikelStartCheckBox: TCheckBox;
    LEChangeConfirmeCheckBox: TCheckBox;
    UseVertLTCheckBox: TCheckBox;
    LTBestandCheckBox: TCheckBox;
    LTNachschubCheckBox: TCheckBox;
    Bevel2: TBevel;
    Bevel3: TBevel;
    Bevel4: TBevel;
    Bevel5: TBevel;
    Bevel6: TBevel;
    LTPlatzComboBox: TComboBoxPro;
    Label8: TLabel;
    LTStartCheckBox: TCheckBox;
    NewLEAllowedCheckBox: TCheckBox;
    procedure LagerComboBoxChange(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure SelectSpedCheckListBoxDrawItem(Control: TWinControl; Index: Integer;
      Rect: TRect; State: TOwnerDrawState);
    procedure CountryEditKeyPress(Sender: TObject; var Key: Char);
    procedure WAComboBoxChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    fRefPackplatz     : Integer;
    fSpedCheckListTab : Integer;
  public
    property RefPackplatz : Integer read fRefPackplatz write fRefPackplatz;

    function Prepare (RefPackplatz : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses
  DB, ADODB, VCLUtilitys, DatenModul, FrontendUtils, LVSWarenausgang,
  SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TEditPackplatzForm.Prepare (RefPackplatz : Integer) : Integer;
var
  idx,
  refsped : Integer;
  query   : TADOQuery;
begin
  fRefPackplatz := RefPackplatz;

  LoadLagerCombobox (LagerComboBox, LVSDatenModul.AktLocationRef);

  if (fRefPackplatz = -1) then begin
    if (LVSDatenModul.AktLagerRef = -1) then
      LagerComboBox.ItemIndex := -1
    else begin
      LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, LVSDatenModul.AktLagerRef);
      LagerComboBoxChange (Nil);
    end;

    WAComboBox.ItemIndex := 0;
  end else begin
    query := TADOQuery.Create (Self);
    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select * from V_WA_PACKPLATZ where REF=:ref');
      query.Parameters.ParamByName('ref').Value := fRefPackplatz;

      query.Open;

      LagerComboBox.Enabled := False;
      WAComboBox.Enabled := False;

      LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, query.FieldByName('REF_LAGER').AsInteger);
      LagerComboBoxChange (Nil);

      WAComboBox.ItemIndex := FindComboboxRef (WAComboBox, query.FieldByName('REF_WA_LB').AsInteger);
      if (WAComboBox.ItemIndex = -1) then WAComboBox.ItemIndex := 0;

      WAComboBoxChange (Nil);

      NameEdit.Text := query.FieldByName('NAME').AsString;
      DescEdit.Text := query.FieldByName('DESCRIPTION').AsString;

      SperrgutCheckBox.Checked := query.FieldByName('OPT_SPERRGUT').AsString = '1';
      SingleCheckBox.Checked   := query.FieldByName('OPT_SINGLE_NVE').AsString = '1';
      ClearingCheckBox.Checked := query.FieldByName('OPT_CLEARING').AsString = '1';

      if not Assigned (query.FindField ('OPT_ERROR_CONFIRMED')) then
        ErrorConfirmedCheckBox.Visible := False
      else
        ErrorConfirmedCheckBox.Checked := query.FieldByName('OPT_ERROR_CONFIRMED').AsString = '1';

      if not Assigned (query.FindField ('OPT_GROUP_PACK_POS')) then
        GroupPosCheckBox.Visible := False
      else
        GroupPosCheckBox.Checked := query.FieldByName('OPT_GROUP_PACK_POS').AsString = '1';

      if not Assigned (query.FindField ('OPT_VPE_SELECT_ALLOWED')) then
        SelectVPECheckBox.Visible := False
      else
        SelectVPECheckBox.Checked := query.FieldByName('OPT_VPE_SELECT_ALLOWED').AsString = '1';

      if not Assigned (query.FindField ('OPT_SELECT_PACK_LT')) then
        SelPackmittelCheckBox.Visible := False
      else
        SelPackmittelCheckBox.Checked := query.FieldByName('OPT_SELECT_PACK_LT').AsString = '1';

      if not Assigned (query.FindField ('OPT_AUTO_CLOSE')) then
        PackAutoCloseCheckBox.Visible := False
      else
        PackAutoCloseCheckBox.Checked := query.FieldByName('OPT_AUTO_CLOSE').AsString = '1';

      if not Assigned (query.FindField ('OPT_DIM_PACK_LT')) then
        DimPackmittelCheckBox.Visible := False
      else
        DimPackmittelCheckBox.Checked := query.FieldByName('OPT_DIM_PACK_LT').AsString = '1';

      if not Assigned (query.FindField ('OPT_OPEN_PACK_ON_SCAN_ITEM')) then
        ArtikelStartCheckBox.Visible := False
      else
        ArtikelStartCheckBox.Checked := query.FieldByName('OPT_OPEN_PACK_ON_SCAN_ITEM').AsString = '1';

      if not Assigned (query.FindField ('OPT_OPEN_PACK_ON_SCAN_LE')) then
        LTStartCheckBox.Visible := False
      else
        LTStartCheckBox.Checked := query.FieldByName('OPT_OPEN_PACK_ON_SCAN_LE').AsString = '1';

      if not Assigned (query.FindField ('OPT_NEW_LE_BY_SCAN')) then
        NewLEAllowedCheckBox.Visible := False
      else
        NewLEAllowedCheckBox.Checked := query.FieldByName('OPT_NEW_LE_BY_SCAN').AsString = '1';

      if not Assigned (query.FindField ('OPT_LE_CHANGE_CONFIRME')) then
        LEChangeConfirmeCheckBox.Visible := False
      else
        LEChangeConfirmeCheckBox.Checked := query.FieldByName('OPT_LE_CHANGE_CONFIRME').AsString = '1';

      if not Assigned (query.FindField ('OPT_LT_BESTAND')) then
        LTBestandCheckBox.Visible := False
      else
        LTBestandCheckBox.Checked := query.FieldByName('OPT_LT_BESTAND').AsString = '1';

      if not Assigned (query.FindField ('OPT_LT_NACHSCHUB')) then
        LTNachschubCheckBox.Visible := False
      else
        LTNachschubCheckBox.Checked := query.FieldByName('OPT_LT_NACHSCHUB').AsString = '1';

      if not Assigned (query.FindField ('REF_PACK_LT_LP')) then
        LTPlatzComboBox.Visible := False
      else if (query.FieldByName('REF_PACK_LT_LP').IsNull) then
        LTPlatzComboBox.ItemIndex := 0
      else begin
        LTPlatzComboBox.ItemIndex := FindComboboxRef (LTPlatzComboBox, query.FieldByName('REF_PACK_LT_LP').AsInteger);
        if (LTPlatzComboBox.ItemIndex = -1) then LTPlatzComboBox.ItemIndex := 0;
      end;

      FullPackCheckBox.Checked := query.FieldByName('OPT_DO_FULL_PACK').AsString = '1';
      NoFehlPackCheckBox.Checked := query.FieldByName('OPT_PACK_FEHLWARE').AsString = '0';

      BatchVerteilCheckBox.Checked := query.FieldByName('OPT_BATCH_DIS').AsString = '1';
      UseVertLTCheckBox.Checked := query.FieldByName('OPT_USE_VERTEIL_KLT').AsString = '1';

      if (query.FieldByName('REF_PICKANZEIGE_ZONE').IsNull) then
        PtoLComboBox.ItemIndex := 0
      else begin
        PtoLComboBox.ItemIndex := FindComboboxRef (PtoLComboBox, query.FieldByName('REF_PICKANZEIGE_ZONE').AsInteger);
        if (PtoLComboBox.ItemIndex = -1) then PtoLComboBox.ItemIndex := 0;
      end;

      IncCountryEdit.Text := query.FieldByName('INCLUDE_COUNTRY').AsString;
      ExcCountryEdit.Text := query.FieldByName('EXCLUDE_COUNTRY').AsString;

      query.Close;

      query.SQL.Clear;
      query.SQL.Add ('select REF_SPED, REIHENFOLGE, OPT_SELECT, OPT_PACK from V_WA_PACKPLATZ_REL_SPED where REF_PACKPLATZ=:ref');
      query.Parameters.ParamByName('ref').Value := fRefPackplatz;

      try
        query.Open;

        while not (query.Eof) do begin
          refsped := DBGetIntegerNull(query.Fields [0]);

          if (query.Fields [2].AsString = '1') then begin
            idx := 0;

            while (idx < SelectSpedCheckListBox.Items.Count) do begin
              if (GetListBoxRef (SelectSpedCheckListBox, idx) = refsped) Then begin
                SelectSpedCheckListBox.Checked [idx] := True;

                break;
              end;

              Inc (idx);
            end;
          end;

          if (query.Fields [3].AsString = '1') then begin
            idx := 0;

            while (idx < PackSpedCheckListBox.Items.Count) do begin
              if (GetListBoxRef (PackSpedCheckListBox, idx) = refsped) Then begin
                PackSpedCheckListBox.Checked [idx] := True;

                break;
              end;

              Inc (idx);
            end;
          end;

          query.Next;
        end;

        query.Close;
      except
      end;
    finally
      query.Free;
    end;
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditPackplatzForm.SelectSpedCheckListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
var
  line   : String;
  strpos : Integer;
begin
  if (Control is TCheckListBox) then begin
    line := (Control as TCheckListBox).Items [Index];
    strpos := Pos ('|', line);

    with (Control as TCheckListBox).Canvas do begin
      FillRect(Rect);

      if (strpos = 0) then
        TextOut (Rect.Left, Rect.Top, line)
      else begin
        TextOut (Rect.Left, Rect.Top, Copy (line,1,strpos - 1));
        TextOut (Rect.Left + fSpedCheckListTab, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 17.04.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditPackplatzForm.WAComboBoxChange(Sender: TObject);
begin
  if (GetComboBoxRef(WAComboBox) > 0) then begin
    if (LTPlatzComboBox.Visible and LTPlatzComboBox.Enabled) then begin
      LoadLPCombobox (LTPlatzComboBox, GetComboBoxRef(WAComboBox), false, -1, -1);

      LTPlatzComboBox.Items.Insert(0, '');
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditPackplatzForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  ref,
  idx    : Integer;
  optstr : String;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    if (GetComboBoxRef(LagerComboBox) = -1) then begin
      CanClose := False;
      if LagerComboBox.CanFocus then LagerComboBox.SetFocus;
      MessageDLG (FormatMessageText (1305, []), mtError, [mbOK], 0)
    end else if (GetComboBoxRef(WAComboBox) = -1) then begin
      CanClose := False;
      if WAComboBox.CanFocus then WAComboBox.SetFocus;
      MessageDLG (FormatMessageText (1306, []), mtError, [mbOK], 0)
    end else if (Length (NameEdit.Text) = 0) then begin
      CanClose := False;
      if NameEdit.CanFocus then NameEdit.SetFocus;
      MessageDLG (FormatMessageText (1176, []), mtError, [mbOK], 0)
    end else begin
      optstr := '';

      if SperrgutCheckBox.Checked then
        optstr := optstr + 'SPERR;';

      if SingleCheckBox.Checked then
        optstr := optstr + 'SINGLE;';

      if ClearingCheckBox.Checked then
        optstr := optstr + 'CLEARING;';

      if BatchVerteilCheckBox.Checked then
        optstr := optstr + 'DISTRIBUTE;';

      if UseVertLTCheckBox.Checked then
        optstr := optstr + 'USE_DIST_LT;';

      if FullPackCheckBox.Checked then
        optstr := optstr + 'PACK_FULL;';

      if NoFehlPackCheckBox.Checked then
        optstr := optstr + 'PACK_NO_FEHL;'
      else optstr := optstr + 'PACK_FEHL;';

      if SelectVPECheckBox.Visible and SelectVPECheckBox.Checked then begin
        optstr := optstr + 'SELECT;';
        optstr := optstr + 'SELECT_VPE;';
      end;

      if SelPackmittelCheckBox.Visible and SelPackmittelCheckBox.Checked then
        optstr := optstr + 'SELECT_PACK;';

      if PackAutoCloseCheckBox.Visible and PackAutoCloseCheckBox.Checked then
        optstr := optstr + 'AUTO_CLOSE;';

      if GroupPosCheckBox.Visible and GroupPosCheckBox.Checked then
        optstr := optstr + 'GROUP_POS;';

      if DimPackmittelCheckBox.Visible and DimPackmittelCheckBox.Checked then
        optstr := optstr + 'DIM_PACK;';

      if ErrorConfirmedCheckBox.Visible and ErrorConfirmedCheckBox.Checked then
        optstr := optstr + 'ERROR_CONF;';

      if ArtikelStartCheckBox.Visible and not ArtikelStartCheckBox.Checked then
        optstr := optstr + 'NO_SCAN_AR;';

      if LTStartCheckBox.Visible and not LTStartCheckBox.Checked then
        optstr := optstr + 'NO_SCAN_LT;';

      if NewLEAllowedCheckBox.Visible and not NewLEAllowedCheckBox.Checked then
        optstr := optstr + 'NO_LE_BY_SCAN;';

      if LEChangeConfirmeCheckBox.Visible and LEChangeConfirmeCheckBox.Checked then
        optstr := optstr + 'LE_CHANGE_CONF;';

      if LTBestandCheckBox.Visible and LTBestandCheckBox.Checked then
        optstr := optstr + 'LT_BES;';

      if LTNachschubCheckBox.Visible and LTNachschubCheckBox.Checked then
        optstr := optstr + 'LT_NACH;';

      if (fRefPackplatz <> -1) then begin
        if LTPlatzComboBox.Visible then
          res := ChangeVerpackungsPlatz (fRefPackplatz, NameEdit.Text, DescEdit.Text, optstr, IncCountryEdit.Text, ExcCountryEdit.Text, GetComboBoxRef (LTPlatzComboBox))
        else
          res := ChangeVerpackungsPlatz (fRefPackplatz, NameEdit.Text, DescEdit.Text, optstr, IncCountryEdit.Text, ExcCountryEdit.Text);

        if (res <> 0) then
          MessageDLG (FormatMessageText (1307, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      end else begin
        if LTPlatzComboBox.Visible then
          res := CreateVerpackungsPlatz (GetComboBoxRef (WAComboBox), NameEdit.Text, DescEdit.Text, optstr, IncCountryEdit.Text, ExcCountryEdit.Text, GetComboBoxRef (LTPlatzComboBox), ref)
        else
          res := CreateVerpackungsPlatz (GetComboBoxRef (WAComboBox), NameEdit.Text, DescEdit.Text, optstr, IncCountryEdit.Text, ExcCountryEdit.Text, ref);

        if (res <> 0) then
          MessageDLG (FormatMessageText (1308, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
        else
          fRefPackplatz := ref;
      end;

      if (res = 0) then
        res := SetPlatzplatzVerteilstation (fRefPackplatz, GetComboBoxRef(PtoLComboBox));

      if (res = 0) then begin
        idx := 0;

        while (idx < SelectSpedCheckListBox.Items.Count) and (res = 0) do begin
          res := SetPlatzplatzSpeditionAuswahl (fRefPackplatz, GetListBoxRef (SelectSpedCheckListBox, idx), -1, SelectSpedCheckListBox.Checked [idx]);

          Inc (idx);
        end;
      end;

      if (res = 0) then begin
        idx := 0;

        while (idx < PackSpedCheckListBox.Items.Count) and (res = 0) do begin
          res := SetPlatzplatzSpeditionPack (fRefPackplatz, GetListBoxRef (PackSpedCheckListBox, idx), -1, PackSpedCheckListBox.Checked [idx]);

          Inc (idx);
        end;
      end;

      CanClose := (res = 0);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditPackplatzForm.FormCreate(Sender: TObject);
begin
  fRefPackplatz := -1;

  NameEdit.Text := '';
  DescEdit.Text := '';

  IncCountryEdit.Text := '';
  ExcCountryEdit.Text := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
    LVSSprachModul.SetNoTranslate (Self, WAComboBox);
    LVSSprachModul.SetNoTranslate (Self, PtoLComboBox);
    LVSSprachModul.SetNoTranslate (Self, LTPlatzComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditPackplatzForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (LagerComboBox);
  ClearComboBoxObjects (WAComboBox);
  ClearComboBoxObjects (PtoLComboBox);
  ClearListBoxObjects (PackSpedCheckListBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditPackplatzForm.FormShow(Sender: TObject);
begin
  Label8.Visible := LTPlatzComboBox.Visible;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditPackplatzForm.CountryEditKeyPress(Sender: TObject;
  var Key: Char);
begin
  Key := UpCase (Key);

  if not (Key in [#8, #9, ',', ^V, ^C, 'A'..'Z']) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditPackplatzForm.LagerComboBoxChange(Sender: TObject);
var
  ref     : Integer;
  query   : TADOQuery;
  tw      : Integer;
  textstr : String;
begin
  ref := GetComboBoxRef (WAComboBox);

  ClearComboBoxObjects (WAComboBox);

  query := TADOQuery.Create (Self);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select REF,NAME,BESCHREIBUNG from V_LB where REF_LAGER=:ref_lager order by NAME');
    query.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);

    try
      query.Open;

      while not (query.Eof) do begin
        WAComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    except
    end;


    ClearComboBoxObjects (PtoLComboBox);

    PtoLComboBox.Items.Add ('');

    query.SQL.Clear;
    query.SQL.Add ('select REF,NAME,BEZEICHNUNG from V_PICKANZEIGE_ZONE where REF_LAGER=:ref_lager order by NAME');
    query.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);

    query.Open;

    while not (query.Eof) do begin
      PtoLComboBox.AddItemIndex (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

      query.Next;
    end;

    query.Close;


    ClearListBoxObjects (SelectSpedCheckListBox);
    ClearListBoxObjects (PackSpedCheckListBox);

    textstr := GetResourceText (1479);
    fSpedCheckListTab := SelectSpedCheckListBox.Canvas.TextWidth (textstr);
    PackSpedCheckListBox.Items.AddObject (textstr, TListBoxRef.Create (-1));

    query.SQL.Clear;
    query.SQL.Add ('select s.REF,s.NAME,s.BESCHREIBUNG,m.NAME from V_SPEDITIONEN s left outer join V_MANDANT m on (m.REF=s.REF_MAND) where s.STATUS in (''ANG'',''AKT'') and (s.REF_LAGER=:ref_lager or (s.REF_LAGER is null and s.REF_LOCATION=:ref_loc))');
    query.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);
    query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    if (LVSDatenModul.AktMandantRef <= 0) then
      query.SQL.Add ('and (s.REF_MAND is null or s.REF_MAND in (select REF from V_PCD_MANDANT))')
    else begin
      query.SQL.Add ('and (s.REF_MAND is null or s.REF_MAND=:ref_mand)');
      query.Parameters.ParamByName('ref_mand').Value := LVSDatenModul.AktMandantRef;
    end;

    query.SQL.Add ('order by m.NAME nulls last, s.NAME');

    try
      query.Open;

      while not (query.Eof) do begin
        if (LVSDatenModul.AktMandantRef > 0) then
          textstr := query.Fields [1].AsString+'|'+query.Fields [2].AsString
        else
          textstr := query.Fields [3].AsString+'|'+query.Fields [1].AsString+'|'+query.Fields [2].AsString;

        SelectSpedCheckListBox.Items.AddObject (textstr, TListBoxRef.Create (query.Fields [0].AsInteger));
        PackSpedCheckListBox.Items.AddObject (textstr, TListBoxRef.Create (query.Fields [0].AsInteger));

        tw := SelectSpedCheckListBox.Canvas.TextWidth (query.Fields [1].AsString);

        if (tw > fSpedCheckListTab) then
          fSpedCheckListTab := tw;

        query.Next;
      end;

      query.Close;

      fSpedCheckListTab := fSpedCheckListTab + 8;
    except
    end;
  finally
    query.Free;
  end;

  if (ref <> -1) then
    WAComboBox.ItemIndex := FindComboboxRef (WAComboBox, ref);

  if (WAComboBox.ItemIndex = -1) then WAComboBox.ItemIndex := 0;
end;

end.
