﻿unit DispAufKommPosDlg;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, DB, ADODB, BetterADODataSet, StdCtrls, Grids, DBGrids,
  SMDBGrid, DBGridPro, Menus, ACOList;

type
  TDispAufKommPosForm = class(TForm)
    AufKommPosDBGrid: TDBGridPro;
    CloseButton: TButton;
    AufTextLabel: TLabel;
    AufNrLabel: TLabel;
    Label3: TLabel;
    KundeNrLabel: TLabel;
    Label5: TLabel;
    AufKommPosDataSet: TBetterADODataSet;
    AufKommPosDataSource: TDataSource;
    Bevel1: TBevel;
    AufKommPopupMenu: TPopupMenu;
    ChangeKommPosMenuItem: TMenuItem;
    ACOListForm1: TACOListForm;
    AufStornoButton: TButton;
    ChangeKommPosMengeMenuItem: TMenuItem;
    AddErsatzMenuItem: TMenuItem;
    N1: TMenuItem;
    N2: TMenuItem;
    PrintBesLabelMenuItem: TMenuItem;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure AufKommPosDBGridDblClick(Sender: TObject);
    procedure ChangeKommPosMenuItemClick(Sender: TObject);
    procedure FormActivate(Sender: TObject);
    procedure AufStornoButtonClick(Sender: TObject);
    procedure AufKommPopupMenuPopup(Sender: TObject);
    procedure AufKommPosDBGridDrawColumnCell(Sender: TObject; const Rect: TRect;
      DataCol: Integer; Column: TColumn; State: TGridDrawState);
    procedure ChangeKommPosMengeMenuItemClick(Sender: TObject);
    procedure AddErsatzMenuItemClick(Sender: TObject);
    procedure PrintBesLabelMenuItemClick(Sender: TObject);
  private
    fRefAuf       : Integer;
    fRefWA        : Integer;
    fRefAufPos    : Integer;

    fChangeFlag   : Boolean;
    fReadOnlyFlag : Boolean;
  public
    property RefWA        : Integer read fRefWA     write fRefWA;
    property RefAuf       : Integer read fRefAuf    write fRefAuf;
    property RefAufPos    : Integer read fRefAufPos write fRefAufPos;

    property ReadOnlyFlag : Boolean read fReadOnlyFlag write fReadOnlyFlag;
    property ChangeFlag   : Boolean read fChangeFlag   write fChangeFlag;
  end;

implementation

{$R *.dfm}

uses
  {$ifdef Trace}
    Trace,
  {$endif}

  VCLUtilitys, StrUtils,
  FrontendMessages,
  DatenModul, LVSDatenInterface, ConfigModul, FrontendUtils, DBGridUtilModule, AufPosKommInfoDLG,
  DispAuftragKommPosDLG, FrontendImageModule, FrontendACOModul, ConfirmDLG, SprachModul, ResourceText,
  FrontendDatasets, ChangeAufPosArtikelDLG, PrintModul, PrinterUtils, PrintLEDLG, LablePrinterUtils;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispAufKommPosForm.FormShow(Sender: TObject);
var
  query : TADOQuery;
begin
  LVSConfigModul.RestoreFormInfo (Self);

  AufStornoButton.Enabled := not (fReadOnlyFlag);

  if (fRefWA > 0) then begin
    query := TADOQuery.Create (Self);
    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select * from V_WARENAUSGANG where REF=:ref');
      query.Parameters.ParamByName('ref').Value := fRefWA;

      query.Open;

      AufNrLabel.Caption   := query.FieldByName ('AUFTRAG_NR').AsString;
      KundeNrLabel.Caption := query.FieldByName ('KUNDEN_NR').AsString + ' : ' + query.FieldByName ('KUNDEN_NAME').AsString;

      query.Close;
    finally
      query.Free;
    end;

    AufKommPosDataSet.CommandText := 'select * from V_PCD_AUFTRAG_KOMM_POS where REF_AUF_KOPF in (select REF_AUFTRAG from V_WA_REL_AUFTRAG where REF_WA=:ref)';
    AufKommPosDataSet.Parameters.ParamByName('ref').Value := fRefWA;
  end else if (fRefAuf > 0) then begin
    query := TADOQuery.Create (Self);
    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select * from V_AUFTRAG where REF=:ref');
      query.Parameters.ParamByName('ref').Value := fRefAuf;

      query.Open;

      AufNrLabel.Caption   := query.FieldByName ('AUFTRAG_NR').AsString;
      KundeNrLabel.Caption := query.FieldByName ('KUNDEN_NR').AsString + ' : ' + query.FieldByName ('KUNDEN_NAME').AsString;

      query.Close;
    finally
      query.Free;
    end;

    AufKommPosDataSet.CommandText := 'select * from V_PCD_AUFTRAG_KOMM_POS where REF_AUF_KOPF=:ref';
    AufKommPosDataSet.Parameters.ParamByName('ref').Value := fRefAuf;
  end;

  AufKommPosDBGrid.DataSource.DataSet.Open;

  if (fRefAufPos > 0) then
    AufKommPosDBGrid.DataSource.DataSet.Locate('REF_AUF_POS', fRefAufPos, []);

  AufKommPosDBGrid.SetColumnVisible('GEWICHT_GRAMM', false);

  AufKommPosDBGrid.SetColumnVisible('ARTIKEL_NR', False);
  AufKommPosDBGrid.SetColumnVisible('ARTIKEL_TEXT', False);

  DBGridUtils.SetGewichtDisplayFunctions(AufKommPosDBGrid.DataSource.DataSet, 'GEWICHT_IST');
  DBGridUtils.SetGewichtDisplayFunctions(AufKommPosDBGrid.DataSource.DataSet, 'AUSLAGER_NETTO_DIFF');
end;

procedure TDispAufKommPosForm.PrintBesLabelMenuItemClick(Sender: TObject);
var
  res,
  prtref    : Integer;
  errmsg    : String;
  prtdaten  : TPrinterPorts;
  prtform   : TPrintLEForm;
begin
  if AufKommPosDataSet.Active and (AufKommPosDataSet.RecNo > 0) then begin
    res := PrintModule.DetectPrinter ('KOMM_BESTAND-LABEL', -1, prtref, prtdaten);

    if (res = 0) and (prtref < 0) then begin
      prtform := TPrintLEForm.Create (Self);

      try
        prtform.Prepare(UserReg.ReadRegValue ('KOMM_BESTAND-PRINTER'), -1, -1);

        if (prtform.PrinterComboBox.Items.Count = 0) then
          FrontendMessages.MessageDLG(FormatResourceText (1041, []), mtError, [mbOK], 0)
        else if (prtform.ShowModal = mrOk) then begin
          if (prtform.PrinterComboBox.ItemIndex >= 0) then begin
            prtdaten := prtform.PortArray [prtform.PrinterComboBox.ItemIndex];

            UserReg.WriteRegValue ('KOMM_BESTAND-PRINTER', prtform.GetSelectedPrinter);
          end;
        end;
      finally
        prtform.Release;
      end;
    end;

    res := PrintKommBestandLabel (prtdaten, AufKommPosDataSet.FieldByName ('REF').AsInteger, errmsg)
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispAufKommPosForm.AufStornoButtonClick(Sender: TObject);
var
  res,
  refta,
  dlgres   : Integer;
  lstat,
  dlggrund : String;
  askform  : TConfirmForm;
  query    : TBetterADODataSet;
  dfudt,
  prtdt    : TDateTime;
begin
  res := 0;

  if AufKommPosDataSet.Active and not (AufKommPosDataSet.FieldByName ('REF_AUF_KOPF').IsNull) then begin
    query := TBetterADODataSet.Create (Self);

    prtdt := 0;
    dfudt := 0;

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.CommandText := 'select a.REF, ad.SEND_PICK_RESULT, l.DRUCK_DATUM, l.STATUS from V_AUFTRAG a, V_AUFTRAG_DFUE ad, V_LIEFERUNG_01 l where ad.REF_AUF_KOPF=a.REF and l.REF=a.REF_LIEFERUNG and a.REF=:ref';
      query.Parameters [0].Value := AufKommPosDataSet.FieldByName ('REF_AUF_KOPF').AsInteger;

      try
        query.Open;

        if query.Fields [1].IsNull then
          dfudt := 0
        else
          dfudt := query.Fields [1].AsDateTime;

        if query.Fields [2].IsNull then
          prtdt := 0
        else
          prtdt := query.Fields [2].AsDateTime;

        lstat := query.Fields [2].AsString;

        query.Close;
      except
        res := -9;
        FrontendMessages.MessageDLG ('Fehler beim Abrufen der Auftragsdaten', mtError, [mbOk], 0);
      end;

      if (res = 0) then begin
        if (lstat = 'ABG') then
          FrontendMessages.MessageDLG ('Die Lieferung ist bereits abgeschlossen, eine Änderung der Kommissionierdaten ist daher nicht mehr möglich!', mtError, [mbOk], 0)
        else if (prtdt <> 0) then
          FrontendMessages.MessageDLG ('Lieferschein ist schon gedruckt, eine Änderung der Kommissionierdaten ist daher nicht mehr möglich!', mtError, [mbOk], 0)
        else if (dfudt <> 0) then
          FrontendMessages.MessageDLG ('Die Kommissionierdaten wurden bereits versendet, eine Änderung ist daher nicht mehr möglich!', mtError, [mbOk], 0)
        else begin
          askform := TConfirmForm.Create (Self);

          askform.PrepareAuf(AufKommPosDataSet.FieldByName('REF_AUF_KOPF').AsInteger, 'AUF_STORNO');

          askform.Caption := 'Alle Kommissionierdaten des Auftrags stornieren';

          if (prtdt <> 0) and (dfudt <> 0) then
            askform.ConfirmLabel.Caption := 'Die Kommissionierdaten wurden bereits versendet und der Lieferschein ist auch schon gedruckt,'+#13+#13+'dennoch alle kommissionierte Menge stornieren?'
          else if (prtdt <> 0) then
            askform.ConfirmLabel.Caption := 'Lieferschein ist schon gedruckt,'+#13+#13+'dennoch alle kommissionierte Menge stornieren?'
          else if (dfudt <> 0) then
            askform.ConfirmLabel.Caption := 'Die Kommissionierdaten wurden bereits versendet,'+#13+#13+'dennoch alle kommissionierte Menge stornieren?'
          else
            askform.ConfirmLabel.Caption := 'Wirklich alle kommissionierte Menge stornieren?';

          askform.ConfirmLabel.Caption := askform.ConfirmLabel.Caption +#13+#13+'Wenn ja, mit oder ohne Bestandskorrektur?';

          askform.Button1.Visible := True;
          askform.Button1.Default := False;
          askform.Button1.ModalResult := mrYes;
          askform.Button1.Caption := 'Mit Bestandskorrektur';

          askform.Button2.Default := False;
          askform.Button2.Visible := True;
          askform.Button2.ModalResult := mrNo;
          askform.Button2.Caption := 'Ohne Bestandskorrektur';

          askform.AbortButton.Visible := True;
          askform.AbortButton.Default := False;
          askform.AbortButton.Cancel := True;
          askform.AbortButton.ModalResult := mrAbort;
          askform.AbortButton.Caption := 'Abbrechen';

          dlgres   := askform.ShowModal;
          dlggrund := askform.Grund;

          askform.Free;

          if (dlgres in [mrYes, mrNo]) then begin
            res := AuftragStorno (AufKommPosDataSet.FieldByName('REF_AUF_KOPF').AsInteger, dlgres=mrYes, dlggrund, refta);

            if (res = 0) then
              AufKommPosDBGrid.Reload
            else
              FrontendMessages.MessageDLG ('Fehler beim Stornieren der Kommissionierdaten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
          end;
        end;
      end;
    finally
      query.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispAufKommPosForm.ChangeKommPosMengeMenuItemClick(Sender: TObject);
var
  res,
  menge,
  dlgres,
  refakp  : Integer;
  arinfo  : TArtikelInfo;
  askform : TConfirmForm;
begin
  if AufKommPosDataSet.Active and (AufKommPosDataSet.RecNo > 0) then begin
    arinfo  := TArtikelInfo.Create;
    askform := TConfirmForm.Create (Self);

    try
      GetArtikelInfos (AufKommPosDataSet.FieldByName('REF_AR_EINHEIT').AsInteger, arinfo);

      askform.Caption := GetResourceText (1347);

      askform.ArtikelInfo := arinfo;

      askform.MengeNull := True;

      askform.EditPanel.Visible  := True;
      askform.EditPanel.Enabled  := True;

      askform.CategoryPanel.Visible := False;
      askform.MHDPanel.Visible  := False;
      askform.HDTEdit.Visible   := False;

      askform.Button1.Visible := False;

      askform.Button2.Default := False;
      askform.Button2.Visible := True;
      askform.Button2.ModalResult := mrYes;
      askform.Button2.Caption := GetResourceText (1335);

      askform.AbortButton.Visible := True;
      askform.AbortButton.Default := False;
      askform.AbortButton.Cancel := True;
      askform.AbortButton.ModalResult := mrAbort;
      askform.AbortButton.Caption := GetResourceText (1346);

      if (AufKommPosDataSet.FieldByName('REF').IsNull) then
        askform.PrepareAufPos (AufKommPosDataSet.FieldByName('REF_AUF_POS').AsInteger, '')
      else
        askform.Prepare (AufKommPosDataSet.FieldByName('REF').AsInteger, '');

      dlgres := askform.ShowModal;

      if (dlgres = mrYes) then begin
        fChangeFlag := True;

        if (askform.MengeUpDown.Position = 0) then
          menge := 0
        else
          menge := askform.MengeUpDown.Position;

        if (AufKommPosDataSet.FieldByName('REF').IsNull) then
          res := AufPosChangeMenge (AufKommPosDataSet.FieldByName('REF_AUF_POS').AsInteger, menge, askform.Grund, refakp)
        else begin
          refakp := AufKommPosDataSet.FieldByName('REF').AsInteger;

          res := AufKommPosChangeMenge (refakp, menge, askform.Grund);
        end;

        if (res <> 0) Then
          FrontendMessages.MessageDLG (FormatMessageText (1633, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
        else
          AufKommPosDBGrid.Reload (refakp);
      end;
    finally
      askform.Release;
      arinfo.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispAufKommPosForm.ChangeKommPosMenuItemClick(Sender: TObject);
var
  dfudt,
  prtdt    : TDateTime;
  res,
  dlgres   : Integer;
  lstat    : String;
  query    : TBetterADODataSet;
  dispform : TDispAuftragKommPosForm;
begin
  res := 0;

  if AufKommPosDataSet.Active and (AufKommPosDataSet.RecNo > 0) then begin
    query := TBetterADODataSet.Create (Self);
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    prtdt := 0;
    dfudt := 0;

    try
      query.CommandText := 'select a.REF, ad.SEND_PICK_RESULT, l.DRUCK_DATUM, l.STATUS from V_AUFTRAG a, V_AUFTRAG_DFUE ad, V_LIEFERUNG_01 l where ad.REF_AUF_KOPF=a.REF and l.REF=a.REF_LIEFERUNG and a.REF=:ref';
      query.Parameters [0].Value := AufKommPosDataSet.FieldByName ('REF_AUF_KOPF').AsInteger;

      try
        query.Open;

        if query.Fields [1].IsNull then
          dfudt := 0
        else
          dfudt := query.Fields [1].AsDateTime;

        if query.Fields [2].IsNull then
          prtdt := 0
        else
          prtdt := query.Fields [2].AsDateTime;

        lstat := query.Fields [2].AsString;

        query.Close;
      except
        res := -9;
        FrontendMessages.MessageDLG ('Fehler beim Abrufen der Auftragsdaten', mtError, [mbOk], 0);
      end;

      if (res = 0) then begin
        if (lstat = 'ABG') then
          FrontendMessages.MessageDLG (FormatMessageText (1137, []), mtError, [mbOk], 0)
        else begin
          if (prtdt <> 0) and (dfudt <> 0) then
            dlgres := FrontendMessages.MessageDLG (FormatMessageText (1138, []), mtConfirmation, [mbYes, mbNo], 0)
          else if (prtdt <> 0) then
            dlgres := FrontendMessages.MessageDLG (FormatMessageText (1139, []), mtConfirmation, [mbYes, mbNo], 0)
          else if (dfudt <> 0) then
            dlgres := FrontendMessages.MessageDLG (FormatMessageText (1140, []), mtConfirmation, [mbYes, mbNo], 0)
          else
            dlgres := mrYes;

          if (dlgres = mrYes) then begin
            fChangeFlag := True;

            res := SetAuftragUpdate (AufKommPosDataSet.FieldByName ('REF_AUF_KOPF').AsInteger);

            if (res <> 0) then
              FrontendMessages.MessageDLG ('Fehler beim Aktualisieren der Auftragsdaten', mtError, [mbOk], 0)
            else begin
              dispform := TDispAuftragKommPosForm.Create (Self);

              dispform.RefAufKom := DBGetReferenz (AufKommPosDataSet.FieldByName ('REF'));
              dispform.RefKomPos := DBGetReferenz (AufKommPosDataSet.FieldByName ('REF_KOMM_POS'));

              dispform.Caption := FormatResourceText (1349, [AufKommPosDataSet.FieldByName ('AUF_POS_NR').AsString, AufKommPosDataSet.FieldByName ('ARTIKEL_NR').AsString]);

              if (dispform.ShowModal = mrOk) then
                AufKommPosDBGrid.Reload;

              dispform.Release;
            end;
          end;
        end;
      end;
    finally
      query.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.06.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispAufKommPosForm.AddErsatzMenuItemClick(Sender: TObject);
var
  res,
  refakp,
  menge    : Integer;
  editfrom : TChangeAufPosArtikelForm;
begin
  {$ifdef Trace}
    ProcedureStart ('TLVSForm.KommPosErsatzMenuItemClick');
  {$endif}

  if (AufKommPosDataSet.Active and (AufKommPosDataSet.RecNo > 0)) then begin
    editfrom := TChangeAufPosArtikelForm.Create (Self);

    editfrom.PrepareAufPos (AufKommPosDataSet.FieldByName ('REF_AUF_POS').AsInteger);
    editfrom.BlankCommendAllowed := True;

    if (editfrom.ShowModal = mrOk) then begin
      if (GetComboBoxRef (editfrom.ArtikelComboBox) <> AufKommPosDataSet.FieldByName ('REF_AR_EINHEIT').AsInteger) then begin
        fChangeFlag := True;

        if (Length (editfrom.MengeEdit.Text) = 0) then
          menge := -1
        else if not TryStrToInt (editfrom.MengeEdit.Text, menge) then
          menge := -1;

        res := BucheErsatzArtikel (-1, AufKommPosDataSet.FieldByName ('REF_AUF_POS').AsInteger, GetComboBoxRef (editfrom.ArtikelComboBox), menge, editfrom.GrundEdit.Text, refakp);

        if (res <> 0) then
          FrontendMessages.MessageDLG('Fehler beim Buchen des Ersatzartikels' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
        else begin
          AufKommPosDBGrid.Reload;
        end;
      end;
    end;

    editfrom.Release;
  end;

  {$ifdef Trace}
    ProcedureStop;
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispAufKommPosForm.AufKommPopupMenuPopup(Sender: TObject);
begin
  ChangeKommPosMenuItem.Enabled := not (fReadOnlyFlag) and (AufKommPosDataSet.Active) and (AufKommPosDataSet.RecNo > 0);
  ChangeKommPosMengeMenuItem.Enabled := ChangeKommPosMenuItem.Enabled;
  AddErsatzMenuItem.Enabled := ChangeKommPosMenuItem.Enabled;
  PrintBesLabelMenuItem.Enabled := (AufKommPosDataSet.Active) and (AufKommPosDataSet.RecNo > 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispAufKommPosForm.AufKommPosDBGridDblClick(Sender: TObject);
var
  infoform : TAufPosKommInfoForm;
begin
  if (AufKommPosDataSet.Active) and (AufKommPosDataSet.RecNo > 0) then begin
    infoform := TAufPosKommInfoForm.Create (Self);

    infoform.Caption := 'Zusatzinfos für die Position '+AufKommPosDataSet.FieldByName ('AUF_POS_NR').AsString+' des Auftrags';
    infoform.Prepare (AufKommPosDataSet.FieldByName ('REF').AsInteger);

    infoform.ShowModal;

    infoform.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispAufKommPosForm.AufKommPosDBGridDrawColumnCell(Sender: TObject; const Rect: TRect; DataCol: Integer; Column: TColumn; State: TGridDrawState);
var
  bm: TBitmap;
  idx: Integer;
begin
  if (PosEx ('STATUS', Column.FieldName) > 0) then begin
    if (LVSConfigModul.FrontendConfig.cfgStatIcons) then begin
      if (Column.Field.AsString = 'ANG') then begin
        idx := 0;
      end else if (Column.Field.AsString = 'ABG') then
        idx := 1
      else if (Column.Field.AsString = 'BER') then
        idx := 3
      else if (Column.Field.AsString = 'BES') then
        idx := 2
      else if (Column.Field.AsString = 'KOM') then
        idx := 4
      else if (Column.Field.AsString = 'WA') then
        idx := 5
      else if (Column.Field.AsString = 'TRA') then
        idx := 6
      else if (Column.Field.AsString = 'MOV') then
        idx := 14
      else if (Column.Field.AsString = 'FIN') then
        idx := 7
      else if (Column.Field.AsString = 'STO') then
        idx := 8
      else if (Column.Field.AsString = 'RLO') then
        idx := 10
      else if (Column.Field.AsString = 'PLA') then
        idx := 12
      else if (Column.Field.AsString = 'UMP') then
        idx := 13
      else if (Column.Field.AsString = 'IFC') then
        idx := 11
      else if (Column.Field.AsString = 'DEL') then
        idx := 9
      else if (Column.Field.AsString = 'SPERR') then
        idx := 16
      else if (Column.Field.AsString = 'QS') then
        idx := 19
      else if (Column.Field.AsString = 'BEL') then
        idx := 18
      else if (Column.Field.AsString = 'FREI') then
        idx := 17
      else if (Column.Field.AsString = 'AKT') then
        idx := 22
      else if (Column.Field.AsString = 'DIS') then
        idx := 22
      else if (Column.Field.AsString = 'CRO') then
        idx := 15
      else if (Column.Field.AsString = 'NACH') then
        idx := 14
      else if (Column.Field.AsString = 'UNV') then
        idx := 23
      else if (Column.Field.AsString = 'RES') then
        idx := 24
      else if (Column.Field.AsString = 'UNG') then
        idx := 25
      else if (Column.Field.AsString = 'OLD') then
        idx := 26
      else if (Column.Field.AsString = 'EIN') then
        idx := 27
      else idx := -1;

      if (idx <> -1) then begin
        (Sender as TDBGridPro).Canvas.FillRect(rect);

        bm := TBitmap.Create;
        try
          if (idx = 0) then
            bm.handle := loadbitmap(hinstance, 'angelegt')
          else if (idx = 1) then
            bm.handle := loadbitmap(hinstance, 'abgeschlossen')
          else if (idx = 2) then
            bm.handle := loadbitmap(hinstance, 'fehler')
          else if (idx = 3) then
            bm.handle := loadbitmap(hinstance, 'transform')
          else if (idx = 4) then
            bm.handle := loadbitmap(hinstance, 'kommissionieren')
          else if (idx = 5) then
            bm.handle := loadbitmap(hinstance, 'warenausgang')
          else if (idx = 6) then
            bm.handle := loadbitmap(hinstance, 'interntransport')
          else if (idx = 7) then
            bm.handle := loadbitmap(hinstance, 'beendet')
          else if (idx = 8) then
            bm.handle := loadbitmap(hinstance, 'storniert')
          else if (idx = 9) then
            bm.handle := loadbitmap(hinstance, 'delete')
          else if (idx = 10) then
            bm.handle := loadbitmap(hinstance, 'reload')
          else if (idx = 11) then
            bm.handle := loadbitmap(hinstance, 'ifcerror')
          else if (idx = 12) then
            bm.handle := loadbitmap(hinstance, 'geplant')
          else if (idx = 13) then
            bm.handle := loadbitmap(hinstance, 'umpack')
          else if (idx = 14) then
            bm.handle := loadbitmap(hinstance, 'gabelstapler')
          else if (idx = 15) then
            bm.handle := loadbitmap(hinstance, 'crossdock')
          else if (idx = 16) then
            bm.handle := loadbitmap(hinstance, 'redcross')
          else if (idx = 17) then
            bm.handle := loadbitmap(hinstance, 'tick')
          else if (idx = 18) then
            bm.handle := loadbitmap(hinstance, 'oneway')
          else if (idx = 19) then
            bm.handle := loadbitmap(hinstance, 'lupe')
          else if (idx = 20) then
            bm.handle := loadbitmap(hinstance, 'palette_leer')
          else if (idx = 21) then
            bm.handle := loadbitmap(hinstance, 'palette_voll')
          else if (idx = 22) then
            bm.handle := loadbitmap(hinstance, 'running')
          else if (idx = 23) then
            bm.handle := loadbitmap(hinstance, 'uncomplete')
          else if (idx = 24) then
            bm.handle := loadbitmap(hinstance, 'reserved')
          else if (idx = 25) then
            bm.handle := loadbitmap(hinstance, 'aks')
          else if (idx = 26) then
            bm.handle := loadbitmap(hinstance, 'old')
          else if (idx = 27) then
            bm.handle := loadbitmap(hinstance, 'palette_nach');

          DrawStatusBitmap ((Sender as TDBGridPro).Canvas, rect, bm);
        finally
          bm.Free;
        end;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispAufKommPosForm.FormActivate(Sender: TObject);
begin
  if Assigned (FrontendACOModule) then
    FrontendACOModule.SetBerechtigungen (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispAufKommPosForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  AufKommPosDataSet.Close;

  if Assigned (DBGridUtils) Then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispAufKommPosForm.FormCreate(Sender: TObject);
begin
  fChangeFlag   := False;
  fReadOnlyFlag := True;

  fRefWA     := -1;
  fRefAuf    := -1;
  fRefAufPos := -1;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, KundeNrLabel);
    LVSSprachModul.SetNoTranslate (Self, AufNrLabel);
  {$endif}
end;

end.
