object QSAbschliessenForm: TQSAbschliessenForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'QSAbschliessenForm'
  ClientHeight = 416
  ClientWidth = 446
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    446
    416)
  TextHeight = 13
  object GrundLabel: TLabel
    Left = 8
    Top = 327
    Width = 134
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Abschliessender Kommentar'
    ExplicitTop = 310
  end
  object Bevel1: TBevel
    Left = 6
    Top = 312
    Width = 434
    Height = 6
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 295
    ExplicitWidth = 428
  end
  object NotOkButton: TButton
    Left = 208
    Top = 383
    Width = 145
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'QS-Pr'#252'fung nicht ok'
    Default = True
    ModalResult = 7
    TabOrder = 3
    ExplicitLeft = 202
    ExplicitTop = 366
  end
  object AbortButton: TButton
    Left = 363
    Top = 383
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
    ExplicitLeft = 357
    ExplicitTop = 366
  end
  object GrundComboBox: TComboBox
    Left = 8
    Top = 346
    Width = 430
    Height = 21
    Anchors = [akLeft, akRight, akBottom]
    MaxLength = 64
    TabOrder = 2
    Text = 'GrundComboBox'
    Items.Strings = (
      'MHD zu kurz'
      'Ware mangelhaft')
    ExplicitTop = 329
    ExplicitWidth = 424
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 446
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    ExplicitWidth = 440
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 31
      Height = 13
      Caption = 'Label1'
    end
    object Label2: TLabel
      Left = 8
      Top = 32
      Width = 31
      Height = 13
      Caption = 'Label2'
    end
  end
  object CheckPanel: TPanel
    Left = 0
    Top = 57
    Width = 446
    Height = 232
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    ExplicitWidth = 440
    object FehlerLabel: TLabel
      Left = 0
      Top = 214
      Width = 440
      Height = 18
      Align = alBottom
      Alignment = taCenter
      AutoSize = False
      Caption = 'FehlerLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      ExplicitLeft = 7
      ExplicitTop = 8
      ExplicitWidth = 433
    end
    object ScrollBox1: TScrollBox
      Left = 0
      Top = 0
      Width = 440
      Height = 209
      Align = alTop
      Anchors = [akLeft, akTop, akRight, akBottom]
      BevelInner = bvNone
      BevelOuter = bvNone
      TabOrder = 0
      object HACCPPanel: TPanel
        Left = 0
        Top = 0
        Width = 436
        Height = 199
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 0
      end
    end
  end
  object OkButton: TButton
    Left = 49
    Top = 383
    Width = 145
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'QS-Pr'#252'fung ok'
    Default = True
    ModalResult = 1
    TabOrder = 4
    ExplicitLeft = 43
    ExplicitTop = 366
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 328
    Top = 8
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 360
    Top = 8
  end
end
