object EditAufWorkloadsForm: TEditAufWorkloadsForm
  Left = 0
  Top = 0
  Caption = 'Zus'#228'tzlichen Aufw'#228'nde'
  ClientHeight = 608
  ClientWidth = 852
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  TextHeight = 13
  object AufWorkloadDBGrid: TDBGridPro
    AlignWithMargins = True
    Left = 8
    Top = 84
    Width = 836
    Height = 208
    Margins.Left = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Align = alClient
    DataSource = AufWorkloadDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = AudWorkloadDBGridPopupMenu
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    OnDblClick = AufWorkloadDBGridDblClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsNormal
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object TopPanel: TPanel
    Left = 0
    Top = 0
    Width = 852
    Height = 81
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      852
      81)
    object Label1: TLabel
      Left = 8
      Top = 16
      Width = 41
      Height = 13
      Caption = 'Auftrag:'
    end
    object AuftragLabel: TLabel
      Left = 80
      Top = 16
      Width = 62
      Height = 13
      Caption = 'AuftragLabel'
    end
    object Label3: TLabel
      Left = 8
      Top = 68
      Width = 115
      Height = 13
      Caption = 'Zugewiesene Aufw'#228'nde'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 59
      Width = 835
      Height = 10
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
  end
  object BottomPanel: TPanel
    Left = 0
    Top = 567
    Width = 852
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      852
      41)
    object CloseButton: TButton
      Left = 768
      Top = 6
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 0
    end
  end
  object DataPanel: TPanel
    Left = 0
    Top = 300
    Width = 852
    Height = 267
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 3
    object GroupBox1: TGroupBox
      AlignWithMargins = True
      Left = 3
      Top = 3
      Width = 846
      Height = 258
      Align = alTop
      Caption = 'M'#246'gliche Aufw'#228'nde'
      TabOrder = 0
      DesignSize = (
        846
        258)
      object WorkloadDBGrid: TDBGridPro
        AlignWithMargins = True
        Left = 6
        Top = 18
        Width = 834
        Height = 191
        Margins.Left = 4
        Margins.Right = 4
        Align = alTop
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = WorkloadDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsNormal
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object AddWorkloadButton: TButton
        Left = 6
        Top = 223
        Width = 75
        Height = 25
        Anchors = [akLeft, akBottom]
        Caption = 'Hinzuf'#252'gen'
        TabOrder = 1
        OnClick = AddWorkloadButtonClick
      end
    end
  end
  object AufWorkloadQuery: TSmartQuery
    Left = 480
    Top = 88
  end
  object AufWorkloadDataSource: TDataSource
    DataSet = AufWorkloadQuery
    Left = 480
    Top = 32
  end
  object WorkloadDataSource: TDataSource
    DataSet = WorkloadQuery
    Left = 616
    Top = 32
  end
  object WorkloadQuery: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    Left = 616
    Top = 88
  end
  object AudWorkloadDBGridPopupMenu: TPopupMenu
    OnPopup = AudWorkloadDBGridPopupMenuPopup
    Left = 504
    Top = 200
    object EditAufWorkloadMenuItem: TMenuItem
      Caption = 'Bearbeiten...'
      OnClick = EditAufWorkloadMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object DelAufWorkloadMenuItem: TMenuItem
      Caption = 'L'#246'schen...'
    end
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 240
    Top = 16
  end
end
