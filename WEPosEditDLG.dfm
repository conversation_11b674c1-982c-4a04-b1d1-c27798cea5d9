object WEPosEditForm: TWEPosEditForm
  Left = 478
  Top = 253
  BorderStyle = bsDialog
  Caption = 'Warenannahme-Position '#228'ndern'
  ClientHeight = 782
  ClientWidth = 508
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poMainFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  TextHeight = 13
  object ArtikelPanel: TPanel
    Left = 0
    Top = 0
    Width = 508
    Height = 133
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      508
      133)
    object Bevel2: TBevel
      Left = 6
      Top = 72
      Width = 497
      Height = 9
      Shape = bsTopLine
    end
    object Label1: TLabel
      Left = 8
      Top = 16
      Width = 79
      Height = 13
      Caption = 'Warenannahme:'
    end
    object Label2: TLabel
      Left = 8
      Top = 32
      Width = 44
      Height = 13
      Caption = 'Lieferant:'
    end
    object Label3: TLabel
      Left = 8
      Top = 48
      Width = 52
      Height = 13
      Caption = 'Bestellung:'
    end
    object WELabel: TLabel
      Left = 112
      Top = 16
      Width = 52
      Height = 13
      Caption = 'WELabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LieferLabel: TLabel
      Left = 112
      Top = 32
      Width = 64
      Height = 13
      Caption = 'LieferLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object BestellLabel: TLabel
      Left = 112
      Top = 48
      Width = 70
      Height = 13
      Caption = 'BestellLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Bevel3: TBevel
      Left = 6
      Top = 131
      Width = 497
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 128
    end
    object Label6: TLabel
      Left = 9
      Top = 80
      Width = 41
      Height = 13
      Caption = 'Artikelnr.'
    end
    object Label7: TLabel
      Left = 8
      Top = 96
      Width = 46
      Height = 13
      Caption = 'Artikeltext'
    end
    object ArtikelNrLabel: TLabel
      Left = 112
      Top = 80
      Width = 81
      Height = 13
      Caption = 'ArtikelNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object ArtikelTextLabel: TLabel
      Left = 112
      Top = 96
      Width = 93
      Height = 13
      Caption = 'ArtikelTextLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object ArtikelInfoTextLabel: TLabel
      Left = 8
      Top = 112
      Width = 63
      Height = 13
      Caption = 'Vereinbarung'
      Visible = False
    end
    object ArtikelInfoLabel: TLabel
      Left = 112
      Top = 112
      Width = 90
      Height = 13
      Caption = 'ArtikelInfoLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LeergutButton: TButton
      Left = 428
      Top = 16
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Leergut'
      TabOrder = 0
      OnClick = LeergutButtonClick
    end
  end
  object NVEPanel: TPanel
    Left = 0
    Top = 165
    Width = 508
    Height = 32
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    object Label4: TLabel
      Left = 8
      Top = 11
      Width = 22
      Height = 13
      Caption = 'NVE'
    end
    object Label22: TLabel
      Left = 264
      Top = 11
      Width = 33
      Height = 13
      Alignment = taRightJustify
      Caption = 'HU-Nr.'
    end
    object NVEEdit: TEdit
      Left = 113
      Top = 8
      Width = 128
      Height = 21
      MaxLength = 18
      TabOrder = 0
      Text = 'NVEEdit'
      OnChange = EditChange
      OnExit = NVEEditExit
    end
    object HUNrEdit: TEdit
      Left = 303
      Top = 8
      Width = 112
      Height = 21
      MaxLength = 18
      TabOrder = 1
      Text = 'HUNrEdit'
      OnChange = EditChange
      OnExit = NVEEditExit
    end
  end
  object MengePanel: TPanel
    Left = 0
    Top = 197
    Width = 508
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    object Label8: TLabel
      Left = 8
      Top = 5
      Width = 33
      Height = 13
      Caption = 'Menge'
    end
    object Label9: TLabel
      Left = 8
      Top = 29
      Width = 39
      Height = 13
      Caption = 'Gewicht'
    end
    object Label12: TLabel
      Left = 186
      Top = 29
      Width = 12
      Height = 13
      Caption = 'kg'
    end
    object MengeEdit: TEdit
      Left = 112
      Top = 2
      Width = 73
      Height = 21
      TabOrder = 0
      Text = '0'
      OnChange = EditChange
      OnExit = MengeEditExit
      OnKeyPress = CheckNumKeyPress
    end
    object MengeUpDown: TIntegerUpDown
      Left = 185
      Top = 2
      Width = 16
      Height = 21
      Associate = MengeEdit
      Max = 25000
      TabOrder = 1
      OnChangingEx = MengeUpDownChangingEx
    end
    object GewichtEdit: TEdit
      Left = 112
      Top = 26
      Width = 73
      Height = 21
      TabOrder = 2
      Text = 'GewichtEdit'
      OnChange = EditChange
      OnExit = GewichtEditExit
    end
  end
  object MHDPanel: TPanel
    Left = 0
    Top = 247
    Width = 508
    Height = 26
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    object MHDLabel: TLabel
      Left = 8
      Top = 5
      Width = 25
      Height = 13
      Caption = 'MHD'
    end
    object Label15: TLabel
      Left = 233
      Top = 6
      Width = 64
      Height = 13
      Alignment = taRightJustify
      Caption = 'Herstelldatum'
    end
    object MHDEdit: TEdit
      Left = 112
      Top = 2
      Width = 97
      Height = 21
      TabOrder = 0
      Text = 'MHDEdit'
      OnChange = EditChange
      OnExit = MHDEditExit
    end
    object ProdDatumEdit: TEdit
      Left = 303
      Top = 2
      Width = 81
      Height = 21
      TabOrder = 1
      Text = 'ProdDatumEdit'
      OnChange = EditChange
      OnExit = ProdDatumEditExit
    end
  end
  object ChargePanel: TPanel
    Left = 0
    Top = 273
    Width = 508
    Height = 24
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    object Label10: TLabel
      Left = 8
      Top = 5
      Width = 34
      Height = 13
      Caption = 'Charge'
    end
    object ChargeEdit: TEdit
      Left = 112
      Top = 2
      Width = 97
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'ChargeEdit'
      OnChange = EditChange
    end
  end
  object GrundPanel: TPanel
    Left = 0
    Top = 420
    Width = 508
    Height = 25
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 9
    ExplicitTop = 396
    DesignSize = (
      508
      25)
    object GrundLabel: TLabel
      Left = 8
      Top = 7
      Width = 76
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Retouren-Grund'
    end
    object GrundComboBox: TComboBox
      Left = 112
      Top = 4
      Width = 385
      Height = 21
      Style = csDropDownList
      Anchors = [akLeft, akBottom]
      MaxLength = 64
      TabOrder = 0
      OnChange = ComboBoxChange
    end
  end
  object CategoryPanel: TPanel
    Left = 0
    Top = 520
    Width = 508
    Height = 25
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 13
    ExplicitTop = 496
    DesignSize = (
      508
      25)
    object Label16: TLabel
      Left = 8
      Top = 7
      Width = 62
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Folgeprozess'
    end
    object CategoryComboBox: TComboBox
      Left = 112
      Top = 4
      Width = 385
      Height = 21
      Style = csDropDownList
      Anchors = [akLeft, akBottom]
      MaxLength = 64
      TabOrder = 0
      OnChange = CategoryComboBoxChange
    end
  end
  object QSPanel: TPanel
    Left = 0
    Top = 445
    Width = 508
    Height = 25
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 10
    ExplicitTop = 421
    DesignSize = (
      508
      25)
    object Label5: TLabel
      Left = 8
      Top = 7
      Width = 39
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Zustand'
    end
    object QSComboBox: TComboBox
      Left = 112
      Top = 4
      Width = 385
      Height = 21
      Style = csDropDownList
      Anchors = [akLeft, akBottom]
      MaxLength = 64
      TabOrder = 0
      OnChange = ComboBoxChange
    end
  end
  object ZustandPanel: TPanel
    Left = 0
    Top = 470
    Width = 508
    Height = 25
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 11
    ExplicitTop = 446
    DesignSize = (
      508
      25)
    object Label11: TLabel
      Left = 8
      Top = 7
      Width = 62
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Zustandskat.'
    end
    object ZustandComboBox: TComboBox
      Left = 112
      Top = 4
      Width = 385
      Height = 21
      Style = csDropDownList
      Anchors = [akLeft, akBottom]
      MaxLength = 64
      TabOrder = 0
      OnChange = ComboBoxChange
    end
  end
  object LEPanel: TPanel
    Left = 0
    Top = 345
    Width = 508
    Height = 75
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 8
    ExplicitTop = 321
    object GroupBox1: TGroupBox
      Left = 9
      Top = 3
      Width = 488
      Height = 67
      Caption = 'Lagerung'
      TabOrder = 0
      object Label13: TLabel
        Left = 9
        Top = 31
        Width = 33
        Height = 13
        Caption = 'LE-Nr.:'
      end
      object LEInfoLabel: TLabel
        Left = 96
        Top = 29
        Width = 183
        Height = 16
        AutoSize = False
        Caption = 'LEInfoLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label14: TLabel
        Left = 287
        Top = 12
        Width = 59
        Height = 13
        Caption = 'Neue LE-Nr.'
      end
      object Label17: TLabel
        Left = 368
        Top = 12
        Width = 84
        Height = 13
        Caption = 'Ladungstr'#228'ger-Art'
      end
      object LENrEdit: TEdit
        Left = 287
        Top = 28
        Width = 74
        Height = 21
        TabOrder = 0
        Text = 'LENrEdit'
        OnExit = LENrEditExit
      end
      object LTComboBox: TComboBox
        Left = 368
        Top = 28
        Width = 113
        Height = 21
        Style = csDropDownList
        Enabled = False
        TabOrder = 1
      end
    end
  end
  object QSHintPanel: TPanel
    Left = 0
    Top = 545
    Width = 508
    Height = 82
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 14
    ExplicitTop = 521
    DesignSize = (
      508
      82)
    object Label18: TLabel
      Left = 9
      Top = 8
      Width = 49
      Height = 13
      Caption = 'Zusatzinfo'
    end
    object QSHintMemo: TMemo
      Left = 112
      Top = 11
      Width = 385
      Height = 65
      Anchors = [akLeft, akTop, akRight, akBottom]
      Lines.Strings = (
        'QSHintMemo')
      MaxLength = 256
      TabOrder = 0
    end
  end
  object AbschlussPanel: TPanel
    Left = 0
    Top = 709
    Width = 508
    Height = 73
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 16
    ExplicitTop = 687
    DesignSize = (
      508
      73)
    object Bevel1: TBevel
      Left = 6
      Top = 28
      Width = 497
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 10
    end
    object FehlerLabel: TLabel
      Left = 0
      Top = 0
      Width = 508
      Height = 34
      Align = alTop
      Alignment = taCenter
      AutoSize = False
      Caption = 'FehlerLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      WordWrap = True
      ExplicitTop = 439
    end
    object OkButton: TButton
      Left = 334
      Top = 40
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Ok'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
    object AbortButton: TButton
      Left = 422
      Top = 40
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
  end
  object PalHeightPanel: TPanel
    Left = 0
    Top = 297
    Width = 508
    Height = 24
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 6
    object Label19: TLabel
      Left = 8
      Top = 5
      Width = 44
      Height = 13
      Caption = 'Pal-H'#246'he'
    end
    object Label20: TLabel
      Left = 194
      Top = 5
      Width = 16
      Height = 13
      Caption = 'mm'
    end
    object PalHeightEdit: TEdit
      Left = 112
      Top = 2
      Width = 73
      Height = 21
      MaxLength = 8
      TabOrder = 0
      Text = 'PalHeightEdit'
      OnChange = EditChange
      OnKeyPress = CheckNumKeyPress
    end
  end
  object BestandIDPanel: TPanel
    Left = 0
    Top = 133
    Width = 508
    Height = 32
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      508
      32)
    object Label21: TLabel
      Left = 8
      Top = 6
      Width = 53
      Height = 13
      Caption = 'Bestand ID'
    end
    object Bevel4: TBevel
      Left = 6
      Top = 28
      Width = 497
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object BestandIDLabel: TLabel
      Left = 112
      Top = 6
      Width = 76
      Height = 13
      Caption = 'BestandIDLabel'
    end
  end
  object ZustandCommentPanel: TPanel
    Left = 0
    Top = 495
    Width = 508
    Height = 25
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 12
    ExplicitTop = 471
    DesignSize = (
      508
      25)
    object Label23: TLabel
      Left = 8
      Top = 7
      Width = 54
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Bemerkung'
    end
    object ZustandCommentComboBox: TComboBox
      Left = 112
      Top = 4
      Width = 385
      Height = 21
      Style = csDropDownList
      Anchors = [akLeft, akBottom]
      MaxLength = 64
      TabOrder = 0
      OnChange = ComboBoxChange
    end
  end
  object HintPanel: TPanel
    Left = 0
    Top = 627
    Width = 508
    Height = 82
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 15
    ExplicitTop = 603
    DesignSize = (
      508
      82)
    object Label24: TLabel
      Left = 9
      Top = 8
      Width = 86
      Height = 13
      Caption = 'Annahmehinweise'
    end
    object HintMemo: TMemo
      Left = 112
      Top = 6
      Width = 385
      Height = 65
      Anchors = [akLeft, akTop, akRight, akBottom]
      Lines.Strings = (
        'HintMemo')
      MaxLength = 256
      TabOrder = 0
    end
  end
  object SerialPanel: TPanel
    Left = 0
    Top = 321
    Width = 508
    Height = 24
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 7
    ExplicitTop = 305
    object Label25: TLabel
      Left = 8
      Top = 5
      Width = 42
      Height = 13
      Caption = 'Seriennr.'
    end
    object SerialEdit: TEdit
      Left = 112
      Top = 2
      Width = 129
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'SerialEdit'
      OnChange = EditChange
    end
  end
end
