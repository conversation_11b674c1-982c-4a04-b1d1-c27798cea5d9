unit EditNachschubParamDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls;

type
  TEditNachschubParamForm = class(TForm)
    Bevel1: TBevel;
    OkButton: TButton;
    AbortButton: TButton;
    KommLPPanel: TPanel;
    Label5: TLabel;
    KommLPLabel: TLabel;
    ArtikelPanel: TPanel;
    Label4: TLabel;
    ARNrLabel: TLabel;
    NachschubPanel: TPanel;
    NachsGroupBox: TGroupBox;
    Label2: TLabel;
    Label3: TLabel;
    Label6: TLabel;
    MinBesEdit: TEdit;
    MinVPEEdit: TEdit;
    MaxVPEEdit: TEdit;
    c: TBevel;
    Bevel3: TBevel;
    LagerPanel: TPanel;
    Bevel4: TBevel;
    Label7: TLabel;
    LagerComboBox: TComboBoxPro;
    Label8: TLabel;
    ARTextLabel: TLabel;
    AutoNachCheckBox: TCheckBox;
    NachschubArtPanel: TPanel;
    Label1: TLabel;
    NachArtComboBox: TComboBoxPro;
    MHDReinCheckBox: TCheckBox;
    PlatzPanel: TPanel;
    Bevel5: TBevel;
    Label9: TLabel;
    LPComboBox: TComboBoxPro;
    ZonePanel: TPanel;
    Label10: TLabel;
    ZoneComboBox: TComboBoxPro;
    BereichPanel: TPanel;
    Label11: TLabel;
    LBComboBox: TComboBoxPro;
    Label12: TLabel;
    FullBesEdit: TEdit;
    procedure FormShow(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure NumEditKeyPress(Sender: TObject; var Key: Char);
    procedure AutoNachCheckBoxClick(Sender: TObject);
    procedure LagerComboBoxChange(Sender: TObject);
    procedure LBComboBoxChange(Sender: TObject);
    procedure ZoneComboBoxChange(Sender: TObject);
  private
    fRefRel    : Integer;
    fRefRelAr  : Integer;
    fRefLPRel  : Integer;
    fRefAr     : Integer;
    fRefKommLP : Integer;
    fRefLager  : Integer;
  public
    property RefRel    : Integer read fRefRel    write fRefRel;
    property RefRelAr  : Integer read fRefRelAr  write fRefRelAr;
    property RefLPRel  : Integer read fRefLPRel  write fRefLPRel;
    property RefAr     : Integer read fRefAr     write fRefAr;
    property RefKommLP : Integer read fRefKommLP write fRefKommLP;
    property RefLager  : Integer read fRefLager  write fRefKommLP;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DB, ADODB, LVSGlobalDaten, DatenModul, SprachModul, FrontendUtils, LVSDatenInterface, ResourceText;

procedure TEditNachschubParamForm.AutoNachCheckBoxClick(Sender: TObject);
begin
  //NachsGroupBox.Enabled := AutoNachCheckBox.Checked;
end;

procedure TEditNachschubParamForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  ref,
  maxbes,
  minbes,
  minvpeanz,
  maxvpeanz  : Integer;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := True;

    (*
    if not (AutoNachCheckBox.Enabled) then begin
      minbes    := -1;
      minvpeanz := -1;
      maxvpeanz := -1;
    end else begin
    *)
      if (Length (MinBesEdit.Text) = 0) then
        minbes := -1
      else if not TryStrToInt (MinBesEdit.Text, minbes) then begin
        CanClose := False;
      end else if (minbes < 0) then begin
        CanClose := False;
      end;

      if (Length (FullBesEdit.Text) = 0) then
        maxbes := -1
      else if not TryStrToInt (FullBesEdit.Text, maxbes) then begin
        CanClose := False;
      end else if (maxbes < 0) then begin
        CanClose := False;
      end;

      if CanClose  then begin
        if (Length (MinVPEEdit.Text) = 0) then
          minvpeanz := -1
        else if not TryStrToInt (MinVPEEdit.Text, minvpeanz) then begin
          CanClose := False;
        end else if (minvpeanz < 0) then begin
          CanClose := False;
        end;
      end;

      if CanClose  then begin
        if (Length (MaxVPEEdit.Text) = 0) then
          maxvpeanz := -1
        else if not TryStrToInt (MaxVPEEdit.Text, maxvpeanz) then begin
          CanClose := False;
        end else if (maxvpeanz < 0) then begin
          CanClose := False;
        end;
      end;
    //end;

    if CanClose then begin
      if (fRefRelAr > 0) then
        res := SetARLBNachschubParameter (fRefRelAr,
                                          GetComboBoxDBItemWert (NachArtComboBox),
                                          minbes,
                                          maxbes,
                                          minvpeanz,
                                          maxvpeanz)
      else if (fRefLPRel > 0) then
        res := SetLPARNachschubParameter (fRefLPRel, GetComboBoxDBItemWert (NachArtComboBox), GetCheckboxStat (MHDReinCheckBox), GetCheckboxStat (AutoNachCheckBox), minbes, minvpeanz, maxvpeanz)
      else begin
        if BereichPanel.Visible then begin
          res := InsertARLBNachschubParameter  (GetComboBoxRef (LagerComboBox),
                                                -1,
                                                fRefAr,
                                                GetComboBoxRef (LBComboBox),
                                                GetComboBoxRef (ZoneComboBox),
                                                GetComboBoxRef (LPComboBox),
                                                GetComboBoxDBItemWert (NachArtComboBox),
                                                minbes,
                                                maxbes,
                                                minvpeanz,
                                                maxvpeanz,
                                                ref);
          if (res = 0) then
            fRefRelAr := ref;
        end else begin
          if (fRefRel = -1) then
            res := SetNachschubParameter (fRefKommLP, fRefAr, GetComboBoxDBItemWert (NachArtComboBox), GetCheckboxStat (MHDReinCheckBox), GetCheckboxStat (AutoNachCheckBox), minbes, minvpeanz, maxvpeanz, ref)
          else if (RefKommLP > 0) then
            res := ChangeNachschubParameter (fRefRel, fRefAr, GetComboBoxDBItemWert (NachArtComboBox), GetCheckboxStat (MHDReinCheckBox), GetCheckboxStat (AutoNachCheckBox), minbes, minvpeanz, maxvpeanz)
          else
            res := SetArtikelNachschub (fRefRel, '', GetCheckboxStat (AutoNachCheckBox), minbes, minvpeanz, maxvpeanz);

          if (res = 0) then begin
            if (fRefRel = -1) then
              fRefRel := ref;
          end;
        end;
      end;

      if (res <> 0) then begin
        CanClose := False;
        MessageDLG(FormatMessageText (1787, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      end;
    end;
  end;
end;

procedure TEditNachschubParamForm.FormCreate(Sender: TObject);
begin
  fRefAr     := -1;
  fRefRel    := -1;
  fRefRelAr  := -1;
  fRefLPRel  := -1;
  fRefKommLP := -1;
  fRefLager  := -1;

  ARNrLabel.Caption := '';
  ARTextLabel.Caption := '';

  KommLPLabel.Caption := '';

  MinBesEdit.Text  := '';
  FullBesEdit.Text := '';
  MinVPEEdit.Text  := '';
  MaxVPEEdit.Text  := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, KommLPLabel);
    LVSSprachModul.SetNoTranslate (Self, ARNrLabel);
    LVSSprachModul.SetNoTranslate (Self, NachArtComboBox);
  {$endif}
end;

procedure TEditNachschubParamForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (NachArtComboBox);
end;

procedure TEditNachschubParamForm.FormShow(Sender: TObject);
var
  query : TADOQuery;
begin
  if (fRefRel <= 0) and (fRefAr <= 0) then begin
    ArtikelPanel.Visible := False;
    Height := Height - ArtikelPanel.Height;
  end;

  if (fRefKommLP <= 0) then begin
    KommLPPanel.Visible := False;
    Height := Height - KommLPPanel.Height;
  end else begin
    LagerPanel.Visible := False;
  end;

  if not (NachschubArtPanel.Visible) then begin
    NachschubPanel.Height := NachschubPanel.Height - NachschubArtPanel.Height;
    Height := Height - NachschubArtPanel.Height;
  end;

  if not (LagerPanel.Visible) then
    Height := Height - LagerPanel.Height
  else begin
    LoadLagerCombobox(LagerComboBox, LVSDatenModul.AktLocationRef);
  end;

  if not (BereichPanel.Visible) then
    Height := Height - BereichPanel.Height;

  if not (ZonePanel.Visible) then
    Height := Height - ZonePanel.Height;

  if not (PlatzPanel.Visible) then
    Height := Height - PlatzPanel.Height;

  if (NachschubArtPanel.Visible) then begin
    LoadComboxDBItems (NachArtComboBox, 'ARTIKEL_REL_LAGER', 'NACHSCHUB_ART');
    NachArtComboBox.Items.Insert (0, '');
  end;

  query := TADOQuery.Create (Nil);
  query.Connection := LVSDatenModul.MainADOConnection;

  try
    if (fRefRelAr > 0) then begin
      query.SQL.Add ('select'
                    +' rel.*'
                    +',lb.NAME as LB_NAME, lb.BESCHREIBUNG as LB_BESCHREIBUNG'
                    +',ar.ARTIKEL_NR,ar.ARTIKEL_TEXT'
                    +' from'
                    +' V_ARTIKEL_REL_NACHSCHUB rel'
                    +' left outer join V_ARTIKEL ar on (ar.REF=rel.REF_AR)'
                    +' left outer join V_LB lb on (lb.REF=rel.REF_LB)'
                    +' where rel.REF=:ref');
      query.Parameters.ParamByName ('ref').Value := fRefRelAr;
    end else if (fRefRel > 0) then begin
      if (fRefKommLP > 0) then begin
        query.SQL.Add ('select * from V_KOMM_LP_AR where REF=:ref');
        query.Parameters.ParamByName ('ref').Value := fRefRel;
      end else begin
        query.SQL.Add ('select rel.*,ar.ARTIKEL_NR,ar.ARTIKEL_TEXT from V_ARTIKEL_REL_LAGER rel left outer join V_ARTIKEL ar on (ar.REF=rel.REF_AR) where rel.REF=:ref');
        query.Parameters.ParamByName ('ref').Value := fRefRel;
      end;
    end else if (fRefLPRel > 0) then begin
      query.SQL.Add ('select * from V_ARTIKEL_REL_LP where REF=:ref');
      query.Parameters.ParamByName ('ref').Value := fRefLPRel;
    end else begin
      if (fRefLager > 0) then begin
        LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, fRefLager);
        LagerComboBox.Enabled := (LagerComboBox.ItemIndex >= 0);
      end else if (LVSDatenModul.AktLagerRef > 0) then begin
        LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, LVSDatenModul.AktLagerRef);
        LagerComboBox.Enabled := (LagerComboBox.ItemIndex >= 0);
      end else if (LagerComboBox.Items.Count = 1) then begin
        LagerComboBox.ItemIndex := 0;
        LagerComboBox.Enabled := false;
      end else
        LagerComboBox.ItemIndex := -1;

      if (LagerComboBox.ItemIndex >= 0) then
        LagerComboBoxChange (Sender);

      if (fRefKommLP > 0) then begin
        if (fRefAr <= -1) then
          query.SQL.Add ('select * from V_KOMM_LP_AR where REF_AR is null and REF_KOMM_LP=:ref_klp')
        else begin
          query.SQL.Add ('select * from V_KOMM_LP_AR where REF_AR=:ref_ar and REF_KOMM_LP=:ref_klp');
          query.Parameters.ParamByName ('ref_ar').Value := fRefAr;
        end;
        query.Parameters.ParamByName ('ref_klp').Value := fRefKommLP;
      end else if (fRefLager > 0) then begin
        if (fRefAr <= 0) then
          query.SQL.Add ('select *,null as ARTIKEL_NR, null as ARTIKEL_TEXT from V_ARTIKEL_REL_LAGER where REF_AR is null and REF_LAGER=:ref_lager')
        else begin
          query.SQL.Add ('select rel.*,ar.ARTIKEL_NR,ar.ARTIKEL_TEXT from V_ARTIKEL_REL_LAGER rel left outer join V_ARTIKEL ar on (ar.REF=rel.REF_AR) where (rel.REF_AR is null or (rel.REF_AR=:ref_ar)) and rel.REF_LAGER=:ref_lager order by rel.REF_AR nulls last');
          query.Parameters.ParamByName ('ref_ar').Value := fRefAr;
        end;

        query.Parameters.ParamByName ('ref_lager').Value := fRefLager;
      end;
    end;

    if (Length (query.SQL.Text) > 0) then
      query.Open;

    if (Length (query.SQL.Text) = 0) or (query.FieldByName ('REF').IsNull) then begin
      query.Close;

      query.SQL.Clear;

      if (fRefKommLP > 0) then begin
        if (fRefAr = -1) then
          query.SQL.Add ('select null as ARTIKEL_NR,null as ARTIKEL_TEXT,klp.LP_DISP from V_KOMM_LP klp where klp.REF=:ref_klp')
        else begin
          query.SQL.Add ('select ar.ARTIKEL_NR,ar.ARTIKEL_TEXT,klp.LP_DISP from V_ARTIKEL ar, V_KOMM_LP klp where ar.REF=:ref_ar and klp.REF=:ref_klp');
          query.Parameters.ParamByName ('ref_ar').Value := fRefAr;
        end;
        query.Parameters.ParamByName ('ref_klp').Value := fRefKommLP;

        query.Open;

        KommLPLabel.Caption := query.FieldByName ('LP_DISP').AsString;
        ARNrLabel.Caption   := query.FieldByName ('ARTIKEL_NR').AsString;
        ARTextLabel.Caption   := query.FieldByName ('ARTIKEL_TEXT').AsString;
      end else if (fRefAr > 0) then begin
        query.SQL.Add ('select ar.ARTIKEL_NR,ar.ARTIKEL_TEXT from V_ARTIKEL ar where ar.REF=:ref_ar');
        query.Parameters.ParamByName ('ref_ar').Value := fRefAr;

        query.Open;

        ARNrLabel.Caption   := query.FieldByName ('ARTIKEL_NR').AsString;
        ARTextLabel.Caption   := query.FieldByName ('ARTIKEL_TEXT').AsString;
      end;
    end else if (fRefRelAr > 0) then begin
      LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, query.FieldByName ('REF_LAGER').AsInteger);

      if not (query.FieldByName ('REF_LB').IsNull) then begin
        LagerComboBoxChange (Sender);

        LBComboBox.ItemIndex := FindComboboxRef (LBComboBox, query.FieldByName ('REF_LB').AsInteger);

        if not (query.FieldByName ('REF_LB_ZONE').IsNull) then begin
          LBComboBoxChange (Sender);

          ZoneComboBox.ItemIndex := FindComboboxRef (ZoneComboBox, query.FieldByName ('REF_LB_ZONE').AsInteger);

          if not (query.FieldByName ('REF_LP').IsNull) then begin
            ZoneComboBoxChange (Sender);

            LPComboBox.ItemIndex := FindComboboxRef (LPComboBox, query.FieldByName ('REF_LP').AsInteger);
          end;
        end else if not (query.FieldByName ('REF_LP').IsNull) then begin
          LBComboBoxChange (Sender);

          LPComboBox.ItemIndex := FindComboboxRef (LPComboBox, query.FieldByName ('REF_LP').AsInteger);
        end;
      end;

      LagerComboBox.Enabled := false;
      LBComboBox.Enabled := false;
      ZoneComboBox.Enabled := false;
      LPComboBox.Enabled := false;

      ARNrLabel.Caption   := query.FieldByName ('ARTIKEL_NR').AsString;
      ARTextLabel.Caption   := query.FieldByName ('ARTIKEL_TEXT').AsString;

      if (query.FieldByName ('MIN_MENGE_BES').IsNull) then
        MinBesEdit.Text := ''
      else
        MinBesEdit.Text := query.FieldByName ('MIN_MENGE_BES').AsString;

      if (query.FieldByName ('MAX_MENGE_BES').IsNull) then
        FullBesEdit.Text := ''
      else
        FullBesEdit.Text := query.FieldByName ('MAX_MENGE_BES').AsString;

      if (query.FieldByName ('MIN_MENGE_NACH').IsNull) then
        MinVPEEdit.Text := ''
      else
        MinVPEEdit.Text := query.FieldByName ('MIN_MENGE_NACH').AsString;

      if (query.FieldByName ('MAX_MENGE_NACH').IsNull) then
        MaxVPEEdit.Text := ''
      else
        MaxVPEEdit.Text := query.FieldByName ('MAX_MENGE_NACH').AsString;
    end else begin
      LagerComboBox.Enabled := False;

      fRefRel   := query.FieldByName ('REF').AsInteger;
      fRefLager := query.FieldByName ('REF_LAGER').AsInteger;

      if (fRefKommLP > 0) then begin
        KommLPLabel.Caption := query.FieldByName ('LP_DISP').AsString;

        if (query.FieldByName ('MHD_REIN').IsNull) then
          MHDReinCheckBox.State := cbGrayed
        else if query.FieldByName ('MHD_REIN').AsString = '0' then
          MHDReinCheckBox.State := cbUnchecked
        else
          MHDReinCheckBox.State := cbChecked;
      end else begin
        LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, query.FieldByName ('REF_LAGER').AsInteger);
      end;

      ARNrLabel.Caption   := query.FieldByName ('ARTIKEL_NR').AsString;
      ARTextLabel.Caption   := query.FieldByName ('ARTIKEL_TEXT').AsString;

      if (NachschubArtPanel.Visible) then begin
        NachArtComboBox.ItemIndex := FindComboboxDBItemWert (NachArtComboBox, query.FieldByName ('NACHSCHUB_ART').AsString);
        if (NachArtComboBox.ItemIndex = -1) then NachArtComboBox.ItemIndex := 0;
      end;

      if (query.FieldByName ('AUTO_NACHSCHUB').IsNull) then
        AutoNachCheckBox.State := cbGrayed
      else if query.FieldByName ('AUTO_NACHSCHUB').AsString = '0' then
        AutoNachCheckBox.State := cbUnchecked
      else
        AutoNachCheckBox.State := cbChecked;

      if (query.FieldByName ('MIN_NACH_BES').IsNull) then
        MinBesEdit.Text := ''
      else
        MinBesEdit.Text := query.FieldByName ('MIN_NACH_BES').AsString;

      if (query.FieldByName ('MIN_NACH_VPE').IsNull) then
        MinVPEEdit.Text := ''
      else
        MinVPEEdit.Text := query.FieldByName ('MIN_NACH_VPE').AsString;

      if (query.FieldByName ('MAX_NACH_VPE').IsNull) then
        MaxVPEEdit.Text := ''
      else
        MaxVPEEdit.Text := query.FieldByName ('MAX_NACH_VPE').AsString;
    end;

    query.Close;
  finally
    query.Free;
  end;

  AutoNachCheckBoxClick (Sender);
end;

procedure TEditNachschubParamForm.LagerComboBoxChange(Sender: TObject);
begin
  if (GetComboBoxRef (LagerComboBox) > 0) then begin
    LoadLBCombobox (LBComboBox, '', GetComboBoxRef (LagerComboBox));

    LBComboBox.Items.Insert (0, '');
    LBComboBox.ItemIndex := 0;
  end;
end;

procedure TEditNachschubParamForm.LBComboBoxChange(Sender: TObject);
begin
  if (GetComboBoxRef (LBComboBox) > 0) then begin
    LoadLBZoneCombobox (ZoneComboBox, GetComboBoxRef (LBComboBox));

    if (ZoneComboBox.Items.Count > 0) then begin
      ZoneComboBox.Items.Insert (0, '');
      ZoneComboBox.ItemIndex := 0;
      ZoneComboBox.Enabled := True;

      LPComboBox.Enabled := False;
      ClearComboBoxObjects (LPComboBox);
    end else begin
      ZoneComboBox.Enabled := False;
      LoadLPCombobox (LPComboBox, GetComboBoxRef (LBComboBox), false, -1, -1);
      LPComboBox.Enabled := (LPComboBox.Items.Count > 0);
    end;
  end;
end;

procedure TEditNachschubParamForm.NumEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,^C,^V, '0'..'9']) then begin
    Beep;
    Key := #0;
  end;
end;

procedure TEditNachschubParamForm.ZoneComboBoxChange(Sender: TObject);
begin
  if (GetComboBoxRef (ZoneComboBox) > 0) then begin
    LoadLPZoneCombobox (LPComboBox, GetComboBoxRef (ZoneComboBox), false, -1, -1);
    LPComboBox.Enabled := (LPComboBox.Items.Count > 0);
  end else begin
    LoadLPZoneCombobox (LPComboBox, -1, false, -1, -1);
    LPComboBox.Enabled := (LPComboBox.Items.Count > 0);
  end;
end;

end.
