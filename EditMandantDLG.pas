﻿unit EditMandantDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls, ComCtrls, MandAdrFRM, CheckLst, FrontendUtils, Vcl.NumberBox;

type
  TEditMandantForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    PageControl1: TPageControl;
    StammTabSheet: TTabSheet;
    ConfigTabSheet: TTabSheet;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label5: TLabel;
    Label6: TLabel;
    Label7: TLabel;
    Label8: TLabel;
    Bevel2: TBevel;
    Label9: TLabel;
    NameEdit: TEdit;
    BeschreibungEdit: TEdit;
    ILNEdit: TEdit;
    AdrEdit: TEdit;
    RoadEdit: TEdit;
    PLZEdit: TEdit;
    OrtEdit: TEdit;
    LandEdit: TEdit;
    GroupBox1: TGroupBox;
    BesChangeGrundCheckBox: TCheckBox;
    BesDelGrundCheckBox: TCheckBox;
    BesCreateGrundCheckBox: TCheckBox;
    BesLockGrundCheckBox: TCheckBox;
    BesUnlockGrundCheckBox: TCheckBox;
    OptionPageControl: TPageControl;
    QSTabSheet: TTabSheet;
    Label19: TLabel;
    QSWEComboBox: TComboBoxPro;
    Label20: TLabel;
    QSRETComboBox: TComboBoxPro;
    InvTabSheet: TTabSheet;
    LiefRetTabSheet: TTabSheet;
    LiefRetBesCheckBox: TCheckBox;
    LiefRetWECheckBox: TCheckBox;
    InvEinzelScanCheckBox: TCheckBox;
    InvBesAbgleichOldMHDCheckBox: TCheckBox;
    WETabSheet: TTabSheet;
    WEPicCheckBox: TCheckBox;
    WELastMHDCheckBox: TCheckBox;
    BesQSGrundCheckBox: TCheckBox;
    BesINVGrundCheckBox: TCheckBox;
    WEQSGrundCheckBox: TCheckBox;
    AvisTabSheet: TTabSheet;
    GroupBox2: TGroupBox;
    Label12: TLabel;
    AVISMailLSCheckBox: TCheckBox;
    AvisMailEdit: TEdit;
    AVISMailAvisCheckBox: TCheckBox;
    PackTabSheet: TTabSheet;
    VerteilPicCheckBox: TCheckBox;
    RetQSGrundCheckBox: TCheckBox;
    RetCatAuswahlCheckBox: TCheckBox;
    RetSelZustandCheckBox: TCheckBox;
    RetSelGrundCheckBox: TCheckBox;
    InvAssignPosCheckBox: TCheckBox;
    WEArEinheitOptCheckBox: TCheckBox;
    WEBedarfCheckBox: TCheckBox;
    ArtikelTabSheet: TTabSheet;
    ArtikelStartEdit: TEdit;
    ArtikelEndEdit: TEdit;
    ArtikelSuffixEdit: TEdit;
    Label10: TLabel;
    ArtikelLenEdit: TEdit;
    Label11: TLabel;
    Label13: TLabel;
    Label14: TLabel;
    ArtikelNextEdit: TEdit;
    Label15: TLabel;
    UseInternalArtikelNrCheckBox: TCheckBox;
    WEKommBestandCheckBox: TCheckBox;
    WENachsBestandCheckBox: TCheckBox;
    BestandTabSheet: TTabSheet;
    UniqueChargenCheckBox: TCheckBox;
    InterfaceTabSheet: TTabSheet;
    IFCAbgleichAufEmpfCheckBox: TCheckBox;
    AufDeleteGrundCheckBox: TCheckBox;
    AufStornoGrundCheckBox: TCheckBox;
    RetProcessPreGradingCheckBox: TCheckBox;
    ERPIDEdit: TEdit;
    DXFEdit: TEdit;
    Label16: TLabel;
    Label17: TLabel;
    ShortNameEdit: TEdit;
    Label18: TLabel;
    BestTabSheet: TTabSheet;
    BestNrUniqueCheckBox: TCheckBox;
    BestEnabledCheckBox: TCheckBox;
    AufTabSheet: TTabSheet;
    AufEnabledCheckBox: TCheckBox;
    VerpacklPicCheckBox: TCheckBox;
    RetAutoPrintCheckBox: TCheckBox;
    RetPrintLableCheckBox: TCheckBox;
    VerpackungCheckBox: TCheckBox;
    WALHMCheckBox: TCheckBox;
    WALHMAutoCheckBox: TCheckBox;
    RetGrundPflichtCheckBox: TCheckBox;
    Label21: TLabel;
    Label22: TLabel;
    RetAvisAutoBuchtCheckBox: TCheckBox;
    BestandBelegCheckBox: TCheckBox;
    TexteTabSheet: TTabSheet;
    BioKontrollNrEdit: TEdit;
    StdPickHintEdit: TEdit;
    StdPackHintEdit: TEdit;
    Label23: TLabel;
    Label24: TLabel;
    Label25: TLabel;
    WELeerProPosCheckBox: TCheckBox;
    WEArtikelDatenCheckBox: TCheckBox;
    IFCStammdatenUpdateCheckBox: TCheckBox;
    VersandTabSheet: TTabSheet;
    RetourenLabelCheckBox: TCheckBox;
    RetourenScheinCheckBox: TCheckBox;
    LieferscheinCheckBox: TCheckBox;
    MandDatenTabSheet: TTabSheet;
    GroupBox3: TGroupBox;
    StammIBANEdit: TEdit;
    Label35: TLabel;
    Label36: TLabel;
    StammBICEdit: TEdit;
    Label37: TLabel;
    StammBankEdit: TEdit;
    GroupBox4: TGroupBox;
    Label31: TLabel;
    StammGFEdit: TEdit;
    StammURLEdit: TEdit;
    Label32: TLabel;
    Label34: TLabel;
    StammTaxNrEdit: TEdit;
    Label38: TLabel;
    StammContactEdit: TEdit;
    Label39: TLabel;
    StammMailEdit: TEdit;
    Label40: TLabel;
    StammVATEdit: TEdit;
    Label41: TLabel;
    StammHREdit: TEdit;
    Label42: TLabel;
    StammLogoEdit: TEdit;
    StammLogoButton: TButton;
    WEArAbmessungCheckBox: TCheckBox;
    WEArAttrCheckBox: TCheckBox;
    WEArBarcodeCheckBox: TCheckBox;
    WEArShipperCheckBox: TCheckBox;
    WEArPalDatenCheckBox: TCheckBox;
    ZollGroupBox: TGroupBox;
    BaseTaricNrEdit: TEdit;
    AutoZollAnmeldungCheckBox: TCheckBox;
    Label43: TLabel;
    AufEmpfSelectCheckBox: TCheckBox;
    AufVorResCheckBox: TCheckBox;
    InvBesAbgleichANGCheckBox: TCheckBox;
    RetZustandCommentCheckBox: TCheckBox;
    WEArGefahrstoffeCheckBox: TCheckBox;
    WEArKlasseCheckBox: TCheckBox;
    ReturnHintMemo: TMemo;
    WEMehrMengeCheckBox: TCheckBox;
    AufPrioGroupBox: TGroupBox;
    IFCPrioCheckBox: TCheckBox;
    MaxPrioEdit: TEdit;
    Label44: TLabel;
    WEBeschaffCheckBox: TCheckBox;
    RetVASCheckBox: TCheckBox;
    WEBBDPastCheckBox: TCheckBox;
    Label33: TLabel;
    StammEORIEdit: TEdit;
    ArContenIntCheckBox: TCheckBox;
    Label45: TLabel;
    PackTypeGroupComboBox: TComboBoxPro;
    AufCheckAdrCheckBox: TCheckBox;
    AufAdrCorrCityCheckBox: TCheckBox;
    AufAdrCorrStrCheckBox: TCheckBox;
    AufAdrCorrCheckBox: TCheckBox;
    PageControl2: TPageControl;
    AbsTabSheet: TTabSheet;
    MandantAbsenderFrame: TMandAdrFrame;
    AdrTabSheet: TTabSheet;
    MandantAdressFrame: TMandAdrFrame;
    WEDutyGroupBox: TGroupBox;
    WESuppDutyCheckBox: TCheckBox;
    WEKFZDutyCheckBox: TCheckBox;
    WELSNoDutyCheckBox: TCheckBox;
    BesLockIDCheckBox: TCheckBox;
    WECategoryCheckBox: TCheckBox;
    WEPackTypeDutyCheckBox: TCheckBox;
    InvOptionGroupBox: TGroupBox;
    InvOptSingleScanCheckBox: TCheckBox;
    InvOptGoLPCheckBox: TCheckBox;
    LSDatumMaxNumberBox: TNumberBox;
    InvSuggestArtikelCheckBox: TCheckBox;
    InvOptNurMengeCheckBox: TCheckBox;
    InvSuggestLEBesCheckBox: TCheckBox;
    InvChkOptionGroupBox: TGroupBox;
    InvChkOptSingleScanCheckBox: TCheckBox;
    InvChkOptGoLPCheckBox: TCheckBox;
    InvChkSuggestArtikelCheckBox: TCheckBox;
    InvChkOptNurMengeCheckBox: TCheckBox;
    InvChkSuggestLEBesCheckBox: TCheckBox;
    BesMandGroupBox: TGroupBox;
    BesMandantCheckListBox: TCheckListBox;
    BesMandOnlyCheckBox: TCheckBox;
    InvChkAreaCheckBox: TCheckBox;
    ArtikelPrefixEdit: TEdit;
    Label4: TLabel;
    Label46: TLabel;
    CurrencyComboBox: TComboBoxPro;
    Label26: TLabel;
    LieferAdrCheckBox: TCheckBox;
    WEAnzPackStCheckBox: TCheckBox;
    WEUseDefaultLTCheckBox: TCheckBox;
    procedure FormShow(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure ArtikelNrEditKeyPress(Sender: TObject; var Key: Char);
    procedure StammLogoButtonClick(Sender: TObject);
    procedure AufCheckAdrCheckBoxClick(Sender: TObject);
    procedure AufAdrCorrCheckBoxClick(Sender: TObject);
    function getBesMandanten(const RefMand: Integer): TArryOfInteger;
    procedure BesMandOnlyCheckBoxClick(Sender: TObject);
  private
    fStammLogoName    : String;
    fOldStammLogoName : String;
    fLogoBaseDir      : String;
    fMaxLogoDirLen    : Integer;
    fRefMand          : Integer;
  public
    property StammLogoName : String read fStammLogoName;

    procedure Prepare (const RefMand, RefMasterMand, ConfigTab : Integer);
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, ADODB, Win32Utils, LVSConst, DatenModul, ConfigModul, ExtDlgs, ResourceText, LVSDatenInterface,
  SprachModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditMandantForm.FormCreate(Sender: TObject);
begin
  fStammLogoName := '';
  fLogoBaseDir   := '';
  fMaxLogoDirLen := -1;

  NameEdit.Text := '';
  BeschreibungEdit.Text := '';
  ILNEdit.Text  := '';
  AdrEdit.Text  := '';
  RoadEdit.Text := '';
  PLZEdit.Text  := '';
  OrtEdit.Text  := '';
  LandEdit.Text := '';

  BioKontrollNrEdit.Text := '';
  StdPickHintEdit.Text   := '';
  StdPackHintEdit.Text   := '';

  AvisMailEdit.Text := '';

  ArtikelStartEdit.Text  := '';
  ArtikelEndEdit.Text    := '';
  ArtikelSuffixEdit.Text := '';
  ArtikelLenEdit.Text    := '';
  ArtikelNextEdit.Text   := '';

  BaseTaricNrEdit.Text   := '';

  ReturnHintMemo.Text    := '';

  CurrencyComboBox.ItemIndex := 0;

  PageControl1.ActivePage := StammTabSheet;
  OptionPageControl.ActivePage := OptionPageControl.FindNextPage (Nil, True, True);

  AufVorResCheckBox.Visible := (LVSConfigModul.UseVorRes);

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul .SetNoTranslate(Self, BesMandantCheckListBox);
    LVSSprachModul.SetNoTranslate (Self, QSWEComboBox);
    LVSSprachModul.SetNoTranslate (Self, QSRETComboBox);
    LVSSprachModul.SetNoTranslate (Self, PackTypeGroupComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditMandantForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (QSWEComboBox);
  ClearComboBoxObjects (QSRETComboBox);
  ClearComboBoxObjects (PackTypeGroupComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditMandantForm.Prepare (const RefMand, RefMasterMand, ConfigTab : Integer);
var
  opt   : char;
  query : TADOQuery;
begin
  fRefMand := RefMand;

  if (ConfigTab = -1) then
    PageControl1.ActivePage := StammTabSheet
  else begin
    PageControl1.ActivePage := ConfigTabSheet;

    if (ConfigTab > 0) and (ConfigTab < OptionPageControl.PageCount) and OptionPageControl.Pages [ConfigTab].TabVisible then
      OptionPageControl.ActivePageIndex := ConfigTab
    else
      OptionPageControl.ActivePageIndex := 0;
  end;

  QSTabSheet.TabVisible := LVSConfigModul.UseQS;

  QSWEComboBox.Items.Add ('');
  QSRETComboBox.Items.Add ('');

  LoadComboxDBItems (PackTypeGroupComboBox, 'LT_TYPEN', 'LT_PACKING_GROUP');
  if (PackTypeGroupComboBox.Items.Count = 0) then
    PackTypeGroupComboBox.Enabled := false
  else
    PackTypeGroupComboBox.Items.Insert (0, '');

  LoadComboxDBItems (CurrencyComboBox, 'MANDANT', 'CURRENCY');
  if (CurrencyComboBox.Items.Count = 0) then
    CurrencyComboBox.Enabled := false
  else
    CurrencyComboBox.Items.Insert (0, '');

  query := TADOQuery.Create (self);
  try
    query.Connection := LVSDatenModul.MainADOConnection;

    if not LVSDatenModul.ViewExits ('V_MANDANT_REL_BES_MAND') then
      BesMandGroupBox.Visible := false
    else begin
      if not LVSDatenModul.FunctionExits ('PA_MANDANT', 'INSERT_MANDANT_BES_MANDANT') then
        BesMandantCheckListBox.Enabled := false;
    end;

    if (RefMand <= 0) then begin
      fStammLogoName := '';

      NameEdit.Text         := '';
      BeschreibungEdit.Text := '';
      ILNEdit.Text          := '';
      ShortNameEdit.Text    := '';
      ERPIDEdit.Text        := '';
      DXFEdit.Text          := '';
      StammLogoEdit.Text    := '';
      StammURLEdit.Text     := '';
      StammGFEdit.Text      := '';
      StammContactEdit.Text := '';
      StammMailEdit.Text    := '';
      StammTaxNrEdit.Text   := '';
      StammVATEdit.Text     := '';
      StammHREdit.Text      := '';
      StammBankEdit.Text    := '';
      StammIBANEdit.Text    := '';
      StammBICEdit.Text     := '';

      query.SQL.Add ('select m.*, cfg.* from V_MANDANT m, V_MANDANT_CONFIG cfg where cfg.REF_MAND=m.REF and m.REF is null');

      query.Open;

      if not Assigned (query.FindField('BIO_KONTROLL_NR')) then
        BioKontrollNrEdit.Visible := False;

      if not Assigned (query.FindField('DEFAULT_PICK_HINWEIS')) then
        StdPickHintEdit.Visible := False;

      if not Assigned (query.FindField('DEFAULT_PACK_HINWEIS')) then
        StdPackHintEdit.Visible := False;

      if not Assigned (query.FindField('MANAGER_NAME')) then
        MandDatenTabSheet.TabVisible := False;

      if not Assigned (query.FindField('OPT_AUTO_CUSTOMS')) then
        ZollGroupBox.Visible := False;

      if not Assigned (query.FindField('LOGO_NAME')) then
        StammLogoEdit.Visible := False
      else
        fMaxLogoDirLen := query.FindField('LOGO_NAME').Size;

      if not Assigned (query.FindField('LT_PACKING_GROUP')) then
        PackTypeGroupComboBox.Visible := false;

      if not Assigned (query.FindField('INV_OPTIONS')) then
        InvOptionGroupBox.Visible := false;

      if not Assigned (query.FindField('INV_CHECK_OPTIONS')) then
        InvChkOptionGroupBox.Visible := false;

      if not Assigned (query.FindField('ARTIKEL_NR_PREFIX')) then
        ArtikelPrefixEdit.Visible := false;

      if not Assigned (query.FindField('CURRENCY')) then
        CurrencyComboBox.Visible := false;

      query.Close;

      QSWEComboBox.ItemIndex := 0;
      QSRETComboBox.ItemIndex := 0;

      if (RefMasterMand > 0) then begin
        BesCreateGrundCheckBox.AllowGrayed := True;
        BesChangeGrundCheckBox.AllowGrayed := True;
        BesDelGrundCheckBox.AllowGrayed := True;
        BesLockGrundCheckBox.AllowGrayed := True;
        BesLockIDCheckBox.AllowGrayed := True;
        BesUnlockGrundCheckBox.AllowGrayed := True;
        BesQSGrundCheckBox.AllowGrayed := True;
        BesINVGrundCheckBox.AllowGrayed := True;
        LiefRetBesCheckBox.AllowGrayed := True;
        LiefRetWECheckBox.AllowGrayed := True;
        InvEinzelScanCheckBox.AllowGrayed := True;
        InvAssignPosCheckBox.AllowGrayed := True;
        InvBesAbgleichOldMHDCheckBox.AllowGrayed := True;
        InvBesAbgleichANGCheckBox.AllowGrayed := True;
        BestNrUniqueCheckBox.AllowGrayed := True;
        WEPicCheckBox.AllowGrayed := True;
        WEBeschaffCheckBox.AllowGrayed := True;
        WEBBDPastCheckBox.AllowGrayed := True;
        WELastMHDCheckBox.AllowGrayed := True;
        WELSNoDutyCheckBox.AllowGrayed := True;
        WEKFZDutyCheckBox.AllowGrayed := True;
        WESuppDutyCheckBox.AllowGrayed := True;
        WEPackTypeDutyCheckBox.AllowGrayed := True;
        WEQSGrundCheckBox.AllowGrayed := True;
        WECategoryCheckBox.AllowGrayed := True;
        WEArEinheitOptCheckBox.AllowGrayed := True;
        WEBedarfCheckBox.AllowGrayed := True;
        WEKommBestandCheckBox.AllowGrayed := True;
        WENachsBestandCheckBox.AllowGrayed := True;
        WELeerProPosCheckBox.AllowGrayed := True;

        WEArtikelDatenCheckBox.AllowGrayed := True;
        WEArAbmessungCheckBox.AllowGrayed := True;
        WEArPalDatenCheckBox.AllowGrayed := True;
        WEArAttrCheckBox.AllowGrayed := True;
        WEArBarcodeCheckBox.AllowGrayed := True;
        WEArShipperCheckBox.AllowGrayed := True;
        WEArGefahrstoffeCheckBox.AllowGrayed := True;
        WEArKlasseCheckBox.AllowGrayed := True;
        WEMehrMengeCheckBox.AllowGrayed := True;

        RetAutoPrintCheckBox.AllowGrayed := True;
        RetPrintLableCheckBox.AllowGrayed := True;
        RetVASCheckBox.AllowGrayed := True;
        RetAvisAutoBuchtCheckBox.AllowGrayed := True;
        RetQSGrundCheckBox.AllowGrayed := True;
        RetQSGrundCheckBox.AllowGrayed := True;
        RetZustandCommentCheckBox.AllowGrayed := True;
        RetSelZustandCheckBox.AllowGrayed := True;
        RetSelGrundCheckBox.AllowGrayed := True;
        RetGrundPflichtCheckBox.AllowGrayed := True;
        RetProcessPreGradingCheckBox.AllowGrayed := True;
        AufStornoGrundCheckBox.AllowGrayed := True;
        AufDeleteGrundCheckBox.AllowGrayed := True;
        UniqueChargenCheckBox.AllowGrayed := True;
        BestandBelegCheckBox.AllowGrayed := True;
        BestEnabledCheckBox.AllowGrayed := True;
        AufEnabledCheckBox.AllowGrayed := True;
        AufEmpfSelectCheckBox.AllowGrayed := True;
      end;
    end else begin
      if QSTabSheet.TabVisible then begin
        query.SQL.Clear;
        query.SQL.Add ('select * from V_ARTIKEL_QS_GROUP where REF_MAND='+IntToStr (RefMand)+' and REF_LAGER is null');

        query.Open;

        while not (query.Eof) do begin
          QSWEComboBox.AddItem (query.FieldByName('NAME').AsString+'|'+query.FieldByName('BESCHREIBUNG').AsString, TComboBoxRef.Create (query.FieldByName('REF').AsInteger));
          QSRETComboBox.AddItem (query.FieldByName('NAME').AsString+'|'+query.FieldByName('BESCHREIBUNG').AsString, TComboBoxRef.Create (query.FieldByName('REF').AsInteger));

          query.Next;
        end;

        query.Close;
      end;

      query.SQL.Clear;
      query.SQL.Add ('select m.*, cfg.* from V_MANDANT m, V_MANDANT_CONFIG cfg where cfg.REF_MAND=m.REF and m.REF=:ref');
      query.Parameters [0].Value := RefMand;

      query.Open;

      NameEdit.Text         := query.FieldByName ('NAME').AsString;
      BeschreibungEdit.Text := query.FieldByName ('BESCHREIBUNG').AsString;
      ILNEdit.Text          := query.FieldByName ('ILN').AsString;
      ShortNameEdit.Text    := query.FieldByName ('SHORT_NAME').AsString;
      ERPIDEdit.Text        := query.FieldByName ('ERP_ID').AsString;
      DXFEdit.Text          := query.FieldByName ('DXF_ID').AsString;

      AdrEdit.Text          := query.FieldByName ('ADR_ZUSATZ').AsString;
      RoadEdit.Text         := query.FieldByName ('STRASSE').AsString;
      PLZEdit.Text          := query.FieldByName ('PLZ').AsString;
      OrtEdit.Text          := query.FieldByName ('ORT').AsString;
      LandEdit.Text         := query.FieldByName ('LAND').AsString;

      if not Assigned (query.FindField('BIO_KONTROLL_NR')) then
        BioKontrollNrEdit.Visible := False
      else
        BioKontrollNrEdit.Text := query.FieldByName ('BIO_KONTROLL_NR').AsString;

      if not Assigned (query.FindField('DEFAULT_PICK_HINWEIS')) then
        StdPickHintEdit.Visible := False
      else
        StdPickHintEdit.Text := query.FieldByName ('DEFAULT_PICK_HINWEIS').AsString;

      if not Assigned (query.FindField('DEFAULT_PACK_HINWEIS')) then
        StdPackHintEdit.Visible := False
      else
        StdPackHintEdit.Text := query.FieldByName ('DEFAULT_PACK_HINWEIS').AsString;

//      if (query.FieldByName ('REF_BES_MAND').IsNull) then
//        BesMandComboBox.ItemIndex := 0
//      else
//        BesMandComboBox.ItemIndex := FindMandantRef (BesMandComboBox, query.FieldByName ('REF_BES_MAND').AsInteger);

      if not Assigned (query.FindField('LT_PACKING_GROUP')) then
        PackTypeGroupComboBox.Visible := false
      else if (query.FieldByName('LT_PACKING_GROUP').IsNull) then
        PackTypeGroupComboBox.ItemIndex := 0
      else begin
        PackTypeGroupComboBox.ItemIndex := FindComboboxDBItem (PackTypeGroupComboBox, query.FieldByName('LT_PACKING_GROUP').AsString);

        if (PackTypeGroupComboBox.ItemIndex = -1) then
          PackTypeGroupComboBox.ItemIndex := PackTypeGroupComboBox.Items.AddObject(query.FieldByName('LT_PACKING_GROUP').AsString, TDBItemsDaten.Create ('LT_PACKING_GROUP', query.FieldByName('LT_PACKING_GROUP').AsString, query.FieldByName('LT_PACKING_GROUP').AsString, '', ''));
      end;

      if not (query.FieldByName ('REF_MASTER_MAND').IsNull) then begin
        BesCreateGrundCheckBox.AllowGrayed := True;
        BesChangeGrundCheckBox.AllowGrayed := True;
        BesDelGrundCheckBox.AllowGrayed := True;
        BesLockGrundCheckBox.AllowGrayed := True;
        BesLockIDCheckBox.AllowGrayed := True;
        BesUnlockGrundCheckBox.AllowGrayed := True;
        BesQSGrundCheckBox.AllowGrayed := True;
        BesINVGrundCheckBox.AllowGrayed := True;
        LiefRetBesCheckBox.AllowGrayed := True;
        LiefRetWECheckBox.AllowGrayed := True;
        InvEinzelScanCheckBox.AllowGrayed := True;
        InvAssignPosCheckBox.AllowGrayed := True;
        InvBesAbgleichOldMHDCheckBox.AllowGrayed := True;
        InvBesAbgleichANGCheckBox.AllowGrayed := True;
        BestNrUniqueCheckBox.AllowGrayed := True;
        WEPicCheckBox.AllowGrayed := True;
        WEBeschaffCheckBox.AllowGrayed := True;
        WEBBDPastCheckBox.AllowGrayed := True;
        WELastMHDCheckBox.AllowGrayed := True;
        WELSNoDutyCheckBox.AllowGrayed := True;
        WEKFZDutyCheckBox.AllowGrayed := True;
        WESuppDutyCheckBox.AllowGrayed := True;
        WEPackTypeDutyCheckBox.AllowGrayed := True;
        WEQSGrundCheckBox.AllowGrayed := True;
        WECategoryCheckBox.AllowGrayed := True;
        WEArEinheitOptCheckBox.AllowGrayed := True;
        WEBedarfCheckBox.AllowGrayed := True;
        WEKommBestandCheckBox.AllowGrayed := True;
        WENachsBestandCheckBox.AllowGrayed := True;
        WELeerProPosCheckBox.AllowGrayed := True;

        WEArtikelDatenCheckBox.AllowGrayed := True;
        WEArAbmessungCheckBox.AllowGrayed := True;
        WEArPalDatenCheckBox.AllowGrayed := True;
        WEArAttrCheckBox.AllowGrayed := True;
        WEArBarcodeCheckBox.AllowGrayed := True;
        WEArShipperCheckBox.AllowGrayed := True;
        WEArGefahrstoffeCheckBox.AllowGrayed := True;
        WEArKlasseCheckBox.AllowGrayed := True;
        WEMehrMengeCheckBox.AllowGrayed := True;
        WEUseDefaultLTCheckBox.AllowGrayed := True;

        RetAutoPrintCheckBox.AllowGrayed := True;
        RetPrintLableCheckBox.AllowGrayed := True;
        RetAvisAutoBuchtCheckBox.AllowGrayed := True;
        RetQSGrundCheckBox.AllowGrayed := True;
        RetZustandCommentCheckBox.AllowGrayed := True;
        RetCatAuswahlCheckBox.AllowGrayed := True;
        RetSelZustandCheckBox.AllowGrayed := True;
        RetSelGrundCheckBox.AllowGrayed := True;
        RetGrundPflichtCheckBox.AllowGrayed := True;
        RetProcessPreGradingCheckBox.AllowGrayed := True;
        AufStornoGrundCheckBox.AllowGrayed := True;
        AufDeleteGrundCheckBox.AllowGrayed := True;
        UniqueChargenCheckBox.AllowGrayed := True;
        BestandBelegCheckBox.AllowGrayed := True;
        BestEnabledCheckBox.AllowGrayed := True;
        AufEnabledCheckBox.AllowGrayed := True;
        AufEmpfSelectCheckBox.AllowGrayed := True;
        IFCPrioCheckBox.AllowGrayed := True;
        VerteilPicCheckBox.AllowGrayed := True;
        VerpacklPicCheckBox.AllowGrayed := True;
        VerpackungCheckBox.AllowGrayed := True;
        WALHMCheckBox.AllowGrayed := True;
        WALHMAutoCheckBox.AllowGrayed := True;
        IFCAbgleichAufEmpfCheckBox.AllowGrayed := True;
        UseInternalArtikelNrCheckBox.AllowGrayed := True;
        IFCStammdatenUpdateCheckBox.AllowGrayed := True;

        BesCreateGrundCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandBesCreateGrund);
        BesChangeGrundCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandBesChangeGrund);
        BesDelGrundCheckBox.State    := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandBesDeleteGrund);
        BesLockGrundCheckBox.State   := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandBesLockGrund);
        BesLockIDCheckBox.State      := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandBesLockID);
        BesUnlockGrundCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandBesUnlockGrund);
        BesQSGrundCheckBox.State     := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandBesQSGrund);
        BesINVGrundCheckBox.State    := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandBesINVGrund);

        LiefRetBesCheckBox.State     := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandLiefRetBes);
        LiefRetWECheckBox.State      := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandLiefRetWE);
        InvEinzelScanCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandInvEinzelScan);
        InvAssignPosCheckBox.State   := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandInvAssigneCountPos);
        InvBesAbgleichOldMHDCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandInvOldMHDBesAbgleich);
        InvBesAbgleichANGCheckBox.State    := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandInvANGBesAbgleich);

        BestNrUniqueCheckBox.State   := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandBestNrUnique);

        WEPicCheckBox.State          := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandArPictureWE);
        WEBeschaffCheckBox.State     := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEBeschaffEinheit);
        WEBBDPastCheckBox.State      := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEBBDFromPast);
        WELastMHDCheckBox.State      := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckLastMHD);
        WELSNoDutyCheckBox.State     := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckLSNo);
        WEKFZDutyCheckBox.State      := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckKFZNo);
        WESuppDutyCheckBox.State     := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckSupplier);
        WEPackTypeDutyCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckPackType);
        WECategoryCheckBox.State     := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckPackType);
        WEQSGrundCheckBox.State      := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckQSGrund);
        WECategoryCheckBox.State     := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWECategory);
        WEArEinheitOptCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEChangeAREinheitOpt);
        WEBedarfCheckBox.State       := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEBedarfOpt);
        WEKommBestandCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEKommBestandOpt);
        WENachsBestandCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWENachsBestandOpt);
        WELeerProPosCheckBox.State   := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWELeerProPosOpt);
        WEMehrMengeCheckBox.State    := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEMehrMenge);

        WEArtikelDatenCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARData);
        WEArAbmessungCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARMesData);
        WEArPalDatenCheckBox.State   := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARPalData);
        WEArAttrCheckBox.State       := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARAttData);
        WEArBarcodeCheckBox.State    := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARBarData);
        WEArShipperCheckBox.State    := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARShipData);
        WEArGefahrstoffeCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARGefahr);
        WEArKlasseCheckBox.State       := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARKlasse);

        RetAutoPrintCheckBox.State   := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetAutoPrint);
        RetPrintLableCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetPrintLabel);
        RetVASCheckBox.State         := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetVAS);
        RetQSGrundCheckBox.State     := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetSelectQuality);
        RetZustandCommentCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetZustandComment);
        RetCatAuswahlCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetSelectCategory);
        RetSelZustandCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetSelectZustand);
        RetSelGrundCheckBox.State    := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetSelectGrund);
        RetGrundPflichtCheckBox.State:= CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetGrundPflicht);
        RetAvisAutoBuchtCheckBox.State:= CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetAvisAutoBuch);
        WEUseDefaultLTCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandUseDefaultLT);

        RetProcessPreGradingCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetProcessPreGrading);

        AufStornoGrundCheckBox.State    := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandStornoOrderGrund);
        AufDeleteGrundCheckBox.State    := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandDelOrderGrund);

        UniqueChargenCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandUniqueChargen);
        BestandBelegCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandBestandBeleg);

        BestEnabledCheckBox.State   := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandBestEnabled);
        AufEnabledCheckBox.State    := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandAufEnabled);
        AufEmpfSelectCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandAufSelEmpf);
        IFCPrioCheckBox.State       := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandIFCAufPrio);

        VerpackungCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandVerpackungErfassen);
        WALHMCheckBox.State       := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandLHMErfassen);
        WALHMAutoCheckBox.State   := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandLHMAutoErfassen);

        IFCAbgleichAufEmpfCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandAbgleichAufEmpf);
        IFCStammdatenUpdateCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandUpdIFCArLagerDaten);

        if not Assigned (query.FindField('INV_OPTIONS')) then
          InvOptionGroupBox.Visible := false
        else begin
          InvOptSingleScanCheckBox.AllowGrayed := True;
          InvOptGoLPCheckBox.AllowGrayed := True;
          InvSuggestArtikelCheckBox.AllowGrayed := True;
          InvOptNurMengeCheckBox.AllowGrayed := True;
          InvSuggestLEBesCheckBox.AllowGrayed := True;

          InvOptSingleScanCheckBox.State  := CheckOptState (query.FieldByName ('INV_OPTIONS').AsString, 1);
          InvOptGoLPCheckBox.State        := CheckOptState (query.FieldByName ('INV_OPTIONS').AsString, 2);
          InvSuggestArtikelCheckBox.State := CheckOptState (query.FieldByName ('INV_OPTIONS').AsString, 3);
          InvOptNurMengeCheckBox.State    := CheckOptState (query.FieldByName ('INV_OPTIONS').AsString, 6);
          InvSuggestLEBesCheckBox.State   := CheckOptState (query.FieldByName ('INV_OPTIONS').AsString, 7);
        end;

        if not Assigned (query.FindField('INV_CHECK_OPTIONS')) then
          InvChkOptionGroupBox.Visible := false
        else begin
          InvChkOptSingleScanCheckBox.AllowGrayed := True;
          InvChkOptGoLPCheckBox.AllowGrayed := True;
          InvChkSuggestArtikelCheckBox.AllowGrayed := True;
          InvChkOptNurMengeCheckBox.AllowGrayed := True;
          InvChkAreaCheckBox.AllowGrayed := True;
          InvChkSuggestLEBesCheckBox.AllowGrayed := True;

          InvChkOptSingleScanCheckBox.State   := CheckOptState (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 1);
          InvChkOptGoLPCheckBox.State         := CheckOptState (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 2);
          InvChkSuggestArtikelCheckBox.State  := CheckOptState (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 3);
          InvChkOptNurMengeCheckBox.State     := CheckOptState (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 6);
          InvChkSuggestLEBesCheckBox.State    := CheckOptState (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 7);
          InvChkAreaCheckBox.State            := CheckOptState (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 8);
        end;
      end else begin
        BesCreateGrundCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandBesCreateGrund);
        BesChangeGrundCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandBesChangeGrund);
        BesDelGrundCheckBox.Checked    := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandBesDeleteGrund);
        BesLockGrundCheckBox.Checked   := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandBesLockGrund);
        BesLockIDCheckBox.Checked      := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandBesLockID);
        BesUnlockGrundCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandBesUnlockGrund);
        BesQSGrundCheckBox.Checked     := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandBesQSGrund);
        BesINVGrundCheckBox.Checked    := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandBesINVGrund);

        LiefRetBesCheckBox.Checked     := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandLiefRetBes);
        LiefRetWECheckBox.Checked      := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandLiefRetWE);
        InvEinzelScanCheckBox.Checked  := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandInvEinzelScan);
        InvAssignPosCheckBox.Checked   := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandInvAssigneCountPos);
        InvBesAbgleichOldMHDCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandInvOldMHDBesAbgleich);
        InvBesAbgleichANGCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandInvANGBesAbgleich);

        BestNrUniqueCheckBox.Checked   := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandBestNrUnique);

        WEPicCheckBox.Checked          := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandArPictureWE);
        WEBeschaffCheckBox.Checked     := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEBeschaffEinheit);
        WEBBDPastCheckBox.Checked      := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEBBDFromPast);
        WELastMHDCheckBox.Checked      := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckLastMHD);
        WELSNoDutyCheckBox.Checked     := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckLSNo);
        WEKFZDutyCheckBox.Checked      := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckKFZNo);
        WESuppDutyCheckBox.Checked     := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckSupplier);
        WEPackTypeDutyCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckPackType);
        WEQSGrundCheckBox.Checked      := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWECheckQSGrund);
        WECategoryCheckBox.Checked     := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWECategory);
        WEArEinheitOptCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEChangeAREinheitOpt);
        WEBedarfCheckBox.Checked       := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEBedarfOpt);
        WEKommBestandCheckBox.Checked  := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEKommBestandOpt);
        WENachsBestandCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWENachsBestandOpt);
        WELeerProPosCheckBox.Checked   := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWELeerProPosOpt);
        WEMehrMengeCheckBox.Checked    := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEMehrMenge);

        WEArtikelDatenCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARData);
        WEArAbmessungCheckBox.Checked  := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARMesData);
        WEArPalDatenCheckBox.Checked   := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARPalData);
        WEArAttrCheckBox.Checked       := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARAttData);
        WEArBarcodeCheckBox.Checked    := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARBarData);
        WEArShipperCheckBox.Checked    := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARShipData);
        WEArGefahrstoffeCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARGefahr);
        WEArKlasseCheckBox.Checked       := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEFillARKlasse);

        RetAutoPrintCheckBox.Checked   := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandRetAutoPrint);
        RetPrintLableCheckBox.Checked  := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandRetPrintLabel);
        RetVASCheckBox.Checked         := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandRetVAS);
        RetQSGrundCheckBox.Checked     := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandRetSelectQuality);
        RetZustandCommentCheckBox.Checked     := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandRetZustandComment);
        RetCatAuswahlCheckBox.Checked  := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandRetSelectCategory);
        RetSelZustandCheckBox.Checked  := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandRetSelectZustand);
        RetSelGrundCheckBox.Checked    := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandRetSelectGrund);
        RetGrundPflichtCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetGrundPflicht);
        RetAvisAutoBuchtCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandRetAvisAutoBuch);

        RetProcessPreGradingCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandRetProcessPreGrading);

        AufStornoGrundCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandStornoOrderGrund);
        AufDeleteGrundCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandDelOrderGrund);

        UniqueChargenCheckBox.Checked  := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandUniqueChargen);
        BestandBelegCheckBox.Checked   := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandBestandBeleg);

        BestEnabledCheckBox.Checked   := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandBestEnabled);
        AufEnabledCheckBox.Checked    := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandAufEnabled);
        AufEmpfSelectCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandAufSelEmpf);
        IFCPrioCheckBox.Checked       := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandIFCAufPrio);

        VerpackungCheckBox.Checked  := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandVerpackungErfassen);
        WALHMCheckBox.Checked       := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandLHMErfassen);
        WALHMAutoCheckBox.Checked   := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandLHMAutoErfassen);

        WEAnzPackStCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandWEAnzPackSt);

        WEUseDefaultLTCheckBox.Checked:= CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandUseDefaultLT);

        IFCAbgleichAufEmpfCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandAbgleichAufEmpf);

        if not Assigned (query.FindField('INV_OPTIONS')) then
          InvOptionGroupBox.Visible := false
        else begin
          InvOptSingleScanCheckBox.Checked  := CheckOpt (query.FieldByName ('INV_OPTIONS').AsString, 1);
          InvOptGoLPCheckBox.Checked        := CheckOpt (query.FieldByName ('INV_OPTIONS').AsString, 2);
          InvSuggestArtikelCheckBox.Checked := CheckOpt (query.FieldByName ('INV_OPTIONS').AsString, 3);
          InvOptNurMengeCheckBox.Checked    := CheckOpt (query.FieldByName ('INV_OPTIONS').AsString, 6);
          InvSuggestLEBesCheckBox.Checked   := CheckOpt (query.FieldByName ('INV_OPTIONS').AsString, 7);
        end;

        if not Assigned (query.FindField('INV_CHECK_OPTIONS')) then
          InvChkOptionGroupBox.Visible := false
        else begin
          InvChkOptSingleScanCheckBox.Checked   := CheckOpt (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 1);
          InvChkOptGoLPCheckBox.Checked         := CheckOpt (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 2);
          InvChkSuggestArtikelCheckBox.Checked  := CheckOpt (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 3);
          InvChkOptNurMengeCheckBox.Checked     := CheckOpt (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 6);
          InvChkSuggestLEBesCheckBox.Checked    := CheckOpt (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 7);
          InvChkAreaCheckBox.Checked            := CheckOpt (query.FieldByName ('INV_CHECK_OPTIONS').AsString, 8);
        end;
      end;

      ArContenIntCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandArContFactor);

      if (Assigned(query.FindField('OPT_LS_DATUM_MAX_TAGE'))) then begin
        LSDatumMaxNumberBox.ValueInt := query.FieldByName ('OPT_LS_DATUM_MAX_TAGE').AsInteger;
      end else begin
        LSDatumMaxNumberBox.Visible := False;
      end;

      AufVorResCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandAufVorRes);
      LieferAdrCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandAufLiefAdr);

      opt := GetOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandAdrCheck);

      if (opt > '0') then begin
        AufCheckAdrCheckBox.Checked := true;

        if (opt = '2') then
          AufAdrCorrCheckBox.Checked := True
        else if (opt = '4') then begin
          AufAdrCorrCheckBox.Checked := True;
          AufAdrCorrCityCheckBox.Checked := True;
        end else if (opt = '8') then begin
          AufAdrCorrCheckBox.Checked := True;
          AufAdrCorrStrCheckBox.Checked := True;
        end else if (opt = 'B') then begin
          AufAdrCorrCheckBox.Checked := True;
          AufAdrCorrCityCheckBox.Checked := True;
          AufAdrCorrStrCheckBox.Checked := True;
        end;
      end;

      if PackTabSheet.TabVisible then begin
        if VerteilPicCheckBox.AllowGrayed then
          VerteilPicCheckBox.State  := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandArPictureVerteil)
        else VerteilPicCheckBox.Checked  := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandArPictureVerteil);

        if VerpacklPicCheckBox.AllowGrayed then
          VerpacklPicCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandArPictureVerpack)
        else VerpacklPicCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandArPictureVerpack);
      end;

      if AvisTabSheet.TabVisible then begin
        AvisMailEdit.Text            := query.FieldByName('MAIL_ADDR_AVIS').AsString;
        AVISMailAvisCheckBox.Checked := (query.FieldByName('AVIS_OPTIONS').AsString [4] = '1');
        AVISMailLSCheckBox.Checked   := (query.FieldByName('AVIS_OPTIONS').AsString [3] = '1');
      end;

      if ArtikelTabSheet.TabVisible then begin
        if VerteilPicCheckBox.AllowGrayed then
          UseInternalArtikelNrCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandLocalArtikelnr)
        else UseInternalArtikelNrCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandLocalArtikelnr);

        if not Assigned (query.FindField('ARTIKEL_NR_PREFIX')) then
          ArtikelPrefixEdit.Visible := False
        else
          ArtikelPrefixEdit.Text := query.FieldByName ('ARTIKEL_NR_PREFIX').AsString;

        ArtikelSuffixEdit.Text := query.FieldByName ('ARTIKEL_NR_SUFFIX').AsString;
        ArtikelStartEdit.Text  := query.FieldByName ('ARTIKEL_NR_START').AsString;
        ArtikelEndEdit.Text    := query.FieldByName ('ARTIKEL_NR_END').AsString;
        ArtikelLenEdit.Text    := query.FieldByName ('ARTIKEL_NR_LEN').AsString;
        ArtikelNextEdit.Text   := query.FieldByName ('ARTIKEL_NR_NEXT').AsString;

        if IFCStammdatenUpdateCheckBox.AllowGrayed then
          IFCStammdatenUpdateCheckBox.State := CheckOptState (query.FieldByName ('CONFIG_OPT').AsString, cMandUpdIFCArLagerDaten)
        else IFCStammdatenUpdateCheckBox.Checked := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandUpdIFCArLagerDaten);
      end;

      if QSTabSheet.TabVisible then begin
        if (query.FieldByName ('REF_QS_DEFAULT_WE').IsNull) then
          QSWEComboBox.ItemIndex := 0
        else begin
          QSWEComboBox.ItemIndex := FindComboboxRef (QSWEComboBox, query.FieldByName ('REF_QS_DEFAULT_WE').AsInteger);
          if (QSWEComboBox.ItemIndex = -1) then QSWEComboBox.ItemIndex := 0;
        end;

        if (query.FieldByName ('REF_QS_DEFAULT_RET').IsNull) then
          QSRETComboBox.ItemIndex := 0
        else begin
          QSRETComboBox.ItemIndex := FindComboboxRef (QSRETComboBox, query.FieldByName ('REF_QS_DEFAULT_RET').AsInteger);
          if (QSRETComboBox.ItemIndex = -1) then QSRETComboBox.ItemIndex := 0;
        end;
      end else begin
        QSWEComboBox.ItemIndex  := 0;
        QSRETComboBox.ItemIndex := 0;
      end;

      if VersandTabSheet.TabVisible then begin
        LieferscheinCheckBox.Checked   := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandLieferSchein);
        RetourenScheinCheckBox.Checked  := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandRetourenSchein);
        RetourenLabelCheckBox.Checked  := CheckOpt (query.FieldByName ('CONFIG_OPT').AsString, cMandRetourenLabel);

        if not Assigned (query.FindField('OPT_AUTO_CUSTOMS')) then
          ZollGroupBox.Visible := False
        else begin
          AutoZollAnmeldungCheckBox.Checked  := query.FieldByName ('OPT_AUTO_CUSTOMS').AsString = '1';
          BaseTaricNrEdit.Text := query.FieldByName ('BASE_TARIC_NUMBER').AsString;
        end;
      end;

        if not Assigned (query.FindField('RETURN_HINWEIS')) then
          ReturnHintMemo.Visible := False
        else begin
          ReturnHintMemo.Visible := True;
          ReturnHintMemo.Text := query.FieldByName ('RETURN_HINWEIS').AsString;
        end;


      if not Assigned (query.FindField('MANAGER_NAME')) then
        MandDatenTabSheet.TabVisible := false
      else begin
        fStammLogoName := query.FieldByName ('LOGO_NAME').AsString;

        StammLogoEdit.Text    := ExtractFileName(fStammLogoName);

        StammURLEdit.Text     := query.FieldByName ('WEB_URL').AsString;
        StammGFEdit.Text      := query.FieldByName ('MANAGER_NAME').AsString;
        StammContactEdit.Text := query.FieldByName ('CONTACT_NAME').AsString;
        StammMailEdit.Text    := query.FieldByName ('CONTACT_MAIL').AsString;
        StammTaxNrEdit.Text   := query.FieldByName ('TAX_NUMBER').AsString;
        StammVATEdit.Text     := query.FieldByName ('VAT_ID').AsString;
        StammHREdit.Text      := query.FieldByName ('HR_INFO').AsString;
        StammBankEdit.Text    := query.FieldByName ('BANK_NAME').AsString;
        StammIBANEdit.Text    := query.FieldByName ('IBAN').AsString;
        StammBICEdit.Text     := query.FieldByName ('BIC').AsString;

        if not Assigned (query.FindField('EORI_NUMMER')) then
          StammEORIEdit.Visible := False
        else
          StammEORIEdit.Text := query.FieldByName ('EORI_NUMMER').AsString;

        if not Assigned (query.FindField('CURRENCY')) then
          CurrencyComboBox.Visible := False
        else
          CurrencyComboBox.ItemIndex := FindComboboxDBItemWert (CurrencyComboBox, query.FieldByName ('CURRENCY').AsString, 0);
      end;

      query.Close;

      query.SQL.Clear;
      query.SQL.Add ('select * from V_MANDANT_REL_LOCATION where REF_MAND=:ref_mand and REF_LOCATION=:ref_loc');
      query.Parameters.ParamByName ('ref_mand').Value := RefMand;
      query.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;

      query.Open;

      MandantAbsenderFrame.NameEdit.Text := query.FieldByName ('ABSENDER_NAME').AsString;
      MandantAbsenderFrame.NameAddEdit.Text := query.FieldByName ('ABSENDER_ZUSATZ').AsString;
      MandantAbsenderFrame.RoadEdit.Text := query.FieldByName ('ABSENDER_STRASSE').AsString;
      MandantAbsenderFrame.PLZEdit.Text  := query.FieldByName ('ABSENDER_PLZ').AsString;
      MandantAbsenderFrame.OrtEdit.Text  := query.FieldByName ('ABSENDER_ORT').AsString;

      if Assigned (query.FindField ('ABSENDER_LAND')) then
        MandantAbsenderFrame.LandEdit.Text := query.FieldByName ('ABSENDER_LAND').AsString;

      if not Assigned (query.FindField ('ABSENDER_CONTACT')) then
        MandantAbsenderFrame.ContactEdit.Visible := false
      else
        MandantAbsenderFrame.ContactEdit.Text := query.FieldByName ('ABSENDER_CONTACT').AsString;

      if not Assigned (query.FindField ('ABSENDER_TELEFON')) then
        MandantAbsenderFrame.PhoneEdit.Visible := false
      else
        MandantAbsenderFrame.PhoneEdit.Text := query.FieldByName ('ABSENDER_TELEFON').AsString;

      if not Assigned (query.FindField ('ABSENDER_EMAIL')) then
        MandantAbsenderFrame.MailEdit.Visible := false
      else
        MandantAbsenderFrame.MailEdit.Text := query.FieldByName ('ABSENDER_EMAIL').AsString;

      if not Assigned (query.FindField ('ADRESS_NAME')) then
        AdrTabSheet.TabVisible := False
      else begin
        MandantAdressFrame.NameEdit.Text := query.FieldByName ('ADRESS_NAME').AsString;
        MandantAdressFrame.NameAddEdit.Text := query.FieldByName ('ADRESS_ZUSATZ').AsString;
        MandantAdressFrame.RoadEdit.Text := query.FieldByName ('ADRESS_STRASSE').AsString;
        MandantAdressFrame.PLZEdit.Text  := query.FieldByName ('ADRESS_PLZ').AsString;
        MandantAdressFrame.OrtEdit.Text  := query.FieldByName ('ADRESS_ORT').AsString;

        if Assigned (query.FindField ('ADRESS_LAND')) then
          MandantAdressFrame.LandEdit.Text := query.FieldByName ('ADRESS_LAND').AsString;

        if not Assigned (query.FindField ('ADRESS_CONTACT')) then
          MandantAdressFrame.ContactEdit.Visible := false
        else
          MandantAdressFrame.ContactEdit.Text := query.FieldByName ('ADRESS_CONTACT').AsString;

        if not Assigned (query.FindField ('ADRESS_TELEFON')) then
          MandantAdressFrame.PhoneEdit.Visible := false
        else
          MandantAdressFrame.PhoneEdit.Text := query.FieldByName ('ADRESS_TELEFON').AsString;

        if not Assigned (query.FindField ('ADRESS_EMAIL')) then
          MandantAdressFrame.MailEdit.Visible := false
        else
          MandantAdressFrame.MailEdit.Text := query.FieldByName ('ADRESS_EMAIL').AsString;
      end;

      query.Close;
    end;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditMandantForm.StammLogoButtonClick(Sender: TObject);
var
  dlg : TOpenPictureDialog;
  dirname : STring;
begin
  dirname := UserReg.ReadRegValue('LastMandantLogoPath');

  dlg := TOpenPictureDialog.Create (Self);

  try
    dlg.Title := FormatResourceText (1386, [NameEdit.Text]);

    if (Length (fStammLogoName) > 0) then
      dlg.InitialDir := ExtractFilePath (fStammLogoName)
    else if (Length (dirname) > 0) then
      dlg.InitialDir := dirname;

    dlg.FileName := fStammLogoName;

    if (dlg.Execute) then begin
      UserReg.WriteRegValue ('LastArticlePicurePath', ExtractFilePath (dlg.FileName));

      if (Length (fLogoBaseDir) + Length (ExtractFileName(fStammLogoName)) > fMaxLogoDirLen) then
        MessageDLG ('Die Dateiname für das gewählte Mandanten-Logo würde zu lang werden', mtError, [mbOK], 0)
      else begin
        fStammLogoName := dlg.FileName;

        StammLogoEdit.Text := ExtractFileName(fStammLogoName);
      end;
    end;
  finally
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditMandantForm.FormShow(Sender: TObject);
var
  numcfg  : Integer;
begin
  GetConfigDaten (-1, -1, -1, 'PATH_MANDANT_LOGO', fLogoBaseDir, numcfg);

  StammLogoButton.Enabled := (Length (fLogoBaseDir) > 0);

  fOldStammLogoName := fStammLogoName;

  //AbsGroupBox.Caption := FormatResourceText (1758, [LVSDatenModul.AktLocation]);

  Label4.Visible  := ArtikelPrefixEdit.Visible;
  Label23.Visible := BioKontrollNrEdit.Visible;
  Label24.Visible := StdPickHintEdit.Visible;
  Label25.Visible := StdPackHintEdit.Visible;
  Label26.Visible := CurrencyComboBox.Visible;

  Label42.Visible         := StammLogoEdit.Visible;
  Label33.Visible         := StammEORIEdit.Visible;
  StammLogoButton.Visible := StammLogoEdit.Visible;

  MandantAbsenderFrame.Label2.Visible := MandantAbsenderFrame.ContactEdit.Visible;
  MandantAbsenderFrame.Label3.Visible := MandantAbsenderFrame.PhoneEdit.Visible;
  MandantAbsenderFrame.Label4.Visible := MandantAbsenderFrame.MailEdit.Visible;

  TexteTabSheet.TabVisible := BioKontrollNrEdit.Visible or StdPickHintEdit.Visible or StdPackHintEdit.Visible;

  BesMandOnlyCheckBoxClick (Sender);
  AufCheckAdrCheckBoxClick (Sender);

  if (PageControl1.ActivePage = StammTabSheet) then begin
    if (NameEdit.Enabled and (Length (NameEdit.Text) = 0)) Then
      NameEdit.SetFocus
    else
      BeschreibungEdit.SetFocus;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditMandantForm.ArtikelNrEditKeyPress(Sender: TObject; var Key: Char);
var
  wert : Integer;
begin
  if not (CharInSet (Key, ['0'..'9', #8, ^X, ^V, ^C])) then
    Key := #0
  else if CharInSet (Key, ['0'..'9']) then begin
    if not (TryStrToInt((Sender as TEDit).Text + key, wert)) then
      Key := #0
    else if (wert > 99999) then
      Key := #0
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 26.09.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditMandantForm.AufAdrCorrCheckBoxClick(Sender: TObject);
begin
  AufAdrCorrCityCheckBox.Enabled := AufAdrCorrCheckBox.Checked;
  AufAdrCorrStrCheckBox.Enabled  := AufAdrCorrCheckBox.Checked;
end;

procedure TEditMandantForm.AufCheckAdrCheckBoxClick(Sender: TObject);
begin
  AufAdrCorrCheckBox.Enabled := AufCheckAdrCheckBox.Checked;
  AufAdrCorrCheckBoxClick (Sender);
end;

procedure TEditMandantForm.BesMandOnlyCheckBoxClick(Sender: TObject);
begin
  if LVSConfigModul.UseSubMandanten then
  begin
    LoadMandantSubMandantCheckListBox(BesMandantCheckListBox, BesMandOnlyCheckBox.Checked, getBesMandanten (fRefMand));
  end
  else
  begin
    LoadMandantCheckListBox(BesMandantCheckListBox, BesMandOnlyCheckBox.Checked, getBesMandanten(fRefMand));
  end;
end;

procedure TEditMandantForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  numcfg  : Integer;
  fname,
  basedir : String;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := False;

    if (Length (NameEdit.Text) = 0) Then
      NameEdit.SetFocus
    else begin
      res := 0;

      if (MandDatenTabSheet.TabVisible) then begin
        if (UpperCase (fStammLogoName) <> UpperCase (fOldStammLogoName)) then begin
          GetConfigDaten (-1, -1, -1, 'PATH_MANDANT_LOGO', basedir, numcfg);
          if (Length (basedir) > 0) and (basedir [Length (basedir)] <> '\') then
            basedir := basedir + '\';

          if (Length (basedir) > 0) and (UpperCase (ExtractFilePath (basedir)) <> UpperCase (ExtractFilePath (fStammLogoName))) then begin
            fname := ExtractFileName (fStammLogoName);

            if FileExists (basedir + fname) then begin
              if not RenameFile (basedir + fname, basedir + fname + '.' + FormatDateTime ('yyyymmdd', Now)) then begin
                res := -4;

                MessageDLG ('Fehler beim Archivieren des alten Mandanten-Logos'+#13+#13+GetAPIErrorMessage (GetLastError), mtError, [mbOK], 0);
              end;
            end else if not DirectoryExists (ExtractFilePath (basedir + fname)) then begin
              if not (ForceDirectories(ExtractFilePath (basedir + fname))) then begin
                res := -3;

                MessageDLG ('Fehler beim Erzeugen des Unterverzeichnisses'+#13+#13+GetAPIErrorMessage (GetLastError), mtError, [mbOK], 0);
              end;
            end;

            if (res = 0) then begin
              if (CopyFile (PChar (fStammLogoName), PChar (basedir + fname), true)) then begin
                fStammLogoName := basedir + fname;
              end else begin
                res := -5;

                MessageDLG ('Fehler beim Kopieren des Mandanten-Logos'+#13+#13+GetAPIErrorMessage (GetLastError), mtError, [mbOK], 0);
              end;
            end;
          end;
        end;
      end;

      CanClose := (res = 0);
    end;
  end;
end;

function TEditMandantForm.getBesMandanten(const RefMand: Integer): TArryOfInteger;
var
  query : TADOQuery;
  index : Integer;
begin
  if RefMand <= 0 then
  begin
    setLength(Result, 0);
  end
  else
  begin
    query := TADOQuery.Create (self);
    try
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Text := 'SELECT REF_BES_MAND FROM V_MANDANT_REL_BES_MAND WHERE REF_MAND = :refMand';
      query.Parameters.ParamByName('refMand').Value := RefMand;

      query.Open;

      index := 0;
      while not query.Eof do
      begin
        setLength(Result, index+1);
        Result[index] := query.Fields[0].AsInteger;

        query.Next;
        Inc(index);
      end;

      query.Close;
    finally
      query.Free;
    end;
  end;
end;

end.
