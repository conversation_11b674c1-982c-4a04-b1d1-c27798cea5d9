object MandantLagerForm: TMandantLagerForm
  Left = 668
  Top = 196
  BorderStyle = bsDialog
  Caption = 'Mandant / Lager ausw'#228'hlen'
  ClientHeight = 192
  ClientWidth = 318
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  FormStyle = fsStayOnTop
  KeyPreview = True
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    318
    192)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Label2: TLabel
    Left = 8
    Top = 102
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Label3: TLabel
    Left = 8
    Top = 54
    Width = 67
    Height = 13
    Caption = 'Niederlassung'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 147
    Width = 303
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object MandantComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 303
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 100
    ItemHeight = 15
    TabOrder = 0
    OnChange = MandantComboBoxChange
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 118
    Width = 303
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 100
    ItemHeight = 15
    TabOrder = 2
  end
  object Button1: TButton
    Left = 143
    Top = 160
    Width = 77
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = '&Ok'
    Default = True
    ModalResult = 1
    TabOrder = 3
  end
  object Button2: TButton
    Left = 231
    Top = 160
    Width = 77
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = '&Abbrechen'
    ModalResult = 3
    TabOrder = 4
  end
  object LocationComboBox: TComboBoxPro
    Left = 7
    Top = 70
    Width = 303
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 100
    ItemHeight = 15
    TabOrder = 1
    OnChange = LocationComboBoxChange
  end
  object ADOQuery1: TADOQuery
    Parameters = <>
    Left = 176
    Top = 8
  end
end
