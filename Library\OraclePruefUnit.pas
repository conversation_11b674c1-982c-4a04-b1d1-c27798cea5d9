unit OraclePruefUnit;


interface

uses
  Windows, Messages, SysUtils;

function BuildSQLString(const Line: string): string;


implementation

//******************************************************************************
//* Function Name:
//* Author       : <PERSON> Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************

function BuildSQLString(const Line: string): string;
var
  k: Integer;
  retstr: string;
begin
  retstr := '';

  for k := 1 to Length(Line) do begin
    if ((Ord(Line[k]) < 32) or (Ord(Line[k]) > 127)) then begin
      retstr := retstr + #39 + '||CHR (' + IntToStr(Ord(Line[k])) + ')||' + #39;
    end else if (Line[k] = '&') then begin
      retstr := retstr + #39 + '||CHR (38)||' + #39;
    end else if (Line[k] = '"') then begin
      retstr := retstr + #39 + '||CHR (34)||' + #39;
    end else if (Line[k] = #96) then begin
      retstr := retstr + #39 + '||CHR (96)||' + #39;
    end else if (Line[k] = #39) then
      retstr := retstr + #39 + #39
    else retstr := retstr + Line[k];
  end;

  BuildSQLString := retstr;
end;


end.

