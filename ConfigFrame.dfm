object ConfigVarFrame: TConfigVarFrame
  Left = 0
  Top = 0
  Width = 762
  Height = 114
  PopupMenu = PopupMenu1
  TabOrder = 0
  TabStop = True
  OnResize = FrameResize
  DesignSize = (
    762
    114)
  object VarNameLabel: TLabel
    Left = 8
    Top = 8
    Width = 81
    Height = 13
    Caption = 'VarNameLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Bevel1: TBevel
    Left = 7
    Top = 108
    Width = 750
    Height = 3
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 84
    ExplicitWidth = 714
  end
  object Label6: TLabel
    Left = 526
    Top = 8
    Width = 27
    Height = 13
    Alignment = taRightJustify
    Anchors = [akTop, akRight]
    Caption = 'Lager'
  end
  object Label7: TLabel
    Left = 203
    Top = 8
    Width = 42
    Height = 13
    Alignment = taRightJustify
    Caption = 'Mandant'
  end
  object MandantComboBox: TComboBoxPro
    Left = 251
    Top = 5
    Width = 229
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 2
    OnChange = EditChange
  end
  object LagerComboBox: TComboBoxPro
    Left = 561
    Top = 5
    Width = 192
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akTop, akRight]
    ItemHeight = 15
    TabOrder = 3
    OnChange = EditChange
  end
  object VarTextEdit: TEdit
    Left = 8
    Top = 27
    Width = 189
    Height = 21
    MaxLength = 64
    TabOrder = 0
    Text = 'VarTextEdit'
    OnChange = EditChange
  end
  object PageControl1: TPageControl
    Left = 251
    Top = 32
    Width = 502
    Height = 73
    ActivePage = TextTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 4
    object BaseTabSheet: TTabSheet
      Caption = 'Basis'
      DesignSize = (
        494
        45)
      object Label1: TLabel
        Left = 8
        Top = 16
        Width = 22
        Height = 13
        Alignment = taRightJustify
        Caption = 'Text'
      end
      object Label8: TLabel
        Left = 364
        Top = 16
        Width = 20
        Height = 13
        Alignment = taRightJustify
        Anchors = [akTop, akRight]
        Caption = 'Zahl'
      end
      object VarStrEdit: TEdit
        Left = 36
        Top = 13
        Width = 309
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 32
        TabOrder = 0
        Text = 'VarStrEdit'
        OnChange = EditChange
      end
      object VarNumEdit: TEdit
        Left = 390
        Top = 13
        Width = 92
        Height = 21
        Anchors = [akTop, akRight]
        MaxLength = 9
        TabOrder = 1
        Text = 'VarNumEdit'
        OnChange = EditChange
        OnKeyPress = VarNumEditKeyPress
      end
    end
    object TextTabSheet: TTabSheet
      Caption = 'Text'
      ImageIndex = 5
      DesignSize = (
        494
        45)
      object Label11: TLabel
        Left = 8
        Top = 16
        Width = 22
        Height = 13
        Alignment = taRightJustify
        Caption = 'Text'
      end
      object VarStrTextEdit: TEdit
        Left = 36
        Top = 13
        Width = 446
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 128
        TabOrder = 0
        Text = 'VarStrTextEdit'
        OnChange = EditChange
      end
    end
    object NumTabSheet: TTabSheet
      Caption = 'Zahl'
      ImageIndex = 6
      object Label12: TLabel
        Left = 8
        Top = 16
        Width = 20
        Height = 13
        Caption = 'Zahl'
      end
      object VarIntegerEdit: TEdit
        Left = 37
        Top = 13
        Width = 132
        Height = 21
        TabOrder = 0
        Text = 'VarIntegerEdit'
        OnChange = EditChange
        OnKeyPress = VarNumEditKeyPress
      end
    end
    object FlagTabSheet: TTabSheet
      Caption = 'Flag'
      object TrueRadioButton: TRadioButton
        Left = 8
        Top = 16
        Width = 58
        Height = 17
        Caption = 'Ein'
        TabOrder = 0
        OnClick = EditChange
      end
      object FalseRadioButton: TRadioButton
        Left = 72
        Top = 16
        Width = 49
        Height = 17
        Caption = 'Aus'
        TabOrder = 1
        OnClick = EditChange
      end
    end
    object LimitTabSheet: TTabSheet
      Caption = 'Limits'
      ImageIndex = 1
      object Label4: TLabel
        Left = 8
        Top = 16
        Width = 20
        Height = 13
        Alignment = taRightJustify
        Caption = 'Min.'
      end
      object Label5: TLabel
        Left = 113
        Top = 16
        Width = 24
        Height = 13
        Alignment = taRightJustify
        Caption = 'Max.'
      end
      object Label3: TLabel
        Left = 236
        Top = 16
        Width = 29
        Height = 13
        Alignment = taRightJustify
        Caption = 'L'#228'nge'
      end
      object VarMinEdit: TEdit
        Left = 37
        Top = 13
        Width = 70
        Height = 21
        TabOrder = 0
        Text = 'VarMinEdit'
        OnChange = EditChange
        OnKeyPress = VarNumEditKeyPress
      end
      object VarMaxEdit: TEdit
        Left = 143
        Top = 13
        Width = 70
        Height = 21
        TabOrder = 1
        Text = 'VarMaxEdit'
        OnChange = EditChange
        OnKeyPress = VarNumEditKeyPress
      end
      object VarLenEdit: TEdit
        Left = 271
        Top = 13
        Width = 34
        Height = 21
        MaxLength = 2
        TabOrder = 2
        Text = 'VarLenEdit'
        OnChange = EditChange
        OnKeyPress = VarNumEditKeyPress
      end
      object CycleCheckBox: TCheckBox
        Left = 339
        Top = 15
        Width = 113
        Height = 17
        Caption = 'Zyklischer Umlauf'
        TabOrder = 3
        OnClick = CycleCheckBoxClick
      end
    end
    object AlarmTabSheet: TTabSheet
      Caption = 'Alarm'
      ImageIndex = 2
      object Label2: TLabel
        Left = 24
        Top = 16
        Width = 42
        Height = 13
        Alignment = taRightJustify
        Caption = 'Alarm ab'
      end
      object ValAlarmLimitEdit: TEdit
        Left = 72
        Top = 13
        Width = 89
        Height = 21
        TabOrder = 0
        Text = 'ValAlarmLimitEdit'
        OnChange = EditChange
        OnKeyPress = VarNumEditKeyPress
      end
    end
    object InfoTabSheet: TTabSheet
      Caption = 'Info'
      ImageIndex = 3
      object Label9: TLabel
        Left = 3
        Top = 3
        Width = 60
        Height = 13
        Caption = 'Angelegt am'
      end
      object Label10: TLabel
        Left = 192
        Top = 3
        Width = 97
        Height = 13
        Caption = 'Letzte '#196'nderung am'
      end
      object CreateLabel: TLabel
        Left = 72
        Top = 3
        Width = 58
        Height = 13
        Caption = 'CreateLabel'
      end
      object DataChangeLabel: TLabel
        Left = 320
        Top = 3
        Width = 85
        Height = 13
        Caption = 'DataChangeLabel'
      end
      object Label13: TLabel
        Left = 192
        Top = 19
        Width = 116
        Height = 13
        Caption = 'Letze Wert'#228'nderung am'
      end
      object ValChangeLabel: TLabel
        Left = 320
        Top = 19
        Width = 76
        Height = 13
        Caption = 'ValChangeLabel'
      end
    end
  end
  object VarDescMemo: TMemo
    Left = 8
    Top = 54
    Width = 189
    Height = 48
    Lines.Strings = (
      'VarDescMemo')
    MaxLength = 256
    TabOrder = 1
    OnChange = EditChange
  end
  object PopupMenu1: TPopupMenu
    Left = 160
    Top = 16
    object Lschen1: TMenuItem
      Caption = 'L'#246'schen'
      OnClick = Lschen1Click
    end
  end
end
