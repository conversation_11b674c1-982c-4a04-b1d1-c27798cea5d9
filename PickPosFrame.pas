unit PickPosFrame;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, GR32_Image;

type
  TPickPositionFrame = class(TFrame)
    ArtikelNrLabel: TLabel;
    ArtikelTextLabel: TLabel;
    Label1: TLabel;
    SollMengeLabel: TLabel;
    Label2: TLabel;
    IstMengeLabel: TLabel;
    ArtikelImagePanel: TPanel;
    LPNrLabel: TLabel;
    ArtikelImage: TImage32;
    procedure FrameResize(Sender: TObject);
  private
    fRefKommPos : Integer;
  public
    procedure ShowArtikelPicture (const Filename : String);

    property RefKommPos : Integer read fRefKommPos write fRefKommPos;
  end;

implementation

{$R *.dfm}

uses
  FrontendUtils;


//******************************************************************************
//* Function Name:
//* Author       : <PERSON>
//* Datum        : 25.10.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPickPositionFrame.FrameResize(Sender: TObject);
var
  w : Integer;
begin
  if (ArtikelImagePanel.Visible) then begin
    w := LPNrLabel.Canvas.TextWidth ('999-99-99-99');

    ArtikelImagePanel.Height := ClientHeight - ArtikelImagePanel.Top + IstMengeLabel.Font.Height - 8;

    if ((LPNrLabel.Left + w + 8) > (ClientWidth - ArtikelImagePanel.Height - ArtikelImagePanel.Top)) then
      ArtikelImagePanel.Height := ClientWidth - ArtikelImagePanel.Top - 8 - LPNrLabel.Left - w;

    ArtikelImagePanel.Width  := ArtikelImagePanel.Height;
    ArtikelImagePanel.Left   := ClientWidth - ArtikelImagePanel.Width - ArtikelImagePanel.Top;

    ArtikelTextLabel.Width   := ClientWidth - ArtikelTextLabel.Left - ArtikelImagePanel.Width - 16;
  end else begin
    ArtikelTextLabel.Width   := ClientWidth - ArtikelTextLabel.Left * 2;
  end;

  SollMengeLabel.Top := Label1.Top - Label1.Font.Height + SollMengeLabel.Font.Height;
  IstMengeLabel.Top  := Label2.Top - Label2.Font.Height + IstMengeLabel.Font.Height;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 24.10.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPickPositionFrame.ShowArtikelPicture (const Filename : String);
begin
  if (ArtikelImagePanel.Visible) then begin
    FrontendUtils.ShowArtikelPicture (ArtikelImage, Filename);
  end;
end;

end.
