﻿//*****************************************************************************
//*  Program System    : LVS-Leistand
//*  Module Name       : LVSFrontLogin
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/LVSFrontLogin.pas $
// $Revision: 65 $
// $Modtime: 21.07.23 11:12 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : <PERSON><PERSON><PERSON> an der Datenbank
//*****************************************************************************
{$WARN UNIT_PLATFORM OFF}
{$WARN SYMBOL_PLATFORM OFF}
unit LVSFrontLogin;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, DB, ADODB, ExtCtrls, ComboBoxPro;

const
  LVSPassKey = 12859;

type
  TDBLoginForm = class(TForm)
    Button1: TButton;
    AbortButton: TButton;
    ADOQuery1: TADOQuery;
    ADOConnection1: TADOConnection;
    Button3: TButton;
    SelectPanel: TPanel;
    SystemPanel: TPanel;
    GroupBox2: TGroupBox;
    SchemaComboBox: TComboBoxPro;
    Label6: TLabel;
    SystemComboBox: TComboBox;
    Label1: TLabel;
    GroupBox1: TGroupBox;
    LoginPanel: TPanel;
    Label2: TLabel;
    Label3: TLabel;
    Bevel1: TBevel;
    DomCheckBox: TCheckBox;
    UserEdit: TEdit;
    PassEdit: TEdit;
    SavePassCheckBox: TCheckBox;
    Panel3: TPanel;
    Label5: TLabel;
    MandantComboBox: TComboBoxPro;
    Label7: TLabel;
    LocationComboBox: TComboBoxPro;
    Label4: TLabel;
    LagerComboBox: TComboBoxPro;
    Timer1: TTimer;
    LeitstandPanel: TPanel;
    LeitstandComboBox: TComboBoxPro;
    Label8: TLabel;
    VerPanel: TPanel;
    procedure FormShow(Sender: TObject);
    procedure SystemComboBoxChange(Sender: TObject);
    procedure UserEditKeyPress(Sender: TObject; var Key: Char);
    procedure SchemaComboBoxChange(Sender: TObject);
    procedure Button3Click(Sender: TObject);
    procedure Button1Click(Sender: TObject);
    procedure DomCheckBoxClick(Sender: TObject);
    procedure LocationComboBoxChange(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure MandantComboBoxChange(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure Timer1Timer(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure UserEditExit(Sender: TObject);
  private
    fLoginTimeout : Integer;
    fShowTime     : TDateTime;

    fFirstShow    : Boolean;

    fRefProject   : Integer;

    ConfigString  : TStringList;
    BaseConfig    : TStringList;

    function  UpdateSchema : Integer;
    procedure UpdateLagerMandant;

    procedure CreateParams(var Params : TCreateParams); override;
  public
    DBConnection : String;

    RegKeyStr    : String;
    SubKeyStr    : String;

    AdminFlag    : Boolean;

    ClientName   : String;

    SchemaFeld   : array [0..16] of String;

    procedure Prepare;
  end;

implementation

uses
  {$ifdef Trace}
  Trace,
  {$endif}
  DateUtils,
  VerInfos,
  EncryptUtils,
  Registry,
  VCLUtilitys, TerminalServices, CmdLineUtils, LVSGlobalDaten,
  RegistryUtils, IniFiles, FrontEndUtils, ConfigModul, SprachModul, ResourceText;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.Prepare;
var
  idx,
  diff,
  intwert  : Integer;
  dbreg    : TRegistryModule;
  items    : TStringList;
  inifile  : TIniFile;
begin
  {$ifdef Trace}
    ProcedureStart ('TDBLoginForm.Prepare');
  {$endif}

  items    := TStringList.Create;

  try
    {$ifdef StoreLogix}
      inifile := TIniFile.Create (ExtractFilePath (ParamStr (0))+'storelogix.ini');
    {$else}
      inifile := TIniFile.Create (ExtractFilePath (ParamStr (0))+'systeme.ini');
    {$endif}

    inifile.ReadSections (items);

    inifile.ReadSectionValues ('Config', BaseConfig);

    inifile.Free;
  except
  end;

  if (BaseConfig.IndexOfName ('Project') <> -1) then
    Caption := Caption + ' : ' + BaseConfig.Values['Project'];

  if (BaseConfig.IndexOfName ('LoginTimeout') = -1) or not (TryStrToInt (BaseConfig.Values['LoginTimeout'], fLoginTimeout)) then
    fLoginTimeout := 5 * 60;

  if not (AdminFlag) and (BaseConfig.IndexOfName ('ForceNDSLogin') <> -1) and (BaseConfig.Values['ForceNDSLogin'] = '1') then
    LoginPanel.Visible := False;

  if not (LoginPanel.Visible) then begin
    SelectPanel.Height := SelectPanel.Height - LoginPanel.Height;
    Height := Height - LoginPanel.Height;
  end;

  {$ifdef DEBUG}
    OutputDebugString ('TDBLoginForm.FormShow');
  {$endif}

  dbreg := TRegistryModule.Create;
  dbreg.OpenKey (HKEY_CURRENT_USER, RegKeyStr, KEY_READ, True);

  if not (AdminFlag) and (BaseConfig.IndexOfName ('NoSystemSelect') <> -1) and (BaseConfig.Values['NoSystemSelect'] = '1') then begin
    intwert := 0;
    Button3.Enabled := False;
    Button3.Visible := False;

    diff := AbortButton.Left - Button1.Left;

    AbortButton.Left := Button3.Left;
    Button1.Left     := AbortButton.Left - diff;

    SystemPanel.Visible := false;
    Height := height - SystemPanel.Height;
  end else if (dbreg.ReadRegValue ('LoginOption', intwert) <> 0) then begin
    intwert := 1;
  end;

  if (intwert = 0) then begin
    Button3.Caption := GetResourceText (1188);
  end else begin
    Button3.Caption := GetResourceText (1189);
  end;

  SystemCombobox.Clear;

  idx := 0;
  while (idx < items.Count) do begin
    if (items [idx] <> 'Config') then
      SystemComboBox.Items.Add(items [idx]);

    Inc (idx);
  end;

  items.Free;

  UserEdit.Text := '';
  PassEdit.Text := '';

  dbreg.OpenKey (HKEY_CURRENT_USER, RegKeyStr, KEY_READ, True);

  SystemComboBox.ItemIndex := SystemComboBox.Items.IndexOf (dbreg.ReadRegValue ('LastDB'));
  if (SystemComboBox.ItemIndex = -1) then
    SystemComboBox.ItemIndex := 0;

  dbreg.Free;

  {$IFDEF TRACE}
  ProcedureStop;
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.CreateParams(var Params: TCreateParams);
begin
  inherited;

  if not (Application.MainForm.Visible) then
    Params.ExStyle := Params.ExStyle or WS_EX_APPWINDOW;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.FormShow(Sender: TObject);
var
  filedate  : TDateTime;
begin
  {$ifdef Trace}
    ProcedureStart ('TDBLoginForm.FormShow');
  {$endif}

  FileAge (ParamStr (0), filedate);

  VerPanel.Caption := FileVersion (3,2) + ' from ' + DateToStr (filedate);

  fShowTime := Now;
  Timer1.Enabled := True;

  if (fFirstShow) then
    SystemComboBoxChange (Self);

  fFirstShow := False;

  {$IFDEF TRACE}
    ProcedureStop;
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TDBLoginForm.UpdateSchema : Integer;
var
  res,
  idx       : Integer;
  envstr,
  prjstr    : String;
  altcursor : TCursor;
begin
  res := 0;


  {$ifdef DEBUG}
    OutputDebugString ('TDBLoginForm.UpdateSchema');
  {$endif}

  SchemaComboBox.Clear;

  {$ifdef Trace}
    TraceParameter ('DBConnection', DBConnection);
  {$endif}

  ADOConnection1.ConnectionString := DBConnection;

  altcursor := Screen.Cursor;
  Screen.Cursor := crSQLWait;
  try
    ADOConnection1.Open('LVSLOGIN','LVSLOGIN');

    ADOQuery1.Connection := ADOConnection1;
    
    ADOQuery1.SQL.Clear;

    envstr := ConfigString.Values['Umgebung'];

    if (ConfigString.Values['DBConnectName'] = '') then
      prjstr := SystemComboBox.Text
    else prjstr := ConfigString.Values['DBConnectName'];

    if (Length (envstr) = 0) then
      ADOQuery1.SQL.Add ('select NAME,BESCHREIBUNG,SCHEMA from SCHEMAS where upper (DBCONNECT)=upper ('+#39+prjstr+#39+') order by NAME')
    else ADOQuery1.SQL.Add ('select NAME,BESCHREIBUNG,SCHEMA from SCHEMAS where upper (DBCONNECT)=upper ('+#39+prjstr+#39+') and upper (UMGEBUNG)=upper ('+#39+envstr+#39+') order by NAME');

    {$ifdef SQLDebug}
      {$ifdef Trace}
        TraceString (ADOQuery1.SQl.Text);
      {$endif}
    {$endif}

    ADOQuery1.Open;
    while not (ADOQuery1.Eof) do begin
      idx := SchemaComboBox.Items.Add (ADOQuery1.Fields [0].AsString+'|'+ADOQuery1.Fields [1].AsString);

      SchemaFeld [idx] := ADOQuery1.Fields [2].AsString;

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;
  except
    on E: Exception do begin
      MessageDlg(FormatMessageText (1014,[])+#13+#13+e.ClassName + ' : ' + e.Message, mtError, [mbOK], 0);

      res := -9;
    end;
  end;

  ADOConnection1.Close;
  Screen.Cursor := altcursor;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.UpdateLagerMandant;
var
  schema    : String;
  altcursor : TCursor;
begin
  {$ifdef DEBUG}
    OutputDebugString ('TDBLoginForm.UpdateLagerMandant');
  {$endif}

  MandantComboBox.Clear;

  LocationComboBox.Clear;
  LagerComboBox.Clear;

  if (Length (SchemaFeld [SchemaComboBox.ItemIndex]) = 0) Then
    schema := ''
  else schema := '"'+SchemaFeld [SchemaComboBox.ItemIndex] + '".';

  ADOConnection1.ConnectionString := DBConnection;

  altcursor := Screen.Cursor;
  Screen.Cursor := crSQLWait;
  try
    ADOConnection1.Open('LVSLOGIN','LVSLOGIN');

    ADOQuery1.SQL.Clear;

    if (true=false) then
      ADOQuery1.SQL.Add ('select distinct REF,REF_MASTER_MAND,NAME,BESCHREIBUNG from '+schema+'V_LOGIN_SUB_MANDANT')
    else
      ADOQuery1.SQL.Add ('select REF,null,NAME,BESCHREIBUNG from '+schema+'V_LOGIN_MANDANT');

    if ((ConfigString.IndexOfName ('VisibleMandant') <> -1) and (Length (ConfigString.Values ['VisibleMandant']) > 0)) then begin
      ADOQuery1.SQL.Add ('where REF in ('+ConfigString.Values ['VisibleMandant']+')');

      if (fRefProject <> -1) then begin
        ADOQuery1.SQL.Add ('and REF_PROJECT=:ref_prj');
        ADOQuery1.Parameters.ParamByName('ref_prj').Value := fRefProject;
      end;
    end else if (fRefProject <> -1) then begin
      ADOQuery1.SQL.Add ('where REF_PROJECT=:ref_prj');
      ADOQuery1.Parameters.ParamByName('ref_prj').Value := fRefProject;

      if (true=false) then begin
        ADOQuery1.SQL.Add ('and USER_ID=:user_id');
        ADOQuery1.Parameters.ParamByName('user_id').Value := UserEdit.Text;
      end;
    end else if (true=false) then begin
      ADOQuery1.SQL.Add ('where USER_ID=:user_id');
      ADOQuery1.Parameters.ParamByName('user_id').Value := UserEdit.Text;
    end;



    ADOQuery1.SQL.Add ('order by NAME');

    ADOQuery1.Open;
    while not (ADOQuery1.Eof) do begin
      if (ADOQuery1.Fields [1].IsNull) then
        MandantComboBox.Items.AddObject (ADOQuery1.Fields [2].AsString+'|'+ADOQuery1.Fields [3].AsString, TComboboxMandantRef.Create(ADOQuery1.Fields [0].AsInteger, -1))
      else
        MandantComboBox.Items.AddObject (ADOQuery1.Fields [2].AsString+'|'+ADOQuery1.Fields [3].AsString, TComboboxMandantRef.Create(ADOQuery1.Fields [1].AsInteger, ADOQuery1.Fields [0].AsInteger));

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;

    if (MandantComboBox.Items.Count > 1) then
      MandantComboBox.Items.Insert (0, GetResourceText (1020));
  except
    on  E: Exception do begin
      MessageDlg(FormatMessageText (1014,[])+#13+#13+e.ClassName + ' : ' + e.Message, mtError, [mbOK], 0);
    end;
  end;

  ADOConnection1.Close;
  Screen.Cursor := altcursor;

  MandantComboBox.ItemIndex := 1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.SystemComboBoxChange(Sender: TObject);
var
  idx,
  intwert   : Integer;
  subreg    : TRegistryModule;
  inifile   : TIniFile;
  schema    : String;
  visflag   : Boolean;
begin
  {$ifdef DEBUG}
    OutputDebugString ('TDBLoginForm.SystemComboBoxChange');
  {$endif}

  if (BaseConfig.IndexOfName ('ForceNDSLogin') <> -1) and (BaseConfig.Values['ForceNDSLogin'] = '1') then
    DomCheckBox.Checked := True
  else begin
    DomCheckBox.Checked := False;
    UserEdit.Text   := '';
    PassEdit.Text   := '';
  end;

  MandantComboBox.Clear;
  LocationComboBox.Clear;
  LagerComboBox.Clear;

  try
    {$ifdef StoreLogix}
      inifile := TIniFile.Create (ExtractFilePath (ParamStr (0))+'storelogix.ini');
    {$else}
      inifile := TIniFile.Create (ExtractFilePath (ParamStr (0))+'systeme.ini');
    {$endif}

    inifile.ReadSectionValues (SystemCombobox.Text, ConfigString);

    inifile.Free;
  except
  end;

  if (ConfigString.IndexOfName ('RefProject') = -1) then
    fRefProject := -1
  else if not (TryStrToInt (ConfigString.Values['RefProject'], intwert)) then
    fRefProject := -1
  else
    fRefProject := intwert;

  visflag := LeitstandPanel.Visible;

  if not (AdminFlag) and ((ConfigString.IndexOfName ('UseLeitstand') = -1) or (ConfigString.Values['UseLeitstand'] = '0')) then begin
    LeitstandPanel.Visible := False;

    if (visflag) then
      Height := Height - LeitstandPanel.Height;
  end else begin
    if not (visflag) then
      Height := Height + LeitstandPanel.Height;

    LeitstandPanel.Visible := True;
  end;

  DBConnection := ConfigString.Values['Connect'];

  if (UpdateSchema <> 0) Then
  else if (SchemaComboBox.Items.Count = 0) then
    MessageDlg(FormatMessageText (1015, []), mtError, [mbOK], 0)
  else begin
    SchemaComboBox.ItemIndex := -1;

    subreg := TRegistryModule.Create;

    if (subreg.OpenKey (HKEY_CURRENT_USER, 'Software\'+LVSConfigModul.MasterRegKeyName+'\Systeme\' + SystemComboBox.Text+'\'+SubKeyStr, KEY_READ, False) = 0) then begin
      schema := subreg.ReadRegValue ('DBSchema');

      idx := 0;
      while (idx < SchemaComboBox.Items.Count) and (SchemaComboBox.ItemIndex = -1) do begin
        if (SchemaFeld [idx] = schema) then
          SchemaComboBox.ItemIndex := idx
        else Inc (idx);
      end;
    end;

    subreg.Free;

    if (SchemaComboBox.ItemIndex = -1) then SchemaComboBox.ItemIndex := 0;
    SchemaComboBoxChange (Sender);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.Timer1Timer(Sender: TObject);
begin
  if not (Application.Terminated) then begin
    if (fShowTime > 0) and (Now > (fShowTime + ((fLoginTimeout) / SecsPerDay))) then
      Application.Terminate;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.UserEditExit(Sender: TObject);
begin
  if (true=false) and not AbortButton.Focused then
    UpdateLagerMandant;
end;

procedure TDBLoginForm.UserEditKeyPress(Sender: TObject; var Key: Char);
var
  keystr : string [5];
begin
  keystr := Key;
  Key := AnsiUpperCase (keystr) [1];
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.SchemaComboBoxChange(Sender: TObject);

  //******************************************************************************
  //* Function Name: CopyRegistryKeys
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description    Kopieren eines gesamten Registrybaumes
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CopyRegistryKeys (const MasterKey : HKEY; const FromKey, ToKey : String) : Integer;
  var
    TempRegFrom, TempRegTo: TRegistry;

    procedure MoveValue(SrcKey, DestKey: HKEY; const Name: string);
    var
      Len   : DWORD;
      Buffer: Pointer;
      dwtyp : DWORD;
    begin
      Buffer := AllocMem (4096);
      try
        if (RegQueryValueEx (SrcKey, PChar (Name), Nil, @dwtyp, Buffer, @Len) = ERROR_SUCCESS) then
          RegSetValueEx (DestKey, PChar (Name), 0, dwtyp, Buffer, Len);
      finally
        FreeMem(Buffer);
      end;
    end;

    procedure CopyValues(SrcKey, DestKey: HKEY);
    var
      I: Integer;
      Len:DWORD;
      KeyInfo: TRegKeyInfo;
      S: string;
    begin
      if TempRegFrom.GetKeyInfo(KeyInfo) then begin
        MoveValue (SrcKey, DestKey, '');
        SetString(S, nil, KeyInfo.MaxValueLen + 1);
        for I := 0 to KeyInfo.NumValues - 1 do begin
          Len := KeyInfo.MaxValueLen + 1;
          if RegEnumValue(SrcKey, I, PChar(S), Len, nil, nil, nil, nil) = ERROR_SUCCESS then begin
            MoveValue(SrcKey, DestKey, PChar(S));
          end;
        end;
      end;
    end;

    procedure CopyKey(FromName, ToName: String);
    var
      List: TStringList;
      I: Integer;
    begin
      TempRegFrom.CloseKey;
      TempRegTo.CloseKey;
      TempRegFrom.OpenKey(FromName, False);

      List:= TStringList.Create;
      try
        TempRegFrom.GetKeyNames(List);

        TempRegTo.OpenKey(ToName, True);

        CopyValues(TempRegFrom.CurrentKey, TempRegTo.CurrentKey);

        for I:= 0 to List.Count - 1 do begin
          if (IncludeTrailingBackslash(FromName) + List[I] <> IncludeTrailingBackslash(ToName)) then
            CopyKey(IncludeTrailingBackslash(FromName) + List[I], IncludeTrailingBackslash(ToName) + List[I]);
        end;
      finally
        List.Free;
      end;
    end;

  begin
    TempRegFrom:= TRegistry.Create;
    TempRegTo:= TRegistry.Create;

    try
      TempRegFrom.RootKey:=MasterKey;
      TempRegTo.RootKey:=MasterKey;

      CopyKey(FromKey, ToKey);
    finally
      TempRegFrom.Free;
      TempRegTo.Free;
    end;

    Result := 0;
  end;

var
  pwstr,
  langstr,
  wertstr   : String;
  subreg    : TRegistryModule;
begin
  {$ifdef DEBUG}
    OutputDebugString ('TDBLoginForm.SchemaComboBoxChange');
  {$endif}

  UpdateLagerMandant;

  subreg := TRegistryModule.Create;

  if (subreg.OpenKey (HKEY_CURRENT_USER, 'Software\'+LVSConfigModul.MasterRegKeyName+'\Systeme\' + SystemComboBox.Text +  '\' + SchemaFeld [SchemaComboBox.ItemIndex]+'\'+SubKeyStr, KEY_READ, False) <> 0) Then begin
    CopyRegistryKeys (HKEY_CURRENT_USER, 'Software\'+LVSConfigModul.MasterRegKeyName+'\Systeme\' + SystemComboBox.Text, 'Software\'+LVSConfigModul.MasterRegKeyName+'\Systeme\' + SystemComboBox.Text + '\' + SchemaFeld [SchemaComboBox.ItemIndex]+'\'+SubKeyStr);
  end else begin
    subreg.CloseKey;
  end;

  if (subreg.OpenKey (HKEY_CURRENT_USER, 'Software\'+LVSConfigModul.MasterRegKeyName+'\Systeme\' + SystemComboBox.Text +  '\' + SchemaFeld [SchemaComboBox.ItemIndex]+'\'+SubKeyStr, KEY_READ, False) = 0) Then begin
    if not (LoginPanel.Visible) then
      DomCheckBox.Checked := True
    else begin
      if (subreg.ReadRegValue('Sprache', langstr) = 0) and (Length (langstr) > 0) then
        LVSSprachModul.AktSprache := langstr;

      if (subreg.ReadRegValue ('NTSAuth', wertstr) = 0) Then
        DomCheckBox.Checked := (UpperCase (wertstr) = 'TRUE');

      DomCheckBox.OnClick(DomCheckbox);

      UserEdit.Text := subreg.ReadRegValue ('DBUser');
      PassEdit.Text := '';

      //Wenn es sich nicht um eine Test-Umgebung handelt, wird das Passwort für die Schema-User nicht mehr vorgeblendet
      if (UserEdit.Text <> SchemaFeld [SchemaComboBox.ItemIndex]) or ((BaseConfig.IndexOfName ('TestSystem') <> -1) and (BaseConfig.Values['TestSystem'] = '1')) then begin
        if (subreg.ReadRegValue ('SavePasswd', wertstr) = 0) Then
          SavePassCheckBox.Checked := (UpperCase (wertstr) = 'TRUE')
        else SavePassCheckBox.Checked := True;

        pwstr := subreg.ReadRegValue ('DBPasswd');
        if (Length (pwstr) > 2) and (pwstr [1] = #$aa) and (pwstr [2] = #$55) then begin
          Delete (pwstr,1,2);
          pwstr := Decrypt (pwstr, LVSPassKey);
        end;

        PassEdit.Text := pwstr;
      end;
    end;

    MandantComboBox.ItemIndex := MandantComboBox.IndexOf (subreg.ReadRegValue ('Mandant'));
    if (MandantComboBox.ItemIndex = -1) then MandantComboBox.ItemIndex := 0;

    MandantComboBoxChange (MandantComboBox);

    if (LocationComboBox.Visible) then begin
      LocationComboBox.ItemIndex := LocationComboBox.IndexOf (subreg.ReadRegValue ('Location'));
      if (LocationComboBox.ItemIndex = -1) then LocationComboBox.ItemIndex := 0;

      LocationComboBoxChange (LocationComboBox);
    end;

    LagerComboBox.ItemIndex := LagerComboBox.IndexOf (subreg.ReadRegValue ('Lager'));
    if (LagerComboBox.ItemIndex = -1) then LagerComboBox.ItemIndex := 0;
  end;

  subreg.Free;

  SelectNext (Nil, true, true)
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.Button3Click(Sender: TObject);
begin
  if SystemPanel.Visible then begin
    SystemPanel.Visible := false;
    Height := height - SystemPanel.Height;
    Button3.Caption := GetResourceText (1188);
  end
  else begin
    SystemPanel.Visible := true;
    Height := height + SystemPanel.height;
    Button3.Caption := GetResourceText (1189);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.Button1Click(Sender: TObject);
var
  subreg    : TRegistryModule;
begin
  subreg := TRegistryModule.Create;
  subreg.OpenKey (HKEY_CURRENT_USER, 'Software\'+LVSConfigModul.MasterRegKeyName+'\',KEY_READ or KEY_WRITE, True);

  if (SystemPanel.Visible) then
    subreg.WriteRegValue ('LoginOption', 1)
  else
    subreg.WriteRegValue ('LoginOption', 0);

  subreg.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.DomCheckBoxClick(Sender: TObject);
begin
  UserEdit.Enabled         := not DomCheckbox.Checked;
  PassEdit.Enabled         := not DomCheckbox.Checked;
  SavePassCheckBox.Enabled := not DomCheckbox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.LocationComboBoxChange(Sender: TObject);

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function GetTerminalSessionInfo : Integer;
  var
    ts     : TTerminalService;
    clname : String;
    cname  : array [0..MAX_COMPUTERNAME_LENGTH] of char;
    csize  : DWORD;
  begin
    csize := sizeof (cname) - 1;
    GetComputerName (cname, csize);
    cname [csize] := #0;
    ClientName := AnsiUpperCase (StrPas (cname));

    ts := TTerminalService.Create;

    try
      if (ts.IsRemoteSession) then begin
        clname := GetParamVal ('ICA_CLIENT');

        if (Length (clname) > 0) then
          ClientName := AnsiUpperCase (clname)
        else
          ClientName := AnsiUpperCase (ts.ClientName);
      end;
    finally
      ts.Free;
    end;

    Result := 0;
  end;

var
  schema,
  wherestr,
  lagerstr  : String;
  idx,
  selidx    : Integer;
  lastdt    : TDateTime;
  connflag  : Boolean;
  altcursor : TCursor;
begin
  connflag := False;

  if (LagerComboBox.ItemIndex = -1) then
    lagerstr := ''
  else lagerstr := LagerComboBox.Text;

  LagerComboBox.Clear;

  if (Length (SchemaFeld [SchemaComboBox.ItemIndex]) = 0) Then
    schema := ''
  else schema := '"'+SchemaFeld [SchemaComboBox.ItemIndex] + '".';

  if not (ADOConnection1.Connected) then
    ADOConnection1.ConnectionString := DBConnection;

  altcursor := Screen.Cursor;
  Screen.Cursor := crSQLWait;
  try
    if (ADOConnection1.Connected) then
      connflag := False
    else begin
      ADOConnection1.Open('LVSLOGIN','LVSLOGIN');

      connflag := True;
    end;

    if (LeitstandPanel.Visible) then begin
      GetTerminalSessionInfo;

      if Assigned (UserLog) then UserLog.Write ('Login: clientname='+AnsiUpperCase (clientname));

      selidx := -1;
      lastdt := 0;

      ClearComboBoxObjects (LeitstandComboBox);
      LeitstandComboBox.Items.Add ('');

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select REF,NAME,BESCHREIBUNG,upper (DEVICE_NAME),LAST_ASSIGNMENT from '+schema+'V_LOGIN_LEITSTAND');

      if (BaseConfig.IndexOfName ('TestSystem') <> -1) and (BaseConfig.Values['TestSystem'] = '1') then
        ADOQuery1.SQL.Add ('where OPT_TEST=''1''')
      else if (BaseConfig.IndexOfName ('TestUser') <> -1) and (Pos (UpperCase (OSUserName), UpperCase (BaseConfig.Values['TestUser'])) > 0) then
        ADOQuery1.SQL.Add ('where OPT_TEST=''1''')
      else
        ADOQuery1.SQL.Add ('where OPT_TEST=''0''');

      if (GetComboBoxRef (LocationComboBox) <> -1) then begin
        ADOQuery1.SQL.Add (' and REF_LOCATION=:ref_loc');
        ADOQuery1.Parameters.ParamByName('ref_loc').Value := GetComboBoxRef (LocationComboBox);
      end;

      ADOQuery1.SQL.Add ('order by upper (NAME)');

      ADOQuery1.Open;
      while not (ADOQuery1.Eof) do begin
        idx := LeitstandComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create(ADOQuery1.Fields [0].AsInteger));

        if (Length (clientname) > 0) and (ADOQuery1.Fields [3].AsString = AnsiUpperCase (clientname)) then begin
          if (selidx = -1) or (lastdt < ADOQuery1.Fields [4].AsDateTime) then begin
            selidx := idx;
            lastdt := ADOQuery1.Fields [4].AsDateTime;
          end;
        end;

        ADOQuery1.Next;
      end;

      ADOQuery1.Close;

      LeitstandComboBox.ItemIndex := selidx;
      if (LeitstandComboBox.ItemIndex = -1) then LeitstandComboBox.ItemIndex := 0;
    end;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF,NAME,BESCHREIBUNG from '+schema+'V_LOGIN_LAGER');

    wherestr := '';

    if (GetComboBoxRef (LocationComboBox) <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_LOCATION='+IntToStr (GetComboBoxRef (LocationComboBox));
    end;

    if (GetComboBoxRef (MandantComboBox) <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF in (select REF_LAGER from '+schema+'V_LOGIN_MAND_REL_LAGER where REF_MAND='+IntToStr (GetComboBoxRef (MandantComboBox))+')';
    end;

    if (Length (wherestr) > 0) then
      ADOQuery1.SQL.Add ('where ' + wherestr);

    ADOQuery1.SQL.Add ('order by NAME');

    ADOQuery1.Open;
    while not (ADOQuery1.Eof) do begin
      LagerComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create(ADOQuery1.Fields [0].AsInteger));

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;

    if (LagerComboBox.Items.Count > 1) then begin
      LagerComboBox.Items.Insert (0, '<alle>');

      LagerComboBox.ItemIndex := LagerComboBox.Items.IndexOf (lagerstr);
      if (LagerComboBox.ItemIndex = -1) then
        LagerComboBox.ItemIndex := 0;
    end else LagerComboBox.ItemIndex := 0;
  except
    MessageDlg(FormatMessageText (1014, []), mtError, [mbOK], 0);
  end;

  if connflag and (ADOConnection1.Connected) then
    ADOConnection1.Close;

  Screen.Cursor := altcursor;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 16.05.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  if (ADOConnection1.Connected) then
    ADOConnection1.Close;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    CanClose := False;

    if not (LoginPanel.Visible) then
      CanClose := True
    else if (DomCheckBox.Checked) then
      CanClose := True
    else if (UserEdit.Enabled and (Length (UserEdit.Text) = 0)) then begin
      MessageDLG (FormatMessageText (1016, []), mtError, [mbOk], 0);
      UserEdit.SetFocus;
    end else if (PassEdit.Enabled and (Length (PassEdit.Text) = 0)) then begin
      MessageDLG (FormatMessageText (1017, []), mtError, [mbOk], 0);
      PassEdit.SetFocus;
    end else
      CanClose := True
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.FormCreate(Sender: TObject);
begin
  fShowTime     := 0;
  fLoginTimeout := 5 * 60;
  fFirstShow    := True;

  BaseConfig   := TStringList.Create;
  ConfigString := TStringList.Create;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, MandantComboBox);
    LVSSprachModul.SetNoTranslate (Self, LocationComboBox);
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
    LVSSprachModul.SetNoTranslate (Self, LeitstandComboBox);
    LVSSprachModul.SetNoTranslate (Self, SystemComboBox);
    LVSSprachModul.SetNoTranslate (Self, SchemaComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (LeitstandComboBox);

  MandantComboBox.Clear;
  LocationComboBox.Clear;
  LagerComboBox.Clear;
  
  if Assigned (BaseConfig) then
    BaseConfig.Free;

  if Assigned (ConfigString) then
    ConfigString.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDBLoginForm.MandantComboBoxChange(Sender: TObject);
var
  schema,
  strwert   : String;
  altcursor : TCursor;
begin
  if (Length (SchemaFeld [SchemaComboBox.ItemIndex]) = 0) Then
    schema := ''
  else schema := '"'+SchemaFeld [SchemaComboBox.ItemIndex] + '".';

  ADOConnection1.ConnectionString := DBConnection;

  altcursor := Screen.Cursor;
  Screen.Cursor := crSQLWait;
  try
    ADOConnection1.Open('LVSLOGIN','LVSLOGIN');

    ADOQuery1.SQL.Clear;
    if (LocationComboBox.Visible) then begin
      strwert := LocationComboBox.GetItemText;

      LocationComboBox.Clear;

      if (GetComboBoxRef (MandantComboBox) = -1) Then
        ADOQuery1.SQL.Add ('select REF,NAME,BESCHREIBUNG from '+schema+'V_LOGIN_LOCATION where REF in (select REF_LOCATION from '+schema+'V_LOGIN_LAGER)')
      else ADOQuery1.SQL.Add ('select REF,NAME,BESCHREIBUNG from '+schema+'V_LOGIN_LOCATION where REF in (select REF_LOCATION from '+schema+'V_LOGIN_LAGER where REF in (select REF_LAGER from '+schema+'V_LOGIN_MAND_REL_LAGER where REF_MAND='+IntToStr (GetComboBoxRef (MandantComboBox))+'))');

      if (fRefProject <> -1) then
        ADOQuery1.SQL.Add ('and REF_PROJECT='+IntToStr (fRefProject));

      if ((ConfigString.IndexOfName ('VisibleLocation') <> -1) and (Length (ConfigString.Values ['VisibleLocation']) > 0)) then
        ADOQuery1.SQL.Add ('and REF in ('+ConfigString.Values ['VisibleLocation']+')');

      ADOQuery1.SQL.Add ('order by NAME');

      ADOQuery1.Open;
      while not (ADOQuery1.Eof) do begin
        LocationComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));
        ADOQuery1.Next;
      end;

//      LocationComboBox.Items.Insert (0, '<alle>');

      LocationComboBox.ItemIndex := LocationComboBox.IndexOf (strwert);
      if (LocationComboBox.ItemIndex = -1) then LocationComboBox.ItemIndex := 0;
      LocationComboBox.OnChange (LocationComboBox);
    end else begin
      ADOQuery1.SQL.Add ('select NAME,BESCHREIBUNG from '+schema+'V_LOGIN_LAGER where REF in (select REF_LAGER from '+schema+'V_LOGIN_MAND_REL_LAGER where REF_MAND='+IntToStr (GetComboBoxRef (MandantComboBox))+')');

      if (fRefProject <> -1) then
        ADOQuery1.SQL.Add ('and REF_PROJECT='+IntToStr (fRefProject));

      ADOQuery1.SQL.Add ('order by NAME');

      strwert := LagerComboBox.GetItemText;
      LagerComboBox.Clear;

      ADOQuery1.Open;
      while not (ADOQuery1.Eof) do begin
        LagerComboBox.Items.Add(ADOQuery1.Fields [0].AsString+'|'+ADOQuery1.Fields [1].AsString);
        ADOQuery1.Next;
      end;

      LagerComboBox.ItemIndex := LocationComboBox.IndexOf (strwert);
      if (LagerComboBox.ItemIndex = -1) then LagerComboBox.ItemIndex := 0;
    end;
    ADOQuery1.Close;
  except
    MessageDlg(FormatMessageText (1014, []), mtError, [mbOK], 0);
  end;

  ADOConnection1.Close;
  Screen.Cursor := altcursor;
end;

end.
