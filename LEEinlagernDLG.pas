unit LEEinlagernDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, DB, ADODB, ExtCtrls, ComboBoxPro, ComCtrls, IntegerUpDown,
  Menus;

type
  TLEEinlagernForm = class(TForm)
    LBComboBox: TComboBoxPro;
    Label3: TLabel;
    LPListBox: TListBox;
    Label4: TLabel;
    ADOQuery1: TADOQuery;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel1: TBevel;
    Bevel2: TBevel;
    PageControl1: TPageControl;
    LETabSheet: TTabSheet;
    BesTabSheet: TTabSheet;
    Label1: TLabel;
    Label5: TLabel;
    StaticText1: TStaticText;
    StaticText2: TStaticText;
    ArtikelListBox: TListBox;
    Label2: TLabel;
    Label6: TLabel;
    Label7: TLabel;
    ArtikelNrLabel: TLabel;
    ArtikelTextLabel: TLabel;
    EinheitLabel: TLabel;
    MengeEdit: TEdit;
    Label11: TLabel;
    MengeUpDown: TIntegerUpDown;
    Label9: TLabel;
    LagerComboBox: TComboBoxPro;
    LPListBoxPopupMenu: TPopupMenu;
    LPSuchMenuItem: TMenuItem;
    procedure FormShow(Sender: TObject);
    procedure LBComboBoxChange(Sender: TObject);
    procedure LPListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure LagerComboBoxChange(Sender: TObject);
    procedure LPSuchMenuItemClick(Sender: TObject);
  private
    fRefAR     : Integer;
    fRefAE     : Integer;
    fRefLT     : Integer;
    fRefLager  : Integer;

    fLPSortType : Integer;

    procedure UpdateLPListe (const LBRef : Integer);
  public
    Lager        : String;
    LagerBereich : String;
    LEReferenz   : Integer;
    LBReferenz   : Integer;
    BesReferenz  : Integer;
  end;

implementation

uses VCLUtilitys, DatenModul, StringUtils, FrontEndUtils, ResourceText,
  SprachModul;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 26.02.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLEEinlagernForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := False;

    if (GetComboBoxRef (LBComboBox) < 0) then begin
      if LBComboBox.CanFocus then LBComboBox.SetFocus;

      MessageDLG (FormatMessageText (1306, []), mtError, [mbOk], 0);
    end else if (GetListBoxRef(LPListBox) < 0) then begin
      if LPListBox.CanFocus then LPListBox.SetFocus;

      MessageDLG (FormatMessageText (1439, []), mtError, [mbOk], 0);
    end else
      CanClose := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLEEinlagernForm.FormCreate(Sender: TObject);
begin
  fRefLager    := -1;
  LEReferenz   := -1;
  LBReferenz   := -1;
  BesReferenz  := -1;
  fRefAE       := -1;

  LETabSheet.TabVisible := False;
  BesTabSheet.TabVisible := False;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
    LVSSprachModul.SetNoTranslate (Self, LBComboBox);
    LVSSprachModul.SetNoTranslate (Self, StaticText1);
    LVSSprachModul.SetNoTranslate (Self, StaticText2);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLEEinlagernForm.FormShow(Sender: TObject);
var
  dbres : Integer;
  idx : Integer;
  Tabulators: array[0..4] of Integer;
begin
  dbres := 0;

  StaticText1.Caption := '';
  StaticText2.Caption := '';

  ADOQuery1.SQL.Clear;

  if (LEReferenz > 0) then begin
    PageControl1.ActivePage := LETabSheet;

    { Set the Tabulator Widths / Tabulatorweiten festlegen}
    Tabulators[0] := 10;
    Tabulators[1] := 10;
    Tabulators[2] := 16;
    Tabulators[3] := 100;
    ArtikelListBox.TabWidth := 1;
    { Set the Tabulators / Tabulatoren setzen }
    SendMessage(ArtikelListBox.Handle, LB_SETTABSTOPS, 4, Longint(@Tabulators));

    ADOQuery1.SQL.Add ('select LE_NR, LE_TYPE, REF_LAGER, LAGER, REF_LT_TYP from V_LE where REF=:ref_le');
    ADOQuery1.Parameters.ParamByName ('ref_le').Value := LEReferenz;

    try
      ADOQuery1.Open;

      fRefLager := ADOQuery1.Fields [2].AsInteger;
      Lager     := ADOQuery1.Fields [3].AsString;
      fRefLT    := ADOQuery1.Fields [4].AsInteger;

      StaticText1.Caption := ADOQuery1.Fields [0].AsString;
      StaticText2.Caption := ADOQuery1.Fields [1].AsString;

      ADOQuery1.Close;

      ArtikelListBox.Clear;

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select ARTIKEL_NR,ARTIKEL_TEXT,MENGE_FREI,EINHEIT from V_BES_LE where REF_LE=:ref_le');
      ADOQuery1.Parameters.ParamByName ('ref_le').Value := LEReferenz;

      try
        ADOQuery1.Open;

        while (dbres = 0) and not (ADOQuery1.EOF) do begin
          ArtikelListBox.Items.Add (ADOQuery1.Fields [2].AsString+#9+ADOQuery1.Fields [3].AsString+#9+ADOQuery1.Fields [0].AsString+#9+ADOQuery1.Fields [1].AsString);

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      except
        dbres := -9;
      end;
    except
      dbres := -9;
    end;
  end else if (BesReferenz > 0) then begin
    fRefLT := -1;

    PageControl1.ActivePage := BesTabSheet;

    ArtikelListBox.Clear;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF_LAGER,LAGER,ARTIKEL_NR,ARTIKEL_TEXT,EINHEIT,MENGE_FREI,MENGE_SPERR,REF_AR_EINHEIT,REF_AR from V_BES where REF=:ref');
    ADOQuery1.Parameters.ParamByName ('ref').Value := BesReferenz;

    try
      ADOQuery1.Open;

      fRefLager := ADOQuery1.Fields [0].AsInteger;
      fRefAR    := ADOQuery1.FieldByName ('REF_AR').AsInteger;
      fRefAE    := ADOQuery1.FieldByName ('REF_AR_EINHEIT').AsInteger;
      Lager     := ADOQuery1.Fields [1].AsString;

      ArtikelNrLabel.Caption := ADOQuery1.Fields [2].AsString;
      ArtikelTextLabel.Caption := ADOQuery1.Fields [3].AsString;
      EinheitLabel.Caption := ADOQuery1.Fields [4].AsString;

      if (ADOQuery1.Fields [6].AsInteger > 0) then begin
        MengeUpDown.Max := ADOQuery1.Fields [6].AsInteger;
        MengeUpDown.Position := ADOQuery1.Fields [6].AsInteger;
      end else begin
        MengeUpDown.Max := ADOQuery1.Fields [5].AsInteger;
        MengeUpDown.Position := ADOQuery1.Fields [5].AsInteger;
      end;

      ADOQuery1.Close;
    except
      dbres := -9;
    end;
  end else begin
    StaticText1.Caption := '';
    StaticText2.Caption := '';
    ArtikelListBox.Clear;
  end;

  LoadLagerCombobox (LagerComboBox, LVSDatenModul.AktLocationRef);

  if (fRefLager > 0) then
    LagerComboBox.ItemIndex := FindComboboxRef(LagerComboBox, fRefLager);

  LagerComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLEEinlagernForm.LagerComboBoxChange(Sender: TObject);
var
  idx : Integer;
begin
  if (fRefLager <> GetComboBoxRef (LagerComboBox)) then
    LBReferenz := -1;

  ADOQuery1.SQL.Clear;

  if (LBReferenz > 0) then begin
    ADOQuery1.SQL.Add ('select distinct lb.REF,lb.NAME,lb.BESCHREIBUNG from V_LB lb, V_ARTIKEL_EINLAGERN rel where lb.REF=rel.REL_REF_LB and rel.REF_LAGER=(select REF_LAGER from V_LB where REF=:ref_lb)');
    ADOQuery1.Parameters.ParamByName ('ref_lb').Value := LBReferenz;

    if (LEReferenz > 0) then begin
      ADOQuery1.SQL.Add ('and (rel.REF_LT_TYP is null or rel.REF_LT_TYP=:ref_lt) and (rel.REF_AR is null or rel.REF_AR in (select REF_AR from V_LE_INHALT where REF_LE=:ref_le))');
      ADOQuery1.Parameters.ParamByName ('ref_le').Value := LEReferenz;
      ADOQuery1.Parameters.ParamByName ('ref_lt').Value := fRefLT;
    end else if (fRefAR > 0) then begin
      ADOQuery1.SQL.Add ('and (rel.REF_AR is null or rel.REF_AR=:ref_ar)');
      ADOQuery1.Parameters.ParamByName ('ref_ar').Value := fRefAR;
    end;

    ADOQuery1.SQL.Add ('group by lb.REF,lb.NAME,lb.BESCHREIBUNG order by upper (lb.NAME)');
  end else begin
    ADOQuery1.SQL.Add ('select distinct lb.REF,lb.NAME,lb.BESCHREIBUNG from V_LB lb, V_ARTIKEL_EINLAGERN rel where lb.REF=rel.REL_REF_LB and rel.REF_LAGER=:ref_lager');
    ADOQuery1.Parameters.ParamByName ('ref_lager').Value := GetComboBoxRef (LagerComboBox);

    if (LEReferenz > 0) then begin
      ADOQuery1.SQL.Add ('and (rel.REF_LT_TYP is null or rel.REF_LT_TYP=:ref_lt) and (rel.REF_AR is null or rel.REF_AR in (select REF_AR from V_LE_INHALT where REF_LE=:ref_le))');
      ADOQuery1.Parameters.ParamByName ('ref_le').Value := LEReferenz;
      ADOQuery1.Parameters.ParamByName ('ref_lt').Value := fRefLT;
    end else if (fRefAR > 0) then begin
      ADOQuery1.SQL.Add ('and (rel.REF_AR is null or rel.REF_AR=:ref_ar)');
      ADOQuery1.Parameters.ParamByName ('ref_ar').Value := fRefAR;
    end;

    ADOQuery1.SQL.Add ('group by lb.REF,lb.NAME,lb.BESCHREIBUNG order by upper (lb.NAME)');
  end;

  ClearListBoxObjects (LPListBox);

  ClearComboBoxObjects (LBComboBox);

  LBComboBox.ItemIndex := -1;

  Screen.Cursor := crSQLWait;

  try
    try
      ADOQuery1.Open;

      while not (ADOQuery1.EOF) do begin
        idx := LBComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create(ADOQuery1.Fields [0].AsInteger));

        if (LBReferenz <> -1) and (ADOQuery1.Fields [0].AsInteger = LBReferenz) then
          LBComboBox.ItemIndex := idx;

        ADOQuery1.Next;
      end;

      ADOQuery1.Close;
    except
    end;
  finally
    Screen.Cursor := crDefault;
  end;

  if (LBComboBox.ItemIndex = -1) then
    LBComboBox.ItemIndex := LBComboBox.Items.IndexOf (LagerBereich);

  if (LBComboBox.ItemIndex >= 0) Then
    UpdateLPListe (GetComboBoxRef (LBComboBox));
end;

procedure TLEEinlagernForm.LBComboBoxChange(Sender: TObject);
begin
  UpdateLPListe (GetComboBoxRef (LBComboBox));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLEEinlagernForm.LPListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
var
  line : String;
  strpos : Integer;
begin
  line := (Control as TListBox).Items [Index];

  with (Control as TListBox).Canvas do begin
    FillRect(Rect);

    strpos := Pos ('|', line);
    if (strpos = 0) then
      TextOut (Rect.Left, Rect.Top, line)
    else begin
      TextOut (Rect.Left, Rect.Top, Copy (line,1,strpos - 1));

      Delete (line, 1, strpos);

      strpos := Pos ('|', line);
      if (strpos = 0) then
        TextOut (Rect.Left + 50, Rect.Top, line)
      else begin
        TextOut (Rect.Left + 50, Rect.Top, Copy (line,1,strpos - 1));
        TextOut (Rect.Left + 200, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLEEinlagernForm.LPSuchMenuItemClick(Sender: TObject);
var
  idx,
  reflb,
  reflager : Integer;
  instr    : String;
  query    : TADOQuery;
  list     : TListBox;
begin
  list     := LPListBox;
  reflb    := GetComboBoxRef (LBComboBox);
  reflager := fRefLager;

  instr := '';

  if (fLPSortType = 2) then begin
    if (InputQuery (GetResourceText (1291), GetResourceText (1292), instr)) then begin
      query := TADOQuery.Create (Self);
      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        if (reflb = -1) then begin
          query.SQL.Add ('select REF from V_LP where REF_LAGER=:reflager and NAME=:nr');
          query.Parameters.ParamByName('reflager').Value := reflager;
        end else begin
          query.SQL.Add ('select REF from V_LP where REF_LB=:LBRef and NAME=:nr');
          query.Parameters.ParamByName('LBRef').Value := reflb;
        end;
        query.Parameters.ParamByName('nr').Value := instr;

        query.Open;

        if query.Fields [0].IsNull then begin
          if (reflb = -1) then
            MessageDLG (FormatMessageText (1097, []), mtError, [mbOk], 0)
          else MessageDLG (FormatMessageText (1098, []), mtError, [mbOk], 0)
        end else begin
          idx := FindListBoxRef (list, query.Fields [0].AsInteger);

          if (idx = -1) then
            MessageDLG (FormatMessageText (1099, []), mtError, [mbOk], 0)
          else
            list.ItemIndex := idx;
        end;

        query.Close;
      finally
        query.Free;
      end;
    end;
  end else if (fLPSortType = 0) then begin
    if (InputQuery (GetResourceText (1291), GetResourceText (1293), instr)) then begin
      query := TADOQuery.Create (Self);
      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        if (reflb = -1) then begin
          query.SQL.Add ('select REF from V_LP where REF_LAGER=:reflager and LP_NR=:nr');
          query.Parameters.ParamByName('reflager').Value := reflager;
        end else begin
          query.SQL.Add ('select REF from V_LP where REF_LB=:LBRef and LP_NR=:nr');
          query.Parameters.ParamByName('LBRef').Value := reflb;
        end;
        query.Parameters.ParamByName('nr').Value := instr;

        query.Open;

        if query.Fields [0].IsNull then begin
          if (reflb = -1) then
            MessageDLG (FormatMessageText (1097, []), mtError, [mbOk], 0)
          else MessageDLG (FormatMessageText (1098, []), mtError, [mbOk], 0)
        end else begin
          idx := FindListBoxRef (list, query.Fields [0].AsInteger);

          if (idx = -1) then
            MessageDLG (FormatMessageText (1099, []), mtError, [mbOk], 0)
          else
            list.ItemIndex := idx;
        end;

        query.Close;
      finally
        query.Free;
      end;
    end;
  end else begin
    if (InputQuery (GetResourceText (1291), GetResourceText (1294), instr)) then begin
      query := TADOQuery.Create (Self);
      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        if (reflb = -1) then begin
          query.SQL.Add ('select REF from V_LP where REF_LAGER=:reflager and LP_KOOR=:koord');
          query.Parameters.ParamByName('reflager').Value := reflager;
        end else begin
          query.SQL.Add ('select REF from V_LP where REF_LB=:LBRef and LP_KOOR=:koord');
          query.Parameters.ParamByName('LBRef').Value := reflb;
        end;
        query.Parameters.ParamByName('koord').Value := instr;

        query.Open;

        if query.Fields [0].IsNull then begin
          if (reflb = -1) then
            MessageDLG (FormatMessageText (1097, []), mtError, [mbOk], 0)
          else MessageDLG (FormatMessageText (1098, []), mtError, [mbOk], 0)
        end else begin
          idx := FindListBoxRef (list, query.Fields [0].AsInteger);

          if (idx = -1) then
            MessageDLG (FormatMessageText (1099, []), mtError, [mbOk], 0)
          else
            list.ItemIndex := idx;
        end;

        query.Close;
      finally
        query.Free;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLEEinlagernForm.UpdateLPListe (const LBRef : Integer);
var
  idx,
  dbres,
  maxanz     : Integer;
  namestr    : String;
  Tabulators : array[0..4] of Integer;
begin
  dbres := 0;

  if (UserReg.ReadRegValue ('SortLP', fLPSortType) <> 0) then
    fLPSortType := 1;

  ClearListBoxObjects (LPListBox);

  { Set the Tabulator Widths / Tabulatorweiten festlegen}
  Tabulators[0] := 10;
  Tabulators[1] := 10;
  Tabulators[2] := 16;
  Tabulators[3] := 100;
  Tabulators[3] := 50;
  LPListBox.TabWidth := 1;
  { Set the Tabulators / Tabulatoren setzen }
  SendMessage(LPListBox.Handle, LB_SETTABSTOPS, 5, Longint(@Tabulators));

  ADOQuery1.SQL.Clear;

  if (fLPSortType = 2) then
    ADOQuery1.SQL.Add ('select REF,LP_DISP,NAME,LP_KOOR,STATUS from V_LP where STATUS<>''SPERR'' and REF_LB=:ref_lb order by LP_DISP')
  else if (fLPSortType = 1) then
    ADOQuery1.SQL.Add ('select REF,LP_KOOR,NAME,LP_NR,STATUS from V_LP where STATUS<>''SPERR'' and REF_LB=:ref_lb order by LP_KOOR')
  else
    ADOQuery1.SQL.Add ('select REF,LP_NR,NAME,LP_KOOR,STATUS from V_LP where STATUS<>''SPERR'' and REF_LB=:ref_lb order by LP_KOOR nulls last, LP_NR nulls first, NAME');

  ADOQuery1.Parameters.ParamByName('ref_lb').Value := LBRef;

  Screen.Cursor := crSQLWait;

  LPListBox.Items.BeginUpdate;

  try
    try
      ADOQuery1.Open;

      while not (ADOQuery1.EOF) do begin
        if not (ADOQuery1.Fields [1].IsNull) then
          namestr := ADOQuery1.Fields [1].AsString+#9+ADOQuery1.Fields [3].AsString
        else if not (ADOQuery1.Fields [3].IsNull) then
          namestr := ADOQuery1.Fields [3].AsString+#9+ADOQuery1.Fields [1].AsString
        else
          namestr := ADOQuery1.Fields [2].AsString+#9+ADOQuery1.Fields [3].AsString;

        if (ADOQuery1.Fields [4].AsString = 'FREI') then
          LPListBox.Items.AddObject (' '+#9+namestr, TListBoxRef.Create (ADOQuery1.Fields [0].AsInteger))
        else
          LPListBox.Items.AddObject ('B'+#9+namestr, TListBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

        ADOQuery1.Next;
      end;

      ADOQuery1.Close;

      if (fRefAE > 0) then begin
        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ('select bes.REF_LP_BES, sum (bes.MENGE_FREI) from V_BES_ARTIKEL bes, VQ_LAGER_LP lp, VQ_ARTIKEL_EINHEIT ae where lp.REF=bes.REF_LP_BES and lp.REF_LB=:ref_lb and ae.REF=:ref_ae and bes.REF_AR=ae.REF_AR group by bes.REF_LP_BES');
        ADOQuery1.Parameters.ParamByName('ref_ae').Value := fRefAE;
        ADOQuery1.Parameters.ParamByName('ref_lb').Value := LBRef;

        ADOQuery1.Open;

        maxanz := -1;

        while not (ADOQuery1.EOF) do begin
          idx := FindListBoxRef (LPListBox, ADOQuery1.Fields [0].AsInteger);

          if (idx <> -1) then begin
            LPListBox.Items [idx] := LPListBox.Items [idx]+#9+ADOQuery1.Fields [1].AsString;

            if (ADOQuery1.Fields [1].AsInteger > maxanz) then begin
              maxanz := ADOQuery1.Fields [1].AsInteger;

              LPListBox.ItemIndex := idx;
            end;
          end;

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      end;
    except
    end;
  finally
    LPListBox.Items.EndUpdate;
    
    Screen.Cursor := crDefault;
  end;
end;

end.
