object LagerBereichStatistikFrame: TLagerBereichStatistikFrame
  Left = 0
  Top = 0
  Width = 786
  Height = 104
  TabOrder = 0
  object LPCapacityBar: TAdvSmoothCapacityBar
    Left = 0
    Top = 26
    Width = 786
    Height = 78
    AntiAlias = aaNone
    Align = alClient
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentFont = False
    Appearance.AutoFormatValues = False
    Appearance.BackGroundFill.Color = clBtnFace
    Appearance.BackGroundFill.ColorTo = 10983309
    Appearance.BackGroundFill.ColorMirror = clNone
    Appearance.BackGroundFill.ColorMirrorTo = clNone
    Appearance.BackGroundFill.GradientType = gtVertical
    Appearance.BackGroundFill.GradientMirrorType = gtSolid
    Appearance.BackGroundFill.BorderColor = clNone
    Appearance.BackGroundFill.Rounding = 0
    Appearance.BackGroundFill.ShadowOffset = 0
    Appearance.BackGroundFill.Glow = gmNone
    Appearance.Color = clLime
    Appearance.ColorTo = clGreen
    Appearance.CapacityFont.Charset = DEFAULT_CHARSET
    Appearance.CapacityFont.Color = clCaptionText
    Appearance.CapacityFont.Height = -12
    Appearance.CapacityFont.Name = 'Tahoma'
    Appearance.CapacityFont.Style = [fsBold]
    Appearance.CapacityFormat = '%.0f'
    Appearance.FreeFormat = '%.0f'
    Appearance.LegendFormat = '%.0f'
    Appearance.LegendFont.Charset = DEFAULT_CHARSET
    Appearance.LegendFont.Color = clWindowText
    Appearance.LegendFont.Height = -11
    Appearance.LegendFont.Name = 'Tahoma'
    Appearance.LegendFont.Style = []
    Appearance.Reflection = False
    Appearance.CapacityTextShadowColor = clNone
    Appearance.LegendTextShadowColor = clNone
    Items = <
      item
        Value = 10.000000000000000000
        Color = clRed
        ColorTo = clMaroon
        Description = 'Gesperrt'
      end
      item
        Value = 10.000000000000000000
        Color = clHighlight
        ColorTo = clNavy
        Description = 'Belegt voll'
      end
      item
        Value = 10.000000000000000000
        Color = clYellow
        ColorTo = clOlive
        Description = 'Teilweise belegt'
      end>
    CapacityDescription = 'Pl'#228'tze'
    FreeDescription = 'Leer'
    TotalCapacity = 100.000000000000000000
    ExplicitTop = 32
    ExplicitWidth = 625
    ExplicitHeight = 74
  end
  object CaptionPanel: TPanel
    Left = 0
    Top = 0
    Width = 786
    Height = 26
    Align = alTop
    BevelOuter = bvNone
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -13
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
    TabOrder = 0
    DesignSize = (
      786
      26)
    object NameLabel: TLabel
      Left = 8
      Top = 5
      Width = 69
      Height = 16
      Caption = 'NameLabel'
    end
    object BestandLabel: TLabel
      Left = 664
      Top = 7
      Width = 64
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'BestandLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object LECountLabel: TLabel
      Left = 504
      Top = 7
      Width = 65
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'LECountLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
    end
    object Label1: TLabel
      Left = 464
      Top = 5
      Width = 26
      Height = 16
      Anchors = [akTop, akRight]
      Caption = 'LEs:'
    end
    object Label2: TLabel
      Left = 592
      Top = 5
      Width = 64
      Height = 16
      Anchors = [akTop, akRight]
      Caption = 'Einheiten:'
    end
  end
end
