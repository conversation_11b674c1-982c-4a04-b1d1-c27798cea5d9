object EditWABereichForm: TEditWABereichForm
  Left = 603
  Top = 272
  Anchors = [akLeft, akTop, akRight]
  BorderStyle = bsDialog
  Caption = 'EditLagerbereichForm'
  ClientHeight = 254
  ClientWidth = 306
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    306
    254)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 64
    Width = 124
    Height = 13
    Caption = 'Name des Lagerbereiches'
  end
  object Label2: TLabel
    Left = 8
    Top = 108
    Width = 65
    Height = 13
    Caption = 'Beschreibung'
  end
  object Label3: TLabel
    Left = 8
    Top = 161
    Width = 79
    Height = 13
    Caption = 'Lagerbereichsart'
  end
  object Bevel1: TBevel
    Left = 6
    Top = 152
    Width = 294
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel2: TBevel
    Left = 6
    Top = 211
    Width = 294
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 482
  end
  object Label4: TLabel
    Left = 8
    Top = 8
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Bevel3: TBevel
    Left = 4
    Top = 53
    Width = 294
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object NameEdit: TEdit
    Left = 8
    Top = 80
    Width = 289
    Height = 21
    TabOrder = 1
    Text = 'NameEdit'
  end
  object BeschreibungEdit: TEdit
    Left = 8
    Top = 124
    Width = 289
    Height = 21
    TabOrder = 2
    Text = 'BeschreibungEdit'
  end
  object ArtComboBox: TComboBoxPro
    Left = 8
    Top = 177
    Width = 289
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 100
    ItemHeight = 16
    TabOrder = 3
    Items.Strings = (
      'WE'
      'WA'
      'LAGER')
  end
  object OkButton: TButton
    Left = 131
    Top = 222
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 4
  end
  object AbortButton: TButton
    Left = 222
    Top = 222
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = '&Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 289
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 100
    ItemHeight = 16
    TabOrder = 0
    Items.Strings = (
      'WE'
      'WA'
      'LAGER')
  end
end
