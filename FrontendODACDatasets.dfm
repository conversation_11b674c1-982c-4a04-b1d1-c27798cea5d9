object FrontendODACDataModule: TFrontendODACDataModule
  Height = 313
  Width = 414
  object AufKopfDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    FetchAll = True
    ReadOnly = True
    BeforeExecute = BeforeExecute
    AfterExecute = AfterExecute
    Left = 32
    Top = 32
  end
  object LagerBesDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 128
    Top = 32
  end
  object LagerREVDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 32
    Top = 88
  end
  object WADataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    FetchAll = True
    ReadOnly = True
    Left = 128
    Top = 88
  end
  object RetoureAvisDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    FetchAll = True
    ReadOnly = True
    BeforeExecute = BeforeExecute
    AfterExecute = AfterExecute
    Left = 32
    Top = 152
  end
  object KommKopfDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    FetchAll = True
    ReadOnly = True
    BeforeExecute = BeforeExecute
    AfterExecute = AfterExecute
    Left = 128
    Top = 152
  end
  object OMSOrderQuery: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    FetchAll = True
    ReadOnly = True
    BeforeExecute = BeforeExecute
    AfterExecute = AfterExecute
    Left = 232
    Top = 32
  end
  object OMSOrderPosQuery: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    FetchAll = True
    ReadOnly = True
    BeforeExecute = BeforeExecute
    AfterExecute = AfterExecute
    Left = 232
    Top = 88
  end
  object BatchKopfDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    FetchAll = True
    ReadOnly = True
    BeforeExecute = BeforeExecute
    AfterExecute = AfterExecute
    Left = 232
    Top = 152
  end
  object WarenBesPoolDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 328
    Top = 32
  end
  object WarenBesDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 328
    Top = 88
  end
  object WarenBesBesDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 328
    Top = 152
  end
  object RetoureDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    FetchAll = True
    ReadOnly = True
    BeforeExecute = BeforeExecute
    AfterExecute = AfterExecute
    Left = 32
    Top = 208
  end
  object ProtoDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 128
    Top = 208
  end
  object ZollBesDataset: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 232
    Top = 208
  end
  object ZollBesMoveDataset: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 328
    Top = 208
  end
end
