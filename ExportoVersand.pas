﻿unit ExportoVersand;

{$i compilers.inc}

interface

uses
  Classes, PrinterUtils, LogFile, DB, Ora, OraSmart;

function CreateExportoAuftrag (const RefGate, RefSped, RefAuf : Integer; const DatenPath : String; var ErrorText : String) : Integer;
function CreateExportoNVETour (const RefGate, RefSped : Integer; const DatenPath : String; var ErrorText : String) : Integer;

function CreateExportoTour    (const RefVerl : Integer; const DatenPath : String; var ErrorText : String) : Integer;

function CreateExportoVersand (Query : TSmartQuery; const DatenPath, LabelType : String; const PrtInfo : TPrinterPorts; var DoneFlag, ResponsFlag : Boolean; var VersandApp, Versender, SendungsID, SendungsNr, TrackUrl, Barcode, LabelFormat : String; LabelImage : TMemoryStream; var RefLabel : Integer; var ErrorText : String) : Integer;

implementation

uses
  {$ifdef Trace}
   Trace,
  {$endif}

  Windows, Math, SysUtils, DateUtils, Variants, EncdDecd, StringUtils, VCLUtilitys,
  xml_base, xml_parser, xml_utils, uLkJSON, Timers,
  SendHTTPRequest

  {$ifdef ErrorTracking}
    ,ErrorTracking
  {$endif}

  {$ifdef ResourceText}
    ,ResourceText
  {$endif}

  {$ifdef UNICODE}
    ,System.NetEncoding
    ,WideStrUtils
  {$endif}

  ,LVSGlobalDaten
  ,PrintModul
  ,DatenModul
  ,FrontendUtils
  ,LVSDatenInterface
  ,LVSArtikelInterface;

const
  RESTDumpDir       = 'Exporto\soap\';
  LabelDumpDir      = 'Exporto\Labels\';

var
  APISped      : Integer;
  APIURL       : String;
  APITokenUser : String;
  APIToken     : String;
  APITokenTime : TDateTime;

function Login (const RefGate : Integer; const DatenPath : String; var ApiURL, ApiToken, ErrorText: String) : Integer;
var
  res       : Integer;
  sdata     : TMemoryStream;
  errcode   : Integer;
  apipw,
  apiuser   : String;
  errtext   : String;
  resp      : String;
  js        : TlkJSONobject;
  fs        : TlkJSONbase;
  cfgquery  : TSmartQuery;
  strlist   : TStringList;

  {$ifdef UNICODE}
    utfstr       : AnsiString;
    datastr      : String;
  {$endif}
begin
  cfgquery  := TSmartQuery.Create (Nil);

  try
    cfgquery.ReadOnly := True;
    cfgquery.Session  := LVSDatenModul.OraMainSession;

    cfgquery.SQL.Add ('select cfg.REST_URL as SPED_REST_URL, g.* from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
    cfgquery.Params [0].Value := RefGate;

    cfgquery.Open;

    if not Assigned (cfgquery.FindField('API_KEY')) then
      ErrorText := 'Not perpared for api key'
    else if cfgquery.FieldByName('API_KEY').IsNull then
      ErrorText := 'No Credentials'
    else begin
      strlist := TStringList.Create;

      try
        strlist.Delimiter := ';';
        strlist.StrictDelimiter := true;

        strlist.DelimitedText := cfgquery.FieldByName('API_KEY').AsString;

        if (strlist.Count < 2) then begin
          res := 24;
          ErrorText := 'Credentials not complete';
        end else begin
          apiuser := strlist [0];
          apipw   := strlist [1];
        end;
      finally
        strlist.Free;
      end;

      if Assigned (cfgquery.FindField ('REST_URL')) then
        ApiURL  := cfgquery.FieldByName('REST_URL').AsString;

      if (Length (ApiURL) = 0) then
        ApiURL  := cfgquery.FieldByName('SPED_REST_URL').AsString;

      if (Length (ApiURL) = 0) then
        ErrorText := 'Not REST url defined';
    end;

    cfgquery.Close;
  finally
    cfgquery.Free;
  end;

  if (Length (ErrorText) > 0) then begin
    res := -26;

    ApiToken      := '';
    APITokenUser  := '';
  end else if (Length (ApiToken) = 0) or (Length (APITokenUser) = 0) or (APITokenUser<>apiuser) or (Now > APITokenTime) then begin
    ApiToken     := '';
    APITokenUser := '';

    StrToFile (DatenPath + RESTDumpDir+FormatDateTime ('yyyymmdd', Now)+'\post_auth_'+FormatDateTime ('hhnnss', Now)+'.txt', 'URL='+ApiURL+', apiuser='+apiuser+', apipw='+apipw);

    sdata := TMemoryStream.Create;

    try
      sdata.Clear;
      if SendRequest(ApiURL, // Host,
                      -1, //Port
                      'v1/auth/token', // Service
                      'POST', //Methode
                      '', //Kein proxy
                      //'localhost:8888', // Fiddler als Proxy
                      apiuser, apipw, // User , PW
                      '', //Action
                      'application/json', //ContentType
                      ['Content-Type: application/json','accept: application/json'], //AddHeader
                      UTF8Encode ('{"grant_type": "client_credentials"}'),         // RequestData
                      resp,
                      sdata, //ResponseStream
                      errcode, // Fehlercode
                      errtext) // Fehlertext
                    then
      begin
        try
          sdata.Position := 0;
          sdata.SaveToFile(DatenPath + RESTDumpDir+FormatDateTime ('yyyymmdd', Now)+'\resp_auth_'+FormatDateTime ('hhnnss', Now)+'.json');
        except
          res := 0;
        end;

        sdata.Position := 0;

        fs := Nil;

        {$ifdef UNICODE}
          SetLength(utfstr, sdata.Size);
          sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
          datastr := StringUtils.StringToUTF (utfstr);

          js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
        {$else}
          sdata.Position := 0;
          js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
        {$endif}

        if not Assigned (js) then begin
          ErrorText := 'Exporto API error message';
        end else begin
          try
            fs := js.Field['access_token'];

            if not Assigned (fs) then begin
              res := 37;
              ErrorText := 'auth error';
            end else begin
              ApiToken     := fs.Value;
              APITokenUser := apiuser;
              APITokenTime := IncSecond (Now, 360);
            end;
          except
            res := 37;
            ErrorText := 'auth error';
          end;
        end;
      end else begin
        res := 22;

        ErrorText := ErrorText + 'Error on Exporto server';

        if (Length (errtext) > 0) then
          ErrorText := ErrorText + #13 + errtext;
      end;
    finally
     sdata.Free;
    end;
  end;

  Result := res;
end;

function SendExportArtikelstamm (const RefAE : Integer; const DatenPath : String; var ApiURL, ApiToken : String; var ErrorText : String) : Integer;
var
  res       : Integer;
  selquery  : TSmartQuery;
  sdata     : TMemoryStream;
  errcode   : Integer;
  errtext   : String;
  resp,
  body      : String;
  js        : TlkJSONobject;
  fs        : TlkJSONbase;

  {$ifdef UNICODE}
    utfstr       : AnsiString;
    datastr      : String;
  {$endif}
begin
  res := 0;

  if (RefAE > 0) then begin
    sdata := TMemoryStream.Create;
    selquery := TSmartQuery.Create (Nil);

    try
      selquery.ReadOnly := True;
      selquery.Session := LVSDatenModul.OraMainSession;

      selquery.SQL.Add ('select'
                       +'  ar.ARTIKEL_NR'
                       +'  ,GETARTIKELTEXT (ar.REF, PA_SESSION_DATEN.GetSprache) as ARTIKEL_KURZ_TEXT'
                       +'  ,nvl (ar.COUNTRY_OF_ORIGIN, ''DE'') as COUNTRY_OF_ORIGIN'
                       +'  ,ae.EAN'
                       +'  ,nvl (ae.BRUTTO_GEWICHT, ae.NETTO_GEWICHT) as GEWICHT'
                       +'  ,nvl (ar.TARIC_NUMBER, mc.BASE_TARIC_NUMBER) as TARIC_NUMBER'
                       +'  ,(select gf.UN_NR from V_ARTIKEL_GEFAHRSTOFFE gf, V_ARTIKEL_REL_GEFAHRSTOFFE rgf where gf.REF=rgf.REF_GEFAHRSTOFFE and rgf.REF_AR=ar.REF and ROWNUM=1) as UN_NR'
                       +' from'
                       +'   VQ_ARTIKEL_EINHEIT ae'
                       +'   inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR)'
                       +'   inner join V_MANDANT m on (m.REF=nvl (ar.REF_SUB_MAND,ar.REF_MAND))'
                       +'   inner join V_MANDANT_CONFIG mc on (mc.REF_MAND=m.REF)'
                       +' where'
                       +'   ae.REF=:ref_ae'
                       );
      selquery.Params [0].Value := RefAE;

      selquery.Open;

      if (selquery.RecordCount > 0) then begin
        body := '{';

        body := body + '"articleId":"'+selquery.FieldByName ('ARTIKEL_NR').AsString+'"';
        body := body + ',"name":"'+selquery.FieldByName ('ARTIKEL_KURZ_TEXT').AsString+'"';
        body := body + ',"ean":"'+selquery.FieldByName ('EAN').AsString+'"';

        if not selquery.FieldByName ('GEWICHT').IsNull then
          body := body + ',"weight":'+selquery.FieldByName ('GEWICHT').AsString;

        body := body + ',"unNumber":"'+selquery.FieldByName ('UN_NR').AsString+'"';
        body := body + ',"originCountry":"'+selquery.FieldByName ('COUNTRY_OF_ORIGIN').AsString+'"';
        body := body + ',"customs": [{';
        body := body +   '"country":"DE"';
        body := body +   ',"hsCode":"'+selquery.FieldByName ('TARIC_NUMBER').AsString+'"';
        body := body + '}]';

        body := body + '}';

        StrToFile (DatenPath + RESTDumpDir+FormatDateTime ('yyyymmdd', Now)+'\post_article_'+selquery.FieldByName ('ARTIKEL_NR').AsString+'.json', body);

        sdata.Clear;
        if SendRequest(ApiURL, // Host,
                        -1, //Port
                        'v1/article', // Service
                        'POST', //Methode
                        '', //Kein proxy
                        //'localhost:8888', // Fiddler als Proxy
                        '', '', // User , PW
                        '', //Action
                        'application/json', //ContentType
                        ['Authorization: Bearer '+ApiToken,'Content-Type: application/json','accept: application/json'], //AddHeader
                        UTF8Encode (body),         // RequestData
                        resp,
                        sdata, //ResponseStream
                        errcode, // Fehlercode
                        errtext) // Fehlertext
                      then
        begin
          try
            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+FormatDateTime ('yyyymmdd', Now)+'\resp_article_'+selquery.FieldByName ('ARTIKEL_NR').AsString+'.json');
          except
            res := 0;
          end;

          sdata.Position := 0;

          fs := Nil;

          {$ifdef UNICODE}
            SetLength(utfstr, sdata.Size);
            sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
            datastr := StringUtils.StringToUTF (utfstr);

            js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
          {$else}
            sdata.Position := 0;
            js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
          {$endif}

          if (sdata.Size > 0) and not Assigned (js) then begin
            res := 26;
            ErrorText := 'Exporto API error message'
          end else if (Pos ('400', resp) > 0) then begin
            res := 27;
            ErrorText := 'Error API error message';

            fs := js.Field['message'];
            if Assigned (fs) and not (fs.Value = NULL) then begin
              //Meldung wegen doppelten Artikel ignorieren
              if (Pos ('unique', fs.Value) > 0) then begin
                res := 0;
                ErrorText := '';
              end;
            end;
          end else begin
          end;
        end else begin
          res := 22;

          ErrorText := ErrorText + 'Error on Exporto server';

          if (Length (errtext) > 0) then
            ErrorText := ErrorText + #13 + errtext;
        end;
      end;

      selquery.Close;
    finally
      sdata.Free;
      selquery.Free;
    end;
  end;

  Result := res;
end;


function CreateExportoVersand (Query : TSmartQuery; const DatenPath, LabelType : String; const PrtInfo : TPrinterPorts; var DoneFlag, ResponsFlag : Boolean; var VersandApp, Versender, SendungsID, SendungsNr, TrackUrl, Barcode, LabelFormat : String; LabelImage : TMemoryStream; var RefLabel : Integer; var ErrorText : String) : Integer;

  function CreateExportoLabel (const Product : String; const RefNVE : Integer; var ErrorText : String) : Integer;
  var
    idx,
    res       : Integer;
    selquery,
    liefquery : TSmartQuery;
    sdata     : TMemoryStream;
    errcode   : Integer;
    errtext   : String;
    bytes     : TBytes;
    ch        : AnsiChar;
    outstr    : AnsiString;
    resp,
    body,
    subbody   : String;
    js        : TlkJSONobject;
    fs        : TlkJSONbase;
    us        : TlkJSONbase;

    {$ifdef UNICODE}
      utfstr       : AnsiString;
      datastr      : String;
    {$endif}
  begin
    res := 0;

    if (RefNVE > 0) then begin
      sdata     := TMemoryStream.Create;
      selquery  := TSmartQuery.Create (Nil);
      liefquery := TSmartQuery.Create (Nil);

      try
        selquery.ReadOnly := True;
        selquery.Session := LVSDatenModul.OraMainSession;
        liefquery.ReadOnly := True;
        liefquery.Session := LVSDatenModul.OraMainSession;

        liefquery.SQL.Add ('select * from V_AUFTRAG_ADR where REF=:RefAdr');

        if Query.FieldByName ('REF_LIEFER_ADR').IsNull then
          liefquery.Params.ParamByName('RefAdr').Value := Query.FieldByName ('REF_KUNDEN_ADR').AsInteger
        else
          liefquery.Params.ParamByName('RefAdr').Value := Query.FieldByName ('REF_LIEFER_ADR').AsInteger;

        liefquery.Open;

        body := '{';

        body := body + '"order":{';
        body := body + '"customerFacingId":"'+Query.FieldByName ('AUFTRAG_NR').AsString+'"';
        body := body + '}';

        body := body + ',"product":{';

        if (PrtInfo.Model = 'ZPL300') or (PrtInfo.Model = 'ZPL_300') then
          body := body + '"format":"ZPL2_300"'
        else
          body := body + '"format":"ZPL2_200"';

        body := body + ',"methodId":"'+Product+'"';
        body := body + '}';

        body := body + ',"address": {';

        if liefquery.FieldByName ('COMPANY').IsNull  then
          body := body +   '"name":"'+liefquery.FieldByName ('NAME1').AsString+'"'
        else
          body := body +   '"name":"'+liefquery.FieldByName ('NAME2').AsString+'"';

        if not liefquery.FieldByName ('COMPANY').IsNull  then
          body := body +   ',"company":"'+liefquery.FieldByName ('COMPANY').AsString+'"';

        body := body +   ',"line1":"'+liefquery.FieldByName ('STRASSE').AsString+'"';

        if not liefquery.FieldByName ('STRASSE_2').IsNull  then
          body := body +   ',"line2":"'+liefquery.FieldByName ('STRASSE_2').AsString+'"';

        body := body +   ',"city":"'+liefquery.FieldByName ('ORT').AsString+'"';
        body := body +   ',"postCode":"'+liefquery.FieldByName ('PLZ').AsString+'"';
        body := body +   ',"countryCode":"'+liefquery.FieldByName ('LAND_ISO').AsString+'"';
        body := body +   ',"email":"'+liefquery.FieldByName ('EMAIL').AsString+'"';

        if not liefquery.FieldByName ('TELEFON').IsNull  then
          body := body +   ',"phone":"'+liefquery.FieldByName ('TELEFON').AsString+'"';

        body := body + '}';

        body := body + ',"package": {';
        body := body +   '"reference":"'+Query.FieldByName ('NVE_NR').AsString+'"';
        body := body +   ',"weight":'+IntToStr (trunc (Query.FieldByName ('SENDUNG_BRUTTO').AsFloat * 1000));
        body := body + '}';

        body := body + '}';

        StrToFile (DatenPath + RESTDumpDir+FormatDateTime ('yyyymmdd', Now)+'\post_label_'+Query.FieldByName ('NVE_NR').AsString+'.json', body);

        sdata.Clear;
        if SendRequest(apiurl, // Host,
                        -1, //Port
                        'v1/label', // Service
                        'POST', //Methode
                        '', //Kein proxy
                        //'localhost:8888', // Fiddler als Proxy
                        '', '', // User , PW
                        '', //Action
                        'application/json', //ContentType
                        ['Authorization: Bearer '+apitoken,'Content-Type: application/json','accept: application/json'], //AddHeader
                        UTF8Encode (body),         // RequestData
                        resp,
                        sdata, //ResponseStream
                        errcode, // Fehlercode
                        errtext) // Fehlertext
                      then
        begin
          try
            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+FormatDateTime ('yyyymmdd', Now)+'\resp_label_'+Query.FieldByName ('NVE_NR').AsString+'.json');
          except
            res := 0;
          end;

          sdata.Position := 0;

          fs := Nil;

          {$ifdef UNICODE}
            SetLength(utfstr, sdata.Size);
            sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
            datastr := StringUtils.StringToUTF (utfstr);

            js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
          {$else}
            sdata.Position := 0;
            js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
          {$endif}

          if (sdata.Size > 0) and not Assigned (js) then begin
            res := 26;
            ErrorText := 'Exporto API error message'
          end else if (Pos ('400', resp) > 0) then begin
            res := 26;
            ErrorText := 'Error API error message';

            fs := js.Field['message'];
            if Assigned (fs) then begin
              for idx := 0 to fs.Count - 1 do begin
                if not (fs.Child [idx].Value = NULL) then
                  ErrorText := ErrorText + #13+#10 + '  ' + StringReplace (fs.Child [idx].Value, '\u0027', '''', [rfReplaceAll]);
              end;
            end;
          end else begin
            try
              us := js.Field['trackingCode'];

              if Assigned (us) and not (us.Value = NULL) then
                SendungsNr := us.Value;

              us := js.Field['trackingUrl'];

              if Assigned (us) and not (us.Value = NULL) then
                TrackUrl := us.Value;

              us := js.Field['carrierName'];

              if Assigned (us) and not (us.Value = NULL) then
                Versender := us.Value;

              us := js.Field['label'];

              if not Assigned (us) then begin
                res := 38;
                ErrorText := 'label_zpl error'
              end else begin
                //outstr := DecodeString ();
                try
                  bytes  := TNetEncoding.Base64.DecodeStringToBytes(us.Value);

                  outstr := '';

                  idx := Low (bytes);
                  while (idx <= High (bytes)) do begin
                    if (ord (bytes [idx]) < 127) then
                      outstr := outstr + chr (bytes [idx])
                    else begin
                      outstr := outstr + GetCP850AnsiChar (bytes [idx]);
                    end;

                    Inc (idx);
                  end;

                  //outstr := TEncoding.UTF8.GetString(bytes);
                except
                end;

                LabelFormat := 'zpl';

                ForceDirectories(DatenPath + LabelDumpDir + 'Exporto');

                LabelImage.WriteBuffer (Pointer(outstr)^, Length(outstr));

                try
                  LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'Exporto\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                except
                end;

                if (res <> 0) then
                  ErrorText := 'Error '+IntToStr (res) + ' while save shipping label data';
              end;
            except
              res := 37;
              ErrorText := 'Label data error';
            end;
          end;
        end else begin
          res := 22;

          ErrorText := 'Error on Exporto server';

          if (Length (errtext) > 0) then
            ErrorText := ErrorText + #13 + errtext;
        end;

        liefquery.Close;
      finally
        sdata.Free;
        liefquery.Free;
        selquery.Free;
      end;
    end;

    Result := res;
  end;

var
  res       : Integer;
  npreis,
  bpreis    : Integer;
  landstr,
  prodstr   : String;
  cfgquery,
  selquery  : TSmartQuery;
  strlist   : TStringList;
  olddec    : Char;
begin
  ErrorText := '';
  DoneFlag := True;

  if (Length (PrtInfo.Port) = 0) then begin
    res := -1;
    {$ifdef ResourceText}
      ErrorText := FormatMessageText(1443, []);
    {$else}
      ErrorText := 'Es ist noch kein NVE-Drucker definiert';
    {$endif}
  end else begin
    if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
      prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
    end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
      prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
    end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
      prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
    end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
      prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
    end else begin
      prodstr := query.FieldByName('SPED_NAME').AsString;
    end;

    if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
      SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

      res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
    end;

    if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
      olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

      try
        {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

        ForceDirectories(DatenPath + RESTDumpDir+FormatDateTime ('yyyymmdd', Now));

        RefLabel := -1;

        cfgquery  := TSmartQuery.Create (Nil);

        try
          cfgquery.ReadOnly := True;
          cfgquery.Session := Query.Session;

          cfgquery.SQL.Add ('select cfg.REST_URL as SPED_REST_URL, g.* from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
          cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

          cfgquery.Open;

          (*
          customstr := cfgquery.FieldByName('SENDIT_CLIENT').AsString;
          locstr    := cfgquery.FieldByName('SENDIT_LOCATION').AsString;
          codestr   := cfgquery.FieldByName('GATEWAY').AsString;
          prodstr   := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;
          telstr    := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;
          *)

          cfgquery.Close;
        finally
          cfgquery.Free;
        end;

        if (Length (ErrorText) > 0) then
          res := -26
        else begin
          if (APISped <> query.FieldByName('REF_SPED_GATEWAY').AsInteger) then
            APIToken := '';

          res := Login (query.FieldByName('REF_SPED_GATEWAY').AsInteger, DatenPath, apiurl, APIToken, ErrorText);

          if (res = 0) then begin
            APISped := query.FieldByName('REF_SPED_GATEWAY').AsInteger;
          end;

          if (res = 0) then begin
            res := CreateExportoLabel (prodstr, query.FieldByName('REF_NVE').AsInteger, ErrorText)
          end;
        end;
      finally
        {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
      end;
    end;
  end;
end;


function CreateExportoAuftrag (const RefGate, RefSped, RefAuf : Integer; const DatenPath : String; var ErrorText : String) : Integer;
var
  query     : TSmartQuery;

function SendExportPakete (const NettoBetrag, BruttoBetrag : Double; var ErrorText : String) : Integer;
var
  idx,
  res       : Integer;
  selquery,
  nvequery,
  liefquery : TSmartQuery;
  sdata     : TMemoryStream;
  errcode   : Integer;
  errtext   : String;
  resp,
  body,
  subbody,
  nvebody,
  orderid   : String;
  nvecount  : Integer;
  js        : TlkJSONobject;
  fs        : TlkJSONbase;

  {$ifdef UNICODE}
    utfstr       : AnsiString;
    datastr      : String;
  {$endif}
begin
  res := 0;

  sdata     := TMemoryStream.Create;
  selquery  := TSmartQuery.Create (Nil);
  liefquery := TSmartQuery.Create (Nil);
  nvequery  := TSmartQuery.Create (Nil);

  try
    selquery.ReadOnly := True;
    selquery.Session := LVSDatenModul.OraMainSession;
    liefquery.ReadOnly := True;
    liefquery.Session := LVSDatenModul.OraMainSession;
    nvequery.ReadOnly := True;
    nvequery.Session := LVSDatenModul.OraMainSession;

    nvequery.SQL.Clear;
    nvequery.SQL.Add ('select count (*) from V_NVE_01 nve where nve.STATUS<>''DEL'' and nve.SENDUNGS_NR is not null and nve.REF_AUF_KOPF=:ref_auf and nve.REF_SPED=:ref_sped');
    nvequery.Params.ParamByName('ref_auf').Value  := RefAuf;
    nvequery.Params.ParamByName('ref_sped').Value := RefSped;

    nvequery.Open;

    nvecount := nvequery.Fields [0].AsInteger;

    nvequery.Close;

    if (nvecount > 0) then begin
      orderid := '';

      sdata.Clear;
      if SendRequest(ApiURL, // Host,
                      -1, //Port
                      'v1/order/'+Query.FieldByName ('AUFTRAG_NR').AsString, // Service
                      'DELETE', //Methode
                      '', //Kein proxy
                      //'localhost:8888', // Fiddler als Proxy
                      '', '', // User , PW
                      '', //Action
                      'application/json', //ContentType
                      ['Authorization: Bearer '+ApiToken,'Content-Type: application/json','accept: application/json'], //AddHeader
                      '',         // RequestData
                      resp,
                      sdata, //ResponseStream
                      errcode, // Fehlercode
                      errtext) // Fehlertext
                    then
      begin
        try
          sdata.Position := 0;
          sdata.SaveToFile(DatenPath + RESTDumpDir+FormatDateTime ('yyyymmdd', Now)+'\post_order_delete_'+Query.FieldByName ('AUFTRAG_NR').AsString+'.json');
        except
          res := 0;
        end;

        sdata.Position := 0;

        fs := Nil;

        {$ifdef UNICODE}
          SetLength(utfstr, sdata.Size);
          sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
          datastr := StringUtils.StringToUTF (utfstr);

          js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
        {$else}
          sdata.Position := 0;
          js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
        {$endif}

        if Assigned (js) then begin
          fs := js.Field['error'];
          if Assigned (fs) then begin
            if (fs.Value = 'Bad Request') then
              orderid := Query.FieldByName ('AUFTRAG_NR').AsString;
          end;
        end;
      end;

      if (Length (orderid) = 0) then begin
        liefquery.SQL.Add ('select * from V_AUFTRAG_ADR where REF=:RefAdr');

        if Query.FieldByName ('REF_LIEFER_ADR').IsNull then
          liefquery.Params.ParamByName('RefAdr').Value := Query.FieldByName ('REF_KUNDEN_ADR').AsInteger
        else
          liefquery.Params.ParamByName('RefAdr').Value := Query.FieldByName ('REF_LIEFER_ADR').AsInteger;

        liefquery.Open;

        nvequery.SQL.Clear;
        nvequery.SQL.Add ('select * from V_NVE_01 nve where nve.STATUS<>''DEL'' and nve.SENDUNGS_NR is not null and nve.REF_AUF_KOPF=:ref_auf and nve.REF_SPED=:ref_sped');
        nvequery.Params.ParamByName('ref_auf').Value  := RefAuf;
        nvequery.Params.ParamByName('ref_sped').Value := RefSped;

        nvequery.Open;

        nvebody := '';

        while not nvequery.Eof do begin
          selquery.SQL.Clear;
          selquery.SQL.Add('select ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN'') as ARTIKEL_TEXT,bes.MENGE,vpe.KURZ_BEZEICHNUNG,ar.COUNTRY_OF_ORIGIN'
                          +',nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT),nvl (ae.EAN,ae.BARCODE) as BARCODE'
                          +',coalesce (rep.NETTO_BETRAG,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT,ae.PREIS * pos.MENGE_GESAMT) as NETTO_BETRAG'
                          +',coalesce (rep.BRUTTO_BETRAG,(re.BRUTTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT,ae.PREIS * pos.MENGE_GESAMT) as BRUTTO_BETRAG'
                          +',nvl (arset.ANZAHL_VPE, 1) as ANZAHL_VPE'
                          +',pos.REF_AR as POS_REF_AR, ae.REF_AR'
                          +' from'
                          +'   V_LAGER_NVE_BESTAND bes'
                          +'   inner join V_AUFTRAG_POS pos on (pos.REF=bes.REF_AUF_POS)'
                          +'   inner join VQ_AUFTRAG auf on (auf.REF=pos.REF_AUF_KOPF)'
                          +'   inner join V_AUFTRAG_RECHNUNG re on (re.REF_AUF_KOPF=auf.REF)'
                          +'   inner join V_AUFTRAG_POS_RECHNUNG rep on (rep.REF_AUF_POS=pos.REF)'
                          +'   inner join V_ARTIKEL posar on (posar.REF=pos.REF_AR)'
                          +'   inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=bes.REF_AR_EINHEIT)'
                          +'   inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'
                          +'   inner join V_ARTIKEL_VPE vpe on (vpe.REF=ae.REF_EINHEIT)'
                          +'   left outer join V_ARTIKEL_SET arset on (arset.REF=posar.REF_ARTIKEL_SET)'
                          +' where'
                          +'   nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0'
                          +'   and bes.REF_NVE=:ref'
                          );
          if nvequery.FieldByName('REF_MAIN_NVE').IsNull then
            selquery.Params [0].Value := nvequery.FieldByName('REF').AsInteger
          else
            selquery.Params [0].Value := nvequery.FieldByName('REF_MAIN_NVE').AsInteger;

          selquery.Open;

          subbody := '';

          while not selquery.Eof do begin
            if (Length (subbody) > 0) then subbody := subbody +',';

            subbody := subbody + '{';
            subbody := subbody + '"articleId":"'+selquery.FieldByName ('ARTIKEL_NR').AsString+'"';
            subbody := subbody + ',"name":"'+selquery.FieldByName ('ARTIKEL_TEXT').AsString+'"';
            subbody := subbody + ',"quantity":'+selquery.FieldByName ('MENGE').AsString;

            if (selquery.FieldByName ('BRUTTO_BETRAG').IsNull or (round (selquery.FieldByName ('BRUTTO_BETRAG').AsInteger / 1000) < 1)) then
              subbody := subbody + ',"priceGross":'+IntToStr (1)
            else
              subbody := subbody + ',"priceGross":'+IntToStr (round (selquery.FieldByName ('BRUTTO_BETRAG').AsInteger / 1000));

            if (selquery.FieldByName ('NETTO_BETRAG').IsNull or (round (selquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000) < 1)) then
              subbody := subbody + ',"priceNet":'+IntToStr (1)
            else
              subbody := subbody + ',"priceNet":'+IntToStr (round (selquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000));

            if not (selquery.FieldByName ('BARCODE').IsNull) then
              subbody := subbody + ',"barcode":"'+selquery.FieldByName ('BARCODE').AsString+'"';

            subbody := subbody + '}';

            selquery.Next;
          end;

          selquery.Close;

          //Wenn auf der NVE überhaupt was darauf ist
          if (Length (subbody) > 0) then begin
            if (Length (nvebody) > 0) then nvebody := nvebody +',';

            nvebody := nvebody + '{';
            nvebody := nvebody +   '"shipmentId":"'+nvequery.FieldByName ('NVE_NR').AsString+'"';

            if (nvequery.FieldByName ('VERSENDER').AsString = 'PCH') then
              nvebody := nvebody +   ',"foreignOutboundTrackingId":"'+nvequery.FieldByName ('SENDUNGS_NR').AsString+'"'
            else
              nvebody := nvebody +   ',"domesticTrackingId":"'+nvequery.FieldByName ('SENDUNGS_NR').AsString+'"';

            nvebody := nvebody +   ',"weight":'+IntToStr (trunc (nvequery.FieldByName ('BRUTTO_GEWICHT').AsFloat * 1000));

            nvebody := nvebody +   ',"lineItems": ['+ subbody + ']';
            nvebody := nvebody + '}';
          end;

          nvequery.Next;
        end;

        nvequery.Close;

        if (Length (nvebody) > 0) then begin
          body := '{';

          body := body + '"orderId":"'+Query.FieldByName ('AUFTRAG_NR').AsString+'"';
          body := body + ',"customerFacingId":"'+Query.FieldByName ('AUFTRAG_NR').AsString+'"';

          if (round (BruttoBetrag) < 1) then
            body := body + ',"totalGross":'+IntToStr (1)
          else
            body := body + ',"totalGross":'+IntToStr (round (BruttoBetrag));

          if (round (NettoBetrag) < 1) then
            body := body + ',"totalNet":'+IntToStr (1)
          else
            body := body + ',"totalNet":'+IntToStr (round (NettoBetrag));

          if Query.FieldByName ('CURRENCY').IsNull then
            body := body + ',"currency":"EUR"'
          else
            body := body + ',"currency":"'+Query.FieldByName ('CURRENCY').AsString+'"';

          body := body + ',"deliveryAddress": {';
          if liefquery.FieldByName ('COMPANY').IsNull  then
            body := body +   '"name":"'+liefquery.FieldByName ('NAME1').AsString+'"'
          else begin
            body := body +   '"company":"'+liefquery.FieldByName ('COMPANY').AsString+'"';
            body := body +   ',"name":"'+liefquery.FieldByName ('NAME2').AsString+'"';
          end;

          body := body +   ',"address":"'+liefquery.FieldByName ('STRASSE').AsString+'"';

          if not liefquery.FieldByName ('STRASSE_2').IsNull  then
            body := body +   ',"additionalAddress":"'+liefquery.FieldByName ('STRASSE_2').AsString+'"';

          body := body +   ',"city":"'+liefquery.FieldByName ('ORT').AsString+'"';
          body := body +   ',"zip":"'+liefquery.FieldByName ('PLZ').AsString+'"';
          body := body +   ',"country":"'+liefquery.FieldByName ('LAND_ISO').AsString+'"';
          body := body +   ',"email":"'+liefquery.FieldByName ('EMAIL').AsString+'"';

          (*
          if not liefquery.FieldByName ('TELEFON').IsNull  then
            body := body +   ',"phone":"'+liefquery.FieldByName ('TELEFON').AsString+'"';
          *)


          body := body + '}';

          body := body + ',"shipments": [' + nvebody + ']';

          body := body + '}';

          StrToFile (DatenPath + RESTDumpDir + FormatDateTime ('yyyymmdd', Now) + '\post_order_'+Query.FieldByName ('AUFTRAG_NR').AsString+'.json', body);

          sdata.Clear;
          if SendRequest(apiurl, // Host,
                          -1, //Port
                          'v1/order', // Service
                          'POST', //Methode
                          '', //Kein proxy
                          //'localhost:8888', // Fiddler als Proxy
                          '', '', // User , PW
                          '', //Action
                          'application/json', //ContentType
                          ['Authorization: Bearer '+apitoken,'Content-Type: application/json','accept: application/json'], //AddHeader
                          UTF8Encode (body),         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            try
              sdata.Position := 0;
              sdata.SaveToFile(DatenPath + RESTDumpDir + FormatDateTime ('yyyymmdd', Now)+'\resp_order_'+Query.FieldByName ('AUFTRAG_NR').AsString+'.json');
            except
              res := 0;
            end;

            sdata.Position := 0;

            fs := Nil;

            {$ifdef UNICODE}
              SetLength(utfstr, sdata.Size);
              sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
              datastr := StringUtils.StringToUTF (utfstr);

              js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
            {$else}
              sdata.Position := 0;
              js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
            {$endif}

            if (sdata.Size > 0) and not Assigned (js) then begin
              res := 26;
              ErrorText := 'Exporto API error message';
            end else if (Pos ('400', resp) > 0) or (Pos ('500', resp) > 0) then begin
              res := 27;
              ErrorText := 'Error API error message';

              fs := js.Field['message'];
              if Assigned (fs) then begin
                if (fs.Count = 0) then
                  ErrorText := ErrorText + #13+#10 + '  ' + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll])
                else begin
                  for idx := 0 to fs.Count - 1 do begin
                    if not (fs.Child [idx].Value = NULL) then
                      ErrorText := ErrorText + #13+#10 + '  ' + StringReplace (fs.Child [idx].Value, '\u0027', '''', [rfReplaceAll]);
                  end;
                end;
              end;
            end else begin
            end;
          end else begin
            res := 22;

            ErrorText := 'Error on Exporto server';

            if (Length (errtext) > 0) then
              ErrorText := ErrorText + #13 + errtext;
          end;
        end;

        liefquery.Close;
      end else begin
        nvequery.SQL.Clear;
        nvequery.SQL.Add ('select * from V_NVE_01 nve where nve.STATUS<>''DEL'' and nve.SENDUNGS_NR is not null and nve.REF_AUF_KOPF=:ref_auf and nve.REF_SPED=:ref_sped');
        nvequery.Params.ParamByName('ref_auf').Value  := RefAuf;
        nvequery.Params.ParamByName('ref_sped').Value := RefSped;

        nvequery.Open;

        while not nvequery.Eof do begin
          selquery.SQL.Clear;
          selquery.SQL.Add('select ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN'') as ARTIKEL_TEXT,bes.MENGE,vpe.KURZ_BEZEICHNUNG,ar.COUNTRY_OF_ORIGIN'
                          +',nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT),nvl (ae.EAN,ae.BARCODE) as BARCODE'
                          +',coalesce (rep.NETTO_BETRAG,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT,ae.PREIS * pos.MENGE_GESAMT) as NETTO_BETRAG'
                          +',coalesce (rep.BRUTTO_BETRAG,(re.BRUTTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT,ae.PREIS * pos.MENGE_GESAMT) as BRUTTO_BETRAG'
                          +',nvl (arset.ANZAHL_VPE, 1) as ANZAHL_VPE'
                          +',pos.REF_AR as POS_REF_AR, ae.REF_AR'
                          +' from'
                          +'   V_LAGER_NVE_BESTAND bes'
                          +'   inner join V_AUFTRAG_POS pos on (pos.REF=bes.REF_AUF_POS)'
                          +'   inner join VQ_AUFTRAG auf on (auf.REF=pos.REF_AUF_KOPF)'
                          +'   inner join V_AUFTRAG_RECHNUNG re on (re.REF_AUF_KOPF=auf.REF)'
                          +'   inner join V_AUFTRAG_POS_RECHNUNG rep on (rep.REF_AUF_POS=pos.REF)'
                          +'   inner join V_ARTIKEL posar on (posar.REF=pos.REF_AR)'
                          +'   inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=bes.REF_AR_EINHEIT)'
                          +'   inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'
                          +'   inner join V_ARTIKEL_VPE vpe on (vpe.REF=ae.REF_EINHEIT)'
                          +'   left outer join V_ARTIKEL_SET arset on (arset.REF=posar.REF_ARTIKEL_SET)'
                          +' where'
                          +'   nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0'
                          +'   and bes.REF_NVE=:ref'
                          );
          if nvequery.FieldByName('REF_MAIN_NVE').IsNull then
            selquery.Params [0].Value := nvequery.FieldByName('REF').AsInteger
          else
            selquery.Params [0].Value := nvequery.FieldByName('REF_MAIN_NVE').AsInteger;

          selquery.Open;

          subbody := '';

          while not selquery.Eof do begin
            if (Length (subbody) > 0) then subbody := subbody +',';

            subbody := subbody + '{';
            subbody := subbody + '"articleId":"'+selquery.FieldByName ('ARTIKEL_NR').AsString+'"';
            subbody := subbody + ',"name":"'+selquery.FieldByName ('ARTIKEL_TEXT').AsString+'"';
            subbody := subbody + ',"quantity":'+selquery.FieldByName ('MENGE').AsString;

            if (selquery.FieldByName ('BRUTTO_BETRAG').IsNull or (round (selquery.FieldByName ('BRUTTO_BETRAG').AsInteger / 1000) < 1)) then
              subbody := subbody + ',"priceGross":'+IntToStr (1)
            else
              subbody := subbody + ',"priceGross":'+IntToStr (round (selquery.FieldByName ('BRUTTO_BETRAG').AsInteger / 1000));

            if (selquery.FieldByName ('NETTO_BETRAG').IsNull or (round (selquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000) < 1)) then
              subbody := subbody + ',"priceNet":'+IntToStr (1)
            else
              subbody := subbody + ',"priceNet":'+IntToStr (round (selquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000));

            if not (selquery.FieldByName ('BARCODE').IsNull) then
              subbody := subbody + ',"barcode":"'+selquery.FieldByName ('BARCODE').AsString+'"';

            subbody := subbody + '}';

            selquery.Next;
          end;

          selquery.Close;

          //Wenn auf der NVE überhaupt was darauf ist
          if (Length (subbody) > 0) then begin
            sdata.Clear;
            if SendRequest(apiurl, // Host,
                            -1, //Port
                            'v1/shipment/'+nvequery.FieldByName ('NVE_NR').AsString, // Service
                            'DELETE', //Methode
                            '', //Kein proxy
                            //'localhost:8888', // Fiddler als Proxy
                            '', '', // User , PW
                            '', //Action
                            'application/json', //ContentType
                            ['Authorization: Bearer '+apitoken,'Content-Type: application/json','accept: application/json'], //AddHeader
                            UTF8Encode (body),         // RequestData
                            resp,
                            sdata, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
                          then
            begin
              try
                sdata.Position := 0;
                sdata.SaveToFile(DatenPath + RESTDumpDir + FormatDateTime ('yyyymmdd', Now)+'\resp_delete_shipment_'+nvequery.FieldByName ('NVE_NR').AsString+'.json');
              except
                res := 0;
              end;

              sdata.Position := 0;

              fs := Nil;

              {$ifdef UNICODE}
                SetLength(utfstr, sdata.Size);
                sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                datastr := StringUtils.StringToUTF (utfstr);

                js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
              {$else}
                sdata.Position := 0;
                js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
              {$endif}

              if (sdata.Size > 0) and not Assigned (js) then begin
                res := 26;
                ErrorText := 'Exporto API error message';
              end else if (Pos ('400', resp) > 0) or (Pos ('500', resp) > 0) then begin
                //res := 27;
                ErrorText := 'Error API error message';

                fs := js.Field['message'];
                if Assigned (fs) then begin
                  if (fs.Count = 0) then
                    ErrorText := ErrorText + #13+#10 + '  ' + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll])
                  else begin
                    for idx := 0 to fs.Count - 1 do begin
                      if not (fs.Child [idx].Value = NULL) then
                        ErrorText := ErrorText + #13+#10 + '  ' + StringReplace (fs.Child [idx].Value, '\u0027', '''', [rfReplaceAll]);
                    end;
                  end;
                end;
              end else begin
              end;
            end else begin
              res := 22;

              ErrorText := 'Error on Exporto server';

              if (Length (errtext) > 0) then
                ErrorText := ErrorText + #13 + errtext;
            end;

            nvebody := '{';
            nvebody := nvebody +   '"shipmentId":"'+nvequery.FieldByName ('NVE_NR').AsString+'"';

            if (nvequery.FieldByName ('VERSENDER').AsString = 'PCH') then
              nvebody := nvebody +   ',"foreignOutboundTrackingId":"'+nvequery.FieldByName ('SENDUNGS_NR').AsString+'"'
            else
              nvebody := nvebody +   ',"domesticTrackingId":"'+nvequery.FieldByName ('SENDUNGS_NR').AsString+'"';

            nvebody := nvebody +   ',"weight":'+IntToStr (trunc (nvequery.FieldByName ('BRUTTO_GEWICHT').AsFloat * 1000));

            nvebody := nvebody +   ',"lineItems": ['+ subbody + ']';
            nvebody := nvebody + '}';

            body := nvebody;

            StrToFile (DatenPath + RESTDumpDir + FormatDateTime ('yyyymmdd', Now) + '\post_order_shipment_'+nvequery.FieldByName ('NVE_NR').AsString+'.json', body);

            sdata.Clear;
            if SendRequest(apiurl, // Host,
                            -1, //Port
                            'v1/order/'+orderid+'/shipment', // Service
                            'POST', //Methode
                            '', //Kein proxy
                            //'localhost:8888', // Fiddler als Proxy
                            '', '', // User , PW
                            '', //Action
                            'application/json', //ContentType
                            ['Authorization: Bearer '+apitoken,'Content-Type: application/json','accept: application/json'], //AddHeader
                            UTF8Encode (body),         // RequestData
                            resp,
                            sdata, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
                          then
            begin
              try
                sdata.Position := 0;
                sdata.SaveToFile(DatenPath + RESTDumpDir + FormatDateTime ('yyyymmdd', Now)+'\resp_order_shipment_'+nvequery.FieldByName ('NVE_NR').AsString+'.json');
              except
                res := 0;
              end;

              sdata.Position := 0;

              fs := Nil;

              {$ifdef UNICODE}
                SetLength(utfstr, sdata.Size);
                sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                datastr := StringUtils.StringToUTF (utfstr);

                js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
              {$else}
                sdata.Position := 0;
                js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
              {$endif}

              if (sdata.Size > 0) and not Assigned (js) then begin
                res := 26;
                ErrorText := 'Exporto API error message';
              end else if (Pos ('400', resp) > 0) or (Pos ('500', resp) > 0) then begin
                //res := 27;
                ErrorText := 'Error API error message';

                fs := js.Field['message'];
                if Assigned (fs) then begin
                  if (fs.Count = 0) then
                    ErrorText := ErrorText + #13+#10 + '  ' + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll])
                  else begin
                    for idx := 0 to fs.Count - 1 do begin
                      if not (fs.Child [idx].Value = NULL) then
                        ErrorText := ErrorText + #13+#10 + '  ' + StringReplace (fs.Child [idx].Value, '\u0027', '''', [rfReplaceAll]);
                    end;
                  end;
                end;
              end else begin
              end;
            end else begin
              res := 22;

              ErrorText := 'Error on Exporto server';

              if (Length (errtext) > 0) then
                ErrorText := ErrorText + #13 + errtext;
            end;
          end;

          nvequery.Next;
        end;

        nvequery.Close;
      end;
    end;
  finally
    sdata.Free;
    liefquery.Free;
    selquery.Free;
  end;

  Result := res;
end;

var
  res       : Integer;
  bpreis,
  npreis    : Double;
  sqlstr    : String;
  selquery  : TSmartQuery;
begin
  ForceDirectories(DatenPath + RESTDumpDir + FormatDateTime ('yyyymmdd', Now));

  query := TSmartQuery.Create (Nil);
  selquery := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;
    selquery.ReadOnly := True;
    selquery.Session := LVSDatenModul.OraMainSession;

    query.SQL.Add ('select a.REF as REF_AUF_KOPF, sped.REF as REF_SPED, av.SPEDITION_NR, sped.DFUE_ART, a.REF_MAND, a.REF_SUB_MAND, a.MANDANT, nvl (a.REF_SUB_MAND,a.REF_MAND) as REF_DOCU_MAND'
                  +',a.REF_LAGER, loc.REF_LOCATION, nvl (adr.LAND_ISO, ''DE'') as LIEFER_LAND_ISO'
                  +',a.REF_LIEFLAGER, a.AUFTRAG_NR, a.AUF_REFERENZ, a.KD_KOMM_NR,a.LIEFERSCHEIN_NR,a.REF_KUNDEN_ADR,a.DRUCKART'
                  +',a.REF_LIEFER_ADR,sped.LABEL_ART, av.OPT_NACHNAHME, lif.AUSLIEFER_NR'
                  +',nvl (versplan.OPT_RETOUREN_SCHEIN, aufplan.OPT_RETOUREN_SCHEIN) as OPT_RETOUREN_SCHEIN, ret.RETOUREN_NR,cfg.CSV_EXPORT_PATH,m.CONFIG_OPT as MAND_CONFIG_OPT,av.OPT_RETOUREN_LABEL'
                  +',ret.SENDUNGS_NR as RET_SENDUNGS_NR,re.RECHNUNGS_NR,a.VERSAND_DATUM,av.VERSAND_ART,sped.IFC_KENNZEICHEN,a.KUNDEN_NR,aufplan.OPT_PRINT_NVE,cfg.OPT_CHECK_FIRST_IMAGE'
                  +',sped.NAME as SPED_NAME,sped.DFUE_KENNZEICHEN,gate.SENDIT_RETURN_CLIENT,cfg.OPT_DIM_REQUIRED,cfg.OPT_SPERRGUT_AS_NORMAL,cfg.OPT_INLAND_RETURN,cfg.OPT_AUSLAND_RETURN'
                  +',gate.SENDIT_LOCATION,gate.SENDIT_PRODUKT as GATEWAY_SENDIT_PRODUKT,gate.SENDIT_PRODUKT_AUSLAND as GATEWAY_SENDIT_PRODUKT_AUS,gate.SENDIT_RETURN_LOCATION,gate.SENDIT_CLIENT,gate.SENDIT_CONFIG,cfg.OPT_MULTI_SHIPMEND,aq.KD_BESTELL_NR'
                  +',re.NETTO_BETRAG,re.BRUTTO_BETRAG,m.ERP_ID as MANDANT_ERP_ID,re.CURRENCY,auftxt.VERSAND_HINWEIS,aufinfo.IFC_AUFTRAG_NR,a.AUFTRAGSART,a.LIEFER_DATUM'
                  +',(select NAME from V_MANDANT where REF=a.REF_SUB_MAND) as SUB_MANDANT,gate.REF as REF_SPED_GATEWAY,gate.ZOLL_DEKLARATION,gate.NOTIFICATION_BY_MAIL,gate.DEFAULT_EMAIL_ADRESS,gate.DEFAULT_PHONE_NUMBER'
                  +' from'
                  +' V_AUFTRAG_01 a'
                  +' left outer join VQ_AUFTRAG aq on (aq.REF=a.REF)'
                  +' left outer join V_AUFTRAG_TEXTE auftxt on (auftxt.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_MANDANT m on (m.REF=coalesce (a.REF_SUB_MAND, a.REF_MAND))'
                  +' left outer join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR)'
                  +' left outer join V_LIEFERUNG_01 lif on (lif.REF=a.REF_LIEFERUNG)'
                  +' left outer join V_AUFTRAG_RECHNUNG re on (re.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_SPEDITIONEN sped on (sped.REF=:ref_sped)'
                  +' left outer join V_SPED_CONFIG cfg on (cfg.REF_SPED=:ref_sped)'
                  +' left outer join V_SPED_GATEWAY gate on ((gate.REF_SUB_MAND is null or (gate.REF_SUB_MAND=a.REF_SUB_MAND)) and gate.REF_LAGER=a.REF_LAGER and gate.REF_SPED=sped.REF'
                  +' and (gate.LAND_ISO is null or (instr (gate.LAND_ISO, adr.LAND_ISO) > 0)) and (gate.REF_TRADER is null or gate.REF_TRADER=a.REF_TRADER))'
                  //Das Produkt muss auch zum gewählten Versendet passen
                  +' left outer join V_RETOUREN_NR ret on (ret.REF_AUF_KOPF=a.REF and ret.STATUS<>''DEL'')'
                  +' left outer join V_AUFTRAG_ART_PLANUNG aufplan on (aufplan.REF=a.REF_ART_PLANUNG)'
                  +' left outer join V_AUFTRAG_ART_VERSAND_CONFIG versplan on (versplan.REF=a.REF_AUF_VERS_CFG)'
                  +' left outer join V_AUFTRAG_ADD_INFOS aufinfo on (aufinfo.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_LOCATION_REL_LAGER loc on (loc.REF_LAGER=a.REF_LAGER)'
                  +' where'
                  +' a.REF=:ref_auf'
                  +' order by gate.LAND_ISO nulls last,case when (instr (gate.LAND_ISO, adr.LAND_ISO) > 0) then 1 else 9 end asc, gate.REF_SUB_MAND nulls last,gate.REF_TRADER nulls last'
                  );
    query.Params.ParamByName('ref_auf').Value  := RefAuf;
    query.Params.ParamByName('ref_sped').Value := RefSped;

    res := 0;

    try
      query.Open;

      if (query.RecordCount > 0)  then begin
        if (APISped <> RefGate) then
          APIToken := '';

        res := Login (RefGate, DatenPath, apiurl, APIToken, ErrorText);

        if (res = 0) then begin
          APISped      := RefGate;
        end;

        if (res = 0) then begin
          selquery := TSmartQuery.Create (Nil);

          try
            selquery.ReadOnly := True;
            selquery.Session := LVSDatenModul.OraMainSession;

            selquery.SQL.Add ('select'
                             +'  distinct bes.REF_AR, bes.REF_AR_EINHEIT'
                             +' from'
                             +'  VQ_LAGER_NVE nve'
                             +'  inner join V_LAGER_NVE_BESTAND bes on (bes.REF_NVE=nve.REF)'
                             +' where'
                             +'  nve.REF_AUF_KOPF=:ref_auf and'
                             +'  nve.REF_SPED=:ref_sped and'
                             +'  bes.REF_AR not in (select REF_AR from V_ARTIKEL_EXPORT where RECEIVER=''EXPORTO'')'
                             );
            selquery.Params.ParamByName('ref_auf').Value  := RefAuf;
            selquery.Params.ParamByName('ref_sped').Value := RefSped;

            selquery.Open;

            while (not selquery.Eof and (res = 0)) do begin
              res := SendExportArtikelstamm (selquery.FieldByName('REF_AR_EINHEIT').AsInteger, DatenPath, apiurl, apitoken, ErrorText);

              if (res = 0) then
                SetArtikelExport (selquery.FieldByName('REF_AR').AsInteger, 'EXPORTO', '');

              selquery.Next;
            end;

            selquery.Close;

            ErrorText := '';

            if (res = 0) then begin
              selquery.SQL.Clear;
              selquery.SQL.Add ('select nvl (NETTO_BETRAG_CALC, NETTO_BETRAG), nvl (BRUTTO_BETRAG_CALC, BRUTTO_BETRAG)'
                               //Nettopreis über die Rechnungs-Positionen bestimmen
                               +' ,(select sum (rp.NETTO_BETRAG) from V_AUFTRAG_POS_RECHNUNG rp, VQ_AUFTRAG_POS ap where rp.REF_AUF_POS=ap.REF and ap.REF_AUF_KOPF=:RefAuf)'
                               //Bruttopreis über die Rechnungs-Positionen bestimmen
                               +' ,(select sum (rp.BRUTTO_BETRAG) from V_AUFTRAG_POS_RECHNUNG rp, VQ_AUFTRAG_POS ap where rp.REF_AUF_POS=ap.REF and ap.REF_AUF_KOPF=:RefAuf)'
                               //Preis über den Artikel bestimmen
                               + ',(select sum (ae.PREIS*ap.MENGE_GESAMT) from VQ_AUFTRAG_POS ap inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=ap.REF_AR_EINHEIT) where ap.REF_AUF_KOPF=:RefAuf)'
                               +'  from V_AUFTRAG_RECHNUNG where REF_AUF_KOPF=:RefAuf');
              selquery.Params.ParamByName('RefAuf').Value := Query.FieldByName ('REF_AUF_KOPF').AsInteger;

              selquery.Open;

              if not (selquery.Fields [0].IsNull) and (selquery.Fields [0].AsInteger > 0) then
                npreis := selquery.Fields [0].AsInteger / 1000.0  //Gesamt netto
              else if not (selquery.Fields [2].IsNull) and (selquery.Fields [2].AsInteger > 0) then
                npreis := selquery.Fields [2].AsInteger / 1000.0  //Summe Rechnungs-Position netto
              else if not (selquery.Fields [4].IsNull) then
                npreis := selquery.Fields [4].AsInteger / 1000.0; //Summe über Artikelpreis

              if not (selquery.Fields [1].IsNull) and (selquery.Fields [1].AsInteger > 0) then
                bpreis := selquery.Fields [1].AsInteger / 1000.0  //Gesamt brutto
              else if not (selquery.Fields [3].IsNull) and (selquery.Fields [3].AsInteger > 0) then
                bpreis := selquery.Fields [3].AsInteger / 1000.0  //Summe Rechnungs-Position brutto
              else if not (selquery.Fields [4].IsNull) then
                bpreis := selquery.Fields [4].AsInteger / 1000.0; //Summe über Artikelpreis

              selquery.Close;

              (* 2023-12-13 VLH : Soll nicht mehr geprüft werden*)
              (* 2024-03-20 VLH : Preis über den Artikelpreis bestimmen, Fehler melden, wenn das auch nicht geht*)
              if (bpreis <= 0) then begin
                //res       := 57;

                {$ifdef ResourceText}
                  ErrorText := ErrorText + FormatMessageText (1880, []);
                {$else}
                  ErrorText := ErrorText + 'Export nicht möglich, fehlende Preise';
                {$endif}
              end else begin
                res := SendExportPakete (npreis, bpreis, ErrorText);

                if (res = 0) then begin
                  selquery.SQL.Clear;
                  selquery.SQL.Add ('select * from VQ_LAGER_NVE nve where nve.STATUS<>''DEL'' and nve.SENDUNGS_NR is not null and nve.REF_AUF_KOPF=:ref_auf and nve.REF_SPED=:ref_sped');
                  selquery.Params.ParamByName('ref_auf').Value  := RefAuf;
                  selquery.Params.ParamByName('ref_sped').Value := RefSped;

                  selquery.Open;

                  while (not selquery.Eof and (res = 0)) do begin
                    res := SetNVESendungExport (selquery.FieldByName('REF').AsInteger, FormatDateTime ('yyyymmdd', Now), 'EXPORTO');

                    selquery.Next;
                  end;

                  selquery.Close;
                end;
              end;
            end;
          finally
            selquery.Free;
          end;
        end;
      end;

      query.Close;
    except
      on  E: Exception do begin
        res := -9;

        {$ifdef ErrorTracking}
          ErrorTrackingModule.WriteErrorLog ('Exception SendExportPakete', e.ClassName + ' : ' + e.Message+#13+#10+query.SQL.Text);
        {$endif}


        ErrorText := 'Fehler beim Lesen der Auftragsdaten';
      end;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

function CreateExportoNVETour (const RefGate, RefSped : Integer; const DatenPath : String; var ErrorText : String) : Integer;
var
  res,
  idx,
  errcode   : Integer;
  js        : TlkJSONobject;
  fs        : TlkJSONbase;

  resp,
  body,
  errtext,
  jsonstr   : String;

  {$ifdef UNICODE}
    utfstr       : AnsiString;
    datastr      : String;
  {$endif}

  query   : TSmartQuery;
  sdata   : TMemoryStream;
  nvelist : TList;
begin
  ForceDirectories(DatenPath + RESTDumpDir + FormatDateTime ('yyyymmdd', Now));

  nvelist := TList.Create;

  query := LVSDatenModul.CreateSmartQuery (Nil, 'CreateExportoNVETour');

  try
    query.SQL.Clear;
    query.SQL.Add ('select * from VQ_LAGER_NVE where STATUS in (''WA'',''ABG'') and CUSTOM_DATE is null and sendungs_nr is not null and REF_SPED=:ref_sped');
    query.Params.ParamByName('ref_sped').Value := RefSped;

    query.Open;

    jsonstr := '';

    while not (query.Eof) do begin
      if (Length (jsonstr) > 0) then jsonstr := jsonstr + ',';

      jsonstr := jsonstr + '"'+ query.FieldByName ('NVE_NR').AsString +'"';

      nvelist.Add (Pointer (query.FieldByName ('REF').AsInteger));

      query.Next;
    end;

    query.Close;

    if (Length (jsonstr) > 0) then begin
      res := 0;

      if (APISped <> RefGate) then
        APIToken := '';

      res := Login (RefGate, DatenPath, apiurl, APIToken, ErrorText);

      if (res = 0) then begin
        APISped      := RefGate;
      end;

      if (res = 0) then begin
        body := '{';

        body := body + '"shipmentIds": [' + jsonstr + ']';
        body := body + ',"type": "outbound"';

        body := body + '}';

        StrToFile (DatenPath + RESTDumpDir + FormatDateTime ('yyyymmdd', Now) + '\post_tour_'+FormatDateTime ('yyyymmdd_hhnn', Now)+'.json', body);

        sdata := TMemoryStream.Create;

        try
          sdata.Clear;
          if SendRequest(apiurl, // Host,
                          -1, //Port
                          'v1/tour', // Service
                          'POST', //Methode
                          '', //Kein proxy
                          //'localhost:8888', // Fiddler als Proxy
                          '', '', // User , PW
                          '', //Action
                          'application/json', //ContentType
                          ['Authorization: Bearer '+apitoken,'Content-Type: application/json','accept: application/json'], //AddHeader
                          UTF8Encode (body),         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            try
              sdata.Position := 0;
              sdata.SaveToFile(DatenPath + RESTDumpDir + FormatDateTime ('yyyymmdd', Now)+'\resp_tour_'+FormatDateTime ('yyyymmdd_hhnn', Now)+'.json');
            except
              res := 0;
            end;

            sdata.Position := 0;

            fs := Nil;

            {$ifdef UNICODE}
              SetLength(utfstr, sdata.Size);
              sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
              datastr := StringUtils.StringToUTF (utfstr);

              js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
            {$else}
              sdata.Position := 0;
              js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
            {$endif}

            if (sdata.Size > 0) and not Assigned (js) then begin
              res := 26;
              ErrorText := 'Exporto API error message';
            end else if (Pos ('400', resp) > 0) or (Pos ('500', resp) > 0) then begin
              res := 27;
              ErrorText := 'Error API error message';

              fs := js.Field['message'];
              if Assigned (fs) then begin
                for idx := 0 to fs.Count - 1 do begin
                  if not (fs.Child [idx].Value = NULL) then
                    ErrorText := ErrorText + #13+#10 + '  ' + StringReplace (fs.Child [idx].Value, '\u0027', '''', [rfReplaceAll]);
                end;
              end;
            end else begin
              idx := 0;

              while (idx < nvelist.Count) do begin
                res := SetNVESendungCustomDate (Integer (nvelist [idx]));

                Inc (idx);
              end;
            end;
          end else begin
            res := 22;

            ErrorText := 'Error on Exporto server';

            if (Length (errtext) > 0) then
              ErrorText := ErrorText + #13 + errtext;
          end;
        finally
          sdata.Free;
        end;
      end;
    end;
  finally
    nvelist.Free;
    query.Free;
  end;
end;

function CreateExportoTour (const RefVerl : Integer; const DatenPath : String; var ErrorText : String) : Integer;
begin
end;

initialization
  APISped      := -1;
  APIToken     := '';
  APITokenUser := '';
  APITokenTime := 0;

end.
