object LTVereinnahmungForm: TLTVereinnahmungForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'LTVereinnahmungForm'
  ClientHeight = 479
  ClientWidth = 856
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    856
    479)
  PixelsPerInch = 96
  TextHeight = 13
  object Bevel1: TBevel
    Left = 0
    Top = 44
    Width = 923
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label2: TLabel
    Left = 8
    Top = 12
    Width = 44
    Height = 13
    Caption = 'Lieferant:'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = []
    ParentFont = False
  end
  object Label3: TLabel
    Left = 714
    Top = 12
    Width = 60
    Height = 13
    Anchors = [akTop, akRight]
    Caption = 'Lieferschein:'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = []
    ParentFont = False
  end
  object StaticText3: TLabel
    Left = 780
    Top = 12
    Width = 66
    Height = 13
    Caption = 'StaticText3'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label1: TLabel
    Left = 8
    Top = 129
    Width = 81
    Height = 13
    Caption = 'Neu erfasste LTs'
  end
  object FehlerLabel: TPanel
    Left = 1
    Top = 51
    Width = 854
    Height = 24
    Anchors = [akLeft, akTop, akRight]
    BevelOuter = bvNone
    Caption = 'FehlerLabel'
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 0
  end
  object StaticText1: TStaticText
    Left = 73
    Top = 12
    Width = 69
    Height = 17
    BevelInner = bvLowered
    BevelOuter = bvRaised
    Caption = 'StaticText1'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
    TabOrder = 1
  end
  object StatusBar1: TStatusBar
    Left = 0
    Top = 460
    Width = 856
    Height = 19
    Panels = <
      item
        Width = 200
      end
      item
        Width = 200
      end
      item
        Width = 250
      end
      item
        Width = 50
      end>
  end
  object CloseButton: TButton
    Left = 773
    Top = 427
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 3
  end
  object LTStringGrid: TStringGridPro
    Left = 8
    Top = 81
    Width = 840
    Height = 340
    Anchors = [akLeft, akTop, akRight, akBottom]
    ColCount = 6
    DefaultColWidth = 20
    DefaultRowHeight = 21
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
    TabOrder = 4
    TitelTexte.Strings = (
      ''
      'LE-Nr.'
      'NVE-Nr.'
      'Nettogewicht'
      'Bruttogewicht'
      'Erfasst um')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'Tahoma'
    TitelFont.Style = []
    ColWidths = (
      20
      176
      157
      113
      130
      214)
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 340
    Top = 492
  end
end
