unit TourInfoFrame;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, IntegerUpDown, ExtCtrls;

type
  TTourInfo = class(TFrame)
    Label15: TLabel;
    SpedComboBox: TComboBoxPro;
    KommPlanGroupBox: TGroupBox;
    Label8: TLabel;
    Label9: TLabel;
    KommHourEdit: TEdit;
    KommHourUpDown: TIntegerUpDown;
    KommMinUpDown: TIntegerUpDown;
    KommMinEdit: TEdit;
    GroupBox2: TGroupBox;
    GroupBox3: TGroupBox;
    Label10: TLabel;
    Label11: TLabel;
    AbgHourEdit: TEdit;
    AbgHourUpDown: TIntegerUpDown;
    AbgMinEdit: TEdit;
    AbgMinUpDown: TIntegerUpDown;
    ForecastHourEdit: TEdit;
    ForecastHourUpDown: TIntegerUpDown;
    Label1: TLabel;
    ForecastMinEdit: TEdit;
    ForecastMinUpDown: TIntegerUpDown;
    Label2: TLabel;
    DistributionCheckBox: TCheckBox;
    AutoKommCheckBox: TCheckBox;
    AbgTimeCheckBox: TCheckBox;
    ForecastTimeCheckBox: TCheckBox;
    FahrerEdit: TEdit;
    FahrzeugEdit: TEdit;
    Label3: TLabel;
    Label4: TLabel;
    Bevel1: TBevel;
    Bevel2: TBevel;
    Label5: TLabel;
    KFZTypComboBox: TComboBox;
    procedure AutoKommCheckBoxClick(Sender: TObject);
    procedure AbgTimeCheckBoxClick(Sender: TObject);
    procedure ForecastTimeCheckBoxClick(Sender: TObject);
    procedure HourEditKeyPress(Sender: TObject; var Key: Char);
    procedure MinEditKeyPress(Sender: TObject; var Key: Char);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;

    procedure ShowFrame;
    procedure Clear;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TTourInfo.Create(AOwner: TComponent);
begin
  inherited Create (AOwner);

  Clear;
end;
//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
destructor TTourInfo.Destroy;
begin
  ClearComboBoxObjects (SpedComboBox);

  inherited Destroy;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTourInfo.AutoKommCheckBoxClick(Sender: TObject);
begin
  KommHourEdit.Enabled   := AutoKommCheckBox.Checked;
  KommMinEdit.Enabled    := AutoKommCheckBox.Checked;
  KommHourUpDown.Enabled := AutoKommCheckBox.Checked;
  KommMinUpDown.Enabled  := AutoKommCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTourInfo.AbgTimeCheckBoxClick(Sender: TObject);
begin
  AbgHourEdit.Enabled   := AbgTimeCheckBox.Checked;
  AbgMinEdit.Enabled    := AbgTimeCheckBox.Checked;
  AbgHourUpDown.Enabled := AbgTimeCheckBox.Checked;
  AbgMinUpDown.Enabled  := AbgTimeCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTourInfo.ForecastTimeCheckBoxClick(Sender: TObject);
begin
  ForecastHourEdit.Enabled   := ForecastTimeCheckBox.Checked;
  ForecastMinEdit.Enabled    := ForecastTimeCheckBox.Checked;
  ForecastHourUpDown.Enabled := ForecastTimeCheckBox.Checked;
  ForecastMinUpDown.Enabled  := ForecastTimeCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTourInfo.ShowFrame;
begin
  AutoKommCheckBoxClick     (AutoKommCheckBox);
  AbgTimeCheckBoxClick      (AbgTimeCheckBox);
  ForecastTimeCheckBoxClick (ForecastTimeCheckBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTourInfo.Clear;
begin
  SpedComboBox.ItemIndex := 0;
  DistributionCheckBox.Checked := True;

  AutoKommCheckBox.Checked := false;
  AbgTimeCheckBox.Checked := false;
  ForecastTimeCheckBox.Checked := false;

  KommHourEdit.Text := '';
  KommMinEdit.Text := '';
  AbgHourEdit.Text := '';
  AbgMinEdit.Text := '';
  ForecastHourEdit.Text := '';
  ForecastMinEdit.Text := '';
  FahrerEdit.Text := '';
  FahrzeugEdit.Text := '';

  KFZTypComboBox.ItemIndex := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTourInfo.HourEditKeyPress(Sender: TObject; var Key: Char);
var
  intwert : Integer;
begin
  if (Key = #8) then
  else if not (Key in [#8,^C,^V,'0'..'9']) then
    Key := #0
  else if not TryStrToInt ((Sender as TEdit).Text + Key, intwert) then
    Key := #0
  else if (intwert > 23) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTourInfo.MinEditKeyPress(Sender: TObject; var Key: Char);
var
  intwert : Integer;
begin
  if (Key = #8) then
  else if not (Key in [#8,^C,^V,'0'..'9']) then
    Key := #0
  else if not TryStrToInt ((Sender as TEdit).Text + Key, intwert) then
    Key := #0
  else if (intwert > 59) then
    Key := #0;
end;

end.
