unit KommLPAZPDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls;

type
  TKommLPAZPForm = class(TForm)
    Label1: TLabel;
    Label2: TLabel;
    KommLPLabel: TLabel;
    ARNrLabel: TLabel;
    AZPEdit: TEdit;
    Label3: TLabel;
    AbortButton: TButton;
    OkButton: TButton;
    Bevel1: TBevel;
    Bevel2: TBevel;
    procedure AZPEditKeyPress(Sender: TObject; var Key: Char);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

procedure TKommLPAZPForm.AZPEditKeyPress(Sender: TObject; var Key: Char);
var
  wert : Double;
begin
  if (Key = #8) then
  else if not (Key in [',','0'..'9',^C,^V]) then
    Key := #0
  else if not (TryStrToFloat(AZPEdit.Text + Key, wert)) then
    Key := #0;
end;

end.
