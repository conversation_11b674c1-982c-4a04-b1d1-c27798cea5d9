object ShowBestandVorResForm: TShowBestandVorResForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu]
  Caption = 'Vorreservierungen'
  ClientHeight = 300
  ClientWidth = 635
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    635
    300)
  PixelsPerInch = 96
  TextHeight = 13
  object BestandVorResDBGrid: TDBGridPro
    Left = 8
    Top = 16
    Width = 619
    Height = 241
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = BestandVorResDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = BestandVorResDBGridPopupMenu
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 552
    Top = 267
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 1
  end
  object BestandVorResDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 464
    Top = 56
  end
  object BestandVorResDataSource: TDataSource
    DataSet = BestandVorResDataSet
    Left = 512
    Top = 56
  end
  object BestandVorResDBGridPopupMenu: TPopupMenu
    OnPopup = BestandVorResDBGridPopupMenuPopup
    Left = 352
    Top = 168
    object DelVorResMenuItem: TMenuItem
      Caption = 'Vorreservierung l'#246'schen...'
      OnClick = DelVorResMenuItemClick
    end
  end
end
