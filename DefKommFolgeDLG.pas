unit DefKommFolgeDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComboBoxPro;

type
  TDefKommFolgeForm = class(TForm)
    LBComboBox: TComboBoxPro;
    Label1: TLabel;
    Label2: TLabel;
    Bevel1: TBevel;
    Bevel4: TBevel;
    StepEdit: TEdit;
    Label9: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel6: TBevel;
    Label11: TLabel;
    LagerComboBox: TComboBoxPro;
    ReiheComboBox: TComboBox;
    StartEdit: TEdit;
    Label12: TLabel;
    EbeneGroupBox: TGroupBox;
    Label6: TLabel;
    EbeneBisEdit: TEdit;
    EbeneVonEdit: TEdit;
    Label5: TLabel;
    FachGroupBox: TGroupBox;
    Label3: TLabel;
    PlatzVonEdit: TEdit;
    Label4: TLabel;
    PlatzBisEdit: TEdit;
    SetLPNrCheckBox: TCheckBox;
    Bevel2: TBevel;
    GroupBox1: TGroupBox;
    Panel1: TPanel;
    AufRadioButton: TRadioButton;
    AbRadioButton: TRadioButton;
    Panel2: TPanel;
    DownUpRadioButton: TRadioButton;
    UpDownRadioButton: TRadioButton;
    Panel3: TPanel;
    ProFachRadioButton: TRadioButton;
    ProEbeneRadioButton: TRadioButton;
    Bevel3: TBevel;
    Bevel5: TBevel;
    TiefeGroupBox: TGroupBox;
    Label7: TLabel;
    Label8: TLabel;
    TiefeBisEdit: TEdit;
    TiefeVonEdit: TEdit;
    procedure LagerComboBoxChange(Sender: TObject);
    procedure LBComboBoxChange(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure NumEditKeyPress(Sender: TObject; var Key: Char);
    procedure NrEditExit(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure SetLPNrCheckBoxClick(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses VCLUtilitys, StringUtils, FrontendUtils, DB, ADODB, DatenModul, ResourceText,
  SprachModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDefKommFolgeForm.LagerComboBoxChange(Sender: TObject);
begin
  LoadLBComboboxNullEntry (LBComboBox, '', GetResourceText (rsComboboxAlle), GetComboBoxRef (LagerComboBox));

  if (Visible) then begin
    if (LBComboBox.Items.Count > 0) Then
      LBComboBox.ItemIndex := 0
    else LBComboBox.Text := '';

    LBComboBoxChange (LBComboBox);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDefKommFolgeForm.LBComboBoxChange(Sender: TObject);
var
  query  : TADOQuery;
begin
  query := TADOQuery.Create (Nil);
  query.Connection := LVSDatenModul.MainADOConnection;

  ReiheComboBox.Clear;

  query.SQL.Add ('select distinct (REIHE) from V_LP where REF_LB='+IntToStr (GetComboBoxRef(LBComboBox)) + ' order by LPAD (REIHE, 3, ''0'')');

  try
    query.Open;

    while not (query.Eof) do begin
      ReiheComboBox.Items.Add(query.Fields [0].AsString);

      query.Next;
    end;

    query.Close;
  except
  end;

  if (ReiheComboBox.Items.Count > 0) then
    ReiheComboBox.ItemIndex := 0
  else ReiheComboBox.Text := '';

  query.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDefKommFolgeForm.FormCreate(Sender: TObject);
begin
  PlatzVonEdit.Text := '';
  PlatzBisEdit.Text := '';
  EbeneVonEdit.Text := '';
  EbeneBisEdit.Text := '';
  TiefeVonEdit.Text := '';
  TiefeBisEdit.Text := '';
  StartEdit.Text := '';
  StepEdit.Text := '';

  LagerComboBox.ColWidths [0] := 100;
  LagerComboBox.ColWidths [1] := 160;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
    LVSSprachModul.SetNoTranslate (Self, LBComboBox);
    LVSSprachModul.SetNoTranslate (Self, ReiheComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDefKommFolgeForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (LagerComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDefKommFolgeForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := False;

    if (Length (PlatzVonEdit.Text) = 0) Then
      PlatzVonEdit.SetFocus
    else if (Length (PlatzBisEdit.Text) = 0) Then
      PlatzBisEdit.SetFocus
    else if (Length (EbeneVonEdit.Text) = 0) Then
      EbeneVonEdit.SetFocus
    else if (Length (EbeneBisEdit.Text) = 0) Then
      EbeneBisEdit.SetFocus
    else if TiefeGroupBox.Visible and (Length (TiefeVonEdit.Text) = 0) Then
      TiefeVonEdit.SetFocus
    else if TiefeGroupBox.Visible and (Length (TiefeBisEdit.Text) = 0) Then
      TiefeBisEdit.SetFocus
    else if (Length (StartEdit.Text) = 0) Then
      StartEdit.SetFocus
    else if (Length (StepEdit.Text) = 0) Then
      StepEdit.SetFocus
    else CanClose := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDefKommFolgeForm.NumEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in ['0'..'9', #8,^C,^V]) then
    Key := #0;
end;

procedure TDefKommFolgeForm.SetLPNrCheckBoxClick(Sender: TObject);
begin

end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDefKommFolgeForm.NrEditExit(Sender: TObject);
var
  nr : Integer;
begin
  if not (AbortButton.Focused) then begin
    if (Length ((Sender as TEdit).Text) > 0) then begin
      if not (TryStrToInt ((Sender as TEdit).Text, nr)) then
        (Sender as TEdit).SetFocus;
    end;
  end;
end;

end.
