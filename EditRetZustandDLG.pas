﻿unit EditRetZustandDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, Vcl.ExtCtrls;

type
  TEditRetZustandForm = class(TForm)
    MandComboBox: TComboBoxPro;
    Label1: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    Label5: TLabel;
    SelEdit: TEdit;
    Label6: TLabel;
    DefEdit: TEdit;
    LinkEdit: TEdit;
    Label7: TLabel;
    RefEdit: TEdit;
    Label8: TLabel;
    Label3: TLabel;
    NrEdit: TEdit;
    AdminCheckBox: TCheckBox;
    GroupBox1: TGroupBox;
    LangComboBox: TComboBoxPro;
    Label4: TLabel;
    TextEdit: TEdit;
    Label2: TLabel;
    AddInfosCheckBox: TCheckBox;
    SperrBesCheckBox: TCheckBox;
    Label9: TLabel;
    VariableEdit: TEdit;
    ReprintLabelCheckBox: TCheckBox;
    FinalStatCheckBox: TCheckBox;
    BeiAnnahmeCheckBox: TCheckBox;
    DefaultCheckBox: TCheckBox;
    AddInfosErfassenCheckBox: TCheckBox;
    AddInfosMandatoryCheckBox: TCheckBox;
    IFCEdit: TEdit;
    Label10: TLabel;
    Bevel1: TBevel;
    Bevel2: TBevel;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure NumberEditKeyPress(Sender: TObject; var Key: Char);
    procedure LangComboBoxChange(Sender: TObject);
    procedure TextEditExit(Sender: TObject);
    procedure VariableEditExit(Sender: TObject);
  private
    fRefZustand : Integer;
    fTextArea   : String;
    fTextArray  : array [0..16] of String;
    fVarArray   : array [0..16] of String;
  public
    property RefZustand : Integer read fRefZustand write fRefZustand;
    property TextArea   : String  read fTextArea   write fTextArea;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DB, ADODB, BetterADODataSet, FrontendUtils, DatenModul, LVSSystemInterface,
  SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditRetZustandForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  i,
  nr,
  sel,
  lnknr,
  refnr,
  res : Integer;
  opt : String;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    TextEditExit     (Sender);
    VariableEditExit (Sender);

    CanClose := False;

    if (Length (SelEdit.Text) = 0) then begin
      sel := -1;
      CanClose := True;
    end else if (TryStrToInt (SelEdit.Text, sel)) then
      CanClose := True
    else
      SelEdit.SetFocus;

    if CanClose then begin
      if (Length (NrEdit.Text) = 0) then begin
        nr := -1;
        CanClose := True;
      end else if (TryStrToInt (NrEdit.Text, nr)) then
        CanClose := True
      else
        NrEdit.SetFocus;
    end;

    if CanClose then begin
      if (Length (LinkEdit.Text) = 0) then begin
        lnknr := -1;
        CanClose := True;
      end else if (TryStrToInt (LinkEdit.Text, lnknr)) then
        CanClose := True
      else
        LinkEdit.SetFocus;
    end;

    if CanClose then begin
      if (Length (RefEdit.Text) = 0) then begin
        refnr := -1;
        CanClose := True;
      end else if (TryStrToInt (RefEdit.Text, refnr)) then
        CanClose := True
      else
        RefEdit.SetFocus;
    end;

    if CanClose then begin
      opt := '00000000';
      if (AdminCheckBox.Checked) then
        opt [1] := '1';
        
      if (AddInfosCheckBox.Checked) then begin
        if (AddInfosMandatoryCheckBox.Checked) then
          opt [2] := '3'
        else if (AddInfosErfassenCheckBox.Checked) then
          opt [2] := '2'
        else
          opt [2] := '1';
      end;

      if (SperrBesCheckBox.Checked) then
        opt [3] := '1';
      if (ReprintLabelCheckBox.Enabled and ReprintLabelCheckBox.Checked) then
        opt [4] := '1';
      if (FinalStatCheckBox.Enabled and FinalStatCheckBox.Checked) then
        opt [5] := '1';
      if (BeiAnnahmeCheckBox.Enabled and BeiAnnahmeCheckBox.Checked) then
        opt [6] := '1';
      if (DefaultCheckBox.Enabled and DefaultCheckBox.Checked) then
        opt [7] := '1';

      if (fRefZustand = -1) then begin
        if IFCEdit.Visible then
          res := InsertRetZustand (GetComboBoxRef(MandComboBox), fTextArea, nr, opt, DefEdit.Text, IFCEdit.Text, sel, lnknr, refnr, fRefZustand)
        else res := InsertRetZustand (GetComboBoxRef(MandComboBox), fTextArea, nr, opt, DefEdit.Text, sel, lnknr, refnr, fRefZustand);

        if (res <> 0) then
          MessageDLG('Fehler beim Anlegen eines neuen Zustandes' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
      end else begin
        if IFCEdit.Visible then
          res := ChangeRetZustand (fRefZustand, nr, opt, DefEdit.Text, IFCEdit.Text, sel, lnknr, refnr)
        else res := ChangeRetZustand (fRefZustand, nr, opt, DefEdit.Text, sel, lnknr, refnr);

        if (res <> 0) then
          MessageDLG('Fehler beim Ändern eines Zustandes' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
      end;

      if (res = 0) then begin
        for i:= 0 to (LangComboBox.Items.Count - 1) do begin
          if (Length (fTextArray [i]) > 0) then begin
            if (VariableEdit.Visible) then
              res := ChangeRetZustandText (fRefZustand, GetComboBoxDBItem (LangComboBox, i).ItemsWert, fTextArray [i], fVarArray [i])
            else
              res := ChangeRetZustandText (fRefZustand, GetComboBoxDBItem (LangComboBox, i).ItemsWert, fTextArray [i]);

            if (res <> 0) then
              break;
          end;
        end;

        if (res <> 0) then
          MessageDLG('Fehler beim Ändern eines Zustandtextes' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
      end;

      CanClose := (res = 0);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditRetZustandForm.FormCreate(Sender: TObject);
var
  i : Integer;
begin
  fRefZustand := -1;

  NrEdit.Text  := '';
  SelEdit.Text  := '';
  LinkEdit.Text  := '';
  RefEdit.Text  := '';
  DefEdit.Text  := '';
  TextEdit.Text := '';
  VariableEdit.Text := '';
  IFCEdit.Text := '';

  for i:=0 to High (fTextArray) do
    fTextArray [i] := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, MandComboBox);
    LVSSprachModul.SetNoTranslate (Self, LangComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditRetZustandForm.FormShow(Sender: TObject);
var
  idx   : Integer;
  query : TBetterADODataSet;
begin
  LoadMandantCombobox (MandComboBox);
  MandComboBox.Items.Insert (0, GetResourceText (1020));

  LoadComboxDBItems (LangComboBox, 'SYS_TEXTE', 'SPRACHE');
  if (LangComboBox.Items.Count > 1) then begin
    LangComboBox.ItemIndex := FindComboboxDBItem (LangComboBox, LVSDatenModul.AktSprache);
    if (LangComboBox.ItemIndex = -1) then LangComboBox.ItemIndex := 0;
  end else begin
    LangComboBox.Enabled := False;
    LangComboBox.ItemIndex := 0;
  end;

  if (fRefZustand = -1) then begin
    if (LVSDatenModul.AktMandantRef = -1) then
      MandComboBox.ItemIndex := 0
    else begin
      MandComboBox.Enabled := False;
      MandComboBox.ItemIndex := FindComboboxRef (MandComboBox, LVSDatenModul.AktMandantRef);
    end;
  end else begin
    MandComboBox.Enabled := False;

    query := TBetterADODataSet.Create(Self);
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    try
      query.CommandText := 'select * from V_RETOUREN_ZUSTAND where REF=:ref';
      query.Parameters [0].Value := fRefZustand;

      query.Open;

      if query.FieldByName ('REF_MAND').IsNull then
        MandComboBox.ItemIndex := 0
      else
        MandComboBox.ItemIndex := FindComboboxRef (MandComboBox, query.FieldByName ('REF_MAND').AsInteger);

      NrEdit.Text   := query.FieldByName ('ZUSTAND_NR').AsString;
      SelEdit.Text  := query.FieldByName ('REIHENFOLGE').AsString;
      LinkEdit.Text := query.FieldByName ('LINK_NR').AsString;
      RefEdit.Text  := query.FieldByName ('REFERENZ_NR').AsString;
      DefEdit.Text  := query.FieldByName ('DEFINITION').AsString;

      AdminCheckBox.Checked    := (query.FieldByName ('OPT_ADMIN').AsString = '1');
      AddInfosCheckBox.Checked := (query.FieldByName ('OPT_ASK_CLARIFY_HINT').AsString <> '0');
      AddInfosErfassenCheckBox.Checked := (query.FieldByName ('OPT_ASK_CLARIFY_HINT').AsString = '2');
      AddInfosMandatoryCheckBox.Checked := (query.FieldByName ('OPT_ASK_CLARIFY_HINT').AsString = '3');
      SperrBesCheckBox.Checked := (query.FieldByName ('OPT_SPERR_BESTAND').AsString = '1');

      if Assigned (query.FindField('OPT_REPRINT_LABEL')) then
        ReprintLabelCheckBox.Checked := (query.FieldByName ('OPT_REPRINT_LABEL').AsString = '1')
      else begin
        ReprintLabelCheckBox.Visible := False;
        ReprintLabelCheckBox.Enabled := False;
      end;

      if Assigned (query.FindField('OPT_FINAL_STATUS')) then
        FinalStatCheckBox.Checked := (query.FieldByName ('OPT_FINAL_STATUS').AsString = '1')
      else begin
        FinalStatCheckBox.Visible := False;
        FinalStatCheckBox.Enabled := False;
      end;

      if Assigned (query.FindField('OPT_ANNAHME_STATUS')) then
        BeiAnnahmeCheckBox.Checked := (query.FieldByName ('OPT_ANNAHME_STATUS').AsString = '1')
      else begin
        BeiAnnahmeCheckBox.Visible := False;
        BeiAnnahmeCheckBox.Enabled := False;
      end;

      if Assigned (query.FindField('OPT_DEFAULT')) then
        DefaultCheckBox.Checked := (query.FieldByName ('OPT_DEFAULT').AsString = '1')
      else begin
        DefaultCheckBox.Visible := False;
        DefaultCheckBox.Enabled := False;
      end;

      if Assigned (query.FindField('IFC_KENNZEICHEN')) then
        IFCEdit.Text := query.FieldByName ('IFC_KENNZEICHEN').AsString
      else begin
        IFCEdit.Visible := False;
        IFCEdit.Enabled := False;
      end;

      Label10.Visible := IFCEdit.Visible;


      query.Close;

      query.CommandText := 'select * from V_SYS_TEXTE where SPRACHE is not null and TEXT_REF=:ref';
      query.Parameters [0].Value := fRefZustand;

      query.Open;

      while not (query.Eof) do begin
        idx := FindComboboxDBItem (LangComboBox, query.FieldByName ('SPRACHE').AsString);
        if (idx = -1) then
          idx := LangComboBox.AddItemIndex (query.FieldByName ('SPRACHE').AsString, TDBItemsDaten.Create ('', query.FieldByName ('SPRACHE').AsString, '', '', ''));

        fTextArray [idx] := query.FieldByName ('TEXT').AsString;

        if not Assigned (query.FindField ('VARIABLEN')) then
          VariableEdit.Visible := False
        else if (VariableEdit.Visible) then
          fVarArray [idx] := query.FieldByName ('VARIABLEN').AsString;

        query.Next;
      end;

      query.Close;

      LangComboBoxChange (Sender);
    finally
      query.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditRetZustandForm.LangComboBoxChange(Sender: TObject);
begin
  if (LangComboBox.ItemIndex = -1) then begin
    TextEdit.Text     := '';
    VariableEdit.Text := '';
  end else begin
    TextEdit.Text     := fTextArray [LangComboBox.ItemIndex];
    VariableEdit.Text := fVarArray [LangComboBox.ItemIndex];
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditRetZustandForm.NumberEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,^C,^V,#9,'0'..'9']) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditRetZustandForm.TextEditExit(Sender: TObject);
begin
  if (LangComboBox.ItemIndex >= 0) then
    fTextArray [LangComboBox.ItemIndex] := TextEdit.Text;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditRetZustandForm.VariableEditExit(Sender: TObject);
begin
  if (LangComboBox.ItemIndex >= 0) then
    fVarArray [LangComboBox.ItemIndex] := VariableEdit.Text;
end;

end.
