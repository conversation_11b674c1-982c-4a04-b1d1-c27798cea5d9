object ChangeAuftragTourForm: TChangeAuftragTourForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  ClientHeight = 362
  ClientWidth = 404
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    404
    362)
  TextHeight = 13
  object OkButton: TButton
    Left = 233
    Top = 329
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    Default = True
    ModalResult = 1
    TabOrder = 3
  end
  object AbortButton: TButton
    Left = 321
    Top = 329
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 4
  end
  object PageControl1: TPageControl
    AlignWithMargins = True
    Left = 3
    Top = 76
    Width = 398
    Height = 182
    ActivePage = SpedTabSheet
    Align = alTop
    MultiLine = True
    TabOrder = 1
    TabPosition = tpBottom
    object TourTabSheet: TTabSheet
      Caption = 'TourTabSheet'
      DesignSize = (
        390
        120)
      object Tournummer: TLabel
        Left = 3
        Top = 3
        Width = 60
        Height = 13
        Caption = 'Tournummer'
      end
      object Tourindex: TLabel
        Left = 3
        Top = 55
        Width = 48
        Height = 13
        Caption = 'Tourindex'
      end
      object TourComboBox: TComboBoxPro
        Left = 3
        Top = 19
        Width = 382
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 0
      end
      object IndexEdit: TEdit
        Left = 3
        Top = 71
        Width = 121
        Height = 21
        TabOrder = 1
        Text = 'IndexEdit'
        OnKeyPress = NumEditKeyPress
      end
    end
    object PrioTabSheet: TTabSheet
      Caption = 'PrioTabSheet'
      ImageIndex = 1
      OnShow = PrioTabSheetShow
      object Label3: TLabel
        Left = 3
        Top = 3
        Width = 38
        Height = 13
        Caption = 'Priorit'#228't'
      end
      object PrioEdit: TEdit
        Left = 3
        Top = 19
        Width = 62
        Height = 21
        TabOrder = 0
        Text = '0'
        OnKeyPress = NumEditKeyPress
      end
      object PrioUpDown: TIntegerUpDown
        Left = 65
        Top = 19
        Width = 16
        Height = 21
        Associate = PrioEdit
        Max = 999
        TabOrder = 1
      end
    end
    object SpedTabSheet: TTabSheet
      Caption = 'SpedTabSheet'
      ImageIndex = 2
      DesignSize = (
        390
        120)
      object Label4: TLabel
        Left = 3
        Top = 3
        Width = 44
        Height = 13
        Caption = 'Spedition'
      end
      object Label5: TLabel
        Left = 3
        Top = 96
        Width = 65
        Height = 13
        Caption = 'Anlieferdepot'
      end
      object Label6: TLabel
        Left = 209
        Top = 96
        Width = 70
        Height = 13
        Caption = 'Auslieferdepot'
      end
      object Label8: TLabel
        Left = 3
        Top = 48
        Width = 37
        Height = 13
        Caption = 'Produkt'
      end
      object SpedComboBox: TComboBoxPro
        Left = 3
        Top = 19
        Width = 382
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 0
        OnChange = SpedComboBoxChange
      end
      object AnDepotComboBox: TComboBoxPro
        Left = 3
        Top = 112
        Width = 166
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 2
      end
      object AusDepotComboBox: TComboBoxPro
        Left = 209
        Top = 112
        Width = 175
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 3
      end
      object SpedProduktComboBox: TComboBoxPro
        Left = 3
        Top = 64
        Width = 382
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 1
      end
    end
    object DeliveryTabSheet: TTabSheet
      Caption = 'DeliveryTabSheet'
      ImageIndex = 3
      object Label9: TLabel
        Left = 3
        Top = 3
        Width = 69
        Height = 13
        Caption = 'Versanddatum'
      end
      object Label17: TLabel
        Left = 3
        Top = 48
        Width = 34
        Height = 13
        Caption = 'Uhrzeit'
        Visible = False
      end
      object DeliveryDateTimePicker: TDateTimePicker
        Left = 3
        Top = 19
        Width = 94
        Height = 21
        Date = 42758.000000000000000000
        Time = 0.343278078697039700
        TabOrder = 0
      end
      object DateTimePicker1: TDateTimePicker
        Left = 3
        Top = 64
        Width = 94
        Height = 21
        Date = 42758.000000000000000000
        Format = 'hh:mm'
        Time = 0.343278078697039700
        Kind = dtkTime
        TabOrder = 1
        Visible = False
      end
      object DateTimePicker2: TDateTimePicker
        Left = 123
        Top = 64
        Width = 94
        Height = 21
        Date = 42758.000000000000000000
        Format = 'hh:mm'
        Time = 0.343278078697039700
        Kind = dtkTime
        TabOrder = 2
        Visible = False
      end
    end
    object RelationTabSheet: TTabSheet
      Caption = 'RelationTabSheet'
      ImageIndex = 4
      DesignSize = (
        390
        120)
      object Label10: TLabel
        Left = 4
        Top = 3
        Width = 35
        Height = 13
        Caption = 'Bereich'
      end
      object Label11: TLabel
        Left = 4
        Top = 47
        Width = 37
        Height = 13
        Caption = 'WA-Tor'
      end
      object Label12: TLabel
        Left = 280
        Top = 47
        Width = 60
        Height = 13
        Caption = 'Verladefolge'
      end
      object LBComboBox: TComboBoxPro
        Left = 4
        Top = 19
        Width = 380
        Height = 22
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 0
        OnChange = LBComboBoxChange
      end
      object WATorComboBox: TComboBoxPro
        Left = 4
        Top = 64
        Width = 263
        Height = 22
        CanItemEnable = True
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 1
      end
      object RelIndexEdit: TEdit
        Left = 280
        Top = 64
        Width = 94
        Height = 21
        TabOrder = 2
        Text = 'RelIndexEdit'
        OnKeyPress = NumEditKeyPress
      end
    end
    object AnlieferTabSheet: TTabSheet
      Caption = 'AnlieferTabSheet'
      ImageIndex = 5
      object Label13: TLabel
        Left = 3
        Top = 43
        Width = 31
        Height = 13
        Caption = 'Datum'
      end
      object Label14: TLabel
        Left = 103
        Top = 43
        Width = 34
        Height = 13
        Caption = 'Uhrzeit'
      end
      object AnlieferDatePicker: TDateTimePicker
        Left = 3
        Top = 59
        Width = 94
        Height = 21
        Date = 42758.000000000000000000
        Time = 0.343278078697039700
        TabOrder = 0
      end
      object AnlieferTimePicker: TDateTimePicker
        Left = 103
        Top = 59
        Width = 94
        Height = 21
        Date = 42758.000000000000000000
        Format = 'HH:mm'
        Time = 0.343278078697039700
        Kind = dtkTime
        TabOrder = 1
      end
      object AnlieferDatCheckBox: TCheckBox
        Left = 3
        Top = 12
        Width = 358
        Height = 17
        Caption = 'Anlieferdatum noch nicht definiert'
        TabOrder = 2
        OnClick = AnlieferDatCheckBoxClick
      end
    end
    object ShippingUnitsTabSheet: TTabSheet
      Caption = 'ShippingUnitsTabSheet'
      ImageIndex = 6
      object Label15: TLabel
        Left = 3
        Top = 3
        Width = 83
        Height = 13
        Caption = 'Versandeinheiten'
      end
      object ShippingUnitsEdit: TEdit
        Left = 3
        Top = 19
        Width = 62
        Height = 21
        TabOrder = 0
        Text = '0'
        OnKeyPress = NumEditKeyPress
      end
      object ShippingUnitsUpDown: TIntegerUpDown
        Left = 65
        Top = 19
        Width = 16
        Height = 21
        Associate = ShippingUnitsEdit
        Max = 999
        TabOrder = 1
      end
    end
    object VersArtTabSheet: TTabSheet
      Caption = 'VersArtTabSheet'
      ImageIndex = 7
      DesignSize = (
        390
        120)
      object Label16: TLabel
        Left = 3
        Top = 3
        Width = 53
        Height = 13
        Caption = 'Versandart'
      end
      object Label18: TLabel
        Left = 3
        Top = 48
        Width = 34
        Height = 13
        Caption = 'Uhrzeit'
        Visible = False
      end
      object Label19: TLabel
        Left = 103
        Top = 67
        Width = 10
        Height = 13
        Caption = ' - '
      end
      object VersArtComboBox: TComboBoxPro
        Left = 3
        Top = 19
        Width = 382
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 0
        OnChange = SpedComboBoxChange
      end
      object VersandArtVonTimePicker: TDateTimePicker
        Left = 3
        Top = 64
        Width = 94
        Height = 21
        Date = 42758.000000000000000000
        Format = 'HH:mm'
        Time = 0.343278078697039700
        Kind = dtkTime
        TabOrder = 1
        OnChange = VersandArtVonTimePickerChange
      end
      object VersandArtBisTimePicker: TDateTimePicker
        Left = 123
        Top = 64
        Width = 94
        Height = 21
        Date = 42758.000000000000000000
        Format = 'HH:mm'
        Time = 0.343278078697039700
        Kind = dtkTime
        TabOrder = 2
      end
    end
    object AvisTabSheet: TTabSheet
      Caption = 'AvisTabSheet'
      ImageIndex = 8
      DesignSize = (
        390
        120)
      object Label20: TLabel
        Left = 3
        Top = 3
        Width = 50
        Height = 13
        Caption = 'Avisierung'
      end
      object Label21: TLabel
        Left = 3
        Top = 48
        Width = 36
        Height = 13
        Caption = 'Telefon'
      end
      object Label22: TLabel
        Left = 267
        Top = 48
        Width = 18
        Height = 13
        Caption = 'Fax'
      end
      object Label23: TLabel
        Left = 3
        Top = 93
        Width = 18
        Height = 13
        Caption = 'Mail'
      end
      object Label24: TLabel
        Left = 136
        Top = 48
        Width = 30
        Height = 13
        Caption = 'Mobile'
      end
      object PhoneEdit: TEdit
        Left = 3
        Top = 64
        Width = 110
        Height = 21
        TabOrder = 0
        Text = 'PhoneEdit'
      end
      object MailEdit: TEdit
        Left = 3
        Top = 109
        Width = 374
        Height = 21
        MaxLength = 64
        TabOrder = 1
        Text = 'MailEdit'
      end
      object FaxEdit: TEdit
        Left = 267
        Top = 64
        Width = 110
        Height = 21
        TabOrder = 2
        Text = 'FaxEdit'
      end
      object MobileEdit: TEdit
        Left = 136
        Top = 64
        Width = 110
        Height = 21
        TabOrder = 3
        Text = 'MobileEdit'
      end
      object AvisComboBox: TComboBoxPro
        Left = 3
        Top = 19
        Width = 382
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 4
        OnChange = SpedComboBoxChange
      end
    end
    object AufLTTabSheet: TTabSheet
      AlignWithMargins = True
      Caption = 'AufLTTabSheet'
      ImageIndex = 9
      DesignSize = (
        384
        114)
      object Label25: TLabel
        Left = 3
        Top = 3
        Width = 109
        Height = 13
        Caption = 'Versand-Ladungstr'#228'ge'
      end
      object Label26: TLabel
        Left = 3
        Top = 51
        Width = 96
        Height = 13
        Caption = 'Pack-Ladungstr'#228'ger'
      end
      object VersLTComboBox: TComboBoxPro
        Left = 3
        Top = 19
        Width = 376
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 0
        OnChange = SpedComboBoxChange
      end
      object PackLTComboBox: TComboBoxPro
        Left = 5
        Top = 68
        Width = 376
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 1
        OnChange = SpedComboBoxChange
      end
    end
  end
  object KopfPanel: TPanel
    Left = 0
    Top = 0
    Width = 404
    Height = 73
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object KundenLabel: TLabel
      Left = 88
      Top = 8
      Width = 61
      Height = 13
      Caption = 'KundenLabel'
    end
    object AuftragLabel: TLabel
      Left = 88
      Top = 28
      Width = 62
      Height = 13
      Caption = 'AuftragLabel'
    end
    object LieferLabel: TLabel
      Left = 88
      Top = 48
      Width = 52
      Height = 13
      Caption = 'LieferLabel'
    end
    object Label7: TLabel
      Left = 12
      Top = 48
      Width = 57
      Height = 13
      Caption = 'Lieferdatum'
    end
    object Label2: TLabel
      Left = 12
      Top = 28
      Width = 37
      Height = 13
      Caption = 'Auftrag'
    end
    object Label1: TLabel
      Left = 12
      Top = 8
      Width = 36
      Height = 13
      Caption = 'Kunden'
    end
  end
  object GrundPanel: TPanel
    Left = 0
    Top = 261
    Width = 404
    Height = 52
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      404
      52)
    object GrundLabel: TLabel
      Left = 8
      Top = 8
      Width = 29
      Height = 13
      Caption = 'Grund'
    end
    object GrundComboBox: TComboBox
      Left = 8
      Top = 24
      Width = 388
      Height = 21
      Style = csDropDownList
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 0
      Items.Strings = (
        'Ware mangelhaft')
    end
  end
end
