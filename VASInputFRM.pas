unit VASInputFRM;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms, 
  Dialogs, StdCtrls, Vcl.ExtCtrls, IntegerUpDown;

type
  TVASInputFrame = class(TFrame)
    CheckboxPanel: TPanel;
    VASCheckBox: TCheckBox;
    EditPanel: TPanel;
    VASEdit: TEdit;
    VASEditLabel: TLabel;
    VASQuantityLabel: TLabel;
    VASQuantityEdit: TEdit;
    VASQuantityUpDown: TIntegerUpDown;
  private
    fRefTempl : Integer;
  public
    property RefTempl : Integer read fRefTempl write fRefTempl;
  end;

implementation

{$R *.dfm}

end.
