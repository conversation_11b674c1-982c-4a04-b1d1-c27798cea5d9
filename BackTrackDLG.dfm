object BackTrackForm: TBackTrackForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Chargen-R'#252'ckverfolgung'
  ClientHeight = 382
  ClientWidth = 349
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    349
    382)
  PixelsPerInch = 96
  TextHeight = 13
  object Bevel2: TBevel
    Left = 8
    Top = 340
    Width = 333
    Height = 5
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 314
    ExplicitWidth = 249
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 349
    Height = 215
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      349
      215)
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object Label5: TLabel
      Left = 8
      Top = 50
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 92
      Width = 333
      Height = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 249
    end
    object Label3: TLabel
      Left = 8
      Top = 118
      Width = 49
      Height = 13
      Caption = 'Artikel-Nr:'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 160
      Width = 333
      Height = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 249
    end
    object Label6: TLabel
      Left = 8
      Top = 166
      Width = 52
      Height = 13
      Caption = 'Von Datum'
    end
    object Label7: TLabel
      Left = 223
      Top = 166
      Width = 47
      Height = 13
      Caption = 'Bis Datum'
    end
    object Bevel4: TBevel
      Left = 8
      Top = 210
      Width = 333
      Height = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 249
    end
    object MandantComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 333
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 66
      Width = 333
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 1
      OnChange = LagerComboBoxChange
    end
    object CheckBox1: TCheckBox
      Left = 8
      Top = 99
      Width = 249
      Height = 17
      Caption = 'Nur gelistete Artikel'
      Checked = True
      State = cbChecked
      TabOrder = 2
      OnClick = CheckBox1Click
    end
    object ArtikelComboBox: TComboBoxPro
      Left = 96
      Top = 134
      Width = 245
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 4
      OnDropDown = ArtikelComboBoxDropDown
    end
    object VonDatumEdit: TEdit
      Left = 8
      Top = 182
      Width = 118
      Height = 21
      TabOrder = 5
      Text = 'VonDatumEdit'
      OnExit = VonDatumEditExit
    end
    object BisDatumEdit: TEdit
      Left = 223
      Top = 182
      Width = 118
      Height = 21
      TabOrder = 6
      Text = 'BisDatumEdit'
      OnExit = BisDatumEditExit
    end
    object ArtikelEdit: TEdit
      Left = 8
      Top = 134
      Width = 73
      Height = 21
      TabOrder = 3
      Text = 'ArtikelEdit'
    end
  end
  object MHDPanel: TPanel
    Left = 0
    Top = 215
    Width = 349
    Height = 45
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      349
      45)
    object Label2: TLabel
      Left = 8
      Top = 2
      Width = 22
      Height = 13
      Caption = 'MHD'
    end
    object MHDEdit: TEdit
      Left = 8
      Top = 18
      Width = 333
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      Text = 'MHDEdit'
      OnExit = MHDEditExit
    end
  end
  object ChargePanel: TPanel
    Left = 0
    Top = 260
    Width = 349
    Height = 48
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      349
      48)
    object Label4: TLabel
      Left = 8
      Top = 2
      Width = 35
      Height = 13
      Caption = 'Charge'
    end
    object ChargeEdit: TEdit
      Left = 8
      Top = 18
      Width = 333
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      Text = 'ChargeEdit'
    end
  end
  object ExcelCheckBox: TCheckBox
    Left = 8
    Top = 314
    Width = 249
    Height = 17
    Anchors = [akLeft, akBottom]
    Caption = 'Ausgabe in Excel'
    TabOrder = 3
  end
  object OkButton: TButton
    Left = 8
    Top = 351
    Width = 118
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Bericht anzeigen...'
    Default = True
    TabOrder = 4
    OnClick = OkButtonClick
  end
  object AbortButton: TButton
    Left = 266
    Top = 351
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 216
    Top = 8
  end
end
