unit ChangeAufPosMHDDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, ComCtrls;

type
  TChangeAufPosMHDForm = class(TForm)
    MinMHDDateTimePicker: TDateTimePicker;
    FixMHDDateTimePicker: TDateTimePicker;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    Label5: TLabel;
    Label6: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel1: TBevel;
    Bevel2: TBevel;
    Bevel3: TBevel;
    AufNrLabel: TLabel;
    KundeLabel: TLabel;
    ArtikelLabel: TLabel;
    MengeLabel: TLabel;
    GrundEdit: TEdit;
    Label7: TLabel;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
  private
    fBlankCommendAllowed : Boolean;
  public
    property BlankCommendAllowed : Boolean read fBlankCommendAllowed write fBlankCommendAllowed;
  end;

implementation

{$R *.dfm}

uses
  CommCtrl, SprachModul, ResourceText, FrontendMessages;

procedure TChangeAufPosMHDForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  systime : TSystemTime;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    if  MinMHDDateTimePicker.Checked and not (DateTime_GetSystemTime (MinMHDDateTimePicker.Handle, systime) = GDT_VALID) then
      MinMHDDateTimePicker.Checked := False;

    if FixMHDDateTimePicker.Checked and not (DateTime_GetSystemTime (FixMHDDateTimePicker.Handle, systime) = GDT_VALID) then
      FixMHDDateTimePicker.Checked := False;

    if (MinMHDDateTimePicker.Checked and FixMHDDateTimePicker.Checked) then begin
      CanClose := False;
      FrontendMessages.MessageDLG (FormatMessageText (1575, []), mtError, [mbOk], 0);
    end else if not (fBlankCommendAllowed) and (Length (GrundEdit.Text) = 0) then begin
      CanClose := False;
      FrontendMessages.MessageDLG (FormatMessageText (1571, []), mtError, [mbOk], 0);
    end else begin
      CanClose := True
    end;
  end;
end;

procedure TChangeAufPosMHDForm.FormCreate(Sender: TObject);
begin
  fBlankCommendAllowed := False;
  GrundEdit.Text := '';
  MinMHDDateTimePicker.Date := Now;
  FixMHDDateTimePicker.Date := Now;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AufNrLabel);
    LVSSprachModul.SetNoTranslate (Self, KundeLabel);
    LVSSprachModul.SetNoTranslate (Self, ArtikelLabel);
    LVSSprachModul.SetNoTranslate (Self, MengeLabel);
  {$endif}
end;

end.
