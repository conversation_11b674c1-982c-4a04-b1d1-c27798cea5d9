//*****************************************************************************
//*  Program System    : ACOModul
//*  Module Name       : UserDLG
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/FrontendACOModul.pas $
// $Revision: 3 $
// $Modtime: 13.09.15 10:39 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Verwaltung der AccessControls einer Anwendung
//*****************************************************************************
unit FrontendACOModul;

interface

uses
  SysUtils, Classes, ACOList, Menus, StdCtrls, ComCtrls, ACOModul;

type
  TFrontendACOModule = class(TACOModule)
    ACOListManager1: TACOListManager;
  public
    constructor Create(AOwner: TComponent); override;
  end;

var
  FrontendACOModule : TFrontendACOModule;

implementation

{$R *.dfm}

constructor TFrontendACOModule.Create(AOwner: TComponent);
begin
  inherited Create (AOwner);

  ApplicationID := 'PCD';

  ACOListManager := ACOListManager1;
end;

end.
