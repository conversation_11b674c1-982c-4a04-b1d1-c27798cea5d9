unit StoreLogixAboutUnit;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TAboutForm = class(TForm)
    Image1: TImage;
    Button1: TButton;
    VerPanel: TPanel;
    procedure FormShow(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

uses VerInfos;

{$R *.dfm}

procedure TAboutForm.FormShow(Sender: TObject);
var
  bm : TBitmap;
  filedate  : TDateTime;
begin
  FileAge (ParamStr (0), filedate);

  VerPanel.Caption := FileVersion (3,2) + ' from ' + DateToStr (filedate);

  bm := TBitmap.Create;
  bm.handle:=loadbitmap(hinstance,'aboutlogo');
  Image1.Picture.Assign (bm);
  bm.Free;
end;

end.
