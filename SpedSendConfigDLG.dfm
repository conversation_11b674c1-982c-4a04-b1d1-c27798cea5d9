object SpedSendConfigForm: TSpedSendConfigForm
  Left = 0
  Top = 0
  Caption = 'SpedSendConfigForm'
  ClientHeight = 589
  ClientWidth = 937
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  TextHeight = 15
  object SpedSendConfigDBGrid: TDBGridPro
    AlignWithMargins = True
    Left = 8
    Top = 49
    Width = 921
    Height = 298
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Align = alClient
    DataSource = SpedSendConfigDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -12
    TitleFont.Name = 'Segoe UI'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\CS'
    RegistrySection = 'DBGrids'
    WidthOfIndicator = 11
    DefaultRowHeight = 19
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object TopPanel: TPanel
    Left = 0
    Top = 0
    Width = 937
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
  end
  object BattomPanel: TPanel
    Left = 0
    Top = 548
    Width = 937
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    object CloseButton: TButton
      Left = 1239
      Top = 6
      Width = 75
      Height = 25
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 8
      TabOrder = 0
    end
  end
  object PageControl1: TPageControl
    Left = 0
    Top = 355
    Width = 937
    Height = 193
    ActivePage = TabSheet1
    Align = alBottom
    TabOrder = 3
    object TabSheet1: TTabSheet
      Caption = 'TabSheet1'
    end
  end
  object SpedSendConfigQuery: TOraQuery
    Left = 448
    Top = 216
  end
  object SpedSendConfigDataSource: TOraDataSource
    DataSet = SpedSendConfigQuery
    Left = 536
    Top = 144
  end
end
