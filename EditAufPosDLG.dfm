object EditAufPosForm: TEditAufPosForm
  Left = 323
  Top = 349
  BorderStyle = bsDialog
  Caption = 'Manueller Auftragsposition erfassen'
  ClientHeight = 530
  ClientWidth = 786
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    786
    530)
  TextHeight = 13
  object Label21: TLabel
    Left = 8
    Top = 24
    Width = 84
    Height = 13
    Caption = 'Untermandant:'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label2: TLabel
    Left = 8
    Top = 40
    Width = 33
    Height = 13
    Caption = 'Name'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object SubMandLabel: TLabel
    Left = 112
    Top = 24
    Width = 72
    Height = 13
    Caption = 'SubMandLabel'
  end
  object KundenNameLabel: TLabel
    Left = 112
    Top = 40
    Width = 91
    Height = 13
    Caption = 'KundenNameLabel'
  end
  object Bevel4: TBevel
    Left = 8
    Top = 438
    Width = 773
    Height = 8
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 421
    ExplicitWidth = 767
  end
  object Label4: TLabel
    Left = 8
    Top = 8
    Width = 63
    Height = 13
    Caption = 'Auftrag-Nr:'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object AuftragNrLabel: TLabel
    Left = 112
    Top = 8
    Width = 71
    Height = 13
    Caption = 'AuftragNrLabel'
  end
  object Label15: TLabel
    Left = 8
    Top = 288
    Width = 101
    Height = 13
    Caption = 'Kommissionierhinweis'
  end
  object GrundBevel: TBevel
    Left = 8
    Top = 331
    Width = 771
    Height = 8
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 765
  end
  object AbortButton: TButton
    Left = 701
    Top = 478
    Width = 75
    Height = 26
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 6
  end
  object OkButton: TButton
    Left = 613
    Top = 478
    Width = 75
    Height = 26
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    ModalResult = 1
    TabOrder = 5
  end
  object FehlerLabel: TPanel
    Left = 8
    Top = 446
    Width = 773
    Height = 24
    Anchors = [akLeft, akRight, akBottom]
    BevelOuter = bvNone
    Caption = 'FehlerLabel'
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 4
  end
  object StatusBar1: TStatusBar
    Left = 0
    Top = 512
    Width = 786
    Height = 18
    Panels = <
      item
        Width = 200
      end>
  end
  object KommHinweisEdit: TEdit
    Left = 8
    Top = 304
    Width = 768
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 64
    TabOrder = 1
    Text = 'KommHinweisEdit'
  end
  object GrundPanel: TPanel
    Left = 8
    Top = 352
    Width = 770
    Height = 42
    Anchors = [akLeft, akRight, akBottom]
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      770
      42)
    object Label19: TLabel
      Left = 0
      Top = 0
      Width = 29
      Height = 13
      Caption = 'Grund'
    end
    object GrundComboBox: TComboBox
      Left = 0
      Top = 16
      Width = 772
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 0
      Text = 'GrundComboBox'
      OnChange = GrundComboBoxChange
    end
  end
  object QSGrundPanel: TPanel
    Left = 8
    Top = 396
    Width = 770
    Height = 42
    Anchors = [akLeft, akRight, akBottom]
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      770
      42)
    object Label20: TLabel
      Left = 0
      Top = 0
      Width = 70
      Height = 13
      Caption = 'QS-Sperrgrund'
    end
    object QSGrundComboBox: TComboBox
      Left = 0
      Top = 16
      Width = 772
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 0
      Text = 'QSGrundComboBox'
      OnChange = GrundComboBoxChange
    end
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 72
    Width = 762
    Height = 205
    ActivePage = ArtikelTabSheet
    TabOrder = 0
    object ArtikelTabSheet: TTabSheet
      Caption = 'Artikel'
      ImageIndex = 1
      DesignSize = (
        754
        177)
      object Label5: TLabel
        Left = 16
        Top = 8
        Width = 22
        Height = 13
        Caption = 'EAN'
      end
      object Label6: TLabel
        Left = 160
        Top = 8
        Width = 41
        Height = 13
        Caption = 'Artikelnr.'
      end
      object Label7: TLabel
        Left = 16
        Top = 67
        Width = 33
        Height = 13
        Caption = 'Menge'
      end
      object Label8: TLabel
        Left = 160
        Top = 67
        Width = 39
        Height = 13
        Caption = 'Gewicht'
      end
      object Label9: TLabel
        Left = 456
        Top = 67
        Width = 47
        Height = 13
        Caption = 'min. MHD'
      end
      object Label10: TLabel
        Left = 655
        Top = 67
        Width = 34
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'Charge'
      end
      object Label11: TLabel
        Left = 256
        Top = 67
        Width = 87
        Height = 13
        Caption = 'Netto Preis pro VE'
        Visible = False
      end
      object Label3: TLabel
        Left = 456
        Top = 8
        Width = 46
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'Artikeltext'
        ExplicitLeft = 459
      end
      object Bevel3: TBevel
        Left = -19
        Top = 57
        Width = 773
        Height = 9
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
        ExplicitWidth = 776
      end
      object Label13: TLabel
        Left = 220
        Top = 86
        Width = 12
        Height = 13
        Caption = 'kg'
      end
      object Label14: TLabel
        Left = 389
        Top = 86
        Width = 23
        Height = 13
        Caption = 'EUR'
        Visible = False
      end
      object Label12: TLabel
        Left = 547
        Top = 67
        Width = 52
        Height = 13
        Caption = 'Fixes MHD'
      end
      object Label16: TLabel
        Left = 336
        Top = 8
        Width = 76
        Height = 13
        Caption = 'Mandatenartikel'
      end
      object Label17: TLabel
        Left = 633
        Top = 114
        Width = 101
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'Artikelnr. des Kunden'
        ExplicitLeft = 636
      end
      object Label18: TLabel
        Left = 17
        Top = 114
        Width = 88
        Height = 13
        Caption = 'Bestandskategorie'
      end
      object VPELabel: TLabel
        Left = 87
        Top = 80
        Width = 34
        Height = 20
        AutoSize = False
        Caption = 'VPELabel'
      end
      object Label25: TLabel
        Left = 456
        Top = 114
        Width = 114
        Height = 13
        Caption = 'Vorgegebene Serien.-Nr'
      end
      object Label26: TLabel
        Left = 160
        Top = 114
        Width = 53
        Height = 13
        Caption = 'Bestand-ID'
      end
      object Label27: TLabel
        Left = 280
        Top = 114
        Width = 47
        Height = 13
        Alignment = taRightJustify
        Caption = 'Projekt-ID'
      end
      object FaktorLabel: TLabel
        Left = 87
        Top = 93
        Width = 43
        Height = 20
        AutoSize = False
        Caption = 'FaktorLabel'
      end
      object EANEdit: TEdit
        Left = 16
        Top = 24
        Width = 106
        Height = 21
        TabOrder = 0
        Text = 'EANEdit'
        OnChange = InputChange
        OnExit = EANEditExit
        OnKeyPress = EANEditKeyPress
      end
      object ArtikelNrEdit: TEdit
        Left = 160
        Top = 24
        Width = 89
        Height = 21
        TabOrder = 1
        Text = 'ArtikelNrEdit'
        OnChange = InputChange
        OnExit = ArtikelNrEditExit
        OnKeyPress = ArtikelNrEditKeyPress
      end
      object MengeEdit: TEdit
        Left = 16
        Top = 83
        Width = 49
        Height = 21
        TabOrder = 5
        Text = '20000'
        OnChange = InputChange
        OnExit = MengeEditExit
        OnKeyPress = EditKeyPress
      end
      object GewichtEdit: TEdit
        Left = 160
        Top = 82
        Width = 55
        Height = 21
        TabOrder = 7
        Text = '9999,999'
        OnChange = InputChange
        OnKeyPress = GewichtEditKeyPress
      end
      object MHDEdit: TEdit
        Left = 456
        Top = 83
        Width = 70
        Height = 21
        TabOrder = 10
        Text = '99.99.9999'
        OnChange = InputChange
        OnExit = MHDEditExit
        OnKeyPress = EditKeyPress
      end
      object ChargeEdit: TEdit
        Left = 633
        Top = 83
        Width = 115
        Height = 21
        Anchors = [akTop, akRight]
        TabOrder = 12
        Text = 'ChargeEdit'
        OnChange = InputChange
      end
      object PreisNetEdit: TEdit
        Left = 256
        Top = 83
        Width = 58
        Height = 21
        TabOrder = 8
        Text = 'PreisNetEdit'
        OnChange = InputChange
        OnKeyPress = PreisNetEditKeyPress
      end
      object MengeUpDown: TIntegerUpDown
        Left = 65
        Top = 83
        Width = 16
        Height = 21
        Associate = MengeEdit
        Max = 20000
        Position = 20000
        TabOrder = 6
        Thousands = False
        OnExit = MengeEditExit
      end
      object ArtikelTextEdit: TEdit
        Left = 456
        Top = 24
        Width = 292
        Height = 21
        TabStop = False
        Anchors = [akTop, akRight]
        Enabled = False
        ReadOnly = True
        TabOrder = 4
        Text = 'ArtikelTextEdit'
      end
      object ListArtikelButton: TButton
        Left = 256
        Top = 24
        Width = 65
        Height = 21
        Caption = '...'
        TabOrder = 2
        OnClick = ListArtikelButtonClick
      end
      object FixMHDEdit: TEdit
        Left = 543
        Top = 83
        Width = 70
        Height = 21
        TabOrder = 11
        Text = 'FixMHDEdit'
        OnChange = InputChange
        OnExit = MHDEditExit
        OnKeyPress = EditKeyPress
      end
      object MandArtikelNrEdit: TEdit
        Left = 336
        Top = 24
        Width = 89
        Height = 21
        TabOrder = 3
        Text = 'ArtikelNrEdit'
        OnChange = InputChange
        OnExit = ArtikelNrEditExit
        OnKeyPress = ArtikelNrEditKeyPress
      end
      object KdArtikelEdit: TEdit
        Left = 633
        Top = 130
        Width = 115
        Height = 21
        Anchors = [akTop, akRight]
        MaxLength = 32
        TabOrder = 17
        Text = 'KdArtikelEdit'
      end
      object CategoryComboBox: TComboBoxPro
        Left = 16
        Top = 130
        Width = 114
        Height = 21
        TabOrder = 13
        Text = 'CategoryComboBox'
        OnChange = CategoryComboBoxChange
      end
      object SerialEdit: TEdit
        Left = 456
        Top = 130
        Width = 157
        Height = 21
        MaxLength = 32
        TabOrder = 16
        Text = 'SerialEdit'
      end
      object BestandIDComboBox: TComboBox
        Left = 160
        Top = 130
        Width = 105
        Height = 21
        TabOrder = 14
        Text = 'BestandIDComboBox'
      end
      object ProjectIDEdit: TEdit
        Left = 280
        Top = 130
        Width = 88
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 32
        TabOrder = 15
        Text = 'ProjectIDEdit'
      end
      object PreisGrosEdit: TEdit
        Left = 325
        Top = 83
        Width = 58
        Height = 21
        TabOrder = 9
        Text = 'PreisGrosEdit'
        OnChange = InputChange
        OnKeyPress = PreisNetEditKeyPress
      end
    end
    object HUTabSheet: TTabSheet
      Caption = 'HU Nr.'
      object Label23: TLabel
        Left = 8
        Top = 10
        Width = 103
        Height = 13
        Caption = 'Vorgegebene NVE-Nr'
      end
      object Label22: TLabel
        Left = 208
        Top = 10
        Width = 97
        Height = 13
        Caption = 'Vorgegebene HU-Nr'
      end
      object Label1: TLabel
        Left = 8
        Top = 67
        Width = 54
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Kunden-Nr:'
      end
      object Label24: TLabel
        Left = 208
        Top = 67
        Width = 93
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Ausgehende HU-Nr'
      end
      object NVENrEdit: TEdit
        Left = 8
        Top = 26
        Width = 151
        Height = 21
        MaxLength = 18
        TabOrder = 0
        Text = 'NVENrEdit'
      end
      object HUNrEdit: TEdit
        Left = 208
        Top = 26
        Width = 113
        Height = 21
        MaxLength = 32
        TabOrder = 1
        Text = 'HUNrEdit'
      end
      object OutNVENrEdit: TEdit
        Left = 8
        Top = 83
        Width = 151
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        MaxLength = 18
        TabOrder = 2
        Text = 'OutNVENrEdit'
      end
      object OutHUNrEdit: TEdit
        Left = 208
        Top = 83
        Width = 113
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        MaxLength = 32
        TabOrder = 3
        Text = 'OutHUNrEdit'
      end
    end
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 728
    Top = 8
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    OnChangeLanguage = CompTranslateForm1ChangeLanguage
    Left = 688
    Top = 8
  end
end
