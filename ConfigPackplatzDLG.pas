unit ConfigPackplatzDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, Menus, Grids, StringGridPro, StdCtrls, DBGrids, SMDBGrid,
  DBGridPro, ExtCtrls, ComboBoxPro, DoPrintDLG;

type
  TConfigPackplatzForm = class(TForm)
    PackplatzDBGrid: TDBGridPro;
    FachStringGridPopupMenu: TPopupMenu;
    NewPlaceButton: TButton;
    DelPlaceButton: TButton;
    CloseButton: TButton;
    NewCaseMenuItem: TMenuItem;
    EditFachMenuItem: TMenuItem;
    PrintFachLabelMenuItem: TMenuItem;
    DelFachMenuItem: TMenuItem;
    N1: TMenuItem;
    N2: TMenuItem;
    PackplatzDataSource: TDataSource;
    PackplatzQuery: TADOQuery;
    Label3: TLabel;
    LagerComboBox: TComboBoxPro;
    Label4: TLabel;
    Bevel1: TBevel;
    GroupBox2: TGroupBox;
    FachStringGrid: TStringGridPro;
    NewCaseButton: TButton;
    DelCaseButton: TButton;
    PrintAllFachLabelMenuItem: TMenuItem;
    EditPlaceButton: TButton;
    PackplatzDBGridPopupMenu: TPopupMenu;
    PackActiveMenuItem: TMenuItem;
    PackDeactiveMenuItem: TMenuItem;
    CopyPlaceButton: TButton;
    procedure FormShow(Sender: TObject);
    procedure LagerComboBoxChange(Sender: TObject);
    procedure PackplatzDataSourceDataChange(Sender: TObject; Field: TField);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FachStringGridPopupMenuPopup(Sender: TObject);
    procedure PrintFachLabelMenuItemClick(Sender: TObject);
    procedure NewPlaceButtonClick(Sender: TObject);
    procedure NewCaseButtonClick(Sender: TObject);
    procedure DelCaseButtonClick(Sender: TObject);
    procedure PackplatzDBGridDblClick(Sender: TObject);
    procedure DelPlaceButtonClick(Sender: TObject);
    procedure PackplatzDBGridPopupMenuPopup(Sender: TObject);
    procedure PackActiveMenuItemClick(Sender: TObject);
    procedure CopyPlaceButtonClick(Sender: TObject);
  private
    fRefPlatz        : Integer;

    function PrintFachLabel (Sender : TDoPrintForm) : Integer;
    function UpdatePackplatzGrid : Integer;
    function UpdatePackfachGrid : Integer;
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, StringUtils, DatenModul, FrontendUtils, DBGridUtilModule, ConfigModul, LVSSecurity,
  PrintModul, PrinterUtils, LablePrinterUtils, LVSWarenausgang, EditPackCase, EditPackplatzDLG, ResourceText,
  SprachModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.LagerComboBoxChange(Sender: TObject);
begin
  UpdatePackplatzGrid;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TConfigPackplatzForm.UpdatePackfachGrid : Integer;
var
  idx   : Integer;
  query : TADOQuery;
begin
  if (PackplatzQuery.Active and (PackplatzQuery.RecNo > 0)) then begin
    ClearGridObjects (FachStringGrid);

    query := TADOQuery.Create (Self);

    try
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select * from V_WA_PACKFACH where REF_PACKPLATZ='+PackplatzQuery.FieldByName ('REF').AsString);

      query.Open;

      idx := FachStringGrid.FixedRows;

      while not (query.Eof) do begin
        FachStringGrid.Cells [1, idx] := query.FieldByName ('NAME').AsString;
        FachStringGrid.Cells [2, idx] := query.FieldByName ('FACH_NR').AsString;
        FachStringGrid.Cells [3, idx] := query.FieldByName ('FACH_KENNUNG').AsString;

        FachStringGrid.Objects [0, idx] := TGridRef.Create (query.FieldByName ('REF').AsInteger);

        Inc (idx);

        query.Next;
      end;

      if (idx > FachStringGrid.FixedRows) then
        FachStringGrid.RowCount := idx
      else begin
        FachStringGrid.RowCount := FachStringGrid.FixedRows + 1;
        FachStringGrid.Rows [FachStringGrid.RowCount - 1].Clear;
      end;

      query.Close;
    finally
      query.Free;
    end;
  end else begin
    FachStringGrid.RowCount := FachStringGrid.FixedRows + 1;
    FachStringGrid.Rows [FachStringGrid.RowCount - 1].Clear;
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.PackActiveMenuItemClick(Sender: TObject);
var
  res : Integer;
begin
  if (PackplatzQuery.Active and (PackplatzQuery.RecNo > 0)) then begin
    if (Sender = PackActiveMenuItem) then begin
      res := ActivateVerpackungsPlatz (PackplatzQuery.FieldByName ('REF').AsInteger);

      if (res <> 0) then
        MessageDLG (FormatMessageText (1318, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else
        PackplatzDBGrid.Reload;
    end else begin
      res := DeactivateVerpackungsPlatz (PackplatzQuery.FieldByName ('REF').AsInteger);

      if (res <> 0) then
        MessageDLG (FormatMessageText (1319, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else
        PackplatzDBGrid.Reload;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.PackplatzDataSourceDataChange(Sender: TObject; Field: TField);
begin
  if (PackplatzQuery.Active and (PackplatzQuery.RecNo > 0)) then begin
    EditPlaceButton.Enabled := True;
    CopyPlaceButton.Enabled := True;

    fRefPlatz := PackplatzQuery.FieldByName ('REF').AsInteger;
  end else begin
    EditPlaceButton.Enabled := False;
    CopyPlaceButton.Enabled := False;

    fRefPlatz := -1;
  end;

  UpdatePackfachGrid;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.PackplatzDBGridDblClick(Sender: TObject);
var
  editform : TEditPackplatzForm;
begin
  if (PackplatzQuery.Active and (PackplatzQuery.RecNo > 0)) then begin
    editform := TEditPackplatzForm.Create (Self);

    editform.Prepare (PackplatzQuery.FieldByName ('REF').AsInteger);

    if (editform.ShowModal = mrOk) then begin
      PackplatzDBGrid.Reload (editform.RefPackplatz);
    end;

    editform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.PackplatzDBGridPopupMenuPopup(Sender: TObject);
begin
  PackActiveMenuItem.Enabled := False;
  PackDeactiveMenuItem.Enabled := False;

  if (PackplatzQuery.Active and (PackplatzQuery.RecNo > 0)) Then begin
    PackDeactiveMenuItem.Enabled := PackplatzQuery.FieldByName ('STATUS').AsString = 'AKT';
    PackActiveMenuItem.Enabled   := not (PackDeactiveMenuItem.Enabled);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TConfigPackplatzForm.PrintFachLabel (Sender : TDoPrintForm) : Integer;
var
  res        : Integer;
  forminfo   : TFormInfos;
  nrstr,
  errtext    : String;
  checkch    : AnsiChar;
  paramarray : array [0..31] of AnsiString;

  //****************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //****************************************************************************
  //* Description
  //****************************************************************************
  //* Return Value :
  //****************************************************************************
  function PrintLabel (const Index : Integer) : Integer;
  var
    res : Integer;
  begin
    nrstr := FormatStr (FachStringGrid.Cells [3, Index],-5,'0');
    checkch := GetLELPCheckChar ('991'+nrstr);

    FillChar (paramarray, sizeof (paramarray), 0);

    paramarray [0] := 'PLATZ_NAME:'+PackplatzQuery.FieldByName ('NAME').AsString;
    paramarray [1] := 'FACH_NAME:'+FachStringGrid.Cells [1, Index];
    paramarray [2] := 'FACH_NR:'+FachStringGrid.Cells [2, Index];
    paramarray [3] := 'BARCODE:'+'991'+nrstr+checkch;
    paramarray [4] := 'BARCODE_TEXT:'+'991 ' + nrstr + ' ' + checkch;
    paramarray [5] := 'KOPIEN:1';

    res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramarray, errtext);

    Result := res;
  end;

var
  idx : Integer;
begin
  with Sender.PrinterPort do
    res := DetectFormular (-1, PackplatzQuery.FieldByName ('REF_LAGER').AsInteger, '', '', Model, 'WA_FACH-LABEL', forminfo);

  if (res <> 0) Then
    MessageDlg (FormatMessageText (1320, ['WA_FACH-LABEL']), mtError, [mbOK], 0)
  else begin
    Screen.Cursor := crHourGlass;

    try
      if (TComboBoxRef (Sender.Data).Ref = 1) then
        res := PrintLabel (FachStringGrid.Row)
      else if (TComboBoxRef (Sender.Data).Ref = 2) then begin
        idx := FachStringGrid.FixedRows;

        while (idx < FachStringGrid.RowCount) and (res = 0) do begin
          res := PrintLabel (idx);

          Inc (idx);
        end;
      end;
    finally
      Screen.Cursor := crDefault;
    end;

    if (res <> 0) Then
      MessageDlg(FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.PrintFachLabelMenuItemClick(Sender: TObject);
var
  printform : TDoPrintForm;
begin
  printform := TDoPrintForm.Create (Self);

  try
    printform.Prepare (PackplatzQuery.FieldByName ('REF_LAGER').AsInteger, UserReg.ReadRegValue ('WA-FACH-PRINTER'), 'E6', PrintFachLabel);

    if (printform.PrinterComboBox.Items.Count = 0) Then
      MessageDlg(FormatMessageText (1058, ['Format E6']), mtError, [mbOK], 0)
    else begin
      printform.Caption := '';

      if (Sender = PrintAllFachLabelMenuItem) then
        printform.Data := TComboBoxRef.Create (2)
      else
        printform.Data := TComboBoxRef.Create (1);

      printform.ShowModal;

      UserReg.WriteRegValue ('WA-FACH-PRINTER', printform.PrinterComboBox.GetItemText ());
    end;
  finally
    printform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.NewCaseButtonClick(Sender: TObject);
var
  res,
  ref      : Integer;
  editform : TEditPackCaseForm;
begin
  if (PackplatzQuery.Active and (PackplatzQuery.RecNo > 0)) then begin
    editform := TEditPackCaseForm.Create (Self);

    if (Sender = NewCaseButton) or (Sender = NewCaseMenuItem) then begin
      editform.Caption := GetResourceText(1743);
      editform.Ref := -1;
    end else if (FachStringGrid.Row >= FachStringGrid.FixedRows) and  Assigned (FachStringGrid.Objects [0, FachStringGrid.Row]) then begin
      editform.Caption := GetResourceText(1744);
      editform.Ref := TGridRef (FachStringGrid.Objects [0, FachStringGrid.Row]).Ref;

      editform.DescEdit.Text := FachStringGrid.Cells [1, FachStringGrid.Row];
      editform.NrEdit.Text   := FachStringGrid.Cells [2, FachStringGrid.Row];
      editform.IDEdit.Text   := FachStringGrid.Cells [3, FachStringGrid.Row];
    end else begin
      editform.Caption := GetResourceText(1743);
      editform.Ref := -1;
    end;

    if (editform.ShowModal = mrOk) then begin
      if (editform.Ref = -1) then begin
        res := CreatePackplatzFach (PackplatzQuery.FieldByName ('REF').AsInteger, editform.DescEdit.Text, editform.NrEdit.Text, editform.IDEdit.Text, ref);

        if (res <> 0) then
          MessageDLG (FormatMessageText (1589, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
        else
          UpdatePackplatzGrid;
      end else begin
        res := ChangePackplatzFach (editform.Ref, editform.DescEdit.Text, editform.NrEdit.Text, editform.IDEdit.Text);

        if (res <> 0) then
          MessageDLG (FormatMessageText (1590, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
        else
          UpdatePackfachGrid;
      end;
    end;

    editform.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.NewPlaceButtonClick(Sender: TObject);
var
  editform : TEditPackplatzForm;
begin
  editform := TEditPackplatzForm.Create (Self);

  editform.Prepare (-1);

  if (editform.ShowModal = mrOk) then begin
    PackplatzDBGrid.Reload (editform.RefPackplatz);
  end;

  editform.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.06.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.CopyPlaceButtonClick(Sender: TObject);
var
  editform : TEditPackplatzForm;
begin
  if (PackplatzQuery.Active and (PackplatzQuery.RecNo > 0)) then begin
    editform := TEditPackplatzForm.Create (Self);

    editform.Prepare (PackplatzQuery.FieldByName ('REF').AsInteger);

    editform.Caption := FormatResourceText (1478, [editform.NameEdit.Text]);

    editform.NameEdit.Text := '';
    editform.RefPackplatz := -1;

    if (editform.ShowModal = mrOk) then begin
      PackplatzDBGrid.Reload (editform.RefPackplatz);
    end;

    editform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.DelCaseButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (FachStringGrid.Row >= FachStringGrid.FixedRows) and  Assigned (FachStringGrid.Objects [0, FachStringGrid.Row]) then begin
    if (MessageDLG (FormatMessageText (1588, []), mtConfirmation, [mbYes, mbNo, mbCancel], 0) = mrYes) then begin
      res := DeletePackplatzFach (TGridRef (FachStringGrid.Objects [0, FachStringGrid.Row]).Ref);

      if (res <> 0) then
        MessageDLG (FormatMessageText (1591, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else
        UpdatePackplatzGrid;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TConfigPackplatzForm.UpdatePackplatzGrid : Integer;
begin
  PackplatzQuery.Close;

  PackplatzQuery.SQL.Clear;
  PackplatzQuery.SQL.Add ('select * from V_PCD_WA_PACKPLATZ where STATUS in (''ANG'',''AKT'')');

  if (GetComboBoxRef (LagerComboBox) <> -1)  then
    PackplatzQuery.SQL.Add ('and REF_LAGER='+GetComboBoxRefStr (LagerComboBox))
  else
    PackplatzQuery.SQL.Add ('and REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION='+IntToStr (LVSDatenModul.AktLocationRef)+')');

  PackplatzQuery.Open;

  PackplatzDBGrid.SetColumnVisible ('OPT_SINGLE_DISTR', False);
  PackplatzDBGrid.SetColumnVisible ('OPT_SINGLE_NVE', False);
  PackplatzDBGrid.SetColumnVisible ('OPT_USE_VERTEIL_KLT', False);
  PackplatzDBGrid.SetColumnVisible ('OPT_SPERRGUT', False);
  PackplatzDBGrid.SetColumnVisible ('OPT_CLEARING', False);
  PackplatzDBGrid.SetColumnVisible ('OPT_VPE_SELECT_ALLOWED', False);
  PackplatzDBGrid.SetColumnVisible ('OPT_SELECT_PACK_LT', False);
  PackplatzDBGrid.SetColumnVisible ('OPT_AUTO_CLOSE', False);

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.05.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.DelPlaceButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (PackplatzQuery.Active and (PackplatzQuery.RecNo > 0)) then begin
    if (MessageDLG (FormatMessageText (1316, []), mtConfirmation, [mbYes, mbNo, mbCancel], 0) = mrYes) then begin
      res := DeleteVerpackungsPlatz (PackplatzQuery.FieldByName ('REF').AsInteger);

      if (res <> 0) then
        MessageDLG (FormatMessageText (1317, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else
        PackplatzDBGrid.Reload;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.FachStringGridPopupMenuPopup(Sender: TObject);
begin
  EditFachMenuItem.Enabled := False;
  PrintFachLabelMenuItem.Enabled := False;
  DelFachMenuItem.Enabled := False;

  if (FachStringGrid.Row >= FachStringGrid.FixedRows) and  Assigned (FachStringGrid.Objects [0, FachStringGrid.Row]) then begin
    EditFachMenuItem.Enabled := True;
    PrintFachLabelMenuItem.Enabled := True;
    DelFachMenuItem.Enabled := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  PackplatzQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  if Assigned (LVSSecurityModule) and Assigned (LVSSecurityModule.ACOModul) then begin
    LVSSecurityModule.ACOModul.SetBerechtigungen (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
  {$endif}

  FachStringGrid.TitelTexte [1] := GetResourceText(1740);
  FachStringGrid.TitelTexte [2] := GetResourceText(1741);
  FachStringGrid.TitelTexte [3] := GetResourceText(1742);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.FormDestroy(Sender: TObject);
begin
  ClearGridObjects (FachStringGrid);
  ClearComboBoxObjects (LagerComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigPackplatzForm.FormShow(Sender: TObject);
begin
  LVSConfigModul.ConfigForm (Self);
  LVSConfigModul.RestoreFormInfo (Self);

  LoadLagerCombobox (LagerComboBox, LVSDatenModul.AktLocationRef, LVSDatenModul.AktMandantRef);

  if (LVSDatenModul.AktLagerRef <> -1) then begin
    LagerComboBox.Enabled := False;
    LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, LVSDatenModul.AktLagerRef)
  end else begin
    LagerComboBox.Items.Insert (0, GetResourceText(1004));
    LagerComboBox.ItemIndex := 0;
  end;

  UpdatePackplatzGrid;
end;

end.
