unit PrintPreViewNewDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ComCtrls, Preview, StdCtrls, ToolWin, ImgList, ExtCtrls;

type
  TReprintProc = procedure(Sender: TObject; PrintPreview : TPrintPreview; Data : TObject) of object;

  TPrintPreviewNewForm = class(TForm)
    PrintPreview: TPrintPreview;
    ThumbnailPreview: TThumbnailPreview;
    PageSetupDialog: TPageSetupDialog;
    ImageList1: TImageList;
    ToolBar1: TToolBar;
    ToolButton1: TToolButton;
    ToolButton2: TToolButton;
    PageLabel: TLabel;
    ToolButton4: TToolButton;
    ToolButton5: TToolButton;
    ToolButton9: TToolButton;
    ToolButton8: TToolButton;
    ToolButton3: TToolButton;
    ZoomComboBox: TComboBox;
    ToolButton7: TToolButton;
    ToolButton6: TToolButton;
    Label1: TLabel;
    Panel1: TPanel;
    CloseButton: TButton;
    PrintAllButton: TButton;
    PrintDialog: TPrintDialog;
    PrinterSetupDialog: TPrinterSetupDialog;
    procedure ZoomComboBoxChange(Sender: TObject);
    procedure ToolButton1Click(Sender: TObject);
    procedure ToolButton2Click(Sender: TObject);
    procedure ToolButton4Click(Sender: TObject);
    procedure ToolButton5Click(Sender: TObject);
    procedure PrintPreviewChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure CloseButtonClick(Sender: TObject);
    procedure PrintAllButtonClick(Sender: TObject);
    procedure ToolButton6Click(Sender: TObject);
  private
    fData       : TObject;
    fOnReprint  : TReprintProc;
  public
    PageBoundsAfterMargin: TRect;

    property    Data      : TObject      read fData      write fData;
    property    OnReprint : TReprintProc read fOnReprint write fOnReprint;
  end;

implementation

uses ConfigModul, Printers;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintPreviewNewForm.CloseButtonClick(Sender: TObject);
begin
  Close;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintPreviewNewForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintPreviewNewForm.FormShow(Sender: TObject);
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.RestoreFormInfo (Self);

  if (PrintPreview.ZoomState = zsZoomToFit) then
    ZoomComboBox.ItemIndex := 0
  else if (PrintPreview.ZoomState = zsZoomToWidth) then
    ZoomComboBox.ItemIndex := 1
  else if (PrintPreview.ZoomState = zsZoomOther) then begin
    if (PrintPreview.Zoom = 25) then
      ZoomComboBox.ItemIndex := 2
    else if (PrintPreview.Zoom = 50) then
      ZoomComboBox.ItemIndex := 3
    else if (PrintPreview.Zoom = 75) then
      ZoomComboBox.ItemIndex := 4
    else if (PrintPreview.Zoom = 100) then
      ZoomComboBox.ItemIndex := 5
    else if (PrintPreview.Zoom = 200) then
      ZoomComboBox.ItemIndex := 5
    else
      ZoomComboBox.ItemIndex := -1
  end;

  if (Printer.PrinterIndex = -1) then
    ToolButton6.Caption := ''
  else
    ToolButton6.Caption := Printer.Printers [Printer.PrinterIndex]
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintPreviewNewForm.PrintAllButtonClick(Sender: TObject);
begin
  with PrintDialog do begin
    MinPage := 1;
    MaxPage := PrintPreview.TotalPages;
    PrintRange := prAllPages;
    if Execute then
    begin
      Screen.Cursor := crHourGlass;
      try
        PrintPreview.PrintJobTitle := Caption;

        case PrintRange of
          prAllPages: PrintPreview.Print;
          prPageNums: PrintPreview.PrintPages(FromPage, ToPage);
        end;
      finally
        Screen.Cursor := crDefault;
      end;
    end;
  end;

  Close;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintPreviewNewForm.PrintPreviewChange(Sender: TObject);
begin
  PageLabel.Caption := IntToStr (PrintPreview.CurrentPage) + '/' + IntToStr (PrintPreview.TotalPages);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintPreviewNewForm.ToolButton1Click(Sender: TObject);
begin
  PrintPreview.CurrentPage := 1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintPreviewNewForm.ToolButton2Click(Sender: TObject);
begin
  if (PrintPreview.CurrentPage > 1) then
    PrintPreview.CurrentPage := PrintPreview.CurrentPage - 1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintPreviewNewForm.ToolButton4Click(Sender: TObject);
begin
  if (PrintPreview.CurrentPage < PrintPreview.TotalPages) then
    PrintPreview.CurrentPage := PrintPreview.CurrentPage + 1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintPreviewNewForm.ToolButton5Click(Sender: TObject);
begin
  PrintPreview.CurrentPage := PrintPreview.TotalPages;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintPreviewNewForm.ToolButton6Click(Sender: TObject);
begin
  if PrinterSetupDialog.Execute then begin
    if (Printer.PrinterIndex = -1) then
      ToolButton6.Caption := ''
    else
      ToolButton6.Caption := Printer.Printers [Printer.PrinterIndex];

    PrintPreview.GetPrinterOptions;

    PrintPreview.SetPageSetupParameters(PageSetupDialog);

    if Assigned (fOnReprint) then begin
      fOnReprint (Self, PrintPreview, fData);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintPreviewNewForm.ZoomComboBoxChange(Sender: TObject);
begin
  if (ZoomComboBox.ItemIndex = 0) then
    PrintPreview.ZoomState :=  zsZoomToFit
  else if (ZoomComboBox.ItemIndex = 1) then
    PrintPreview.ZoomState :=  zsZoomToWidth
  else if (ZoomComboBox.ItemIndex = 2) then begin
    PrintPreview.ZoomState :=  zsZoomOther;
    PrintPreview.Zoom := 25;
  end else if (ZoomComboBox.ItemIndex = 3) then begin
    PrintPreview.ZoomState :=  zsZoomOther;
    PrintPreview.Zoom := 50;
  end else if (ZoomComboBox.ItemIndex = 4) then begin
    PrintPreview.ZoomState :=  zsZoomOther;
    PrintPreview.Zoom := 75;
  end else if (ZoomComboBox.ItemIndex = 5) then begin
    PrintPreview.ZoomState :=  zsZoomOther;
    PrintPreview.Zoom := 100;
  end else if (ZoomComboBox.ItemIndex = 6) then begin
    PrintPreview.ZoomState :=  zsZoomOther;
    PrintPreview.Zoom := 200;
  end;
end;

end.
