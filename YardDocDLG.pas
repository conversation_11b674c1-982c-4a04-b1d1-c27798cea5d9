unit YardDocDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ComCtrls, StdCtrls, ExtCtrls, ComboBoxPro, PrinterUtils;

type
  TYardDocForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    PrinterComboBox: TComboBoxPro;
    Label8: TLabel;
    Label2: TLabel;
    WENrLabel: TLabel;
    Bevel1: TBevel;
    Label3: TLabel;
    WeBestNrLabel: TLabel;
    RampenScheinPanel: TPanel;
    Label1: TLabel;
    YardDocCountEdit: TEdit;
    YardDocCountUpDown: TUpDown;
    EtikettenPanel: TPanel;
    PrintMultiColliEANRadioGroup: TRadioGroup;
    KopfPanel: TPanel;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    fRefWE    : Integer;
    fRefMand  : Integer;
    fRefLager : Integer;

    fBestellNummer : String;

    PortArray : array [0..255] of TPrinterPorts;
  public
    property RefWE : Integer read fRefWE write fRefWE;

    function GetSelectedPrinter : String;
  end;

implementation

uses
  VCLUtilitys, KeyboardUtils, ResourceText,
  DB, ADODB, DatenModul, FrontendUtils, ConfigModul, PrintModul, EANLabelPrintDLG, LVSDatenInterface;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.05.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TYardDocForm.GetSelectedPrinter : String;
var
  prtstr : String;
begin
  if (PrinterComboBox.ItemIndex <= Low (PortArray)) or (PrinterComboBox.ItemIndex > High (PortArray)) then
    prtstr := ''
  else if (Length (PortArray [PrinterComboBox.ItemIndex].Name) = 0) then
    prtstr := ''
  else if (Length (PortArray [PrinterComboBox.ItemIndex].PrtTyp) = 0) then
    prtstr := PortArray [PrinterComboBox.ItemIndex].Name
  else
    prtstr := PortArray [PrinterComboBox.ItemIndex].Name + ';' + PortArray [PrinterComboBox.ItemIndex].PrtTyp;

  Result := prtstr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.03.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TYardDocForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  i,
  res,
  count,
  oldref,
  oldsubref  : Integer;
  errtxt,
  setstr,
  chargestr  : String;
  query,
  chquery,
  subquery   : TADOQuery;
  strlist    : TStringList;
  eanprtform : TEANLabelPrintForm;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    res := 0;
    
    if RampenScheinPanel.Visible and (YardDocCountUpDown.Position > 0) then
      PrintModule.PrintReport ('', PrintModule.StdLaserPrinter.Port, '', -1, fRefMand, fRefLager, '', 'WE-RAMPENSCHEIN', '', ['REF:' + IntToStr (fRefWE)], errtxt, False, YardDocCountUpDown.Position - 1, -1);

    if (PrintMultiColliEANRadioGroup.ItemIndex > 0) then begin
      eanprtform := TEANLabelPrintForm.Create (Self);

      if (PrinterComboBox.ItemIndex = -1) then
        eanprtform.Prepare ('', fRefMand, fRefLager, True)
      else eanprtform.Prepare (GetSelectedPrinter, fRefMand, fRefLager, False);

      if (eanprtform.PrinterComboBox.Items.Count = 0) then
        MessageDlg (FormatResourceText (1041, []), mtError, [mbOK], 0)
      else begin
        query    := TADOQuery.Create (Self);
        chquery  := TADOQuery.Create (Self);
        subquery := TADOQuery.Create (Self);

        try
          query.LockType := ltReadOnly;
          query.Connection := LVSDatenModul.MainADOConnection;

          chquery.LockType := ltReadOnly;
          chquery.Connection := LVSDatenModul.MainADOConnection;

          subquery.LockType := ltReadOnly;
          subquery.Connection := LVSDatenModul.MainADOConnection;

          if (PrintMultiColliEANRadioGroup.ItemIndex = 5) then begin
            query.SQL.Add ('select ae.REF_AR,ae.REF as REF_AR_EINHEIT,ae.OPT_MULTI_COLLI,wp.MHD,wp.CHARGE,sum (MENGE) as SUM_MENGE,wp.REF as REF_BEST_POS');
            query.SQL.Add ('from V_ARTIKEL_EINHEIT ae, V_WE_POS wp where ae.REF=wp.REF_AR_EINHEIT and wp.REF_WE=:ref_we');
            query.Parameters.ParamByName('ref_we').Value := fRefWE;
            query.SQL.Add ('group by ae.REF_AR,wp.ARTIKEL_NR,ae.REF,ae.OPT_MULTI_COLLI,wp.MHD,wp.CHARGE,wp.REF order by wp.ARTIKEL_NR');
          end else begin
            query.SQL.Add ('select ae.REF_AR,ae.REF as REF_AR_EINHEIT,verspos.REF_BEST_POS,ae.OPT_MULTI_COLLI,sum (verspos.MENGE_SOLL) as SUM_MENGE_SOLL, sum (verspos.MENGE_SOLL - nvl (verspos.MENGE_GESAMT, 0)) as SUM_MENGE_OFFEN');
            query.SQL.Add ('from V_ARTIKEL_EINHEIT ae, V_VER_BESTELL_POS verspos where verspos.REF_LAGER=:ref_lager and ae.REF=verspos.REF_AR_EINHEIT');
            query.Parameters.ParamByName('ref_lager').Value := fRefLager;

            if (Pos (';', fBestellNummer) = 0) then
              query.SQL.Add ('and verspos.BESTELL_NR='+#39+fBestellNummer+#39)
            else begin
              //Die Liste der Bestellnummern zerlegen und als Parameter nutzen
              setstr :='';
              strlist := TStringList.Create;

              try
                strlist.Delimiter := ';';
                strlist.DelimitedText := fBestellNummer;

                for i := 0 to strlist.Count - 1 do begin
                  if (Length (setstr) > 0) then setstr := setstr+',';
                  setstr := setstr+#39+strlist[i]+#39;
                end;
              finally
                strlist.Free;
              end;

              query.SQL.Add ('and verspos.BESTELL_NR in ('+setstr+')');
            end;

            if (PrintMultiColliEANRadioGroup.ItemIndex = 1) or (PrintMultiColliEANRadioGroup.ItemIndex = 2) then
              query.SQL.Add ('and nvl (ae.OPT_MULTI_COLLI, ''0'')=''1''');

            query.SQL.Add ('group by ae.REF_AR,verspos.ARTIKEL_NR,ae.REF,verspos.REF_BEST_POS,ae.OPT_MULTI_COLLI order by verspos.ARTIKEL_NR');
          end;

          query.Open;

          res := 0;

          oldref := -1;

          while not (query.Eof) and (res = 0) do begin
            res := GetArtikelInfos (query.FieldByName('REF_AR').AsInteger, query.FieldByName('REF_AR_EINHEIT').AsInteger, eanprtform.ArtikelInfo);

            if (res = 0) then begin
              if (oldref <> -1) and (oldref = query.FieldByName('REF_BEST_POS').AsInteger) then
              else begin
                if (PrintMultiColliEANRadioGroup.ItemIndex = 5) then
                  count := query.FieldByName('SUM_MENGE').AsInteger
                else if (PrintMultiColliEANRadioGroup.ItemIndex = 1) or (PrintMultiColliEANRadioGroup.ItemIndex = 3) then
                  count := query.FieldByName('SUM_MENGE_SOLL').AsInteger
                else
                  count := query.FieldByName('SUM_MENGE_OFFEN').AsInteger;

                if (query.FieldByName('OPT_MULTI_COLLI').AsString = '1') then begin
                  subquery.SQL.Clear;
                  subquery.SQL.Add ('select REF_SET_AR_EINHEIT from V_ARTIKEL_EINHEIT_COLLI where REF_MASTER_AR_EINHEIT=:ref_ae');
                  subquery.Parameters.ParamByName('ref_ae').Value := query.FieldByName('REF_AR_EINHEIT').AsInteger;

                  if (eanprtform.ArtikelInfo.ChargeArt = 'P') and LVSConfigModul.UseCharge then begin
                    eanprtform.PageControl1.ActivePage := eanprtform.EAN128TabSheet;
                    eanprtform.MHDEdit.Text := '';

                    chquery.SQL.Clear;
                    chquery.SQL.Add ('select CHARGE from V_BESTELL_POS_CHARGE where REF_BEST_POS=:ref_pos');
                    chquery.Parameters.ParamByName('ref_pos').Value := query.FieldByName('REF_BEST_POS').AsInteger;

                    if (PrintMultiColliEANRadioGroup.ItemIndex = 2) or (PrintMultiColliEANRadioGroup.ItemIndex = 4) then
                      chquery.SQL.Add ('and nvl (MENGE_IST,0)=0');

                    chquery.SQL.Add ('group by CHARGE order by CHARGE');

                    chquery.Open;

                    while (count > 0) and (res = 0) do begin
                      if not (chquery.Fields [0].IsNull) then
                        chargestr := chquery.Fields [0].AsString
                      else
                        res := CreateWECharge (fRefLager, fRefMand, '', eanprtform.ArtikelInfo.RefArtikel, '', 0, chargestr);

                      if (res = 0) and (Length (chargestr) = 0) then begin
                        res := -18;

                        MessageDlg ('In diesem Lager ist keine WE-Charge konfiguriert', mtError, [mbOK], 0);
                      end;

                      if (res = 0) then begin
                        oldsubref := -1;

                        subquery.Open;

                        while not (subquery.Eof) and (res = 0) do begin
                          if (oldsubref <> -1) and (oldsubref = subquery.FieldByName('REF_SET_AR_EINHEIT').AsInteger) then
                          else begin
                            res := GetArtikelInfos (query.FieldByName('REF_AR').AsInteger, subquery.FieldByName('REF_SET_AR_EINHEIT').AsInteger, eanprtform.ArtikelInfo);

                            if (res = 0) then begin
                              eanprtform.ChargeEdit.Text := chargestr;
                              eanprtform.KartonUpDown.Position := 1;

                              if (eanprtform.Preview) then begin
                                if (eanprtform.ShowModal = mrOk) then
                                  eanprtform.Preview := False
                              end else begin
                                eanprtform.PreviewCopies  := 1;
                                res := eanprtform.DoPrint (Sender);
                              end;
                            end;

                            oldsubref := subquery.FieldByName('REF_SET_AR_EINHEIT').AsInteger;
                          end;

                          subquery.Next;
                        end;

                        subquery.Close;
                      end;

                      chquery.Next;

                      Dec (count);
                    end;
                  end else begin
                    oldsubref := -1;

                    subquery.Open;

                    while not (subquery.Eof) and (res = 0) do begin
                      if (oldsubref <> -1) and (oldsubref = subquery.FieldByName('REF_SET_AR_EINHEIT').AsInteger) then
                      else begin
                        res := GetArtikelInfos (query.FieldByName('REF_AR').AsInteger, subquery.FieldByName('REF_SET_AR_EINHEIT').AsInteger, eanprtform.ArtikelInfo);

                        if (res = 0) then begin
                          eanprtform.PageControl1.ActivePage := eanprtform.EANTabSheet;
                          eanprtform.EANLabelCountUpDown.Position := count;

                          if (eanprtform.Preview) then begin
                            if (eanprtform.ShowModal = mrOk) then
                              eanprtform.Preview := False
                          end else begin
                            eanprtform.PreviewCopies  := count;
                            eanprtform.DoPrint (Sender);
                          end;
                        end;

                        oldsubref := subquery.FieldByName('REF_SET_AR_EINHEIT').AsInteger;
                      end;

                      subquery.Next;
                    end;

                    subquery.Close;
                  end;
                end else begin
                  if (eanprtform.ArtikelInfo.ChargeArt <> 'P') or not (LVSConfigModul.UseCharge) then begin
                    eanprtform.PageControl1.ActivePage := eanprtform.EANTabSheet;
                    eanprtform.EANLabelCountUpDown.Position := count;
                  end else begin
                    if (PrintMultiColliEANRadioGroup.ItemIndex = 5) then
                      chargestr := query.FieldByName('CHARGE').AsString
                    else begin
                      res := CreateWECharge (fRefLager, fRefMand, '', eanprtform.ArtikelInfo.RefArtikel, '', 0, chargestr);

                      if (res = 0) and (Length (chargestr) = 0) then begin
                        res := -18;

                        MessageDlg ('In diesem Lager ist keine WE-Charge konfiguriert', mtError, [mbOK], 0);
                      end;
                    end;

                    eanprtform.PageControl1.ActivePage := eanprtform.EAN128TabSheet;
                    eanprtform.MHDEdit.Text := '';
                    eanprtform.ChargeEdit.Text := chargestr;
                    eanprtform.KartonUpDown.Position := count;
                  end;

                  if (res = 0) then begin
                    if (eanprtform.Preview) then begin
                      if (eanprtform.ShowModal = mrOk) then
                        eanprtform.Preview := False
                    end else begin
                      eanprtform.PreviewCopies  := count;
                      res := eanprtform.DoPrint (Sender);
                    end;
                  end;
                end;
              end;

              oldref := query.FieldByName('REF_BEST_POS').AsInteger;
            end;

            query.Next;
          end;

          query.Close;
        finally
          query.Free;
        end;
      end;

      eanprtform.Release;
    end;

    CanClose := (res = 0);
  end;

  UserReg.WriteRegValue ('YARD-PRINTER', GetSelectedPrinter);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.03.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TYardDocForm.FormCreate(Sender: TObject);
begin
  YardDocCountEdit.Text       := '';
  YardDocCountUpDown.Position := 1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.03.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TYardDocForm.FormShow(Sender: TObject);
var
  prtname : String;
  query   : TADOQuery;
begin
  if not (RampenScheinPanel.Visible) then
    Height := Height - RampenScheinPanel.Height;

  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from V_WARENEINGANG where REF='+IntToStr (fRefWE));

    query.Open;

    fRefMand  := query.FieldByName ('REF_MAND').AsInteger;
    fRefLager := query.FieldByName ('REF_LAGER').AsInteger;
    fBestellNummer := query.FieldByName ('BESTELL_NR').AsString;

    WENrLabel.Caption := query.FieldByName ('EINGANGS_NR').AsString;
    WeBestNrLabel.Caption := fBestellNummer;

    query.Close;
  finally
    query.Free;
  end;

  if not (LVSConfigModul.UseArtikelCollis) then begin
    TRadioButton (PrintMultiColliEANRadioGroup.Controls[1]).Visible := False;
    TRadioButton (PrintMultiColliEANRadioGroup.Controls[2]).Visible := False;
  end;

  if (Length (fBestellNummer) = 0) then begin
    TRadioButton (PrintMultiColliEANRadioGroup.Controls[1]).Enabled := False;
    TRadioButton (PrintMultiColliEANRadioGroup.Controls[2]).Enabled := False;
    TRadioButton (PrintMultiColliEANRadioGroup.Controls[3]).Enabled := False;
    TRadioButton (PrintMultiColliEANRadioGroup.Controls[4]).Enabled := False;
  end;

  prtname := UserReg.ReadRegValue ('YARD-PRINTER');
  if (Length (prtname) = 0) then prtname := UserReg.ReadRegValue ('VPE-PRINTER');

  PrintModule.LoadPrinterCombobox (fRefLager, 'E7', PrinterComboBox, PortArray, prtname, True);

  (*
  if (PrinterComboBox.Items.Count = 0) Then begin
    PrinterComboBox.ItemIndex := -1;
    PrinterComboBox.Text := '';
  end else begin
    prtname := UserReg.ReadRegValue ('YARD-PRINTER');
    if (Length (prtname) = 0) then prtname := UserReg.ReadRegValue ('VPE-PRINTER');

    PrinterComboBox.ItemIndex := PrinterComboBox.IndexOf (prtname, 0, True);

    if (PrinterComboBox.ItemIndex = -1) then begin
      PrinterComboBox.ItemIndex := 0;
    end;
  end;
  *)
end;

end.
