﻿unit AuftragAdrFRM;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls,
  Ora, OraSmart;

type
  TAuftragAdrFrame = class(TFrame)
    AdrGroupBox: TGroupBox;
    Label1: TLabel;
    AdrNameLabel: TLabel;
    Label2: TLabel;
    AdrZusatzLabel: TLabel;
    Label6: TLabel;
    AdrFilialLabel: TLabel;
    Label3: TLabel;
    AdrStrasseLabel: TLabel;
    AdrStrasseAddLabel: TLabel;
    Label4: TLabel;
    AdrOrtLabel: TLabel;
    AdrLandLabel: TLabel;
    Label18: TLabel;
    Label12: TLabel;
    AdrColliLabel: TLabel;
    Label19: TLabel;
    AdrILNLabel: TLabel;
    Label24: TLabel;
    AvisInfoLabel: TLabel;
    Label26: TLabel;
    AdrTelLabel: TLabel;
    Label25: TLabel;
    AdrMailLabel: TLabel;
    Label28: TLabel;
    AdrMobileLabel: TLabel;
  private
    fRefAdr : Integer;

    fAufStatus  : String;
    fLager      : String;
    fMandant    : String;
    fStrasse    : String;
    fOrt        : String;
    fLand       : String;

    constructor Create;
  public
    property  RefAdr : Integer read fRefAdr;

    procedure ShowAdress (const Caption : String; AdrQuery : TSmartQuery);
  end;

implementation

{$R *.dfm}

constructor TAuftragAdrFrame.Create;
begin
  AdrNameLabel.Caption := '';
  AdrZusatzLabel.Caption := '';
  AdrStrasseLabel.Caption := '';
  AdrStrasseAddLabel.Caption := '';
  AdrOrtLabel.Caption := '';
  AdrLandLabel.Caption := '';
  AdrColliLabel.Caption := '';
  AdrFilialLabel.Caption := '';
  AdrILNLabel.Caption := '';
  AdrMailLabel.Caption := '';
  AdrTelLabel.Caption := '';
  AdrMobileLabel.Caption := '';
  AvisInfoLabel.Caption := '';
end;

procedure TAuftragAdrFrame.ShowAdress (const Caption : String; AdrQuery : TSmartQuery);
begin
  AdrGroupBox.Caption := Caption;

  fRefAdr := AdrQuery.FieldByName('REF').AsInteger;

  fAufStatus  := AdrQuery.FieldByName('STATUS').AsString;
  fLager      := AdrQuery.FieldByName('LAGER').AsString;
  fMandant    := AdrQuery.FieldByName('MANDANT').AsString;

  fStrasse := AdrQuery.FieldByName('STRASSE').AsString;
  fOrt := AdrQuery.FieldByName('ORT').AsString;
  fLand := AdrQuery.FieldByName('LAND').AsString;

  AdrNameLabel.Caption := StringReplace (AdrQuery.FieldByName('NAME1').AsString, '&', '&&', [rfReplaceAll]);
  if not (AdrQuery.FieldByName('NAME2').IsNull) then
    AdrNameLabel.Caption := AdrNameLabel.Caption+ ' / ' + StringReplace (AdrQuery.FieldByName('NAME2').AsString, '&', '&&', [rfReplaceAll]);

  AdrZusatzLabel.Caption := StringReplace (AdrQuery.FieldByName('NAMEZUSATZ').AsString, '&', '&&', [rfReplaceAll]);
  AdrFilialLabel.Caption := StringReplace (AdrQuery.FieldByName('FILIAL_NR').AsString, '&', '&&', [rfReplaceAll]);
  AdrStrasseLabel.Caption := StringReplace (AdrQuery.FieldByName('STRASSE').AsString, '&', '&&', [rfReplaceAll]);
  AdrStrasseAddLabel.Caption := StringReplace (AdrQuery.FieldByName('STRASSE_2').AsString, '&', '&&', [rfReplaceAll]);
  AdrOrtLabel.Caption := StringReplace (AdrQuery.FieldByName('PLZ').AsString+' '+AdrQuery.FieldByName('ORT').AsString, '&', '&&', [rfReplaceAll]);
  AdrLandLabel.Caption := AdrQuery.FieldByName('LAND').AsString+' ('+AdrQuery.FieldByName('LAND_ISO').AsString+')';
  AdrColliLabel.Caption := StringReplace (AdrQuery.FieldByName('COLLI_ADR_TEXT').AsString, '&', '&&', [rfReplaceAll]);
  AdrILNLabel.Caption := AdrQuery.FieldByName('ILN').AsString;
  AdrMailLabel.Caption := AdrQuery.FieldByName('EMAIL').AsString;
  AdrTelLabel.Caption := AdrQuery.FieldByName('TELEFON').AsString;
  AdrMobileLabel.Caption := AdrQuery.FieldByName('MOBILE').AsString;

  if not Assigned (AdrQuery.FindField ('AVIS_INFOS')) then
    Label24.Visible := false
  else
    AvisInfoLabel.Caption := AdrQuery.FieldByName('AVIS_INFOS').AsString;
end;

end.
