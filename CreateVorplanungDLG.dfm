object CreateVorplanungForm: TCreateVorplanungForm
  Left = 426
  Top = 388
  Caption = 'Auftrag '#228'ndern'
  ClientHeight = 703
  ClientWidth = 608
  Color = clBtnFace
  Constraints.MinHeight = 500
  Constraints.MinWidth = 400
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object MandPanel: TPanel
    Left = 0
    Top = 0
    Width = 608
    Height = 48
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      608
      48)
    object Label2: TLabel
      Left = 8
      Top = 8
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 9
      Top = 24
      Width = 590
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      ItemHeight = 16
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 48
    Width = 608
    Height = 46
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      608
      46)
    object Label11: TLabel
      Left = 8
      Top = 6
      Width = 67
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 591
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      ItemHeight = 16
      TabOrder = 0
    end
  end
  object LagerPanel: TPanel
    Left = 0
    Top = 94
    Width = 608
    Height = 46
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      608
      46)
    object Label3: TLabel
      Left = 8
      Top = 6
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 591
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      ItemHeight = 16
      TabOrder = 0
      OnChange = LagerComboBoxChange
    end
  end
  object EmpfPanel: TPanel
    Left = 0
    Top = 140
    Width = 608
    Height = 221
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    object EmpfKopfPanel: TPanel
      Left = 0
      Top = 0
      Width = 608
      Height = 49
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 0
      DesignSize = (
        608
        49)
      object Label12: TLabel
        Left = 8
        Top = 8
        Width = 99
        Height = 13
        Caption = 'Warenempf'#228'nger Nr.'
      end
      object Bevel1: TBevel
        Left = 8
        Top = 2
        Width = 591
        Height = 4
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
        ExplicitWidth = 601
      end
      object EmpfNameLabel: TLabel
        Left = 168
        Top = 27
        Width = 78
        Height = 13
        Caption = 'EmpfNameLabel'
      end
      object EmpfNrEdit: TEdit
        Left = 8
        Top = 24
        Width = 121
        Height = 21
        TabOrder = 0
        Text = 'EmpfNrEdit'
        OnExit = EmpfNrEditExit
      end
      object ShowEmpfGridButton: TButton
        Left = 425
        Top = 13
        Width = 174
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Warenempf'#228'nger anzeigen...'
        TabOrder = 1
        TabStop = False
        OnClick = ShowEmpfGridButtonClick
      end
    end
    object EmpfGridPanel: TPanel
      Left = 0
      Top = 49
      Width = 608
      Height = 172
      Align = alClient
      BevelOuter = bvNone
      Color = clSkyBlue
      ParentBackground = False
      TabOrder = 1
      Visible = False
      DesignSize = (
        608
        172)
      object Label1: TLabel
        Left = 16
        Top = 10
        Width = 82
        Height = 13
        Caption = 'Warenempf'#228'nger'
      end
      object AllEmpfCheckBox: TCheckBox
        Left = 416
        Top = 7
        Width = 175
        Height = 17
        Anchors = [akTop, akRight]
        BiDiMode = bdLeftToRight
        Caption = 'Alle Warenempf'#228'nger anzeigen'
        ParentBiDiMode = False
        TabOrder = 0
        OnClick = AllEmpfCheckBoxClick
      end
      object KundenDBGrid: TDBGridPro
        Left = 16
        Top = 29
        Width = 575
        Height = 136
        Anchors = [akLeft, akTop, akRight, akBottom]
        BiDiMode = bdLeftToRight
        DataSource = KundenDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ParentBiDiMode = False
        PopupMenu = KundenGridPopupMenu
        ReadOnly = True
        TabOrder = 1
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        OnExit = KundenDBGridExit
        OnKeyPress = KundenDBGridKeyPress
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'MS Sans Serif'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
        OnColumnSort = KundenDBGridColumnSort
      end
    end
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 361
    Width = 608
    Height = 295
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      608
      295)
    object Label4: TLabel
      Left = 8
      Top = 43
      Width = 43
      Height = 13
      Caption = 'Bestellnr.'
    end
    object Label9: TLabel
      Left = 368
      Top = 43
      Width = 140
      Height = 13
      Caption = 'Voraussichtliches Lieferdatum'
    end
    object Label5: TLabel
      Left = 8
      Top = 77
      Width = 37
      Height = 13
      Caption = 'Hinweis'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 100
      Width = 591
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Bevel2: TBevel
      Left = 8
      Top = 2
      Width = 591
      Height = 4
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 601
    end
    object Label6: TLabel
      Left = 153
      Top = 43
      Width = 115
      Height = 13
      Caption = 'Datum der Reservierung'
    end
    object Label7: TLabel
      Left = 8
      Top = 115
      Width = 29
      Height = 13
      Caption = 'Artikel'
    end
    object Label8: TLabel
      Left = 8
      Top = 145
      Width = 54
      Height = 13
      Caption = 'Ausf'#252'hrung'
    end
    object Bevel4: TBevel
      Left = 8
      Top = 172
      Width = 591
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object BestNrEdit: TEdit
      Left = 72
      Top = 40
      Width = 73
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'BestNrEdit'
      OnChange = AufChange
    end
    object LiefDateTimePicker: TDateTimePicker
      Left = 514
      Top = 40
      Width = 86
      Height = 21
      Date = 41221.655059525460000000
      Time = 41221.655059525460000000
      TabOrder = 2
      OnChange = AufChange
    end
    object HintTextEdit: TEdit
      Left = 72
      Top = 74
      Width = 527
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 256
      TabOrder = 3
      Text = 'HintTextEdit'
      OnChange = AufChange
    end
    object ResDateTimePicker: TDateTimePicker
      Left = 274
      Top = 40
      Width = 87
      Height = 21
      Date = 41221.655059525460000000
      Time = 41221.655059525460000000
      TabOrder = 1
      OnChange = AufChange
    end
    object ArNrEdit: TEdit
      Left = 72
      Top = 112
      Width = 98
      Height = 21
      TabOrder = 4
      Text = 'ArNrEdit'
      OnExit = ArNrEditExit
    end
    object ArtikelComboBox: TComboBoxPro
      Left = 185
      Top = 112
      Width = 233
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 5
      OnChange = ArtikelComboBoxChange
      OnDropDown = ArtikelComboBoxDropDown
    end
    object VarianteEdit: TEdit
      Left = 72
      Top = 142
      Width = 345
      Height = 21
      TabOrder = 6
      Text = 'VarianteEdit'
      OnChange = AufChange
    end
    object MengePanel: TPanel
      Left = 0
      Top = 177
      Width = 608
      Height = 56
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 7
      object Label10: TLabel
        Left = 16
        Top = 8
        Width = 33
        Height = 13
        Caption = 'Menge'
      end
      object Label13: TLabel
        Left = 16
        Top = 38
        Width = 39
        Height = 13
        Caption = 'Gewicht'
      end
      object Label14: TLabel
        Left = 156
        Top = 38
        Width = 12
        Height = 13
        Caption = 'kg'
      end
      object MengeEdit: TEdit
        Left = 72
        Top = 5
        Width = 81
        Height = 21
        TabOrder = 0
        Text = '0'
        OnChange = AufChange
      end
      object MengeUpDown: TIntegerUpDown
        Left = 153
        Top = 5
        Width = 16
        Height = 21
        Associate = MengeEdit
        Max = 10000
        TabOrder = 1
      end
      object GewichtEdit: TEdit
        Left = 72
        Top = 35
        Width = 81
        Height = 21
        TabOrder = 2
        Text = 'GewichtEdit'
        OnChange = AufChange
      end
    end
    object ChargePanel: TPanel
      Left = 0
      Top = 264
      Width = 608
      Height = 31
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 9
      object Label15: TLabel
        Left = 16
        Top = 13
        Width = 34
        Height = 13
        Caption = 'Charge'
      end
      object ChargeEdit: TEdit
        Left = 72
        Top = 10
        Width = 121
        Height = 21
        MaxLength = 32
        TabOrder = 0
        Text = 'ChargeEdit'
        OnChange = AufChange
      end
    end
    object MHDPanel: TPanel
      Left = 0
      Top = 233
      Width = 608
      Height = 31
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 8
      object MHDLabel: TLabel
        Left = 15
        Top = 14
        Width = 25
        Height = 13
        Caption = 'MHD'
      end
      object Label16: TLabel
        Left = 185
        Top = 13
        Width = 64
        Height = 13
        Alignment = taRightJustify
        Caption = 'Herstelldatum'
      end
      object MHDEdit: TEdit
        Left = 72
        Top = 10
        Width = 81
        Height = 21
        TabOrder = 0
        Text = 'MHDEdit'
        OnChange = AufChange
      end
      object ProdDatumEdit: TEdit
        Left = 255
        Top = 10
        Width = 81
        Height = 21
        TabOrder = 1
        Text = 'ProdDatumEdit'
        OnChange = AufChange
      end
    end
  end
  object Panel1: TPanel
    Left = 0
    Top = 656
    Width = 608
    Height = 47
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      608
      47)
    object Bevel5: TBevel
      Left = 8
      Top = 6
      Width = 591
      Height = 10
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object OkButton: TButton
      Left = 436
      Top = 14
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
    object AbortButton: TButton
      Left = 524
      Top = 14
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 216
    Top = 8
  end
  object KundenQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 112
    Top = 704
  end
  object KundenDataSource: TDataSource
    DataSet = KundenQuery
    OnDataChange = KundenDataSourceDataChange
    Left = 152
    Top = 704
  end
  object PosDataSource: TDataSource
    DataSet = PosQuery
    OnDataChange = PosDataSourceDataChange
    Left = 64
    Top = 704
  end
  object PosQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 24
    Top = 704
  end
  object KundenGridPopupMenu: TPopupMenu
    Left = 360
    Top = 240
    object NeuerWarenempfngeranlegen1: TMenuItem
      Caption = 'Neuer Warenempf'#228'nger anlegen...'
      OnClick = NeuerWarenempfngeranlegen1Click
    end
    object Warenempfngerbearbeiten1: TMenuItem
      Caption = 'Warenempf'#228'nger bearbeiten...'
    end
    object N1: TMenuItem
      Caption = '-'
    end
  end
end
