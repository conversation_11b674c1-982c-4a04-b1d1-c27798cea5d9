object SetNVEGewichtForm: TSetNVEGewichtForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Gewicht der NVE'
  ClientHeight = 318
  ClientWidth = 412
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnKeyPress = FloatEditKeyPress
  OnShow = FormShow
  DesignSize = (
    412
    318)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 48
    Width = 42
    Height = 13
    Caption = 'NVE-Nr.:'
  end
  object Label2: TLabel
    Left = 8
    Top = 28
    Width = 56
    Height = 13
    Caption = 'Empf'#228'nger:'
  end
  object Label3: TLabel
    Left = 8
    Top = 68
    Width = 48
    Height = 13
    Caption = 'Spedition:'
  end
  object NVELabel: TLabel
    Left = 88
    Top = 48
    Width = 50
    Height = 13
    Caption = 'NVELabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object EmpfLabel: TLabel
    Left = 88
    Top = 28
    Width = 58
    Height = 13
    Caption = 'EmpfLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object SpedLabel: TLabel
    Left = 88
    Top = 68
    Width = 58
    Height = 13
    Caption = 'SpedLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label4: TLabel
    Left = 8
    Top = 8
    Width = 41
    Height = 13
    Caption = 'Auftrag:'
  end
  object AufLabel: TLabel
    Left = 88
    Top = 8
    Width = 49
    Height = 13
    Caption = 'AufLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Bevel1: TBevel
    Left = 8
    Top = 92
    Width = 396
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 386
  end
  object Label5: TLabel
    Left = 8
    Top = 104
    Width = 58
    Height = 13
    Caption = 'Anzahl VPEs'
  end
  object Label6: TLabel
    Left = 8
    Top = 150
    Width = 64
    Height = 13
    Caption = 'Nettogewicht'
  end
  object Label7: TLabel
    Left = 112
    Top = 150
    Width = 67
    Height = 13
    Caption = 'Bruttogewicht'
  end
  object Bevel2: TBevel
    Left = 8
    Top = 273
    Width = 396
    Height = 6
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 200
    ExplicitWidth = 386
  end
  object Label13: TLabel
    Left = 8
    Top = 196
    Width = 55
    Height = 13
    Caption = 'Abmessung'
  end
  object Label11: TLabel
    Left = 64
    Top = 215
    Width = 6
    Height = 13
    Caption = 'x'
  end
  object Label12: TLabel
    Left = 134
    Top = 215
    Width = 6
    Height = 13
    Caption = 'x'
  end
  object Label14: TLabel
    Left = 205
    Top = 215
    Width = 13
    Height = 13
    Caption = 'cm'
  end
  object Label8: TLabel
    Left = 92
    Top = 169
    Width = 11
    Height = 13
    Caption = 'kg'
  end
  object Label9: TLabel
    Left = 196
    Top = 169
    Width = 11
    Height = 13
    Caption = 'kg'
  end
  object AnzVPEEdit: TEdit
    Left = 8
    Top = 120
    Width = 80
    Height = 21
    TabOrder = 0
    Text = 'AnzVPEEdit'
    OnKeyPress = IntEditKeyPress
  end
  object NettoEdit: TEdit
    Left = 8
    Top = 166
    Width = 80
    Height = 21
    TabOrder = 1
    Text = 'NettoEdit'
    OnKeyPress = FloatEditKeyPress
  end
  object BruttoEdit: TEdit
    Left = 112
    Top = 166
    Width = 80
    Height = 21
    TabOrder = 2
    Text = 'BruttoEdit'
    OnKeyPress = FloatEditKeyPress
  end
  object OkButton: TButton
    Left = 248
    Top = 285
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    Default = True
    ModalResult = 1
    TabOrder = 7
  end
  object AbortButton: TButton
    Left = 329
    Top = 285
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 8
  end
  object LTLength: TEdit
    Left = 8
    Top = 212
    Width = 50
    Height = 21
    MaxLength = 4
    TabOrder = 3
    Text = 'LTLength'
  end
  object LTWidth: TEdit
    Left = 78
    Top = 212
    Width = 50
    Height = 21
    MaxLength = 4
    TabOrder = 4
    Text = 'LTWidth'
  end
  object LTHeigth: TEdit
    Left = 148
    Top = 212
    Width = 50
    Height = 21
    MaxLength = 4
    TabOrder = 5
    Text = 'LTHeigth'
  end
  object SperrgutCheckBox: TCheckBox
    Left = 8
    Top = 250
    Width = 396
    Height = 17
    Caption = 'Als Sperrgut versenden'
    TabOrder = 6
  end
end
