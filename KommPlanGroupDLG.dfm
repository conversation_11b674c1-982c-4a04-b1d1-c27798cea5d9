object KommPlanGroupForm: TKommPlanGroupForm
  Left = 0
  Top = 0
  Caption = 'Kommissionier Planungs Gruppen'
  ClientHeight = 482
  ClientWidth = 838
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    838
    482)
  PixelsPerInch = 96
  TextHeight = 13
  object Label4: TLabel
    Left = 8
    Top = 8
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Label3: TLabel
    Left = 8
    Top = 64
    Width = 41
    Height = 13
    Caption = 'Gruppen'
  end
  object KommPlanGroupDBGrid: TDBGridPro
    Left = 8
    Top = 80
    Width = 705
    Height = 329
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = KommPlanGroupDataSource
    Options = [dgTit<PERSON>, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\CS'
    RegistrySection = 'DBGrids'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object NewPlaceButton: TButton
    Left = 719
    Top = 80
    Width = 112
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Neue Gruppe...'
    TabOrder = 1
    OnClick = NewPlaceButtonClick
  end
  object EditPlaceButton: TButton
    Left = 719
    Top = 115
    Width = 112
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Bearbeiten...'
    TabOrder = 2
    OnClick = EditPlaceButtonClick
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 822
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 3
    OnChange = LagerComboBoxChange
  end
  object DelPlaceButton: TButton
    Left = 719
    Top = 384
    Width = 112
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Gruppe l'#246'schen...'
    TabOrder = 4
  end
  object CloseButton: TButton
    Left = 719
    Top = 449
    Width = 112
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 5
  end
  object KommPlanGroupDataSource: TDataSource
    DataSet = KommPlanGroupQuery
    Left = 448
    Top = 88
  end
  object KommPlanGroupQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 480
    Top = 88
  end
end
