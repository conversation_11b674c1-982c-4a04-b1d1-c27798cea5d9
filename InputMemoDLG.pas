﻿unit InputMemoDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Vcl.Menus, Vcl.Grids, StringGridPro, VCLUtilitys;

type
  TInputMemoForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    InputMemo: TMemo;
    InputLabel: TLabel;
    erfassteSeriennummernLabel: TLabel;
    InputMemoLabel: TLabel;
    SNStringGridPro: TStringGridPro;
    PopupMenu1: TPopupMenu;
    Lschen1: TMenuItem;
    procedure FormCreate(Sender: TObject);
    procedure Lschen1Click(Sender: TObject);

    procedure FillSNStringGrid;
    procedure FormShow(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses FrontendDatasets, LVSDatenInterface, FrontendMessages, DatenModul, ADODB, ConfigModul;

procedure TInputMemoForm.FormCreate(Sender: TObject);
begin
  InputLabel.Caption := '';
  InputMemo.Text := '';
end;

procedure TInputMemoForm.FormShow(Sender: TObject);
begin
  LVSConfigModul.ConfigForm (Self);

  if SNStringGridPro.Visible then begin
    erfassteSeriennummernLabel.Caption := 'Bereits erfasste Seriennummern:';
    InputMemoLabel.Caption := 'Füge eine Seriennummer hinzu:';
  end else begin
    InputMemoLabel.Visible := false;
    erfassteSeriennummernLabel.Visible := false;

    InputMemo.Left  := 8;
    InputMemo.Width := InputMemo.Left + ClientWidth - (InputMemo.Left * 2);
  end;
end;

procedure TInputMemoForm.FillSNStringGrid();
var
  query: TADOQuery;
  idx: integer;
begin

  SNStringGridPro.RowCount := SNStringGridPro.FixedRows + 1;
  SNStringGridPro.Row := SNStringGridPro.FixedRows;
  SNStringGridPro.Rows[SNStringGridPro.FixedRows].Clear;

  query := TADOQuery.Create(Self);
  idx := 0;
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add
      ('select INFO_TEXT, akpi.REF from V_AUFTRAG_KOMM_POS_INFO akpi, V_AUFTRAG_KOMM_POS_01 akp where INFO_ART=''SERIAL'' and akpi.REF_AUF_KOMM_POS=akp.REF and akp.REF_NVE_BESTAND=:ref_nve');
    query.Parameters[0].Value := FrontendDataModule.WANVEBestandDataSet.FieldByName('REF')
      .AsInteger;

    query.Open;

    while not(query.Eof) do begin
      SNStringGridPro.RowCount := SNStringGridPro.FixedRows + idx + 1;

      SNStringGridPro.Rows[SNStringGridPro.FixedRows + idx].BeginUpdate;
      try
        // Laufende Nummer
        SNStringGridPro.Cells[0, SNStringGridPro.FixedRows + idx] := IntToStr(idx + 1);
        // Seriennummer
        SNStringGridPro.Cells[1, SNStringGridPro.FixedRows + idx] := query.Fields[0].AsString;

        SNStringGridPro.Rows[SNStringGridPro.FixedRows + idx].Objects[0] :=
          TGridRef.Create(query.Fields[1].AsInteger);

      finally
        SNStringGridPro.Rows[SNStringGridPro.FixedRows + idx].EndUpdate;
        SNStringGridPro.SetGridOptimalColWidth();
      end;

      Inc(idx);
      query.Next;
    end;

    query.Close;
  finally
    query.Free;
  end;
end;

procedure TInputMemoForm.Lschen1Click(Sender: TObject);
var
  ref, res: integer;
begin
  if Assigned(SNStringGridPro.Rows[SNStringGridPro.Row].Objects[0]) then begin
    ref := TGridRef(SNStringGridPro.Rows[SNStringGridPro.Row].Objects[0]).ref;

    if (FrontendMessages.MessageDLG('Willst du die Seriennummer wirklich löschen?', mtConfirmation,
      [mbYes, mbNo, mbCancel], 0) = mrYes) then begin

      res := LVSDatenInterface.DeleteNVEBestandSerial
        (FrontendDataModule.WANVEBestandDataSet.FieldByName('REF_NVE').AsInteger,
        FrontendDataModule.WANVEBestandDataSet.FieldByName('ARTIKEL_NR').AsString, ref.ToString);
      if (res <> 0) then begin
        FrontendMessages.MessageDLG('Fehler beim Löschen der Seriennummer' + #13 + #13 +LVSDatenModul.LastLVSErrorText, mtError,[mbOK], 0)
      end else begin
        FillSNStringGrid();
        SNStringGridPro.Refresh;
      end;

    end;

  end;
end;

end.
