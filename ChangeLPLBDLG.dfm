object ChangeLPLBForm: TChangeLPLBForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Bereich des Lagerplatzs '#228'ndern'
  ClientHeight = 136
  ClientWidth = 375
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  DesignSize = (
    375
    136)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 94
    Height = 13
    Caption = 'Neuer Lagerbereich'
  end
  object Label2: TLabel
    Left = 8
    Top = 56
    Width = 154
    Height = 13
    Caption = 'Neuer Lagerzone in dem Bereich'
  end
  object LBComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 357
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 0
    OnChange = LBComboBoxChange
  end
  object OkButton: TButton
    Left = 208
    Top = 107
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 2
  end
  object AbortButton: TButton
    Left = 292
    Top = 107
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 3
  end
  object LBZoneComboBox: TComboBoxPro
    Left = 8
    Top = 72
    Width = 357
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 1
  end
end
