unit WENVEDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, Grids, StringGridPro;

type
  TWENVEForm = class(TForm)
    StringGridPro1: TStringGridPro;
    Button1: TButton;
    Button2: TButton;
    Label1: TLabel;
    StaticText1: TStaticText;
    Label2: TLabel;
    StaticText2: TStaticText;
    Bevel1: TBevel;
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

end.
