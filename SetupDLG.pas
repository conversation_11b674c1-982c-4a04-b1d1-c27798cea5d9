﻿{$WARN UNIT_PLATFORM OFF}
{$WARN SYMBOL_PLATFORM OFF}
unit SetupDLG;

{$i compilers.inc}

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, CompTranslate, StdCtrls, ComCtrls, ExtColorCombo, ExtCtrls, DB,
  ADODB, ComboBoxPro, Buttons, VCLUtilitys, AdvFontCombo, Menus

  {$if CompilerVersion > 30.0}
    , vcl.Themes
  {$ifend}
  ;

type
  TPrinterType = class (TComboboxRef)
    PrtString : String;
    PrtName   : String;
    PrtPort   : String;
    PrtType   : String;

    constructor Create (const pRefPrt : Integer; const pPrtStr, pPrtName, pPrtPort, pPrtType : String);
  end;

  TComboboxPackplatz = class (TComboboxRef)
    RefPtL       : Integer;

    constructor Create (const pRefPack, pRefPtL : Integer);
  end;

  TSetupForm = class(TForm)
    Button1: TButton;
    Button2: TButton;
    CompTranslateForm1: TCompTranslateForm;
    Label7: TLabel;
    Label8: TLabel;
    PageControl1: TPageControl;
    TabSheet1: TTabSheet;
    TabSheet2: TTabSheet;
    ScannerEnableCheckBox: TCheckBox;
    ScannerComboBox: TComboBox;
    PrinterTabSheet: TTabSheet;
    GroupBox1: TGroupBox;
    StdPrinterComboBox: TComboBoxPro;
    LabelPrtGroupBox: TGroupBox;
    Label6: TLabel;
    NVELabelComboBox: TComboBoxPro;
    TabSheet4: TTabSheet;
    GroupBox4: TGroupBox;
    TouchCheckBox: TCheckBox;
    ScreenKeysCheckBox: TCheckBox;
    GroupBox5: TGroupBox;
    StatIconCheckBox: TCheckBox;
    Label5: TLabel;
    VerladePrinterComboBox: TComboBoxPro;
    Label13: TLabel;
    Label12: TLabel;
    VPELabelComboBox: TComboBoxPro;
    Label14: TLabel;
    KommPrinterComboBox: TComboBoxPro;
    Label15: TLabel;
    LieferPrintComboBox: TComboBoxPro;
    Label16: TLabel;
    WEPALLabelComboBox: TComboBoxPro;
    Label18: TLabel;
    AutoWEPALLabelComboBox: TComboBoxPro;
    Label17: TLabel;
    KommNVEComboBox: TComboBoxPro;
    PrinterLocLabel: TLabel;
    PrinterLocBevel: TBevel;
    ScanErrSoundComboBox: TComboBox;
    Button3: TButton;
    PlayScanErrSoundButton: TBitBtn;
    Label19: TLabel;
    PrintingCheckBox: TCheckBox;
    Label20: TLabel;
    KommLSComboBox: TComboBoxPro;
    LeitstandTabSheet: TTabSheet;
    LeitstandEdit: TEdit;
    Label22: TLabel;
    ADOQuery1: TADOQuery;
    TabSheet5: TTabSheet;
    SortGroupBox: TGroupBox;
    SortLieferantRadioGroup: TRadioGroup;
    SortEmpfRadioGroup: TRadioGroup;
    Label26: TLabel;
    RechungPrintComboBox: TComboBoxPro;
    RechungBinComboBox: TComboBoxPro;
    LieferBinComboBox: TComboBoxPro;
    StdBinComboBox: TComboBoxPro;
    KommBinComboBox: TComboBoxPro;
    VerladeBinComboBox: TComboBoxPro;
    WEScanForegroundCheckBox: TCheckBox;
    SortLPRadioGroup: TRadioGroup;
    GroupBox7: TGroupBox;
    Label36: TLabel;
    Label37: TLabel;
    SubTabFontSizeEdit: TEdit;
    SubTabFontColorCombobox: TExtColorCombo;
    SubTabFontBoldCheckbox: TCheckBox;
    SubTabFontSizeUpDown: TUpDown;
    Label31: TLabel;
    SubTabHeightEdit: TEdit;
    SubTabHeightUpDown: TUpDown;
    GroupBox6: TGroupBox;
    Label32: TLabel;
    Label33: TLabel;
    Label30: TLabel;
    MainTabFontSizeEdit: TEdit;
    MainTabFontColorCombobox: TExtColorCombo;
    CheckBox3: TCheckBox;
    MainTabFontBoldCheckbox: TCheckBox;
    MainTabHeightEdit: TEdit;
    MainTabFontSizeUpDown: TUpDown;
    MainTabHeightUpDown: TUpDown;
    WEParamGroupBox: TGroupBox;
    WEAutoJumpCheckBox: TCheckBox;
    GridFetchAllCheckBox: TCheckBox;
    GridAttrGroupBox: TGroupBox;
    CheckBox1: TCheckBox;
    Label1: TLabel;
    OddColorBox: TColorBox;
    Label2: TLabel;
    EvenColorBox: TColorBox;
    Label3: TLabel;
    FontSizeEdit: TEdit;
    Label4: TLabel;
    TextColorCombo: TExtColorCombo;
    GroupBox3: TGroupBox;
    Label9: TLabel;
    Label10: TLabel;
    Label11: TLabel;
    TitleSizeEdit: TEdit;
    TitleSizeUpDown: TUpDown;
    TitleColorCombo: TExtColorCombo;
    TitleWrapCheckBox: TCheckBox;
    TitleBackgndCombo: TExtColorCombo;
    FontSizeUpDown: TUpDown;
    Label38: TLabel;
    HiddenSelColorCombobox: TColorBox;
    Konfiguartion: TPageControl;
    LeitstandCfgTabSheet: TTabSheet;
    PrinterGroupBox: TGroupBox;
    Label21: TLabel;
    Label23: TLabel;
    LeitstandLaserComboBox: TComboBoxPro;
    LeitstandLabelComboBox: TComboBoxPro;
    WEGroupBox: TGroupBox;
    Label34: TLabel;
    Label35: TLabel;
    WELBComboBox: TComboBoxPro;
    RETLBComboBox: TComboBoxPro;
    PackGroupBox: TGroupBox;
    Label27: TLabel;
    Label28: TLabel;
    Label29: TLabel;
    PackplatzComboBox: TComboBoxPro;
    PackDisplayWaitTimeEdit: TEdit;
    PicktoLightGroupBox: TGroupBox;
    Label24: TLabel;
    Label25: TLabel;
    PickByLightColorComboBox: TComboBox;
    PickByLightStationComboBox: TComboBoxPro;
    LeitstandMsgTagSheet: TTabSheet;
    LeitMsgAufPopupCheckBox: TCheckBox;
    Label39: TLabel;
    ZollPrintComboBox: TComboBoxPro;
    ZollBinComboBox: TComboBoxPro;
    LeitMsgCheckInvPopupCheckBox: TCheckBox;
    SortUserRadioGroup: TRadioGroup;
    GridFontSelector: TAdvFontSelector;
    Label40: TLabel;
    TitleFontSelector: TAdvFontSelector;
    Label41: TLabel;
    Label42: TLabel;
    PaketLabelCombobox: TComboBoxPro;
    Label43: TLabel;
    BestandLabelCombobox: TComboBoxPro;
    GridAutoUpdateCheckBox: TCheckBox;
    PrinterPopupMenu: TPopupMenu;
    PrtPropertiesMenuItem: TMenuItem;
    GroupBox2: TGroupBox;
    Label45: TLabel;
    HighlightKommPosColorCombobox: TExtColorCombo;
    HighlightKommPosCheckbox: TCheckBox;
    StyleComboBox: TComboBox;
    Label44: TLabel;
    LeitMsgAutoCheckBox: TCheckBox;
    procedure FormKeyPress(Sender: TObject; var Key: Char);
    procedure CheckBox1Click(Sender: TObject);
    procedure ScannerEnableCheckBoxClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure PrinterTabSheetShow(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure TabSheet2Show(Sender: TObject);
    procedure PlayScanErrSoundButtonClick(Sender: TObject);
    procedure ScanErrSoundComboBoxChange(Sender: TObject);
    procedure LeitstandTabSheetShow(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure RechungBinComboBoxDropDown(Sender: TObject);
    procedure LieferBinComboBoxDropDown(Sender: TObject);
    procedure VerladeBinComboBoxDropDown(Sender: TObject);
    procedure KommBinComboBoxDropDown(Sender: TObject);
    procedure StdBinComboBoxDropDown(Sender: TObject);
    procedure PackDisplayWaitTimeEditKeyPress(Sender: TObject; var Key: Char);
    procedure PickByLightStationComboBoxChange(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure KonfiguartionChange(Sender: TObject);
    procedure ZollBinComboBoxDropDown(Sender: TObject);
    procedure PrtPropertiesMenuItemClick(Sender: TObject);
    procedure StyleComboBoxChange(Sender: TObject);
    procedure TabSheet4Show(Sender: TObject);
  private
    fScanErrSound : String;
    fLeitstandTabSheetLoaded : Boolean;

    function ReloadPrinters : Integer;
    function ReloadLeitstandPrinters : Integer;
    function LoadBinInfo (const PrinterName : String; ComboBox : TComboBoxPro) : Integer;
  public
    StdPrinter       : String;
    StdPrinterBin    : String;
    KommPrinter      : String;
    KommPrinterBin   : String;
    ColliPrinter     : String;
    ColliPrinterBin  : String;
    REPrinter        : String;
    REPrinterBin     : String;
    LSPrinter        : String;
    LSPrinterBin     : String;
    VPEPrinter       : String;
    NVEPrinter       : String;
    WEPALPrinter     : String;
    ZollPrinter      : String;
    ZollPrinterBin   : String;
    AutoWEPALPrinter : String;
    KommNVEPrinter   : String;
    KommLSPrinter    : String;
    PaketPrinter     : String;
    BestandPrinter   : String;

    LeitstandLaserPrinter : String;
    LeitstandLabelPrinter : String;

    LeitstandWEStation   : String;
    LeitstandWEPlatz     : String;
    LeitstandRETStation  : String;
    LeitstandRETPlatz    : String;
    LeitstandPackStation : String;
    LeitstandPickStation : String;
    LeitstandPickColor   : Integer;

    property ScanErrSound            : String  read fScanErrSound write fScanErrSound;
    property LeitstandTabSheetLoaded : Boolean read fLeitstandTabSheetLoaded;

    procedure Prepare;
  end;

var
  SetupForm: TSetupForm;

implementation

uses
  {$ifdef Trace}
     Trace,
  {$endif}

  CmdLineUtils,

  SprachModul, Printers, WinSpool, BarCodeScanner, LVSGlobalDaten, DatenModul, FrontendImageModule, mmSystem,
  FrontendUtils, PrinterUtils, ConfigModul, TerminalServices,

  {$ifdef Numpad}
    Numpad,
  {$endif}

  LVSDatenInterface, PrintModul, LVSPrinterInterface, LVSFrontEndMain, ResourceText,
  FrontendMessages;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboboxPackplatz.Create (const pRefPack, pRefPtL : Integer);
begin
  inherited Create (pRefPack);

  RefPtL   := pRefPtL;
end;

constructor TPrinterType.Create (const pRefPrt : Integer; const pPrtStr, pPrtName, pPrtPort, pPrtType : String);
begin
  inherited Create (pRefPrt);

  PrtName   := pPrtName;
  PrtPort   := pPrtPort;
  PrtType   := pPrtType;
  PrtString := pPrtStr;
end;

function GetComboboxPrinterString (Combobox : TComboBoxPro) : String;
begin
  if (Combobox.ItemIndex <> -1) and Assigned (Combobox.Items.Objects [Combobox.ItemIndex]) then
    Result := TPrinterType (Combobox.Items.Objects [Combobox.ItemIndex]).PrtString
  else
    Result := ''
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.Prepare;
var
  idx,
  nveprtref: Integer;
  strwert  : string;
  flag     : Boolean;
  res,
  binr,
  prtref   : Integer;
  prtdaten : TPrinterPorts;
begin
  ScannerComboBox.Clear;

  idx := 0;
  while (idx <= High (ScannerModel)) do begin
    ScannerComboBox.Items.Add (ScannerModel [idx]);

    Inc (idx);
  end;

  ClearComboBoxObjects (StdPrinterComboBox);
  ClearComboBoxObjects (StdBinComboBox);
  ClearComboBoxObjects (KommPrinterComboBox);
  ClearComboBoxObjects (KommBinComboBox);
  ClearComboBoxObjects (VerladePrinterComboBox);
  ClearComboBoxObjects (VerladeBinComboBox);
  ClearComboBoxObjects (LieferPrintComboBox);
  ClearComboBoxObjects (LieferBinComboBox);
  ClearComboBoxObjects (RechungPrintComboBox);
  ClearComboBoxObjects (RechungBinComboBox);
  ClearComboBoxObjects (ZollPrintComboBox);
  ClearComboBoxObjects (ZollBinComboBox);
  ClearComboBoxObjects (NVELabelComboBox);
  ClearComboBoxObjects (VPELabelComboBox);
  ClearComboBoxObjects (WEPALLabelComboBox);
  ClearComboBoxObjects (AutoWEPALLabelComboBox);
  ClearComboBoxObjects (KommNVEComboBox);
  ClearComboBoxObjects (KommLSComboBox);
  ClearComboBoxObjects (PaketLabelCombobox);
  ClearComboBoxObjects (BestandLabelCombobox);

  MainTabHeightUpDown.Position      := MainPagerSetup.TabHeight;
  MainTabFontSizeUpDown.Position    := MainPagerSetup.FontSize;
  MainTabFontBoldCheckbox.Checked   := MainPagerSetup.FontBold;
  MainTabFontColorCombobox.SelColor := MainPagerSetup.FontColor;

  SubTabHeightUpDown.Position      := SubPagerSetup.TabHeight;
  SubTabFontSizeUpDown.Position    := SubPagerSetup.FontSize;
  SubTabFontBoldCheckbox.Checked   := SubPagerSetup.FontBold;
  SubTabFontColorCombobox.SelColor := SubPagerSetup.FontColor;

  if (UserReg.ReadRegValue ('WEScanForeground', flag) <> 0) then
    flag := False;
  WEScanForegroundCheckBox.Checked := flag;

  if (UserReg.ReadRegValue ('WEAutoJump', flag) <> 0) then
    flag := True;
  WEAutoJumpCheckBox.Checked := flag;

  UserReg.ReadRegValue('HighlightKommPos', flag, false);
    HighlightKommPosCheckbox.Checked := flag;

  if (UserReg.ReadRegValue('HighlightKommPosColor', strwert) = 0) and (strwert <> '') then
    HighlightKommPosColorCombobox.SelColor := StringToColor(strwert);

  GridFetchAllCheckBox.Checked := LVSConfigModul.DBGrids.GridFetchAll;
  GridAutoUpdateCheckBox.Checked := LVSConfigModul.DBGrids.GridAutoUpdate;

  CheckBox1.Checked := LVSConfigModul.DBGrids.AlternateColors;

  if (Length (LVSConfigModul.DBGrids.FontName) > 0) then
    GridFontSelector.Text := LVSConfigModul.DBGrids.FontName
  else begin
    {$ifdef UNICODE}
      GridFontSelector.Text := 'Lucida Sans Unicode';
    {$else}
      GridFontSelector.Text := 'MS Sans Serif';
    {$endif}
  end;

  if (Length (LVSConfigModul.DBGrids.TitleFont) > 0) then
    TitleFontSelector.Text := LVSConfigModul.DBGrids.TitleFont
  else begin
    {$ifdef UNICODE}
      TitleFontSelector.Text := 'Lucida Sans Unicode';
    {$else}
      TitleFontSelector.Text := 'MS Sans Serif';
    {$endif}

  end;

  FontSizeUpDown.Position := LVSConfigModul.DBGrids.FontSize;
  TextColorCombo.SelColor := LVSConfigModul.DBGrids.TextColor;
  HiddenSelColorCombobox.Selected := LVSConfigModul.DBGrids.HiddenSelColor;
  EvenColorBox.Selected := LVSConfigModul.DBGrids.EvenColor;
  OddColorBox.Selected := LVSConfigModul.DBGrids.OddColor;

  TitleWrapCheckBox.Checked := LVSConfigModul.DBGrids.TitleWrap;
  TitleSizeUpDown.Position := LVSConfigModul.DBGrids.TitleSize;
  TitleColorCombo.SelColor := LVSConfigModul.DBGrids.TitleColor;

  StatIconCheckBox.Checked := LVSConfigModul.FrontendConfig.cfgStatIcons;
  ScanErrSound := LVSConfigModul.FrontendConfig.ScanErrSound;

  if (ScannerTyp = -1) then
    ScannerEnableCheckBox.Checked := False
  else begin
    ScannerEnableCheckBox.Checked := True;
    ScannerComboBox.ItemIndex := ScannerTyp;
  end;

  TouchCheckBox.Checked := (aoTouch in AppOptions);

  {$ifdef Numpad}
    ScreenKeysCheckBox.Checked := NumericKeypad.Visible;
  {$else}
    ScreenKeysCheckBox.Visible := False;
    ScreenKeysCheckBox.Checked := False;
  {$endif}

  PrintingCheckBox.Checked := LVSConfigModul.FrontendConfig.cfgUsePrintingProcess;

  LeitstandTabSheet.TabVisible := LVSConfigModul.UseLeitstand;

  if not (LVSDatenModul.MainADOConnection.Connected) then begin
    PrinterTabSheet.Enabled := False;
    LeitstandTabSheet.Enabled := False;
  end else begin
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'STD_PRINTER',     StdPrinter);
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'STD_PRINTER_BIN', binr, StdPrinterBin);
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'KOMM_PRINTER',    KommPrinter);
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'COLLI_PRINTER',   ColliPrinter);
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'RE_PRINTER',      REPrinter);
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'RE_PRINTER_BIN',  binr, REPrinterBin);
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'LS_PRINTER',      LSPrinter);
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'LS_PRINTER_BIN',  binr, LSPrinterBin);
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'ZOLL_PRINTER',    ZollPrinter);
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'ZOLL_PRINTER_BIN',binr, ZollPrinterBin);
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'VPE_PRINTER',     VPEPrinter);
    ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'NVE_PRINTER',     nveprtref, NVEPrinter);

    if (LVSConfigModul.UseLeitstand) then begin
      if (LVSConfigModul.RefLeitstand = -1) then begin
        LeitstandEdit.Enabled := True;
        LeitstandEdit.Text := '';
      end else begin
        LeitstandEdit.Enabled := False;

        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ('select * from V_LEITSTAND where REF=:ref');
        ADOQuery1.Parameters [0].Value := LVSConfigModul.RefLeitstand;

        ADOQuery1.Open;

        LeitstandEdit.Text := ADOQuery1.FieldByName('NAME').AsString;

        if not Assigned (ADOQuery1.FindField ('OPT_POPUP')) then
          LeitstandMsgTagSheet.TabVisible := False
        else begin
          LeitMsgAufPopupCheckBox.Checked      := CheckOpt (ADOQuery1.FieldByName('OPT_POPUP').AsString, 1);
          LeitMsgCheckInvPopupCheckBox.Checked := CheckOpt (ADOQuery1.FieldByName('OPT_POPUP').AsString, 2);
          LeitMsgAutoCheckBox.Checked          := CheckOpt (ADOQuery1.FieldByName('OPT_POPUP').AsString, 3);
        end;

        ADOQuery1.Close;

        ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'LASER_PRINTER', LeitstandLaserPrinter);
        ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'LABEL_PRINTER', LeitstandLabelPrinter);

        ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'WE_BEREICH', LeitstandWEStation);
        ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'WE_PLATZ',   LeitstandWEPlatz);
        ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'RET_BEREICH', LeitstandRETStation);
        ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'RET_PLATZ',   LeitstandRETPlatz);
        ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'PACK_PLATZ', LeitstandPackStation);

        ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'PICK_TO_LIGHT_STATION', LeitstandPickStation);

        if (ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'PICK_TO_LIGHT_COLOR', strwert) <> 0) then
          LeitstandPickColor := -1
        else if (Length (strwert) = 0) then
          LeitstandPickColor := -1
        else if not TryStrToInt (strwert, LeitstandPickColor) then
          LeitstandPickColor := -1;
      end;
    end;

    res := PrintModule.DetectPrinter ('WEPAL-LABEL', -1, prtref, prtdaten);
    if (res = 0) and (prtref > 0) then
      WEPALPrinter := IntToStr (prtref)
    else if (Length (prtdaten.PrtTyp) > 0) then
      WEPALPrinter := prtdaten.Port
    else
      WEPALPrinter := '';

    res := PrintModule.DetectPrinter ('AUTO-WEPAL-LABEL', -1, prtref, prtdaten);
    if (res = 0) and (prtref > 0) then
      AutoWEPALPrinter := IntToStr (prtref)
    else if (Length (prtdaten.PrtTyp) > 0) then
      AutoWEPALPrinter := prtdaten.Port
    else
      AutoWEPALPrinter := '';

    res := PrintModule.DetectPrinter ('NVE-LABEL', -1, prtref, prtdaten);
    if (res = 0) and (prtref > 0) then
      KommNVEPrinter := IntToStr (prtref)
    else if (Length (prtdaten.PrtTyp) > 0) then
      KommNVEPrinter := prtdaten.Port
    else
      KommNVEPrinter := '';

    res := PrintModule.DetectPrinter ('KOMM-LS', -1, prtref, prtdaten);
    if (Length (prtdaten.PrtTyp) > 0) then
      KommLSPrinter := prtdaten.Port
    else
      KommLSPrinter := '';

    res := PrintModule.DetectPrinter ('PAKET-LABEL', -1, prtref, prtdaten);
    if (Length (prtdaten.PrtTyp) > 0) then
      PaketPrinter := prtdaten.Port
    else
      PaketPrinter := '';

    res := PrintModule.DetectPrinter ('BESTAND-LABEL', -1, prtref, prtdaten);
    if (Length (prtdaten.PrtTyp) > 0) then
      BestandPrinter := prtdaten.Port
    else
      BestandPrinter := '';
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.FormKeyPress(Sender: TObject; var Key: Char);
begin
  if (Key = Chr (VK_ESCAPE)) then
    ModalResult := mrAbort;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.PackDisplayWaitTimeEditKeyPress(Sender: TObject; var Key: Char);
var
  wert : Integer;
begin
  if not (Key in ['0'..'9', #8,^C,^V]) then
    Key := #0
  else if (Key in ['0'..'9']) then begin
    if not (TryStrToInt((Sender as TEDit).Text + key, wert)) then
      Key := #0
    else if (wert > 9999) then Key := #0
  end;
end;

procedure TSetupForm.KonfiguartionChange(Sender: TObject);
begin

end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.PickByLightStationComboBoxChange(Sender: TObject);
begin
  if (PickByLightStationComboBox.ItemIndex <= 0) or not Assigned (PickByLightStationComboBox.Items.Objects[PickByLightStationComboBox.ItemIndex]) then
    PickByLightColorComboBox.Enabled := False
  else
    PickByLightColorComboBox.Enabled := (TComboboxPackplatz (PickByLightStationComboBox.Items.Objects[PickByLightStationComboBox.ItemIndex]).RefPtL > 0)
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.PlayScanErrSoundButtonClick(Sender: TObject);
begin
  if (Length (fScanErrSound) > 0) then
    PlaySound (fScanErrSound);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.CheckBox1Click(Sender: TObject);
begin
  EvenColorBox.Enabled := CheckBox1.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.ScannerEnableCheckBoxClick(Sender: TObject);
begin
  ScannerComboBox.Enabled := ScannerEnableCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.VerladeBinComboBoxDropDown(Sender: TObject);
begin
  if (VerladePrinterComboBox.ItemIndex <> -1) and Assigned (VerladePrinterComboBox.Items.Objects [VerladePrinterComboBox.ItemIndex]) then begin
    LoadBinInfo (TPrinterType (VerladePrinterComboBox.Items.Objects [VerladePrinterComboBox.ItemIndex]).PrtName, Sender as TComboBoxPro);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.ZollBinComboBoxDropDown(Sender: TObject);
begin
  if (ZollPrintComboBox.ItemIndex <> -1) and Assigned (ZollPrintComboBox.Items.Objects [ZollPrintComboBox.ItemIndex]) then begin
    LoadBinInfo (TPrinterType (ZollPrintComboBox.Items.Objects [ZollPrintComboBox.ItemIndex]).PrtName, Sender as TComboBoxPro);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.StdBinComboBoxDropDown(Sender: TObject);
begin
  if (StdPrinterComboBox.ItemIndex <> -1) and Assigned (StdPrinterComboBox.Items.Objects [StdPrinterComboBox.ItemIndex]) then begin
    LoadBinInfo (TPrinterType (StdPrinterComboBox.Items.Objects [StdPrinterComboBox.ItemIndex]).PrtName, Sender as TComboBoxPro);
  end;
end;

procedure TSetupForm.StyleComboBoxChange(Sender: TObject);
begin
  {$if CompilerVersion > 30.0}
    if (StyleComboBox.ItemIndex = 0) then
      TStyleManager.TrySetStyle('Windows')
    else if (StyleComboBox.ItemIndex = 1) then
      TStyleManager.TrySetStyle('Windows10')
    else if (StyleComboBox.ItemIndex = 2) then
      TStyleManager.TrySetStyle('Windows10 Dark')
    else if (StyleComboBox.ItemIndex = 3) then
      TStyleManager.TrySetStyle('Windows10 SlateGray')
    else if (StyleComboBox.ItemIndex = 4) then
      TStyleManager.TrySetStyle('Windows10 Blue')
    else if (StyleComboBox.ItemIndex = 5) then
      TStyleManager.TrySetStyle('Windows10 Green')
    else if (StyleComboBox.ItemIndex = 6) then
      TStyleManager.TrySetStyle('Windows10 Purple');
  {$ifend}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.RechungBinComboBoxDropDown (Sender: TObject);
begin
  if (RechungPrintComboBox.ItemIndex <> -1) and Assigned (RechungPrintComboBox.Items.Objects [RechungPrintComboBox.ItemIndex]) then begin
    LoadBinInfo (TPrinterType (RechungPrintComboBox.Items.Objects [RechungPrintComboBox.ItemIndex]).PrtName, Sender as TComboBoxPro);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.LieferBinComboBoxDropDown(Sender: TObject);
begin
  if (LieferPrintComboBox.ItemIndex <> -1) and Assigned (LieferPrintComboBox.Items.Objects [LieferPrintComboBox.ItemIndex]) then begin
    LoadBinInfo (TPrinterType (LieferPrintComboBox.Items.Objects [LieferPrintComboBox.ItemIndex]).PrtName, Sender as TComboBoxPro);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.KommBinComboBoxDropDown(Sender: TObject);
begin
  if (KommPrinterComboBox.ItemIndex <> -1) and Assigned (KommPrinterComboBox.Items.Objects [KommPrinterComboBox.ItemIndex]) then begin
    LoadBinInfo (TPrinterType (KommPrinterComboBox.Items.Objects [KommPrinterComboBox.ItemIndex]).PrtName, Sender as TComboBoxPro);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  strpos,
  intwert,
  leitref : Integer;
  optstr,
  portstr,
  prtname  : String;
begin
  if (ModalResult = mrOk) then begin
    MainPagerSetup.TabHeight := MainTabHeightUpDown.Position;
    MainPagerSetup.FontSize  := MainTabFontSizeUpDown.Position;
    MainPagerSetup.FontBold  := MainTabFontBoldCheckbox.Checked;
    MainPagerSetup.FontColor := MainTabFontColorCombobox.SelColor;

    SubPagerSetup.TabHeight := SubTabHeightUpDown.Position;
    SubPagerSetup.FontSize  := SubTabFontSizeUpDown.Position;
    SubPagerSetup.FontBold  := SubTabFontBoldCheckbox.Checked;
    SubPagerSetup.FontColor := SubTabFontColorCombobox.SelColor;

    if StyleComboBox.Visible then begin
      UserReg.WriteRegValue ('Style', StyleComboBox.ItemIndex);
    end;

    if (SortLieferantRadioGroup.ItemIndex <> -1) then
      UserReg.WriteRegValue ('SortLieferant', SortLieferantRadioGroup.ItemIndex);

    if (SortEmpfRadioGroup.ItemIndex <> -1) then
      UserReg.WriteRegValue ('SortEmpf', SortEmpfRadioGroup.ItemIndex);

    if (SortLPRadioGroup.ItemIndex <> -1) then
      UserReg.WriteRegValue ('SortLP', SortLPRadioGroup.ItemIndex);

    if (SortUserRadioGroup.ItemIndex <> -1) then
      UserReg.WriteRegValue ('SortUser', SortUserRadioGroup.ItemIndex);

    if (Length (PackDisplayWaitTimeEdit.Text) = 0) then
      intwert := -1
    else if not (TryStrToInt(PackDisplayWaitTimeEdit.Text, intwert)) then
      intwert := 5;

    UserReg.WriteRegValue ('PackDisplayWaitTimeEdit', intwert);

    UserReg.WriteRegValue ('WEScanForeground', WEScanForegroundCheckBox.Checked);

    UserReg.WriteRegValue ('WEAutoJump', WEAutoJumpCheckBox.Checked);

    UserReg.WriteRegValue('HighlightKommPos', HighlightKommPosCheckbox.Checked);
    UserReg.WriteRegValue('HighlightKommPosColor', ColorToString(HighlightKommPosColorCombobox.SelColor));

    if (LVSConfigModul.UseLeitstand) then begin
      if (LeitstandTabSheetLoaded) then begin
        if LeitstandEdit.Enabled and (Length (LeitstandEdit.Text) > 0) Then begin
          res := CreateLeitstand (LVSDatenModul.AktLocationRef, LeitstandEdit.Text, '', LVSDatenModul.AktClientName, LVSConfigModul.TestFlag, leitref);

          if (res = 0) then
            LVSConfigModul.RefLeitstand  := leitref;
        end;

        if (LeitstandMsgTagSheet.TabVisible) then begin
          optstr := '00000000';

          if LeitMsgAufPopupCheckBox.Checked then
            optstr [1] := '1';

          if LeitMsgCheckInvPopupCheckBox.Checked then
            optstr [2] := '1';

          if LeitMsgAutoCheckBox.Checked then
            optstr [3] := '1';

          res := SetLeitstandPopup (LVSConfigModul.RefLeitstand, optstr);
        end;
      end;
    end;

    if (TouchCheckBox.Checked) then
      AppOptions := AppOptions + [aoTouch]
    else AppOptions := AppOptions - [aoTouch];

    if (ScannerEnableCheckBox.Checked) then
      SetupScanner(SetupForm.ScannerComboBox.ItemIndex)
    else SetupScanner(-1);

    if LVSConfigModul.UseLeitstand and LeitstandTabSheet.Enabled then begin
      if (LeitstandLaserComboBox.ItemIndex <> -1) then
        PrintModule.LeitLaserPrinter.Port := GetComboboxPrinterString (SetupForm.LeitstandLabelComboBox);

      if (LeitstandLabelComboBox.ItemIndex <> -1) then
        PrintModule.LeitLabelPrinter.Port := GetComboboxPrinterString (SetupForm.LeitstandLabelComboBox);
    end;

    if (PrinterTabSheet.Enabled) then begin
      //Drucker nur dann übernehmen, wenn auch wirkliche einer ausgewählt wurde
      if (LieferPrintComboBox.ItemIndex <> -1) then begin
        PrintModule.LSLaserPrinter.Port := GetComboboxPrinterString (LieferPrintComboBox);

        if (SetupForm.LieferBinComboBox.ItemIndex <> -1) then
          PrintModule.LSLaserPrinter.BinNr := GetComboBoxRef (LieferBinComboBox);
      end;

      if (SetupForm.StdPrinterComboBox.ItemIndex <> -1) then begin
        PrintModule.StdLaserPrinter.Port  := GetComboboxPrinterString (StdPrinterComboBox);

        if (SetupForm.StdBinComboBox.ItemIndex <> -1) then
          PrintModule.StdLaserPrinter.BinNr := GetComboBoxRef (StdBinComboBox);
      end;

      if (SetupForm.RechungPrintComboBox.ItemIndex <> -1) then begin
        PrintModule.RELaserPrinter.Port  := GetComboboxPrinterString (RechungPrintComboBox);

        if (SetupForm.RechungBinComboBox.ItemIndex <> -1) then
          PrintModule.RELaserPrinter.BinNr := GetComboBoxRef (RechungBinComboBox);
      end;

      if (SetupForm.ZollPrintComboBox.ItemIndex <> -1) then begin
        PrintModule.ZollLaserPrinter.Port  := GetComboboxPrinterString (ZollPrintComboBox);

        if (SetupForm.ZollBinComboBox.ItemIndex <> -1) then
          PrintModule.ZollLaserPrinter.BinNr := GetComboBoxRef (ZollBinComboBox);
      end;

      if (SetupForm.KommPrinterComboBox.ItemIndex <> -1) then
        PrintModule.KommLaserPrinter.Port := GetComboboxPrinterString (KommPrinterComboBox);

      if (SetupForm.VerladePrinterComboBox.ItemIndex <> -1) then
        PrintModule.VerladeLaserPrinter.Port := GetComboboxPrinterString (VerladePrinterComboBox);

      if (SetupForm.NVELabelComboBox.ItemIndex <> -1) then begin
        PrintModule.NVELabelPrinter.Ref  := GetComboBoxRef (NVELabelComboBox);

        prtname := GetComboboxPrinterString (NVELabelComboBox);

        strpos := Pos (';', prtname);

        if (strpos > 0) then
          prtname := Copy (prtname, 1, strpos - 1);

        PrintModule.NVELabelPrinter.Name := prtname;

        if (PrintModule.VPELabelPrinter.Ref = -1) then
          PrintModule.NVELabelPrinter.Port := PrintModule.NVELabelPrinter.Name;
      end;

      if (SetupForm.VPELabelComboBox.ItemIndex <> -1) then begin
        PrintModule.VPELabelPrinter.Ref  := GetComboBoxRef (VPELabelComboBox);

        prtname := GetComboboxPrinterString (VPELabelComboBox);

        strpos := Pos (';', prtname);

        if (strpos > 0) then
          prtname := Copy (prtname, 1, strpos - 1);

        if (PrintModule.VPELabelPrinter.Ref = -1) then
          PrintModule.VPELabelPrinter.Port := PrintModule.VPELabelPrinter.Name;
      end;

      //Wenn nicht angeben ist, die Leitstandsangaben übernehmen
      if (Length (PrintModule.StdLaserPrinter.Port) = 0) then
        PrintModule.StdLaserPrinter.Port :=  PrintModule.LeitLaserPrinter.Port;

      if (Length (PrintModule.LSLaserPrinter.Port) = 0) then
        PrintModule.LSLaserPrinter.Port :=  PrintModule.LeitLaserPrinter.Port;

      if (Length (PrintModule.RELaserPrinter.Port) = 0) then
        PrintModule.RELaserPrinter.Port :=  PrintModule.LeitLaserPrinter.Port;

      if (Length (PrintModule.KommLaserPrinter.Port) = 0) then
        PrintModule.KommLaserPrinter.Port  := PrintModule.LeitLaserPrinter.Port;

      if (Length (PrintModule.VerladeLaserPrinter.Port) = 0) then
        PrintModule.VerladeLaserPrinter.Port := PrintModule.LeitLaserPrinter.Port;


      if (Length (PrintModule.NVELabelPrinter.Name) = 0) then
        PrintModule.NVELabelPrinter.Name := PrintModule.LeitLabelPrinter.Name;

      if (Length (PrintModule.VPELabelPrinter.Name) = 0) then
        PrintModule.VPELabelPrinter.Name := PrintModule.LeitLabelPrinter.Name;
    end;

    if (LVSDatenModul.MainADOConnection.Connected) then begin
      optstr := '';

      if (SetupForm.TouchCheckBox.Checked) then begin
        while (Length(optstr) < 1) do optstr := optstr + '0';

        optstr[1] := '1';
      end;

      if (SetupForm.ScreenKeysCheckBox.Checked) then begin
        while (Length(optstr) < 2) do optstr := optstr + '0';

        optstr[2] := '1';
      end;

      LVSDatenModul.BeginTransaction (LVSDatenModul.MainADOConnection);

      try
        ChangeDeviceConfig (LVSDatenModul.AktConfigName, ScannerTyp, optstr);

        if (SetupForm.StdPrinterComboBox.ItemIndex <> -1) then begin
          ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'STD_PRINTER', GetComboboxPrinterString (StdPrinterComboBox));

          if (SetupForm.StdBinComboBox.ItemIndex <> -1) then
            ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'STD_PRINTER_BIN', GetComboBoxRef (StdBinComboBox), StdBinComboBox.Text);
        end;

        if (SetupForm.LieferPrintComboBox.ItemIndex <> -1) then begin
          ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'LS_PRINTER', GetComboboxPrinterString (LieferPrintComboBox));

          if (SetupForm.LieferBinComboBox.ItemIndex <> -1) then
            ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'LS_PRINTER_BIN', GetComboBoxRef (LieferBinComboBox), LieferBinComboBox.Text);
        end;

        if (RechungPrintComboBox.ItemIndex <> -1) then begin
          ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'RE_PRINTER', GetComboboxPrinterString (RechungPrintComboBox));

          if (RechungBinComboBox.ItemIndex <> -1) then
            ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'RE_PRINTER_BIN', GetComboBoxRef (RechungBinComboBox), SetupForm.RechungBinComboBox.Text);
        end;

        if (ZollPrintComboBox.ItemIndex <> -1) then begin
          ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'ZOLL_PRINTER', GetComboboxPrinterString (ZollPrintComboBox));

          if (ZollBinComboBox.ItemIndex <> -1) then
            ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'ZOLL_PRINTER_BIN', GetComboBoxRef (ZollBinComboBox), SetupForm.ZollBinComboBox.Text);
        end;

        if (KommPrinterComboBox.ItemIndex <> -1) then  begin
          ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'KOMM_PRINTER', GetComboboxPrinterString (KommPrinterComboBox));

          if (KommBinComboBox.ItemIndex <> -1) then
            ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'KOMM_PRINTER_BIN', GetComboBoxRef (KommBinComboBox), KommBinComboBox.Text);
        end;

        if (VerladePrinterComboBox.ItemIndex <> -1) then begin
          ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'COLLI_PRINTER', GetComboboxPrinterString (VerladePrinterComboBox));

          if (VerladeBinComboBox.ItemIndex <> -1) then
            ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'COLLI_PRINTER_BIN', GetComboBoxRef (VerladeBinComboBox), VerladeBinComboBox.Text);
        end;

        if (NVELabelComboBox.ItemIndex <> -1) then begin
          if (GetComboBoxRef (NVELabelComboBox) <> -1) then
            ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'NVE_PRINTER', GetComboBoxRef (NVELabelComboBox), GetComboboxPrinterString (NVELabelComboBox))
          else begin
            strpos := Pos (';', NVELabelComboBox.GetItemText);

            if (strpos = 0) then
              portstr := NVELabelComboBox.GetItemText
            else
              portstr := copy (NVELabelComboBox.GetItemText, 1, strpos - 1);

            ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'NVE_PRINTER', GetComboBoxRef (NVELabelComboBox), portstr);
          end;
        end;

        if (VPELabelComboBox.ItemIndex <> -1) then begin
          if (GetComboBoxRef (VPELabelComboBox) <> -1) then
            ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'VPE_PRINTER', GetComboBoxRef (VPELabelComboBox), GetComboboxPrinterString (VPELabelComboBox))
          else begin
            strpos := Pos (';', VPELabelComboBox.GetItemText);

            if (strpos = 0) then
              portstr := VPELabelComboBox.GetItemText
            else
              portstr := copy (VPELabelComboBox.GetItemText, 1, strpos - 1);

            ChangeDeviceConfigValue (LVSDatenModul.AktConfigName, 'VPE_PRINTER', GetComboBoxRef (VPELabelComboBox), portstr);
          end;
        end;

        if (GetComboBoxRef (WEPALLabelComboBox) <> -1) then
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'WEPAL-LABEL', GetComboBoxRef (WEPALLabelComboBox), '')
        else if (WEPALLabelComboBox.ItemIndex > 0) then
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'WEPAL-LABEL', -1, GetComboboxPrinterString (WEPALLabelComboBox))
        else
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'WEPAL-LABEL', -1, '');

        if (GetComboBoxRef (AutoWEPALLabelComboBox) <> -1) then
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'AUTO-WEPAL-LABEL', GetComboBoxRef (AutoWEPALLabelComboBox), '')
        else if (AutoWEPALLabelComboBox.ItemIndex > 0) then
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'AUTO-WEPAL-LABEL', -1, GetComboboxPrinterString (AutoWEPALLabelComboBox))
        else
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'AUTO-WEPAL-LABEL', -1, '');

        if (GetComboBoxRef (KommNVEComboBox) <> -1) then
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'NVE-LABEL', GetComboBoxRef (KommNVEComboBox), '')
        else if (KommNVEComboBox.ItemIndex > 0) then
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'NVE-LABEL', -1, GetComboboxPrinterString (KommNVEComboBox))
        else
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'NVE-LABEL', -1, '');

        if (KommLSComboBox.ItemIndex > 0) then
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'KOMM-LS', -1, GetComboboxPrinterString (KommLSComboBox))
        else
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'KOMM-LS', -1, '');

        if (GetComboBoxRef (PaketLabelCombobox) <> -1) then
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'PAKET-LABEL', GetComboBoxRef (PaketLabelCombobox), '')
        else if (PaketLabelCombobox.ItemIndex > 0) then
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'PAKET-LABEL', -1, GetComboboxPrinterString (PaketLabelCombobox))
        else
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'PAKET-LABEL', -1, '');

        if (GetComboBoxRef (BestandLabelCombobox) <> -1) then
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'BESTAND-LABEL', GetComboBoxRef (BestandLabelCombobox), '')
        else if (BestandLabelCombobox.ItemIndex > 0) then
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'BESTAND-LABEL', -1, GetComboboxPrinterString (BestandLabelCombobox))
        else
          SetPrinterRelation (LVSDatenModul.AktLocationRef, -1, LVSDatenModul.AktConfigName, 'BESTAND-LABEL', -1, '');

        if (LeitstandTabSheetLoaded) then begin
          if (LVSConfigModul.UseLeitstand) and (LVSConfigModul.RefLeitstand <> -1) then begin
            if (LeitstandLaserComboBox.ItemIndex <> -1) then
              ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'LASER_PRINTER', GetComboboxPrinterString (LeitstandLaserComboBox));

            if (GetComboBoxRef (LeitstandLabelComboBox) <> -1) then
              ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'LABEL_PRINTER', IntToStr (GetComboBoxRef (LeitstandLabelComboBox)))
            else if (LeitstandLabelComboBox.ItemIndex <> -1) then
              ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'LABEL_PRINTER', GetComboboxPrinterString (LeitstandLabelComboBox));

            ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'WE_BEREICH', WELBComboBox.GetItemText (-1, 0));
            ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'WE_PLATZ',   WELBComboBox.GetItemText (-1, 2));

            ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'RET_BEREICH', RETLBComboBox.GetItemText (-1, 0));
            ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'RET_PLATZ',   RETLBComboBox.GetItemText (-1, 2));

            ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'PACK_PLATZ', PackplatzComboBox.GetItemText);
            ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'PICK_TO_LIGHT_STATION', PickByLightStationComboBox.GetItemText);

            if not (PickByLightColorComboBox.Enabled) then
              ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'PICK_TO_LIGHT_COLOR', '')
            else if (PickByLightColorComboBox.ItemIndex = -1) then
              ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'PICK_TO_LIGHT_COLOR', '')
            else
              ChangeLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'PICK_TO_LIGHT_COLOR', IntToStr (PickByLightColorComboBox.ItemIndex + 1));
          end;
        end;

        LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trCommit)
      except
        LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);

        FrontendMessages.MessageDLG ('Fehler beim Übernahmen der Konfiguration in die Datenbank', mtError, [mbOk], 0);
      end;
    end;

    {$ifdef Numpad}
      if not (ScreenKeysCheckBox.Checked) then
        NumericKeypad.Visible := False
      else begin
        NumericKeypad.Visible := True;
        SetFocus;
      end;
    {$endif}

    LVSConfigModul.DBGrids.FontName       := GridFontSelector.Text;
    LVSConfigModul.DBGrids.FontSize       := FontSizeUpDown.Position;
    LVSConfigModul.DBGrids.TextColor      := TextColorCombo.SelColor;
    LVSConfigModul.DBGrids.HiddenSelColor := HiddenSelColorCombobox.Selected;

    LVSConfigModul.DBGrids.TitleFont      := TitleFontSelector.Text;
    LVSConfigModul.DBGrids.TitleSize      := TitleSizeUpDown.Position;
    LVSConfigModul.DBGrids.TitleWrap      := TitleWrapCheckBox.Checked;
    LVSConfigModul.DBGrids.TitleColor     := TitleColorCombo.SelColor;
    LVSConfigModul.DBGrids.TitleBackgnd   := TitleBackgndCombo.SelColor;

    LVSConfigModul.DBGrids.AlternateColors := CheckBox1.Checked;
    LVSConfigModul.DBGrids.EvenColor := EvenColorBox.Selected;
    LVSConfigModul.DBGrids.OddColor := OddColorBox.Selected;

    LVSConfigModul.DBGrids.GridFetchAll   := GridFetchAllCheckBox.Checked;
    LVSConfigModul.DBGrids.GridAutoUpdate := GridAutoUpdateCheckBox.Checked;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.FormCreate(Sender: TObject);
begin
  fLeitstandTabSheetLoaded := False;

  {$if CompilerVersion > 30.0}
  {$else}
    StyleComboBox.Visible := false;
  {$ifend}

  Label44.Visible := StyleComboBox.Visible;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, StdPrinterComboBox);
    LVSSprachModul.SetNoTranslate (Self, StdBinComboBox);
    LVSSprachModul.SetNoTranslate (Self, PickByLightStationComboBox);
    LVSSprachModul.SetNoTranslate (Self, NVELabelComboBox);
    LVSSprachModul.SetNoTranslate (Self, AutoWEPALLabelComboBox);
    LVSSprachModul.SetNoTranslate (Self, EvenColorBox);
    LVSSprachModul.SetNoTranslate (Self, OddColorBox);
    LVSSprachModul.SetNoTranslate (Self, KommBinComboBox);
    LVSSprachModul.SetNoTranslate (Self, KommLSComboBox);
    LVSSprachModul.SetNoTranslate (Self, PaketLabelCombobox);
    LVSSprachModul.SetNoTranslate (Self, BestandLabelCombobox);
    LVSSprachModul.SetNoTranslate (Self, KommNVEComboBox);
    LVSSprachModul.SetNoTranslate (Self, KommPrinterComboBox);
    LVSSprachModul.SetNoTranslate (Self, LeitstandEdit);
    LVSSprachModul.SetNoTranslate (Self, LeitstandLabelComboBox);
    LVSSprachModul.SetNoTranslate (Self, LeitstandLaserComboBox);
    LVSSprachModul.SetNoTranslate (Self, LieferBinComboBox);
    LVSSprachModul.SetNoTranslate (Self, LieferPrintComboBox);
    LVSSprachModul.SetNoTranslate (Self, PackplatzComboBox);
    LVSSprachModul.SetNoTranslate (Self, PickByLightColorComboBox);
    LVSSprachModul.SetNoTranslate (Self, RechungBinComboBox);
    LVSSprachModul.SetNoTranslate (Self, RechungPrintComboBox);
    LVSSprachModul.SetNoTranslate (Self, ZollBinComboBox);
    LVSSprachModul.SetNoTranslate (Self, ZollPrintComboBox);
    LVSSprachModul.SetNoTranslate (Self, ScanErrSoundComboBox);
    LVSSprachModul.SetNoTranslate (Self, RechungPrintComboBox);
    LVSSprachModul.SetNoTranslate (Self, ScannerComboBox);
    LVSSprachModul.SetNoTranslate (Self, TextColorCombo);
    LVSSprachModul.SetNoTranslate (Self, StdPrinterComboBox);
    LVSSprachModul.SetNoTranslate (Self, TitleBackgndCombo);
    LVSSprachModul.SetNoTranslate (Self, TitleColorCombo);
    LVSSprachModul.SetNoTranslate (Self, VerladeBinComboBox);
    LVSSprachModul.SetNoTranslate (Self, VerladePrinterComboBox);
    LVSSprachModul.SetNoTranslate (Self, VPELabelComboBox);
    LVSSprachModul.SetNoTranslate (Self, WEPALLabelComboBox);
    LVSSprachModul.SetNoTranslate (Self, MainTabFontColorCombobox);
    LVSSprachModul.SetNoTranslate (Self, SubTabFontColorCombobox);
    LVSSprachModul.SetNoTranslate (Self, GridAttrGroupBox);
    LVSSprachModul.SetNoTranslate (Self, ScanErrSoundComboBox);
    LVSSprachModul.SetNoTranslate (Self, HiddenSelColorCombobox);
  {$endif}

  PageControl1.ActivePageIndex := 0;
end;

procedure TSetupForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (PackplatzComboBox);
  ClearComboBoxObjects (LeitstandLaserComboBox);
  ClearComboBoxObjects (LeitstandLabelComboBox);
  ClearComboBoxObjects (WELBComboBox);
  ClearComboBoxObjects (RETLBComboBox);
  ClearComboBoxObjects (PickByLightStationComboBox);

  ClearComboBoxObjects (StdBinComboBox);
  ClearComboBoxObjects (StdPrinterComboBox);
  ClearComboBoxObjects (KommBinComboBox);
  ClearComboBoxObjects (KommPrinterComboBox);
  ClearComboBoxObjects (VerladeBinComboBox);
  ClearComboBoxObjects (VerladePrinterComboBox);
  ClearComboBoxObjects (LieferBinComboBox);
  ClearComboBoxObjects (LieferPrintComboBox);
  ClearComboBoxObjects (RechungBinComboBox);
  ClearComboBoxObjects (RechungPrintComboBox);
  ClearComboBoxObjects (ZollBinComboBox);
  ClearComboBoxObjects (ZollPrintComboBox);

  ClearComboBoxObjects (NVELabelComboBox);
  ClearComboBoxObjects (KommNVEComboBox);
  ClearComboBoxObjects (KommLSComboBox);
  ClearComboBoxObjects (PaketLabelCombobox);
  ClearComboBoxObjects (BestandLabelCombobox);
  ClearComboBoxObjects (VPELabelComboBox);
  ClearComboBoxObjects (WEPALLabelComboBox);
  ClearComboBoxObjects (AutoWEPALLabelComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TSetupForm.ReloadPrinters : Integer;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function GetComboBox (Combobox : TComboBoxPro; const DefaultStr : String) : String;
  begin
    if (Combobox.ItemIndex = -1) then
      Result := DefaultStr
    else Result := GetComboboxPrinterString (Combobox);
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  procedure SetCombobox (Combobox : TComboBoxPro; const TextStr : String; const Default : Integer = 0);
  var
    idx,
    ref,
    strpos   : Integer;
    prtname,
    prttype,
    cmpname,
    cmptype,
    prttext  : String;
    isport   : boolean;
  begin
    if (Combobox.Items.Count = 0) Then begin
      Combobox.ItemIndex := -1;
    end else begin
      strpos := Pos (';', TextStr);

      if (strpos = 0) then begin
        prtname := TextStr;
        prttype := '';
        isport := (copy (TextStr, 1, 2) = '\\');
      end else begin
        isport := false;
        prtname := Copy (TextStr, 1, strpos - 1);
        prttype := Copy (TextStr, strpos + 1);
      end;

      idx := 0;
      while (idx < ComboBox.Items.Count) do begin
        if Assigned (ComboBox.Items.Objects[idx]) then begin
          prttext := '';

          if isport and (ComboBox.Items.Objects[idx] is TPrinterType) then
            prttext := (ComboBox.Items.Objects[idx] as TPrinterType).PrtPort
          else if (ComboBox.Items.Objects[idx] is TPrinterType) then
            prttext := (ComboBox.Items.Objects[idx] as TPrinterType).PrtString
          else if (ComboBox.Items.Objects[idx] is TComboBoxStr) then
            prttext := (ComboBox.Items.Objects[idx] as TComboBoxStr).StrRef
          else if (ComboBox.Items.Objects[idx] is TComboBoxRefStr) then
            prttext := (ComboBox.Items.Objects[idx] as TComboBoxRefStr).StrRef;

          if (Length (prttext) > 0) then begin
            strpos := Pos (';', prttext);

            if (strpos = 0) then begin
              cmpname := prttext;
              cmptype := '';
            end else begin
              cmpname := Copy (prttext, 1, strpos - 1);
              cmptype := Copy (prttext, strpos + 1);
            end;

            if (UpperCase (cmpname) = UpperCase (prtname)) then begin
              if (Length (prttype) = 0) or (cmptype = prttype) then
                break;
            end;
          end;
        end;

        Inc (idx);
      end;

      if (idx < ComboBox.Items.Count) then
        Combobox.ItemIndex := idx
      else Combobox.ItemIndex := -1;

      if (Combobox.ItemIndex = -1) then begin
        if not TryStrToInt (TextStr, ref) then begin
          Combobox.ItemIndex := Combobox.IndexOf (TextStr, 0, True);

          if (Combobox.ItemIndex = -1) then
            Combobox.ItemIndex := Default
        end else begin
          Combobox.ItemIndex := FindComboboxRef (Combobox, ref);

          if (Combobox.ItemIndex = -1) then
            Combobox.ItemIndex := Default;
        end;
      end;
    end;
  end;

var
  idx,
  lblidx,
  dispidx       : integer;
  lsprtstr,
  lsbinstr,
  stdprtstr,
  stdbinstr,
  reprtstr,
  rebinstr,
  weprtstr,
  nveprtstr,
  vpeprtstr,
  prtname,
  prtdesc,
  autoweprtstr,
  kommprtstr,
  kommbinstr,
  kommlsprtstr,
  kommnveprtstr,
  colliprtstr,
  collibinstr,
  paketprtstr,
  bestandprtstr,
  zollprtstr,
  zollbinstr  : String;
  pname       : array [0..256] of char;
  dwsize      : DWORD;
  h           : THANDLE;
  pinfo       : ^PRINTER_INFO_2;
  altcursor   : TCursor;
  csize       : DWORD;
  cname       : array [0..MAX_COMPUTERNAME_LENGTH] of char;
  compname    : String;
begin
  csize := sizeof (cname) - 1;
  GetComputerName (cname, csize);
  cname [csize] := #0;

  compname := StrPas (cname);

  stdprtstr   := GetCombobox (StdPrinterComboBox,   StdPrinter);
  stdbinstr   := GetCombobox (StdBinComboBox,   StdPrinterBin);
  kommprtstr  := GetCombobox (KommPrinterComboBox,  KommPrinter);
  kommbinstr  := GetCombobox (KommBinComboBox,  KommPrinterBin);
  colliprtstr := GetCombobox (VerladePrinterComboBox, ColliPrinter);
  collibinstr := GetCombobox (VerladeBinComboBox, ColliPrinterBin);
  lsprtstr    := GetCombobox (LieferPrintComboBox,  LSPrinter);
  lsbinstr    := GetCombobox (LieferBinComboBox,  LSPrinterBin);
  reprtstr    := GetCombobox (RechungPrintComboBox, REPrinter);
  rebinstr    := GetCombobox (RechungBinComboBox, REPrinterBin);
  zollprtstr  := GetCombobox (ZollPrintComboBox, ZollPrinter);
  zollbinstr  := GetCombobox (ZollBinComboBox, ZollPrinterBin);

  Update;

  altcursor := Screen.Cursor;
  Screen.Cursor := crHourGlass;

  try
    Printer.Refresh;

    try
      if (Length (stdprtstr) = 0) and (printer.PrinterIndex >= 0) then
        stdprtstr := Printer.Printers.strings [printer.PrinterIndex];
    except
      stdprtstr := '';
    end;

    try
      if (Length (kommprtstr) = 0) and (printer.PrinterIndex >= 0) then
        kommprtstr := Printer.Printers.strings [printer.PrinterIndex];
    except
      kommprtstr := '';
    end;

    try
      if (Length (colliprtstr) = 0) and (printer.PrinterIndex >= 0) then
        colliprtstr := Printer.Printers.strings [printer.PrinterIndex];
    except
      colliprtstr := '';
    end;

    try
      if (Length (lsprtstr) = 0) and (printer.PrinterIndex >= 0) then
        lsprtstr := Printer.Printers.strings [printer.PrinterIndex];
    except
      lsprtstr := '';
    end;

    try
      if (Length (reprtstr) = 0) and (printer.PrinterIndex >= 0) then
        reprtstr := Printer.Printers.strings [printer.PrinterIndex];
    except
      reprtstr := '';
    end;

    try
      if (Length (zollprtstr) = 0) and (printer.PrinterIndex >= 0) then
        zollprtstr := Printer.Printers.strings [printer.PrinterIndex];
    except
      zollprtstr := '';
    end;

    nveprtstr     := GetCombobox (NVELabelComboBox, NVEPrinter);
    vpeprtstr     := GetCombobox (VPELabelComboBox, VPEPrinter);
    weprtstr      := GetCombobox (WEPALLabelComboBox, WEPALPrinter);
    autoweprtstr  := GetCombobox (AutoWEPALLabelComboBox, AutoWEPALPrinter);
    kommnveprtstr := GetCombobox (KommNVEComboBox, KommNVEPrinter);
    kommlsprtstr  := GetCombobox (KommLSComboBox, KommLSPrinter);
    paketprtstr   := GetCombobox (PaketLabelCombobox, PaketPrinter);
    bestandprtstr := GetCombobox (BestandLabelCombobox, BestandPrinter);

    ClearComboBoxObjects (StdPrinterComboBox);
    ClearComboBoxObjects (KommPrinterComboBox);
    ClearComboBoxObjects (VerladePrinterComboBox);
    ClearComboBoxObjects (LieferPrintComboBox);
    ClearComboBoxObjects (RechungPrintComboBox);
    ClearComboBoxObjects (ZollPrintComboBox);

    ClearComboBoxObjects (NVELabelComboBox);
    ClearComboBoxObjects (KommNVEComboBox);
    ClearComboBoxObjects (KommLSComboBox);
    ClearComboBoxObjects (VPELabelComboBox);
    ClearComboBoxObjects (WEPALLabelComboBox);
    ClearComboBoxObjects (AutoWEPALLabelComboBox);
    ClearComboBoxObjects (PaketLabelCombobox);
    ClearComboBoxObjects (BestandLabelCombobox);

    VerladePrinterComboBox.Text := '';

    idx     := 0;
    dispidx := 0;

    while (idx < Printer.Printers.Count) do begin
      StrPCopy (pname, Printer.Printers.strings [idx]);

      if (OpenPrinter (pname, h, nil)) then begin
        GetPrinter (h, 2, nil, 0, @dwsize);
        GetMem (pinfo, dwsize);

        try
          if (GetPrinter (h, 2, pinfo, dwsize, @dwsize)) Then begin
            {$ifdef Trace}
              {$ifdef Debug}
                TraceString ('pServerName:'+pinfo^.pServerName+', pPrinterName:'+pinfo^.pPrinterName+', pShareName:'+pinfo^.pShareName+', pPortName:'+pinfo^.pPortName+', pParameters:'+pinfo^.pParameters);
              {$endif}
            {$endif}

            if Assigned (pinfo^.pServerName) then
              prtname := pinfo^.pPrinterName
            else if (Length (pinfo^.pShareName) = 0) then
              prtname := pinfo^.pPrinterName
            else
              prtname := '\\' + compname + '\' + pinfo^.pShareName;

            if Assigned (pinfo^.pServerName) then
              prtdesc := copy (pinfo^.pPrinterName, Length (pinfo^.pServerName) + 2)
            else if (Length (pinfo^.pShareName) > 0) then
              prtdesc := pinfo^.pShareName
            else
              prtdesc := pinfo^.pPrinterName;

            StdPrinterComboBox.Items.AddObject     (prtdesc+'|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
            KommPrinterComboBox.Items.AddObject    (prtdesc+'|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
            VerladePrinterComboBox.Items.AddObject (prtdesc+'|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
            LieferPrintComboBox.Items.AddObject    (prtdesc+'|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
            RechungPrintComboBox.Items.AddObject   (prtdesc+'|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
            ZollPrintComboBox.Items.AddObject      (prtdesc+'|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));

            if (dispidx = 0) then begin
              WEPALLabelComboBox.Items.Add ('--- '+GetResourceText (1269));
              AutoWEPALLabelComboBox.Items.Add ('--- '+GetResourceText (1269));
              NVELabelComboBox.Items.Add ('--- '+GetResourceText (1269));
              VPELabelComboBox.Items.Add ('--- '+GetResourceText (1269));
              KommNVEComboBox.Items.Add ('--- '+GetResourceText (1269));
              KommLSComboBox.Items.Add ('--- '+GetResourceText (1269));
              PaketLabelCombobox.Items.Add ('--- '+GetResourceText (1269));
              BestandLabelCombobox.Items.Add ('--- '+GetResourceText (1269));
            end;

            WEPALLabelComboBox.Items.AddObject     (prtdesc+'|LASER|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
            AutoWEPALLabelComboBox.Items.AddObject (prtdesc+'|LASER|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));

            NVELabelComboBox.Items.AddObject       (prtdesc+'|LASER|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
            VPELabelComboBox.Items.AddObject       (prtdesc+'|LASER|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
            KommNVEComboBox.Items.AddObject        (prtdesc+'|LASER|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));

            KommLSComboBox.Items.AddObject         (prtdesc+'|LASER|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
            PaketLabelCombobox.Items.AddObject     (prtdesc+'|LASER|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
            BestandLabelCombobox.Items.AddObject   (prtdesc+'|LASER|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));

            Inc (dispidx);
          end;
        finally
          FreeMem (pinfo);
        end;

        ClosePrinter (h);
      end;

      Inc (idx);
    end;
  except
  end;

  Screen.Cursor := altcursor;

  //Wenn kein Drucker für den Benutzer angelegt sind, muss die Breite für die Darstellung manuelle festgelegt werden
  if (StdPrinterComboBox.Items.Count = 0) then begin
    StdPrinterComboBox.ColWidths [0] := 140;

    StdPrinterComboBox.Enabled     := False;
    KommPrinterComboBox.Enabled    := False;
    VerladePrinterComboBox.Enabled := False;
    LieferPrintComboBox.Enabled    := False;
    RechungPrintComboBox.Enabled   := False;
    ZollPrintComboBox.Enabled   := False;
  end else begin
    StdPrinterComboBox.Enabled     := True;
    KommPrinterComboBox.Enabled    := True;
    VerladePrinterComboBox.Enabled := True;
    LieferPrintComboBox.Enabled    := True;
    RechungPrintComboBox.Enabled   := True;
    ZollPrintComboBox.Enabled      := True;

    StdPrinterComboBox.Prepare;

    SetCombobox (StdPrinterComboBox, stdprtstr);
    StdBinComboBox.Text := stdbinstr;
    SetCombobox (KommPrinterComboBox, kommprtstr);
    KommBinComboBox.Text := kommbinstr;
    SetCombobox (VerladePrinterComboBox, colliprtstr);
    VerladeBinComboBox.Text := collibinstr;
    SetCombobox (LieferPrintComboBox, lsprtstr);
    LieferBinComboBox.Text := lsbinstr;
    SetCombobox (RechungPrintComboBox, reprtstr);
    RechungBinComboBox.Text := rebinstr;
    SetCombobox (ZollPrintComboBox, zollprtstr);
    ZollBinComboBox.Text := zollbinstr;
  end;

  StdBinComboBox.Enabled     := StdPrinterComboBox.Enabled;
  KommBinComboBox.Enabled    := KommPrinterComboBox.Enabled;
  VerladeBinComboBox.Enabled := VerladePrinterComboBox.Enabled;
  LieferBinComboBox.Enabled  := LieferPrintComboBox.Enabled;
  RechungBinComboBox.Enabled := RechungPrintComboBox.Enabled;
  ZollBinComboBox.Enabled    := ZollPrintComboBox.Enabled;


  if (ADOQuery1.Connection.Connected) then begin
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add('select REF,NAME,BESCHREIBUNG,TYPE,PORT from V_PRT_PRINTER where STATUS=''ON'' and (REF_LOCATION is null or REF_LOCATION=:ref_loc)');
    ADOQuery1.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    if not (LVSConfigModul.TestFlag) then
      ADOQuery1.SQL.Add('and OPT_TEST=''0''');

    ADOQuery1.SQL.Add('order by NAME');

    ADOQuery1.Open;

    if (ADOQuery1.RecordCount > 0) then begin
      WEPALLabelComboBox.Items.Add ('--- '+GetResourceText (1270));
      AutoWEPALLabelComboBox.Items.Add ('--- '+GetResourceText (1270));
      NVELabelComboBox.Items.Add ('--- '+GetResourceText (1270));
      VPELabelComboBox.Items.Add ('--- '+GetResourceText (1270));
      KommNVEComboBox.Items.Add ('--- '+GetResourceText (1270));
      KommLSComboBox.Items.Add ('--- '+GetResourceText (1270));
      PaketLabelCombobox.Items.Add ('--- '+GetResourceText (1270));
      BestandLabelCombobox.Items.Add ('--- '+GetResourceText (1270));

      lblidx := WEPALLabelComboBox.Items.Count;

      while not (ADOQuery1.Eof) do begin
        idx := lblidx;

        while (idx < WEPALLabelComboBox.Items.Count) do begin
          if (NVELabelComboBox.GetItemText (idx, 0) = ADOQuery1.Fields [1].AsString) then begin
            break;
          end;

          Inc (idx);
        end;

        if not (idx < WEPALLabelComboBox.Items.Count) then begin
          NVELabelComboBox.Items.AddObject       (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [3].AsString+'|'+ADOQuery1.Fields [2].AsString, TPrinterType.Create(ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [1].AsString+';'+'LABEL', ADOQuery1.Fields [1].AsString, ADOQuery1.Fields [4].AsString, 'LABEL'));
          VPELabelComboBox.Items.AddObject       (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [3].AsString+'|'+ADOQuery1.Fields [2].AsString, TPrinterType.Create(ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [1].AsString+';'+'LABEL', ADOQuery1.Fields [1].AsString, ADOQuery1.Fields [4].AsString, 'LABEL'));
          WEPALLabelComboBox.Items.AddObject     (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [3].AsString+'|'+ADOQuery1.Fields [2].AsString, TPrinterType.Create(ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [1].AsString+';'+'LABEL', ADOQuery1.Fields [1].AsString, ADOQuery1.Fields [4].AsString, 'LABEL'));
          AutoWEPALLabelComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [3].AsString+'|'+ADOQuery1.Fields [2].AsString, TPrinterType.Create(ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [1].AsString+';'+'LABEL', ADOQuery1.Fields [1].AsString, ADOQuery1.Fields [4].AsString, 'LABEL'));
          KommNVEComboBox.Items.AddObject        (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [3].AsString+'|'+ADOQuery1.Fields [2].AsString, TPrinterType.Create(ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [1].AsString+';'+'LABEL', ADOQuery1.Fields [1].AsString, ADOQuery1.Fields [4].AsString, 'LABEL'));
          PaketLabelCombobox.Items.AddObject     (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [3].AsString+'|'+ADOQuery1.Fields [2].AsString, TPrinterType.Create(ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [1].AsString+';'+'LABEL', ADOQuery1.Fields [1].AsString, ADOQuery1.Fields [4].AsString, 'LABEL'));
          BestandLabelCombobox.Items.AddObject   (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [3].AsString+'|'+ADOQuery1.Fields [2].AsString, TPrinterType.Create(ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [1].AsString+';'+'LABEL', ADOQuery1.Fields [1].AsString, ADOQuery1.Fields [4].AsString, 'LABEL'));
        end;

        ADOQuery1.Next;
      end;
    end;

    ADOQuery1.Close;
  end;

  NVELabelComboBox.Items.Insert (0, '');
  VPELabelComboBox.Items.Insert (0, '');
  WEPALLabelComboBox.Items.Insert (0, '');
  AutoWEPALLabelComboBox.Items.Insert (0, '');
  KommNVEComboBox.Items.Insert (0, '');
  KommLSComboBox.Items.Insert (0, '');
  PaketLabelCombobox.Items.Insert (0, '');
  BestandLabelCombobox.Items.Insert (0, '');

  if (NVELabelComboBox.ColWidths [0] > StdPrinterComboBox.ColWidths [0]) then
    StdPrinterComboBox.ColWidths [0] := NVELabelComboBox.ColWidths [0]
  else
    NVELabelComboBox.ColWidths [0] := StdPrinterComboBox.ColWidths [0];

  NVELabelComboBox.ColWidths [1]       := NVELabelComboBox.ColWidths [0] + 60;

  VPELabelComboBox.ColWidths [0]       := NVELabelComboBox.ColWidths [0];   VPELabelComboBox.ColWidths [1]       := NVELabelComboBox.ColWidths [0] + 60;
  WEPALLabelComboBox.ColWidths [0]     := NVELabelComboBox.ColWidths [0];   WEPALLabelComboBox.ColWidths [1]     := NVELabelComboBox.ColWidths [0] + 60;
  AutoWEPALLabelComboBox.ColWidths [0] := NVELabelComboBox.ColWidths [0];   AutoWEPALLabelComboBox.ColWidths [1] := NVELabelComboBox.ColWidths [0] + 60;
  KommNVEComboBox.ColWidths [0]        := NVELabelComboBox.ColWidths [0];   KommNVEComboBox.ColWidths [1]        := NVELabelComboBox.ColWidths [0] + 60;
  KommLSComboBox.ColWidths [0]         := NVELabelComboBox.ColWidths [0];   KommLSComboBox.ColWidths [1]         := NVELabelComboBox.ColWidths [1] + 60;
  PaketLabelCombobox.ColWidths [0]     := NVELabelComboBox.ColWidths [0];   PaketLabelCombobox.ColWidths [1]     := NVELabelComboBox.ColWidths [1] + 60;
  BestandLabelCombobox.ColWidths [0]   := NVELabelComboBox.ColWidths [0];   BestandLabelCombobox.ColWidths [1]   := NVELabelComboBox.ColWidths [1] + 60;

  SetCombobox (NVELabelComboBox, nveprtstr);
  SetCombobox (VPELabelComboBox, vpeprtstr);
  SetCombobox (WEPALLabelComboBox, weprtstr, 0);
  SetCombobox (AutoWEPALLabelComboBox, autoweprtstr, 0);
  SetCombobox (KommNVEComboBox, kommnveprtstr, 0);
  SetCombobox (KommLSComboBox, kommlsprtstr, 0);
  SetCombobox (PaketLabelCombobox, paketprtstr, 0);
  SetCombobox (BestandLabelCombobox, bestandprtstr, 0);

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TSetupForm.LoadBinInfo (const PrinterName : String; ComboBox : TComboBoxPro) : Integer;
var
  binstr,
  selport,
  seltype,
  binname   : String;
  idx,
  selidx,
  strpos    : Integer;
  pdataname : Array [0..128] of array [0..23] of char;
  pdatabins : array [0..128] of WORD;
  dwsize    : DWORD;
  dwcount   : DWORD;
  i         : Integer;
  h         : THANDLE;
  pinfo     : ^PRINTER_INFO_2;
  perrstr   : array [0..255] of Char;
begin
  binstr := ComboBox.Text;

  ClearComboBoxObjects (ComboBox);
  ComboBox.Items.Add ('');

  if (Length (PrinterName) > 0) then begin
    selport := '';
    seltype := '';

    strpos := Pos (';', PrinterName);

    if (strpos = 0) then
      selport := PrinterName
    else begin
      selport := Copy (PrinterName, 1, strpos - 1);
      seltype := Copy (PrinterName, strpos + 1);
    end;

    Screen.Cursor := crHourglass;    { Cursor als Sanduhr }

    try
      if not (OpenPrinter (PChar (selport), h, nil)) then begin
        FormatMessage (FORMAT_MESSAGE_FROM_SYSTEM, Nil, GetLastError, LANG_NEUTRAL, @perrstr, sizeof (perrstr), Nil);
        FrontendMessages.MessageDLG (FormatMessageText (1524, [selport, StrPas (perrstr)]), mtError, [mbOk], 0);
      end else begin
        GetPrinter (h, 2, nil, 0, @dwsize);
        GetMem (pinfo, dwsize);

        if not (GetPrinter (h, 2, pinfo, dwsize, @dwsize)) Then begin
          FormatMessage (FORMAT_MESSAGE_FROM_SYSTEM, Nil, GetLastError, LANG_NEUTRAL, @perrstr, sizeof (perrstr), Nil);
          FrontendMessages.MessageDLG (FormatMessageText (1525, [selport, StrPas (perrstr)]), mtError, [mbOk], 0);
        end else begin
          dwsize := DeviceCapabilities (pinfo^.pPrinterName, pinfo^.pPortName, DC_BINS, nil, nil);

          if (dwsize < 1) then
            ComboBox.ItemIndex := 0
          else begin
            dwcount := DeviceCapabilities (pinfo^.pPrinterName, pinfo^.pPortName, DC_BINS, @pdatabins, nil);
            dwcount := DeviceCapabilities (pinfo^.pPrinterName, pinfo^.pPortName, DC_BINNAMES, @pdataname, nil);

            selidx := -1;

            for i:=0 to (dwsize - 1) do begin
              if ((pdatabins [i] and $f000) = 0) then begin
                binname := StrPas (pdataname [i]) + ' ('+IntToStr (pdatabins [i])+')';

                idx := ComboBox.AddItemIndex (binname, TComboBoxRef.Create (pdatabins [i]));

                if (Length (binstr) > 0) and (binname = binstr) then
                  selidx := idx;
              end;
            end;

            if (selidx = -1) then
              ComboBox.ItemIndex := 0
            else
             ComboBox.ItemIndex := selidx;
          end;
        end;

        FreeMem (pinfo);

        ClosePrinter (h);
      end;
    finally
      Screen.Cursor := crDefault;
    end;
  end;

  Result := 0;
end;

function TSetupForm.ReloadLeitstandPrinters : Integer;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function GetComboBox (Combobox : TComboBoxPro; const DefaultStr : String) : String;
  begin
    if (Combobox.ItemIndex = -1) then
      Result := DefaultStr
    else Result := GetComboboxPrinterString (Combobox);
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  procedure SetCombobox (Combobox : TComboBoxPro; const TextStr : String; const Default : Integer = 0);
  var
    idx,
    ref,
    strpos   : Integer;
    prtname,
    prttype,
    cmpname,
    cmptype,
    prttext  : String;
  begin
    if (Combobox.Items.Count = 0) Then begin
      Combobox.ItemIndex := -1;
    end else begin
      strpos := Pos (';', TextStr);

      if (strpos = 0) then begin
        prtname := TextStr;
        prttype := '';
      end else begin
        prtname := Copy (TextStr, 1, strpos - 1);
        prttype := Copy (TextStr, strpos + 1);
      end;

      idx := 0;
      while (idx < ComboBox.Items.Count) do begin
        if Assigned (ComboBox.Items.Objects[idx]) then begin
          prttext := '';

          if (ComboBox.Items.Objects[idx] is TPrinterType) then
            prttext := (ComboBox.Items.Objects[idx] as TPrinterType).PrtString
          else if (ComboBox.Items.Objects[idx] is TComboBoxStr) then
            prttext := (ComboBox.Items.Objects[idx] as TComboBoxStr).StrRef
          else if (ComboBox.Items.Objects[idx] is TComboBoxRefStr) then
            prttext := (ComboBox.Items.Objects[idx] as TComboBoxRefStr).StrRef;

          if (Length (prttext) > 0) then begin
            strpos := Pos (';', prttext);

            if (strpos = 0) then begin
              cmpname := prttext;
              cmptype := '';
            end else begin
              cmpname := Copy (prttext, 1, strpos - 1);
              cmptype := Copy (prttext, strpos + 1);
            end;

            if (UpperCase (cmpname) = UpperCase (prtname)) then begin
              if (Length (prttype) = 0) or (cmptype = prttype) then
                break;
            end;
          end;
        end;

        Inc (idx);
      end;

      if (idx < ComboBox.Items.Count) then
        Combobox.ItemIndex := idx
      else Combobox.ItemIndex := -1;

      if (Combobox.ItemIndex = -1) then begin
        if not TryStrToInt (TextStr, ref) then begin
          Combobox.ItemIndex := Combobox.IndexOf (TextStr, 0, True);

          if (Combobox.ItemIndex = -1) then
            Combobox.ItemIndex := Default
        end else begin
          Combobox.ItemIndex := FindComboboxRef (Combobox, ref);

          if (Combobox.ItemIndex = -1) then
            Combobox.ItemIndex := Default;
        end;
      end;
    end;
  end;

var
  idx         : integer;
  stdprtstr,
  nveprtstr,
  prtdesc,
  prtname     : String;
  pname       : array [0..256] of char;
  dwsize      : DWORD;
  h           : THANDLE;
  pinfo       : ^PRINTER_INFO_2;
  altcursor   : TCursor;
  csize       : DWORD;
  cname       : array [0..MAX_COMPUTERNAME_LENGTH] of char;
  compname    : String;
begin
  csize := sizeof (cname) - 1;
  GetComputerName (cname, csize);
  cname [csize] := #0;

  compname := StrPas (cname);

  stdprtstr   := GetCombobox (LeitstandLaserComboBox, LeitstandLaserPrinter);

  Update;

  altcursor := Screen.Cursor;
  Screen.Cursor := crHourGlass;

  try
    try
      if (Length (stdprtstr) = 0) and (printer.PrinterIndex >= 0) then
        stdprtstr := Printer.Printers.strings [printer.PrinterIndex];
    except
      stdprtstr := '';
    end;

    nveprtstr := GetCombobox (LeitstandLabelComboBox, LeitstandLabelPrinter);

    ClearComboBoxObjects (LeitstandLaserComboBox);
    ClearComboBoxObjects (LeitstandLabelComboBox);


    idx := 0;
    while (idx < Printer.Printers.Count) do begin
      StrPCopy (pname, Printer.Printers.strings [idx]);

      if (OpenPrinter (pname, h, nil)) then begin
        GetPrinter (h, 2, nil, 0, @dwsize);
        GetMem (pinfo, dwsize);

        if (GetPrinter (h, 2, pinfo, dwsize, @dwsize)) Then begin
          if Assigned (pinfo^.pServerName) then
            prtname := pinfo^.pPrinterName
          else if (Length (pinfo^.pShareName) = 0) then
            prtname := pinfo^.pPrinterName
          else
            prtname := '\\' + compname + '\' + pinfo^.pShareName;

          if Assigned (pinfo^.pServerName) then
            prtdesc := copy (pinfo^.pPrinterName, Length (pinfo^.pServerName) + 1)
          else if (Length (pinfo^.pShareName) > 0) then
            prtdesc := pinfo^.pShareName
          else
            prtdesc := pinfo^.pPrinterName;

          LeitstandLaserComboBox.Items.AddObject (prtdesc+'|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
          LeitstandLabelComboBox.Items.AddObject (prtdesc+'|'+'|LASER|'+StrPas (pinfo^.pComment), TPrinterType.Create(-1, prtname+';'+'LASER', pinfo^.pPrinterName, pinfo^.pPortName, 'LASER'));
        end;

        FreeMem (pinfo);

        ClosePrinter (h);
      end;

      Inc (idx);
    end;
  except
  end;

  Screen.Cursor := altcursor;

  SetCombobox (LeitstandLaserComboBox, stdprtstr);

  if (ADOQuery1.Connection.Connected) then begin
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add('select REF,NAME,BESCHREIBUNG,TYPE,PORT from V_PRT_PRINTER where STATUS=''ON'' and REF_LOCATION=:ref_loc');
    ADOQuery1.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    if not (LVSConfigModul.TestFlag) then
      ADOQuery1.SQL.Add('and OPT_TEST=''0''');

    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      if (LeitstandLabelComboBox.IndexOf (ADOQuery1.Fields [1].AsString) = -1) then begin
        LeitstandLabelComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [3].AsString+'|'+ADOQuery1.Fields [2].AsString, TPrinterType.Create(ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [1].AsString+';'+'LABEL', ADOQuery1.Fields [1].AsString, ADOQuery1.Fields [4].AsString, 'LABEL'));
      end;

      ADOQuery1.Next;
    end;
    ADOQuery1.Close;
  end;

  LeitstandLabelComboBox.Items.Insert (0, '');

  SetCombobox (LeitstandLabelComboBox, nveprtstr);

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.ScanErrSoundComboBoxChange (Sender: TObject);
begin
  if (ScanErrSoundComboBox.ItemIndex = 0) then begin
    fScanErrSound := '';
    PlayScanErrSoundButton.Enabled := false;
  end else begin
    fScanErrSound := GetEnvironmentVariable ('SystemRoot') + '\Media\' + ScanErrSoundComboBox.Items [ScanErrSoundComboBox.ItemIndex];
    PlayScanErrSoundButton.Enabled := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.TabSheet2Show(Sender: TObject);
var
  res,
  idx     : Integer;
  suchrec : TSearchRec;
  suchdir : String;
begin
  ScannerEnableCheckBoxClick (Sender);

  ScanErrSoundComboBox.Clear;
  ScanErrSoundComboBox.ItemIndex := -1;

  ScanErrSoundComboBox.Items.Add (GetResourceText (1636));

  suchdir := GetEnvironmentVariable ('SystemRoot') + '\Media\';

  res := FindFirst (suchdir + '*.wav',  faArchive, suchrec);

  while (res = 0) do begin
    idx := ScanErrSoundComboBox.Items.Add (suchrec.Name);

    if (UpperCase (ExtractFileName (fScanErrSound)) = UpperCase (suchrec.Name)) then
      ScanErrSoundComboBox.ItemIndex := idx;

    res := FindNext (suchrec);
  end;

  if (ScanErrSoundComboBox.ItemIndex = -1) then
    ScanErrSoundComboBox.ItemIndex := 0;

  FindClose (suchrec);
end;

procedure TSetupForm.TabSheet4Show(Sender: TObject);
begin
  {$if CompilerVersion > 30.0}
    if (TStyleManager.ActiveStyle.Name = 'Windows') then
      StyleComboBox.ItemIndex := 0
    else if (TStyleManager.ActiveStyle.Name = 'Windows10') then
      StyleComboBox.ItemIndex := 1
    else if (TStyleManager.ActiveStyle.Name = 'Windows10 Dark') then
      StyleComboBox.ItemIndex := 2
    else if (TStyleManager.ActiveStyle.Name = 'Windows10 SlateGray') then
      StyleComboBox.ItemIndex := 3
    else if (TStyleManager.ActiveStyle.Name = 'Windows10 Blue') then
      StyleComboBox.ItemIndex := 4
    else if (TStyleManager.ActiveStyle.Name = 'Windows10 Green') then
      StyleComboBox.ItemIndex := 5
    else if (TStyleManager.ActiveStyle.Name = 'Windows10 Purple') then
      StyleComboBox.ItemIndex := 6
  {$ifend}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.PrinterTabSheetShow(Sender: TObject);
begin
  PrinterLocLabel.Caption := LVSDatenModul.AktLocation;
  PrinterLocBevel.Left    := PrinterLocLabel.Left + PrinterLocLabel.Width + 5;
  PrinterLocBevel.Width   := LabelPrtGroupBox.ClientWidth - PrinterLocBevel.Left - 10;

  if (PrinterTabSheet.Enabled) then begin
    ReloadPrinters;
  end;
end;

procedure TSetupForm.PrtPropertiesMenuItemClick(Sender: TObject);
var
  hPrinter: THandle;
  hdevmode  : THandle;
  portstr : String;
  prtsize : DWORD;
  ppi: PPrinterInfo2;
  popup : TPopupMenu;
  combo : TComboBoxPro;
begin
  popup := ((Sender as TMenuItem).GetParentMenu as TPopupMenu);

  if Assigned (popup) and Assigned (popup.PopupComponent) and (popup.PopupComponent is TComboBoxPro) then begin
    combo := popup.PopupComponent as TComboBoxPro;

    if (combo.ItemIndex <> -1) and Assigned (combo.Items.Objects [combo.ItemIndex]) then begin
      if (TPrinterType (combo.Items.Objects [combo.ItemIndex]).PrtType = 'LABEL') then
        portstr := TPrinterType (combo.Items.Objects [combo.ItemIndex]).PrtPort
      else
        portstr := TPrinterType (combo.Items.Objects [combo.ItemIndex]).PrtName;

      if not OpenPrinter (PChar (portstr), hPrinter, nil) then
        FrontendMessages.MessageDLG ('Drucker '+portstr+' nicht ansprechbar', mtError, [mbOK], 0)
      else begin
        try
          WinSpool.GetPrinter (hPrinter, 2, Nil, 0, @prtsize);
          hdevmode := GlobalAlloc(GPTR, prtsize);

          try
            ppi := GlobalLock(hdevmode);

            Screen.Cursor := crHourGlass;

            try
              if not WinSpool.GetPrinter(hPrinter, 2, ppi, prtsize, @prtsize) then
                FrontendMessages.MessageDLG ('Konfiguration des Drucker '+portstr+' nicht abrufbar', mtError, [mbOK], 0)
              else begin
                { Display a printer-properties property sheet for the specified printer }
                if (DocumentProperties (Handle, hPrinter, PChar (portstr), ppi^.pDevMode^, ppi^.pDevMode^, DM_PROMPT or DM_IN_BUFFER or DM_OUT_BUFFER) = IDOK) then begin
                  if SetPrinter (hPrinter, 2, ppi, 0) then
                    FrontendMessages.MessageDLG ('Fehler beim Setzen der Druckerkonfiguration für '+portstr, mtError, [mbOK], 0)
                end;
              end;
            finally
              Screen.Cursor := crDefault;

              GlobalUnlock (hdevmode);
            end;

          finally
            GlobalFree (hdevmode);
          end;
       finally
          { Close the specified printer object }
          ClosePrinter(hPrinter);
        end;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.FormShow(Sender: TObject);
var
  intwert : Integer;
begin
  PrinterTabSheet.Enabled       := LVSDatenModul.MainADOConnection.Connected;
  TouchCheckBox.Enabled         := LVSDatenModul.MainADOConnection.Connected;
  ScannerEnableCheckBox.Enabled := LVSDatenModul.MainADOConnection.Connected;
  ScannerComboBox.Enabled       := LVSDatenModul.MainADOConnection.Connected;

  if (UserReg.ReadRegValue ('SortLieferant', intwert) <> 0) then
    SortLieferantRadioGroup.ItemIndex := -1
  else if (intwert < 0) and (intwert >= SortLieferantRadioGroup.Items.Count) then
    SortLieferantRadioGroup.ItemIndex := -1
  else
    SortLieferantRadioGroup.ItemIndex := intwert;

  if (UserReg.ReadRegValue ('SortEmpf', intwert) <> 0) then
    SortEmpfRadioGroup.ItemIndex := -1
  else if (intwert < 0) and (intwert >= SortEmpfRadioGroup.Items.Count) then
    SortEmpfRadioGroup.ItemIndex := -1
  else
    SortEmpfRadioGroup.ItemIndex := intwert;

  if (UserReg.ReadRegValue ('SortLP', intwert) <> 0) then
    SortLPRadioGroup.ItemIndex := -1
  else if (intwert < 0) and (intwert >= SortLPRadioGroup.Items.Count) then
    SortLPRadioGroup.ItemIndex := -1
  else
    SortLPRadioGroup.ItemIndex := intwert;

  if (UserReg.ReadRegValue ('SortUser', intwert) <> 0) then
    SortUserRadioGroup.ItemIndex := -1
  else if (intwert < 0) and (intwert >= SortUserRadioGroup.Items.Count) then
    SortUserRadioGroup.ItemIndex := -1
  else
    SortUserRadioGroup.ItemIndex := intwert;

  if (UserReg.ReadRegValue ('PackDisplayWaitTimeEdit', intwert) <> 0) then
    PackDisplayWaitTimeEdit.Text := '5'
  else if (intwert = -1) then
    PackDisplayWaitTimeEdit.Text := ''
  else
    PackDisplayWaitTimeEdit.Text := IntToStr (intwert);

  if Assigned (PageControl1.ActivePage.OnShow) then
    PageControl1.ActivePage.OnShow (PageControl1.ActivePage);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetupForm.LeitstandTabSheetShow(Sender: TObject);
var
  clientname : String;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function GetTerminalSessionInfo : Integer;
  var
    ts     : TTerminalService;
    clname : String;
    cname  : array [0..MAX_COMPUTERNAME_LENGTH] of char;
    csize  : DWORD;
  begin
    csize := sizeof (cname) - 1;
    GetComputerName (cname, csize);
    cname [csize] := #0;
    clientname := AnsiUpperCase (StrPas (cname));

    ts := TTerminalService.Create;

    try
      if (ts.IsRemoteSession) then begin
        clname := GetParamVal ('ICA_CLIENT');

        if (Length (clname) > 0) then
          clientname := AnsiUpperCase (clname)
        else
          clientname := AnsiUpperCase (ts.ClientName);
      end;
    finally
      ts.Free;
    end;

    Result := 0;
  end;

var
  idx,
  sellbidx,
  sellpidx,
  selidx    : Integer;
  lbstr,
  lpstr     : String;
begin
  if (LeitstandTabSheet.Enabled) then begin
    //Wenn noch kein Leitstand ausgewählt ist, dann den Computernamen als Leitstand setzen
    if (LeitstandEdit.Enabled and (Length (LeitstandEdit.Text) = 0)) then begin
      GetTerminalSessionInfo;

      LeitstandEdit.Text := clientname;
    end;

    fLeitstandTabSheetLoaded := True;

    ReloadLeitstandPrinters;

    if (WELBComboBox.ItemIndex <= 0) then begin
      lbstr := LeitstandWEStation;
      lpstr := LeitstandWEPlatz;
    end else begin
      lbstr := WELBComboBox.GetItemText (-1, 0);
      lpstr := WELBComboBox.GetItemText (-1, 2);
    end;

    sellbidx := -1;
    sellpidx := -1;

    Screen.Cursor := crSQLWait;

    WELBComboBox.Items.BeginUpdate;

    try
      ClearComboBoxObjects (WELBComboBox);

      WELBComboBox.Items.Add ('');

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select lb.REF,lb.NAME,lb.BESCHREIBUNG,lp.REF,nvl (lp.NAME,lp.LP_NR) from V_LB lb left outer join V_LP lp on (lp.REF_LB=lb.REF)');
      ADOQuery1.SQL.Add ('WHERE lb.LB_ART=''WE'' and lb.REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc) order by lb.WE_SPERR, lb.NAME, lp.NAME, lp.LP_NR');
      ADOQuery1.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

      try
        ADOQuery1.Open;

        while not (ADOQuery1.Eof) do begin
          if not (ADOQuery1.Fields [0].IsNull) then begin
            //Den WE-Bereich einmal auch ohne LP anzeigen
            if (FindComboboxRef (WELBComboBox, ADOQuery1.Fields [0].AsInteger) = -1) then begin
              idx := WELBComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString+'|', TComboBoxLBLPRef.Create(DBGetReferenz (ADOQuery1.Fields [0]), -1));

              if (sellbidx = -1) and (Length (lbstr) > 0) and (lbstr = ADOQuery1.Fields [1].AsString) then
                sellbidx := idx;
            end;

            if not (ADOQuery1.Fields [3].IsNull) then begin
              idx := WELBComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString+'|'+ADOQuery1.Fields [4].AsString, TComboBoxLBLPRef.Create(DBGetReferenz (ADOQuery1.Fields [0]), DBGetReferenz (ADOQuery1.Fields [3])));

              if (Length (lpstr) > 0) and (lpstr = ADOQuery1.Fields [4].AsString) then
                sellpidx := idx;
            end;
          end;

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      except
      end;
    finally
      WELBComboBox.Items.EndUpdate;

      Screen.Cursor := crDefault;
    end;

    if (sellpidx = -1) then
      WELBComboBox.ItemIndex := sellbidx
    else WELBComboBox.ItemIndex := sellpidx;

    if (WELBComboBox.ItemIndex = -1) then WELBComboBox.ItemIndex := 0;


    if (RETLBComboBox.ItemIndex <= 0) then begin
      lbstr := LeitstandRETStation;
      lpstr := LeitstandRETPlatz;
    end else begin
      lbstr := RETLBComboBox.GetItemText (-1, 0);
      lpstr := RETLBComboBox.GetItemText (-1, 2);
    end;

    sellbidx := -1;
    sellpidx := -1;

    Screen.Cursor := crSQLWait;

    RETLBComboBox.Items.BeginUpdate;

    try
      ClearComboBoxObjects (RETLBComboBox);

      RETLBComboBox.Items.Add ('');

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select lb.REF,lb.NAME,lb.BESCHREIBUNG,lp.REF,nvl (lp.NAME,lp.LP_NR) from V_LB lb left outer join V_LP lp on (lp.REF_LB=lb.REF)');
      ADOQuery1.SQL.Add ('WHERE lb.LB_ART=''WE'' and lb.REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc) order by lb.WE_SPERR, lb.NAME, lp.NAME, lp.LP_NR');
      ADOQuery1.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

      try
        ADOQuery1.Open;

        while not (ADOQuery1.Eof) do begin
          if not (ADOQuery1.Fields [0].IsNull) then begin
            //Den WE-Bereich einmal auch ohne LP anzeigen
            if (FindComboboxRef (RETLBComboBox, ADOQuery1.Fields [0].AsInteger) = -1) then begin
              idx := RETLBComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString+'|', TComboBoxLBLPRef.Create(DBGetReferenz (ADOQuery1.Fields [0]), -1));

              if (sellbidx = -1) and (Length (lbstr) > 0) and (lbstr = ADOQuery1.Fields [1].AsString) then
                sellbidx := idx;
            end;

            if not (ADOQuery1.Fields [3].IsNull) then begin
              idx := RETLBComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString+'|'+ADOQuery1.Fields [4].AsString, TComboBoxLBLPRef.Create(DBGetReferenz (ADOQuery1.Fields [0]), DBGetReferenz (ADOQuery1.Fields [3])));

              if (Length (lpstr) > 0) and (lpstr = ADOQuery1.Fields [4].AsString) then
                sellpidx := idx;
            end;
          end;

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      except
      end;
    finally
      RETLBComboBox.Items.EndUpdate;

      Screen.Cursor := crDefault;
    end;

    if (sellpidx = -1) then
      RETLBComboBox.ItemIndex := sellbidx
    else RETLBComboBox.ItemIndex := sellpidx;

    if (RETLBComboBox.ItemIndex = -1) then RETLBComboBox.ItemIndex := 0;

    ClearComboBoxObjects (PackplatzComboBox);

    PackplatzComboBox.Items.Add ('');

    ADOQuery1.SQL.Clear;
    if (LVSDatenModul.AktLagerRef > 0) then begin
      ADOQuery1.SQL.Add ('select REF,NAME from V_WA_PACKPLATZ where STATUS=''AKT'' and REF_LAGER=:ref_lager order by lower (NAME)');
      ADOQuery1.Parameters [0].Value := LVSDatenModul.AktLagerRef;
    end else begin
      ADOQuery1.SQL.Add ('select REF,NAME from V_WA_PACKPLATZ where STATUS=''AKT'' and REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc) order by lower (NAME)');
      ADOQuery1.Parameters [0].Value := LVSDatenModul.AktLocationRef;
    end;

    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      idx := PackplatzComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

      if (ADOQuery1.Fields [1].AsString = LeitstandPackStation) then
        selidx := idx;

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;

    if (Length (LeitstandPackStation) = 0) then
      PackplatzComboBox.ItemIndex := 0
    else begin
      PackplatzComboBox.ItemIndex := PackplatzComboBox.Items.IndexOf (LeitstandPackStation);
      if (PackplatzComboBox.ItemIndex = -1) then PackplatzComboBox.ItemIndex := 0
    end;


    ClearComboBoxObjects (PickByLightStationComboBox);

    PickByLightStationComboBox.Items.Add ('');

    ADOQuery1.SQL.Clear;

    if (LVSDatenModul.AktLagerRef > 0) then begin
      ADOQuery1.SQL.Add ('select REF,REF_PICKANZEIGE_ZONE,NAME from V_WA_PACKPLATZ where STATUS=''AKT'' and OPT_BATCH_DIS=''1'' and REF_LAGER=:ref_lager order by lower (NAME)');
      ADOQuery1.Parameters [0].Value := LVSDatenModul.AktLagerRef;
    end else begin
      ADOQuery1.SQL.Add ('select REF,REF_PICKANZEIGE_ZONE,NAME from V_WA_PACKPLATZ where STATUS=''AKT'' and OPT_BATCH_DIS=''1'' and REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc) order by lower (NAME)');
      ADOQuery1.Parameters [0].Value := LVSDatenModul.AktLocationRef;
    end;

    ADOQuery1.Open;

    selidx := -1;

    while not (ADOQuery1.Eof) do begin
      idx := PickByLightStationComboBox.Items.AddObject (ADOQuery1.Fields [2].AsString, TComboboxPackplatz.Create (ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [1].AsInteger));

      if (ADOQuery1.Fields [2].AsString = LeitstandPickStation) then
        selidx := idx;

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;

    //LoadComboxDBItems (PickByLightStationComboBox, 'PICKANZEIGE', 'ANZEIGE_ZONE', False);

    if (PickByLightStationComboBox.Items.Count = 0) then begin
      PickByLightStationComboBox.Enabled := False;
      PickByLightStationComboBox.Text := '';
      PickByLightColorComboBox.Enabled := False;
    end else begin
      PickByLightStationComboBox.Enabled := True;
      PickByLightStationComboBox.ItemIndex := selidx;

      (*
      PickByLightColorComboBox.Enabled   := True;

      PickByLightStationComboBox.Items.Insert (0, '');

      PickByLightStationComboBox.ItemIndex := FindComboboxDBItem (PickByLightStationComboBox, LeitstandPickStation);
      if (PickByLightStationComboBox.ItemIndex = -1) then PickByLightStationComboBox.ItemIndex := 0;

      PickByLightColorComboBox.ItemIndex := -1;

      if (LeitstandPickColor > 0) and (LeitstandPickColor < PickByLightColorComboBox.Items.Count) then
        PickByLightColorComboBox.ItemIndex := LeitstandPickColor - 1;
      *)
    end;
  end;
end;

end.
