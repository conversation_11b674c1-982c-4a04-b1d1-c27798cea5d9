﻿//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : ACLTemplate
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/XE11/ACLTemplate.pas $
// $Revision: 30 $
// $Modtime: 15.01.24 22:17 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Anzeigen der ACOs in einem Tree
//*****************************************************************************
unit ACLTemplate;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ComCtrls, DB, ADODB, StdCtrls, ExtCtrls, ComboBoxPro, Menus, ACOTypen

  {$IFDEF VER360}
    ,System.ImageList, Vcl.ImgList
  {$ELSE}
    {$IFDEF VER350}
      ,System.ImageList, Vcl.ImgList
    {$else}
      ,ImgList
    {$endif}
  {$endif}
  ;

type
  TACLFrame = class(TFrame)
    ACOTreeView: TTreeView;
    Panel1: TPanel;
    Label1: TLabel;
    AppComboBox: TComboBoxPro;
    TreePopupMenu: TPopupMenu;
    ListederBenutzer1: TMenuItem;
    ImageList1: TImageList;
    GroupComboBox: TComboBoxPro;
    procedure AppComboBoxChange(Sender: TObject);
    procedure ListederBenutzer1Click(Sender: TObject);
    procedure ACOTreeViewDeletion(Sender: TObject; Node: TTreeNode);
  private
    fBaumstruktur : Boolean;
  public
    procedure Prepare (const AppName, Typen : String);

    function  RefreshACOQuery : Integer;

    constructor Create(AOwner: TComponent); override;
  end;

implementation

{$R *.dfm}

uses
  DatenModul, HTMLDisplayDLG, FrontendUtils, SprachModul;

constructor TACLFrame.Create(AOwner: TComponent);
begin
  inherited Create (AOwner);

  LVSSprachModul.InitFrame (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AppComboBox);
    LVSSprachModul.SetNoTranslate (Self, GroupComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindEintrag (startnode : TTreeNode; const nodename : String; var suchnode : TTreeNode) : Boolean;
var
  i : Integer;
  found : Boolean;
  name : String;
begin
  found := false;

//  if (startnode.Data <> Nil) then
//    found := false;

  if (startnode.Data <> Nil) then begin
    name := TACLNodeDaten (startnode.Data).ACOName;

    if (name = nodename) then
      found := true
    else if (name = '#' + nodename + '#') then
      found := true;
  end;

  if (found) then
    suchnode := startnode
  else begin
    i := 0;

    while not (found) and (i < startnode.Count) do begin
      if (FindEintrag (startnode.Item [i], nodename, suchnode)) then
        found := true
      else Inc (i);
    end;
  end;

  FindEintrag := found;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACLFrame.ListederBenutzer1Click(Sender: TObject);
var
  query   : TADOQuery;
  disp    : THTMLDisplay;
  acldata : TACLNodeDaten;
begin
  if Assigned (ACOTreeView.Selected) then begin
    if (ACOTreeView.Selected.Level = 1) then
      acldata := TACLNodeDaten (ACOTreeView.Selected.Parent.Data)
    else if (ACOTreeView.Selected.Level = 0) then
      acldata := TACLNodeDaten (ACOTreeView.Selected.Data)
    else
      acldata := Nil;

    if Assigned (acldata) then begin
      disp := THTMLDisplay.Create (Self);

      disp.Caption    := 'Übersicht der Rechtezuordnung';
      disp.RechteName := ACOTreeView.Selected.Text;
      disp.ACLData    := acldata;

      LoadComboxDBItems (disp.CustomerComboBox, 'SYS_BEN','FIRMA', True);
      disp.CustomerComboBox.Items.Insert (0, 'Alle Firmen');

      query := TADOQuery.Create (self);

      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        query.SQL.Add ('select distinct g.GROUPE_ID,g.GROUPE_NAME from V_SYS_BEN b, V_SYS_BEN_GRP g, V_SYS_ACO_LIST al'
                      +' where al.REF_ACO=:ref_aco and g.REF=al.REF_GRP and b.STATUS=''AKT'' and b.REF in (select REF_BEN from V_SYS_REL_BEN_GRP where REF_GRP=g.REF)');

        //Normal sterbliche dürfen den Schemabesitzer nicht sehen ;-)
        if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then begin
          query.SQL.Add ('and b.STATUS<>''DEL'' and (instr (PA_SYS_BENUTZER.GET_OBJECT_RECHTE (:ref_ben, b.REF_ACO), ''A'') is not null) and Upper (b.USER_ID)<>:user_id');
          query.Parameters.ParamByName ('ref_ben').Value := LVSDatenModul.AktUserRef;
          query.Parameters.ParamByName ('user_id').Value := UpperCase (LVSDatenModul.Schema);
        end;

        query.Parameters.ParamByName ('ref_aco').Value := acldata.ACORef;

        query.Open;

        while not (query.Eof) do begin
          disp.ACOGruppeComboBox.AddItem (query.Fields [0].AsString+'|'+query.Fields [1].AsString, Nil);

          query.Next;
        end;

        query.Close;
      finally
        query.Free;
      end;

      disp.ACOGruppeComboBox.Items.Insert (0, 'Alle Gruppen');

      disp.CustomerComboBox.ItemIndex  := 0;
      disp.ACOGruppeComboBox.ItemIndex := 0;

      disp.ShowModal;

      disp.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACLFrame.Prepare (const AppName, Typen : String);
var
  query : TADOQuery;
begin
  AppComboBox.OnChange := AppComboBoxChange;
  GroupComboBox.OnChange := AppComboBoxChange;

  query := TADOQuery.Create (self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    AppComboBox.Clear;
    AppComboBox.Items.Add ('<alle>');

    query.SQL.Clear;
    query.SQL.Add ('select NAME, BESCHREIBUNG from V_GRANTED_ACO where TYP=''APP''');

    try
      query.Open;

      while not (query.Eof) do begin
        AppComboBox.Items.Add (query.Fields[0].AsString+'|'+query.Fields[1].AsString);

        query.Next;
      end;

      query.Close;
    except
    end;

    AppComboBox.ItemIndex := AppComboBox.IndexOf(AppName);
    if (AppComboBox.ItemIndex = -1) then AppComboBox.ItemIndex := 0;

    GroupComboBox.Clear;
    GroupComboBox.Items.Add ('<alle>');

    query.SQL.Clear;
    query.SQL.Add ('select OBJECT_GROUP from V_SYS_ACO where OBJECT_GROUP is not null group by OBJECT_GROUP order by OBJECT_GROUP');

    try
      query.Open;

      while not (query.Eof) do begin
        GroupComboBox.Items.Add (query.Fields[0].AsString);

        query.Next;
      end;

      query.Close;
    except
    end;

    GroupComboBox.ItemIndex := 0;
  finally
    query.Free;
  end;

  RefreshACOQuery;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACLFrame.RefreshACOQuery : Integer;
var
  strpos    : Integer;
  name,
  nodename,
  teilname  : String;
  found     : Boolean;
  rootnode,
  newnode,
  selnode,
  insnode   : TTreeNode;
  i,
  res,
  selref    : Integer;
  suchnode  : TTreeNode;
  nodedaten : TACLNodeDaten;
  query     : TADOQuery;
begin
  res := 0;

  fBaumstruktur := False;

  if (ACOTreeView.Selected <> Nil) and (ACOTreeView.Selected.Data <> Nil) Then
    selref := TACLNodeDaten (ACOTreeView.Selected.Data).ACORef
  else selref := -1;

  ACOTreeView.Items.Clear;
  ACOTreeView.Items.BeginUpdate;

  newnode := nil;
  selnode := Nil;
  rootnode := Nil;

  query := TADOQuery.Create (self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select aco.REF, aco.OBJECT_NAME, aco.OBJECT_ID, aco.NAME, aco.BESCHREIBUNG, aco.TYP from V_GRANTED_ACO aco'
                 + ' where (aco.TYP <> ''BEN'' or nvl ((select STATUS from V_SYS_BEN where REF_ACO=aco.REF), ''ANG'')=''AKT'')');

    if (AppComboBox.ItemIndex > 0) Then begin
      query.SQL.Add ('and aco.APPLICATION=:app');
      query.Parameters.ParamByName ('app').Value := AppComboBox.GetItemText;
    end;

    if (GroupComboBox.ItemIndex > 0) Then begin
      query.SQL.Add ('and aco.OBJECT_GROUP=:grp');
      query.Parameters.ParamByName ('grp').Value := GroupComboBox.GetItemText;
    end;

    query.SQL.Add ('order by aco.TYP,aco.NAME,aco.OBJECT_NAME');

    try
      query.Open;

      while not (query.Eof) do begin
        if (query.Fields [4].IsNull) then begin
          if (query.Fields [3].IsNull) then
            name := query.Fields [1].AsString
          else name := query.Fields [3].AsString;
        end else begin
          name := query.Fields [4].AsString;

          if (query.Fields [3].IsNull) then
            name := name + ' (' + query.Fields [1].AsString + ')'
          else name := name + ' (' + query.Fields [3].AsString + ')';
        end;

        if (Length (name) > 0) then begin
          i := 0;

          nodedaten := TACLNodeDaten.Create;

          nodedaten.ACORef  := query.Fields [0].AsInteger;
          nodedaten.ACOName := query.Fields [1].AsString;
          nodedaten.ACOID   := query.Fields [2].AsInteger;

          if (query.Fields [5].AsString = 'FORM') then
            nodedaten.ACOType := acoForm
          else if (query.Fields [5].AsString = 'TAB') then
            nodedaten.ACOType := acoTab
          else if (query.Fields [5].AsString = 'BUTTON') then
            nodedaten.ACOType := acoButton
          else if (query.Fields [5].AsString = 'PANEL') then
            nodedaten.ACOType := acoPanel
          else if (query.Fields [5].AsString = 'MENU') then
            nodedaten.ACOType := acoMenu
          else if (query.Fields [5].AsString = 'BEN_GRP') then
            nodedaten.ACOType := acoBenGrp
          else if (query.Fields [5].AsString = 'LAGER') then
            nodedaten.ACOType := acoLager
          else if (query.Fields [5].AsString = 'MANDANT') then
            nodedaten.ACOType := acoMand
          else if (query.Fields [5].AsString = 'APP') then
            nodedaten.ACOType := acoApp
          else if (query.Fields [5].AsString = 'BEN') then
            nodedaten.ACOType := acoBen
          else if (query.Fields [5].AsString = 'LOC') then
            nodedaten.ACOType := acoLoc
          else if (query.Fields [5].AsString = 'QUERY') then
            nodedaten.ACOType := acoQuery
          else if (query.Fields [5].AsString = 'ACO') then
            nodedaten.ACOType := acoACO
          else if (query.Fields [5].AsString = 'DMS_GROUP') then
            nodedaten.ACOType := acoDMSGrp
          else if (query.Fields [5].AsString = 'PRJ') then
            nodedaten.ACOType := acoPrj
          else if (query.Fields [5].AsString = 'BES') then
            nodedaten.ACOType := acoBes
          else
            nodedaten.ACOType := acoNone;

          if (not fBaumstruktur) then begin
            newnode := ACOTreeView.Items.AddChild (rootnode, name);

            ACOTreeView.Items.AddChild (newnode, query.Fields [1].AsString);

            if (query.Fields [5].AsString = 'FORM') then
              newnode.ImageIndex := 0
            else if (query.Fields [5].AsString = 'TAB') then
              newnode.ImageIndex := 1
            else if (query.Fields [5].AsString = 'BUTTON') then
              newnode.ImageIndex := 2
            else if (query.Fields [5].AsString = 'MENU') then
              newnode.ImageIndex := 3
            else if (query.Fields [5].AsString = 'PANEL') then
              newnode.ImageIndex := 12
            else if (query.Fields [5].AsString = 'BEN_GRP') then
              newnode.ImageIndex := 4
            else if (query.Fields [5].AsString = 'LAGER') then
              newnode.ImageIndex := 9
            else if (query.Fields [5].AsString = 'MANDANT') then
              newnode.ImageIndex := 6
            else if (query.Fields [5].AsString = 'APP') then
              newnode.ImageIndex := 7
            else if (query.Fields [5].AsString = 'BEN') then
              newnode.ImageIndex := 8
            else if (query.Fields [5].AsString = 'LOC') then
              newnode.ImageIndex := 5
            else if (query.Fields [5].AsString = 'QUERY') then
              newnode.ImageIndex := 10
            else if (query.Fields [5].AsString = 'ACO') then
              newnode.ImageIndex := 11
            else if (query.Fields [5].AsString = 'FIRMA') then
              newnode.ImageIndex := 13
            else if (query.Fields [5].AsString = 'PRJ') then
              newnode.ImageIndex := 14
            else if (query.Fields [5].AsString = 'DMS_GROUP') then
              newnode.ImageIndex := 10
            else if (query.Fields [5].AsString = 'BES') then
              newnode.ImageIndex := 15
            else newnode.ImageIndex := -1;

            newnode.SelectedIndex := newnode.ImageIndex;
          end else begin
            strpos := 1;
            insnode := rootnode;
            while (strpos <= Length (name)) do begin
              teilname := '';

              while (strpos <= Length (name)) and (name [strpos] <> '.') do begin
                teilname := teilname + name [strpos];
                Inc (strpos);
              end;

              if (strpos > Length (name)) then
                nodename := name
              else begin
                nodename := Copy (name, 1, strpos - 1);
                Inc (strpos);
              end;

              found := false;

              while not (found) and (i < ACOTreeView.Items.Count) do begin
                if (FindEintrag (ACOTreeView.Items [i], nodename, suchnode)) then
                  found := true
                else  Inc (i);
              end;

              if (found) then
                insnode := suchnode
              else begin
                newnode := ACOTreeView.Items.AddChild (insnode, teilname);
                insnode := newnode;
              end;
            end;
          end;

          newnode.Data := nodedaten;

          if (selref = nodedaten.ACORef) and (selnode = Nil) then
            selnode := newnode;
        end;

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  ACOTreeView.Items.EndUpdate;

  ACOTreeView.Selected := selnode;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACLFrame.ACOTreeViewDeletion(Sender: TObject; Node: TTreeNode);
begin
  if Assigned (Node.Data) then
    TACLNodeDaten (Node.Data).Free;

  Node.Data := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACLFrame.AppComboBoxChange(Sender: TObject);
begin
  RefreshACOQuery;
end;

end.
