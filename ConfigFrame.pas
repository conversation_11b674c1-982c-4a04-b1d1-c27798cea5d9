unit ConfigFrame;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms, 
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls, Menus, ComCtrls;

type
  TConfigVarFrame = class(TFrame)
    VarNameLabel: TLabel;
    MandantComboBox: TComboBoxPro;
    LagerComboBox: TComboBoxPro;
    Bevel1: TBevel;
    PopupMenu1: TPopupMenu;
    Lschen1: TMenuItem;
    Label6: TLabel;
    Label7: TLabel;
    VarTextEdit: TEdit;
    PageControl1: TPageControl;
    BaseTabSheet: TTabSheet;
    LimitTabSheet: TTabSheet;
    AlarmTabSheet: TTabSheet;
    Label1: TLabel;
    VarStrEdit: TEdit;
    Label8: TLabel;
    VarNumEdit: TEdit;
    Label4: TLabel;
    VarMinEdit: TEdit;
    Label5: TLabel;
    VarMaxEdit: TEdit;
    Label3: TLabel;
    VarLenEdit: TEdit;
    CycleCheckBox: TCheckBox;
    VarDescMemo: TMemo;
    ValAlarmLimitEdit: TEdit;
    Label2: TLabel;
    InfoTabSheet: TTabSheet;
    Label9: TLabel;
    Label10: TLabel;
    CreateLabel: TLabel;
    DataChangeLabel: TLabel;
    Label13: TLabel;
    ValChangeLabel: TLabel;
    FlagTabSheet: TTabSheet;
    TextTabSheet: TTabSheet;
    Label11: TLabel;
    VarStrTextEdit: TEdit;
    TrueRadioButton: TRadioButton;
    FalseRadioButton: TRadioButton;
    NumTabSheet: TTabSheet;
    Label12: TLabel;
    VarIntegerEdit: TEdit;
    procedure Lschen1Click(Sender: TObject);
    procedure EditChange(Sender: TObject);
    procedure CycleCheckBoxClick(Sender: TObject);
    procedure VarNumEditKeyPress(Sender: TObject; var Key: Char);
    procedure FrameResize(Sender: TObject);
  private
    fDeleted,
    fChanged  : Boolean;

    fOnDataChange : TNotifyEvent;

    procedure SetChanged (Flag : Boolean);
  public
    property IsDeleted : Boolean read fDeleted;
    property IsChanged : Boolean read fChanged write SetChanged;

    property OnDataChange : TNotifyEvent read fOnDataChange write fOnDataChange;

    constructor Create(AOwner: TComponent); override;
  end;

implementation

{$R *.dfm}

uses
  SprachModul, ResourceText;

constructor TConfigVarFrame.Create(AOwner: TComponent);
begin
  inherited Create (AOwner);

  TextTabSheet.TabVisible := False;
  FlagTabSheet.TabVisible := False;
  NumTabSheet.TabVisible := False;

  PageControl1.ActivePage := BaseTabSheet;

  fDeleted := False;
  fChanged := False;

  CreateLabel.Caption := '';
  DataChangeLabel.Caption := '';
  ValChangeLabel.Caption := '';

  LVSSprachModul.InitFrame (Self);
end;

procedure TConfigVarFrame.Lschen1Click(Sender: TObject);
begin
  if (MessageDlg (FormatMessageText (1169, []), mtConfirmation, [mbYes,mbNo], 0) = mrYes) then begin
    Visible := False;
    fChanged := True;
    fDeleted := True;

    if Assigned (fOnDataChange) then
      fOnDataChange (Self);
  end;
end;

procedure TConfigVarFrame.VarNumEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,^C,^V,#9,'0'..'9']) then
    Key := #0;
end;

procedure TConfigVarFrame.CycleCheckBoxClick(Sender: TObject);
begin
  fChanged := True;

  VarNameLabel.Font.Color := clRed;

  if Assigned (fOnDataChange) then
    fOnDataChange (Self);
end;

procedure TConfigVarFrame.EditChange(Sender: TObject);
begin
  fChanged := True;

  VarNameLabel.Font.Color := clRed;

  if Assigned (fOnDataChange) then
    fOnDataChange (Self);
end;

procedure TConfigVarFrame.FrameResize(Sender: TObject);
var
  w : Integer;
begin
  w := PageControl1.Width;

  MandantComboBox.Width := (w - Label6.Width - 16 - 8) div 2;
  LagerComboBox.Width := (w - Label6.Width - 16 - 8) div 2;

  Label6.Left := MandantComboBox.Left + MandantComboBox.Width + 16;
  LagerComboBox.Left := MandantComboBox.Left + MandantComboBox.Width + 16 + Label6.Width + 8;
end;

procedure TConfigVarFrame.SetChanged (Flag : Boolean);
begin
  fChanged := Flag;

  if (fChanged) then
    VarNameLabel.Font.Color := clRed
  else
    VarNameLabel.Font.Color := clWindowText;
end;

end.
