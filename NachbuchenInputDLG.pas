unit NachbuchenInputDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TNachbuchenInputForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    MengeEdit: TEdit;
    Label1: TLabel;
    GewichtEdit: TEdit;
    Label2: TLabel;
    Label3: TLabel;
    Bevel1: TBevel;
    procedure MengeEditKeyPress(Sender: TObject; var Key: Char);
    procedure GewichtEditKeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

procedure TNachbuchenInputForm.MengeEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in ['0'..'9', #8,^C,^V]) then
    Key := #0
end;

procedure TNachbuchenInputForm.GewichtEditKeyPress(Sender: TObject; var Key: Char);
var
  wert : Double; 
begin
  if (Key = #8) then
  else if not (Key in [',','.','0'..'9',^C,^V]) then
    Key := #0
  else if not TryStrToFloat((Sender as TEDit).Text + Key, wert) then
    Key := #0;
end;

procedure TNachbuchenInputForm.FormCreate(Sender: TObject);
begin
  MengeEdit.Text := '';
  GewichtEdit.Text := '';
end;

end.
