object AuftragPosVerpackenForm: TAuftragPosVerpackenForm
  Left = 0
  Top = 0
  BorderIcons = []
  Caption = ' '
  ClientHeight = 873
  ClientWidth = 1319
  Color = clBtnFace
  Constraints.MinHeight = 480
  Constraints.MinWidth = 600
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  TextHeight = 13
  object InhaltPanel: TPanel
    Left = 0
    Top = 49
    Width = 1319
    Height = 123
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      1319
      123)
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 51
      Height = 13
      Caption = 'Auftragnr.'
    end
    object AuftragNrLabel: TLabel
      Left = 104
      Top = 8
      Width = 85
      Height = 13
      Caption = 'AuftragNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label3: TLabel
      Left = 8
      Top = 27
      Width = 52
      Height = 13
      Caption = 'Empf'#228'nger'
    end
    object WarenempfLabel: TLabel
      Left = 104
      Top = 27
      Width = 393
      Height = 13
      AutoSize = False
      Caption = 'WarenempfLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
      ShowAccelChar = False
    end
    object Label2: TLabel
      Left = 8
      Top = 80
      Width = 38
      Height = 13
      Caption = 'NVE-Nr.'
    end
    object NVELabel: TLabel
      Left = 104
      Top = 80
      Width = 50
      Height = 13
      Caption = 'NVELabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Bevel1: TBevel
      Left = 6
      Top = 119
      Width = 1307
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 1242
    end
    object Label8: TLabel
      Left = 503
      Top = 27
      Width = 77
      Height = 13
      Caption = 'Lieferscheintext'
    end
    object LSTextLabel: TLabel
      Left = 600
      Top = 24
      Width = 637
      Height = 50
      AutoSize = False
      Caption = 'LSTextLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
      ShowAccelChar = False
      WordWrap = True
    end
    object Label10: TLabel
      Left = 503
      Top = 8
      Width = 57
      Height = 13
      Caption = 'Lieferdatum'
    end
    object LieferDatumLabel: TLabel
      Left = 600
      Top = 8
      Width = 100
      Height = 13
      Caption = 'LieferDatumLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object FachLabel: TLabel
      Left = 1205
      Top = 6
      Width = 105
      Height = 25
      Alignment = taRightJustify
      Anchors = [akTop, akRight]
      Caption = 'FachLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
      ExplicitLeft = 1140
    end
    object ContainerLabel: TLabel
      Left = 1065
      Top = 121
      Width = 242
      Height = 39
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Alignment = taRightJustify
      Anchors = [akTop, akRight]
      Caption = 'ContainerLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -32
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
      ExplicitLeft = 1000
    end
    object NewNVEButton: TButton
      Left = 256
      Top = 72
      Width = 129
      Height = 31
      Caption = 'In neue NVE packen'
      TabOrder = 0
      OnClick = NewNVEButtonClick
    end
  end
  object ButtonPanel: TPanel
    Left = 0
    Top = 793
    Width = 1319
    Height = 80
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 7
    DesignSize = (
      1319
      80)
    object Bevel2: TBevel
      Left = 6
      Top = 1
      Width = 1307
      Height = 13
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 1242
    end
    object AbortButton: TButton
      Left = 1160
      Top = 20
      Width = 150
      Height = 50
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Abbrechen'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ModalResult = 3
      ParentFont = False
      TabOrder = 2
    end
    object VerpackenButton: TButton
      Left = 8
      Top = 20
      Width = 150
      Height = 50
      Caption = 'Verpacken'
      Default = True
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ModalResult = 1
      ParentFont = False
      TabOrder = 0
    end
    object ReprintAdvertisingButton: TButton
      Left = 873
      Top = 20
      Width = 264
      Height = 50
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Werbung nachdrucken'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 1
      OnClick = ReprintAdvertisingButtonClick
    end
  end
  object FehlerPanel: TPanel
    AlignWithMargins = True
    Left = 8
    Top = 568
    Width = 1303
    Height = 50
    Margins.Left = 8
    Margins.Right = 8
    Align = alBottom
    BevelOuter = bvNone
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 5
    DesignSize = (
      1303
      50)
    object FehlerButton: TButton
      Left = 1101
      Top = 6
      Width = 195
      Height = 38
      Action = ErrorConfirmAction
      Anchors = [akTop, akRight, akBottom]
      TabOrder = 0
    end
  end
  object InfoPanel: TPanel
    Left = 0
    Top = 621
    Width = 1319
    Height = 172
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 6
    DesignSize = (
      1319
      172)
    object VerpackungLabel: TLabel
      Left = 8
      Top = 7
      Width = 120
      Height = 19
      Caption = 'VerpackungLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      ShowAccelChar = False
    end
    object PresentLabel: TLabel
      Left = 8
      Top = 59
      Width = 1232
      Height = 25
      Alignment = taCenter
      AutoSize = False
      Caption = 'PresentLabel'
      Color = clBtnFace
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clRed
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentColor = False
      ParentFont = False
      ShowAccelChar = False
    end
    object VPEAnzLabel: TLabel
      Left = 1203
      Top = 6
      Width = 107
      Height = 19
      Alignment = taRightJustify
      Anchors = [akTop, akRight]
      Caption = 'VPEAnzLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
      ExplicitLeft = 1138
    end
    object Label4: TLabel
      Left = 8
      Top = 124
      Width = 148
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Bruttogewicht inkl. Verpackung'
    end
    object Label5: TLabel
      Left = 74
      Top = 144
      Width = 11
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'kg'
    end
    object VersandKartonLabel: TLabel
      Left = 9
      Top = 34
      Width = 140
      Height = 19
      Caption = 'VersandKartonLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      ShowAccelChar = False
    end
    object VerpackMengeLabel: TLabel
      Left = 914
      Top = 130
      Width = 323
      Height = 39
      Alignment = taRightJustify
      Anchors = [akLeft, akBottom]
      Caption = 'VerpackMengeLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -32
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
      ExplicitTop = 80
    end
    object PackHinweisLabel: TLabel
      Left = 7
      Top = 76
      Width = 1232
      Height = 25
      Alignment = taCenter
      AutoSize = False
      Caption = 'PackHinweisLabel'
      Color = clBtnFace
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clGreen
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentColor = False
      ParentFont = False
      ShowAccelChar = False
    end
    object PackHintLabel: TLabel
      Left = 8
      Top = 98
      Width = 1232
      Height = 25
      Alignment = taCenter
      AutoSize = False
      Caption = 'PackHintLabel'
      Color = clBtnFace
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlue
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentColor = False
      ParentFont = False
      ShowAccelChar = False
    end
    object BruttoGewichtEdit: TEdit
      Left = 8
      Top = 141
      Width = 60
      Height = 21
      Anchors = [akLeft, akBottom]
      TabOrder = 0
      Text = '2'
      OnChange = BruttoGewichtEditChange
      OnKeyPress = BruttoGewichtEditKeyPress
    end
    object AbmessungPanel: TPanel
      Left = 528
      Top = 131
      Width = 337
      Height = 36
      Anchors = [akLeft, akBottom]
      TabOrder = 1
      object Label11: TLabel
        Left = 170
        Top = 13
        Width = 6
        Height = 13
        Caption = 'x'
      end
      object Label12: TLabel
        Left = 240
        Top = 13
        Width = 6
        Height = 13
        Caption = 'x'
      end
      object Label13: TLabel
        Left = 40
        Top = 13
        Width = 55
        Height = 13
        Caption = 'Abmessung'
      end
      object Label14: TLabel
        Left = 311
        Top = 13
        Width = 13
        Height = 13
        Caption = 'cm'
      end
      object PaintBox2: TPaintBox
        Left = 8
        Top = 6
        Width = 24
        Height = 24
      end
      object LTLength: TEdit
        Left = 114
        Top = 10
        Width = 50
        Height = 21
        MaxLength = 4
        TabOrder = 0
        Text = 'LTLength'
        OnKeyPress = LTDimKeyPress
      end
      object LTWidth: TEdit
        Left = 184
        Top = 10
        Width = 50
        Height = 21
        MaxLength = 4
        TabOrder = 1
        Text = 'LTWidth'
        OnKeyPress = LTDimKeyPress
      end
      object LTHeigth: TEdit
        Left = 254
        Top = 10
        Width = 50
        Height = 21
        MaxLength = 4
        TabOrder = 2
        Text = 'LTHeigth'
        OnKeyPress = LTDimKeyPress
      end
    end
    object WaagePanel: TPanel
      Left = 200
      Top = 131
      Width = 297
      Height = 36
      TabOrder = 2
      OnDblClick = WaagePanelDblClick
      object WaageLabel: TLabel
        Left = 40
        Top = 13
        Width = 59
        Height = 13
        Caption = 'WaageLabel'
      end
      object WaageGewichtLabel: TLabel
        Left = 184
        Top = 13
        Width = 97
        Height = 13
        Alignment = taRightJustify
        Caption = 'WaageGewichtLabel'
      end
      object WaagePaintBox: TPaintBox
        Left = 5
        Top = 6
        Width = 24
        Height = 24
        OnPaint = WaagePaintBoxPaint
      end
      object WaageLEDPanel: TPanel
        Left = 136
        Top = 10
        Width = 16
        Height = 16
        Color = clMaroon
        ParentBackground = False
        TabOrder = 0
      end
    end
  end
  object ListPanel: TPanel
    Left = 0
    Top = 314
    Width = 1319
    Height = 251
    Align = alClient
    BevelOuter = bvNone
    Constraints.MinHeight = 20
    TabOrder = 4
    OnResize = ListPanelResize
    DesignSize = (
      1319
      251)
    object Bevel7: TBevel
      Left = 6
      Top = 3
      Width = 1307
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 1242
    end
    object ArtikelImagePanel: TPanel
      Left = 503
      Top = 13
      Width = 161
      Height = 161
      BevelOuter = bvSpace
      Color = clWhite
      TabOrder = 0
      object ArtikelImage: TImage32
        Left = 1
        Top = 1
        Width = 159
        Height = 159
        Align = alClient
        Bitmap.ResamplerClassName = 'TDraftResampler'
        BitmapAlign = baCenter
        Scale = 1.000000000000000000
        ScaleMode = smOptimal
        TabOrder = 0
      end
    end
    object VerpackAufPosListView: TListView
      Left = 9
      Top = 15
      Width = 472
      Height = 216
      Checkboxes = True
      Columns = <>
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'Tahoma'
      Font.Style = []
      GridLines = True
      MultiSelect = True
      ReadOnly = True
      RowSelect = True
      ParentFont = False
      PopupMenu = VerpackAufPosListViewPopupMenu
      TabOrder = 1
      ViewStyle = vsReport
      OnChange = VerpackAufPosListViewChange
      OnClick = VerpackAufPosListViewClick
      OnColumnClick = VerpackAufPosListViewColumnClick
      OnCompare = VerpackAufPosListViewCompare
      OnDeletion = VerpackAufPosListViewDeletion
      OnKeyDown = VerpackAufPosListViewKeyDown
      OnKeyPress = VerpackAufPosListViewKeyPress
      OnMouseDown = VerpackAufPosListViewMouseDown
      OnMouseUp = VerpackAufPosListViewMouseUp
    end
    object SummenPanel: TPanel
      Left = 0
      Top = 232
      Width = 1319
      Height = 19
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 2
      object SummeVPELabel: TLabel
        Left = 8
        Top = 5
        Width = 77
        Height = 13
        Caption = 'SummeVPELabel'
      end
    end
  end
  object WaBoxePanel: TPanel
    Left = 0
    Top = 273
    Width = 1319
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      1319
      41)
    object Bevel3: TBevel
      Left = 6
      Top = 0
      Width = 1307
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 1242
    end
    object Label7: TLabel
      Left = 9
      Top = 9
      Width = 73
      Height = 13
      Caption = 'Beleget Boxen:'
    end
    object UsedBoxLabel: TLabel
      Left = 104
      Top = 3
      Width = 148
      Height = 25
      Caption = 'UsedBoxLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object EmmasbocPanel: TPanel
    Left = 0
    Top = 232
    Width = 1319
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      1319
      41)
    object Label9: TLabel
      Left = 9
      Top = 9
      Width = 55
      Height = 13
      Caption = 'Emmasbox:'
    end
    object EmmasboxResLabel: TLabel
      Left = 104
      Top = 3
      Width = 148
      Height = 25
      Caption = 'UsedBoxLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Bevel4: TBevel
      Left = 6
      Top = 0
      Width = 1307
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 1242
    end
  end
  object SubMandantPanel: TPanel
    Left = 0
    Top = 0
    Width = 1319
    Height = 49
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      1319
      49)
    object SubMandantLabel: TLabel
      Left = 8
      Top = 11
      Width = 188
      Height = 25
      Caption = 'SubMandantLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
      ShowAccelChar = False
    end
    object Bevel5: TBevel
      Left = 6
      Top = 45
      Width = 1307
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 1242
    end
  end
  object SpedPanel: TPanel
    AlignWithMargins = True
    Left = 8
    Top = 172
    Width = 1303
    Height = 60
    Margins.Left = 8
    Margins.Top = 0
    Margins.Right = 8
    Margins.Bottom = 0
    Align = alTop
    BevelOuter = bvNone
    ParentBackground = False
    TabOrder = 8
    DesignSize = (
      1303
      60)
    object Label6: TLabel
      Left = 0
      Top = 22
      Width = 48
      Height = 13
      Caption = 'Spedition:'
    end
    object SpedLabel: TLabel
      Left = 344
      Top = 17
      Width = 107
      Height = 25
      Caption = 'SpedLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
      ShowAccelChar = False
    end
    object SpedImage: TImage
      Left = 96
      Top = 4
      Width = 183
      Height = 50
      Center = True
      Proportional = True
      Stretch = True
    end
    object ChangeSpedButton: TButton
      Left = 1099
      Top = 13
      Width = 195
      Height = 34
      Anchors = [akTop, akRight]
      Caption = 'Spedition wechseln'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      OnClick = ChangeSpedButtonClick
    end
  end
  object ActionList1: TActionList
    Left = 920
    Top = 32
    object ErrorConfirmAction: TAction
      Caption = 'Best'#228'tigen (F8)'
      Hint = 'Fehlermeldung best'#228'tigen'
      ShortCut = 119
      OnExecute = ErrorConfirmActionExecute
    end
  end
  object VerpackAufPosListViewPopupMenu: TPopupMenu
    AutoPopup = False
    OnPopup = VerpackAufPosListViewPopupMenuPopup
    Left = 816
    Top = 104
    object Zellekopieren1: TMenuItem
      Caption = 'Zelleninhalt in die Zwischenablage kopieren'
      OnClick = Zellekopieren1Click
    end
    object N5: TMenuItem
      Caption = '-'
    end
    object SelectAllMenuItem: TMenuItem
      Caption = 'Alle ausw'#228'hlen'
      OnClick = SelectAllMenuItemClick
    end
    object DeselectAllMenuItem: TMenuItem
      Caption = 'Auswahl aufheben'
      OnClick = DeselectAllMenuItemClick
    end
    object N4: TMenuItem
      Caption = '-'
    end
    object BulkPackMenuItem: TMenuItem
      Caption = 'Bulk-Verpacken...'
      Visible = False
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object ChangeGewichtMenuItem: TMenuItem
      Caption = 'Gewicht '#228'ndern....'
      OnClick = ChangeGewichtMenuItemClick
    end
    object N3: TMenuItem
      Caption = '-'
    end
    object PrintEANLabelMenuItem: TMenuItem
      Caption = 'EAN13 Label drucken...'
      OnClick = PrintLabelMenuItemClick
    end
    object PrintBarcodeLabelMenuItem: TMenuItem
      Caption = 'Barcode Label drucken...'
      OnClick = PrintLabelMenuItemClick
    end
    object PrintIDLabelMenuItem: TMenuItem
      Caption = 'SKU Label drucken...'
      OnClick = PrintLabelMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object ColumnsResizeMenuItem: TMenuItem
      Caption = 'Spalten neu ausrichten'
      OnClick = ColumnsResizeMenuItemClick
    end
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 376
    Top = 48
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 960
    Top = 32
  end
end
