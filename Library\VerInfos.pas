//******************************************************************************
//* Modul Name: VerInfos
//* Author    : Stefan Graf
//******************************************************************************
//* $Revision: 2 $
//* $Date: 7.11.17 11:44 $
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
unit VerInfos;

interface

type
  TFileInfos = record
    FileDescription : String;
    FileVersion : String;
    ProductName : String;
    ProductVersion : String;
    CompanyName : String;
    Comments : String;
    InternalName : String;
    LegalCopyright : String;
  end;

function FileVersion  (Level, Stellen : Integer): String;
function GetFileInfos (const FileName : String; var FileInfos : TFileInfos): Integer;
function GetWindowsVersion:string;

implementation

uses Windows, Classes, SysUtils, StringUtils;

{-------------- FileversionInfo-----------------}
type
  TTranslationPair = packed record
    Lang,
    CharSet: word;
  end;

  PTranslationIDList = ^TTranslationIDList;
  TTranslationIDList = array[0..MAXINT div SizeOf(TTranslationPair)-1] of TTranslationPair;

const
  TRANSLATION_INFO = '\VarFileInfo\Translation';

type
  TQueryStr = String [64];

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function QueryLangFileInfo (const VersionInfo: PChar) : String;
type
  chararray = array [0..2048] of char;
var
  resstr : String;
  qstr   : array [0..256] of char;
  cstr   : ^chararray;
  clen   : UINT;
begin
  resstr := '';

  clen := 0;
  cstr := Nil;

  StrPCopy (qstr, '\VarFileInfo\Translation');
  if VerQueryValue(VersionInfo, qstr, Pointer (cstr), clen) then begin
    if (cstr = Nil) Then
      resstr := ''
    else begin
      resstr := IntToHex (Ord (cstr^[1]),2)+IntToHex (Ord (cstr^[0]),2)+IntToHex (Ord (cstr^[3]),2)+IntToHex (Ord (cstr^[2]),2);
    end;
  end;

  QueryLangFileInfo := resstr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function QueryStringFileInfo (const VersionInfo: PChar; const Language : String; const QueryStr : TQueryStr) : String;
type
  chararray = array [0..2048] of char;
var
  resstr : String;
  qstr   : array [0..256] of char;
  cstr : ^chararray;
  clen : UINT;
begin
  resstr := '';

  clen := 0;
  cstr := Nil;


  StrPCopy (qstr, '\StringFileInfo\'+Language+'\' + QueryStr);

  if VerQueryValue(VersionInfo, qstr, Pointer (cstr), clen) then begin
    if (cstr = Nil) Then
      resstr := ''
    else begin
      if (clen < 256) then
        cstr^[clen] := #0
      else cstr^[256] := #0;

      resstr := StrPas (cstr^);
    end;
  end;

  QueryStringFileInfo := resstr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FileVersion (Level, Stellen : Integer): String;
var
  Dummy : DWORD;
  VInfoSize : DWord;
  TempFile  : array[0..255] of char;
  FVersionInfo: PChar;
  FixedInfoData: PVSFixedFileInfo;
  QueryLen: UINT;
  IDs: PTranslationIDList;
  IDsLen: UINT;
  IDCount: integer;
  FTranslationIDs: TStringList;
  FMostSignificant: DWORD;
  FLeastSignificant: DWORD;
begin
  Result:='';
  FVersionInfo := nil;
  FTranslationIDs := nil;

  StrPCopy(TempFile, ExtractFileName (ParamStr (0)));
  VInfoSize := GetFileVersionInfoSize(TempFile, Dummy);

  if VInfosize > 0 then begin
    try
     FTranslationIDs:=TStringList.Create;
     GetMem(FVersionInfo, VInfoSize);
     GetFileVersionInfo(TempFile, Dummy, VInfoSize, FVersionInfo);
     VerQueryValue(FVersionInfo, '\', pointer(FixedInfoData), QueryLen);

     if VerQueryValue(FVersionInfo, TRANSLATION_INFO, Pointer(IDs), IDsLen) then begin
       IDCount := IDsLen div SizeOf(TTranslationPair);

       for Dummy := 0 to IDCount-1 do
         FTranslationIDs.Add(Format('%.3x%.3x', [IDs^[Dummy].Lang,IDs^[Dummy].CharSet]));

       {$ifdef Unicode}
         FMostSignificant  := FixedInfoData.dwFileVersionMS;
         FLeastSignificant := FixedInfoData.dwFileVersionLS;
       {$else}
         FMostSignificant  := FixedInfoData.dwProductVersionMS;
         FLeastSignificant := FixedInfoData.dwProductVersionLS;
       {$endif}

       Result := IntToStr (HiWord(FMostSignificant));

       if (Level > 1) then
         Result:= Result + '.' + FormatIntToStr (LoWord (FMostSignificant), Stellen);

       if (Level > 2) then
         Result:= Result + '.' + FormatIntToStr (HiWord(FLeastSignificant), Stellen);

       if (Level > 3) then
         Result:= Result + '.' + FormatIntToStr (LoWord(FLeastSignificant), Stellen);
     end;

    finally
      if Assigned (FVersionInfo) then
        FreeMem(FVersionInfo, VInfoSize);

      if Assigned (FTranslationIDs) then
        FTranslationIDs.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetFileInfos (const FileName : String; var FileInfos : TFileInfos): Integer;
var
  verinfo  : PChar;
  Dummy    : DWORD;
  fstr     : array [0..1024] of char;
  verlen   : UINT;
  lang     : String;
begin
  verinfo := Nil;
  FillChar (FileInfos, Sizeof (TFileInfos), 0);

  StrPCopy (fstr, FileName);

  verlen := GetFileVersionInfoSize (fstr, Dummy);

  if (verlen > 0) then begin
    GetMem (verinfo, verlen + 256);

    try
      if (GetFileVersionInfo(fstr, Dummy, verlen, verinfo)) then begin
        lang := QueryLangFileInfo (verinfo);

        FileInfos.ProductName := QueryStringFileInfo (verinfo, lang, 'ProductName');
        FileInfos.ProductVersion := QueryStringFileInfo (verinfo, lang, 'ProductVersion');
        FileInfos.FileDescription := QueryStringFileInfo (verinfo, lang, 'FileDescription');
        FileInfos.FileVersion := QueryStringFileInfo (verinfo, lang, 'FileVersion');
        FileInfos.CompanyName := QueryStringFileInfo (verinfo, lang, 'CompanyName');
        FileInfos.Comments := QueryStringFileInfo (verinfo, lang, 'Comments');
        FileInfos.InternalName := QueryStringFileInfo (verinfo, lang, 'InternalName');
        FileInfos.LegalCopyright := QueryStringFileInfo (verinfo, lang, 'LegalCopyright');
      end;
    finally
      if (verinfo <> Nil) then
        FreeMem (verinfo);
    end;
  end;

  GetFileInfos := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetWindowsVersion:string;
var
  OsVinfo   : TOSVERSIONINFO;
  HilfStr   : array[0..128] of Char;
begin
  ZeroMemory(@OsVinfo,sizeOf(OsVinfo));
  OsVinfo.dwOSVersionInfoSize := sizeof(TOSVERSIONINFO);
  if GetVersionEx(OsVinfo) then begin
    if OsVinfo.dwPlatformId = VER_PLATFORM_WIN32_WINDOWS then
     begin
      if (OsVinfo.dwMajorVersion = 4) and
       (OsVinfo.dwMinorVersion > 0) then
        StrFmt(HilfStr,'Windows 98 - Version %d.%.2d.%d',
               [OsVinfo.dwMajorVersion, OsVinfo.dwMinorVersion,
                OsVinfo.dwBuildNumber AND $FFFF])
      else
        StrFmt(HilfStr,'Windows 95 - Version %d.%d Build %d',
               [OsVinfo.dwMajorVersion, OsVinfo.dwMinorVersion,
                OsVinfo.dwBuildNumber AND $FFFF]);
    end;
    if OsVinfo.dwPlatformId = VER_PLATFORM_WIN32_NT then
      StrFmt(HilfStr,'Microsoft Windows NT Version %d.%.2d.%d %s',
             [OsVinfo.dwMajorVersion, OsVinfo.dwMinorVersion,
              OsVinfo.dwBuildNumber AND $FFFF,OsVinfo.szCSDVersion]);
  end
  else
    StrCopy(HilfStr,'Fehler bei GetversionEx()!');
  Result:=string(HilfStr);
end;

end.