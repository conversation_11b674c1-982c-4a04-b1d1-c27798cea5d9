object LieferantenForm: TLieferantenForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Lieferanten'
  ClientHeight = 453
  ClientWidth = 795
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    795
    453)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 55
    Height = 13
    Caption = 'Lieferanten'
  end
  object LieferantenDBGrid: TDBGridPro
    Left = 8
    Top = 24
    Width = 689
    Height = 209
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = LieferantenDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgCol<PERSON>ines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    OnDblClick = EditButtonClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\CS'
    RegistrySection = 'DBGrids'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
    OnColumnSort = LieferantenDBGridColumnSort
  end
  object NewButton: TButton
    Left = 704
    Top = 24
    Width = 85
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Neu...'
    TabOrder = 1
    OnClick = NewButtonClick
  end
  object EditButton: TButton
    Left = 704
    Top = 55
    Width = 85
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Bearbeiten...'
    TabOrder = 2
    OnClick = EditButtonClick
  end
  object DelButton: TButton
    Left = 704
    Top = 96
    Width = 85
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'L'#246'schen'
    TabOrder = 3
    OnClick = DelButtonClick
  end
  object Button1: TButton
    Left = 714
    Top = 420
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    TabOrder = 4
    OnClick = Button1Click
  end
  object GroupBox1: TGroupBox
    Left = 8
    Top = 248
    Width = 689
    Height = 197
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Anlieferung'
    TabOrder = 5
    DesignSize = (
      689
      197)
    object Label18: TLabel
      Left = 8
      Top = 12
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object DayPageControl: TPageControl
      Left = 8
      Top = 56
      Width = 593
      Height = 133
      ActivePage = TabSheet8
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      object TabSheet8: TTabSheet
        Caption = 'Alle Wochentage'
        ImageIndex = 7
        DesignSize = (
          585
          105)
        object Label16: TLabel
          Left = 11
          Top = 8
          Width = 44
          Height = 13
          Caption = 'Spedition'
        end
        object Label17: TLabel
          Left = 11
          Top = 56
          Width = 81
          Height = 13
          Caption = 'Lademittel-Konto'
        end
        object SpedComboBox: TComboBoxPro
          Left = 11
          Top = 24
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 0
          OnChange = ComboBoxChange
        end
        object LeerComboBox: TComboBoxPro
          Left = 11
          Top = 72
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 1
          OnChange = ComboBoxChange
        end
      end
      object TabSheet1: TTabSheet
        Caption = 'Montag'
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        DesignSize = (
          585
          105)
        object Label2: TLabel
          Left = 11
          Top = 8
          Width = 44
          Height = 13
          Caption = 'Spedition'
        end
        object Label3: TLabel
          Left = 11
          Top = 56
          Width = 69
          Height = 13
          Caption = 'Leergut-Konto'
        end
        object MoSpedComboBox: TComboBoxPro
          Left = 11
          Top = 24
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 0
          OnChange = ComboBoxChange
        end
        object MoLeerComboBox: TComboBoxPro
          Left = 11
          Top = 72
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 1
          OnChange = ComboBoxChange
        end
      end
      object TabSheet2: TTabSheet
        Caption = 'Dienstag'
        ImageIndex = 1
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        DesignSize = (
          585
          105)
        object Label4: TLabel
          Left = 11
          Top = 8
          Width = 44
          Height = 13
          Caption = 'Spedition'
        end
        object Label5: TLabel
          Left = 11
          Top = 56
          Width = 69
          Height = 13
          Caption = 'Leergut-Konto'
        end
        object DiSpedComboBox: TComboBoxPro
          Left = 11
          Top = 24
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 0
          OnChange = ComboBoxChange
        end
        object DiLeerComboBox: TComboBoxPro
          Left = 11
          Top = 72
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 1
          OnChange = ComboBoxChange
        end
      end
      object TabSheet3: TTabSheet
        Caption = 'Mittwoch'
        ImageIndex = 2
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        DesignSize = (
          585
          105)
        object Label6: TLabel
          Left = 11
          Top = 8
          Width = 44
          Height = 13
          Caption = 'Spedition'
        end
        object Label7: TLabel
          Left = 11
          Top = 56
          Width = 69
          Height = 13
          Caption = 'Leergut-Konto'
        end
        object MiSpedComboBox: TComboBoxPro
          Left = 11
          Top = 24
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 0
          OnChange = ComboBoxChange
        end
        object MiLeerComboBox: TComboBoxPro
          Left = 11
          Top = 72
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 1
          OnChange = ComboBoxChange
        end
      end
      object TabSheet4: TTabSheet
        Caption = 'Donnerstag'
        ImageIndex = 3
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        DesignSize = (
          585
          105)
        object Label8: TLabel
          Left = 11
          Top = 8
          Width = 44
          Height = 13
          Caption = 'Spedition'
        end
        object Label9: TLabel
          Left = 11
          Top = 56
          Width = 69
          Height = 13
          Caption = 'Leergut-Konto'
        end
        object DoSpedComboBox: TComboBoxPro
          Left = 11
          Top = 24
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 0
          OnChange = ComboBoxChange
        end
        object DoLeerComboBox: TComboBoxPro
          Left = 11
          Top = 72
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 1
          OnChange = ComboBoxChange
        end
      end
      object TabSheet5: TTabSheet
        Caption = 'Freitag'
        ImageIndex = 4
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        DesignSize = (
          585
          105)
        object Label10: TLabel
          Left = 11
          Top = 8
          Width = 44
          Height = 13
          Caption = 'Spedition'
        end
        object Label11: TLabel
          Left = 11
          Top = 56
          Width = 69
          Height = 13
          Caption = 'Leergut-Konto'
        end
        object FrSpedComboBox: TComboBoxPro
          Left = 11
          Top = 24
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 0
          OnChange = ComboBoxChange
        end
        object FrLeerComboBox: TComboBoxPro
          Left = 11
          Top = 72
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 1
          OnChange = ComboBoxChange
        end
      end
      object TabSheet6: TTabSheet
        Caption = 'Samstag'
        ImageIndex = 5
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        DesignSize = (
          585
          105)
        object Label12: TLabel
          Left = 11
          Top = 8
          Width = 44
          Height = 13
          Caption = 'Spedition'
        end
        object Label13: TLabel
          Left = 11
          Top = 56
          Width = 69
          Height = 13
          Caption = 'Leergut-Konto'
        end
        object SaSpedComboBox: TComboBoxPro
          Left = 11
          Top = 24
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 0
          OnChange = ComboBoxChange
        end
        object SaLeerComboBox: TComboBoxPro
          Left = 11
          Top = 72
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 1
          OnChange = ComboBoxChange
        end
      end
      object TabSheet7: TTabSheet
        Caption = 'Sonntag'
        ImageIndex = 6
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        DesignSize = (
          585
          105)
        object Label14: TLabel
          Left = 11
          Top = 8
          Width = 44
          Height = 13
          Caption = 'Spedition'
        end
        object Label15: TLabel
          Left = 11
          Top = 56
          Width = 69
          Height = 13
          Caption = 'Leergut-Konto'
        end
        object SoSpedComboBox: TComboBoxPro
          Left = 11
          Top = 24
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 0
        end
        object SoLeerComboBox: TComboBoxPro
          Left = 11
          Top = 72
          Width = 560
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 1
        end
      end
    end
    object RejectButton: TButton
      Left = 607
      Top = 164
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Verwerfen'
      TabOrder = 1
      OnClick = RejectButtonClick
    end
    object SaveButton: TButton
      Left = 607
      Top = 132
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      TabOrder = 2
      OnClick = SaveButtonClick
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 29
      Width = 593
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 3
      OnChange = LagerComboBoxChange
    end
  end
  object LieferantenQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 392
    Top = 152
  end
  object LieferantenDataSource: TDataSource
    DataSet = LieferantenQuery
    OnDataChange = LieferantenDataSourceDataChange
    Left = 536
    Top = 208
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 504
    Top = 64
  end
end
