object ShowPickResultForm: TShowPickResultForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'ShowPickResultForm'
  ClientHeight = 625
  ClientWidth = 977
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    977
    625)
  PixelsPerInch = 96
  TextHeight = 13
  object GroupBox1: TGroupBox
    Left = 8
    Top = 8
    Width = 961
    Height = 249
    Anchors = [akLeft, akTop, akRight]
    Caption = 'GroupBox1'
    TabOrder = 0
    DesignSize = (
      961
      249)
    object Label1: TLabel
      Left = 448
      Top = 21
      Width = 76
      Height = 13
      Caption = 'Erkannte Fehler'
    end
    object Label2: TLabel
      Left = 11
      Top = 20
      Width = 61
      Height = 13
      Caption = 'Komm.-Lauf:'
    end
    object Label3: TLabel
      Left = 11
      Top = 40
      Width = 92
      Height = 13
      Caption = 'Abgeschlossen um:'
    end
    object Label4: TLabel
      Left = 11
      Top = 60
      Width = 32
      Height = 13
      Caption = 'Picker:'
    end
    object Label5: TLabel
      Left = 11
      Top = 80
      Width = 76
      Height = 13
      Caption = 'Komm.Beh'#228'lter:'
    end
    object KommNrLabel: TLabel
      Left = 120
      Top = 20
      Width = 250
      Height = 13
      AutoSize = False
      Caption = 'KommNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object CloseDateLabel: TLabel
      Left = 120
      Top = 40
      Width = 250
      Height = 13
      AutoSize = False
      Caption = 'CloseDateLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object PickerLabel: TLabel
      Left = 120
      Top = 60
      Width = 250
      Height = 13
      AutoSize = False
      Caption = 'PickerLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object PickLENrLabel: TLabel
      Left = 120
      Top = 80
      Width = 250
      Height = 13
      AutoSize = False
      Caption = 'PickLENrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object BuchenButton: TButton
      Left = 11
      Top = 213
      Width = 179
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'Ergebnisse '#252'bernehmen'
      TabOrder = 0
      OnClick = BuchenButtonClick
    end
    object ErrorDBGrid: TDBGridPro
      Left = 448
      Top = 37
      Width = 505
      Height = 201
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = ResultErrorDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      PopupMenu = ErrorDBGridPopupMenu
      ReadOnly = True
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      OnDrawColumnCell = DBGridDrawColumnCell
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
  end
  object PickResultDBGrid: TDBGridPro
    Left = 8
    Top = 288
    Width = 961
    Height = 298
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = PickResultDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = PickResultDBGridPopupMenu
    ReadOnly = True
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    OnDrawColumnCell = DBGridDrawColumnCell
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 894
    Top = 592
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 2
  end
  object PickResultQuery: TSmartQuery
    Left = 672
    Top = 528
  end
  object ResultErrorQuery: TSmartQuery
    Left = 568
    Top = 528
  end
  object PickResultDataSource: TDataSource
    DataSet = PickResultQuery
    Left = 704
    Top = 528
  end
  object ResultErrorDataSource: TDataSource
    DataSet = ResultErrorQuery
    Left = 600
    Top = 528
  end
  object ErrorDBGridPopupMenu: TPopupMenu
    Left = 664
    Top = 96
    object KommLENummerndern1: TMenuItem
      Caption = 'Komm-LE Nummer '#228'ndern...'
      OnClick = KommLENummerndern1Click
    end
  end
  object PickResultDBGridPopupMenu: TPopupMenu
    Left = 288
    Top = 368
    object ChangeArtikelMenuItem: TMenuItem
      Caption = 'Artikel '#228'ndern...'
      OnClick = ChangeArtikelMenuItemClick
    end
  end
end
