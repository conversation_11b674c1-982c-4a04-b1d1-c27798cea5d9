{$i compilers.inc}

unit VerlaufDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, ComCtrls;

type
  TVerlaufForm = class(TForm)
    ProgressBar1: TProgressBar;
    Label1: TLabel;
    AbortButton: TButton;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    Label5: TLabel;
    Label6: TLabel;
    Label7: TLabel;
    DauerLabel: TLabel;
    Label8: TLabel;
    procedure AbortButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormShow(Sender: TObject);
    procedure FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
  private
    fAbortFlag      : Boolean;
    fWindowList     : Pointer;
    fActiveWindow   : HWnd;
    fSaveFocusState : TFocusState;
    fModalFlag      : Boolean;
    fStartTime      : Int64;
    fLastDisp       : Int64;
  public
    property AbortFlag : Boolean read fAbortFlag write fAbortFlag;

    procedure BeginShowModal;
    procedure UpdateModal;
    procedure EndShowModal;

    procedure ResetDuration;
  end;

implementation

{$R *.dfm}

uses
  {$ifdef Trace}
    Trace,
  {$endif}

  ResourceText;

{$ifdef DELPHIXE4_UP}
  type
    TMyCustomForm = class (TCustomForm)
      public
        FFormState: TFormState;
    end;
{$endif}

procedure TVerlaufForm.AbortButtonClick(Sender: TObject);
begin
  fAbortFlag := True;
end;

procedure TVerlaufForm.FormCreate(Sender: TObject);
begin
  fModalFlag := False;
  fStartTime := Int64 (GetTickCount);

  //Die Dauer erst mal nicht anzeigen
  Label8.Visible := False;
  DauerLabel.Visible := False;

  fAbortFlag := False;

  Label1.Caption := '';
  Label2.Caption := '';
  Label3.Caption := '';
  Label4.Caption := '';
  Label5.Caption := '';
  Label6.Caption := '';
  Label7.Caption := '';

  Label1.Visible := False;
  Label2.Visible := False;
  Label3.Visible := False;
  Label4.Visible := False;
  Label5.Visible := False;
  Label6.Visible := False;
  Label7.Visible := False;
end;

procedure TVerlaufForm.FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
begin
  if (Key = VK_ESCAPE) then
    fAbortFlag := True;
end;

procedure TVerlaufForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  fAbortFlag := True;

  if fModalFlag then begin
    EnableTaskWindows(fWindowList);
    if fActiveWindow <> 0 then SetActiveWindow(fActiveWindow);

    RestoreFocusState (fSaveFocusState);

    {$ifdef DELPHIXE4_UP}
      Exclude(TMyCustomForm(Self).FFormState, fsModal);
    {$else}
      Exclude((Self as TCustomForm).FFormState, fsModal);
    {$endif}

    Application.ModalFinished;

    fModalFlag := False;
  end;
end;

procedure TVerlaufForm.FormShow(Sender: TObject);
begin
  fAbortFlag := False;
end;

procedure TVerlaufForm.ResetDuration;
begin
  fLastDisp  := -1;
  fStartTime := Int64 (GetTickCount);

  DauerLabel.Caption := '00:00:00';
end;

procedure TVerlaufForm.BeginShowModal;
begin
  Label8.Caption      := GetResourceText(1825);
  DauerLabel.Caption  := '';
  AbortButton.Caption := GetResourceText(1346);

  DauerLabel.Visible := True;

  ResetDuration;

  Label8.Visible := True;

  if not (Label6.Visible) then
    Height := Height - Label6.Height;

  if not (Label3.Visible) then
    Height := Height - Label3.Height;

  if not (Label2.Visible) then
    Height := Height - Label2.Height;

  {$if CompilerVersion > 30.0}
    //Application.ModalStarted;

    (*
    {$ifdef DELPHIXE4_UP}
      Include(TMyCustomForm (Self).FFormState, fsModal);
    {$else}
      Include((Self as TCustomForm).FFormState, fsModal);
    {$endif}
    *)

    fActiveWindow := GetActiveWindow;
    fWindowList := DisableTaskwindows (Application.Handle);

    Show;
    Update;
  {$else}
    fModalFlag := True;

    CancelDrag;
    if GetCapture <> 0 then SendMessage(GetCapture, WM_CANCELMODE, 0, 0);
    ReleaseCapture;

    Application.ModalStarted;

    {$ifdef DELPHIXE4_UP}
      Include(TMyCustomForm (Self).FFormState, fsModal);
    {$else}
      Include((Self as TCustomForm).FFormState, fsModal);
    {$endif}

    fActiveWindow := GetActiveWindow;
    fSaveFocusState := Forms.SaveFocusState;
    fWindowList := DisableTaskWindows (Application.Handle);

    EnableWindow (Handle, True);

    Show;

    SendMessage(Handle, CM_ACTIVATE, 0, 0);

    Update;
  {$ifend}
end;

procedure TVerlaufForm.UpdateModal;
var
  t,
  diff,
  ticks   : Int64;
  dtstr   : String;
  h, m, s : Integer;
begin
  {$ifdef Trace}
    ProcedureStart ('TVerlaufForm.UpdateModal');
  {$endif}

  ticks := Int64 (GetTickCount);

  {$ifdef Trace} TraceResult('ticks', ticks); {$endif}
  {$ifdef Trace} TraceResult('fStartTime', fStartTime); {$endif}

  if (ticks >= fStartTime) then
    diff := ticks - fStartTime
  else
    diff := ($100000000 - fStartTime) + ticks;

  {$ifdef Trace} TraceResult('diff', diff); {$endif}

  //Nur einmal pro Sekunde anzeigen
  if ((fLastDisp = -1) or ((fLastDisp + 1000) < diff)) then begin
    try
      fLastDisp := diff;

      if (ProgressBar1.Visible and DauerLabel.Visible) then begin
        {$ifdef Trace} TraceResult('ProgressBar1.Max     ', ProgressBar1.Max); {$endif}
        {$ifdef Trace} TraceResult('ProgressBar1.Position', ProgressBar1.Position); {$endif}

        s := diff div 1000;

        h := Trunc(s div 3600);
        s := s - (3600 * Trunc(s div 3600));
        m := Trunc(s div 60);
        s := s - (60 * Trunc(s div 60));

        {$ifdef Trace} TraceString ('h:m:s = '+IntToStr (h)+':'+IntToStr (m)+':'+IntToStr (s)); {$endif}

        try
          dtstr := TimeToStr (EncodeTime (h, m, s, 0));
        except
          dtstr := '00:00:00'
        end;

        DauerLabel.Caption := dtstr;

        if (ProgressBar1.Position > 0) then begin
          t := diff div ProgressBar1.Position;
          if (t = 0) then
            t := 1;

          {$ifdef Trace} TraceResult('t', t); {$endif}

          if (ProgressBar1.Max < ProgressBar1.Position) then
            s := 0
          else begin
            s := Int64 (ProgressBar1.Max - ProgressBar1.Position);
            s := (t * s) div 1000;
          end;

          {$ifdef Trace} TraceResult('s', s); {$endif}

          h := Trunc(s div 3600);
          if (h > 12) then h := 12;
          s := s - (3600 * Trunc(s div 3600));
          m := Trunc(s div 60);
          s := s - (60 * Trunc(s div 60));

          {$ifdef Trace} TraceString('h:m:s = '+IntToStr (h)+':'+IntToStr (m)+':'+IntToStr (s)); {$endif}

          try
            dtstr := TimeToStr (EncodeTime (h, m, s, 0));
          except
            dtstr := '00:00:00'
          end;

          DauerLabel.Caption := DauerLabel.Caption + ' -> ' + dtstr+ ' (' + IntToStr (t) + ' ms)';
        end;
      end;
    except
      DauerLabel.Caption := '---';
    end;

    Update;
  end;

  Application.ProcessMessages;

  {$ifdef Trace}
    ProcedureStop;
  {$endif}
end;

procedure TVerlaufForm.EndShowModal;
begin
  {$if CompilerVersion > 30.0}
    EnableTaskWindows(fWindowList);
    if fActiveWindow <> 0 then SetActiveWindow(fActiveWindow);

    (*
    {$ifdef DELPHIXE4_UP}
      Exclude(TMyCustomForm(Self).FFormState, fsModal);
    {$else}
      Exclude((Self as TCustomForm).FFormState, fsModal);
    {$endif}
    *)

    //Application.ModalFinished;
  {$else}
    EnableTaskWindows(fWindowList);
    if fActiveWindow <> 0 then SetActiveWindow(fActiveWindow);

    RestoreFocusState (fSaveFocusState);

    {$ifdef DELPHIXE4_UP}
      Exclude(TMyCustomForm(Self).FFormState, fsModal);
    {$else}
      Exclude((Self as TCustomForm).FFormState, fsModal);
    {$endif}

    Application.ModalFinished;

    fModalFlag := False;
  {$ifend}
end;

end.
