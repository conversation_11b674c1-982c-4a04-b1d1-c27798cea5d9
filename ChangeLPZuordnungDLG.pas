unit ChangeLPZuordnungDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, CheckLst, StdCtrls, ComboBoxPro, ExtCtrls;

type
  TChangeLPZuordnungForm = class(TForm)
    LagerComboBox: TComboBoxPro;
    Label1: TLabel;
    LPTypenListBox: TListBox;
    GroupBox1: TGroupBox;
    LPBesCheckBox: TCheckBox;
    CloseButton: TButton;
    UpdateButton: TButton;
    ZuordnungCheckListBox: TCheckListBox;
    ADOQuery1: TADOQuery;
    Label2: TLabel;
    Label3: TLabel;
    Bevel1: TBevel;
    MixedCheckBox: TCheckBox;
    TransferCheckBox: TCheckBox;
    OverLTCheckBox: TCheckBox;
    procedure LagerComboBoxChange(Sender: TObject);
    procedure LPTypenListBoxClick(Sender: TObject);
    procedure ListBoxDrawItem(Control: TWinControl; Index: Integer;
      Rect: TRect; State: TOwnerDrawState);
    procedure ChangeClick(Sender: TObject);
    procedure UpdateButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    procedure Prepare (const RefLager : Integer);
  end;

implementation

{$R *.dfm}

uses VCLUtilitys, DatenModul, LVSLagertopologie, FrontendUtils, SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeLPZuordnungForm.Prepare (const RefLager : Integer);
begin
  LoadLagerCombobox (LagerComboBox, LVSDatenModul.AktLocationRef);

  LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, RefLager);

  if (LagerComboBox.ItemIndex = -1) then
    LagerComboBox.ItemIndex := 0
  else if (LagerComboBox.Items.Count = 1) then
    LagerComboBox.Enabled := False;

  LagerComboBoxChange (Nil);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeLPZuordnungForm.FormCreate(Sender: TObject);
begin
  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
    LVSSprachModul.SetNoTranslate (Self, LPTypenListBox);
    LVSSprachModul.SetNoTranslate (Self, ZuordnungCheckListBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeLPZuordnungForm.LagerComboBoxChange(Sender: TObject);
begin
  LPTypenListBox.Clear;

  ADOQuery1.SQL.Clear;

  ADOQuery1.SQL.Add ('select REF, TYP, BEZEICHNUNG from V_LAGER_LP_TYPEN where REF_LAGER=:ref_lager');
  ADOQuery1.Parameters [0].Value := GetComboBoxRef(LagerComboBox);

  ADOQuery1.Open;

  while not (ADOQuery1.Eof) do begin
    LPTypenListBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TListBoxRef.Create(ADOQuery1.Fields [0].AsInteger));

    ADOQuery1.Next;
  end;

  ADOQuery1.Close;

  if (LPTypenListBox.Count > 0) then
    LPTypenListBox.ItemIndex := 0
  else LPTypenListBox.ItemIndex := -1;

  LPTypenListBoxClick (LPTypenListBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeLPZuordnungForm.LPTypenListBoxClick(Sender: TObject);
var
  idx    : Integer;
  defstr : String;
begin
  if (LPTypenListBox.ItemIndex = -1) then begin
    GroupBox1.Enabled := False;

    LPBesCheckBox.Checked := False;
    ZuordnungCheckListBox.Clear;
  end else begin
    GroupBox1.Enabled := True;

    ZuordnungCheckListBox.Clear;

    ADOQuery1.SQL.Clear;

    ADOQuery1.SQL.Add ('select * from V_LAGER_LP_TYPEN where REF=:ref');
    ADOQuery1.Parameters [0].Value := GetListBoxRef (LPTypenListBox);

    try
      ADOQuery1.Open;

      LPBesCheckBox.Checked := (ADOQuery1.FieldByName ('OPT_BESTAND').AsString = '1');

      if not Assigned (ADOQuery1.FindField ('OPT_BESTAND_MIXED_LE')) then
        MixedCheckBox.Visible := False
      else
        MixedCheckBox.Checked := (ADOQuery1.FieldByName ('OPT_BESTAND_MIXED_LE').AsString = '1');

      if not Assigned (ADOQuery1.FindField ('OPT_TRANSFER_LP')) then
        TransferCheckBox.Visible := False
      else
        TransferCheckBox.Checked := (ADOQuery1.FieldByName ('OPT_TRANSFER_LP').AsString = '1');

      if not Assigned (ADOQuery1.FindField ('OPT_LE_OVERSIZED')) then
        OverLTCheckBox.Visible := False
      else
        OverLTCheckBox.Checked := (ADOQuery1.FieldByName ('OPT_LE_OVERSIZED').AsString = '1');

      ADOQuery1.Close;
    except
    end;

    ADOQuery1.SQL.Clear;

    ADOQuery1.SQL.Add ('select REF, NAME, BESCHREIBUNG, DEFAULT_LT from V_LT_TYPEN where STATUS=''AKT'' and (REF_LAGER is null and REF_LOCATION=(select REF_LOCATION from V_LOCATION_REL_LAGER where REF_LAGER=:ref_lager_1) or REF_LAGER=:ref_lager_2) order by REIHENFOLGE');
    ADOQuery1.Parameters [0].Value := GetComboBoxRef(LagerComboBox);
    ADOQuery1.Parameters [1].Value := GetComboBoxRef(LagerComboBox);

    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      if (ADOQuery1.Fields [3].AsString = '1') then
        defstr := 'Default'
      else defstr := '';

      ZuordnungCheckListBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TListBoxRef.Create(ADOQuery1.Fields [0].AsInteger));

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF_LT_TYPE, OPT_PLATZ from V_LAGER_LP_TYPE_REL_LT_TYPE where REF_LP_TYPE=:ref_lt');
    ADOQuery1.Parameters [0].Value := GetListBoxRef (LPTypenListBox);

    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      idx := 0;

      while (idx < ZuordnungCheckListBox.Items.Count) do begin
        if (GetListBoxRef (ZuordnungCheckListBox, idx) = ADOQuery1.Fields [0].AsInteger) Then begin
          ZuordnungCheckListBox.Checked [idx] := (ADOQuery1.Fields [1].AsString = '1');

          idx := ZuordnungCheckListBox.Items.Count;
        end else Inc (idx);
      end;

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;
  end;

  UpdateButton.Enabled := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeLPZuordnungForm.ListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
var
  line : String;
  strpos : Integer;
begin
  if (Control is TListBox) then begin
    line := (Control as TListBox).Items [Index];
    strpos := Pos ('|', line);

    with (Control as TListBox).Canvas do begin
      FillRect(Rect);

      TextOut (Rect.Left, Rect.Top, Copy (line,1,strpos - 1));
      TextOut (Rect.Left + 80, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
    end;
  end else if (Control is TCheckListBox) then begin
    line := (Control as TCheckListBox).Items [Index];
    strpos := Pos ('|', line);

    with (Control as TCheckListBox).Canvas do begin
      FillRect(Rect);

      TextOut (Rect.Left, Rect.Top, Copy (line,1,strpos - 1));
      TextOut (Rect.Left + 150, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeLPZuordnungForm.ChangeClick(Sender: TObject);
begin
  if (Sender = LPBesCheckBox) then begin
    MixedCheckBox.Enabled := LPBesCheckBox.Checked;
  end;

  UpdateButton.Enabled := True;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeLPZuordnungForm.UpdateButtonClick(Sender: TObject);
var
  res,
  idx : Integer;
  opt : String;
begin
  if not (MixedCheckBox.Visible and OverLTCheckBox.Visible) then
    res := SetLPTypenDaten (GetListBoxRef (LPTypenListBox), LPBesCheckBox.Checked)
  else begin
    opt := '~~~~~~~~';

    if not (LPBesCheckBox.Visible or LPBesCheckBox.Enabled) then
      opt [1] := '~'
    else
      opt [1] := GetCheckboxStat(LPBesCheckBox);

    if not (MixedCheckBox.Visible or MixedCheckBox.Enabled) then
      opt [2] := '~'
    else
      opt [2] := GetCheckboxStat(MixedCheckBox);

    if not (OverLTCheckBox.Visible or OverLTCheckBox.Enabled) then
      opt [4] := '~'
    else
      opt [4] := GetCheckboxStat(OverLTCheckBox);

    res := SetLPTypenOptions (GetListBoxRef (LPTypenListBox), opt);
  end;

  if (res = 0) then begin
    idx := 0;
    while (idx < ZuordnungCheckListBox.Items.Count) and (res = 0) do begin
      res := SetLPLTZuordnung (GetListBoxRef (LPTypenListBox), GetListBoxRef (ZuordnungCheckListBox, idx), ZuordnungCheckListBox.Checked [idx]);

      Inc (Idx);
    end;
  end;

  if (res = 0) Then
    UpdateButton.Enabled := False
  else MessageDLG (FormatMessageText (1625, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
end;

end.
