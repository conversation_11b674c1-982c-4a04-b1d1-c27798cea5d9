object PlanBackTransportForm: TPlanBackTransportForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'R'#252'cklagerungen planen'
  ClientHeight = 244
  ClientWidth = 432
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  DesignSize = (
    432
    244)
  PixelsPerInch = 96
  TextHeight = 13
  object Label12: TLabel
    Left = 8
    Top = 11
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Label8: TLabel
    Left = 8
    Top = 43
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Label4: TLabel
    Left = 8
    Top = 73
    Width = 63
    Height = 13
    Caption = 'Quell-Bereich'
  end
  object Label2: TLabel
    Left = 8
    Top = 107
    Width = 55
    Height = 13
    Caption = 'Ziel-Bereich'
  end
  object Bevel5: TBevel
    Left = 6
    Top = 97
    Width = 419
    Height = 4
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label5: TLabel
    Left = 8
    Top = 170
    Width = 35
    Height = 13
    Caption = 'Anzahl '
  end
  object Bevel1: TBevel
    Left = 6
    Top = 199
    Width = 419
    Height = 4
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 262
  end
  object MandantComboBox: TComboBoxPro
    Left = 80
    Top = 10
    Width = 345
    Height = 21
    Style = csOwnerDrawFixed
    ItemHeight = 15
    TabOrder = 0
    OnChange = MandantComboBoxChange
  end
  object LagerComboBox: TComboBoxPro
    Left = 80
    Top = 40
    Width = 345
    Height = 22
    Style = csOwnerDrawFixed
    ItemHeight = 16
    TabOrder = 1
    OnChange = LagerComboBoxChange
  end
  object LBSourceComboBox: TComboBoxPro
    Left = 80
    Top = 70
    Width = 345
    Height = 22
    Style = csOwnerDrawFixed
    ItemHeight = 16
    TabOrder = 2
  end
  object LBDestComboBox: TComboBoxPro
    Left = 80
    Top = 104
    Width = 345
    Height = 22
    Style = csOwnerDrawFixed
    ItemHeight = 16
    TabOrder = 3
  end
  object CountEdit: TEdit
    Left = 80
    Top = 167
    Width = 73
    Height = 21
    TabOrder = 4
    Text = '10'
  end
  object OkButton: TButton
    Left = 264
    Top = 211
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 5
  end
  object AbortButton: TButton
    Left = 350
    Top = 211
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 6
  end
end
