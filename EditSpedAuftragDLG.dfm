object EditSpedAuftragForm: TEditSpedAuftragForm
  Left = 0
  Top = 0
  Anchors = [akLeft, akTop, akRight]
  BorderStyle = bsDialog
  Caption = 'EditSpedAuftragForm'
  ClientHeight = 528
  ClientWidth = 401
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  ExplicitWidth = 320
  DesignSize = (
    401
    528)
  PixelsPerInch = 96
  TextHeight = 13
  object Label6: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Bevel4: TBevel
    Left = 6
    Top = 161
    Width = 389
    Height = 11
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel1: TBevel
    Left = 6
    Top = 217
    Width = 389
    Height = 11
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label2: TLabel
    Left = 8
    Top = 172
    Width = 15
    Height = 13
    Caption = 'Art'
  end
  object Label3: TLabel
    Left = 8
    Top = 116
    Width = 22
    Height = 13
    Caption = 'Tour'
  end
  object Label4: TLabel
    Left = 8
    Top = 57
    Width = 29
    Height = 13
    Caption = 'Depot'
  end
  object Bevel2: TBevel
    Left = 6
    Top = 53
    Width = 389
    Height = 11
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel3: TBevel
    Left = 6
    Top = 105
    Width = 389
    Height = 11
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label5: TLabel
    Left = 8
    Top = 233
    Width = 56
    Height = 13
    Caption = 'Abholen bei'
  end
  object Label7: TLabel
    Left = 8
    Top = 284
    Width = 60
    Height = 13
    Caption = 'Anliefern bei'
  end
  object Label8: TLabel
    Left = 8
    Top = 340
    Width = 51
    Height = 13
    Caption = 'Auftragnr.'
  end
  object Label9: TLabel
    Left = 8
    Top = 388
    Width = 62
    Height = 13
    Caption = 'Ausliefertext'
  end
  object Label10: TLabel
    Left = 8
    Top = 436
    Width = 77
    Height = 13
    Caption = 'Auslieferhinweis'
  end
  object Bevel5: TBevel
    Left = 6
    Top = 329
    Width = 389
    Height = 11
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel6: TBevel
    Left = 6
    Top = 487
    Width = 389
    Height = 11
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 525
    ExplicitWidth = 343
  end
  object Label1: TLabel
    Left = 152
    Top = 340
    Width = 42
    Height = 13
    Caption = 'Zeitraum'
  end
  object MandComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 385
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 120
    AutoPrepare = False
    ColumeCount = 2
    ItemHeight = 15
    TabOrder = 0
    OnChange = MandComboBoxChange
  end
  object OkButton: TButton
    Left = 234
    Top = 495
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 12
  end
  object AbortButton: TButton
    Left = 318
    Top = 495
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 13
  end
  object ArtComboBox: TComboBoxPro
    Left = 8
    Top = 189
    Width = 385
    Height = 21
    Style = csDropDownList
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 0
    TabOrder = 3
    OnChange = ArtComboBoxChange
  end
  object AbholDateTimePicker: TDateTimePicker
    Left = 310
    Top = 249
    Width = 83
    Height = 21
    Anchors = [akTop, akRight]
    Date = 40767.435847060190000000
    Time = 40767.435847060190000000
    TabOrder = 5
    OnChange = AbholDateTimePickerChange
  end
  object LieferDateTimePicker: TDateTimePicker
    Left = 310
    Top = 300
    Width = 83
    Height = 21
    Anchors = [akTop, akRight]
    Date = 40767.435847060190000000
    Time = 40767.435847060190000000
    TabOrder = 7
  end
  object TourComboBox: TComboBoxPro
    Left = 8
    Top = 132
    Width = 385
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 120
    AutoPrepare = False
    ColumeCount = 2
    ItemHeight = 16
    TabOrder = 2
  end
  object DepotComboBox: TComboBoxPro
    Left = 8
    Top = 73
    Width = 385
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 120
    AutoPrepare = False
    ColumeCount = 2
    ItemHeight = 15
    TabOrder = 1
    OnChange = DepotComboBoxChange
  end
  object AbholAdrComboBox: TComboBoxPro
    Left = 8
    Top = 249
    Width = 287
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 100
    AutoPrepare = False
    ColumeCount = 2
    ItemHeight = 16
    PopupMenu = AdrPopupMenu
    TabOrder = 4
  end
  object AnlieferAdrComboBox: TComboBoxPro
    Left = 8
    Top = 300
    Width = 287
    Height = 19
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 100
    AutoPrepare = False
    ColumeCount = 2
    ItemHeight = 13
    PopupMenu = AdrPopupMenu
    TabOrder = 6
  end
  object AufNrEdit: TEdit
    Left = 8
    Top = 356
    Width = 121
    Height = 21
    TabOrder = 8
    Text = 'AufNrEdit'
  end
  object AuslieferTextEdit: TEdit
    Left = 8
    Top = 404
    Width = 385
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 10
    Text = 'AuslieferTextEdit'
  end
  object HinweisEdit: TEdit
    Left = 8
    Top = 452
    Width = 385
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 11
    Text = 'HinweisEdit'
  end
  object ZeitEdit: TEdit
    Left = 152
    Top = 356
    Width = 121
    Height = 21
    TabOrder = 9
    Text = 'ZeitEdit'
  end
  object AdrPopupMenu: TPopupMenu
    Left = 112
    Top = 256
    object NeueAdresse1: TMenuItem
      Caption = 'Neue Adresse...'
      OnClick = NeueAdresse1Click
    end
  end
end
