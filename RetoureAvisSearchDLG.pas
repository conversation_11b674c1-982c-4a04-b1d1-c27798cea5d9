unit RetoureAvisSearchDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, ComCtrls, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro,
  DB, MemDS, DBAccess, Ora, BarCodeScanner

  {$IFDEF VER360}
    ,System.Actions, Vcl.ActnList
  {$ELSE}
    {$IFDEF VER350}
      ,System.Actions, Vcl.ActnList
    {$else}
      ,ActnList
    {$endif}
  {$endif}
  ;

type
  TRetoureAvisSearchForm = class(TForm)
    KopfPanel: TPanel;
    RetoureAvisDBGrid: TDBGridPro;
    FussPanel: TPanel;
    AbortButton: TButton;
    CreateButton: TButton;
    AufNrEdit: TEdit;
    Label1: TLabel;
    Label182: TLabel;
    RetoureAvisVonDateTimePicker: TDateTimePicker;
    Label185: TLabel;
    RetoureAvisBisDateTimePicker: TDateTimePicker;
    Bevel1: TBevel;
    Label2: TLabel;
    KDNameEdit: TEdit;
    RefreshButton: TButton;
    RetoureAvisDataSet: TOraQuery;
    RetoureAvisDataSource: TDataSource;
    Label3: TLabel;
    PLZEdit: TEdit;
    FehlerPanel: TPanel;
    ActionList1: TActionList;
    RefreshAction: TAction;
    RetoureAvisPosDataSet: TOraQuery;
    RetoureAvisPosDataSource: TDataSource;
    RetoureAvisPosDBGrid: TDBGridPro;
    Splitter1: TSplitter;
    SplitPanel: TPanel;
    Label4: TLabel;
    KdNrEdit: TEdit;
    Label5: TLabel;
    AufRefEdit: TEdit;
    Label6: TLabel;
    TrackingEdit: TEdit;
    procedure FormCreate(Sender: TObject);
    procedure RefreshButtonClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure RefreshActionExecute(Sender: TObject);
    procedure RetoureAvisDataSourceDataChange(Sender: TObject; Field: TField);
    procedure RetoureAvisDBGridDrawColumnCell(Sender: TObject;
      const Rect: TRect; DataCol: Integer; Column: TColumn;
      State: TGridDrawState);
    procedure CreateButtonClick(Sender: TObject);
    procedure EditKeyPress(Sender: TObject; var Key: Char);
  private
    procedure ScannerErfassung (var Message: TMessage); message WM_USER + SCANNER_DETECT;
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  StrUtils, VCLUtilitys, ConfigModul, LVSConst, DatenModul, FrontendUtils, DBGridUtilModule, LVSGlobalDaten, SprachModul,
  LVSFrontendScannerErfassung, LVSDatenInterface, RetoureAnlage, FrontendMainWEDialoge;

procedure TRetoureAvisSearchForm.RetoureAvisDataSourceDataChange(Sender: TObject; Field: TField);
begin
  RetoureAvisPosDataSet.Close;

  if (RetoureAvisDataSet.Active and (RetoureAvisDataSet.RecNo > 0)) then begin
    CreateButton.Enabled := (RetoureAvisDataSet.FieldByName ('STATUS').AsString = 'ANG');

    RetoureAvisPosDataSet.ParamByName('ref').Value := RetoureAvisDataSet.FieldByName ('REF').AsInteger;

    RetoureAvisPosDataSet.Open;

    RetoureAvisPosDBGrid.SetColumnVisible('UNITS', False);
    RetoureAvisPosDBGrid.SetColumnVisible('STR_UNITS', LVSConfigModul.UseArtikelUnit);
  end else begin
    CreateButton.Enabled := False;
  end;
end;

procedure TRetoureAvisSearchForm.RetoureAvisDBGridDrawColumnCell(Sender: TObject; const Rect: TRect; DataCol: Integer; Column: TColumn; State: TGridDrawState);
var
  bm: TBitmap;
  idx: Integer;
begin
  if (PosEx ('STATUS', Column.FieldName) > 0) then begin
    if (LVSConfigModul.FrontendConfig.cfgStatIcons) then begin
      if (Column.Field.AsString = 'ANG') then
        idx := 17
      else if (Column.Field.AsString = 'ABG') then
        idx := 1
      else if (Column.Field.AsString = 'DEL') then
        idx := 9
      else if (Column.Field.AsString = 'INA') then
        idx := 18
      else if (Column.Field.AsString = 'SPERR') then
        idx := 16
      else if (Column.Field.AsString = 'QS') then
        idx := 19
      else if (Column.Field.AsString = 'AKT') then
        idx := 17
      else
        idx := -1;

      if (idx <> -1) then begin
        (Sender as TDBGridPro).Canvas.FillRect(rect);

        bm := TBitmap.Create;

        try
          if (idx = 0) then
            bm.handle := loadbitmap(hinstance, 'angelegt')
          else if (idx = 1) then
            bm.handle := loadbitmap(hinstance, 'abgeschlossen')
          else if (idx = 2) then
            bm.handle := loadbitmap(hinstance, 'fehler')
          else if (idx = 3) then
            bm.handle := loadbitmap(hinstance, 'transform')
          else if (idx = 4) then
            bm.handle := loadbitmap(hinstance, 'kommissionieren')
          else if (idx = 5) then
            bm.handle := loadbitmap(hinstance, 'warenausgang')
          else if (idx = 6) then
            bm.handle := loadbitmap(hinstance, 'interntransport')
          else if (idx = 7) then
            bm.handle := loadbitmap(hinstance, 'beendet')
          else if (idx = 8) then
            bm.handle := loadbitmap(hinstance, 'storniert')
          else if (idx = 9) then
            bm.handle := loadbitmap(hinstance, 'delete')
          else if (idx = 10) then
            bm.handle := loadbitmap(hinstance, 'reload')
          else if (idx = 11) then
            bm.handle := loadbitmap(hinstance, 'ifcerror')
          else if (idx = 12) then
            bm.handle := loadbitmap(hinstance, 'geplant')
          else if (idx = 13) then
            bm.handle := loadbitmap(hinstance, 'umpack')
          else if (idx = 14) then
            bm.handle := loadbitmap(hinstance, 'gabelstapler')
          else if (idx = 15) then
            bm.handle := loadbitmap(hinstance, 'crossdock')
          else if (idx = 16) then
            bm.handle := loadbitmap(hinstance, 'redcross')
          else if (idx = 17) then
            bm.handle := loadbitmap(hinstance, 'tick')
          else if (idx = 18) then
            bm.handle := loadbitmap(hinstance, 'oneway')
          else if (idx = 19) then
            bm.handle := loadbitmap(hinstance, 'lupe');

          DrawStatusBitmap ((Sender as TDBGridPro).Canvas, rect, bm);
        finally
          bm.Free;
        end;
      end;
    end;
  end;
end;

procedure TRetoureAvisSearchForm.EditKeyPress(Sender: TObject; var Key: Char);
begin
  if (Key = #13) then begin
    RefreshButtonClick (Sender);
  end;
end;

procedure TRetoureAvisSearchForm.CreateButtonClick(Sender: TObject);
var
  res,
  refwe,
  refret,
  refavis    : Integer;
  anlageform : TRetoureAnlageForm;
begin
  if (RetoureAvisDataSet.Active and (RetoureAvisDataSet.RecNo > 0)) then begin
    anlageform := TRetoureAnlageForm.Create (Self);

    try
      anlageform.SubMandPanel.Visible := LVSConfigModul.UseSubMandanten;

      refavis := RetoureAvisDataSet.FieldByName('REF').AsInteger;

      res := anlageform.Prepare (refavis);

      if (res = 0) then begin
        if (anlageform.ShowModal = mrOk) then begin
          with anlageform do
            if (GetComboBoxRef (RetGrundComboBox) > 0) then
              res := RetourenannahmeErzeugen (refavis,
                                              GetComboboxLBRef (WEComboBox), GetComboboxLPRef (WEComboBox),
                                              SpedComboBox.Text,
                                              GetComboBoxRef (LeerComboBox),
                                              GetComboBoxDBItemWert (RetArtComboBox), RetGrundComboBox.Text,
                                              LSNrEdit.Text, RMANrEdit.Text, HintEdit.Text,
                                              LSDateDateTimePicker.Date,
                                              RefRet, RefWE)
            else
              res := RetourenannahmeErzeugen (refavis,
                                              GetComboboxLBRef (WEComboBox), GetComboboxLPRef (WEComboBox),
                                              SpedComboBox.Text,
                                              GetComboBoxRef (LeerComboBox),
                                              GetComboBoxDBItemWert (RetArtComboBox), RetGrundComboBox.Text,
                                              LSNrEdit.Text,
                                              LSDateDateTimePicker.Date,
                                              refret, refwe);

          if (res <> 0) then
            MessageDLG('Fehler beim Anlegen einer neuen Retourenannahme' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
          else begin
            res := RetoureErfassen (Self, Sender, refret, refwe);

            if (res <> 0) then
              MessageDLG('Fehler beim Erfassen der Retourenpositionen' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
            else begin
              (*
                res := WarenannahmeAbschliessen (refwe);

                if (res <> 0) then
                  MessageDLG('Fehler bei Abschliessen der Warenretoure' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
              *)
            end;
          end;
        end;
      end;
    finally
      anlageform.Release;
    end;

    if (RetoureAvisDataSet is TOraQuery) then begin
      try
        (RetoureAvisDataSet as TOraQuery).RefreshRecord;
      except
      end;
    end;
  end;
end;

procedure TRetoureAvisSearchForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  LVSConfigModul.SaveFormParameter(Self, 'DateRange', trunc (RetoureAvisBisDateTimePicker.Date) - trunc (RetoureAvisVonDateTimePicker.Date));

  LVSConfigModul.SaveFormInfo (Self);
end;

procedure TRetoureAvisSearchForm.FormCreate (Sender: TObject);
begin
  AufNrEdit.Text := '';
  KDNameEdit.Text := '';
  PLZEdit.Text := '';
  KDNrEdit.Text := '';
  AufRefEdit.Text := '';
  TrackingEdit.Text := '';

  RetoureAvisBisDateTimePicker.Date := trunc (now);
  RetoureAvisBisDateTimePicker.Time := 0;

  RetoureAvisVonDateTimePicker.Date := trunc (now) - 30;
  RetoureAvisVonDateTimePicker.Time := 0;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
  {$endif}
end;

procedure TRetoureAvisSearchForm.FormShow(Sender: TObject);
var
  intwert : Integer;
  query   : TOraQuery;
begin
  query := TOraQuery.Create (Self);
  try
    query.ReadOnly := true;
    query.Connection := LVSDatenModul.OraMainSession;

    query.SQL.Add ('select * from V_PCD_RETOUREN_AVIS where ROWNUM=0');

    query.Open;

    AufRefEdit.Visible   := Assigned (query.FindField ('ORIG_AUF_REFERENZ'));
    TrackingEdit.Visible := Assigned (query.FindField ('RETOUREN_BARCODE_NR'));

    query.Close;
  finally
    query.Free;
  end;

  Label5.Visible := AufRefEdit.Visible;
  Label6.Visible := TrackingEdit.Visible;

  LVSConfigModul.RestoreFormInfo (Self);

  if (LVSConfigModul.ReadFormParameter(Self, 'DateRange', intwert) = 0) and (intwert > 0) then begin
    try
      RetoureAvisVonDateTimePicker.Date := RetoureAvisBisDateTimePicker.Date - intwert;
    except
      RetoureAvisVonDateTimePicker.Date := RetoureAvisBisDateTimePicker.Date - 30;
    end;
  end;
end;

procedure TRetoureAvisSearchForm.RefreshActionExecute(Sender: TObject);
begin
  RefreshButtonClick (Sender);
end;

procedure TRetoureAvisSearchForm.RefreshButtonClick(Sender: TObject);
begin
  CreateButton.Enabled := False;
  
  RetoureAvisPosDataSet.SQL.Clear;
  RetoureAvisPosDataSet.SQL.Add ('select * from V_RETOUREN_AVIS_POS where REF_RETOUREN_AVIS=:ref');

  RetoureAvisPosDataSet.Close;
  RetoureAvisDataSet.Close;

  with RetoureAvisDataSet do begin
    SQL.Clear;
    SQL.Add ('select * from V_PCD_RETOUREN_AVIS where AVIS_DATUM between :von and :bis');
    ParamByName('von').AsDate := Trunc (RetoureAvisVonDateTimePicker.Date);
    ParamByName('bis').AsDate := Trunc (RetoureAvisBisDateTimePicker.Date) + (86399/86400);

    if (LVSDatenModul.AktLagerRef <> -1) then begin
      SQL.Add ('and REF_LAGER=:ref_lager');
      ParamByName('ref_lager').Value := LVSDatenModul.AktLagerRef;
    end else begin
      SQL.Add ('and REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc)');
      ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
    end;

    //SQL.Add ('and (STATUS=''ANG'' or STATUS=''AKT'')');

    if (Length (AufNrEdit.Text) > 0) then begin
      SQL.Add ('and (ORIG_AUFTRAG_NR=:auf_nr)');
      ParamByName('auf_nr').Value := AufNrEdit.Text;
    end;

    if (Length (KDNrEdit.Text) > 0) then begin
      SQL.Add ('and (KUNDEN_NR=:kd_nr)');
      ParamByName('kd_nr').Value := KDNrEdit.Text;
    end;

    if (Length (KDNameEdit.Text) > 0) then begin
      if (Pos ('%', KDNameEdit.Text) > 0) then
        SQL.Add ('and (upper (KUNDEN_NAME) like upper (:kd_name))')
      else SQL.Add ('and (upper (KUNDEN_NAME)=upper (:kd_name))');
      ParamByName('kd_name').Value := KDNameEdit.Text;
    end;

    if (Length (PLZEdit.Text) > 0) then begin
      SQL.Add ('and (KUNDEN_PLZ=:plz)');
      ParamByName('plz').Value := PLZEdit.Text;
    end;

    if AufRefEdit.Visible and (Length (AufRefEdit.Text) > 0) then begin
      SQL.Add ('and (ORIG_AUF_REFERENZ=:auf_ref)');
      ParamByName('auf_ref').Value := AufRefEdit.Text;
    end;

    if TrackingEdit.Visible and (Length (TrackingEdit.Text) > 0) then begin
      SQL.Add ('and (RETOUREN_BARCODE_NR=:tracking)');
      ParamByName('tracking').Value := TrackingEdit.Text;
    end;

    SQL.Add ('order by RETOUREN_DATUM desc');

    SQLRefresh.Clear;
    SQLRefresh.Add ('select * from V_PCD_RETOUREN_AVIS where REF=:REF');
  end;

  Screen.Cursor := crSQLWait;

  try
    RetoureAvisDataSet.Open;
  finally
    Screen.Cursor := crDefault;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRetoureAvisSearchForm.ScannerErfassung (var Message: TMessage);
var
  refavis   : Integer;
  retart    : String;
  query     : TOraQuery;
begin
  FehlerPanel.Tag := 0;
  FehlerPanel.Visible := False;
  FehlerPanel.Caption := '';

  DoRetoureAvisScannerErfassung (Self, refavis, retart);

  if (refavis > 0) then begin
    query := TOraQuery.Create (self);

    try
      query.SQL.Add ('select * from V_PCD_RETOUREN_AVIS where REF=:ref');
      query.ParamByName ('ref').Value := refavis;

      query.Open;

      KDNameEdit.Text := query.FieldByName ('KUNDEN_NAME').AsString;

      query.Close;

      RefreshButtonClick (Nil);
    finally
      query.Free;
    end;
  end;
end;

end.
