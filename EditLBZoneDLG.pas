unit EditLBZoneDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComCtrls;

type
  TEditLBZoneForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    Label1: TLabel;
    LBLabel: TLabel;
    Bevel1: TBevel;
    NameEdit: TEdit;
    DescEdit: TEdit;
    Label2: TLabel;
    Label3: TLabel;
    NummerEdit: TEdit;
    Label4: TLabel;
    Label5: TLabel;
    KommFolgeEdit: TEdit;
    KommFolgeUpDown: TUpDown;
    Label17: TLabel;
    ABCComboBox: TComboBox;
    Bevel2: TBevel;
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure KommFolgeUpDownChangingEx(Sender: TObject;
      var AllowChange: Boolean; NewValue: Smallint;
      Direction: TUpDownDirection);
    procedure KommFolgeEditChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  ConfigModul, SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLBZoneForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    CanClose := False;

    if (NameEdit.Enabled and (Length (NameEdit.Text) = 0)) then begin
      MessageDLG (FormatMessageText (1623, []), mtError, [mbOk], 0);
      NameEdit.SetFocus;
    end else
      CanClose := True
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLBZoneForm.FormCreate(Sender: TObject);
begin
  NameEdit.Text := '';
  NummerEdit.Text := '';
  DescEdit.Text := '';
  KommFolgeUpDown.Position := 0;
  KommFolgeEdit.Text := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, ABCComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLBZoneForm.FormShow(Sender: TObject);
begin
  if not (LVSConfigModul.UseABCKlassen) then
    ABCComboBox.Visible := False;

  Label17.Visible := ABCComboBox.Visible;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLBZoneForm.KommFolgeEditChange(Sender: TObject);
begin
  if (KommFolgeUpDown.Position = -1) then
    KommFolgeEdit.Text := '';
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLBZoneForm.KommFolgeUpDownChangingEx(Sender: TObject; var AllowChange: Boolean; NewValue: Smallint; Direction: TUpDownDirection);
begin
  if (NewValue = -1) and Assigned ((Sender as TUpDown).Associate) and ((Sender as TUpDown).Associate is TEdit) then
    ((Sender as TUpDown).Associate as TEdit).Text := '';
end;

end.
