object DBLoginForm: TDBLoginForm
  Left = 291
  Top = 140
  BorderIcons = [biSystemMenu]
  BorderStyle = bsDialog
  Caption = 'Anmeldung'
  ClientHeight = 540
  ClientWidth = 438
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poScreenCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    438
    540)
  TextHeight = 13
  object Button1: TButton
    Left = 187
    Top = 483
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = '&Anmelden'
    Default = True
    ModalResult = 1
    TabOrder = 3
    OnClick = Button1Click
  end
  object AbortButton: TButton
    Left = 269
    Top = 483
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'A&bbrechen'
    ModalResult = 2
    TabOrder = 4
  end
  object Button3: TButton
    Left = 353
    Top = 483
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = '&Optionen <<'
    TabOrder = 5
    OnClick = Button3Click
  end
  object SelectPanel: TPanel
    Left = 0
    Top = 0
    Width = 438
    Height = 297
    Align = alTop
    TabOrder = 0
    DesignSize = (
      438
      297)
    object GroupBox1: TGroupBox
      Left = 8
      Top = 8
      Width = 422
      Height = 283
      Anchors = [akLeft, akTop, akRight, akBottom]
      Caption = ' Anmeldung '
      TabOrder = 0
      object LoginPanel: TPanel
        Left = 2
        Top = 15
        Width = 418
        Height = 138
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 0
        DesignSize = (
          418
          138)
        object Label2: TLabel
          Left = 12
          Top = 26
          Width = 42
          Height = 13
          Caption = 'Benutzer'
        end
        object Label3: TLabel
          Left = 12
          Top = 68
          Width = 43
          Height = 13
          Caption = 'Passwort'
        end
        object Bevel1: TBevel
          Left = 9
          Top = 133
          Width = 401
          Height = 8
          Anchors = [akLeft, akRight, akBottom]
          Shape = bsTopLine
          ExplicitWidth = 395
        end
        object DomCheckBox: TCheckBox
          Left = 12
          Top = 4
          Width = 217
          Height = 17
          Caption = 'Domain-Anmeldung benutzen'
          TabOrder = 0
          OnClick = DomCheckBoxClick
        end
        object UserEdit: TEdit
          Left = 12
          Top = 42
          Width = 389
          Height = 21
          TabOrder = 1
          Text = 'UserEdit'
          OnExit = UserEditExit
          OnKeyPress = UserEditKeyPress
        end
        object PassEdit: TEdit
          Left = 12
          Top = 84
          Width = 389
          Height = 21
          PasswordChar = '*'
          TabOrder = 2
          Text = 'PassEdit'
        end
        object SavePassCheckBox: TCheckBox
          Left = 12
          Top = 111
          Width = 141
          Height = 17
          Caption = 'Passwort speichern'
          TabOrder = 3
        end
      end
      object Panel3: TPanel
        Left = 2
        Top = 153
        Width = 418
        Height = 125
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 1
        object Label5: TLabel
          Left = 12
          Top = 2
          Width = 42
          Height = 13
          Caption = 'Mandant'
        end
        object Label7: TLabel
          Left = 12
          Top = 44
          Width = 67
          Height = 13
          Caption = 'Niederlassung'
        end
        object Label4: TLabel
          Left = 12
          Top = 86
          Width = 27
          Height = 13
          Caption = 'Lager'
        end
        object MandantComboBox: TComboBoxPro
          Left = 12
          Top = 18
          Width = 389
          Height = 21
          Style = csOwnerDrawFixed
          ColWidth = 140
          AutoPrepare = False
          ItemHeight = 15
          TabOrder = 0
          OnChange = MandantComboBoxChange
        end
        object LocationComboBox: TComboBoxPro
          Left = 11
          Top = 60
          Width = 389
          Height = 21
          Style = csOwnerDrawFixed
          ColWidth = 140
          AutoPrepare = False
          ItemHeight = 15
          TabOrder = 1
          OnChange = LocationComboBoxChange
        end
        object LagerComboBox: TComboBoxPro
          Left = 12
          Top = 102
          Width = 389
          Height = 21
          Style = csOwnerDrawFixed
          ColWidth = 140
          AutoPrepare = False
          ItemHeight = 15
          TabOrder = 2
        end
      end
    end
  end
  object SystemPanel: TPanel
    Left = 0
    Top = 357
    Width = 438
    Height = 97
    Align = alTop
    TabOrder = 2
    object GroupBox2: TGroupBox
      Left = 10
      Top = 6
      Width = 417
      Height = 77
      Caption = 'System und Schema'
      TabOrder = 0
      object Label6: TLabel
        Left = 168
        Top = 23
        Width = 39
        Height = 13
        Caption = 'Schema'
      end
      object Label1: TLabel
        Left = 12
        Top = 23
        Width = 34
        Height = 13
        Caption = 'System'
      end
      object SchemaComboBox: TComboBoxPro
        Left = 168
        Top = 39
        Width = 234
        Height = 21
        Style = csOwnerDrawFixed
        ColumeCount = 1
        ItemHeight = 15
        ItemIndex = 0
        TabOrder = 1
        Text = 'LVSENTW'
        OnChange = SchemaComboBoxChange
        Items.Strings = (
          'LVSENTW')
      end
      object SystemComboBox: TComboBox
        Left = 12
        Top = 39
        Width = 141
        Height = 21
        Style = csDropDownList
        TabOrder = 0
        OnChange = SystemComboBoxChange
      end
    end
  end
  object LeitstandPanel: TPanel
    Left = 0
    Top = 297
    Width = 438
    Height = 60
    Align = alTop
    TabOrder = 1
    object Label8: TLabel
      Left = 24
      Top = 8
      Width = 54
      Height = 13
      Caption = 'Arbeitsplatz'
    end
    object LeitstandComboBox: TComboBoxPro
      Left = 22
      Top = 24
      Width = 388
      Height = 22
      Style = csOwnerDrawFixed
      ColWidth = 140
      AutoPrepare = False
      TabOrder = 0
    end
  end
  object VerPanel: TPanel
    AlignWithMargins = True
    Left = 3
    Top = 521
    Width = 427
    Height = 16
    Margins.Right = 8
    Align = alBottom
    Alignment = taRightJustify
    BevelOuter = bvNone
    Caption = 'VerPanel'
    ParentColor = True
    TabOrder = 6
    ExplicitTop = 519
  end
  object ADOQuery1: TADOQuery
    Connection = ADOConnection1
    Parameters = <>
    Left = 360
    Top = 8
  end
  object ADOConnection1: TADOConnection
    LoginPrompt = False
    Provider = 'OraOLEDB.Oracle'
    Left = 328
    Top = 8
  end
  object Timer1: TTimer
    Enabled = False
    OnTimer = Timer1Timer
    Left = 288
    Top = 8
  end
end
