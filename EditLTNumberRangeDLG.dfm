object EditLTNumberRangeForm: TEditLTNumberRangeForm
  Left = 0
  Top = 236
  BorderStyle = bsDialog
  Caption = 'Nummerkreise f'#252'r die Ladungstr'#228'ger bearbeiten'
  ClientHeight = 426
  ClientWidth = 374
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    374
    426)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 16
    Width = 67
    Height = 13
    Caption = 'Niederlassung'
  end
  object LocationLabel: TLabel
    Left = 128
    Top = 16
    Width = 78
    Height = 13
    Caption = 'LocationLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Bevel1: TBevel
    Left = 6
    Top = 59
    Width = 364
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 307
  end
  object Label4: TLabel
    Left = 8
    Top = 40
    Width = 86
    Height = 13
    Caption = 'Ladungstr'#228'gertyp'
  end
  object LTTypeLabel: TLabel
    Left = 128
    Top = 40
    Width = 71
    Height = 13
    Caption = 'LTTypeLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Bevel3: TBevel
    Left = 6
    Top = 35
    Width = 364
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 307
  end
  object Bevel4: TBevel
    Left = 6
    Top = 113
    Width = 364
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 324
  end
  object Label2: TLabel
    Left = 8
    Top = 68
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Label10: TLabel
    Left = 8
    Top = 142
    Width = 64
    Height = 13
    Caption = 'Beschreibung'
  end
  object Label11: TLabel
    Left = 8
    Top = 123
    Width = 359
    Height = 13
    Alignment = taCenter
    Anchors = [akLeft, akTop, akRight]
    AutoSize = False
    Caption = 'Label11'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
    ExplicitWidth = 319
  end
  object Label18: TLabel
    Left = 291
    Top = 142
    Width = 11
    Height = 13
    Caption = 'ID'
  end
  object AbortButton: TButton
    Left = 291
    Top = 393
    Width = 76
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
  object OkButton: TButton
    Left = 210
    Top = 393
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 4
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 84
    Width = 359
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 16
    TabOrder = 0
    OnChange = LagerComboBoxChange
  end
  object DescEdit: TEdit
    Left = 8
    Top = 158
    Width = 274
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 64
    TabOrder = 1
    Text = 'DescEdit'
    OnChange = EditChange
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 185
    Width = 358
    Height = 193
    ActivePage = LTNumberTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 3
    object LTNumberTabSheet: TTabSheet
      Caption = 'LT-Nummer'
      ExplicitLeft = 0
      ExplicitTop = 0
      ExplicitWidth = 0
      ExplicitHeight = 0
      DesignSize = (
        350
        165)
      object Label3: TLabel
        Left = 8
        Top = 24
        Width = 67
        Height = 13
        Caption = 'Erste Nummer'
      end
      object Label5: TLabel
        Left = 104
        Top = 24
        Width = 72
        Height = 13
        Caption = 'Letzte Nummer'
      end
      object Label7: TLabel
        Left = 241
        Top = 24
        Width = 29
        Height = 13
        Caption = 'L'#228'nge'
      end
      object Bevel5: TBevel
        Left = 6
        Top = 71
        Width = 324
        Height = 6
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object Label8: TLabel
        Left = 8
        Top = 112
        Width = 28
        Height = 13
        Caption = 'Prefix'
      end
      object Label6: TLabel
        Left = 104
        Top = 112
        Width = 80
        Height = 13
        Caption = 'Aktuelle Nummer'
      end
      object Label9: TLabel
        Left = 241
        Top = 112
        Width = 33
        Height = 13
        Caption = 'Postfix'
      end
      object BeginEdit: TEdit
        Left = 8
        Top = 40
        Width = 86
        Height = 21
        MaxLength = 9
        TabOrder = 0
        Text = 'BeginEdit'
        OnChange = EditChange
        OnKeyPress = IntEditKeyPress
      end
      object EndEdit: TEdit
        Left = 104
        Top = 40
        Width = 86
        Height = 21
        MaxLength = 9
        TabOrder = 1
        Text = 'EndEdit'
        OnChange = EditChange
        OnKeyPress = IntEditKeyPress
      end
      object LenEdit: TEdit
        Left = 241
        Top = 40
        Width = 86
        Height = 21
        MaxLength = 9
        TabOrder = 2
        Text = 'LenEdit'
        OnChange = EditChange
        OnKeyPress = IntEditKeyPress
      end
      object CycleCheckBox: TCheckBox
        Left = 8
        Top = 76
        Width = 319
        Height = 17
        Caption = 'Umlaufende Nummer'
        TabOrder = 3
        OnClick = EditChange
      end
      object PrefixEdit: TEdit
        Left = 8
        Top = 128
        Width = 86
        Height = 21
        MaxLength = 8
        TabOrder = 4
        Text = 'PrefixEdit'
        OnChange = EditChange
      end
      object SequenceEdit: TEdit
        Left = 104
        Top = 128
        Width = 129
        Height = 21
        MaxLength = 9
        TabOrder = 5
        Text = 'SequenceEdit'
        OnChange = EditChange
        OnKeyPress = IntEditKeyPress
      end
      object PostfixEdit: TEdit
        Left = 241
        Top = 128
        Width = 86
        Height = 21
        MaxLength = 8
        TabOrder = 6
        Text = 'PostfixEdit'
        OnChange = EditChange
      end
    end
    object SerialTabSheet: TTabSheet
      Caption = 'Seriennummer'
      ImageIndex = 1
      ExplicitLeft = 0
      ExplicitTop = 0
      ExplicitWidth = 0
      ExplicitHeight = 0
      DesignSize = (
        350
        165)
      object Label12: TLabel
        Left = 241
        Top = 112
        Width = 33
        Height = 13
        Caption = 'Postfix'
      end
      object Label13: TLabel
        Left = 104
        Top = 112
        Width = 80
        Height = 13
        Caption = 'Aktuelle Nummer'
      end
      object Bevel6: TBevel
        Left = 6
        Top = 71
        Width = 324
        Height = 6
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object Label14: TLabel
        Left = 8
        Top = 112
        Width = 28
        Height = 13
        Caption = 'Prefix'
      end
      object Label15: TLabel
        Left = 241
        Top = 24
        Width = 29
        Height = 13
        Caption = 'L'#228'nge'
      end
      object Label16: TLabel
        Left = 104
        Top = 24
        Width = 72
        Height = 13
        Caption = 'Letzte Nummer'
      end
      object Label17: TLabel
        Left = 8
        Top = 24
        Width = 67
        Height = 13
        Caption = 'Erste Nummer'
      end
      object SerialPostfixEdit: TEdit
        Left = 241
        Top = 128
        Width = 86
        Height = 21
        MaxLength = 8
        TabOrder = 5
        Text = 'PostfixEdit'
        OnChange = EditChange
      end
      object SerialSequenceEdit: TEdit
        Left = 104
        Top = 128
        Width = 129
        Height = 21
        MaxLength = 9
        TabOrder = 4
        Text = 'SequenceEdit'
        OnChange = EditChange
        OnKeyPress = IntEditKeyPress
      end
      object SerialPrefixEdit: TEdit
        Left = 8
        Top = 128
        Width = 86
        Height = 21
        MaxLength = 8
        TabOrder = 3
        Text = 'PrefixEdit'
        OnChange = EditChange
      end
      object SerialLenEdit: TEdit
        Left = 241
        Top = 40
        Width = 86
        Height = 21
        MaxLength = 9
        TabOrder = 2
        Text = 'LenEdit'
        OnChange = EditChange
        OnKeyPress = IntEditKeyPress
      end
      object SerialEndEdit: TEdit
        Left = 104
        Top = 40
        Width = 86
        Height = 21
        MaxLength = 9
        TabOrder = 1
        Text = 'EndEdit'
        OnChange = EditChange
        OnKeyPress = IntEditKeyPress
      end
      object SerialBeginEdit: TEdit
        Left = 8
        Top = 40
        Width = 86
        Height = 21
        MaxLength = 9
        TabOrder = 0
        Text = 'BeginEdit'
        OnChange = EditChange
        OnKeyPress = IntEditKeyPress
      end
    end
  end
  object LTIDEdit: TEdit
    Left = 291
    Top = 158
    Width = 75
    Height = 21
    TabOrder = 2
    Text = 'LTIDEdit'
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 296
    Top = 8
  end
  object PopupMenu1: TPopupMenu
    Left = 264
    Top = 8
  end
end
