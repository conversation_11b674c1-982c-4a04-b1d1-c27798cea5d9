unit AssignPackplatzDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls;

type
  TAssignPackplatzForm = class(TForm)
    GrundLabel: TLabel;
    PackComboBox: TComboBoxPro;
    OkButton: TButton;
    AbortButton: TButton;
    Label1: TLabel;
    OrderLabel: TLabel;
    Bevel1: TBevel;
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormDestroy(Sender: TObject);
  private
    fRefAuf   : Integer;
    fRefBatch : Integer;
    fRefLager : Integer;
  public
    procedure Prepare (const RefAuftrag, RefBatchlauf : Integer; const Options : String = '');
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DB, ADODB, DatenModul, FrontendUtils, FrontendMainUtils, LVSDatenInterface, ResourceText,
  SprachModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.02.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAssignPackplatzForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res : Integer;
begin
  res := 0;
  
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    if (fRefAuf > 0) then
      res := AssigneAuftragPackplatz (GetComboBoxRef(PackComboBox), fRefAuf)
    else if (fRefBatch > 0) then
      res := AssigneBatchPackplatz (GetComboBoxRef(PackComboBox), fRefBatch);

    if (res = 0) then
      CanClose := True
    else begin
       CanClose := False;

      MessageDLG ('Fehler beim Zuweisen des Packplatzes' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.02.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAssignPackplatzForm.FormCreate(Sender: TObject);
begin
  fRefAuf   := -1;
  fRefBatch := -1;
  fRefLager := -1;

  Label1.Caption     := '';
  OrderLabel.Caption := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, PackComboBox);
    LVSSprachModul.SetNoTranslate (Self, Label1);
    LVSSprachModul.SetNoTranslate (Self, OrderLabel);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.02.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAssignPackplatzForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (PackComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.02.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAssignPackplatzForm.Prepare (const RefAuftrag, RefBatchlauf : Integer; const Options : String);
var
  query : TADOQuery;
begin
  fRefAuf   := RefAuftrag;
  fRefBatch := RefBatchlauf;

  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (fRefAuf > 0) then begin
      Label1.Caption := GetResourceText (1627);

      query.SQL.Add ('select REF,REF_LAGER,AUFTRAG_NR from VQ_AUFTRAG where REF=:ref');
      query.Parameters [0].Value := fRefAuf;
    end else if (fRefBatch > 0) then begin
      Label1.Caption := 'Batchlauf';

      query.SQL.Add ('select REF,REF_LAGER, BATCH_NR from V_AUFTRAG_BATCHLAUF where REF=:ref');
      query.Parameters [0].Value := fRefBatch;
    end;

    query.Open;

    if (query.RecordCount > 0) then begin
      fRefLager := query.Fields [1].AsInteger;

      OrderLabel.Caption := query.Fields [2].AsString;
    end;

    query.Close;
  finally
    query.Free;
  end;

  if (fRefLager > 0) then
    LoadPackplatz (PackComboBox, fRefLager, Options);
end;


end.
