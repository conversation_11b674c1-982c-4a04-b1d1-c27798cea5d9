object BaseStammdatenForm: TBaseStammdatenForm
  Left = 336
  Top = 268
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Basisstammdaten'
  ClientHeight = 523
  ClientWidth = 933
  Color = clBtnFace
  Constraints.MinHeight = 450
  Constraints.MinWidth = 800
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnKeyDown = FormKeyDown
  OnResize = FormResize
  OnShow = FormShow
  DesignSize = (
    933
    523)
  TextHeight = 13
  object CloseButton: TButton
    Left = 849
    Top = 492
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 1
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 8
    Width = 917
    Height = 470
    ActivePage = LHMTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 0
    object LHMTabSheet: TTabSheet
      Caption = 'Ladungstr'#228'ger'
      OnResize = LHMTabSheetResize
      OnShow = LHMTabSheetShow
      DesignSize = (
        909
        442)
      object Label1: TLabel
        Left = 3
        Top = 8
        Width = 94
        Height = 13
        Caption = 'Ladungstr'#228'gertypen'
      end
      object LTDBGrid: TDBGridPro
        Left = 3
        Top = 27
        Width = 805
        Height = 237
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = LTDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
        PopupMenu = LTDBGridPopupMenu
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        OnDblClick = LTDBGridDblClick
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'MS Sans Serif'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object NeuLTButton: TButton
        Left = 826
        Top = 23
        Width = 75
        Height = 25
        Anchors = [akTop, akRight]
        Caption = '&Neu...'
        TabOrder = 1
        OnClick = NeuLTButtonClick
      end
      object DelLTButton: TButton
        Left = 826
        Top = 236
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = '&L'#246'schen'
        TabOrder = 4
        OnClick = DelLTButtonClick
      end
      object EinsatzGroupBox: TGroupBox
        Left = 3
        Top = 267
        Width = 265
        Height = 172
        Anchors = [akLeft, akBottom]
        Caption = ' Einsatzm'#246'glichkeiten '
        TabOrder = 5
        DesignSize = (
          265
          172)
        object EinsatzCommitButton: TButton
          Left = 92
          Top = 139
          Width = 75
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = #220'bernehmen'
          TabOrder = 0
          OnClick = EinsatzCommitButtonClick
        end
        object EinsatzRollbackButton: TButton
          Left = 180
          Top = 139
          Width = 75
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = '&Verwerfen'
          TabOrder = 1
          OnClick = EinsatzRollbackButtonClick
        end
        object EinsatzCheckListBox: TCheckListBox
          Left = 8
          Top = 24
          Width = 247
          Height = 109
          Anchors = [akLeft, akTop, akRight, akBottom]
          ItemHeight = 13
          Style = lbOwnerDrawFixed
          TabOrder = 2
          OnClick = EinsatzCheckListBoxClick
          OnDrawItem = ListBoxDrawItem
        end
      end
      object EditLTButton: TButton
        Left = 826
        Top = 86
        Width = 75
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Bearbeiten...'
        TabOrder = 3
        OnClick = EditLTButtonClick
      end
      object CopyLTButton: TButton
        Left = 826
        Top = 54
        Width = 75
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Kopieren...'
        TabOrder = 2
        OnClick = EditLTButtonClick
      end
      object TypGroupBox: TGroupBox
        Left = 281
        Top = 267
        Width = 240
        Height = 172
        Anchors = [akBottom]
        Caption = 'Zul'#228'ssige LP-Typen f'#252'r den Ladungstr'#228'ger'
        TabOrder = 6
        DesignSize = (
          240
          172)
        object ZuordnungRollbackButton: TButton
          Left = 153
          Top = 138
          Width = 75
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = '&Verwerfen'
          TabOrder = 0
          OnClick = ZuordnungRollbackButtonClick
        end
        object ZuordnungCommitButton: TButton
          Left = 65
          Top = 138
          Width = 75
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = #220'bernehmen'
          TabOrder = 1
          OnClick = ZuordnungCommitButtonClick
        end
        object ZuordnungCheckListBox: TCheckListBox
          Left = 8
          Top = 24
          Width = 221
          Height = 109
          Anchors = [akLeft, akTop, akRight, akBottom]
          ItemHeight = 13
          Style = lbOwnerDrawFixed
          TabOrder = 2
          OnClick = ZuordnungCheckListBoxClick
          OnDrawItem = ListBoxDrawItem
        end
      end
      object MandantGroupBox: TGroupBox
        Left = 527
        Top = 267
        Width = 240
        Height = 172
        Anchors = [akBottom]
        Caption = 'Verwenden bei Mandant'
        TabOrder = 7
        DesignSize = (
          240
          172)
        object MandantRollbackButton: TButton
          Left = 153
          Top = 138
          Width = 75
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = '&Verwerfen'
          TabOrder = 0
          OnClick = MandantRollbackButtonClick
        end
        object MandantCommitButton: TButton
          Left = 65
          Top = 138
          Width = 75
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = #220'bernehmen'
          TabOrder = 1
          OnClick = MandantCommitButtonClick
        end
        object MandantCheckListBox: TCheckListBox
          Left = 8
          Top = 24
          Width = 221
          Height = 109
          Anchors = [akLeft, akTop, akRight, akBottom]
          ItemHeight = 13
          Style = lbOwnerDrawFixed
          TabOrder = 2
          OnClick = MandantCheckListBoxClick
          OnDrawItem = ListBoxDrawItem
        end
      end
    end
    object TextTabSheet: TTabSheet
      Caption = 'Texte'
      ImageIndex = 1
      OnShow = TextTabSheetShow
      DesignSize = (
        909
        442)
      object Bereich: TLabel
        Left = 8
        Top = 16
        Width = 36
        Height = 13
        Caption = 'Bereich'
      end
      object TextAreaComboBox: TComboBoxPro
        Left = 8
        Top = 32
        Width = 805
        Height = 21
        Style = csDropDownList
        Anchors = [akLeft, akTop, akRight]
        DropDownCount = 16
        TabOrder = 0
        OnChange = TextAreaComboBoxChange
      end
      object AddTextButton: TButton
        Left = 819
        Top = 72
        Width = 87
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Hinzuf'#252'gen'
        TabOrder = 1
        OnClick = AddTextButtonClick
      end
      object EditTextButton: TButton
        Left = 819
        Top = 103
        Width = 87
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Bearbeiten'
        TabOrder = 2
        OnClick = EditTextButtonClick
      end
      object DelTextButton: TButton
        Left = 819
        Top = 380
        Width = 87
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'L'#246'schen'
        TabOrder = 3
        OnClick = DelTextButtonClick
      end
      object TextDBGrid: TDBGridPro
        Left = 8
        Top = 72
        Width = 805
        Height = 333
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = TextDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 4
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        OnDblClick = EditTextButtonClick
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
        RegistryKey = 'Software\CS'
        RegistrySection = 'DBGrids'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
    end
    object BesCategoryTabSheet: TTabSheet
      Caption = 'Folgeprozesse'
      ImageIndex = 2
      OnShow = BesCategoryTabSheetShow
      DesignSize = (
        909
        442)
      object BesCategoryDBGrid: TDBGridPro
        Left = 8
        Top = 8
        Width = 805
        Height = 253
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = BesCategoryDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
        RegistryKey = 'Software\CS'
        RegistrySection = 'DBGrids'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object BesCatAddButton: TButton
        Left = 819
        Top = 8
        Width = 87
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Hinzuf'#252'gen'
        TabOrder = 1
      end
      object BesCatEditButton: TButton
        Left = 819
        Top = 39
        Width = 87
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Bearbeiten'
        TabOrder = 2
      end
      object BesCatDelButton: TButton
        Left = 819
        Top = 236
        Width = 87
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'L'#246'schen'
        TabOrder = 3
      end
      object GroupBox1: TGroupBox
        Left = 8
        Top = 276
        Width = 805
        Height = 145
        Anchors = [akLeft, akRight, akBottom]
        Caption = 'Daten'
        TabOrder = 4
        object Label3: TLabel
          Left = 8
          Top = 24
          Width = 86
          Height = 13
          Caption = 'Lagerungsgruppe:'
        end
        object StorageGroupLabel: TLabel
          Left = 120
          Top = 24
          Width = 110
          Height = 13
          Caption = 'StorageGroupLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object Label4: TLabel
          Left = 8
          Top = 64
          Width = 89
          Height = 13
          Caption = 'Hersteller mischen:'
        end
        object ManfReinLabel: TLabel
          Left = 120
          Top = 64
          Width = 86
          Height = 13
          Caption = 'ManfReinLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object Label8: TLabel
          Left = 8
          Top = 88
          Width = 65
          Height = 13
          Caption = 'Zustand egal:'
        end
        object ZustandEgalLabel: TLabel
          Left = 120
          Top = 88
          Width = 103
          Height = 13
          Caption = 'ZustandEgalLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object Label10: TLabel
          Left = 8
          Top = 112
          Width = 102
          Height = 13
          Caption = 'Zusatzinfos abfragen:'
        end
        object AskAddInfoLabel: TLabel
          Left = 120
          Top = 112
          Width = 97
          Height = 13
          Caption = 'AskAddInfoLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
      end
    end
    object RetZustandTabSheet: TTabSheet
      Caption = 'Retouren Zustand'
      ImageIndex = 3
      OnShow = RetZustandTabSheetShow
      DesignSize = (
        909
        442)
      object Label2: TLabel
        Left = 8
        Top = 16
        Width = 36
        Height = 13
        Caption = 'Bereich'
      end
      object RetZustandAreaComboBox: TComboBoxPro
        Left = 8
        Top = 32
        Width = 781
        Height = 21
        Style = csDropDownList
        Anchors = [akLeft, akTop, akRight]
        DropDownCount = 16
        TabOrder = 0
        OnChange = RetZustandAreaComboBoxChange
      end
      object RetZustandDBGrid: TDBGridPro
        Left = 8
        Top = 70
        Width = 781
        Height = 333
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = RetZustandDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 1
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        OnDblClick = EditRetZustandButtonClick
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
        RegistryKey = 'Software\CS'
        RegistrySection = 'DBGrids'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object AddRetZustandButton: TButton
        Left = 795
        Top = 72
        Width = 110
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Hinzuf'#252'gen'
        TabOrder = 2
        OnClick = AddRetZustandButtonClick
      end
      object EditRetZustandButton: TButton
        Left = 795
        Top = 103
        Width = 110
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Bearbeiten'
        TabOrder = 3
        OnClick = EditRetZustandButtonClick
      end
      object DelRetZustandButton: TButton
        Left = 795
        Top = 380
        Width = 110
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'L'#246'schen'
        TabOrder = 5
        OnClick = DelRetZustandButtonClick
      end
      object PrintRetButton: TButton
        Left = 795
        Top = 151
        Width = 110
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Barcodes drucken'
        TabOrder = 4
        OnClick = PrintRetButtonClick
      end
    end
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 824
    Top = 120
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 728
    Top = 336
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 488
    Top = 24
  end
  object LTQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 448
    Top = 24
  end
  object LTDataSource: TDataSource
    DataSet = LTQuery
    OnDataChange = LTDataSourceDataChange
    Left = 416
    Top = 24
  end
  object LTDBGridPopupMenu: TPopupMenu
    OnPopup = LTDBGridPopupMenuPopup
    Left = 224
    Top = 96
    object StdLTMenuItem: TMenuItem
      Caption = 'Als Standard-LT im Lager definieren'
      OnClick = StdLTMenuItemClick
    end
    object StdLTEinlagerMenuItem: TMenuItem
      Caption = 'Als Standard-LT f'#252'r die Einlagerung definieren'
      OnClick = StdLTEinlagerMenuItemClick
    end
    object StdLTKommMenuItem: TMenuItem
      Caption = 'Als Standard-LT f'#252'r die Kommissionierung definieren'
      OnClick = StdLTKommMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object CreateLTRangeMenuItem: TMenuItem
      Caption = 'Nummerkreis f'#252'r das LT anlegen...'
      OnClick = EditLTRangeMenuItemClick
    end
    object EditLTRangeMenuItem: TMenuItem
      Caption = 'Nummerkreis des LTs bearbeiten...'
      OnClick = EditLTRangeMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object Etikettendrucken1: TMenuItem
      Caption = 'Etiketten drucken...'
      OnClick = Etikettendrucken1Click
    end
  end
  object TextDataSource: TDataSource
    DataSet = TextQuery
    OnDataChange = TextDataSourceDataChange
    Left = 560
    Top = 32
  end
  object TextQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 592
    Top = 32
  end
  object RetZustandDataSource: TDataSource
    DataSet = RetZustandQuery
    OnDataChange = RetZustandDataSourceDataChange
    Left = 632
    Top = 32
  end
  object RetZustandQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 664
    Top = 32
  end
  object BesCategoryDataSource: TDataSource
    DataSet = BesCategoryQuery
    OnDataChange = BesCategoryDataSourceDataChange
    Left = 560
    Top = 72
  end
  object BesCategoryQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 592
    Top = 72
  end
  object BesCategoryDBGridPopupMenu: TPopupMenu
    Left = 400
    Top = 104
  end
end
