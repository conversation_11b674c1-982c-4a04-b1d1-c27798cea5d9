unit EditWABereichDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComCtrls, ComboBoxPro, ExtCtrls, DB, ADODB;

type
  TEditWABereichForm = class(TForm)
    NameEdit: TEdit;
    BeschreibungEdit: TEdit;
    ArtComboBox: TComboBoxPro;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel1: TBevel;
    Bevel2: TBevel;
    LagerComboBox: TComboBoxPro;
    Label4: TLabel;
    Bevel3: TBevel;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    fRefLager : Integer;
  public
    procedure Prepare (const RefLager : Integer);
  end;

implementation

{$R *.dfm}

uses VCLUtilitys, DatenModul, FrontendUtils, SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditWABereichForm.FormCloseQuery (Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := False;

    if (LagerComboBox.ItemIndex = -1) then begin
      LagerComboBox.SetFocus;
      MessageDLG (FormatMessageText (1305, []), mtError, [mbOk], 0)
    end else if (Length (NameEdit.Text) = 0) then  begin
      NameEdit.SetFocus;
      MessageDLG (FormatMessageText (1176, []), mtError, [mbOk], 0)
    end else
      CanClose := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditWABereichForm.FormCreate(Sender: TObject);
begin
  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
    LVSSprachModul.SetNoTranslate (Self, ArtComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditWABereichForm.FormShow(Sender: TObject);
begin
  if (LagerComboBox.Items.Count = 1) then begin
    LagerComboBox.ItemIndex := 0;
    LagerComboBox.Enabled := False;
  end else if (fRefLager = -1) then
    LagerComboBox.ItemIndex := -1
  else begin
    LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, fRefLager);

    if (LagerComboBox.ItemIndex <> -1) Then
      LagerComboBox.Enabled := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditWABereichForm.Prepare (const RefLager : Integer);
begin
  fRefLager := RefLager;

  NameEdit.Text         := '';
  BeschreibungEdit.Text := '';

  ArtComboBox.ItemIndex := 0;

  LoadLagerCombobox (LagerComboBox, LVSDatenModul.AktLocation);

  LoadComboxDBItems (ArtComboBox, 'LB', 'LB_ART');
end;

end.
