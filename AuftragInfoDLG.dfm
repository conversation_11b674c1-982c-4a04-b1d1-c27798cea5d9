object AuftragInfoForm: TAuftragInfoForm
  Left = 0
  Top = 0
  Caption = 'Auftragsinfo'
  ClientHeight = 647
  ClientWidth = 624
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    624
    647)
  TextHeight = 15
  object Button1: TButton
    Left = 541
    Top = 614
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 4
  end
  object TextGroupBox: TGroupBox
    Left = 8
    Top = 112
    Width = 608
    Height = 193
    Anchors = [akLeft, akTop, akRight]
    Caption = 'Texte'
    TabOrder = 1
    DesignSize = (
      608
      193)
    object Label1: TLabel
      Left = 8
      Top = 24
      Width = 82
      Height = 15
      Caption = 'Kommhinweise'
    end
    object Label2: TLabel
      Left = 8
      Top = 70
      Width = 71
      Height = 15
      Caption = 'Packhinweise'
    end
    object Label3: TLabel
      Left = 8
      Top = 144
      Width = 87
      Height = 15
      Anchors = [akLeft, akBottom]
      Caption = 'Versandhinweise'
      ExplicitTop = 128
    end
    object KommEdit: TEdit
      Left = 8
      Top = 40
      Width = 594
      Height = 23
      ReadOnly = True
      TabOrder = 0
      Text = 'KommEdit'
    end
    object VersandEdit: TEdit
      Left = 8
      Top = 160
      Width = 594
      Height = 23
      Anchors = [akLeft, akBottom]
      ReadOnly = True
      TabOrder = 1
      Text = 'VersandEdit'
    end
    object PackEdit: TMemo
      Left = 8
      Top = 86
      Width = 594
      Height = 51
      Lines.Strings = (
        'PackEdit')
      ReadOnly = True
      TabOrder = 2
    end
  end
  object RechnungGroupBox: TGroupBox
    Left = 8
    Top = 311
    Width = 608
    Height = 131
    Anchors = [akLeft, akTop, akRight]
    Caption = 'Rechnung'
    TabOrder = 2
    object Label4: TLabel
      Left = 8
      Top = 24
      Width = 73
      Height = 15
      Caption = 'Rechnungsnr.'
    end
    object Label5: TLabel
      Left = 96
      Top = 55
      Width = 30
      Height = 15
      Caption = 'Netto'
    end
    object Label6: TLabel
      Left = 224
      Top = 55
      Width = 33
      Height = 15
      Caption = 'Brutto'
    end
    object Label9: TLabel
      Left = 8
      Top = 75
      Width = 74
      Height = 15
      Caption = #220'bernommen'
    end
    object Label7: TLabel
      Left = 8
      Top = 99
      Width = 53
      Height = 15
      Caption = 'Berechnet'
    end
    object Label8: TLabel
      Left = 187
      Top = 75
      Width = 6
      Height = 15
      Caption = #8364
    end
    object Label10: TLabel
      Left = 187
      Top = 99
      Width = 6
      Height = 15
      Caption = #8364
    end
    object Label11: TLabel
      Left = 315
      Top = 75
      Width = 6
      Height = 15
      Caption = #8364
    end
    object Label12: TLabel
      Left = 315
      Top = 99
      Width = 6
      Height = 15
      Caption = #8364
    end
    object BillNoEdit: TEdit
      Left = 96
      Top = 21
      Width = 242
      Height = 23
      ReadOnly = True
      TabOrder = 0
      Text = 'BillNoEdit'
    end
    object BillNetEdit: TEdit
      Left = 96
      Top = 72
      Width = 90
      Height = 23
      ReadOnly = True
      TabOrder = 1
      Text = 'BillNetEdit'
    end
    object BillGrosEdit: TEdit
      Left = 224
      Top = 72
      Width = 90
      Height = 23
      ReadOnly = True
      TabOrder = 2
      Text = 'BillGrosEdit'
    end
    object BillNetCalcEdit: TEdit
      Left = 96
      Top = 96
      Width = 90
      Height = 23
      ReadOnly = True
      TabOrder = 3
      Text = 'BillNetEdit'
    end
    object BillGrosCalcEdit: TEdit
      Left = 224
      Top = 96
      Width = 90
      Height = 23
      ReadOnly = True
      TabOrder = 4
      Text = 'BillGrossEdit'
    end
    object BillBarCheckBox: TCheckBox
      Left = 472
      Top = 24
      Width = 97
      Height = 17
      Caption = 'Barzahler'
      Enabled = False
      TabOrder = 5
    end
    object BillCODCheckBox: TCheckBox
      Left = 472
      Top = 47
      Width = 97
      Height = 17
      Caption = 'Nachnahme'
      Enabled = False
      TabOrder = 6
    end
  end
  object VersandGroupBox: TGroupBox
    Left = 8
    Top = 448
    Width = 608
    Height = 152
    Anchors = [akLeft, akTop, akRight]
    Caption = 'Versand'
    TabOrder = 3
    object Label13: TLabel
      Left = 8
      Top = 104
      Width = 72
      Height = 15
      Caption = 'Avisierung an'
    end
    object Label14: TLabel
      Left = 224
      Top = 104
      Width = 75
      Height = 15
      Caption = 'Avis-Hinweise'
    end
    object Label15: TLabel
      Left = 8
      Top = 52
      Width = 122
      Height = 15
      Caption = 'Versand Ladehilfsmittel'
    end
    object Label16: TLabel
      Left = 224
      Top = 52
      Width = 122
      Height = 15
      Caption = 'Versand Packhilfsmittel'
    end
    object VersTallLiftCheckBox: TCheckBox
      Left = 8
      Top = 24
      Width = 193
      Height = 17
      Caption = 'Hebeb'#252'he erforderlich'
      Enabled = False
      TabOrder = 0
    end
    object DSADVCheckBox: TCheckBox
      Left = 344
      Top = 24
      Width = 82
      Height = 17
      Caption = 'DESADV'
      Enabled = False
      TabOrder = 1
    end
    object PrintLSCheckBox: TCheckBox
      Left = 456
      Top = 24
      Width = 150
      Height = 17
      Caption = 'Lieferschein drucken'
      Enabled = False
      TabOrder = 2
    end
    object AvisToEdit: TEdit
      Left = 8
      Top = 120
      Width = 185
      Height = 23
      ReadOnly = True
      TabOrder = 3
      Text = 'AvisToEdit'
    end
    object AvisInfoEdit: TEdit
      Left = 224
      Top = 120
      Width = 369
      Height = 23
      ReadOnly = True
      TabOrder = 4
      Text = 'AvisInfoEdit'
    end
    object PrintRetCheckBox: TCheckBox
      Left = 456
      Top = 40
      Width = 150
      Height = 17
      Caption = 'Retourenschein drucken'
      Enabled = False
      TabOrder = 5
    end
    object PrintRetLabelCheckBox: TCheckBox
      Left = 456
      Top = 56
      Width = 150
      Height = 17
      Caption = 'Retourenlabel drucken'
      Enabled = False
      TabOrder = 6
    end
    object VersGLNEdit: TEdit
      Left = 224
      Top = 21
      Width = 114
      Height = 23
      ReadOnly = True
      TabOrder = 7
      Text = 'VersGLNEdit'
    end
    object PrintNVEInhaltCheckBox: TCheckBox
      Left = 456
      Top = 72
      Width = 150
      Height = 17
      Caption = 'NVE Inhaltsliste drucken'
      Enabled = False
      TabOrder = 8
    end
    object VersLTComboBox: TComboBoxPro
      Left = 8
      Top = 68
      Width = 185
      Height = 22
      Style = csOwnerDrawFixed
      Enabled = False
      TabOrder = 9
    end
    object VersPackComboBox: TComboBoxPro
      Left = 224
      Top = 68
      Width = 185
      Height = 22
      Style = csOwnerDrawFixed
      Enabled = False
      TabOrder = 10
    end
  end
  object KopfGroupBox: TGroupBox
    Left = 8
    Top = 7
    Width = 608
    Height = 99
    Caption = 'Allgemein'
    TabOrder = 0
    object Label17: TLabel
      Left = 8
      Top = 24
      Width = 60
      Height = 15
      Caption = 'Auftragnr.:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object AufNrLabel: TLabel
      Left = 104
      Top = 24
      Width = 60
      Height = 15
      Caption = 'AufNrLabel'
    end
    object Label18: TLabel
      Left = 8
      Top = 45
      Width = 64
      Height = 15
      Caption = 'Empf'#228'nger:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object EmpfLabel: TLabel
      Left = 104
      Top = 45
      Width = 56
      Height = 15
      Caption = 'EmpfLabel'
    end
    object Label19: TLabel
      Left = 8
      Top = 77
      Width = 49
      Height = 15
      Caption = 'Vertrieb:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object SalesLabel: TLabel
      Left = 104
      Top = 77
      Width = 54
      Height = 15
      Caption = 'SalesLabel'
    end
    object Label21: TLabel
      Left = 309
      Top = 77
      Width = 69
      Height = 15
      Alignment = taRightJustify
      Caption = 'Innendienst:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object OfficeLabel: TLabel
      Left = 384
      Top = 77
      Width = 60
      Height = 15
      Caption = 'OfficeLabel'
    end
    object Label23: TLabel
      Left = 272
      Top = 24
      Width = 106
      Height = 15
      Alignment = taRightJustify
      Caption = 'Kunden Auftragnr.:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object KdAufNrLabel: TLabel
      Left = 384
      Top = 24
      Width = 74
      Height = 15
      Caption = 'KdAufNrLabel'
    end
  end
end
