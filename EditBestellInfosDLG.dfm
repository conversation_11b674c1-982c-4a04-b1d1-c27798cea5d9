object EditBestellInfosForm: TEditBestellInfosForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Lieferanten-Bestellung bearbeiten'
  ClientHeight = 275
  ClientWidth = 533
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  DesignSize = (
    533
    275)
  PixelsPerInch = 96
  TextHeight = 13
  object OkButton: TButton
    Left = 358
    Top = 242
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 2
    ExplicitTop = 210
  end
  object AbortButton: TButton
    Left = 450
    Top = 242
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 3
    ExplicitTop = 210
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 0
    Width = 533
    Height = 81
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      533
      81)
    object Bevel1: TBevel
      Left = 6
      Top = 73
      Width = 521
      Height = 8
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 121
      ExplicitWidth = 511
    end
    object Label4: TLabel
      Left = 8
      Top = 16
      Width = 49
      Height = 13
      Caption = 'Bestellnr.:'
    end
    object Label5: TLabel
      Left = 8
      Top = 40
      Width = 47
      Height = 13
      Caption = 'Lieferant:'
    end
    object BestellNrLabel: TLabel
      Left = 80
      Top = 16
      Width = 80
      Height = 13
      Caption = 'BestellNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LieferantLabel: TLabel
      Left = 80
      Top = 40
      Width = 81
      Height = 13
      Caption = 'LieferantLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object InputPanel: TPanel
    Left = 0
    Top = 81
    Width = 533
    Height = 155
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      533
      155)
    object Label1: TLabel
      Left = 8
      Top = 16
      Width = 117
      Height = 13
      Caption = 'Avisiertes Anlieferdatum'
    end
    object Label2: TLabel
      Left = 8
      Top = 45
      Width = 42
      Height = 13
      Caption = 'Hinweise'
    end
    object Label3: TLabel
      Left = 8
      Top = 123
      Width = 76
      Height = 13
      Caption = 'Einlagerplanung'
    end
    object Bevel2: TBevel
      Left = 6
      Top = 151
      Width = 521
      Height = 8
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 116
    end
    object LieferDateTimePicker: TDateTimePicker
      Left = 144
      Top = 11
      Width = 97
      Height = 21
      Date = 42412.453377974540000000
      Time = 42412.453377974540000000
      TabOrder = 0
    end
    object EinlagerplanComboBox: TComboBoxPro
      Left = 144
      Top = 120
      Width = 381
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 3
    end
    object TeilLieferCheckBox: TCheckBox
      Left = 280
      Top = 15
      Width = 97
      Height = 17
      Caption = 'Teillieferung'
      TabOrder = 1
    end
    object HinweisMemo: TMemo
      Left = 144
      Top = 45
      Width = 381
      Height = 62
      Lines.Strings = (
        'HinweisMemo')
      MaxLength = 256
      TabOrder = 2
    end
  end
end
