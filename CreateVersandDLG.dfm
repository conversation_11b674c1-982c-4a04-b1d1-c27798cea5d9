object CreateVersandForm: TCreateVersandForm
  Left = 0
  Top = 0
  Caption = 'Versand anlegen'
  ClientHeight = 665
  ClientWidth = 697
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  DesignSize = (
    697
    665)
  PixelsPerInch = 96
  TextHeight = 13
  object Label16: TLabel
    Left = 8
    Top = 21
    Width = 84
    Height = 13
    Caption = 'Warenempf'#228'nger'
  end
  object KundenDBGrid: TDBGridPro
    Left = 8
    Top = 37
    Width = 681
    Height = 308
    Anchors = [akLeft, akTop, akRight, akBottom]
    BiDiMode = bdLeftToRight
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ParentBiDiMode = False
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object GroupBox1: TGroupBox
    Left = 8
    Top = 360
    Width = 680
    Height = 257
    Anchors = [akLeft, akTop, akRight]
    Caption = 'Versenden an'
    TabOrder = 1
    DesignSize = (
      680
      257)
    object Label1: TLabel
      Left = 8
      Top = 68
      Width = 84
      Height = 13
      Caption = 'Name, erste Zeile'
    end
    object Label3: TLabel
      Left = 312
      Top = 68
      Width = 90
      Height = 13
      Caption = 'Name, zweite Zeile'
    end
    object Label2: TLabel
      Left = 8
      Top = 112
      Width = 64
      Height = 13
      Caption = 'Adresszusatz'
    end
    object StrasseLabel: TLabel
      Left = 8
      Top = 158
      Width = 81
      Height = 13
      Caption = 'Strasse, Hausnr.'
    end
    object Label6: TLabel
      Left = 312
      Top = 158
      Width = 71
      Height = 13
      Caption = 'Strasse Zusatz'
    end
    object PLZLabel: TLabel
      Left = 8
      Top = 204
      Width = 54
      Height = 13
      Caption = 'Postleitzahl'
    end
    object OrtLabel: TLabel
      Left = 112
      Top = 204
      Width = 16
      Height = 13
      Caption = 'Ort'
    end
    object LandLabel: TLabel
      Left = 543
      Top = 204
      Width = 23
      Height = 13
      Caption = 'Land'
    end
    object Label4: TLabel
      Left = 8
      Top = 19
      Width = 44
      Height = 13
      Caption = 'Spedition'
    end
    object Name1Edit: TEdit
      Left = 8
      Top = 84
      Width = 275
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      Text = 'Name1Edit'
      ExplicitWidth = 277
    end
    object Name2Edit: TEdit
      Left = 310
      Top = 84
      Width = 354
      Height = 21
      Anchors = [akTop, akRight]
      TabOrder = 1
      Text = 'Name2Edit'
      ExplicitLeft = 311
    end
    object ZusatzEdit: TEdit
      Left = 8
      Top = 128
      Width = 656
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 2
      Text = 'ZusatzEdit'
      ExplicitWidth = 657
    end
    object StrasseEdit: TEdit
      Left = 8
      Top = 174
      Width = 275
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 3
      ExplicitWidth = 277
    end
    object StrasseZusatzEdit: TEdit
      Left = 312
      Top = 174
      Width = 352
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 4
      ExplicitWidth = 353
    end
    object PLZEdit: TEdit
      Left = 8
      Top = 220
      Width = 79
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 12
      TabOrder = 5
      ExplicitWidth = 81
    end
    object OrtEdit: TEdit
      Left = 112
      Top = 220
      Width = 416
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 6
      ExplicitWidth = 417
    end
    object LandComboBox: TComboBox
      Left = 542
      Top = 220
      Width = 122
      Height = 21
      Style = csDropDownList
      Anchors = [akTop, akRight]
      ItemHeight = 13
      MaxLength = 2
      TabOrder = 7
      ExplicitLeft = 543
    end
    object SpedComboBox: TComboBoxPro
      Left = 8
      Top = 35
      Width = 656
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 8
      ExplicitWidth = 657
    end
  end
  object Button1: TButton
    Left = 608
    Top = 632
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Button1'
    TabOrder = 2
  end
  object Button2: TButton
    Left = 520
    Top = 632
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Button2'
    TabOrder = 3
  end
  object KundenQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 112
    Top = 512
  end
  object KundenDataSource: TDataSource
    DataSet = KundenQuery
    Left = 152
    Top = 512
  end
end
