object EditArtikelForm: TEditArtikelForm
  Left = 238
  Top = 153
  Anchors = [akRight, akBottom]
  BorderStyle = bsDialog
  Caption = 'EditArtikelForm'
  ClientHeight = 776
  ClientWidth = 476
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    476
    776)
  TextHeight = 13
  object OkButton: TButton
    Left = 306
    Top = 747
    Width = 75
    Height = 25
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 5
  end
  object AbortButton: TButton
    Left = 395
    Top = 747
    Width = 75
    Height = 25
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 6
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 0
    Width = 476
    Height = 45
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      476
      45)
    object Label21: TLabel
      Left = 7
      Top = 4
      Width = 69
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Untermandant'
    end
    object Bevel1: TBevel
      Left = 7
      Top = 44
      Width = 463
      Height = 7
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object SubMandComboBox: TComboBoxPro
      Left = 7
      Top = 20
      Width = 463
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      Enabled = False
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 188
    Width = 476
    Height = 552
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      476
      552)
    object Label14: TLabel
      Left = 168
      Top = 23
      Width = 5
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = '-'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -9
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label18: TLabel
      Left = 258
      Top = 1
      Width = 46
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Zusatznr.'
    end
    object Label6: TLabel
      Left = 7
      Top = 42
      Width = 64
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Artikelgruppe'
    end
    object Label5: TLabel
      Left = 258
      Top = 42
      Width = 121
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Basis-Verpackungseinheit'
    end
    object Label27: TLabel
      Left = 7
      Top = 1
      Width = 145
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Artikelnummer des Mandanten'
    end
    object TexteGroupBox: TGroupBox
      Left = 6
      Top = 341
      Width = 464
      Height = 210
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akRight, akBottom]
      Caption = 'Artikeltexte'
      TabOrder = 15
      DesignSize = (
        464
        210)
      object Label2: TLabel
        Left = 18
        Top = 21
        Width = 39
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Sprache'
      end
      object SpracheComboBox: TComboBoxPro
        Left = 81
        Top = 18
        Width = 375
        Height = 22
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 0
        OnChange = SpracheComboBoxChange
      end
      object TextPageControl: TPageControl
        Left = 7
        Top = 46
        Width = 449
        Height = 161
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        ActivePage = TextSheet
        Anchors = [akLeft, akTop, akRight, akBottom]
        TabOrder = 1
        object TextSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Artikeltexte'
          DesignSize = (
            441
            133)
          object Label3: TLabel
            Left = 7
            Top = 10
            Width = 41
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Kurztext'
          end
          object Label4: TLabel
            Left = 7
            Top = 33
            Width = 43
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Langtext'
          end
          object Label8: TLabel
            Left = 7
            Top = 88
            Width = 40
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akBottom]
            Caption = 'Infotext'
            ExplicitTop = 89
          end
          object Label9: TLabel
            Left = 7
            Top = 112
            Width = 56
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akBottom]
            Caption = 'Hinweistext'
            ExplicitTop = 113
          end
          object KurzTextEdit: TEdit
            Left = 70
            Top = 7
            Width = 370
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            MaxLength = 128
            TabOrder = 0
            Text = 'KurzTextEdit'
            OnExit = TextExit
          end
          object LangTextMemo: TMemo
            Left = 70
            Top = 33
            Width = 370
            Height = 48
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight, akBottom]
            Lines.Strings = (
              'LangTextMemo')
            MaxLength = 2000
            ScrollBars = ssVertical
            TabOrder = 1
            OnExit = TextExit
          end
          object InfoTextEdit: TEdit
            Left = 70
            Top = 85
            Width = 369
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akBottom]
            MaxLength = 256
            TabOrder = 2
            Text = 'InfoTextEdit'
            OnExit = TextExit
          end
          object HinweisTextEdit: TEdit
            Left = 70
            Top = 109
            Width = 369
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akBottom]
            MaxLength = 256
            TabOrder = 3
            Text = 'HinweisTextEdit'
            OnExit = TextExit
          end
        end
        object HintTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Hinweise'
          ImageIndex = 1
          DesignSize = (
            441
            133)
          object Label38: TLabel
            Left = 7
            Top = 41
            Width = 76
            Height = 24
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            AutoSize = False
            Caption = 'Verpackungs hinweis'
            WordWrap = True
          end
          object Label39: TLabel
            Left = 7
            Top = 71
            Width = 76
            Height = 24
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            AutoSize = False
            Caption = 'Versand hinweis'
            WordWrap = True
          end
          object Label40: TLabel
            Left = 7
            Top = 11
            Width = 76
            Height = 24
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            AutoSize = False
            Caption = 'Kommissionier hinweis'
            WordWrap = True
          end
          object Label45: TLabel
            Left = 8
            Top = 101
            Width = 76
            Height = 24
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            AutoSize = False
            Caption = 'Retouren hinweis'
            WordWrap = True
          end
          object PackTextEdit: TEdit
            Left = 88
            Top = 44
            Width = 350
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            MaxLength = 128
            TabOrder = 1
            Text = 'PackTextEdit'
            OnExit = TextExit
          end
          object VersTextEdit: TEdit
            Left = 88
            Top = 74
            Width = 350
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            MaxLength = 128
            TabOrder = 2
            Text = 'VersTextEdit'
            OnExit = TextExit
          end
          object KommTextEdit: TEdit
            Left = 88
            Top = 14
            Width = 350
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            MaxLength = 128
            TabOrder = 0
            Text = 'KommTextEdit'
            OnExit = TextExit
          end
          object RetTextEdit: TEdit
            Left = 88
            Top = 104
            Width = 350
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            MaxLength = 128
            TabOrder = 3
            Text = 'VersTextEdit'
            OnExit = TextExit
          end
        end
      end
    end
    object OrgaInventCheckBox: TCheckBox
      Left = 237
      Top = 268
      Width = 233
      Height = 14
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Orgainvent-Informationen sind Pflichteingabe '
      Enabled = False
      TabOrder = 11
    end
    object BestandCheckBox: TCheckBox
      Left = 7
      Top = 284
      Width = 219
      Height = 15
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'F'#252'r den Artikel wird ein Bestand gef'#252'hrt'
      TabOrder = 8
      OnClick = BestandCheckBoxClick
    end
    object SerialCheckBox: TCheckBox
      Left = 237
      Top = 284
      Width = 233
      Height = 15
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Seriennummern m'#252'ssen erfasst werden'
      TabOrder = 12
      OnClick = SerialCheckBoxClick
    end
    object MehrMengeCheckBox: TCheckBox
      Left = 7
      Top = 316
      Width = 219
      Height = 15
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Kommissionierung von Mehrmengen zul'#228'ssig'
      TabOrder = 10
    end
    object TextArtikelCheckBox: TCheckBox
      Left = 7
      Top = 268
      Width = 219
      Height = 14
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Nur als Textposition nutzbar'
      TabOrder = 7
      OnClick = TextArtikelCheckBoxClick
    end
    object PageControl1: TPageControl
      Left = 7
      Top = 90
      Width = 463
      Height = 166
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      ActivePage = PackTabSheet
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 6
      object MHDTabSheet: TTabSheet
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Haltbarkeit'
        object Label7: TLabel
          Left = 217
          Top = 7
          Width = 86
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Restlaufzeiten bei'
        end
        object RLZWeLabel: TLabel
          Left = 217
          Top = 32
          Width = 70
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Wareneingang'
        end
        object RLZWaLabel: TLabel
          Left = 217
          Top = 56
          Width = 73
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Warenausgang'
        end
        object Label10: TLabel
          Left = 217
          Top = 80
          Width = 84
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Kommissionierung'
        end
        object Label22: TLabel
          Left = 217
          Top = 104
          Width = 81
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Nach Herstellung'
        end
        object Label23: TLabel
          Left = 378
          Top = 104
          Width = 24
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Tage'
        end
        object Label11: TLabel
          Left = 378
          Top = 80
          Width = 24
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Tage'
        end
        object Label12: TLabel
          Left = 336
          Top = 7
          Width = 20
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Min.'
        end
        object Label13: TLabel
          Left = 383
          Top = 7
          Width = 24
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Max.'
        end
        object inTagenWeLabel: TLabel
          Left = 416
          Top = 56
          Width = 24
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Tage'
        end
        object inTagenWaLabel: TLabel
          Left = 416
          Top = 32
          Width = 24
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Tage'
        end
        object HaltbarkeitGroupBox: TGroupBox
          Left = 8
          Top = 7
          Width = 199
          Height = 126
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Haltbarkeitsdatum'
          TabOrder = 0
          object Label35: TLabel
            Left = 7
            Top = 83
            Width = 62
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'MHD Fenster'
          end
          object Label36: TLabel
            Left = 57
            Top = 99
            Width = 24
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Tage'
          end
          object KeinHaltbarkeitsdatumRadioButton: TRadioButton
            Left = 7
            Top = 18
            Width = 164
            Height = 14
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'kein Haltbarkeitsdatum'
            Enabled = False
            TabOrder = 0
            OnClick = RLZRadioButtonClick
          end
          object MindesthaltbarkeitsRadioButton: TRadioButton
            Left = 7
            Top = 33
            Width = 164
            Height = 15
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Mindesthaltbarkeitsdatum'
            Enabled = False
            TabOrder = 1
            OnClick = RLZRadioButtonClick
          end
          object VerbrauchsdatumRadioButton: TRadioButton
            Left = 7
            Top = 49
            Width = 164
            Height = 14
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Verbrauchsdatum'
            Enabled = False
            TabOrder = 2
            OnClick = RLZRadioButtonClick
          end
          object ProduktionsdatumRadioButton: TRadioButton
            Left = 7
            Top = 64
            Width = 187
            Height = 15
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Errechnet aus Produktionsdatum'
            Enabled = False
            TabOrder = 3
            OnClick = RLZRadioButtonClick
          end
          object MHDRangeEdit: TEdit
            Left = 7
            Top = 97
            Width = 29
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            TabOrder = 4
            Text = '0'
          end
          object MHDRangeUpDown: TIntegerUpDown
            Left = 36
            Top = 97
            Width = 13
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Associate = MHDRangeEdit
            Max = 365
            TabOrder = 5
          end
        end
        object RLZProdEdit: TEdit
          Left = 331
          Top = 101
          Width = 33
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Enabled = False
          MaxLength = 4
          TabOrder = 6
          OnKeyPress = NumEditKeyPress
        end
        object RLZKommEdit: TEdit
          Left = 331
          Top = 77
          Width = 33
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Enabled = False
          MaxLength = 4
          TabOrder = 5
          OnKeyPress = NumEditKeyPress
        end
        object RLZWaEdit: TEdit
          Left = 331
          Top = 53
          Width = 33
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Enabled = False
          MaxLength = 4
          TabOrder = 3
          OnKeyPress = NumEditKeyPress
        end
        object RLZWeEdit: TEdit
          Left = 331
          Top = 29
          Width = 33
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Enabled = False
          MaxLength = 4
          TabOrder = 1
          Text = ' '
          OnKeyPress = NumEditKeyPress
        end
        object RLZWeMaxEdit: TEdit
          Left = 378
          Top = 29
          Width = 33
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Enabled = False
          MaxLength = 4
          TabOrder = 2
          Text = ' '
          OnKeyPress = NumEditKeyPress
        end
        object RLZWaMaxEdit: TEdit
          Left = 378
          Top = 53
          Width = 33
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Enabled = False
          MaxLength = 4
          TabOrder = 4
          Text = ' '
          OnKeyPress = NumEditKeyPress
        end
      end
      object ChargeTabSheet: TTabSheet
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Chargen'
        ImageIndex = 5
        object ChargeRadioGroup: TRadioGroup
          Left = 10
          Top = 10
          Width = 205
          Height = 79
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Chargenverwaltung'
          ItemIndex = 1
          Items.Strings = (
            'Keine'
            'Optional'
            'Pflicht')
          TabOrder = 0
          OnClick = ChargeRadioGroupClick
        end
        object ChargeSumBesCheckBox: TCheckBox
          Left = 10
          Top = 99
          Width = 202
          Height = 14
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Summenbest'#228'nde nach Chargen trennen'
          TabOrder = 1
        end
        object AutoChargeRadioGroup: TRadioGroup
          Left = 231
          Top = 10
          Width = 211
          Height = 122
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Automatische Chargengenerierung im WE'
          ItemIndex = 0
          Items.Strings = (
            'Nein'
            'Pro Bestellposition und Einheit'
            'Pro Bestellposition'
            'Pro WE-Position'
            'Pro WE'
            'Pro Tag')
          TabOrder = 3
        end
        object UniqueSKUChargeCheckBox: TCheckBox
          Left = 10
          Top = 119
          Width = 202
          Height = 14
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Nur eine Charge pro St'#252'ck bzw. Set '
          TabOrder = 2
        end
      end
      object ManfTabSheet: TTabSheet
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Hersteller'
        ImageIndex = 1
        DesignSize = (
          455
          138)
        object Label19: TLabel
          Left = 7
          Top = 8
          Width = 46
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Hersteller'
        end
        object Label17: TLabel
          Left = 305
          Top = 60
          Width = 122
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Artikelnr. des Lieferanten'
        end
        object Label16: TLabel
          Left = 7
          Top = 60
          Width = 118
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Artikelnr. des Herstellers'
        end
        object Label25: TLabel
          Left = 136
          Top = 60
          Width = 29
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Marke'
        end
        object ArtikelManfComboBox: TComboBoxPro
          Left = 7
          Top = 24
          Width = 399
          Height = 22
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Style = csOwnerDrawFixed
          TabOrder = 0
        end
        object ArtikelNrLieferantEdit: TEdit
          Left = 305
          Top = 76
          Width = 144
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          MaxLength = 64
          TabOrder = 4
          Text = 'ArtikelNrLieferantEdit'
        end
        object ArtikelNrHerstellerEdit: TEdit
          Left = 7
          Top = 76
          Width = 113
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          MaxLength = 64
          TabOrder = 2
          Text = 'ArtikelNrHerstellerEdit'
        end
        object EditManfButton: TButton
          Left = 415
          Top = 24
          Width = 37
          Height = 22
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akTop, akRight]
          Caption = '...'
          TabOrder = 1
          OnClick = EditManfButtonClick
        end
        object BrandEdit: TAdvEditBtn
          Left = 136
          Top = 76
          Width = 149
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          EmptyTextStyle = []
          LabelFont.Charset = DEFAULT_CHARSET
          LabelFont.Color = clWindowText
          LabelFont.Height = -10
          LabelFont.Name = 'Tahoma'
          LabelFont.Style = []
          Lookup.Font.Charset = DEFAULT_CHARSET
          Lookup.Font.Color = clWindowText
          Lookup.Font.Height = -11
          Lookup.Font.Name = 'Tahoma'
          Lookup.Font.Style = []
          Lookup.Separator = ';'
          Color = clWindow
          ShortCut = 0
          TabOrder = 3
          Text = 'BrandEdit'
          Visible = True
          Version = '1.7.1.3'
          ButtonStyle = bsButton
          ButtonWidth = 14
          Flat = False
          Etched = False
          ReadOnly = False
        end
      end
      object ZollTabSheet: TTabSheet
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Zoll/Export'
        ImageIndex = 2
        object Label20: TLabel
          Left = 7
          Top = 2
          Width = 69
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Ursprungsland'
        end
        object Label24: TLabel
          Left = 7
          Top = 46
          Width = 80
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Zolltarifnummern'
        end
        object Label30: TLabel
          Left = 7
          Top = 90
          Width = 110
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Grad Plato (Biersteuer)'
        end
        object OriginEdit: TAdvEditBtn
          Left = 7
          Top = 18
          Width = 96
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          EmptyTextStyle = []
          LabelFont.Charset = DEFAULT_CHARSET
          LabelFont.Color = clWindowText
          LabelFont.Height = -10
          LabelFont.Name = 'Tahoma'
          LabelFont.Style = []
          Lookup.Font.Charset = DEFAULT_CHARSET
          Lookup.Font.Color = clWindowText
          Lookup.Font.Height = -11
          Lookup.Font.Name = 'Tahoma'
          Lookup.Font.Style = []
          Lookup.Separator = ';'
          Color = clWindow
          MaxLength = 12
          ShortCut = 0
          TabOrder = 0
          Text = 'OriginEdit'
          Visible = True
          Version = '1.7.1.3'
          ButtonStyle = bsButton
          ButtonWidth = 14
          Flat = False
          Etched = False
          ReadOnly = False
        end
        object TariffEdit: TAdvEditBtn
          Left = 7
          Top = 61
          Width = 95
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          EmptyTextStyle = []
          LabelFont.Charset = DEFAULT_CHARSET
          LabelFont.Color = clWindowText
          LabelFont.Height = -10
          LabelFont.Name = 'Tahoma'
          LabelFont.Style = []
          Lookup.Font.Charset = DEFAULT_CHARSET
          Lookup.Font.Color = clWindowText
          Lookup.Font.Height = -11
          Lookup.Font.Name = 'Tahoma'
          Lookup.Font.Style = []
          Lookup.Separator = ';'
          Color = clWindow
          MaxLength = 16
          ShortCut = 0
          TabOrder = 1
          Text = 'TariffEdit'
          Visible = True
          Version = '1.7.1.3'
          ButtonStyle = bsButton
          ButtonWidth = 14
          Flat = False
          Etched = False
          ReadOnly = False
        end
        object GradPlatoEdit: TAdvEditBtn
          Left = 7
          Top = 106
          Width = 96
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          EmptyTextStyle = []
          LabelFont.Charset = DEFAULT_CHARSET
          LabelFont.Color = clWindowText
          LabelFont.Height = -10
          LabelFont.Name = 'Tahoma'
          LabelFont.Style = []
          Lookup.Font.Charset = DEFAULT_CHARSET
          Lookup.Font.Color = clWindowText
          Lookup.Font.Height = -11
          Lookup.Font.Name = 'Tahoma'
          Lookup.Font.Style = []
          Lookup.Separator = ';'
          Color = clWindow
          MaxLength = 16
          ShortCut = 0
          TabOrder = 2
          Text = 'GradPlatoEdit'
          Visible = True
          Version = '1.7.1.3'
          ButtonStyle = bsButton
          ButtonWidth = 14
          Flat = False
          Etched = False
          ReadOnly = False
        end
      end
      object PackTabSheet: TTabSheet
        Caption = 'Verpacken'
        ImageIndex = 9
        DesignSize = (
          455
          138)
        object PackActionRadioGroup: TRadioGroup
          Left = 8
          Top = 11
          Width = 439
          Height = 46
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Funktion beim Verpacken'
          Columns = 3
          Items.Strings = (
            'Kein'
            'Als Hinweis'
            'Muss erfasst werden')
          TabOrder = 0
        end
      end
      object VersandTabSheet: TTabSheet
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Versand'
        ImageIndex = 7
        DesignSize = (
          455
          138)
        object Label56: TLabel
          Left = 7
          Top = 48
          Width = 44
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Spedition'
        end
        object Label55: TLabel
          Left = 7
          Top = 94
          Width = 53
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Versandart'
        end
        object Label43: TLabel
          Left = 7
          Top = 5
          Width = 115
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Verpackungsreihenfolge'
        end
        object VersandSpedComboBox: TComboBoxPro
          Left = 7
          Top = 64
          Width = 448
          Height = 22
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 1
        end
        object VersandArtComboBox: TComboBoxPro
          Left = 7
          Top = 110
          Width = 448
          Height = 22
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 2
        end
        object PackFolgeEdit: TAdvEditBtn
          Left = 7
          Top = 20
          Width = 57
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          EmptyTextStyle = []
          LabelFont.Charset = DEFAULT_CHARSET
          LabelFont.Color = clWindowText
          LabelFont.Height = -10
          LabelFont.Name = 'Tahoma'
          LabelFont.Style = []
          Lookup.Font.Charset = DEFAULT_CHARSET
          Lookup.Font.Color = clWindowText
          Lookup.Font.Height = -11
          Lookup.Font.Name = 'Tahoma'
          Lookup.Font.Style = []
          Lookup.Separator = ';'
          Color = clWindow
          MaxLength = 4
          ShortCut = 0
          TabOrder = 0
          Text = 'PackFolgeEdit'
          Visible = True
          OnKeyPress = NumEditKeyPress
          Version = '1.7.1.3'
          ButtonStyle = bsButton
          ButtonWidth = 14
          Flat = False
          Etched = False
          ReadOnly = False
        end
      end
      object LagerTabSheet: TTabSheet
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Lagerparameter'
        ImageIndex = 4
        DesignSize = (
          455
          138)
        object Label26: TLabel
          Left = 7
          Top = 49
          Width = 54
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'ABC-Klasse'
        end
        object Label29: TLabel
          Left = 197
          Top = 4
          Width = 64
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Artikel-Klasse'
        end
        object Label34: TLabel
          Left = 101
          Top = 49
          Width = 26
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'LT-ID'
        end
        object Label37: TLabel
          Left = 7
          Top = 4
          Width = 59
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Pick-Prozess'
        end
        object Label44: TLabel
          Left = 197
          Top = 49
          Width = 79
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Pick-Reihenfolge'
        end
        object ABCComboBox: TComboBox
          Left = 7
          Top = 65
          Width = 82
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Style = csDropDownList
          ItemIndex = 0
          TabOrder = 2
          Items.Strings = (
            ''
            'A'
            'B'
            'C')
        end
        object KlasseComboBox: TComboBoxPro
          Left = 197
          Top = 20
          Width = 250
          Height = 22
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Style = csOwnerDrawFixed
          TabOrder = 1
        end
        object BestandIDRadioGroup: TRadioGroup
          Left = 7
          Top = 96
          Width = 442
          Height = 37
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Bestands ID'
          Columns = 4
          Items.Strings = (
            'Keine'
            'Optional'
            'Pflicht'
            'Eindeutig')
          TabOrder = 7
        end
        object LTIDEdit: TEdit
          Left = 101
          Top = 65
          Width = 87
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          MaxLength = 16
          TabOrder = 3
          Text = 'LTIDEdit'
        end
        object PickProcessComboBox: TComboBoxPro
          Left = 7
          Top = 20
          Width = 181
          Height = 22
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Style = csOwnerDrawFixed
          TabOrder = 0
        end
        object KommFolgeEdit: TAdvEditBtn
          Left = 197
          Top = 65
          Width = 64
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          EmptyTextStyle = []
          LabelFont.Charset = DEFAULT_CHARSET
          LabelFont.Color = clWindowText
          LabelFont.Height = -10
          LabelFont.Name = 'Tahoma'
          LabelFont.Style = []
          Lookup.Font.Charset = DEFAULT_CHARSET
          Lookup.Font.Color = clWindowText
          Lookup.Font.Height = -11
          Lookup.Font.Name = 'Tahoma'
          Lookup.Font.Style = []
          Lookup.Separator = ';'
          Color = clWindow
          MaxLength = 4
          ShortCut = 0
          TabOrder = 4
          Text = 'KommFolgeEdit'
          Visible = True
          OnKeyPress = NumEditKeyPress
          Version = '1.7.1.3'
          ButtonStyle = bsButton
          ButtonWidth = 14
          Flat = False
          Etched = False
          ReadOnly = False
        end
        object ERPBestandCheckBox: TCheckBox
          Left = 296
          Top = 55
          Width = 150
          Height = 17
          Caption = 'Bestand an ERP Melden'
          TabOrder = 5
        end
        object FlyerCheckBox: TCheckBox
          Left = 296
          Top = 78
          Width = 150
          Height = 17
          Caption = 'Der Artikel ist ein Flyer'
          TabOrder = 6
        end
      end
      object SortimentTabSheet: TTabSheet
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Sortiment'
        ImageIndex = 5
        DesignSize = (
          455
          138)
        object Label32: TLabel
          Left = 7
          Top = 7
          Width = 52
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Sortimente'
        end
        object SortimentListBox: TCheckListBox
          Left = 7
          Top = 23
          Width = 336
          Height = 87
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 20
          Style = lbOwnerDrawFixed
          TabOrder = 0
          TabWidth = 1
          OnClick = SortimentListBoxClick
          OnDrawItem = IdentListBoxDrawItem
        end
      end
      object IdentTabSheet: TTabSheet
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Kennzeichnungen'
        ImageIndex = 6
        DesignSize = (
          455
          138)
        object Label31: TLabel
          Left = 7
          Top = 7
          Width = 78
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Kennzeichungen'
        end
        object IdentListBox: TCheckListBox
          Left = 7
          Top = 23
          Width = 336
          Height = 87
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 20
          Style = lbOwnerDrawFixed
          TabOrder = 0
          TabWidth = 24
          OnClick = IdentListBoxClick
          OnDrawItem = IdentListBoxDrawItem
        end
      end
      object GefahrgutTabSheet: TTabSheet
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Gefahrgut'
        ImageIndex = 8
        DesignSize = (
          455
          138)
        object Label41: TLabel
          Left = 7
          Top = 47
          Width = 83
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Gefahrgut-Stoffe'
        end
        object Label28: TLabel
          Left = 7
          Top = 7
          Width = 78
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Gefahrgutklasse'
        end
        object GefahrStoffeCheckListBox: TCheckListBox
          Left = 7
          Top = 59
          Width = 336
          Height = 71
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 20
          Style = lbOwnerDrawFixed
          TabOrder = 1
          TabWidth = 100
          OnClick = GefahrStoffeCheckListBoxClick
          OnDrawItem = IdentListBoxDrawItem
        end
        object GefahrgutComboBox: TComboBoxPro
          Left = 7
          Top = 20
          Width = 333
          Height = 22
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Style = csOwnerDrawFixed
          TabOrder = 0
        end
      end
    end
    object ZusatzNrEdit: TEdit
      Left = 258
      Top = 17
      Width = 165
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 1
      Text = 'ZusatzNrEdit'
    end
    object ArtikelGrpComboBox: TComboBoxPro
      Left = 7
      Top = 58
      Width = 191
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      Enabled = False
      TabOrder = 2
    end
    object EditGruppeButton: TButton
      Left = 207
      Top = 58
      Width = 34
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = '...'
      TabOrder = 3
      OnClick = EditGruppeButtonClick
    end
    object BasisVPEComboBox: TComboBoxPro
      Left = 258
      Top = 58
      Width = 165
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      Enabled = False
      TabOrder = 4
    end
    object EditVPEButton: TButton
      Left = 436
      Top = 58
      Width = 34
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = '...'
      Enabled = False
      TabOrder = 5
      OnClick = EditVPEButtonClick
    end
    object MandArNrEdit: TEdit
      Left = 7
      Top = 17
      Width = 190
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      MaxLength = 32
      TabOrder = 0
      Text = 'MandArNrEdit'
    end
    object AutoWABuchenCheckBox: TCheckBox
      Left = 7
      Top = 300
      Width = 219
      Height = 14
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Automatische WA-Bestandsausbuchung'
      TabOrder = 9
    end
    object PrintingCheckBox: TCheckBox
      Left = 237
      Top = 316
      Width = 232
      Height = 15
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Auf Liefereschein und Rechnung ausdrucken'
      Checked = True
      State = cbChecked
      TabOrder = 14
    end
    object BesSerialCheckBox: TCheckBox
      Left = 258
      Top = 300
      Width = 232
      Height = 14
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Seriennummern im Bestand f'#252'hren'
      TabOrder = 13
    end
  end
  object ArtikelNummerPanel: TPanel
    Left = 0
    Top = 90
    Width = 476
    Height = 51
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      476
      51)
    object Label1: TLabel
      Left = 7
      Top = 4
      Width = 138
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Artikelnummer aus der WaWi'
    end
    object Label15: TLabel
      Left = 176
      Top = 4
      Width = 45
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Pr'#252'fziffer'
    end
    object Bevel2: TBevel
      Left = 7
      Top = 47
      Width = 463
      Height = 7
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object ArtikelNrEdit: TEdit
      Left = 7
      Top = 20
      Width = 157
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Enabled = False
      MaxLength = 64
      TabOrder = 0
      Text = 'ArtikelNrEdit'
    end
    object CheckEdit: TEdit
      Left = 176
      Top = 20
      Width = 22
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Enabled = False
      MaxLength = 1
      TabOrder = 1
      Text = 'CheckEdit'
    end
    object CreateArNrButton: TButton
      Left = 259
      Top = 18
      Width = 211
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Neue Artikelnr. erzeugen'
      TabOrder = 2
      OnClick = CreateArNrButtonClick
    end
  end
  object MainArtikelPanel: TPanel
    Left = 0
    Top = 141
    Width = 476
    Height = 47
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      476
      47)
    object Bevel3: TBevel
      Left = 7
      Top = 42
      Width = 463
      Height = 8
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label33: TLabel
      Left = 7
      Top = 1
      Width = 58
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Hauptartikel'
    end
    object MainArtikelComboBox: TComboBoxPro
      Left = 176
      Top = 16
      Width = 294
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 1
      OnDropDown = MainArtikelComboBoxDropDown
    end
    object MainArtikelNrEdit: TEdit
      Left = 7
      Top = 16
      Width = 154
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      MaxLength = 32
      TabOrder = 0
      Text = 'MainArtikelNrEdit'
      OnChange = MainArtikelNrEditChange
    end
  end
  object TraderPanel: TPanel
    Left = 0
    Top = 45
    Width = 476
    Height = 45
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      476
      45)
    object Label42: TLabel
      Left = 7
      Top = 4
      Width = 47
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Verk'#228'ufer'
    end
    object Bevel4: TBevel
      Left = 7
      Top = 44
      Width = 463
      Height = 7
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object TraderComboBox: TComboBoxPro
      Left = 7
      Top = 20
      Width = 463
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      Enabled = False
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 296
    Top = 661
  end
end
