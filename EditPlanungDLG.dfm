object EditPlanungForm: TEditPlanungForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'EditPlanungForm'
  ClientHeight = 421
  ClientWidth = 336
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  DesignSize = (
    336
    421)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 148
    Width = 27
    Height = 13
    Caption = 'Name'
  end
  object Label2: TLabel
    Left = 8
    Top = 194
    Width = 64
    Height = 13
    Caption = 'Beschreibung'
  end
  object Label3: TLabel
    Left = 8
    Top = 248
    Width = 57
    Height = 13
    Caption = 'Planungsart'
  end
  object Label4: TLabel
    Left = 8
    Top = 334
    Width = 42
    Height = 13
    Caption = 'G'#252'ltig ab'
  end
  object Label5: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Label6: TLabel
    Left = 8
    Top = 56
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 141
    Width = 320
    Height = 3
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel2: TBevel
    Left = 8
    Top = 379
    Width = 320
    Height = 3
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 283
  end
  object Bevel3: TBevel
    Left = 8
    Top = 239
    Width = 320
    Height = 3
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object Label7: TLabel
    Left = 8
    Top = 288
    Width = 89
    Height = 13
    Caption = 'Planungsvorgehen'
  end
  object Bevel4: TBevel
    Left = 8
    Top = 51
    Width = 320
    Height = 3
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label8: TLabel
    Left = 8
    Top = 96
    Width = 62
    Height = 13
    Caption = 'Lagerbereich'
  end
  object OkButton: TButton
    Left = 172
    Top = 387
    Width = 75
    Height = 26
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 7
  end
  object AbortButton: TButton
    Left = 253
    Top = 388
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 8
  end
  object ArtComboBox: TComboBoxPro
    Left = 8
    Top = 264
    Width = 320
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 200
    ItemHeight = 15
    TabOrder = 4
  end
  object NameEdit: TEdit
    Left = 8
    Top = 164
    Width = 320
    Height = 21
    TabOrder = 2
    Text = 'NameEdit'
  end
  object DescEdit: TEdit
    Left = 8
    Top = 210
    Width = 320
    Height = 21
    TabOrder = 3
    Text = 'DescEdit'
  end
  object ValideDateTimePicker: TDateTimePicker
    Left = 8
    Top = 350
    Width = 129
    Height = 21
    Date = 39171.559489189820000000
    Time = 39171.559489189820000000
    ShowCheckbox = True
    Checked = False
    TabOrder = 6
  end
  object MandantComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 320
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 80
    Enabled = False
    ItemHeight = 15
    TabOrder = 0
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 72
    Width = 320
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 80
    ItemHeight = 15
    TabOrder = 1
    OnChange = LagerComboBoxChange
  end
  object VorgangComboBox: TComboBoxPro
    Left = 8
    Top = 304
    Width = 320
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 200
    ItemHeight = 15
    TabOrder = 5
  end
  object BereichComboBox: TComboBoxPro
    Left = 8
    Top = 112
    Width = 320
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 120
    ItemHeight = 15
    TabOrder = 9
  end
end
