object EditHACCPTestForm: TEditHACCPTestForm
  Left = 394
  Top = 125
  Caption = 'EditHACCPTestForm'
  ClientHeight = 659
  ClientWidth = 391
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    391
    659)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 64
    Width = 36
    Height = 13
    Caption = 'Bereich'
  end
  object Label2: TLabel
    Left = 8
    Top = 110
    Width = 40
    Height = 13
    Caption = 'Vorgang'
  end
  object Label3: TLabel
    Left = 8
    Top = 336
    Width = 21
    Height = 13
    Caption = 'Text'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 157
    Width = 382
    Height = 9
    Shape = bsTopLine
  end
  object Bevel3: TBevel
    Left = 5
    Top = 384
    Width = 382
    Height = 9
    Shape = bsTopLine
  end
  object Label4: TLabel
    Left = 8
    Top = 216
    Width = 53
    Height = 13
    Caption = 'Ergebnisart'
  end
  object Label6: TLabel
    Left = 8
    Top = 416
    Width = 100
    Height = 13
    Caption = 'Vorbelegtes Ergebnis'
  end
  object Bevel2: TBevel
    Left = 5
    Top = 621
    Width = 382
    Height = 4
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 460
  end
  object Label7: TLabel
    Left = 8
    Top = 576
    Width = 57
    Height = 13
    Caption = 'Reihenfolge'
  end
  object Bevel4: TBevel
    Left = 9
    Top = 56
    Width = 382
    Height = 5
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label8: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Bevel5: TBevel
    Left = 4
    Top = 56
    Width = 382
    Height = 9
    Shape = bsTopLine
  end
  object Label11: TLabel
    Left = 8
    Top = 164
    Width = 62
    Height = 13
    Caption = 'Bezeichnung'
  end
  object Label12: TLabel
    Left = 296
    Top = 164
    Width = 11
    Height = 13
    Caption = 'ID'
  end
  object BereichComboBox: TComboBoxPro
    Left = 8
    Top = 80
    Width = 377
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 120
    AutoPrepare = False
    ItemHeight = 15
    TabOrder = 1
  end
  object VorgangComboBox: TComboBoxPro
    Left = 8
    Top = 129
    Width = 377
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 120
    AutoPrepare = False
    ItemHeight = 15
    TabOrder = 2
  end
  object TextEdit: TEdit
    Left = 8
    Top = 355
    Width = 377
    Height = 21
    MaxLength = 64
    TabOrder = 6
    Text = 'TextEdit'
  end
  object OkButton: TButton
    Left = 225
    Top = 629
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 13
  end
  object AbortButton: TButton
    Left = 310
    Top = 629
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 14
  end
  object ArtGroupBox: TGroupBox
    Left = 8
    Top = 264
    Width = 377
    Height = 65
    Caption = 'Auswahl'
    TabOrder = 5
    object Label5: TLabel
      Left = 8
      Top = 16
      Width = 21
      Height = 13
      Caption = 'Text'
    end
    object SelTextComboBox: TComboBox
      Left = 8
      Top = 32
      Width = 361
      Height = 21
      ItemHeight = 13
      TabOrder = 0
      Text = 'SelTextComboBox'
      OnChange = SelTextComboBoxChange
    end
  end
  object OptResCheckBox: TCheckBox
    Left = 8
    Top = 392
    Width = 121
    Height = 17
    Caption = 'Eingabe optional'
    TabOrder = 7
  end
  object DefResEdit: TEdit
    Left = 8
    Top = 432
    Width = 377
    Height = 21
    TabOrder = 8
    Text = 'DefResEdit'
  end
  object ArtComboBox: TComboBoxPro
    Left = 8
    Top = 232
    Width = 377
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 120
    AutoPrepare = False
    ItemHeight = 16
    TabOrder = 4
    OnChange = ArtComboBoxChange
  end
  object DefSelComboBox: TComboBoxPro
    Left = 8
    Top = 432
    Width = 377
    Height = 21
    ColWidth = 120
    AutoPrepare = False
    ItemHeight = 13
    TabOrder = 9
    Text = 'DefSelComboBox'
    OnChange = DefSelComboBoxChange
  end
  object ReihenfolgeEdit: TEdit
    Left = 8
    Top = 592
    Width = 49
    Height = 21
    TabOrder = 11
    Text = '0'
  end
  object ReihenfolgeUpDown: TUpDown
    Left = 57
    Top = 592
    Width = 15
    Height = 21
    Associate = ReihenfolgeEdit
    TabOrder = 12
  end
  object MandantComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 377
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 120
    AutoPrepare = False
    ItemHeight = 16
    TabOrder = 0
  end
  object GroupBox1: TGroupBox
    Left = 8
    Top = 459
    Width = 375
    Height = 111
    Caption = 'Auswertung'
    TabOrder = 10
    object Label9: TLabel
      Left = 8
      Top = 64
      Width = 189
      Height = 13
      Caption = 'Auswahltext f'#252'r die negative Beurteilung'
    end
    object Label10: TLabel
      Left = 298
      Top = 64
      Width = 51
      Height = 13
      Caption = 'Bewertung'
    end
    object AnalyseCheckBox: TCheckBox
      Left = 8
      Top = 14
      Width = 353
      Height = 17
      Caption = 'Ergebnisse soll ausgewertet werden'
      TabOrder = 0
    end
    object BadSelComboBox: TComboBoxPro
      Left = 8
      Top = 82
      Width = 284
      Height = 21
      ItemHeight = 13
      TabOrder = 3
      Text = 'BadSelComboBox'
      OnChange = SelTextComboBoxChange
    end
    object AnalyseNegRadioButton: TRadioButton
      Left = 8
      Top = 41
      Width = 203
      Height = 17
      Caption = 'Nur negative Ergebnisse auswerten'
      TabOrder = 1
    end
    object AnalyseAllRadioButton: TRadioButton
      Left = 217
      Top = 41
      Width = 144
      Height = 17
      Caption = 'Alle Ergebnisse auswerten'
      TabOrder = 2
    end
    object BadPointsEdit: TEdit
      Left = 298
      Top = 82
      Width = 68
      Height = 21
      TabOrder = 4
      Text = 'BadPointsEdit'
      OnKeyPress = BadPointsEditKeyPress
    end
  end
  object DescEdit: TEdit
    Left = 8
    Top = 183
    Width = 265
    Height = 21
    MaxLength = 64
    TabOrder = 3
  end
  object IDEdit: TEdit
    Left = 296
    Top = 183
    Width = 87
    Height = 21
    MaxLength = 32
    TabOrder = 15
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 224
    Top = 8
  end
end
