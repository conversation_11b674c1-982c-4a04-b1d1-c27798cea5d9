unit EditKommPlanGroupDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComboBoxPro;

type
  TEditKommPlanGroupForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    Label8: TLabel;
    LagerComboBox: TComboBoxPro;
    Bevel5: TBevel;
    NameEdit: TEdit;
    Label1: TLabel;
    Bevel1: TBevel;
    Label2: TLabel;
    DescEdit: TEdit;
    FolgeEdit: TEdit;
    Label3: TLabel;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
    fRef : Integer;
  public
    property Ref : integer read fRef;
    
    function Prepare (const RefKommPlanGrp : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses
  DB, ADODB, VCLUtilitys, StringUtils, DatenModul, FrontendUtils, DBGridUtilModule, ConfigModul, SprachModul,
  LVSDatenInterface;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditKommPlanGroupForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  ref,
  idx     : Integer;
  folgenr : Integer;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    CanClose := False;

    folgenr := -1;

    if (Length (NameEdit.Text) = 0) then begin
      NameEdit.SetFocus;
    end else if (Length (FolgeEdit.Text) > 0) and not TryStrToInt (FolgeEdit.Text, folgenr) then begin
      FolgeEdit.SetFocus;
    end else begin
      if (fRef = -1) then begin
        res := CreateKommPlanGroup (GetComboBoxRef(LagerComboBox), NameEdit.Text, DescEdit.Text, folgenr, ref);

        if (res = 0) then
          fRef := ref;
      end else begin
        res := ChangeKommPlanGroup (fRef, NameEdit.Text, DescEdit.Text, folgenr);
      end;

      CanClose := (res = 0);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditKommPlanGroupForm.FormCreate(Sender: TObject);
begin
  NameEdit.Text := '';
  DescEdit.Text := '';
  FolgeEdit.Text := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, NameEdit);
    LVSSprachModul.SetNoTranslate (Self, DescEdit);
    LVSSprachModul.SetNoTranslate (Self, FolgeEdit);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditKommPlanGroupForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (LagerComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TEditKommPlanGroupForm.Prepare (const RefKommPlanGrp : Integer) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  res := 0;

  fRef := RefKommPlanGrp;

  LoadLagerCombobox(LagerComboBox, LVSDatenModul.AktLocationRef);

  if (RefKommPlanGrp = -1) then begin
    NameEdit.Text := '';
    DescEdit.Text := '';
    FolgeEdit.Text := '';

    if (LagerComboBox.Items.Count = 1) then begin
      LagerComboBox.Enabled := False;
      LagerComboBox.ItemIndex := 0;
    end else if (LVSDatenModul.AktLagerRef = -1) then
      LagerComboBox.ItemIndex := 0
    else begin
      LagerComboBox.ItemIndex := FindComboboxRef(LagerComboBox, LVSDatenModul.AktLagerRef);
      if (LagerComboBox.ItemIndex = -1) then LagerComboBox.ItemIndex := 0;
    end;
  end else begin
    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Clear;
      query.SQL.Add ('select * from V_KOMM_PLAN_GROUP where REF=:ref');
      query.Parameters [0].Value := RefKommPlanGrp;

      query.Open;

      LagerComboBox.Enabled := False;
      LagerComboBox.ItemIndex := FindComboboxRef(LagerComboBox, query.FieldByName('REF_LAGER').AsInteger);
      if (LagerComboBox.ItemIndex = -1) then LagerComboBox.ItemIndex := 0;

      NameEdit.Text := query.FieldByName('NAME').AsString;
      DescEdit.Text := query.FieldByName('DESCRIPTION').AsString;

      if (query.FieldByName('KOMM_FOLGE').IsNull) then
        FolgeEdit.Text := ''
      else FolgeEdit.Text := query.FieldByName('KOMM_FOLGE').AsString;

      query.Close;
    finally
      query.Free;
    end;
  end;

  Result := res;
end;

end.
