unit AuftragInfoDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics,
  Controls, Forms, Dialogs, StdCtrls, ComboBoxPro;

type
  TAuftragInfoForm = class(TForm)
    Button1: TButton;
    TextGroupBox: TGroupBox;
    RechnungGroupBox: TGroupBox;
    VersandGroupBox: TGroupBox;
    KommEdit: TEdit;
    VersandEdit: TEdit;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    PackEdit: TMemo;
    BillNoEdit: TEdit;
    BillNetEdit: TEdit;
    BillGrosEdit: TEdit;
    Label4: TLabel;
    Label5: TLabel;
    Label6: TLabel;
    BillNetCalcEdit: TEdit;
    BillGrosCalcEdit: TEdit;
    Label9: TLabel;
    Label7: TLabel;
    Label8: TLabel;
    Label10: TLabel;
    Label11: TLabel;
    Label12: TLabel;
    VersTallLiftCheckBox: TCheckBox;
    DSADVCheckBox: TCheckBox;
    PrintLSCheckBox: TCheckBox;
    BillBarCheckBox: TCheckBox;
    BillCODCheckBox: TCheckBox;
    AvisToEdit: TEdit;
    AvisInfoEdit: TEdit;
    Label13: TLabel;
    Label14: TLabel;
    PrintRetCheckBox: TCheckBox;
    PrintRetLabelCheckBox: TCheckBox;
    VersGLNEdit: TEdit;
    PrintNVEInhaltCheckBox: TCheckBox;
    Label15: TLabel;
    Label16: TLabel;
    VersLTComboBox: TComboBoxPro;
    VersPackComboBox: TComboBoxPro;
    KopfGroupBox: TGroupBox;
    Label17: TLabel;
    AufNrLabel: TLabel;
    Label18: TLabel;
    EmpfLabel: TLabel;
    Label19: TLabel;
    SalesLabel: TLabel;
    Label21: TLabel;
    OfficeLabel: TLabel;
    Label23: TLabel;
    KdAufNrLabel: TLabel;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    function Prepare (const RefAuf : Integer) : Integer;
  end;

implementation

uses
  VCLUtilitys, Ora, OraSmart, DatenModul, FrontendUtils;

{$R *.dfm}

procedure TAuftragInfoForm.FormCreate(Sender: TObject);
begin
  KommEdit.Text := '';
  PackEdit.Text := '';
  VersandEdit.Text := '';
  BillNoEdit.Text := '';
  BillNetEdit.Text := '';
  BillGrosEdit.Text := '';
  BillNetCalcEdit.Text := '';
  BillGrosCalcEdit.Text := '';
  AvisToEdit.Text := '';
  AvisInfoEdit.Text := '';
end;

procedure TAuftragInfoForm.FormShow(Sender: TObject);
begin
  Label13.Visible := AvisToEdit.Visible;
  Label14.Visible := AvisInfoEdit.Visible;
  Label16.Visible := VersPackComboBox.Visible;
end;

function TAuftragInfoForm.Prepare (const RefAuf : Integer) : Integer;
var
  query : TSmartQuery;
  empfstr : String;
begin
  query := TSmartQuery.Create (Self);

  try
    query.ReadOnly := True;
    query.Session  := LVSDatenModul.OraMainSession;

    query.SQL.Add ('select * from V_AUFTRAG_TEXTE where REF_AUF_KOPF=:ref');
    query.Params.ParamByName('ref').Value := RefAuf;

    query.Open;

    KommEdit.Text    := query.FieldByName ('KOMM_TEXT').AsString;
    PackEdit.Text    := query.FieldByName ('PACK_TEXT').AsString;
    VersandEdit.Text := query.FieldByName ('VERSAND_HINWEIS').AsString;

    query.Close;

    query.SQL.Clear;
    query.SQL.Add ('select * from V_AUFTRAG_RECHNUNG where REF_AUF_KOPF=:ref');
    query.Params.ParamByName('ref').Value := RefAuf;

    query.Open;

    BillNoEdit.Text    := query.FieldByName ('RECHNUNGS_NR').AsString;

    if not (query.FieldByName ('NETTO_BETRAG').IsNull) then
     BillNetEdit.Text   := Format ('%7.2f', [query.FieldByName ('NETTO_BETRAG').AsFloat / 1000.0]);

    if not (query.FieldByName ('BRUTTO_BETRAG').IsNull) then
      BillGrosEdit.Text := Format ('%7.2f', [query.FieldByName ('BRUTTO_BETRAG').AsFloat / 1000.0]);

    if not (query.FieldByName ('NETTO_BETRAG_CALC').IsNull) then
      BillNetCalcEdit.Text   := Format ('%7.2f', [query.FieldByName ('NETTO_BETRAG_CALC').AsFloat / 1000.0]);

    if not (query.FieldByName ('BRUTTO_BETRAG_CALC').IsNull) then
      BillGrosCalcEdit.Text := Format ('%7.2f', [query.FieldByName ('BRUTTO_BETRAG_CALC').AsFloat / 1000.0]);

    query.Close;

    query.SQL.Clear;
    query.SQL.Add ('select av.*, a.REF_MAND, a.AUFTRAG_NR, a.REF_SUB_MAND, a.REF_LAGER, a.REF_LT, adr.ILN'
                  +' ,ai.KD_AUFTRAG_NR, ai.FIELD_SALES_STUFF, ai.OFFICE_SALES_STUFF'
                  +' ,adr.AVIS_INFOS, adr.COMPANY, adr.NAME1, adr.STRASSE, adr.PLZ,adr.ORT, adr.LAND_ISO'
                  +' from V_AUFTRAG_01 a, V_AUFTRAG_VERSAND av, V_AUFTRAG_ADR adr, V_AUFTRAG_ADD_INFOS ai'
                  +' where av.REF_AUF_KOPF=a.REF and ai.REF_AUF_KOPF=a.REF and adr.REF=a.REF_LIEFER_ADR and a.REF=:ref');
    query.Params.ParamByName('ref').Value := RefAuf;

    query.Open;

    AufNrLabel.Caption := query.FieldByName ('AUFTRAG_NR').AsString;

    if not (query.FieldByName ('COMPANY').IsNull) then
      empfstr := query.FieldByName ('COMPANY').AsString
    else
      empfstr := query.FieldByName ('NAME1').AsString;

    empfstr := empfstr + '/' + query.FieldByName ('STRASSE').AsString;
    empfstr := empfstr + '/' + query.FieldByName ('PLZ').AsString;
    empfstr := empfstr + '/' + query.FieldByName ('ORT').AsString;
    empfstr := empfstr + '/' + query.FieldByName ('LAND_ISO').AsString;

    EmpfLabel.Caption := empfstr;

    LoadLTCombobox (VersLTComboBox, 'NVE', LVSDatenModul.AktLocationRef, query.FieldByName ('REF_LAGER').AsInteger);
    LoadLTCombobox (VersPackComboBox, 'PACK', LVSDatenModul.AktLocationRef, query.FieldByName ('REF_LAGER').AsInteger);

    if (query.FieldByName ('REF_LT').IsNull) then
      VersLTComboBox.ItemIndex := 0
    else begin
      VersLTComboBox.ItemIndex := FindComboboxRef (VersLTComboBox, query.FieldByName ('REF_LT').AsInteger);
      if (VersLTComboBox.ItemIndex = -1) then VersLTComboBox.ItemIndex := 0;
    end;

    if not Assigned (query.FindField ('REF_PACKMITTEL')) then
      VersPackComboBox.Visible := false
    else if (query.FieldByName ('REF_PACKMITTEL').IsNull) then
      VersPackComboBox.ItemIndex := 0
    else begin
      VersPackComboBox.ItemIndex := FindComboboxRef (VersPackComboBox, query.FieldByName ('REF_PACKMITTEL').AsInteger);
      if (VersPackComboBox.ItemIndex = -1) then VersPackComboBox.ItemIndex := 0;
    end;

    KdAufNrLabel.Caption := query.FieldByName ('KD_AUFTRAG_NR').AsString;
    SalesLabel.Caption := query.FieldByName ('FIELD_SALES_STUFF').AsString;
    OfficeLabel.Caption := query.FieldByName ('OFFICE_SALES_STUFF').AsString;

    VersGLNEdit.Text := query.FieldByName ('ILN').AsString;

    BillBarCheckBox.Checked := (query.FieldByName ('OPT_BARZAHLER').AsString > '0');
    BillCODCheckBox.Checked := (query.FieldByName ('OPT_NACHNAHME').AsString > '0');

    if Assigned (query.FindField ('OPT_TALL_LIFT')) then
      VersTallLiftCheckBox.Checked := (query.FieldByName ('OPT_TALL_LIFT').AsString > '0')
    else
      VersTallLiftCheckBox.Visible := false;

    if not Assigned (query.FindField ('OPT_DESADV')) then
      DSADVCheckBox.Visible := false
    else if (query.FieldByName ('OPT_DESADV').AsString > '0') then begin
      DSADVCheckBox.Checked := true;
      DSADVCheckBox.Caption := query.FieldByName ('OPT_DESADV').AsString;
    end;

    if Assigned (query.FindField ('OPT_PRINT_LS')) then
      PrintLSCheckBox.Checked := (query.FieldByName ('OPT_PRINT_LS').AsString > '0')
    else
      PrintLSCheckBox.Visible := false;

    if Assigned (query.FindField ('OPT_RETOUREN_SCHEIN')) then
      PrintRetCheckBox.Checked := (query.FieldByName ('OPT_RETOUREN_SCHEIN').AsString > '0')
    else
      PrintRetCheckBox.Visible := false;

    if Assigned (query.FindField ('OPT_RETOUREN_LABEL')) then
      PrintRetLabelCheckBox.Checked := (query.FieldByName ('OPT_RETOUREN_LABEL').AsString > '0')
    else
      PrintRetLabelCheckBox.Visible := false;

    if Assigned (query.FindField ('OPT_PRINT_NVE_INHALT')) then
      PrintRetLabelCheckBox.Checked := (query.FieldByName ('OPT_PRINT_NVE_INHALT').AsString > '0')
    else
      PrintRetLabelCheckBox.Visible := false;

    if not Assigned (query.FindField ('DELIVERY_NOTIFICATION')) then
      AvisToEdit.Visible := false
    else
      AvisToEdit.Text    := query.FieldByName ('DELIVERY_NOTIFICATION').AsString;

    if Assigned (query.FindField ('AVIS_HINWEIS')) then
      AvisInfoEdit.Text    := query.FieldByName ('AVIS_HINWEIS').AsString
    else
      AvisInfoEdit.Text    := query.FieldByName ('AVIS_INFOS').AsString;

    query.Close;
  finally
    query.Free;
  end;

  Result := 0;
end;

end.
