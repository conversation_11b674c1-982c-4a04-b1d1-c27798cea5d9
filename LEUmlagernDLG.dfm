object UmlagerForm: TUmlagerForm
  Left = 353
  Top = 305
  BorderStyle = bsDialog
  Caption = 'LE umlagern'
  ClientHeight = 650
  ClientWidth = 416
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnPaint = FormPaint
  OnShow = FormShow
  DesignSize = (
    416
    650)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 16
    Width = 68
    Height = 13
    Caption = 'Ladungstr'#228'ger'
  end
  object Label5: TLabel
    Left = 184
    Top = 16
    Width = 89
    Height = 13
    Caption = 'Ladungstr'#228'ger-Typ'
  end
  object Label6: TLabel
    Left = 8
    Top = 56
    Width = 62
    Height = 13
    Caption = 'Lagerbereich'
  end
  object Label7: TLabel
    Left = 184
    Top = 56
    Width = 49
    Height = 13
    Caption = 'Lagerplatz'
  end
  object Bevel1: TBevel
    Left = 6
    Top = 96
    Width = 404
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 395
  end
  object Label2: TLabel
    Left = 8
    Top = 104
    Width = 26
    Height = 13
    Caption = 'Inhalt'
  end
  object OkButton: TButton
    Left = 247
    Top = 617
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Umlagern'
    Default = True
    ModalResult = 1
    TabOrder = 0
  end
  object Button1: TButton
    Left = 332
    Top = 617
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 1
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 199
    Width = 400
    Height = 412
    ActivePage = LocalTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 2
    object LocalTabSheet: TTabSheet
      Caption = 'Innerhalb von $0'
      DesignSize = (
        392
        384)
      object Label3: TLabel
        Left = 8
        Top = 8
        Width = 62
        Height = 13
        Caption = 'Lagerbereich'
      end
      object Label4: TLabel
        Left = 8
        Top = 105
        Width = 49
        Height = 13
        Caption = 'Lagerplatz'
      end
      object Label13: TLabel
        Left = 8
        Top = 53
        Width = 50
        Height = 13
        Caption = 'Lagerzone'
      end
      object LBComboBox: TComboBoxPro
        Left = 8
        Top = 24
        Width = 375
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ColWidth = 100
        ItemHeight = 15
        TabOrder = 0
        OnChange = LBComboBoxChange
      end
      object LPListBox: TListBox
        Left = 8
        Top = 121
        Width = 375
        Height = 260
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 13
        PopupMenu = LPListBoxPopupMenu
        TabOrder = 2
        OnDrawItem = ListBoxDrawItem
      end
      object LBZoneComboBox: TComboBoxPro
        Left = 8
        Top = 69
        Width = 375
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ColWidth = 100
        ItemHeight = 15
        TabOrder = 1
        OnChange = LBZoneComboBoxChange
      end
    end
    object ExternTabSheet: TTabSheet
      Caption = 'Anderes Lager'
      ImageIndex = 1
      OnShow = ExternTabSheetShow
      object Label8: TLabel
        Left = 8
        Top = 8
        Width = 67
        Height = 13
        Caption = 'Niederlassung'
      end
      object Label9: TLabel
        Left = 8
        Top = 56
        Width = 27
        Height = 13
        Caption = 'Lager'
      end
      object LocComboBox: TComboBoxPro
        Left = 8
        Top = 24
        Width = 378
        Height = 22
        Style = csOwnerDrawFixed
        ColWidth = 120
        ItemHeight = 16
        TabOrder = 0
        OnChange = LocComboBoxChange
      end
      object LagerComboBox: TComboBoxPro
        Left = 8
        Top = 72
        Width = 378
        Height = 21
        Style = csOwnerDrawFixed
        ColWidth = 120
        ItemHeight = 15
        TabOrder = 1
        OnChange = LagerComboBoxChange
      end
      object InhousGroupBox: TGroupBox
        Left = 8
        Top = 104
        Width = 378
        Height = 277
        Caption = 'Direkte Einlagerung'
        TabOrder = 2
        DesignSize = (
          378
          277)
        object Label10: TLabel
          Left = 8
          Top = 24
          Width = 62
          Height = 13
          Caption = 'Lagerbereich'
        end
        object Label11: TLabel
          Left = 8
          Top = 118
          Width = 49
          Height = 13
          Caption = 'Lagerplatz'
        end
        object Label12: TLabel
          Left = 8
          Top = 64
          Width = 50
          Height = 13
          Caption = 'Lagerzone'
        end
        object InHousLBComboBox: TComboBoxPro
          Left = 8
          Top = 40
          Width = 363
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 100
          ItemHeight = 15
          TabOrder = 0
          OnChange = InHousLBComboBoxChange
        end
        object InhousLPListBox: TListBox
          Left = 7
          Top = 136
          Width = 363
          Height = 127
          Anchors = [akLeft, akTop, akRight, akBottom]
          ItemHeight = 13
          PopupMenu = InhousLPListBoxPopupMenu
          TabOrder = 2
          OnDrawItem = ListBoxDrawItem
        end
        object InHousLBZoneComboBox: TComboBoxPro
          Left = 7
          Top = 80
          Width = 363
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 100
          ItemHeight = 15
          TabOrder = 1
          OnChange = InHousLBZoneComboBoxChange
        end
      end
    end
    object AuslagernTabSheet: TTabSheet
      Caption = 'Auslagern'
      ImageIndex = 2
      OnShow = AuslagernTabSheetShow
      DesignSize = (
        392
        384)
      object Label14: TLabel
        Left = 8
        Top = 12
        Width = 17
        Height = 13
        Caption = 'Ziel'
      end
      object Label15: TLabel
        Left = 8
        Top = 328
        Width = 29
        Height = 13
        Anchors = [akLeft, akBottom]
        Caption = 'Grund'
      end
      object Label16: TLabel
        Left = 9
        Top = 90
        Width = 82
        Height = 13
        Caption = 'Warenempf'#228'nger'
      end
      object Label17: TLabel
        Left = 8
        Top = 274
        Width = 44
        Height = 13
        Anchors = [akLeft, akBottom]
        Caption = 'Spedition'
      end
      object GrundEdit: TEdit
        Left = 8
        Top = 344
        Width = 378
        Height = 21
        Anchors = [akLeft, akBottom]
        MaxLength = 128
        TabOrder = 4
        Text = 'GrundEdit'
      end
      object AuslagerRelComboBox: TComboBoxPro
        Left = 8
        Top = 28
        Width = 378
        Height = 19
        Style = csOwnerDrawFixed
        ItemHeight = 13
        TabOrder = 0
      end
      object KundenDBGrid: TDBGridPro
        Left = 8
        Top = 106
        Width = 378
        Height = 156
        Anchors = [akLeft, akTop, akRight, akBottom]
        BiDiMode = bdLeftToRight
        DataSource = KundenDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ParentBiDiMode = False
        ReadOnly = True
        TabOrder = 2
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'MS Sans Serif'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object KundenCheckBox: TCheckBox
        Left = 8
        Top = 63
        Width = 378
        Height = 17
        Caption = 'Warenempf'#228'nger ausw'#228'hlen'
        TabOrder = 1
        OnClick = KundenCheckBoxClick
      end
      object SpedComboBox: TComboBoxPro
        Left = 8
        Top = 290
        Width = 378
        Height = 22
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akRight, akBottom]
        ItemHeight = 16
        TabOrder = 3
      end
    end
  end
  object StaticText1: TStaticText
    Left = 8
    Top = 32
    Width = 153
    Height = 17
    AutoSize = False
    BevelInner = bvLowered
    BevelOuter = bvRaised
    BorderStyle = sbsSunken
    Caption = 'StaticText1'
    TabOrder = 3
  end
  object StaticText2: TStaticText
    Left = 184
    Top = 32
    Width = 224
    Height = 17
    AutoSize = False
    BevelInner = bvLowered
    BevelOuter = bvRaised
    BorderStyle = sbsSunken
    Caption = 'StaticText2'
    TabOrder = 4
  end
  object StaticText3: TStaticText
    Left = 8
    Top = 72
    Width = 153
    Height = 17
    AutoSize = False
    BevelInner = bvLowered
    BevelOuter = bvRaised
    BorderStyle = sbsSunken
    Caption = 'StaticText3'
    TabOrder = 5
  end
  object StaticText4: TStaticText
    Left = 184
    Top = 72
    Width = 224
    Height = 17
    AutoSize = False
    BevelInner = bvLowered
    BevelOuter = bvRaised
    BorderStyle = sbsSunken
    Caption = 'StaticText4'
    TabOrder = 6
  end
  object ArtikelListBox: TListBox
    Left = 8
    Top = 123
    Width = 400
    Height = 66
    TabStop = False
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 13
    TabOrder = 7
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 376
    Top = 65424
  end
  object LPListBoxPopupMenu: TPopupMenu
    Left = 40
    Top = 376
    object LagerSuchenLPMenuItem: TMenuItem
      Caption = 'Suchen...'
      ShortCut = 16454
      OnClick = InhousLPSuchMenuItemClick
    end
  end
  object InhousLPListBoxPopupMenu: TPopupMenu
    Left = 344
    Top = 544
    object InhousLPSuchMenuItem: TMenuItem
      Caption = 'Suchen...'
      ShortCut = 16454
      OnClick = InhousLPSuchMenuItemClick
    end
  end
  object KundenQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 112
    Top = 512
  end
  object KundenDataSource: TDataSource
    DataSet = KundenQuery
    Left = 152
    Top = 512
  end
end
