unit ArtikelInfoDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TArtikelInfoForm = class(TForm)
    Label1: TLabel;
    EANLabel: TLabel;
    Label3: TLabel;
    ArNrLabel: TLabel;
    Label5: TLabel;
    ArTextLabel: TLabel;
    CloseButton: TButton;
    Bevel1: TBevel;
    Bevel2: TBevel;
    ArColliLabel: TLabel;
    procedure FormCreate(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

procedure TArtikelInfoForm.FormCreate(Sender: TObject);
begin
  EANLabel.Caption := '';
  ArTextLabel.Caption := '';
  ArColliLabel.Caption := '';
end;

end.
