object SelectArtikelForm: TSelectArtikelForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Artikel ausw'#228'hlen'
  ClientHeight = 227
  ClientWidth = 367
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    367
    227)
  PixelsPerInch = 96
  TextHeight = 13
  object FehlerLabel: TLabel
    Left = 0
    Top = 169
    Width = 367
    Height = 18
    Align = alTop
    Alignment = taCenter
    AutoSize = False
    Caption = 'FehlerLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -13
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
    ExplicitTop = 171
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 367
    Height = 169
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      367
      169)
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object Label5: TLabel
      Left = 8
      Top = 50
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 92
      Width = 351
      Height = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 249
    end
    object Label3: TLabel
      Left = 8
      Top = 118
      Width = 49
      Height = 13
      Caption = 'Artikel-Nr:'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 160
      Width = 351
      Height = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 249
    end
    object MandantComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 351
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 65
      Width = 351
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 1
      OnChange = LagerComboBoxChange
    end
    object ListedCheckBox: TCheckBox
      Left = 8
      Top = 99
      Width = 249
      Height = 17
      Caption = 'Nur gelistete Artikel'
      Checked = True
      State = cbChecked
      TabOrder = 2
      OnClick = ListedCheckBoxClick
    end
    object ArtikelComboBox: TComboBoxPro
      Left = 120
      Top = 134
      Width = 239
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 4
      OnCloseUp = ArtikelComboBoxCloseUp
      OnDropDown = ArtikelComboBoxDropDown
    end
    object ArtikelEdit: TEdit
      Left = 8
      Top = 134
      Width = 106
      Height = 21
      TabOrder = 3
      Text = 'ArtikelEdit'
      OnChange = EditChange
    end
  end
  object OkButton: TButton
    Left = 200
    Top = 196
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    Default = True
    ModalResult = 1
    TabOrder = 1
    ExplicitTop = 174
  end
  object AbortButton: TButton
    Left = 284
    Top = 196
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 2
    ExplicitTop = 174
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 216
    Top = 8
  end
end
