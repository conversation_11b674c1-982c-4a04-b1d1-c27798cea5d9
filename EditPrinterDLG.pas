unit EditPrinterDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, Vcl.ExtCtrls;

type
  TEditPrinterForm = class(TForm)
    NameEdit: TEdit;
    PortEdit: TEdit;
    AbortButton: TButton;
    OkButton: TButton;
    Label10: TLabel;
    LocComboBox: TComboBoxPro;
    TypComboBox: TComboBox;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    LagerComboBox: TComboBoxPro;
    Label5: TLabel;
    DescEdit: TEdit;
    Label6: TLabel;
    FormatComboBox: TComboBox;
    Label7: TLabel;
    ModelComboBox: TComboBox;
    Label8: TLabel;
    PrtNrEdit: TEdit;
    Label9: TLabel;
    PrtStationEdit: TEdit;
    Bevel1: TBevel;
    Bevel2: TBevel;
    Bevel3: TBevel;
    procedure FormCreate(Sender: TObject);
    procedure LocComboBoxChange(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
    fRefPrt : Integer;
  public
    property RefPrt : Integer read fRefPrt;
    
    function Prepare (const RefPrt : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, Ora, OraSmart, LVSConst, DatenModul, FrontendUtils, ResourceText, LVSPrinterInterface,
  FrontendMessages, SprachModul;

procedure TEditPrinterForm.LocComboBoxChange(Sender: TObject);
begin
  if (LocComboBox.ItemIndex <= 0) then
    LagerComboBox.Enabled := false
  else begin
    LagerComboBox.Enabled := true;

    LoadLagerCombobox(LagerComboBox, GetComboBoxRef(LocComboBox));
    LagerComboBox.Items.Insert(0, GetResourceText(1004));
  end;
end;

function TEditPrinterForm.Prepare (const RefPrt : Integer) : Integer;
var
  query    : TSmartQuery;
begin
  LoadLocationCombobox (LocComboBox);
  LocComboBox.Items.Insert (0, GetResourceText(1004));

  query := TSmartQuery.Create (Self);

  try
    query.ReadOnly := True;
    query.Session  := LVSDatenModul.OraMainSession;

    if (RefPrt <= 0) then begin
      LocComboBox.ItemIndex := 0;
      LagerComboBox.Enabled := False;

      Caption := GetResourceText(1863);
    end else begin
      fRefPrt := RefPrt;

      Caption := GetResourceText(1864);

      query.SQL.Add ('select * from V_PRT_PRINTER where REF=:ref');
      query.Params.ParamByName('ref').Value := fRefPrt;

      query.Open;

      NameEdit.Text := query.FieldByName ('NAME').AsString;
      DescEdit.Text := query.FieldByName ('BESCHREIBUNG').AsString;
      PortEdit.Text := query.FieldByName ('PORT').AsString;
      PrtNrEdit.Text := query.FieldByName ('PRT_NUMMER').AsString;
      PrtStationEdit.Text := query.FieldByName ('LEITSTAND').AsString;

      if query.FieldByName ('REF_LOCATION').IsNull then begin
        LocComboBox.ItemIndex := 0;
        LagerComboBox.Enabled := False;
      end else begin
        LocComboBox.ItemIndex := FindComboboxRef(LocComboBox, query.FieldByName ('REF_LOCATION').AsInteger);
        if (LocComboBox.ItemIndex = -1) then begin
          LocComboBox.ItemIndex := 0;
          LagerComboBox.Enabled := False;
        end else begin
          LocComboBoxChange (Nil);

          if query.FieldByName ('REF_LAGER').IsNull then
            LagerComboBox.ItemIndex := 0
          else begin
            LagerComboBox.ItemIndex := FindComboboxRef(LagerComboBox, query.FieldByName ('REF_LAGER').AsInteger);
            if (LagerComboBox.ItemIndex = -1) then
              LagerComboBox.ItemIndex := 0;
          end;
        end;
      end;

      TypComboBox.ItemIndex := TypComboBox.Items.IndexOf(query.FieldByName ('TYPE').AsString);
      if (TypComboBox.ItemIndex = -1) then
        TypComboBox.ItemIndex := TypComboBox.Items.Add (query.FieldByName ('TYPE').AsString);

      ModelComboBox.ItemIndex := ModelComboBox.Items.IndexOf(query.FieldByName ('MODEL').AsString);
      if (ModelComboBox.ItemIndex = -1) then
        ModelComboBox.ItemIndex := ModelComboBox.Items.Add (query.FieldByName ('MODEL').AsString);

      FormatComboBox.ItemIndex := FormatComboBox.Items.IndexOf(query.FieldByName ('FORMAT').AsString);
      if (FormatComboBox.ItemIndex = -1) then
        FormatComboBox.ItemIndex := FormatComboBox.Items.Add (query.FieldByName ('FORMAT').AsString);

      query.Close;
    end;
  finally
    query.Free;
  end;

  Result := 0;
end;

procedure TEditPrinterForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  ref : Integer;
begin
  if (ModalResult <> mrOk) then begin
    CanClose := True;
  end else begin
    if (Length (NameEdit.Text) = 0) then begin
      CanClose := False;
      NameEdit.SetFocus;
      FrontendMessages.MessageDLG (FormatMessageText (1842, []), mtError, [mbOK], 0);
    end else if (Length (PortEdit.Text) = 0) then begin
      CanClose := False;
      PortEdit.SetFocus;
      FrontendMessages.MessageDLG (FormatMessageText (1843, []), mtError, [mbOK], 0);
    end else begin
      if (fRefPrt = -1) then begin
        res := InsertPrinter (GetComboBoxRef (LocComboBox), GetComboBoxRef (LagerComboBox), NameEdit.Text, DescEdit.Text, PortEdit.Text, TypComboBox.Text, ModelComboBox.Text, FormatComboBox.Text, ref);

        if (res = 0) then
          fRefPrt := ref
        else
          FrontendMessages.MessageDLG (FormatMessageText (1844, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end else begin
        res := UpdatePrinter (fRefPrt, GetComboBoxRef (LocComboBox), GetComboBoxRef (LagerComboBox), NameEdit.Text, DescEdit.Text, PortEdit.Text, TypComboBox.Text, ModelComboBox.Text, FormatComboBox.Text);

        if (res <> 0) then
          FrontendMessages.MessageDLG (FormatMessageText (1845, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end;

      if (res = 0) then begin
        res := PrinterSetNr (fRefPrt, PrtNrEdit.Text);

        if (res = 0) then
          res := PrinterSetLeitstand (fRefPrt, PrtStationEdit.Text);

        if (res <> 0) then
          FrontendMessages.MessageDLG (FormatMessageText (1845, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end;

      CanClose := (res = 0);
    end;
  end;
end;

procedure TEditPrinterForm.FormCreate(Sender: TObject);
begin
  fRefPrt := -1;

  NameEdit.Text := '';
  DescEdit.Text := '';
  PortEdit.Text := '';
  PrtNrEdit.Text := '';
  PrtStationEdit.Text := '';
  LocComboBox.ItemIndex := 0;
  LagerComboBox.Enabled := False;
  TypComboBox.ItemIndex := 0;
  ModelComboBox.ItemIndex := 0;
  FormatComboBox.ItemIndex := 0;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LocComboBox);
  {$endif}
end;

end.
