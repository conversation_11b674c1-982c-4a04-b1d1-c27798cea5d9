object KommLPAssignForm: TKommLPAssignForm
  Left = 300
  Top = 251
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Kommissionierplatz zuordnen'
  ClientHeight = 391
  ClientWidth = 692
  Color = clBtnFace
  Constraints.MinHeight = 380
  Constraints.MinWidth = 700
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnHide = FormHide
  OnKeyDown = FormKeyDown
  OnResize = FormResize
  OnShow = FormShow
  DesignSize = (
    692
    391)
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 692
    Height = 202
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      692
      202)
    object Label3: TLabel
      Left = 8
      Top = 75
      Width = 62
      Height = 13
      Caption = 'Artikelgruppe'
    end
    object Label1: TLabel
      Left = 340
      Top = 75
      Width = 62
      Height = 13
      Caption = 'Lagerbereich'
    end
    object Label2: TLabel
      Left = 10
      Top = 189
      Width = 29
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Artikel'
    end
    object Label4: TLabel
      Left = 440
      Top = 190
      Width = 98
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Kommissionier-Pl'#228'tze'
    end
    object Label5: TLabel
      Left = 340
      Top = 11
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Label6: TLabel
      Left = 10
      Top = 11
      Width = 39
      Height = 13
      Caption = 'Planung'
    end
    object Bevel1: TBevel
      Left = 7
      Top = 62
      Width = 680
      Height = 11
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object ARGrpComboBox: TComboBoxPro
      Left = 10
      Top = 94
      Width = 303
      Height = 22
      Style = csOwnerDrawFixed
      ItemHeight = 16
      TabOrder = 2
      OnChange = ARGrpComboBoxChange
    end
    object LBComboBox: TComboBoxPro
      Left = 340
      Top = 94
      Width = 347
      Height = 22
      Style = csOwnerDrawFixed
      ItemHeight = 16
      TabOrder = 6
      OnChange = LBComboBoxChange
    end
    object CheckBox1: TCheckBox
      Left = 440
      Top = 128
      Width = 281
      Height = 17
      Caption = 'Nur freie Pl'#228'tze anzeigen'
      Checked = True
      State = cbChecked
      TabOrder = 7
      OnClick = CheckBox1Click
    end
    object CheckBox2: TCheckBox
      Left = 8
      Top = 128
      Width = 297
      Height = 17
      Caption = 'Nur nicht zugeordnete Artikel anzeigen'
      Checked = True
      State = cbChecked
      TabOrder = 3
      OnClick = CheckBox2Click
    end
    object CheckBox3: TCheckBox
      Left = 440
      Top = 146
      Width = 169
      Height = 17
      Caption = 'Nur belegte Pl'#228'tze anzeigen'
      TabOrder = 8
      OnClick = CheckBox3Click
    end
    object LagerComboBox: TComboBoxPro
      Left = 340
      Top = 30
      Width = 347
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      ItemHeight = 16
      TabOrder = 1
      OnChange = LagerComboBoxChange
    end
    object CheckBox4: TCheckBox
      Left = 8
      Top = 146
      Width = 281
      Height = 17
      Caption = 'Inhalts-Artikel anzeigen'
      TabOrder = 4
      OnClick = CheckBox2Click
    end
    object CheckBox5: TCheckBox
      Left = 8
      Top = 164
      Width = 281
      Height = 17
      Caption = 'Nur gelistete Artikel anzeigen'
      Checked = True
      State = cbChecked
      TabOrder = 5
      OnClick = CheckBox2Click
    end
    object PlanungComboBox: TComboBoxPro
      Left = 10
      Top = 30
      Width = 303
      Height = 22
      Style = csOwnerDrawFixed
      ColWidth = 160
      Enabled = False
      ItemHeight = 16
      TabOrder = 0
      OnChange = PlanungComboBoxChange
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 360
    Width = 692
    Height = 31
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      692
      31)
    object OkButton: TButton
      Left = 612
      Top = 2
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
  end
  object ArtikelDBGrid: TDBGridPro
    Left = 8
    Top = 208
    Width = 281
    Height = 135
    Anchors = [akLeft, akTop, akBottom]
    DataSource = ARDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 2
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDblClick = ArtikelDBGridDblClick
    OnMouseDown = ArtikelDBGridMouseDown
    OnMouseUp = ArtikelDBGridMouseUp
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
    OnColumnSort = ArtikelDBGridColumnSort
  end
  object KommLPDBGrid: TDBGridPro
    Left = 303
    Top = 208
    Width = 330
    Height = 135
    Anchors = [akTop, akBottom]
    DataSource = KommLPDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ParentShowHint = False
    PopupMenu = KommLPGridPopupMenu
    ReadOnly = True
    ShowHint = True
    TabOrder = 3
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDblClick = AZPangeben1Click
    OnDragDrop = KommLPDBGridDragDrop
    OnDragOver = KommLPDBGridDragOver
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoCellHint, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
    OnColumnSort = ArtikelDBGridColumnSort
  end
  object ARDataSource: TDataSource
    DataSet = ARQuery
    OnDataChange = ARDataSourceDataChange
    Left = 160
    Top = 136
  end
  object ARQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 192
    Top = 136
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 392
    Top = 8
  end
  object KommLPDataSource: TDataSource
    DataSet = KommLPQuery
    Left = 488
    Top = 160
  end
  object KommLPQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 528
    Top = 160
  end
  object KommLPGridPopupMenu: TPopupMenu
    OnPopup = KommLPGridPopupMenuPopup
    Left = 496
    Top = 248
    object Artikelzordnungaufheben1: TMenuItem
      Caption = 'Artikelzordnung aufheben'
      ShortCut = 46
      OnClick = Artikelzordnungaufheben1Click
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object AZPangeben1: TMenuItem
      Caption = 'AZP angeben...'
      OnClick = AZPangeben1Click
    end
    object SetNachsParamMenuItem: TMenuItem
      Caption = 'Nachschub-Parameter...'
      OnClick = SetNachsParamMenuItemClick
    end
    object SetPARParamMenuItem: TMenuItem
      Caption = 'Verwaltungsparameter setzen..'
      OnClick = SetPARParamMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object ChangeKommLPPopupItem: TMenuItem
      Caption = 'Daten des Komm.-Platzes '#228'ndern...'
      OnClick = ChangeKommLPPopupItemClick
    end
  end
  object KommLPDataSet: TADODataSet
    Parameters = <>
    Left = 584
    Top = 144
  end
end
