object EditWATourForm: TEditWATourForm
  Left = 561
  Top = 277
  Caption = 'Warenausgangs-Relationen'
  ClientHeight = 663
  ClientWidth = 599
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    599
    663)
  PixelsPerInch = 96
  TextHeight = 13
  object OkButton: TButton
    Left = 424
    Top = 630
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 2
  end
  object AbortButton: TButton
    Left = 516
    Top = 630
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 3
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 422
    Width = 583
    Height = 201
    ActivePage = TabSheet1
    Anchors = [akLeft, akBottom]
    PopupMenu = WochentagPopupMenu
    TabOrder = 1
    TabWidth = 70
    object TabSheet1: TTabSheet
      Caption = 'Alle Tage'
      OnShow = TabSheet1Show
      inline DefaultTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet2: TTabSheet
      Caption = 'Montag'
      ImageIndex = 1
      inline MoTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet3: TTabSheet
      Caption = 'Dienstag'
      ImageIndex = 2
      inline DiTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet4: TTabSheet
      Caption = 'Mittwoch'
      ImageIndex = 3
      inline MiTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet5: TTabSheet
      Caption = 'Donnerstag'
      ImageIndex = 4
      inline DoTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet6: TTabSheet
      Caption = 'Freitag'
      ImageIndex = 5
      inline FrTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet7: TTabSheet
      Caption = 'Samstag'
      ImageIndex = 6
      inline SaTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
    object TabSheet8: TTabSheet
      Caption = 'Sonntag'
      ImageIndex = 7
      inline SoTourInfo: TTourInfo
        Left = 0
        Top = 0
        Width = 575
        Height = 173
        Align = alClient
        TabOrder = 0
        TabStop = True
        ExplicitWidth = 575
        ExplicitHeight = 173
        inherited Label3: TLabel
          Width = 30
          ExplicitWidth = 30
        end
        inherited Label4: TLabel
          Width = 62
          ExplicitWidth = 62
        end
        inherited Label5: TLabel
          Width = 58
          ExplicitWidth = 58
        end
        inherited GroupBox3: TGroupBox
          Left = 407
          ExplicitLeft = 407
        end
        inherited ForecastTimeCheckBox: TCheckBox
          Left = 415
          ExplicitLeft = 415
        end
      end
    end
  end
  object TourDatenPanel: TPanel
    Left = 0
    Top = 0
    Width = 599
    Height = 417
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      599
      417)
    object Label1: TLabel
      Left = 8
      Top = 71
      Width = 39
      Height = 13
      Caption = 'Tour-Nr.'
    end
    object Label3: TLabel
      Left = 8
      Top = 11
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object Label4: TLabel
      Left = 8
      Top = 109
      Width = 89
      Height = 13
      Caption = 'Kommissionierlager'
    end
    object Label14: TLabel
      Left = 8
      Top = 41
      Width = 29
      Height = 13
      Caption = 'Depot'
    end
    object Bevel1: TBevel
      Left = 7
      Top = 97
      Width = 584
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label2: TLabel
      Left = 8
      Top = 141
      Width = 65
      Height = 13
      Caption = 'Beschreibung'
    end
    object MandantComboBox: TComboBoxPro
      Left = 103
      Top = 8
      Width = 488
      Height = 22
      Style = csOwnerDrawFixed
      ColWidth = 140
      ItemHeight = 16
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
    object LagerComboBox: TComboBoxPro
      Left = 103
      Top = 106
      Width = 488
      Height = 22
      Style = csOwnerDrawFixed
      ColWidth = 100
      ItemHeight = 16
      TabOrder = 3
      OnChange = LagerComboBoxChange
    end
    object RadioPrioPanel: TPanel
      Left = 8
      Top = 163
      Width = 583
      Height = 62
      Anchors = [akLeft, akTop, akRight]
      BevelOuter = bvNone
      TabOrder = 5
      object PrioRadioGroup: TRadioGroup
        Left = 0
        Top = 0
        Width = 583
        Height = 62
        Align = alClient
        Caption = 'Priorit'#228't'
        Columns = 4
        Items.Strings = (
          'Niedrig'
          'Normal'
          'Hoch'
          'Dringend')
        TabOrder = 0
      end
    end
    object EditPrioPanel: TPanel
      Left = 63
      Top = 171
      Width = 536
      Height = 62
      BevelOuter = bvNone
      TabOrder = 6
      object GroupBox1: TGroupBox
        Left = 0
        Top = 0
        Width = 536
        Height = 62
        Align = alClient
        Caption = 'Priorit'#228't'
        TabOrder = 0
        DesignSize = (
          536
          62)
        object Label6: TLabel
          Left = 71
          Top = 11
          Width = 462
          Height = 46
          Anchors = [akLeft, akTop, akRight, akBottom]
          AutoSize = False
          Caption = 
            'Die Priorit'#228't wird aufsteigend vergeben. Maximal ist eine Priori' +
            't'#228't von 999 m'#246'glich. '#13#10'Sinnvollerweise wird die Priorit'#228't in 10e' +
            'r-Schritten vergeben, damit bei der Auftragsplanung noch das Auf' +
            'tragsvolumen mit einfliesen kann'
          Layout = tlCenter
          WordWrap = True
        end
        object PrioEdit: TEdit
          Left = 8
          Top = 24
          Width = 41
          Height = 21
          MaxLength = 3
          TabOrder = 0
          Text = '0'
        end
        object PrioUpDown: TIntegerUpDown
          Left = 49
          Top = 24
          Width = 16
          Height = 21
          Associate = PrioEdit
          Max = 999
          TabOrder = 1
        end
      end
    end
    object DepotComboBox: TComboBoxPro
      Left = 103
      Top = 38
      Width = 488
      Height = 22
      Style = csOwnerDrawFixed
      ColWidth = 140
      ItemHeight = 16
      TabOrder = 1
      OnChange = DepotComboBoxChange
    end
    object RelationGroupBox: TGroupBox
      Left = 8
      Top = 330
      Width = 583
      Height = 82
      Caption = 'Relationen'
      TabOrder = 7
      object Label8: TLabel
        Left = 8
        Top = 21
        Width = 78
        Height = 13
        Caption = 'Verlade-Relation'
      end
      object Label5: TLabel
        Left = 16
        Top = 51
        Width = 59
        Height = 13
        Caption = 'Ziel-Relation'
      end
      object VerlRelComboBox: TComboBoxPro
        Left = 103
        Top = 18
        Width = 465
        Height = 22
        Style = csOwnerDrawFixed
        ColWidth = 160
        ItemHeight = 16
        TabOrder = 0
      end
      object ZielRelComboBox: TComboBoxPro
        Left = 103
        Top = 48
        Width = 465
        Height = 22
        Style = csOwnerDrawFixed
        ColWidth = 160
        ItemHeight = 16
        TabOrder = 1
      end
    end
    object KommGroupBox: TGroupBox
      Left = 8
      Top = 234
      Width = 583
      Height = 90
      Caption = 'Kommissionierung'
      TabOrder = 8
      object Label18: TLabel
        Left = 8
        Top = 18
        Width = 146
        Height = 13
        Caption = 'Abweichende Kommissionierart'
      end
      object KommArtComboBox: TComboBoxPro
        Left = 8
        Top = 35
        Width = 190
        Height = 21
        Style = csOwnerDrawFixed
        ColWidth = 80
        ItemHeight = 15
        TabOrder = 0
      end
      object KommUserPanel: TPanel
        Left = 212
        Top = 18
        Width = 356
        Height = 43
        BevelOuter = bvNone
        TabOrder = 1
        DesignSize = (
          356
          43)
        object Label7: TLabel
          Left = 0
          Top = 0
          Width = 144
          Height = 13
          Caption = 'Vorgesehener Kommissionierer'
        end
        object KommUserComboBox: TComboBoxPro
          Left = 0
          Top = 17
          Width = 357
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 100
          ItemHeight = 15
          TabOrder = 0
          OnDrawItem = KommUserComboBoxDrawItem
        end
      end
      object SammelKommCheckBox: TCheckBox
        Left = 8
        Top = 64
        Width = 560
        Height = 17
        Caption = 'Alle Auftr'#228'ge der Tour zu einer Sammelpackliste zusammenfassen'
        TabOrder = 2
      end
    end
    object TourComboBox: TComboBoxPro
      Left = 103
      Top = 68
      Width = 488
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 140
      ItemHeight = 15
      TabOrder = 2
      OnChange = TourComboBoxChange
    end
    object DescEdit: TEdit
      Left = 103
      Top = 136
      Width = 488
      Height = 21
      TabOrder = 4
      Text = 'DescEdit'
    end
  end
  object WochentagPopupMenu: TPopupMenu
    Left = 376
    Top = 496
    object Montag1: TMenuItem
      AutoCheck = True
      Caption = 'Montag'
      OnClick = WochentagClick
    end
    object Dienstag1: TMenuItem
      AutoCheck = True
      Caption = 'Dienstag'
      GroupIndex = 1
      OnClick = WochentagClick
    end
    object Mittwoch1: TMenuItem
      AutoCheck = True
      Caption = 'Mittwoch'
      GroupIndex = 1
      OnClick = WochentagClick
    end
    object Donnerstag1: TMenuItem
      AutoCheck = True
      Caption = 'Donnerstag'
      GroupIndex = 1
      OnClick = WochentagClick
    end
    object Freitag1: TMenuItem
      AutoCheck = True
      Caption = 'Freitag'
      GroupIndex = 1
      OnClick = WochentagClick
    end
    object Samstag1: TMenuItem
      AutoCheck = True
      Caption = 'Samstag'
      GroupIndex = 1
      OnClick = WochentagClick
    end
    object Sonntag1: TMenuItem
      AutoCheck = True
      Caption = 'Sonntag'
      GroupIndex = 1
      OnClick = WochentagClick
    end
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 320
    Top = 600
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 304
    Top = 120
  end
end
