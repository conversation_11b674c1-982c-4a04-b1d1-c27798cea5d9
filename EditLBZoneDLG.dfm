object EditLBZoneForm: TEditLBZoneForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Form'
  ClientHeight = 257
  ClientWidth = 383
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    383
    257)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 16
    Width = 66
    Height = 13
    Caption = 'Lagerbereich:'
  end
  object LBLabel: TLabel
    Left = 88
    Top = 16
    Width = 43
    Height = 13
    Caption = 'LBLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Bevel1: TBevel
    Left = 6
    Top = 35
    Width = 372
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label2: TLabel
    Left = 8
    Top = 48
    Width = 27
    Height = 13
    Caption = 'Name'
  end
  object Label3: TLabel
    Left = 8
    Top = 101
    Width = 64
    Height = 13
    Caption = 'Beschreibung'
  end
  object Label4: TLabel
    Left = 288
    Top = 48
    Width = 39
    Height = 13
    Caption = 'Nummer'
  end
  object Label5: TLabel
    Left = 8
    Top = 160
    Width = 96
    Height = 13
    Caption = 'Kommissionier-Folge'
  end
  object Label17: TLabel
    Left = 158
    Top = 160
    Width = 54
    Height = 13
    Caption = 'ABC-Klasse'
  end
  object Bevel2: TBevel
    Left = 6
    Top = 211
    Width = 372
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object OkButton: TButton
    Left = 224
    Top = 224
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 6
  end
  object AbortButton: TButton
    Left = 305
    Top = 224
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 7
  end
  object NameEdit: TEdit
    Left = 8
    Top = 64
    Width = 265
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 0
    Text = 'NameEdit'
  end
  object DescEdit: TEdit
    Left = 8
    Top = 120
    Width = 367
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 2
    Text = 'DescEdit'
  end
  object NummerEdit: TEdit
    Left = 288
    Top = 64
    Width = 87
    Height = 21
    TabOrder = 1
    Text = 'NummerEdit'
  end
  object KommFolgeEdit: TEdit
    Left = 8
    Top = 176
    Width = 89
    Height = 21
    TabOrder = 3
    Text = '0'
    OnChange = KommFolgeEditChange
  end
  object KommFolgeUpDown: TUpDown
    Left = 97
    Top = 176
    Width = 16
    Height = 21
    Associate = KommFolgeEdit
    Min = -1
    TabOrder = 4
    OnChangingEx = KommFolgeUpDownChangingEx
  end
  object ABCComboBox: TComboBox
    Left = 158
    Top = 176
    Width = 217
    Height = 21
    Style = csDropDownList
    ItemHeight = 13
    ItemIndex = 0
    TabOrder = 5
    Items.Strings = (
      ''
      'A'
      'B'
      'C')
  end
end
