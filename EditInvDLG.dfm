object EditInvForm: TEditInvForm
  Left = 390
  Top = 264
  Anchors = [akRight, akBottom]
  BorderStyle = bsDialog
  Caption = 'EditInvForm'
  ClientHeight = 519
  ClientWidth = 457
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    457
    519)
  TextHeight = 13
  object OkButton: TButton
    Left = 294
    Top = 488
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'OK'
    Default = True
    ModalResult = 1
    TabOrder = 5
  end
  object AbortButton: TButton
    Left = 374
    Top = 488
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 6
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 148
    Width = 457
    Height = 205
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      457
      205)
    object Label2: TLabel
      Left = 8
      Top = 60
      Width = 62
      Height = 13
      Caption = 'Lagerbereich'
    end
    object Label3: TLabel
      Left = 8
      Top = 115
      Width = 81
      Height = 13
      Caption = 'Erfassungsdatum'
    end
    object Label4: TLabel
      Left = 8
      Top = 160
      Width = 18
      Height = 13
      Caption = 'Info'
    end
    object Label5: TLabel
      Left = 8
      Top = 5
      Width = 55
      Height = 13
      Caption = 'Inventur-Art'
    end
    object Label7: TLabel
      Left = 294
      Top = 115
      Width = 85
      Height = 13
      Caption = 'Bewertungsdatum'
    end
    object Bevel2: TBevel
      Left = 6
      Top = 0
      Width = 449
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Bevel3: TBevel
      Left = 6
      Top = 49
      Width = 449
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Bevel4: TBevel
      Left = 6
      Top = 106
      Width = 449
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object LBComboBox: TComboBoxPro
      Left = 8
      Top = 76
      Width = 441
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      ItemHeight = 15
      TabOrder = 0
    end
    object InvDateTimePicker: TDateTimePicker
      Left = 8
      Top = 130
      Width = 156
      Height = 21
      Date = 38553.000000000000000000
      Time = 0.338474502321332700
      TabOrder = 2
      OnChange = InvDateTimePickerChange
    end
    object InfoEdit: TEdit
      Left = 8
      Top = 176
      Width = 441
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 4
      Text = 'InfoEdit'
    end
    object ArtComboBox: TComboBoxPro
      Left = 8
      Top = 20
      Width = 441
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      TabOrder = 1
      OnChange = ArtComboBoxChange
    end
    object WertDateTimePicker: TDateTimePicker
      Left = 293
      Top = 130
      Width = 156
      Height = 21
      Date = 38553.000000000000000000
      Time = 0.338474502321332700
      TabOrder = 3
    end
  end
  object LagerPanel: TPanel
    Left = 0
    Top = 94
    Width = 457
    Height = 54
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      457
      54)
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel1: TBevel
      Left = 6
      Top = 1
      Width = 449
      Height = 1
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 25
      Width = 441
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 120
      ItemHeight = 15
      TabOrder = 0
      OnChange = LagerComboBoxChange
    end
  end
  object MandPanel: TPanel
    Left = 0
    Top = 0
    Width = 457
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label6: TLabel
      Left = 8
      Top = 8
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 441
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 120
      ItemHeight = 15
      TabOrder = 0
      OnChange = MandComboBoxChange
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 50
    Width = 457
    Height = 44
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    object Label8: TLabel
      Left = 8
      Top = 0
      Width = 67
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 8
      Top = 16
      Width = 441
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 120
      ItemHeight = 15
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object OptPanel: TPanel
    Left = 0
    Top = 353
    Width = 457
    Height = 128
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      457
      128)
    object OptionGroupBox: TGroupBox
      Left = 8
      Top = 6
      Width = 441
      Height = 118
      Anchors = [akLeft, akTop, akRight, akBottom]
      Caption = 'Optionen'
      TabOrder = 0
      object OptSingleScanCheckBox: TCheckBox
        Left = 16
        Top = 40
        Width = 225
        Height = 17
        Caption = 'Artikel einzeln scannen'
        TabOrder = 1
      end
      object OptGoLPCheckBox: TCheckBox
        Left = 16
        Top = 56
        Width = 177
        Height = 17
        Caption = 'Von Platz zu Platz f'#252'hren'
        TabOrder = 2
      end
      object SuggestArtikelCheckBox: TCheckBox
        Left = 16
        Top = 72
        Width = 401
        Height = 17
        Caption = 'Erster Artikel vorschlagen'
        TabOrder = 3
      end
      object OptNurMengeCheckBox: TCheckBox
        Left = 16
        Top = 18
        Width = 321
        Height = 17
        Caption = 'Nur Mengen erfassen, Kein MHD oder Charge'
        TabOrder = 0
      end
      object SuggestLEBesCheckBox: TCheckBox
        Left = 16
        Top = 88
        Width = 401
        Height = 17
        Caption = 'Einzene Bestand auf HU oder LE vorschlagen'
        TabOrder = 4
      end
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 328
    Top = 48
  end
end
