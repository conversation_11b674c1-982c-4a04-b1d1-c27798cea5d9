unit SelectNVEDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, ExtCtrls, DB, ADODB;

type
  TSelectNVEForm = class(TForm)
    Panel1: TPanel;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    LTInhaltDBGrid: TDBGridPro;
    Panel2: TPanel;
    OkButton: TButton;
    NVEDataSource: TDataSource;
    NVEQuery: TADOQuery;
    AbortButton: TButton;
    VorgangLabel: TLabel;
    Bevel1: TBevel;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
  private
    fRefLager     : Integer;
    fRefMand      : Integer;
    fRefSubMand   : Integer;
    fRefAuftrag   : Integer;

    fSelNVENr     : String;
    fRefSelNVE    : Integer;

    procedure UpdateNVEQuery (Sender: TObject);
  public
   property  SelNVENr  : String  read fSelNVENr;
   property  RefSelNVE : Integer read fRefSelNVE;

    procedure Prepare (const RefAuftrag : Integer);
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DatenModul, ConfigModul, DBGridUtilModule, FrontendUtils;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectNVEForm.Prepare (const RefAuftrag : Integer);
var
  query : TADOQuery;
begin
  fRefAuftrag := RefAuftrag;

  query := TADOQuery.Create (Self);

  try
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select l.REF, m.REF, sm.REF from V_AUFTRAG a inner join V_LAGER l on (l.REF=a.REF_LAGER) inner join V_MANDANT m on (m.REF=a.REF_MAND) left outer join V_MANDANT sm on (sm.REF=a.REF_SUB_MAND) where a.REF='+IntToStr (RefAuftrag));

    query.Open;

    fRefLager   := query.Fields [0].AsInteger;
    fRefMand    := query.Fields [1].AsInteger;
    fRefSubMand := DBGetReferenz (query.Fields [2]);

    query.Close;
  finally
    query.Free;
  end;

  UpdateNVEQuery (Nil);
end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectNVEForm.UpdateNVEQuery (Sender: TObject);
begin
  NVEQuery.SQL.Clear;

  NVEQuery.SQL.Add ('select nve.REF,nve.NVE_NR,nve.NVE_TYPE,nve.GESAMT_VPE,nve.NETTO_GEWICHT from V_NVE nve');
  NVEQuery.SQL.Add ('where nve.REF_AUF_KOPF is null and nve.STATUS in (''ANG'',''AKT'',''WA'',''FIN'') and nve.REF_LAGER='+IntToStr (fRefLager));
  NVEQuery.SQL.Add ('and NVE_NR not in (select NVE_NR from V_AUFTRAG_POS_LT where REF_AUF_KOPF in (select REF from V_AUFTRAG_02 where STATUS not in (''ABG'')))');
  NVEQuery.SQL.Add ('and REF_MAND='+IntToStr (fRefMand));

  if (fRefSubMand = -1) then
    NVEQuery.SQL.Add ('and REF_SUB_MAND is null')
  else NVEQuery.SQL.Add ('and REF_SUB_MAND='+IntToStr (fRefSubMand));

  NVEQuery.SQL.Add ('order by nve.NETTO_GEWICHT');

  NVEQuery.Open;

  DBGridUtils.SetGewichtDisplayFunctions (NVEQuery, 'NETTO_GEWICHT');
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectNVEForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  if (NVEQuery.Active) and (NVEQuery.RecNo <> -1) then begin
    fSelNVENr  := NVEQuery.FieldByName ('NVE_NR').AsString;
    fRefSelNVE := DBGetReferenz (NVEQuery.FieldByName ('REF'))
  end else begin
    fSelNVENr := '';
    fRefSelNVE := -1;
  end;

  NVEQuery.Close;

  if Assigned (DBGridUtils) Then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectNVEForm.FormCreate(Sender: TObject);
begin
  fRefMand    := -1;
  fRefSubMand := -1;
  fRefLager   := -1;

  Label1.Caption := '';
  Label2.Caption := '';
  Label3.Caption := '';
  Label4.Caption := '';

  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectNVEForm.FormShow(Sender: TObject);
var
  query : TADOQuery;
begin
  LVSConfigModul.RestoreFormInfo (Self);

  Label1.Caption := 'Mandant:';
  Label3.Caption := 'Lager:';

  query := TADOQuery.Create (Self);

  try
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select l.NAME as LAGER_NAME, m.NAME as MAND_NAME, sm.NAME as SUB_MAND_NAME from V_AUFTRAG a inner join V_LAGER l on (l.REF=a.REF_LAGER) inner join V_MANDANT m on (m.REF=a.REF_MAND) left outer join V_MANDANT sm on (sm.REF=a.REF_SUB_MAND) where a.REF='+IntToStr (fRefAuftrag));

    query.Open;

    if query.FieldByName ('SUB_MAND_NAME').IsNull then
      Label2.Caption := query.FieldByName ('MAND_NAME').AsString
    else Label2.Caption := query.FieldByName ('MAND_NAME').AsString+' / '+query.FieldByName ('SUB_MAND_NAME').AsString;

    Label4.Caption := query.FieldByName ('LAGER_NAME').AsString;

    query.Close;
  finally
    query.Free;
  end;
end;

end.
