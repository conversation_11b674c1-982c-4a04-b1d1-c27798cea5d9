﻿//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : UserDLG
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/XE11/UserAdminFrame.pas $
// $Revision: 68 $
// $Modtime: 15.01.24 17:26 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : <PERSON><PERSON><PERSON> und ä<PERSON>, <PERSON><PERSON>e vergeben
//*****************************************************************************
unit UserAdminFrame;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, ComCtrls, StdCtrls, Grids, ACLTemplate, StringGridPro,
  Menus, ExtCtrls, LVSSecurity, LVSBenutzer, ComboBoxPro, ACOTypen

  {$IFDEF VER360}
    ,System.ImageList, Vcl.ImgList
  {$ELSE}
    {$IFDEF VER350}
      ,System.ImageList, Vcl.ImgList
    {$else}
      ,ImgList
    {$endif}
  {$endif}
  ;

type
  TUserAdminFrame = class(TFrame)
    UserGrpTreeView: TTreeView;
    UserGroupBox: TGroupBox;
    Label1: TLabel;
    UserIDEdit: TEdit;
    Label2: TLabel;
    UserNameEdit: TEdit;
    Label3: TLabel;
    UserFunkEdit: TEdit;
    NewUserButton: TButton;                             
    SaveUserButton: TButton;
    GrpGroupBox: TGroupBox;
    GrpNameEdit: TEdit;
    Label5: TLabel;
    NewGrpButton: TButton;
    SaveGrpButton: TButton;
    UserGrpGroupBox: TGroupBox;
    UserGrpListBox: TListBox;
    Button5: TButton;
    Button6: TButton;
    AddACOWButton: TButton;
    DelACOEButton: TButton;
    GrpUserGroupBox: TGroupBox;
    GrpUserListBox: TListBox;
    AddACOEButton: TButton;
    AddACORButton: TButton;
    AddACOAButton: TButton;
    DelACORButton: TButton;
    DelACOWButton: TButton;
    DelACOAButton: TButton;
    ACOStringGrid: TStringGridPro;
    GrpBezEdit: TEdit;
    Label4: TLabel;
    UserDOMGroupBox: TGroupBox;
    DomUserEdit: TEdit;
    Label6: TLabel;
    Label7: TLabel;
    DomComboBox: TComboBox;
    AddACOGButton: TButton;
    DelACOGButton: TButton;
    AdminCheckBox: TCheckBox;
    PopupMenu1: TPopupMenu;
    NeuerBenutzer: TMenuItem;
    ChangePasswort: TMenuItem;
    NeueGruppe: TMenuItem;
    BenutzerSperren: TMenuItem;
    BenutzerFreigeben: TMenuItem;
    Bevel1: TBevel;
    Label8: TLabel;
    Label9: TLabel;
    Label10: TLabel;
    Label11: TLabel;
    Button1: TButton;
    ImageList1: TImageList;
    SimilarBenutzerAnlegen: TMenuItem;
    N1: TMenuItem;
    N2: TMenuItem;
    N3: TMenuItem;
    DelUserButton: TButton;
    DelGrpButton: TButton;
    N4: TMenuItem;
    Benutzerrechtesynchronisieren1: TMenuItem;
    Benutzerlschen1: TMenuItem;
    GrpLocComboBox: TComboBoxPro;
    Label12: TLabel;
    GrpACORadioButton: TRadioButton;
    GrpKOMMRadioButton: TRadioButton;
    FirmaComboBox: TComboBoxPro;
    Label13: TLabel;
    ACLFrame1: TACLFrame;
    N5: TMenuItem;
    PrintKommBarcodeMenuItem: TMenuItem;
    ActiveUserCheckBox: TCheckBox;
    UserMailEdit: TEdit;
    Label14: TLabel;
    Label15: TLabel;
    UserNumIDEdit: TEdit;
    ShortNameEdit: TEdit;
    Label16: TLabel;
    OracleUserunlock1: TMenuItem;
    GrpNACHRadioButton: TRadioButton;
    AdminGroupCheckBox: TCheckBox;
    GrpTRANSRadioButton: TRadioButton;
    Label17: TLabel;
    LangComboBox: TComboBoxPro;
    procedure SaveUserButtonClick(Sender: TObject);
    procedure NewUserButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure UserGrpTreeViewChange(Sender: TObject; Node: TTreeNode);
    procedure AddACOButtonClick(Sender: TObject);
    procedure DelACOButtonClick(Sender: TObject);
    procedure NewGrpButtonClick(Sender: TObject);
    procedure UserDataChange(Sender: TObject);
    procedure GrpDatenChange(Sender: TObject);
    procedure ACOStringGridClick(Sender: TObject);
    procedure UserGrpListBoxDrawItem(Control: TWinControl; Index: Integer;
      Rect: TRect; State: TOwnerDrawState);
    procedure GrpUserListBoxDrawItem(Control: TWinControl; Index: Integer;
      Rect: TRect; State: TOwnerDrawState);
    procedure Button5Click(Sender: TObject);
    procedure Button6Click(Sender: TObject);
    procedure SaveGrpButtonClick(Sender: TObject);
    procedure ACLFrame1ACOTreeViewChange(Sender: TObject; Node: TTreeNode);
    procedure UserIDEditKeyPress(Sender: TObject; var Key: Char);
    procedure PopupMenu1Popup(Sender: TObject);
    procedure NeuerBenutzerClick(Sender: TObject);
    procedure NeueGruppeClick(Sender: TObject);
    procedure UserGrpTreeViewMouseUp(Sender: TObject; Button: TMouseButton;
      Shift: TShiftState; X, Y: Integer);
    procedure ChangePasswortClick(Sender: TObject);
    procedure UserGrpTreeViewAdvancedCustomDrawItem(
      Sender: TCustomTreeView; Node: TTreeNode; State: TCustomDrawState;
      Stage: TCustomDrawStage; var PaintImages, DefaultDraw: Boolean);
    procedure Button1Click(Sender: TObject);
    procedure BenutzerFreigebenClick(Sender: TObject);
    procedure BenutzerSperrenClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormKeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
    procedure FormResize(Sender: TObject);
    procedure SimilarBenutzerAnlegenClick(Sender: TObject);
    procedure UserGrpTreeViewMouseDown(Sender: TObject;
      Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
    procedure DelUserButtonClick(Sender: TObject);
    procedure DelGrpButtonClick(Sender: TObject);
    procedure Benutzerrechtesynchronisieren1Click(Sender: TObject);
    procedure BenutzerDeleteClick(Sender: TObject);
    procedure PrintKommBarcodeMenuItemClick(Sender: TObject);
    procedure ActiveUserCheckBoxClick(Sender: TObject);
    procedure UserNumIDEditKeyPress(Sender: TObject; var Key: Char);
    procedure ACOStringGridEnter(Sender: TObject);
    procedure UserGrpTreeViewDeletion(Sender: TObject; Node: TTreeNode);
    procedure OracleUserunlock1Click(Sender: TObject);
  private
    fIsAdmin,
    fUserChanged,
    fGroupChanged : Boolean;

    fAktUserID   : String;

    AktUserIndex : Integer;
    AktGrpIndex  : Integer;

    LastNode,
    GroupNode,
    UserNode : TTreeNode;

    procedure ChangeSelection (var Message: TMessage); message WM_USER + 1;

    procedure DoNewGroup;
    procedure DoNewUser (const SimilarUserIdx : Integer);

    procedure EnableACOButton (const ACOType : TACOType; const Rechte : TLVSRechte; const IsUserACO : Boolean);
    procedure DisableACOButton;
    procedure UpdateUserGrpTree;

    procedure ShowUserDaten (const Reference : Integer);
    procedure ShowGruppenDaten (const Reference : Integer);
  public
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;

    procedure Prepare;
    procedure CleanUp;
    procedure Show;
  end;

type
  TNodeDaten = class (TObject)
    NodeType  : Integer;
    Ref       : Integer;
    ID        : String;
    Active    : Boolean;
    Deleted   : Boolean;
    NodeName  : String;
    IsKommGrp : Boolean;

    constructor Create;
  end;

  TACOGridEntry = class (TObject)
    Ref    : Integer;
    RefGrp : Integer;
  end;

implementation

uses
  VCLUtilitys, StringUtils, DatenModul, SelectUserGroupDLG, ChangePasswdDLG,
  NewUserDLG, NewUserGroupDLG, SynchUserACODLG, FrontendUtils, PrintModul,
  ConfigModul, SprachModul, ResourceText, FrontendMessages;


{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TNodeDaten.Create;
begin
  inherited Create;

  NodeType := 0;
  Ref      := -1;
  ID       := '';
  NodeName := '';
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TUserAdminFrame.Create(AOwner: TComponent);
begin
  inherited Create (AOwner);

  GrpGroupBox.BoundsRect := UserGroupBox.BoundsRect;
  FormResize (Self);

  UserGroupBox.Visible := False;
  GrpGroupBox.Visible := False;

  AktUserIndex := -1;
  fAktUserID   := '';
  
  UserIDEdit.Text := '';
  UserNameEdit.Text := '';
  UserMailEdit.Text := '';

  LVSSprachModul.InitFrame (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, DomComboBox);
    LVSSprachModul.SetNoTranslate (Self, LangComboBox);
    LVSSprachModul.SetNoTranslate (Self, FirmaComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
destructor TUserAdminFrame.Destroy;
begin
  ClearComboBoxObjects(GrpLocComboBox);
  ClearComboBoxObjects(FirmaComboBox);
  ClearComboBoxObjects(LangComboBox);

  ClearGridObjects (ACOStringGrid);
  ClearListBoxObjects (GrpUserListBox);

  inherited Destroy;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.CleanUp;
begin
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.UpdateUserGrpTree;
var
  intwert  : Integer;
  nodedata : TNodeDaten;
  query    : TADOQuery;
begin
  if (UserReg.ReadRegValue ('SortUser', intwert) <> 0) then
    intwert := 0;

  LastNode := Nil;
  UserGrpTreeView.Items.Clear;

  nodedata := TNodeDaten.Create;
  nodedata.NodeType := 0;

  UserNode := UserGrpTreeView.Items.AddChildObject (Nil, GetResourceText (1449), nodedata);
  UserNode.ImageIndex    := 0;
  UserNode.SelectedIndex := UserNode.ImageIndex;

  query := TADOQuery.Create (self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;

    if (intwert = 1) then
      query.SQL.Add ('select b.REF, b.USER_NAME, b.USER_ID, b.STATUS from V_SYS_BEN b where b.REF<>0')
    else
      query.SQL.Add ('select b.REF, b.USER_ID, b.USER_NAME, b.STATUS from V_SYS_BEN b where b.REF<>0');

    //Normal sterbliche dürfen den Schemabesitzer nicht sehen ;-)
    if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then begin
      query.SQL.Add ('and b.STATUS<>''DEL'' and (instr (PA_SYS_BENUTZER.GET_OBJECT_RECHTE (:ref_ben, b.REF_ACO), ''A'') is not null) and Upper (b.USER_ID)<>Upper (:user_id)');
      query.Parameters.ParamByName ('ref_ben').Value := LVSDatenModul.AktUserRef;
      query.Parameters.ParamByName ('user_id').Value := LVSDatenModul.Schema;
    end;

    if (ActiveUserCheckBox.Checked) then
      query.SQL.Add ('and b.STATUS=''AKT''');

    if (intwert = 1) then
      query.SQL.Add ('order by b.USER_NAME')
    else
      query.SQL.Add ('order by b.USER_ID');

    query.Open;

    while not (query.Eof) do begin
      nodedata := TNodeDaten.Create;
      nodedata.NodeType  := 1;
      nodedata.Ref       := query.FieldByName ('REF').AsInteger;
      nodedata.NodeName  := query.FieldByName ('USER_NAME').AsString;
      nodedata.ID        := query.FieldByName ('USER_ID').AsString;
      nodedata.Active    := query.FieldByName ('STATUS').AsString = 'AKT';
      nodedata.Deleted   := query.FieldByName ('STATUS').AsString = 'DEL';
      nodedata.IsKommGrp := False;

      with UserGrpTreeView.Items.AddChildObject (usernode,query.Fields [1].AsString + '|' + query.Fields [2].AsString, nodedata) do begin
        if (nodedata.Active) then
          ImageIndex := 0
        else if (nodedata.Deleted) then
          ImageIndex := 6
        else
          ImageIndex := 8;

        SelectedIndex := ImageIndex;
      end;

      query.Next;
    end;
    query.Close;

    nodedata := TNodeDaten.Create;
    nodedata.NodeType := 0;

    GroupNode := UserGrpTreeView.Items.AddChildObject (Nil, GetResourceText (1448), nodedata);
    GroupNode.ImageIndex    := 2;
    GroupNode.SelectedIndex := GroupNode.ImageIndex;

    query.SQL.Clear;
    query.SQL.Add ('select g.REF, g.GROUPE_NAME, g.GROUPE_ID, g.GROUPE_ART from V_SYS_BEN_GRP g');

    if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then begin
      query.SQL.Add ('where (instr (PA_SYS_BENUTZER.GET_OBJECT_RECHTE (:ref_ben, g.REF_ACO), ''A'') is not null)');
      query.Parameters.ParamByName ('ref_ben').Value := LVSDatenModul.AktUserRef;
    end;

    query.SQL.Add ('order by g.GROUPE_ID');
    query.Open;

    while not (query.Eof) do begin
      nodedata := TNodeDaten.Create;
      nodedata.NodeType := 2;
      nodedata.Active   := TRUE;

      nodedata.Ref       := query.Fields [0].AsInteger;
      nodedata.NodeName  := query.Fields [1].AsString;
      nodedata.ID        := query.Fields [2].AsString;
      nodedata.IsKommGrp := query.Fields [3].AsString = 'KOMM';

      with UserGrpTreeView.Items.AddChildObject (GroupNode,query.Fields [2].AsString+'|'+query.Fields [1].AsString, nodedata) do begin
        if (query.Fields [3].AsString = 'ACO') Then
          ImageIndex    := 2
        else if (query.Fields [3].AsString = 'KOMM') Then
          ImageIndex    := 3
        else
          ImageIndex    := 2;

        SelectedIndex := ImageIndex;
      end;

      query.Next;
    end;

    query.Close;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.Prepare;
var
  query    : TADOQuery;
begin
  query := TADOQuery.Create (self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select * from V_SYS_FIRMA where STATUS=''AKT''');

    query.Open;

    while not (query.Eof) do begin
      FirmaComboBox.AddItem (query.FieldByName ('NAME').AsString+'|'+query.FieldByName ('BESCHREIBUNG').AsString, TComboBoxRef.Create (query.FieldByName ('REF').AsInteger));

      query.Next;
    end;
    
    query.Close;
  finally
    query.Free
  end;

  FirmaComboBox.Items.Insert (0, '');

  if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then
    FirmaComboBox.Enabled := False;

  LoadLocationCombobox (GrpLocComboBox);
  GrpLocComboBox.Items.Insert (0, GetResourceText (1004));

  if (AnsiUpperCase(LVSDatenModul.AktUser) = AnsiUpperCase(LVSDatenModul.Schema)) then begin
    AdminCheckBox.Enabled  := True;
    GrpLocComboBox.Enabled := True;
  end else begin
    AdminCheckBox.Enabled  := False;
    GrpLocComboBox.Enabled := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.PrintKommBarcodeMenuItemClick(Sender: TObject);
var
  res     : Integer;
  errtext : String;
begin
  if Assigned (UserGrpTreeView.Selected) and Assigned (UserGrpTreeView.Selected.Data) then begin
    if (TNodeDaten (UserGrpTreeView.Selected.Data).NodeType = 2) then begin
      if Assigned (PrintModule) then begin
        PrintModule.PreparePreview;

        res := PrintModule.PrintReport ('', -1, -1, '', 'KOMMUSER-BARCODE-LISTE', '', ['REF_GRP:' + IntToStr (TNodeDaten (UserGrpTreeView.Selected.Data).Ref)], errtext);

        PrintModule.BeginPreview;

        if (res <> 0) Then
          FrontendMessages.MessageDLG ('Fehler bei der Berichtausgabe.'+#13+#13+errtext, mtError, [mbOK], 0);
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.Show;
var
  query    : TADOQuery;
begin
  UpdateUserGrpTree;

  ACLFrame1.Prepare (LVSSecurityModule.ACOModul.ApplicationID, '');

  query := TADOQuery.Create (self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select IS_ADMIN from V_SYS_BEN where REF=:ref_ben');
    query.Parameters.ParamByName ('ref_ben').Value := LVSDatenModul.AktUserRef;

    try
      query.Open;

      if (query.Fields [0].IsNull) then
        fIsAdmin := False
      else
        fIsAdmin := (query.Fields [0].AsString [1] = '1');

      query.Close;
    except
      fIsAdmin := False
    end;
  finally
    query.Free;
  end;

  NewUserButton.Enabled          := fIsAdmin;
  NeuerBenutzer.Enabled          := fIsAdmin;
  SimilarBenutzerAnlegen.Enabled := fIsAdmin;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.ShowUserDaten (const Reference : Integer);
var
  i,
  ref_aco  : Integer;
  acoentry : TACOGridEntry;
  recht    : TLVSRechte;
  query    : TADOQuery;
begin
  ref_aco := -1;
  
  AktUserIndex := Reference;

  if (Reference = -1) then begin
    fAktUserID := '';

    UserIDEdit.Enabled := False;
    UserIDEdit.Text   := '';

    UserNumIDEdit.Enabled := False;
    UserNumIDEdit.Text   := '';

    UserNameEdit.Enabled := False;
    UserNameEdit.Text := '';

    ShortNameEdit.Enabled := False;
    ShortNameEdit.Text := '';

    UserNumIDEdit.Enabled := False;
    UserNumIDEdit.Text := '';

    UserFunkEdit.Enabled := False;
    UserFunkEdit.Text := '';

    UserMailEdit.Enabled := False;
    UserMailEdit.Text := '';

    LangComboBox.Enabled := False;
    LangComboBox.Text := '';

    FirmaComboBox.Enabled := False;
    FirmaComboBox.Text := '';

    DomComboBox.Enabled := False;
    DomComboBox.Text := '';

    DomUserEdit.Enabled := False;
    DomUserEdit.Text := '';

    AdminCheckBox.Enabled := False;

    ClearListBoxObjects (UserGrpListBox);

    UserGrpGroupBox.Enabled := False;

    ChangePasswort.Enabled := False;
    BenutzerSperren.Enabled := False;
    BenutzerFreigeben.Enabled := False;
    SimilarBenutzerAnlegen.Enabled := False;

    DisableACOButton;

    ACOStringGrid.Enabled := False;
    DelUserButton.Enabled := False;
  end else begin
    DomComboBox.Clear;
    DomComboBox.Items.Add ('');

    query := TADOQuery.Create (self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select distinct (NTS_DOMAINE) from V_SYS_BEN where NTS_DOMAINE is not null');
      try
        query.Open;

        while not (query.Eof) do begin
          DomComboBox.Items.Add (query.Fields [0].AsString);
          query.Next;
        end;
        query.Close;
      except
      end;


      UserIDEdit.Enabled := False;

      query.SQL.Clear;
      query.SQL.Add ('select * from V_SYS_BEN where REF=:ref');
      query.Parameters.ParamByName('ref').Value := AktUserIndex;

      ref_aco := -1;

      query.Open;

      try
        if (query.FieldByName ('REF_ACO').IsNull) then
          ref_aco := -1
        else ref_aco := query.FieldByName ('REF_ACO').AsInteger;

        fAktUserID := query.FieldByName ('USER_ID').AsString;

        UserIDEdit.Text       := query.FieldByName ('USER_ID').AsString;
        UserNumIDEdit.Text    := query.FieldByName ('USER_NUM_ID').AsString;
        UserNameEdit.Text     := query.FieldByName ('USER_NAME').AsString;

        if not Assigned (query.FindField ('USER_SHORT_NAME')) then begin
          ShortNameEdit.Text    := '';
          ShortNameEdit.Enabled := False;
        end else begin
          ShortNameEdit.Text    := query.FieldByName ('USER_SHORT_NAME').AsString;
          ShortNameEdit.Enabled := True;
        end;

        UserFunkEdit.Text     := query.FieldByName ('USER_TITLE').AsString;
        UserMailEdit.Text     := query.FieldByName ('EMAIL').AsString;
        LangComboBox.ItemIndex := LangComboBox.IndexOf (query.FieldByName ('SPRACHE').AsString);

        DomUserEdit.Text      := query.FieldByName ('NTS_USER').AsString;
        DomComboBox.ItemIndex := DomComboBox.Items.IndexOf (query.FieldByName ('NTS_DOMAINE').AsString);

        if (query.FieldByName ('IS_ADMIN').IsNull) then
          AdminCheckBox.Checked := False
        else AdminCheckBox.Checked := (query.FieldByName ('IS_ADMIN').AsString [1] = '1');

        if (query.FieldByName ('REF_FIRMA').IsNull) then
          FirmaComboBox.ItemIndex := 0
        else begin
          FirmaComboBox.ItemIndex := FindComboboxRef (FirmaComboBox, query.FieldByName ('REF_FIRMA').AsInteger);
          if (FirmaComboBox.ItemIndex = -1) then FirmaComboBox.ItemIndex := 0;
        end;

        query.Close;

        query.SQL.Clear;
        query.SQL.Add ('select bg.GROUPE_ID, bg.GROUPE_NAME from V_SYS_BEN_GRP bg, V_SYS_REL_BEN_GRP rbg where bg.REF=rbg.REF_GRP and rbg.REF_BEN=:ref order by bg.GROUPE_ID');
        query.Parameters.ParamByName('ref').Value := AktUserIndex;

        query.Open;

        UserGrpGroupBox.Enabled := True;
        ClearListBoxObjects (UserGrpListBox);

        while not (query.Eof) do begin
          UserGrpListBox.Items.Add (query.Fields [0].AsString+'|'+query.Fields [1].AsString);

          query.Next;
        end;
        query.Close;

        query.SQL.Clear;
        query.SQL.Add ('select a.BESCHREIBUNG, a.NAME, al.RECHTE_FLAGS, a.REF, al.REF_GRP from V_SYS_ACO_LIST al, V_SYS_ACO a where a.REF=al.REF_ACO and al.REF_BEN=:ref');
        query.SQL.Add ('and (a.TYP <> ''BEN'' or nvl ((select STATUS from V_SYS_BEN where REF_ACO=a.REF), ''ANG'')=''AKT'') order by a.OBJECT_NAME');
        query.Parameters.ParamByName('ref').Value := AktUserIndex;

        ClearGridObjects (ACOStringGrid);

        i := 0;

        try;
          query.Open;

          while not (query.Eof) do begin
            ACOStringGrid.Cells [1, ACOStringGrid.FixedRows + i] := '';
            ACOStringGrid.Cells [2, ACOStringGrid.FixedRows + i] := query.Fields [2].AsString;

            if (query.Fields [0].IsNull) then
              ACOStringGrid.Cells [3, ACOStringGrid.FixedRows + i] := query.Fields [1].AsString
            else ACOStringGrid.Cells [3, ACOStringGrid.FixedRows + i] := query.Fields [0].AsString;

            acoentry := TACOGridEntry.Create;
            acoentry.Ref    := query.Fields [3].AsInteger;
            acoentry.RefGrp := DBGetReferenz (query.Fields [4]);

            ACOStringGrid.Rows [ACOStringGrid.FixedRows + i].Objects [0] := acoentry;

            Inc (i);

            query.Next;
          end;

          query.Close;
        except
        end;

        query.SQL.Clear;
        query.SQL.Add ('select GRP_NAME, BESCHREIBUNG, ACO_NAME, RECHTE, REF_ACO from V_USER_ACO where REF_BEN=:ref order by Upper (BESCHREIBUNG)');
        query.Parameters.ParamByName('ref').Value := AktUserIndex;

        try
          query.Open;

          while not (query.Eof) do begin
            ACOStringGrid.Cells [1, ACOStringGrid.FixedRows + i] := query.Fields [0].AsString;
            ACOStringGrid.Cells [2, ACOStringGrid.FixedRows + i] := query.Fields [3].AsString;

            if (query.Fields [1].IsNull) then
              ACOStringGrid.Cells [3, ACOStringGrid.FixedRows + i] := query.Fields [2].AsString
            else ACOStringGrid.Cells [3, ACOStringGrid.FixedRows + i] := query.Fields [1].AsString;

            acoentry := TACOGridEntry.Create;
            acoentry.Ref := query.Fields [4].AsInteger;

            ACOStringGrid.Rows [ACOStringGrid.FixedRows + i].Objects [0] := acoentry;

            Inc (i);

            query.Next;
          end;

          query.Close;
        except
        end;
      finally
        query.Free;
      end;

      if (i > 0) Then
        ACOStringGrid.RowCount := ACOStringGrid.FixedRows + i
      else begin
        ACOStringGrid.RowCount := ACOStringGrid.FixedRows + 1;
        ACOStringGrid.Rows [1].Clear;
      end;

      ACOStringGridClick (ACOStringGrid);
    except
    end;

    if (ref_aco = -1) then
      recht := []
    else if (AnsiUpperCase(LVSDatenModul.AktUser) = AnsiUpperCase(LVSDatenModul.Schema)) then
      recht := [Admin]
    else GetUserRechte (LVSDatenModul.AktUser, ref_aco, recht);

    UserIDEdit.Enabled    := (Admin in recht);
    UserNumIDEdit.Enabled := (Admin in recht);
    ShortNameEdit.Enabled  := (Admin in recht);
    UserNameEdit.Enabled  := (Admin in recht);
    UserFunkEdit.Enabled  := (Admin in recht);
    UserMailEdit.Enabled  := (Admin in recht);
    DomComboBox.Enabled   := (Admin in recht);
    DomUserEdit.Enabled   := (Admin in recht);
    AdminCheckBox.Enabled := (Admin in recht);
    ACOStringGrid.Enabled := (Admin in recht);
    ChangePasswort.Enabled := (Admin in recht);
    BenutzerSperren.Enabled := (Admin in recht);
    BenutzerFreigeben.Enabled := (Admin in recht);
    SimilarBenutzerAnlegen.Enabled := (Admin in recht);

    DelUserButton.Enabled := fIsAdmin and (Admin in recht) and (UserIDEdit.Text<>LVSDatenModul.AktUser) and (UserIDEdit.Text<>LVSDatenModul.Schema);
  end;

  fUserChanged := False;
  SaveUserButton.Enabled := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.ShowGruppenDaten (const Reference : Integer);
var
  i        : Integer;
  acoentry : TACOGridEntry;
  query    : TADOQuery;
begin
  AktGrpIndex := Reference;

  if (Reference = -1) then begin
    GrpNameEdit.Text   := '';
    GrpBezEdit.Text   := '';

    GrpNameEdit.Enabled := False;
    GrpBezEdit.Enabled   := False;

    ClearListBoxObjects (GrpUserListBox);

    GrpUserGroupBox.Enabled := False;

    DisableACOButton;

    ACOStringGrid.Enabled := False;
    DelGrpButton.Enabled := False;
  end else begin
    ACOStringGrid.Enabled := True;

    query := TADOQuery.Create (self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select GROUPE_ID, GROUPE_NAME, REF_ACO, REF_LOCATION, GROUPE_ART, ADMIN_FLAG from V_SYS_BEN_GRP where REF=:ref');
      query.Parameters.ParamByName ('ref').Value := Reference;

      query.Open;
      GrpNameEdit.Text := query.Fields [0].AsString;
      GrpBezEdit.Text  := query.Fields [1].AsString;

      if (query.Fields [3].IsNull) then
        GrpLocComboBox.ItemIndex := 0
      else
        GrpLocComboBox.ItemIndex := FindComboboxRef(GrpLocComboBox, query.Fields [3].AsInteger);

      if (query.Fields [4].AsString = 'ACO') then
        GrpACORadioButton.Checked := True
      else if (query.Fields [4].AsString = 'KOMM') then
        GrpKOMMRadioButton.Checked := True
      else if (query.Fields [4].AsString = 'NACH') then
        GrpNACHRadioButton.Checked := True
      else if (query.Fields [4].AsString = 'TRANS') then
        GrpTRANSRadioButton.Checked := True
      else
        GrpACORadioButton.Checked := True;

      AdminGroupCheckBox.Checked := (query.Fields [5].AsString = '1');


      if CheckAdminRecht (LVSDatenModul.AktUser, query.Fields [2].AsInteger) then begin
        GrpNameEdit.Enabled  := True;
        GrpBezEdit.Enabled   := True;

        DelGrpButton.Enabled := True;
        AdminGroupCheckBox.Enabled := True;
      end else begin
        GrpNameEdit.Enabled  := False;
        GrpBezEdit.Enabled   := False;

        DelGrpButton.Enabled := False;
        AdminGroupCheckBox.Enabled := False;
      end;

      query.Close;

      query.SQL.Clear;
      query.SQL.Add ('select b.USER_ID, b.USER_NAME, b.STATUS from V_SYS_BEN b, V_SYS_REL_BEN_GRP rbg where b.REF=rbg.REF_BEN and rbg.REF_GRP=:ref_grp');
      query.Parameters.ParamByName ('ref_grp').Value := Reference;

      if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then begin
        query.SQL.Add ('and b.STATUS=''AKT'' and (instr (PA_SYS_BENUTZER.GET_OBJECT_RECHTE (:ref_ben, b.REF_ACO), ''A'') is not null) and Upper (b.USER_ID)<>:user_id');
        query.Parameters.ParamByName ('ref_ben').Value := LVSDatenModul.AktUserRef;
        query.Parameters.ParamByName ('user_id').Value := LVSDatenModul.Schema;
      end else begin
        if (ActiveUserCheckBox.Checked) then
          query.SQL.Add ('and b.STATUS=''AKT''');
      end;

      query.SQL.Add ('order by b.USER_ID');
      query.Open;

      GrpUserGroupBox.Enabled := True;

      ClearListBoxObjects (GrpUserListBox);

      while not (query.Eof) do begin
        if (query.Fields [2].AsString = 'DEL') then
          GrpUserListBox.AddItem (query.Fields [0].AsString + '|' + query.Fields [1].AsString, TComboBoxRef.Create (3))
        else if (query.Fields [2].AsString = 'AKT') then
          GrpUserListBox.AddItem (query.Fields [0].AsString + '|' + query.Fields [1].AsString, TComboBoxRef.Create (1))
        else if (query.Fields [2].AsString = 'ANG') then
          GrpUserListBox.AddItem (query.Fields [0].AsString + '|' + query.Fields [1].AsString, TComboBoxRef.Create (2))
        else
          GrpUserListBox.AddItem (query.Fields [0].AsString + '|' + query.Fields [1].AsString, TComboBoxRef.Create (0));

        query.Next;
      end;
      query.Close;

      ClearGridObjects (ACOStringGrid);

      i := 0;

      query.SQL.Clear;
      query.SQL.Add ('select GRP_NAME, BESCHREIBUNG, ACO_NAME, RECHTE, REF_ACO from V_GRP_ACO where REF_GRP=:ref_grp order by Upper (BESCHREIBUNG)');
      query.Parameters.ParamByName ('ref_grp').Value := Reference;

      try
        query.Open;

        while not (query.Eof) do begin
          ACOStringGrid.Cells [1, ACOStringGrid.FixedRows + i] := query.Fields [0].AsString;
          ACOStringGrid.Cells [2, ACOStringGrid.FixedRows + i] := query.Fields [3].AsString;

          if (query.Fields [1].IsNull) then
            ACOStringGrid.Cells [3, ACOStringGrid.FixedRows + i] := query.Fields [2].AsString
          else ACOStringGrid.Cells [3, ACOStringGrid.FixedRows + i] := query.Fields [1].AsString;

          acoentry := TACOGridEntry.Create;

          acoentry.Ref    := query.Fields [4].AsInteger;
          acoentry.RefGrp := -1;

          ACOStringGrid.Rows [ACOStringGrid.FixedRows + i].Objects [0] := acoentry;

          Inc (i);

          query.Next;
        end;

        query.Close;
      except
      end;
    finally
      query.Free;
    end;

    if (i > 0) Then
      ACOStringGrid.RowCount := ACOStringGrid.FixedRows + i
    else begin
      ACOStringGrid.RowCount := ACOStringGrid.FixedRows + 1;
      ACOStringGrid.Rows [1].Clear;
    end;

    ACOStringGridClick (ACOStringGrid);
  end;

  fGroupChanged := False;
  SaveGrpButton.Enabled := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.SaveUserButtonClick(Sender: TObject);
var
  res        : Integer;
  ok,
  found      : Boolean;
  passwd,
  usrname,
  dbusrname  : String;
  passwdform : TChangePasswdForm;
begin
  if (AktUserIndex <> -1) then begin
    ok := True;
    passwd := '';

    usrname := TrailingStripString (StripString (AnsiUpperCase (UserIDEdit.Text)));

    if (Length (usrname) > 2) and (usrname [1] = '"') and (usrname [Length (usrname)] = '"') then begin
      usrname := Copy (usrname, 2, Length (usrname) - 2);
      dbusrname := Copy (usrname, 2, Length (usrname) - 2);
    end else if (Length (LVSConfigModul.DBUserPrefix) > 0) then
      dbusrname := UpperCase (LVSConfigModul.DBUserPrefix) + '-' + usrname
    else
      dbusrname := usrname;

    res := CheckOrcaleUserExists (dbusrname, found);

    if (res = 0) and not (found) then begin
      passwdform := TChangePasswdForm.Create (Self);

      passwdform.Height := passwdform.Height - passwdform.OldPanel.Height;
      passwdform.OldPanel.Visible := False;
      passwdform.OldPanel.Height := 0;

      passwdform.UserLabel.Caption := usrname;
      passwdform.ChangeButton.Caption := 'Ok';

      if (passwdform.ShowModal <> mrOk) Then
        ok := False
      else begin
        ok := True;
        passwd := passwdform.NewPwEdit.Text;
      end;

      passwdform.Free;
    end;

    if (res = 0) and (ok) Then begin
      res := ChangeUser (AktUserIndex, GetComboBoxRef (FirmaComboBox), usrname, ShortNameEdit.Text, UserNumIDEdit.Text, UserNameEdit.Text, dbusrname, UserFunkEdit.Text, LangComboBox.GetItemText, passwd, DomComboBox.Text, DomUserEdit.Text, UserMailEdit.Text, AdminCheckBox.Checked);

      if (res <> 0) then
        FrontendMessages.MessageDLG (FormatMessageText (1257, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else begin
        ShowUserDaten (AktUserIndex);

        SaveUserButton.Enabled := False;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.NewUserButtonClick(Sender: TObject);
begin
  DoNewUser (-1);
end;

procedure TUserAdminFrame.OracleUserunlock1Click(Sender: TObject);
var
  dbuser,
  errtext : String;
begin
  if Assigned (UserGrpTreeView.Selected) and Assigned (UserGrpTreeView.Selected.Data) then begin
    if (Length (LVSConfigModul.DBUserPrefix) > 0) then
      dbuser := UpperCase (LVSConfigModul.DBUserPrefix) + '-' + TNodeDaten (UserGrpTreeView.Selected.Data).ID
    else
      dbuser := TNodeDaten (UserGrpTreeView.Selected.Data).ID;

    if (UnlockUser (dbuser, errtext) <> 0) then
      FrontendMessages.MessageDLG (FormatMessageText (1258, [errtext]), mtError, [mbOK], 0);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.DoNewUser (const SimilarUserIdx : Integer);
var
  res,
  idx,
  uref,
  uaco,
  ref_ma_firma : Integer;
  ok,
  found,
  delflag      : Boolean;
  passwd,
  usrname,
  dbuserstr    : String;
  recht        : TLVSRechte;
  newform      : TNewUserForm;
  passwdform   : TChangePasswdForm;
  query    : TADOQuery;
begin
  newform := TNewUserForm.Create (Self);

  newform.FirmaComboBox.Items.Assign (FirmaComboBox.Items);

  if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then begin
    newform.FirmaComboBox.Items.Delete (0);
    newform.FirmaComboBox.Enabled := False;
  end;

  if (LVSDatenModul.AktFirmaRef = -1) then
    newform.FirmaComboBox.ItemIndex := -1
  else begin
    newform.FirmaComboBox.ItemIndex := FindComboboxRef (newform.FirmaComboBox, LVSDatenModul.AktFirmaRef);
    if (newform.FirmaComboBox.ItemIndex = -1) then newform.FirmaComboBox.ItemIndex := -1;
  end;

  if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then begin
    newform.AdminCheckBox.Visible := False;
  end;

  if (SimilarUserIdx = -1) then
    newform.Caption := GetResourceText (1450)
  else newform.Caption := GetResourceText (1451);

  //Alle bisher verwendeten Doamins einfügen
  newform.DomComboBox.Items.Add ('');

  query := TADOQuery.Create (self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select distinct (NTS_DOMAINE) from V_SYS_BEN where NTS_DOMAINE is not null');
    try
      query.Open;

      while not (query.Eof) do begin
        newform.DomComboBox.Items.Add (query.Fields [0].AsString);
        query.Next;
      end;
      query.Close;
    except
    end;

    repeat
      if (newform.ShowModal = mrOk) then begin
        usrname := TrailingStripString (StripString (AnsiUpperCase (newform.UserIDEdit.Text)));

        res := CheckUserExists (usrname, found, delflag, uref);

        if (found) then begin
          ok := false;

          if not (delflag) then
            FrontendMessages.MessageDlg(FormatMessageText (1266, [usrname]), mtError, [mbOK], 0)
          else begin
            if (FrontendMessages.MessageDlg(FormatMessageText (1267, [usrname]), mtConfirmation, [mbYes, mbNo, mbCancel], 0) = mrYes) then begin
              if (ActivateUser (uref) = 0) then
                ok := True
              else
                FrontendMessages.MessageDLG (FormatMessageText (1268, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
            end;
          end;
        end else begin
          ok := True;
        end;

        if (ok) then begin
          if (Length (usrname) > 2) and (usrname [1] = '"') and (usrname [Length (usrname)] = '"') then begin
            usrname   := Copy (usrname, 2, Length (usrname) - 2);
            dbuserstr := usrname;
          end else if (Length (LVSConfigModul.DBUserPrefix) > 0) then
            dbuserstr := UpperCase (LVSConfigModul.DBUserPrefix) + '-' + usrname
          else
            dbuserstr := usrname;

          res := CheckOrcaleUserExists (dbuserstr, found);

          if (res = 0) and not (found) then begin
            passwdform := TChangePasswdForm.Create (Self);

            passwdform.Height := passwdform.Height - passwdform.OldPanel.Height;
            passwdform.OldPanel.Visible := False;
            passwdform.OldPanel.Height := 0;

            passwdform.UserLabel.Caption := usrname;
            passwdform.ChangeButton.Caption := 'Ok';

            if (passwdform.ShowModal <> mrOk) Then
              ok := False
            else begin
              ok := True;
              passwd := passwdform.NewPwEdit.Text;
            end;

            passwdform.Free;
          end else if (uref = -1) then begin
            if (FrontendMessages.MessageDlg(FormatMessageText (1259, [usrname]), mtConfirmation, [mbYes,mbNo,mbCancel], 0) <> mrYes) then begin
              ok := False;
            end;
          end;
        end;
      end else begin
        res := -9;
        ok := true;
      end;
    until (res <> 0) or ok;

    if (res = 0) and (ok) Then begin
      if (uref = -1) then begin
        res := CreateUser (GetComboBoxRef (newform.FirmaComboBox), usrname, newform.ShortNameEdit.Text, newform.UserNumIDEdit.Text, newform.UserNameEdit.Text, dbuserstr, newform.UserFunkEdit.Text, passwd, newform.DomComboBox.Text, newform.DomUserEdit.Text, newform.UserMailEdit.Text, newform.AdminCheckBox.Checked, uref);

        if (res <> 0) then
          FrontendMessages.MessageDLG (FormatMessageText (1260, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      end else begin
        res := ChangeUser (uref, GetComboBoxRef (newform.FirmaComboBox), usrname, newform.ShortNameEdit.Text, newform.UserNumIDEdit.Text, newform.UserNameEdit.Text, dbuserstr, newform.UserFunkEdit.Text, 'DE', passwd, newform.DomComboBox.Text, newform.DomUserEdit.Text, newform.UserMailEdit.Text, newform.AdminCheckBox.Checked);

        if (res <> 0) then
          FrontendMessages.MessageDLG (FormatMessageText (1257, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      end;

      if (res = 0) then begin
        if (GetComboBoxRef (newform.FirmaComboBox) <> -1) then begin
          query.SQL.Clear;
          query.SQL.Add('select REF_ACO from SYS_BEN where REF=:ref');
          query.Parameters.ParamByName ('ref').Value := uref;

          query.Open;

          if (query.RecordCount > 0) and not (query.Fields [0].IsNull) then
            uaco := query.Fields [0].AsInteger
          else
            uaco := -1;

          query.Close;

          if (uaco <> -1) then begin
            query.SQL.Clear;
            query.SQL.Add('select REF_ADMIN_GRP, REF_MASTER_FIRMA from V_SYS_FIRMA where REF=:ref');
            query.Parameters.ParamByName ('ref').Value := GetComboBoxRef (newform.FirmaComboBox);

            query.Open;

            if (query.RecordCount > 0) and not (query.Fields [0].IsNull) then begin
              res := SetGruppenRechte (query.Fields [0].AsInteger, uaco, [Grant, Admin]);

              if (res = 0) and not (query.Fields [1].IsNull) then begin
                ref_ma_firma := query.Fields [1].AsInteger;

                query.Close;

                query.SQL.Clear;
                query.SQL.Add('select REF_ADMIN_GRP from V_SYS_FIRMA where REF=:ref');
                query.Parameters.ParamByName ('ref').Value := ref_ma_firma;

                query.Open;

                if (query.RecordCount > 0) and not (query.Fields [0].IsNull) then
                  res := SetGruppenRechte (query.Fields [0].AsInteger, uaco, [Grant, Admin]);
              end;
            end;

            query.Close;
          end;
        end;

        if (res = 0) and (SimilarUserIdx <> -1) then begin
          query.SQL.Clear;
          query.SQL.Add('select REF_ACO, RECHTE_FLAGS from V_SYS_ACO_LIST where REF_BEN=:ref');
          query.Parameters.ParamByName ('ref').Value := SimilarUserIdx;

          query.Open;
          while not (query.Eof) do begin
            recht := [];

            if (Pos ('E', query.Fields [1].AsString) <> 0) then recht := recht + [Exec];
            if (Pos ('R', query.Fields [1].AsString) <> 0) then recht := recht + [Read];
            if (Pos ('W', query.Fields [1].AsString) <> 0) then recht := recht + [Write];
            if (Pos ('A', query.Fields [1].AsString) <> 0) then recht := recht + [Admin];
            if (Pos ('G', query.Fields [1].AsString) <> 0) then recht := recht + [Grant];

            res := SetUserRechte (usrname, query.Fields [0].AsInteger, recht);

            query.Next;
          end;
          query.Close;

          query.SQL.Clear;
          query.SQL.Add ('select bg.GROUPE_ID from V_SYS_BEN_GRP bg, V_SYS_REL_BEN_GRP rbg where bg.REF=rbg.REF_GRP and rbg.REF_BEN=:ref');
          query.Parameters.ParamByName ('ref').Value := AktUserIndex;

          query.Open;
          while not (query.Eof) do begin
            res := AddUserToGroup (usrname, query.Fields [0].AsString);

            query.Next;
          end;
          query.Close;
        end;

        UpdateUserGrpTree;

        AktUserIndex := -1;

        idx := 0;

        while (idx < UserGrpTreeView.Items.Count) and (AktUserIndex = -1) do begin
          if Assigned (UserGrpTreeView.Items [idx].Data) then begin
            if (TNodeDaten (UserGrpTreeView.Items [idx].Data).ID = usrname) then
              AktUserIndex := idx
          end;

          if (AktUserIndex = -1) then
            Inc (idx);
        end;

        if (AktUserIndex <> -1) then begin
          UserGrpTreeView.Selected := UserGrpTreeView.Items [AktUserIndex];

          ShowUserDaten (AktUserIndex);
        end;

        SaveUserButton.Enabled := False;
      end;
    end;
  finally
    query.Free;
  end;

  newform.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.FormCreate(Sender: TObject);
begin
  UserGroupBox.Visible := False;
  GrpGroupBox.Visible := False;

  LoadComboxDBItems (LangComboBox, 'SYS_TEXTE', 'SPRACHE');
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.UserGrpTreeViewChange(Sender: TObject; Node: TTreeNode);
begin
  LastNode := Node;

  if Assigned (Node) and Assigned (Node.Data) then begin
    with TNodeDaten (Node.Data) do begin
      if (NodeType = 0) then begin
        if (Node = UserNode) then begin
          GrpGroupBox.Visible := False;
          UserGroupBox.Visible := True;
          ShowUserDaten (-1);

          UserNode.Expand (True);
        end else begin
          UserGroupBox.Visible := False;
          GrpGroupBox.Visible := True;
          ShowGruppenDaten (-1);

          GroupNode.Expand (True);
        end;
      end else if (NodeType = 1) then begin
        UserGroupBox.Visible := True;
        GrpGroupBox.Visible := False;

        ShowUserDaten (Ref);
      end else if (NodeType = 2) then begin
        UserGroupBox.Visible := False;
        GrpGroupBox.Visible := True;

        ShowGruppenDaten (Ref);
      end else begin
       UserGroupBox.Visible := False;
       GrpGroupBox.Visible := False;
      end;
    end;
  end else begin
    UserGroupBox.Visible := False;
    GrpGroupBox.Visible := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.UserGrpTreeViewDeletion(Sender: TObject; Node: TTreeNode);
begin
  if Assigned (Node.Data) then
    TNodeDaten (Node.Data).Free;

  Node.Data := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.AddACOButtonClick(Sender: TObject);
var
  idx,
  objref : Integer;
  found  : Boolean;
  recht  : TLVSRechte;
begin
  if (ACLFrame1.ACOTreeView.Selected <> Nil) and (ACLFrame1.ACOTreeView.Selected.Data <> Nil) and (TACLNodeDaten (ACLFrame1.ACOTreeView.Selected.Data).ACORef <> 0) then begin
    objref := TACLNodeDaten (ACLFrame1.ACOTreeView.Selected.Data).ACORef;

    if Assigned (UserGrpTreeView.Selected) and Assigned (UserGrpTreeView.Selected.Data) then begin
      with TNodeDaten (UserGrpTreeView.Selected.Data) do begin
        if (NodeType = 1) then begin
          GetUserRecht (Ref, objref, recht);

          if (Sender = AddACOEButton) then
            recht := recht + [Exec]
          else if (Sender = AddACORButton) then
            recht := recht + [Read]
          else if (Sender = AddACOWButton) then
            recht := recht + [Write]
          else if (Sender = AddACOAButton) then
            recht := recht + [Admin]
          else if (Sender = AddACOGButton) then
            recht := recht + [Grant];

          SetUserRechte (ID, objref, recht);

          ShowUserDaten (AktUserIndex);
        end else if (NodeType = 2) then begin
          GetGruppenRechte (ID, objref, recht);

          if (Sender = AddACOEButton) then
            recht := recht + [Exec]
          else if (Sender = AddACORButton) then
            recht := recht + [Read]
          else if (Sender = AddACOWButton) then
            recht := recht + [Write]
          else if (Sender = AddACOAButton) then
            recht := recht + [Admin]
          else if (Sender = AddACOGButton) then
            recht := recht + [Grant];

          SetGruppenRechte (Ref, objref, recht);

          ShowGruppenDaten (AktGrpIndex);
        end;

        found := False;
        idx := ACOStringGrid.FixedRows;

        while (idx < ACOStringGrid.RowCount) and not (found) do begin
          if Assigned (ACOStringGrid.Rows [idx].Objects [0]) then begin
            if (TACOGridEntry (ACOStringGrid.Rows [idx].Objects [0]).Ref = objref) then
              found := True;
          end;

          if not (found) then Inc (idx);
        end;

        if (found) then begin
          ACOStringGrid.Selection := TGridrect (Rect (0,0,0,0));
          ACOStringGrid.Row := idx;
        end;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.DelACOButtonClick(Sender: TObject);
var
  recht  : TLVSRechte;
begin
  if (ACLFrame1.ACOTreeView.Selected <> Nil) and (ACLFrame1.ACOTreeView.Selected.Data <> Nil) and (TACLNodeDaten (ACLFrame1.ACOTreeView.Selected.Data).ACORef <> 0) then begin
    if Assigned (UserGrpTreeView.Selected) and Assigned (UserGrpTreeView.Selected.Data) then begin
      with TNodeDaten (UserGrpTreeView.Selected.Data) do begin
        if (NodeType = 1) then begin
          GetUserRecht (Ref, TACLNodeDaten (ACLFrame1.ACOTreeView.Selected.Data).ACORef, recht);

          if (Sender = DelACOEButton) then
            recht := recht - [Exec]
          else if (Sender = DelACORButton) then
            recht := recht - [Read]
          else if (Sender = DelACOWButton) then
            recht := recht - [Write]
          else if (Sender = DelACOAButton) then
            recht := recht - [Admin]
          else if (Sender = DelACOGButton) then
            recht := recht - [Grant];

          if (recht = []) then
            DropUserRechte (ID, TACLNodeDaten (ACLFrame1.ACOTreeView.Selected.Data).ACORef)
          else SetUserRechte (ID, TACLNodeDaten (ACLFrame1.ACOTreeView.Selected.Data).ACORef, recht);

          ShowUserDaten (AktUserIndex);
        end else if (NodeType = 2) then begin
          GetGruppenRechte (ID, TACLNodeDaten (ACLFrame1.ACOTreeView.Selected.Data).ACORef, recht);

          if (Sender = DelACOEButton) then
            recht := recht - [Exec]
          else if (Sender = DelACORButton) then
            recht := recht - [Read]
          else if (Sender = DelACOWButton) then
            recht := recht - [Write]
          else if (Sender = DelACOAButton) then
            recht := recht - [Admin]
          else if (Sender = DelACOGButton) then
            recht := recht - [Grant];

          if (recht = []) then
            DropGruppenRechte (ID, TACLNodeDaten (ACLFrame1.ACOTreeView.Selected.Data).ACORef)
          else SetGruppenRechte (Ref, TACLNodeDaten (ACLFrame1.ACOTreeView.Selected.Data).ACORef, recht);

          ShowGruppenDaten (AktGrpIndex);
        end;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.NewGrpButtonClick(Sender: TObject);
begin
  DoNewGroup;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.DoNewGroup;
var
  res,
  idx     : Integer;
  grpart  : String;
  selnode : TTreeNode;
  grpform : TNewUserGroupForm;
begin
  grpform := TNewUserGroupForm.Create (Self);

  LoadLocationCombobox (grpform.GrpLocComboBox);
  grpform.GrpLocComboBox.Items.Insert (0, GetResourceText (1004));

  grpform.GrpLocComboBox.ItemIndex := FindComboboxRef (grpform.GrpLocComboBox, LVSDatenModul.AktLocationRef);

  if (AnsiUpperCase(LVSDatenModul.AktUser) = AnsiUpperCase(LVSDatenModul.Schema)) then begin
    grpform.GrpLocComboBox.Enabled := True;
  end else begin
    grpform.GrpLocComboBox.Enabled := False;
  end;

  if (grpform.ShowModal = mrok) then begin
    if (grpform.GrpACORadioButton.Checked) then
      grpart := 'ACO'
    else if (grpform.GrpKOMMRadioButton.Checked) then
      grpart := 'KOMM'
    else if (grpform.GrpNACHRadioButton.Checked) then
      grpart := 'NACH'
    else if (grpform.GrpTRANSRadioButton.Checked) then
      grpart := 'TRANS'
    else
      grpart := 'ACO';

    res := CreateUserGroupe (grpart, grpform.GrpIDEdit.Text, grpform.GrpBezEdit.Text, GetComboBoxRef (grpform.GrpLocComboBox), grpform.AdminGroupCheckBox.Checked);

    if (res <> 0) then
      FrontendMessages.MessageDLG (FormatMessageText (1261, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    else begin
      UpdateUserGrpTree;

      idx := 0;
      selnode := Nil;

      while (idx < UserGrpTreeView.Items.Count) and (selnode = Nil) do begin
        if (UserGrpTreeView.Items [idx].Text = grpform.GrpIDEdit.Text) then
          selnode := UserGrpTreeView.Items [idx];

        Inc (idx);
      end;

      if (selnode <> Nil) then
        UserGrpTreeView.Selected := selnode;
    end;
  end;

  grpform.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.UserDataChange(Sender: TObject);
begin
  fUserChanged := True;
  SaveUserButton.Enabled := True;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.GrpDatenChange(Sender: TObject);
begin
  fGroupChanged := True;
  SaveGrpButton.Enabled := True;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.EnableACOButton (const ACOType : TACOType; const Rechte : TLVSRechte; const IsUserACO : Boolean);
begin
  AddACOGButton.Enabled := (Grant in Rechte);
  AddACOAButton.Enabled := (Admin in Rechte) and (ACOType in [acoBen, acoBenGrp, acoLager]);

  DelACOGButton.Enabled := IsUserACO and (Grant in Rechte);
  DelACOAButton.Enabled := IsUserACO and (Admin in Rechte);

  AddACOEButton.Enabled := ACOType in [acoMenu, acoQuery, acoButton, acoApp, acoACO];
  AddACORButton.Enabled := ACOType in [acoTab, acoPanel, acoMand, acoLoc, acoLager, acoForm, acoApp, acoBenGrp, acoACO, acoPrj, acoBes];
  AddACOWButton.Enabled := ACOType in [acoTab, acoPanel, acoMand, acoLoc, acoLager, acoForm, acoApp, acoACO, acoPrj, acoBes];

  if (Admin in Rechte) then begin
    DelACOEButton.Enabled := IsUserACO;
    DelACORButton.Enabled := IsUserACO;
    DelACOWButton.Enabled := IsUserACO;
  end else begin
    AddACOEButton.Enabled := AddACOEButton.Enabled and (Exec in Rechte);
    AddACORButton.Enabled := AddACORButton.Enabled and (Read in Rechte);
    AddACOWButton.Enabled := AddACOWButton.Enabled and (Write in Rechte);

    DelACOEButton.Enabled := IsUserACO and (Exec in Rechte);
    DelACORButton.Enabled := IsUserACO and (Read in Rechte);
    DelACOWButton.Enabled := IsUserACO and (Write in Rechte);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.DisableACOButton;
begin
  AddACOEButton.Enabled := False;
  AddACORButton.Enabled := False;
  AddACOWButton.Enabled := False;
  AddACOAButton.Enabled := False;
  AddACOGButton.Enabled := False;

  DelACOEButton.Enabled := False;
  DelACORButton.Enabled := False;
  DelACOWButton.Enabled := False;
  DelACOAButton.Enabled := False;
  DelACOGButton.Enabled := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.ACOStringGridClick(Sender: TObject);
var
  idx,
  ref     : Integer;
  acoref  : Integer;
  selnode : TTreeNode;
  recht   : TLVSRechte;
begin
  if (ACOStringGrid.Focused) then begin
    selnode := Nil;

    if (ACOStringGrid.Row = -1) or (ACOStringGrid.Rows [ACOStringGrid.Row].Objects [0] = Nil) then
      ref := 0
    else ref := TACOGridEntry (ACOStringGrid.Rows [ACOStringGrid.Row].Objects [0]).Ref;

    if (ref > 0) Then begin
      with ACLFrame1 do begin
        idx := 0;

        while (idx < ACOTreeView.Items.Count) and (selnode = Nil) do begin
          if Assigned (ACOTreeView.Items [idx].Data) then begin
            if (TACLNodeDaten (ACOTreeView.Items [idx].Data).ACORef = ref) then
              selnode := ACOTreeView.Items [idx];
          end;

          Inc (idx);
        end;
      end;
    end;

    ACLFrame1.ACOTreeView.Selected := selnode;

    if (selnode = Nil) Then
      DisableACOButton
    else if (TACOGridEntry (ACOStringGrid.Rows [ACOStringGrid.Row].Objects [0]).RefGrp <> -1) then
      DisableACOButton
    else if Assigned (UserGrpTreeView.Selected) and Assigned (UserGrpTreeView.Selected.Data) then begin
      if Assigned (selnode.Data) then begin
        acoref := TACLNodeDaten (selnode.Data).ACORef;

        with TNodeDaten (UserGrpTreeView.Selected.Data) do begin
          if (NodeType = 1) then begin
            GetRechte (LVSDatenModul.AktUserRef, acoref, recht);
            EnableACOButton (TACLNodeDaten (selnode.Data).ACOType, recht, True);
          end else begin
            GetRechte (LVSDatenModul.AktUserRef, acoref, recht);
            EnableACOButton (TACLNodeDaten (selnode.Data).ACOType, recht, True);
          end;
        end;
      end else DisableACOButton;
    end else DisableACOButton;
  end;
end;

procedure TUserAdminFrame.ACOStringGridEnter(Sender: TObject);
var
  ref,
  idx   : Integer;
  found : Boolean;
begin
  if Assigned (ACLFrame1.ACOTreeView.Selected) and Assigned (ACLFrame1.ACOTreeView.Selected.Data) then
    ref := TACLNodeDaten (ACLFrame1.ACOTreeView.Selected.Data).ACORef
  else ref := -1;

  if (ref <= 0) Then begin
    ACOStringGrid.Selection := TGridrect (Rect (0,0,0,0));
    ACOStringGrid.Row       := ACOStringGrid.FixedRows;
  end else begin
    found := False;
    idx := ACOStringGrid.FixedRows;

    while (idx < ACOStringGrid.RowCount) and not (found) do begin
      if Assigned (ACOStringGrid.Rows [idx].Objects [0]) then begin
        if (TACOGridEntry (ACOStringGrid.Rows [idx].Objects [0]).Ref = ref) then
          found := True;
      end;

      if not (found) then Inc (idx);
    end;

    if (found) then begin
      ACOStringGrid.Selection := TGridrect (Rect (0,0,0,0));
      ACOStringGrid.Row := idx
    end else begin
      ACOStringGrid.Selection := TGridrect (Rect (0,0,0,0));
      ACOStringGrid.Row       := ACOStringGrid.FixedRows;
    end;

    if (ACOStringGrid.Row < ACOStringGrid.TopRow) or (ACOStringGrid.Row > (ACOStringGrid.TopRow + ACOStringGrid.VisibleRowCount)) then begin
      if (ACOStringGrid.Row < ACOStringGrid.VisibleRowCount) then
        ACOStringGrid.TopRow := 0
      else ACOStringGrid.TopRow := ACOStringGrid.Row - (ACOStringGrid.VisibleRowCount div 2);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.ActiveUserCheckBoxClick(Sender: TObject);
begin
  UpdateUserGrpTree;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.UserGrpListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
var
  line   : String;
  strpos : Integer;
  Icon   : TIcon;
begin
  line := (Control as TListBox).Items [Index];
  strpos := Pos ('|', line);

  (Control as TListBox).Canvas.FillRect(Rect);

  Icon  := TIcon.Create;

  try
    ImageList1.GetIcon (2, Icon);

    (Control as TListBox).Canvas.Draw (Rect.Left + 2, Rect.Top, Icon);
  finally
    Icon.Free;
  end;

  (Control as TListBox).Canvas.TextOut (Rect.Left + ImageList1.Width + 4,       Rect.Top + 2, Copy (line,1,strpos - 1));
  (Control as TListBox).Canvas.TextOut (Rect.Left + ImageList1.Width + 4 + 100, Rect.Top + 2, Copy (line,strpos + 1, Length (line) - strpos));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.GrpUserListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
var
  line   : String;
  strpos : Integer;
  Icon   : TIcon;
begin
  line := (Control as TListBox).Items [Index];
  strpos := Pos ('|', line);

  (Control as TListBox).Canvas.FillRect(Rect);

  if Assigned ((Control as TListBox).Items.Objects [Index]) then begin
    Icon  := TIcon.Create;

    try
      if (TComboBoxRef ((Control as TListBox).Items.Objects [Index]).Ref = 1) Then
        ImageList1.GetIcon (0, Icon)
      else if (TComboBoxRef ((Control as TListBox).Items.Objects [Index]).Ref = 2) Then
        ImageList1.GetIcon (6, Icon)
      else if (TComboBoxRef ((Control as TListBox).Items.Objects [Index]).Ref = 3) Then
        ImageList1.GetIcon (8, Icon);

      (Control as TListBox).Canvas.Draw (Rect.Left + 2, Rect.Top, Icon);
    finally
      Icon.Free;
    end;
  end;

  (Control as TListBox).Canvas.TextOut (Rect.Left + ImageList1.Width + 4, Rect.Top + 2, Copy (line,1,strpos - 1));
  (Control as TListBox).Canvas.TextOut (Rect.Left + ImageList1.Width + 4 + 100, Rect.Top + 2, Copy (line,strpos + 1, Length (line) - strpos));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.Button5Click(Sender: TObject);
var
  selform : TSelectUserGroupeForm;
  grpname : String;
  res,
  idx,
  strpos  : Integer;
begin
  res := 0;

  selform := TSelectUserGroupeForm.Create (Self);
  selform.Prepare (GetResourceText (1452), AktUserIndex);

  if (selform.ShowModal = mrOk) then begin
    idx := 0;

    while (res = 0) and (idx < selform.GrpListBox.Items.Count) do begin
      if (selform.GrpListBox.Selected [idx]) then begin
        grpname := selform.GrpListBox.Items [idx];

        strpos := Pos ('|', grpname);
        if (strpos > 0) Then
          grpname := Copy (grpname, 1, strpos - 1);

        res := AddUserToGroup (UserIDEdit.Text, grpname);
      end;

      Inc (idx);
    end;

    if (res = 0) Then
      ShowUserDaten (AktUserIndex)
    else FrontendMessages.MessageDLG (FormatMessageText (1262, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
  end;

  selform.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.Button6Click(Sender: TObject);
var
  res,
  strpos  : Integer;
  grpname : String;
begin
  if (UserGrpListBox.ItemIndex <> -1) then begin
    grpname := UserGrpListBox.Items [UserGrpListBox.ItemIndex];

    strpos := Pos ('|', grpname);
    if (strpos > 0) Then
      grpname := Copy (grpname, 1, strpos - 1);

    res := RemoveUserFromGroup (UserIDEdit.Text, grpname);

    if (res = 0) Then
      ShowUserDaten (AktUserIndex);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.SaveGrpButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (AktGrpIndex <> -1) then begin
    res := ChangeUserGroupe (AktGrpIndex, GrpNameEdit.Text, GrpBezEdit.Text, GetComboBoxRef (GrpLocComboBox), AdminGroupCheckBox.Checked);

    if (res <> 0) then
      FrontendMessages.MessageDLG (FormatMessageText (1263, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    else begin
      UpdateUserGrpTree;

      SaveGrpButton.Enabled := False;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.ACLFrame1ACOTreeViewChange(Sender: TObject; Node: TTreeNode);
var
  ref,
  idx   : Integer;
  found : Boolean;
  recht : TLVSRechte;
begin
  if not (ACOStringGrid.Focused) then begin
    found := False;

    if Assigned (Node) and Assigned (Node.Data) then
      ref := TACLNodeDaten (Node.Data).ACORef
    else ref := 0;

    if (ref = 0) Then
      EnableACOButton (acoNone, [], True)
    else begin
      found := False;
      idx := ACOStringGrid.FixedRows;

      while (idx < ACOStringGrid.RowCount) and not (found) do begin
        if Assigned (ACOStringGrid.Rows [idx].Objects [0]) then begin
          if (TACOGridEntry (ACOStringGrid.Rows [idx].Objects [0]).Ref = ref) then
            found := True;
        end;

        if not (found) then Inc (idx);
      end;

      if (found) then begin
        ACOStringGrid.Selection := TGridrect (Rect (0,0,0,0));
        ACOStringGrid.Row := idx;

        if (ACOStringGrid.Row < ACOStringGrid.TopRow) or (ACOStringGrid.Row > (ACOStringGrid.TopRow + ACOStringGrid.VisibleRowCount)) then begin
          if (ACOStringGrid.Row < ACOStringGrid.VisibleRowCount) then
            ACOStringGrid.TopRow := 0
          else ACOStringGrid.TopRow := ACOStringGrid.Row - (ACOStringGrid.VisibleRowCount div 2);
        end;
      end else begin
        ACOStringGrid.Selection := TGridrect (Rect (-1,-1,-1,-1));
        ACOStringGrid.Row := -1;
      end;

      GetRechte (LVSDatenModul.AktUserRef, TACLNodeDaten (Node.Data).ACORef, recht);

      if (found) then
        EnableACOButton (TACLNodeDaten (Node.Data).ACOType, recht, False)
      else if not (Assigned (ACOStringGrid.Rows [idx].Objects [0])) then
        EnableACOButton (TACLNodeDaten (Node.Data).ACOType, recht, False)
      else
        EnableACOButton (TACLNodeDaten (Node.Data).ACOType, recht, (TACOGridEntry (ACOStringGrid.Rows [idx].Objects [0]).RefGrp = -1))
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.UserIDEditKeyPress(Sender: TObject; var Key: Char);
var
  keystr : string;
begin
  keystr := Key;
  Key := AnsiUpperCase (keystr) [1];
end;

procedure TUserAdminFrame.UserNumIDEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (CharInSet (Key, ['0'..'9',#8,#9,^V,^C,^X])) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.PopupMenu1Popup(Sender: TObject);
begin
  if (UserGrpTreeView.Selected <> LastNode) then
    UserGrpTreeViewChange (UserGrpTreeView, UserGrpTreeView.Selected);

  NeuerBenutzer.Visible := False;
  NeueGruppe.Visible := False;
  ChangePasswort.Visible := False;
  BenutzerSperren.Visible := False;
  BenutzerFreigeben.Visible := False;
  Benutzerlschen1.Visible := False;
  Benutzerrechtesynchronisieren1.Visible := False;
  SimilarBenutzerAnlegen.Visible := False;
  PrintKommBarcodeMenuItem.Visible := False;

  if Assigned (UserGrpTreeView.Selected) and Assigned (UserGrpTreeView.Selected.Data) then begin
    with TNodeDaten (UserGrpTreeView.Selected.Data) do begin
      if (NodeType = 0) then begin
        if (UserGrpTreeView.Selected = UserNode) then begin
          NeuerBenutzer.Visible := True;
        end else begin
          NeueGruppe.Visible := True;
        end;
      end else if (NodeType = 1) then begin
        NeuerBenutzer.Visible := True;
        ChangePasswort.Visible := True;
        BenutzerSperren.Visible := True;
        BenutzerFreigeben.Visible := True;
        SimilarBenutzerAnlegen.Visible := True;
        Benutzerlschen1.Visible := True;
        Benutzerrechtesynchronisieren1.Visible := LVSDatenModul.ViewExits ('V_SYS_ACO');
      end else if (NodeType = 2) then begin
        NeueGruppe.Visible := True;
        PrintKommBarcodeMenuItem.Visible := True;
        PrintKommBarcodeMenuItem.Enabled := Assigned (PrintModule) and IsKommGrp or (Copy (ID, 1, 8) = 'KommUser');
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.NeuerBenutzerClick(Sender: TObject);
begin
  DoNewUser (-1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.NeueGruppeClick(Sender: TObject);
begin
  DoNewGroup;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.UserGrpTreeViewMouseUp(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
begin
  PostMessage (Handle, WM_USER + 1, 0, 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.ChangeSelection (var Message: TMessage);
begin
  if (UserGrpTreeView.Selected <> LastNode) then
    UserGrpTreeViewChange (UserGrpTreeView, UserGrpTreeView.Selected);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.ChangePasswortClick(Sender: TObject);
var
  res        : Integer;
  found      : Boolean;
  username,
  dbusername : String;
  passwdform : TChangePasswdForm;
begin
  passwdform := TChangePasswdForm.Create (Self);

  passwdform.Height := passwdform.Height - passwdform.OldPanel.Height;
  passwdform.OldPanel.Visible := False;
  passwdform.OldPanel.Height := 0;

  passwdform.UserLabel.Caption := UserIDEdit.Text;
  passwdform.ChangeButton.Caption := GetResourceText (1453);

  if (passwdform.ShowModal = mrOk) Then begin
    with passwdform do begin
      username := UserIDEdit.Text;

      if (Length (username) > 2) and (username [1] = '"') and (username [Length (username)] = '"') then begin
        username   := Copy (username, 2, Length (username) - 2);
        dbusername := username;
      end else if (Length (LVSConfigModul.DBUserPrefix) > 0) then
        dbusername := UpperCase (LVSConfigModul.DBUserPrefix) + '-' + username
      else
        dbusername := username;

      res := CheckOrcaleUserExists (dbusername, found);

      if (res = 0) and not (found) then begin
        res := ChangeUser (AktUserIndex, GetComboBoxRef (FirmaComboBox), username, ShortNameEdit.Text, UserNumIDEdit.Text, UserNameEdit.Text, dbusername, UserFunkEdit.Text, LangComboBox.GetItemText, NewPwEdit.Text, DomComboBox.Text, DomUserEdit.Text, UserMailEdit.Text, AdminCheckBox.Checked);
      end else begin
        if (ResetUserPassword (username, dbusername, NewPwEdit.Text) <> 0) then
          FrontendMessages.MessageDLG (FormatMessageText (1264, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      end;
    end;
  end;

  passwdform.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.UserGrpTreeViewAdvancedCustomDrawItem(
  Sender: TCustomTreeView; Node: TTreeNode; State: TCustomDrawState;
  Stage: TCustomDrawStage; var PaintImages, DefaultDraw: Boolean);
var
  line    : String;
  strpos  : Integer;
  rect,
  delrect : TRect;
begin
  if Assigned (Node.Data) Then begin
    if (TNodeDaten (Node.Data).NodeType = 1) or (TNodeDaten (Node.Data).NodeType = 2) Then begin
      if (Stage = cdPostPaint) then begin
        rect := Node.DisplayRect(true);
        delrect := rect;
        delrect.Top := delrect.Top;
        delrect.Right := delrect.Left + 200;
        delrect.Bottom := delrect.Bottom;

        line := Node.Text;
        strpos := Pos ('|', line);

        if Node.Selected then begin
          Sender.Canvas.Brush.Color := clHighlight;

          if (TNodeDaten (Node.Data).Active) then
            Sender.Canvas.Font.Color := clHighlightText
          else Sender.Canvas.Font.Color := clGray;
        end else begin
          Sender.Canvas.Brush.Color := (Sender as TTreeView).Color;

          if (TNodeDaten (Node.Data).Active) then
            Sender.Canvas.Font.Color := (Sender as TTreeView).Font.Color
          else Sender.Canvas.Font.Color := clGray;
        end;

        with Sender.Canvas do begin
          FillRect(delrect);

          TextOut (rect.Left + 2, rect.Top + 1, Copy (line,1,strpos - 1));
          TextOut (rect.Left +100, rect.Top + 1, Copy (line,strpos + 1, Length (line) - strpos));
        end;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.Button1Click(Sender: TObject);
var
  res,
  strpos   : Integer;
  username : String;
begin
  if (GrpUserListBox.ItemIndex <> -1) then begin
    username := GrpUserListBox.Items [GrpUserListBox.ItemIndex];

    strpos := Pos ('|', username);
    if (strpos > 0) Then
      username := Copy (username, 1, strpos - 1);

    res := RemoveUserFromGroup (username, GrpNameEdit.Text);

    if (res = 0) Then
      ShowGruppenDaten (AktGrpIndex);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.BenutzerFreigebenClick(Sender: TObject);
var
  res : Integer;
begin
  if Assigned (UserGrpTreeView.Selected) and Assigned (UserGrpTreeView.Selected.Data) then begin
    res := ActivateUser (TNodeDaten (UserGrpTreeView.Selected.Data).Ref);

    if (res = 0) then begin
      TNodeDaten (UserGrpTreeView.Selected.Data).Active  := True;
      TNodeDaten (UserGrpTreeView.Selected.Data).Deleted := False;

      UserGrpTreeView.Selected.ImageIndex    := 0;
      UserGrpTreeView.Selected.SelectedIndex := UserGrpTreeView.Selected.ImageIndex;
    end else begin
      FrontendMessages.MessageDlg(FormatMessageText (1268, []), mtError, [mbOK], 0);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.BenutzerDeleteClick(Sender: TObject);
var
  res  : Integer;
  node : TTreeNode;
begin
  if Assigned (UserGrpTreeView.Selected) and Assigned (UserGrpTreeView.Selected.Data) then begin
    if (FrontendMessages.MessageDlg (FormatMessageText (1253, [TNodeDaten (UserGrpTreeView.Selected.Data).NodeName]), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then begin
      res := DeleteUser (TNodeDaten (UserGrpTreeView.Selected.Data).Ref);

      if (res = 0) then begin
        node := UserGrpTreeView.Selected;

        if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then begin
          if UserGrpTreeView.Selected.GetNext <> Nil then
            UserGrpTreeView.Selected := UserGrpTreeView.Selected.GetNext
          else
            UserGrpTreeView.Selected := UserGrpTreeView.Selected.GetPrev;

          UserGrpTreeView.Items.Delete (node);
        end else begin
          TNodeDaten (node.Data).Deleted := True;

          node.ImageIndex := 6;
          node.SelectedIndex := node.ImageIndex;
        end;
      end else begin
        FrontendMessages.MessageDlg (FormatMessageText (1254, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.Benutzerrechtesynchronisieren1Click(Sender: TObject);
var
  idx,
  res       : Integer;
  recht     : TLVSRechte;
  syncdata  : TACOSyncData;
  synchform : TSynchUserACOForm;
  query    : TADOQuery;
begin
  if Assigned (UserGrpTreeView.Selected) and Assigned (UserGrpTreeView.Selected.Data) and (TNodeDaten (UserGrpTreeView.Selected.Data).NodeType = 1) then begin
    synchform := TSynchUserACOForm.Create (Self);

    synchform.BenRef         := TNodeDaten (UserGrpTreeView.Selected.Data).Ref;
    synchform.Label2.Caption := TNodeDaten (UserGrpTreeView.Selected.Data).ID + ' ('+ TNodeDaten (UserGrpTreeView.Selected.Data).NodeName+')';

    query := TADOQuery.Create (self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Clear;
      query.SQL.Add ('select b.REF, b.USER_NAME, b.USER_ID from V_SYS_BEN b where b.STATUS=''AKT'' and b.REF<>:ref');
      query.Parameters.ParamByName ('ref').Value := TNodeDaten (UserGrpTreeView.Selected.Data).Ref;

      //Normal sterbliche dürfen den Schemabesitzer nicht sehen ;-)
      if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then begin
        query.SQL.Add ('and (instr (PA_SYS_BENUTZER.GET_OBJECT_RECHTE (:ref_ben, b.REF_ACO), ''A'') is not null) and Upper (b.USER_ID)<>:user_id');
        query.Parameters.ParamByName ('ref_ben').Value := LVSDatenModul.AktUserRef;
        query.Parameters.ParamByName ('user_id').Value := LVSDatenModul.Schema;
      end;

      query.SQL.Add ('order by b.USER_ID');

      query.Open;

      while not (query.Eof) do begin
        synchform.SourceBenComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create(query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    finally
      query.Free;
    end;

    synchform.SourceBenComboBox.ItemIndex := 0;
    synchform.SourceBenComboBox.OnChange (synchform.SourceBenComboBox);

    if (synchform.ShowModal = mrOk) then begin
      if (TNodeDaten (UserGrpTreeView.Selected.Data).NodeType = 1) then begin
        res := 0;

        idx := 0;

        while (idx < synchform.CopyACOListBox.Items.Count) and (res = 0) do begin
          syncdata := TACOSyncData (synchform.CopyACOListBox.Items.Objects [idx]);

          if (syncdata.Ref > 0) then begin
            recht := [];

            if (Pos ('E', syncdata.RechtStr) > 0) then
              recht := recht + [Exec];
            if (Pos ('R', syncdata.RechtStr) > 0) then
              recht := recht + [Read];
            if (Pos ('W', syncdata.RechtStr) > 0) then
              recht := recht + [Write];
            if (Pos ('A', syncdata.RechtStr) > 0) then
              recht := recht + [Admin];
            if (Pos ('G', syncdata.RechtStr) > 0) then
              recht := recht + [Grant];

            res := SetUserRechte (TNodeDaten (UserGrpTreeView.Selected.Data).ID, syncdata.Ref, recht);
          end else if (syncdata.GrpRef > 0) then begin
            res := AddUserToGroup (TNodeDaten (UserGrpTreeView.Selected.Data).ID, syncdata.GrpID);
          end;

          Inc (idx);
        end;
      end;

      ShowUserDaten (AktUserIndex);
    end;

    synchform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.BenutzerSperrenClick(Sender: TObject);
var
  res : Integer;
begin
  if Assigned (UserGrpTreeView.Selected) and Assigned (UserGrpTreeView.Selected.Data) then begin
    res := DeactivateUser (TNodeDaten (UserGrpTreeView.Selected.Data).Ref);

    if (res = 0) then begin
      TNodeDaten (UserGrpTreeView.Selected.Data).Active := False;

      UserGrpTreeView.Selected.ImageIndex    := 8;
      UserGrpTreeView.Selected.SelectedIndex := UserGrpTreeView.Selected.ImageIndex;
    end else begin
      FrontendMessages.MessageDlg(FormatMessageText (1265, []), mtError, [mbOK], 0);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.FormShow(Sender: TObject);
begin
  LastNode := Nil;

  if (AnsiUpperCase(LVSDatenModul.AktUser) = AnsiUpperCase(LVSDatenModul.Schema)) then
    AdminCheckBox.Enabled := True
  else AdminCheckBox.Enabled := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
begin
  if (Key = VK_F5) then
    UpdateUserGrpTree;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.FormResize(Sender: TObject);
begin
  ACOStringGrid.ColWidths [1] := (ACOStringGrid.ClientWidth * 20) div 100;
  ACOStringGrid.ColWidths [2] := (ACOStringGrid.ClientWidth * 20) div 100;
  ACOStringGrid.ColWidths [3] := (ACOStringGrid.ClientWidth * 60) div 100;

  NewUserButton.Left  := UserGroupBox.ClientWidth - NewUserButton.Width - 8;
  SaveUserButton.Left := UserGroupBox.ClientWidth - SaveUserButton.Width - 8;
  DelUserButton.Left  := UserGroupBox.ClientWidth - DelUserButton.Width - 8;

  UserGroupBox.Left   := UserGrpTreeView.Left + UserGrpTreeView.Width + 16;
  UserGroupBox.Top    := UserGrpTreeView.Top;
  UserGroupBox.Width  := ClientWidth - UserGroupBox.Left;
  UserGroupBox.Height := UserGrpTreeView.Height;

  ShortNameEdit.Left  := NewUserButton.Left - ShortNameEdit.Width - 16;
  Label16.Left := ShortNameEdit.Left;

  UserNumIDEdit.Left  := UserIDEdit.Left + UserIDEdit.Width + 8;
  UserNumIDEdit.Width := ShortNameEdit.Left - UserNumIDEdit.Left - 8;
  Label15.Left := UserNumIDEdit.Left;

  GrpGroupBox.Left   := UserGrpTreeView.Left + UserGrpTreeView.Width + 16;
  GrpGroupBox.Top    := UserGrpTreeView.Top;
  GrpGroupBox.Width  := ClientWidth - UserGroupBox.Left;
  GrpGroupBox.Height := UserGrpTreeView.Height;

  NewGrpButton.Left  := GrpGroupBox.ClientWidth - NewGrpButton.Width - 8;
  SaveGrpButton.Left := GrpGroupBox.ClientWidth - SaveGrpButton.Width - 8;
  DelGrpButton.Left  := GrpGroupBox.ClientWidth - DelGrpButton.Width - 8;

  GrpUserGroupBox.Width := GrpGroupBox.ClientWidth - GrpUserGroupBox.Left - 8;
  Button1.Left := GrpUserGroupBox.ClientWidth - Button1.Width - 8;
  GrpUserListBox.Width := Button1.Left - GrpUserListBox.Left - 8;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.SimilarBenutzerAnlegenClick(Sender: TObject);
begin
  DoNewUser (AktUserIndex);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.UserGrpTreeViewMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
var
  node : TTreeNode;
  clp  : TPoint;
begin
  if (Button = mbRight) then begin
    node := (Sender as TTreeView).GetNodeAt (x, y);

    if Assigned (node) then begin
      (Sender as TTreeView).Selected := node;
//      (Sender as TTreeView).Update;
    end;

    if Assigned ((Sender as TTreeView).PopupMenu) then begin
      GetCursorPos(clp);

      (Sender as TTreeView).PopupMenu.Popup(clp.X, clp.Y);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.DelUserButtonClick(Sender: TObject);
var
  res     : Integer;
  delnode : TTreeNode;
begin
  if Assigned (UserGrpTreeView.Selected) and Assigned (UserGrpTreeView.Selected.Data) then begin
    if (FrontendMessages.MessageDlg(FormatMessageText (1253, [TNodeDaten (UserGrpTreeView.Selected.Data).NodeName]), mtConfirmation, [mbYes, mbNo, mbCancel], 0) = mrYes) then begin
      res := DeleteUser (TNodeDaten (UserGrpTreeView.Selected.Data).Ref);

      if (res <> 0) then
        FrontendMessages.MessageDlg(FormatMessageText (1254, []), mtError, [mbOK], 0)
      else begin
        delnode := UserGrpTreeView.Selected;

        UserGrpTreeView.Selected := UserGrpTreeView.FindNextToSelect;

        UserGrpTreeView.Items.Delete(delnode);
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TUserAdminFrame.DelGrpButtonClick(Sender: TObject);
var
  res     : Integer;
  delnode : TTreeNode;
begin
  if Assigned (UserGrpTreeView.Selected) and Assigned  (UserGrpTreeView.Selected.Data) then begin
    if (TNodeDaten (UserGrpTreeView.Selected.Data).NodeType = 2) and (TNodeDaten (UserGrpTreeView.Selected.Data).Ref <> -1) then begin
      if (FrontendMessages.MessageDlg(FormatMessageText (1255, [TNodeDaten (UserGrpTreeView.Selected.Data).NodeName]), mtConfirmation, [mbYes, mbNo, mbCancel], 0) = mrYes) then begin
        res := DeleteUserGroupe (TNodeDaten (UserGrpTreeView.Selected.Data).Ref);

        if (res <> 0) then
          FrontendMessages.MessageDLG (FormatMessageText (1256, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
        else begin
          delnode := UserGrpTreeView.Selected;

          UserGrpTreeView.Selected := UserGrpTreeView.FindNextToSelect;

          UserGrpTreeView.Items.Delete(delnode);
        end;
      end;
    end;
  end;
end;

end.
