﻿unit StoreLogixSplashScreen;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ComCtrls, ExtCtrls;

type
  TSplashThread = class (TThread)
  private
  protected
    procedure Execute; override;
  public
    OwnerForm : TForm;
  end;

  TSplashForm = class(TForm)
    Image1: TImage;
    Image2: TImage;
    VerPanel: TPanel;
    procedure FormCreate(Sender: TObject);
    procedure Timer1Timer(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
  private
    fProgress : Integer;
    thread    : TSplashThread;
  public
  end;

implementation

{$R *.dfm}

uses
  DateUtils, VerInfos;

procedure TSplashThread.Execute;
begin
  while not (Terminated) do begin
    Sleep (800);

    if Assigned (OwnerForm) then
      (OwnerForm as TSplashForm).Timer1Timer (Nil);
  end;
end;


procedure TSplashForm.FormCreate(Sender: TObject);
var
  bm        : TBitmap;
  rect      : TRect;
  filedate  : TDateTime;
begin
  FileAge (ParamStr (0), filedate);

  VerPanel.Caption := FileVersion (3,2) + ' from ' + DateToStr (filedate);

  fProgress := 0;
  thread := Nil;

  bm := TBitmap.Create;
  bm.handle:=loadbitmap(hinstance, 'aboutlogo');
  Image1.Picture.Assign (bm);
  bm.Free;

  if (Image2.Visible) then begin
    thread := TSplashThread.Create (True);
    thread.OwnerForm := Self;
    thread.FreeOnTerminate := True;

    rect.Top := 0;
    rect.Left := 0;
    rect.Right := Image2.Width;
    rect.Bottom := Image2.Height;

    with Image2.Picture.Bitmap do begin
      Width  := Image2.Width;
      Height := Image2.Height;

      Canvas.Brush.Style := bsSolid;
      Canvas.Brush.Color := clWhite;
      Canvas.FillRect (rect);
    end;

    thread.Resume;
  end;
end;

procedure TSplashForm.FormDestroy(Sender: TObject);
begin
  if Assigned (thread) then begin
    thread.OwnerForm := Nil;

    thread.Terminate;
  end;
end;

procedure TSplashForm.Timer1Timer(Sender: TObject);
var
  bm : TBitmap;
begin
  if (fProgress < 3) then begin
    fProgress:= fProgress + 1;

    bm := TBitmap.Create;

    if (fProgress = 1) then
      bm.handle:=loadbitmap(hinstance, 'storelogixprogress1')
    else if (fProgress = 2) then
      bm.handle:=loadbitmap(hinstance, 'storelogixprogress2')
    else if (fProgress = 3) then
      bm.handle:=loadbitmap(hinstance, 'storelogixprogress3');

    if Assigned (Image2) then
      Image2.Picture.Assign (bm);

    bm.Free;
  end;
end;

end.
