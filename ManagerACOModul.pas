//*****************************************************************************
//*  Program System    : ACOModul
//*  Module Name       : UserDLG
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /Zimbo/LVS/FrontEnd/ManagerACOModul.pas $
// $Revision: 1 $
// $Modtime: 17.06.05 14:41 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Verwaltung der AccessControls einer Anwendung
//*****************************************************************************
unit ManagerACOModul;

interface

uses
  SysUtils, Classes, ACOList, Menus, StdCtrls, ComCtrls, ACOModul;

type
  TManagerACOModule = class(TACOModule)
    ACOListManager1: TACOListManager;
  public
    constructor Create(AOwner: TComponent); override;
  end;

var
  ManagerACOModule : TManagerACOModule;

implementation

{$R *.dfm}

constructor TManagerACOModule.Create(AOwner: TComponent);
begin
  inherited Create (AOwner);

  ApplicationID := 'PCD';
  
  ACOListManager := ACOListManager1;
end;

end.
