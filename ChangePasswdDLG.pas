unit ChangePasswdDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, EditBtn, AdvEdit, AdvEdBtn

  {$IFDEF VER360}
    ,System.ImageList, Vcl.ImgList
  {$ELSE}
    {$IFDEF VER350}
      ,System.ImageList, Vcl.ImgList
    {$else}
      ,ImgList
    {$endif}
  {$endif}
  ;

type
  TChangePasswdForm = class(TForm)
    OldPanel: TPanel;
    Label1: TLabel;
    NewPanel: TPanel;
    Label2: TLabel;
    Label3: TLabel;
    ChangeButton: TButton;
    AbortButton: TButton;
    KopfPanel: TPanel;
    Label4: TLabel;
    UserLabel: TLabel;
    Bevel1: TBevel;
    PwHintLabel: TLabel;
    NewPwEdit: TAdvEditBtn;
    ImageList1: TImageList;
    NewPwRepEdit: TAdvEditBtn;
    OldPwEdit: TAdvEditBtn;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure NewPwEditChange(Sender: TObject);
    procedure NewPwEditClickBtn(Sender: TObject);
  private
    fPWLen       : Integer;
    fAlphaCase   : Boolean;
    fAlphaNum    : Boolean;
    fSpecialChar : Boolean;

    function CheckPasswordRestrictons (const PWStr : String; var HintStr : String) : Integer;
  public
    property MinLength    : integer read fPWLen       write fPWLen;
    property AlphaCase    : boolean read fAlphaCase   write fAlphaCase;
    property AlphaNum     : boolean read fAlphaNum    write fAlphaNum;
    property SpecialChar  : boolean read fSpecialChar write fSpecialChar;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, SprachModul, ResourceText, FrontendMessages;

procedure TChangePasswdForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  level   : Integer;
  hintstr : String;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    level := CheckPasswordRestrictons (NewPwEdit.Text, hintstr);

    if (OldPanel.Visible) and (Length (OldPwEdit.Text) = 0) then begin
      CanClose := False;
      FrontendMessages.MessageDlg(FormatMessageText (1011,[]), mtError, [mbOK], 0);
    end else if (Length (NewPwEdit.Text) = 0) then begin
      CanClose := False;
      FrontendMessages.MessageDlg(FormatMessageText (1012,[]), mtError, [mbOK], 0);
    end else if ((level > 0) and (level < 9)) then begin
      CanClose := False;
      FrontendMessages.MessageDlg(FormatMessageText (1687, [hintstr]), mtError, [mbOK], 0);
    end else if (NewPwRepEdit.Text <> NewPwEdit.Text) then begin
      CanClose := False;
      FrontendMessages.MessageDlg(FormatMessageText (1013,[]), mtError, [mbOK], 0);
    end else CanClose := True;
 end;
end;

procedure TChangePasswdForm.FormCreate(Sender: TObject);
begin
  fPWLen       := 0;
  fAlphaCase   := False;
  fAlphaNum    := False;
  fSpecialChar := False;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, UserLabel);
    LVSSprachModul.SetNoTranslate (Self, OldPwEdit);
    LVSSprachModul.SetNoTranslate (Self, NewPwEdit);
    LVSSprachModul.SetNoTranslate (Self, NewPwRepEdit);
  {$endif}
end;

function TChangePasswdForm.CheckPasswordRestrictons (const PWStr : String; var HintStr : String) : Integer;
var
  i,
  level : Integer;
begin
  HintStr := '';

  level := 0;

  for i:=1 to Length (PWStr) do begin
    case PWStr [i] of
      '0'..'9': level := level or $01;
      'a'..'z': level := level or $02;
      'A'..'Z': level := level or $04;
      else
        level := level or $08;
    end;
  end;

  if (fPWLen = 0) and not (fAlphaNum) and not (fAlphaCase) and not (fSpecialChar) then
    Result := 0
  else if (fPWLen > 0) and (Length (PWStr) < fPWLen) then
    Result := 1
  else if fAlphaNum and ((level and $01) = $00) then
    Result := 2
  else if fAlphaCase and ((level and $06) <> $06) then
    Result := 3
  else if fSpecialChar and ((level and $08) = $00) then
    Result := 4
  else
    Result := 9;

  if (fPWLen > 0) and (Length (PWStr) < fPWLen) then
    HintStr := FormatResourceText (1792, [IntToStr (fPWLen)]);

  if fAlphaNum and ((level and $01) = $00) then begin
    if (Length (HintStr) > 0) then HintStr := HintStr + ', ';
    HintStr := HintStr + GetResourceText (1794);
  end;

  if fAlphaCase and ((level and $06) <> $06) then begin
    if (Length (HintStr) > 0) then HintStr := HintStr + ', ';
    HintStr := HintStr + GetResourceText (1793);
  end;

  if fSpecialChar and ((level and $08) = $00) then begin
    if (Length (HintStr) > 0) then HintStr := HintStr + ', ';
    HintStr := HintStr + GetResourceText (1795);
  end;
end;

procedure TChangePasswdForm.FormShow(Sender: TObject);
begin
  OldPwEdit.Text        := '';
  OldPwEdit.EditType    := etPassword;
  OldPwEdit.GlyphIndex  := 0;

  NewPwEdit.Text        := '';
  NewPwEdit.EditType    := etPassword;
  NewPwEdit.GlyphIndex  := 0;

  NewPwRepEdit.Text       := '';
  NewPwRepEdit.EditType   := etPassword;
  NewPwRepEdit.GlyphIndex := 0;

  PwHintLabel.Caption := '';

  if (OldPanel.Visible) then
    OldPwEdit.SetFocus
  else NewPwEdit.SetFocus;
end;

procedure TChangePasswdForm.NewPwEditChange(Sender: TObject);
var
  level   : Integer;
  hintstr : String;
begin
  hintstr := '';
  
  if (Length (NewPwEdit.Text) = 0) then
    level := 0
  else
    level := CheckPasswordRestrictons (NewPwEdit.Text, hintstr);

  if (level = 0) then
    NewPwEdit.Color := clWindow
  else if (level = 1) then
    NewPwEdit.Color := clRed
  else if (level = 2) then
    NewPwEdit.Color := clWebCoral
  else if (level = 3) then
    NewPwEdit.Color := clWebSalmon
  else if (level = 4) then
    NewPwEdit.Color := clWebLightCoral
  else
    NewPwEdit.Color := clWebLime;

  PwHintLabel.Caption := hintstr;
end;

procedure TChangePasswdForm.NewPwEditClickBtn(Sender: TObject);
var
  edit : TAdvEditBtn;
begin
  if (Sender is TAdvEditBtn) then begin
    edit := (Sender as TAdvEditBtn);

    if (edit.PasswordChar = '*') then begin
      edit.EditType     := etString;
      edit.PasswordChar := #0;
      edit.GlyphIndex   := 1;
    end else begin
      edit.EditType     := etPassword;
      edit.PasswordChar := '*';
      edit.GlyphIndex   := 0;
    end;
  end;
end;

end.
