object TourenPlanenForm: TTourenPlanenForm
  Left = 0
  Top = 0
  Caption = 'Auftragstouren planen'
  ClientHeight = 611
  ClientWidth = 1062
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel55: TPanel
    Left = 0
    Top = 0
    Width = 1062
    Height = 185
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      1062
      185)
    object Label177: TLabel
      Left = 8
      Top = 168
      Width = 43
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Auftr'#228'ge'
      ExplicitTop = 112
    end
    object GroupBox25: TGroupBox
      Left = 8
      Top = 8
      Width = 1046
      Height = 154
      Anchors = [akLeft, akTop, akRight, akBottom]
      Caption = 'Auswahl'
      TabOrder = 0
      DesignSize = (
        1046
        154)
      object Label172: TLabel
        Left = 18
        Top = 22
        Width = 73
        Height = 13
        Alignment = taRightJustify
        Caption = 'Planungsdatum'
      end
      object Label173: TLabel
        Left = 47
        Top = 59
        Width = 44
        Height = 13
        Alignment = taRightJustify
        Caption = 'Spedition'
      end
      object Bevel2: TBevel
        Left = 5
        Top = 45
        Width = 1038
        Height = 6
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object Label1: TLabel
        Left = 46
        Top = 114
        Width = 45
        Height = 13
        Alignment = taRightJustify
        Caption = 'Fahrzeug'
      end
      object Label2: TLabel
        Left = 47
        Top = 87
        Width = 23
        Height = 13
        Caption = 'Land'
      end
      object Label4: TLabel
        Left = 267
        Top = 22
        Width = 67
        Height = 13
        Alignment = taRightJustify
        Caption = 'Anlieferdatum'
      end
      object Label5: TLabel
        Left = 456
        Top = 22
        Width = 13
        Height = 13
        Caption = 'bis'
      end
      object TourplanungDateTimePicker: TDateTimePicker
        Left = 97
        Top = 17
        Width = 104
        Height = 21
        Date = 38189.712940011560000000
        Time = 38189.712940011560000000
        TabOrder = 0
        OnCloseUp = TourplanungDateTimePickerChange
        OnChange = TourplanungDateTimePickerChange
      end
      object SpedComboBox: TComboBoxPro
        Left = 97
        Top = 56
        Width = 488
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 1
        OnChange = SpedComboBoxChange
      end
      object KFZComboBox: TComboBoxPro
        Left = 97
        Top = 112
        Width = 488
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 3
        OnChange = SpedComboBoxChange
      end
      object LandComboBox: TComboBoxPro
        Left = 97
        Top = 84
        Width = 488
        Height = 22
        Style = csOwnerDrawFixed
        ItemHeight = 16
        TabOrder = 2
        OnChange = LandComboBoxChange
      end
      object DeliveryFromDateTimePicker: TDateTimePicker
        Left = 344
        Top = 18
        Width = 105
        Height = 21
        Date = 42758.404858194440000000
        Time = 42758.404858194440000000
        TabOrder = 4
        OnCloseUp = TourplanungDateTimePickerChange
        OnChange = TourplanungDateTimePickerChange
      end
      object DeliveryToDateTimePicker: TDateTimePicker
        Left = 480
        Top = 18
        Width = 105
        Height = 21
        Date = 42758.404858194440000000
        Time = 42758.404858194440000000
        TabOrder = 5
        OnCloseUp = TourplanungDateTimePickerChange
        OnChange = TourplanungDateTimePickerChange
      end
    end
  end
  object TourplanungDBGrid: TDBGridPro
    AlignWithMargins = True
    Left = 8
    Top = 188
    Width = 1046
    Height = 345
    Margins.Left = 8
    Margins.Right = 8
    Align = alClient
    Constraints.MinHeight = 100
    DataSource = TourplanungDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit, dgMultiSelect]
    ReadOnly = True
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoCheckBoxSelect, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 29
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object Panel88: TPanel
    Left = 0
    Top = 536
    Width = 1062
    Height = 75
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      1062
      75)
    object Label3: TLabel
      Left = 8
      Top = 18
      Width = 69
      Height = 13
      Alignment = taRightJustify
      Anchors = [akLeft, akBottom]
      Caption = 'Versanddatum'
    end
    object CloseButton: TButton
      Left = 979
      Top = 43
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 2
    end
    object PlanTourButton: TButton
      Left = 8
      Top = 43
      Width = 249
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'Neue Touren planen'
      TabOrder = 1
      OnClick = PlanTourButtonClick
    end
    object VersandDateTimePicker: TDateTimePicker
      Left = 83
      Top = 13
      Width = 104
      Height = 21
      Anchors = [akLeft, akBottom]
      Date = 38189.712940011560000000
      Time = 38189.712940011560000000
      TabOrder = 0
      OnCloseUp = TourplanungDateTimePickerChange
      OnChange = TourplanungDateTimePickerChange
    end
  end
  object TourplanungDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 912
    Top = 160
  end
  object TourplanungDataSource: TDataSource
    DataSet = TourplanungDataSet
    Left = 944
    Top = 160
  end
end
