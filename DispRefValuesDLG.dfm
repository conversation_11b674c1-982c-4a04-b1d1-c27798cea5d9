object DispRefValuesForm: TDispRefValuesForm
  Left = 616
  Top = 351
  BorderIcons = [biSystemMenu]
  Caption = 'SQL-Query und verdeckte Spalten'
  ClientHeight = 270
  ClientWidth = 336
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Splitter1: TSplitter
    Left = 0
    Top = 81
    Width = 336
    Height = 3
    Cursor = crVSplit
    Align = alTop
  end
  object Panel1: TPanel
    Left = 0
    Top = 223
    Width = 336
    Height = 47
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      336
      47)
    object Button1: TButton
      Left = 253
      Top = 12
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
    object DBNavigator1: TDBNavigator
      Left = 8
      Top = 12
      Width = 160
      Height = 25
      DataSource = DataSource1
      VisibleButtons = [nbFirst, nbPrior, nbNext, nbLast]
      TabOrder = 1
    end
  end
  object StringGrid1: TStringGrid
    Left = 0
    Top = 84
    Width = 336
    Height = 139
    Align = alClient
    ColCount = 3
    DefaultColWidth = 20
    DefaultRowHeight = 20
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goDrawFocusSelected]
    PopupMenu = PopupMenu1
    TabOrder = 1
    OnSelectCell = StringGrid1SelectCell
    ColWidths = (
      20
      133
      157)
  end
  object Memo1: TMemo
    Left = 0
    Top = 0
    Width = 336
    Height = 81
    Align = alTop
    Lines.Strings = (
      'Memo1')
    PopupMenu = MemoPopupMenu
    ReadOnly = True
    TabOrder = 2
  end
  object DataSource1: TDataSource
    OnDataChange = DataSource1DataChange
    Left = 176
    Top = 224
  end
  object PopupMenu1: TPopupMenu
    OnPopup = PopupMenu1Popup
    Left = 192
    Top = 152
    object Kopieren1: TMenuItem
      Caption = 'Kopieren'
      ShortCut = 16451
      OnClick = Kopieren1Click
    end
  end
  object MemoPopupMenu: TPopupMenu
    Left = 160
    Top = 16
    object CopyQueryParamMenuItem: TMenuItem
      Caption = 'Query mit Parametern kopieren'
      OnClick = CopyQueryParamMenuItemClick
    end
    object CopyQueryMenuItem: TMenuItem
      Caption = 'Kopieren'
      ShortCut = 16451
      OnClick = CopyQueryMenuItemClick
    end
  end
end
