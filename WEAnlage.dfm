object WEAnlageForm: TWEAnlageForm
  Left = 410
  Top = 331
  BorderStyle = bsDialog
  Caption = 'Neue Warenannahme anlegen'
  ClientHeight = 541
  ClientWidth = 511
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poMainFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    511
    541)
  TextHeight = 13
  object Bevel4: TBevel
    Left = 6
    Top = 502
    Width = 497
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 403
  end
  object OkButton: TButton
    Left = 339
    Top = 510
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 4
  end
  object AbortButton: TButton
    Left = 426
    Top = 510
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
  object WEAbrechungCheckBox: TCheckBox
    Left = 8
    Top = 514
    Width = 276
    Height = 17
    Anchors = [akLeft, akBottom]
    Caption = 'Dienstleistung soll abgerechnet werden'
    TabOrder = 3
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 511
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      511
      50)
    object Label9: TLabel
      Left = 12
      Top = 8
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 12
      Top = 24
      Width = 489
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      TabOrder = 0
      OnChange = MandantComboBoxChange
      OnDropDown = ComboBoxDropDown
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 50
    Width = 511
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      511
      50)
    object Label17: TLabel
      Left = 12
      Top = 8
      Width = 67
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 12
      Top = 24
      Width = 489
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      TabOrder = 0
      OnChange = SubMandComboBoxChange
      OnDropDown = ComboBoxDropDown
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 100
    Width = 511
    Height = 389
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      511
      389)
    object Bevel1: TBevel
      Left = 6
      Top = 135
      Width = 497
      Height = 5
      Shape = bsTopLine
    end
    object Label2: TLabel
      Left = 12
      Top = 243
      Width = 99
      Height = 13
      Caption = 'Lieferschein-Nummer'
    end
    object Label3: TLabel
      Left = 208
      Top = 11
      Width = 110
      Height = 13
      Caption = 'Wareneingangsbereich'
    end
    object Label4: TLabel
      Left = 12
      Top = 143
      Width = 44
      Height = 13
      Caption = 'Anlieferer'
    end
    object Label5: TLabel
      Left = 304
      Top = 143
      Width = 75
      Height = 13
      Caption = 'Lademittelkonto'
    end
    object Label6: TLabel
      Left = 12
      Top = 67
      Width = 276
      Height = 13
      Caption = 'Gegen diese Bestellung aus der Warenwirtschaft erfassen:'
    end
    object Lieferant: TLabel
      Left = 12
      Top = 88
      Width = 41
      Height = 13
      Caption = 'Lieferant'
    end
    object Label1: TLabel
      Left = 304
      Top = 88
      Width = 68
      Height = 13
      Caption = 'Bestellnummer'
    end
    object Bevel2: TBevel
      Left = 6
      Top = 235
      Width = 497
      Height = 6
      Shape = bsTopLine
    end
    object Bevel3: TBevel
      Left = 6
      Top = 59
      Width = 497
      Height = 9
      Shape = bsTopLine
    end
    object Label7: TLabel
      Left = 192
      Top = 243
      Width = 91
      Height = 13
      Caption = 'Lieferschein-Datum'
    end
    object Label8: TLabel
      Left = 12
      Top = 11
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel5: TBevel
      Left = 6
      Top = 3
      Width = 497
      Height = 9
      Shape = bsTopLine
    end
    object Label10: TLabel
      Left = 12
      Top = 295
      Width = 64
      Height = 13
      Caption = 'Anlieferdatum'
    end
    object Label11: TLabel
      Left = 104
      Top = 295
      Width = 42
      Height = 13
      Caption = 'Lieferzeit'
    end
    object Label12: TLabel
      Left = 8
      Top = 192
      Width = 56
      Height = 13
      Caption = 'Fahrername'
    end
    object Label14: TLabel
      Left = 304
      Top = 192
      Width = 89
      Height = 13
      Caption = 'LKW-Kennzeichen'
    end
    object Label13: TLabel
      Left = 304
      Top = 295
      Width = 75
      Height = 13
      Caption = 'Dokumenten-ID'
    end
    object Label15: TLabel
      Left = 304
      Top = 243
      Width = 57
      Height = 13
      Caption = 'Packst'#252'cke'
    end
    object Label16: TLabel
      Left = 12
      Top = 347
      Width = 43
      Height = 13
      Caption = 'Hinweise'
    end
    object Bevel6: TBevel
      Left = 6
      Top = 286
      Width = 497
      Height = 6
      Shape = bsTopLine
    end
    object Label18: TLabel
      Left = 386
      Top = 243
      Width = 75
      Height = 13
      Caption = 'Verpackungsart'
    end
    object PackTypDutyLabel: TLabel
      Left = 377
      Top = 263
      Width = 8
      Height = 20
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object LSDateDutyLabel: TLabel
      Left = 183
      Top = 263
      Width = 8
      Height = 20
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object LSNrDutyLabel: TLabel
      Left = 4
      Top = 263
      Width = 8
      Height = 20
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object LiefDutyLabel: TLabel
      Left = 4
      Top = 107
      Width = 8
      Height = 20
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object KFZDutyLabel: TLabel
      Left = 297
      Top = 212
      Width = 8
      Height = 20
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object DateDutyLabel: TLabel
      Left = 4
      Top = 315
      Width = 8
      Height = 20
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object PackStDutyLabel: TLabel
      Left = 295
      Top = 262
      Width = 8
      Height = 20
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object LSNrEdit: TEdit
      Left = 12
      Top = 259
      Width = 165
      Height = 21
      MaxLength = 30
      TabOrder = 8
      Text = 'LSNrEdit'
      OnChange = EditChange
    end
    object WEComboBox: TComboBoxPro
      Left = 208
      Top = 27
      Width = 293
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 1
      OnDropDown = ComboBoxDropDown
    end
    object SpedComboBox: TComboBoxPro
      Left = 12
      Top = 159
      Width = 277
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 80
      ItemHeight = 15
      PopupMenu = SpedPopupMenu
      TabOrder = 4
      OnChange = SpedComboBoxChange
      OnDropDown = ComboBoxDropDown
    end
    object LeerComboBox: TComboBoxPro
      Left = 304
      Top = 159
      Width = 197
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 140
      ItemHeight = 15
      TabOrder = 5
      OnChange = LeerComboBoxChange
      OnDropDown = ComboBoxDropDown
    end
    object BestNrComboBox: TComboBox
      Left = 304
      Top = 104
      Width = 197
      Height = 21
      Style = csDropDownList
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
      TabOrder = 3
      OnChange = BestNrComboBoxChange
      OnDropDown = ComboBoxDropDown
    end
    object LieferantComboBox: TComboBoxPro
      Left = 12
      Top = 103
      Width = 277
      Height = 22
      Style = csOwnerDrawFixed
      PopupMenu = LFPopupMenu
      TabOrder = 2
      OnChange = LieferantComboBoxChange
      OnCloseUp = LieferantComboBoxChange
      OnDropDown = ComboBoxDropDown
    end
    object LagerComboBox: TComboBoxPro
      Left = 12
      Top = 27
      Width = 189
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 0
      OnChange = LagerComboBoxChange
      OnDropDown = ComboBoxDropDown
    end
    object DateDateTimePicker: TDateTimePicker
      Left = 12
      Top = 311
      Width = 86
      Height = 21
      Date = 39343.000000000000000000
      Time = 0.897649131940852400
      TabOrder = 12
      OnCloseUp = DateDateTimePickerCloseUp
      OnChange = DateDateTimePickerChange
      OnDropDown = DateTimePickerDropDown
    end
    object TimeDateTimePicker: TDateTimePicker
      Left = 104
      Top = 311
      Width = 73
      Height = 21
      Date = 39343.000000000000000000
      Time = 0.897649131940852400
      Kind = dtkTime
      TabOrder = 13
      OnChange = EditChange
    end
    object FahrerEdit: TEdit
      Left = 8
      Top = 208
      Width = 281
      Height = 21
      MaxLength = 32
      TabOrder = 6
      Text = 'FahrerEdit'
      OnChange = EditChange
    end
    object LKWEdit: TEdit
      Left = 304
      Top = 208
      Width = 197
      Height = 21
      MaxLength = 32
      TabOrder = 7
      Text = 'LKWEdit'
      OnChange = EditChange
    end
    object DocIDEdit: TEdit
      Left = 304
      Top = 311
      Width = 197
      Height = 21
      MaxLength = 32
      TabOrder = 14
      Text = 'DocIDEdit'
      OnChange = EditChange
    end
    object PackageEdit: TEdit
      Left = 306
      Top = 259
      Width = 45
      Height = 21
      MaxLength = 4
      TabOrder = 10
      OnChange = EditChange
      OnKeyPress = NumEditKeyPress
    end
    object HintEdit: TEdit
      Left = 11
      Top = 363
      Width = 490
      Height = 21
      MaxLength = 128
      TabOrder = 15
      Text = 'HintEdit'
      OnChange = EditChange
    end
    object PackageTypComboBox: TComboBox
      Left = 386
      Top = 259
      Width = 115
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 11
      Text = 'PackageTypComboBox'
      OnChange = EditChange
    end
    object LSDateDateTimePicker: TAdvDateTimePicker
      Left = 193
      Top = 259
      Width = 96
      Height = 21
      Date = 43816.000000000000000000
      Format = ''
      Time = 0.620659722218988500
      DoubleBuffered = True
      Kind = dkDate
      ParentDoubleBuffered = False
      TabOrder = 9
      OnCloseUp = LSDateDateTimePickerChange
      OnChange = LSDateDateTimePickerChange
      OnDropDown = DateTimePickerDropDown
      BorderStyle = bsSingle
      Ctl3D = True
      DateTime = 43816.620659722220000000
      Version = '1.3.6.6'
      LabelFont.Charset = DEFAULT_CHARSET
      LabelFont.Color = clWindowText
      LabelFont.Height = -11
      LabelFont.Name = 'Tahoma'
      LabelFont.Style = []
    end
    object LeerPanel: TPanel
      Left = 193
      Top = 311
      Width = 90
      Height = 29
      Color = clRed
      ParentBackground = False
      TabOrder = 16
      Visible = False
    end
    object PackageUpDown: TIntegerUpDown
      Left = 351
      Top = 259
      Width = 17
      Height = 21
      Max = 100000
      TabOrder = 17
      OnClick = PackageUpDownClick
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 448
    Top = 8
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 432
    Top = 112
  end
  object LFPopupMenu: TPopupMenu
    Left = 184
    Top = 200
    object NeuerLieferant1: TMenuItem
      Caption = 'Neuer Lieferant...'
      ShortCut = 115
      OnClick = NeuerLieferant1Click
    end
  end
  object SpedPopupMenu: TPopupMenu
    Left = 160
    Top = 256
    object NewSpeditionMenuItem: TMenuItem
      Caption = 'Neue Spedition...'
      OnClick = NewSpeditionMenuItemClick
    end
  end
end
