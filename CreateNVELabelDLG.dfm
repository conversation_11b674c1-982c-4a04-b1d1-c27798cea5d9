object CreateNVELabelForm: TCreateNVELabelForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Neue NVE-Etiketten erzeugen'
  ClientHeight = 433
  ClientWidth = 494
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    494
    433)
  TextHeight = 13
  object Label2: TLabel
    Left = 192
    Top = 178
    Width = 45
    Height = 13
    Caption = 'Bestellnr.'
  end
  object Label7: TLabel
    Left = 8
    Top = 170
    Width = 52
    Height = 32
    Alignment = taCenter
    AutoSize = False
    Caption = 'Menge auf der NVE'
    WordWrap = True
  end
  object Label9: TLabel
    Left = 8
    Top = 267
    Width = 38
    Height = 13
    Caption = 'NVE-Nr.'
  end
  object Bevel4: TBevel
    Left = 8
    Top = 229
    Width = 476
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel5: TBevel
    Left = 8
    Top = 364
    Width = 476
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object Bevel6: TBevel
    Left = 8
    Top = 296
    Width = 476
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label1: TLabel
    Left = 8
    Top = 302
    Width = 52
    Height = 32
    Alignment = taCenter
    AutoSize = False
    Caption = 'Anzahl Lables'
    WordWrap = True
  end
  object Label10: TLabel
    Left = 192
    Top = 311
    Width = 32
    Height = 13
    Caption = 'Kopien'
  end
  object Label11: TLabel
    Left = 8
    Top = 205
    Width = 38
    Height = 13
    Caption = 'Gewicht'
  end
  object Label12: TLabel
    Left = 143
    Top = 205
    Width = 11
    Height = 13
    Caption = 'kg'
  end
  object FussPanel: TPanel
    Left = 0
    Top = 373
    Width = 494
    Height = 60
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 12
    DesignSize = (
      494
      60)
    object Label8: TLabel
      Left = 8
      Top = 6
      Width = 37
      Height = 13
      Caption = 'Drucker'
    end
    object PrinterComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 289
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 100
      ItemHeight = 15
      TabOrder = 0
    end
    object PrintButton: TButton
      Left = 303
      Top = 20
      Width = 75
      Height = 25
      Caption = 'Drucken'
      Default = True
      TabOrder = 1
      OnClick = PrintButtonClick
    end
    object CloseButton: TButton
      Left = 409
      Top = 20
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 2
    end
  end
  object ArtikelPanel: TPanel
    Left = 0
    Top = 0
    Width = 494
    Height = 73
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      494
      73)
    object Label3: TLabel
      Left = 8
      Top = 13
      Width = 37
      Height = 13
      Caption = 'Artikel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label4: TLabel
      Left = 8
      Top = 32
      Width = 74
      Height = 13
      Caption = 'Bezeichnung'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label5: TLabel
      Left = 8
      Top = 51
      Width = 26
      Height = 13
      Caption = 'EAN'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object ArtNrLabel: TLabel
      Left = 120
      Top = 13
      Width = 51
      Height = 13
      Caption = 'ArtNrLabel'
    end
    object ArtTextLabel: TLabel
      Left = 119
      Top = 32
      Width = 62
      Height = 13
      Caption = 'ArtTextLabel'
    end
    object ArtEANLabel: TLabel
      Left = 120
      Top = 51
      Width = 60
      Height = 13
      Caption = 'ArtEANLabel'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 70
      Width = 476
      Height = 14
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
  end
  object LayoutPanel: TPanel
    Left = 0
    Top = 73
    Width = 494
    Height = 49
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      494
      49)
    object Label6: TLabel
      Left = 8
      Top = 15
      Width = 33
      Height = 13
      Caption = 'Layout'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 47
      Width = 476
      Height = 8
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object LayoutComboBox: TComboBoxPro
      Left = 66
      Top = 12
      Width = 418
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 0
    end
  end
  object MHDChargePanel: TPanel
    Left = 0
    Top = 122
    Width = 494
    Height = 45
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      494
      45)
    object Bevel2: TBevel
      Left = 8
      Top = 41
      Width = 476
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label13: TLabel
      Left = 8
      Top = 13
      Width = 22
      Height = 13
      Caption = 'MHD'
    end
    object Label14: TLabel
      Left = 192
      Top = 13
      Width = 35
      Height = 13
      Caption = 'Charge'
    end
    object MHDEdit: TEdit
      Left = 66
      Top = 10
      Width = 87
      Height = 21
      MaxLength = 12
      TabOrder = 0
      Text = 'MHDEdit'
      OnExit = MHDEditExit
    end
    object ChargeEdit: TEdit
      Left = 248
      Top = 10
      Width = 236
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 1
      Text = 'ChargeEdit'
    end
  end
  object MengeEdit: TEdit
    Left = 66
    Top = 175
    Width = 71
    Height = 21
    TabOrder = 3
    Text = '0'
  end
  object SSCCEdit: TEdit
    Left = 66
    Top = 264
    Width = 153
    Height = 21
    TabOrder = 8
    Text = 'SSCCEdit'
  end
  object AutoNVENrCheckBox: TCheckBox
    Left = 66
    Top = 241
    Width = 153
    Height = 17
    Caption = 'NVE automatisch erzeugen'
    Checked = True
    State = cbChecked
    TabOrder = 7
    OnClick = AutoNVENrCheckBoxClick
  end
  object BestNrEdit: TEdit
    Left = 248
    Top = 175
    Width = 97
    Height = 21
    MaxLength = 32
    TabOrder = 6
    Text = 'BestNrEdit'
  end
  object MengeUpDown: TIntegerUpDown
    Left = 137
    Top = 175
    Width = 17
    Height = 21
    Associate = MengeEdit
    Max = 10000
    TabOrder = 4
    Thousands = False
  end
  object AnzahlEdit: TEdit
    Left = 66
    Top = 307
    Width = 71
    Height = 21
    TabOrder = 9
    Text = '1'
  end
  object AnzahlUpDown: TIntegerUpDown
    Left = 137
    Top = 307
    Width = 16
    Height = 21
    Associate = AnzahlEdit
    Min = 1
    Position = 1
    TabOrder = 10
  end
  object KopieComboBox: TComboBox
    Left = 248
    Top = 307
    Width = 145
    Height = 21
    Style = csDropDownList
    ItemIndex = 0
    TabOrder = 11
    Text = 'Keine'
    Items.Strings = (
      'Keine'
      '1 Kopie'
      '2 Kopien'
      '3 Kopien'
      '4 Kopien'
      '5 Kopien')
  end
  object GewichtEdit: TEdit
    Left = 66
    Top = 202
    Width = 71
    Height = 21
    TabOrder = 5
    Text = '0'
    OnExit = GewichtEditExit
  end
end
