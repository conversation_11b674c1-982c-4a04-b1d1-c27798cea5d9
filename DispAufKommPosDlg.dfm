object DispAufKommPosForm: TDispAufKommPosForm
  Left = 318
  Top = 306
  BorderIcons = [biSystemMenu]
  Caption = 'DispAufKommPosForm'
  ClientHeight = 462
  ClientWidth = 740
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    740
    462)
  TextHeight = 13
  object AufTextLabel: TLabel
    Left = 8
    Top = 8
    Width = 34
    Height = 13
    Caption = 'Auftrag'
  end
  object AufNrLabel: TLabel
    Left = 64
    Top = 8
    Width = 53
    Height = 13
    Caption = 'AufNrLabel'
  end
  object Label3: TLabel
    Left = 8
    Top = 32
    Width = 31
    Height = 13
    Caption = 'Kunde'
  end
  object KundeNrLabel: TLabel
    Left = 64
    Top = 32
    Width = 68
    Height = 13
    Caption = 'KundeNrLabel'
  end
  object Label5: TLabel
    Left = 8
    Top = 64
    Width = 80
    Height = 13
    Caption = 'Erfasste Mengen'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 56
    Width = 721
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object AufKommPosDBGrid: TDBGridPro
    Left = 8
    Top = 80
    Width = 721
    Height = 341
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = AufKommPosDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = AufKommPopupMenu
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDrawColumnCell = AufKommPosDBGridDrawColumnCell
    OnDblClick = AufKommPosDBGridDblClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 656
    Top = 427
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 2
  end
  object AufStornoButton: TButton
    Left = 8
    Top = 427
    Width = 201
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Auftrag nachtr'#228'glich stornieren...'
    TabOrder = 1
    OnClick = AufStornoButtonClick
  end
  object AufKommPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 656
    Top = 120
  end
  object AufKommPosDataSource: TDataSource
    DataSet = AufKommPosDataSet
    Left = 616
    Top = 120
  end
  object AufKommPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = AufKommPopupMenuPopup
    Left = 464
    Top = 208
    object ChangeKommPosMenuItem: TMenuItem
      Caption = 'Position '#228'ndern...'
      OnClick = ChangeKommPosMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object ChangeKommPosMengeMenuItem: TMenuItem
      Caption = 'Menge '#228'ndern...'
      OnClick = ChangeKommPosMengeMenuItemClick
    end
    object AddErsatzMenuItem: TMenuItem
      Caption = 'Ersatzartikel erfassen...'
      OnClick = AddErsatzMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object PrintBesLabelMenuItem: TMenuItem
      Caption = 'Bestandslabel drucken...'
      OnClick = PrintBesLabelMenuItemClick
    end
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 632
    Top = 8
  end
end
