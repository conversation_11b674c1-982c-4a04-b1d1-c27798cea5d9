unit LagerRegalDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro;

type
  TLagerRegalForm = class(TForm)
    RegalDBGrid: TDBGridPro;
    GroupBox1: TGroupBox;
    ApplyButton: TButton;
    QuashButton: TButton;
    CloseButton: TButton;
    RegalQuery: TADOQuery;
    RegalDataSource: TDataSource;
    Label5: TLabel;
    DescEdit: TEdit;
    GroupBox2: TGroupBox;
    Label1: TLabel;
    FachLastEdit: TEdit;
    Label3: TLabel;
    Label6: TLabel;
    EbeneLastEdit: TEdit;
    Label7: TLabel;
    Label2: TLabel;
    FeldLastEdit: TEdit;
    Label4: TLabel;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure RegalDataSourceDataChange(Sender: TObject; Field: TField);
    procedure IntgerEditKeyPress(Sender: TObject; var Key: Char);
    procedure DatenChange(Sender: TObject);
    procedure ApplyButtonClick(Sender: TObject);
    procedure QuashButtonClick(Sender: TObject);
  private
    fRefRegal,
    fRefLager,
    fRefBereich : Integer;

    procedure UpdateGrid;
  public
    procedure Prepare (const RefLager, RefBereich, RefRegal : Integer);
  end;

implementation

{$R *.dfm}

uses
 VCLUtilitys, FrontendUtils, DatenModul, DBGridUtilModule, ConfigModul, ResourceText, LVSLagertopologie,
  SprachModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerRegalForm.Prepare (const RefLager, RefBereich, RefRegal : Integer);
begin
  fRefRegal   := RefRegal;
  fRefLager   := RefLager;
  fRefBereich := RefBereich;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerRegalForm.QuashButtonClick(Sender: TObject);
begin
  RegalDataSourceDataChange (Sender, Nil);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerRegalForm.RegalDataSourceDataChange(Sender: TObject; Field: TField);
begin
  DescEdit.Text := RegalQuery.FieldByName ('BEZEICHNUNG').AsString;

  if (RegalQuery.FieldByName ('MAX_FACH_LAST').IsNull) then
    FachLastEdit.Text := ''
  else FachLastEdit.Text := RegalQuery.FieldByName ('MAX_FACH_LAST').AsString;

  if (RegalQuery.FieldByName ('MAX_EBENE_LAST').IsNull) then
    EbeneLastEdit.Text := ''
  else EbeneLastEdit.Text := RegalQuery.FieldByName ('MAX_EBENE_LAST').AsString;

  if (RegalQuery.FieldByName ('MAX_FELD_LAST').IsNull) then
    FeldLastEdit.Text := ''
  else FeldLastEdit.Text := RegalQuery.FieldByName ('MAX_FELD_LAST').AsString;

  ApplyButton.Enabled := False;
  QuashButton.Enabled := ApplyButton.Enabled;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerRegalForm.UpdateGrid;
begin
  RegalQuery.Close;

  RegalQuery.SQL.Clear;
  RegalQuery.SQL.Add ('select * from V_PCD_LAGER_REGAL where REF_LAGER=:ref_lager');
  RegalQuery.Parameters.ParamByName('ref_lager').Value := fRefLager;

  if (fRefBereich > 0) then begin
    RegalQuery.SQL.Add ('and REF_LB=:ref_lb');
    RegalQuery.Parameters.ParamByName('ref_lb').Value := fRefBereich;
  end;

  RegalQuery.Open;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerRegalForm.IntgerEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,^V,'0'..'9'])  then begin
    Key := #0;
    Beep;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerRegalForm.ApplyButtonClick(Sender: TObject);
var
  res,
  fachlast,
  feldlast,
  ebenelast : Integer;
begin
  fachlast  := -1;
  feldlast  := -1;
  ebenelast := -1;

  if ((Length (FachLastEdit.Text) > 0) and not TryStrToInt(FachLastEdit.Text, fachlast)) then begin
    FachLastEdit.SetFocus;
  end else if ((Length (EbeneLastEdit.Text) > 0) and not TryStrToInt(EbeneLastEdit.Text, ebenelast)) then begin
    EbeneLastEdit.SetFocus;
  end else if ((Length (FeldLastEdit.Text) > 0) and not TryStrToInt(FeldLastEdit.Text, feldlast)) then begin
    FeldLastEdit.SetFocus;
  end else begin
    if (fachlast > 0) then fachlast := fachlast * 1000;
    if (feldlast > 0) then feldlast := feldlast * 1000;
    if (ebenelast > 0) then ebenelast := ebenelast * 1000;

    res := ChangeRegal (RegalQuery.FieldByName ('REF').AsInteger, DBGetReferenz (RegalQuery.FieldByName ('REF_GASSE')), DescEdit.Text, fachlast, ebenelast, feldlast);

    if (res <> 0) then
      MessageDLG (FormatMessageText (1624, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    else begin
      RegalDBGrid.Reload;

      ApplyButton.Enabled := False;
      QuashButton.Enabled := ApplyButton.Enabled;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerRegalForm.DatenChange(Sender: TObject);
begin
  ApplyButton.Enabled := True;
  QuashButton.Enabled := ApplyButton.Enabled;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerRegalForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  fRefRegal   := -1;
  fRefLager   := -1;
  fRefBereich := -1;

  DescEdit.Text := '';
  FachLastEdit.Text := '';
  FeldLastEdit.Text := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, LBComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerRegalForm.FormDestroy(Sender: TObject);
begin
  RegalQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);
end;

procedure TLagerRegalForm.FormShow(Sender: TObject);
begin
  UpdateGrid;

  if (fRefRegal > 0) then
    RegalQuery.Locate('REF', fRefRegal, []);
end;

end.
