unit DispKommPlanung;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, DB, ADODB,
  BetterADODataSet;

type
  TDispKommPlanungForm = class(TForm)
    DataSource1: TDataSource;
    BetterADODataSet1: TBetterADODataSet;
    DBGridPro1: TDBGridPro;
    CloseButton: TButton;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
  private
    fRefPlanung : Integer;
  public
    property RefPlanung : Integer read fRefPlanung write fRefPlanung;
  end;

implementation

{$R *.dfm}

uses
  DatenModul, ConfigModul, DBGridUtilModule;

//******************************************************************************
//* Function Name:
//* Author       : <PERSON>
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispKommPlanungForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  BetterADODataSet1.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispKommPlanungForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);

    DBGridPro1.OnKeyPress := DBGridUtils.HotTrackKeyPress;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDispKommPlanungForm.FormShow(Sender: TObject);
begin
  LVSConfigModul.RestoreFormInfo (Self);

  BetterADODataSet1.CommandText := 'select * from V_KOMM_LP_ZUORDNUNG where REF_PLANUNG='+IntToStr (fRefPlanung);

  BetterADODataSet1.Open;

  DBGridPro1.SetColumnVisible('STATUS', False);
  DBGridPro1.SetColumnVisible('LT_COUNT', False);
  DBGridPro1.SetColumnVisible('NACH_LP_NR', False);
end;

end.
