//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : LTDispDLG
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/BaseStammdatenDLG.pas $
// $Revision: 45 $
// $Modtime: 30.09.23 16:18 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Alle Daten der LTs anzeigen
//*****************************************************************************
unit BaseStammdatenDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, DB, ADODB,
  CompTranslate, CheckLst, ExtCtrls, ComCtrls, ComboBoxPro, Menus, ACOList;

type
  TBaseStammdatenForm = class(TForm)
    CloseButton: TButton;
    CompTranslateForm1: TCompTranslateForm;
    PageControl1: TPageControl;
    LHMTabSheet: TTabSheet;
    Label1: TLabel;
    LTDBGrid: TDBGridPro;
    NeuLTButton: TButton;
    DelLTButton: TButton;
    EinsatzGroupBox: TGroupBox;
    EinsatzCommitButton: TButton;
    EinsatzRollbackButton: TButton;
    EditLTButton: TButton;
    LTQuery: TADOQuery;
    LTDataSource: TDataSource;
    ADOQuery2: TADOQuery;
    CopyLTButton: TButton;
    TextTabSheet: TTabSheet;
    TextAreaComboBox: TComboBoxPro;
    Bereich: TLabel;
    AddTextButton: TButton;
    EditTextButton: TButton;
    DelTextButton: TButton;
    TextDBGrid: TDBGridPro;
    TextDataSource: TDataSource;
    TextQuery: TADOQuery;
    TypGroupBox: TGroupBox;
    ZuordnungRollbackButton: TButton;
    ZuordnungCommitButton: TButton;
    ZuordnungCheckListBox: TCheckListBox;
    LTDBGridPopupMenu: TPopupMenu;
    StdLTMenuItem: TMenuItem;
    StdLTEinlagerMenuItem: TMenuItem;
    StdLTKommMenuItem: TMenuItem;
    ACOListForm1: TACOListForm;
    RetZustandTabSheet: TTabSheet;
    Label2: TLabel;
    RetZustandAreaComboBox: TComboBoxPro;
    RetZustandDBGrid: TDBGridPro;
    AddRetZustandButton: TButton;
    EditRetZustandButton: TButton;
    DelRetZustandButton: TButton;
    RetZustandDataSource: TDataSource;
    RetZustandQuery: TADOQuery;
    BesCategoryTabSheet: TTabSheet;
    BesCategoryDBGrid: TDBGridPro;
    BesCatAddButton: TButton;
    BesCatEditButton: TButton;
    BesCatDelButton: TButton;
    GroupBox1: TGroupBox;
    BesCategoryDataSource: TDataSource;
    BesCategoryQuery: TADOQuery;
    Label3: TLabel;
    StorageGroupLabel: TLabel;
    BesCategoryDBGridPopupMenu: TPopupMenu;
    Label4: TLabel;
    ManfReinLabel: TLabel;
    Label8: TLabel;
    ZustandEgalLabel: TLabel;
    Label10: TLabel;
    AskAddInfoLabel: TLabel;
    EinsatzCheckListBox: TCheckListBox;
    N1: TMenuItem;
    Etikettendrucken1: TMenuItem;
    N2: TMenuItem;
    CreateLTRangeMenuItem: TMenuItem;
    EditLTRangeMenuItem: TMenuItem;
    PrintRetButton: TButton;
    MandantGroupBox: TGroupBox;
    MandantRollbackButton: TButton;
    MandantCommitButton: TButton;
    MandantCheckListBox: TCheckListBox;
    procedure FormShow(Sender: TObject);
    procedure LTDBGridDblClick(Sender: TObject);
    procedure LTDataSourceDataChange(Sender: TObject; Field: TField);
    procedure EinsatzRollbackButtonClick(Sender: TObject);
    procedure EinsatzCommitButtonClick(Sender: TObject);
    procedure NeuLTButtonClick(Sender: TObject);
    procedure DelLTButtonClick(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure ListBoxDrawItem(Control: TWinControl; Index: Integer;
      Rect: TRect; State: TOwnerDrawState);
    procedure EinsatzCheckListBoxClick(Sender: TObject);
    procedure EditLTButtonClick(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure FormKeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
    procedure FormResize(Sender: TObject);
    procedure StdLTButtonClick(Sender: TObject);
    procedure TextAreaComboBoxChange(Sender: TObject);
    procedure TextTabSheetShow(Sender: TObject);
    procedure EditTextButtonClick(Sender: TObject);
    procedure AddTextButtonClick(Sender: TObject);
    procedure TextDataSourceDataChange(Sender: TObject; Field: TField);
    procedure DelTextButtonClick(Sender: TObject);
    procedure ZuordnungCommitButtonClick(Sender: TObject);
    procedure ZuordnungRollbackButtonClick(Sender: TObject);
    procedure ZuordnungCheckListBoxClick(Sender: TObject);
    procedure LHMTabSheetResize(Sender: TObject);
    procedure StdLTMenuItemClick(Sender: TObject);
    procedure StdLTEinlagerMenuItemClick(Sender: TObject);
    procedure StdLTKommMenuItemClick(Sender: TObject);
    procedure LTDBGridPopupMenuPopup(Sender: TObject);
    procedure FormActivate(Sender: TObject);
    procedure RetZustandAreaComboBoxChange(Sender: TObject);
    procedure RetZustandTabSheetShow(Sender: TObject);
    procedure EditRetZustandButtonClick(Sender: TObject);
    procedure AddRetZustandButtonClick(Sender: TObject);
    procedure RetZustandDataSourceDataChange(Sender: TObject; Field: TField);
    procedure BesCategoryTabSheetShow(Sender: TObject);
    procedure BesCategoryDataSourceDataChange(Sender: TObject; Field: TField);
    procedure LHMTabSheetShow(Sender: TObject);
    procedure DelRetZustandButtonClick(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure Etikettendrucken1Click(Sender: TObject);
    procedure EditLTRangeMenuItemClick(Sender: TObject);
    procedure PrintRetButtonClick(Sender: TObject);
    procedure MandantCheckListBoxClick(Sender: TObject);
    procedure MandantRollbackButtonClick(Sender: TObject);
    procedure MandantCommitButtonClick(Sender: TObject);
  private
    fRetAdminFlag   : Boolean;
    EinsatzChange   : Boolean;
    ZuordnungChange : Boolean;

    procedure UpdateLTEinsatz;
    procedure UpdateLTZuordnung;
    procedure UpdateLTMandant;
  public
    { Public-Deklarationen }
  end;


implementation

uses VCLUtilitys, FrontendUtils, LVSGlobalDaten, DatenModul, FrontendACOModul, SprachModul, LTEditDLG, LVSLadungstraeger,
     DBGridUtilModule, LVSLagertopologie, ConfigModul, EditSysTextDLG, LVSSystemInterface, EditRetZustandDLG, ResourceText,
     LVSSecurity, PrintLPLableDLG, EditLTNumberRangeDLG, FrontendMessages,
  PrintModul;

{$R *.dfm}


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.FormShow(Sender: TObject);
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.RestoreFormInfo (Self);

  EinsatzCommitButton.Enabled := False;
  EinsatzRollbackButton.Enabled := False;

  PageControl1.FindNextPage (Nil, True, True);

  if Assigned (PageControl1.ActivePage.OnShow) then
    PageControl1.ActivePage.OnShow (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.LTDBGridDblClick(Sender: TObject);
var
  v,
  vmax      : Int64;
  tg,
  ng,
  ngmax,
  res,
  l,b,h,
  hkl,
  refleer,
  ref       : Integer;
  vol       : Int64;
  fachanz,
  intwert   : Integer;
  p         : Double;
  weopt,
  waopt,
  leeropt,
  pfandopt,
  tauschopt : Char;
  editform  : TLTEditForm;
begin
  if (LTQuery.FieldByName ('STATUS').AsString = 'AKT') then begin
    editform := TLTEditForm.Create (Self);

    editform.MandantPanel.Visible  := True;
    editform.LagerPanel.Visible    := False;
    editform.TemplatePanel.Visible := False;

    editform.Prepare (LTQuery.FieldByName ('REF').AsInteger);

    if (Sender = CopyLTButton) then begin
      editform.MandantComboBox.Enabled := True;
      LoadMandantCombobox (editform.MandantComboBox);

      editform.MandantComboBox.Items.Insert (0, '');
      editform.MandantComboBox.ItemIndex := 0;
    end;

    if (Sender = CopyLTButton) then
      editform.Caption := GetResourceText(1481)
    else
      editform.Caption := GetResourceText(1482);

    if (editform.ShowModal = mrOk) Then begin
      if (editform.ChangeFlag) or (Sender = CopyLTButton) then begin
        with editform do begin
          if not FachAnzEdit.Visible or (Length (FachAnzEdit.Text) = 0) then
            fachanz := -1
          else if not TryStrToInt (FachAnzEdit.Text, fachanz) then
            fachanz := -1;

          if (Length (BEdit.Text) = 0) Then
            b := -1
          else b := StrToInt (BEdit.Text);

          if (Length (HEdit.Text) = 0) Then
            h := -1
          else h := StrToInt (HEdit.Text);

          if (Length (LEdit.Text) = 0) Then
            l := -1
          else l := StrToInt (LEdit.Text);

          if (Length (MaxVolumenEdit.Text) = 0) Then
            vmax := -1
          else vmax := Round (StrToFloat (MaxVolumenEdit.Text) * 1000000000);

          if (Length (TaraGewichtEdit.Text) = 0) Then
            tg := -1
          else tg := Round (StrToFloat (TaraGewichtEdit.Text) * 1000);

          if (Length (MaxKommGwEdit.Text) = 0) Then
            ngmax := -1
          else ngmax := Round (StrToFloat (MaxKommGwEdit.Text) * 1000);

          if not (WEPflichtCheckBox.Checked) then
            weopt := #0
          else if (WEPflichtRadioGroup.ItemIndex = 2) then
            weopt := '2'
          else if (WEPflichtRadioGroup.ItemIndex = 1) then
            weopt := '1'
          else
            weopt := '0';

          if not (WAPflichtCheckBox.Checked) then
            waopt := #0
          else if (WAPflichtRadioGroup.ItemIndex = 2) then
            waopt := '2'
          else if (WAPflichtRadioGroup.ItemIndex = 1) then
            waopt := '1'
          else
            waopt := '0';

          pfandopt   := '0';
          tauschopt := '0';

          if (PfandTauschRadioGroup.ItemIndex = 1) then
            tauschopt := '1'
          else if (PfandTauschRadioGroup.ItemIndex = 2) then
            pfandopt := '1';

          if not (LTHKLComboBox.Visible) then
            hkl := -1
          else if (LTHKLComboBox.ItemIndex = 0) then
            hkl := -1
          else if not (TryStrToInt (GetComboBoxDBItemWert (LTHKLComboBox), hkl)) then
            hkl := -1;

          if (LTLeerComboBox.ItemIndex <= 0) then begin
            refleer := -1;
            leeropt := '0';
          end else if (LTLeerComboBox.ItemIndex = 1) then begin
            refleer := -1;
            leeropt := '1';
          end else begin
            refleer := GetComboBoxRef (LTLeerComboBox);
            leeropt := '1';
          end;

          if (Sender = CopyLTButton) then begin
            res := CreateLT (LVSConfigModul.RefProject,
                             GetComboBoxRef (MandantComboBox),
                             -1, -1, -1,
                             NameEdit.Text,
                             IDEdit.Text,
                             BeschreibungEdit.Text,
                             EDIComboBox.GetItemText, ArtikelNrEdit.Text, EANEdit.Text,
                             FolgeUpDown.Position,
                             tg, l, b, h, hkl, refleer,
                             vmax, ngmax,
                             weopt, waopt, GetCheckboxStat (PermaCheckBox),
                             pfandopt,
                             leeropt, tauschopt,
                             GetCheckboxStat (AutoDeleteCheckBox),
                             ref);
          end else begin
            ref := LTQuery.FieldByName ('REF').AsInteger;

            res := ChangeLT (LTQuery.FieldByName ('REF').AsInteger,
                             NameEdit.Text,
                             IDEdit.Text,
                             BeschreibungEdit.Text,
                             EDIComboBox.GetItemText, ArtikelNrEdit.Text, EANEdit.Text,
                             FolgeUpDown.Position,
                             tg, l, b, h, hkl, refleer,
                             vmax, ngmax,
                             weopt, waopt, GetCheckboxStat (PermaCheckBox), pfandopt,
                             leeropt, tauschopt,
                             GetCheckboxStat (AutoDeleteCheckBox));
          end;

          if (res = 0) and FachAnzEdit.Visible then
            res := SetLTFachAnzahl (ref, fachanz);

          if (res = 0) and RelocationCheckBox.Visible then
            res := SetLTRelocation (ref, GetCheckboxStat (RelocationCheckBox));

          if (res = 0) and OversizeUpDown.Visible then
            res := SetLTOversize (ref, OversizeUpDown.Position);
        end;

        if (res = 0) Then
          LTDBGrid.Reload (ref)
        else if (Sender = CopyLTButton) then
          FrontendMessages.MessageDLG (FormatMessageText (1315, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
        else
          FrontendMessages.MessageDLG (FormatMessageText (1215, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      end;
    end;

    editform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.LTDBGridPopupMenuPopup(Sender: TObject);
begin
  StdLTMenuItem.Enabled         := (LTQuery.Active) and (LTQuery.RecNo > 0) and (LTQuery.FieldByName ('DEFAULT_LT').AsString <> '1');
  StdLTEinlagerMenuItem.Enabled := (LTQuery.Active) and (LTQuery.RecNo > 0) and (LTQuery.FieldByName ('DEFAULT_EINLAGER_LT').AsString <> '1');
  StdLTKommMenuItem.Enabled     := (LTQuery.Active) and (LTQuery.RecNo > 0) and (LTQuery.FieldByName ('DEFAULT_KOMM_LT').AsString <> '1');
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.MandantCheckListBoxClick(Sender: TObject);
begin
  MandantCommitButton.Enabled := True;
  MandantRollbackButton.Enabled := True;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 23.02.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.MandantCommitButtonClick(Sender: TObject);
var
  res,
  idx : Integer;
begin
  res := 0;

  idx := 0;
  while (idx < MandantCheckListBox.Items.Count) and (res = 0) do begin
    res := SetLTMandantUse (LTQuery.FieldByName ('REF').AsInteger, GetListBoxRef (MandantCheckListBox, idx), MandantCheckListBox.Checked [idx]);

    Inc (Idx);
  end;

  if (res = 0) Then
    UpdateLTMandant
  else
    FrontendMessages.MessageDLG (FormatMessageText (1214, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 23.02.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.MandantRollbackButtonClick(Sender: TObject);
begin
  UpdateLTMandant;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.LTDataSourceDataChange(Sender: TObject; Field: TField);
begin
  EditLTButton.Enabled := False;

  if (LTQuery.Active) and (LTQuery.RecNo > 0) Then begin
    if (LTQuery.FieldByName ('STATUS').AsString = 'AKT') then begin
      EditLTButton.Enabled := True;
    end;
  end;

  UpdateLTEinsatz;
  UpdateLTZuordnung;
  UpdateLTMandant;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.UpdateLTEinsatz;
var
  idx     : Integer;
begin
  if (LTQuery.Active) and (LTQuery.RecordCount > 0) Then begin
    idx := 0;

    while (idx < EinsatzCheckListBox.Items.Count) do begin
      EinsatzCheckListBox.Checked [idx] := False;

      Inc (idx);
    end;

    ADOQuery2.SQL.Clear;
    ADOQuery2.SQL.Add ('select EINSATZ from V_LT_EINSATZ where REF=:ref');
    ADOQuery2.Parameters [0].Value := LTQuery.FieldByName ('REF').AsInteger;

    ADOQuery2.Open;

    while not (ADOQuery2.Eof) do begin
      idx := SeparateIndexOf (EinsatzCheckListBox.Items, ADOQuery2.Fields [0].AsString);

      if (idx <> -1) then
        EinsatzCheckListBox.Checked [idx] := True;

      ADOQuery2.Next;
    end;
    ADOQuery2.Close;
  end;

  EinsatzChange := False;
  EinsatzCommitButton.Enabled := False;
  EinsatzRollbackButton.Enabled := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.UpdateLTZuordnung;
var
  idx     : Integer;
begin
  if (LTQuery.Active) and (LTQuery.RecordCount > 0) Then begin
    idx := 0;

    while (idx < ZuordnungCheckListBox.Items.Count) do begin
      ZuordnungCheckListBox.Checked [idx] := False;

      Inc (idx);
    end;

    //Die Zuordnung zu diesem LT-Typen auslesen und den LP-Typen zuordnen
    ADOQuery2.SQL.Clear;
    ADOQuery2.SQL.Add ('select REF_LP_TYPE, OPT_PLATZ from V_LAGER_LP_TYPE_REL_LT_TYPE where REF_LT_TYPE=:ref');
    ADOQuery2.Parameters [0].Value := LTQuery.FieldByName ('REF').AsInteger;

    ADOQuery2.Open;

    while not (ADOQuery2.Eof) do begin
      idx := 0;

      while (idx < ZuordnungCheckListBox.Items.Count) do begin
        if (GetListBoxRef (ZuordnungCheckListBox, idx) = ADOQuery2.Fields [0].AsInteger) Then begin
          ZuordnungCheckListBox.Checked [idx] := (ADOQuery2.Fields [1].AsString = '1');

          break;
        end;

        Inc (idx);
      end;

      ADOQuery2.Next;
    end;

    ADOQuery2.Close;
  end;

  ZuordnungCommitButton.Enabled := False;
  ZuordnungRollbackButton.Enabled := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.UpdateLTMandant;
var
  idx     : Integer;
begin
  if (LTQuery.Active) and (LTQuery.RecordCount > 0) Then begin
    if MandantGroupBox.Visible then begin
      idx := 0;

      while (idx < MandantCheckListBox.Items.Count) do begin
        MandantCheckListBox.Checked [idx] := False;

        Inc (idx);
      end;

      //
      ADOQuery2.SQL.Clear;
      ADOQuery2.SQL.Add ('select REF_MAND from V_LT_TYPEN where STATUS=''AKT'' and REF_BASIS_LT=:ref');

      if LTQuery.FieldByName ('REF_BASIS_LT').IsNull then
        ADOQuery2.Parameters [0].Value := LTQuery.FieldByName ('REF').AsInteger
      else
        ADOQuery2.Parameters [0].Value := LTQuery.FieldByName ('REF_BASIS_LT').AsInteger;

      ADOQuery2.Open;

      while not (ADOQuery2.Eof) do begin
        idx := 0;

        while (idx < MandantCheckListBox.Items.Count) do begin
          if (GetListBoxRef (MandantCheckListBox, idx) = ADOQuery2.Fields [0].AsInteger) Then begin
            MandantCheckListBox.Checked [idx] := True;

            break;
          end;

          Inc (idx);
        end;

        ADOQuery2.Next;
      end;

      ADOQuery2.Close;
    end;
  end;

  MandantCommitButton.Enabled := False;
  MandantRollbackButton.Enabled := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.EinsatzRollbackButtonClick(Sender: TObject);
begin
  UpdateLTEinsatz;

  EinsatzChange := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 07.06.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.Etikettendrucken1Click(Sender: TObject);
var
  prtform : TPrintLPLableForm;
begin
  prtform := TPrintLPLableForm.Create (self);

  prtform.Prepare(UserReg.ReadRegValue ('LE-PRINTER'), 'E9', -1, -1, -1, -1);

  if (prtform.PrinterComboBox.Items.Count = 0) Then
    FrontendMessages.MessageDlg(FormatMessageText (1058, ['E9']), mtError, [mbOK], 0)
  else begin
    prtform.LETabSheet.TabVisible := True;
    prtform.PageControl1.ActivePage := prtform.LETabSheet;

    prtform.LTComboBox.ItemIndex := FindComboboxRef (prtform.LTComboBox, LTQuery.FieldByName ('REF').AsInteger);
    prtform.LTComboBox.Enabled := (prtform.LTComboBox.ItemIndex <> -1);

    prtform.ShowModal;

    UserReg.WriteRegValue ('LE-PRINTER', prtform.GetSelectedPrinter);
  end;

  prtform.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.EinsatzCommitButtonClick(Sender: TObject);
var
  idx,
  res     : Integer;
  strlist : TStringList;
begin
  strlist := TStringList.Create;

  idx := 0;
  while (idx < EinsatzCheckListBox.Items.Count) do begin
    if EinsatzCheckListBox.Checked [idx] then
      strlist.Add ('+' + SeparateGetText (EinsatzCheckListBox.Items, idx))
    else strlist.Add ('-' + SeparateGetText (EinsatzCheckListBox.Items, idx));

    Inc (idx);
  end;

  res := SetLTEinsatz (LTQuery.FieldByName ('REF').AsInteger, strlist);

  if (res <> 0) Then
    FrontendMessages.MessageDLG (FormatMessageText (1217, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
  else begin
    UpdateLTEinsatz;
    
    EinsatzChange := False;
  end;

  strlist.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.NeuLTButtonClick(Sender: TObject);
var
  v,
  vmax,
  tg,
  ng,
  ngmax,
  idx,
  res,
  l, b, h,
  hkl,
  ref,
  refleer,
  intwert,
  fachanz   : Integer;
  weopt,
  waopt,
  pfandopt,
  leeropt,
  tauschopt : Char;
  editform  : TLTEditForm;
begin
  editform := TLTEditForm.Create (Self);

  editform.MandantPanel.Visible  := True;
  editform.LagerPanel.Visible    := False;
  editform.TemplatePanel.Visible := False;

  with editform do begin
    Caption := GetResourceText (1480);

    LoadMandantCombobox (MandantComboBox);
    if (LVSDatenModul.AktMandantRef = -1) then
      MandantComboBox.ItemIndex := -1
    else begin
      MandantComboBox.ItemIndex := FindComboboxRef(MandantComboBox, LVSDatenModul.AktMandantRef);
      MandantComboBox.Enabled := (MandantComboBox.ItemIndex = -1);
    end;

    EDIComboBox.Clear;
    EDIComboBox.Items.Add ('');

    ADOQuery2.SQL.Clear;
    ADOQuery2.SQL.Add ('select WERT,BESCHREIBUNG from V_INF_WERTE where TABELLE=''LT_TYPEN'' and SPALTE=''EDI_CODE''');

    try
      ADOQuery2.Open;

      while not (ADOQuery2.Eof) do begin
        EDIComboBox.Items.Add (ADOQuery2.Fields [0].AsString+'|'+ADOQuery2.Fields [1].AsString);

        ADOQuery2.Next;
      end;

      ADOQuery2.Close;
    except
    end;

    NameEdit.Text         := '';
    IDEdit.Text           := '';
    BeschreibungEdit.Text := '';
    TaraGewichtEdit.Text  := '';

    if (Length (LVSDatenModul.AktLager) = 0) then begin
      LagerComboBox.Enabled   := True;
      LagerComboBox.ItemIndex := 0
    end else begin
      LagerComboBox.Enabled   := False;
      LagerComboBox.ItemIndex := LagerComboBox.IndexOf (LVSDatenModul.AktLager);
    end;

    EDIComboBox.ItemIndex := 0;
    ArtikelNrEdit.Text := '';

    FolgeUpDown.Position := 0;
    FolgeEdit.Text := '';
  end;

  if (editform.ShowModal = mrOk) Then begin
    with editform do begin
      if not FachAnzEdit.Visible or (Length (FachAnzEdit.Text) = 0) then
        fachanz := -1
      else if not TryStrToInt (FachAnzEdit.Text, fachanz) then
        fachanz := -1;

      if (Length (BEdit.Text) = 0) Then
        b := -1
      else b := StrToInt (BEdit.Text);

      if (Length (HEdit.Text) = 0) Then
        h := -1
      else h := StrToInt (HEdit.Text);

      if (Length (LEdit.Text) = 0) Then
        l := -1
      else l := StrToInt (LEdit.Text);

      if (Length (MaxVolumenEdit.Text) = 0) Then
        vmax := -1
      else vmax := Round (StrToFloat (MaxVolumenEdit.Text) * 1000000000);

      if (Length (TaraGewichtEdit.Text) = 0) Then
        tg := -1
      else tg := Round (StrToFloat (TaraGewichtEdit.Text) * 1000);

      if (Length (MaxKommGwEdit.Text) = 0) Then
        ngmax := -1
      else ngmax := Round (StrToFloat (MaxKommGwEdit.Text) * 1000);

      if not (WEPflichtCheckBox.Checked) then
        weopt := #0
      else if (WEPflichtRadioGroup.ItemIndex = 2) then
        weopt := '2'
      else if (WEPflichtRadioGroup.ItemIndex = 1) then
        weopt := '1'
      else
        weopt := '0';

      if not (WAPflichtCheckBox.Checked) then
        waopt := #0
      else if (WAPflichtRadioGroup.ItemIndex = 2) then
        waopt := '2'
      else if (WAPflichtRadioGroup.ItemIndex = 1) then
        waopt := '1'
      else
        waopt := '0';

      pfandopt   := '0';
      tauschopt := '0';

      if (PfandTauschRadioGroup.ItemIndex = 1) then
        tauschopt := '1'
      else if (PfandTauschRadioGroup.ItemIndex = 2) then
        pfandopt := '1';

      if not (LTHKLComboBox.Visible) then
        hkl := -1
      else if (LTHKLComboBox.ItemIndex = 0) then
        hkl := -1
      else if not (TryStrToInt (GetComboBoxDBItemWert (LTHKLComboBox), hkl)) then
        hkl := -1;

      if (LTLeerComboBox.ItemIndex <= 0) then begin
        refleer := -1;
        leeropt := '0';
      end else if (LTLeerComboBox.ItemIndex = 1) then begin
        refleer := -1;
        leeropt := '1';
      end else begin
        refleer := GetComboBoxRef (LTLeerComboBox);
        leeropt := '1';
      end;

      res := CreateLT (LVSConfigModul.RefProject, GetComboBoxRef (MandantComboBox), -1, -1, -1, NameEdit.Text, IDEdit.Text, BeschreibungEdit.Text, EDIComboBox.GetItemText, ArtikelNrEdit.Text, EANEdit.Text, FolgeUpDown.Position, tg, l, b, h, hkl, refleer, vmax, ngmax, weopt, waopt, GetCheckboxStat (PermaCheckBox), pfandopt,
                       leeropt, tauschopt,
                       GetCheckboxStat (AutoDeleteCheckBox), ref);

      if (res = 0) and FachAnzEdit.Visible then
        res := SetLTFachAnzahl (ref, fachanz);

      if (res = 0) and RelocationCheckBox.Visible then
        res := SetLTRelocation (ref, GetCheckboxStat (RelocationCheckBox));

      if (res = 0) and OversizeUpDown.Visible then
        res := SetLTOversize (ref, OversizeUpDown.Position);
    end;

    if (res <> 0) Then
      FrontendMessages.MessageDLG (FormatMessageText (1218, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    else
      LTDBGrid.Reload (ref);
  end;

  editform.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.09.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.PrintRetButtonClick(Sender: TObject);
var
  res    : integer;
  errtxt : String;
begin
  res := PrintModule.PreparePreview;

  if (res = 0) then begin
    res := PrintModule.PrintReport ('', PrintModule.LSLaserPrinter.Port, '', -1, -1, -1, '', 'RET_BARCODE', '', ['AREA:' + GetComboBoxStr (RetZustandAreaComboBox)], errtxt, True);

    PrintModule.BeginPreview;
  end;

  if (res <> 0) Then
    FrontendMessages.MessageDLG (FormatResourceText (1012, [])+#13+#13+errtxt, mtError, [mbOK], 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.RetZustandAreaComboBoxChange(Sender: TObject);
begin
  RetZustandQuery.Close;

  AddTextButton.Enabled := LVSSecurityModule.CheckChangeRecht ('', '', Self.Name, TextTabSheet.Name);

  RetZustandQuery.SQL.Clear;

  if (RetZustandAreaComboBox.ItemIndex < 1) then begin
    PrintRetButton.Enabled := false;

    RetZustandQuery.SQL.Add ('select st.* from V_PCD_RETOUREN_ZUSTAND st where st.STATUS in (''ANG'',''AKT'')');
  end else begin
    PrintRetButton.Enabled := True;

    RetZustandQuery.SQL.Add ('select st.* from V_PCD_RETOUREN_ZUSTAND st where st.STATUS in (''ANG'',''AKT'') and st.AREA=:area');
    RetZustandQuery.Parameters.ParamByName('area').Value := GetComboBoxStr (RetZustandAreaComboBox);
  end;

  if (LVSDatenModul.AktMandantRef = -1) then
    RetZustandQuery.SQL.Add ('and (st.REF_MAND is null or st.REF_MAND in (select REF from V_PCD_MANDANT))')
  else begin
    RetZustandQuery.SQL.Add ('and (st.REF_MAND is null or st.REF_MAND=:ref_mand)');
    RetZustandQuery.Parameters.ParamByName('ref_mand').Value := LVSDatenModul.AktMandantRef;
  end;

  if (RetZustandAreaComboBox.ItemIndex < 1) then
    RetZustandQuery.SQL.Add ('order by st.AREA, st.REIHENFOLGE')
  else
    RetZustandQuery.SQL.Add ('order by st.REIHENFOLGE');

  RetZustandQuery.Open;

  RetZustandDBGrid.SetColumnVisible ('AREA', (RetZustandAreaComboBox.ItemIndex < 1));

  RetZustandDBGrid.SetColumnVisible ('OPT_ADMIN', False);
  RetZustandDBGrid.SetColumnVisible ('OPT_REPRINT_LABEL', False);
  RetZustandDBGrid.SetColumnVisible ('OPT_FINAL_STATUS', False);
  RetZustandDBGrid.SetColumnVisible ('OPT_ANNAHME_STATUS', False);
  RetZustandDBGrid.SetColumnVisible ('OPT_DEFAULT', False);

  RetZustandDBGrid.SetColumnVisible ('STATUS', False);

  RetZustandDBGrid.SetColumnVisible ('MANDANT', LVSDatenModul.AktMandantRef > 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.RetZustandDataSourceDataChange(Sender: TObject; Field: TField);
begin
  EditRetZustandButton.Enabled := False;
  DelRetZustandButton.Enabled := False;

  if (RetZustandQuery.Active and (RetZustandQuery.RecNo > 0)) then begin
    EditRetZustandButton.Enabled := (fRetAdminFlag or (RetZustandQuery.FieldByName('OPT_ADMIN').AsString = '0'));

    DelRetZustandButton.Enabled := EditRetZustandButton.Enabled;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.RetZustandTabSheetShow(Sender: TObject);
begin
  fRetAdminFlag := LVSSecurityModule.CheckChangeRecht (LVSDatenModul.AktMandant, LVSDatenModul.AktLager, '', 'RetourenAdmin');

  RetZustandAreaComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.StdLTButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (LTQuery.Active) and (LTQuery.RecNo > 0) Then begin
    res := SetDefaultLT (LTQuery.FieldByName ('REF').AsInteger);

    if (res <> 0) then
      FrontendMessages.MessageDLG (FormatMessageText (1213, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    else begin
      LTQuery.Refresh;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.StdLTEinlagerMenuItemClick(Sender: TObject);
var
  res : Integer;
begin
  if (LTQuery.Active) and (LTQuery.RecNo > 0) Then begin
    res := SetDefaultLT (LTQuery.FieldByName ('REF').AsInteger, 'EINL');

    if (res <> 0) then
      FrontendMessages.MessageDLG (FormatMessageText (1220, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    else begin
      LTQuery.Refresh;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.StdLTKommMenuItemClick(Sender: TObject);
var
  res : Integer;
begin
  if (LTQuery.Active) and (LTQuery.RecNo > 0) Then begin
    res := SetDefaultLT (LTQuery.FieldByName ('REF').AsInteger, 'KOMM');

    if (res <> 0) then
      FrontendMessages.MessageDLG (FormatMessageText (1221, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    else begin
      LTQuery.Refresh;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.StdLTMenuItemClick(Sender: TObject);
var
  res : Integer;
begin
  if (LTQuery.Active) and (LTQuery.RecNo > 0) Then begin
    res := SetDefaultLT (LTQuery.FieldByName ('REF').AsInteger);

    if (res <> 0) then
      FrontendMessages.MessageDLG (FormatMessageText (1219, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    else begin
      LTQuery.Refresh;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.LHMTabSheetResize(Sender: TObject);
begin
 if MandantGroupBox.Visible then begin
   EinsatzGroupBox.Width := (LTDBGrid.Width div 3) - 8;

   TypGroupBox.Left      := EinsatzGroupBox.Left + EinsatzGroupBox.Width + 8;
   TypGroupBox.Width     := EinsatzGroupBox.Width;

   MandantGroupBox.Left  := TypGroupBox.Left + TypGroupBox.Width + 8;
   MandantGroupBox.Width := EinsatzGroupBox.Width;
 end else begin
   EinsatzGroupBox.Width := (LTDBGrid.Width div 2);

   TypGroupBox.Left      := EinsatzGroupBox.Left + EinsatzGroupBox.Width + 8;
   TypGroupBox.Width     := EinsatzGroupBox.Width;
 end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.LHMTabSheetShow(Sender: TObject);
var
  query : TADOQuery;
begin
  EinsatzCommitButton.Enabled := False;
  EinsatzRollbackButton.Enabled := False;

  MandantCommitButton.Enabled := False;
  MandantRollbackButton.Enabled := False;

  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from V_PCD_LT_TYPEN where rownum=0');

    query.Open;

    MandantGroupBox.Visible := Assigned (query.FindField ('REF_BASIS_LT'));

    query.Close;

    if (MandantCheckListBox.Visible) then begin
      ClearListBoxObjects (MandantCheckListBox);

      query.SQL.Clear;
      query.SQL.Add ('select * from V_PCD_MANDANT where REF in (select REF_MAND from V_MANDANT_REL_LOCATION where REF_LOCATION=:ref_loc)');
      query.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;

      query.Open;

      while not (query.Eof) do begin
        MandantCheckListBox.Items.AddObject (query.FieldByName ('NAME').AsString+'|'+query.FieldByName ('BESCHREIBUNG').AsString, TComboBoxRef.Create (query.FieldByName ('REF').AsInteger));

        query.Next;
      end;

      query.Close;
    end;
  finally
    query.Free;
  end;

  LTQuery.Close;

  LTQuery.SQL.Clear;
  LTQuery.SQL.Add ('select * from V_PCD_LT_TYPEN where STATUS=''AKT'' and REF_LAGER is null and REF_LOCATION is null');

  if MandantGroupBox.Visible then
    LTQuery.SQL.Add ('and REF_MAND is null');

  if (LVSConfigModul.RefProject <> -1) then
    LTQuery.SQL.Add ('and REF_PROJECT='+IntToStr (LVSConfigModul.RefProject));

  LTQuery.Open;

  LTDBGrid.SetColumnVisible ('MANDANT', not MandantGroupBox.Visible);
  LTDBGrid.SetColumnVisible ('DEFAULT_NVE_LT', False);
  LTDBGrid.SetColumnVisible ('DEFAULT_LT', False);
  LTDBGrid.SetColumnVisible ('DEFAULT_EINLAGER_LT', False);
  LTDBGrid.SetColumnVisible ('DEFAULT_KOMM_LT', False);
  LTDBGrid.SetColumnVisible ('PFAND_LT', False);
  LTDBGrid.SetColumnVisible ('TAUSCH_LT', False);
  LTDBGrid.SetColumnVisible ('PERMANENT_LT', False);
  LTDBGrid.SetColumnVisible ('REUSABLE_LT', False);
  LTDBGrid.SetColumnVisible ('LEERGUT_LT', False);
  LTDBGrid.SetColumnVisible ('RANGE_PREFIX', False);
  LTDBGrid.SetColumnVisible ('HOEHEN_KLASSE', False);

  LTDBGrid.SetColumnVisible ('LAGER', Length (LVSDatenModul.AktLager) = 0);

  DBGridUtils.SetGewichtDisplayFunctions (LTDBGrid.DataSource.DataSet, 'TARA_GEWICHT');
  DBGridUtils.SetGewichtDisplayFunctions (LTDBGrid.DataSource.DataSet, 'KOMM_GEWICHT');
  DBGridUtils.SetGewichtDisplayFunctions (LTDBGrid.DataSource.DataSet, 'MAX_KOMM_GEWICHT');

  DBGridUtils.SetVolumenDisplayFunctions (LTDBGrid.DataSource.DataSet, 'KOMM_VOLUMEN');
  DBGridUtils.SetVolumenDisplayFunctions (LTDBGrid.DataSource.DataSet, 'MAX_KOMM_VOLUMEN');

  EinsatzChange := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.TextTabSheetShow(Sender: TObject);
begin
  TextAreaComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.TextAreaComboBoxChange(Sender: TObject);
begin
  TextQuery.Close;

  AddTextButton.Enabled := LVSSecurityModule.CheckChangeRecht ('', '', Self.Name, TextTabSheet.Name);

  TextQuery.SQL.Clear;

  if (TextAreaComboBox.ItemIndex < 1) then begin
    TextQuery.SQL.Add ('select st.REF,st.REF_MAND,(select NAME from V_MANDANT where REF=st.REF_MAND) as MANDANT,st.SPRACHE,st.AREA,st.SUB_AREA,st.DEFINITION,st.TEXT,st.TEXT_NR,st.SEL,st.LINK_NR,st.REFERENZ_NR'
                      +',decode (st.OPT_DEFAULT, ''1'', '''+rsYes+''', ''0'', '''+rsNo+''') as STR_OPT_DEFAULT from V_SYS_TEXTE st'
                      +' where REF is not null'
                      );
  end else begin
    TextQuery.SQL.Add ('select st.REF,st.REF_MAND,(select NAME from V_MANDANT where REF=st.REF_MAND) as MANDANT,st.SPRACHE,st.DEFINITION,st.TEXT,st.TEXT_NR,st.SEL,st.LINK_NR,st.REFERENZ_NR'
                      +',decode (st.OPT_DEFAULT, ''1'', '''+rsYes+''', ''0'', '''+rsNo+''') as STR_OPT_DEFAULT from V_SYS_TEXTE st'
                      +' where st.AREA=:area'
                      );
    TextQuery.Parameters.ParamByName('area').Value := GetComboBoxStr (TextAreaComboBox);
  end;

  if (LVSDatenModul.AktMandantRef = -1) then
    TextQuery.SQL.Add ('and (st.REF_MAND is null or st.REF_MAND in (select REF from V_PCD_MANDANT))')
  else begin
    TextQuery.SQL.Add ('and (st.REF_MAND is null or st.REF_MAND=:ref_mand)');
    TextQuery.Parameters.ParamByName('ref_mand').Value := LVSDatenModul.AktMandantRef;
  end;

  TextQuery.SQL.Add ('order by SEL, TEXT_NR');

  TextQuery.Open;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.TextDataSourceDataChange(Sender: TObject; Field: TField);
begin
  if (TextQuery.Active and (TextQuery.RecNo > 0)) then begin
    EditTextButton.Enabled := LVSSecurityModule.CheckChangeRecht ('', '', Self.Name, TextTabSheet.Name);
    DelTextButton.Enabled  := EditTextButton.Enabled;
  end else begin
    EditTextButton.Enabled := False;
    DelTextButton.Enabled := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 27.07.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.AddRetZustandButtonClick(Sender: TObject);
var
  editfrom : TEditRetZustandForm;
begin
  editfrom := TEditRetZustandForm.Create (Self);

  editfrom.AdminCheckBox.Enabled := fRetAdminFlag;

  editfrom.TextArea := GetComboBoxStr(RetZustandAreaComboBox);

  if (editfrom.ShowModal = mrOk) then
    RetZustandDBGrid.Reload (editfrom.RefZustand);

  editfrom.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.AddTextButtonClick(Sender: TObject);
var
  editfrom : TEditSysTextForm;
begin
  editfrom := TEditSysTextForm.Create (Self);

  editfrom.TextArea := GetComboBoxStr(TextAreaComboBox);

  if (editfrom.ShowModal = mrOk) then
    TextDBGrid.Reload (editfrom.RefText);

  editfrom.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.DelLTButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (LTQuery.Active) and (LTQuery.RecordCount > 0) Then begin
    res := DeleteLT (LTQuery.FieldByName ('REF').AsInteger);

    if (res <> 0) then
      FrontendMessages.MessageDLG (FormatMessageText (1213, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    else
      LTDBGrid.Reload;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.DelRetZustandButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (RetZustandQuery.Active and (RetZustandQuery.RecNo > 0)) then begin
    if (FrontendMessages.MessageDlg (FormatMessageText (1310, []), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then begin
      res := DeleteRetZustand (RetZustandQuery.FieldByName('REF').AsInteger);

      if (res = 0) then
        RetZustandDBGrid.Reload
      else
        FrontendMessages.MessageDLG(FormatMessageText (1309, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.DelTextButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (TextQuery.Active and (TextQuery.RecNo > 0)) then begin
    if (FrontendMessages.MessageDlg (FormatMessageText (1311, [LVSDatenModul.LastLVSErrorText]), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then begin
      res := DeleteSysText (TextQuery.FieldByName('REF').AsInteger);

      if (res = 0) then
        TextDBGrid.Reload
      else
        FrontendMessages.MessageDLG(FormatMessageText (1312, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res : Integer;
begin
  if not (EinsatzChange) then
    CanClose := True
  else begin
    res := FrontendMessages.MessageDlg(FormatMessageText (1313, []), mtConfirmation, [mbYes,mbNo,mbCancel], 0);

    if (res = mrYes) then begin
      EinsatzChange := False;
      CanClose := True;
    end else if (res = mrNo) or (res = mrCancel) then begin
      CanClose := False;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.ListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
var
  line : String;
  strpos : Integer;
begin
  if (Control is TListBox) then begin
    line := (Control as TListBox).Items [Index];
    strpos := Pos ('|', line);

    with (Control as TListBox).Canvas do begin
      FillRect(Rect);

      if (strpos = 0) then
        TextOut (Rect.Left, Rect.Top, line)
      else begin
        TextOut (Rect.Left, Rect.Top, Copy (line,1,strpos - 1));
        TextOut (Rect.Left + 40, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
      end;
    end;
  end else if (Control is TCheckListBox) then begin
    line := (Control as TCheckListBox).Items [Index];
    strpos := Pos ('|', line);

    with (Control as TCheckListBox).Canvas do begin
      FillRect(Rect);

      TextOut (Rect.Left, Rect.Top, Copy (line,1,strpos - 1));
      TextOut (Rect.Left + 80, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.EinsatzCheckListBoxClick(Sender: TObject);
begin
  EinsatzChange := True;

  EinsatzCommitButton.Enabled := True;
  EinsatzRollbackButton.Enabled := True;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 27.07.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.EditLTButtonClick(Sender: TObject);
begin
  LTDBGridDblClick (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 07.06.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.EditLTRangeMenuItemClick(Sender: TObject);
var
  editfrom : TEditLTNumberRangeForm;
begin
  editfrom := TEditLTNumberRangeForm.Create (Self);

  try
    editfrom.RefLoc   := -1;
    editfrom.RefLager := -1;
    editfrom.LagerComboBox.Enabled := false;

    if (Sender = EditLTRangeMenuItem) then
      editfrom.RefLTType := LTQuery.FieldByName ('REF').AsInteger;

    if (editfrom.ShowModal = mrOk) then begin
      if (Sender <> EditLTRangeMenuItem) then
        LTDBGrid.Reload;
    end;
  finally
    editfrom.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.EditRetZustandButtonClick(Sender: TObject);
var
  editfrom : TEditRetZustandForm;
begin
  if (RetZustandQuery.Active and (RetZustandQuery.RecNo > 0)) then begin
    if (fRetAdminFlag or (RetZustandQuery.FieldByName('OPT_ADMIN').AsString = '0')) then begin
      editfrom := TEditRetZustandForm.Create (Self);

      editfrom.AdminCheckBox.Enabled := fRetAdminFlag;

      editfrom.RefZustand := RetZustandQuery.FieldByName('REF').AsInteger;

      if (editfrom.ShowModal = mrOk) then
        RetZustandDBGrid.Reload (editfrom.RefZustand);

      editfrom.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.EditTextButtonClick(Sender: TObject);
var
  editfrom : TEditSysTextForm;
begin
  if (TextQuery.Active and (TextQuery.RecNo > 0)) then begin
    editfrom := TEditSysTextForm.Create (Self);

    editfrom.RefText := TextQuery.FieldByName('REF').AsInteger;

    if (editfrom.ShowModal = mrOk) then
      TextDBGrid.Reload;

    editfrom.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.BesCategoryDataSourceDataChange(Sender: TObject; Field: TField);
begin
  if (BesCategoryQuery.Active and (BesCategoryQuery.RecNo > 0)) then begin
    StorageGroupLabel.Caption := BesCategoryQuery.FieldByName('STORAGE_GROUP').AsString;

    if (BesCategoryQuery.FieldByName('STORAGE_MANF_PURE').AsString = '1') then
      ManfReinLabel.Caption := rsYes
    else ManfReinLabel.Caption := rsNo;

    if (BesCategoryQuery.FieldByName('STORAGE_MIXED_ZUSTAND').AsString = '1') then
      ZustandEgalLabel.Caption := rsYes
    else ZustandEgalLabel.Caption := rsNo;

    if (BesCategoryQuery.FieldByName('STORAGE_GROUP').AsString = '1') then
      ManfReinLabel.Caption := rsYes
    else ManfReinLabel.Caption := rsNo;

    if (BesCategoryQuery.FieldByName('OPT_ASK_CLARIFY_HINT').AsString = '1') then
      AskAddInfoLabel.Caption := rsYes
    else AskAddInfoLabel.Caption := rsNo;
  end else begin
    StorageGroupLabel.Caption := '';
    ManfReinLabel.Caption     := '';
    ZustandEgalLabel.Caption  := '';
    ManfReinLabel.Caption     := '';
    AskAddInfoLabel.Caption   := '';
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.BesCategoryTabSheetShow(Sender: TObject);
begin
  StorageGroupLabel.Caption := '';
  ManfReinLabel.Caption     := '';
  ZustandEgalLabel.Caption  := '';
  ManfReinLabel.Caption     := '';
  AskAddInfoLabel.Caption   := '';

  BesCategoryQuery.Close;

  BesCategoryQuery.SQL.Clear;
  BesCategoryQuery.SQL.Add ('select * from V_PCD_BESTAND_CATEGORY where STATUS=''AKT''');

  BesCategoryQuery.Open;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.FormActivate(Sender: TObject);
begin
  if Assigned (FrontendACOModule) then
    FrontendACOModule.SetBerechtigungen (Self);

  TextAreaComboBox.AddItem ('-', nil);

  if (RetZustandTabSheet.TabVisible) then begin
    RetZustandAreaComboBox.AddItem (GetResourceText(1483), TComboBoxStr.Create ('RET_GRUND_RET'));
    RetZustandAreaComboBox.AddItem (GetResourceText(1484), TComboBoxStr.Create ('RET_GRUND_POS'));
    RetZustandAreaComboBox.AddItem (GetResourceText(1485), TComboBoxStr.Create ('RET_QS_GRUND_POS'));
    RetZustandAreaComboBox.AddItem (GetResourceText(1486), TComboBoxStr.Create ('RET_QS_ZUSTAND_POS'));
    RetZustandAreaComboBox.AddItem (GetResourceText(1835), TComboBoxStr.Create ('RET_QS_ZUSTAND_COMMENT'));
    RetZustandAreaComboBox.AddItem (GetResourceText(1839), TComboBoxStr.Create ('RET_POS_COMMENT'));
    RetZustandAreaComboBox.AddItem (GetResourceText(1838), TComboBoxStr.Create ('RET_POS_QS_COMMENT'));
  end else begin
    TextAreaComboBox.AddItem (GetResourceText(1483), TComboBoxStr.Create ('RET_GRUND_RET'));
    TextAreaComboBox.AddItem (GetResourceText(1484), TComboBoxStr.Create ('RET_GRUND_POS'));
    TextAreaComboBox.AddItem (GetResourceText(1485), TComboBoxStr.Create ('RET_QS_GRUND_POS'));
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  LTQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, EinsatzCheckListBox);
    LVSSprachModul.SetNoTranslate (Self, EinsatzGroupBox);
    LVSSprachModul.SetNoTranslate (Self, TextAreaComboBox);
    LVSSprachModul.SetNoTranslate (Self, RetZustandAreaComboBox);
    LVSSprachModul.SetNoTranslate (Self, StorageGroupLabel);
    LVSSprachModul.SetNoTranslate (Self, ManfReinLabel);
    LVSSprachModul.SetNoTranslate (Self, ZustandEgalLabel);
    LVSSprachModul.SetNoTranslate (Self, AskAddInfoLabel);
  {$endif}

  EinsatzCheckListBox.Items.Add('WE|'+GetResourceText(1297));
  EinsatzCheckListBox.Items.Add('WA|'+GetResourceText(1298));
  EinsatzCheckListBox.Items.Add('LE|'+GetResourceText(1299));
  EinsatzCheckListBox.Items.Add('NVE|'+GetResourceText(1300));
  EinsatzCheckListBox.Items.Add('TRA|'+GetResourceText(1301));
  EinsatzCheckListBox.Items.Add('VTL|'+GetResourceText(1302));
  EinsatzCheckListBox.Items.Add('KOM|'+GetResourceText(1303));
  EinsatzCheckListBox.Items.Add('PACK|'+GetResourceText(1336));

  //Alle Basis LP-Typen auslesen
  ADOQuery2.SQL.Clear;
  ADOQuery2.SQL.Add ('select REF, TYP, BEZEICHNUNG from V_LAGER_LP_TYPEN where REF_LAGER is null and REF_LOCATION is null');

  ADOQuery2.Open;

  while not (ADOQuery2.Eof) do begin
    ZuordnungCheckListBox.Items.AddObject (ADOQuery2.Fields [1].AsString+'|'+ADOQuery2.Fields [2].AsString, TListBoxRef.Create(ADOQuery2.Fields [0].AsInteger));

    ADOQuery2.Next;
  end;

  ADOQuery2.Close;


  TextAreaComboBox.Items.Insert (0, GetResourceText(1004));

  TextAreaComboBox.AddItem (GetResourceText(1490), TComboBoxStr.Create ('LOCK_LP'));
  TextAreaComboBox.AddItem (GetResourceText(1491), TComboBoxStr.Create ('CREATE_BES'));
  TextAreaComboBox.AddItem ('-', nil);

  if (LVSConfigModul.UseQS) then begin
    TextAreaComboBox.AddItem (GetResourceText(1504), TComboBoxStr.Create ('QS_BES'));

    TextAreaComboBox.AddItem ('-', nil);
  end;

  TextAreaComboBox.AddItem (GetResourceText(1492), TComboBoxStr.Create ('LOCK_BES'));
  TextAreaComboBox.AddItem (GetResourceText(1493), TComboBoxStr.Create ('UNLOCK_BES'));
  TextAreaComboBox.AddItem (GetResourceText(1494), TComboBoxStr.Create ('DEL_BES'));
  TextAreaComboBox.AddItem (GetResourceText(1495), TComboBoxStr.Create ('CHANGE_BES'));
  TextAreaComboBox.AddItem (GetResourceText(1854), TComboBoxStr.Create ('DISPOSE_BES'));

  if (LVSConfigModul.UseBesCategory) then
    TextAreaComboBox.AddItem (GetResourceText(1496), TComboBoxStr.Create ('BES_CAT'));

  TextAreaComboBox.AddItem ('-', nil);

  TextAreaComboBox.AddItem (GetResourceText(1497), TComboBoxStr.Create ('QS_GRUND_POS'));
  TextAreaComboBox.AddItem ('-', nil);

  TextAreaComboBox.AddItem (GetResourceText(1498), TComboBoxStr.Create ('BEST_POS'));
  TextAreaComboBox.AddItem (GetResourceText(1499), TComboBoxStr.Create ('AUF_POS'));
  TextAreaComboBox.AddItem (GetResourceText(1500), TComboBoxStr.Create ('QS_GRUND_BEST_POS'));
  TextAreaComboBox.AddItem ('-', nil);

  TextAreaComboBox.AddItem (GetResourceText(1828), TComboBoxStr.Create ('AUF_CHANGE_SOLL'));
  TextAreaComboBox.AddItem (GetResourceText(1829), TComboBoxStr.Create ('AUF_CHANGE_TOUR'));
  TextAreaComboBox.AddItem ('-', nil);

  TextAreaComboBox.AddItem (GetResourceText(1501), TComboBoxStr.Create ('AUF_RESET'));
  TextAreaComboBox.AddItem (GetResourceText(1502), TComboBoxStr.Create ('AUF_DELETE'));
  TextAreaComboBox.AddItem (GetResourceText(1503), TComboBoxStr.Create ('AUF_STORNO'));

  TextAreaComboBox.ItemIndex := 0;

  RetZustandAreaComboBox.Items.Insert (0, GetResourceText(1004));
  RetZustandAreaComboBox.ItemIndex := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects(TextAreaComboBox);
  ClearComboBoxObjects(RetZustandAreaComboBox);
  ClearListBoxObjects (EinsatzCheckListBox);
  ClearListBoxObjects (ZuordnungCheckListBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
begin
  if (Key = VK_F5) then begin
    if (PageControl1.ActivePage = LHMTabSheet) then
      LTDBGrid.Reload
    else if (PageControl1.ActivePage = TextTabSheet) then
      TextDBGrid.Reload
    else if (PageControl1.ActivePage = TextTabSheet) then
      TextDBGrid.Reload
    else if (PageControl1.ActivePage = RetZustandTabSheet) then
      RetZustandDBGrid.Reload;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.FormResize(Sender: TObject);
begin
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.ZuordnungCheckListBoxClick(Sender: TObject);
begin
  ZuordnungChange := True;

  ZuordnungCommitButton.Enabled := True;
  ZuordnungRollbackButton.Enabled := True;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.ZuordnungCommitButtonClick(Sender: TObject);
var
  res,
  idx : Integer;
begin
  res := 0;

  idx := 0;
  while (idx < ZuordnungCheckListBox.Items.Count) and (res = 0) do begin
    res := SetLPLTZuordnung (GetListBoxRef (ZuordnungCheckListBox, idx), LTQuery.FieldByName ('REF').AsInteger, ZuordnungCheckListBox.Checked [idx]);

    Inc (Idx);
  end;

  if (res = 0) Then
    UpdateLTEinsatz
  else
    FrontendMessages.MessageDLG (FormatMessageText (1214, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBaseStammdatenForm.ZuordnungRollbackButtonClick(Sender: TObject);
begin
  UpdateLTZuordnung;
end;

end.
