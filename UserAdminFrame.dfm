object UserAdminFrame: TUserAdminFrame
  Left = 0
  Top = 0
  Width = 843
  Height = 827
  Align = alClient
  Constraints.MinHeight = 720
  Constraints.MinWidth = 400
  TabOrder = 0
  TabStop = True
  OnResize = FormResize
  DesignSize = (
    843
    827)
  object Bevel1: TBevel
    Left = 8
    Top = 409
    Width = 7256
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 719
  end
  object Label8: TLabel
    Left = 8
    Top = 416
    Width = 36
    Height = 15
    Caption = 'Rechte'
  end
  object Label9: TLabel
    Left = 408
    Top = 416
    Width = 115
    Height = 15
    Caption = 'Zugewiesende Rechte'
  end
  object Label10: TLabel
    Left = 8
    Top = 8
    Width = 119
    Height = 15
    Caption = 'Benutzer und Gruppen'
  end
  object Label11: TLabel
    Left = 304
    Top = 8
    Width = 74
    Height = 15
    Caption = 'Eigenschaften'
  end
  object UserGrpTreeView: TTreeView
    Left = 8
    Top = 27
    Width = 290
    Height = 376
    HideSelection = False
    Images = ImageList1
    Indent = 19
    PopupMenu = PopupMenu1
    ReadOnly = True
    RightClickSelect = True
    RowSelect = True
    TabOrder = 0
    OnAdvancedCustomDrawItem = UserGrpTreeViewAdvancedCustomDrawItem
    OnChange = UserGrpTreeViewChange
    OnDeletion = UserGrpTreeViewDeletion
    OnMouseDown = UserGrpTreeViewMouseDown
    OnMouseUp = UserGrpTreeViewMouseUp
  end
  object AddACOWButton: TButton
    Left = 359
    Top = 543
    Width = 41
    Height = 17
    Caption = '>> W'
    TabOrder = 3
    OnClick = AddACOButtonClick
  end
  object DelACOEButton: TButton
    Left = 303
    Top = 679
    Width = 41
    Height = 17
    Caption = '<< E'
    TabOrder = 4
    OnClick = DelACOButtonClick
  end
  object AddACOEButton: TButton
    Left = 359
    Top = 495
    Width = 41
    Height = 17
    Caption = '>> E'
    TabOrder = 5
    OnClick = AddACOButtonClick
  end
  object AddACORButton: TButton
    Left = 359
    Top = 519
    Width = 41
    Height = 17
    Caption = '>> R'
    TabOrder = 6
    OnClick = AddACOButtonClick
  end
  object AddACOAButton: TButton
    Left = 359
    Top = 567
    Width = 41
    Height = 17
    Caption = '>> A'
    TabOrder = 7
    OnClick = AddACOButtonClick
  end
  object DelACORButton: TButton
    Left = 303
    Top = 655
    Width = 41
    Height = 17
    Caption = '<< R'
    TabOrder = 8
    OnClick = DelACOButtonClick
  end
  object DelACOWButton: TButton
    Left = 303
    Top = 631
    Width = 41
    Height = 17
    Caption = '<< W'
    TabOrder = 9
    OnClick = DelACOButtonClick
  end
  object DelACOAButton: TButton
    Left = 303
    Top = 607
    Width = 41
    Height = 17
    Caption = '<< A'
    TabOrder = 10
    OnClick = DelACOButtonClick
  end
  object ACOStringGrid: TStringGridPro
    Left = 406
    Top = 456
    Width = 4531
    Height = 8688
    Anchors = [akLeft, akTop, akRight, akBottom]
    ColCount = 4
    DefaultColWidth = 20
    DefaultRowHeight = 20
    RowCount = 2
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRowSelect]
    TabOrder = 11
    OnClick = ACOStringGridClick
    OnEnter = ACOStringGridEnter
    GridStyle.EvenColor = clInfoBk
    TitelTexte.Strings = (
      ''
      'Gruppe'
      'Rechte'
      'ACO-Name')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'MS Sans Serif'
    TitelFont.Style = [fsBold]
    ExplicitWidth = 4139
    ExplicitHeight = 8581
    ColWidths = (
      20
      52
      63
      143)
  end
  object AddACOGButton: TButton
    Left = 303
    Top = 531
    Width = 41
    Height = 18
    Caption = '>> G'
    TabOrder = 12
    OnClick = AddACOButtonClick
  end
  object DelACOGButton: TButton
    Left = 359
    Top = 639
    Width = 41
    Height = 17
    Caption = '<< G'
    TabOrder = 13
    OnClick = DelACOButtonClick
  end
  inline ACLFrame1: TACLFrame
    Left = 8
    Top = 440
    Width = 289
    Height = 13269
    Anchors = [akLeft, akTop, akBottom]
    TabOrder = 14
    ExplicitLeft = 8
    ExplicitTop = 440
    ExplicitWidth = 289
    ExplicitHeight = 13269
    inherited ACOTreeView: TTreeView
      Width = 289
      Height = 13198
      OnChange = ACLFrame1ACOTreeViewChange
      ExplicitWidth = 289
      ExplicitHeight = 13198
    end
    inherited Panel1: TPanel
      Width = 289
      ExplicitWidth = 289
      inherited AppComboBox: TComboBoxPro
        Width = 289
        ExplicitWidth = 289
      end
      inherited GroupComboBox: TComboBoxPro
        Width = 289
        ExplicitWidth = 289
      end
    end
  end
  object ActiveUserCheckBox: TCheckBox
    Left = 168
    Top = 7
    Width = 130
    Height = 17
    Caption = 'Nur aktive Benutzer'
    Checked = True
    State = cbChecked
    TabOrder = 15
    OnClick = ActiveUserCheckBoxClick
  end
  object UserGroupBox: TGroupBox
    Left = 304
    Top = 24
    Width = 486
    Height = 379
    Caption = ' Benutzer '
    Constraints.MinWidth = 400
    TabOrder = 1
    DesignSize = (
      486
      379)
    object Label1: TLabel
      Left = 8
      Top = 16
      Width = 62
      Height = 15
      Caption = 'Benutzer-ID'
    end
    object Label2: TLabel
      Left = 8
      Top = 64
      Width = 68
      Height = 15
      Caption = 'Bezeichnung'
    end
    object Label3: TLabel
      Left = 152
      Top = 112
      Width = 47
      Height = 15
      Caption = 'Funktion'
    end
    object Label13: TLabel
      Left = 8
      Top = 112
      Width = 30
      Height = 15
      Caption = 'Firma'
    end
    object Label14: TLabel
      Left = 9
      Top = 156
      Width = 75
      Height = 15
      Caption = 'eMail-Adresse'
    end
    object Label15: TLabel
      Left = 192
      Top = 16
      Width = 42
      Height = 15
      Caption = 'num. ID'
    end
    object Label16: TLabel
      Left = 265
      Top = 16
      Width = 32
      Height = 15
      Anchors = [akTop, akRight]
      Caption = 'K'#252'rzel'
    end
    object Label17: TLabel
      Left = 264
      Top = 64
      Width = 42
      Height = 15
      Anchors = [akTop, akRight]
      Caption = 'Sprache'
    end
    object UserIDEdit: TEdit
      Left = 8
      Top = 32
      Width = 177
      Height = 23
      TabOrder = 0
      Text = 'UserIDEdit'
      OnChange = UserDataChange
      OnKeyPress = UserIDEditKeyPress
    end
    object UserNameEdit: TEdit
      Left = 8
      Top = 80
      Width = 246
      Height = 23
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 3
      Text = 'UserNameEdit'
      OnChange = UserDataChange
    end
    object UserFunkEdit: TEdit
      Left = 152
      Top = 128
      Width = 221
      Height = 23
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 6
      Text = 'UserFunkEdit'
      OnChange = UserDataChange
    end
    object NewUserButton: TButton
      Left = 387
      Top = 12
      Width = 92
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Neuer User...'
      TabOrder = 11
      OnClick = NewUserButtonClick
    end
    object SaveUserButton: TButton
      Left = 387
      Top = 43
      Width = 92
      Height = 25
      Anchors = [akTop, akRight]
      Caption = #220'bernehmen'
      TabOrder = 12
      OnClick = SaveUserButtonClick
    end
    object UserGrpGroupBox: TGroupBox
      Left = 8
      Top = 264
      Width = 470
      Height = 105
      Anchors = [akLeft, akTop, akRight]
      Caption = ' Gruppenzuordnung '
      TabOrder = 10
      DesignSize = (
        470
        105)
      object UserGrpListBox: TListBox
        Left = 8
        Top = 24
        Width = 322
        Height = 73
        Style = lbOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight, akBottom]
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Microsoft Sans Serif'
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        OnDrawItem = UserGrpListBoxDrawItem
      end
      object Button5: TButton
        Left = 341
        Top = 70
        Width = 121
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Gruppe hinzuf'#252'gen'
        TabOrder = 1
        OnClick = Button5Click
      end
      object Button6: TButton
        Left = 341
        Top = 24
        Width = 121
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Aus Gruppe entfernen'
        TabOrder = 2
        OnClick = Button6Click
      end
    end
    object UserDOMGroupBox: TGroupBox
      Left = 8
      Top = 201
      Width = 470
      Height = 61
      Anchors = [akLeft, akTop, akRight]
      Caption = 'Windows Dom'#228'n User'
      TabOrder = 9
      DesignSize = (
        470
        61)
      object Label6: TLabel
        Left = 8
        Top = 16
        Width = 71
        Height = 15
        Caption = 'Win-Dom'#228'ne'
      end
      object Label7: TLabel
        Left = 144
        Top = 16
        Width = 97
        Height = 15
        Caption = 'Dom'#228'n-Username'
      end
      object DomUserEdit: TEdit
        Left = 144
        Top = 32
        Width = 318
        Height = 23
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 1
        Text = 'DomUserEdit'
        OnChange = UserDataChange
      end
      object DomComboBox: TComboBox
        Left = 8
        Top = 32
        Width = 129
        Height = 23
        TabOrder = 0
        Text = 'DComboBox'
        OnChange = UserDataChange
      end
    end
    object AdminCheckBox: TCheckBox
      Left = 387
      Top = 130
      Width = 89
      Height = 17
      Anchors = [akTop, akRight]
      Caption = 'Administrator'
      TabOrder = 7
      OnClick = UserDataChange
    end
    object DelUserButton: TButton
      Left = 387
      Top = 74
      Width = 92
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'L'#246'schen...'
      TabOrder = 13
      OnClick = DelUserButtonClick
    end
    object FirmaComboBox: TComboBoxPro
      Left = 8
      Top = 128
      Width = 129
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 5
      OnChange = UserDataChange
    end
    object UserMailEdit: TEdit
      Left = 8
      Top = 172
      Width = 365
      Height = 23
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 128
      TabOrder = 8
      Text = 'UserMailEdit'
      OnChange = UserDataChange
    end
    object UserNumIDEdit: TEdit
      Left = 192
      Top = 32
      Width = 62
      Height = 23
      TabOrder = 1
      Text = 'UserNumIDEdit'
      OnChange = UserDataChange
      OnKeyPress = UserNumIDEditKeyPress
    end
    object ShortNameEdit: TEdit
      Left = 263
      Top = 32
      Width = 110
      Height = 23
      Anchors = [akTop, akRight]
      MaxLength = 8
      TabOrder = 2
      Text = 'ShortNameEdit'
      OnChange = UserDataChange
    end
    object LangComboBox: TComboBoxPro
      Left = 263
      Top = 80
      Width = 110
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akTop, akRight]
      ItemHeight = 15
      TabOrder = 4
      OnChange = UserDataChange
    end
  end
  object GrpGroupBox: TGroupBox
    Left = 431
    Top = 14
    Width = 400
    Height = 323
    Caption = ' Gruppe '
    TabOrder = 2
    DesignSize = (
      400
      323)
    object Label5: TLabel
      Left = 8
      Top = 40
      Width = 32
      Height = 15
      Caption = 'Name'
    end
    object Label4: TLabel
      Left = 8
      Top = 83
      Width = 72
      Height = 15
      Caption = 'Beschreibung'
    end
    object Label12: TLabel
      Left = 8
      Top = 128
      Width = 128
      Height = 15
      Caption = 'G'#252'ltig f'#252'r Niederlassung'
    end
    object GrpNameEdit: TEdit
      Left = 8
      Top = 56
      Width = 129
      Height = 23
      MaxLength = 32
      TabOrder = 5
      Text = 'GrpNameEdit'
      OnChange = GrpDatenChange
    end
    object NewGrpButton: TButton
      Left = 309
      Top = 12
      Width = 85
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Neue Gruppe'
      TabOrder = 8
      OnClick = NewGrpButtonClick
    end
    object SaveGrpButton: TButton
      Left = 309
      Top = 43
      Width = 85
      Height = 25
      Anchors = [akTop, akRight]
      Caption = #220'bernehmen'
      TabOrder = 9
      OnClick = SaveGrpButtonClick
    end
    object DelGrpButton: TButton
      Left = 309
      Top = 75
      Width = 85
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'L'#246'schen...'
      TabOrder = 10
      OnClick = DelGrpButtonClick
    end
    object GrpBezEdit: TEdit
      Left = 8
      Top = 99
      Width = 288
      Height = 23
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 6
      Text = 'GrpBezEdit'
      OnChange = GrpDatenChange
    end
    object GrpLocComboBox: TComboBoxPro
      Left = 7
      Top = 144
      Width = 289
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      TabOrder = 7
      OnChange = GrpDatenChange
    end
    object GrpACORadioButton: TRadioButton
      Left = 160
      Top = 14
      Width = 143
      Height = 17
      Caption = 'Rechte-Gruppe'
      Enabled = False
      TabOrder = 1
    end
    object GrpKOMMRadioButton: TRadioButton
      Left = 160
      Top = 32
      Width = 121
      Height = 17
      Caption = 'Komm-Gruppe'
      Enabled = False
      TabOrder = 3
    end
    object GrpNACHRadioButton: TRadioButton
      Left = 160
      Top = 50
      Width = 129
      Height = 17
      Caption = 'Nachschub-Gruppe'
      Enabled = False
      TabOrder = 2
    end
    object AdminGroupCheckBox: TCheckBox
      Left = 8
      Top = 17
      Width = 97
      Height = 17
      Caption = 'Admin-Gruppe'
      TabOrder = 0
      OnClick = GrpDatenChange
    end
    object GrpTRANSRadioButton: TRadioButton
      Left = 160
      Top = 68
      Width = 129
      Height = 17
      Caption = 'Transport-Gruppe'
      Enabled = False
      TabOrder = 4
    end
    object GrpUserGroupBox: TGroupBox
      Left = 8
      Top = 176
      Width = 384
      Height = 137
      Anchors = [akLeft, akTop, akRight]
      Caption = ' Mitglieder '
      TabOrder = 11
      DesignSize = (
        384
        137)
      object GrpUserListBox: TListBox
        Left = 8
        Top = 24
        Width = 236
        Height = 105
        TabStop = False
        Style = lbOwnerDrawVariable
        Anchors = [akLeft, akTop, akRight, akBottom]
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Microsoft Sans Serif'
        Font.Style = []
        Items.Strings = (
          'Test    : Test'
          'LVS     : Lvs')
        ParentFont = False
        TabOrder = 0
        OnDrawItem = GrpUserListBoxDrawItem
      end
      object Button1: TButton
        Left = 255
        Top = 24
        Width = 121
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Aus Gruppe entfernen'
        TabOrder = 1
        OnClick = Button1Click
      end
    end
  end
  object PopupMenu1: TPopupMenu
    AutoPopup = False
    Images = ImageList1
    OnPopup = PopupMenu1Popup
    Left = 80
    Top = 80
    object NeuerBenutzer: TMenuItem
      Caption = 'Neuer Benutzer anlegen...'
      ImageIndex = 5
      OnClick = NeuerBenutzerClick
    end
    object SimilarBenutzerAnlegen: TMenuItem
      Caption = #196'hnlicher Benutzer anlegen...'
      OnClick = SimilarBenutzerAnlegenClick
    end
    object N4: TMenuItem
      Caption = '-'
    end
    object Benutzerrechtesynchronisieren1: TMenuItem
      Caption = 'Benutzerrechte synchronisieren...'
      OnClick = Benutzerrechtesynchronisieren1Click
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object BenutzerFreigeben: TMenuItem
      Caption = 'Benutzer freigeben'
      OnClick = BenutzerFreigebenClick
    end
    object OracleUserunlock1: TMenuItem
      Caption = 'Oracle-User unlock'
      OnClick = OracleUserunlock1Click
    end
    object BenutzerSperren: TMenuItem
      Caption = 'Benutzer sperren'
      ImageIndex = 8
      OnClick = BenutzerSperrenClick
    end
    object Benutzerlschen1: TMenuItem
      Caption = 'Benutzer l'#246'schen'
      ImageIndex = 6
      OnClick = BenutzerDeleteClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object ChangePasswort: TMenuItem
      Caption = 'Passwort zur'#252'cksetzen...'
      ImageIndex = 7
      OnClick = ChangePasswortClick
    end
    object N3: TMenuItem
      Caption = '-'
    end
    object NeueGruppe: TMenuItem
      Caption = 'Neue Gruppe...'
      ImageIndex = 9
      OnClick = NeueGruppeClick
    end
    object N5: TMenuItem
      Caption = '-'
    end
    object PrintKommBarcodeMenuItem: TMenuItem
      Caption = 'Kommissionierer-Barcodes drucken...'
      OnClick = PrintKommBarcodeMenuItemClick
    end
  end
  object ImageList1: TImageList
    Left = 520
    Top = 8
    Bitmap = {
      494C01010A000C00040010001000FFFFFFFFFF10FFFFFFFFFFFFFFFF424D3600
      0000000000003600000028000000400000003000000001002000000000000030
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000DEEFF700ADC6DE00C68C7300CE73
      3100D67B4200D67B4200D67B4200CE7B3900B55A3900B5BDCE006BA58C002173
      4200186B31002173420084B59400000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000E7EFF7009CADE700526BC600395AC6003952BD004A63C60094A5DE00E7E7
      F70000000000000000000000000000000000529CE7007384AD00CE7B5A00FFBD
      6300FFB56300FFB55A00FFB55A00FFB55A00DE8C52004A6B5A00298C520063BD
      8C0094D6B50063BD8C00298C520084AD94000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000000000000000000000000000C6CE
      EF005263CE003952CE00737BEF008C94EF008C94EF00737BE700314AC600425A
      BD00BDC6E7000000000000000000000000003184D60094ADCE00E7AD7300FFBD
      5A00FFB55A00FFA55A00FFA55200FF944A00FF944A00216B390063BD8C0063BD
      84000000000063BD840063BD8C00217339000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000C6CEEF005263
      CE005A63E700A5A5F7007B84EF005A63EF005A5AE7007B84EF009CA5F700525A
      D6003152BD00BDC6E7000000000000000000DEC6B500B5735A00FFD6B500FFD6
      8400FFCE6300FFC65A00FFB55200FFA55200FFC69400317B4A009CD6B5000000
      0000000000000000000094D6B500186B31000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000000000000000000000000000000EFEFFF006373D600636B
      E700A5ADF700525AEF00525AEF004A5AEF004A5AE7004A52E7005252E7009CA5
      F7005263D600425ABD00E7E7F7000000000000000000DEB59C00CE7B5A00FFEF
      C600ADAD8400A59C7B00A5947300F7CE9C00E79473004A845A0094D6B50094D6
      B5000000000063BD8C0063BD8C00217339000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000000000000000000000000000000ADB5EF004A52DE00A5AD
      F7005263F7005263EF004A5AEF004A5AEF004A5AEF004A5AEF004A5AE700525A
      E7009CA5F7003152C60094A5DE00000000000000000000000000CE9C8400737B
      940073ADE70084B5E7007BB5E7006B9CC60073522900529C630063AD840094D6
      B500BDE7D6006BBD8C00298C5200639C8C000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000000000000000000000000000000737BDE00848CEF007B94
      F7005A73F7004A5AEF004A5AEF004A5AEF004A5AEF004A5AEF004A5AEF004A5A
      EF007B84F700737BE7004A63C600000000000000000000000000BDB5BD0073A5
      D600ADD6F700ADD6FF00B5D6F70094C6E70052848C007BCE94006BB57B005A94
      73004A8C630042845A00428452004A9CE7000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000000000000000000000000000000636BDE00A5ADF7007384
      FF006B84F7000000000000000000000000000000000000000000000000004A5A
      EF005A63EF00949CF7003952BD000000000000000000000000006B8CB500A5BD
      DE0094C6F700A5D6FF0094C6F700B5CEE700427BA5007BCE8C0073CE8C0073CE
      84006BCE7B006BCE7B00639463004A94DE000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000000000006B6BDE00ADBDFF007B94
      FF007384F7000000000000000000000000000000000000000000000000004A5A
      EF005A6BEF00949CF7003952C600000000000000000000000000637BAD00526B
      9C006384AD007BA5D6005273A500425A940042639C007BD69C0073CE940073CE
      8C0073CE8C009CDEAD006BB573008CA58C000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000000000007B7BE700A5ADF7009CAD
      FF00738CF700525AEF00525AEF00525AEF00525AEF00525AEF00525AEF006373
      F700848CF7007B7BEF00526BCE00000000000000000000000000C6CED6004263
      9400425A9400425A9400425A9400425A94006B8C9C00849C94007B948C00738C
      7B00A5D6B50084BD840073AD7B00F7FFF7000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000000000000000000000000000000B5B5F7007B84EF00CED6
      FF008C9CFF007B94F700738CEF006B84F7006B84F7006B84F7006B84F700637B
      F700A5ADFF00394AD600A5ADE70000000000000000000000000000000000ADB5
      C6004A6B9C00425A940042639C00527B84006B7B940073ADE70084B5E7007BB5
      E7006B9CC6006B946B00F7FFF700000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000000000000000000000000000000EFEFFF007B7BE700A5A5
      F700D6DEFF00849CFF007B94F7007B8CF7007B94FF007B94FF00738CFF00ADB5
      FF00636BE7005A6BCE00E7EFF700000000000000000000000000000000000000
      0000000000000000000000000000BDB5BD0073A5D600ADD6F700ADD6FF00B5D6
      F70094C6E7007B94AD0000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000CECEF700736B
      E700ADADF700DEDEFF00ADBDFF0094A5FF008C9CFF009CADFF00BDC6FF00737B
      EF005263CE00C6CEEF0000000000000000000000000000000000000000000000
      00000000000000000000000000007BA5C600BDD6E70094C6F700A5D6FF0094C6
      F700B5CEE7004A94C60000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000000000000000000000000000CECE
      F7007B7BE7008C94EF00BDC6FF00CED6FF00C6CEFF00ADB5F7006373E700636B
      D600C6CEEF000000000000000000000000000000000000000000000000000000
      00000000000000000000000000007BA5C60031ADE70063BDE7008CCEF70039AD
      E70018A5E700319CD60000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000EFEFFF00B5B5F7007B7BE7006B6BDE006B6BDE00737BDE00ADB5EF00EFEF
      FF00000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000C6CEDE0021A5DE0018A5E70018A5E70018A5
      E70018A5E70084ADC60000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000ADBDCE00319CD60018A5E70021A5
      DE0073A5C600FFFFFF0000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000000000000000000094B5E700215A
      C6000042BD00185AC60084A5DE00000000000000000000000000000000000000
      000000000000000000000000000000000000000000000000000084B594002173
      4200186B31002173420084B59400000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000A5D6E7004AA5D600429CD6004294
      CE0094C6DE000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000F7FFFF00F7F7
      F700D6947B00AD523900AD4A2900AD4A2100AD4A2100634A73002163C6002173
      E700007BEF000063DE00004ABD0084A5DE000000000000000000F7FFFF00F7F7
      F700D6947B00AD523900AD4A2900AD4A2100AD4A21005A5A3100298C520063BD
      8C0094D6B50063BD8C00298C520084AD94000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000000000000000000000063BDE700C6EFF7007BE7F7009CE7
      F7003994CE008CC6DE0000000000000000000000000000000000000000000000
      00000000000000000000000000000000000000000000BDD6EF00297BCE008C63
      6300BD633100FFBD6300FFBD6300FFBD6300FFBD63001852AD00639CF700187B
      FF000073FF000073EF00006BE700185AC60000000000BDD6EF00297BCE008C63
      6300BD633100FFBD6300FFBD6300FFBD6300FFBD6300216B390063BD8C0063BD
      84000000000063BD840063BD8C00217339000000000000000000000000000000
      9C0000009C000000000000000000000000000000000000000000000000000000
      9C0000009C0000000000000000000000000042B5E700C6F7FF0042D6F7004ADE
      F70084E7F700398CCE008CBDD600000000000000000000000000000000000000
      00000000000000000000000000000000000000000000297BD60084BDEF009C63
      5A00F7BD8400FFAD5A00FFAD5A00FFA55200FF9C52000042BD00ADCEFF000000
      00000000000000000000187BEF000042BD0000000000297BD60084BDEF009C63
      5A00F7BD8400FFAD5A00FFAD5A00FFA55200FF9C5200317B4A009CD6B5000000
      0000000000000000000094D6B500186B3100000000000000000000009C00104A
      FF001039F70000009C000000000000000000000000000000000000009C000029
      FF000031FF0000009C0000000000000000004AB5E700BDEFFF0039D6F70029C6
      EF004ADEF70084E7F7004294CE0084BDD6000000000000000000000000000000
      00000000000000000000000000000000000000000000297BCE007BB5EF00B59C
      9400FFB56300FFB56300FFB56300FFAD5A00FFA55A002152AD008CB5F7004A94
      FF001073FF002184FF00428CEF00215AC60000000000297BCE007BB5EF00B59C
      9400FFB56300FFB56300FFB56300FFAD5A00FFA55A004A8C5A0094D6B50094D6
      B5000000000063BD8C0063BD8C0021733900000000000000000000009C001842
      FF00184AFF001039F70000009C00000000000000000000009C000029F7000031
      FF000029F70000009C00000000000000000042B5E700F7FFFF00B5EFFF0042DE
      F70029CEEF0042D6F7008CE7F7004294CE0084B5CE0000000000000000000000
      00000000000000000000000000000000000000000000E7CEBD008C524200FFCE
      AD00FFD69C00FFC66B00FFBD6B00FFBD6300FFB55A0094848C003973CE008CB5
      F700BDD6FF0073ADF700296BCE0094ADE70000000000E7CEBD008C524200FFCE
      AD00FFD69C00FFC66B00FFBD6B00FFBD6300FFB55A00A59C630063AD840094D6
      B500BDE7D6006BBD8C00298C520084AD94000000000000000000000000000000
      9C001842F700184AFF001039F70000009C0000009C000029FF000031FF000029
      F70000009C0000000000000000000000000094D6EF0042B5E70042B5E700ADEF
      FF004ADEF70029CEEF0039D6F7008CE7F7004294CE004A94C600297BD6002984
      DE005AA5CE0000000000000000000000000000000000FFFFFF00DEBDA500C64A
      1800F7E7D600FFE7A500FFD67300FFCE6B00FFC66300FFB55A009494A500295A
      C6000042BD001852B50084A5DE000000000000000000FFFFFF00DEBDA500C64A
      1800F7E7D600FFE7A500FFD67300FFCE6B00FFC66300FFB55A00ADAD7B006394
      73004A8C63004A845A009CBDA500000000000000000000000000000000000000
      000000009C00184AF7001852FF001039FF000031FF000031FF000029F7000000
      9C0000000000000000000000000000000000000000000000000042B5E700F7FF
      FF00BDF7FF007BE7F70029D6F70031D6F70084E7F70039ADE700A5F7FF00ADF7
      FF002984D6005AA5CE0000000000000000000000000000000000FFFFFF00DEB5
      9C00BD4A1800F7E7D6004A7BAD004A7BAD004A7BAD004A7BAD00F7D6C600BD42
      1800D6AD9C00FFFFF70000000000000000000000000000000000FFFFFF00DEB5
      9C00BD4A1800F7E7D6004A7BAD004A7BAD004A7BAD004A7BAD00F7D6C600BD42
      1800D6AD9C00FFFFF70000000000000000000000000000000000000000000000
      00000000000000009C00184AFF001042FF000839FF000031F70000009C000000
      000000000000000000000000000000000000000000000000000094D6EF0042B5
      E70042B5E70073C6EF00ADEFFF0039D6F7004ADEF70063E7F70039CEF70031CE
      EF0084EFFF002984D600529CC60000000000000000000000000000000000FFFF
      FF00A5847300316BA5009CCEFF00ADD6F700ADD6F700A5CEF7003173AD009C7B
      7300FFFFF700000000000000000000000000000000000000000000000000FFFF
      FF00A5847300316BA5009CCEFF00ADD6F700ADD6F700A5CEF7003173AD009C7B
      7300FFFFF7000000000000000000000000000000000000000000000000000000
      00000000000000009C00214AFF00184AFF001842FF001042F70000009C000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000FFFFFF0094D6EF006BC6EF0084E7F70039D6F7005ADEF7006BDEF70052D6
      F70031CEEF0084EFFF00297BD600529CC6000000000000000000000000000000
      00005A84AD00A5CEEF00ADCEEF00A5D6F700ADD6F700ADCEEF00A5CEEF005284
      AD00000000000000000000000000000000000000000000000000000000000000
      00005A84AD00A5CEEF00ADCEEF00A5D6F700ADD6F700ADCEEF00A5CEEF005284
      AD00000000000000000000000000000000000000000000000000000000000000
      000000009C00295AFF00295AFF00214AFF00214AFF002152FF001842F7000000
      9C00000000000000000000000000000000000000000000000000000000000000
      00000000000042B5E700D6F7FF008CE7FF007BE7F7007BE7F7007BE7F70084E7
      F70042D6F70039CEF700ADF7FF00297BD6000000000000000000000000000000
      0000296BA500DEEFF70094C6F7008CBDE7007BADD6008CB5DE00CEDEEF002973
      AD00FFFFFF000000000000000000000000000000000000000000000000000000
      0000296BA500DEEFF70094C6F7008CBDE7007BADD6008CB5DE00CEDEEF002973
      AD00FFFFFF000000000000000000000000000000000000000000000000000000
      9C003163FF00396BFF00295AF70000009C0000009C00214AF700215AFF00184A
      F70000009C000000000000000000000000000000000000000000000000000000
      00000000000042B5E700BDF7FF007BE7F7007BE7F70084E7F70094EFFF00BDF7
      FF008CDEF7004ADEF700C6FFFF003194DE000000000000000000000000000000
      0000083984007B94BD008CB5E700739CCE0010426B001842730021426B001039
      6B00FFFFFF000000000000000000000000000000000000000000000000000000
      0000083984007B94BD008CB5E700739CCE0010426B001842730021426B001039
      6B00FFFFFF00000000000000000000000000000000000000000000009C004273
      FF004273FF00315AF70000009C00000000000000000000009C00214AF700215A
      FF00214AFF0000009C0000000000000000000000000000000000000000000000
      00000000000042B5E700DEFFFF008CE7FF007BE7F70094EFFF00BDEFFF0042B5
      E70042ADE700EFFFFF00319CDE006BB5D6000000000000000000000000000000
      0000084A9400105A9C00084A8C00084A8400104A8400104A8400104273001839
      6300000000000000000000000000000000000000000000000000000000000000
      0000084A9400105A9C00084A8C00084A8400104A8400104A8400104273001839
      630000000000000000000000000000000000000000000000000000009C004A7B
      FF003963F70000009C000000000000000000000000000000000000009C002152
      F7002152FF0000009C0000000000000000000000000000000000000000000000
      00000000000094D6EF0042B5E700CEF7FF008CE7FF00A5EFFF0042B5E70042B5
      E7000000000039A5DE006BB5D600000000000000000000000000000000000000
      000094A5BD00104A9400105A9C00105A9C0010529400084A8400083973008C94
      A500000000000000000000000000000000000000000000000000000000000000
      000094A5BD00104A9400105A9C00105A9C0010529400084A8400083973008C94
      A500000000000000000000000000000000000000000000000000000000000000
      9C0000009C000000000000000000000000000000000000000000000000000000
      9C0000009C000000000000000000000000000000000000000000000000000000
      0000000000000000000094D6EF0042B5E700CEF7FF009CEFFF00BDF7FF00FFFF
      FF0042B5E7008CCEEF0000000000000000000000000000000000000000000000
      0000000000008C9CB50018529400104A9400084A8C00184A84008494AD000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000008C9CB50018529400104A9400084A8C00184A84008494AD000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000094D6EF0042B5E700DEFFFF00DEFFFF0042B5
      E70094D6EF000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000000000000000000000094D6EF0042B5E70042B5E70094D6
      EF00000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000000000000000000000000000000ADC6DE00C68C7300CE73
      3100D67B4200D67B4200D67B4200CE7B3900B55A3900B5BDCE00C6DEF700FFFF
      FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF000000000000000000000000000000
      000000000000000000000000000000000000000000000000000010843100007B
      1800000000000000000000000000000000000000000000000000F7FFFF00F7F7
      F700D6947B00AD523900AD4A2900AD4A2100AD4A2100AD4A2900A54A3100C67B
      5A00F7F7F700F7FFFF0000000000000000000000000000000000F7FFFF00F7F7
      F700D6947B00AD523900AD4A2900AD4A2100AD4A2100AD4A2900A54A3100C67B
      5A00F7F7F700F7FFFF000000000000000000529CE7007384AD00CE7B5A00FFBD
      6300FFB56300FFB55A00FFB55A00FFB55A00DE8C520084737B004A9CE700FFFF
      FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00000000000000000000000000D694
      7B00AD523900AD4A2900AD4A2100AD4A2100AD4A2900A54A31001084290042A5
      5A0031944A0000000000000000000000000000000000BDD6EF00297BCE008C63
      6300BD633100FFBD6300FFBD6300FFBD6300FFBD6300FFBD6300FFBD6300B54A
      21007B6B73003184CE00B5CEEF000000000000000000BDD6EF00297BCE003129
      84004A217300FFBD6300FFBD6300FFBD6300FFBD6300FFBD6300FFBD63004218
      6B0031298C003184CE00B5CEEF00000000003184D60094ADCE00E7AD7300FFBD
      5A00FFB55A00FFA55A00FFA55200FF944A00FF944A00B59CA5004A94DE00FFFF
      FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00BDD6EF00297BCE008C636300BD63
      3100FFBD6300FFBD63002194520018944A00108C4200108C3900399C5A0084C6
      940042A56300218C4200000000000000000000000000297BD60084BDEF009C63
      5A00F7BD8400FFAD5A00FFAD5A00FFA55200FF9C5200FFA55200FF9C5200FFAD
      7B00A55A4A0084BDEF00297BCE000000000000000000297BD600314ABD004A52
      BD006B6BC60063428400FFAD5A00FFA55200FF9C5200FFA5520063427B006363
      C6004242B500314ABD00297BCE0000000000DEC6B500B5735A00FFD6B500FFD6
      8400FFCE6300FFC65A00FFB55200FFA55200FFC69400D67B5A00B5947B00FFFF
      FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00297BD60084BDEF009C635A00F7BD
      8400FFAD5A00FFAD5A00299C5A0094CEAD008CCEA5008CC6A5008CC69C006BB5
      840084C694004AA56300299442000000000000000000297BCE007BB5EF00B59C
      9400FFB56300FFB56300FFB56300FFAD5A00FFA55A00FF9C5200FF944A00FF8C
      4200BD8C84007BBDEF002973CE000000000000000000297BCE00314ABD005A6B
      D6007373BD00736BBD00634A8400FFAD5A00FFA55A0063427B00635AB5006B52
      B5004A52C600314ABD002973CE0000000000FFFFFF00DEB59C00CE7B5A00FFEF
      C600ADAD8400A59C7B00A5947300F7CE9C00E7947300CE846300FFF7F700FFFF
      FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00297BCE007BB5EF00B59C9400FFB5
      6300FFB56300FFB56300319C630094CEAD006BBD8C006BBD8C0063B5840063B5
      840063B5840084C69C0039A55A000084290000000000E7CEBD008C524200FFCE
      AD00FFD69C00FFC66B00FFBD6B00FFBD6300FFB55A00FFAD5A00FFA55200FFB5
      7B00FFAD84008C524200D6BDAD000000000000000000E7CEBD008C5242006352
      A5007B7BD600737BC6007373BD00634A840063428400635ABD006363BD006B63
      C60063428C008C524200D6BDAD0000000000FFFFFF00FFFFFF00CE9C8400737B
      940073ADE70084B5E7007BB5E7006B9CC600735229005AA563005AAD6B005AA5
      630052A5630052945200BDB5BD00C6DEF700E7CEBD008C524200FFCEAD00FFD6
      9C00FFC66B00FFBD6B0031A56B0094CEB50094CEAD0094CEAD0094CEAD0073BD
      94008CC6A50042A56B00088C39000000000000000000FFFFFF00DEBDA500C64A
      1800F7E7D600FFE7A500FFD67300FFCE6B00FFC66300FFB55A00FFC68400F7D6
      C600C64A1800D6B59C00FFFFFF000000000000000000FFFFFF00DEBDA500C64A
      1800635AB5007B84D6007B84C6007373C6006B6BBD006B63BD00636BC6006352
      AD00C64A1800D6B59C00FFFFFF0000000000FFFFFF00FFFFFF00BDB5BD0073A5
      D600ADD6F700ADD6FF00B5D6F70094C6E70052848C007BCE940073CE8C0073CE
      8C0073CE94006BB573005A8C5A004A9CE70000000000DEBDA500C64A1800F7E7
      D600FFE7A500FFD6730039A56B0039A56B0031A56300319C630052AD7B0094CE
      AD004AAD730018944A0000000000000000000000000000000000FFFFFF00DEB5
      9C00BD4A1800F7E7D6004A7BAD004A7BAD004A7BAD004A7BAD00F7D6C600BD42
      1800D6AD9C00FFFFF70000000000000000000000000000000000FFFFFF00DEB5
      9C00BD4A1800635AB500295ADE00295ADE002152DE002152D6006352AD00BD42
      1800D6AD9C00FFFFF7000000000000000000FFFFFF00FFFFFF006B8CB500A5BD
      DE0094C6F700A5D6FF0094C6F700B5CEE700427BA5007BCE8C0073CE8C0073CE
      84006BCE7B006BCE7B00639463004A94DE000000000000000000DEB59C00BD4A
      1800F7E7D6004A7BAD004A7BAD004A7BAD004A7BAD00F7D6C600399C5A005AB5
      8400299C5200000000000000000000000000000000000000000000000000FFFF
      FF00A5847300316BA5009CCEFF00ADD6F700ADD6F700A5CEF7003173AD009C7B
      7300FFFFF700000000000000000000000000000000000000000000000000FFFF
      FF00A584730010299C005284FF005A84FF005284FF00527BF7001029A5009C7B
      7300FFFFF700000000000000000000000000FFFFFF00FFFFFF00637BAD00526B
      9C006384AD007BA5D6005273A500425A940042639C007BD69C0073CE940073CE
      8C0073CE8C009CDEAD006BB573008CA58C00000000000000000000000000A584
      7300316BA5009CCEFF00ADD6F700ADD6F700A5CEF7003173AD00399C6B00319C
      6300000000000000000000000000000000000000000000000000000000000000
      00005A84AD00A5CEEF00ADCEEF00A5D6F700ADD6F700ADCEEF00A5CEEF005284
      AD00000000000000000000000000000000000000000000000000000000000000
      00002131A5005A8CF7005A8CF7005A84F7005284F7005A84F700527BF7002131
      A50000000000000000000000000000000000FFFFFF00FFFFFF00C6CED6004263
      9400425A9400425A9400425A9400425A94006B8C9C00849C94007B948C00738C
      7B00A5D6B50084BD840073AD7B00F7FFF7000000000000000000000000005A84
      AD00A5CEEF00ADCEEF00A5D6F700ADD6F700ADCEEF00A5CEEF005284AD000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000296BA500DEEFF70094C6F7008CBDE7007BADD6008CB5DE00CEDEEF002973
      AD00FFFFFF000000000000000000000000000000000000000000000000006363
      C6003163D6007B9CFF005A84F700394ABD003142B5004A73EF006B8CFF00215A
      DE006363C600000000000000000000000000FFFFFF00FFFFFF00FFFFFF00ADB5
      C6004A6B9C00425A940042639C00527B84006B7B940073ADE70084B5E7007BB5
      E7006B9CC6006B946B00F7FFF700FFFFFF00000000000000000000000000296B
      A500DEEFF70094C6F7008CBDE7007BADD6008CB5DE00CEDEEF002973AD000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000083984007B94BD008CB5E700739CCE0010426B001842730021426B001039
      6B00FFFFFF0000000000000000000000000000000000000000006363C6008CAD
      FF00295ACE005273DE00394ABD00739CCE0010426B0008188C00214ABD00214A
      C6007B94FF006363C6000000000000000000FFFFFF00FFFFFF00FFFFFF00FFFF
      FF00FFFFFF00FFFFFF00FFFFFF00BDB5BD0073A5D600ADD6F700ADD6FF00B5D6
      F70094C6E7007B94AD00FFFFFF00FFFFFF000000000000000000000000000839
      84007B94BD008CB5E700739CCE0010426B001842730021426B0010396B000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000084A9400105A9C00084A8C00084A8400104A8400104A8400104273001839
      63000000000000000000000000000000000000000000000000006363C60094B5
      FF00295ACE0000219C00084A8C00084A8400104A8400104A840000188C00214A
      BD007B9CFF006363C6000000000000000000FFFFFF00FFFFFF00FFFFFF00FFFF
      FF00FFFFFF00FFFFFF00FFFFFF007BA5C600BDD6E70094C6F700A5D6FF0094C6
      F700B5CEE7004A94C600FFFFFF00FFFFFF00000000000000000000000000084A
      9400105A9C00084A8C00084A8400104A8400104A840010427300183963000000
      0000000000000000000000000000000000000000000000000000000000000000
      000094A5BD00104A9400105A9C00105A9C0010529400084A8400083973008C94
      A500000000000000000000000000000000000000000000000000000000006363
      C6003942A500104A9400105A9C00105A9C0010529400084A8400083973003139
      9C006363C600000000000000000000000000FFFFFF00FFFFFF00FFFFFF00FFFF
      FF00FFFFFF00FFFFFF00FFFFFF007BA5C60031ADE70063BDE7008CCEF70039AD
      E70018A5E700319CD600FFFFFF00FFFFFF0000000000000000000000000094A5
      BD00104A9400105A9C00105A9C0010529400084A8400083973008C94A5000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000008C9CB50018529400104A9400084A8C00184A84008494AD000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000008C9CB50018529400104A9400084A8C00184A84008494AD000000
      000000000000000000000000000000000000FFFFFF00FFFFFF00FFFFFF00FFFF
      FF00FFFFFF00FFFFFF00FFFFFF00C6CEDE0021A5DE0018A5E70018A5E70018A5
      E70018A5E70084ADC600FFFFFF00FFFFFF000000000000000000000000000000
      00008C9CB50018529400104A9400084A8C00184A84008494AD00000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000FFFFFF00FFFFFF00FFFFFF00FFFF
      FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00ADBDCE00319CD60018A5E70021A5
      DE0073A5C600FFFFFF00FFFFFF00FFFFFF000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000424D3E000000000000003E000000
      2800000040000000300000000100010000000000800100000000000000000000
      000000000000000000000000FFFFFF00FFFF000100000000F00F000000000000
      E007000800000000C003001C0000000080018008000000008001C00000000000
      8001C0000000000087E1C0000000000087E1C000000000008001C00000000000
      8001E001000000008001FE0300000000C003FE0300000000E007FE0300000000
      F00FFE0300000000FFFFFF0300000000FFC1FFC1FFFF07FFC000C000FFFF03FF
      80008008E7E701FF801C801CC3C300FF80008008C183007F80008000E0070007
      80018001F00FC003C003C003F81FC001E007E007F81FF000F00FF00FF00FF800
      F007F007E007F800F007F007C183F800F00FF00FC3C3F809F00FF00FE7E7FC03
      F81FF81FFFFFFE07FFFFFFFFFFFFFF0FFFFFFFFF8000FFCFC003C0030000E007
      8001800100000003800180010000000180018001000000008001800100000001
      8001800100008003C003C0030000C007E007E0070000E00FF00FF00F0000E01F
      F007E0070000E01FF007C0030000E01FF00FC0030000E01FF00FE0070000E01F
      F81FF81F0000F03FFFFFFFFF0000FFFF00000000000000000000000000000000
      000000000000}
  end
end
