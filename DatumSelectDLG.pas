unit DatumSelectDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComCtrls;

type
  TDatumSelectForm = class(TForm)
    VonDateTimePicker: TDateTimePicker;
    Bevel1: TBevel;
    OkButton: TButton;
    AbortButton: TButton;
    Datum: TLabel;
    BisDateTimePicker: TDateTimePicker;
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

var
  DatumSelectForm: TDatumSelectForm;

implementation

{$R *.dfm}

end.
