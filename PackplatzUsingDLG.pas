unit PackplatzUsingDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ComCtrls, ExtCtrls, StdCtrls;

type
  TPackplatzUsingForm = class(TForm)
    Panel1: TPanel;
    Label1: TLabel;
    VonDateTimePicker: TDateTimePicker;
    Bevel1: TBevel;
    PageControl1: TPageControl;
    Panel2: TPanel;
    OkButton: TButton;
    Button2: TButton;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure VonDateTimePickerChange(Sender: TObject);
  private
    fRefLager : Integer;
    fFrameList : TList;

    function UpdateDaten (Sender: TObject) : Integer;
  public
    property RefLager : Integer read fRefLager write fRefLager;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DB, ADODB, ADOInt, DatenModul, PackplatzUsingFRM, LVSWarenausgang, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.08.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPackplatzUsingForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  idx : Integer;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    res := 0;
    idx := 0;

    while (idx < fFrameList.Count) and (res = 0) do begin
      if (TPackplatzUsingFrame (fFrameList[idx]).Date >= trunc (Now)) then begin
        if (TPackplatzUsingFrame (fFrameList[idx]).PackplatzButton.Clicked) then begin
          if (TPackplatzUsingFrame (fFrameList[idx]).ActiveFlag <> '1') then
            res := SetPackstationStatus (TPackplatzUsingFrame (fFrameList[idx]).RefWAPlatz, trunc (TPackplatzUsingFrame (fFrameList[idx]).Date), '1')
        end else begin
          if (TPackplatzUsingFrame (fFrameList[idx]).ActiveFlag <> '0') then
            res := SetPackstationStatus (TPackplatzUsingFrame (fFrameList[idx]).RefWAPlatz, trunc (TPackplatzUsingFrame (fFrameList[idx]).Date), '0');
        end;
      end;

      Inc (idx);
    end;

    if (res = 0) then
      CanClose := True
    else
      MessageDLG (FormatMessageText (1002, []) + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.08.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPackplatzUsingForm.FormCreate(Sender: TObject);
begin
  VonDateTimePicker.Date := trunc (Now);

  fFrameList := TList.Create;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.08.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPackplatzUsingForm.FormShow(Sender: TObject);
begin
  UpdateDaten (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.08.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TPackplatzUsingForm.UpdateDaten (Sender: TObject) : Integer;
var
  frame : TPackplatzUsingFrame;
  tab   : TTabSheet;
  query : TADOQuery;
  count : Integer;
begin
  fFrameList.Clear;
  while (PageControl1.PageCount > 0) do
    PageControl1.Pages [0].Free;

  tab := TTabSheet.Create (Self);
  tab.PageControl := PageControl1;
  tab.Caption := DateToStr (VonDateTimePicker.Date);

  query := TADOQuery.Create (Self);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select pp.REF,pp.NAME,pp.DESCRIPTION,pu.ACTIVE_FLAG from V_WA_PACKPLATZ pp left outer join V_WA_PACKPLATZ_USING pu on (pu.REF_PACKPLATZ=pp.REF and trunc (pu.ACTIVE_DATE)=:datum) where pp.REF_LAGER=:ref_lager');
    query.Parameters.ParamByName('ref_lager').Value := fRefLager;
    query.Parameters.ParamByName('datum').ParameterObject.Type_ := adDBTimeStamp;
    query.Parameters.ParamByName('datum').Value := trunc (VonDateTimePicker.Date);

    query.Open;

    count := 1;

    while not (query.Eof) do begin
      frame := TPackplatzUsingFrame.Create (Self);
      frame.Name := 'PackplatzUsingFrame'+IntToStr (count);
      frame.Parent := tab;

      fFrameList.Add (frame);

      frame.Date       := trunc (VonDateTimePicker.Date);

      if (query.FieldByName ('ACTIVE_FLAG').IsNull) then
        frame.ActiveFlag := #0
      else frame.ActiveFlag := query.FieldByName ('ACTIVE_FLAG').AsString [1];

      frame.RefWAPlatz := query.FieldByName ('REF').AsInteger;

      frame.DescLabel.Caption := query.FieldByName ('DESCRIPTION').AsString;
      frame.PackplatzButton.Caption := query.FieldByName ('NAME').AsString;

      if (query.FieldByName ('ACTIVE_FLAG').AsString = '1') then
        frame.PackplatzButton.Clicked := True;

      if (frame.Date < trunc (Now)) then
        frame.PackplatzButton.Enabled := False;

      frame.PackplatzButtonClick (Sender);

      Inc (count);

      query.Next;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := 0;
end;

procedure TPackplatzUsingForm.VonDateTimePickerChange(Sender: TObject);
begin
  if not (VonDateTimePicker.DroppedDown) then
    UpdateDaten (Sender)
end;

end.
