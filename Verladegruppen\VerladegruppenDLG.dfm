object VerladegruppenMainForm: TVerladegruppenMainForm
  Left = 0
  Top = 0
  Caption = 'VerladegruppenMainForm'
  ClientHeight = 644
  ClientWidth = 795
  Color = clBtnFace
  Constraints.MinWidth = 500
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  OnClick = CheckBoxesClick
  OnClose = FormClose
  OnCreate = FormCreate
  OnKeyDown = FormKeyDown
  OnShow = FormShow
  TextHeight = 15
  object Splitter1: TSplitter
    Left = 0
    Top = 300
    Width = 795
    Height = 3
    Cursor = crVSplit
    Align = alTop
    ExplicitWidth = 343
  end
  object UpperPanel: TPanel
    Left = 0
    Top = 0
    Width = 795
    Height = 300
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object VerladegruppenDBGridPro: TDBGridPro
      Left = 0
      Top = 0
      Width = 645
      Height = 300
      Align = alClient
      DataSource = VerladegruppenDataSource
      Options = [dgEditing, dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick, dgTitleHotTrack]
      ReadOnly = True
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -12
      TitleFont.Name = 'Segoe UI'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -12
      BandsFont.Name = 'Segoe UI'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsNormal
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 19
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
    object UpperButtonPanel: TPanel
      Left = 645
      Top = 0
      Width = 150
      Height = 300
      Align = alRight
      Constraints.MinHeight = 150
      TabOrder = 1
      DesignSize = (
        150
        300)
      object NewGroupButton: TButton
        Left = 5
        Top = 26
        Width = 140
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Neue Gruppe...'
        TabOrder = 0
        OnClick = NewGroupButtonClick
      end
      object EditGroupButton: TButton
        Left = 4
        Top = 57
        Width = 140
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Gruppe bearbeiten...'
        TabOrder = 1
        OnClick = EditGroupButtonClick
      end
      object DeleteGroupButton: TButton
        Left = 4
        Top = 88
        Width = 140
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Gruppe l'#246'schen'
        TabOrder = 2
        OnClick = DeleteGroupButtonClick
      end
    end
  end
  object LowerPanel: TPanel
    Left = 0
    Top = 303
    Width = 795
    Height = 301
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 1
    object LowerButtonPanel: TPanel
      Left = 645
      Top = 0
      Width = 150
      Height = 301
      Align = alRight
      Constraints.MinHeight = 200
      TabOrder = 0
      DesignSize = (
        150
        301)
      object AssignedCheckBox: TCheckBox
        Left = 6
        Top = 19
        Width = 139
        Height = 17
        Caption = 'Zugewiesene'
        Checked = True
        State = cbChecked
        TabOrder = 0
        OnClick = CheckBoxesClick
      end
      object NotAssignedCheckBox: TCheckBox
        Left = 6
        Top = 51
        Width = 139
        Height = 17
        Caption = 'Nicht Zugewiesene'
        TabOrder = 1
        OnClick = CheckBoxesClick
      end
      object OtherAssignedCheckBox: TCheckBox
        Left = 6
        Top = 82
        Width = 139
        Height = 17
        Caption = 'Anderen zugewiesen'
        TabOrder = 2
        OnClick = CheckBoxesClick
      end
      object AssignSelectionButton: TButton
        Left = 6
        Top = 110
        Width = 140
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Auswahl zuweisen'
        TabOrder = 3
        OnClick = AssignSelectionButtonClick
      end
      object UnassignButton: TButton
        Left = 6
        Top = 141
        Width = 140
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Zuweisung aufheben'
        TabOrder = 4
        OnClick = UnassignButtonClick
      end
    end
    object SpeditionDBGridPro: TDBGridPro
      Left = 0
      Top = 0
      Width = 645
      Height = 301
      Align = alClient
      DataSource = SpeditionenDataSource
      Options = [dgEditing, dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgMultiSelect, dgTitleClick, dgTitleHotTrack]
      ReadOnly = True
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -12
      TitleFont.Name = 'Segoe UI'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -12
      BandsFont.Name = 'Segoe UI'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsNormal
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 19
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
  end
  object BottomPanel: TPanel
    Left = 0
    Top = 604
    Width = 795
    Height = 40
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      795
      40)
    object CloseButton: TButton
      Left = 649
      Top = 8
      Width = 140
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Schlie'#223'en'
      TabOrder = 0
      OnClick = CloseButtonClick
    end
  end
  object VerladegruppenDataSource: TDataSource
    DataSet = VerladegruppenQuery
    OnDataChange = VerladegruppenDataSourceDataChange
    Left = 376
    Top = 160
  end
  object VerladegruppenQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 512
    Top = 160
  end
  object SpeditionenQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 504
    Top = 424
  end
  object SpeditionenDataSource: TDataSource
    DataSet = SpeditionenQuery
    Left = 352
    Top = 432
  end
end
