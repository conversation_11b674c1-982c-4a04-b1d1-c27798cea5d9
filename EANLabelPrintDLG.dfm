object EANLabelPrintForm: TEANLabelPrintForm
  Left = 417
  Top = 241
  BorderIcons = [biSystemMenu]
  BorderStyle = bsDialog
  Caption = 'EAN128-Labels drucken'
  ClientHeight = 439
  ClientWidth = 563
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  TextHeight = 13
  object PageControl1: TPageControl
    Left = 0
    Top = 246
    Width = 563
    Height = 134
    ActivePage = EANTabSheet
    Align = alClient
    TabOrder = 1
    object EAN128TabSheet: TTabSheet
      Caption = 'Egalisierte Ware'
      object Label1: TLabel
        Left = 8
        Top = 8
        Width = 71
        Height = 13
        Caption = 'An<PERSON><PERSON>rton<PERSON>'
      end
      object KartonEdit: TEdit
        Left = 8
        Top = 24
        Width = 89
        Height = 21
        TabOrder = 0
        Text = '0'
      end
      object KartonUpDown: TIntegerUpDown
        Left = 97
        Top = 24
        Width = 16
        Height = 21
        Associate = KartonEdit
        TabOrder = 1
      end
    end
    object EAN128NTabSheet: TTabSheet
      Caption = 'Nicht egalisierte Ware'
      ImageIndex = 1
      object Label2: TLabel
        Left = 8
        Top = 8
        Width = 63
        Height = 13
        Caption = 'Nettogewicht'
      end
      object Label9: TLabel
        Left = 87
        Top = 27
        Width = 12
        Height = 13
        Caption = 'kg'
      end
      object GewichtEdit: TEdit
        Left = 8
        Top = 24
        Width = 73
        Height = 21
        TabOrder = 0
        Text = 'GewichtEdit'
        OnExit = GewichtEditExit
      end
    end
    object TabSheet3: TTabSheet
      Caption = 'Palettenlabel'
      ImageIndex = 2
      TabVisible = False
    end
    object LETabSheet: TTabSheet
      Caption = 'LE drucken'
      ImageIndex = 3
      object Label10: TLabel
        Left = 8
        Top = 8
        Width = 55
        Height = 13
        Caption = 'LE-Nummer'
      end
      object Label11: TLabel
        Left = 8
        Top = 56
        Width = 87
        Height = 13
        Caption = 'Enthaltene Menge'
      end
      object Label12: TLabel
        Left = 256
        Top = 56
        Width = 65
        Height = 13
        Caption = 'Bruttogewicht'
      end
      object LEEdit: TEdit
        Left = 8
        Top = 24
        Width = 233
        Height = 21
        Enabled = False
        ReadOnly = True
        TabOrder = 0
        Text = 'LEEdit'
      end
      object MengeEdit: TEdit
        Left = 8
        Top = 72
        Width = 233
        Height = 21
        Enabled = False
        TabOrder = 1
        Text = 'MengeEdit'
      end
      object BruttoGewichtEdit: TEdit
        Left = 256
        Top = 72
        Width = 233
        Height = 21
        Enabled = False
        TabOrder = 2
        Text = 'BruttoGewichtEdit'
      end
    end
    object EANTabSheet: TTabSheet
      Caption = 'EAN'
      ImageIndex = 4
      OnShow = EANTabSheetShow
      object Label7: TLabel
        Left = 8
        Top = 45
        Width = 77
        Height = 13
        Caption = 'Anzahl Etiketten'
      end
      object EANLabel: TLabel
        Left = 8
        Top = 16
        Width = 48
        Height = 13
        Caption = 'EANLabel'
      end
      object EANLabelCountEdit: TEdit
        Left = 8
        Top = 61
        Width = 89
        Height = 21
        TabOrder = 0
        Text = '0'
      end
      object EANLabelCountUpDown: TIntegerUpDown
        Left = 97
        Top = 61
        Width = 16
        Height = 21
        Associate = EANLabelCountEdit
        TabOrder = 1
      end
      object NormalEANRadioButton: TRadioButton
        Left = 160
        Top = 15
        Width = 158
        Height = 18
        Caption = 'Standard EAN-Label'
        Checked = True
        TabOrder = 2
        TabStop = True
      end
      object ColliEANRadioButton: TRadioButton
        Left = 331
        Top = 14
        Width = 158
        Height = 18
        Caption = 'EAN-Label mit Colli-Nummer'
        TabOrder = 3
      end
    end
    object BarcodeTabSheet: TTabSheet
      Caption = 'Barcode'
      ImageIndex = 5
      OnShow = BarcodeTabSheetShow
      object Label15: TLabel
        Left = 8
        Top = 45
        Width = 77
        Height = 13
        Caption = 'Anzahl Etiketten'
      end
      object BarcodeLabel: TLabel
        Left = 8
        Top = 16
        Width = 66
        Height = 13
        Caption = 'BarcodeLabel'
      end
      object BarcodeCountEdit: TEdit
        Left = 8
        Top = 61
        Width = 89
        Height = 21
        TabOrder = 0
        Text = '0'
      end
      object BarcodeCountUpDown: TIntegerUpDown
        Left = 97
        Top = 61
        Width = 16
        Height = 21
        Associate = BarcodeCountEdit
        TabOrder = 1
      end
    end
    object CustomCodeTabSheet: TTabSheet
      Caption = '28/29er Codes'
      ImageIndex = 6
      object CustomCodeCountLabel: TLabel
        Left = 248
        Top = 5
        Width = 77
        Height = 13
        Caption = 'Anzahl Etiketten'
      end
      object CustomCodeGroupBox: TGroupBox
        Left = 3
        Top = 32
        Width = 227
        Height = 72
        Caption = 'Zusatzwert'
        TabOrder = 0
        object Label19: TLabel
          Left = 8
          Top = 20
          Width = 111
          Height = 13
          Caption = 'Code (Gewicht, Preis...)'
        end
        object CustomCodeEdit: TEdit
          Left = 8
          Top = 37
          Width = 121
          Height = 21
          MaxLength = 5
          TabOrder = 0
          Text = 'CustomCodeEdit'
          OnKeyPress = CustomCodeEditKeyPress
        end
      end
      object CustomCodeCheckBox: TCheckBox
        Left = 3
        Top = 9
        Width = 350
        Height = 17
        Caption = 'Variablen Code in den EAN mit aufnehmen'
        Checked = True
        State = cbChecked
        TabOrder = 1
        OnClick = CustomCodeCheckBoxClick
      end
      object CustomCodeCountEdit: TEdit
        Left = 248
        Top = 21
        Width = 89
        Height = 21
        TabOrder = 2
        Text = '1'
      end
      object CustomCodeCountUpDown: TIntegerUpDown
        Left = 337
        Top = 21
        Width = 16
        Height = 21
        Associate = CustomCodeCountEdit
        Position = 1
        TabOrder = 3
      end
    end
    object CustomTabSheet: TTabSheet
      Caption = 'Kundenspezifisch'
      ImageIndex = 7
      object Label20: TLabel
        Left = 8
        Top = 8
        Width = 42
        Height = 13
        Caption = 'Seriennr.'
      end
      object CustomSerialEdit: TEdit
        Left = 8
        Top = 24
        Width = 79
        Height = 21
        TabOrder = 0
        Text = 'CustomSerialEdit'
      end
    end
    object PackTabSheet: TTabSheet
      Caption = 'IDs'
      ImageIndex = 8
      object Label21: TLabel
        Left = 14
        Top = 29
        Width = 42
        Height = 13
        Caption = 'Seriennr.'
      end
      object Label22: TLabel
        Left = 14
        Top = 56
        Width = 53
        Height = 13
        Caption = 'Bestand-ID'
      end
      object SerialEdit: TEdit
        Left = 78
        Top = 26
        Width = 104
        Height = 21
        TabOrder = 0
        Text = 'SerialEdit'
      end
      object BestandIDEdit: TEdit
        Left = 78
        Top = 53
        Width = 104
        Height = 21
        TabOrder = 1
        Text = 'BestandIDEdit'
      end
    end
  end
  object ArtikelPanel: TPanel
    Left = 0
    Top = 73
    Width = 563
    Height = 73
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label3: TLabel
      Left = 8
      Top = 13
      Width = 37
      Height = 13
      Caption = 'Artikel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label4: TLabel
      Left = 8
      Top = 32
      Width = 74
      Height = 13
      Caption = 'Bezeichnung'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label5: TLabel
      Left = 8
      Top = 51
      Width = 26
      Height = 13
      Caption = 'EAN'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object ArtNrLabel: TLabel
      Left = 120
      Top = 13
      Width = 50
      Height = 13
      Caption = 'ArtNrLabel'
    end
    object ArtTextLabel: TLabel
      Left = 119
      Top = 32
      Width = 60
      Height = 13
      Caption = 'ArtTextLabel'
    end
    object ArtEANLabel: TLabel
      Left = 120
      Top = 51
      Width = 61
      Height = 13
      Caption = 'ArtEANLabel'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 72
      Width = 547
      Height = 9
      Shape = bsTopLine
    end
  end
  object GrundPanel: TPanel
    Left = 0
    Top = 187
    Width = 563
    Height = 59
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    object Label6: TLabel
      Left = 8
      Top = 15
      Width = 32
      Height = 13
      Caption = 'Layout'
    end
    object LayoutComboBox: TComboBoxPro
      Left = 56
      Top = 12
      Width = 497
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 0
      OnChange = LayoutComboBoxChange
    end
    object TestCheckBox: TCheckBox
      Left = 56
      Top = 39
      Width = 497
      Height = 17
      Caption = 'Testdruck, nur ein Etikett pro Artikel'
      TabOrder = 1
    end
  end
  object MHDChargePanel: TPanel
    Left = 0
    Top = 146
    Width = 563
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    object Bevel2: TBevel
      Left = 8
      Top = 37
      Width = 547
      Height = 9
      Shape = bsTopLine
    end
    object Label13: TLabel
      Left = 8
      Top = 13
      Width = 25
      Height = 13
      Caption = 'MHD'
    end
    object Label14: TLabel
      Left = 200
      Top = 13
      Width = 34
      Height = 13
      Caption = 'Charge'
    end
    object MHDEdit: TEdit
      Left = 56
      Top = 10
      Width = 129
      Height = 21
      TabOrder = 0
      Text = 'MHDEdit'
      OnExit = MHDEditExit
    end
    object ChargeEdit: TEdit
      Left = 240
      Top = 10
      Width = 313
      Height = 21
      TabOrder = 1
      Text = 'ChargeEdit'
    end
  end
  object NVEPanel: TPanel
    Left = 0
    Top = 0
    Width = 563
    Height = 73
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    object Label16: TLabel
      Left = 8
      Top = 13
      Width = 75
      Height = 13
      Caption = 'NVE-Nummer'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label17: TLabel
      Left = 8
      Top = 32
      Width = 57
      Height = 13
      Caption = 'Auftragnr.'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label18: TLabel
      Left = 8
      Top = 51
      Width = 61
      Height = 13
      Caption = 'Empf'#228'nger'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object NVENrLabel: TLabel
      Left = 120
      Top = 13
      Width = 59
      Height = 13
      Caption = 'NVENrLabel'
    end
    object AuftragNrLabel: TLabel
      Left = 120
      Top = 32
      Width = 71
      Height = 13
      Caption = 'AuftragNrLabel'
    end
    object WarenEmpfLabel: TLabel
      Left = 120
      Top = 51
      Width = 82
      Height = 13
      Caption = 'WarenEmpfLabel'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 72
      Width = 547
      Height = 9
      Shape = bsTopLine
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 380
    Width = 563
    Height = 59
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      563
      59)
    object Label8: TLabel
      Left = 8
      Top = 6
      Width = 38
      Height = 13
      Caption = 'Drucker'
    end
    object PrinterComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 314
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 160
      ItemHeight = 15
      TabOrder = 0
    end
    object PrintButton: TButton
      Left = 328
      Top = 20
      Width = 75
      Height = 25
      Caption = 'Drucken'
      Default = True
      TabOrder = 1
      OnClick = WAPrintAllEANMenuItemClick
    end
    object CloseButton: TButton
      Left = 480
      Top = 20
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 2
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 464
    Top = 56
  end
end
