unit DialogSendITTestMain;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls;

type
  TDialogTestForm = class(TForm)
    Button1: TButton;
    procedure Button1Click(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    procedure Init;
    function OpenDatanbase : Integer;
  end;

var
  DialogTestForm: TDialogTestForm;

implementation

{$R *.dfm}

uses LVSSecurity, FrontendUtils, ConfigModul, DatenModul, RegistryUtils,
     BatchConfigDLG;

procedure TDialogTestForm.Button1Click(Sender: TObject);
var
 dlgform : TBatchdialogForm;
begin
 dlgform := TBatchdialogForm.Create (Self);

 dlgform.ShowModal;

 dlgform.Release;
end;

procedure TDialogTestForm.Init;
var
  intwert : Integer;
  winstat : TWindowState;
begin
  UserReg := TRegistryModule.Create;
  UserReg.OpenKey(HKEY_CURRENT_USER, 'Software\' + LVSConfigModul.MasterRegKeyName + '\LVSDialog', KEY_READ or KEY_WRITE, True);

  if (UserReg.ReadRegValue('MainState', intwert) <> 0) then
    winstat := wsNormal
  else begin
    winstat := TWindowState(intwert);
  end;

  WindowState := winstat;

  if (winstat = wsNormal) then begin
    if (UserReg.ReadRegValue('MainTop', intwert) = 0) then Top := intwert;
    if (UserReg.ReadRegValue('MainLeft', intwert) = 0) then Left := intwert;
    if (UserReg.ReadRegValue('MainWidth', intwert) = 0) then Width := intwert;
    if (UserReg.ReadRegValue('MainHeight', intwert) = 0) then Height := intwert;

    if (Top > Monitor.Height) then
      Top := Monitor.Height - Height - 20;
    if (Left > Monitor.Height) then
      Left := Monitor.Height - Width - 20;
  end;
end;

function TDialogTestForm.OpenDatanbase : Integer;
begin
  Result := OpenLVSDatabase ('PCD', 'DialogTest', True);

  if (Result = 0) then begin
  end;
end;

end.
