object MessageForm: TMessageForm
  Left = 322
  Top = 509
  BorderStyle = bsDialog
  Caption = 'MessageForm'
  ClientHeight = 121
  ClientWidth = 709
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    709
    121)
  PixelsPerInch = 96
  TextHeight = 13
  object MessageLabel: TLabel
    Left = 96
    Top = 16
    Width = 91
    Height = 16
    Anchors = [akLeft, akTop, akRight]
    Caption = 'MessageLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -13
    Font.Name = 'MS Sans Serif'
    Font.Style = []
    ParentFont = False
  end
  object TimeoutLabel: TLabel
    Left = 8
    Top = 98
    Width = 64
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'TimeoutLabel'
  end
  object IconImage: TImage
    Left = 16
    Top = 16
    Width = 64
    Height = 64
    Visible = False
  end
  object Button1: TButton
    Left = 544
    Top = 90
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 0
  end
  object Button2: TButton
    Left = 624
    Top = 90
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 2
    TabOrder = 1
  end
  object Button3: TButton
    Left = 463
    Top = 90
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 2
  end
end
