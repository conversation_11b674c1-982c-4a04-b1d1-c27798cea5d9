unit InputLHMSerial;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, BarCodeScanner, ComboBoxPro;

type
  TInputLHMSerialForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    KopfPanel: TPanel;
    Bevel1: TBevel;
    Label1: TLabel;
    Label2: TLabel;
    Label7: TLabel;
    Label3: TLabel;
    SpedLabel: TLabel;
    LTTypLabel: TLabel;
    AuftragLabel: TLabel;
    KundenLabel: TLabel;
    NrPanel: TPanel;
    Bevel2: TBevel;
    TrackingNrEdit: TEdit;
    Label4: TLabel;
    LHMSerialEdit: TEdit;
    InputLabel: TLabel;
    SpedPanel: TPanel;
    Label5: TLabel;
    SpedComboBox: TComboBoxPro;
    Label8: TLabel;
    SpedProduktComboBox: TComboBoxPro;
    Bevel3: TBevel;
    VersenderPanel: TPanel;
    Label6: TLabel;
    VersenderEdit: TEdit;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure SpedComboBoxChange(Sender: TObject);
  private
    fRefAuftrag    : Integer;
    fRefLT         : Integer;
    fRefSped       : Integer;
    fRefSpedProd   : Integer;
    fRefLager      : Integer;
    fRefMand       : Integer;
    fRefNVE        : Integer;
    fPflicht       : Boolean;
    fOptLHMSerial  : Char;
    fOptShipmendNo : Char;

    procedure ScannerErfassung(var Message: TMessage); message WM_USER + SCANNER_DETECT;
  public
    property Pflicht       : Boolean read fPflicht write fPflicht;
    property OptLHMSerial  : Char read fOptLHMSerial;
    property OptShipmendNo : Char read fOptShipmendNo;

    function Prepare (const RefNVE : Integer; const ProcessStep : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses
  DB, ADODB, VCLUtilitys, DatenModul, FrontendUtils, ConfigModul, LVSDatenInterface,
  FrontendMainUtils, SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInputLHMSerialForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    if (not fPflicht or (Length (LHMSerialEdit.Text) > 0)) then
      CanClose := True
    else begin
      CanClose := False;
      LHMSerialEdit.SetFocus;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInputLHMSerialForm.FormCreate(Sender: TObject);
begin
  LTTypLabel.Caption := '';
  LHMSerialEdit.Text := '';
  TrackingNrEdit.Text := '';
  VersenderEdit.Text := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AuftragLabel);
    LVSSprachModul.SetNoTranslate (Self, KundenLabel);
    LVSSprachModul.SetNoTranslate (Self, LTTypLabel);
    LVSSprachModul.SetNoTranslate (Self, SpedLabel);
    LVSSprachModul.SetNoTranslate (Self, SpedComboBox);
    LVSSprachModul.SetNoTranslate (Self, SpedProduktComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInputLHMSerialForm.FormShow(Sender: TObject);
begin
  if not (VersenderPanel.Visible) then
    Height := Height - VersenderPanel.Height;

  if not (SpedPanel.Visible) then begin
    Height := Height - SpedPanel.Height;
  end else begin
    Label3.Visible := False;
    SpedLabel.Visible := False;

    KopfPanel.Height := KopfPanel.Height - 16;
  end;

  if not (TrackingNrEdit.Visible) then begin
    NrPanel.Height := NrPanel.Height - 16 - 24;
    Height := Height - 16 - 24;
  end;

  Label4.Visible := TrackingNrEdit.Visible;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TInputLHMSerialForm.Prepare (const RefNVE : Integer; const ProcessStep : Integer) : Integer;
var
  idx      : Integer;
  query,
  selquery : TADOQuery;
begin
  fRefNVE := RefNVE;

  query := TADOQuery.Create (Self);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (fRefNVE > 0) then begin
      if (ProcessStep = 1) then begin
        query.SQL.Add ('select lt.REF, lt.NAME, nvl (lt.OPT_LHM_SERIAL, ''0''), nvl (sped_cfg.OPT_ENTER_SHIPMEND_NO, ''0''), auf.REF, auf.AUFTRAG_NR, auf.KUNDEN_NAME, sped.NAME,'
                       + ' nvl (sped_cfg.OPT_SENDUNGS_NR, ''0'') as OPT_SENDUNGS_NR, nve.REF_SPED, auf.REF_MAND, auf.REF_LAGER'
                       +' from V_NVE_01 nve inner join V_LT_TYPEN lt on (lt.REF=nve.REF_LT_TYP) inner join V_AUFTRAG auf on (auf.REF=nve.REF_AUF_KOPF)'
                       +' left outer join V_SPEDITIONEN sped on (sped.REF=nve.REF_SPED) left outer join V_SPED_CONFIG sped_cfg on (sped_cfg.REF_SPED=sped.REF) where nve.REF=:ref'
                      )
      end else begin
        query.SQL.Add ('select lt.REF, lt.NAME, nvl (lt.OPT_LHM_SERIAL, ''0''), nvl (sped_cfg.OPT_ENTER_SHIPMEND_NO, ''0''), auf.REF, auf.AUFTRAG_NR, auf.KUNDEN_NAME, sped.NAME,'
                       + ' nvl (sped_cfg.OPT_SENDUNGS_NR, ''0'') as OPT_SENDUNGS_NR, nve.REF_SPED, auf.REF_MAND, auf.REF_LAGER,nvl (send.SENDUNGS_NR, nve.SENDUNGS_NR) as SENDUNGS_NR,send.TRACKING_ID,send.VERSENDER'
                       +' from V_NVE_01 nve inner join V_LT_TYPEN lt on (lt.REF=nve.REF_LT_TYP) inner join V_AUFTRAG auf on (auf.REF=nve.REF_AUF_KOPF)'
                       +' left outer join V_SPEDITIONEN sped on (sped.REF=nve.REF_SPED) left outer join V_SPED_CONFIG sped_cfg on (sped_cfg.REF_SPED=sped.REF)'
                       +' left outer join V_SENDUNGS_NR send on (send.REF_NVE=nve.REF and send.STATUS=''ANG'')'
                       +' where nve.REF=:ref'
                      );
      end;

      query.Parameters [0].Value := fRefNVE;

      query.Open;

      fRefAuftrag := query.Fields[4].AsInteger;

      AuftragLabel.Caption := query.Fields[5].AsString;
      KundenLabel.Caption  := query.Fields[6].AsString;
      SpedLabel.Caption    := query.Fields[7].AsString;
      LTTypLabel.Caption := query.Fields[1].AsString;

      fRefLager          := DBGetReferenz (query.Fields[11]);
      fRefMand           := DBGetReferenz (query.Fields[10]);
      fRefLT             := DBGetReferenz (query.Fields[0]);
      fRefSped           := DBGetReferenz (query.Fields[8]);
      fRefSpedProd       := DBGetReferenz (query.Fields[9]);

      if (ProcessStep = 1) then begin
        SpedPanel.Visible := False;
        TrackingNrEdit.Visible := False;
        VersenderPanel.Visible := False;

        if (Length (query.Fields[2].AsString) < 1) then
          fOptLHMSerial := '0'
        else begin
          fOptLHMSerial := query.Fields[2].AsString [1];
          fPflicht := (fOptLHMSerial = '2');

          Caption := GetResourceText (1810);
          InputLabel.Caption := GetResourceText (1811);
        end;
      end else if (ProcessStep = 2) then begin
        if (SpedPanel.Visible) then begin
          SpedComboBox.Items.Insert (0, '');

          selquery := TADOQuery.Create (Self);
          try
            selquery.LockType := ltReadOnly;
            selquery.Connection := LVSDatenModul.MainADOConnection;

            selquery.SQl.Add ('SELECT REF,nvl (NAME,SPEDITION_NR) as NAME,BESCHREIBUNG,IFC_KENNZEICHEN FROM V_SPEDITIONEN where STATUS=''ANG''');
            selquery.SQl.Add ('and ((REF_LAGER is not null and REF_LAGER=:ref_lager) or (REF_LAGER is null and (REF_LOCATION is null or REF_LOCATION=:ref_loc))) and (REF_MAND is null or REF_MAND=:ref_mand) order by REF_MAND nulls last, upper (NAME)');
            selquery.Parameters.ParamByName('ref_loc').Value   := LVSDatenModul.AktLocationRef;
            selquery.Parameters.ParamByName('ref_lager').Value := fRefLager;
            selquery.Parameters.ParamByName('ref_mand').Value  := fRefMand;

            try
              selquery.Open;

              while not (selquery.Eof) do begin
                idx := SpedComboBox.AddItemIndex (selquery.FieldByName ('NAME').AsString+'|'+selquery.FieldByName ('BESCHREIBUNG').AsString, TComboboxSped.Create (selquery.FieldByName ('REF').AsInteger, selquery.FieldByName ('IFC_KENNZEICHEN').AsString));

                if (fRefSped > 0) and (selquery.FieldByName ('REF').AsInteger = fRefSped) then
                  SpedComboBox.ItemIndex := idx;

                selquery.Next;
              end;

              selquery.Close;
            except
            end;
          finally
            selquery.Free;
          end;

          if (SpedComboBox.ItemIndex = -1) then
            SpedComboBox.ItemIndex := 0
          else begin
            SpedComboBoxChange (Self);

            if (fRefSpedProd <= 0) then
              SpedProduktComboBox.ItemIndex := 0
            else begin
              SpedProduktComboBox.ItemIndex := FindComboboxRef(SpedProduktComboBox, fRefSpedProd);
              if (SpedProduktComboBox.ItemIndex = -1) then SpedProduktComboBox.ItemIndex := 0;
            end;
          end;
        end;

        TrackingNrEdit.Visible := True;

        if (VersenderPanel.Visible) then
          VersenderEdit.Text := query.FieldByName ('VERSENDER').AsString
        else if not (query.FieldByName ('VERSENDER').IsNull) then
          SpedLabel.Caption := SpedLabel.Caption +  '(' + query.FieldByName ('VERSENDER').AsString +')';

        LHMSerialEdit.Text := query.FieldByName ('SENDUNGS_NR').AsString;
        TrackingNrEdit.Text := query.FieldByName ('TRACKING_ID').AsString;

        if (Length (query.Fields[3].AsString) < 1) then
          fOptShipmendNo := '0'
        else begin
          fOptShipmendNo := query.Fields[3].AsString [1];
          fPflicht := (query.Fields[8].AsString <> '0') or (fOptShipmendNo = '2');

          Caption := GetResourceText (1812);
          InputLabel.Caption := GetResourceText (1813);

          Label4.Caption := GetResourceText (1814);
        end;
      end;

      query.Close;
    end;
  finally
    query.Free;
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInputLHMSerialForm.ScannerErfassung(var Message: TMessage);
begin
  if (Length (ScanCode) > 3) and (copy (ScanCode, 2, 5) <> '#KBD-') then begin
    LHMSerialEdit.Text := copy (ScanCode, 2);

    ModalResult := mrOk;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInputLHMSerialForm.SpedComboBoxChange(Sender: TObject);
begin
  if (GetComboBoxRef(SpedComboBox) = -1) then
    ClearComboBoxObjects (SpedProduktComboBox)
  else begin
    LoadSpedProdukt (SpedProduktComboBox, GetComboBoxRef(SpedComboBox), fRefSpedProd);

    if (SpedProduktComboBox.Items.Count = 0) then
      SpedProduktComboBox.Enabled := False
    else begin
      SpedProduktComboBox.Items.Insert (0, '');
    end;
  end;
end;

end.
