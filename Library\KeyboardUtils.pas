unit KeyboardUtils;

interface

function isAltDown : Boolean;
function isCtrlDown : Boolean;
function isShiftDown : Boolean;

implementation

uses
  Windows;

function isAltDown : Boolean;
var
  State: TKeyboardState;
begin { isAltDown }
  GetKeyboardState(State);
  Result := ((State[vk_Menu] and 128)<>0);
end; { isAltDown }

function isCtrlDown : Boolean;
var
  State: TKeyboardState;
begin { isCtrlDown }
  GetKeyboardState(State);
  Result := ((State[VK_CONTROL] and 128)<>0);
end; { isCtrlDown }

function isShiftDown : Boolean;
var
  State: TKeyboardState;
begin { isShiftDown }
  GetKeyboardState(State);
  Result := ((State[vk_Shift] and 128)<>0);
end; { isShiftDown }

end.
