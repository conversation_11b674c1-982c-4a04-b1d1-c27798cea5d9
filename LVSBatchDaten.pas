unit LVSBatchDaten;

interface

uses <PERSON><PERSON>, DB, ADODB;

function ChangeBatchConfig    (	const RefCfg, RefMand, RefSubMand, RefBereich, RefPackplatz, RefLagerplatz: Integer;
                                const sConfName, sAufBatch, sAblauf : String;
                                const RefArtPlan : Integer;
                                const iPrio : Integer;
                                const Option : String) : Integer;

function CreateBatchConfig    ( const RefMand, RefSubMand, RefLocation, RefLager, RefBereich, RefPackplatz, RefLagerplatz: Integer;
                                const sConfName, sAufBatch, sAblauf : String;
                                const RefArtPlan : Integer;
                                const iPrio : Integer;
                                const Option : String;
                                var   Ref : Integer) : Integer;

function SetBatchConfigAufLimit     (const RefCfg, MinVPE, MaxVPE, MaxGewicht : Integer; const MaxVolumne: Int64) : Integer;
function SetBatchConfigLimit        (const RefCfg, <PERSON>Anz, MaxAnz, MaxGewicht : Integer; const MaxVolumne: Int64) : Integer;
function SetBatchConfigKommArtOpt   (const RefCfg : Integer; const KommArtOpt : String) : Integer;
function SetBatchConfigKommLTType   (const RefCfg : Integer; const RefLT : Integer) : Integer;
function SetBatchConfigTAGroup      (const RefCfg : Integer; const RefTAGroup : Integer) : Integer;
function SetBatchConfigRules        (const RefCfg : Integer; const IncLand, ExcLand, IncAufArt, ExcAufArt, IncSped, ExcSped, IncVersArt, ExcVersArt, IncAufInd, ExcAufInd : String) : Integer;

function SetBatchConfigStatus (const RefCfg : Integer; const Status : String) : Integer;
function DeleteBatchConfig    (const RefBConf : Integer) : Integer;


implementation

   uses  SysUtils, DatenModul, Variants;


function SetBatchConfigStatus (const RefCfg : Integer; const Status : String) : Integer;
var
  dbres: Integer;
  StoredProcedure: TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create(nil);

  try
    with StoredProcedure do begin
      ProcedureName := 'PA_BATCH_PLANUNG.SET_BATCH_CONFIG_STATUS';

      Parameters.CreateParameter('Result',     ftInteger, pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRef',       ftInteger, pdInput,  12, RefCfg);
      Parameters.CreateParameter('pStatus',    ftString,  pdInput,  16, Status);

      Parameters.CreateParameter('pErrorCode', ftInteger, pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText', ftString, pdOutput, 1024, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure(StoredProcedure);

  finally
    StoredProcedure.Free;
  end;
  Result := dbres;
end;



function ChangeBatchConfig    (	const RefCfg, RefMand, RefSubMand, RefBereich, RefPackplatz, RefLagerplatz: Integer;
                                const sConfName, sAufBatch, sAblauf : String;
                                const RefArtPlan : Integer;
                                const iPrio : Integer;
                                const Option : String) : Integer;
var
  dbres : Integer;
  StoredProcedure: TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create(nil);

  try

    with StoredProcedure do begin
      ProcedureName := 'PA_BATCH_PLANUNG.CHANGE_BATCH_CONFIG';

      Parameters.CreateParameter('Result',             ftInteger, pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRef',        ftInteger, pdInput,  12, RefCfg);
      Parameters.CreateParameter('pRefMand',    ftInteger, pdInput,  12, GetPLSQLParameter(RefMand));
      Parameters.CreateParameter('pRefSubMand', ftInteger, pdInput,  12, GetPLSQLParameter(RefSubMand));
      Parameters.CreateParameter('pBereich',    ftInteger, pdInput,  12, GetPLSQLParameter(RefBereich));
      Parameters.CreateParameter('pPackplatz',  ftInteger, pdInput,  12, GetPLSQLParameter(RefPackplatz));
      Parameters.CreateParameter('pLagerplatz', ftInteger, pdInput,  12, GetPLSQLParameter(RefLagerplatz));

      Parameters.CreateParameter('pConfName',   ftString,  pdInput,  32, sConfName);
      Parameters.CreateParameter('pAufBatch',   ftString,  pdInput,  12, sAufBatch);
      Parameters.CreateParameter('pAblauf',     ftString,  pdInput,  12, sAblauf);
      Parameters.CreateParameter('pRefPlanArt', ftInteger, pdInput,  12, GetPLSQLParameter (RefArtPlan));
      Parameters.CreateParameter('pPrio',       ftInteger, pdInput,  12, GetPLSQLParameter (iPrio));

      Parameters.CreateParameter('pOptions',    ftString,  pdInput,  16, Option);

      Parameters.CreateParameter('pErrorCode',  ftInteger, pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',  ftString, pdOutput, 1024, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure(StoredProcedure);
  finally
    StoredProcedure.Free;
  end;


  Result := dbres;
end;

function SetBatchConfigAufLimit     (const RefCfg, MinVPE, MaxVPE, MaxGewicht : Integer; const MaxVolumne: Int64) : Integer;
var
  dbres : Integer;
  StoredProcedure: TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create(nil);

  try
    with StoredProcedure do begin
      ProcedureName := 'PA_BATCH_PLANUNG.SET_BATCH_CONFIG_AUF_LIMIT';

      Parameters.CreateParameter('Result',             ftInteger, pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRef',        ftInteger, pdInput,  12, RefCfg);
      Parameters.CreateParameter('pMinVPE',     ftInteger, pdInput,  12, GetPLSQLParameter(MinVPE));
      Parameters.CreateParameter('pMaxVPE',     ftInteger, pdInput,  12, GetPLSQLParameter(MaxVPE));
      Parameters.CreateParameter('pMaxGewicht', ftInteger, pdInput,  12, GetPLSQLParameter(MaxGewicht));
      Parameters.CreateParameter('pMaxVolumen', ftInteger, pdInput,  12, GetPLSQLParameter(MaxVolumne));

      Parameters.CreateParameter('pErrorCode',  ftInteger, pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',  ftString, pdOutput, 1024, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure(StoredProcedure);

  finally
    StoredProcedure.Free;
  end;


  Result := dbres;
end;

function SetBatchConfigLimit (	const RefCfg, MinAnz, MaxAnz, MaxGewicht : Integer; const MaxVolumne: Int64) : Integer;
var
  dbres : Integer;
  StoredProcedure: TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create(nil);

  try
    with StoredProcedure do begin
      ProcedureName := 'PA_BATCH_PLANUNG.SET_BATCH_CONFIG_LIMIT';

      Parameters.CreateParameter('Result',             ftInteger, pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRef',        ftInteger, pdInput,  12, RefCfg);
      Parameters.CreateParameter('pMinAnz',     ftInteger, pdInput,  12, GetPLSQLParameter(MinAnz));
      Parameters.CreateParameter('pMaxAnz',     ftInteger, pdInput,  12, GetPLSQLParameter(MaxAnz));
      Parameters.CreateParameter('pMaxGewicht', ftInteger, pdInput,  12, GetPLSQLParameter(MaxGewicht));
      Parameters.CreateParameter('pMaxVolumen', ftInteger, pdInput,  12, GetPLSQLParameter(MaxVolumne));

      Parameters.CreateParameter('pErrorCode',  ftInteger, pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',  ftString, pdOutput, 1024, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure(StoredProcedure);

  finally
    StoredProcedure.Free;
  end;


  Result := dbres;
end;

function SetBatchConfigKommArtOpt (	const RefCfg : Integer; const KommArtOpt : String) : Integer;
var
  dbres : Integer;
  StoredProcedure: TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create(nil);

  try
    with StoredProcedure do begin
      ProcedureName := 'PA_BATCH_PLANUNG.SET_BATCH_CONFIG_KOMM_ART_OPT';

      Parameters.CreateParameter('Result',      ftInteger, pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRef',        ftInteger, pdInput,  12, RefCfg);
      Parameters.CreateParameter('pKommArtOpt', ftString, pdInput,  16, KommArtOpt);

      Parameters.CreateParameter('pErrorCode',  ftInteger, pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',  ftString, pdOutput, 1024, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure(StoredProcedure);

  finally
    StoredProcedure.Free;
  end;


  Result := dbres;
end;

function SetBatchConfigKommLTType (const RefCfg : Integer; const RefLT : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure: TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create(nil);

  try
    with StoredProcedure do begin
      ProcedureName := 'PA_BATCH_PLANUNG.SET_BATCH_CONFIG_KOMM_LT';

      Parameters.CreateParameter('Result',      ftInteger, pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRef',   ftInteger, pdInput,  12, RefCfg);
      Parameters.CreateParameter('pRefLT', ftInteger, pdInput,  12, GetPLSQLParameter (RefLT));

      Parameters.CreateParameter('pErrorCode',  ftInteger, pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',  ftString, pdOutput, 1024, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure(StoredProcedure);

  finally
    StoredProcedure.Free;
  end;

  Result := dbres;
end;

function SetBatchConfigTAGroup (const RefCfg : Integer; const RefTAGroup : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure: TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create(nil);

  try
    with StoredProcedure do begin
      ProcedureName := 'PA_BATCH_PLANUNG.SET_BATCH_CONFIG_TA_GROUP';

      Parameters.CreateParameter('Result',      ftInteger, pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRef',      ftInteger, pdInput,  12, RefCfg);
      Parameters.CreateParameter('pRefGroup', ftInteger, pdInput,  12, GetPLSQLParameter (RefTAGroup));

      Parameters.CreateParameter('pErrorCode',  ftInteger, pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',  ftString, pdOutput, 1024, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure(StoredProcedure);

  finally
    StoredProcedure.Free;
  end;

  Result := dbres;
end;

function SetBatchConfigRules (const RefCfg : Integer; const IncLand, ExcLand, IncAufArt, ExcAufArt, IncSped, ExcSped, IncVersArt, ExcVersArt, IncAufInd, ExcAufInd : String) : Integer;
var
  dbres : Integer;
  StoredProcedure: TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create(nil);

  try
    with StoredProcedure do begin
      ProcedureName := 'PA_BATCH_PLANUNG.SET_BATCH_CONFIG_REGELN';

      Parameters.CreateParameter('Result',      ftInteger, pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRef',      ftInteger, pdInput,  12, RefCfg);
      Parameters.CreateParameter('pIncLand', ftString, pdInput,  128, IncLand);
      Parameters.CreateParameter('pExcLand', ftString, pdInput,  128, ExcLand);
      Parameters.CreateParameter('pIncAufArt', ftString, pdInput,  128, IncAufArt);
      Parameters.CreateParameter('pExcAufArt', ftString, pdInput,  128, ExcAufArt);
      Parameters.CreateParameter('pIncSped', ftString, pdInput,  256, IncSped);
      Parameters.CreateParameter('pExcSped', ftString, pdInput,  256, ExcSped);
      Parameters.CreateParameter('pIncVersArt', ftString, pdInput,  128, IncVersArt);
      Parameters.CreateParameter('pExcVersArt', ftString, pdInput,  128, ExcVersArt);
      Parameters.CreateParameter('pIncAufInd', ftString, pdInput,  128, IncAufInd);
      Parameters.CreateParameter('pExcAufInd', ftString, pdInput,  128, ExcAufInd);

      Parameters.CreateParameter('pErrorCode',  ftInteger, pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',  ftString, pdOutput, 1024, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure(StoredProcedure);

  finally
    StoredProcedure.Free;
  end;

  Result := dbres;
end;


function CreateBatchConfig (const RefMand, RefSubMand, RefLocation, RefLager, RefBereich, RefPackplatz, RefLagerplatz: Integer;
                            const sConfName, sAufBatch, sAblauf : String;
                            const RefArtPlan : Integer;
                            const iPrio : Integer;
                            const Option : String;
                            var   Ref : Integer) : Integer;
var
  dbres: Integer;
  StoredProcedure: TADOStoredProc;
begin
  dbres := 0;

  Ref := -1;

  StoredProcedure := TADOStoredProc.Create(nil);
  try
    with StoredProcedure do begin

      ProcedureName := 'PA_BATCH_PLANUNG.CREATE_BATCH_CONFIG';

      Parameters.CreateParameter('Result',             ftInteger, pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRefMand',          ftInteger, pdInput,  12, GetPLSQLParameter(RefMand));
      Parameters.CreateParameter('pRefSubMand',       ftInteger, pdInput,  12, GetPLSQLParameter(RefSubMand));
      Parameters.CreateParameter('pRefLocation',      ftInteger, pdInput,  12, GetPLSQLParameter(RefLocation));
      Parameters.CreateParameter('pRefLager',         ftInteger, pdInput,  12, GetPLSQLParameter(RefLager));
      Parameters.CreateParameter('pRefBereich',       ftInteger, pdInput,  12, GetPLSQLParameter(RefBereich));
      Parameters.CreateParameter('pRefPackplatz',     ftInteger, pdInput,  12, GetPLSQLParameter(RefPackplatz));
      Parameters.CreateParameter('pRefLagerplatz',    ftInteger, pdInput,  12, GetPLSQLParameter(RefLagerplatz));

      Parameters.CreateParameter('pConfName',         ftString,  pdInput,  32, sConfName);
      Parameters.CreateParameter('pAufBatch',         ftString,  pdInput,  12, sAufBatch);
      Parameters.CreateParameter('pAblauf',           ftString,  pdInput,  12, sAblauf);
      Parameters.CreateParameter('pRefPlanArt',       ftInteger, pdInput,  12, GetPLSQLParameter (RefArtPlan));
      Parameters.CreateParameter('pPrio',             ftInteger, pdInput,  12, GetPLSQLParameter (iPrio));

      Parameters.CreateParameter('pOptions',          ftString,  pdInput,  16, Option);

      Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, NULL);

      Parameters.CreateParameter('pErrorCode', ftInteger, pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText', ftString, pdOutput, 1024, '');

      dbres := LVSDatenModul.CallStoreProcedure(StoredProcedure);
    end;

    if (dbres = 0) then begin
      if not (StoredProcedure.Parameters.ParamValues ['oRef'] = NULL) then
        Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
    end;
    
  finally
    StoredProcedure.Free;
  end;
  Result := dbres;
end;

function DeleteBatchConfig (const RefBConf : Integer) : Integer;
var
  dbres: Integer;
  StoredProcedure: TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create(nil);
  try
    with StoredProcedure do begin
      ProcedureName := 'PA_BATCH_PLANUNG.DELETE_BATCH_CONFIG';

      Parameters.CreateParameter('Result',     ftInteger, pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRef',       ftInteger, pdInput,  12, RefBConf);

      Parameters.CreateParameter('pErrorCode', ftInteger, pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText', ftString, pdOutput, 1024, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure(StoredProcedure);

  finally
    StoredProcedure.Free;
  end;

  Result := dbres;
end;

end.
