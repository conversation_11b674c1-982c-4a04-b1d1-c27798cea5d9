﻿unit AuftragAdrInfoDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Menus, Vcl.ExtCtrls, AuftragAdrFRM;

type
  TAuftragAdrInfoForm = class(TForm)
    OkButton: TButton;
    KundenAdrPopupMenu: TPopupMenu;
    EditKundenAdrMenuItem: TMenuItem;
    LiefAdrPopupMenu: TPopupMenu;
    EditLiefAdrMenuItem: TMenuItem;
    ShowAddrGoogleMapsMenuItem: TMenuItem;
    KundenAdrPanel: TPanel;
    LiefAdrPanel: TPanel;
    ShipPanel: TPanel;
    ShipGroupBox: TGroupBox;
    Label13: TLabel;
    ShipAdrNameLabel: TLabel;
    Label15: TLabel;
    Label16: TLabel;
    Label17: TLabel;
    ShipAdrZusatzLabel: TLabel;
    ShipAdrStrasseLabel: TLabel;
    ShipAdrOrtLabel: TLabel;
    Label21: TLabel;
    ShipAdrColliLabel: TLabel;
    Label23: TLabel;
    ShipAdrFilialLabel: TLabel;
    EndAdrPanel: TPanel;
    KundenAdrFrame: TAuftragAdrFrame;
    LieferAdrFrame: TAuftragAdrFrame;
    EndKundeAdrFrame: TAuftragAdrFrame;
    EndAdrPopupMenu: TPopupMenu;
    MenuItem1: TMenuItem;
    procedure FormCreate(Sender: TObject);
    procedure EditAdrMenuItemClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure KundenAdrPopupMenuPopup(Sender: TObject);
    procedure LiefAdrPopupMenuPopup(Sender: TObject);
    procedure ShowAddrGoogleMapsMenuItemClick(Sender: TObject);
  private
    fRefAuf       : Integer;
    fRefWA        : Integer;

    fAufStatus    : String;
    fLager        : String;
    fMandant      : String;

    fUpdateFlag : Boolean;

    procedure DisplayAddress;
  public
    property UpdateFlag  : Boolean read fUpdateFlag;

    property RefAuf       : Integer read fRefAuf       write fRefAuf;
    property RefWA        : Integer read fRefWA        write fRefWA;
  end;

implementation

{$R *.dfm}

uses
  DB, ADODB, BetterADODataSet, DatenModul, EditAuftragAddressDLG, LVSSecurity, WebDLG,
  ConfigModul, SprachModul, Ora, OraSmart;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragAdrInfoForm.EditAdrMenuItemClick(Sender: TObject);
var
  editfrom : TEditAuftragAddressForm;
begin
  if (LVSSecurityModule.CheckChangeRecht (fMandant, '', '', 'ChangeAddress')) then begin
    editfrom := TEditAuftragAddressForm.Create (Self);

    try
      editfrom.RefAuf := fRefAuf;

      if (Sender = EditKundenAdrMenuItem) then
        editfrom.RefKundenAdr := KundenAdrFrame.RefAdr
      else if (Sender = EditLiefAdrMenuItem) then
        editfrom.RefLiefAdr := LieferAdrFrame.RefAdr
      else if (Sender = EndAdrPopupMenu) then
        editfrom.RefAdr := EndKundeAdrFrame.RefAdr;

      if (editfrom.ShowModal = mrOk) then begin
        fUpdateFlag := True;
      end;
    finally
      editfrom.Release;
    end;

    if fUpdateFlag  then
      DisplayAddress;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragAdrInfoForm.FormCreate(Sender: TObject);
begin
  fUpdateFlag := False;

  fRefWA  := -1;
  fRefAuf := -1;
  fAufStatus := '';
  fMandant := '';

  ShipAdrNameLabel.Caption := '';
  ShipAdrZusatzLabel.Caption := '';
  ShipAdrStrasseLabel.Caption := '';
  ShipAdrOrtLabel.Caption := '';
  ShipAdrColliLabel.Caption := '';
  ShipAdrFilialLabel.Caption := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragAdrInfoForm.FormShow(Sender: TObject);
begin
  if not (ShipGroupBox.Visible) then
    Height := Height - ShipGroupBox.Height;

  DisplayAddress;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragAdrInfoForm.KundenAdrPopupMenuPopup(Sender: TObject);
begin
  EditKundenAdrMenuItem.Enabled := LVSSecurityModule.CheckChangeRecht (fMandant, fLager, '', 'ChangeAuftrag') and LVSSecurityModule.CheckChangeRecht (fMandant, fLager, '', 'ChangeAddress') and (KundenAdrFrame.RefAdr <> -1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragAdrInfoForm.LiefAdrPopupMenuPopup(Sender: TObject);
begin
  EditLiefAdrMenuItem.Enabled := LVSSecurityModule.CheckChangeRecht (fMandant, fLager, '', 'ChangeAuftrag') and LVSSecurityModule.CheckChangeRecht (fMandant, fLager, '', 'ChangeAddress') and (LieferAdrFrame.RefAdr <> -1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragAdrInfoForm.ShowAddrGoogleMapsMenuItemClick(Sender: TObject);
var
  webform : TWebForm;
begin
  webform := TWebForm.Create (Self);
  webform.Name := 'GoogelMapsWebForm';

  try
    LVSConfigModul.RestoreFormInfo (webform);

    (*
    webform.Caption := 'Adresse: '+fKundeStrasse+', '+fKundeOrt;
    webform.GoogelMapsAddr := fKundeStrasse+', '+fKundeOrt;
    webform.ShowModal;
    *)

    LVSConfigModul.SaveFormInfo (webform);
  finally
    webform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragAdrInfoForm.DisplayAddress;
var
  query   : TSmartQuery;
begin
  query := LVSDatenModul.CreateSmartQuery (Self, 'DisplayAddress');

  try
    if (fRefAuf > 0) then begin
      query.SQL.Add ('select a.STATUS,a.MANDANT as MANDANT,a.LAGER as LAGER,adr.* from V_AUFTRAG_ADR adr, V_AUFTRAG a where adr.REF=a.REF_KUNDEN_ADR and a.REF=:ref');
      query.Params[0].Value := RefAuf;

      try
        query.Open;

        fLager      := query.FieldByName('LAGER').AsString;
        fMandant    := query.FieldByName('MANDANT').AsString;
        fAufStatus  := query.FieldByName('STATUS').AsString;

        KundenAdrFrame.ShowAdress ('Kunden-Adresse', query);

        query.Close;
      except
      end;

      query.SQL.Clear;
      query.SQL.Add ('select a.STATUS,a.MANDANT as MANDANT,a.LAGER as LAGER,adr.* from V_AUFTRAG_ADR adr, V_AUFTRAG a where adr.REF=a.REF_LIEFER_ADR and a.REF=:ref');
      query.Params[0].Value := RefAuf;

      try
        query.Open;

        if (KundenAdrFrame.RefAdr = query.FieldByName('REF').AsInteger) then begin
          if LiefAdrPanel.Visible then begin
            LiefAdrPanel.Visible := false;
            Height := Height - LiefAdrPanel.Height;
          end;
        end else begin
          LieferAdrFrame.ShowAdress ('Abweichende Lieferanschrift', query);
        end;

        query.Close;
      except
      end;

      query.SQL.Clear;
      query.SQL.Add ('select a.STATUS,a.MANDANT as MANDANT,a.LAGER as LAGER,adr.* from V_AUFTRAG_ADR adr, V_AUFTRAG a where adr.ART=''ENDKUNDE_ADR'' and adr.REF_AUF_KOPF=a.REF and a.REF=:ref');
      query.Params[0].Value := RefAuf;

      try
        query.Open;

        if query.FieldByName('REF').IsNull then begin
          if EndAdrPanel.Visible then begin
            EndAdrPanel.Visible := false;
            Height := Height - EndAdrPanel.Height;
          end;
        end else begin
          EndKundeAdrFrame.ShowAdress ('Endkunden Lieferanschrift', query);
        end;

        query.Close;
      except
      end;

      if (ShipGroupBox.Visible) then begin
        query.SQL.Clear;
        query.SQL.Add ('select * from V_AUFTRAG_ADR where ART=''DEPOT_ADR'' and REF_AUF_KOPF=:ref');
        query.Params[0].Value := RefAuf;

        try
          query.Open;

          if (query.FieldByName('REF').IsNull) then begin
            if ShipPanel.Visible then begin
              ShipPanel.Visible := False;
              Height := Height - ShipPanel.Height;
            end;
          end else begin
            ShipAdrNameLabel.Caption := StringReplace (query.FieldByName('NAME1').AsString, '&', '&&', [rfReplaceAll]);
            if not (query.FieldByName('NAME2').IsNull) then
              ShipAdrNameLabel.Caption := ShipAdrNameLabel.Caption+  ' / ' + StringReplace (query.FieldByName('NAME2').AsString, '&', '&&', [rfReplaceAll]);

            ShipAdrZusatzLabel.Caption := StringReplace (query.FieldByName('NAMEZUSATZ').AsString, '&', '&&', [rfReplaceAll]);
            ShipAdrFilialLabel.Caption := StringReplace (query.FieldByName('FILIAL_NR').AsString, '&', '&&', [rfReplaceAll]);
            ShipAdrStrasseLabel.Caption := StringReplace (query.FieldByName('STRASSE').AsString, '&', '&&', [rfReplaceAll]);
            ShipAdrOrtLabel.Caption := query.FieldByName('PLZ').AsString+' '+StringReplace (query.FieldByName('ORT').AsString, '&', '&&', [rfReplaceAll]);
            ShipAdrColliLabel.Caption := StringReplace (query.FieldByName('COLLI_ADR_TEXT').AsString, '&', '&&', [rfReplaceAll]);
          end;

          query.Close;
        except
        end;
      end;
    end else if (fRefWA > 0) then begin
      query.SQL.Clear;
      query.SQL.Add ('select wa.MANDANT,wa.LAGER, adr.* from V_WARENAUSGANG wa, V_AUFTRAG_ADR adr where adr.REF=wa.REF_KUNDEN_ADR and wa.REF=:ref');
      query.Params[0].Value := fRefWA;

      try
        query.Open;

        fLager      := query.FieldByName('LAGER').AsString;
        fMandant    := query.FieldByName('MANDANT').AsString;
        fAufStatus  := query.FieldByName('STATUS').AsString;

        KundenAdrFrame.ShowAdress ('Kunden-Adresse', query);

        query.SQL.Clear;
        query.SQL.Add ('select wa.MANDANT,wa.LAGER, adr.* from V_WARENAUSGANG wa, V_AUFTRAG_ADR adr where adr.REF=wa.REF_LIEFER_ADR and wa.REF=:ref');
        query.Params[0].Value := fRefWA;

        if (KundenAdrFrame.RefAdr = query.FieldByName('REF').AsInteger) then begin
          if LiefAdrPanel.Visible then begin
            LiefAdrPanel.Visible := false;
            Height := Height - LiefAdrPanel.Height;
          end;
        end else begin
          LieferAdrFrame.ShowAdress ('Abweichende Lieferanschrift', query);
        end;

        query.Close;
      except
      end;
    end;
  finally
    query.Free;
  end;
end;

end.
