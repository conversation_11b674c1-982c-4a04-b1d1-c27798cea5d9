unit DateComboboxDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ComCtrls;

type
  TDateComboboxForm = class(TForm)
    DateTimePicker: TDateTimePicker;
    procedure FormCreate(Sender: TObject);
    procedure FormKeyPress(Sender: TObject; var Key: Char);
    procedure FormShow(Sender: TObject);
  private
    fParentSelControl : TWinControl;
  public
    property ParentSelControl : TWinControl read fParentSelControl write fParentSelControl;
  end;

implementation

{$R *.dfm}

procedure TDateComboboxForm.FormCreate(Sender: TObject);
begin
  fParentSelControl := Nil;

  DateTimePicker.Date := Now;
end;

procedure TDateComboboxForm.FormKeyPress(Sender: TObject; var Key: Char);
begin
  if (Key = #27) then begin
    Key := #0;

    ModalResult := mrAbort;
    Close;
  end else if (Key = #13) then begin
    Key := #0;

    ModalResult := mrOk;
  end;
end;

procedure TDateComboboxForm.FormShow(Sender: TObject);
var
  cl      : TPoint;
begin
  if Assigned (fParentSelControl) and Assigned (fParentSelControl.Parent) then begin
    cl := fParentSelControl.Parent.ClientToScreen (Point (fParentSelControl.Left + (fParentSelControl.Width div 2), fParentSelControl.Top + (fParentSelControl.Height div 2)));

    Top  := cl.Y;
    Left := cl.X;
  end;

  DateTimePicker.SetFocus;
end;

end.
