unit ACOManagerMain;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComCtrls, UserAdminFrame, RegistryUtils, Menus;

type
  TMainForm = class(TForm)
    StatusBar1: TStatusBar;
    Panel1: TPanel;
    Button1: TButton;
    Bevel1: TBevel;
    UserAdminFrame1: TUserAdminFrame;
    procedure Button1Click(Sender: TObject);
    procedure FormKeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
  private
  public
    procedure Init;
    function OpenDatanbase : Integer;
  end;

var
  MainForm: TMainForm;

implementation

{$R *.dfm}

uses ManagerACOModul, LVSSecurity, FrontendUtils, UserDLG, ConfigModul,
     DatenModul, SynchUserACODLG;

procedure TMainForm.Init;
var
  intwert : Integer;
  winstat : TWindowState;
begin
  LVSSecurityModule.ACOModul := ManagerACOModule;

  UserReg := TRegistryModule.Create;
  UserReg.OpenKey(HKEY_CURRENT_USER, 'Software\' + LVSConfigModul.MasterRegKeyName + '\ACOManager', KEY_READ or KEY_WRITE, True);

  if (UserReg.ReadRegValue('MainState', intwert) <> 0) then
    winstat := wsNormal
  else begin
    winstat := TWindowState(intwert);
  end;

  WindowState := winstat;

  if (winstat = wsNormal) then begin
    if (UserReg.ReadRegValue('MainTop', intwert) = 0) then Top := intwert;
    if (UserReg.ReadRegValue('MainLeft', intwert) = 0) then Left := intwert;
    if (UserReg.ReadRegValue('MainWidth', intwert) = 0) then Width := intwert;
    if (UserReg.ReadRegValue('MainHeight', intwert) = 0) then Height := intwert;

    if (Top > Monitor.Height) then
      Top := Monitor.Height - Height - 20;
    if (Left > Monitor.Height) then
      Left := Monitor.Height - Width - 20;
  end;
end;

function TMainForm.OpenDatanbase : Integer;
begin
  Result := OpenLVSDatabase ('ACO', 'ACOManager', True);

  if (Result = 0) then begin
    StatusBar1.Panels[1].Text := LVSDatenModul.AktUser + '@' + LVSDatenModul.Schema;
    StatusBar1.Panels[2].Text := LVSDatenModul.AktMandant;

    UserAdminFrame1.Prepare;
    UserAdminFrame1.Show;
  end;
end;


procedure TMainForm.Button1Click(Sender: TObject);
begin
  Close;
end;

procedure TMainForm.FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
begin
  if (Key = VK_F5) then
    UserAdminFrame1.Show;
end;

end.
