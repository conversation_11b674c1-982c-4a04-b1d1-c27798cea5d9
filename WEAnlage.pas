﻿//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : WEAnlage
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/WEAnlage.pas $
// $Revision: 93 $
// $Modtime: 13.06.23 13:26 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Daten zu einem neuen Wareneingang erfassen
//*****************************************************************************
unit WEAnlage;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, DB, ADODB, CompTranslate, ExtCtrls, ComCtrls,
  ComboBoxPro, Menus, ConfigModul, BarCodeScanner, IntegerUpDown,
  AdvDateTimePicker;

type
  TWEAnlageForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    ADOQuery1: TADOQuery;
    Bevel4: TBevel;
    WEAbrechungCheckBox: TCheckBox;
    Panel1: TPanel;
    Label9: TLabel;
    MandantComboBox: TComboBoxPro;
    SubMandPanel: TPanel;
    Label17: TLabel;
    SubMandComboBox: TComboBoxPro;
    Panel2: TPanel;
    Bevel1: TBevel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    Label5: TLabel;
    Label6: TLabel;
    Lieferant: TLabel;
    Label1: TLabel;
    Bevel2: TBevel;
    Bevel3: TBevel;
    Label7: TLabel;
    Label8: TLabel;
    Bevel5: TBevel;
    Label10: TLabel;
    Label11: TLabel;
    Label12: TLabel;
    Label14: TLabel;
    Label13: TLabel;
    Label15: TLabel;
    Label16: TLabel;
    Bevel6: TBevel;
    LSNrEdit: TEdit;
    WEComboBox: TComboBoxPro;
    SpedComboBox: TComboBoxPro;
    LeerComboBox: TComboBoxPro;
    BestNrComboBox: TComboBox;
    LieferantComboBox: TComboBoxPro;
    LagerComboBox: TComboBoxPro;
    DateDateTimePicker: TDateTimePicker;
    TimeDateTimePicker: TDateTimePicker;
    FahrerEdit: TEdit;
    LKWEdit: TEdit;
    DocIDEdit: TEdit;
    PackageEdit: TEdit;
    HintEdit: TEdit;
    CompTranslateForm1: TCompTranslateForm;
    LFPopupMenu: TPopupMenu;
    NeuerLieferant1: TMenuItem;
    SpedPopupMenu: TPopupMenu;
    NewSpeditionMenuItem: TMenuItem;
    PackageTypComboBox: TComboBox;
    Label18: TLabel;
    LSDateDateTimePicker: TAdvDateTimePicker;
    LeerPanel: TPanel;
    PackTypDutyLabel: TLabel;
    LSDateDutyLabel: TLabel;
    LSNrDutyLabel: TLabel;
    LiefDutyLabel: TLabel;
    KFZDutyLabel: TLabel;
    DateDutyLabel: TLabel;
    PackStDutyLabel: TLabel;
    PackageUpDown: TIntegerUpDown;
    procedure LieferantComboBoxChange(Sender: TObject);
    procedure BestNrComboBoxChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure SpedComboBoxChange(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure LagerComboBoxChange(Sender: TObject);
    procedure NeuerLieferant1Click(Sender: TObject);
    procedure MandantComboBoxChange(Sender: TObject);
    procedure NewSpeditionMenuItemClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure LeerComboBoxChange(Sender: TObject);
    procedure NumEditKeyPress(Sender: TObject; var Key: Char);
    procedure ComboBoxDropDown(Sender: TObject);
    procedure EditChange(Sender: TObject);
    procedure LSDateDateTimePickerChange(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure SubMandComboBoxChange(Sender: TObject);
    procedure DateDateTimePickerCloseUp(Sender: TObject);
    procedure DateTimePickerDropDown(Sender: TObject);
    procedure DateDateTimePickerChange(Sender: TObject);
    procedure PackageUpDownClick(Sender: TObject; Button: TUDBtnType);
  private
    fRefLager        : Integer;
    fMandantConfig   : TMandantConfig;
    fSortLieferant   : Integer;
    fLieferantName   : String;
    fLieferantNr     : String;
    fLastLSDate      : TDateTime;
    fSubMandRequired : Boolean;

    fMandOptions,
    fSubMandOptions  : String;

    fNoteDateReq     : Boolean;   //Lieferscheindatum pflicht
    fDelDateReq      : Boolean;   //Lieferdatum pflicht
    fNoteNoReq       : Boolean;   //Lieferscheinnummer pflicht
    fPackTypReq      : Boolean;   //Verpackungstyp pflicht
    fKFZReq          : Boolean;   //KFZ Kennzeichen pflicht
    fLieferantReq    : Boolean;   //Lieferant pflicht
    fPackStReq       : Boolean;   //Packstück Anzahl pflicht

    procedure UpdateDuty;

    function UpdateLiefCombobox (Sender: TObject) : Integer;

    procedure ScannerErfassung (var Message: TMessage); message WM_USER + SCANNER_DETECT;
    procedure SetDateMaxDays(const refMand: Integer;var errormsg: string);
    function IsOptionRequired(const Options: string; const Opt: Integer): Boolean;
  public
    VonDate,
    BisDate : TDateTime;

    PflichSpedition : Boolean;
    PflichLeergut   : Boolean;
    PflichWELP      : Boolean;

    property LieferantName : String read fLieferantName;
    property LieferantNr   : String read fLieferantNr;

    procedure Prepare (const MandantRef, SubMandantRef, LagerRef : Integer);
  end;

implementation

uses
  ADOInt, DateUtils, CommCtrl,
  VCLUtilitys, StringUtils, SprachModul, DatenModul, LVSConst, LVSGlobalDaten, FrontEndUtils, EditLieferantDLG, LVSLieferanten,
  EditSpeditionDLG, LVSSpedInterface, ResourceText, LVSKundenBarcodes, LablePrinterUtils, LVSDatenInterface;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.EditChange(Sender: TObject);
begin
  LeerPanel.Visible := false;

  if (Sender is TEdit) then
    (Sender as TEdit).Color := clWindow;
end;


procedure TWEAnlageForm.PackageUpDownClick(Sender: TObject; Button: TUDBtnType);
var
  value: Integer;
begin
  value := StrToIntDef(PackageEdit.Text, 0);
  case Button of
    btNext: Inc(value);
    btPrev: Dec(value);
  end;
  PackageEdit.Text := IntToStr(value);
end;


procedure TWEAnlageForm.Prepare (const MandantRef, SubMandantRef, LagerRef : Integer);
var
  altcursor : TCursor;
  query     : TADOQuery;
begin
  LSNrEdit.Text := '';

  DateTime_SetFormat(LSDateDateTimePicker.Handle, PChar(''''''));
  DateTime_SetFormat(DateDateTimePicker.Handle, PChar(''''''));
  DateTime_SetFormat(TimeDateTimePicker.Handle, PChar(''''''));

  ClearComboBoxObjects (SpedComboBox);
  ClearComboBoxObjects (WEComboBox);
  BestNrComboBox.Clear;

  ClearComboBoxObjects (LeerComboBox);

  fRefLager := LagerRef;

  altcursor := Screen.Cursor;
  Screen.Cursor :=crSQLWait;

  try
    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select * from V_WARENEINGANG where ROWNUM=0');

      query.Open;

      if not Assigned (query.FindField ('TYPE_PACKAGES')) then
        PackageTypComboBox.Visible := false;
    finally
      query.Free;
    end;

    if (PackageTypComboBox.Visible) then begin
      LoadComboxDBItems (PackageTypComboBox, 'WARENEINGANG', 'TYPE_PACKAGES', False);

      if (PackageTypComboBox.Items.Count = 0) then
        PackageTypComboBox.Enabled := False;
    end;

    LoadMandantCombobox (MandantComboBox, LVSDatenModul.AktLocationRef);

    if (MandantRef <> -1) Then begin
      MandantComboBox.Enabled := False;
      MandantComboBox.ItemIndex := FindComboboxRef(MandantComboBox, MandantRef)
    end else if (LVSDatenModul.AktMandantRef <> -1) Then begin
      MandantComboBox.Enabled := False;
      MandantComboBox.ItemIndex := FindComboboxRef(MandantComboBox, LVSDatenModul.AktMandantRef)
    end else if (MandantComboBox.Items.Count = 1) Then begin
      MandantComboBox.Enabled := False;
      MandantComboBox.ItemIndex := 0;
    end else begin
      MandantComboBox.ItemIndex := -1;
      MandantComboBox.Enabled := True;
    end;

    if (MandantComboBox.ItemIndex <> -1) then begin
      MandantComboBox.OnChange (MandantComboBox);

      if (SubMandantRef <> -1) Then begin
        SubMandComboBox.Enabled := False;
        SubMandComboBox.ItemIndex := FindComboboxRef(SubMandComboBox, SubMandantRef)
      end;

      if (fRefLager <> -1) then begin
        LagerComboBox.ItemIndex := FindComboboxRef(LagerComboBox, fRefLager);
        LagerComboBoxChange (LagerComboBox);
      end;
    end;
  except
  end;

  Screen.Cursor := altcursor;
end;

procedure TWEAnlageForm.SetDateMaxDays(const refMand: Integer;var errormsg: string);
var
  res,Tage : Integer;

begin
  Tage := 3;

  res := GetLSMaxFutureDays(refMand, Tage, errormsg);
  if (res <> 0) then begin
    MessageDLG(errormsg, mtError, [mbOk], 0);
  end;

  LSDateDateTimePicker.MaxDate := Now + Tage;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.LieferantComboBoxChange(Sender: TObject);
var
  bestnr : String;
begin
  if not (LieferantComboBox.DroppedDown) and (BestNrComboBox.Enabled) then begin
    if (BestNrComboBox.Enabled) then begin
      if (BestNrComboBox.ItemIndex = -1) or (BestNrComboBox.ItemIndex = 0) then
        bestnr := '<keine>'
      else if (BestNrComboBox.ItemIndex = 1) then
        bestnr := '<alle>'
      else
        bestnr := BestNrComboBox.Text;

      ClearComboBoxObjects (BestNrComboBox);

      BestNrComboBox.Items.Add (GetResourceText (1057));
      BestNrComboBox.Items.Add (GetResourceText (1058));

      if (GetComboBoxRef (LieferantComboBox) > 0) Then begin
        BestNrComboBox.Enabled := True;

        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ('select distinct (BESTELL_NR) from V_BESTELLUNG where REF_LIEFERANT=:ref_lf and REF_MAND=:ref_mand and REF_LAGER=:ref_lager');
        ADOQuery1.Parameters.ParamByName('ref_lf').Value := GetComboBoxRef (LieferantComboBox);
        ADOQuery1.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandantComboBox);
        ADOQuery1.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);

        if (VonDate > 0) Then begin
          ADOQuery1.SQL.Add ('and WE_DATUM >= :von_date');
          ADOQuery1.Parameters.ParamByName('von_date').ParameterObject.Type_ := adDBTimeStamp;
          ADOQuery1.Parameters.ParamByName('von_date').Value := Trunc (VonDate);
        end;

        if (BisDate > 0) Then begin
          ADOQuery1.SQL.Add ('and WE_DATUM <= :bis_date');
          ADOQuery1.Parameters.ParamByName('bis_date').ParameterObject.Type_ := adDBTimeStamp;
          ADOQuery1.Parameters.ParamByName('bis_date').Value := Trunc (BisDate) + (86399/86400);
        end;

        ADOQuery1.SQL.Add ('order by BESTELL_NR');

        try
          ADOQuery1.Open;

          while not (ADOQuery1.Eof) do begin
            BestNrComboBox.Items.Add (ADOQuery1.Fields [0].AsString);

            ADOQuery1.Next;
          end;

          ADOQuery1.Close;
        except
        end;
      end;

      if (bestnr = '<keine>') then
        BestNrComboBox.ItemIndex := 0
      else if (bestnr = '<alle>') then
        BestNrComboBox.ItemIndex := 1
      else begin
        BestNrComboBox.ItemIndex := BestNrComboBox.Items.IndexOf (bestnr);
        if (BestNrComboBox.ItemIndex = -1) then BestNrComboBox.ItemIndex := BestNrComboBox.Items.Add (bestnr);
      end;

      BestNrComboBoxChange (Self);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.LSDateDateTimePickerChange(Sender: TObject);
var
  dt             : TDateTime;
  accdays,
  y1,y2,md,
  d1,d2,d3,d4,d5 : Integer;
begin
  EditChange (Sender);

  DateTime_SetFormat(LSDateDateTimePicker.Handle, PChar('dd.MM.yyyy'));

  if not ((Sender as TDateTimePicker).DroppedDown) then begin
    CheckConfigParameter (GetComboBoxRef (MandantComboBox), LVSDatenModul.AktLocationRef, -1, 'WE_ABGRENZUNG_TAGE', accdays, -1);

    if (accdays <= 0) then
      fLastLSDate := Trunc (LSDateDateTimePicker.Date)
    else begin
      dt := Trunc (LSDateDateTimePicker.Date);

      y1 := YearOf (Now);
      y2 := YearOf (dt);
      d1 := MonthOf (Now);
      d2 := MonthOf (dt);

      if (y1 > y2) then
        md := d2 - 12 - d1
      else if (y1 < y2) then
        md := (12 - d1) + d2
      else md := d2 - d1;

      d3 := DayOfTheMonth (Now);
      d4 := DayOfTheMonth (dt);
      d5 := DaysInMonth (dt);

      if (md < -1) or ((md < 0) and (d3 > accdays)) then begin
        MessageDLG('Das Lieferschein-Datum liegt ausserhalb des Abgrenzungszeitraumes', mtInformation, [mbOK], 0);

        LSDateDateTimePicker.Date := Trunc (fLastLSDate);
      end else begin
        fLastLSDate := Trunc (LSDateDateTimePicker.Date);
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 17.12.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.DateTimePickerDropDown(Sender: TObject);
begin
  EditChange (Sender);

  if ((Sender as TDateTimePicker).DateTime < 10) then begin
    (Sender as TDateTimePicker).Kind := dtkTime;
    (Sender as TDateTimePicker).Kind := dtkDate;

    (Sender as TDateTimePicker).DateTime := Now;

    SendMessage((Sender as TDateTimePicker).Handle,WM_SYSKEYDOWN,VK_DOWN, 0);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.NumEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,#9,^C,^V,'0'..'9']) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.BestNrComboBoxChange(Sender: TObject);
var
  leerref,
  spedref : Integer;
begin
  if (BestNrComboBox.ItemIndex > 1) and (Length (BestNrComboBox.Text) > 0) and (Pos (';', BestNrComboBox.Text) = 0) then begin
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_LIEFERANT_REL_SPEDITION where REF_LIEFERANT=:ref_lief and (REF_SPEDITION is not null or REF_LEERGUT_KONTO is not null)');
    ADOQuery1.SQL.Add ('and (wochen_tag=0 or wochen_tag=TO_CHAR ((select WE_DATUM from V_BESTELLUNG where REF_MAND=:ref_mand and REF_LAGER=:ref_lager and BESTELL_NR=:best_nr),''D'')) order by wochen_tag desc');
    ADOQuery1.Parameters.ParamByName ('ref_lief').Value := GetComboBoxRef (LieferantComboBox);
    ADOQuery1.Parameters.ParamByName ('ref_mand').Value := GetComboBoxRef (MandantComboBox);
    ADOQuery1.Parameters.ParamByName ('ref_lager').Value := GetComboBoxRef (LagerComboBox);
    ADOQuery1.Parameters.ParamByName ('best_nr').Value := BestNrComboBox.Text;

    try
      ADOQuery1.Open;

      if (ADOQuery1.FieldByName ('REF_SPEDITION').IsNull) then
        spedref := -1
      else spedref := ADOQuery1.FieldByName ('REF_SPEDITION').AsInteger;

      if (ADOQuery1.FieldByName ('REF_LEERGUT_KONTO').IsNull) then
        leerref := -1
      else leerref := ADOQuery1.FieldByName ('REF_LEERGUT_KONTO').AsInteger;

      ADOQuery1.Close;
    except
      leerref := -1;
      spedref := -1;
    end;

    if (spedref = -1) then begin
      SpedComboBox.ItemIndex := 0;

      //Wenn noch kein Konto ausgew�hlt wurde
      if (LeerComboBox.ItemIndex <= 0) then begin
        if (leerref = -1) then
           LeerComboBox.ItemIndex := 0
        else begin
          LeerComboBox.ItemIndex := FindComboboxRef (LeerComboBox, leerref);
          if (LeerComboBox.ItemIndex = -1) then LeerComboBox.ItemIndex := 0;
        end;
      end;
    end else begin
      SpedComboBox.ItemIndex := FindComboboxRef (SpedComboBox, spedref);
      if (SpedComboBox.ItemIndex = -1) then SpedComboBox.ItemIndex := 0;

      SpedComboBoxChange (SpedComboBox);

      //Wenn noch kein Konto ausgew�hlt wurde
      if (LeerComboBox.ItemIndex <= 0) then begin
        if (leerref <> -1) then begin
          LeerComboBox.ItemIndex := FindComboboxRef (LeerComboBox, leerref);
          if (LeerComboBox.ItemIndex = -1) then LeerComboBox.ItemIndex := 0;
        end;
      end;
    end;


    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select LIEFERSCHEIN_NR from V_PCD_BESTELLUNG where REF_MAND=:ref_mand and REF_LIEFERANT=:ref_lief and BESTELL_NR=:best_nr');
    ADOQuery1.Parameters.ParamByName ('ref_lief').Value := GetComboBoxRef (LieferantComboBox);
    ADOQuery1.Parameters.ParamByName ('ref_mand').Value := GetComboBoxRef (MandantComboBox);
    ADOQuery1.Parameters.ParamByName ('best_nr').Value := BestNrComboBox.Text;

    try
      ADOQuery1.Open;

      if not (ADOQuery1.Fields [0].IsNull) then
        LSNrEdit.Text := ADOQuery1.Fields [0].AsString;

      ADOQuery1.Close;
    except
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.FormShow(Sender: TObject);
begin
  if (MandantComboBox.ItemIndex > 0) then
    MandantComboBoxChange (Sender);

  if not (SubMandPanel.Visible) then
    Height := Height - SubMandPanel.Height
  else if (SubMandComboBox.ItemIndex > 0) then
    SubMandComboBoxChange (Sender);

  if not (fMandantConfig.FlagLieferantenVerwaltung) then begin
    NeuerLieferant1.Visible := False;
    BestNrComboBox.Style := csDropDownList;
  end else begin
    NeuerLieferant1.Visible := LieferantComboBox.Enabled;

    BestNrComboBox.Style := csDropDown;
  end;

  fLastLSDate := Trunc (LSDateDateTimePicker.Date);

  Label18.Visible := PackageTypComboBox.Visible;

  NeuerLieferant1.Enabled := NeuerLieferant1.Visible;

  WEAbrechungCheckBox.Visible := LVSConfigModul.UseAbrechnung;

  UpdateDuty;

  if (MandantComboBox.Enabled) and (MandantComboBox.ItemIndex < 1) then
    MandantComboBox.SetFocus
  else if (LagerComboBox.Enabled) and (LagerComboBox.ItemIndex < 1) then
    LagerComboBox.SetFocus
  else if (LieferantComboBox.Enabled) and (LieferantComboBox.ItemIndex < 1) then
    LieferantComboBox.SetFocus
  else if (SpedComboBox.Enabled) and (SpedComboBox.ItemIndex < 1) then
    SpedComboBox.SetFocus
  else if (LSNrEdit.Enabled) and (Length (LSNrEdit.Text) = 0) then
    LSNrEdit.SetFocus
  else
    HintEdit.SetFocus;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.UpdateDuty;
begin
  PackTypDutyLabel.Visible := PackageTypComboBox.Visible and fPackTypReq;
  LSNrDutyLabel.Visible := fNoteNoReq;
  LiefDutyLabel.Visible := fLieferantReq;
  KFZDutyLabel.Visible := fKFZReq;
  DateDutyLabel.Visible := fDelDateReq;
  LSDateDutyLabel.Visible := fNoteDateReq;
  PackStDutyLabel.Visible := fPackStReq;
end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.SpedComboBoxChange(Sender: TObject);
begin
  SpedComboBox.Color := clWindow;

  //Wenn noch kein Konto ausgew�hlt wurde
  if (LeerComboBox.ItemIndex <= 0) then begin
    if (SpedComboBox.ItemIndex = 0) then
      LeerComboBox.ItemIndex := 0
    else begin
      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select * from V_SPEDITIONEN where REF=:ref');
      ADOQuery1.Parameters [0].Value := GetComboBoxRef (SpedComboBox);

      ADOQuery1.Open;

      if ADOQuery1.FieldByName ('REF_LEER_KONTO').IsNull then
        LeerComboBox.ItemIndex := 0
      else LeerComboBox.ItemIndex := FindComboboxRef (LeerComboBox, ADOQuery1.FieldByName ('REF_LEER_KONTO').AsInteger);

      ADOQuery1.Close;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 12.05.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.SubMandComboBoxChange(Sender: TObject);
var
  idx   : Integer;
  optch : Char;
  errormsg : String;
begin
  if (GetComboBoxRef (LagerComboBox) > 0) then begin
    if (GetComboBoxRef (SubMandComboBox) > 0) then begin
      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select * from V_MANDANT_CONFIG where REF_MAND=:ref_mand');
      ADOQuery1.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (SubMandComboBox);

      ADOQuery1.Open;

      fSubMandOptions := ADOQuery1.FieldByName ('CONFIG_OPT').AsString;

      ADOQuery1.Close;

      fPackTypReq     := IsOptionRequired(fMandOptions, cMandWECheckPackType);
      fNoteNoReq      := IsOptionRequired(fMandOptions, cMandWECheckLSNo);
      fLieferantReq   := IsOptionRequired(fMandOptions, cMandWECheckSupplier);
      fKFZReq         := IsOptionRequired(fMandOptions, cMandWECheckKFZNo);
      fPackStReq      := IsOptionRequired(fMandOptions, cMandWEAnzPackSt);

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select REF from V_LEERGUT_KONTO where STATUS in (''AKT'', ''ANG'') and (REF_LAGER is null or REF_LAGER=:ref_lager) and REF_LT_BESTAND_MAND=:ref_sub_mand order by REF_LAGER nulls last');
      ADOQuery1.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);
      ADOQuery1.Parameters.ParamByName('ref_sub_mand').Value := GetComboBoxRef (SubMandComboBox);

      ADOQuery1.Open;

      if not (ADOQuery1.Fields [0].IsNull) then begin
        idx := FindComboboxRef (LeerComboBox, ADOQuery1.Fields [0].AsInteger);

        if (idx > 0) then
          LeerComboBox.ItemIndex := idx;
      end;

      ADOQuery1.Close;

      SetDateMaxDays(GetComboBoxRef(SubMandComboBox), errormsg);
    end;
  end;

  UpdateDuty;

  UpdateLiefCombobox (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  rect   : TRect;
  canvas : TCanvas;
begin
  LeerPanel.Visible := false;

  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := False;

    if (MandantComboBox.ItemIndex = -1) Then begin
      if (MandantComboBox.Enabled) then begin
        MandantComboBox.Color := clRed;
        MandantComboBox.SetFocus;
      end;
    end else if (SubMandPanel.Visible and fSubMandRequired and (SubMandComboBox.ItemIndex = -1)) Then begin
      if (SubMandComboBox.Enabled) then begin
        SubMandComboBox.Color := clRed;
        SubMandComboBox.SetFocus;
      end;
    end else if (LagerComboBox.ItemIndex = -1) Then begin
      if (LagerComboBox.Enabled) then begin
        LagerComboBox.Color := clRed;
        LagerComboBox.invalidate;

        LagerComboBox.SetFocus;
      end;
    end else if LSDateDateTimePicker.Enabled and fNoteDateReq and (LSDateDateTimePicker.Date <= 1000) Then begin
      LeerPanel.Top := LSDateDateTimePicker.Top;
      LeerPanel.Left := LSDateDateTimePicker.Left;
      LeerPanel.Height := LSDateDateTimePicker.Height;
      LeerPanel.Width := LSDateDateTimePicker.Width - 32;

      LeerPanel.Visible := True;

      //LSDateDateTimePicker.Color := clRed;
      //LSDateDateTimePicker.BorderColor := clRed;

      LSDateDateTimePicker.SetFocus
    end else if DateDateTimePicker.Enabled and fDelDateReq and (DateDateTimePicker.Date <= 1000) Then begin
      LeerPanel.Top := DateDateTimePicker.Top;
      LeerPanel.Left := DateDateTimePicker.Left;
      LeerPanel.Height := DateDateTimePicker.Height;
      LeerPanel.Width := DateDateTimePicker.Width - 32;

      LeerPanel.Visible := True;

      DateDateTimePicker.Color := clRed;
      DateDateTimePicker.SetFocus
    end else if (PflichSpedition) and SpedComboBox.Enabled and (SpedComboBox.ItemIndex <= 0) Then begin
      SpedComboBox.Color := clRed;
      SpedComboBox.SetFocus
    end else if (PflichWELP) and WEComboBox.Enabled and not (GetComboboxLPRef (WEComboBox) > 0) Then begin
      WEComboBox.Color := clRed;
      WEComboBox.SetFocus
    end else if (PflichLeergut) and LeerComboBox.Enabled and (LeerComboBox.ItemIndex <= 0) Then begin
      LeerComboBox.Color := clRed;
      LeerComboBox.SetFocus
    end else if (Length (LSNrEdit.Text) = 0) and fNoteNoReq then begin
      LSNrEdit.Color := clRed;
      LSNrEdit.SetFocus
    end else if PackageTypComboBox.Visible and PackageTypComboBox.Enabled and (PackageTypComboBox.Items.Count > 1) and (PackageTypComboBox.ItemIndex <= 0) and fPackTypReq then begin
      PackageTypComboBox.Color := clRed;
      PackageTypComboBox.SetFocus
    end else if LieferantComboBox.Enabled and (LieferantComboBox.Items.Count > 1) and (LieferantComboBox.ItemIndex <= 0) and fLieferantReq then begin
      LieferantComboBox.Color := clRed;
      LieferantComboBox.SetFocus
    end else if (Length (PackageEdit.Text) = 0) and fPackStReq then begin
      PackageEdit.Color := clRed;
      PackageEdit.SetFocus
    end else if (Length (LKWEdit.Text) = 0) and fKFZReq then begin
      LKWEdit.Color := clRed;
      LKWEdit.SetFocus
    end else begin
      if (fSortLieferant = 0) then begin
        fLieferantName := LieferantComboBox.GetItemText (-1, 1);
        fLieferantNr   := LieferantComboBox.GetItemText (-1, 0);
      end else begin
        fLieferantName := LieferantComboBox.GetItemText (-1, 0);
        fLieferantNr   := LieferantComboBox.GetItemText (-1, 1);
      end;

      CanClose := True;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.FormCreate(Sender: TObject);
begin
  DateDateTimePicker.Date := Now;
  TimeDateTimePicker.Time := Frac (Now);
  //Anlieferdatum maximal ein Tage in der Zukunft
  DateDateTimePicker.MaxDate := Now + 1;

  //Lieferscheindatum maximal drei Tage in der Zukunft
  LSDateDateTimePicker.MaxDate := Now + 3;

  fSubMandRequired := False;

  fPackTypReq      := False;
  fKFZReq          := False;
  fLieferantReq    := False;

  FahrerEdit.Text := '';
  LKWEdit.Text := '';
  DocIDEdit.Text := '';
  HintEdit.Text := '';
  PackageEdit.Text := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, MandantComboBox);
    LVSSprachModul.SetNoTranslate (Self, SubMandComboBox);
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
    LVSSprachModul.SetNoTranslate (Self, LeerComboBox);
    LVSSprachModul.SetNoTranslate (Self, LieferantComboBox);
    LVSSprachModul.SetNoTranslate (Self, WEComboBox);
    LVSSprachModul.SetNoTranslate (Self, SpedComboBox);
    LVSSprachModul.SetNoTranslate (Self, BestNrComboBox);
    LVSSprachModul.SetNoTranslate (Self, LeerPanel);
    LVSSprachModul.SetNoTranslate (Self, PackageTypComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects(MandantComboBox);
  ClearComboBoxObjects(SubMandComboBox);
  ClearComboBoxObjects(LagerComboBox);
  ClearComboBoxObjects(LeerComboBox);
  ClearComboBoxObjects(WEComboBox);
  ClearComboBoxObjects(SpedComboBox);
  ClearComboBoxObjects(LieferantComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 12.05.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TWEAnlageForm.UpdateLiefCombobox (Sender: TObject) : Integer;
var
  idx,
  selidx,
  liefref : Integer;
begin
  liefref := GetComboBoxRef (LieferantComboBox);

  if (UserReg.ReadRegValue ('SortLieferant', fSortLieferant) <> 0) then
    fSortLieferant := 0;

  if (fSortLieferant = 0) then
    LieferantComboBox.ColWidth := 80
  else
    LieferantComboBox.ColWidth := 220;

  selidx := -1;

  ClearComboBoxObjects (LieferantComboBox);

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF, LIEFERANT_NR, LIEFERANT_NAME, IS_DEFAULT from V_WE_LIEFERANTEN where STATUS in (''AKT'', ''OLD'') and REF_LAGER=:ref_lager and REF_MAND=:ref_mand');
  ADOQuery1.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);
  ADOQuery1.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandantComboBox);

  if (GetComboBoxRef (SubMandComboBox) > 0) then begin
    ADOQuery1.SQL.Add ('and (REF_SUB_MAND is null or REF_SUB_MAND=:ref_sub_mand)');
    ADOQuery1.Parameters.ParamByName('ref_sub_mand').Value := GetComboBoxRef (SubMandComboBox);
  end;

  if (fSortLieferant = 0) then
    ADOQuery1.SQL.Add ('order by LPAD (LIEFERANT_NR, 9, ''0''),upper (LIEFERANT_NAME)')
  else
    ADOQuery1.SQL.Add ('order by upper (LIEFERANT_NAME),LPAD (LIEFERANT_NR, 9, ''0'')');

  try
    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      if (fSortLieferant = 0) then
        idx := LieferantComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger))
      else
        idx := LieferantComboBox.Items.AddObject (ADOQuery1.Fields [2].AsString+'|'+ADOQuery1.Fields [1].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

      if (ADOQuery1.Fields [3].AsString = '1') then
        selidx := idx;

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;
  except
  end;

  if (LieferantComboBox.Items.Count = 0) then begin
    //Wenn keine Lieferant geladen wurden
    LieferantComboBox.Enabled := False;
    LieferantComboBox.ItemIndex := -1
  end else begin
    LieferantComboBox.Enabled := True;

    //Null-Eintrag für 'kein Lieferant' hinzuf�gen
    LieferantComboBox.Items.Insert (0, '');

    if (liefref = -1) then begin
      if (selidx > 0) then
        LieferantComboBox.ItemIndex := selidx
      else
        LieferantComboBox.ItemIndex := 0;
    end else begin
      LieferantComboBox.ItemIndex := FindComboboxRef (LieferantComboBox, liefref);
      if (LieferantComboBox.ItemIndex = -1) then LieferantComboBox.ItemIndex := 0;
    end;

    if (LieferantComboBox.ItemIndex <> -1) Then
      LieferantComboBox.OnChange (LieferantComboBox);
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.LagerComboBoxChange(Sender: TObject);
var
  idx       : Integer;
  textstr   : String;
  sellpidx,
  sellbidx  : Integer;
  optch     : char;
  lbstr,
  lpstr     : String;
  flag      : Boolean;
  altcursor : TCursor;
begin
  LagerComboBox.Color := clWindow;

  altcursor := Screen.Cursor;
  Screen.Cursor :=crSQLWait;

  try
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select OPTIONS from V_LAGER where REF=:ref_lager');
    ADOQuery1.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);

    ADOQuery1.Open;

    optch := GetOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 14);

    if (optch = #0) or (optch = ' ') then begin
      fNoteDateReq     := false;

      DateTime_SetFormat(LSDateDateTimePicker.Handle, PChar('dd.MM.yyyy'));
      LSDateDateTimePicker.DateTime := Now;
    end else begin
      fNoteDateReq := (optch = '2') or (optch = '3');

      if (optch = '0') or (optch = '3') then begin
        DateTime_SetFormat(LSDateDateTimePicker.Handle, PChar('dd.MM.yyyy'));

        LSDateDateTimePicker.DateTime := Now;
      end else if (LSDateDateTimePicker.MinDate > 0) then begin
        LSDateDateTimePicker.Date   := LSDateDateTimePicker.MinDate + 1;
        LSDateDateTimePicker.Format := '';
      end else begin
        LSDateDateTimePicker.Date   := 1;
        LSDateDateTimePicker.Format := '';
      end;
    end;

    TimeDateTimePicker.Date := Now;

    optch := GetOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 15);

    if (optch = #0) or (optch = ' ') then begin
      fDelDateReq     := false;

      DateTime_SetFormat(TimeDateTimePicker.Handle, PChar('HH:mm'));
      DateTime_SetFormat(DateDateTimePicker.Handle, PChar('dd.MM.yyyy'));

      DateDateTimePicker.Date := Trunc (Now);
    end else begin
      fDelDateReq := (optch = '2') or (optch = '3');

      if (optch = '0') or (optch = '3') then begin
        DateTime_SetFormat(TimeDateTimePicker.Handle, PChar('HH:mm'));
        DateTime_SetFormat(DateDateTimePicker.Handle, PChar('dd.MM.yyyy'));

        DateDateTimePicker.Date := Trunc (Now);
      end else if (DateDateTimePicker.MinDate > 0) then begin
        DateDateTimePicker.Date   := DateDateTimePicker.MinDate + 1;
        DateDateTimePicker.Format := '';
      end else begin
        DateDateTimePicker.Date   := 1;
        DateDateTimePicker.Format := '';
      end;
    end;

    optch := GetOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 18);

    if (optch = #0) or (optch = ' ') then
      fNoteNoReq := true
    else
      fNoteNoReq := (optch = '0');

    ADOQuery1.Close;

    if (CheckConfigParameter (GetComboBoxRef (MandantComboBox), -1, GetComboBoxRef (LagerComboBox), 'WE_SPED_PFLICHT', flag, False) = 0) then
      PflichSpedition := flag
    else
      PflichSpedition := False;

    if (CheckConfigParameter (GetComboBoxRef (MandantComboBox), -1, GetComboBoxRef (LagerComboBox), 'WE_LEER_PFLICHT', flag, False) = 0) then
      PflichLeergut := flag
    else
      PflichLeergut := False;

    if (CheckConfigParameter (GetComboBoxRef (MandantComboBox), -1, GetComboBoxRef (LagerComboBox), 'WE_LP_PFLICHT', flag, False) = 0) then
      PflichWELP := flag
    else
      PflichWELP := False;

    ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'WE_BEREICH', lbstr);
    ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'WE_PLATZ',   lpstr);

    if (Length (lbstr) = 0) then
      lbstr := WEComboBox.GetItemText (-1, 0);

    sellpidx := -1;
    sellbidx := -1;

    ClearComboBoxObjects (WEComboBox);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select lb.REF,lb.NAME,lb.BESCHREIBUNG,lp.REF,nvl (lp.NAME,lp.LP_NR),lb.WE_OPT from V_LB lb left outer join V_LP lp on (lp.REF_LB=lb.REF and lp.STATUS<>''SPERR'')');
    ADOQuery1.SQL.Add ('where lb.LB_ART=''WE'' and lb.REF_LAGER=:ref_lager order by lb.WE_SPERR, lb.NAME, lp.NAME, lp.LP_NR');
    ADOQuery1.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);

    try
      ADOQuery1.Open;

      while not (ADOQuery1.Eof) do begin
        if not (ADOQuery1.Fields [0].IsNull) then begin
          //Nur wenn in diesem Bereich ohne LP vereinnahmt werden darf
          if (copy (ADOQuery1.Fields [5].AsString, 3, 1) = '0') then begin
            //Den WE-Bereich einmal auch ohne LP anzeigen
            if (FindComboboxRef (WEComboBox, ADOQuery1.Fields [0].AsInteger) = -1) then begin
                idx := WEComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString+'|', TComboBoxLBLPRef.Create(DBGetReferenz (ADOQuery1.Fields [0]), -1));

                if (sellbidx = -1) and (Length (lbstr) > 0) and (lbstr = ADOQuery1.Fields [1].AsString) then
                  sellbidx := idx;
            end;
          end;

          if not (ADOQuery1.Fields [3].IsNull) then begin
            idx := WEComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString+'|'+ADOQuery1.Fields [4].AsString, TComboBoxLBLPRef.Create(DBGetReferenz (ADOQuery1.Fields [0]), DBGetReferenz (ADOQuery1.Fields [3])));

            if (Length (lpstr) > 0) and (lpstr = ADOQuery1.Fields [4].AsString) then
              sellpidx := idx;
          end;
        end;

        ADOQuery1.Next;
      end;

      ADOQuery1.Close;
    except
    end;

    if (sellpidx > 0) then
      WEComboBox.ItemIndex := sellpidx
    else WEComboBox.ItemIndex := sellbidx;
    if (WEComboBox.ItemIndex = -1) then WEComboBox.ItemIndex := 0;

    UpdateLiefCombobox (Sender);

    ClearComboBoxObjects (SpedComboBox);

    SpedComboBox.Items.Add ('');

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select distinct REF, NAME, BESCHREIBUNG, LAGER from V_SPEDITIONEN where STATUS in (''ANG'',''AKT'') and REF_LOCATION=:ref_loc and (REF_MAND is null or REF_MAND=:ref_mand) and (REF_LAGER is null or REF_LAGER=:ref_lager)');
    ADOQuery1.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
    ADOQuery1.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);
    ADOQuery1.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandantComboBox);

    try
      ADOQuery1.Open;

      while not (ADOQuery1.Eof) do begin
        if (ADOQuery1.Fields [3].IsNull) then
          SpedComboBox.AddItem (ADOQuery1.Fields [1].AsString+'||'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger))
        else
          SpedComboBox.AddItem (ADOQuery1.Fields [1].AsString+'|('+ADOQuery1.Fields [3].AsString+')|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

        ADOQuery1.Next;
      end;

      ADOQuery1.Close;
    except
    end;
    SpedComboBox.ItemIndex := 0;

    ClearComboBoxObjects (LeerComboBox);

    LeerComboBox.Items.Insert(0, '');

    //Pr�fen ob es den Dummyeintrag für <pro Lieferant> gibt
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF from V_LEERGUT_KONTO where REF=0');

    try
      ADOQuery1.Open;

      if not (ADOQuery1.Fields [0].IsNull) then
        //Wenn ja, das das ausgew�hlt werden
        LeerComboBox.Items.InsertObject(1, '<pro Lieferant>', TComboBoxRef.Create(0));

      ADOQuery1.Close;
    except
    end;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF, KONTO_NAME, LAGER, OPT_SPIEGEL_KONTO from V_LEERGUT_KONTO where STATUS in (''AKT'', ''ANG'') and (REF_LOCATION is null or (REF_LOCATION=:ref_loc and (REF_LAGER is null or REF_LAGER=:ref_lager))) order by KONTO_NAME');
    ADOQuery1.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
    ADOQuery1.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);

    try
      ADOQuery1.Open;

      while not (ADOQuery1.Eof) do begin
        if (ADOQuery1.Fields [2].IsNull) then
          textstr := ADOQuery1.Fields [1].AsString
        else
          textstr := ADOQuery1.Fields [1].AsString+'|('+ADOQuery1.Fields [2].AsString+')';

        if (ADOQuery1.Fields [3].AsString = '1') then
          textstr := textstr + ' (S)';

        LeerComboBox.AddItem (textstr, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

        ADOQuery1.Next;
      end;

      ADOQuery1.Close;
    except
    end;

    LeerComboBox.ItemIndex := -1;

    if (GetComboBoxRef (SubMandComboBox) > 0) then begin
      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select REF from V_LEERGUT_KONTO where STATUS in (''AKT'', ''ANG'') and REF_LAGER=:ref_lager and REF_LT_BESTAND_MAND=:ref_sub_mand');
      ADOQuery1.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);
      ADOQuery1.Parameters.ParamByName('ref_sub_mand').Value := GetComboBoxRef (SubMandComboBox);

      ADOQuery1.Open;

      if not (ADOQuery1.Fields [0].IsNull) then begin
        idx := FindComboboxRef (LeerComboBox, ADOQuery1.Fields [0].AsInteger);

        if (idx > 0) then
          LeerComboBox.ItemIndex := idx;
      end;

      ADOQuery1.Close;
    end;
  except
  end;

  Screen.Cursor := altcursor;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.ComboBoxDropDown(Sender: TObject);
begin
  if (Sender is TComboBox) then
    (Sender as TComboBox).Color := clWindow
  else if (Sender is TComboBoxPro) then
    (Sender as TComboBoxPro).Color := clWindow;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.09.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.DateDateTimePickerChange(Sender: TObject);
begin
  DateDateTimePicker.Format := EmptyStr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 17.12.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.DateDateTimePickerCloseUp(Sender: TObject);
begin
  DateTime_SetFormat(TimeDateTimePicker.Handle, PChar('HH:mm'));
  DateTime_SetFormat((Sender as TDateTimePicker).Handle, PChar('dd.MM.yyyy'));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.LeerComboBoxChange(Sender: TObject);
begin
  LeerComboBox.Color := clWindow;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.NeuerLieferant1Click(Sender: TObject);
var
  idx      : Integer;
  editform : TEditLieferantForm;
begin
  editform := TEditLieferantForm.Create (Self);
  editform.Caption := GetResourceText (1056);

  editform.RefMand    := GetComboBoxRef (MandantComboBox);

  if (SubMandPanel.Visible) then
    editform.RefSubMand := GetComboBoxRef (SubMandComboBox);
    
  editform.RefLager   := GetComboBoxRef (LagerComboBox);

  if (editform.ShowModal = mrOk) then begin
    idx := LieferantComboBox.Items.AddObject (editform.NrEdit.Text+'|'+editform.NameEdit.Text, TComboBoxRef.Create (editform.Ref));

    LieferantComboBox.ItemIndex := idx;
    LieferantComboBoxChange (LieferantComboBox);
  end;

  editform.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.NewSpeditionMenuItemClick(Sender: TObject);
var
  res,
  ref,
  idx       : Integer;
  optstr,
  lblstr,
  dfuestr,
  chgoptstr : String;
  editform  : TEditSpeditionForm;
begin
  editform := TEditSpeditionForm.Create (Self);

  LoadMandantCombobox (editform.MandComboBox);
  if (Length (LVSDatenModul.AktMandant) = 0) Then begin
    editform.MandComboBox.Enabled := True;
    editform.MandComboBox.ItemIndex := -1
  end else begin
    editform.MandComboBox.Enabled := False;
    editform.MandComboBox.ItemIndex := editform.MandComboBox.IndexOf (LVSDatenModul.AktMandant);
  end;

  LoadLagerCombobox(editform.LagerComboBox, LVSDatenModul.AktLocation);
  editform.LagerComboBox.Items.Insert (0, '');

  if (GetComboBoxRef (LagerComboBox) = -1) then
    editform.LagerComboBox.ItemIndex := -1
  else begin
    editform.LagerComboBox.Enabled := False;
    editform.LagerComboBox.ItemIndex := FindComboboxRef (editform.LagerComboBox, GetComboBoxRef (LagerComboBox));
    editform.LagerComboBoxChange (editform.LagerComboBox);
  end;

  editform.Caption := GetResourceText (1059);
  editform.OkButton.Caption := GetResourceText (1060);

  editform.NummerEdit.Text := '';
  editform.NameEdit.Text := '';
  editform.DescEdit.Text := '';

  if (editform.ShowModal = mrOk) then begin
    chgoptstr := '1111';

    if (LVSDatenModul.DatabaseVersion < 37) then
      optstr := ''
    else begin
      if not (editform.EmpfLabelCheckBox.Checked) then
        optstr := '00'
      else if (editform.EmpfLabelLimitCheckBox.Checked) then
        optstr := '11'
      else
        optstr := '10';
    end;

    if (editform.DFUEComboBox.ItemIndex = 0) then
      dfuestr := ''
    else
      dfuestr := GetComboBoxDBItemWert (editform.DFUEComboBox);

    if (editform.LabelArtComboBox.ItemIndex = 0) then
      lblstr := ''
    else
      lblstr := GetComboBoxDBItemWert (editform.LabelArtComboBox);

    res := CreateSpedition (LVSDatenModul.AktLocationRef,
                            GetComboboxRef (editform.MandComboBox),
                            GetComboboxRef (editform.LagerComboBox),
                            editform.NummerEdit.Text,
                            editform.NameEdit.Text,
                            editform.KennungEdit.Text,
                            editform.DescEdit.Text,
                            editform.AvisMailEdit.Text,
                            GetComboboxRef (editform.VerlRelComboBox),
                            GetComboboxRef (editform.PackRelComboBox),
                            GetComboboxRef (editform.WELeerComboBox),
                            GetComboboxRef (editform.WALeerComboBox),
                            lblstr,
                            optstr,
                            chgoptstr,
                            ref);

    if (res = 0) then
      res := SetSpeditionAdresse (ref,
                                  '',
                                  editform.AdrNameEdit.Text,
                                  '',
                                  editform.AdrNameZusatzEdit.Text,
                                  editform.AdrStrasseEdit.Text,
                                  '',
                                  editform.AdrPLZEdit.Text,
                                  editform.AdrOrtEdit.Text,
                                  editform.AdrLandEdit.Text,
                                  editform.AdrFonEdit.Text,
                                  editform.AdrFaxEdit.Text,
                                  editform.AdrContactEdit.Text,
                                  editform.AdrMailEdit.Text);

    if (res = 0) then
      res := SetIFTMINDaten (ref,
                             editform.DFUEKennungEdit.Text,
                             editform.ILNEdit.Text,
                             editform.AccountNrEdit.Text,
                             editform.DepotEdit.Text,
                             dfuestr);

    if (res <> 0) then
      MessageDLG (FormatResourceText (1061, [])+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
    else begin
      idx := SpedComboBox.Items.AddObject (editform.NameEdit.Text+'|'+editform.DescEdit.Text, TComboBoxRef.Create (ref));

      SpedComboBox.ItemIndex := idx;
      SpedComboBoxChange (SpedComboBox);
    end;
  end;

  editform.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.MandantComboBoxChange(Sender: TObject);
var
  altcursor : TCursor;
  optch     : Char;
  errormsg  : String;
begin
  MandantComboBox.Color := clWindow;

  altcursor := Screen.Cursor;
  Screen.Cursor :=crSQLWait;

  try
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_MANDANT_CONFIG where REF_MAND=:ref_mand');
    ADOQuery1.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandantComboBox);

    ADOQuery1.Open;

    fMandOptions := ADOQuery1.FieldByName ('CONFIG_OPT').AsString;

    ADOQuery1.Close;

    fPackTypReq     := IsOptionRequired(fMandOptions, cMandWECheckPackType);
    fNoteNoReq      := IsOptionRequired(fMandOptions, cMandWECheckLSNo);
    fLieferantReq   := IsOptionRequired(fMandOptions, cMandWECheckSupplier);
    fKFZReq         := IsOptionRequired(fMandOptions, cMandWECheckKFZNo);
    fPackStReq      := IsOptionRequired(fMandOptions, cMandWEAnzPackSt);

    LVSConfigModul.ReadMandantConfig (MandantComboBox.GetItemText, fMandantConfig);

    CheckConfigParameter (GetComboBoxRef(MandantComboBox), LVSDatenModul.AktLocationRef, -1, 'WE_SUB_MANDANT_REQUIRED', fSubMandRequired, False);
    SetDateMaxDays(GetComboBoxRef(MandantComboBox), errormsg);

    if (SubMandPanel.Visible) then begin
      ClearComboBoxObjects (SubMandComboBox);

      LoadSubMandantCombobox (SubMandComboBox, LVSDatenModul.AktLocationRef, GetComboBoxRef(MandantComboBox));

      if (SubMandComboBox.Items.Count = 0) then
        SubMandComboBox.Enabled := False
      else begin
        SubMandComboBox.Enabled := True;

        SubMandComboBox.Items.Insert (0, MandantComboBox.GetItemText(-1, 0)+'|'+MandantComboBox.GetItemText(-1, 1));
        SubMandComboBox.ItemIndex := -1;
      end;
    end;

    ClearComboBoxObjects (LagerComboBox);
    LoadManagedLagerCombobox (LagerComboBox, LVSDatenModul.AktLocation, GetComboboxRef (MandantComboBox));

    if (Length (LVSDatenModul.AktLager) > 0) Then begin
      LagerComboBox.Enabled := False;

      LagerComboBox.ItemIndex := LagerComboBox.IndexOf (LVSDatenModul.AktLager);
    end else if (fRefLager <> -1) then begin
      LagerComboBox.Enabled := True;

      LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, fRefLager);
      if (LagerComboBox.ItemIndex = -1) then
        LagerComboBox.ItemIndex := 0;
    end else begin
      LagerComboBox.Enabled   := True;
      LagerComboBox.ItemIndex := -1;
    end;

    if (LagerComboBox.ItemIndex <> -1) then
      LagerComboBoxChange (LagerComboBox);
  except
  end;

  Screen.Cursor := altcursor;

  UpdateDuty;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.06.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEAnlageForm.ScannerErfassung (var Message: TMessage);
var
  doneflag : Boolean;
  barinfo  : TBarcodeInfos;
  errcode  : Integer;
  idstr,
  errmsg   : String;
  query    : TADOQuery;
begin
  errmsg := '';
  idstr  := '';

  DecodeKundenBarcode (ScanCode, doneflag, barinfo, errcode, errmsg);

  if (doneflag) then begin
    idstr := barinfo.DocID;
  end else begin
    if (Length(ScanCode) = 13) and (ScanCode[1] = ITFID) and (Copy (ScanCode, 2, 2) = '52') then begin
      //Doc-ID
      if (GetLELPCheckChar (copy (ScanCode, 2, 10)) = ScanCode [12]) then begin
        idstr := Copy (ScanCode, 2, 11);
      end else begin
        errmsg := 'Ungültiger Doc-ID Barcode '+Copy (ScanCode, 2, Length (ScanCode) - 2) +' gescannt (Prüfziffer)';
      end;
    end else if (ScanCode[1] = ITFID) then begin  //Bei ITFID muss die Barcode-Prüfziffer entfernt werden
      idstr := Copy (ScanCode, 2, Length (ScanCode) - 2);
    end else begin
      idstr := Copy (ScanCode, 2, Length (ScanCode) - 1);
    end;
  end;

  if (Length (idstr) > 0)  then begin
    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select * from V_WARENEINGANG where STATUS not in (''STO'', ''DEL'') and REF_MAND=:ref_mand and DOCID=:id order by ANLIEFER_DATUM desc');
      query.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandantComboBox);
      query.Parameters.ParamByName('id').Value := idstr;

      try
        query.Open;

        if not (query.FieldByName ('REF').IsNull) then
          errmsg := 'Die Doc-ID '+idstr+' wird bereits im Wareneingang '+query.FieldByName ('EINGANGS_NR').AsString+' vom '+query.FieldByName ('ANLIEFER_DATUM').AsString+' für die Lieferscheinnummer '+query.FieldByName ('LIEFER_NR').AsString + ' genutzt'
        else
          DocIDEdit.Text := idstr;
      except
        errmsg := 'Fehler beim Prüfen der Doc-ID';
      end;

      query.Close;
    finally
      query.Free;
    end;
  end;

  if (Length (errmsg) > 0) then
    MessageDLG (errmsg, mtError, [mbOk], 0);
end;

function TWEAnlageForm.IsOptionRequired(const Options: string; const Opt: Integer): Boolean;
var
  ch: Char;
begin
  ch := GetOpt(Options, Opt);
  Result := (ch <> #0) and (ch <> ' ') and (ch = '1');
end;


end.

