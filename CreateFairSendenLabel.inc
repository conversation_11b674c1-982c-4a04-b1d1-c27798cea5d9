﻿  //******************************************************************************
  //* Function Name: CreateFairSendenLabel
  //* Author       : <PERSON>
  //* Datum        : 26.06.2022
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateFairSendenLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    idx,
    res,
    keyidx,
    errcode  : Integer;
    errtext  : String;
    xmlstr   : String;
    urlparam : String;
    body     : String;
    resp     : String;
    hdr      : String;
    nve      : String;
    labelurl : String;
    nrstr    : String;
    orderid  : String;
    timestr  : String;
    keystr,
    streetstr,
    vorstr,
    nachstr  : String;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    js       : TlkJSONobject;
    fs,
    us,
    errfs    : TlkJSONbase;
    cfgquery : TSmartQuery;
    olddec   : Char;
    found    : boolean;
    urlstr,
    authurlstr,
    apiurlstr,
    prodstr,
    telstr,
    idstr,
    cfgstr,
    userid,
    usersec   : String;
  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      ErrorText := '';

      urlstr  := '';

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select cfg.REST_URL as SPED_REST_URL, g.* from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if cfgquery.FieldByName('SENDIT_PRODUKT').IsNull then
          ErrorText := 'No product defined'
        else if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else begin
          keystr  := cfgquery.FieldByName('API_KEY').AsString;
          prodstr := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;
          telstr  := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;
          cfgstr  := cfgquery.FieldByName('SENDIT_CONFIG').AsString;

          if Assigned (cfgquery.FindField ('REST_URL')) then
            urlstr  := cfgquery.FieldByName('REST_URL').AsString;

          if (Length (urlstr) = 0) then
            urlstr  := cfgquery.FieldByName('SPED_REST_URL').AsString;

          if (Length (urlstr) = 0) then
            ErrorText := 'Not REST url defined';
        end;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        //Die Zugangsdaten stehen im API_KEY
        strlist := TStringList.Create;
        try
          strlist.Delimiter := ';';
          strlist.StrictDelimiter := true;

          strlist.DelimitedText := keystr;

          if (strlist.Count < 2) then begin
            res := 24;
            ErrorText := 'Credential not complete';
          end else begin
            userid := strlist [0];     //ClientId
            usersec := strlist [1];     //Secret
          end;

          if (res = 0) then begin
            strlist.DelimitedText := urlstr;

            if (strlist.Count < 2) then begin
              res := 24;
              ErrorText := 'api urls not complete';
            end else begin
              authurlstr := strlist [0];     //ClientId
              apiurlstr := strlist [1];     //Secret
            end;
          end;
        finally
          strlist.Free;
        end;

        sdata := TMemoryStream.Create;

        try
          keyidx := 0;

          while (keyidx < 32) do begin
            if (fRESTToken [keyidx].AccessKey = keystr) then
              break
            else if (Length (fRESTToken [keyidx].AccessKey) = 0) then begin
              fRESTToken [keyidx].AccessKey := keystr;
              fRESTToken [keyidx].AccessToken := '';

              break;
            end;

            Inc (keyidx);
          end;

          ForceDirectories(DatenPath + 'soap\FairSenden\'+FormatDateTime ('yyyymmdd', Now));

          if (Length (fRESTToken [keyidx].AccessToken) = 0) or (MinutesBetween (fRESTToken [keyidx].CreateAt, Now) > 50) then begin
            //Neues Token erzeugen
            urlparam := '';

            body := 'grant_type=client_credentials';

            if SendRequest(authurlstr, // Host,
                            -1, //Port
                            'oauth2/token', // Service
                            'POST', //Methode
                            '', // Proxy,
                            userid, usersec, // User , PW
                            '', //Action
                            'application/x-www-form-urlencoded', //ContentType
                            ['Host: '+authurlstr],
                            body,         // RequestData
                            resp,
                            sdata, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
                          then
            begin
              sdata.Position := 0;
              sdata.SaveToFile(DatenPath + 'soap\FairSenden\'+FormatDateTime ('yyyymmdd', Now)+'\tocken_'+FormatDateTime ('hhnnss', Now)+'.json');

              sdata.Position := 0;
              js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;

              if not Assigned (js) then begin
                ErrorText := 'API error message (token)';
              end else begin
                try
                  fs := js.Field['access_token'];
                except
                  fs := nil;
                end;

                if not Assigned (fs) then
                  ErrorText := 'Fehler token'
                else begin
                  fRESTToken [keyidx].AccessToken := fs.Value;
                  fRESTToken [keyidx].CreateAt := Now;
                end;
              end;
            end else begin
              ErrorText := 'Fehler token';
            end;
          end;

          if (Length (fRESTToken [keyidx].AccessToken) > 0) then begin
            hdr := 'Authorization: Bearer '+fRESTToken [keyidx].AccessToken;

            nve := query.FieldByName('NVE_NR').AsString;

            if (query.FieldByName('COUNT_FREIGABE').AsInteger > 1) then
              orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('COUNT_FREIGABE').AsString
            else if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
              orderid := query.FieldByName('AUFTRAG_NR').AsString
            else
              orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;

            if Assigned (SendITLog) then begin
              SendITLog.Logging (clNormal, 'Versand: FairSenden, RefNVE: '+IntToStr (query.FieldByName('REF_NVE').AsInteger)+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
            end;

            if (cfgstr = 'LABEL') then begin
              if not query.FieldByName('AUF_REFERENZ').IsNull then
                idstr := query.FieldByName('AUF_REFERENZ').AsString
              else if not query.FieldByName('IFC_AUFTRAG_NR').IsNull then
                idstr := query.FieldByName('IFC_AUFTRAG_NR').AsString
              else
                idstr := query.FieldByName('AUFTRAG_NR').AsString;

              Barcode    := idstr;
              SendungsNr := idstr;
            end else begin
              //Token erzeugen
              urlparam := '';

              body := '{'+
                      '  "sender":{';

              //Absender Adresse übergeben
              cfgquery  := TSmartQuery.Create (Nil);

              try
                cfgquery.ReadOnly := True;
                cfgquery.Session := Query.Session;

                //Bei Dropship wird die Traderadresse angezeigt
                cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                                   +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                                   +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                                   +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                                   +'coalesce (translate (tadr.LAND using CHAR_CS),loc.LAND,''DE'') as LAND,'
                                   +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON'
                                  +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                                  +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                                  +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                                  +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                  +' where auf.REF=:ref_auf'
                                  );

                cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

                cfgquery.Open;

                if (cfgquery.FieldByName('REF').IsNull) then begin
                  cfgquery.Close;

                  cfgquery.SQL.Clear;
                  cfgquery.SQL.Add ( 'select coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as NAME1,ml.ABSENDER_ZUSATZ as NAMEZUSATZ,nvl (ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE'
                                    +',nvl (ml.ABSENDER_PLZ,loc.PLZ) as PLZ,nvl (ml.ABSENDER_ORT,loc.ORT) as ORT,nvl (loc.LAND,''DE'') as LAND,loc.TELEFON'
                                    +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                    +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                    +' where m.REF=:ref_mand'
                                    );

                  cfgquery.Params.ParamByName('ref_loc').Value := query.FieldByName('REF_LOCATION').AsInteger;

                  if query.FieldByName('REF_SUB_MAND').IsNull then
                    cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
                  else cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

                  cfgquery.Open;
                end;

                nrstr := '';
                streetstr := cfgquery.FieldByName ('STRASSE').AsString;
                idx := Length (streetstr);
                while (idx > 1) and (streetstr [idx] <> ' ') do
                  Dec (idx);

                if (idx > 1) then begin
                  nrstr := copy (streetstr, idx + 1);

                  while (idx > 1) and (streetstr [idx] = ' ') do
                    Dec (idx);

                  streetstr := copy (streetstr, 1, idx);
                end;

                body := body + '   "company": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('NAME1').AsString)+'",'+
                               '   "address": {'+
                               '    "street": "'+ConvertJSONSonderzeichen (streetstr+' '+nrstr)+'",'+
                               '    "city": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('ORT').AsString)+'",'+
                               '    "zip": "'+cfgquery.FieldByName ('PLZ').AsString+'",'+
                               '    "countrycode": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('LAND').AsString)+'"'+
                               '   },';

                body := body + '"first_name": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('NAME1').AsString)+'",'+
                               '"last_name": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('NAME1').AsString)+'"';

                cfgquery.Close;

              finally
                cfgquery.Free;
              end;

              body := body + '   },';

              body := body + '  "recipient":{';

              nrstr  := '';
              streetstr := liefquery.FieldByName ('STRASSE').AsString;

              idx := Length (streetstr);
              while (idx > 1) and (streetstr [idx] <> ' ') do
                Dec (idx);

              if (idx > 1) then begin
                nrstr := copy (streetstr, idx + 1);
                streetstr := Copy (streetstr, 1, idx);
              end;

              ExtractVorUndNachname (vorstr, nachstr);

              if (liefquery.FieldByName('COMPANY').IsNull) then begin
                body := body + '  "company": "",'
              end else begin
                body := body + '  "company": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('COMPANY').AsString, ';', ',', [rfReplaceAll]))+'",';
              end;

              if not (kepemail) or adrquery.FieldByName('EMAIL').IsNull then
                body := body + '  "email": "'+ConvertJSONSonderzeichen (StringReplace (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString, ';', ',', [rfReplaceAll]))+'",'
              else
                body := body + '  "email": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll]))+'",';

              body := body + '  "phone": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll]))+'",';

              body := body + '"address":{'+
                             '  "street": "'+ConvertJSONSonderzeichen (StringReplace (streetstr + ' ' + nrstr, ';', ',', [rfReplaceAll]))+'",';

              if not (liefquery.FieldByName('STRASSE_2').IsNull) then
                body := body + '  "additional_information": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]))+'",';

              body := body + '  "zip": "'+liefquery.FieldByName('PLZ').AsString+'",'+
                             '  "city": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('ORT').AsString, ';', ',', [rfReplaceAll]))+'",'+
                             '  "countrycode": "'+landstr+'"'+
                             '},';

              body := body + '"first_name": "'+vorstr+'",'+
                             '"last_name": "'+nachstr+'"'+
                             '},';

              body := body + '  "customerReferenceId": "'+orderid+'",'+
                             '  "deliveryType": "DELIVERY",'+
                             '  "totalWeight": "'+FormatFloat ('0.3', query.FieldByName('BRUTTO_GEWICHT').AsFloat)+'"';

              (*
              cfgquery  := TSmartQuery.Create (Nil);

              try
                cfgquery.ReadOnly := True;
                cfgquery.Session := Query.Session;

                cfgquery.SQL.Add ('select * from V_NVE_INHALT where REF_NVE=:ref');
                cfgquery.Params [0].Value := query.FieldByName('REF_MAIN_NVE').AsInteger;

                cfgquery.Open;

                body := body + '  ,"Parcel_items":[';

                while not (cfgquery.Eof) do begin
                  if (cfgquery.RecNo > 1) then body := body + ',';

                  body := body + '  {'+
                                 '    "description": "'+AnsiToUtf8 (ConvertJSONSonderzeichen (StringReplace (cfgquery.FieldByName('ARTIKEL_TEXT').AsString, ';', ',', [rfReplaceAll])))+'",'+
                                 '    "quantity": '+cfgquery.FieldByName('MENGE').AsString+','+
                                 '    "sku": "'+AnsiToUtf8 (ConvertJSONSonderzeichen (StringReplace (cfgquery.FieldByName('ARTIKEL_NR').AsString, ';', ',', [rfReplaceAll])))+'"'+
                                 '  }';

                  cfgquery.Next;
                end;

                body := body + ']';

                cfgquery.Close;
              finally
                cfgquery.Free;
              end;
              *)

              body := body + '}';

              StrToFile (DatenPath + 'soap\FairSenden\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_'+nve+'.json', body);

              sdata.Clear;
              if SendRequest(apiurlstr, // Host,
                              -1, //Port
                              'shipments', // Service
                              'POST', //Methode
                              '', // Proxy,
                              '', '', // User , PW
                              '', //Action
                              'application/json', //ContentType
                              [hdr], //AddHeader
                              body,         // RequestData
                              resp,
                              sdata, //ResponseStream
                              errcode, // Fehlercode
                              errtext) // Fehlertext
                            then
              begin
                StrToFile (DatenPath + 'soap\FairSenden\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_resp_'+nve+'.txt', resp);

                labelurl := '';

                sdata.Position := 0;
                sdata.SaveToFile(DatenPath + 'soap\FairSenden\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nve+'.json');

                sdata.Position := 0;
                js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;

                if not Assigned (js) then begin
                  res := 11;
                  ErrorText := 'API error message';
                end else begin
                  try
                    us := nil;

                    fs := js.Field['shipmentId'];
                    if not Assigned (fs) then begin
                      res := 15;
                      ErrorText := 'tracking_number code error';

                      fs := js.Field['error'];
                      if Assigned (fs) then begin
                        us := js.Field['message'];

                        if Assigned (us) then
                          ErrorText := ErrorText + ':' + StringReplace (us.Value, '\u0027', '''', [rfReplaceAll]);
                      end;
                    end else begin
                      idstr := fs.Value;

                      us := js.Field['trackUrl'];

                      if not Assigned (us) then begin
                        res := 16;
                        ErrorText := 'url error'
                      end else begin
                        TrackUrl := us.Value;
                      end;

                      Barcode    := idstr;
                      SendungsNr := idstr;

                      body := '"CUSTOMERDEPOT_READY"';

                      sdata.Clear;
                      if SendRequest(apiurlstr, // Host,
                                      -1, //Port
                                      'shipments/'+idstr+'/status', // Service
                                      'PUT', //Methode
                                      '', // Proxy,
                                      '', '', // User , PW
                                      '', //Action
                                      'application/json', //ContentType
                                      [hdr], //AddHeader
                                      body,         // RequestData
                                      resp,
                                      sdata, //ResponseStream
                                      errcode, // Fehlercode
                                      errtext) // Fehlertext
                      then begin
                        StrToFile (DatenPath + 'soap\FairSenden\'+FormatDateTime ('yyyymmdd', Now)+'\status_resp_'+nve+'.txt', resp);

                        sdata.Position := 0;
                        sdata.SaveToFile(DatenPath + 'soap\FairSenden\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_status_'+nve+'.json');
                      end else begin
                        res := 11;
                      end;
                    end;
                  except
                    res := 10;

                    ErrorText := 'Label data error';
                  end;
                end;
              end else begin
                res := 11;
              end;
            end;

            if (res = 0) then begin
              LabelImage.Clear;

              body := '';
              if SendRequest(apiurlstr, // Host,
                              -1, //Port
                              'shipments/'+idstr+'/label', // Service
                              'GET', //Methode
                              '', // Proxy,
                              '', '', // User , PW
                              '', //Action
                              'application/pdf', //ContentType
                              [hdr, 'accept: '+'application/pdf'], //AddHeader
                              body,         // RequestData
                              resp,
                              LabelImage, //ResponseStream
                              errcode, // Fehlercode
                              errtext) // Fehlertext
              then begin
                StrToFile (DatenPath + 'soap\FairSenden\'+FormatDateTime ('yyyymmdd', Now)+'\label_resp_'+nve+'.txt', resp);
                if (Pos ('404', resp) > 0) then begin
                  res := 26;
                  ErrorText := 'No label found';
                end else begin
                  if (Pos ('application/zpl', resp) > 0) then
                    LabelFormat := 'zpl'
                  else if (Pos ('application/pdf', resp) > 0)  then
                    LabelFormat := 'pdf'
                  else
                    LabelFormat := '###';

                  ForceDirectories(DatenPath + 'FairSenden');

                  LabelImage.Position := 0;
                  
                  try
                    LabelImage.SaveToFile(DatenPath + 'FairSenden\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                  except
                  end;

                  if Assigned (SendITLog) then begin
                    SendITLog.Logging (clNormal, 'Versand: FairSenden, RefNVE: '+IntToStr (query.FieldByName('REF_NVE').AsInteger)+', Versender: '+Versender+', SendungsNr: '+SendungsNr);
                  end;
                end;
              end else begin
                res := 25;

                ErrorText := 'Download error '+IntToStr (errcode)+' ('+errtext;
              end;
            end else begin
              res := 25;

              ErrorText := 'Status error '+IntToStr (errcode)+' ('+errtext;
            end;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: FairSenden, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;
