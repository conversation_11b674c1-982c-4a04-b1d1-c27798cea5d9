DECLARE
  column_exists NUMBER := 0;
BEGIN
  SELECT COUNT(*)
  INTO column_exists
  FROM user_tab_columns
  WHERE table_name = 'SYS_ACO'
    AND column_name = 'COMPONENT_PATH';

  IF column_exists = 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE SYS_ACO ADD COMPONENT_PATH VARCHAR2(1024)';

    EXECUTE IMMEDIATE 'COMMENT ON COLUMN SYS_ACO.COMPONENT_PATH IS ''Automatisch gesammelter Komponentenpfad durch Parent-Hierarchie''';

    DBMS_OUTPUT.PUT_LINE('COMPONENT_PATH Spalte erfolgreich hinzugefügt');
  ELSE
    DBMS_OUTPUT.PUT_LINE('COMPONENT_PATH Spalte existiert bereits');
  END IF;
END;
