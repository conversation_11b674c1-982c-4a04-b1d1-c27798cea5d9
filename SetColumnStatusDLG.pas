unit SetColumnStatusDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TColumnStatusForm = class(TForm)
    Panel1: TPanel;
    ScrollBox1: TScrollBox;
    OkButton: TButton;
    AbortButton: TButton;
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

end.
