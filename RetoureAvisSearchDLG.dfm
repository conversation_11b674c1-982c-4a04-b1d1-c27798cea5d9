object RetoureAvisSearchForm: TRetoureAvisSearchForm
  Left = 0
  Top = 0
  Caption = 'Retoure Avis suchen'
  ClientHeight = 538
  ClientWidth = 808
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  TextHeight = 13
  object Splitter1: TSplitter
    Left = 0
    Top = 310
    Width = 808
    Height = 3
    Cursor = crVSplit
    Align = alTop
    ExplicitTop = 316
    ExplicitWidth = 140
  end
  object KopfPanel: TPanel
    Left = 0
    Top = 0
    Width = 808
    Height = 137
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      808
      137)
    object Label1: TLabel
      Left = 8
      Top = 72
      Width = 51
      Height = 13
      Caption = 'Auftragnr.'
    end
    object Label182: TLabel
      Left = 9
      Top = 22
      Width = 18
      Height = 13
      Alignment = taRightJustify
      Caption = 'Von'
    end
    object Label185: TLabel
      Left = 146
      Top = 22
      Width = 13
      Height = 13
      Alignment = taRightJustify
      Caption = 'Bis'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 56
      Width = 791
      Height = 17
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label2: TLabel
      Left = 8
      Top = 99
      Width = 56
      Height = 13
      Caption = 'Kundename'
    end
    object Label3: TLabel
      Left = 273
      Top = 99
      Width = 17
      Height = 13
      Alignment = taRightJustify
      Caption = 'PLZ'
    end
    object Label4: TLabel
      Left = 240
      Top = 72
      Width = 50
      Height = 13
      Alignment = taRightJustify
      Caption = 'Kundennr.'
    end
    object Label5: TLabel
      Left = 452
      Top = 72
      Width = 62
      Height = 13
      Alignment = taRightJustify
      Caption = 'Auftrag-Ref.'
    end
    object Label6: TLabel
      Left = 455
      Top = 99
      Width = 59
      Height = 13
      Alignment = taRightJustify
      Caption = 'Tracking-Nr.'
    end
    object AufNrEdit: TEdit
      Left = 96
      Top = 69
      Width = 121
      Height = 21
      Hint = 'Die zu suchende original Auftragsnummer'
      MaxLength = 32
      TabOrder = 2
      Text = 'AufNrEdit'
      OnKeyPress = EditKeyPress
    end
    object RetoureAvisVonDateTimePicker: TDateTimePicker
      Left = 32
      Top = 18
      Width = 97
      Height = 21
      Date = 38189.000000000000000000
      Time = 0.712940011559112500
      ParentShowHint = False
      ShowHint = True
      TabOrder = 0
    end
    object RetoureAvisBisDateTimePicker: TDateTimePicker
      Left = 164
      Top = 18
      Width = 97
      Height = 21
      Date = 38189.000000000000000000
      Time = 0.713017673610011100
      ParentShowHint = False
      ShowHint = True
      TabOrder = 1
    end
    object KDNameEdit: TEdit
      Left = 96
      Top = 96
      Width = 121
      Height = 21
      Hint = 'Name des Kunden'
      MaxLength = 64
      TabOrder = 5
      Text = 'KDNameEdit'
      OnKeyPress = EditKeyPress
    end
    object RefreshButton: TButton
      Left = 656
      Top = 101
      Width = 139
      Height = 25
      Action = RefreshAction
      Anchors = [akTop, akRight]
      TabOrder = 8
    end
    object PLZEdit: TEdit
      Left = 296
      Top = 96
      Width = 121
      Height = 21
      Hint = 'Postleitzahl'
      MaxLength = 16
      TabOrder = 6
      Text = 'PLZEdit'
      OnKeyPress = EditKeyPress
    end
    object KdNrEdit: TEdit
      Left = 296
      Top = 69
      Width = 121
      Height = 21
      Hint = 'Kundennummer'
      MaxLength = 64
      TabOrder = 3
      Text = 'KdNrEdit'
      OnKeyPress = EditKeyPress
    end
    object AufRefEdit: TEdit
      Left = 520
      Top = 69
      Width = 121
      Height = 21
      Hint = 'Auftrags-Referenz'
      MaxLength = 64
      TabOrder = 4
      Text = 'AufRefEdit'
      OnKeyPress = EditKeyPress
    end
    object TrackingEdit: TEdit
      Left = 520
      Top = 96
      Width = 121
      Height = 21
      Hint = 'Postleitzahl'
      MaxLength = 16
      TabOrder = 7
      Text = 'TrackingEdit'
      OnKeyPress = EditKeyPress
    end
  end
  object RetoureAvisDBGrid: TDBGridPro
    Left = 0
    Top = 137
    Width = 808
    Height = 173
    Margins.Left = 8
    Margins.Right = 8
    Align = alTop
    Constraints.MinHeight = 100
    DataSource = RetoureAvisDataSource
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = 'MS Sans Serif'
    Font.Style = []
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ParentFont = False
    ReadOnly = True
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDrawColumnCell = RetoureAvisDBGridDrawColumnCell
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object FussPanel: TPanel
    Left = 0
    Top = 497
    Width = 808
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      808
      41)
    object AbortButton: TButton
      Left = 704
      Top = 8
      Width = 91
      Height = 25
      Anchors = [akTop, akRight, akBottom]
      Caption = 'Schlie'#223'en'
      ModalResult = 2
      TabOrder = 1
    end
    object CreateButton: TButton
      Left = 8
      Top = 8
      Width = 241
      Height = 25
      Anchors = [akLeft, akTop, akBottom]
      Caption = 'Retoure erfassen'
      TabOrder = 0
      OnClick = CreateButtonClick
    end
  end
  object FehlerPanel: TPanel
    Left = 0
    Top = 456
    Width = 808
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 4
    Visible = False
  end
  object RetoureAvisPosDBGrid: TDBGridPro
    Left = 0
    Top = 329
    Width = 808
    Height = 127
    Margins.Left = 8
    Margins.Right = 8
    Align = alClient
    Constraints.MinHeight = 100
    DataSource = RetoureAvisPosDataSource
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = 'MS Sans Serif'
    Font.Style = []
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ParentFont = False
    ReadOnly = True
    TabOrder = 3
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object SplitPanel: TPanel
    Left = 0
    Top = 313
    Width = 808
    Height = 16
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
  end
  object RetoureAvisDataSet: TOraQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 320
    Top = 16
  end
  object RetoureAvisDataSource: TDataSource
    DataSet = RetoureAvisDataSet
    OnDataChange = RetoureAvisDataSourceDataChange
    Left = 344
    Top = 16
  end
  object ActionList1: TActionList
    Left = 608
    Top = 16
    object RefreshAction: TAction
      Caption = 'Aktualisieren (F5)'
      ShortCut = 116
      OnExecute = RefreshActionExecute
    end
  end
  object RetoureAvisPosDataSet: TOraQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 432
    Top = 16
  end
  object RetoureAvisPosDataSource: TDataSource
    DataSet = RetoureAvisPosDataSet
    Left = 456
    Top = 16
  end
end
