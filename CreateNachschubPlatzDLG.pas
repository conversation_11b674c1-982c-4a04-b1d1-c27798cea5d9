unit CreateNachschubPlatzDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls;

type
  TCreateNachschubPlatzForm = class(TForm)
    Label1: TLabel;
    LBComboBox: TComboBoxPro;
    Label2: TLabel;
    LBZoneComboBox: TComboBoxPro;
    Label28: TLabel;
    LPArtComboBox: TComboBoxPro;
    Label3: TLabel;
    RegalEdit: TEdit;
    Label12: TLabel;
    NameEdit: TEdit;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel1: TBevel;
    Bevel2: TBevel;
    procedure LBComboBoxChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
    fLagerRef : Integer;

    function LoadLPArten (const RefLager : Integer; ComboBox : TComboBoxPro; const Default : String) : Integer;
  public
    property LagerRef : Integer read fLagerRef write fLagerRef;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DB, ADODB, DatenModul, FrontendUtils;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.10.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TCreateNachschubPlatzForm.LoadLPArten (const RefLager : Integer; ComboBox : TComboBoxPro; const Default : String) : Integer;
var
  idx   : Integer;
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select REF,TYP,BEZEICHNUNG from V_LAGER_LP_TYPEN where REF_LAGER=:ref_lager');
    query.Parameters [0].Value := RefLager;

    ComboBox.Clear;

    query.Open;

    while not (query.Eof) do begin
      idx := ComboBox.Items.AddObject (query.Fields [1].AsString + '|' + query.Fields [2].AsString, TComboBoxRef.Create(query.Fields [0].AsInteger));

      if (Length (Default) > 0) and (query.Fields [1].AsString = Default) then
        ComboBox.ItemIndex := idx;

      query.Next;
    end;
    query.Close;
  finally
    query.Free;
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.10.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TCreateNachschubPlatzForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := False;

    if (GetComboBoxRef(LBComboBox) = -1) then
      LBComboBox.SetFocus
    else if (GetComboBoxRef(LPArtComboBox) = -1) then
      LPArtComboBox.SetFocus
    else if (Length (RegalEdit.Text) = 0) then
      RegalEdit.SetFocus
    else
      CanClose := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.10.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TCreateNachschubPlatzForm.FormCreate(Sender: TObject);
begin
  RegalEdit.Text := '';
  NameEdit.Text := '';
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.10.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TCreateNachschubPlatzForm.FormShow(Sender: TObject);
begin
  LoadLPArten (fLagerRef, LPArtComboBox, 'NACH');
  if (LPArtComboBox.ItemIndex = -1) then LPArtComboBox.ItemIndex := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.10.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TCreateNachschubPlatzForm.LBComboBoxChange(Sender: TObject);
begin
  LoadLBZoneCombobox (LBZoneComboBox, GetComboBoxRef (LBComboBox));

  if (LBZoneComboBox.Items.Count = 0) then
    LBZoneComboBox.Enabled := False
  else begin
    LBZoneComboBox.Enabled := True;
    LBZoneComboBox.Items.Insert (0, '');
    LBZoneComboBox.ItemIndex := 0;
  end;
end;

end.
