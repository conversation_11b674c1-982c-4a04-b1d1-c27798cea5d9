unit WEBulkDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, ComCtrls, ComboBoxPro, FrontEndUtils, BarCodeScanner;

type
  TWEBulkForm = class(TForm)
    LTAnzEdit: TEdit;
    LTAnzUpDown: TUpDown;
    UmPackEdit: TEdit;
    VPEEdit: TEdit;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel2: TBevel;
    FehlerLabel: TPanel;
    BereichComboBox: TComboBoxPro;
    Label9: TLabel;
    LabelRadioGroup: TRadioGroup;
    Panel1: TPanel;
    Label6: TLabel;
    WENrLabel: TLabel;
    Label8: TLabel;
    BestNrLabel: TLabel;
    Label5: TLabel;
    ArtikelTextLabel: TLabel;
    ArtikelEinheitLabel: TLabel;
    Label14: TLabel;
    Label7: TLabel;
    SollLabel: TLabel;
    Label10: TLabel;
    ErfasstLabel: TLabel;
    Bevel1: TBevel;
    MHDPanel: TPanel;
    MHDLabel: TLabel;
    MHDEdit: TEdit;
    ChargePanel: TPanel;
    Label11: TLabel;
    ChargeEdit: TEdit;
    Bevel3: TBevel;
    LTComboBox: TComboBoxPro;
    PalHeightEdit: TEdit;
    Label12: TLabel;
    Label13: TLabel;
    ChargeDutyLabel: TLabel;
    MHDDutyLabel: TLabel;
    procedure FormCreate(Sender: TObject);
    procedure IntEditKeyPress(Sender: TObject; var Key: Char);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure EditChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure MHDEditExit(Sender: TObject);
    procedure CheckPalFaktor(Sender: TObject);
  private
    fRefWE         : Integer;
    fRefWELB       : Integer;
    fBestPosRef    : Integer;
    fRefAE         : Integer;
    fRefMand       : Integer;
    fRefSubMand    : Integer;
    fRefLager      : Integer;
    fMengeSoll     : Integer;
    fMengeErfasst  : Integer;
    fPalFaktor     : Integer;
    fMandant       : String;
    fLager         : String;
    fChargePflicht : Char;
    fArtikelInfo   : TArtikelInfo;

    procedure ScannerErfassung (var Message: TMessage); message WM_USER + SCANNER_DETECT;
  public
    property  MengeSoll    : Integer read fMengeSoll    write fMengeSoll;
    property  MengeErfasst : Integer read fMengeErfasst write fMengeErfasst;

    procedure Prepare (RefWE, BestPosRef, RefArEinheit : Integer);
  end;

implementation

uses
  DB, ADODB,
  VCLUtilitys, DatenModul, LVSDatenInterface, PrintModul, PrinterUtils, LablePrinterUtils,
  LVSSecurity, ConfigModul, Ora, OraSmart, SprachModul, ResourceText, EAN128Utils;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 03.07.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEBulkForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  LVSConfigModul.SaveFormParameter (Self, 'LabelRadioGroup', LabelRadioGroup.ItemIndex);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEBulkForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  leidx,
  umidx,
  umanz,
  gewicht,
  vpeanz,
  prtref,
  prtres      : Integer;
  lenr,
  errtxt,
  errmsg      : String;
  leref,
  palh,
  refpos      : Integer;
  prtdaten    : TPrinterPorts;
  leforminfo,
  besforminfo : TFormInfos;
  mhd         : TDateTime;
  charge      : String;
begin
  res := 0;

  mhd := 0;
  charge := '';
  palh := -1;

  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    CanClose := False;

    if not (LTAnzUpDown.Position > 0) then
      LTAnzEdit.SetFocus
    else if not (GetComboBoxRef (LTComboBox) > 0) then
      LTComboBox.SetFocus
    else if (Length (UmPackEdit.Text) = 0) then
      UmPackEdit.SetFocus
    else if not (TryStrToInt (UmPackEdit.Text, umanz)) then
      UmPackEdit.SetFocus
    else if (Length (VPEEdit.Text) = 0) then
      VPEEdit.SetFocus
    else if not (TryStrToInt (VPEEdit.Text, vpeanz)) then
      VPEEdit.SetFocus
    else if PalHeightEdit.Visible and PalHeightEdit.Enabled and (Length (PalHeightEdit.Text) > 0) and not (TryStrToInt (PalHeightEdit.Text, palh)) then
      PalHeightEdit.SetFocus
    else if MHDPanel.Visible and ((Length (MHDEdit.Text) = 0) or not (TryStrToDate (MHDEdit.Text, mhd))) then
      MHDEdit.SetFocus
    else if ChargePanel.Visible and (fArtikelInfo.ChargeArt = 'P') and (Length (ChargeEdit.Text) = 0) then
      ChargeEdit.SetFocus
    else begin
      if (fMengeSoll > 0) and (fMengeErfasst + (LTAnzUpDown.Position * umanz * vpeanz) > fMengeSoll) then begin
        if not (LVSSecurityModule.CheckChangeRecht (fMandant, fLager, '', 'WEOverDelivery')) then begin
          errmsg := FormatMessageText (1375, [])
        end else begin
          if (MessageDlg (FormatMessageText (1054, []), mtConfirmation, [mbYes,mbNo], 0) <> mrYes) then
            errmsg := FormatMessageText (1375, []);
        end;
      end;

      if (Length (errmsg) > 0) then begin
        FehlerLabel.Visible := True;
        FehlerLabel.Caption := errmsg;
      end else begin
        if (fArtikelInfo.NettoGewicht > 0) then
          gewicht := round (fArtikelInfo.NettoGewicht * vpeanz)
        else
          gewicht := -1;

        if ChargePanel.Visible then
          charge := ChargeEdit.Text;

        if (LabelRadioGroup.ItemIndex <> 0) then begin
          res := PrintModule.DetectPrinter ('WEPAL-LABEL', fRefLager, prtref, prtdaten);
          if (res <> 0) or (prtref <= 0) then
            errtxt := FormatMessageText (1430, [])
          else begin
            res := DetectFormular (fRefMand, fRefLager, '', '', prtdaten.Model, 'LE-LABEL', leforminfo);

            if (res <> 0) then
              errtxt := FormatMessageText (1431, [prtdaten.Model+'/'+'LE-LABEL'])
            else if (LabelRadioGroup.ItemIndex = 2) or (LabelRadioGroup.ItemIndex = 3) then begin
              res := DetectFormular (fRefMand, fRefLager, '', '', prtdaten.Model, 'BES-LABEL', besforminfo);

              if (res <> 0) then
                errtxt := FormatMessageText (1432, [prtdaten.Model+'/'+'BES-LABEL']);
            end;
          end;
        end;

        if (res <> 0) Then
          MessageDlg (errtxt, mtError, [mbOK], 0)
        else begin
          res := 0;
          leidx := 0;

          while (leidx < LTAnzUpDown.Position) and (res = 0) do begin
            lenr := '';

            res := CreateLE (fRefMand, fRefLager, lenr, '', GetComboBoxRef(LTComboBox), leref);

            if (res <> 0) then
              res := MessageDlg (FormatMessageText (1376, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOk], 0)
            else begin
              if (prtref > 0) and ((LabelRadioGroup.ItemIndex = 1) or (LabelRadioGroup.ItemIndex = 3)) then begin
                prtres := PrintLELabel (prtdaten, leforminfo, leref, errtxt);

                if (prtres <> 0) then begin
                  prtref := 0;

                  MessageDlg (FormatMessageText (1378, [errtxt, prtdaten.Name]), mtError, [mbOK], 0);
                end;
              end;

              if (res = 0) then begin
                umidx := 0;

                while (umidx < umanz) and (res = 0) do begin
                  if (fBestPosRef > 0) then
                    res := BestellPosEinheitAnnahme (fRefWE,
                      fBestPosRef,
                      fRefAE,
                      leref,
                      -1,
                      GetComboBoxRef (BereichComboBox),
                      '',
                      vpeanz,
                      gewicht,
                      0,
                      mhd,
                      charge,
                      '',
                      '',
                      '',
                      false,
                      -1,
                      '',
                      '',
                      '',
                      refpos)
                  else
                    res := WEPosEinheitAnnahme (fRefWE,
                      fRefAE,
                      leref,
                      -1,
                      GetComboBoxRef (BereichComboBox),
                      '',
                      vpeanz,
                      gewicht,
                      0,
                      mhd,
                      charge,
                      '',
                      '',
                      '',
                      false,
                      -1,
                      '',
                      '',
                      '',
                      refpos);

                  if (res <> 0) then
                    res := MessageDlg (FormatMessageText (1429, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOk], 0)
                  else begin
                    if (PalHeightEdit.Visible and (palh > 0)) then
                      res := SetWarenannahmePosPalHeight (refpos, palh);
                  end;

                  Inc (umidx);
                end;

                if (res = 0) then begin
                  if (prtref > 0) then begin
                    if (LabelRadioGroup.ItemIndex = 2) or (LabelRadioGroup.ItemIndex = 3) then begin
                      prtres := PrintWEPalettenLabels (prtdaten, besforminfo, fRefWE, refpos, True, errtxt);

                      if (prtres <> 0) then
                        MessageDlg (FormatMessageText (1422, [errtxt, prtdaten.Name]), mtError, [mbOK], 0);
                    end;
                  end;
                end;

                if (GetComboBoxLPRef (BereichComboBox) > 0) then begin
                  res := LEAufLP (fRefLager, leref, GetComboBoxLPRef (BereichComboBox), 'Bulk WE');

                  if (res <> 0) then
                    MessageDlg (FormatMessageText (1277, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
                end;
              end;
            end;

            Inc (leidx);
          end;
        end;

        CanClose := (res = 0);
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEBulkForm.FormCreate(Sender: TObject);
begin
  fMengeSoll    := -1;
  fMengeErfasst := 0;

  MHDEdit.Text    := '';
  ChargeEdit.Text := '';
  VPEEdit.Text    := '';
  UmPackEdit.Text := '';

  FehlerLabel.Visible := False;

  fArtikelInfo := TArtikelInfo.Create;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LTComboBox);
    LVSSprachModul.SetNoTranslate (Self, BereichComboBox);
    LVSSprachModul.SetNoTranslate (Self, WENrLabel);
    LVSSprachModul.SetNoTranslate (Self, ArtikelTextLabel);
    LVSSprachModul.SetNoTranslate (Self, ArtikelEinheitLabel);
    LVSSprachModul.SetNoTranslate (Self, BestNrLabel);
    LVSSprachModul.SetNoTranslate (Self, SollLabel);
    LVSSprachModul.SetNoTranslate (Self, ErfasstLabel);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEBulkForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (LTComboBox);
  ClearComboBoxObjects (BereichComboBox);

  fArtikelInfo.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEBulkForm.FormShow(Sender: TObject);
var
  intwert : Integer;
begin
  if not (MHDPanel.Visible) then
    Height := Height - MHDPanel.Height;

  if not (ChargePanel.Visible) then
    Height := Height - ChargePanel.Height
  else
    ChargeDutyLabel.Visible := (fArtikelInfo.ChargeArt = 'P');

  if (LVSConfigModul.ReadFormParameter (Self, 'LabelRadioGroup', intwert, LabelRadioGroup.ItemIndex) = 0) then
    LabelRadioGroup.ItemIndex := intwert;

  if (fMengeSoll <= 0) then
    SollLabel.Caption := ''
  else SollLabel.Caption := IntToStr (fMengeSoll);

  if (fMengeErfasst < 0) then
    ErfasstLabel.Caption := ''
  else ErfasstLabel.Caption := IntToStr (fMengeErfasst);

  Label9.Visible := BereichComboBox.Visible;

  Label12.Visible := PalHeightEdit.Visible;
  Label13.Visible := PalHeightEdit.Visible;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEBulkForm.IntEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not CharInSet (Key, [#8,^C,^V,'0'..'9']) then
    Key := #0
  else if ((Sender as TEdit).Color <> clWindow) then
    (Sender as TEdit).Color := clWindow;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEBulkForm.MHDEditExit(Sender: TObject);
var
  dt     : TDateTime;
  errtxt : String;
begin
  if not (AbortButton.Focused) then begin
    if (Length (MHDEdit.Text) > 0) then begin
      if (CheckMHD (MHDEdit.Text, fArtikelInfo.RestlaufzeitWE, FormatMessageText (1433, []), GetResourceText (1095), dt, errtxt)) then
        MHDEdit.Text := DateToStr (dt)
      else begin
        FehlerLabel.Visible := True;
        FehlerLabel.Caption := errtxt;

        MHDEdit.SetFocus;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEBulkForm.Prepare (RefWE, BestPosRef, RefArEinheit : Integer);
var
  idx     : Integer;
  lt_type : String;
  query   : TSmartQuery;
begin
  fRefWE      := RefWE;
  fRefAE      := RefArEinheit;
  fBestPosRef := BestPosRef;

  GetArtikelInfos (-1, fRefAE, fArtikelInfo);

  MHDPanel.Visible    := (fArtikelInfo.MHDArt <> 'O');
  ChargePanel.Visible := (fArtikelInfo.ChargeArt <> 'N');

  query := TSmartQuery.Create (Self);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Add ('select'
                  +'   ae.*,ar.ARTIKEL_NR,ar.ARTIKEL_TEXT,we.REF_LAGER,we.REF_MAND,we.REF_SUB_MAND,we.EINGANGS_NR,b.BESTELL_NR,bp.BEST_POS_NR,m.NAME as MANDANT,l.NAME as LAGER,we.REF_WE,lt.LT_ID'
                  +'   ,bp.MHD,bp.CHARGE'
                  +' from'
                  +'   V_ARTIKEL_EINHEIT ae'
                  +'   inner join V_ARTIKEL_EINHEIT_INFO aei on (aei.REF_AR_EINHEIT=ae.REF)'
                  +'   inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'
                  +'   inner join V_WARENEINGANG we on (we.REF=:ref_we)'
                  +'   inner join V_MANDANT m on (m.REF=we.REF_MAND)'
                  +'   inner join V_LAGER l on (l.REF=we.REF_LAGER)'
                  +'   left outer join V_BESTELL_POS bp on (bp.REF=:ref_best)'
                  +'   left outer join V_BESTELLUNG b on (b.REF=bp.REF_BEST_KOPF)'
                  +'   left outer join V_LT_TYPEN lt on (lt.REF=aei.REF_DEFAULT_LT)'
                  +' where ae.REF=:ref_ae'
                  );

    query.Params.ParamByName ('ref_ae').Value := fRefAE;
    query.Params.ParamByName ('ref_we').Value := fRefWE;
    query.Params.ParamByName ('ref_best').Value := fBestPosRef;

    query.Open;

    fLager     := query.FieldByName ('LAGER').AsString;
    fMandant   := query.FieldByName ('MANDANT').AsString;

    fRefLager  := query.FieldByName ('REF_LAGER').AsInteger;
    fRefMand   := query.FieldByName ('REF_MAND').AsInteger;
    fRefSubMand:= DBGetReferenz (query.FieldByName ('REF_SUB_MAND'));

    fPalFaktor := DBGetReferenz (query.FieldByName ('PAL_FAKTOR'));
    fRefWELB   := DBGetReferenz (query.FieldByName ('REF_WE'));

    WENrLabel.Caption := query.FieldByName ('EINGANGS_NR').AsString;

    if not Assigned (query.FindField ('PAL_HEIGHT')) Then
      PalHeightEdit.Visible := False
    else if (query.FieldByName ('PAL_HEIGHT').IsNUll) then
      PalHeightEdit.Text := ''
    else
      PalHeightEdit.Text := query.FieldByName ('PAL_HEIGHT').AsString;

    if (fPalFaktor > 0) then begin
      UmPackEdit.Text := '1';
      VPEEdit.Text    := IntToStr (fPalFaktor);
    end else begin
      UmPackEdit.Text := '1';
      VPEEdit.Text    := '';
    end;

    if (query.FieldByName ('BESTELL_NR').IsNull) then
      BestNrLabel.Caption := ''
    else begin
      BestNrLabel.Caption := query.FieldByName ('BESTELL_NR').AsString;

      if not (query.FieldByName ('BEST_POS_NR').IsNull) then
        BestNrLabel.Caption :=  BestNrLabel.Caption +' / ' + query.FieldByName ('BEST_POS_NR').AsString;
    end;

    if query.FieldByName ('MHD').IsNull then
      MHDEdit.Text := ''
    else MHDEdit.Text := query.FieldByName ('MHD').AsString;

    ChargeEdit.Text := query.FieldByName ('CHARGE').AsString;

    ArtikelTextLabel.Caption := query.FieldByName ('ARTIKEL_NR').AsString + ' - ' + query.FieldByName ('ARTIKEL_TEXT').AsString;
    ArtikelEinheitLabel.Caption := query.FieldByName ('EINHEIT').AsString + ' / ' +query.FieldByName ('EAN').AsString;

    LoadLTCombobox (LTCombobox, 'LE', LVSDatenModul.AktLocationRef, fRefLager);

    if not (query.FieldByName ('LT_ID').IsNull) then begin
      lt_type := query.FieldByName ('LT_ID').AsString;

      idx := 0;

      while (idx < LTCombobox.Items.Count) do begin
        if (Assigned (LTCombobox.Items.Objects [idx]) and (TComboBoxLTData (LTCombobox.Items.Objects [idx]).LTID = lt_type)) then
          break;

        Inc (idx);
      end;

      if (idx < LTCombobox.Items.Count) then
        LTCombobox.ItemIndex := idx;
    end;

    query.Close;

    if (fRefWELB <= 0) then
      BereichComboBox.Visible := False
    else begin
      query.SQL.Clear;
      query.SQL.Add ('select rel.REF_LB_REL,rel.REF_LP_REL,lb.NAME,lp.LP_NR,lp.NAME from V_LAGER_LB_RELATION rel left outer join V_LB lb on (lb.REF=rel.REF_LB_REL) left outer join V_LP lp on (lp.REF=rel.REF_LP_REL) where rel.REF_LB=:ref_lb and REL_ART is not null');
      query.Params [0].Value := fRefWELB;

      query.Open;

      while not (query.Eof) do begin
        if not (query.Fields [0].IsNull) then
          BereichComboBox.AddItem (query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxLBLPRef.Create (query.Fields [0].AsInteger, query.Fields [1].AsInteger));

        query.Next;
      end;

      query.Close;

      if (BereichComboBox.Items.Count = 0) then
        BereichComboBox.Visible := False
      else if (BereichComboBox.Items.Count = 1) then begin
        BereichComboBox.ItemIndex := 0;
        BereichComboBox.Enabled := False;
      end;
    end;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEBulkForm.CheckPalFaktor(Sender: TObject);
var
  anz,
  kaanz,
  vpeanz : Integer;
begin
  if (Length ((Sender as TEdit).Text) > 0) then begin
    if not (TryStrToInt ((Sender as TEdit).Text, anz)) then begin
      (Sender as TEdit).Color := clRed;
      (Sender as TEdit).SetFocus;
    end else if ((fPalFaktor > 0) and (Length (UmPackEdit.Text) > 0) and (Length (VPEEdit.Text) > 0)) then begin
      if (TryStrToInt (UmPackEdit.Text, kaanz) and TryStrToInt (VPEEdit.Text, vpeanz) and (kaanz > 0) and (vpeanz > 0)) then begin
        if ((kaanz * vpeanz) <> fPalFaktor) then begin
          if (MessageDLG (FormatMessageText (1428, [IntToStr (kaanz * vpeanz), IntToStr (fPalFaktor)]), mtConfirmation, [mbYes, mbNo], 0) <> mrYes) then begin
            (Sender as TEdit).SetFocus;
          end;
        end;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEBulkForm.EditChange(Sender: TObject);
begin
  FehlerLabel.Visible := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 22.01.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWEBulkForm.ScannerErfassung (var Message: TMessage);
var
  res        : Integer;
  errmsg     : String;
  barcode    : TEANBarcode;
begin
  if (Length (ScanCode) > 3) and (ScanCode [1] = EAN128ID) then begin
    res := DecodeEANBarcode (Copy (ScanCode, 2, Length (ScanCode) - 1), barcode, errmsg);

    if (res <> 0) then begin
      if (Length (errmsg) = 0) then
        errmsg := FormatMessageText (1400, [])
    end else begin
      if (Length (barcode.Charge) > 0) then
        ChargeEdit.Text := barcode.Charge;
    end;
  end else if (ChargeEdit.Focused and ChargeEdit.Enabled) then
    ChargeEdit.Text := Copy (ScanCode, 2)
end;

end.
