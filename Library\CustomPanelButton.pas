unit CustomPanelButton;

interface

uses
   SysUtils, Classes, Controls, ExtCtrls, Messages, ActnList;

type TButtonType =(btNone, btButton, btRadio);

type
   TCustomPanelButton = class(TPanel)
   private
     { Private declarations }
     FClicked: Boolean;
     FButtonType: TButtonType;

     procedure SetClicked(const Value: Boolean);
     procedure SetButtonType(const Value: TButtonType);
   protected
     { Protected declarations }
     procedure Click(); override;
     procedure MouseUp(Button: TMouseButton; Shift: TShiftState;
       X, Y: Integer); override;
     procedure MouseDown(Button: TMouseButton; Shift: TShiftState;
       X, Y: Integer); override;
   public
     { Public declarations }
     constructor Create(AOwner: TComponent); override;
     destructor Destroy(); override;
   published
     { Published declarations }
     property Clicked: Boolean read FClicked write SetClicked;
     property ButtonType: TButtonType read FButtonType write SetButtonType;
   end;

procedure Register;

implementation

procedure Register;
begin
   RegisterComponents('cs', [TCustomPanelButton]);
end;

{ TCustomPanelButton }

constructor TCustomPanelButton.Create(AOwner: TComponent);
begin
   inherited Create(AOwner);
   FButtonType := btNone;
   FClicked := False;

   self.Width := 75;
   self.Height := 25;
   self.Caption := 'Button';
end;

destructor TCustomPanelButton.Destroy;
begin
   inherited;
end;

procedure TCustomPanelButton.Click;
begin
   if FButtonType = btRadio then
   begin
     FClicked := not (FClicked);

     if FClicked then
       BevelOuter := bvLowered
     else
       BevelOuter := bvRaised;
   end;

  inherited;
end;

procedure TCustomPanelButton.MouseDown(Button: TMouseButton;
   Shift: TShiftState; X, Y: Integer);
begin
   inherited;
   if FButtonType = btButton then
     BevelOuter := bvLowered;
end;

procedure TCustomPanelButton.MouseUp(Button: TMouseButton;
   Shift: TShiftState; X, Y: Integer);
begin
   inherited;
   if FButtonType = btButton then
     BevelOuter := bvRaised;
end;

procedure TCustomPanelButton.SetButtonType(const Value: TButtonType);
begin
   FButtonType := Value;
   if FButtonType = btButton then
     BevelOuter := bvRaised
   else if (FButtonType = btRadio) then begin
     if FClicked then
       BevelOuter := bvLowered
     else
       BevelOuter := bvRaised;
   end;
end;

procedure TCustomPanelButton.SetClicked(const Value: Boolean);
begin
   FClicked := Value;
   if FClicked and (FButtonType = btRadio) then
     BevelOuter := bvLowered
   else
     BevelOuter := bvRaised;
end;



end.