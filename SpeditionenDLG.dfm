object SpeditionenForm: TSpeditionenForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Speditionen und Ausliefertouren'
  ClientHeight = 370
  ClientWidth = 729
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    729
    370)
  TextHeight = 13
  object CloseButton: TButton
    Left = 647
    Top = 337
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 0
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 8
    Width = 714
    Height = 321
    ActivePage = Speditionen
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 1
    object Speditionen: TTabSheet
      Caption = 'Speditionen'
      ImageIndex = 1
      OnShow = SpeditionenShow
      DesignSize = (
        706
        293)
      object NewSpedButton: TButton
        Left = 425
        Top = 257
        Width = 85
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Neu...'
        TabOrder = 0
        OnClick = NewSpedButtonClick
      end
      object DelSpedButton: TButton
        Left = 615
        Top = 257
        Width = 85
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'L'#246'schen...'
        TabOrder = 1
        OnClick = DelSpedButtonClick
      end
      object EditSpedButton: TButton
        Left = 520
        Top = 257
        Width = 85
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Bearbeiten...'
        TabOrder = 2
        OnClick = EditSpedButtonClick
      end
      object ShowDelSpedCheckBox: TCheckBox
        Left = 8
        Top = 261
        Width = 201
        Height = 17
        Anchors = [akLeft, akBottom]
        Caption = 'Gel'#246'schte anzeigen'
        TabOrder = 3
        OnClick = ShowDelSpedCheckBoxClick
      end
      object SpedDBGrid: TDBGridPro
        Left = 8
        Top = 8
        Width = 692
        Height = 243
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = SpedDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
        PopupMenu = SpedDBGridPopupMenu
        ReadOnly = True
        TabOrder = 4
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        OnDrawColumnCell = DBGridDrawColumnCell
        OnDblClick = SpedDBGridDblClick
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
        RegistryKey = 'Software\CS'
        RegistrySection = 'DBGrids'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
    end
    object TabSheet1: TTabSheet
      Caption = 'Ausliefertouren'
      OnShow = TabSheet1Show
      DesignSize = (
        706
        293)
      object TourDBGrid: TDBGridPro
        Left = 8
        Top = 8
        Width = 692
        Height = 243
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = TourDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        OnDrawColumnCell = DBGridDrawColumnCell
        OnDblClick = TourDBGridDblClick
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
        RegistryKey = 'Software\CS'
        RegistrySection = 'DBGrids'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object NewTourButton: TButton
        Left = 425
        Top = 257
        Width = 85
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Neu...'
        TabOrder = 1
        OnClick = EditTourButtonClick
      end
      object DelTourButton: TButton
        Left = 615
        Top = 257
        Width = 85
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'L'#246'schen...'
        TabOrder = 2
        OnClick = DelTourButtonClick
      end
      object EditTourButton: TButton
        Left = 520
        Top = 257
        Width = 85
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Bearbeiten...'
        TabOrder = 3
        OnClick = EditTourButtonClick
      end
      object ShowDelTourCheckBox: TCheckBox
        Left = 8
        Top = 261
        Width = 201
        Height = 17
        Anchors = [akLeft, akBottom]
        Caption = 'Gel'#246'schte anzeigen'
        TabOrder = 4
        OnClick = ShowDelTourCheckBoxClick
      end
    end
  end
  object SpedADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 560
    Top = 48
  end
  object SpedDataSource: TDataSource
    DataSet = SpedADOQuery
    OnDataChange = SpedDataSourceDataChange
    Left = 600
    Top = 48
  end
  object TourADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 560
    Top = 96
  end
  object TourDataSource: TDataSource
    DataSet = TourADOQuery
    OnDataChange = TourDataSourceDataChange
    Left = 600
    Top = 96
  end
  object SpedDBGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = SpedDBGridPopupMenuPopup
    Left = 152
    Top = 96
    object EditVersandConfigMenuItem: TMenuItem
      Caption = 'Versand Konfigurationen'
      OnClick = EditVersandConfigMenuItemClick
    end
    object EditSpedGatewayMenuItem: TMenuItem
      Caption = 'Gateway Konfigurationen...'
      OnClick = EditSpedGatewayMenuItemClick
    end
    object N6: TMenuItem
      Caption = '-'
    end
    object RoutingTabelleimportieren1: TMenuItem
      Caption = 'Routing-Tabelle importieren...'
      OnClick = RoutingTabelleimportieren1Click
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object SpedImportSendungMenuItem: TMenuItem
      Caption = 'Sendungsdaten importieren...'
      OnClick = SpedImportSendungMenuItemClick
    end
    object N5: TMenuItem
      Caption = '-'
    end
    object SpedExportSendungMenuItem: TMenuItem
      Caption = 'Aktuelle Sendungsdaten exportieren...'
      OnClick = SpedExportSendungMenuItemClick
    end
    object N4: TMenuItem
      Caption = '-'
    end
    object SpedForcastMenuItem: TMenuItem
      Caption = 'Speditions Voranmeldung erzeugen...'
      OnClick = SpedForcastMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object SpedDFUEClose: TMenuItem
      Caption = 'Tagesabschluss senden...'
      OnClick = SpedDFUECloseClick
    end
    object N3: TMenuItem
      Caption = '-'
    end
    object SpedPrintNVEListeMenuItem: TMenuItem
      Caption = 'Versandliste drucken...'
      OnClick = SpedPrintNVEListeMenuItemClick
    end
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 648
    Top = 48
  end
end
