unit LVSPrinterInterface;

interface

function SetPrinterOnline  (const PrtRef : Integer) : integer;
function SetPrinterOffline (const PrtRef : Integer) : integer;

function DeletePrintJob    (const JobRef : Integer) : integer;
function NachdruckPrintJob (const JobRef : Integer) : integer;

function SetPrinterRelation (const LocRef, LagerRef : Integer; const StationName, RelName : String; const PrtRef : Integer; const PrtPort : String) : integer;

function InsertPrinter (const RefLoc, RefLager : Integer; const PrtName, PrtDesc, PrtPort, PrtTyp, PrtModel, PrtFormat : String; var RefPrt : Integer) : Integer;
function UpdatePrinter (const RefPrt, RefLoc, RefLager : Integer; const PrtName, PrtDesc, PrtPort, PrtTyp, PrtModel, PrtFormat : String) : Integer;
function DeletePrinter (const RefPrt : Integer) : Integer;

function PrinterSetNr         (const RefPrt : Integer; const PrtNr : String) : Integer;
function PrinterSetLeitstand  (const RefPrt : Integer; const PrtLeitstand : String) : Integer;

implementation

uses DB, ADODB, Variants, DatenModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetPrinterOnline (const PrtRef : Integer) : integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PRINTER.PRINTER_ONLINE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pPrtRef',ftInteger,pdInput, 12, PrtRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetPrinterOffline (const PrtRef : Integer) : integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PRINTER.PRINTER_OFFLINE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pPrtRef',ftInteger,pdInput, 12, PrtRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function NachdruckPrintJob (const JobRef : Integer) : integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PRINTER.JOB_NACHDRUCKEN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pJobRef',ftInteger,pdInput, 12, JobRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetPrinterRelation (const LocRef, LagerRef : Integer; const StationName, RelName  : String; const PrtRef : Integer; const PrtPort : String) : integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PRINTER.SET_PRINTER_RELATION';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    if (LocRef =-1) then
      Parameters.CreateParameter('pRefLoc',ftInteger,pdInput, 12, null)
    else Parameters.CreateParameter('pRefLoc',ftInteger,pdInput, 12, LocRef);

    if (LagerRef =-1) then
      Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, null)
    else Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, LagerRef);

    Parameters.CreateParameter('pName',ftString,pdInput, 32, RelName);
    Parameters.CreateParameter('pStation',ftString,pdInput, 32, StationName);

    if (PrtRef =-1) then
      Parameters.CreateParameter('pRefPrt',ftInteger,pdInput, 12, null)
    else Parameters.CreateParameter('pRefPrt',ftInteger,pdInput, 12, PrtRef);

    Parameters.CreateParameter('pPrtPort',ftString,pdInput, 256, PrtPort);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeletePrintJob (const JobRef : Integer) : integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PRINTER.DELETE_JOB';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pJobRef',ftInteger,pdInput, 12, JobRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

function InsertPrinter (const RefLoc, RefLager : Integer; const PrtName, PrtDesc, PrtPort, PrtTyp, PrtModel, PrtFormat : String; var RefPrt : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  RefPrt := -1;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PRINTER.INSERT_PRINTER';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefLoc',ftInteger,pdInput, 12, GetPLSQLParameter (RefLoc));
    Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, GetPLSQLParameter (RefLager));

    Parameters.CreateParameter('pName',ftString,pdInput, 32, copy (PrtName, 1, 32));
    Parameters.CreateParameter('pDesc',ftString,pdInput, 64, copy (PrtDesc, 1, 64));
    Parameters.CreateParameter('pPrtPort',ftString,pdInput, 256, copy (PrtPort, 1, 256));
    Parameters.CreateParameter('pPrtTyp',ftString,pdInput, 16, copy (PrtTyp, 1, 16));
    Parameters.CreateParameter('pPrtModel',ftString,pdInput, 32, copy (PrtModel, 1, 32));
    Parameters.CreateParameter('pPrtFormat',ftString,pdInput, 8, copy (PrtFormat, 1, 8));

    Parameters.CreateParameter('oRefPrt',ftString,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if not (StoredProcedure.Parameters.ParamValues ['oRefPrt'] = NULL) then
      RefPrt := StoredProcedure.Parameters.ParamValues ['oRefPrt'];
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

function UpdatePrinter (const RefPrt, RefLoc, RefLager : Integer; const PrtName, PrtDesc, PrtPort, PrtTyp, PrtModel, PrtFormat : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PRINTER.UPDATE_PRINTER';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefPrt',ftInteger,pdInput, 12, RefPrt);

    Parameters.CreateParameter('pRefLoc',ftInteger,pdInput, 12, GetPLSQLParameter (RefLoc));
    Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, GetPLSQLParameter (RefLager));

    Parameters.CreateParameter('pName',ftString,pdInput, 32, copy (PrtName, 1, 32));
    Parameters.CreateParameter('pDesc',ftString,pdInput, 64, copy (PrtDesc, 1, 64));
    Parameters.CreateParameter('pPrtPort',ftString,pdInput, 256, copy (PrtPort, 1, 256));
    Parameters.CreateParameter('pPrtTyp',ftString,pdInput, 16, copy (PrtTyp, 1, 16));
    Parameters.CreateParameter('pPrtModel',ftString,pdInput, 32, copy (PrtModel, 1, 32));
    Parameters.CreateParameter('pPrtFormat',ftString,pdInput, 8, copy (PrtFormat, 1, 8));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

function DeletePrinter (const RefPrt : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PRINTER.DELETE_PRINTER';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefPrt',ftInteger,pdInput, 12, RefPrt);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

function PrinterSetNr (const RefPrt : Integer; const PrtNr : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PRINTER.SET_PRINTER_NR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefPrt',ftInteger,pdInput, 12, RefPrt);
    Parameters.CreateParameter('pPrtNr',ftString,pdInput, 16, Copy (PrtNr, 1, 16));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

function PrinterSetLeitstand (const RefPrt : Integer; const PrtLeitstand : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_PRINTER.SET_PRINTER_LEITSTAND';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefPrt',ftInteger,pdInput, 12, RefPrt);
    Parameters.CreateParameter('pPrtLeitstand',ftString,pdInput, 32, Copy (PrtLeitstand, 1, 32));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

end.
