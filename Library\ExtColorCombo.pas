unit ExtColorCombo;

interface

uses
  Windows, Messages, SysUtils, Classes, Graphics, Controls, Forms, Dialogs,
  StdCtrls;

const
  MaxColors = 16; { 0..15 }

type

  TColors = array[0..(MaxColors-1)] of TColor;
  TComboColors = class(TPersistent)
  private
    FColor: TColors;
    FUserColor: TColor;
  published
    property Color0: TColor read FColor[0] write FColor[0];
    property Color1: TColor read FColor[1] write FColor[1];
    property Color2: TColor read FColor[2] write FColor[2];
    property Color3: TColor read FColor[3] write FColor[3];
    property Color4: TColor read FColor[4] write FColor[4];
    property Color5: TColor read FColor[5] write FColor[5];
    property Color6: TColor read FColor[6] write FColor[6];
    property Color7: TColor read FColor[7] write FColor[7];
    property Color8: TColor read FColor[8] write FColor[8];
    property Color9: TColor read FColor[9] write FColor[9];
    property Color10: TColor read FColor[10] write FColor[10];
    property Color11: TColor read FColor[11] write FColor[11];
    property Color12: TColor read FColor[12] write FColor[12];
    property Color13: TColor read FColor[13] write FColor[13];
    property Color14: TColor read FColor[14] write FColor[14];
    property Color15: TColor read FColor[15] write FColor[15];
    property UserColor: TColor read FUserColor write FUserColor;
  end;

  TExtColorCombo = class(TComboBox)
  private
    FComboColors: TComboColors;
    FColorWidth, FTextSpace: Integer;
    FSelColor: TColor;
    FOnUserColorClick: TNotifyEvent;
    { Private-Deklarationen }
  protected
    procedure CreateWnd; override;
    procedure DrawItem(Index: Integer; Rect: TRect; State: TOwnerDrawState); override;
    procedure Click; override;
    function GetColorFromString(s: String): TColor;
    function GetTextFromString(s: String): String;
    procedure SetSelColor(const NewColor: TColor);
    { Protected-Deklarationen }
  public
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;

    procedure   Loaded; override;

    { Public-Deklarationen }
  published
    property ComboColors: TComboColors read FComboColors write FComboColors;
    property ColorWidth: Integer read FColorWidth write FColorWidth;
    property TextSpace: Integer read FTextSpace write FTextSpace;
    property SelColor: TColor read FSelColor write SetSelColor;
    property OnUserColorClick: TNotifyEvent read FOnUserColorClick write FOnUserColorClick;
    { Published-Deklarationen }
  end;

procedure Register;

implementation

uses FarbenUnit;

{procedure TExtColorCombo.Change;
begin
  inherited Change;
  FSelColor := GetColorFromString(Text);//Items.Strings[ItemIndex]);
end;}

procedure TExtColorCombo.Click;
begin
  inherited Click;
  if ItemIndex = MaxColors then begin
    FSelColor := ComboColors.FUserColor;
    if Assigned(FOnUserColorClick) then
     FOnUserColorClick(Self);
  end else FSelColor := GetColorFromString(Text);
end;

procedure TExtColorCombo.Loaded;
var
  i,
  idx,
  selidx : Integer;
begin
  inherited Loaded;

  selidx := 16;

  Items.Clear;

  with Items do begin
    for i:=0 to 15 do begin
      idx := Add('#'+IntToStr (i)+'|'+ColorToString (FComboColors.fColor[i]));
      if (fSelColor = FComboColors.fColor[i]) then
        selidx := idx;
    end;

    Add('#16|Benutzer...');
  end;

  ItemIndex := selidx;
end;

procedure TExtColorCombo.CreateWnd;
begin
  inherited CreateWnd;
end;

function TExtColorCombo.GetColorFromString(s: String): TColor;
var c: TColor;
    p, nr: Integer;
begin
  c := clBlack;
  p := Pos('|',s);
  if p>1 then begin
    s := Copy(s, 1, p-1);
    if s[1]='#' then begin
      nr := StrToInt(Copy(s,2,Length(s)));
      if nr in [0..MaxColors-1] then
       c := ComboColors.FColor[nr]
      else if nr = MaxColors then
       c := ComboColors.FUserColor;
    end else if s[1]='$' then begin
      c := StrToInt(s);
    end else if s[1]='!' then begin
      c := RGBColor(StrToInt(Copy(s,2,3)),StrToInt(Copy(s,6,3)),StrToInt(Copy(s,10,3)));
    end;
  end;
  Result := c;
end;

function TExtColorCombo.GetTextFromString(s: String): String;
var p: Integer;
begin
  p := Pos('|',s);
  if p>0 then begin
    Result := Copy(s,p+1,Length(s));
  end else Result := s;
end;

procedure TExtColorCombo.SetSelColor(const NewColor: TColor);
var i: Integer;
begin
  with ComboColors do begin
    if NewColor = Color0 then i := 0
    else if NewColor = Color1 then i := 1
    else if NewColor = Color2 then i := 2
    else if NewColor = Color3 then i := 3
    else if NewColor = Color4 then i := 4
    else if NewColor = Color5 then i := 5
    else if NewColor = Color6 then i := 6
    else if NewColor = Color7 then i := 7
    else if NewColor = Color8 then i := 8
    else if NewColor = Color9 then i := 9
    else if NewColor = Color10 then i := 10
    else if NewColor = Color11 then i := 11
    else if NewColor = Color12 then i := 12
    else if NewColor = Color13 then i := 13
    else if NewColor = Color14 then i := 14
    else if NewColor = Color15 then i := 15
    else begin
      i := 16;
      FUserColor := NewColor;
      Repaint;
    end;
  end;
  FSelColor := NewColor;
  ItemIndex := i;
end;

procedure TExtColorCombo.DrawItem(Index: Integer; Rect: TRect; State: TOwnerDrawState);
var c: TColor;
    r: TRect;
    s: String;
begin
  c := GetColorFromString(Items.Strings[Index]);
  Canvas.Brush.Color := c;
  r.Left := Rect.Left; r.Top := Rect.Top; r.Bottom := Rect.Bottom;
  r.Right := Rect.Left+FColorWidth;
  Canvas.FillRect(r);
  Canvas.Pen.Color := clBlack;
  Canvas.MoveTo(r.Right,Rect.Top);
  Canvas.LineTo(r.Right,Rect.Bottom);
  s := GetTextFromString(Items.Strings[Index]);
  Rect.Left := r.Right+1;
  Canvas.Brush.Color := Color;
  Canvas.Font.Color := Font.Color;
  Canvas.TextRect(Rect, Rect.Left+FTextSpace, Rect.Top+((Rect.Bottom-Rect.Top-Canvas.TextHeight(s)) div 2), s);
  if Assigned(OnDrawItem) then OnDrawItem(Self, Index, Rect, State);
end;

constructor TExtColorCombo.Create(AOwner: TComponent);
begin
  inherited Create(AOwner);
  FColorWidth := 24;
  FTextSpace := 3;
  FComboColors := TComboColors.Create;
  with FComboColors do begin
    FColor[0] := clBlack;
    FColor[1] := clMaroon;
    FColor[2] := clGreen;
    FColor[3] := clOlive;
    FColor[4] := clNavy;
    FColor[5] := clPurple;
    FColor[6] := clTeal;
    FColor[7] := clGray;
    FColor[8] := clSilver;
    FColor[9] := clRed;
    FColor[10] := clLime;
    FColor[11] := clYellow;
    FColor[12] := clBlue;
    FColor[13] := clFuchsia;
    FColor[14] := clAqua;
    FColor[15] := clWhite;
    FUserColor := clBlack;
  end;
  Style := csOwnerDrawFixed;
  Width := 110;
end;

destructor TExtColorCombo.Destroy;
begin
  if Assigned (FComboColors) Then
    FComboColors.Free;
    
  inherited Destroy;
end;

procedure Register;
begin
  RegisterComponents('c+s', [TExtColorCombo]);
end;

end.
