object EditSpeditionForm: TEditSpeditionForm
  Left = 0
  Top = 0
  Anchors = [akLeft, akTop, akRight]
  BorderStyle = bsDialog
  Caption = 'Speditionsdaten '#228'ndern'
  ClientHeight = 741
  ClientWidth = 419
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    419
    741)
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 50
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Label2: TLabel
    Left = 8
    Top = 142
    Width = 75
    Height = 13
    Caption = 'Speditionsname'
  end
  object Label3: TLabel
    Left = 8
    Top = 184
    Width = 64
    Height = 13
    Caption = 'Beschreibung'
  end
  object Label5: TLabel
    Left = 8
    Top = 100
    Width = 87
    Height = 13
    Caption = 'Speditionsnummer'
  end
  object Label6: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Bevel4: TBevel
    Left = 8
    Top = 95
    Width = 403
    Height = 11
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 376
  end
  object Label22: TLabel
    Left = 288
    Top = 142
    Width = 60
    Height = 13
    Caption = 'Kennzeichen'
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 66
    Width = 401
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 1
    OnChange = LagerComboBoxChange
    ExplicitWidth = 374
  end
  object NameEdit: TEdit
    Left = 8
    Top = 158
    Width = 257
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 32
    TabOrder = 3
    Text = 'NameEdit'
  end
  object DescEdit: TEdit
    Left = 8
    Top = 200
    Width = 401
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 64
    TabOrder = 4
    Text = 'DescEdit'
    ExplicitWidth = 374
  end
  object OkButton: TButton
    Left = 251
    Top = 708
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 7
    ExplicitLeft = 224
  end
  object AbortButton: TButton
    Left = 334
    Top = 708
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 8
    ExplicitLeft = 307
  end
  object NummerEdit: TEdit
    Left = 8
    Top = 116
    Width = 401
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 16
    TabOrder = 2
    Text = 'NummerEdit'
    ExplicitWidth = 374
  end
  object MandComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 401
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 0
    OnChange = MandComboBoxChange
    ExplicitWidth = 374
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 229
    Width = 403
    Height = 469
    ActivePage = TabSheet3
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 6
    ExplicitWidth = 376
    object TabSheet3: TTabSheet
      Caption = 'Adresse'
      ImageIndex = 2
      DesignSize = (
        395
        441)
      object Label9: TLabel
        Left = 8
        Top = 130
        Width = 69
        Height = 13
        Caption = 'Namenszusatz'
      end
      object Label10: TLabel
        Left = 8
        Top = 172
        Width = 36
        Height = 13
        Caption = 'Strasse'
      end
      object Label41: TLabel
        Left = 8
        Top = 214
        Width = 17
        Height = 13
        Caption = 'PLZ'
      end
      object Label12: TLabel
        Left = 79
        Top = 214
        Width = 16
        Height = 13
        Caption = 'Ort'
      end
      object Label11: TLabel
        Left = 254
        Top = 214
        Width = 23
        Height = 13
        Caption = 'Land'
      end
      object Label13: TLabel
        Left = 8
        Top = 88
        Width = 27
        Height = 13
        Caption = 'Name'
      end
      object Label14: TLabel
        Left = 8
        Top = 264
        Width = 36
        Height = 13
        Caption = 'Telefon'
      end
      object Label15: TLabel
        Left = 113
        Top = 264
        Width = 18
        Height = 13
        Caption = 'Fax'
      end
      object Label17: TLabel
        Left = 216
        Top = 264
        Width = 81
        Height = 13
        Caption = 'Ansprechpartner'
      end
      object Bevel3: TBevel
        Left = 6
        Top = 258
        Width = 383
        Height = 9
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
        ExplicitWidth = 356
      end
      object Label18: TLabel
        Left = 8
        Top = 306
        Width = 24
        Height = 13
        Caption = 'Email'
      end
      object Bevel1: TBevel
        Left = 6
        Top = 82
        Width = 383
        Height = 9
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
        ExplicitWidth = 356
      end
      object Label31: TLabel
        Left = 8
        Top = 34
        Width = 112
        Height = 13
        Caption = 'Standard Anlieferdepot'
      end
      object AdrNameZusatzEdit: TEdit
        Left = 8
        Top = 146
        Width = 379
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 3
        Text = 'AdrNameZusatzEdit'
        ExplicitWidth = 352
      end
      object AdrStrasseEdit: TEdit
        Left = 8
        Top = 188
        Width = 379
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 4
        Text = 'AdrStrasseEdit'
        ExplicitWidth = 352
      end
      object AdrPLZEdit: TEdit
        Left = 8
        Top = 230
        Width = 59
        Height = 21
        MaxLength = 8
        TabOrder = 5
        Text = 'AdrPLZEdit'
      end
      object AdrOrtEdit: TEdit
        Left = 79
        Top = 230
        Width = 164
        Height = 21
        MaxLength = 128
        TabOrder = 6
        Text = 'AdrOrtEdit'
      end
      object AdrNameEdit: TEdit
        Left = 8
        Top = 104
        Width = 379
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 2
        Text = 'AdrNameEdit'
        ExplicitWidth = 352
      end
      object AdrFonEdit: TEdit
        Left = 8
        Top = 280
        Width = 90
        Height = 21
        MaxLength = 32
        TabOrder = 8
        Text = 'AdrFonEdit'
      end
      object AdrMailEdit: TEdit
        Left = 8
        Top = 322
        Width = 379
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 11
        Text = 'AdrMailEdit'
        ExplicitWidth = 352
      end
      object AdrFaxEdit: TEdit
        Left = 113
        Top = 280
        Width = 90
        Height = 21
        MaxLength = 32
        TabOrder = 9
        Text = 'AdrFaxEdit'
      end
      object AdrContactEdit: TEdit
        Left = 216
        Top = 280
        Width = 171
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 10
        Text = 'AdrContactEdit'
        ExplicitWidth = 144
      end
      object AdrLandEdit: TEdit
        Left = 281
        Top = 230
        Width = 106
        Height = 21
        Anchors = [akTop, akRight]
        MaxLength = 16
        TabOrder = 7
        Text = 'AdrLandEdit'
        ExplicitLeft = 254
      end
      object DepotComboBox: TComboBoxPro
        Left = 8
        Top = 50
        Width = 382
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 1
        ExplicitWidth = 355
      end
      object DepotPflichtCheckBox: TCheckBox
        Left = 8
        Top = 11
        Width = 357
        Height = 17
        Caption = 'Das Anlieferdepot muss bei Verladungen angegeben werden'
        TabOrder = 0
      end
    end
    object ConfigTabSheet: TTabSheet
      Caption = 'Konfiguration'
      ImageIndex = 4
      DesignSize = (
        395
        441)
      object Label32: TLabel
        Left = 8
        Top = 13
        Width = 53
        Height = 13
        Caption = 'Versandart'
      end
      object VersandArtEdit: TEdit
        Left = 8
        Top = 32
        Width = 374
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 0
        Text = 'VersandArtEdit'
      end
    end
    object WATabSheet: TTabSheet
      Caption = 'Warenausgang'
      ImageIndex = 3
      DesignSize = (
        395
        441)
      object Label20: TLabel
        Left = 8
        Top = 188
        Width = 72
        Height = 13
        Caption = 'Verladerelation'
      end
      object Label23: TLabel
        Left = 8
        Top = 8
        Width = 57
        Height = 13
        Caption = 'Etikettenart'
      end
      object Label26: TLabel
        Left = 170
        Top = 285
        Width = 111
        Height = 13
        Caption = 'Standardladungstr'#228'ger'
      end
      object Label27: TLabel
        Left = 170
        Top = 328
        Width = 83
        Height = 13
        Caption = 'Logistik-Plattform'
      end
      object Label30: TLabel
        Left = 8
        Top = 146
        Width = 97
        Height = 13
        Caption = 'Verpackungsrelation'
      end
      object Bevel2: TBevel
        Left = 8
        Top = 277
        Width = 357
        Height = 8
        Shape = bsTopLine
      end
      object VerlRelComboBox: TComboBoxPro
        Left = 8
        Top = 204
        Width = 382
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 6
        ExplicitWidth = 355
      end
      object LabelArtComboBox: TComboBoxPro
        Left = 8
        Top = 24
        Width = 382
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 0
        OnChange = LabelArtComboBoxChange
        ExplicitWidth = 355
      end
      object PlanGroupBox: TGroupBox
        Left = 8
        Top = 372
        Width = 355
        Height = 60
        Caption = 'Planungsoptionen'
        TabOrder = 12
        object PlanDeliveryDateCheckBox: TCheckBox
          Left = 8
          Top = 27
          Width = 318
          Height = 17
          Caption = 'Auftragstouren nach Anlieferdatum planen'
          TabOrder = 0
        end
      end
      object DefaultLTComboBox: TComboBoxPro
        Left = 170
        Top = 301
        Width = 192
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 10
      end
      object LogPlatformEdit: TEdit
        Left = 170
        Top = 344
        Width = 190
        Height = 21
        MaxLength = 32
        TabOrder = 11
        Text = 'LogPlatformEdit'
      end
      object CutOffGroupBox: TGroupBox
        Left = 8
        Top = 291
        Width = 153
        Height = 75
        Caption = 'Cut-Off Zeit'
        TabOrder = 9
        object Label28: TLabel
          Left = 52
          Top = 34
          Width = 6
          Height = 13
          Caption = 'h'
        end
        object Label29: TLabel
          Left = 116
          Top = 34
          Width = 16
          Height = 13
          Caption = 'min'
        end
        object CutOffHourEdit: TEdit
          Left = 8
          Top = 31
          Width = 22
          Height = 21
          MaxLength = 2
          TabOrder = 0
          Text = '0'
        end
        object CutOffHourUpDown: TIntegerUpDown
          Left = 30
          Top = 31
          Width = 16
          Height = 21
          Associate = CutOffHourEdit
          Max = 23
          TabOrder = 1
        end
        object CutOffMinEdit: TEdit
          Left = 72
          Top = 31
          Width = 22
          Height = 21
          MaxLength = 2
          TabOrder = 2
          Text = '0'
        end
        object CutOffMinUpDown: TIntegerUpDown
          Left = 94
          Top = 31
          Width = 16
          Height = 21
          Associate = CutOffMinEdit
          Max = 59
          TabOrder = 3
        end
      end
      object PackRelComboBox: TComboBoxPro
        Left = 8
        Top = 162
        Width = 382
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 5
        ExplicitWidth = 355
      end
      object SendungNrPflichtCheckBox: TCheckBox
        Left = 8
        Top = 72
        Width = 357
        Height = 17
        Caption = 'Paket Sendungsnr. ist muss angegeben sein'
        TabOrder = 2
      end
      object SendungNrCheckBox: TCheckBox
        Left = 8
        Top = 52
        Width = 357
        Height = 17
        Caption = 'Paket Sendungsnr. muss erfasst werden'
        TabOrder = 1
      end
      object DimPflichtCheckBox: TCheckBox
        Left = 8
        Top = 92
        Width = 354
        Height = 17
        Caption = 'Paket Abmessungen m'#252'ssen angegeben werden'
        TabOrder = 3
      end
      object BrokerCheckBox: TCheckBox
        Left = 8
        Top = 119
        Width = 357
        Height = 17
        Caption = 'Versand-Broker, Verladung '#252'ber ermittelten Versender'
        TabOrder = 4
      end
      object VerlCloseInfoCheckBox: TCheckBox
        Left = 8
        Top = 233
        Width = 357
        Height = 17
        Caption = 'Infos bei Verladeabschluss abfragen'
        TabOrder = 7
        OnClick = VerlCloseInfoCheckBoxClick
      end
      object VerlInfoPflichCheckBox: TCheckBox
        Left = 19
        Top = 254
        Width = 346
        Height = 17
        Caption = 'Angabe der Infos ist Pflicht'
        TabOrder = 8
      end
    end
    object TabSheet2: TTabSheet
      Caption = 'Daten'#252'bertragung'
      ImageIndex = 1
      DesignSize = (
        395
        441)
      object Label7: TLabel
        Left = 8
        Top = 8
        Width = 126
        Height = 13
        Caption = 'Art der Daten'#252'bertragung'
      end
      object Label16: TLabel
        Left = 8
        Top = 96
        Width = 16
        Height = 13
        Caption = 'ILN'
      end
      object Label19: TLabel
        Left = 134
        Top = 96
        Width = 44
        Height = 13
        Caption = 'Kundenr.'
      end
      object Label21: TLabel
        Left = 254
        Top = 96
        Width = 29
        Height = 13
        Caption = 'Depot'
      end
      object Label24: TLabel
        Left = 8
        Top = 52
        Width = 66
        Height = 13
        Caption = 'DF'#220'-Kennung'
      end
      object Label8: TLabel
        Left = 8
        Top = 394
        Width = 146
        Height = 13
        Caption = 'Mail-Empf'#228'nger f'#252'r Lieferavise'
      end
      object DFUEComboBox: TComboBoxPro
        Left = 8
        Top = 24
        Width = 383
        Height = 22
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 0
        ExplicitWidth = 356
      end
      object GroupBox1: TGroupBox
        Left = 8
        Top = 155
        Width = 383
        Height = 140
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Optionen'
        TabOrder = 5
        ExplicitWidth = 356
        object EmpfLabelCheckBox: TCheckBox
          Left = 8
          Top = 24
          Width = 292
          Height = 17
          Caption = 'Empf'#228'ngeradress als Etikette'
          TabOrder = 0
          Visible = False
        end
        object EmpfLabelLimitCheckBox: TCheckBox
          Left = 8
          Top = 47
          Width = 295
          Height = 17
          Caption = 'Empf'#228'ngeradressetikette bis zu einer VPE-Obergrenze'
          TabOrder = 1
          Visible = False
        end
        object PresetDimCheckBox: TCheckBox
          Left = 8
          Top = 79
          Width = 295
          Height = 17
          Caption = 'Abmessungen vorbelegen'
          TabOrder = 2
        end
        object PreSetWeightCheckBox: TCheckBox
          Left = 8
          Top = 103
          Width = 295
          Height = 17
          Caption = 'Gewicht vorbelegen'
          TabOrder = 3
        end
      end
      object ILNEdit: TEdit
        Left = 8
        Top = 112
        Width = 100
        Height = 21
        MaxLength = 16
        TabOrder = 2
        Text = 'ILNEdit'
      end
      object AccountNrEdit: TEdit
        Left = 134
        Top = 112
        Width = 100
        Height = 21
        MaxLength = 32
        TabOrder = 3
        Text = 'AccountNrEdit'
      end
      object DepotEdit: TEdit
        Left = 254
        Top = 112
        Width = 100
        Height = 21
        MaxLength = 32
        TabOrder = 4
        Text = 'DepotEdit'
      end
      object DFUEKennungEdit: TEdit
        Left = 8
        Top = 68
        Width = 100
        Height = 21
        MaxLength = 16
        TabOrder = 1
        Text = 'DFUEKennungEdit'
      end
      object EDIOptGroupBox: TGroupBox
        Left = 8
        Top = 301
        Width = 357
        Height = 84
        Caption = 'EDI Optionen'
        TabOrder = 6
        object EDIDimCheckBox: TCheckBox
          Left = 8
          Top = 24
          Width = 320
          Height = 17
          Caption = 'Abmessungen '#252'bermitteln'
          TabOrder = 0
        end
        object EDIWeightCheckBox: TCheckBox
          Left = 8
          Top = 47
          Width = 320
          Height = 17
          Caption = 'Gewichte '#252'bermitteln'
          TabOrder = 1
        end
      end
      object AvisMailEdit: TEdit
        Left = 8
        Top = 410
        Width = 383
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 7
        Text = 'AvisMailEdit'
        ExplicitWidth = 356
      end
    end
    object TabSheet1: TTabSheet
      Caption = 'Lademittel'
      DesignSize = (
        395
        441)
      object Label4: TLabel
        Left = 8
        Top = 8
        Width = 108
        Height = 13
        Caption = 'Lademittelkonto im WE'
      end
      object Label25: TLabel
        Left = 8
        Top = 64
        Width = 109
        Height = 13
        Caption = 'Lademittelkonto im WA'
      end
      object WELeerComboBox: TComboBoxPro
        Left = 8
        Top = 24
        Width = 382
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        PopupMenu = LeergutPopupMenu
        TabOrder = 0
        ExplicitWidth = 355
      end
      object WALeerComboBox: TComboBoxPro
        Left = 8
        Top = 80
        Width = 382
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        PopupMenu = LeergutPopupMenu
        TabOrder = 1
        ExplicitWidth = 355
      end
    end
  end
  object KennungEdit: TEdit
    Left = 286
    Top = 158
    Width = 123
    Height = 21
    Anchors = [akTop, akRight]
    MaxLength = 32
    TabOrder = 5
    Text = 'KennungEdit'
  end
  object LeergutPopupMenu: TPopupMenu
    Left = 336
    Top = 224
    object NeuesLeergutkontoanlegen1: TMenuItem
      Caption = 'Neues Leergutkonto anlegen...'
      OnClick = NeuesLeergutkontoanlegen1Click
    end
  end
end
