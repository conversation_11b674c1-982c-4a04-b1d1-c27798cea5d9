﻿unit PrintLPLableDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, DB, ADODB, PrinterUtils, ComboBoxPro,
  ComCtrls;

type
  TPrintLPLableForm = class(TForm)
    Panel2: TPanel;
    Label8: TLabel;
    PrinterComboBox: TComboBoxPro;
    PrintButton: TButton;
    CloseButton: TButton;
    PageControl1: TPageControl;
    LPTabSheet: TTabSheet;
    LPNrLabel: TLabel;
    LPKoorLabel: TLabel;
    LPArtRadioGroup: TRadioGroup;
    ADOQuery1: TADOQuery;
    KommLPTabSheet: TTabSheet;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    UpRadioButton: TRadioButton;
    DownRadioButton: TRadioButton;
    KommLPNrLabel: TLabel;
    KommArNrLabel: TLabel;
    Label6: TLabel;
    KommArTextLabel: TLabel;
    KommArVPELabel: TLabel;
    Label7: TLabel;
    KommArAZPLabel: TLabel;
    Label5: TLabel;
    KommArSTVPELabel: TLabel;
    RelationTabSheet: TTabSheet;
    Label9: TLabel;
    Label10: TLabel;
    RelNameLabel: TLabel;
    RelCodeLabel: TLabel;
    RelAnzahlEdit: TEdit;
    RelAnzahlUpDown: TUpDown;
    Label11: TLabel;
    Label1: TLabel;
    Label12: TLabel;
    Label15: TLabel;
    Label17: TLabel;
    Label16: TLabel;
    LPNameLabel: TLabel;
    Label18: TLabel;
    RelPlatzLabel: TLabel;
    Label19: TLabel;
    Label20: TLabel;
    RelBereichLabel: TLabel;
    RelArtRadioGroup: TRadioGroup;
    GroupBox1: TGroupBox;
    RelTextLabel: TLabel;
    Label21: TLabel;
    RelDescLabel: TLabel;
    LETabSheet: TTabSheet;
    LTComboBox: TComboBoxPro;
    Label22: TLabel;
    LEVonEdit: TEdit;
    LECountEdit: TEdit;
    LEBisEdit: TEdit;
    LENeuRadioButton: TRadioButton;
    LEReprintRadioButton: TRadioButton;
    Label23: TLabel;
    Label24: TLabel;
    Label25: TLabel;
    LBZonenTabSheet: TTabSheet;
   Label26: TLabel;
    ZonenNameLabel: TLabel;
    Label28: TLabel;
    ZonenNrLabel: TLabel;
    Label27: TLabel;
    LBNameLabel: TLabel;
    GebindeTabSheet: TTabSheet;
    GBNeuRadioButton: TRadioButton;
    Label29: TLabel;
    GBCountEdit: TEdit;
    GBReprintRadioButton: TRadioButton;
    Label30: TLabel;
    Label31: TLabel;
    GBBisEdit: TEdit;
    GBVonEdit: TEdit;
    LPExportButton: TButton;
    SaveDialog1: TSaveDialog;
    LECSVButton: TButton;
    FachEdit: TEdit;
    Label32: TLabel;
    LPCheckLabel: TLabel;
    LEKopienComboBox: TComboBox;
    Label33: TLabel;
    DocIDTabSheet: TTabSheet;
    Label34: TLabel;
    DocIDCountEdit: TEdit;
    DocIDTextEdit: TEdit;
    Label35: TLabel;
    Label36: TLabel;
    LPLBNameLabel: TLabel;
    LPLBZoneNameLabel: TLabel;
    Label37: TLabel;
    PrinterTabSheet: TTabSheet;
    Label13: TLabel;
    Label14: TLabel;
    Label38: TLabel;
    PrinterNameLabel: TLabel;
    PrinterDescLabel: TLabel;
    PrinterPortLabel: TLabel;
    Label39: TLabel;
    PrinterNumberLabel: TLabel;
    Label40: TLabel;
    LBShortNameLabel: TLabel;
    LEArtGroupBox: TGroupBox;
    LEPalRadioButton: TRadioButton;
    LEKLTRadioButton: TRadioButton;
    LEKLTBigRadioButton: TRadioButton;
    LETypRadioButton: TRadioButton;
    LELeer5RadioButton: TRadioButton;
    LELeer10RadioButton: TRadioButton;
    LEEANRadioButton: TRadioButton;
    LESerialRadioButton: TRadioButton;
    LELeerRadioButton: TRadioButton;
    LEKommRadioButton: TRadioButton;
    LEPackAutomatRadioButton: TRadioButton;
    RadioButton12: TRadioButton;
    Label41: TLabel;
    LPLayoutComboBox: TComboBoxPro;
    Label42: TLabel;
    KommArEANLabel: TLabel;
    PrtPreviewCheckBox: TCheckBox;
    BarcodeGroupBox: TGroupBox;
    LPITFRadioButton: TRadioButton;
    LPQRRadioButton: TRadioButton;
    LPDMRadioButton: TRadioButton;
    I2F5CheckCheckBox: TCheckBox;
    LP128RadioButton: TRadioButton;
    GroupBox2: TGroupBox;
    LEITFRadioButton: TRadioButton;
    LEQRRadioButton: TRadioButton;
    LEDMRadioButton: TRadioButton;
    LEI2F5CheckCheckBox: TCheckBox;
    LE128RadioButton: TRadioButton;
    procedure PrintButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure LERadioButtonClick(Sender: TObject);
    procedure LECountEditKeyPress(Sender: TObject; var Key: Char);
    procedure GBNeuRadioButtonClick(Sender: TObject);
    procedure LPExportButtonClick(Sender: TObject);
    procedure LECSVButtonClick(Sender: TObject);
    procedure LTComboBoxChange(Sender: TObject);
    procedure LETabSheetShow(Sender: TObject);
    procedure LECountEditChange(Sender: TObject);
    procedure LEVonEditChange(Sender: TObject);
    procedure PrinterComboBoxChange(Sender: TObject);
    procedure LPITFRadioButtonClick(Sender: TObject);
    procedure LEITFRadioButtonClick(Sender: TObject);
  private
    PortArray    :  array [0..255] of TPrinterPorts;

    fMandant     : Integer;
    fLocation    : Integer;
    fLager       : Integer;
    fPlanung     : Integer;
    fRefLB       : Integer;
    fRefLBZone   : Integer;
    fRefRelation : Integer;

    fLEPrefix    : String;
  public
    LPList : TStringList;

    property RefLB       : Integer read fRefLB       write fRefLB;
    property RefLBZone   : Integer read fRefLBZone   write fRefLBZone;
    property RefRelation : Integer read fRefRelation write fRefRelation;

    function GetSelectedPrinter : String;

    procedure Prepare (const DefPrinterName, Format : String; const RefMand, RefLocation, RefLager, RefPlanung : Integer);
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DatenModul, ErrorTracking, LablePrinterUtils, PrintModul, StringUtils, FrontendUtils,
  LVSDatenInterface, ResourceText, SprachModul, VerlaufDLG;

type
  TArrayOfString = array of string;

procedure SetRadiogroupHints(rg:TCustomRadioGroup; const Hints:array of string);
var
   i : Integer;
begin
   for i := 0 to rg.ControlCount-1 do
   begin
      if i > High(Hints) then
         Break;
      with rg.controls[i] do
      begin
         Hint := Hints[i];
         ShowHint := Hint<>'';
      end;
   end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.05.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TPrintLPLableForm.GetSelectedPrinter : String;
var
  prtstr : String;
begin
  if (PrinterComboBox.ItemIndex <= Low (PortArray)) or (PrinterComboBox.ItemIndex > High (PortArray)) then
    prtstr := ''
  else if (Length (PortArray [PrinterComboBox.ItemIndex].Name) = 0) then
    prtstr := ''
  else if (Length (PortArray [PrinterComboBox.ItemIndex].PrtTyp) = 0) then
    prtstr := PortArray [PrinterComboBox.ItemIndex].Name
  else
    prtstr := PortArray [PrinterComboBox.ItemIndex].Name + ';' + PortArray [PrinterComboBox.ItemIndex].PrtTyp;

  Result := prtstr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.Prepare (const DefPrinterName, Format : String; const RefMand, RefLocation, RefLager, RefPlanung : Integer);
begin
  fMandant  := RefMand;
  fLocation := RefLocation;
  fLager    := RefLager;
  fPlanung  := RefPlanung;

  PrintModule.LoadPrinterCombobox (fLager, Format, PrinterComboBox, PortArray, DefPrinterName, True);
  PrinterComboBoxChange (Nil);
  
  FachEdit.Text    := '';
  LECountEdit.Text := '';
  LEVonEdit.Text   := '';
  LEBisEdit.Text   := '';

  LoadLTCombobox (LTComboBox, '''LE'',''VTL'',''KOM'',''KOMM'',''PACK''', fLocation, fLager);

  GBCountEdit.Text := '';
  GBVonEdit.Text   := '';
  GBBisEdit.Text   := '';
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.PrintButtonClick(Sender: TObject);
var
  art,
  lpstr,
  artsuf,
  fname,
  errtext     : String;
  res,
  dbres,
  idx,
  strpos,
  pcount      : Integer;
  checkch     : AnsiChar;
  stop        : Boolean;
  i, c,
  count,
  leanz,
  levon,
  lebis,
  lblanz,
  fachanz,
  koordsize   : Integer;
  lenr,
  lestr,
  sernr,
  errmsg,
  reihe,
  tmplenr     : String;
  cfgstr      : String;
  num,
  numlen      : Integer;
  paramstr    : array [0..31] of AnsiString;
  paramstru   : array [0..31] of String;
  forminfo    : TFormInfos;
  subforminfo : TFormInfos;
  altcursor   : TCursor;
  verlauf     : TVerlaufForm;
begin
  verlauf := Nil;

  if (PrinterComboBox.ItemIndex = -1) or (PrinterComboBox.ItemIndex > High (PortArray)) then
    MessageDlg(FormatMessageText (1381, []), mtError, [mbOK], 0)
  else begin
    res := 0;

    if (PageControl1.ActivePage = LPTabSheet) then begin
      if (LPArtRadioGroup.ItemIndex = -1) then
        MessageDlg(FormatMessageText (1382, []), mtError, [mbOK], 0)
      else begin
        altcursor := Screen.Cursor;
        Screen.Cursor := crHourGlass;

        try
          with PortArray [PrinterComboBox.ItemIndex] do
            res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);
        finally
          Screen.Cursor := altcursor;
        end;

        if (res <> 0) then
          MessageDlg (FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
        else begin
          if (LPArtRadioGroup.itemindex = 0) then
            art := 'LP-NR'
          else if (LPArtRadioGroup.itemindex = 1) then
            art := 'LP-KOORD-GRUND'
          else if (LPArtRadioGroup.itemindex = 2) then
            art := 'LP-KOORD'
          else if (LPArtRadioGroup.itemindex = 3) then
            art := 'LPNR-KOORD'
          else if (LPArtRadioGroup.itemindex = 4) then
            art := 'LP-PROJECT'
          else
            art := '';

          if LPQRRadioButton.Checked then
            art := art + '_QR'
          else if LPDMRadioButton.Checked then
            art := art + '_DM'
          else if LP128RadioButton.Checked then
            art := art + '_128';

          if (res = 0) Then begin
            res := BeginPrinting('LP Label',errtext);

            if ((PortArray [PrinterComboBox.ItemIndex].PrtTyp = 'LASER') and PrtPreviewCheckBox.Checked) then
              res := PrintModule.PreparePreview;

            if (res <> 0) Then
              errtext := FormatResourceText (1012, [])
            else begin
              try
                ADOQuery1.SQL.Clear;
                ADOQuery1.SQL.Add ('select lp.*,l.ID_IFC,lb.SHORT_NAME from V_LP lp, V_LB lb, V_LAGER l where lb.REF=lp.REF_LB and l.REF=lp.REF_LAGER and lp.REF=:ref');

                idx := 0;
                while (idx < LPList.Count) and (res = 0) do begin
                  ADOQuery1.Parameters.ParamByName('ref').Value := StrToInt (LPList.Strings [idx]);

                  try
                    ADOQuery1.Open;

                    reihe := FormatStr (ADOQuery1.FieldByName ('REIHE').AsString,-3,'0');

                    if ((LPArtRadioGroup.itemindex = 1) or (LPArtRadioGroup.itemindex = 2)) then begin
                      if (IsNumber (reihe[1]) and IsNumber (reihe[2]) and IsNumber (reihe[3])) then begin
                        if (LPLayoutComboBox.Visible) then
                          res := DetectFormular (-1, ADOQuery1.FieldByName ('REF_LAGER').AsInteger, GetComboBoxRef (LPLayoutComboBox), '', '', PortArray [PrinterComboBox.ItemIndex].Model, art, forminfo)
                        else
                          res := DetectFormular (-1, ADOQuery1.FieldByName ('REF_LAGER').AsInteger, '', '', PortArray [PrinterComboBox.ItemIndex].Model, art, forminfo)
                      end else begin
                        if (LPLayoutComboBox.Visible) then
                          res := DetectFormular (-1, ADOQuery1.FieldByName ('REF_LAGER').AsInteger, GetComboBoxRef (LPLayoutComboBox), '', '', PortArray [PrinterComboBox.ItemIndex].Model, art + '_ALPHA', forminfo)
                        else
                          res := DetectFormular (-1, ADOQuery1.FieldByName ('REF_LAGER').AsInteger, '', '', PortArray [PrinterComboBox.ItemIndex].Model, art + '_ALPHA', forminfo);
                      end;
                    end else begin
                      if (LPLayoutComboBox.Visible) then
                        res := DetectFormular (-1, ADOQuery1.FieldByName ('REF_LAGER').AsInteger, GetComboBoxRef (LPLayoutComboBox), '', '', PortArray [PrinterComboBox.ItemIndex].Model, art, forminfo)
                      else
                        res := DetectFormular (-1, ADOQuery1.FieldByName ('REF_LAGER').AsInteger, '', '', PortArray [PrinterComboBox.ItemIndex].Model, art, forminfo);
                    end;

                    if (res <> 0) Then
                      errtext := FormatMessageText (1083, [PortArray [PrinterComboBox.ItemIndex].Model, art])
                    else if (forminfo.Generator = 'CR') then begin
                      paramstr [0] := 'REF:'+ADOQuery1.FieldByName ('REF').AsString;

                      if (LPITFRadioButton.Checked and I2F5CheckCheckBox.Checked) then
                        paramstr [1] := 'CHECK:Y'
                      else paramstr [1] := 'CHECK:N';

                      if (LPArtRadioGroup.itemindex = 1) then
                        paramstr [2] := 'EBENE:1'
                      else if (LPArtRadioGroup.itemindex = 2) then
                        paramstr [2] := 'EBENE:0';

                      for i := Low (paramstr) to High (paramstr) do
                        paramstru [i] := paramstr [i];

                      if (res = 0) then
                        res := PrintModule.PrintFormular (ADOQuery1.FieldByName ('LP_DISP').AsString, PortArray [PrinterComboBox.ItemIndex].Port, '', fMandant, ADOQuery1.FieldByName ('REF_LAGER').AsInteger, art, forminfo, paramstru, PrtPreviewCheckBox.Checked, errtext);
                    end else begin
                      checkch := #0;

                      koordsize := 2;

                      if (LPArtRadioGroup.ItemIndex = 1) or (LPArtRadioGroup.ItemIndex = 2) then begin
                        if (ADOQuery1.FieldByName ('FELD').AsInteger > 99) or (ADOQuery1.FieldByName ('PLATZ').AsInteger > 99) or (ADOQuery1.FieldByName ('EBENE').AsInteger > 99) then
                          koordsize := 3;
                      end;

                      if (LPArtRadioGroup.ItemIndex = 0) then begin
                        if (Length (ADOQuery1.FieldByName ('LP_NR').AsString) > 9) then
                          lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-11,'0')
                        else if (Length (ADOQuery1.FieldByName ('LP_NR').AsString) > 7) then
                          lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-9,'0')
                        else
                          lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-7,'0');

                        checkch := GetLELPCheckChar ('2'+lpstr);
                      end else if (LPArtRadioGroup.ItemIndex = 1) then begin
                        lpstr := FormatStr (ADOQuery1.FieldByName ('REIHE').AsString,-3,'0')+FormatStr (ADOQuery1.FieldByName ('FELD').AsString, koordsize * -1,'0')+FormatStr (ADOQuery1.FieldByName ('PLATZ').AsString, koordsize * -1,'0');
                        checkch := GetLELPCheckChar ('3'+lpstr);
                      end else if (LPArtRadioGroup.ItemIndex = 2) then begin
                        lpstr := FormatStr (ADOQuery1.FieldByName ('REIHE').AsString,-3,'0')+FormatStr (ADOQuery1.FieldByName ('FELD').AsString, koordsize * -1,'0')+FormatStr (ADOQuery1.FieldByName ('PLATZ').AsString, koordsize * -1,'0')+FormatStr (ADOQuery1.FieldByName ('EBENE').AsString, koordsize * -1,'0');
                        checkch := GetLELPCheckChar ('4'+lpstr);
                      end else if (LPArtRadioGroup.ItemIndex = 3) then begin
                        if (Length (ADOQuery1.FieldByName ('LP_NR').AsString) > 9) then
                          lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-11,'0')
                        else if (Length (ADOQuery1.FieldByName ('LP_NR').AsString) > 7) then
                          lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-9,'0')
                        else
                          lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-7,'0');

                        checkch := GetLELPCheckChar ('2'+lpstr);
                      end;

                      paramstr [0] := 'LB_NAME:'+ADOQuery1.FieldByName ('BEREICH').AsString;
                      paramstr [1] := 'LB_SHORT_NAME:'+ADOQuery1.FieldByName ('SHORT_BEREICH').AsString;
                      paramstr [2] := 'LB_ZONE:'+ADOQuery1.FieldByName ('BEREICH_ZONE').AsString;

                      paramstr [3] := 'LP_NAME:'+ADOQuery1.FieldByName ('NAME').AsString;
                      paramstr [4] := 'LP_NR:'+Copy ('              ', 1, 3 - (Length (ADOQuery1.FieldByName ('LP_NR').AsString) div 2)) + ADOQuery1.FieldByName ('LP_NR').AsString;

                      if (LPArtRadioGroup.ItemIndex = 0) then begin
                        paramstr [5] := 'LP_KOORD:';

                        paramstr [6] := 'BARCODE_ID:';
                        if Assigned (ADOQuery1.FindField ('BARCODE_ID')) then
                          paramstr [6] := paramstr [6]+ADOQuery1.FieldByName ('BARCODE_ID').AsString;

                        if (LPITFRadioButton.Checked and I2F5CheckCheckBox.Checked) then
                          paramstr [7] := 'BARCODE:' + '2'+lpstr+checkch+'0'
                        else
                          paramstr [7] := 'BARCODE:' + '2'+lpstr+checkch;

                        paramstr [8] := 'BARCODE_TEXT:' + '2 '+lpstr+' '+checkch;

                        if LPDMRadioButton.Checked then
                          paramstr [9] := 'BARCODE_2D:' + 'sl'+'2'+lpstr+checkch+'sl'
                        else if LPQRRadioButton.Checked then
                          paramstr [9] := 'BARCODE_2D:' + 'slx'+'2'+lpstr+checkch
                      end else if (LPArtRadioGroup.ItemIndex = 1) then begin
                        //Koordinate ohne Ebene
                        if Assigned (ADOQuery1.FindField ('KOORD_OHNE_EBENE')) and not (ADOQuery1.FieldByName ('KOORD_OHNE_EBENE').IsNull) then
                          paramstr [5] := 'LP_KOORD:'+ADOQuery1.FieldByName ('KOORD_OHNE_EBENE').AsString
                        else
                          paramstr [5] := 'LP_KOORD:'+FormatStr (ADOQuery1.FieldByName ('REIHE').AsString,-3,'0')+'-'+FormatStr (ADOQuery1.FieldByName ('FELD').AsString,koordsize * -1,'0')+'-'+FormatStr (ADOQuery1.FieldByName ('PLATZ').AsString,koordsize * -1,'0')+'-'+FormatStr ('x',koordsize * -1,'x');

                        paramstr [6] := 'BARCODE_ID:';
                        if Assigned (ADOQuery1.FindField ('BARCODE_ID')) then
                          paramstr [6] := paramstr [6]+ADOQuery1.FieldByName ('BARCODE_ID').AsString;

                        if (I2F5CheckCheckBox.Checked) then
                          paramstr [7] := 'BARCODE:' + '3'+lpstr+checkch+'0'
                        else paramstr [7] := 'BARCODE:' + '3'+lpstr+checkch;

                        paramstr [8] := 'BARCODE_TEXT:' + '3 '+lpstr+' '+checkch;

                        if LPDMRadioButton.Checked then
                          paramstr [9] := 'BARCODE_2D:' + 'sl'+'3'+lpstr+checkch+'sl'
                        else if LPQRRadioButton.Checked then
                          paramstr [9] := 'BARCODE_2D:' + 'slx'+'3'+lpstr+checkch
                        else
                          paramstr [9] := 'BARCODE_2D:';
                      end else if (LPArtRadioGroup.ItemIndex = 2) then begin
                        //Koordinate mit Ebene
                        if Assigned (ADOQuery1.FindField ('KOORD_MIT_EBENE')) and not (ADOQuery1.FieldByName ('KOORD_MIT_EBENE').IsNull) then
                          paramstr [5] := 'LP_KOORD:'+ADOQuery1.FieldByName ('KOORD_MIT_EBENE').AsString
                        else
                          paramstr [5] := 'LP_KOORD:'+FormatStr (ADOQuery1.FieldByName ('REIHE').AsString,-3,'0')+'-'+FormatStr (ADOQuery1.FieldByName ('FELD').AsString,koordsize * -1,'0')+'-'+FormatStr (ADOQuery1.FieldByName ('PLATZ').AsString,koordsize * -1,'0')+'-'+FormatStr (ADOQuery1.FieldByName ('EBENE').AsString,koordsize * -1,'0');

                        paramstr [6] := 'BARCODE_ID:';
                        if Assigned (ADOQuery1.FindField ('BARCODE_ID')) then
                          paramstr [6] := paramstr [6]+ADOQuery1.FieldByName ('BARCODE_ID').AsString;

                        if (I2F5CheckCheckBox.Checked) then
                          paramstr [7] := 'BARCODE:' + '4'+lpstr+checkch+'0'
                        else paramstr [7] := 'BARCODE:' + '4'+lpstr+checkch;

                        paramstr [8] := 'BARCODE_TEXT:' + '4 '+lpstr+' '+checkch;

                        if LPDMRadioButton.Checked then
                          paramstr [9] := 'BARCODE_2D:' + 'sl'+'4'+lpstr+checkch+'sl'
                        else if LPQRRadioButton.Checked then
                          paramstr [9] := 'BARCODE_2D:' + 'slx'+'4'+lpstr+checkch
                        else
                          paramstr [9] := 'BARCODE_2D:';
                      end else if (LPArtRadioGroup.ItemIndex = 3) then begin
                        paramstr [5] := 'LP_KOORD:'+ADOQuery1.FieldByName ('LP_DISP').AsString;

                        paramstr [6] := 'BARCODE_ID:';
                        if Assigned (ADOQuery1.FindField ('BARCODE_ID')) then
                          paramstr [6] := paramstr [5]+ADOQuery1.FieldByName ('BARCODE_ID').AsString;

                        if (I2F5CheckCheckBox.Checked) then
                          paramstr [7] := 'BARCODE:' + '2'+lpstr+checkch+'0'
                        else paramstr [7] := 'BARCODE:' + '2'+lpstr+checkch;

                        paramstr [8] := 'BARCODE_TEXT:' + '2 '+lpstr+' '+checkch;

                        if LPDMRadioButton.Checked then
                          paramstr [9] := 'BARCODE_2D:' + 'sl'+'2'+lpstr+checkch+'sl'
                        else if LPQRRadioButton.Checked then
                          paramstr [9] := 'BARCODE_2D:' + 'slx'+'2'+lpstr+checkch
                        else
                          paramstr [9] := 'BARCODE_2D:';
                      end else if (LPArtRadioGroup.ItemIndex = 4) then begin
                        paramstr [5] := 'LP_KOORD:'+FormatStr (ADOQuery1.FieldByName ('REIHE').AsString,-3,'0')+'-'+FormatStr (ADOQuery1.FieldByName ('FELD').AsString,koordsize * -1,'0')+'-'+FormatStr (ADOQuery1.FieldByName ('PLATZ').AsString,koordsize * -1,'0')+'-'+FormatStr (ADOQuery1.FieldByName ('EBENE').AsString,koordsize * -1,'0');

                        paramstr [6] := 'BARCODE_ID:';
                        if Assigned (ADOQuery1.FindField ('BARCODE_ID')) then
                          paramstr [6] := paramstr [6]+ADOQuery1.FieldByName ('BARCODE_ID').AsString;

                        if Assigned (ADOQuery1.FindField ('BARCODE_ID')) and not (ADOQuery1.FieldByName ('BARCODE_ID').IsNull) then
                          paramstr [7] := 'BARCODE:' + ADOQuery1.FieldByName ('BARCODE_ID').AsString
                        else if not (ADOQuery1.FieldByName ('WWS_KOOR').IsNull) then
                          paramstr [7] := 'BARCODE:' + ADOQuery1.FieldByName ('WWS_KOOR').AsString
                        else
                          paramstr [7] := 'BARCODE:' + ADOQuery1.FieldByName ('NAME').AsString;

                        paramstr [8] := 'BARCODE_TEXT:' + ADOQuery1.FieldByName ('LP_DISP').AsString;

                        if LPDMRadioButton.Checked then
                          paramstr [9] := 'BARCODE_2D:'+'sl'+'id'+ADOQuery1.FieldByName ('BARCODE_ID').AsString+'sl'
                        else if LPQRRadioButton.Checked then
                          paramstr [9] := 'BARCODE_2D:'+'slx'+'id'+ADOQuery1.FieldByName ('BARCODE_ID').AsString
                        else
                          paramstr [9] := 'BARCODE_2D:';
                      end;

                      paramstr [10]  := 'LP_LAGER:'+ADOQuery1.FieldByName ('LAGER').AsString;
                      paramstr [11] := 'LP_REIHE:'+ADOQuery1.FieldByName ('REIHE').AsString;
                      paramstr [12] := 'LP_FELD:'+FormatStr (ADOQuery1.FieldByName ('FELD').AsString,koordsize * -1,'0');
                      paramstr [13] := 'LP_PLATZ:'+FormatStr (ADOQuery1.FieldByName ('PLATZ').AsString,koordsize * -1,'0');
                      paramstr [14] := 'LP_EBENE:'+FormatStr (ADOQuery1.FieldByName ('EBENE').AsString,koordsize * -1,'0');

                      paramstr [15] := 'LP_CHECK:'+FormatStr (ADOQuery1.FieldByName ('CHECK_DIGITS').AsString,-2,'0');

                      if (LPArtRadioGroup.ItemIndex = 2) then
                        paramstr [16] := 'LP_STELLPLATZ_NR:'
                      else
                        paramstr [16] := 'LP_STELLPLATZ_NR:'+ADOQuery1.FieldByName ('STELLPLATZ_NR').AsString;

                      if (I2F5CheckCheckBox.Checked) then
                        paramstr [17] := 'ZPL-CHECK:Y'
                      else paramstr [17] := 'ZPL-CHECK:N';

                      paramstr [18] := 'LAGER:'+ADOQuery1.FieldByName ('LAGER').AsString;
                      paramstr [19] := 'LAGER_ID:'+ADOQuery1.FieldByName ('ID_IFC').AsString;
                      paramstr [20] := 'LB_SHORT_NAME:'+ADOQuery1.FieldByName ('SHORT_NAME').AsString;

                      paramstr [21] := 'KOPIE:1';

                      res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);
                    end;
                  except
                    on e: Exception do begin
                      ErrorTrackingModule.WriteErrorLog ('TPrintLPLableForm.PrintButtonClick 1', e.ClassName + ' : ' + e.Message);
                    end;
                  end;

                  ADOQuery1.Close;

                  Inc (idx);
                end;

                if (res <> 0) Then
                  MessageDlg (FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
              finally
                EndPrinting;

                if ((PortArray [PrinterComboBox.ItemIndex].PrtTyp = 'LASER') and PrtPreviewCheckBox.Checked) then
                  PrintModule.BeginPreview
              end;
            end;
          end;
        end;

        ClosePrinterPort;
      end;
    end else if (PageControl1.ActivePage = KommLPTabSheet) then begin
      altcursor := Screen.Cursor;
      Screen.Cursor := crHourGlass;

      try
        with PortArray [PrinterComboBox.ItemIndex] do
          res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);
      finally
        Screen.Cursor := altcursor;
      end;

      if (res <> 0) then
        MessageDlg (FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
      else begin
        if (LPList.Count = 1) Then begin
          if (UpRadioButton.Checked) then
            art := 'KOMM-LP-OBEN'
          else
            art := 'KOMM-LP-UNTEN';

          res := DetectFormular (fMandant, fLager, '', '', PortArray [PrinterComboBox.ItemIndex].Model, art, forminfo);
          if (res <> 0) Then
            errtext := FormatMessageText (1083, [PortArray [PrinterComboBox.ItemIndex].Model, art])
          else begin
            res := BeginPrinting('Komm-LP Label',errtext);

            if (res = 0) then begin
              ADOQuery1.SQL.Clear;
              ADOQuery1.SQL.Add ('select lp.* from V_KOMM_LP klp, V_LP lp where lp.REF=klp.REF_LP and klp.REF=:ref');
              ADOQuery1.Parameters.ParamByName('ref').Value := StrToInt (LPList.Strings [0]);

              ADOQuery1.Open;

              lpstr := FormatStr (KommLPNrLabel.Caption,-7,'0');
              checkch := GetLELPCheckChar ('6'+lpstr);

              paramstr [0] := 'KOMM_LP_NR:'+KommLPNrLabel.Caption;
              paramstr [1] := 'AR_NR:'+KommArNrLabel.Caption;
              paramstr [2] := 'AR_TEXT:'+KommArTextLabel.Caption;
              paramstr [3] := 'AR_VPE:'+KommArVPELabel.Caption;
              paramstr [4] := 'BARCODE:' + '6'+lpstr+checkch;
              paramstr [5] := 'BARCODE_TEXT:' + '6 '+lpstr+' '+checkch;
              paramstr [6] := 'AR_AZP:'+KommArAZPLabel.Caption;
              paramstr [7] := 'LB_NAME:'+ADOQuery1.FieldByName ('BEREICH').AsString;
              paramstr [8] := 'LB_SHORT_NAME:'+ADOQuery1.FieldByName ('SHORT_BEREICH').AsString;
              paramstr [9] := 'LB_ZONE:'+ADOQuery1.FieldByName ('BEREICH_ZONE').AsString;
              paramstr [10] := 'LP_NAME:'+ADOQuery1.FieldByName ('NAME').AsString;
              paramstr [11] := 'LP_NR:'+Copy ('              ', 1, 3 - (Length (ADOQuery1.FieldByName ('LP_NR').AsString) div 2)) + ADOQuery1.FieldByName ('LP_NR').AsString;
              paramstr [12] := 'KOPIE:1';
              paramstr [13] := 'AR_STUECK:'+KommArSTVPELabel.Caption;
              paramstr [14] := 'EAN:'+KommArEANLabel.Caption;

              if (Length (KommArSTVPELabel.Caption) > 0) then
                paramstr [8] := paramstr [8] + '('+KommArSTVPELabel.Caption+')';

              res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

              ADOQuery1.Close;

              EndPrinting;
            end;
          end;
        end else begin
          res := BeginPrinting('Komm-LP Label',errtext);

          if (res = 0) Then begin
            ADOQuery1.SQL.Clear;
            ADOQuery1.SQL.Add ('select klp.*, lp.SHORT_BEREICH, lp.NAME, lp.BEREICH_ZONE, aei.EINHEIT as INHALT_EINHEIT, ari.ARTIKEL_NR as INHALT_ARTIKEL_NR'
                              +' from V_KOMM_LP_ZUORDNUNG klp'
                              +' inner join V_LP lp on (lp.REF=klp.REF_LP)'
                              +' left outer join V_ARTIKEL_EINHEIT ae on (ae.STATUS=''AKT'' and ae.REF_AR=klp.REF_AR and ae.REF_EINHEIT=klp.REF_EINHEIT)'
                              +' left outer join V_ARTIKEL_EINHEIT aei on (aei.REF=ae.REF_INHALT)'
                              +' left outer join V_ARTIKEL ari on (ari.REF=aei.REF_AR)'
                              +' where klp.REF_KOMM_LP=:ref'
                              );

            if (fPlanung = -1) then
              ADOQuery1.SQL.Add ('and REF_PLANUNG is null')
            else
              ADOQuery1.SQL.Add ('and REF_PLANUNG='+IntToStr (fPlanung));

            ADOQuery1.SQL.Add ('order by REF_KOMM_REL');

            idx := 0;
            while (idx < LPList.Count) and (res = 0) do begin
              ADOQuery1.Parameters.ParamByName('ref').Value := StrToInt (LPList.Strings [idx]);

              try
                ADOQuery1.Open;

                if (UpRadioButton.Checked) then
                  art := 'KOMM-LP-OBEN'
                else
                  art := 'KOMM-LP-UNTEN';

                res := DetectFormular (ADOQuery1.Fields [3].AsInteger, ADOQuery1.Fields [6].AsInteger, '', '', PortArray [PrinterComboBox.ItemIndex].Model, art, forminfo);
                if (res <> 0) Then
                  errtext := 'Es ist kein Formular für diesen Vorgang ('+PortArray [PrinterComboBox.ItemIndex].Model+'\'+art+') definiert'
                else begin
                  lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-7,'0');
                  checkch := GetLELPCheckChar ('6'+lpstr);

                  paramstr [0] := 'KOMM_LP_NR:'+ADOQuery1.FieldByName ('LP_NR').AsString;
                  paramstr [1] := 'AR_NR:'+ADOQuery1.FieldByName ('ARTIKEL_NR').AsString;
                  paramstr [2] := 'AR_TEXT:'+ADOQuery1.FieldByName ('ARTIKEL_TEXT').AsString;
                  paramstr [3] := 'AR_VPE:'+ADOQuery1.FieldByName ('EINHEIT').AsString;
                  paramstr [4] := 'BARCODE:' + '6'+lpstr+checkch;
                  paramstr [5] := 'BARCODE_TEXT:' + '6 '+lpstr+' '+checkch;
                  paramstr [6] := 'AR_AZP:'+ADOQuery1.FieldByName ('AR_AZP').AsString;
                  paramstr [7] := 'LB_NAME:'+ADOQuery1.FieldByName ('BEREICH').AsString;
                  paramstr [8] := 'LB_SHORT_NAME:'+ADOQuery1.FieldByName ('SHORT_BEREICH').AsString;
                  paramstr [9] := 'LB_ZONE:'+ADOQuery1.FieldByName ('BEREICH_ZONE').AsString;
                  paramstr [10] := 'LP_NAME:'+ADOQuery1.FieldByName ('NAME').AsString;
                  paramstr [11] := 'LP_NR:'+Copy ('              ', 1, 3 - (Length (ADOQuery1.FieldByName ('LP_NR').AsString) div 2)) + ADOQuery1.FieldByName ('LP_NR').AsString;
                  paramstr [12] := 'KOPIE:1';

                  paramstr [13] := 'AR_STUECK:';
                  if not ADOQuery1.FieldByName ('INHALT_ARTIKEL_NR').IsNull then
                    paramstr [13] := paramstr [13] + ADOQuery1.FieldByName ('INHALT_EINHEIT').AsString + '=' + ADOQuery1.FieldByName ('INHALT_ARTIKEL_NR').AsString;

                  paramstr [14] := 'EAN:';

                  if Assigned (ADOQuery1.FindField('EAN')) then
                    paramstr [14] := paramstr [14]+ADOQuery1.FieldByName ('EAN').AsString;

                  res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);
                end;

                ADOQuery1.Close;
              except
                on e: Exception do begin
                  ErrorTrackingModule.WriteErrorLog ('TPrintLPLableForm.PrintButtonClick 3', e.ClassName + ' : ' + e.Message);
                end;
              end;

              Inc (idx);
            end;

            EndPrinting;
          end;
        end;

        ClosePrinterPort;
      end;
    end else if (PageControl1.ActivePage = RelationTabSheet) then begin
      altcursor := Screen.Cursor;
      Screen.Cursor := crHourGlass;

      try
        with PortArray [PrinterComboBox.ItemIndex] do
          res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);
      finally
        Screen.Cursor := altcursor;
      end;

      if (res <> 0) then
        MessageDlg (FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
      else begin
        with PortArray [PrinterComboBox.ItemIndex] do begin
          if (RelArtRadioGroup.ItemIndex = 1) then
            res := DetectFormular (fMandant, fLager, '', '', Model, 'RELATIONS-PLATZ', forminfo)
          else if (Length (RelTextLabel.Caption) > 0) then
            res := DetectFormular (fMandant, fLager, '', '', Model, 'RELATION_TEXT-LABEL', forminfo)
          else
            res := DetectFormular (fMandant, fLager, '', '', Model, 'RELATIONS-LABEL', forminfo);
        end;

        if (res <> 0) Then
          errtext := FormatMessageText (1083, [PortArray [PrinterComboBox.ItemIndex].Model, ''])
        else begin
          if (RelAnzahlUpDown.Position > 99) then
            lblanz := 99
          else lblanz := RelAnzahlUpDown.Position;

          if (PortArray [PrinterComboBox.ItemIndex].PrtTyp = 'LASER') then begin
            with PortArray [PrinterComboBox.ItemIndex] do
              res := PrintModule.PrintFormular ('', Port, '', fMandant, -1, forminfo.FormularName, forminfo, ['REF:'+IntToStr (fRefRelation)], PrtPreviewCheckBox.Checked, errtext);

            if (res <> 0) Then
              MessageDlg(FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
            else if PrtPreviewCheckBox.Checked then
              PrintModule.BeginPreview
          end else begin
            res := BeginPrinting('Relation Label',errtext);

            if (res = 0) then begin
              pcount := 0;

              idx := 1;
              if (Length (RelTextLabel.Caption) > 0) then begin
                lpstr := RelTextLabel.Caption;

                repeat
                  strpos := Pos (#13, lpstr);

                  if (strpos = 0) then
                    paramstr [pcount] := 'REL_TEXT_' + IntToStr (idx) + ':' + lpstr
                  else begin
                    paramstr [pcount] := 'REL_TEXT_' + IntToStr (idx) + ':' + Copy (lpstr, 1, strpos - 1);

                    if (Length (lpstr) > strpos) and (lpstr [strpos + 1] = #10) then Inc (strpos);
                    lpstr := Copy (lpstr, strpos + 1, Length (lpstr) - strpos);
                  end;

                  Inc (idx);
                  Inc (pcount);
                until (strpos = 0) and (Length (lpstr) > 0) or (idx > 5);
              end;

              while (idx < 6) do begin
                paramstr [pcount] := 'REL_TEXT_' + IntToStr (idx) + ':';

                Inc (idx);
                Inc (pcount);
              end;

              if (Length (RelNameLabel.Caption) > 6) then begin
                paramstr [pcount] := 'REL_NAME:';
                paramstr [pcount + 1] := 'REL_NAME_LANG:'+RelNameLabel.Caption;
              end else begin
                paramstr [pcount] := 'REL_NAME:'+RelNameLabel.Caption;
                paramstr [pcount + 1] := 'REL_NAME_LANG:';
              end;

              Inc (pcount, 2);

              paramstr [pcount] := 'REL_DESC:'+RelDescLabel.Caption;
              Inc (pcount);

              if (Length (RelCodeLabel.Caption) > 0) then begin
                if (RelArtRadioGroup.ItemIndex = 0) then begin
                  lpstr := FormatStr (RelCodeLabel.Caption,-7,'0');
                  checkch := GetLELPCheckChar ('8'+lpstr);

                  paramstr [pcount] := 'BARCODE:'+'8'+lpstr+checkch;
                  paramstr [pcount + 1] := 'BARCODE_TEXT:'+'8 '+lpstr+' '+checkch;
                end else begin
                  lpstr := FormatStr (RelCodeLabel.Caption,-7,'0');
                  checkch := GetLELPCheckChar ('9'+lpstr);

                  paramstr [pcount] := 'BARCODE:'+'9'+lpstr+checkch;
                  paramstr [pcount + 1] := 'BARCODE_TEXT:'+'9 '+lpstr+' '+checkch;
                end;
              end else begin
                checkch := #0;
                paramstr [pcount] := 'BARCODE:';
                paramstr [pcount + 1] := 'BARCODE_TEXT:';
              end;

              Inc (pcount, 2);

              paramstr [pcount] := 'LB:'+RelBereichLabel.Caption;
              Inc (pcount, 1);

              paramstr [pcount] := 'LP:'+RelPlatzLabel.Caption;
              Inc (pcount, 1);

              paramstr [pcount] := 'KOPIEN:'+GetCopySequenz (lblanz);
              Inc (pcount);

              res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

              EndPrinting;
            end;
          end;
        end;

        ClosePrinterPort;
      end;
    end else if (PageControl1.ActivePage = LETabSheet) then begin
      if (GetComboBoxRef (LTComboBox) = -1) then
        MessageDlg(FormatMessageText (1383, []), mtError, [mbOK], 0)
      //else if (LEArtRadioGroup.ItemIndex = -1) then
      //  MessageDlg('Die Auszeichnungsart muss gewählt werden', mtError, [mbOK], 0)
      else begin
        altcursor := Screen.Cursor;
        Screen.Cursor := crHourGlass;

        try
          with PortArray [PrinterComboBox.ItemIndex] do
            res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);
        finally
          Screen.Cursor := altcursor;
        end;

        if (res <> 0) then
          MessageDlg (FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
        else begin
          if LEQRRadioButton.Checked then
            artsuf := '_QR'
          else if LEDMRadioButton.Checked then
            artsuf := '_DM'
          else if LE128RadioButton.Checked then
            artsuf := '_128'
          else
            artsuf := '';

          ADOQuery1.SQL.Clear;
          ADOQuery1.SQL.Add ('select * from V_LT_TYPEN where REF=:ref');
          ADOQuery1.Parameters.ParamByName('ref').Value := GetComboBoxRef (LTComboBox);

          try
            ADOQuery1.Open;

            with PortArray [PrinterComboBox.ItemIndex] do begin
              if (LEPalRadioButton.Checked) then begin
                art := 'LE-LABEL' + artsuf;
                res := DetectFormular (fMandant, fLager, '', '', Model, art, forminfo);

                if Assigned (ADOQuery1.FindField ('FACH_ANZAHL')) and not (ADOQuery1.FieldByName('FACH_ANZAHL').IsNull) then begin
                  art := 'LE-LABEL-FACH' + artsuf;
                  res := DetectFormular (fMandant, fLager, '', '', Model, art, subforminfo);
                end else if (FachEdit.Enabled) then begin
                  art := 'LE-LABEL-FACH' + artsuf;
                  res := DetectFormular (fMandant, fLager, '', '', Model, art, subforminfo);
                end;
              end else if (LEKLTRadioButton.Checked) then begin
                art := 'LE-LABEL-KLT' + artsuf;
                res := DetectFormular (fMandant, fLager, '', '', Model, art, forminfo);

                if Assigned (ADOQuery1.FindField ('FACH_ANZAHL')) and not (ADOQuery1.FieldByName('FACH_ANZAHL').IsNull) then begin
                  art := 'LE-LABEL-KLT-FACH' + artsuf;
                  res := DetectFormular (fMandant, fLager, '', '', Model, art, subforminfo);
                end else if (FachEdit.Enabled) then begin
                  art := 'LE-LABEL-KLT-FACH' + artsuf;
                  res := DetectFormular (fMandant, fLager, '', '', Model, art, subforminfo);
                end;
              end else if (LEKLTBigRadioButton.Checked) then begin
                art := 'LE-LABEL-KLT-BIG' + artsuf;
                res := DetectFormular (fMandant, fLager, '', '', Model, art, forminfo);

                if Assigned (ADOQuery1.FindField ('FACH_ANZAHL')) and not (ADOQuery1.FieldByName('FACH_ANZAHL').IsNull) then begin
                  art := 'LE-LABEL-KLT-FACH' + artsuf;
                  res := DetectFormular (fMandant, fLager, '', '', Model, art, subforminfo);
                end else if (FachEdit.Enabled) then begin
                  art := 'LE-LABEL-KLT-FACH' + artsuf;
                  res := DetectFormular (fMandant, fLager, '', '', Model, art, subforminfo);
                end;
              end else if (LETypRadioButton.Checked) then begin
                art := 'LE-TYPE' + artsuf;
                res := DetectFormular (fMandant, fLager, '', '', Model, art, forminfo);
              end else if (LELeerRadioButton.Checked) or (LELeer5RadioButton.Checked) or (LELeer10RadioButton.Checked)then begin
                art := 'LE-TYPE-LEER' + artsuf;
                res := DetectFormular (fMandant, fLager, '', '', Model, art, forminfo);
              end else if (LEEANRadioButton.Checked) then begin
                art := 'LE-EAN';
                res := DetectFormular (fMandant, fLager, '', '', Model, art, forminfo);
              end else if (LESerialRadioButton.Checked) then begin
                art := 'LE-SERIAL' + artsuf;
                res := DetectFormular (fMandant, fLager, '', '', Model, art, forminfo);
              end else if (LEKommRadioButton.Checked) then begin
                art := 'KOMM-WAGEN' + artsuf;
                res := DetectFormular (fMandant, fLager, '', '', Model, art, forminfo);

                if Assigned (ADOQuery1.FindField ('FACH_ANZAHL')) and not (ADOQuery1.FieldByName('FACH_ANZAHL').IsNull) then begin
                  art := 'KOMM-WAGEN-FACH' + artsuf;
                  res := DetectFormular (fMandant, fLager, '', '', Model, art, subforminfo);
                end;
              end else if (LEPackAutomatRadioButton.Checked) then begin
                art := 'LE-AUTOMAT' + artsuf;
                res := DetectFormular (fMandant, fLager, '', '', Model, art, forminfo);
              end;
            end;

            if (res <> 0) Then
              errtext := FormatMessageText (1083, [PortArray [PrinterComboBox.ItemIndex].Model, art])
            else begin
              lebis := -1;
              res := 0;
              errmsg := '';
              stop := False;
              pcount := 0;

              if (LEEANRadioButton.Checked) then begin
                paramstr [pcount] := 'LE_NAME:';
                Inc (pcount);
                paramstr [pcount] := 'LT_NAME:'+ADOQuery1.FieldByName('NAME').AsString;
                Inc (pcount);
                paramstr [pcount] := 'LT_ID:'+ADOQuery1.FieldByName('LT_ID').AsString;
                Inc (pcount);
                paramstr [pcount] := 'LT_EAN:'+ADOQuery1.FieldByName('EAN').AsString;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE:' + ADOQuery1.FieldByName('EAN').AsString;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE_TEXT:' + ADOQuery1.FieldByName('EAN').AsString;
                Inc (pcount);
                paramstr [pcount] := 'KOPIEN:'+GetCopySequenz (1);
                Inc (pcount);

                if (forminfo.Generator <> 'CR') then
                  res := BeginPrinting('LT Typlabel',errtext);

                if (forminfo.Generator = 'CR') then begin
                  for i := Low (paramstr) to High (paramstr) do
                    paramstru [i] := paramstr [i];

                  res := PrintModule.PrintFormular ('', PortArray [PrinterComboBox.ItemIndex].Port, '', fMandant, fLager, art, forminfo, paramstru, PrtPreviewCheckBox.Checked, errtext);

                  if (res <> 0) Then
                    MessageDlg(FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
                  else if PrtPreviewCheckBox.Checked then
                    PrintModule.BeginPreview
                end else begin
                  res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);
                end;

                if (forminfo.Generator <> 'CR') then
                  EndPrinting;
              end else if (LETypRadioButton.Checked) then begin
                lestr := ADOQuery1.FieldByName('LT_ID').AsString;
                checkch := GetLELPCheckChar ('51'+lestr);

                paramstr [pcount] := 'LE_NAME:';
                Inc (pcount);
                paramstr [pcount] := 'LT_NAME:'+ADOQuery1.FieldByName('NAME').AsString;
                Inc (pcount);
                paramstr [pcount] := 'LT_ID:'+ADOQuery1.FieldByName('LT_ID').AsString;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE:' + '51'+lestr+checkch;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE_TEXT:' + '51 '+lestr+' '+checkch;
                Inc (pcount);
                paramstr [pcount] := 'KOPIEN:'+GetCopySequenz (1);
                Inc (pcount);

                if (forminfo.Generator <> 'CR') then
                  res := BeginPrinting('LT Typlabel',errtext);

                if (forminfo.Generator = 'CR') then begin
                  for i := Low (paramstr) to High (paramstr) do
                    paramstru [i] := paramstr [i];

                  res := PrintModule.PrintFormular ('', PortArray [PrinterComboBox.ItemIndex].Port, '', fMandant, fLager, art, forminfo, paramstru, PrtPreviewCheckBox.Checked, errtext);

                  if (res <> 0) Then
                    MessageDlg(FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
                  else if PrtPreviewCheckBox.Checked then
                    PrintModule.BeginPreview
                end else begin
                  res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);
                end;

                if (forminfo.Generator <> 'CR') then
                  EndPrinting;
              end else if (LELeerRadioButton.Checked) or (LELeer5RadioButton.Checked) or (LELeer10RadioButton.Checked) then begin
                lestr := ADOQuery1.FieldByName('LT_ID').AsString;

                if LELeerRadioButton.Checked then
                  lestr := lestr + 'x001'
                else if LELeer5RadioButton.Checked then
                  lestr := lestr + 'x005'
                else if LELeer10RadioButton.Checked then
                  lestr := lestr + 'x010';

                checkch := GetLELPCheckChar ('55'+lestr);

                paramstr [pcount] := 'LE_NAME:';
                Inc (pcount);
                paramstr [pcount] := 'LT_NAME:'+ADOQuery1.FieldByName('NAME').AsString;
                Inc (pcount);
                paramstr [pcount] := 'LT_ID:'+ADOQuery1.FieldByName('LT_ID').AsString;
                Inc (pcount);

                if LELeerRadioButton.Checked then
                  paramstr [pcount] := 'MULTIPLIER:'
                else if LELeer5RadioButton.Checked then
                  paramstr [pcount] := 'MULTIPLIER:5fach'
                else if LELeer10RadioButton.Checked then
                  paramstr [pcount] := 'MULTIPLIER:10fach';
                Inc (pcount);

                paramstr [pcount] := 'BARCODE:' + '55'+lestr+checkch;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE_TEXT:' + '55 '+lestr+' '+checkch;
                Inc (pcount);
                paramstr [pcount] := 'KOPIEN:'+GetCopySequenz (1);
                Inc (pcount);

                if (forminfo.Generator <> 'CR') then
                  res := BeginPrinting('LT Leergut Typlabel',errtext);

                if (forminfo.Generator = 'CR') then begin
                  for i := Low (paramstr) to High (paramstr) do
                    paramstru [i] := paramstr [i];

                  res := PrintModule.PrintFormular ('', PortArray [PrinterComboBox.ItemIndex].Port, '', fMandant, fLager, art, forminfo, paramstru, PrtPreviewCheckBox.Checked, errtext);

                  if (res <> 0) Then
                    MessageDlg(FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
                  else if PrtPreviewCheckBox.Checked then
                    PrintModule.BeginPreview
                end else begin
                  res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);
                end;

                if (forminfo.Generator <> 'CR') then
                  EndPrinting;
              end else begin
                if (LENeuRadioButton.Checked) then begin
                  if (Length (LECountEdit.Text) = 0) then begin
                    errmsg := FormatMessageText (1085, []);
                    if LECountEdit.CanFocus then LECountEdit.SetFocus;
                  end else if not (TryStrToInt (LECountEdit.Text, leanz)) then begin
                    errmsg := FormatMessageText (1085, []);
                    if LECountEdit.CanFocus then LECountEdit.SetFocus;
                  end else if (leanz > 99) then begin
                    errmsg := FormatMessageText (1641, []);
                    if LECountEdit.CanFocus then LECountEdit.SetFocus;
                  end;
                end else if (LEReprintRadioButton.Checked) then begin
                  if (Length (LEVonEdit.Text) = 0) then begin
                    errmsg := FormatMessageText (1086, []);
                    if LEVonEdit.CanFocus then LEVonEdit.SetFocus;
                  end else if (Length (LEVonEdit.Text) <> 9) then begin
                    errmsg := FormatMessageText (1087, []);
                    if LEVonEdit.CanFocus then LEVonEdit.SetFocus;
                  end else if (Length (fLEPrefix) > 0) and (Copy (LEVonEdit.Text, 1, Length (fLEPrefix)) <> fLEPrefix) then begin
                    errmsg := FormatMessageText (1088, []);
                    if LEVonEdit.CanFocus then LEVonEdit.SetFocus;
                  end else if not (TryStrToInt (LEVonEdit.Text, levon)) then begin
                    errmsg := FormatMessageText (1089, []);
                    if LEVonEdit.CanFocus then LEVonEdit.SetFocus;
                  end else if (Length (fLEPrefix) > 0) and (Length (LEBisEdit.Text) > 0) and (Copy (LEBisEdit.Text, 1, Length (fLEPrefix)) <> fLEPrefix) then begin
                    errmsg := FormatMessageText (1090, []);
                    if LEBisEdit.CanFocus then LEBisEdit.SetFocus;
                  end else if (Length (LEBisEdit.Text) > 0) and not (TryStrToInt (LEBisEdit.Text, lebis)) then begin
                    errmsg := FormatMessageText (1091, []);
                    if LEBisEdit.CanFocus then LEBisEdit.SetFocus;
                  end else if (lebis = -1) then
                    lebis := levon
                  else if (levon > lebis) then
                    errmsg := FormatMessageText (1092, [])
                  else if ((lebis - levon) > 99) then
                    lebis := levon + 99;

                  leanz := (lebis - levon);
                end else
                  errmsg := FormatMessageText (1093, []);

                if (Length (errmsg) > 0) then
                  MessageDlg(errmsg, mtError, [mbOK], 0)
                else begin
                  if (LESerialRadioButton.Checked) then
                    fachanz := 0
                  else if Assigned (ADOQuery1.FindField ('FACH_ANZAHL')) and not (ADOQuery1.FieldByName('FACH_ANZAHL').IsNull) then begin
                    if (ADOQuery1.FieldByName('FACH_ANZAHL').AsInteger <= 1) then
                      fachanz := 0
                    else
                      fachanz := ADOQuery1.FieldByName('FACH_ANZAHL').AsInteger;
                  end else if not (FachEdit.Enabled) then
                    fachanz := 0
                  else if not TryStrToInt (FachEdit.Text, fachanz) then
                    fachanz := 0;

                  dbres := 0;
                  count := 0;

                  if (leanz > 3) then begin
                    verlauf := TVerlaufForm.Create(Self);

                    verlauf.Caption := GetResourceText (1818);
                    verlauf.Label1.Visible := True;
                    verlauf.Label1.Caption := GetResourceText (1819);
                    verlauf.ProgressBar1.Max := leanz;

                    verlauf.BeginShowModal;
                  end;

                  try
                    while not (stop) and (res = 0) and (dbres = 0) do begin
                      if (LENeuRadioButton.Checked) then begin
                        if (count >= leanz) then
                          stop := true
                        else begin
                          if (LEPackAutomatRadioButton.Checked) then begin
                            dbres := CreateLTNummer (fLocation, fLager, GetComboBoxRef (LTComboBox), lenr);
                          end else if (LESerialRadioButton.Checked) then
                            dbres := CreateLTSerial (fLocation, fLager, GetComboBoxRef (LTComboBox), sernr)
                          else
                            dbres := CreateLTNummer (fLocation, fLager, GetComboBoxRef (LTComboBox), lenr);

                          if (dbres <> 0) then
                            MessageDlg(FormatMessageText (1384, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
                        end;
                      end else if (LEReprintRadioButton.Checked) then begin
                        if ((levon + count) > lebis) then
                          stop := true
                        else
                          lenr := IntToStr (levon + count);
                      end else
                        stop := True;

                      if (res = 0) and (dbres = 0) and not (stop) then begin
                        if (forminfo.Generator <> 'CR') then
                          res := BeginPrinting('LT Label',errtext);

                        if (res = 0) then begin
                          for i := 0 to fachanz do begin
                            pcount := 0;

                            if (LEPackAutomatRadioButton.Checked) then
                            else if (LESerialRadioButton.Checked) then
                            else begin
                              if (fachanz > 1) then
                                tmplenr := Copy (lenr, 1, Length (lenr) - 2) + FormatIntToStr (i, 2)
                              else if not (FachEdit.Enabled) then
                                tmplenr := lenr
                              else
                                tmplenr := Copy (lenr, 1, Length (lenr) - 1) + chr (i + ord ('0'));

                              lestr := FormatStr (tmplenr,-9,'0');
                              checkch := GetLELPCheckChar ('1'+lestr);
                            end;

                            if (fLager = -1) then
                              paramstr [pcount] := 'LT_NAME:'+LTComboBox.GetItemText (LTComboBox.ItemIndex, 1)
                            else
                              paramstr [pcount] := 'LT_NAME:'+LTComboBox.GetItemText (LTComboBox.ItemIndex, 0);

                            Inc (pcount);

                            if (fachanz > 1) then
                              paramstr [pcount] := 'FACH_ANZAHL:'+IntToStr (fachanz)
                            else if (FachEdit.Enabled) then
                              paramstr [pcount] := 'FACH_ANZAHL:'+IntToStr (fachanz)
                            else
                              paramstr [pcount] := 'FACH_ANZAHL:';
                            Inc (pcount);

                            paramstr [pcount] := 'MASTER_LE_NR:'+lenr;
                            Inc (pcount);
                            paramstr [pcount] := 'FACH_NR:'+IntToStr (i);
                            Inc (pcount);

                            paramstr [pcount] := 'LE_NAME:';
                            Inc (pcount);

                            if (LESerialRadioButton.Checked) then begin
                              paramstr [pcount] := 'LT_SERIAL:'+sernr;
                              Inc (pcount);

                              paramstr [pcount] := 'BARCODE:' + '8004'+sernr;
                              Inc (pcount);
                              paramstr [pcount] := 'BARCODE_TEXT:' + '(8004)'+sernr;
                              Inc (pcount);
                            end else if (LEPackAutomatRadioButton.Checked) then begin
                              paramstr [pcount] := 'LE_NR:'+lenr;
                              Inc (pcount);

                              paramstr [pcount] := 'BARCODE:' + lenr;
                              Inc (pcount);
                              paramstr [pcount] := 'BARCODE_TEXT:' + lenr;
                              Inc (pcount);
                            end else begin
                              paramstr [pcount] := 'LE_NR:'+tmplenr;
                              Inc (pcount);

                              paramstr [pcount] := 'BARCODE:' + '1'+tmplenr+checkch;
                              Inc (pcount);
                              paramstr [pcount] := 'BARCODE_TEXT:' + '1 '+tmplenr+' '+checkch;
                              Inc (pcount);

                              if LEDMRadioButton.Checked then
                                paramstr [pcount] := 'BARCODE_2D:' + 'sl'+'1'+tmplenr+checkch+'sl'
                              else if LEQRRadioButton.Checked then
                                paramstr [pcount] := 'BARCODE_2D:' + 'slx'+'1'+tmplenr+checkch;
                              Inc (pcount);
                            end;

                            paramstr [pcount] := 'KOPIEN:'+GetCopySequenz (1);
                            Inc (pcount);

                            if (forminfo.Generator = 'CR') then begin
                              for c := Low (paramstr) to High (paramstr) do
                                paramstru [c] := paramstr [c];

                              res := PrintModule.PrintFormular ('', PortArray [PrinterComboBox.ItemIndex].Port, '', fMandant, fLager, art, forminfo, paramstru, PrtPreviewCheckBox.Checked, errtext);
                            end else begin
                              if (i = 0) then begin
                                //Das Hauptlabel drucken
                                for c := 0 to LEKopienComboBox.ItemIndex do
                                  res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext)
                              end else begin
                                //Die Fächer drucken
                                for c := 0 to LEKopienComboBox.ItemIndex do
                                  res := PrintTemplate (LabelTemplatePath+subforminfo.FormularName, paramstr, errtext);
                              end;
                            end;

                            if (res <> 0) then
                              break;
                          end;

                          if (forminfo.Generator <> 'CR') then
                            EndPrinting
                          else begin
                            if (res <> 0) Then
                              MessageDlg(FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
                            else if PrtPreviewCheckBox.Checked then
                              PrintModule.BeginPreview;
                          end;
                        end;
                      end;

                      Inc (count);

                      if Assigned (verlauf) then begin
                        verlauf.ProgressBar1.Position := count;

                        //Sonst kann man nicht abbrechen
                        Application.ProcessMessages;

                        if verlauf.AbortFlag then
                          stop := true
                        else
                          Sleep (1000);
                          //Sleep (250);
                      end;
                    end;
                  finally
                    if Assigned (verlauf) then
                      verlauf.EndShowModal;
                  end;
                end;
              end;
            end;

            ADOQuery1.Close;
          except
            errtext := FormatMessageText (1101, []);
          end;

          ClosePrinterPort;
        end;
      end;
    end else if (PageControl1.ActivePage = GebindeTabSheet) then begin
      altcursor := Screen.Cursor;
      Screen.Cursor := crHourGlass;

      try
        with PortArray [PrinterComboBox.ItemIndex] do
          res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);
      finally
        Screen.Cursor := altcursor;
      end;

      if (res <> 0) then
        MessageDlg (FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
      else begin
        with PortArray [PrinterComboBox.ItemIndex] do begin
          res := DetectFormular (fMandant, fLager, '', '', Model, 'GB-LABEL', forminfo)
        end;

        if (res <> 0) Then
          errtext := FormatMessageText (1083, [PortArray [PrinterComboBox.ItemIndex].Model, 'GB-LABEL'])
        else begin
          res := 0;
          stop := False;

          if (GBNeuRadioButton.Checked) then begin
            if not (TryStrToInt (GBCountEdit.Text, leanz)) then
              stop := True
            else if (leanz > 99) then
              leanz := 99;
          end else if (LEReprintRadioButton.Checked) then begin
            if not (TryStrToInt (GBVonEdit.Text, levon)) then
              stop := True
            else if not (TryStrToInt (GBBisEdit.Text, lebis)) then
              stop := True
            else if (levon > lebis) then
              stop := True
            else if ((lebis - levon) > 99) then
              lebis := levon + 99;
          end else begin
            stop := True;
            MessageDlg(FormatMessageText (1093, []), mtError, [mbOK], 0)
          end;

          dbres := 0;
          count := 0;

          while not (stop) and (res = 0) and (dbres = 0) do begin
            if (GBNeuRadioButton.Checked) then begin
              if (count >= leanz) then
                stop := true
              else begin
                dbres := GetConfigSequenzNummer (-1, fLocation, fLager, 'GB_NUMMER', lenr);

                if (dbres <> 0) then
                  MessageDlg(FormatMessageText (1385, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
                else if (Length (lenr) = 0) then begin
                  dbres := 9;
                  MessageDlg(FormatMessageText (1084, ['GB_NUMMER']), mtError, [mbOK], 0)
                end;
              end;
            end else if (GBReprintRadioButton.Checked) then begin
              if ((levon + count) > lebis) then
                stop := true
              else
                lenr := IntToStr (levon + count);
            end else
              stop := True;

            if (res = 0) and (dbres = 0) and not (stop) then begin
              res := BeginPrinting('GB Label',errtext);

              if (res = 0) then begin
                pcount := 0;

                lestr := FormatStr (lenr,-6,'0');
                checkch := GetLELPCheckChar ('50'+lestr);

                paramstr [pcount] := 'GB_NR:'+lenr;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE:' + '50'+lestr+checkch;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE_TEXT:' + '50 '+lestr+' '+checkch;
                Inc (pcount);
                paramstr [pcount] := 'KOPIEN:'+GetCopySequenz (1);
                Inc (pcount);

                res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

                EndPrinting;
              end;
            end;

            Inc (count);
          end;
        end;

        ClosePrinterPort;
      end;
    end else if (PageControl1.ActivePage = DocIDTabSheet) then begin
      altcursor := Screen.Cursor;
      Screen.Cursor := crHourGlass;

      try
        with PortArray [PrinterComboBox.ItemIndex] do
          res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);
      finally
        Screen.Cursor := altcursor;
      end;

      if (res <> 0) then
        MessageDlg (FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
      else begin
        with PortArray [PrinterComboBox.ItemIndex] do begin
          res := DetectFormular (fMandant, fLager, '', '', Model, 'DOCID-LABEL', forminfo)
        end;

        if (res <> 0) Then
          errtext := FormatMessageText (1083, [PortArray [PrinterComboBox.ItemIndex].Model, 'DOCID-LABEL'])
        else begin
          res := 0;
          stop := False;

          if not (TryStrToInt (DocIDCountEdit.Text, leanz)) then
            stop := True;

          dbres := 0;
          count := 0;

          while not (stop) and (res = 0) and (dbres = 0) do begin
            if (count >= leanz) then
              stop := true
            else begin
              dbres := GetConfigSequenzNummer (fMandant, fLocation, fLager, 'DOCID_NUMMER', lenr);

              if (dbres <> 0) then
                MessageDlg(FormatMessageText (1386, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
              else if (Length (lenr) = 0) then begin
                dbres := 9;
                MessageDlg(FormatMessageText (1084, ['DOCID_NUMMER']), mtError, [mbOK], 0)
              end;
            end;

            if (res = 0) and (dbres = 0) and not (stop) then begin
              res := BeginPrinting('DocID Label',errtext);

              if (res = 0) then begin
                pcount := 0;

                lestr := FormatStr (lenr, -8, '0');
                checkch := GetLELPCheckChar ('51'+lestr);

                paramstr [pcount] := 'DOCID_TEXT:'+DocIDTextEdit.Text;
                Inc (pcount);
                paramstr [pcount] := 'DOCID_NR:'+lenr;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE:' + '51'+lestr+checkch;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE_TEXT:' + '51 '+lestr+' '+checkch;
                Inc (pcount);
                paramstr [pcount] := 'KOPIEN:'+GetCopySequenz (1);
                Inc (pcount);

                res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

                EndPrinting;
              end;
            end;

            Inc (count);
          end;
        end;

        ClosePrinterPort;
      end;
    end else if (PageControl1.ActivePage = LBZonenTabSheet) then begin
      altcursor := Screen.Cursor;
      Screen.Cursor := crHourGlass;

      try
        with PortArray [PrinterComboBox.ItemIndex] do
          res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);
      finally
        Screen.Cursor := altcursor;
      end;

      if (res <> 0) then
        MessageDlg (FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
      else begin
        if (fRefLBZone > 0) then
          art := 'LBZONE-LABEL'
        else art := 'LB-LABEL';

        with PortArray [PrinterComboBox.ItemIndex] do begin
          res := DetectFormular (fMandant, fLager, '', '', Model, art, forminfo)
        end;

        if (res <> 0) Then
          errtext := FormatMessageText (1083, [PortArray [PrinterComboBox.ItemIndex].Model, art])
        else begin
          if (forminfo.Generator = 'CR') then begin
            if (fRefLBZone > 0) then
              res := PrintModule.PrintFormular ('', PortArray [PrinterComboBox.ItemIndex].Port, '', fMandant, fLager, art, forminfo, ['REF:'+IntToStr (fRefLBZone)], PrtPreviewCheckBox.Checked, errtext)
            else
              res := PrintModule.PrintFormular ('', PortArray [PrinterComboBox.ItemIndex].Port, '', fMandant, fLager, art, forminfo, ['REF:'+IntToStr (fRefLB)], PrtPreviewCheckBox.Checked, errtext);

            if (res <> 0) Then
              MessageDlg(FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
            else if PrtPreviewCheckBox.Checked then
              PrintModule.BeginPreview;
          end else begin
            if (fRefLBZone > 0) then begin
              res := BeginPrinting('LB Zonen Label',errtext);

              if (res = 0) then begin
                pcount := 0;

                lestr := FormatStr (ZonenNrLabel.Caption,-8,'0');
                checkch := GetLELPCheckChar ('91'+lestr);

                paramstr [pcount] := 'LB_NAME:'+LBNameLabel.Caption;
                Inc (pcount);
                paramstr [pcount] := 'ZONEN_NAME:'+ZonenNameLabel.Caption;
                Inc (pcount);
                paramstr [pcount] := 'ZONEN_NR:'+ZonenNrLabel.Caption;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE:' + '91'+lestr+checkch;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE_TEXT:' + '91 '+lestr+' '+checkch;
                Inc (pcount);
                paramstr [pcount] := 'KOPIEN:'+GetCopySequenz (1);
                Inc (pcount);

                res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

                EndPrinting;
              end;
            end else begin
              res := BeginPrinting('LB Label',errtext);

              if (res = 0) then begin
                pcount := 0;

                lestr := LBShortNameLabel.Caption;
                checkch := GetLELPCheckChar ('92'+lestr);

                paramstr [pcount] := 'LB_NAME:'+LBNameLabel.Caption;
                Inc (pcount);
                paramstr [pcount] := 'LB_SHORT_NAME:'+LBShortNameLabel.Caption;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE:' + '92'+lestr+checkch;
                Inc (pcount);
                paramstr [pcount] := 'BARCODE_TEXT:' + '92 '+lestr+' '+checkch;
                Inc (pcount);
                paramstr [pcount] := 'KOPIEN:'+GetCopySequenz (1);
                Inc (pcount);

                res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

                EndPrinting;
              end;
            end;
          end;
        end;

        ClosePrinterPort;
      end;
    end else if (PageControl1.ActivePage = PrinterTabSheet) then begin
      altcursor := Screen.Cursor;
      Screen.Cursor := crHourGlass;

      try
        with PortArray [PrinterComboBox.ItemIndex] do
          res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);
      finally
        Screen.Cursor := altcursor;
      end;

      if (res <> 0) then
        MessageDlg (FormatMessageText (1361, [errtext]), mtError, [mbOK], 0)
      else begin
        with PortArray [PrinterComboBox.ItemIndex] do begin
          res := DetectFormular (fMandant, fLager, '', '', Model, 'PRINTERNAME', forminfo)
        end;

        if (res <> 0) Then
          errtext := FormatMessageText (1083, [PortArray [PrinterComboBox.ItemIndex].Model, 'PRINTERNAME'])
        else begin
          res := BeginPrinting('Name Label',errtext);

          if (res = 0) then begin
            pcount := 0;

            paramstr [pcount] := 'PRINTER_NAME:'+PrinterNameLabel.Caption;
            Inc (pcount);
            paramstr [pcount] := 'PRINTER_NUMBER:'+PrinterNumberLabel.Caption;
            Inc (pcount);
            paramstr [pcount] := 'PRINTER_DESC:'+PrinterDescLabel.Caption;
            Inc (pcount);
            paramstr [pcount] := 'PORT_NAME:'+PrinterPortLabel.Caption;
            Inc (pcount);
            paramstr [pcount] := 'BARCODE:' + 'PRT-'+PrinterNumberLabel.Caption;
            Inc (pcount);
            paramstr [pcount] := 'BARCODE_TEXT:' + 'PRT-'+PrinterNumberLabel.Caption;
            Inc (pcount);
            paramstr [pcount] := 'KOPIEN:'+GetCopySequenz (1);
            Inc (pcount);

            res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramstr, errtext);

            EndPrinting;
          end;
        end;

        ClosePrinterPort;
      end;
    end;

    if Assigned (verlauf) then
      verlauf.Release;

    verlauf := Nil;

    if (res <> 0) or (Length (errtext) > 0) Then
      MessageDlg(FormatMessageText (1360, [errtext]), mtError, [mbOK], 0)
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 20.07.2023
//******************************************************************************
//* Description  : PrtPreviewCheckBox nur bei Windowsdruckern Enabled
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.PrinterComboBoxChange(Sender: TObject);
begin
  if (PrinterComboBox.ItemIndex < 0) or (PrinterComboBox.ItemIndex >= PrinterComboBox.Items.Count) then
    PrtPreviewCheckBox.Enabled := false
  else
    PrtPreviewCheckBox.Enabled := (PortArray [PrinterComboBox.ItemIndex].PrtTyp = 'LASER');
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.LPExportButtonClick(Sender: TObject);
var
  idx,
  res,
  koordsize : Integer;
  lpstr,
  barcode   : String;
  outstr    : AnsiString;
  checkch   : AnsiChar;
  expfile   : Textfile;
begin
  res := 0;

  if (SaveDialog1.Execute) then begin
    Screen.Cursor := crHourGlass;

    try
      AssignFile (expfile, SaveDialog1.FileName);

      Rewrite (expfile);

      if (IOResult <> 0) then
        MessageDLG (FormatMessageText (1387, ['', SaveDialog1.FileName]), mtError, [mbOK], 0)
      else begin
        WriteLn (expfile, 'Regal;Feld;Platz;Ebene;Prüfziffer;Barcode;Platz-Nr.;Koordinaten;Bezeichnung;Bereich;Zone;Barcode2D');

        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ('select lp.REF_LAGER,lp.LAGER,lp.NAME,lp.LP_NR,lp.REIHE,lp.FELD,lp.PLATZ,lp.EBENE,lp.CHECK_DIGITS,lb.SHORT_NAME,lbz.NAME,lp.BARCODE_ID');
        ADOQuery1.SQL.Add ('from V_LP lp inner join V_LB lb on (lb.REF=lp.REF_LB) left outer join V_LB_ZONE lbz on (lbz.REF=lp.REF_LB_ZONE) where lp.REF=:ref');

        Screen.Cursor := crSQLWait;

        try
          idx := 0;
          while (idx < LPList.Count) and (res = 0) do begin
            ADOQuery1.Parameters.ParamByName('ref').Value := StrToInt (LPList.Strings [idx]);

            try
              ADOQuery1.Open;

              checkch := #0;

              koordsize := 2;

              if (LPArtRadioGroup.ItemIndex = 1) or (LPArtRadioGroup.ItemIndex = 2) then begin
                if (ADOQuery1.FieldByName ('FELD').AsInteger > 99) or (ADOQuery1.FieldByName ('PLATZ').AsInteger > 99) or (ADOQuery1.FieldByName ('EBENE').AsInteger > 99) then
                  koordsize := 3;
              end;

              if (LPArtRadioGroup.ItemIndex = 0) then begin
                if (Length (ADOQuery1.FieldByName ('LP_NR').AsString) > 9) then
                  lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-11,'0')
                else if (Length (ADOQuery1.FieldByName ('LP_NR').AsString) > 7) then
                  lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-9,'0')
                else
                  lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-7,'0');

                checkch := GetLELPCheckChar ('2'+lpstr);
              end else if (LPArtRadioGroup.ItemIndex = 1) then begin
                lpstr := FormatStr (ADOQuery1.Fields [4].AsString,-3,'0')+FormatStr (ADOQuery1.Fields [5].AsString,koordsize*-1,'0')+FormatStr (ADOQuery1.Fields [6].AsString,koordsize*-1,'0');
                checkch := GetLELPCheckChar ('3'+lpstr);
              end else if (LPArtRadioGroup.ItemIndex = 2) then begin
                lpstr := FormatStr (ADOQuery1.Fields [4].AsString,-3,'0')+FormatStr (ADOQuery1.Fields [5].AsString,koordsize*-1,'0')+FormatStr (ADOQuery1.Fields [6].AsString,koordsize*-1,'0')+FormatStr (ADOQuery1.Fields [7].AsString,koordsize*-1,'0');
                checkch := GetLELPCheckChar ('4'+lpstr);
              end else if (LPArtRadioGroup.ItemIndex = 3) then begin
                if (Length (ADOQuery1.FieldByName ('LP_NR').AsString) > 9) then
                  lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-11,'0')
                else if (Length (ADOQuery1.FieldByName ('LP_NR').AsString) > 7) then
                  lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-9,'0')
                else
                  lpstr := FormatStr (ADOQuery1.FieldByName ('LP_NR').AsString,-7,'0');

                checkch := GetLELPCheckChar ('2'+lpstr);
              end;

              outstr := '"'+FormatStr (ADOQuery1.Fields [4].AsString,-3,'0')+'";"'+FormatStr (ADOQuery1.Fields [5].AsString,koordsize*-1,'0')+'";"'+FormatStr (ADOQuery1.Fields [6].AsString,koordsize*-1,'0')+'";"'+FormatStr (ADOQuery1.Fields [7].AsString,koordsize*-1,'0')+'"';

              outstr := outstr + ';'+ADOQuery1.Fields [8].AsString;

              if (LPArtRadioGroup.ItemIndex = 4) then begin
                outstr := outstr + ';"'+ADOQuery1.FieldByName('BARCODE_ID').AsString+'"';
              end else if (LPArtRadioGroup.ItemIndex = 2) then begin
                outstr := outstr + ';"'+'4'+lpstr+checkch+'"';
              end else if (LPArtRadioGroup.ItemIndex = 1) then begin
                outstr := outstr + ';"'+'3'+lpstr+checkch+'"';
              end else begin
                outstr := outstr + ';"'+'2'+lpstr+checkch+'"';
              end;

              outstr := outstr + ';"'+ADOQuery1.Fields [3].AsString+'"';

              outstr := outstr + ';"'+FormatStr (ADOQuery1.Fields [4].AsString,-3,'0')+'-'+FormatStr (ADOQuery1.Fields [5].AsString,koordsize*-1,'0')+'-'+FormatStr (ADOQuery1.Fields [6].AsString,koordsize*-1,'0')+'-'+FormatStr (ADOQuery1.Fields [7].AsString,koordsize*-1,'0')+'"';

              outstr := outstr + ';"'+ADOQuery1.Fields [2].AsString+'"' + ';"'+ADOQuery1.Fields [9].AsString+'"' + ';"'+ADOQuery1.Fields [10].AsString+'"';

              if (LPArtRadioGroup.ItemIndex = 4) then begin
                outstr := outstr + ';"' + ADOQuery1.FieldByName('BARCODE_ID').AsString+'"';
              end else begin
                if (LPArtRadioGroup.ItemIndex = 2) then begin
                  barcode := '4'+lpstr+checkch;
                end else if (LPArtRadioGroup.ItemIndex = 1) then begin
                  barcode := '3'+lpstr+checkch;
                end else begin
                  barcode := '2'+lpstr+checkch;
                end;

                if LPDMRadioButton.Checked then
                  outstr := outstr + ';"' + 'sl'+barcode+'sl'+'"'
                else if LPQRRadioButton.Checked then
                  outstr := outstr + ';"' + 'slx'+barcode+'"'
                else
                  outstr := outstr + ';';
              end;

              WriteLn (expfile, outstr);

              if (IOResult <> 0) then
                res := -2;

              ADOQuery1.Close;
            except
            end;

            Inc (idx);
          end;
        finally
          Screen.Cursor := crDefault;
        end;

        if (res <> 0) then
          MessageDLG (FormatMessageText (1388, ['', SaveDialog1.FileName]), mtError, [mbOK], 0);

        Closefile (expfile);
      end;
    finally
      Screen.Cursor := crDefault;
    end;
  end;
end;

procedure TPrintLPLableForm.LPITFRadioButtonClick(Sender: TObject);
begin
  I2F5CheckCheckBox.Enabled := LPITFRadioButton.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.LTComboBoxChange(Sender: TObject);
begin
  FachEdit.Enabled := false;

  if (GetComboBoxRef (LTComboBox) <> -1) then begin
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_LT_NUMBER_RANGE where REF_LT_TYP='+GetComboBoxRefStr (LTComboBox));

    try
      ADOQuery1.Open;

      fLEPrefix := ADOQuery1.FieldByName ('PREFIX').AsString;

      if not (ADOQuery1.FieldByName ('PREFIX').IsNull) and (copy (ADOQuery1.FieldByName ('PREFIX').AsString,1,1) = '9') then
        FachEdit.Enabled := True;

      ADOQuery1.Close;
    except
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.LECountEditChange(Sender: TObject);
begin
  if (Length (LECountEdit.Text) > 0) then
    LENeuRadioButton.Checked := True
end;

procedure TPrintLPLableForm.LECountEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,^C,^V,'0'..'9']) then begin
    Key := #0;
    Beep;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.LERadioButtonClick(Sender: TObject);
begin
  LECountEdit.Enabled := LENeuRadioButton.Checked;

  LEVonEdit.Enabled   := LEReprintRadioButton.Checked;
  LEBisEdit.Enabled   := LEReprintRadioButton.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 24.09.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.LETabSheetShow(Sender: TObject);
begin
(*
Palette
KLT
KLT groß

Nur Typ
EAN


Seriennr.
Komm
*)

  //SetRadiogroupHints(LEArtRadioGroup, ['Label für eine Palette', 'Label für einen Kleinbehälter', 'Grosses Label','','Barcode für den LT-Typen','Barcode mit dem EAN des LTs']);

  if LTComboBox.Enabled and (GetComboBoxRef (LTComboBox) = -1) then
    LTComboBox.SetFocus
  else if (LENeuRadioButton.Checked) then
    LECountEdit.SetFocus
  else if (LEReprintRadioButton.Checked) then
    LEVonEdit.SetFocus;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.LEVonEditChange(Sender: TObject);
begin
  if (Length (LEVonEdit.Text) > 0) then
    LEReprintRadioButton.Checked := True
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.LECSVButtonClick(Sender: TObject);
var
  res     : Integer;
  checkch : AnsiChar;
  expfile : Textfile;
  stop      : Boolean;
  dbres,
  count,
  leanz,
  levon,
  lebis     : Integer;
  lenr,
  lestr     : String;
begin
  res := 0;

  if (SaveDialog1.Execute) then begin
    AssignFile (expfile, SaveDialog1.FileName);

    Rewrite (expfile);

    if (IOResult <> 0) then
      MessageDLG (FormatMessageText (1387, ['', SaveDialog1.FileName]), mtError, [mbOK], 0)
    else begin
      WriteLn (expfile, 'LT-Type;LE-Nr;Barcode;Barcode Text');

      res := 0;
      stop := False;

      if (LENeuRadioButton.Checked) then begin
        if not (TryStrToInt (LECountEdit.Text, leanz)) then
          stop := True;
      end else if (LEReprintRadioButton.Checked) then begin
        if not (TryStrToInt (LEVonEdit.Text, levon)) then
          stop := True
        else if not (TryStrToInt (LEBisEdit.Text, lebis)) then
          stop := True;
      end else begin
        stop := True;
        MessageDlg(FormatMessageText (1093, []), mtError, [mbOK], 0)
      end;

      dbres := 0;
      count := 0;

      while not (stop) and (res = 0) and (dbres = 0) do begin
        if (LENeuRadioButton.Checked) then begin
          if (count >= leanz) then
            stop := true
          else begin
            dbres := CreateLTNummer (fLocation, fLager, GetComboBoxRef (LTComboBox), lenr);

            if (dbres <> 0) then
              MessageDlg('Fehler beim Erzeugen der Gebinde-Nummer'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
          end;
        end else if (LEReprintRadioButton.Checked) then begin
          if ((levon + count) > lebis) then
            stop := true
          else
            lenr := IntToStr (levon + count);
        end else
          stop := True;

        if (res = 0) and (dbres = 0) and not (stop) then begin
          lestr := FormatStr (lenr,-9,'0');
          checkch := GetLELPCheckChar ('1'+lestr);

          if (fLager = -1) then
            Write (expfile, '"'+LTComboBox.GetItemText (LTComboBox.ItemIndex, 1)+'"')
          else
            Write (expfile, '"'+LTComboBox.GetItemText (LTComboBox.ItemIndex, 0)+'"');

          Write (expfile, ';'+'"'+lenr+'"');
          Write (expfile, ';'+'"'+'1'+lestr+checkch+'"');
          Write (expfile, ';'+'"'+'1 '+lestr+' '+checkch+'"');

          WriteLn (expfile);

          if (IOResult <> 0) then
            res := -2;
        end;

        Inc (count);
      end;

      if (res <> 0) then
        MessageDLG (FormatMessageText (1388, ['', SaveDialog1.FileName]), mtError, [mbOK], 0);

      Closefile (expfile);
    end;
  end;
end;

procedure TPrintLPLableForm.LEITFRadioButtonClick(Sender: TObject);
begin
  LEI2F5CheckCheckBox.Enabled := LEITFRadioButton.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.FormClose(Sender: TObject; var Action: TCloseAction);
var
  idx : Integer;
begin
  ClearComboBoxObjects (LTComboBox);

  if (PageControl1.ActivePage = LPTabSheet) then begin
    UserReg.WriteRegValue('TPrintLPLableForm.LPArtRadioGroup', LPArtRadioGroup.ItemIndex);

    if LPITFRadioButton.Checked then
      idx := 0
    else if LP128RadioButton.Checked then
      idx := 1
    else if LPQRRadioButton.Checked then
      idx := 2
    else if LPDMRadioButton.Checked then
      idx := 3
    else
      idx := 0;

    UserReg.WriteRegValue('TPrintLPLableForm.LPBarcodeType', idx);
  end else if (PageControl1.ActivePage = LETabSheet) then begin
    if LEPalRadioButton.Checked then
      idx := 0
    else if LEKLTRadioButton.Checked then
      idx := 1
    else if LEKLTBigRadioButton.Checked then
      idx := 2
    else if LETypRadioButton.Checked then
      idx := 3
    else if LEEANRadioButton.Checked then
      idx := 4
    else if LESerialRadioButton.Checked then
      idx := 5
    else if LEKommRadioButton.Checked then
      idx := 6
    else if LELeerRadioButton.Checked then
      idx := 7
    else if LELeer5RadioButton.Checked then
      idx := 8
    else if LELeer10RadioButton.Checked then
      idx := 9
    else if LEPackAutomatRadioButton.Checked then
      idx := 10
    else
      idx := -1;

    if (idx <> -1) then
      UserReg.WriteRegValue('TPrintLPLableForm.LEArtRadioGroup', idx);

    if LEITFRadioButton.Checked then
      idx := 0
    else if LE128RadioButton.Checked then
      idx := 1
    else if LEQRRadioButton.Checked then
      idx := 2
    else if LEDMRadioButton.Checked then
      idx := 3
    else
      idx := 0;

    UserReg.WriteRegValue('TPrintLPLableForm.LEBarcodeType', idx);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.FormCreate(Sender: TObject);
begin
  LPList := TStringList.Create;

  LPTabSheet.TabVisible       := false;
  KommLPTabSheet.TabVisible   := false;
  RelationTabSheet.TabVisible := false;
  LETabSheet.TabVisible       := false;
  LBZonenTabSheet.TabVisible  := false;
  GebindeTabSheet.TabVisible  := false;
  DocIDTabSheet.TabVisible    := false;
  PrinterTabSheet.TabVisible  := false;

  RelAnzahlUpDown.Position := 1;

  DocIDCountEdit.Text := '0';
  DocIDTextEdit.Text  := '';

  PrinterNameLabel.Caption := '';
  PrinterDescLabel.Caption := '';
  PrinterPortLabel.Caption := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, PrinterComboBox);
    LVSSprachModul.SetNoTranslate (Self, LTComboBox);
    LVSSprachModul.SetNoTranslate (Self, LEKopienComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (LPLayoutComboBox);
  ClearComboBoxObjects (PrinterComboBox);

  if Assigned (LPList) then
    LPList.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.FormShow(Sender: TObject);
var
  intwert : Integer;
begin
  if (PageControl1.ActivePage = LPTabSheet) then begin
    if (UserReg.ReadRegValue('TPrintLPLableForm.LPArtRadioGroup', intwert) = 0) then
      LPArtRadioGroup.ItemIndex := intwert;

    if (UserReg.ReadRegValue('TPrintLPLableForm.LPBarcodeType', intwert) = 0) then begin
      if (intwert = 0) then
        LPITFRadioButton.Checked := true
      else if (intwert = 1) then
        LP128RadioButton.Checked := true
      else if (intwert = 2) then
        LPQRRadioButton.Checked := true
      else if (intwert = 3) then
        LPDMRadioButton.Checked := true
      else
        LPITFRadioButton.Checked := true
    end;

    ClearComboBoxObjects (LPLayoutComboBox);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_PRT_LAYOUT where LAYOUT_ART=''LP'' and (REF_MAND is null or REF_MAND='+IntToStr (fMandant)+')');

    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      LPLayoutComboBox.AddItem (ADOQuery1.FieldByName ('NAME').AsString+'|'+ADOQuery1.FieldByName ('BESCHREIBUNG').AsString, TComboboxRef.Create (ADOQuery1.FieldByName ('REF').AsInteger));

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;

    if (LPLayoutComboBox.Items.Count = 0) then begin
      Label41.Visible := False;
      LPLayoutComboBox.Visible := False;
    end else begin
      LPLayoutComboBox.Items.Insert (0, '');
      LPLayoutComboBox.ItemIndex := 0;
    end;

    if (LPList.Count = 1) Then begin
      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select * from V_LP where REF=:ref');
      ADOQuery1.Parameters.ParamByName('ref').Value := StrToInt (LPList.Strings [0]);

      try
        ADOQuery1.Open;

        fLager   := ADOQuery1.FieldByName ('REF_LAGER').AsInteger;

        LPLBNameLabel.Caption := ADOQuery1.FieldByName ('NAME').AsString;
        if not (ADOQuery1.FieldByName ('SHORT_BEREICH').IsNull) then
          LPLBNameLabel.Caption := LPLBNameLabel.Caption + ' ('+ADOQuery1.FieldByName ('SHORT_BEREICH').AsString+')';

        LPLBZoneNameLabel.Caption   := ADOQuery1.FieldByName ('BEREICH_ZONE').AsString;

        LPNameLabel.Caption   := ADOQuery1.FieldByName ('NAME').AsString;
        LPNrLabel.Caption     := ADOQuery1.FieldByName ('LP_NR').AsString;
        LPKoorLabel.Caption   := ADOQuery1.FieldByName ('LP_KOOR').AsString;
        LPCheckLabel.Caption  := ADOQuery1.FieldByName ('CHECK_DIGITS').AsString;

        ADOQuery1.Close;
      except
      end;
    end else if (LPList.Count > 1) Then begin
      LPNameLabel.Caption   := GetResourceText (1514);
      LPNrLabel.Caption     := GetResourceText (1514);
      LPKoorLabel.Caption   := GetResourceText (1514);
      LPCheckLabel.Caption  := GetResourceText (1514);

      LPLBNameLabel.Caption     := '';
      LPLBZoneNameLabel.Caption := '';
    end;
  end else if (PageControl1.ActivePage = LBZonenTabSheet) then begin
    if (fRefLBZone > 0) then begin
      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select lb.NAME,lb.SHORT_NAME,lbz.NAME,lbz.ZONEN_NR from V_LB lb, V_LB_ZONE lbz where lb.REF=lbz.REF_LB and lbz.REF=:ref');
      ADOQuery1.Parameters.ParamByName('ref').Value := fRefLBZone;

      try
        ADOQuery1.Open;

        LBNameLabel.Caption      := ADOQuery1.Fields [0].AsString;
        LBShortNameLabel.Caption := ADOQuery1.Fields [1].AsString;
        ZonenNameLabel.Caption   := ADOQuery1.Fields [2].AsString;
        ZonenNrLabel.Caption     := ADOQuery1.Fields [3].AsString;

        ADOQuery1.Close;
      except
      end;
    end else begin
      Label26.Visible := False;
      Label28.Visible := False;
      ZonenNameLabel.Visible := False;
      ZonenNrLabel.Visible := False;

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select lb.NAME,lb.SHORT_NAME from V_LB lb where lb.REF=:ref');
      ADOQuery1.Parameters.ParamByName('ref').Value := fRefLB;

      try
        ADOQuery1.Open;

        LBNameLabel.Caption      := ADOQuery1.Fields [0].AsString;
        LBShortNameLabel.Caption := ADOQuery1.Fields [1].AsString;

        ADOQuery1.Close;
      except
      end;
    end;
  end else if (PageControl1.ActivePage = LETabSheet) then begin
    if (UserReg.ReadRegValue('TPrintLPLableForm.LEArtRadioGroup', intwert) = 0) then begin
      if (intwert = 0) then
        LEPalRadioButton.Checked := True
      else if (intwert = 1) then
        LEKLTRadioButton.Checked := True
      else if (intwert = 2) then
        LEKLTBigRadioButton.Checked := True
      else if (intwert = 3) then
        LETypRadioButton.Checked := True
      else if (intwert = 4) then
        LEEANRadioButton.Checked := True
      else if (intwert = 5) then
        LESerialRadioButton.Checked := True
      else if (intwert = 6) then
        LEKommRadioButton.Checked := True
      else if (intwert = 7) then
        LELeerRadioButton.Checked := True
      else if (intwert = 8) then
        LELeer5RadioButton.Checked := True
      else if (intwert = 9) then
        LELeer10RadioButton.Checked := True
      else if (intwert = 10) then
        LEPackAutomatRadioButton.Checked := True;
    end;

    if (UserReg.ReadRegValue('TPrintLPLableForm.LEBarcodeType', intwert) = 0) then begin
      if (intwert = 0) then
        LEITFRadioButton.Checked := true
      else if (intwert = 1) then
        LE128RadioButton.Checked := true
      else if (intwert = 2) then
        LEQRRadioButton.Checked := true
      else if (intwert = 3) then
        LEDMRadioButton.Checked := true
      else
        LEITFRadioButton.Checked := true
    end;

    LTComboBoxChange (Sender);
  end else if (PageControl1.ActivePage = KommLPTabSheet) then begin
    if (LPList.Count = 1) Then begin
      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select * from V_KOMM_LP_ZUORDNUNG where REF_KOMM_LP='+LPList.Strings [0]);

      if (fPlanung = -1) then
        ADOQuery1.SQL.Add ('and REF_PLANUNG is null')
      else
        ADOQuery1.SQL.Add ('and REF_PLANUNG='+IntToStr (fPlanung));

      ADOQuery1.SQL.Add ('order by REF_KOMM_REL');

      try
        ADOQuery1.Open;

        fMandant := ADOQuery1.FieldByName ('REF_AR_MAND').AsInteger;
        fLager   := ADOQuery1.FieldByName ('REF_LAGER').AsInteger;

        KommLPNrLabel.Caption   := ADOQuery1.FieldByName ('LP_NR').AsString;
        KommArNrLabel.Caption   := ADOQuery1.FieldByName ('ARTIKEL_NR').AsString;
        KommArTextLabel.Caption := ADOQuery1.FieldByName ('ARTIKEL_TEXT').AsString;
        KommArVPELabel.Caption  := ADOQuery1.FieldByName ('EINHEIT').AsString;
        KommArAZPLabel.Caption  := ADOQuery1.FieldByName ('AR_AZP').AsString;

        if Assigned (ADOQuery1.FindField('EAN')) then
          KommArEANLabel.Caption := ADOQuery1.FieldByName ('EAN').AsString
        else
          KommArEANLabel.Caption := '';

        if (ADOQuery1.RecordCount > 1) then begin
          ADOQuery1.Next;
          KommArSTVPELabel.Caption := ADOQuery1.FieldByName ('EINHEIT').AsString + ' = ' + ADOQuery1.FieldByName ('ARTIKEL_NR').AsString;
        end else
          KommArSTVPELabel.Caption := '';

        ADOQuery1.Close;
      except
      end;
    end else if (LPList.Count > 1) Then begin
      KommLPNrLabel.Caption    := GetResourceText (1514);
      KommArNrLabel.Caption    := GetResourceText (1514);
      KommArTextLabel.Caption  := GetResourceText (1514);
      KommArVPELabel.Caption   := GetResourceText (1514);
      KommArAZPLabel.Caption   := GetResourceText (1514);
      KommArSTVPELabel.Caption := GetResourceText (1514);
      KommArEANLabel.Caption   := GetResourceText (1514);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLPLableForm.GBNeuRadioButtonClick(Sender: TObject);
begin
  GBCountEdit.Enabled := GBNeuRadioButton.Checked;

  GBVonEdit.Enabled   := GBReprintRadioButton.Checked;
  GBBisEdit.Enabled   := GBReprintRadioButton.Checked;
end;

end.
