object FrontendMsgForm: TFrontendMsgForm
  Left = 369
  Top = 139
  BorderStyle = bsDialog
  Caption = 'Nachricht versenden'
  ClientHeight = 398
  ClientWidth = 569
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  DesignSize = (
    569
    398)
  PixelsPerInch = 96
  TextHeight = 13
  object Label2: TLabel
    Left = 8
    Top = 8
    Width = 120
    Height = 13
    Caption = 'Vordefinierte Nachrichten'
  end
  object DefMsgComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 553
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 120
    ItemHeight = 15
    TabOrder = 0
    OnChange = DefMsgComboBoxChange
  end
  object GroupBox1: TGroupBox
    Left = 8
    Top = 56
    Width = 553
    Height = 293
    Caption = 'Nachricht'
    TabOrder = 1
    DesignSize = (
      553
      293)
    object Label1: TLabel
      Left = 8
      Top = 18
      Width = 13
      Height = 13
      Caption = 'Art'
    end
    object Label3: TLabel
      Left = 8
      Top = 118
      Width = 82
      Height = 13
      Caption = 'Nachrichten-Text'
    end
    object Label4: TLabel
      Left = 8
      Top = 64
      Width = 112
      Height = 13
      Caption = 'Nachrichten-'#220'berschrift'
    end
    object Label5: TLabel
      Left = 368
      Top = 18
      Width = 38
      Height = 13
      Caption = 'Timeout'
    end
    object Label6: TLabel
      Left = 447
      Top = 38
      Width = 49
      Height = 13
      Caption = 'Sekunden'
    end
    object MsgArtComboBox: TComboBoxPro
      Left = 8
      Top = 34
      Width = 337
      Height = 19
      Style = csOwnerDrawFixed
      ColWidth = 80
      ItemHeight = 13
      TabOrder = 0
    end
    object MsgMemo: TMemo
      Left = 8
      Top = 136
      Width = 537
      Height = 145
      Anchors = [akLeft, akTop, akRight, akBottom]
      Lines.Strings = (
        'Memo1')
      TabOrder = 4
    end
    object MsgTitelEdit: TEdit
      Left = 8
      Top = 80
      Width = 537
      Height = 21
      TabOrder = 3
      Text = 'MsgTitelEdit'
    end
    object TimeoutEdit: TEdit
      Left = 368
      Top = 34
      Width = 56
      Height = 21
      TabOrder = 1
      Text = '0'
    end
    object TimeoutUpDown: TIntegerUpDown
      Left = 424
      Top = 34
      Width = 16
      Height = 21
      Associate = TimeoutEdit
      Max = 3600
      TabOrder = 2
    end
  end
  object OkButton: TButton
    Left = 401
    Top = 364
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 2
  end
  object AbortButton: TButton
    Left = 486
    Top = 364
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 3
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 512
    Top = 72
  end
end
