object LEEinlagernForm: TLEEinlagernForm
  Left = 318
  Top = 331
  BorderStyle = bsDialog
  Caption = 'Ladungstr'#228'ger einlagern'
  ClientHeight = 498
  ClientWidth = 402
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    402
    498)
  PixelsPerInch = 96
  TextHeight = 13
  object Label3: TLabel
    Left = 8
    Top = 208
    Width = 62
    Height = 13
    Caption = 'Lagerbereich'
  end
  object Label4: TLabel
    Left = 8
    Top = 256
    Width = 49
    Height = 13
    Caption = 'Lagerplatz'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 160
    Width = 385
    Height = 9
    Shape = bsTopLine
  end
  object Bevel2: TBevel
    Left = 8
    Top = 447
    Width = 385
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 344
  end
  object Label9: TLabel
    Left = 8
    Top = 168
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object LBComboBox: TComboBoxPro
    Left = 8
    Top = 224
    Width = 386
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 120
    DropDownCount = 16
    ItemHeight = 15
    TabOrder = 2
    OnChange = LBComboBoxChange
  end
  object LPListBox: TListBox
    Left = 8
    Top = 272
    Width = 385
    Height = 160
    Anchors = [akLeft, akTop, akRight, akBottom]
    ItemHeight = 13
    PopupMenu = LPListBoxPopupMenu
    TabOrder = 3
  end
  object OkButton: TButton
    Left = 232
    Top = 463
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 4
  end
  object AbortButton: TButton
    Left = 320
    Top = 463
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 8
    Width = 386
    Height = 146
    ActivePage = LETabSheet
    Anchors = [akLeft, akTop, akRight]
    MultiLine = True
    Style = tsFlatButtons
    TabOrder = 0
    object LETabSheet: TTabSheet
      Caption = 'LE'
      ExplicitLeft = 0
      ExplicitTop = 0
      ExplicitWidth = 0
      ExplicitHeight = 0
      DesignSize = (
        378
        115)
      object Label1: TLabel
        Left = 0
        Top = 8
        Width = 68
        Height = 13
        Caption = 'Ladungstr'#228'ger'
      end
      object Label5: TLabel
        Left = 176
        Top = 8
        Width = 89
        Height = 13
        Caption = 'Ladungstr'#228'ger-Typ'
      end
      object Label2: TLabel
        Left = 0
        Top = 48
        Width = 26
        Height = 13
        Caption = 'Inhalt'
      end
      object StaticText1: TStaticText
        Left = 0
        Top = 24
        Width = 165
        Height = 17
        AutoSize = False
        BevelInner = bvLowered
        BevelOuter = bvRaised
        BorderStyle = sbsSunken
        Caption = 'StaticText1'
        TabOrder = 0
      end
      object StaticText2: TStaticText
        Left = 176
        Top = 24
        Width = 199
        Height = 17
        Anchors = [akLeft, akTop, akRight]
        AutoSize = False
        BevelInner = bvLowered
        BevelOuter = bvRaised
        BorderStyle = sbsSunken
        Caption = 'StaticText1'
        TabOrder = 1
      end
      object ArtikelListBox: TListBox
        Left = 0
        Top = 64
        Width = 375
        Height = 48
        Anchors = [akLeft, akTop, akRight, akBottom]
        ItemHeight = 13
        TabOrder = 2
      end
    end
    object BesTabSheet: TTabSheet
      Caption = 'Bes'
      ImageIndex = 1
      ExplicitLeft = 0
      ExplicitTop = 0
      ExplicitWidth = 0
      ExplicitHeight = 0
      object Label6: TLabel
        Left = 8
        Top = 16
        Width = 41
        Height = 13
        Caption = 'Artikelnr.'
      end
      object Label7: TLabel
        Left = 8
        Top = 32
        Width = 46
        Height = 13
        Caption = 'Artikeltext'
      end
      object ArtikelNrLabel: TLabel
        Left = 88
        Top = 16
        Width = 81
        Height = 13
        Caption = 'ArtikelNrLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object ArtikelTextLabel: TLabel
        Left = 88
        Top = 32
        Width = 93
        Height = 13
        Caption = 'ArtikelTextLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object EinheitLabel: TLabel
        Left = 95
        Top = 93
        Width = 71
        Height = 13
        Caption = 'EinheitLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label11: TLabel
        Left = 8
        Top = 74
        Width = 79
        Height = 13
        Caption = 'Anzahl Einheiten'
      end
      object MengeEdit: TEdit
        Left = 8
        Top = 90
        Width = 65
        Height = 21
        TabOrder = 0
        Text = '1'
      end
      object MengeUpDown: TIntegerUpDown
        Left = 73
        Top = 90
        Width = 16
        Height = 21
        Associate = MengeEdit
        Min = 1
        Max = 100000
        Position = 1
        TabOrder = 1
      end
    end
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 184
    Width = 386
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 120
    ItemHeight = 15
    TabOrder = 1
    OnChange = LagerComboBoxChange
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 64
    Top = 344
  end
  object LPListBoxPopupMenu: TPopupMenu
    Left = 218
    Top = 346
    object LPSuchMenuItem: TMenuItem
      Caption = 'Suchen...'
      ShortCut = 16454
      OnClick = LPSuchMenuItemClick
    end
  end
end
