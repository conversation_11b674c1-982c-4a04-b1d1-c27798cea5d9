unit MailUtils;

interface

uses WinSock, OverbyteIcsSmtpProt, StdCtrls;

type
  TSmtpAutomat = class (TSmtpCli)
    private
    public
      procedure SmtpClientRequestDone(Sender : TObject; RqType : TSmtpRequest; Error  : Word);
  end;

function SendMail (const FromAdr, RecvAdr, Subject, MailText : String; AttFiles : array of String) : integer; overload;
function SendMail (const FromAdr, RecvAdr, CCAdr, BCCAdr, ReplayAdr, Subject, MailText : String; AttFiles : array of String) : integer; overload;

var
  SMTPMailServer : String;
  SMTPMailUser   : String = 'lvs';

implementation

uses
  {$ifdef Trace}
    Trace,
  {$endif}

  Forms, SysUtils, OverbyteIcsWSocket, ErrorTracking;

procedure TSmtpAutomat.SmtpClientRequestDone (Sender : TObject; RqType : TSmtpRequest; Error  : Word);
var
  errstr : String;
begin
  if Error <> 0 then begin
    case RqType of
      smtpConnect:  errstr := 'Helo';
      smtpHelo:     errstr := 'MailFrom';
      smtpMailFrom: errstr := 'RcptTo';
      smtpRcptTo:   errstr := 'Data';
      smtpData:     errstr := 'Quit';
      smtpQuit:     errstr := 'Finished';
    end;

    {$ifdef Trace}
      TraceError ('SMTP-Error',IntToStr (Error) + ' -> ' + ErrorMessage);
    {$endif}

    if Assigned (ErrorTrackingModule) then
      ErrorTrackingModule.WriteErrorLog ('SMTP-Error',errstr + ' : ' + IntToStr (Error) + ' -> ' + ErrorMessage);

    Abort;
  end else begin
    case RqType of
      smtpConnect:  Helo;
      smtpHelo:     MailFrom;
      smtpMailFrom: RcptTo;
      smtpRcptTo:   Data;
      smtpData:     Quit;
      smtpQuit:     ;
    end;
  end;
end;

function SendMail (const FromAdr, RecvAdr, Subject, MailText : String; AttFiles : array of String) : integer;
begin
  Result := SendMail (FromAdr, RecvAdr, '', '', '', Subject, MailText, AttFiles);
end;

function SendMail (const FromAdr, RecvAdr, CCAdr, BCCAdr, ReplayAdr, Subject, MailText : String; AttFiles : array of String) : integer;
var
  line,
  recvname   : String;
  i,
  res,
  strpos     : Integer;
  SmtpClient : TSmtpAutomat;
begin
  {$IFDEF TRACE}
    FunctionStart('SendMail');
    TraceParameter ('FromAdr', FromAdr);
    TraceParameter ('RecvAdr', RecvAdr);
    TraceParameter ('Subject', Subject);
  {$ENDIF}

  res := 0;

  SmtpClient := TSmtpAutomat.Create (Nil);

  try
    SmtpClient.OnRequestDone := SmtpClient.SmtpClientRequestDone;

    SmtpClient.Host   := SMTPMailServer;
    SmtpClient.SignOn := SMTPMailUser;

    SmtpClient.RcptName.Clear;

    strpos := 1;
    line := RecvAdr;
    recvname := '';

    while (strpos <= Length (line)) do begin
      if (line [strpos] = ';') or (line [strpos] = ',') then begin
        SmtpClient.RcptName.Add (recvname);
        recvname := '';
      end else recvname := recvname + line [strpos];

      Inc (strpos);
    end;

    if (Length (recvname) > 0) then
      SmtpClient.RcptName.Add (recvname);

    if (Length (BCCAdr) > 0) then
      SmtpClient.RcptName.Add (BCCAdr);

    SmtpClient.FromName   := FromAdr;

    SmtpClient.HdrFrom    := FromAdr;
    SmtpClient.HdrTo      := RecvAdr;
    SmtpClient.HdrCc      := CCAdr;
    SmtpClient.HdrReplyTo := ReplayAdr;
    SmtpClient.HdrSubject := Subject;

    SmtpClient.MailMessage.Clear;
    SmtpClient.MailMessage.Add (MailText);

    for i := Low (AttFiles) to High (AttFiles) do begin
      if (Length (AttFiles [i]) > 0) then
        SmtpClient.EmailFiles.Add (AttFiles [i]);
    end;

    try
      SmtpClient.Connect;

      while (SmtpClient.State <> smtpReady) do begin
        Application.ProcessMessages;
      end;

      if (SmtpClient.State <> smtpReady) then
        SmtpClient.Abort;
    except
      on E : Exception do begin
        res := -9;

        SmtpClient.Abort;

        ErrorTrackingModule.WriteErrorLog ('SMTP-Error',E.ClassName + ':' + E.Message);
      end;
    end;
  finally
    SmtpClient.Free;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$ENDIF}
end;

end.
