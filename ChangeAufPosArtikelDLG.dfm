object ChangeAufPosArtikelForm: TChangeAufPosArtikelForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Ersatzartikel ausw'#228'hlen'
  ClientHeight = 320
  ClientWidth = 518
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  DesignSize = (
    518
    320)
  TextHeight = 13
  object Label2: TLabel
    Left = 8
    Top = 18
    Width = 51
    Height = 13
    Caption = 'Auftragnr.'
  end
  object AufNrLabel: TLabel
    Left = 104
    Top = 18
    Width = 53
    Height = 13
    Caption = 'AufNrLabel'
  end
  object Label3: TLabel
    Left = 8
    Top = 37
    Width = 30
    Height = 13
    Caption = 'Kunde'
  end
  object KundeLabel: TLabel
    Left = 104
    Top = 37
    Width = 55
    Height = 13
    Caption = 'KundeLabel'
  end
  object Bevel3: TBevel
    Left = 6
    Top = 56
    Width = 506
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 341
  end
  object Label4: TLabel
    Left = 8
    Top = 66
    Width = 74
    Height = 13
    Caption = 'Besteller Artikel'
  end
  object Label5: TLabel
    Left = 8
    Top = 85
    Width = 76
    Height = 13
    Caption = 'Bestellte Menge'
  end
  object ArtikelLabel: TLabel
    Left = 104
    Top = 66
    Width = 55
    Height = 13
    Caption = 'ArtikelLabel'
  end
  object MengeLabel: TLabel
    Left = 104
    Top = 85
    Width = 57
    Height = 13
    Caption = 'MengeLabel'
  end
  object Bevel2: TBevel
    Left = 6
    Top = 104
    Width = 506
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 341
  end
  object Label6: TLabel
    Left = 8
    Top = 176
    Width = 59
    Height = 13
    Caption = 'Ersatzartikel'
  end
  object Label7: TLabel
    Left = 448
    Top = 175
    Width = 62
    Height = 13
    Anchors = [akTop, akRight]
    Caption = 'Ersatzmenge'
  end
  object Label8: TLabel
    Left = 8
    Top = 231
    Width = 98
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Grund der '#196'nderung'
    ExplicitTop = 205
  end
  object Bevel1: TBevel
    Left = 6
    Top = 276
    Width = 506
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 250
    ExplicitWidth = 341
  end
  object Label1: TLabel
    Left = 8
    Top = 120
    Width = 66
    Height = 13
    Caption = 'Warengruppe'
  end
  object OkButton: TButton
    Left = 354
    Top = 287
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    Default = True
    ModalResult = 1
    TabOrder = 7
  end
  object AbortButton: TButton
    Left = 435
    Top = 287
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 8
  end
  object ArtikelComboBox: TComboBoxPro
    Left = 136
    Top = 191
    Width = 306
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 3
    OnDropDown = ArtikelComboBoxDropDown
  end
  object MengeEdit: TEdit
    Left = 448
    Top = 191
    Width = 44
    Height = 21
    Anchors = [akTop, akRight]
    MaxLength = 4
    TabOrder = 4
    Text = '0'
    OnKeyPress = MengeEditKeyPress
  end
  object GrundEdit: TEdit
    Left = 8
    Top = 250
    Width = 502
    Height = 21
    Anchors = [akLeft, akRight, akBottom]
    MaxLength = 64
    TabOrder = 6
    Text = 'GrundEdit'
  end
  object ARGrpComboBox: TComboBoxPro
    Left = 8
    Top = 136
    Width = 502
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 0
    OnChange = ARGrpComboBoxChange
  end
  object ArNrEdit: TEdit
    Left = 8
    Top = 191
    Width = 76
    Height = 21
    TabOrder = 1
    Text = 'ArNrEdit'
    OnChange = ArNrEditChange
    OnExit = ArNrEditExit
  end
  object MengeUpDown: TUpDown
    Left = 492
    Top = 191
    Width = 16
    Height = 21
    Anchors = [akTop, akRight]
    Associate = MengeEdit
    TabOrder = 5
  end
  object ListArtikelButton: TButton
    Left = 89
    Top = 191
    Width = 41
    Height = 21
    Caption = '...'
    TabOrder = 2
    OnClick = ListArtikelButtonClick
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 288
    Top = 184
  end
end
