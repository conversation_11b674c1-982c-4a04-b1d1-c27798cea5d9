object AddInvErgForm: TAddInvErgForm
  Left = 462
  Top = 390
  Anchors = [akRight, akBottom]
  BorderStyle = bsDialog
  Caption = 'Neues Inventurergebnis hinzuf'#252'gen'
  ClientHeight = 247
  ClientWidth = 689
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    689
    247)
  TextHeight = 13
  object Bevel1: TBevel
    Left = 8
    Top = 208
    Width = 673
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object Label2: TLabel
    Left = 9
    Top = 8
    Width = 62
    Height = 13
    Caption = 'Lagerbereich'
  end
  object Label3: TLabel
    Left = 320
    Top = 56
    Width = 32
    Height = 13
    Caption = 'Einheit'
  end
  object A: TLabel
    Left = 9
    Top = 56
    Width = 29
    Height = 13
    Caption = 'Artikel'
  end
  object Lagerplatz: TLabel
    Left = 448
    Top = 8
    Width = 49
    Height = 13
    Caption = 'Lagerplatz'
  end
  object Label1: TLabel
    Left = 448
    Top = 56
    Width = 30
    Height = 13
    Caption = 'LE-Nr.'
  end
  object Label4: TLabel
    Left = 240
    Top = 120
    Width = 12
    Height = 13
    Caption = 'kg'
  end
  object Label5: TLabel
    Left = 160
    Top = 104
    Width = 53
    Height = 13
    Caption = 'Gewicht Ist'
  end
  object Label6: TLabel
    Left = 9
    Top = 104
    Width = 47
    Height = 13
    Caption = 'Menge Ist'
  end
  object Label7: TLabel
    Left = 320
    Top = 104
    Width = 39
    Height = 13
    Caption = 'MHD Ist'
  end
  object Label8: TLabel
    Left = 448
    Top = 104
    Width = 48
    Height = 13
    Caption = 'Charge Ist'
  end
  object OkButton: TButton
    Left = 526
    Top = 216
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'OK'
    Default = True
    ModalResult = 1
    TabOrder = 10
  end
  object AbortButton: TButton
    Left = 606
    Top = 216
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 11
  end
  object LBComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 417
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 120
    TabOrder = 0
    OnChange = LBComboBoxChange
  end
  object ArtikelComboBox: TComboBoxPro
    Left = 8
    Top = 72
    Width = 281
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 60
    TabOrder = 2
    OnChange = ArtikelComboBoxChange
  end
  object LPComboBox: TComboBoxPro
    Left = 448
    Top = 24
    Width = 233
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 80
    TabOrder = 1
  end
  object EinheitComboBox: TComboBoxPro
    Left = 320
    Top = 72
    Width = 105
    Height = 22
    Style = csOwnerDrawFixed
    TabOrder = 3
    OnChange = EinheitComboBoxChange
  end
  object LEEdit: TEdit
    Left = 448
    Top = 72
    Width = 121
    Height = 21
    TabOrder = 4
    Text = 'LEEdit'
  end
  object MengeEdit: TEdit
    Left = 8
    Top = 120
    Width = 105
    Height = 21
    TabOrder = 5
    Text = '0'
    OnChange = MengeEditChange
    OnKeyPress = MengeEditKeyPress
  end
  object MengeUpDown: TIntegerUpDown
    Left = 113
    Top = 120
    Width = 16
    Height = 21
    Associate = MengeEdit
    Max = 10000
    TabOrder = 6
    OnChanging = MengeUpDownChanging
  end
  object GewichtEdit: TEdit
    Left = 160
    Top = 120
    Width = 77
    Height = 21
    TabOrder = 7
    Text = 'GewichtEdit'
    OnExit = GewichtEditExit
  end
  object MHDEdit: TEdit
    Left = 320
    Top = 120
    Width = 105
    Height = 21
    TabOrder = 8
    Text = 'MHDEdit'
    OnExit = MHDEditExit
    OnKeyPress = MHDEditKeyPress
  end
  object ChargeEdit: TEdit
    Left = 448
    Top = 120
    Width = 121
    Height = 21
    TabOrder = 9
    Text = 'ChargeEdit'
  end
  object ADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 192
    Top = 216
  end
  object LEBetterADODataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 352
    Top = 184
  end
end
