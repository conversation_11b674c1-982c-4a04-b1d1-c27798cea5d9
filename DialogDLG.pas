unit DialogDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls;

type
  TDialogSuppressedForm = class(TForm)
    KommSollIstCheckBox: TCheckBox;
    AufFehleMengeCheckBox: TCheckBox;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel1: TBevel;
    Panel1: TPanel;
    Label1: TLabel;
    CheckBox1: TCheckBox;
    CheckBox2: TCheckBox;
    procedure FormShow(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
    { Private-Deklarationen }
  public
    OptStr : String;
  end;

implementation

{$R *.dfm}

uses FrontendConst, FrontendUtils;

procedure TDialogSuppressedForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult = mrOk) then begin
    if KommSollIstCheckBox.Checked then
      OptStr := ResetOpt (OptStr, DLG_SOLLIST)
    else OptStr := SetOpt(OptStr, DLG_SOLLIST);

    if AufFehleMengeCheckBox.Checked then
      OptStr := ResetOpt (OptStr, DLG_AUFABG)
    else OptStr := SetOpt (OptStr, DLG_AUFABG);
  end;
end;

procedure TDialogSuppressedForm.FormShow(Sender: TObject);
begin
  KommSollIstCheckBox.Checked   := not (CheckOpt (OptStr, DLG_SOLLIST));
  AufFehleMengeCheckBox.Checked := not (CheckOpt (OptStr, DLG_AUFABG));
end;

end.
