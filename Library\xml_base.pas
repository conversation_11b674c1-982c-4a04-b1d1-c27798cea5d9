//*****************************************************************************
//*  Program System    : system
//*  Module Name       : XML-Lib
//*  Author            : <PERSON>
//*  Date of creation  : 01.09.2000
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/Library/xml_base.pas $
// $Revision: 10 $
// $Modtime: 29.03.22 14:46 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : XML-Library
//*****************************************************************************
unit xml_base;

interface
uses Classes;

type
  TXMLTagEntry = class (TObject)
  private
    fTagName     : String;
    fTagAttribut : String;
    fTagValue    : String;

    fTagOpen     : Boolean;

    function GetSubTagCount : Integer;
  protected
    fParentTag   : TXMLTagEntry;
    fRootTag     : TXMLTagEntry;
    fSubTag      : TXMLTagEntry;
    fNextTag     : TXMLTagEntry;
    fPrevTag     : TXMLTagEntry;

    fCheckFlag   : Boolean;

  public
    constructor Create; overload;
    constructor Create (const Name : String; Parent : TXMLTagEntry = Nil); overload;
    destructor Destroy; override;

    property SubCount    : Integer read GetSubTagCount;
    property ParentTag   : TXMLTagEntry read fParentTag write fParentTag;
    property RootTag     : TXMLTagEntry read fRootTag write fRootTag;
    property SubTag      : TXMLTagEntry read fSubTag write fSubTag;
    property NextTag     : TXMLTagEntry read fNextTag write fNextTag;
    property PrevTag     : TXMLTagEntry read fPrevTag write fPrevTag;
    //property ChildNodes  : TXMLTagEntry read fSubTag;
    //property First       : TXMLTagEntry read fRootTag;

    property TagOpen     : Boolean read fTagOpen write fTagOpen;

    property TagName     : String read fTagName write fTagName;
    property NodeName    : String read fTagName;
    property TagAttribut : String read fTagAttribut write fTagAttribut;
    property TagValue    : String read fTagValue write fTagValue;
    property NodeValue   : String read fTagValue;

    property CheckFlag   : Boolean read fCheckFlag write fCheckFlag;

    function GetFullTagName : String;

    function FindSubTag (const TagName : String) : TXMLTagEntry;
    function FindTag    (const TagName : String; StartNode : TXMLTagEntry = Nil; const MaxDepth : Integer = -1) : TXMLTagEntry;

    function NextSibling : TXMLTagEntry;

    function GetAttribut (const AttrName : StrinG) : String;
  end;


TXMLSheed = class (TComponent)
protected
  Root : TXMLTagEntry;
public
  constructor Create (AOwner:TComponent); override;
  destructor  Destroy; override;

  procedure Cleanup;
  function FindTag (const TagName : String; StartNode : TXMLTagEntry = Nil) : TXMLTagEntry;
end;

implementation

uses SysUtils;

constructor TXMLTagEntry.Create;
begin
  inherited Create;

  fParentTag := Nil;
  fSubTag    := Nil;
  fRootTag   := Nil;
  fNextTag   := Nil;
  fPrevTag   := Nil;

  fTagName     := '';
  fTagAttribut := '';
  fTagValue    := '';

  fTagOpen     := False;
  fCheckFlag   := False;
end;

destructor TXMLTagEntry.Destroy;
var
  xmltag,
  lasttag : TXMLTagEntry;
begin
  if Assigned (fSubTag) then begin
    xmltag := fSubTag;

    while Assigned (xmltag) do begin
      lasttag := xmltag;
      xmltag  := xmltag.NextTag;

      FreeAndNil (lasttag);
    end;
  end;

  fSubTag := Nil;

  inherited Destroy;
end;

constructor TXMLTagEntry.Create (const Name : String; Parent : TXMLTagEntry = Nil);
begin
  Self.Create;

  fParentTag := Parent;
  fTagName := Name;
end;

function TXMLTagEntry.GetSubTagCount : Integer;
var
  c   : integer;
  tag : TXMLTagEntry;
begin
  c := 0;

  if Assigned (fSubTag) then begin
    tag := fSubTag;

    while Assigned (tag.NextTag) do begin
      Inc (c);
      tag := tag.NextTag;
    end;
  end;

  Result := c;
end;

function TXMLTagEntry.FindTag (const TagName : String; StartNode : TXMLTagEntry; const MaxDepth : Integer) : TXMLTagEntry;
var
  xmltag,
  subxmltag : TXMLTagEntry;
  found     : Boolean;
begin
  if (Length (TagName) = 0) then begin
    xmltag := RootTag;
  end else begin
//    TraceParameter ("TagName", TagName);

    if (StartNode <> Nil) then begin
//      TraceParameter ("StartNode", StartNode.TagName);

      xmltag := StartNode;
    end	else begin
//      TraceParameter ("StartNode", "(Nil)");

      xmltag := Self;
    end;

    found := FALSE;

    while ((xmltag <> Nil) and not (found)) do begin
      if (xmltag.TagName = TagName) then
        found := TRUE
      else if (xmltag.SubTag <> Nil) then begin
        if (MaxDepth = -1) then
          subxmltag := FindTag (TagName, xmltag.SubTag, -1)
        else if (MaxDepth = 0) Then
          subxmltag := Nil
        else subxmltag := FindTag (TagName, xmltag.SubTag, MaxDepth - 1);

        if (subxmltag <> Nil) then begin
          found := true;
          xmltag := subxmltag;
        end;
      end;

      if not (found) then
        xmltag := xmltag.NextTag;
    end;

    (*
    TraceResult ("found", found);

    if (found)
      TraceResult ("Tag", xmltag.TagName);
    *)
  end;

  Result := xmltag;
end;

function TXMLTagEntry.GetFullTagName : String;
var
  line   : String;
  parent : TXMLTagEntry;
begin
  line := TagName;

  parent := fParentTag;

  while (parent <> Nil) do begin
    line := parent.TagName +'.' + line;
    parent := parent.ParentTag;
  end;

  Result := line;
end;

function TXMLTagEntry.NextSibling : TXMLTagEntry;
begin
  Result := fNextTag;
end;

//*****************************************************************************
//*  Function Name     :
//*  Author            : Stefan Graf
//*****************************************************************************
//*  Description       :
//*---------------------------------------------------------------------------
//*  Return Value      :
//****************************************************************************
function TXMLTagEntry.GetAttribut (const AttrName : String) : String;
var
  step,
  strpos    : Integer;
  namestr,
  wertstr   : String;
begin
  Result := '';

  step := 0;
  strpos := 1;
  namestr := '';
  wertstr := '';

  while (strpos <= Length (fTagAttribut)) do begin
    if (step = 0) then begin
      if (fTagAttribut [strpos] <> ' ') then begin
        step := 1;
        namestr := fTagAttribut [strpos];
      end;
    end else if (step = 1) then begin
      if (fTagAttribut [strpos] = ' ') then
        step := 2
      else if (fTagAttribut [strpos] = '=') then
        step := 3
      else
        namestr := namestr + fTagAttribut [strpos];
    end else if (step = 2) then begin
      if (fTagAttribut [strpos] = '=') then
        step := 3;
    end else if (step = 3) then begin
      if (fTagAttribut [strpos] <> '"') and (fTagAttribut [strpos] <> ' ') then begin
        step := 4;
        wertstr := fTagAttribut [strpos];
      end;
    end else if (step = 4) then begin
      if (fTagAttribut [strpos] = '"') then begin
        if (namestr = AttrName) then
          break
        else begin
          step := 0;
          namestr := '';
        end;
      end else
        wertstr := wertstr + fTagAttribut [strpos];
    end;

    Inc (strpos);
  end;

  if (namestr = AttrName) then
    Result := wertstr;
end;

//*****************************************************************************
//*  Function Name     :
//*  Author            : Stefan Graf
//*****************************************************************************
//*  Description       :
//*---------------------------------------------------------------------------
//*  Return Value      :
//****************************************************************************
function TXMLTagEntry.FindSubTag (const TagName : String) : TXMLTagEntry;
var
  xmltag : TXMLTagEntry;
  found  : Boolean;
begin
  if (Length (TagName) = 0) then begin
    xmltag := Nil;
  end else begin
//    TraceParameter ("TagName", TagName);

    found := FALSE;
    xmltag := SubTag;

    while ((xmltag <> Nil) and not (found)) do begin
      if (xmltag.TagName = TagName) then
        found := TRUE
      else xmltag := xmltag.NextTag;
    end;

//    TraceResult ("found", found);
  end;

  Result := xmltag;
end;

constructor TXMLSheed.Create (AOwner:TComponent);
begin
  inherited Create(AOwner);

  Root := Nil;
end;

destructor TXMLSheed.Destroy;
begin
  Cleanup;

  inherited Destroy;
end;

procedure TXMLSheed.Cleanup;
begin
//  TraceParameter ("Tag", TagNode->TagName);

  FreeAndNil (Root);
end;

function TXMLSheed.FindTag (const TagName : String; StartNode : TXMLTagEntry = Nil) : TXMLTagEntry;
var
  xmltag,
  subxmltag : TXMLTagEntry;
  found     : Boolean;
begin
  if (Length (TagName) = 0) then begin
    xmltag := Root;
  end else begin
//    TraceParameter ("TagName", TagName);

    if (StartNode <> Nil) then begin
//      TraceParameter ("StartNode", StartNode.TagName);

      xmltag := StartNode;
    end	else begin
//      TraceParameter ("StartNode", "(Nil)");

      xmltag := Root;
    end;

    found := FALSE;

    while ((xmltag <> Nil) and not (found)) do begin
      if (xmltag.TagName = TagName) then
        found := TRUE
      else if (xmltag.SubTag <> Nil) then begin
        subxmltag := FindTag (TagName, xmltag.SubTag);

        if (subxmltag <> Nil) then begin
          found := true;
          xmltag := subxmltag;
        end;
      end;

      if not (found) then
        xmltag := xmltag.NextTag;
    end;

    (*
    TraceResult ("found", found);

    if (found)
      TraceResult ("Tag", xmltag.TagName);
    *)
  end;

  Result := xmltag;
end;


end.
