unit MessageDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls;

const
  mrTimeout = 99;

type
  TMessageForm = class(TForm)
    Button1: TButton;
    Button2: TButton;
    MessageLabel: TLabel;
    TimeoutLabel: TLabel;
  private
    { Private-Deklarationen }
  public
    function ShowModal (const Timeout : DWORD) : Integer; reintroduce; overload;
  end;

var
  MessageForm: TMessageForm;

implementation

{$R *.dfm}

uses Consts, StringUtils;

var
  FocusMessages: Boolean = True;
  FocusCount: Integer = 0;

function TMessageForm.ShowModal (const Timeout : DWORD) : Integer;
var
  WindowList: Pointer;
  SaveFocusCount: Integer;
  SaveCursor: TCursor;
  ActiveWindow: HWnd;
  res,
  sec,oldsec : Integer;
begin
  CancelDrag;
  if Visible or not Enabled or (fsModal in FFormState) or
    (FormStyle = fsMDIChild) then
    raise EInvalidOperation.Create(SCannotShowModal);
  if GetCapture <> 0 then SendMessage(GetCapture, WM_CANCELMODE, 0, 0);
  ReleaseCapture;
  Application.ModalStarted;
  try
    SaveFocusCount := FocusCount;
    Include(FFormState, fsModal);
    ActiveWindow := GetActiveWindow;
    SaveCursor := Screen.Cursor;
    Screen.Cursor := crDefault;
    WindowList := DisableTaskWindows(0);

    try
      Show;
      try
        SendMessage(Handle, CM_ACTIVATE, 0, 0);
        ModalResult := 0;

        oldsec := -1;

        TimeoutLabel.Visible := (Timeout <> 0);

        repeat
          if (Timeout <> 0) then begin
            sec := (Timeout - GetTickCount) div 1000;

            if (sec <> oldsec) then begin
              oldsec := sec;
              TimeoutLabel.Caption := FormatStr (IntToStr (sec div 60),-2,'0')+':'+FormatStr (IntToStr (sec mod 60),-2,'0');
            end;
          end;

          Application.HandleMessage;
          if Application.Terminated then
            ModalResult := mrCancel
          else if ModalResult <> 0 then begin
            res := ModalResult;
            Close;
            ModalResult := res;
          end else if ((Timeout <> 0) and (GetTickCount > Timeout)) then
            ModalResult := mrTimeout;
        until (ModalResult <> 0);
        Result := ModalResult;
        SendMessage(Handle, CM_DEACTIVATE, 0, 0);
        if GetActiveWindow <> Handle then ActiveWindow := 0;
      finally
        Hide;
      end;
    finally
      EnableTaskWindows(WindowList);
      if ActiveWindow <> 0 then SetActiveWindow(ActiveWindow);
      FocusCount := SaveFocusCount;
      Exclude(FFormState, fsModal);
    end;
  finally
    Application.ModalFinished;
  end;
end;

end.
