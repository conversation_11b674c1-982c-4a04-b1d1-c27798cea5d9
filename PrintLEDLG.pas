unit PrintLEDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls, PrinterUtils;

type
  TPrintLEForm = class(TForm)
    Panel2: TPanel;
    Label8: TLabel;
    PrinterComboBox: TComboBoxPro;
    PrintButton: TButton;
    CloseButton: TButton;
    procedure FormCreate(Sender: TObject);
  private
    fRefLE,
    fRefLager   : Integer;
    fPortArray  :  array [0..255] of TPrinterPorts;

    function GetPrinterPorts (Index: Integer) : TPrinterPorts;
  public
    property PortArray [Index: Integer] : TPrinterPorts read GetPrinterPorts;
    function GetSelectedPrinter : String;

    procedure Prepare (const DefPrinterName : String; const RefLager, RefLE : Integer);
  end;

implementation

{$R *.dfm}

uses
  PrintModul, SprachModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 29.11.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLEForm.FormCreate(Sender: TObject);
begin
  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, PrinterComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TPrintLEForm.GetPrinterPorts (Index: Integer) : TPrinterPorts;
begin
  Result := fPortArray [Index]
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintLEForm.Prepare (const DefPrinterName : String; const RefLager, RefLE : Integer);
begin
  fRefLE    := RefLE;
  fRefLager := RefLager;

  PrintModule.LoadPrinterCombobox (fRefLager, '', PrinterComboBox, fPortArray, DefPrinterName, True);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.05.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TPrintLEForm.GetSelectedPrinter : String;
var
  prtstr : String;
begin
  if (PrinterComboBox.ItemIndex <= Low (fPortArray)) or (PrinterComboBox.ItemIndex > High (fPortArray)) then
    prtstr := ''
  else if (Length (fPortArray [PrinterComboBox.ItemIndex].Name) = 0) then
    prtstr := ''
  else if (Length (fPortArray [PrinterComboBox.ItemIndex].PrtTyp) = 0) then
    prtstr := fPortArray [PrinterComboBox.ItemIndex].Name
  else
    prtstr := fPortArray [PrinterComboBox.ItemIndex].Name + ';' + fPortArray [PrinterComboBox.ItemIndex].PrtTyp;

  Result := prtstr;
end;

end.
