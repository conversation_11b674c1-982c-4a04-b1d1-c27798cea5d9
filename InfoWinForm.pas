{$i compilers.inc}

unit InfoWinForm;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, ComCtrls;

type
  TInfoWin = class(TForm)
    Label1: TLabel;
    ProgressPanel: TPanel;
    ProgressBar1: TProgressBar;
    ButtonPanel: TPanel;
    AbortButton: TButton;
    DauerLabel: TLabel;
    Label2: TLabel;
    procedure FormShow(Sender: TObject);
    procedure AbortButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
  private
    fWindowList: Pointer;
    fActiveWindow: HWnd;
    fAbortFlag : Boolean;
    fModalFlag : Boolean;

    fStartTime : Int64;
  public
    property  AbortFlag : Boolean read fAbortFlag;

    procedure BeginShowModal;
    procedure EndShowModal;
    procedure UpdateModal;
  end;

implementation

{$R *.dfm}

uses
  ResourceText;

{$ifdef DELPHIXE4_UP}
  type
    TMyCustomForm = class (TCustomForm)
      public
        FFormState: TFormState;
    end;
{$endif}

procedure TInfoWin.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  if fModalFlag then begin
    EnableTaskWindows(fWindowList);
    if fActiveWindow <> 0 then SetActiveWindow(fActiveWindow);

    {$ifdef DELPHIXE4_UP}
      //Exclude(((Self as TCustomForm) as TMyCustomForm).FFormState, fsModal);
    {$else}
      Exclude((Self as TCustomForm).FFormState, fsModal);
    {$endif}

    Application.ModalFinished;

    fModalFlag := False;
  end;
end;

procedure TInfoWin.FormCreate(Sender: TObject);
begin
  fModalFlag := False;
  fStartTime := Int64 (GetTickCount);

  Label2.Caption      := GetResourceText(1825);
  DauerLabel.Caption  := '';
  AbortButton.Caption := GetResourceText(1346);
end;

procedure TInfoWin.FormShow(Sender: TObject);
begin
  fAbortFlag := False;

  if not (ButtonPanel.Visible) then
    Height := Height - ButtonPanel.Height;

  if not (ProgressPanel.Visible) then
    Height := Height - ProgressPanel.Height;

  DauerLabel.Left := Label2.Width + 16;
end;

procedure TInfoWin.AbortButtonClick(Sender: TObject);
begin
  fAbortFlag := True;
end;

procedure TInfoWin.BeginShowModal;
begin
  fModalFlag := True;;
  fAbortFlag := False;

  fStartTime := Int64 (GetTickCount);

  CancelDrag;
  if GetCapture <> 0 then SendMessage(GetCapture, WM_CANCELMODE, 0, 0);
  ReleaseCapture;

  Application.ModalStarted;

  {$ifdef DELPHIXE4_UP}
    //Include(((Self as TCustomForm) as TMyCustomForm).FFormState, fsModal);
  {$else}
    Include((Self as TCustomForm).FFormState, fsModal);
  {$endif}

  fActiveWindow := GetActiveWindow;
  fWindowList := DisableTaskWindows (Application.Handle);

  EnableWindow (Handle, True);

  Show;

  SendMessage(Handle, CM_ACTIVATE, 0, 0);

  Update;
end;

procedure TInfoWin.UpdateModal;
var
  t,
  diff,
  ticks   : Int64;
  dtstr   : String;
  h, m, s : Integer;
begin
  if (ProgressPanel.Visible) then begin
    ticks := Int64 (GetTickCount);

    if (ticks > fStartTime) then
      diff := ticks - fStartTime
    else
      diff := ($100000000 - fStartTime) + ticks;

    s := diff div 1000;

    h := Trunc(s div 3600);
    s := s - (3600 * Trunc(s div 3600));
    m := Trunc(s div 60);
    s := s - (60 * Trunc(s div 60));

    try
      dtstr := TimeToStr (EncodeTime (h, m, s, 0));
    except
      dtstr := '00:00:00'
    end;

    DauerLabel.Caption := dtstr;

    if (ProgressBar1.Position > 0) then begin
      t := diff div ProgressBar1.Position;

      if (ProgressBar1.Max < ProgressBar1.Position) then
        s := 0
      else
        s := (t * (ProgressBar1.Max - ProgressBar1.Position) div 1000);

      h := Trunc(s div 3600);
      s := s - (3600 * Trunc(s div 3600));
      m := Trunc(s div 60);
      s := s - (60 * Trunc(s div 60));

      try
        dtstr := TimeToStr (EncodeTime (h, m, s, 0));
      except
        dtstr := '00:00:00'
      end;

      DauerLabel.Caption := DauerLabel.Caption + ' -> ' + dtstr + ', ' + IntToStr (t) + ' ms';
    end;

    Update;
  end;

  Application.ProcessMessages;
end;

procedure TInfoWin.EndShowModal;
begin
  EnableTaskWindows(fWindowList);
  if fActiveWindow <> 0 then SetActiveWindow(fActiveWindow);

  {$ifdef DELPHIXE4_UP}
    //Exclude(((Self as TCustomForm) as TMyCustomForm).FFormState, fsModal);
  {$else}
    Exclude((Self as TCustomForm).FFormState, fsModal);
  {$endif}

  Application.ModalFinished;

  fModalFlag := False;

  Close;
end;

end.
