object ConfigAufAblaufForm: TConfigAufAblaufForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Konfiguration der Auftragsabwicklung'
  ClientHeight = 638
  ClientWidth = 833
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    833
    638)
  TextHeight = 13
  object Label11: TLabel
    Left = 15
    Top = 16
    Width = 60
    Height = 13
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Caption = 'WA-Relation'
  end
  object CloseButton: TButton
    Left = 756
    Top = 606
    Width = 70
    Height = 24
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 0
  end
  object PageControl1: TPageControl
    Left = 7
    Top = 7
    Width = 819
    Height = 594
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    ActivePage = PlanTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 1
    object ConfigTabSheet: TTabSheet
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Auftragart Konfiguration'
      OnResize = ConfigTabSheetResize
      OnShow = ConfigTabSheetShow
      object AufCfgDBGrid: TDBGridPro
        Left = 0
        Top = 0
        Width = 811
        Height = 224
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Align = alClient
        DataSource = AufCfgDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object ConfigDataGroupBox: TGroupBox
        Left = 0
        Top = 224
        Width = 811
        Height = 342
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Align = alBottom
        Caption = 'Parameter'
        TabOrder = 1
        DesignSize = (
          811
          342)
        object Label7: TLabel
          Left = 15
          Top = 16
          Width = 117
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Standard Ladungstr'#228'ger'
        end
        object Label15: TLabel
          Left = 15
          Top = 60
          Width = 111
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Vorgegebene Spedition'
        end
        object Label5: TLabel
          Left = 285
          Top = 60
          Width = 82
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akTop, akRight]
          Caption = 'Default Spedition'
        end
        object Label6: TLabel
          Left = 15
          Top = 149
          Width = 41
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Druckart'
        end
        object Label8: TLabel
          Left = 535
          Top = 16
          Width = 80
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Speditions-Regel'
        end
        object Label22: TLabel
          Left = 535
          Top = 60
          Width = 57
          Height = 13
          Caption = 'Default-Prio'
        end
        object Label28: TLabel
          Left = 285
          Top = 16
          Width = 114
          Height = 13
          Caption = 'Verpackungsart-Gruppe'
        end
        object Label21: TLabel
          Left = 535
          Top = 104
          Width = 82
          Height = 13
          Caption = 'MHD Restlaufzeit'
        end
        object Label24: TLabel
          Left = 15
          Top = 104
          Width = 109
          Height = 13
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Spedition f'#252'r Retouren'
        end
        object Label25: TLabel
          Left = 285
          Top = 104
          Width = 143
          Height = 13
          Caption = 'Ablage f'#252'r PDF-Retourenlabel'
        end
        object Label23: TLabel
          Left = 140
          Top = 104
          Width = 81
          Height = 13
          Caption = 'Aus ISO L'#228'ndern'
        end
        object LTComboBox: TComboBoxPro
          Left = 15
          Top = 32
          Width = 250
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Style = csOwnerDrawFixed
          ItemHeight = 15
          TabOrder = 0
        end
        object SpedPrefComboBox: TComboBoxPro
          Left = 15
          Top = 76
          Width = 250
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Style = csOwnerDrawFixed
          ItemHeight = 15
          TabOrder = 3
        end
        object SpedDefaultComboBox: TComboBoxPro
          Left = 285
          Top = 76
          Width = 228
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Style = csOwnerDrawFixed
          ItemHeight = 15
          TabOrder = 4
        end
        object DruckartEdit: TEdit
          Left = 15
          Top = 164
          Width = 781
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          ReadOnly = True
          TabOrder = 12
          Text = 'DruckartEdit'
        end
        object ConfigApplyButton: TButton
          Left = 607
          Top = 309
          Width = 90
          Height = 23
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akRight, akBottom]
          Caption = #220'bernehmen'
          TabOrder = 16
          OnClick = ConfigApplyButtonClick
        end
        object ConfigQuashButton: TButton
          Left = 706
          Top = 309
          Width = 90
          Height = 23
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akRight, akBottom]
          Caption = 'Verwerfen'
          TabOrder = 17
          OnClick = PlanQuashButtonClick
        end
        object SpedRoleEdit: TEdit
          Left = 535
          Top = 32
          Width = 261
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 2
          Text = 'SpedRoleEdit'
        end
        object KonfGroupBox: TGroupBox
          Left = 249
          Top = 197
          Width = 248
          Height = 106
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Fertigung'
          TabOrder = 15
          DesignSize = (
            248
            106)
          object KonfAufCheckBox: TCheckBox
            Left = 16
            Top = 16
            Width = 200
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Fertigungsauftrag'
            TabOrder = 0
          end
          object KonfBestCheckBox: TCheckBox
            Left = 16
            Top = 32
            Width = 520
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Automatische Vereinnahmung angelegen'
            TabOrder = 1
          end
        end
        object PrioEdit: TEdit
          Left = 535
          Top = 76
          Width = 48
          Height = 21
          TabOrder = 5
          Text = '0'
        end
        object PrioUpDown: TIntegerUpDown
          Left = 583
          Top = 76
          Width = 16
          Height = 21
          Associate = PrioEdit
          TabOrder = 6
        end
        object PackTypeGroupComboBox: TComboBoxPro
          Left = 285
          Top = 32
          Width = 228
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 1
        end
        object OptGroupBox: TGroupBox
          Left = 15
          Top = 197
          Width = 235
          Height = 106
          Caption = 'Optionen'
          TabOrder = 13
          DesignSize = (
            235
            106)
          object VorResCheckBox: TCheckBox
            Left = 16
            Top = 16
            Width = 243
            Height = 17
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Vorreservierung von Best'#228'nden'
            TabOrder = 0
          end
          object PartDeliveryCheckBox: TCheckBox
            Left = 16
            Top = 32
            Width = 243
            Height = 17
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Teillieferungen zul'#228'ssig'
            TabOrder = 1
          end
          object OnePackageCheckBox: TCheckBox
            Left = 16
            Top = 48
            Width = 202
            Height = 17
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Warnung bei Paketsplit'
            TabOrder = 2
          end
          object DeliveryNoteMailCheckBox: TCheckBox
            Left = 16
            Top = 64
            Width = 210
            Height = 17
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Lieferschein per Mail an den Empf'#228'nger'
            TabOrder = 3
          end
          object DetermineLTCheckBox: TCheckBox
            Left = 16
            Top = 80
            Width = 250
            Height = 17
            Anchors = [akTop, akRight]
            Caption = 'Optimale Verpackung bestimmen'
            TabOrder = 4
          end
        end
        object PrintGroupBox: TGroupBox
          Left = 513
          Top = 197
          Width = 259
          Height = 106
          Caption = 'Drucken'
          TabOrder = 14
          DesignSize = (
            259
            106)
          object Label29: TLabel
            Left = 16
            Top = 78
            Width = 74
            Height = 13
            Anchors = [akLeft, akBottom]
            Caption = '1. Versandlabel'
          end
          object PrintInvoiceCheckBox: TCheckBox
            Left = 16
            Top = 16
            Width = 217
            Height = 17
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Rechnung drucken'
            TabOrder = 0
          end
          object PrintProformaCheckBox: TCheckBox
            Left = 16
            Top = 32
            Width = 201
            Height = 17
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Proforma drucken'
            TabOrder = 1
          end
          object PrintCMRCheckBox: TCheckBox
            Left = 16
            Top = 48
            Width = 217
            Height = 17
            Anchors = [akLeft, akTop, akRight]
            Caption = 'CMR drucken'
            TabOrder = 2
          end
          object ShipLabelComboBox: TComboBox
            Left = 111
            Top = 75
            Width = 138
            Height = 21
            Anchors = [akLeft, akBottom]
            ItemIndex = 0
            TabOrder = 3
            Text = 'Immer erzeugen'
            Items.Strings = (
              'Immer erzeugen'
              'Image '#252'ber Auftragnr. '
              'Image '#252'ber Kd. Best. Nr.'
              'Image '#252'ber Auf. Referenz'
              'Aus dem DMS')
          end
        end
        object BBDRemainingEdit: TEdit
          Left = 535
          Top = 120
          Width = 48
          Height = 21
          TabOrder = 10
          Text = '0'
        end
        object BBDRemainingUpDown: TIntegerUpDown
          Left = 583
          Top = 120
          Width = 16
          Height = 21
          Anchors = [akTop, akRight]
          Associate = BBDRemainingEdit
          TabOrder = 11
        end
        object SpedRetComboBox: TComboBoxPro
          Left = 15
          Top = 120
          Width = 117
          Height = 21
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Style = csOwnerDrawFixed
          ItemHeight = 15
          TabOrder = 7
        end
        object RetPDFPathEdit: TEdit
          Left = 285
          Top = 120
          Width = 228
          Height = 21
          TabOrder = 9
          Text = 'RetPDFPathEdit'
        end
        object SpedRetLandEdit: TEdit
          Left = 140
          Top = 120
          Width = 125
          Height = 21
          TabOrder = 8
          Text = 'SpedRetLandEdit'
        end
      end
    end
    object PlanTabSheet: TTabSheet
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Auftragart Planung'
      ImageIndex = 1
      OnShow = PlanTabSheetShow
      object GroupBox1: TGroupBox
        Left = 0
        Top = 218
        Width = 811
        Height = 348
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Align = alBottom
        Caption = 'Parameter'
        TabOrder = 0
        DesignSize = (
          811
          348)
        object PlanApplyButton: TButton
          Left = 613
          Top = 316
          Width = 90
          Height = 24
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akRight, akBottom]
          Caption = #220'bernehmen'
          TabOrder = 2
          OnClick = PlanApplyButtonClick
        end
        object PlanQuashButton: TButton
          Left = 711
          Top = 316
          Width = 90
          Height = 24
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akRight, akBottom]
          Caption = 'Verwerfen'
          TabOrder = 1
          OnClick = PlanQuashButtonClick
        end
        object PlanPageControl: TPageControl
          AlignWithMargins = True
          Left = 10
          Top = 63
          Width = 791
          Height = 247
          Margins.Left = 8
          Margins.Top = 6
          Margins.Right = 8
          Margins.Bottom = 0
          ActivePage = PlanKommTabSheet
          Align = alTop
          TabOrder = 0
          object GlobalTabSheet: TTabSheet
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Allgemein'
            ImageIndex = 4
            object BatchPlanCheckBox: TCheckBox
              Left = 7
              Top = 15
              Width = 329
              Height = 16
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Batchplanung zul'#228'ssig'
              TabOrder = 0
            end
          end
          object PlanPlanTabSheet: TTabSheet
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Planen'
            object PlanPanel: TPanel
              Left = 0
              Top = 58
              Width = 783
              Height = 91
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Align = alTop
              BevelOuter = bvNone
              TabOrder = 1
              DesignSize = (
                783
                91)
              object Bevel1: TBevel
                Left = 11
                Top = 89
                Width = 1550
                Height = 5
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Anchors = [akLeft, akRight, akBottom]
                Shape = bsTopLine
                ExplicitWidth = 1514
              end
              object Bevel5: TBevel
                Left = 11
                Top = 33
                Width = 1550
                Height = 6
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Anchors = [akLeft, akRight, akBottom]
                Shape = bsTopLine
                ExplicitWidth = 1514
              end
              object KommPlanBereichCheckBox: TCheckBox
                Left = 215
                Top = 7
                Width = 173
                Height = 17
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'VE-Kommlauf pro Bereich planen'
                TabOrder = 1
              end
              object PALKommPlanBereichCheckBox: TCheckBox
                Left = 215
                Top = 66
                Width = 173
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'PAL-Kommlauf pro Bereich planen'
                TabOrder = 5
              end
              object KommPlanZoneCheckBox: TCheckBox
                Left = 453
                Top = 7
                Width = 180
                Height = 17
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'VE-Kommlauf pro Zone planen'
                TabOrder = 2
              end
              object PALKommPlanZoneCheckBox: TCheckBox
                Left = 453
                Top = 66
                Width = 180
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'PAL-Kommlauf pro Zone planen'
                TabOrder = 6
              end
              object PlanFiFoCheckBox: TCheckBox
                Left = 15
                Top = 7
                Width = 157
                Height = 17
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Nach FiFo planen'
                TabOrder = 0
              end
              object PlanFullPALCheckBox: TCheckBox
                Left = 15
                Top = 45
                Width = 157
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Vollpaletten-Planung zul'#228'ssig'
                TabOrder = 3
                OnClick = PlanFullPALCheckBoxClick
              end
              object PlanTeilPALCheckBox: TCheckBox
                Left = 15
                Top = 66
                Width = 157
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Anbruchlpaletten zul'#228'ssig'
                TabOrder = 4
              end
            end
            object PlanKommVerdichtenCheckBox: TCheckBox
              Left = 15
              Top = 174
              Width = 157
              Height = 15
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Kommpos. verdichten'
              TabOrder = 2
            end
            object TAPanelPanel: TPanel
              Left = 0
              Top = 0
              Width = 783
              Height = 58
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Align = alTop
              BevelOuter = bvNone
              TabOrder = 0
              DesignSize = (
                783
                58)
              object Bevel6: TBevel
                Left = -766
                Top = 55
                Width = 1549
                Height = 6
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Anchors = [akLeft, akRight, akBottom]
                Shape = bsTopLine
                ExplicitWidth = 1513
              end
              object Label20: TLabel
                Left = 11
                Top = 11
                Width = 86
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Transport-Gruppe'
                WordWrap = True
              end
              object TAGroupComboBox: TComboBoxPro
                Left = 11
                Top = 25
                Width = 226
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Style = csOwnerDrawFixed
                ItemHeight = 15
                TabOrder = 0
              end
            end
            object PickShipCheckBox: TCheckBox
              Left = 215
              Top = 174
              Width = 157
              Height = 15
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Pick und Patch'
              TabOrder = 3
            end
          end
          object PlanKommTabSheet: TTabSheet
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Kommissionieren'
            ImageIndex = 1
            object Panel1: TPanel
              Left = 0
              Top = 55
              Width = 783
              Height = 55
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Align = alTop
              BevelOuter = bvNone
              TabOrder = 0
              DesignSize = (
                783
                55)
              object Bevel4: TBevel
                Left = 4
                Top = 49
                Width = 791
                Height = 5
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Anchors = [akLeft, akRight, akBottom]
                Shape = bsTopLine
                ExplicitWidth = 741
              end
              object Label4: TLabel
                Left = 15
                Top = 6
                Width = 99
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Kommablauf Planung'
              end
              object Label17: TLabel
                Left = 215
                Top = 6
                Width = 86
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Kommplan Gruppe'
              end
              object KommPlanCombobox: TComboBox
                Left = 15
                Top = 20
                Width = 157
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Style = csDropDownList
                ItemIndex = 0
                TabOrder = 0
                Text = 'LP-Reihenfolge'
                Items.Strings = (
                  'LP-Reihenfolge'
                  'Nach Gewicht des Artikels'
                  'Nach Staplefolge des Artikels'
                  'Nach Artikel und Reihenfolge')
              end
              object KommPlanGrpComboBox: TComboBox
                Left = 215
                Top = 20
                Width = 156
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Style = csOwnerDrawFixed
                ItemHeight = 15
                TabOrder = 1
                Items.Strings = (
                  'LP-Reihenfolge'
                  'Nach Gewicht des Artikels'
                  'Nach Staplefolge des Artikels'
                  'Nach Artikel und Reihenfolge')
              end
              object KommSplitPackageCheckBox: TCheckBox
                Left = 453
                Top = 8
                Width = 276
                Height = 17
                Caption = 'Pro Packst'#252'ck ein Komm-Lauf'
                TabOrder = 2
              end
            end
            object KommPanel: TPanel
              Left = 0
              Top = 110
              Width = 783
              Height = 52
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Align = alTop
              BevelOuter = bvNone
              TabOrder = 1
              DesignSize = (
                783
                52)
              object Bevel2: TBevel
                Left = 4
                Top = 48
                Width = 791
                Height = 6
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Anchors = [akLeft, akRight, akBottom]
                Shape = bsTopLine
                ExplicitWidth = 741
              end
              object Label1: TLabel
                Left = 15
                Top = 6
                Width = 74
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'VE-Kommablauf'
              end
              object Label2: TLabel
                Left = 215
                Top = 6
                Width = 74
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'WA-Abwicklung'
              end
              object KommPackComboBox: TComboBox
                Left = 15
                Top = 20
                Width = 157
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Style = csDropDownList
                TabOrder = 0
                Items.Strings = (
                  'Direkt auf NVEs'
                  'Verpacken am Packplatz')
              end
              object WAAblaufComboBox: TComboBox
                Left = 215
                Top = 20
                Width = 158
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Style = csDropDownList
                TabOrder = 1
                Items.Strings = (
                  'Automatisch abschliessen'
                  'Im WA verpacken')
              end
              object PrintMDENVECheckBox: TCheckBox
                Left = 453
                Top = 14
                Width = 230
                Height = 15
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'NVEs '#252'ber das MDE drucken'
                TabOrder = 2
              end
              object PrintMDENVEInhaltCheckBox: TCheckBox
                Left = 453
                Top = 31
                Width = 230
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'NVE-Inhalt '#252'ber das MDE drucken'
                TabOrder = 3
              end
            end
            object MDELHMCheckBox: TCheckBox
              Left = 453
              Top = 107
              Width = 276
              Height = 15
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Ladehilfsmitte nach MDE Kommissionierung erfassen'
              TabOrder = 2
            end
            object Panel3: TPanel
              Left = 0
              Top = 0
              Width = 783
              Height = 55
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Align = alTop
              BevelOuter = bvNone
              TabOrder = 3
              DesignSize = (
                783
                55)
              object Bevel10: TBevel
                Left = 4
                Top = 48
                Width = 791
                Height = 7
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Anchors = [akLeft, akRight, akBottom]
                Shape = bsTopLine
                ExplicitWidth = 741
              end
              object Label19: TLabel
                Left = 15
                Top = 6
                Width = 77
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Kommablauf-Art'
              end
              object KommArtComboBox: TComboBox
                Left = 15
                Top = 20
                Width = 157
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Style = csDropDownList
                TabOrder = 0
                Items.Strings = (
                  'Komm auf NVEs'
                  'Komm mit Verpacken'
                  'Nur von Kommpl'#228'tze'
                  'Frei aus dem Bestand'
                  'Nur volle Platten entnehmen'
                  'Pick&Pack ohne LE-Optimierung'
                  'Pick&Pack mit Optimierung'
                  'Pick&Patch')
              end
            end
            object KommMHDCheckBox: TCheckBox
              Left = 15
              Top = 167
              Width = 167
              Height = 16
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Beim Picken MHD erfassen'
              TabOrder = 4
            end
            object KommChargeCheckBox: TCheckBox
              Left = 15
              Top = 189
              Width = 167
              Height = 15
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Beim Pick Charge erfassen'
              TabOrder = 5
            end
            object KommScanSKUCheckBox: TCheckBox
              Left = 215
              Top = 189
              Width = 167
              Height = 15
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Artikel muss gescannt werden'
              TabOrder = 6
            end
            object KommScanPlaceCheckBox: TCheckBox
              Left = 215
              Top = 167
              Width = 167
              Height = 16
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Platz muss gescannt werden'
              TabOrder = 7
            end
          end
          object PlanWATabSheet: TTabSheet
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Warenausgang'
            ImageIndex = 2
            object WAPanel: TPanel
              Left = 0
              Top = 0
              Width = 783
              Height = 147
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Align = alTop
              BevelOuter = bvNone
              TabOrder = 0
              DesignSize = (
                783
                147)
              object Bevel3: TBevel
                Left = 11
                Top = 141
                Width = 1550
                Height = 6
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Anchors = [akLeft, akRight, akBottom]
                Shape = bsTopLine
                ExplicitWidth = 1514
              end
              object Label3: TLabel
                Left = 15
                Top = 17
                Width = 60
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'WA-Relation'
              end
              object Label9: TLabel
                Left = 15
                Top = 39
                Width = 45
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Packplatz'
              end
              object Label10: TLabel
                Left = 15
                Top = 74
                Width = 51
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'WA-Buffer'
              end
              object Label12: TLabel
                Left = 15
                Top = 97
                Width = 70
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'WA-Crossdock'
              end
              object Label13: TLabel
                Left = 15
                Top = 119
                Width = 58
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'WA-Umpack'
              end
              object WARelComboBox: TComboBoxPro
                Left = 93
                Top = 14
                Width = 186
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Style = csOwnerDrawFixed
                ItemHeight = 15
                TabOrder = 0
                OnChange = WAComboBoxChange
              end
              object WAPackplatzComboBox: TComboBoxPro
                Left = 93
                Top = 37
                Width = 186
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Style = csOwnerDrawFixed
                ItemHeight = 15
                TabOrder = 1
                OnChange = WAComboBoxChange
              end
              object WABufferComboBox: TComboBoxPro
                Left = 93
                Top = 72
                Width = 186
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Style = csOwnerDrawFixed
                ItemHeight = 15
                TabOrder = 2
              end
              object WACrossdockComboBox: TComboBoxPro
                Left = 93
                Top = 94
                Width = 186
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Style = csOwnerDrawFixed
                ItemHeight = 15
                TabOrder = 3
              end
              object WAUmpackComboBox: TComboBoxPro
                Left = 93
                Top = 116
                Width = 186
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Style = csOwnerDrawFixed
                ItemHeight = 15
                TabOrder = 4
              end
            end
            object Panel2: TPanel
              Left = 0
              Top = 147
              Width = 783
              Height = 120
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Align = alTop
              BevelOuter = bvNone
              TabOrder = 1
              object LHMCheckBox: TCheckBox
                Left = 282
                Top = 6
                Width = 180
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Ladehilfsmittel erfassen'
                TabOrder = 0
                OnClick = LHMCheckBoxClick
              end
              object AutoLHMCheckBox: TCheckBox
                Left = 297
                Top = 27
                Width = 180
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Automatisch bei Abschluss'
                TabOrder = 1
              end
              object WAMasterCheckBox: TCheckBox
                Left = 15
                Top = 50
                Width = 276
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'NVEs m'#252'ssen gemaster werden'
                TabOrder = 2
              end
              object SetWALPCheckBox: TCheckBox
                Left = 15
                Top = 27
                Width = 234
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'WA Platz beim ersten Abstellen fixieren'
                TabOrder = 3
              end
              object DefWALPCheckBox: TCheckBox
                Left = 15
                Top = 6
                Width = 234
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'WA-Platz bei Komm-Beginn festlegen'
                TabOrder = 4
                OnClick = DefWALPCheckBoxClick
              end
              object EmptyCheckBox: TCheckBox
                Left = 506
                Top = 6
                Width = 180
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Leergut r'#252'ckerfassen'
                TabOrder = 5
                OnClick = EmptyCheckBoxClick
              end
              object AutoEmptyCheckBox: TCheckBox
                Left = 521
                Top = 27
                Width = 180
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Automatisch bei Abschluss'
                TabOrder = 6
              end
            end
          end
          object v: TTabSheet
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Drucken'
            ImageIndex = 3
            object PrintPanel: TPanel
              Left = 0
              Top = 0
              Width = 783
              Height = 80
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Align = alTop
              BevelOuter = bvNone
              TabOrder = 0
              object Bevel8: TBevel
                Left = 230
                Top = 3
                Width = 208
                Height = 73
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
              end
              object Kopien: TLabel
                Left = 253
                Top = 53
                Width = 32
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Kopien'
              end
              object Bevel7: TBevel
                Left = 7
                Top = 3
                Width = 210
                Height = 73
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
              end
              object Bevel9: TBevel
                Left = 445
                Top = 3
                Width = 209
                Height = 73
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
              end
              object Label18: TLabel
                Left = 30
                Top = 53
                Width = 26
                Height = 13
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Kopie'
              end
              object LSPrintCheckBox: TCheckBox
                Left = 15
                Top = 13
                Width = 179
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Lieferscheine drucken'
                TabOrder = 0
                OnClick = LSPrintCheckBoxClick
              end
              object AutoLSPrintCheckBox: TCheckBox
                Left = 30
                Top = 28
                Width = 179
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Automatisch bei Abschluss'
                DragCursor = crHandPoint
                TabOrder = 1
              end
              object NVEPrintCheckBox: TCheckBox
                Left = 237
                Top = 12
                Width = 179
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Palettenlables drucken'
                TabOrder = 2
                OnClick = NVEPrintCheckBoxClick
              end
              object AutoNVEPrintCheckBox: TCheckBox
                Left = 253
                Top = 28
                Width = 179
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Automatisch bei Abschluss'
                TabOrder = 3
              end
              object NVEInhaltPrintCheckBox: TCheckBox
                Left = 468
                Top = 13
                Width = 180
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Paletteninhaltsliste drucken'
                TabOrder = 6
                OnClick = NVEInhaltPrintCheckBoxClick
              end
              object AutoNVEInhaltPrintCheckBox: TCheckBox
                Left = 483
                Top = 28
                Width = 179
                Height = 16
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Caption = 'Automatisch bei Abschluss'
                TabOrder = 7
              end
              object NVECopyEdit: TEdit
                Left = 289
                Top = 51
                Width = 25
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                TabOrder = 4
                Text = '0'
              end
              object NVECopyUpDown: TUpDown
                Left = 314
                Top = 51
                Width = 15
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Associate = NVECopyEdit
                Max = 9
                TabOrder = 5
              end
              object LSCopyEdit: TEdit
                Left = 74
                Top = 50
                Width = 24
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                TabOrder = 8
                Text = '0'
              end
              object LSCopyUpDown: TUpDown
                Left = 98
                Top = 50
                Width = 15
                Height = 21
                Margins.Left = 5
                Margins.Top = 5
                Margins.Right = 5
                Margins.Bottom = 5
                Associate = LSCopyEdit
                Max = 9
                TabOrder = 9
              end
            end
            object DeliverNotifyCheckBox: TCheckBox
              Left = 8
              Top = 104
              Width = 577
              Height = 17
              Caption = 'Bei Auslieferung Nachricht mit LS versenden'
              TabOrder = 1
            end
            object LSDMSCheckBox: TCheckBox
              Left = 8
              Top = 88
              Width = 577
              Height = 17
              Caption = 'Den Lieferschein als PDF ins DMS ablegen'
              TabOrder = 2
            end
          end
        end
        object NamePanel: TPanel
          Left = 2
          Top = 15
          Width = 807
          Height = 42
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Align = alTop
          BevelOuter = bvNone
          TabOrder = 3
          DesignSize = (
            807
            42)
          object Label14: TLabel
            Left = 7
            Top = 2
            Width = 27
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Name'
          end
          object Label16: TLabel
            Left = 201
            Top = 2
            Width = 64
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Beschreibung'
          end
          object NameEdit: TEdit
            Left = 7
            Top = 17
            Width = 176
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            TabOrder = 0
            Text = 'NameEdit'
          end
          object DescEdit: TEdit
            Left = 202
            Top = 17
            Width = 596
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            TabOrder = 1
            Text = 'DescEdit'
          end
        end
      end
      object AufAblaufDBGrid: TDBGridPro
        Left = 0
        Top = 0
        Width = 811
        Height = 218
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Align = alClient
        DataSource = AufAblaufDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 1
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
    end
  end
  object AufAblaufQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 168
    Top = 48
  end
  object AufAblaufDataSource: TDataSource
    DataSet = AufAblaufQuery
    OnDataChange = AufAblaufDataSourceDataChange
    Left = 200
    Top = 48
  end
  object AufCfgQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 168
    Top = 80
  end
  object AufCfgDataSource: TDataSource
    DataSet = AufCfgQuery
    OnDataChange = AufCfgDataSourceDataChange
    Left = 200
    Top = 80
  end
end
