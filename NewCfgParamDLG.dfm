object NewParamForm: TNewParamForm
  Left = 307
  Top = 387
  BorderStyle = bsDialog
  Caption = 'Neuer Konfigurationsparameter'
  ClientHeight = 242
  ClientWidth = 368
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    368
    242)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Label2: TLabel
    Left = 8
    Top = 96
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 144
    Width = 350
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label3: TLabel
    Left = 8
    Top = 152
    Width = 79
    Height = 13
    Caption = 'Parameter-Name'
  end
  object Label4: TLabel
    Left = 8
    Top = 52
    Width = 67
    Height = 13
    Caption = 'Niederlassung'
  end
  object MandantComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 350
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 1
    OnChange = MandantComboBoxChange
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 112
    Width = 350
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 2
  end
  object ParamComboBox: TComboBoxPro
    Left = 8
    Top = 168
    Width = 350
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 3
  end
  object AbortButton: TButton
    Left = 282
    Top = 210
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 4
  end
  object OkButton: TButton
    Left = 194
    Top = 210
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 5
  end
  object LocationComboBox: TComboBoxPro
    Left = 8
    Top = 68
    Width = 350
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 0
    OnChange = LocationComboBoxChange
  end
end
