//{$DEFINE USE_TNT}

//{$DEFINE USE_BDE}
//{$DEFINE VER_ENTERPRISE}
//{$DEFINE VER_KBMMEMTABLE} {added by G.<PERSON><PERSON> Doornink" <<EMAIL>>}
//{$DEFINE FLATSCROLLBARS}
//{$DEFINE USE_DBEXPRESS} {use DBExpress ClientDataset}

{$IFDEF VER100}
  {$DEFINE SMForDelphi3}
{$ENDIF}

{$IFDEF VER110}
  {$DEFINE SMForDelphi3}
{$ENDIF}

{$IFDEF VER120}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
{$ENDIF}

{$IFDEF VER125}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
{$ENDIF}

{$IFDEF VER130}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
{$ENDIF}

{$IFDEF VER140}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
{$ENDIF}

{$IFDEF VER150}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
{$ENDIF}

{$IFDEF VER170}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
{$ENDIF}

{$IFDEF VER180}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
  {$ENDIF}
{$ENDIF}

{$IFDEF VER185}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
{$ENDIF}

{$IFDEF VER190}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
{$ENDIF}

{$IFDEF VER200}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
{$ENDIF}

{$IFDEF VER210}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
{$ENDIF}

{$IFDEF VER220}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
{$ENDIF}

{$IFDEF VER230}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
{$ENDIF}

{$IFDEF VER240}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
{$ENDIF}

{$IFDEF VER250}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
{$ENDIF}

{$IFDEF VER260}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
  {$DEFINE SMForDelphiXE5}
{$ENDIF}

{$IFDEF VER270}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
  {$DEFINE SMForDelphiXE5}
  {$DEFINE SMForDelphiXE6}
{$ENDIF}

{$IFDEF VER280}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
  {$DEFINE SMForDelphiXE5}
  {$DEFINE SMForDelphiXE6}
  {$DEFINE SMForDelphiXE7}
{$ENDIF}

{$IFDEF VER290}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
  {$DEFINE SMForDelphiXE5}
  {$DEFINE SMForDelphiXE6}
  {$DEFINE SMForDelphiXE7}
  {$DEFINE SMForDelphiXE8}
{$ENDIF}

{$IFDEF VER300}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
  {$DEFINE SMForDelphiXE5}
  {$DEFINE SMForDelphiXE6}
  {$DEFINE SMForDelphiXE7}
  {$DEFINE SMForDelphiXE8}
  {$DEFINE SMForDelphi10Seattle}
{$ENDIF}

{$IFDEF VER310}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
  {$DEFINE SMForDelphiXE5}
  {$DEFINE SMForDelphiXE6}
  {$DEFINE SMForDelphiXE7}
  {$DEFINE SMForDelphiXE8}
  {$DEFINE SMForDelphi10Seattle}
  {$DEFINE SMForDelphi101Berlin}
{$ENDIF}

{$IFDEF VER320}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
  {$DEFINE SMForDelphiXE5}
  {$DEFINE SMForDelphiXE6}
  {$DEFINE SMForDelphiXE7}
  {$DEFINE SMForDelphiXE8}
  {$DEFINE SMForDelphi10Seattle}
  {$DEFINE SMForDelphi101Berlin}
  {$DEFINE SMForDelphi102Tokyo}
{$ENDIF}

{$IFDEF VER330}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
  {$DEFINE SMForDelphiXE5}
  {$DEFINE SMForDelphiXE6}
  {$DEFINE SMForDelphiXE7}
  {$DEFINE SMForDelphiXE8}
  {$DEFINE SMForDelphi10Seattle}
  {$DEFINE SMForDelphi101Berlin}
  {$DEFINE SMForDelphi102Tokyo}
  {$DEFINE SMForDelphi103Rio}
{$ENDIF}

{$IFDEF VER340}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
  {$DEFINE SMForDelphiXE5}
  {$DEFINE SMForDelphiXE6}
  {$DEFINE SMForDelphiXE7}
  {$DEFINE SMForDelphiXE8}
  {$DEFINE SMForDelphi10Seattle}
  {$DEFINE SMForDelphi101Berlin}
  {$DEFINE SMForDelphi102Tokyo}
  {$DEFINE SMForDelphi103Rio}
  {$DEFINE SMForDelphi104Sydney}
{$ENDIF}

{$IFDEF VER350}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
  {$DEFINE SMForDelphiXE5}
  {$DEFINE SMForDelphiXE6}
  {$DEFINE SMForDelphiXE7}
  {$DEFINE SMForDelphiXE8}
  {$DEFINE SMForDelphi10Seattle}
  {$DEFINE SMForDelphi101Berlin}
  {$DEFINE SMForDelphi102Tokyo}
  {$DEFINE SMForDelphi103Rio}
  {$DEFINE SMForDelphi11Alexandria}
{$ENDIF}

{$IFDEF VER360}
  {$DEFINE SMForDelphi3}
  {$DEFINE SMForDelphi4}
  {$DEFINE SMForDelphi5}
  {$DEFINE SMForDelphi6}
  {$DEFINE SMForDelphi7}
  {$DEFINE SMForDelphi2005}
  {$DEFINE SMForDelphi2006}
  {$IFDEF BCB}
    {$DEFINE SMForBCB2006}
    {$DEFINE SMForBCB2007}
    {$DEFINE SMForBCB2009}
    {$DEFINE SMForBCB2010}
  {$ENDIF}
  {$DEFINE SMForDelphi2007}
  {$DEFINE SMForRADStudio2007}
  {$DEFINE SMForDelphi2009}
  {$DEFINE SMForDelphi2010}
  {$DEFINE SMForDelphiXE}
  {$DEFINE SMForDelphiXE2}
  {$DEFINE SMForDelphiXE3}
  {$DEFINE SMForDelphiXE4}
  {$DEFINE SMForDelphiXE5}
  {$DEFINE SMForDelphiXE6}
  {$DEFINE SMForDelphiXE7}
  {$DEFINE SMForDelphiXE8}
  {$DEFINE SMForDelphi10Seattle}
  {$DEFINE SMForDelphi101Berlin}
  {$DEFINE SMForDelphi102Tokyo}
  {$DEFINE SMForDelphi103Rio}
  {$DEFINE SMForDelphi11Alexandria}
  {$DEFINE SMForDelphi12Athens}
{$ENDIF}

{$IFDEF SMForDelphiXE7}
  {$DEFINE SM_ADD_ComponentPlatformsAttribute}
{$ENDIF}

{$IFDEF SMForDelphi6}
  {$WARN SYMBOL_PLATFORM OFF}
{$ENDIF}

{$IFDEF SMForDelphi7}
  {$WARN SYMBOL_DEPRECATED OFF}
  {$WARN UNSAFE_CODE OFF}
  {$WARN UNSAFE_CAST OFF}
  {$WARN UNSAFE_TYPE OFF}
{$ENDIF}
