object InvCountInputForm: TInvCountInputForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Inventurz'#228'hlung erfassen'
  ClientHeight = 476
  ClientWidth = 657
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  TextHeight = 13
  object LELPPanel: TPanel
    Left = 0
    Top = 106
    Width = 657
    Height = 56
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      657
      56)
    object Label2: TLabel
      Left = 16
      Top = 8
      Width = 15
      Height = 13
      Caption = 'LP:'
    end
    object LELabel: TLabel
      Left = 72
      Top = 24
      Width = 36
      Height = 13
      Caption = 'LELabel'
    end
    object LPLabel: TLabel
      Left = 72
      Top = 8
      Width = 36
      Height = 13
      Caption = 'LPLabel'
    end
    object Label3: TLabel
      Left = 16
      Top = 24
      Width = 11
      Height = 13
      Caption = 'LE'
    end
    object Bevel2: TBevel
      Left = 16
      Top = 49
      Width = 631
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 748
    end
    object LENrEdit: TEdit
      Left = 366
      Top = 22
      Width = 121
      Height = 21
      TabOrder = 0
      Text = 'LENrEdit'
    end
  end
  object FixArtikelPanel: TPanel
    Left = 0
    Top = 0
    Width = 657
    Height = 53
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      657
      53)
    object Label1: TLabel
      Left = 16
      Top = 16
      Width = 34
      Height = 13
      Caption = 'Artikel:'
    end
    object ArtikelNrLabel: TLabel
      Left = 72
      Top = 16
      Width = 66
      Height = 13
      Caption = 'ArtikelNrLabel'
    end
    object ArtikelTextLabel: TLabel
      Left = 72
      Top = 32
      Width = 77
      Height = 13
      Caption = 'ArtikelTextLabel'
    end
    object Bevel1: TBevel
      Left = 16
      Top = 49
      Width = 631
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 748
    end
  end
  object FussPanel: TPanel
    Left = 0
    Top = 433
    Width = 657
    Height = 43
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 8
    DesignSize = (
      657
      43)
    object OkButton: TButton
      Left = 484
      Top = 8
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
    object AbortButton: TButton
      Left = 572
      Top = 8
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
  end
  object MengePanel: TPanel
    Left = 0
    Top = 162
    Width = 657
    Height = 107
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      657
      107)
    object Bevel3: TBevel
      Left = 16
      Top = 105
      Width = 631
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 81
      ExplicitWidth = 748
    end
    object Label4: TLabel
      Left = 16
      Top = 32
      Width = 89
      Height = 13
      Caption = 'verf'#252'gbare Menge'
    end
    object EinheitLabel: TLabel
      Left = 184
      Top = 32
      Width = 57
      Height = 13
      Caption = 'EinheitLabel'
    end
    object CountLabel: TLabel
      Left = 16
      Top = 0
      Width = 54
      Height = 13
      Caption = 'CountLabel'
    end
    object Label5: TLabel
      Left = 16
      Top = 56
      Width = 82
      Height = 13
      Caption = 'gesperrte Menge'
      Visible = False
    end
    object SperrEinheitLabel: TLabel
      Left = 184
      Top = 56
      Width = 83
      Height = 13
      Caption = 'SperrEinheitLabel'
      Visible = False
    end
    object Label7: TLabel
      Left = 296
      Top = 56
      Width = 54
      Height = 13
      Caption = 'Sperrgrund'
      Visible = False
    end
    object MengeEdit: TEdit
      Left = 112
      Top = 29
      Width = 66
      Height = 21
      TabOrder = 0
      Text = 'MengeEdit'
      OnExit = MengeEditExit
      OnKeyPress = MengeEditKeyPress
    end
    object SperrMengeEdit: TEdit
      Left = 112
      Top = 53
      Width = 66
      Height = 21
      TabOrder = 1
      Text = 'SperrMengeEdit'
      Visible = False
      OnExit = MengeEditExit
      OnKeyPress = MengeEditKeyPress
    end
    object SperrGrundEdit: TEdit
      Left = 366
      Top = 53
      Width = 281
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 2
      Text = 'SperrGrundEdit'
      Visible = False
    end
    object GewichtPanel: TPanel
      Left = 0
      Top = 76
      Width = 657
      Height = 31
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 3
      object Label10: TLabel
        Left = 16
        Top = 6
        Width = 69
        Height = 13
        Caption = 'Netto-Gewicht'
      end
      object Label11: TLabel
        Left = 184
        Top = 6
        Width = 11
        Height = 13
        Caption = 'kg'
      end
      object WeightDutyLabel: TLabel
        Left = 99
        Top = 2
        Width = 11
        Height = 23
        Caption = '*'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -19
        Font.Name = 'Symbol'
        Font.Style = [fsBold]
        ParentFont = False
        Visible = False
      end
      object GewichtEdit: TEdit
        Left = 112
        Top = 3
        Width = 66
        Height = 21
        TabOrder = 0
        Text = 'GewichtEdit'
        OnKeyPress = GewichtEditKeyPress
      end
    end
  end
  object SelectArtikelPanel: TPanel
    Left = 0
    Top = 53
    Width = 657
    Height = 53
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      657
      53)
    object Label6: TLabel
      Left = 16
      Top = 8
      Width = 34
      Height = 13
      Caption = 'Artikel:'
    end
    object Bevel4: TBevel
      Left = 16
      Top = 49
      Width = 631
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 748
    end
    object ArNrEdit: TEdit
      Left = 72
      Top = 5
      Width = 98
      Height = 21
      TabOrder = 0
      Text = 'ArNrEdit'
      OnExit = ArNrEditExit
    end
    object ArtikelComboBox: TComboBoxPro
      Left = 184
      Top = 5
      Width = 463
      Height = 21
      ItemDelimiter = #9
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 1
      OnChange = ArtikelComboBoxChange
      OnCloseUp = ArtikelComboBoxCloseUp
      OnDropDown = ArtikelComboBoxDropDown
    end
    object ListedCheckBox: TCheckBox
      Left = 72
      Top = 31
      Width = 343
      Height = 17
      Caption = 'nicht gelistete Artikel auch auff'#252'hren'
      TabOrder = 2
    end
  end
  object ChargePanel: TPanel
    Left = 0
    Top = 310
    Width = 657
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      657
      41)
    object Bevel5: TBevel
      Left = 16
      Top = 35
      Width = 631
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label8: TLabel
      Left = 16
      Top = 10
      Width = 35
      Height = 13
      Caption = 'Charge'
    end
    object ChargeDutyLabel: TLabel
      Left = 99
      Top = 5
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object ChargeEdit: TEdit
      Left = 112
      Top = 7
      Width = 121
      Height = 21
      TabOrder = 0
      Text = 'ChargeEdit'
    end
  end
  object MHDPanel: TPanel
    Left = 0
    Top = 269
    Width = 657
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      657
      41)
    object Bevel6: TBevel
      Left = 16
      Top = 35
      Width = 631
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label9: TLabel
      Left = 16
      Top = 10
      Width = 22
      Height = 13
      Caption = 'MHD'
    end
    object MHDDutyLabel: TLabel
      Left = 99
      Top = 6
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object MHDEdit: TEdit
      Left = 112
      Top = 7
      Width = 121
      Height = 21
      TabOrder = 0
      Text = 'MHDEdit'
      OnChange = MHDEditChange
      OnExit = MHDEditExit
    end
  end
  object BestandIDPanel: TPanel
    Left = 0
    Top = 392
    Width = 657
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 7
    DesignSize = (
      657
      41)
    object Bevel7: TBevel
      Left = 16
      Top = 35
      Width = 631
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label12: TLabel
      Left = 16
      Top = 10
      Width = 54
      Height = 13
      Caption = 'Bestand-ID'
    end
    object Label13: TLabel
      Left = 99
      Top = 5
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object BestandIDEdit: TEdit
      Left = 112
      Top = 6
      Width = 121
      Height = 21
      TabOrder = 0
      Text = 'BestandIDEdit'
    end
  end
  object ProjectIDPanel: TPanel
    Left = 0
    Top = 351
    Width = 657
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 6
    DesignSize = (
      657
      41)
    object Bevel8: TBevel
      Left = 16
      Top = 35
      Width = 631
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label14: TLabel
      Left = 16
      Top = 10
      Width = 49
      Height = 13
      Caption = 'Projekt-ID'
    end
    object Label15: TLabel
      Left = 99
      Top = 5
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object ProjectIDEdit: TEdit
      Left = 112
      Top = 7
      Width = 121
      Height = 21
      TabOrder = 0
      Text = 'ProjectIDEdit'
    end
  end
end
