unit EditBestandVorhaltenDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, ComboBoxPro;

type
  TPanelList = class (TPersistent)
  public
    AuftragArt : String;

    Panel    : TPanel;
    Edit     : TEdit;
    ArtLabel : TLabel;
  end;

  TEditBestandVorhalten = class(TForm)
    PanelScrollBox: TScrollBox;
    Panel1: TPanel;
    Panel2: TPanel;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel1: TBevel;
    DefaultPanel: TPanel;
    DefaultEdit: TEdit;
    DefaultLabel: TLabel;
    ARGrpComboBox: TComboBoxPro;
    Label1: TLabel;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure ARGrpComboBoxChange(Sender: TObject);
  private
    fList : TList;

    fRefMand    : Integer;
    fRefSubMand : Integer;

    procedure UpdateList(Sender: TObject);
  public
    property RefMand    : Integer read fRefMand    write fRefMand;
    property RefSubMand : Integer read fRefSubMand write fRefSubMand;
  end;

implementation

{$R *.dfm}

uses
  DB, ADODB,
  VCLUtilitys, DatenModul, SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditBestandVorhalten.ARGrpComboBoxChange(Sender: TObject);
begin
  UpdateList (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditBestandVorhalten.FormCreate(Sender: TObject);
begin
  fRefMand    := -1;
  fRefSubMand := -1;

  fList := TList.Create;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditBestandVorhalten.FormDestroy(Sender: TObject);
begin
  fList.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditBestandVorhalten.FormShow(Sender: TObject);
var
  i        : Integer;
  blankstr : String;
  query    : TADOQuery;
begin
  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ARGrpComboBox);
    ARGrpComboBox.Items.Add (GetResourceText (1020));

    query.SQL.Clear;
    query.SQL.Add ('select LEVEL, REF, NAME, BEZEICHNUNG from V_ARTIKEL_GRUPPE where REF_MAND=:ref_mand');
    query.Parameters.ParamByName ('ref_mand').Value := fRefMand;

    if (fRefSubMand > 0) then begin
      query.SQL.Add ('and REF_SUB_MAND=:ref_sub_mand');
      query.Parameters.ParamByName ('ref_sub_mand').Value := fRefSubMand;
    end;

    query.SQL.Add ('start with REF_PARENT is null connect by prior REF=REF_PARENT');
    query.SQL.Add ('order siblings by case when (substr (NAME,1,1) between ''0'' and ''9'') then lpad (NAME,9,'' '') else upper (NAME) end');

    try
      query.Open;

      while not (query.EOF) do begin
        blankstr := '';

        for i:= 0 to query.Fields [0].AsInteger do
          blankstr := blankstr + ' ';

        ARGrpComboBox.AddItem (blankstr+query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxRef.Create (query.Fields [1].AsInteger));

        query.Next;
      end;

      query.Close;
    except
    end;

    ARGrpComboBox.Enabled := (ARGrpComboBox.Items.Count > 0);
    ARGrpComboBox.ItemIndex := 0;
  finally
    query.Free;
  end;

  UpdateList (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditBestandVorhalten.UpdateList(Sender: TObject);
var
  i     : Integer;
  t     : Integer;
  query : TADOQuery;
  entry : TPanelList;
begin
  while fList.Count > 0 do begin
    if Assigned (TPanelList (fList [0]).Panel) and (TPanelList (fList [0]).Panel <> DefaultPanel) then begin
      TPanelList (fList [0]).Edit.Free;
      TPanelList (fList [0]).ArtLabel.Free;
      TPanelList (fList [0]).Panel.Free;
    end;

    fList.Delete (0);
  end;

  fList.Clear;

  entry := TPanelList.Create;
  entry.AuftragArt := '';
  entry.Panel := DefaultPanel;
  entry.Edit  := DefaultEdit;

  fList.Add (entry);

  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select WERT,BESCHREIBUNG from V_INF_WERTE where TABELLE=''AUFTRAG'' and SPALTE=''AUFTRAGSART''');

    query.Open;

    t := DefaultPanel.Top + DefaultPanel.Height;

    while not (query.Eof) do begin
      entry := TPanelList.Create;
      entry.AuftragArt := query.Fields [0].AsString;

      fList.Add (entry);

      entry.Panel := TPanel.Create (Self);
      entry.Panel.Parent := PanelScrollBox;
      entry.Panel.BevelOuter := bvNone;
      entry.Panel.Top    := t;
      entry.Panel.Left   := DefaultPanel.Left;
      entry.Panel.Width  := DefaultPanel.Width;
      entry.Panel.Height := DefaultPanel.Height;

      t := t + entry.Panel.Height;

      entry.ArtLabel := TLabel.Create (Self);
      entry.ArtLabel.Parent := entry.Panel;
      entry.ArtLabel.Top  := DefaultLabel.Top;
      entry.ArtLabel.Left := DefaultLabel.Left;
      entry.ArtLabel.Caption := entry.AuftragArt + '; '+query.Fields [1].AsString;

      entry.Edit := TEdit.Create (Self);
      entry.Edit.Parent := entry.Panel;
      entry.Edit.Top  := DefaultEdit.Top;
      entry.Edit.Left := DefaultEdit.Left;
      entry.Edit.Width := DefaultEdit.Width;

      query.Next;
    end;

    query.Close;

    TPanelList (fList[0]).Edit.SetFocus;

    for i:=0 to fList.Count - 1 do begin
      query.SQL.Clear;
      query.SQL.Add ('select MIN_VPE from V_AUFTRAG_ART_REL_ARTIKEL where STATUS=''AKT'' and REF_AR is null');

      if (GetComboBoxRef(ARGrpComboBox) = -1) then
        query.SQL.Add ('and REF_AR_GRUPPE is null')
      else begin
        query.SQL.Add ('and REF_AR_GRUPPE=:ref_grp');
        query.Parameters.ParamByName('ref_grp').Value := GetComboBoxRef(ARGrpComboBox);
      end;

      if (Length (TPanelList (fList [i]).AuftragArt) = 0) then
        query.SQL.Add ('and AUFTRAG_ART is null')
      else begin
        query.SQL.Add ('and AUFTRAG_ART=:auf_art');
        query.Parameters.ParamByName('auf_art').Value := TPanelList (fList [i]).AuftragArt;
      end;

      query.Open;

      if query.Fields [0].IsNull then
        TPanelList (fList [i]).Edit.Text := ''
      else
        TPanelList (fList [i]).Edit.Text := query.Fields [0].AsString;

      query.Close;
    end;
  finally
    query.Free;
  end;
end;

end.
