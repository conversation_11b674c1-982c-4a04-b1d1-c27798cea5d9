unit SelectNVEVerdichtungDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, ExtCtrls, DB, ADODB;

type
  TSelectNVEVerdichtungForm = class(TForm)
    Panel1: TPanel;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    LTInhaltDBGrid: TDBGridPro;
    Panel2: TPanel;
    OkButton: TButton;
    NVEDataSource: TDataSource;
    NVEQuery: TADOQuery;
    AbortButton: TButton;
    VorgangLabel: TLabel;
    Label5: TLabel;
    Label6: TLabel;
    AuftragCheckBox: TCheckBox;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure AuftragCheckBoxClick(Sender: TObject);
  private
    fRefNVE     : Integer;
    fRefSelNVE  : Integer;
    fRefLiefAdr : Integer;
    fRefAuftrag : Integer;
    fRefMand    : Integer;
    fRefSubMand : Integer;
    fPLZ        : String;
    fKdNr       : String;

    procedure UpdateNVEQuery (Sender: TObject);
  public
    property RefSelNVE : Integer read fRefSelNVE;

    procedure Prepare (const RefAuftrag, RefNVE : Integer);
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DatenModul, ConfigModul, DBGridUtilModule, FrontendUtils;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectNVEVerdichtungForm.Prepare (const RefAuftrag, RefNVE : Integer);
var
  query : TADOQuery;
begin
  fRefNVE     := RefNVE;
  fRefAuftrag := RefAuftrag;

  if (fRefAuftrag > 0) then begin
    query := TADOQuery.Create (Self);

    try
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Clear;
      query.SQL.Add ('select a.REF_MAND, a.REF_SUB_MAND, a.KUNDEN_NR, adr.REF_EMPF_ADR, adr.PLZ from V_AUFTRAG a, V_AUFTRAG_ADR adr where adr.REF=a.REF_LIEFER_ADR and a.REF='+IntToStr (fRefAuftrag));

      query.Open;

      fRefMand    := DBGetReferenz (query.FieldByName ('REF_MAND'));
      fRefSubMand := DBGetReferenz (query.FieldByName ('REF_SUB_MAND'));
      fKdNr       := query.FieldByName ('KUNDEN_NR').AsString;
      fPLZ        := query.FieldByName ('PLZ').AsString;
      fRefLiefAdr := DBGetReferenz (query.FieldByName ('REF_EMPF_ADR'));

      query.Close;
    finally
      query.Free;
    end;

    UpdateNVEQuery (Nil);
  end;
end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectNVEVerdichtungForm.UpdateNVEQuery (Sender: TObject);
begin
  NVEQuery.Close;

  NVEQuery.SQL.Clear;

  NVEQuery.SQL.Add ('select nve.REF,a.AUFTRAG_NR,adr.NAME1,adr.PLZ,adr.ORT,nve.NVE_NR,nve.NVE_TYPE,nve.GESAMT_VPE,nve.NETTO_GEWICHT from V_NVE_01 nve, V_AUFTRAG a, V_AUFTRAG_ADR adr');
  NVEQuery.SQL.Add ('where nve.OPT_MASTER_NVE=''0'' and nve.STATUS in (''ANG'',''AKT'',''WA'',''FIN'') and a.REF=nve.REF_AUF_KOPF and adr.REF=a.REF_LIEFER_ADR');

  if AuftragCheckBox.Checked then begin
    NVEQuery.SQL.Add ('and nve.REF_AUF_KOPF=:ref_auf_nr');
    NVEQuery.Parameters.ParamByName('ref_auf_nr').Value := fRefAuftrag;
  end else begin
    if (fRefSubMand > 0) then begin
      if (Length (fKdNr) = 0) then
        NVEQuery.SQL.Add ('and nve.REF_AUF_KOPF in (select REF from V_AUFTRAG where REF_SUB_MAND=:ref_sub_mand and REF_LIEFER_ADR in (select REF from V_AUFTRAG_ADR where PLZ=:plz))')
      else begin
        NVEQuery.SQL.Add ('and nve.REF_AUF_KOPF in (select REF from V_AUFTRAG where REF_SUB_MAND=:ref_sub_mand and KUNDEN_NR=:kd_nr and REF_LIEFER_ADR in (select REF from V_AUFTRAG_ADR where PLZ=:plz))');
        NVEQuery.Parameters.ParamByName('kd_nr').Value := fKdNr;
      end;

      NVEQuery.Parameters.ParamByName('ref_sub_mand').Value := fRefSubMand;
    end else begin
      if (Length (fKdNr) = 0) then
        NVEQuery.SQL.Add ('and nve.REF_AUF_KOPF in (select REF from V_AUFTRAG where REF_MAND=:ref_mand and REF_LIEFER_ADR in (select REF from V_AUFTRAG_ADR where PLZ=:plz))')
      else begin
        NVEQuery.SQL.Add ('and nve.REF_AUF_KOPF in (select REF from V_AUFTRAG where REF_MAND=:ref_mand and KUNDEN_NR=:kd_nr and REF_LIEFER_ADR in (select REF from V_AUFTRAG_ADR where PLZ=:plz))');
        NVEQuery.Parameters.ParamByName('kd_nr').Value := fKdNr;
      end;

      NVEQuery.Parameters.ParamByName('ref_mand').Value := fRefMand;
    end;

    NVEQuery.Parameters.ParamByName('plz').Value := fPLZ;
  end;

  NVEQuery.SQL.Add ('and nve.REF<>:ref_nve order by nve.NETTO_GEWICHT');
  NVEQuery.Parameters.ParamByName('ref_nve').Value := fRefNVE;

  NVEQuery.Open;

  DBGridUtils.SetGewichtDisplayFunctions (NVEQuery, 'NETTO_GEWICHT');
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 19.05.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectNVEVerdichtungForm.AuftragCheckBoxClick(Sender: TObject);
begin
  if (fRefAuftrag > 0) then
    UpdateNVEQuery (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectNVEVerdichtungForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  if (NVEQuery.Active) and (NVEQuery.RecNo <> -1) then
    fRefSelNVE := DBGetReferenz (NVEQuery.FieldByName ('REF'))
  else
    fRefSelNVE := -1;

  NVEQuery.Close;

  if Assigned (DBGridUtils) Then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectNVEVerdichtungForm.FormCreate(Sender: TObject);
begin
  fRefNVE     := -1;
  fRefSelNVE  := -1;
  fRefAuftrag := -1;
  fRefLiefAdr := -1;

  Label1.Caption := '';
  Label2.Caption := '';
  Label3.Caption := '';
  Label4.Caption := '';

  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectNVEVerdichtungForm.FormShow(Sender: TObject);
var
  query : TADOQuery;
begin
  LVSConfigModul.RestoreFormInfo (Self);

  Label1.Caption := 'Auftrag:';
  Label3.Caption := 'Kunde:';
  Label5.Caption := 'Anliefer Adresse:';

  query := TADOQuery.Create (Self);

  try
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select a.*,adr.REF_EMPF_ADR,adr.NAME1,adr.STRASSE,adr.LAND,adr.PLZ, adr.ORT from V_AUFTRAG a, V_AUFTRAG_ADR adr where adr.REF=a.REF_LIEFER_ADR and a.REF='+IntToStr (fRefAuftrag));

    query.Open;

    fRefAuftrag := query.FieldByName ('REF').AsInteger;
    fRefLiefAdr := DBGetReferenz (query.FieldByName ('REF_EMPF_ADR'));

    Label2.Caption := query.FieldByName ('AUFTRAG_NR').AsString;

    if query.FieldByName ('KUNDEN_NR').IsNull then
      Label4.Caption := query.FieldByName ('KUNDEN_NAME').AsString
    else
      Label4.Caption := query.FieldByName ('KUNDEN_NR').AsString + ' / ' + query.FieldByName ('KUNDEN_NAME').AsString;

    Label6.Caption := query.FieldByName ('NAME1').AsString + ' / '+query.FieldByName ('STRASSE').AsString + ' / '+query.FieldByName ('PLZ').AsString+' '+query.FieldByName ('ORT').AsString;

    query.Close;

    AuftragCheckBox.Enabled := (fRefLiefAdr > 0);
  finally
    query.Free;
  end;
end;

end.
