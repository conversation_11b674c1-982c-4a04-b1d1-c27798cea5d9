object DispAuftragKommPosForm: TDispAuftragKommPosForm
  Left = 339
  Top = 282
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'DispAuftragKommPosForm'
  ClientHeight = 414
  ClientWidth = 772
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnKeyDown = FormKeyDown
  OnShow = FormShow
  DesignSize = (
    772
    414)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 80
    Height = 13
    Caption = 'Erfasste Mengen'
  end
  object KommAufPosDBGrid: TDBGridPro
    Left = 8
    Top = 25
    Width = 755
    Height = 127
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = KommAufPosDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDrawColumnCell = KommAufPosDBGridDrawColumnCell
    OnDblClick = ChangeGewichClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 688
    Top = 381
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 6
  end
  object InfoGroupBox: TGroupBox
    Left = 8
    Top = 198
    Width = 755
    Height = 177
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Zusatzinfos'
    PopupMenu = InfoGroupBoxPopupMenu
    TabOrder = 5
    DesignSize = (
      755
      177)
    object InfoArtComboBox: TComboBoxPro
      Left = 8
      Top = 20
      Width = 377
      Height = 21
      Style = csDropDownList
      ItemHeight = 0
      TabOrder = 0
      OnChange = InfoArtComboBoxChange
    end
    object InfoMemo: TMemo
      Left = 8
      Top = 64
      Width = 739
      Height = 73
      Anchors = [akLeft, akTop, akRight, akBottom]
      Lines.Strings = (
        'Memo1')
      TabOrder = 1
      OnChange = InfoMemoChange
    end
    object SaveInfoButton: TButton
      Left = 584
      Top = 143
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      TabOrder = 2
      OnClick = SaveInfoButtonClick
    end
    object QuashInfoButton: TButton
      Left = 672
      Top = 143
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Verwerfen'
      TabOrder = 3
      OnClick = QuashInfoButtonClick
    end
  end
  object GewichtButton: TButton
    Left = 8
    Top = 158
    Width = 140
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Gewicht angeben...'
    TabOrder = 1
    OnClick = ChangeGewichClick
  end
  object DeleteButton: TButton
    Left = 623
    Top = 158
    Width = 140
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Position l'#246'schen...'
    TabOrder = 4
    OnClick = DeleteButtonClick
  end
  object MengeButton: TButton
    Left = 165
    Top = 158
    Width = 140
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Menge '#228'ndern...'
    TabOrder = 2
    OnClick = MengeButtonClick
  end
  object MHDButton: TButton
    Left = 319
    Top = 158
    Width = 140
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'MHD/Charge '#228'ndern...'
    TabOrder = 3
    OnClick = MHDButtonClick
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 432
    Top = 160
  end
  object KommAufPosADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 584
    Top = 64
  end
  object KommAufPosDataSource: TDataSource
    DataSet = KommAufPosADOQuery
    OnDataChange = KommAufPosDataSourceDataChange
    Left = 552
    Top = 64
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 624
    Top = 64
  end
  object InfoGroupBoxPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = InfoGroupBoxPopupMenuPopup
    Left = 488
    Top = 216
    object AddOrgaInfoMenuItem: TMenuItem
      Caption = 'OrgaInvent-Daten hinzuf'#252'gen'
      OnClick = AddOrgaInfoMenuItemClick
    end
    object AddSerialInfoMenuItem: TMenuItem
      Caption = 'Seriennummer hinzuf'#252'gen'
      OnClick = AddSerialInfoMenuItemClick
    end
  end
end
