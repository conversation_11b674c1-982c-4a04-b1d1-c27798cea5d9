unit ChromiumDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ComCtrls, AdvCustomControl, AdvWebBrowser;

type
  TChromiumForm = class(TForm)
    WebBrowser: TAdvWebBrowser;
    StatusBar: TStatusBar;
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  ShellAPI, ConfigModul;

procedure TChromiumForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  LVSConfigModul.SaveFormInfo (Self);
end;

procedure TChromiumForm.FormCreate(Sender: TObject);
begin
  //CefSingleProcess := false;
end;

(*
procedure TChromiumForm.WebBrowserBeforeDownload(Sender: TObject;
  const browser: ICefBrowser; const downloadItem: ICefDownloadItem;
  const suggestedName: ustring; const callback: ICefBeforeDownloadCallback);
var
  fname   : String;
  ctmpstr : array [0..MAX_PATH] of Char;
begin
  GetTempPath(MAX_PATH, @ctmpstr);

  fname := StrPas (ctmpstr) + suggestedName;

  callback.Cont(fname, True);
end;

procedure TChromiumForm.WebBrowserDownloadUpdated(Sender: TObject;
  const browser: ICefBrowser; const downloadItem: ICefDownloadItem;
  const callback: ICefDownloadItemCallback);
var
 dwres : DWORD;
begin
  if downloadItem.IsInProgress then
    StatusBar.SimpleText := IntToStr(downloadItem.PercentComplete) + '%'
  else begin
    StatusBar.SimpleText := '';

    if (ExtractFileExt(downloadItem.FullPath) = '.pdf') then begin
      dwres := ShellExecute (0, nil, PChar(downloadItem.FullPath), nil, nil, SW_SHOWNORMAL);

      if dwres <= 32 then
    end;
  end;
end;

procedure TChromiumForm.WebBrowserLoadEnd(Sender: TObject;
  const browser: ICefBrowser; const frame: ICefFrame; httpStatusCode: Integer);
begin
  Cursor := crDefault;
end;

procedure TChromiumForm.WebBrowserLoadError(Sender: TObject;
  const browser: ICefBrowser; const frame: ICefFrame; errorCode: Integer;
  const errorText, failedUrl: ustring);
begin
  Cursor := crDefault;
end;

procedure TChromiumForm.WebBrowserLoadStart(Sender: TObject; const browser: ICefBrowser; const frame: ICefFrame);
begin
  Cursor := crHourGlass;
end;

procedure TChromiumForm.WebBrowserStatusMessage(Sender: TObject;
  const browser: ICefBrowser; const value: ustring);
begin
  StatusBar.SimpleText := value
end;

procedure TChromiumForm.WebBrowserTitleChange(Sender: TObject;
  const browser: ICefBrowser; const title: ustring);
begin
  Caption := title;
end;
*)

initialization
  //CefBrowserSubprocessPath := 'cromelib\wow_helper.exe';

end.
