object MandantForm: TMandantForm
  Left = 379
  Top = 431
  BorderIcons = [biSystemMenu]
  Caption = 'Mandantenverwaltung'
  ClientHeight = 460
  ClientWidth = 660
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    660
    460)
  TextHeight = 13
  object Bevel1: TBevel
    Left = 8
    Top = 418
    Width = 636
    Height = 6
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 401
    ExplicitWidth = 630
  end
  object CloseButton: TButton
    Left = 547
    Top = 430
    Width = 97
    Height = 24
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 0
  end
  object MandantPanel: TPanel
    Left = 0
    Top = 0
    Width = 660
    Height = 178
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      660
      178)
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 54
      Height = 13
      Caption = 'Mandanten'
    end
    object MandStringGrid: TStringGridPro
      Left = 8
      Top = 24
      Width = 519
      Height = 150
      Anchors = [akLeft, akTop, akRight, akBottom]
      ColCount = 3
      DefaultColWidth = 20
      DefaultRowHeight = 20
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
      PopupMenu = MandPopupMenu
      TabOrder = 0
      OnClick = MandStringGridClick
      OnDblClick = MandStringGridDblClick
      GridStyle.OddColor = clInfoBk
      TitelTexte.Strings = (
        ''
        'Name'
        'Beschreibung')
      TitelFont.Charset = DEFAULT_CHARSET
      TitelFont.Color = clWindowText
      TitelFont.Height = -11
      TitelFont.Name = 'MS Sans Serif'
      TitelFont.Style = [fsBold]
      ColWidths = (
        20
        124
        288)
    end
    object NeuButton: TButton
      Left = 547
      Top = 24
      Width = 97
      Height = 25
      Anchors = [akTop, akRight]
      Caption = '&Neu...'
      TabOrder = 1
      OnClick = NeuButtonClick
    end
    object CopyButton: TButton
      Left = 547
      Top = 54
      Width = 97
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Kopieren...'
      TabOrder = 2
      OnClick = CopyButtonClick
    end
    object ChangeButton: TButton
      Left = 547
      Top = 84
      Width = 97
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Bearbeiten...'
      Enabled = False
      TabOrder = 3
      OnClick = MandStringGridDblClick
    end
    object DelButton: TButton
      Left = 547
      Top = 149
      Width = 97
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = '&L'#246'schen'
      Enabled = False
      TabOrder = 4
      Visible = False
    end
  end
  object SubMandantPanel: TPanel
    Left = 0
    Top = 178
    Width = 660
    Height = 234
    Align = alTop
    Anchors = [akLeft, akTop, akRight, akBottom]
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      660
      234)
    object Untermandanten: TLabel
      Left = 8
      Top = 19
      Width = 79
      Height = 13
      Caption = 'Untermandanten'
    end
    object SubMandStringGrid: TStringGridPro
      Left = 8
      Top = 36
      Width = 519
      Height = 198
      Anchors = [akLeft, akTop, akRight, akBottom]
      ColCount = 6
      DefaultColWidth = 20
      DefaultRowHeight = 20
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
      PopupMenu = SubMandStringGridPopupMenu
      TabOrder = 0
      OnDblClick = SubMandStringGridDblClick
      OnPostDrawCell = SubMandStringGridPostDrawCell
      GridStyle.OddColor = clInfoBk
      TitelTexte.Strings = (
        ''
        'Status'
        'Name'
        'Beschreibung'
        'ERP-ID'
        'NL aktiv')
      TitelFont.Charset = DEFAULT_CHARSET
      TitelFont.Color = clWindowText
      TitelFont.Height = -11
      TitelFont.Name = 'MS Sans Serif'
      TitelFont.Style = [fsBold]
      ColWidths = (
        20
        65
        108
        133
        80
        70)
    end
    object SubMandNewButton: TButton
      Left = 547
      Top = 34
      Width = 97
      Height = 25
      Anchors = [akTop, akRight]
      Caption = '&Neu...'
      TabOrder = 1
      OnClick = SubMandNewButtonClick
    end
    object SubMandCopyButton: TButton
      Left = 547
      Top = 64
      Width = 97
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Kopieren...'
      TabOrder = 2
      OnClick = SubMandCopyButtonClick
    end
    object SubMandChangeButton: TButton
      Left = 547
      Top = 95
      Width = 97
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Bearbeiten...'
      Enabled = False
      TabOrder = 3
      OnClick = SubMandStringGridDblClick
    end
    object SubMandDelButton: TButton
      Left = 547
      Top = 208
      Width = 97
      Height = 24
      Anchors = [akRight, akBottom]
      Caption = '&L'#246'schen'
      Enabled = False
      TabOrder = 4
      OnClick = SubMandDelMenuItemClick
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 24
    Top = 144
  end
  object MandPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = MandPopupMenuPopup
    Left = 216
    Top = 136
    object MandCopyColMenuitem: TMenuItem
      Caption = 'Kopieren'
      ImageIndex = 26
      OnClick = StringGridCopyColMenuItemClick
    end
    object MandColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      ImageIndex = 27
      OnClick = StringGridColOptimalMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object MandConfigMenuItem: TMenuItem
      Caption = 'Konfiguration...'
      OnClick = ConfigButtonClick
    end
    object MandCommMenuItem: TMenuItem
      Caption = 'Kommunikation...'
      OnClick = CommButtonClick
    end
    object MandLagerMenuItem: TMenuItem
      Caption = 'Lager...'
      OnClick = LagerButtonClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object MandCheckConfigMenuItem: TMenuItem
      Caption = 'Konfiguration '#252'berpr'#252'fen...'
      OnClick = MandCheckConfigMenuItemClick
    end
  end
  object SubMandStringGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = SubMandStringGridPopupMenuPopup
    Left = 360
    Top = 264
    object SubMandCopyColMenuitem: TMenuItem
      Caption = 'Kopieren'
      ImageIndex = 26
      OnClick = StringGridCopyColMenuItemClick
    end
    object SubMandColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      ImageIndex = 27
      OnClick = StringGridColOptimalMenuItemClick
    end
    object N3: TMenuItem
      Caption = '-'
    end
    object SubMandLocationMenuItem: TMenuItem
      Caption = 'Einer Niederlassung zuordnen...'
      OnClick = SubMandLocationMenuItemClick
    end
    object SubMandDeactiveMenuItem: TMenuItem
      Caption = 'Deaktivieren..'
      ImageIndex = 22
      OnClick = SubMandDeactiveMenuItemClick
    end
    object SubMandActiveMenuItem: TMenuItem
      Caption = 'Aktivieren...'
      ImageIndex = 28
      OnClick = SubMandActiveMenuItemClick
    end
    object SubMandDelMenuItem: TMenuItem
      Caption = 'L'#246'schen...'
      ImageIndex = 23
      OnClick = SubMandDelMenuItemClick
    end
  end
end
