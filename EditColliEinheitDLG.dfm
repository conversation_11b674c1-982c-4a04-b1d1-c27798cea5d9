object EditColliEinheitenForm: TEditColliEinheitenForm
  Left = 232
  Top = 92
  ClientHeight = 553
  ClientWidth = 404
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    404
    553)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 32
    Height = 13
    Caption = 'Einheit'
  end
  object Label5: TLabel
    Left = 8
    Top = 136
    Width = 22
    Height = 13
    Caption = 'EAN'
  end
  object Bevel2: TBevel
    Left = 6
    Top = 237
    Width = 394
    Height = 4
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label4: TLabel
    Left = 8
    Top = 191
    Width = 63
    Height = 13
    Caption = 'Nettogewicht'
  end
  object Label6: TLabel
    Left = 136
    Top = 191
    Width = 65
    Height = 13
    Caption = 'Bruttogewicht'
  end
  object Bevel3: TBevel
    Left = 6
    Top = 183
    Width = 394
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel5: TBevel
    Left = 6
    Top = 347
    Width = 394
    Height = 4
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label7: TLabel
    Left = 8
    Top = 246
    Width = 30
    Height = 13
    Caption = 'L'#228'nge'
  end
  object Label8: TLabel
    Left = 88
    Top = 246
    Width = 27
    Height = 13
    Caption = 'Breite'
  end
  object Label9: TLabel
    Left = 168
    Top = 246
    Width = 26
    Height = 13
    Caption = 'H'#246'he'
  end
  object Label10: TLabel
    Left = 8
    Top = 301
    Width = 124
    Height = 13
    Caption = 'Anzahl Einheiten pro Lage'
  end
  object Label11: TLabel
    Left = 168
    Top = 301
    Width = 133
    Height = 13
    Caption = 'Anzahl Einheiten pro Palette'
  end
  object Label13: TLabel
    Left = 85
    Top = 210
    Width = 23
    Height = 13
    Caption = 'in kg'
  end
  object Label14: TLabel
    Left = 213
    Top = 210
    Width = 23
    Height = 13
    Caption = 'in kg'
  end
  object Label15: TLabel
    Left = 256
    Top = 265
    Width = 54
    Height = 13
    Caption = 'in Millimeter'
  end
  object Bevel8: TBevel
    Left = 6
    Top = 58
    Width = 394
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label22: TLabel
    Left = 136
    Top = 137
    Width = 40
    Height = 13
    Caption = 'Barcode'
  end
  object Bevel10: TBevel
    Left = 6
    Top = 292
    Width = 394
    Height = 4
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label2: TLabel
    Left = 8
    Top = 72
    Width = 56
    Height = 13
    Caption = 'Bezeichung'
  end
  object Beschreibung: TLabel
    Left = 136
    Top = 72
    Width = 65
    Height = 13
    Caption = 'Beschreibung'
  end
  object Bevel1: TBevel
    Left = 6
    Top = 122
    Width = 394
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object OkButton: TButton
    Left = 238
    Top = 521
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 14
    OnClick = OkButtonClick
  end
  object AbortButton: TButton
    Left = 323
    Top = 521
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 15
  end
  object EinheitComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 305
    Height = 21
    Style = csOwnerDrawFixed
    ItemHeight = 15
    TabOrder = 0
  end
  object NettoEdit: TEdit
    Left = 8
    Top = 207
    Width = 70
    Height = 21
    TabOrder = 6
    Text = 'NettoEdit'
    OnExit = NettoEditExit
    OnKeyPress = FloatEditKeyPress
  end
  object BruttoEdit: TEdit
    Left = 136
    Top = 207
    Width = 70
    Height = 21
    TabOrder = 7
    Text = 'BruttoEdit'
    OnExit = BruttoEditExit
    OnKeyPress = FloatEditKeyPress
  end
  object LaengeEdit: TEdit
    Left = 8
    Top = 262
    Width = 57
    Height = 21
    MaxLength = 4
    TabOrder = 8
    Text = 'LaengeEdit'
    OnKeyPress = IntEditKeyPress
  end
  object BreiteEdit: TEdit
    Left = 88
    Top = 262
    Width = 57
    Height = 21
    MaxLength = 4
    TabOrder = 9
    Text = 'BreiteEdit'
    OnKeyPress = IntEditKeyPress
  end
  object HoeheEdit: TEdit
    Left = 168
    Top = 262
    Width = 57
    Height = 21
    MaxLength = 4
    TabOrder = 10
    Text = 'HoeheEdit'
    OnKeyPress = IntEditKeyPress
  end
  object EANEdit: TEdit
    Left = 8
    Top = 155
    Width = 111
    Height = 21
    MaxLength = 32
    TabOrder = 4
    Text = 'Edit'
    OnChange = EANEditChange
    OnExit = EANEditExit
    OnKeyPress = EANEditKeyPress
  end
  object LagenFaktorEdit: TEdit
    Left = 8
    Top = 317
    Width = 57
    Height = 21
    TabOrder = 11
    Text = 'LagenFaktorEdit'
    OnKeyPress = IntEditKeyPress
  end
  object PalFaktorEdit: TEdit
    Left = 168
    Top = 317
    Width = 57
    Height = 21
    TabOrder = 12
    Text = 'PalFaktorEdit'
    OnKeyPress = IntEditKeyPress
  end
  object VEPButton: TButton
    Left = 323
    Top = 24
    Width = 75
    Height = 21
    Caption = 'Anlegen...'
    TabOrder = 1
    OnClick = VEPButtonClick
  end
  object BarcodeEdit: TEdit
    Left = 136
    Top = 156
    Width = 260
    Height = 21
    MaxLength = 32
    TabOrder = 5
    Text = 'Edit'
    OnExit = EANEditExit
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 356
    Width = 390
    Height = 151
    ActivePage = KommTabSheet
    TabOrder = 13
    object KommTabSheet: TTabSheet
      Caption = 'Kommissionierung'
      ImageIndex = 6
      ExplicitLeft = 0
      ExplicitTop = 0
      ExplicitWidth = 0
      ExplicitHeight = 0
      object Label32: TLabel
        Left = 8
        Top = 16
        Width = 78
        Height = 13
        Caption = 'Kommissionierart'
      end
      object KommArtComboBox: TComboBoxPro
        Left = 8
        Top = 32
        Width = 370
        Height = 22
        Style = csOwnerDrawFixed
        ItemHeight = 16
        TabOrder = 0
      end
      object BigItemCheckBox: TCheckBox
        Left = 8
        Top = 78
        Width = 370
        Height = 17
        Caption = 'Big Item'
        TabOrder = 1
      end
    end
    object LTTabSheet: TTabSheet
      Caption = 'Ladungstr'#228'ger'
      ImageIndex = 1
      ExplicitLeft = 0
      ExplicitTop = 0
      ExplicitWidth = 0
      ExplicitHeight = 0
      DesignSize = (
        382
        123)
      object Label17: TLabel
        Left = 8
        Top = 64
        Width = 108
        Height = 13
        Caption = 'Standard H'#246'henklasse'
      end
      object Label18: TLabel
        Left = 8
        Top = 16
        Width = 114
        Height = 13
        Caption = 'Standard Ladungstr'#228'ger'
      end
      object Label21: TLabel
        Left = 264
        Top = 64
        Width = 54
        Height = 13
        Caption = 'Stapelh'#246'he'
      end
      object Label19: TLabel
        Left = 321
        Top = 83
        Width = 16
        Height = 13
        Caption = 'mm'
      end
      object HKLComboBox: TComboBoxPro
        Left = 8
        Top = 80
        Width = 225
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 1
      end
      object LTComboBox: TComboBoxPro
        Left = 8
        Top = 32
        Width = 365
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 0
      end
      object PalHeightEdit: TEdit
        Left = 264
        Top = 80
        Width = 50
        Height = 21
        MaxLength = 4
        TabOrder = 2
        Text = 'PalHeightEdit'
        OnKeyPress = IntEditKeyPress
      end
    end
    object VersandTabSheet: TTabSheet
      Caption = 'Versand'
      ImageIndex = 2
      ExplicitLeft = 0
      ExplicitTop = 0
      ExplicitWidth = 0
      ExplicitHeight = 0
      object ReadyShipCheckBox: TCheckBox
        Left = 8
        Top = 16
        Width = 357
        Height = 17
        Caption = 'Die Einheit ist versandf'#228'hig verpackt'
        TabOrder = 0
      end
      object SperrgutCheckBox: TCheckBox
        Left = 8
        Top = 39
        Width = 357
        Height = 17
        Caption = 'Die Einheit muss als Sperrgut verschickt werden'
        TabOrder = 1
      end
    end
  end
  object NameEdit: TEdit
    Left = 9
    Top = 88
    Width = 110
    Height = 21
    TabOrder = 2
    Text = 'NameEdit'
  end
  object DescEdit: TEdit
    Left = 136
    Top = 88
    Width = 260
    Height = 21
    TabOrder = 3
    Text = 'DescEdit'
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 360
    Top = 144
  end
end
