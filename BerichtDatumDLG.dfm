object BerichtDatumForm: TBerichtDatumForm
  Left = 542
  Top = 180
  BorderStyle = bsDialog
  Caption = 'BerichtDatumForm'
  ClientHeight = 738
  ClientWidth = 281
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poMainFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    281
    738)
  PixelsPerInch = 96
  TextHeight = 13
  object Bevel3: TBevel
    Left = 4
    Top = 693
    Width = 272
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 586
  end
  object ShowButton: TButton
    Left = 8
    Top = 706
    Width = 137
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Bericht anzeigen...'
    Default = True
    TabOrder = 10
    OnClick = ShowButtonClick
  end
  object AbortButton: TButton
    Left = 186
    Top = 706
    Width = 88
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 11
  end
  object VorschauCheckBox: TCheckBox
    Left = 8
    Top = 669
    Width = 217
    Height = 17
    Anchors = [akLeft, akBottom]
    Caption = 'Mit Druckvorschau'
    Checked = True
    State = cbChecked
    TabOrder = 9
  end
  object LagerPanel: TPanel
    Left = 0
    Top = 114
    Width = 281
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      281
      57)
    object LagerLabel: TLabel
      Left = 8
      Top = 8
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel2: TBevel
      Left = 4
      Top = 56
      Width = 272
      Height = 9
      Shape = bsTopLine
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 266
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
      OnChange = LagerComboBoxChange
    end
  end
  object MandantPanel: TPanel
    Left = 0
    Top = 0
    Width = 281
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      281
      57)
    object Label3: TLabel
      Left = 8
      Top = 8
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object Bevel4: TBevel
      Left = 4
      Top = 56
      Width = 272
      Height = 9
      Shape = bsTopLine
    end
    object MandantComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 266
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object DatumPanel: TPanel
    Left = 0
    Top = 171
    Width = 281
    Height = 104
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      281
      104)
    object DatumLabel: TLabel
      Left = 8
      Top = 8
      Width = 31
      Height = 13
      Caption = 'Datum'
    end
    object BisDatumLabel: TLabel
      Left = 169
      Top = 8
      Width = 48
      Height = 13
      Caption = 'Bis Datum'
    end
    object Bevel1: TBevel
      Left = 4
      Top = 101
      Width = 272
      Height = 23
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 141
    end
    object DatumDateTimePicker: TDateTimePicker
      Left = 8
      Top = 24
      Width = 105
      Height = 21
      Date = 38275.496297569450000000
      Time = 38275.496297569450000000
      TabOrder = 0
    end
    object BisDatumDateTimePicker: TDateTimePicker
      Left = 169
      Top = 24
      Width = 105
      Height = 21
      Anchors = [akTop, akRight]
      Date = 38275.496297569450000000
      Time = 38275.496297569450000000
      TabOrder = 1
      Visible = False
      OnChange = BisDatumDateTimePickerChange
    end
    object DatumZeitPanel: TPanel
      Left = 0
      Top = 54
      Width = 281
      Height = 50
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 2
      Visible = False
      DesignSize = (
        281
        50)
      object Label1: TLabel
        Left = 8
        Top = 0
        Width = 33
        Height = 13
        Caption = 'Uhrzeit'
      end
      object BisDatumZeitLabel: TLabel
        Left = 169
        Top = 0
        Width = 33
        Height = 13
        Caption = 'Uhrzeit'
      end
      object Bevel13: TBevel
        Left = 4
        Top = 47
        Width = 272
        Height = 23
        Anchors = [akLeft, akRight, akBottom]
        Shape = bsTopLine
        ExplicitTop = 141
      end
      object DatumZeitTimePicker: TDateTimePicker
        Left = 8
        Top = 16
        Width = 105
        Height = 21
        Date = 38275.496297569450000000
        Time = 38275.496297569450000000
        Kind = dtkTime
        TabOrder = 0
      end
      object BisDatumZeitTimePicker: TDateTimePicker
        Left = 169
        Top = 16
        Width = 105
        Height = 21
        Anchors = [akTop, akRight]
        Date = 38275.496297569450000000
        Time = 38275.496297569450000000
        Kind = dtkTime
        TabOrder = 1
        Visible = False
        OnChange = BisDatumDateTimePickerChange
      end
    end
  end
  object SortPanel: TPanel
    Left = 0
    Top = 560
    Width = 281
    Height = 55
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 7
    Visible = False
    DesignSize = (
      281
      55)
    object Bevel5: TBevel
      Left = 4
      Top = 54
      Width = 272
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label4: TLabel
      Left = 8
      Top = 8
      Width = 69
      Height = 13
      Caption = 'Sortieren nach'
    end
    object SortARRadioButton: TRadioButton
      Left = 8
      Top = 28
      Width = 113
      Height = 17
      Caption = 'Artikel'
      TabOrder = 0
    end
    object SortLPRadioButton: TRadioButton
      Left = 88
      Top = 28
      Width = 89
      Height = 17
      Caption = 'Lagerplatz'
      TabOrder = 1
    end
    object SortMHDRadioButton: TRadioButton
      Left = 183
      Top = 28
      Width = 89
      Height = 17
      Caption = 'MHD'
      TabOrder = 2
    end
  end
  object CheckPanel: TPanel
    Left = 0
    Top = 615
    Width = 281
    Height = 49
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 8
    Visible = False
    DesignSize = (
      281
      49)
    object Bevel6: TBevel
      Left = 4
      Top = 45
      Width = 273
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 54
      ExplicitWidth = 232
    end
    object CheckBox1: TCheckBox
      Left = 8
      Top = 6
      Width = 225
      Height = 17
      Caption = 'CheckBox1'
      TabOrder = 0
      Visible = False
    end
    object CheckBox2: TCheckBox
      Left = 8
      Top = 23
      Width = 228
      Height = 17
      Caption = 'CheckBox2'
      TabOrder = 1
      Visible = False
    end
  end
  object AuswahlPanel: TPanel
    Left = 0
    Top = 275
    Width = 281
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    Visible = False
    DesignSize = (
      281
      57)
    object AuswahlLabel: TLabel
      Left = 8
      Top = 8
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel7: TBevel
      Left = 4
      Top = 56
      Width = 272
      Height = 9
      Shape = bsTopLine
    end
    object AuswahlComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 266
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
      OnChange = AuswahlComboBoxChange
      OnDropDown = AuswahlComboBoxDropDown
    end
  end
  object InputPanel: TPanel
    Left = 0
    Top = 446
    Width = 281
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    Visible = False
    DesignSize = (
      281
      57)
    object InputLabel: TLabel
      Left = 8
      Top = 8
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel8: TBevel
      Left = 4
      Top = 56
      Width = 272
      Height = 9
      Shape = bsTopLine
    end
    object InputEdit: TEdit
      Left = 8
      Top = 24
      Width = 266
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      Text = 'InputEdit'
      OnKeyPress = InputEditKeyPress
    end
  end
  object DetailAuswahlPanel: TPanel
    Left = 0
    Top = 389
    Width = 281
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    Visible = False
    DesignSize = (
      281
      57)
    object DetailAuswahlLabel: TLabel
      Left = 8
      Top = 8
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel9: TBevel
      Left = 4
      Top = 56
      Width = 272
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object DetailAuswahlComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 266
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
    end
  end
  object InputPanel2: TPanel
    Left = 0
    Top = 503
    Width = 281
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 6
    Visible = False
    DesignSize = (
      281
      57)
    object InputLabel2: TLabel
      Left = 8
      Top = 8
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel10: TBevel
      Left = 4
      Top = 56
      Width = 272
      Height = 9
      Shape = bsTopLine
    end
    object InputEdit2: TEdit
      Left = 8
      Top = 24
      Width = 266
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      Text = 'InputEdit'
      OnKeyPress = InputEditKeyPress
    end
  end
  object AuswahlEditPanel: TPanel
    Left = 0
    Top = 332
    Width = 281
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 12
    Visible = False
    DesignSize = (
      281
      57)
    object AuswahlEditLabel: TLabel
      Left = 8
      Top = 8
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel11: TBevel
      Left = 4
      Top = 56
      Width = 272
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object AuswahlEditComboBox: TComboBoxPro
      Left = 96
      Top = 24
      Width = 178
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 1
      OnCloseUp = AuswahlEditComboBoxCloseUp
      OnDropDown = AuswahlComboBoxDropDown
    end
    object AuswahlEdit: TEdit
      Left = 8
      Top = 24
      Width = 82
      Height = 21
      TabOrder = 0
      Text = 'AuswahlEdit'
    end
  end
  object LocationPanel: TPanel
    Left = 0
    Top = 57
    Width = 281
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 13
    Visible = False
    DesignSize = (
      281
      57)
    object LocationLabel: TLabel
      Left = 8
      Top = 8
      Width = 67
      Height = 13
      Caption = 'Niederlassung'
    end
    object Bevel12: TBevel
      Left = 4
      Top = 56
      Width = 272
      Height = 9
      Shape = bsTopLine
    end
    object LocationComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 266
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
      OnChange = LocationComboBoxChange
    end
  end
end
