unit SprachDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro;

type
  TChangeSpracheForm = class(TForm)
    SprachComboBox: TComboBoxPro;
    Label1: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    Label2: TLabel;
    DBSprachComboBox: TComboBoxPro;
    procedure FormDestroy(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    procedure Prepare (const DBSprache : String);
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, SprachModul, CompTranslate, FrontendUtils;

procedure TChangeSpracheForm.Prepare (const DBSprache : String);
var
  idx     : Integer;
  langstr : String;
begin
  SprachComboBox.Items.Assign (LVSSprachModul.LVSCompTranslate.LangTypes);

  idx := 0;
  while (idx < SprachComboBox.Items.Count) do begin
    langstr := LVSSprachModul.LVSCompTranslate.GetEntryText (SprachComboBox.Items [idx], tlFixtext, '');

    if (Length (langstr) > 0) then
      SprachComboBox.Items [idx] := SprachComboBox.Items [idx] + '|' + langstr;

    Inc (idx);
  end;

  SprachComboBox.ItemIndex := SprachComboBox.IndexOf (LVSSprachModul.AktSprache);

  LoadComboxDBItems (DBSprachComboBox, 'ARTIKEL_TEXTE', 'SPRACHE');

  if (DBSprachComboBox.IndexOf (DBSprache) = -1) then
    DBSprachComboBox.Items.Add (DBSprache);
  DBSprachComboBox.ItemIndex := DBSprachComboBox.IndexOf(DBSprache);
end;

procedure TChangeSpracheForm.FormCreate(Sender: TObject);
begin
  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, SprachComboBox);
    LVSSprachModul.SetNoTranslate (Self, DBSprachComboBox);
  {$endif}
end;

procedure TChangeSpracheForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects(SprachComboBox);
  ClearComboBoxObjects(DBSprachComboBox);
end;

end.
