object SendITSetupForm: TSendITSetupForm
  Left = 0
  Top = 0
  Caption = 'SendITSetupForm'
  ClientHeight = 476
  ClientWidth = 852
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    852
    476)
  TextHeight = 13
  object Label3: TLabel
    Left = 8
    Top = 5
    Width = 70
    Height = 13
    Caption = 'SendIT-Server'
  end
  object SendITPageControl: TPageControl
    Left = 8
    Top = 48
    Width = 836
    Height = 378
    ActivePage = ClientTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 0
    object ClientTabSheet: TTabSheet
      Caption = 'Mandanten'
      OnShow = ClientTabSheetShow
      object ClientDBGrid: TDBGridPro
        Left = 0
        Top = 0
        Width = 828
        Height = 350
        Align = alClient
        DataSource = ClientDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsNormal
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
    end
    object ShipperTabSheet: TTabSheet
      Caption = 'Versender'
      ImageIndex = 1
      OnShow = ShipperTabSheetShow
      DesignSize = (
        828
        350)
      object Label1: TLabel
        Left = 3
        Top = 8
        Width = 42
        Height = 13
        Caption = 'Mandant'
      end
      object ClientComboBox: TComboBoxPro
        Left = 3
        Top = 24
        Width = 814
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 0
        OnChange = ClientComboBoxChange
      end
      object ShipperPageControl: TPageControl
        Left = 3
        Top = 56
        Width = 814
        Height = 282
        ActivePage = PCHTabSheet
        Anchors = [akLeft, akTop, akRight, akBottom]
        TabOrder = 1
        object DHLTabSheet: TTabSheet
          Caption = 'DHL'
          OnShow = DHLTabSheetShow
          object DHLGridPro: TDBGridPro
            Left = 0
            Top = 0
            Width = 806
            Height = 254
            Align = alClient
            DataSource = ShipperDataSource
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
            ReadOnly = True
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Flat = False
            BandsFont.Charset = DEFAULT_CHARSET
            BandsFont.Color = clWindowText
            BandsFont.Height = -11
            BandsFont.Name = 'Tahoma'
            BandsFont.Style = []
            Groupings = <>
            GridStyle.Style = gsNormal
            GridStyle.OddColor = clWindow
            GridStyle.EvenColor = clWindow
            TitleHeight.PixelCount = 24
            FooterColor = clBtnFace
            ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
            RegistryKey = 'Software\Scalabium'
            RegistrySection = 'SMDBGrid'
            WidthOfIndicator = 11
            DefaultRowHeight = 17
            ScrollBars = ssHorizontal
            ColCount = 2
            RowCount = 2
          end
        end
        object UPSTabSheet: TTabSheet
          Caption = 'UPS'
          ImageIndex = 1
          OnShow = UPSTabSheetShow
          object UPSGridPro: TDBGridPro
            Left = 0
            Top = 0
            Width = 806
            Height = 254
            Align = alClient
            DataSource = ShipperDataSource
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
            ReadOnly = True
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Flat = False
            BandsFont.Charset = DEFAULT_CHARSET
            BandsFont.Color = clWindowText
            BandsFont.Height = -11
            BandsFont.Name = 'Tahoma'
            BandsFont.Style = []
            Groupings = <>
            GridStyle.Style = gsNormal
            GridStyle.OddColor = clWindow
            GridStyle.EvenColor = clWindow
            TitleHeight.PixelCount = 24
            FooterColor = clBtnFace
            ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
            RegistryKey = 'Software\Scalabium'
            RegistrySection = 'SMDBGrid'
            WidthOfIndicator = 11
            DefaultRowHeight = 17
            ScrollBars = ssHorizontal
            ColCount = 2
            RowCount = 2
          end
        end
        object DPDTabSheet: TTabSheet
          Caption = 'DPD'
          ImageIndex = 2
          OnShow = DPDTabSheetShow
          object DPDGridPro: TDBGridPro
            Left = 0
            Top = 0
            Width = 806
            Height = 254
            Align = alClient
            DataSource = ShipperDataSource
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
            ReadOnly = True
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Flat = False
            BandsFont.Charset = DEFAULT_CHARSET
            BandsFont.Color = clWindowText
            BandsFont.Height = -11
            BandsFont.Name = 'Tahoma'
            BandsFont.Style = []
            Groupings = <>
            GridStyle.Style = gsNormal
            GridStyle.OddColor = clWindow
            GridStyle.EvenColor = clWindow
            TitleHeight.PixelCount = 24
            FooterColor = clBtnFace
            ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
            RegistryKey = 'Software\Scalabium'
            RegistrySection = 'SMDBGrid'
            WidthOfIndicator = 11
            DefaultRowHeight = 17
            ScrollBars = ssHorizontal
            ColCount = 2
            RowCount = 2
          end
        end
        object GLSTabSheet: TTabSheet
          Caption = 'GLS'
          ImageIndex = 3
          OnShow = GLSTabSheetShow
          object GLSDBGrid: TDBGridPro
            Left = 0
            Top = 0
            Width = 806
            Height = 254
            Align = alClient
            DataSource = ShipperDataSource
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
            ReadOnly = True
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Flat = False
            BandsFont.Charset = DEFAULT_CHARSET
            BandsFont.Color = clWindowText
            BandsFont.Height = -11
            BandsFont.Name = 'Tahoma'
            BandsFont.Style = []
            Groupings = <>
            GridStyle.Style = gsNormal
            GridStyle.OddColor = clWindow
            GridStyle.EvenColor = clWindow
            TitleHeight.PixelCount = 24
            FooterColor = clBtnFace
            ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
            RegistryKey = 'Software\Scalabium'
            RegistrySection = 'SMDBGrid'
            WidthOfIndicator = 11
            DefaultRowHeight = 17
            ScrollBars = ssHorizontal
            ColCount = 2
            RowCount = 2
          end
        end
        object DHL2MHTabSheet: TTabSheet
          Caption = 'DHL 2 Mann'
          ImageIndex = 4
          OnShow = DHL2MHTabSheetShow
          object DHL2MHDBGrid: TDBGridPro
            Left = 0
            Top = 0
            Width = 806
            Height = 254
            Align = alClient
            DataSource = ShipperDataSource
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
            ReadOnly = True
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Flat = False
            BandsFont.Charset = DEFAULT_CHARSET
            BandsFont.Color = clWindowText
            BandsFont.Height = -11
            BandsFont.Name = 'Tahoma'
            BandsFont.Style = []
            Groupings = <>
            GridStyle.Style = gsNormal
            GridStyle.OddColor = clWindow
            GridStyle.EvenColor = clWindow
            TitleHeight.PixelCount = 24
            FooterColor = clBtnFace
            ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
            RegistryKey = 'Software\Scalabium'
            RegistrySection = 'SMDBGrid'
            WidthOfIndicator = 11
            DefaultRowHeight = 17
            ScrollBars = ssHorizontal
            ColCount = 2
            RowCount = 2
          end
        end
        object HVSTabSheet: TTabSheet
          Caption = 'Hermes'
          ImageIndex = 5
          OnShow = HVSTabSheetShow
          object HVSDBGrid: TDBGridPro
            Left = 0
            Top = 0
            Width = 806
            Height = 254
            Align = alClient
            DataSource = ShipperDataSource
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
            ReadOnly = True
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Flat = False
            BandsFont.Charset = DEFAULT_CHARSET
            BandsFont.Color = clWindowText
            BandsFont.Height = -11
            BandsFont.Name = 'Tahoma'
            BandsFont.Style = []
            Groupings = <>
            GridStyle.Style = gsNormal
            GridStyle.OddColor = clWindow
            GridStyle.EvenColor = clWindow
            TitleHeight.PixelCount = 24
            FooterColor = clBtnFace
            ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
            RegistryKey = 'Software\Scalabium'
            RegistrySection = 'SMDBGrid'
            WidthOfIndicator = 11
            DefaultRowHeight = 17
            ScrollBars = ssHorizontal
            ColCount = 2
            RowCount = 2
          end
        end
        object PATTabSheet: TTabSheet
          Caption = 'Post AT'
          ImageIndex = 6
          OnShow = PATTabSheetShow
          object PATDBGrid: TDBGridPro
            Left = 0
            Top = 0
            Width = 806
            Height = 254
            Align = alClient
            DataSource = ShipperDataSource
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
            ReadOnly = True
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Flat = False
            BandsFont.Charset = DEFAULT_CHARSET
            BandsFont.Color = clWindowText
            BandsFont.Height = -11
            BandsFont.Name = 'Tahoma'
            BandsFont.Style = []
            Groupings = <>
            GridStyle.Style = gsNormal
            GridStyle.OddColor = clWindow
            GridStyle.EvenColor = clWindow
            TitleHeight.PixelCount = 24
            FooterColor = clBtnFace
            ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
            RegistryKey = 'Software\Scalabium'
            RegistrySection = 'SMDBGrid'
            WidthOfIndicator = 11
            DefaultRowHeight = 17
            ScrollBars = ssHorizontal
            ColCount = 2
            RowCount = 2
          end
        end
        object PFRTabSheet: TTabSheet
          Caption = 'Post FR'
          ImageIndex = 7
          OnShow = PFRTabSheetShow
          object PFRDBGrid: TDBGridPro
            Left = 0
            Top = 0
            Width = 806
            Height = 254
            Align = alClient
            DataSource = ShipperDataSource
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
            ReadOnly = True
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Flat = False
            BandsFont.Charset = DEFAULT_CHARSET
            BandsFont.Color = clWindowText
            BandsFont.Height = -11
            BandsFont.Name = 'Tahoma'
            BandsFont.Style = []
            Groupings = <>
            GridStyle.Style = gsNormal
            GridStyle.OddColor = clWindow
            GridStyle.EvenColor = clWindow
            TitleHeight.PixelCount = 24
            FooterColor = clBtnFace
            ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
            RegistryKey = 'Software\Scalabium'
            RegistrySection = 'SMDBGrid'
            WidthOfIndicator = 11
            DefaultRowHeight = 17
            ScrollBars = ssHorizontal
            ColCount = 2
            RowCount = 2
          end
        end
        object PCHTabSheet: TTabSheet
          Caption = 'Post CH'
          ImageIndex = 8
          OnShow = PCHTabSheetShow
          object PCHDBGrid: TDBGridPro
            Left = 0
            Top = 0
            Width = 806
            Height = 254
            Align = alClient
            DataSource = ShipperDataSource
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
            ReadOnly = True
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Flat = False
            BandsFont.Charset = DEFAULT_CHARSET
            BandsFont.Color = clWindowText
            BandsFont.Height = -11
            BandsFont.Name = 'Tahoma'
            BandsFont.Style = []
            Groupings = <>
            GridStyle.Style = gsNormal
            GridStyle.OddColor = clWindow
            GridStyle.EvenColor = clWindow
            TitleHeight.PixelCount = 24
            FooterColor = clBtnFace
            ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
            RegistryKey = 'Software\Scalabium'
            RegistrySection = 'SMDBGrid'
            WidthOfIndicator = 11
            DefaultRowHeight = 17
            ScrollBars = ssHorizontal
            ColCount = 2
            RowCount = 2
          end
        end
        object SCHTabSheet: TTabSheet
          Caption = 'DB Schenker'
          ImageIndex = 9
          OnShow = SCHTabSheetShow
          object SCHDBGrid: TDBGridPro
            Left = 0
            Top = 0
            Width = 806
            Height = 254
            Align = alClient
            DataSource = ShipperDataSource
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
            ReadOnly = True
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Flat = False
            BandsFont.Charset = DEFAULT_CHARSET
            BandsFont.Color = clWindowText
            BandsFont.Height = -11
            BandsFont.Name = 'Tahoma'
            BandsFont.Style = []
            Groupings = <>
            GridStyle.Style = gsNormal
            GridStyle.OddColor = clWindow
            GridStyle.EvenColor = clWindow
            TitleHeight.PixelCount = 24
            FooterColor = clBtnFace
            ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
            RegistryKey = 'Software\Scalabium'
            RegistrySection = 'SMDBGrid'
            WidthOfIndicator = 11
            DefaultRowHeight = 17
            ScrollBars = ssHorizontal
            ColCount = 2
            RowCount = 2
          end
        end
        object AWSTabSheet: TTabSheet
          Caption = 'Amazon'
          ImageIndex = 10
          OnShow = AWSTabSheetShow
          object AWSDBGrid: TDBGridPro
            Left = 0
            Top = 0
            Width = 806
            Height = 254
            Align = alClient
            DataSource = ShipperDataSource
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
            ReadOnly = True
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Flat = False
            BandsFont.Charset = DEFAULT_CHARSET
            BandsFont.Color = clWindowText
            BandsFont.Height = -11
            BandsFont.Name = 'Tahoma'
            BandsFont.Style = []
            Groupings = <>
            GridStyle.Style = gsNormal
            GridStyle.OddColor = clWindow
            GridStyle.EvenColor = clWindow
            TitleHeight.PixelCount = 24
            FooterColor = clBtnFace
            ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
            RegistryKey = 'Software\Scalabium'
            RegistrySection = 'SMDBGrid'
            WidthOfIndicator = 11
            DefaultRowHeight = 17
            ScrollBars = ssHorizontal
            ColCount = 2
            RowCount = 2
          end
        end
      end
    end
    object StationTabSheet: TTabSheet
      Caption = 'Printer und Stationen'
      ImageIndex = 2
      OnShow = StationTabSheetShow
      object Splitter1: TSplitter
        Left = 0
        Top = 186
        Width = 828
        Height = 3
        Cursor = crVSplit
        Align = alTop
        ExplicitLeft = -3
        ExplicitTop = 177
      end
      object StationKopfPanel: TPanel
        Left = 0
        Top = 0
        Width = 828
        Height = 65
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 0
        object Label2: TLabel
          Left = 3
          Top = 8
          Width = 45
          Height = 13
          Caption = 'Locations'
        end
        object StationLocComboBox: TComboBoxPro
          Left = 3
          Top = 27
          Width = 814
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 0
          OnChange = StationLocComboBoxChange
        end
      end
      object StationPanel: TPanel
        Left = 0
        Top = 189
        Width = 828
        Height = 41
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 1
      end
      object StationDBGrid: TDBGridPro
        Left = 0
        Top = 230
        Width = 828
        Height = 120
        Align = alClient
        DataSource = StationDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 2
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsNormal
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object PrinterDBGrid: TDBGridPro
        Left = 0
        Top = 65
        Width = 828
        Height = 121
        Align = alTop
        DataSource = PrinterDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 3
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsNormal
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
    end
  end
  object Button1: TButton
    Left = 769
    Top = 443
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 1
  end
  object ServerComboBox: TComboBox
    Left = 8
    Top = 21
    Width = 297
    Height = 21
    TabOrder = 2
    Text = 'ServerComboBox'
    OnChange = ServerComboBoxChange
  end
  object SendITADOConnection: TADOConnection
    Left = 752
    Top = 32
  end
  object ClientADOQuery: TADOQuery
    Connection = SendITADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 688
    Top = 32
  end
  object ClientDataSource: TDataSource
    DataSet = ClientADOQuery
    Left = 624
    Top = 32
  end
  object ShipperDataSource: TDataSource
    DataSet = ShipperADOQuery
    Left = 512
    Top = 32
  end
  object ShipperADOQuery: TADOQuery
    Connection = SendITADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 576
    Top = 32
  end
  object StationDataSource: TDataSource
    DataSet = StationQuery
    Left = 328
    Top = 8
  end
  object StationQuery: TADOQuery
    Connection = SendITADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 392
    Top = 8
  end
  object PrinterDataSource: TDataSource
    DataSet = PrinterQuery
    Left = 328
    Top = 40
  end
  object PrinterQuery: TADOQuery
    Connection = SendITADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 392
    Top = 40
  end
end
