object ConfigSetupForm: TConfigSetupForm
  Left = 200
  Top = 253
  BorderIcons = [biSystemMenu]
  Caption = #196'nderen der Konfigurationsparameter'
  ClientHeight = 230
  ClientWidth = 752
  Color = clBtnFace
  Constraints.MaxWidth = 768
  Constraints.MinWidth = 760
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 752
    Height = 49
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label1: TLabel
      Left = 16
      Top = 8
      Width = 32
      Height = 13
      Caption = 'Label1'
    end
    object Label2: TLabel
      Left = 16
      Top = 32
      Width = 32
      Height = 13
      Caption = 'Label2'
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 184
    Width = 752
    Height = 46
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      752
      46)
    object OkButton: TButton
      Left = 504
      Top = 13
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Ok'
      Default = True
      ModalResult = 1
      TabOrder = 0
      OnClick = OkButtonClick
    end
    object AbortButton: TButton
      Left = 588
      Top = 13
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
    object NewButton: TButton
      Left = 89
      Top = 13
      Width = 75
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'Neu...'
      TabOrder = 2
      OnClick = NewButtonClick
    end
    object CopyButton: TButton
      Left = 8
      Top = 13
      Width = 75
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'Kopieren'
      Enabled = False
      TabOrder = 3
      OnClick = CopyButtonClick
    end
    object UebButton: TButton
      Left = 672
      Top = 13
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      TabOrder = 4
      OnClick = UebButtonClick
    end
  end
  object PageControl1: TPageControl
    Left = 0
    Top = 49
    Width = 752
    Height = 135
    Align = alClient
    TabOrder = 2
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 392
    Top = 16
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Prepared = True
    Left = 424
    Top = 16
  end
  object ADOQuery3: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Prepared = True
    Left = 456
    Top = 16
  end
end
