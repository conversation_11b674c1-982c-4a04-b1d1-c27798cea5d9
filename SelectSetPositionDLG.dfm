object SelectSetPositionForm: TSelectSetPositionForm
  Left = 0
  Top = 0
  Caption = 'Artikelset Position ausw'#228'hlen'
  ClientHeight = 346
  ClientWidth = 635
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object KopfPanel: TPanel
    Left = 0
    Top = 0
    Width = 635
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
  end
  object FussPanel: TPanel
    Left = 0
    Top = 305
    Width = 635
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      635
      41)
    object AbortButton: TButton
      Left = 550
      Top = 6
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 0
    end
    object OkButton: TButton
      Left = 469
      Top = 6
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Ok'
      ModalResult = 1
      TabOrder = 1
    end
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 41
    Width = 635
    Height = 264
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      635
      264)
    object Label1: TLabel
      Left = 8
      Top = 11
      Width = 44
      Height = 13
      Caption = 'Artikelnr.'
    end
    object Label2: TLabel
      Left = 248
      Top = 11
      Width = 50
      Height = 13
      Caption = 'Artikeltext'
    end
    object Label3: TLabel
      Left = 8
      Top = 190
      Width = 32
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Menge'
    end
    object Label4: TLabel
      Left = 8
      Top = 219
      Width = 56
      Height = 13
      Alignment = taRightJustify
      Anchors = [akLeft, akBottom]
      Caption = 'Bruchfaktor'
    end
    object Label5: TLabel
      Left = 145
      Top = 219
      Width = 11
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = '%'
    end
    object ArNrEdit: TEdit
      Left = 80
      Top = 8
      Width = 137
      Height = 21
      TabOrder = 0
      Text = 'ArNrEdit'
      OnExit = EditExit
      OnKeyPress = EditKeyPress
    end
    object ArtikelListView: TListView
      Left = 8
      Top = 48
      Width = 617
      Height = 129
      Anchors = [akLeft, akTop, akRight, akBottom]
      Columns = <
        item
          Caption = 'Mandant'
        end
        item
          Caption = 'Artikel-Nr.'
          Width = 80
        end
        item
          Caption = 'Artikeltext'
          Width = 90
        end
        item
          Caption = 'Einheit'
        end
        item
          Alignment = taRightJustify
          Caption = 'Nettogewicht'
          Width = 80
        end
        item
          Caption = 'Wert in '#8364
        end>
      MultiSelect = True
      ReadOnly = True
      RowSelect = True
      TabOrder = 2
      ViewStyle = vsReport
      OnColumnClick = ArtikelListViewColumnClick
      OnCompare = ArtikelListViewCompare
    end
    object ArTextEdit: TEdit
      Left = 320
      Top = 8
      Width = 305
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 1
      Text = 'ArTextEdit'
      OnExit = EditExit
      OnKeyPress = EditKeyPress
    end
    object MengeEdit: TEdit
      Left = 80
      Top = 190
      Width = 60
      Height = 21
      Anchors = [akLeft, akBottom]
      TabOrder = 3
      Text = 'MengeEdit'
    end
    object HinweisMemo: TMemo
      Left = 168
      Top = 190
      Width = 457
      Height = 72
      Anchors = [akLeft, akRight, akBottom]
      Lines.Strings = (
        'HinweisMemo')
      TabOrder = 6
    end
    object BruchProzentEdit: TEdit
      Left = 80
      Top = 217
      Width = 60
      Height = 21
      Anchors = [akLeft, akBottom]
      TabOrder = 4
      Text = 'MengeEdit'
    end
    object ProdPartCheckBox: TCheckBox
      Left = 8
      Top = 248
      Width = 154
      Height = 17
      Anchors = [akLeft, akBottom]
      Caption = 'Fertigungsbestandteil'
      TabOrder = 5
    end
  end
end
