unit IntegerUpDown;

interface

uses
  {$IFDEF LINUX}
  WinUtils,
  {$ENDIF}
  Messages, Windows, SysUtils, CommCtrl, Classes, Controls, Forms, Graphics, StdCtrls, ComCtrls;


const
  {$EXTERNALSYM UDM_SETPOS32}
  UDM_SETPOS32 = WM_USER+113;
  {$EXTERNALSYM UDM_GETPOS32}
  UDM_GETPOS32 = WM_USER+114;

type
  {$ifdef VER180}
    TIntUDChangingEventEx = procedure (Sender: TObject; var AllowChange: Boolean; NewValue: Integer; Direction: TUpDownDirection) of object;

    TCustomIntegerUpDown = class(TWinControl)
    private
      FArrowKeys: Boolean;
      FAssociate: TWinControl;
      FMin: Integer;
      FMax: Integer;
      FIncrement: Integer;
      FNewValue: Integer;
      FNewValueDelta: Integer;
      FPosition: Integer;
      FThousands: Boolean;
      FWrap: <PERSON>olean;
      FOnClick: TUDClickEvent;
      FAlignButton: TUDAlignButton;
      FOrientation: TUDOrientation;
      FOnChanging: TUDChangingEvent;
      FOnChangingEx: TIntUDChangingEventEx;
      procedure UndoAutoResizing(Value: TWinControl);
      procedure SetAssociate(Value: TWinControl);
      function GetPosition: Integer;
      procedure SetMin(Value: Integer);
      procedure SetMax(Value: Integer);
      procedure SetIncrement(Value: Integer);
      procedure SetPosition(Value: Integer);
      procedure SetAlignButton(Value: TUDAlignButton);
      procedure SetOrientation(Value: TUDOrientation);
      procedure SetArrowKeys(Value: Boolean);
      procedure SetThousands(Value: Boolean);
      procedure SetWrap(Value: Boolean);
      procedure CMAllChildrenFlipped(var Message: TMessage); message CM_ALLCHILDRENFLIPPED;
      procedure CNNotify(var Message: TWMNotify); message CN_NOTIFY;
      procedure WMHScroll(var Message: TWMHScroll); message CN_HSCROLL;
      procedure WMSize(var Message: TWMSize); message WM_SIZE;
      procedure WMVScroll(var Message: TWMVScroll); message CN_VSCROLL;
    protected
      function DoCanChange(NewVal: Integer; Delta: Integer): Boolean;
      function CanChange: Boolean; dynamic;
      procedure CreateParams(var Params: TCreateParams); override;
      procedure CreateWnd; override;
      procedure Notification(AComponent: TComponent; Operation: TOperation); override;
      procedure Click(Button: TUDBtnType); reintroduce; dynamic;
      property AlignButton: TUDAlignButton read FAlignButton write SetAlignButton default udRight;
      property ArrowKeys: Boolean read FArrowKeys write SetArrowKeys default True;
      property Associate: TWinControl read FAssociate write SetAssociate;
      property Min: Integer read FMin write SetMin default 0;
      property Max: Integer read FMax write SetMax default 100;
      property Increment: Integer read FIncrement write SetIncrement default 1;
      property Orientation: TUDOrientation read FOrientation write SetOrientation default udVertical;
      property Position: Integer read GetPosition write SetPosition default 0;
      property Thousands: Boolean read FThousands write SetThousands default True;
      property Wrap: Boolean read FWrap write SetWrap default False;
      property OnChanging: TUDChangingEvent read FOnChanging write FOnChanging;
      property OnChangingEx: TIntUDChangingEventEx read FOnChangingEx write FOnChangingEx;
      property OnClick: TUDClickEvent read FOnClick write FOnClick;
    public
      constructor Create(AOwner: TComponent); override;
    end;
  {$endif}

  {$ifdef VER180}
    TIntegerUpDown = class(TCustomIntegerUpDown)
  {$else}
    TIntegerUpDown = class(TCustomUpDown)
  {$endif}
  published
    property AlignButton;
    property Anchors;
    property Associate;
    property ArrowKeys;
    property Enabled;
    property Hint;
    property Min;
    property Max;
    property Increment;
    property Constraints;
    property Orientation;
    property ParentShowHint;
    property PopupMenu;
    property Position;
    property ShowHint;
    property TabOrder;
    property TabStop;
    property Thousands;
    property Visible;
    property Wrap;
    property OnChanging;
    property OnChangingEx;
    property OnContextPopup;
    property OnClick;
    property OnEnter;
    property OnExit;
    property OnMouseDown;
    property OnMouseMove;
    property OnMouseUp;
  end;

  Procedure Register;

implementation

uses ComStrs;

Procedure Register;
Begin
  RegisterComponents('c+s', [TIntegerUpDown]);
End;


{$ifdef VER180}

{ TUpDown }

constructor TCustomIntegerUpDown.Create(AOwner: TComponent);
begin
  inherited Create(AOwner);
  Width := GetSystemMetrics(SM_CXVSCROLL);
  Height := GetSystemMetrics(SM_CYVSCROLL);
  Height := Height + (Height div 2);
  FArrowKeys := True;
  FWrap := false;
  FPosition := 0;
  FMin := 0;
  FMax := 100;
  FIncrement := 1;
  FAlignButton := udRight;
  FOrientation := udVertical;
  FThousands := True;
  ControlStyle := ControlStyle - [csDoubleClicks];
end;

procedure TCustomIntegerUpDown.CreateParams(var Params: TCreateParams);
begin
  InitCommonControl(ICC_UPDOWN_CLASS);
  inherited CreateParams(Params);
  with Params do
  begin
    Style := Style or UDS_SETBUDDYINT;
    if FAlignButton = udRight then Style := Style or UDS_ALIGNRIGHT
    else Style := Style or UDS_ALIGNLEFT;
    if FOrientation = udHorizontal then Style := Style or UDS_HORZ;
    if FArrowKeys then Style := Style or UDS_ARROWKEYS;
    if not FThousands then Style := Style or UDS_NOTHOUSANDS;
    if FWrap then Style := Style or UDS_WRAP;
  end;
  CreateSubClass(Params, UPDOWN_CLASS);
  with Params.WindowClass do
    style := style and not (CS_HREDRAW or CS_VREDRAW) or CS_DBLCLKS;
end;

procedure TCustomIntegerUpDown.CreateWnd;
var
  OrigWidth: Integer;
  AccelArray: array [0..0] of TUDAccel;
begin
  OrigWidth := Width;  { control resizes width - disallowing user to set width }
  inherited CreateWnd;
  if FAssociate <> nil then
  begin
    UndoAutoResizing(FAssociate);
    SendMessage(Handle, UDM_SETBUDDY, FAssociate.Handle, 0);
  end;
  Width := OrigWidth;
  SendMessage(Handle, UDM_SETRANGE32, FMin, FMax);
  SendMessage(Handle, UDM_SETPOS32, 0, FPosition);
  SendMessage(Handle, UDM_GETACCEL, 1, Longint(@AccelArray));
  AccelArray[0].nInc := FIncrement;
  SendMessage(Handle, UDM_SETACCEL, 1, Longint(@AccelArray));
end;

procedure TCustomIntegerUpDown.WMVScroll(var Message: TWMVScroll);
begin
  inherited;
  if Message.ScrollCode = SB_THUMBPOSITION then
  begin
    if Message.Pos > FPosition then
      Click(btNext)
    else if Message.Pos < FPosition then Click(btPrev);

    FPosition := Message.Pos;
  end;
end;

procedure TCustomIntegerUpDown.WMSize(var Message: TWMSize);
var
  R: TRect;
begin
  inherited;
  R := ClientRect;
  InvalidateRect(Handle, @R, False);
end;

procedure TCustomIntegerUpDown.WMHScroll(var Message: TWMHScroll);
begin
  inherited;
  if Message.ScrollCode = SB_THUMBPOSITION then
  begin
    if Message.Pos > FPosition then Click(btNext)
    else if Message.Pos < FPosition then Click(btPrev);
    FPosition := Message.Pos;
  end;
end;

function TCustomIntegerUpDown.DoCanChange(NewVal: Integer; Delta: Integer): Boolean;
begin
  FNewValue := NewVal;
  FNewValueDelta := Delta;

  Result := CanChange;
end;

function TCustomIntegerUpDown.CanChange: Boolean;
var
  Direction: TUpDownDirection;
  
begin
  Result := True;
  Direction := updNone;

  if (FNewValue < Min) and (FNewValueDelta < 0) or
  (FNewValue > Max) and (FNewValueDelta > 0) then
    Direction := updNone
  else if FNewValueDelta < 0 then
    Direction := updDown
  else if FNewValueDelta > 0 then
    Direction := updUp;

  if Assigned(FOnChanging) then
    FOnChanging(Self, Result);
  if Assigned(FOnChangingEx) then
    FOnChangingEx(Self, Result, FNewValue, Direction);
end;

procedure TCustomIntegerUpDown.CMAllChildrenFlipped(var Message: TMessage);
begin
  if FAlignButton = udRight then
    SetAlignButton(udLeft)
  else
    SetAlignButton(udRight);
end;

procedure TCustomIntegerUpDown.CNNotify(var Message: TWMNotify);
begin
  with Message do
    if NMHdr^.code = UDN_DELTAPOS then
    begin
      LongBool(Result) := not DoCanChange(PNMUpDown(NMHdr).iPos + PNMUpDown(NMHdr).iDelta,
                                          PNMUpDown(NMHdr).iDelta);
    end;
end;

procedure TCustomIntegerUpDown.Click(Button: TUDBtnType);
begin
  if Assigned(FOnClick) then FOnClick(Self, Button);
end;

procedure TCustomIntegerUpDown.SetAssociate(Value: TWinControl);
var
  I: Integer;

  function IsClass(ClassType: TClass; const Name: string): Boolean;
  begin
    Result := True;
    while ClassType <> nil do
    begin
      if ClassType.ClassNameIs(Name) then Exit;
      ClassType := ClassType.ClassParent;
    end;
    Result := False;
  end;

begin
  if Value <> nil then
    for I := 0 to Parent.ControlCount - 1 do // is control already associated
      if (Parent.Controls[I] is TCustomIntegerUpDown) and (Parent.Controls[I] <> Self) then
        if TCustomIntegerUpDown(Parent.Controls[I]).Associate = Value then
          raise Exception.CreateResFmt(@sUDAssociated, [Value.Name, Parent.Controls[I].Name]);

  if FAssociate <> nil then { undo the current associate control }
  begin
    if HandleAllocated then
      SendMessage(Handle, UDM_SETBUDDY, 0, 0);
    FAssociate := nil;
  end;

  if (Value <> nil) and (Value.Parent = Self.Parent) and
    not (Value is TCustomIntegerUpDown) and
    not (Value is TCustomTreeView) and not (Value is TCustomListView) and
    not IsClass(Value.ClassType, 'TDBEdit') and
    not IsClass(Value.ClassType, 'TDBMemo') then
  begin
    if HandleAllocated then
    begin
      UndoAutoResizing(Value);
      SendMessage(Handle, UDM_SETBUDDY, Value.Handle, 0);
    end;
    FAssociate := Value;
    if Value is TCustomEdit then
      TCustomEdit(Value).Text := IntToStr(FPosition);
  end;
end;

procedure TCustomIntegerUpDown.UndoAutoResizing(Value: TWinControl);
var
  OrigWidth, NewWidth, DeltaWidth: Integer;
  OrigLeft, NewLeft, DeltaLeft: Integer;
begin
  { undo Window's auto-resizing }
  OrigWidth := Value.Width;
  OrigLeft := Value.Left;
  SendMessage(Handle, UDM_SETBUDDY, Value.Handle, 0);
  NewWidth := Value.Width;
  NewLeft := Value.Left;
  DeltaWidth := OrigWidth - NewWidth;
  DeltaLeft := NewLeft - OrigLeft;
  Value.Width := OrigWidth + DeltaWidth;
  Value.Left := OrigLeft - DeltaLeft;
end;

procedure TCustomIntegerUpDown.Notification(AComponent: TComponent;
  Operation: TOperation);
begin
  inherited Notification(AComponent, Operation);
  if (Operation = opRemove) and (AComponent = FAssociate) then
    if HandleAllocated then
    begin
      SendMessage(Handle, UDM_SETBUDDY, 0, 0);
      FAssociate := nil;
    end;
end;

function TCustomIntegerUpDown.GetPosition: Integer;
begin
  if HandleAllocated then
  begin
    Result := SendMessage(Handle, UDM_GETPOS32, 0, 0);
    FPosition := Result;
  end
  else Result := FPosition;
end;

procedure TCustomIntegerUpDown.SetMin(Value: Integer);
begin
  if Value <> FMin then
  begin
    FMin := Value;
    if HandleAllocated then
      SendMessage(Handle, UDM_SETRANGE32, FMin, FMax);
  end;
end;

procedure TCustomIntegerUpDown.SetMax(Value: Integer);
begin
  if Value <> FMax then
  begin
    FMax := Value;
    if HandleAllocated then
      SendMessage(Handle, UDM_SETRANGE32, FMin, FMax);
  end;
end;

procedure TCustomIntegerUpDown.SetIncrement(Value: Integer);
var
  AccelArray: array [0..0] of TUDAccel;
begin
  if Value <> FIncrement then
  begin
    FIncrement := Value;
    if HandleAllocated then
    begin
      SendMessage(Handle, UDM_GETACCEL, 1, Longint(@AccelArray));
      AccelArray[0].nInc := Value;
      SendMessage(Handle, UDM_SETACCEL, 1, Longint(@AccelArray));
    end;
  end;
end;

procedure TCustomIntegerUpDown.SetPosition(Value: Integer);
begin
  if Value <> FPosition then
  begin
    if not (csDesigning in ComponentState) then
      if not DoCanChange(Value, Value-FPosition) then Exit;
    FPosition := Value;
    if (csDesigning in ComponentState) and (FAssociate <> nil) then
      if FAssociate is TCustomEdit then
        TCustomEdit(FAssociate).Text := IntToStr(FPosition);
    if HandleAllocated then
      SendMessage(Handle, UDM_SETPOS32, 0, FPosition);
  end;
end;

procedure TCustomIntegerUpDown.SetOrientation(Value: TUDOrientation);
begin
  if Value <> FOrientation then
  begin
    FOrientation := Value;
    if ComponentState * [csLoading, csUpdating] = [] then
      SetBounds(Left, Top, Height, Width);
    if HandleAllocated then
      SendMessage(Handle, UDM_SETBUDDY, 0, 0);
    RecreateWnd;
  end;
end;

procedure TCustomIntegerUpDown.SetAlignButton(Value: TUDAlignButton);
begin
  if Value <> FAlignButton then
  begin
    FAlignButton := Value;
    if HandleAllocated then
      SendMessage(Handle, UDM_SETBUDDY, 0, 0);
    RecreateWnd;
  end;
end;

procedure TCustomIntegerUpDown.SetArrowKeys(Value: Boolean);
begin
  if Value <> FArrowKeys then
  begin
    FArrowKeys := Value;
    if HandleAllocated then
      SendMessage(Handle, UDM_SETBUDDY, 0, 0);
    RecreateWnd;
  end;
end;

procedure TCustomIntegerUpDown.SetThousands(Value: Boolean);
begin
  if Value <> FThousands then
  begin
    FThousands := Value;
    if HandleAllocated then
      SendMessage(Handle, UDM_SETBUDDY, 0, 0);
    RecreateWnd;
  end;
end;

procedure TCustomIntegerUpDown.SetWrap(Value: Boolean);
begin
  if Value <> FWrap then
  begin
    FWrap := Value;
    if HandleAllocated then
      SendMessage(Handle, UDM_SETBUDDY, 0, 0);
    RecreateWnd;
  end;
end;
{$endif}

end.
