unit AufPosKommInfoDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, DB, ADODB;

type
  TAufPosKommInfoForm = class(TForm)
    GroupBox1: TGroupBox;
    InfoArtComboBox: TComboBoxPro;
    InfoMemo: TMemo;
    CloseButton: TButton;
    ADOQuery1: TADOQuery;
    procedure InfoArtComboBoxChange(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    function Prepare (const RefAufPos : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses VCLUtilitys, DatenModul;

procedure TAufPosKommInfoForm.InfoArtComboBoxChange(Sender: TObject);
begin
  InfoMemo.Clear;

  if (InfoArtComboBox.ItemIndex <> -1) then begin
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select INFO_TEXT from V_AUFTRAG_KOMM_POS_INFO where REF='+IntToStr (GetComboBoxRef (InfoArtComboBox)));

    try
      ADOQuery1.Open;

      while not (ADOQuery1.Eof) do begin
        if (Length (InfoMemo.Text) > 0) then InfoMemo.Lines.Add ('------------------------');

        InfoMemo.Lines.Add (ADOQuery1.Fields [0].AsString);

        ADOQuery1.Next;
      end;

      ADOQuery1.Close;
    except
    end;
  end;

  InfoMemo.ReadOnly := True;
end;

function TAufPosKommInfoForm.Prepare (const RefAufPos : Integer) : Integer;
var
  count : Integer;
begin
  ClearComboBoxObjects (InfoArtComboBox);

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF,INFO_ART from V_AUFTRAG_KOMM_POS_INFO where REF_AUF_KOMM_POS='+IntToStr (RefAufPos));

  try
    ADOQuery1.Open;

    count := 1;

    while not (ADOQuery1.Eof) do begin
      if (count = 1) and (ADOQuery1.RecordCount = 1) then
        InfoArtComboBox.Items.AddObject(ADOQuery1.Fields [1].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger))
      else
        InfoArtComboBox.Items.AddObject(ADOQuery1.Fields [1].AsString + ' ('+IntToStr (count)+')',TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

      Inc (count);

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;
  except
  end;

  if (InfoArtComboBox.Items.Count > 0) then begin
    InfoArtComboBox.ItemIndex := 0;
    InfoArtComboBox.Enabled := True;
    InfoMemo.Enabled := True;

    InfoArtComboBox.OnChange (InfoArtComboBox);
  end else begin
    InfoArtComboBox.Text := '';
    InfoArtComboBox.Enabled := False;

    InfoMemo.Clear;
    InfoMemo.Enabled := False;
  end;

  Result := 0;
end;

end.
