object NachschubEinlagernForm: TNachschubEinlagernForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Nachschub einlagern'
  ClientHeight = 587
  ClientWidth = 749
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 749
    Height = 65
    Align = alTop
    TabOrder = 0
    DesignSize = (
      749
      65)
    object Bevel1: TBevel
      Left = 6
      Top = 36
      Width = 735
      Height = 17
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 725
    end
    object Label1: TLabel
      Left = 8
      Top = 17
      Width = 67
      Height = 13
      Caption = 'Nachschub-LE'
    end
    object NachLELabel: TLabel
      Left = 88
      Top = 17
      Width = 69
      Height = 13
      Caption = 'NachLELabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object FehlerPanel: TPanel
    Left = 0
    Top = 281
    Width = 749
    Height = 24
    Align = alBottom
    BevelOuter = bvNone
    Caption = 'FehlerPanel'
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 1
  end
  object WELEPanel: TPanel
    Left = 0
    Top = 305
    Width = 749
    Height = 37
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    object Label11: TLabel
      Left = 8
      Top = 13
      Width = 148
      Height = 13
      Caption = 'Sammel-LE f'#252'r die Einlagerung:'
    end
    object WELELabel: TLabel
      Left = 216
      Top = 6
      Width = 137
      Height = 29
      Caption = 'WELELabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -24
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object ChangeWELEButton: TButton
      Left = 576
      Top = 6
      Width = 165
      Height = 25
      Caption = 'Neue Einlagerung-LE anlegen'
      TabOrder = 0
      OnClick = ChangeWELEButtonClick
    end
    object LTComboBox: TComboBoxPro
      Left = 400
      Top = 9
      Width = 170
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 1
      OnChange = LTComboBoxChange
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 342
    Width = 749
    Height = 245
    Align = alBottom
    TabOrder = 3
    DesignSize = (
      749
      245)
    object Label8: TLabel
      Left = 10
      Top = 13
      Width = 32
      Height = 13
      Caption = 'Menge'
    end
    object Bevel2: TBevel
      Left = 6
      Top = 148
      Width = 735
      Height = 17
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 725
    end
    object MengeUpDown: TIntegerUpDown
      Left = 89
      Top = 10
      Width = 16
      Height = 21
      Associate = MengeEdit
      Max = 10000
      TabOrder = 0
    end
    object BesUeberButton: TButton
      Left = 6
      Top = 45
      Width = 204
      Height = 73
      Caption = #220'bernehmen ->'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -21
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 1
      OnClick = BesUeberButtonClick
    end
    object MengeEdit: TEdit
      Left = 48
      Top = 10
      Width = 41
      Height = 21
      TabOrder = 2
      Text = '0'
      OnChange = MengeEditChange
    end
    object EinlagerButton: TButton
      Left = 232
      Top = 161
      Width = 257
      Height = 54
      Caption = 'Einlagern'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -32
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 3
      OnClick = EinlagerButtonClick
    end
    object EinlagerBesDBGrid: TDBGridPro
      Left = 216
      Top = 6
      Width = 525
      Height = 136
      DataSource = EinlagerBesDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
      ReadOnly = True
      TabOrder = 4
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'Tahoma'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
      RegistryKey = 'Software\CS'
      RegistrySection = 'DBGrids'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
    object AbortButton: TButton
      Left = 666
      Top = 190
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 5
    end
    object StatusBar1: TStatusBar
      Left = 1
      Top = 225
      Width = 747
      Height = 19
      Panels = <
        item
          Width = 200
        end
        item
          Width = 50
        end>
    end
  end
  object NachschubLEBesDBGrid: TDBGridPro
    Left = 0
    Top = 65
    Width = 749
    Height = 216
    Align = alClient
    DataSource = NachschubLEBesDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 4
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\CS'
    RegistrySection = 'DBGrids'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object NachschubLEBesQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 528
    Top = 152
  end
  object EinlagerBesQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 624
    Top = 368
  end
  object NachschubLEBesDataSource: TDataSource
    DataSet = NachschubLEBesQuery
    OnDataChange = NachschubLEBesDataSourceDataChange
    Left = 440
    Top = 144
  end
  object EinlagerBesDataSource: TDataSource
    DataSet = EinlagerBesQuery
    OnDataChange = EinlagerBesDataSourceDataChange
    Left = 664
    Top = 368
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 592
    Top = 520
  end
end
