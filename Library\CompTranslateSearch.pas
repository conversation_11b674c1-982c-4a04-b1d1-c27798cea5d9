unit CompTranslateSearch;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls;

type
  TCompTranslateSearchForm = class(TForm)
    SearchEdit: TEdit;
    SearchButton: TButton;
    AbortButton: TButton;
    Label1: TLabel;
    procedure FormCreate(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

procedure TCompTranslateSearchForm.FormCreate(Sender: TObject);
begin
  SearchEdit.Text := '';
end;

end.
