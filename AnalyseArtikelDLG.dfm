object AnalyseArtikelForm: TAnalyseArtikelForm
  Left = 0
  Top = 0
  Caption = 'AnalyseArtikelForm'
  ClientHeight = 443
  ClientWidth = 852
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    852
    443)
  PixelsPerInch = 96
  TextHeight = 13
  object GridLabel: TLabel
    Left = 8
    Top = 16
    Width = 44
    Height = 13
    Caption = 'GridLabel'
  end
  object ArtikelEinheitDBGrid: TDBGridPro
    Left = 8
    Top = 32
    Width = 836
    Height = 367
    Hint = 'Sortieren mit linker Maustaste, Suchen mit rechter Maustaste|'
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = ArtikelDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = ArtikelEinheitDBGridPopupMenu
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 26
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 769
    Top = 410
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 1
  end
  object ArtikelDataSource: TDataSource
    DataSet = ARDataSet
    Left = 24
    Top = 400
  end
  object ARDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 56
    Top = 400
  end
  object ArtikelEinheitDBGridPopupMenu: TPopupMenu
    Left = 176
    Top = 160
    object Artikeleinhietdeaktivieren1: TMenuItem
      Caption = 'Artikeleinheit deaktivieren...'
      OnClick = Artikeleinhietdeaktivieren1Click
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object Artikeleinhietbearbeiten1: TMenuItem
      Caption = 'Artikeleinheit bearbeiten...'
      OnClick = Artikeleinhietbearbeiten1Click
    end
  end
end
