unit LicenceDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics,
  Controls, Forms, Dialogs, DB, DBAccess, Ora, MemDS, OraSmart, StdCtrls, Grids, DBGrids, SMDBGrid,
  DBGridPro, ExtCtrls, ComCtrls, Menus;

type
  TLicenceForm = class(TForm)
    TopPanel: TPanel;
    LicDBGrid: TDBGridPro;
    BottomPanel: TPanel;
    CloseButton: TButton;
    LicQuery: TSmartQuery;
    LicOraDataSource: TOraDataSource;
    LicDBGridPopupMenu: TPopupMenu;
    CreateDayLicMenuItem: TMenuItem;
    GroupBoxSelection: TGroupBox;
    cbOnlyActiveLicences: TCheckBox;
    Label1: TLabel;
    FromDatePicker: TDateTimePicker;
    Label2: TLabel;
    ToDatePicker: TDateTimePicker;
    FrameGroupBox: TGroupBox;
    Label3: TLabel;
    Label5: TLabel;
    Label4: TLabel;
    MaxLicLabel: TLabel;
    FirmaLabel: TLabel;
    LocLabel: TLabel;
    LicCountLabel: TLabel;
    DayLicCountLabel: TLabel;
    CreateDayLicButton: TButton;
    SessionFirmaComboBox: TComboBox;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure CreateDayLicMenuItemClick(Sender: TObject);
    procedure DatePickerChange(Sender: TObject);
    procedure LicDBGridPopupMenuPopup(Sender: TObject);
    procedure CreateDayLicButtonClick(Sender: TObject);
    procedure cbOnlyActiveLicencesClick(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure SessionFirmaComboBoxChange(Sender: TObject);
  private
    fRefDayLic  : Integer;
    fRefLocation: Integer;
    fLicPrice   : Integer;
    fDayCount   : Integer;
    fRefFirma   : Integer;
    fSLACount   : Integer;
    fTotalCount : Integer;

    procedure QueryUpdate;
    procedure QueryOnlyActiveLicenses;
    procedure OpenCreateDayLicForm;
    procedure SetLicCountLabel(Count: integer);
    procedure CalculateLicCount(query : TSmartQuery);
    procedure UpdateSelectedQuery();
    procedure FormatMoneyColumns();
    procedure UpdateLicInfo;
  public
    property RefDayLic  : Integer read fRefDayLic;
  end;

implementation

{$R *.dfm}

uses
 DateUtils, VCLUtilitys, StrUtils, StringUtils, FrontendUtils, DBGridUtilModule, DatenModul, ConfigModul, SprachModul,
 CreateDayLicDLG, ResourceText, FrontendMessages;

procedure TLicenceForm.UpdateLicInfo;
var
  querySQLText : String;
  query : TSmartQuery;
begin
  query := LVSDatenModul.CreateSmartQuery (Self, 'UpdateLicInfo');

  try
    querySQLText := 'select'
                   +'   (select LICENCE_COUNT from V_SYS_FIRMA_LICENCE where REF_FIRMA=:ref_firma and STATUS=''AKT'' and LICENCE_TYPE=''SLA'') as LICENCE_COUNT'
                   +'   ,(select LICENCE_COUNT from V_SYS_FIRMA_LICENCE where REF_FIRMA=:ref_firma and STATUS=''AKT'' and LICENCE_TYPE=''DAY'') as DAY_COUNT'
                   +'   ,(select sum (case when LICENCE_TYPE=''ADD'' then lu.USED_COUNT else 0 end)'
                   +' from'
                   +'   V_SYS_FIRMA_LICENCE_USED lu'
                   +'   inner join V_SYS_FIRMA_LICENCE li on (li.REF=lu.REF_LICENCE)'
                   +' where'
                   +'   li.REF_FIRMA=:ref_firma and'
                   +'   (lu.ACTIVE_FROM is null or (lu.ACTIVE_FROM < sysdate)) and'
                   +'   (lu.USED_UNTIL is null or (lu.USED_UNTIL > sysdate))) as USED_COUNT from dual';

    query.SQL.Add(querySQLText);

    if SessionFirmaComboBox.Visible then
      query.ParamByName ('ref_firma').Value := GetComboBoxRef (SessionFirmaComboBox)
    else
      query.ParamByName ('ref_firma').Value :=fRefFirma;

    query.Open;

    fSLACount   := DBGetIntegerNull (query.FieldByName ('LICENCE_COUNT'));
    fDayCount   := DBGetIntegerNull (query.FieldByName ('DAY_COUNT'));
    fTotalCount := query.FieldByName ('USED_COUNT').AsInteger;

    if (fDayCount = -1) and (fSLACount > 0) then
      fDayCount := trunc (fSLACount * 0.30);

    query.Close;
  finally
    query.Free;
  end;

  if FirmaLabel.Visible then
    FirmaLabel.Caption := FirmaLabel.Caption + ', ' + FormatResourceText (1873, [IntToStr (fSLACount), IntToStr (fTotalCount)]);

  if (fDayCount > 0) then begin
    DayLicCountLabel.Caption := IntToStr (fDayCount);
    MaxLicLabel.Caption := FormatResourceText (1872, ['30', IntToStr (fSLACount)]);
    CreateDayLicButton.Enabled := True;
  end else begin
    DayLicCountLabel.Caption := '---';
    MaxLicLabel.Caption := FormatResourceText (1892, []);
    CreateDayLicButton.Enabled := False;
  end;

end;

procedure TLicenceForm.CalculateLicCount(query : TSmartQuery);
var
  summe: integer;
begin
  summe := 0;
  query.First;
  while not query.Eof do
  begin
    summe := summe + query.FieldByName('USED_COUNT').AsInteger;
    query.Next;
  end;
  SetLicCountLabel(summe);
end;

procedure TLicenceForm.cbOnlyActiveLicencesClick(Sender: TObject);
begin
  UpdateSelectedQuery();
end;

procedure TLicenceForm.UpdateSelectedQuery();
begin
  if (cbOnlyActiveLicences.State = cbChecked) then begin
    QueryOnlyActiveLicenses;
  end
  else if (cbOnlyActiveLicences.State = cbUnchecked) then begin
    QueryUpdate;
  end;
end;

procedure TLicenceForm.CreateDayLicButtonClick(Sender: TObject);
begin
  OpenCreateDayLicForm;
end;

procedure TLicenceForm.CreateDayLicMenuItemClick(Sender: TObject);
begin
  OpenCreateDayLicForm;
end;

procedure TLicenceForm.FormatMoneyColumns;
begin
  DBGridUtils.SetPreisDisplayFunctions(LicDBGrid.DataSource.DataSet, 'LICENCE_PRICE');
  DBGridUtils.SetPreisDisplayFunctions(LicDBGrid.DataSource.DataSet, 'TOTAL_PRICE');
end;

procedure TLicenceForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  LicQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

procedure TLicenceForm.FormCreate(Sender: TObject);
var
  query : TSmartQuery;
begin
  fRefDayLic  := -1;
  fLicPrice   := -1;

  fDayCount   := 0;
  fTotalCount := 0;
  fSLACount   := 0;

  fRefLocation := LVSDatenModul.AktLocationRef;

  FirmaLabel.Caption := '';
  LocLabel.Caption := LVSDatenModul.AktLocation;
  LicCountLabel.Caption := '';
  DayLicCountLabel.Caption := '';

  ClearComboBoxObjects (SessionFirmaComboBox);

  query := LVSDatenModul.CreateSmartQuery (Self, 'FormCreate');

  try
    query.SQL.Add('select'
                 +' f.REF as REF_FIRMA, f.NAME as FIRMA'
                 +' ,(select max (REF) from V_SYS_FIRMA_LICENCE where STATUS=''AKT'' and LICENCE_TYPE=''DAY'' and REF_FIRMA=f.REF) as REF_DAY_LIC'
                 +' from'
                 +'  V_SYS_FIRMA f'
                 +' where'
                 +'  f.REF=(select REF_FIRMA from V_SYS_BEN where REF=:ref_ben)');
    query.ParamByName ('ref_ben').Value :=LVSDatenModul.AktUserRef;

    try
      query.Open;

      FirmaLabel.Caption := query.FieldByName ('FIRMA').AsString;

      fRefFirma   := query.FieldByName ('REF_FIRMA').AsInteger;

      if (LVSDatenModul.AktUser = LVSDatenModul.Schema) then begin
        fRefDayLic := 0;

        FirmaLabel.Visible := false;

        SessionFirmaComboBox.Left := FirmaLabel.Left;
        SessionFirmaComboBox.Top := FirmaLabel.Top;
        SessionFirmaComboBox.Visible := True;

        query.Close;

        query.SQL.Clear;
        query.SQL.Add ('select fi.* from V_SYS_FIRMA fi where fi.STATUS=''AKT'' and fi.REF in (select REF_FIRMA from V_SYS_FIRMA_LICENCE where STATUS=''AKT'' and LICENCE_TYPE=''DAY'')');

        query.Open;

        if (query.RecordCount = 0) then
          fRefDayLic := -1
        else begin
          while not (query.Eof) do begin
            SessionFirmaComboBox.AddItem (query.FieldByName ('NAME').AsString, TComboBoxRef.Create (query.FieldByName ('REF').AsInteger));

            query.Next;
          end;

          SessionFirmaComboBox.ItemIndex := FindComboboxRef (SessionFirmaComboBox, fRefFirma);

          if (SessionFirmaComboBox.ItemIndex = -1) then
            SessionFirmaComboBox.ItemIndex := 0;
        end;
      end else begin
        fRefDayLic := DBGetReferenz (query.FieldByName ('REF_DAY_LIC'));
      end;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;

  FromDatePicker.Date := Trunc (EncodeDate (YearOf(Now), MonthOf(Now), 1)) + 0.8;
  ToDatePicker.Date := Trunc (EncodeDate (YearOf(Now), MonthOf(Now), DaysInMonth (Now))) + 0.8;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, );
  {$endif}
end;

procedure TLicenceForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (SessionFirmaComboBox);
end;

procedure TLicenceForm.QueryUpdate;
var
  fromDate : TDateTime;
  toDate : TDateTime;
begin
  fromDate := FromDatePicker.Date;
  toDate := ToDatePicker.Date +  EncodeTime(23, 59, 59, 0);

  LicQuery.Close;

  LicQuery.SQL.Clear;
  LicQuery.SQL.Add ('select'
                   +'  lu.*'
                   +' from'
                   +'  V_PCD_SYS_FIRMA_LICENCE_USED lu'
                   +'  inner join V_SYS_FIRMA_LICENCE li on (li.REF=lu.REF_LICENCE)'
                   +' where'
                   +'  (lu.REF_LOCATION is null or lu.REF_LOCATION=:ref_loc)'
                   +'  and (lu.CREATE_AT between :dt_from and :dt_to)'
                   +'  and li.LICENCE_TYPE=''DAY'''
                   +'  and li.REF_FIRMA=:ref_firma'
                   +' ORDER BY'
                   +'  lu.CREATE_AT ASC');
  LicQuery.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
  LicQuery.ParamByName ('dt_from').AsDate  := Trunc(fromDate);
  LicQuery.ParamByName ('dt_to').AsDateTime  := toDate;

  if SessionFirmaComboBox.Visible then
    LicQuery.ParamByName ('ref_firma').Value := GetComboBoxRef (SessionFirmaComboBox)
  else
    LicQuery.ParamByName ('ref_firma').Value :=fRefFirma;

  try
    LicQuery.Open;

    FormatMoneyColumns;

    CalculateLicCount(LicQuery);
  except
  end;
end;

procedure TLicenceForm.QueryOnlyActiveLicenses;
var
  last24Hours : TDateTime;
begin
  last24Hours :=  IncMinute(Now, -24 * 60);
  LicQuery.Close;

  LicQuery.SQL.Clear;
  LicQuery.SQL.Add ('select'
                   +'  lu.*'
                   +' from'
                   +'  V_PCD_SYS_FIRMA_LICENCE_USED lu'
                   +'  inner join V_SYS_FIRMA_LICENCE li on (li.REF=lu.REF_LICENCE)'
                   +' where'
                   +'  (lu.REF_LOCATION is null or lu.REF_LOCATION=:ref_loc)'
                   +'  and (lu.ACTIVE_FROM BETWEEN :dt_from AND SYSDATE)'
                   +'  and li.LICENCE_TYPE=''DAY'''
                   +'  and li.REF_FIRMA=:ref_firma'
                   +' ORDER BY'
                   +'  lu.CREATE_AT ASC');
  LicQuery.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
  LicQuery.ParamByName ('dt_from').AsDateTime  := last24Hours;

  if SessionFirmaComboBox.Visible then
    LicQuery.ParamByName ('ref_firma').Value := GetComboBoxRef (SessionFirmaComboBox)
  else
    LicQuery.ParamByName ('ref_firma').Value :=fRefFirma;


  try
    LicQuery.Open;

    FormatMoneyColumns;

    CalculateLicCount(LicQuery);
  except
  end;
end;

procedure TLicenceForm.SessionFirmaComboBoxChange(Sender: TObject);
begin
  UpdateLicInfo;

  if cbOnlyActiveLicences.Checked then
    QueryOnlyActiveLicenses
  else
    QueryUpdate;
end;

procedure TLicenceForm.SetLicCountLabel(Count: integer);
begin
  LicCountLabel.Caption := '0';
  if (Count > 0)  then begin
    LicCountLabel.Caption := IntToStr(Count);
  end;
end;

procedure TLicenceForm.FormShow(Sender: TObject);
begin
  if Assigned (LVSConfigModul) then begin
    LVSConfigModul.RestoreFormInfo (Self);
  end;

  UpdateLicInfo;
end;

procedure TLicenceForm.LicDBGridPopupMenuPopup(Sender: TObject);
begin
  CreateDayLicMenuItem.Enabled := (fRefDayLic > 0) and (fDayCount > 0);
end;

procedure TLicenceForm.OpenCreateDayLicForm;
var
  query  : TSmartQuery;
  crfrom : TCreateDayLicForm;
begin
  crfrom := TCreateDayLicForm.Create (Self);

  try
    crfrom.RefDayLic   := -1;
    crfrom.LicPrice    := fLicPrice;
    crfrom.RefLocation := fRefLocation;

    query := LVSDatenModul.CreateSmartQuery (Self, '');

    try
      query.SQL.Add ('select REF, LICENCE_PRICE from V_SYS_FIRMA_LICENCE where REF_FIRMA=:ref_firma and LICENCE_TYPE=''DAY''');

      if SessionFirmaComboBox.Visible then begin
        query.Params [0].Value := GetComboBoxRef (SessionFirmaComboBox);
      end else begin
        query.Params [0].Value := fRefFirma;
      end;

      query.Open;

      if not (query.Fields [0].IsNull) then
        crfrom.RefDayLic := query.Fields [0].AsInteger;
        crfrom.LicPrice  := query.Fields [1].AsInteger;

      query.Close;
    finally
      query.Free;
    end;

    if not (crfrom.RefDayLic > 0) then
      FrontendMessages.MessageDLG(FormatMessageText (1878, []), mtError, [mbOk], 0)
    else begin
      crfrom.LicCountUpDown.Max      := fDayCount;
      crfrom.LicCountUpDown.Position := 1;

      if (crfrom.ShowModal = mrOk) then begin
        LicDBGrid.Reload (crfrom.Ref);
        UpdateSelectedQuery();
        FrontendMessages.MessageDLG(FormatMessageText (1857, []), mtInformation, [mbOk], 0);
      end;
    end;
  finally
    crfrom.Release;
  end;
end;

procedure TLicenceForm.DatePickerChange(Sender: TObject);
begin
  cbOnlyActiveLicences.State := cbUnchecked;
  QueryUpdate;
end;



end.
