unit SprachEditorMain;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs;

type
  TSprachEditorForm = class(TForm)
    procedure FormShow(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

var
  SprachEditorForm: TSprachEditorForm;

implementation

{$R *.dfm}

uses
  SprachModul;

procedure TSprachEditorForm.FormShow(Sender: TObject);
begin
  //LVSSprachModul.ShowEditor (Self);
end;

end.
