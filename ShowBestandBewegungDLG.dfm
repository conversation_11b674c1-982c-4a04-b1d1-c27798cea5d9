object ShowBestandBewegungForm: TShowBestandBewegungForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Bestandsbewegungen'
  ClientHeight = 449
  ClientWidth = 827
  Color = clBtnFace
  Constraints.MinHeight = 400
  Constraints.MinWidth = 700
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  OnHide = FormHide
  OnResize = FormResize
  OnShow = FormShow
  DesignSize = (
    827
    449)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 40
    Width = 44
    Height = 13
    Caption = 'Artikelnr.'
  end
  object ArtikelLabel: TLabel
    Left = 72
    Top = 40
    Width = 68
    Height = 13
    Caption = 'ArtikelLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label2: TLabel
    Left = 8
    Top = 180
    Width = 193
    Height = 13
    Caption = 'Bewegungen im vorgegebenen Zeitraum'
  end
  object Label4: TLabel
    Left = 8
    Top = 24
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object LagerLabel: TLabel
    Left = 72
    Top = 24
    Width = 62
    Height = 13
    Caption = 'LagerLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label6: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object MandLabel: TLabel
    Left = 72
    Top = 8
    Width = 61
    Height = 13
    Caption = 'MandLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Bevel1: TBevel
    Left = 8
    Top = 58
    Width = 811
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object RevBewDBGrid: TDBGridPro
    Left = 8
    Top = 199
    Width = 811
    Height = 213
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = RevBewDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 2
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\CS'
    RegistrySection = 'DBGrids'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
    OnColumnSort = RevBewDBGridColumnSort
  end
  object CloseButton: TButton
    Left = 744
    Top = 418
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 3
  end
  object DispGroupBox: TGroupBox
    Left = 408
    Top = 64
    Width = 411
    Height = 66
    Caption = 'Anzeigen'
    TabOrder = 1
    object WECheckBox: TCheckBox
      Left = 8
      Top = 15
      Width = 97
      Height = 17
      Caption = 'Wareneing'#228'nge'
      Checked = True
      State = cbChecked
      TabOrder = 0
      OnClick = CheckBoxClick
    end
    object WACheckBox: TCheckBox
      Left = 8
      Top = 30
      Width = 97
      Height = 17
      Caption = 'Warenausg'#228'nge'
      Checked = True
      State = cbChecked
      TabOrder = 1
      OnClick = CheckBoxClick
    end
    object RETCheckBox: TCheckBox
      Left = 8
      Top = 45
      Width = 97
      Height = 17
      Caption = 'Retouren'
      Checked = True
      State = cbChecked
      TabOrder = 2
      OnClick = CheckBoxClick
    end
    object KORRCheckBox: TCheckBox
      Left = 175
      Top = 15
      Width = 97
      Height = 17
      Caption = 'Korrekturen'
      Checked = True
      State = cbChecked
      TabOrder = 3
      OnClick = CheckBoxClick
    end
  end
  object DatumGroupBox: TGroupBox
    Left = 8
    Top = 64
    Width = 394
    Height = 66
    Caption = 'Datumsbereich'
    TabOrder = 0
    object Label5: TLabel
      Left = 136
      Top = 33
      Width = 13
      Height = 13
      Caption = 'bis'
    end
    object VonDateTimePicker: TDateTimePicker
      Left = 8
      Top = 29
      Width = 105
      Height = 21
      Date = 40912.720334942130000000
      Time = 40912.720334942130000000
      TabOrder = 0
      OnCloseUp = DateTimePickerChange
      OnChange = DateTimePickerChange
    end
    object BisDateTimePicker: TDateTimePicker
      Left = 176
      Top = 29
      Width = 105
      Height = 21
      Date = 40912.720334942130000000
      Time = 40912.720334942130000000
      TabOrder = 1
      OnCloseUp = DateTimePickerChange
      OnChange = DateTimePickerChange
    end
  end
  object DisplMultiColliRadioGroup: TRadioGroup
    Left = 8
    Top = 136
    Width = 811
    Height = 38
    Anchors = [akLeft, akTop, akRight]
    Caption = 'Multi-Colli'
    Columns = 3
    Items.Strings = (
      'Alle'
      'Nur Collibewegungen'
      'Nur SKU Bewegungen')
    TabOrder = 4
    OnClick = CheckBoxClick
  end
  object GroupByCheckBox: TCheckBox
    Left = 8
    Top = 418
    Width = 394
    Height = 17
    Anchors = [akLeft, akBottom]
    Caption = 'Bewegungen zusammenfassen'
    TabOrder = 5
    OnClick = GroupByCheckBoxClick
  end
  object RevBewDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 48
    Top = 208
  end
  object RevBewDataSource: TDataSource
    DataSet = RevBewDataSet
    Left = 88
    Top = 208
  end
end
