//******************************************************************************
//* Modul Name: RegistryUtils
//* Author    : <PERSON>
//******************************************************************************
//* $Revision: 56 $
//* $Date: 14.01.18 10:24 $
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
unit RegistryUtils;

interface

uses Windows, SysUtils, Classes, Registry;

const
  KEY_WOW64_64KEY = $0100;
  KEY_WOW64_32KEY = $0200;

  REG_QWORD       = $0b;

type
  TRegistryModule = class(TPersistent)
  private
    fKey         : HKEY;
    fRemodeKey   : HKEY;
    fSubKeyIndex : DWORD;
    fValueIndex  : DWORD;
    fKeyName     : String;
    fKeyBase     : HKEY;
    fIsOpen      : Boolean;
    fAccessTyp   : DWORD;

    function GetKeyName : String;
    function GetRootKeyName : String;

    function GetValueSubKey (const ValName : string; const AccessTyp : DWORD; var KeyHandle : HKEY; var KeyName : String) : Integer;
  protected
  public
    property FullKeyName : String read GetKeyName;
    property KeyBase     : HKEY read fKeyBase;
    property KeyName     : String read fKeyName;
    property IsOpen      : Boolean read fIsOpen;
    property RemodeKey   : HKEY read fRemodeKey;

    constructor Create; overload;
    constructor Create (const MasterRef : TRegistryModule); overload;
    destructor  Destroy; override;

    function OpenKey       (const MasterKey : HKEY; SubKey : String; AccessTyp : DWORD; const CreateFlag : Boolean) : Integer;
    function OpenRemodeKey (const Server : String; const MasterKey : HKEY; SubKey : String; AccessTyp : DWORD; const CreateFlag : Boolean) : Integer;

    function GetLastKeyAccess : TDateTime;

    function CloseKey : Integer;
    function CloseRemodeKey : Integer;

    function GetFirstValue (var StrValue : string) : Integer;
    function GetNextValue  (var StrValue : string) : Integer;

    function CheckRegValue (const ValName : string) : Integer;

    function ReadRegValue (const ValName : string) : string; overload;
    function ReadRegValue (const ValName : string; var Wert : String) : Integer; overload;
    function ReadRegValue (const ValName : string; var Wert : Integer) : Integer; overload;
    function ReadRegValue (const ValName : string; var Wert : Smallint) : Integer; overload;
    function ReadRegValue (const ValName : string; var Wert; const MaxSize : Integer; var Size : Integer) : Integer; overload;
    function ReadRegValue (const ValName : string; var Flag : Boolean; DefaultFlag : boolean = False) : Integer; overload;

    function WriteRegValue (const ValName, Wert : string) : Integer; overload;
    function WriteRegValue (const ValName : String; const Wert : Integer) : Integer; overload;
    function WriteRegValue (const ValName : String; const Wert : Boolean) : Integer; overload;
    function WriteRegValue (const ValName : string; var Wert; const Size : Integer) : Integer; overload;

    function DeleteRegKey   (const SubKey : String; const Recursive : Boolean = False) : Integer;
    function DeleteRegValue (const ValName : string) : Integer;

    function GetFirstSubKey : string;
    function GetNextSubKey : string;

    function SaveRegistry (const Filename : TFilename) : Integer;
    function RestoreRegistry (const Filename : TFilename; const CreateFlag : Boolean = True) : Integer;
  end;

implementation

  {$IFDEF TRACE}
uses
    Trace, Win32Utils;
  {$ENDIF}

type
  CharArray  = array [0..4095] of char;
  PCharArray = ^CharArray;

function FileTime2DateTime (FileTime: TFileTime): TDateTime;
var
   LocalFileTime: TFileTime;
   SystemTime: TSystemTime;
begin
   FileTimeToLocalFileTime(FileTime, LocalFileTime) ;
   FileTimeToSystemTime(LocalFileTime, SystemTime) ;
   Result := SystemTimeToDateTime(SystemTime) ;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TRegistryModule.Create;
begin
  inherited Create;

  fKey         := 0;
  fRemodeKey   := 0;
  fSubKeyIndex := 0;
  fValueIndex  := 0;
  fKeyName     := '';
  fKeyBase     := 0;
  fIsOpen      := false;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TRegistryModule.Create (const MasterRef : TRegistryModule);
begin
  inherited Create;

  fKey := 0;
  fSubKeyIndex := 0;
  fValueIndex  := 0;
  fKeyName     := '';
  fKeyBase     := 0;
  fIsOpen      := false;

  fRemodeKey := MasterRef.RemodeKey;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
destructor TRegistryModule.Destroy;
begin
  if (fKey <> 0) then
    RegCloseKey (fKey);

  if (fRemodeKey <> 0) Then
    RegCloseKey (fRemodeKey);

  inherited Destroy;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.GetKeyName : String;
begin
  case fKeyBase of
    HKEY_CLASSES_ROOT     : Result := 'HKEY_CLASSES_ROOT\'+ fKeyName;
    HKEY_CURRENT_CONFIG   : Result := 'HKEY_CURRENT_CONFIG\'+ fKeyName;
    HKEY_CURRENT_USER     : Result := 'HKEY_CURRENT_USER\'+ fKeyName;
    HKEY_LOCAL_MACHINE    : Result := 'HKEY_LOCAL_MACHINE\'+ fKeyName;
    HKEY_PERFORMANCE_DATA : Result := 'HKEY_PERFORMANCE_DATA\'+ fKeyName;
    HKEY_USERS            : Result := 'HKEY_USERS\'+ fKeyName;
    else
      Result := fKeyName;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.GetRootKeyName : String;
begin
  case fKeyBase of
    HKEY_CLASSES_ROOT     : Result := 'HKEY_CLASSES_ROOT';
    HKEY_CURRENT_CONFIG   : Result := 'HKEY_CURRENT_CONFIG';
    HKEY_CURRENT_USER     : Result := 'HKEY_CURRENT_USER';
    HKEY_LOCAL_MACHINE    : Result := 'HKEY_LOCAL_MACHINE';
    HKEY_PERFORMANCE_DATA : Result := 'HKEY_PERFORMANCE_DATA';
    HKEY_USERS            : Result := 'HKEY_USERS';
    else
      Result := '';
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.OpenKey (const MasterKey : HKEY; SubKey : String; AccessTyp : DWORD; const CreateFlag : Boolean) : Integer;
var
  cname : array [0..255] of char;
  lres  : LongWord;
  res   : Integer;
  stat  : DWORD;
  mkey  : HKEY;
begin
  {$IFDEF TRACE}
    FunctionStart (TRACE_EXTENDET, 'TRegistryModule.OpenKey');
    TraceParameter (TRACE_EXTENDET, 'MasterKey ', '0x'+IntToHex (MasterKey, 0));
    TraceParameter (TRACE_EXTENDET, 'SubKey    ', SubKey);
    TraceParameter (TRACE_EXTENDET, 'AccessTyp ', '0x'+IntToHex (AccessTyp, 4));
    TraceParameter (TRACE_EXTENDET, 'CreateFlag', CreateFlag);
  {$ENDIF}

  CloseKey;


  if (Length (SubKey) = 0) then
    cname [0] := #0  
  else begin
    StrPCopy (cname, SubKey);

    if (SubKey [Length (SubKey)] = '\') then
      cname [Length (SubKey) - 1] := #0
    else
      cname [Length (SubKey)] := #0;
  end;

  if (fRemodeKey = 0) Then
    mkey := MasterKey
  else
    mkey := fRemodeKey;

  lres := RegOpenKeyEx (mkey, cname, 0, AccessTyp, fKey);
  if (lres = ERROR_SUCCESS) then begin
    res := 0;

    fKeyName   := StrPas (cname);
    fKeyBase   := mkey;
    fIsOpen    := True;
    fAccessTyp := AccessTyp;
  end else if (lres = ERROR_FILE_NOT_FOUND) and (CreateFlag) then begin
    lres := RegCreateKeyEx (mkey, cname, 0, Nil, REG_OPTION_NON_VOLATILE, AccessTyp, Nil, fKey, @stat);

    if (lres = ERROR_SUCCESS) then begin
      res := 0;

      fIsOpen    := True;
      fKeyName   := StrPas (cname);
      fKeyBase   := mkey;
      fAccessTyp := AccessTyp;
    end else begin
      res := lres;

      {$IFDEF TRACE}
        TraceString ('RegCreateKeyEx: '+GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
      {$ENDIF}
    end;
  end else begin
    res := lres;

    {$IFDEF TRACE}
      TraceString ('RegOpenKeyEx: '+GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
    {$ENDIF}
  end;

  OpenKey := res;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.OpenRemodeKey (const Server : String; const MasterKey : HKEY; SubKey : String; AccessTyp : DWORD; const CreateFlag : Boolean) : Integer;
var
  sname : array [0..255] of char;
  cname : array [0..255] of char;
  lres  : DWORD;
  res,
  count : Integer;
  stat  : DWORD;
begin
  {$IFDEF TRACE}
    FunctionStart (TRACE_EXTENDET, 'TRegistryModule.OpenRemodeKey');

    TraceParameter (TRACE_EXTENDET, 'Server    ', Server);
    TraceParameter (TRACE_EXTENDET, 'MasterKey ', '0x'+IntToHex (MasterKey, 0));
    TraceParameter (TRACE_EXTENDET, 'SubKey    ', SubKey);
    TraceParameter (TRACE_EXTENDET, 'AccessTyp ', '0x'+IntToHex (AccessTyp, 4));
    TraceParameter (TRACE_EXTENDET, 'CreateFlag', CreateFlag);
  {$ENDIF}

  CloseKey;

  if (fRemodeKey <> 0) Then
    RegCloseKey (fRemodeKey);
  fRemodeKey := 0;

  StrPCopy (sname, Server);

  lres := RegConnectRegistry (sname, MasterKey, fRemodeKey);
  if (lres = ERROR_IO_PENDING) then begin
    count := 0;

    while (lres = ERROR_IO_PENDING) and (count < 10) do begin
      Inc (count);

      Sleep (50 + Random (50));

      lres := RegConnectRegistry (sname, MasterKey, fRemodeKey)
    end;
  end;

  if (lres <> ERROR_SUCCESS) then begin
    {$IFDEF TRACE}
      TraceError ('RegConnectRegistry: ',GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
    {$ENDIF}

    res := lres;
  end else begin
    StrPCopy (cname, SubKey);
    cname [Length (SubKey)] := #0;

    lres := RegOpenKeyEx (RemodeKey, cname, 0, AccessTyp, fKey);

    if (lres = ERROR_SUCCESS) then begin
      fKeyName := SubKey;
      fIsOpen  := True;

      res := 0;
    end else if (lres = ERROR_FILE_NOT_FOUND) and (CreateFlag) then begin
      lres := RegCreateKeyEx (RemodeKey, cname, 0, Nil, REG_OPTION_NON_VOLATILE, AccessTyp, Nil, fKey, @stat);

      if (lres = ERROR_SUCCESS) then begin
        fKeyName := SubKey;
        fIsOpen  := True;

        res := 0;
      end else begin
        {$IFDEF TRACE}
          TraceError ('RegCreateKeyEx', GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
        {$ENDIF}

        res := lres;
      end;
    end else begin
      {$IFDEF TRACE}
        TraceError ('RegOpenKeyEx', GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
      {$ENDIF}

      res := lres;
    end;
  end;

  OpenRemodeKey := res;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.CloseKey : Integer;
begin
  {$IFDEF TRACE}
    FunctionStart (TRACE_EXTENDET, 'TRegistryModule.CloseKey');
  {$endif}

  if (fKey <> 0) then
    RegCloseKey (fKey);

  fKey    := 0;
  fIsOpen := false;

  CloseKey := 0;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.CloseRemodeKey : Integer;
begin
  CloseKey;

  if (fRemodeKey <> 0) Then
    RegCloseKey (fRemodeKey);

  fRemodeKey := 0;

  CloseRemodeKey := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.CheckRegValue (const ValName : string) : Integer;
var
  valtype : DWORD;
  lres    : LongWord;
  regkey  : HKEY;
  keyname : String;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.CheckRegValue');
  TraceParameter (TRACE_EXTENDET, 'ValName', ValName);
  {$ENDIF}

  lres := GetValueSubKey (ValName, KEY_READ, regkey, keyname);

  if (lres <> 0) then begin
    {$IFDEF TRACE}
      TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
    {$ENDIF}

    CheckRegValue := -1
  end else begin
    lres := RegQueryValueEx (regkey, PChar (keyname), Nil, @valtype, Nil, Nil);

    if (lres = ERROR_SUCCESS) then
      CheckRegValue := 0
    else begin
      {$IFDEF TRACE}
        TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
      {$ENDIF}

      CheckRegValue := -1;
    end;
  end;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.GetFirstSubKey : string;
var
  size,
  lres    : DWORD;
  value   : PChar;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.GetFirstSubKey');
  {$ENDIF}

  {$ifdef Trace}
    TraceString (TRACE_EXTENDET, '1');
  {$endif}

  fSubKeyIndex := 0;

  size := 511;
  GetMem (value, size + 1);

  ZeroMemory (value, size);

  try
    lres := RegEnumKeyEx (fKey, fSubKeyIndex, value, size, Nil, Nil, Nil, Nil);
    if (lres <> ERROR_SUCCESS) then begin
    {$IFDEF TRACE}
      TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
    {$ENDIF}

      GetFirstSubKey := ''
    end else begin
      {$ifdef Trace}
        TraceResult (TRACE_EXTENDET, 'size', size);
      {$endif}

      if (size = 0) then
        GetFirstSubKey := ''
      else begin
        PCharArray (value)^[size div sizeof (char)] := #0;
        GetFirstSubKey := StrPas (PCharArray (value)^);
      end;

    {$ifdef Trace}
      TraceResult (TRACE_EXTENDET, 'GetFirstSubKey', Result);
    {$endif}
    end;
  finally
    FreeMem (value);
  end;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.GetNextSubKey : string;
var
  size,
  lres    : DWORD;
  value   : PChar;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.GetNextSubKey');
  {$ENDIF}

  size := 511;
  GetMem (value, size + 1);

  ZeroMemory (value, size);

  try
    lres := RegEnumKeyEx (fKey, fSubKeyIndex + 1, value, size, Nil, Nil, Nil, Nil);

    if (lres <> ERROR_SUCCESS) then begin
    {$IFDEF TRACE}
      TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
    {$ENDIF}

      GetNextSubKey := ''
    end else begin
      {$ifdef Trace}
        TraceResult (TRACE_EXTENDET, 'size', size);
      {$endif}

      Inc (fSubKeyIndex);

      if (size = 0) then
        GetNextSubKey := ''
      else begin
        PCharArray (value)^[size div sizeof (char)] := #0;
        GetNextSubKey := StrPas (PCharArray (value)^);
      end;

    {$ifdef Trace}
      TraceResult (TRACE_EXTENDET, 'GetNextSubKey', Result);
    {$endif}
    end;
  finally
    FreeMem (value);
  end;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.GetFirstValue (var StrValue : string) : Integer;
var
  size,
  dwres,
  valtype : DWORD;
  value   : PChar;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.GetFirstValue');
  {$ENDIF}

  fValueIndex := 0;

  size := 511;
  GetMem (value, size + 1);

  ZeroMemory (value, size);

  try
    dwres := RegEnumValue (fKey, fValueIndex, value, size, Nil, @valtype, Nil, Nil);
    if (dwres = ERROR_SUCCESS) then begin
      if (size = 0) then
        StrValue := ''
      else begin
        StrValue := StrPas (PCharArray (value)^);
      end;

      {$IFDEF TRACE}
        TraceResult ('StrValue', StrValue);
      {$endif}

      Result := 0;
    end else begin
      StrValue := '';
      Result := dwres;
    end;
  finally
    FreeMem (value);
  end;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.GetNextValue (var StrValue : string) : Integer;
var
  size,
  dwres,
  valtype : DWORD;
  value   : PChar;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.GetNextValue');
  {$ENDIF}

  size := 511;
  GetMem (value, size + 1);

  ZeroMemory (value, size);

  try
    dwres := RegEnumValue (fKey, fValueIndex + 1, value, size, Nil, @valtype, Nil, Nil);
    if (dwres = ERROR_SUCCESS) then begin
      Inc (fValueIndex);

      if (size = 0) then
        StrValue := ''
      else begin
        StrValue := StrPas (PCharArray (value)^);
      end;

      {$IFDEF TRACE}
        TraceResult ('StrValue', StrValue);
      {$endif}

      Result := 0;
    end else begin
      StrValue := '';
      Result := dwres;
    end;
  finally
    FreeMem (value);
  end;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.GetValueSubKey (const ValName : string; const AccessTyp : DWORD; var KeyHandle : HKEY; var KeyName : String) : Integer;
var
  res     : Integer;
  strpos  : Integer;
  lres    : LongWord;
  stat    : DWORD;
  subname : String;
begin
  strpos := Length (ValName);
  while (strpos > 0) and (ValName [strpos] <> '\') do Dec (strpos);

  if (strpos = 0) then begin
    res := 0;
    KeyHandle  := fKey;
    KeyName := ValName;
  end else if (fKeyBase = 0) then
    res := ERROR_INVALID_HANDLE
  else begin
    KeyName := Copy (ValName, strpos + 1, Length (ValName) - strpos);

    subname := fKeyName + '\' + Copy (ValName,1,strpos - 1) + '\';

    lres := RegOpenKeyEx (fKeyBase, PChar (subname), 0, AccessTyp, KeyHandle);

    if (lres = ERROR_SUCCESS) then
      res := 0
    else if (lres = ERROR_FILE_NOT_FOUND) and ((AccessTyp and KEY_WRITE) = KEY_WRITE) then begin
      lres := RegCreateKeyEx (fKeyBase, PChar (subname), 0, Nil, REG_OPTION_NON_VOLATILE, AccessTyp, Nil, KeyHandle, @stat);

      if (lres = ERROR_SUCCESS) then
        res := 0
      else
        res := lres;
    end else
      res := lres;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.ReadRegValue (const ValName : string) : string;
var
  size    : DWORD;
  valtype : DWORD;
  lres    : LongWord;
  value   : PChar;
  regkey  : HKEY;
  keyname : String;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.ReadRegValue (String 1)');
  TraceParameter (TRACE_EXTENDET, 'ValName', ValName);
  {$ENDIF}

  if (GetValueSubKey (ValName, KEY_READ, regkey, keyname) <> 0) then
    ReadRegValue := ''
  else begin
    size := 4096 * sizeof (char);
    GetMem (value, size + 1);

    ZeroMemory (value, size);

    try
      lres := RegQueryValueEx (regkey, PChar (keyname), Nil, @valtype, PByte (value), @size);

      if (lres = ERROR_SUCCESS) then begin
        {$IFDEF TRACE}
          TraceResult ('valtype', valtype);
          TraceResult ('size   ', size);
        {$ENDIF}

        if not ((valtype = REG_EXPAND_SZ) or (valtype = REG_MULTI_SZ) or (valtype = REG_SZ)) then
          ReadRegValue := ''
        else if (size = 0) then
          ReadRegValue := ''
        else if (size = 1) then
          ReadRegValue := ''
        else begin
          PCharArray (value)^[(size - 1) div sizeof (char)] := #0;
          ReadRegValue := StrPas (PCharArray (value)^);
        end;
      end else begin
        {$IFDEF TRACE}
          TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
        {$ENDIF}

        ReadRegValue := '';
      end;

      if (regkey <> fKey) then
        CloseHandle (regkey);
    finally
      FreeMem (value);
    end;
  end;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.ReadRegValue (const ValName : string; var Wert : String) : Integer;
var
  size    : DWORD;
  valtype : DWORD;
  lres    : LongWord;
  value   : PChar;
  regkey  : HKEY;
  keyname : String;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.ReadRegValue (String 2)');
  TraceParameter (TRACE_EXTENDET, 'ValName', ValName);
  {$ENDIF}

  lres := GetValueSubKey (ValName, KEY_READ, regkey, keyname);

  if (lres <> 0) then begin
    {$IFDEF TRACE}
      TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
    {$ENDIF}

    Wert := '';
    ReadRegValue := -1
  end else begin
    size := 4096 * sizeof (char);
    GetMem (value, size + 1);

    ZeroMemory (value, size);

    try
      lres := RegQueryValueEx (regkey, PChar (keyname), Nil, @valtype, PByte (value), @size);

      if (lres = ERROR_SUCCESS) then begin
        if not ((valtype = REG_EXPAND_SZ) or (valtype = REG_MULTI_SZ) or (valtype = REG_SZ)) then
          Wert := ''
        else if (size = 0) then
          Wert := ''
        else if (size = 1) then
          Wert := ''
        else begin
          PCharArray (value)^[(size - 1) div sizeof (char)] := #0;
          Wert := StrPas (PCharArray (value)^);
        end;

        ReadRegValue := 0;
      end else begin
        {$IFDEF TRACE}
          TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
        {$ENDIF}

        Wert := '';
        ReadRegValue := -1
      end;

      if (regkey <> fKey) then
        CloseHandle (regkey);
    finally
      FreeMem (value);
    end;
  end;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.ReadRegValue (const ValName : string; var Flag : Boolean; DefaultFlag : boolean = False) : Integer;
var
  size    : DWORD;
  valtype : DWORD;
  lres    : LongWord;
  value   : DWORD;
  regkey  : HKEY;
  keyname : String;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.ReadRegValue (bool)');
  TraceParameter (TRACE_EXTENDET, 'ValName', ValName);
  {$ENDIF}

  Flag := DefaultFlag;

  lres := GetValueSubKey (ValName, KEY_READ, regkey, keyname);

  if (lres <> 0) then begin
    {$IFDEF TRACE}
      TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
    {$ENDIF}

    ReadRegValue := -1
  end else begin
    size := sizeof (value);

    lres := RegQueryValueEx (regkey, PChar (keyname), Nil, @valtype, @value, @size);

    if (lres = ERROR_SUCCESS) then begin
      Flag := (value <> 0);
      ReadRegValue := 0;
    end else begin
      {$IFDEF TRACE}
        TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
      {$ENDIF}

      ReadRegValue := -1
    end;

    if (regkey <> fKey) then
      CloseHandle (regkey);
  end;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.ReadRegValue (const ValName : string; var Wert : Smallint) : Integer;
var
  intwert : Integer;
begin
  Result := ReadRegValue (ValName, intwert);

  if (Result = 0) then
    Wert := intwert and $10000;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.ReadRegValue (const ValName : string; var Wert : Integer) : Integer;
var
  size    : DWORD;
  valtype : DWORD;
  lres    : LongWord;
  value   : DWORD;
  regkey  : HKEY;
  keyname : String;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.ReadRegValue (int)');
  TraceParameter (TRACE_EXTENDET, 'ValName', ValName);
  {$ENDIF}

  lres := GetValueSubKey (ValName, KEY_READ, regkey, keyname);

  if (lres <> 0) then begin
    {$IFDEF TRACE}
      TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
    {$ENDIF}

    ReadRegValue := -1
  end else begin
    size := sizeof (value);

    lres := RegQueryValueEx (regkey, PChar (keyname), Nil, @valtype, @value, @size);

    if (lres = ERROR_SUCCESS) then begin
      Wert := Integer (value);
      ReadRegValue := 0;
    end else begin
      {$IFDEF TRACE}
        TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
      {$ENDIF}

      ReadRegValue := -1
    end;

    if (regkey <> fKey) then
      CloseHandle (regkey);
  end;
  
  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.ReadRegValue (const ValName : string; var Wert; const MaxSize : Integer; var Size : Integer) : Integer;
var
  dwsize  : DWORD;
  valtype : DWORD;
  lres    : LongWord;
  value   : Pointer;
  regkey  : HKEY;
  keyname : String;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.ReadRegValue');
  TraceParameter (TRACE_EXTENDET,'ValName', ValName);
  {$ENDIF}

  lres := GetValueSubKey (ValName, KEY_READ, regkey, keyname);

  if (lres <> 0) then begin
    {$IFDEF TRACE}
      TraceString (GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
    {$ENDIF}

    ReadRegValue := -1
  end else begin
    dwsize := MaxSize;

    value := @Wert;

    lres := RegQueryValueEx (regkey, PChar (keyname), Nil, @valtype, value, @dwsize);

    if (lres = ERROR_SUCCESS) then begin
      {$IFDEF TRACE}
        TraceResult ('valtype', valtype);
        TraceResult ('dwsize ', dwsize);
      {$ENDIF}

  //    Move (Wert, value, dwsize);
      Size := dwsize;
      ReadRegValue := 0;
    end else begin
      {$IFDEF TRACE}
        TraceError ('TRegistryModule.ReadRegValue:', GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
      {$ENDIF}

      ReadRegValue := -1
    end;
  end;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.WriteRegValue (const ValName, Wert : string) : Integer;
var
  res      : Integer;
  valuestr : array [0..4096] of char;
  regkey   : HKEY;
  keyname  : String;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.WriteRegValue (string)');
  TraceParameter (TRACE_EXTENDET,'ValName', ValName);
  TraceParameter (TRACE_EXTENDET,'Wert', Wert);
  {$ENDIF}

  res := GetValueSubKey (ValName, KEY_WRITE, regkey, keyname);

  if (res = 0) then begin
    StrPCopy (valuestr, Copy (Wert, 1, High (valuestr) - 1));

    res := RegSetValueEx (regkey, PChar (keyname), 0, REG_SZ, @valuestr [0], (Length (Wert) + 1) * SizeOf (Char));

    if (regkey <> fKey) then
      CloseHandle (regkey);
  end;

  WriteRegValue := res;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.DeleteRegKey (const SubKey : String; const Recursive : Boolean) : Integer;

    function DeleteSubKeys (SubKeyName : String) : Integer;
    var
      res,
      keyidx  : Integer;
      subkey  : HKEY;
      size    : DWORD;
      value   : PChar;
      keylist : TStringList;
    begin
      res := RegOpenKeyEx (fKeyBase, PChar (SubKeyName), 0, fAccessTyp, subkey);
      if (res = ERROR_SUCCESS) then begin
        try
          keyidx := 0;

          size := 512;
          GetMem (value, size);

          keylist := TStringList.Create;

          try
            size := 512;
            res := RegEnumKeyEx (subkey, keyidx, value, size, Nil, Nil, Nil, Nil);

            while (res = ERROR_SUCCESS) do begin
              keylist.Add (StrPas (value));

              Inc (keyidx);

              size := 512;
              res := RegEnumKeyEx (subkey, keyidx, value, size, Nil, Nil, Nil, Nil);
            end;

            res := 0;
            keyidx := 0;

            while (res = 0) and (keyidx < keylist.Count) do begin
              res := DeleteSubKeys (SubKeyName+'\'+keylist [keyidx]);

              if (res = 0) then
                res := RegDeleteKey (subkey, PChar (keylist [keyidx]));

              if (res = 0) then
                Inc (keyidx);
            end;
          finally
            keylist.Free;
            FreeMem (value);
          end;
        finally
          CloseHandle (subkey);
        end;
      end;

      Result := res;
    end;

var
  res      : Integer;
begin
  {$IFDEF TRACE}
    FunctionStart (TRACE_EXTENDET, 'TRegistryModule.DeleteRegKey');
    TraceParameter (TRACE_EXTENDET,'SubKey', SubKey);
  {$ENDIF}

  if not (Recursive) then
    res := RegDeleteKey (fKey, PChar (SubKey))
  else begin
    res := DeleteSubKeys (fKeyName + '\' + SubKey);

    if (res = 0) then
      res := RegDeleteKey (fKey, PChar (SubKey))
  end;

  DeleteRegKey := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.DeleteRegValue (const ValName : string) : Integer;
var
  res      : Integer;
  namestr  : array [0..255] of char;
begin
  {$IFDEF TRACE}
    FunctionStart (TRACE_EXTENDET, 'TRegistryModule.DeleteRegValue');
    TraceParameter (TRACE_EXTENDET,'ValName', ValName);
  {$ENDIF}

  StrPCopy (namestr, ValName);

  res := RegDeleteValue (fKey, @namestr);

  DeleteRegValue := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.WriteRegValue (const ValName : String; const Wert : Integer) : Integer;
var
  res      : Integer;
  value    : DWORD;
  regkey  : HKEY;
  keyname : String;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.WriteRegValue (int)');
  TraceParameter (TRACE_EXTENDET,'ValName', ValName);
  TraceParameter (TRACE_EXTENDET,'Wert', Wert);
  {$ENDIF}

  res := GetValueSubKey (ValName, KEY_WRITE, regkey, keyname);

  if (res = 0) then begin
    value := DWORD (Wert);

    res := RegSetValueEx (regkey, PChar (keyname), 0, REG_DWORD, @value, sizeof (DWORD));

    if (regkey <> fKey) then
      CloseHandle (regkey);
  end;

  WriteRegValue := res;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.WriteRegValue (const ValName : String; const Wert : Boolean) : Integer;
var
  res     : Integer;
  value   : DWORD;
  regkey  : HKEY;
  keyname : String;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.WriteRegValue (bool)');
  TraceParameter (TRACE_EXTENDET,'ValName', ValName);
  TraceParameter (TRACE_EXTENDET,'Wert', Wert);
  {$ENDIF}

  res := GetValueSubKey (ValName, KEY_WRITE, regkey, keyname);

  if (res = 0) then begin
    if (Wert) Then
      value := 1
    else value := 0;

    res := RegSetValueEx (regkey, PChar (keyname), 0, REG_DWORD, @value, sizeof (DWORD));

    if (regkey <> fKey) then
      CloseHandle (regkey);
  end;

  WriteRegValue := res;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.WriteRegValue (const ValName : string; var Wert; const Size : Integer) : Integer;
var
  res      : Integer;
  value    : Pointer;
  regkey  : HKEY;
  keyname : String;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.WriteRegValue (var)');
  TraceParameter (TRACE_EXTENDET,'ValName', ValName);
  {$ENDIF}

  res := GetValueSubKey (ValName, KEY_WRITE, regkey, keyname);

  if (res = 0) then begin
    value := @Wert;
    res := RegSetValueEx (regkey, PChar (keyname), 0, REG_BINARY, value, Size);

    if (regkey <> fKey) then
      CloseHandle (regkey);
  end;

  WriteRegValue := res;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.GetLastKeyAccess : TDateTime;
var
  lres    : LongWord;
  ftime   : FILETIME;
  inttime : Integer;
begin
  {$IFDEF TRACE}
  FunctionStart (TRACE_EXTENDET, 'TRegistryModule.GetLastKeyAccess');
  {$endif}

  lres := RegQueryInfoKey (fKey, Nil, Nil, Nil, Nil, Nil, Nil, Nil, Nil, Nil, Nil, @ftime);

  if (lres = ERROR_SUCCESS) then begin
    FileTimeToLocalFileTime(ftime, ftime);

    if FileTimeToDosDateTime(ftime, LongRec(inttime).Hi, LongRec(inttime).Lo) then
      GetLastKeyAccess := FileDateToDateTime (inttime)
    else GetLastKeyAccess := 0;
  end else begin
    {$IFDEF TRACE}
      TraceError ('TRegistryModule.GetLastKeyAccess:', GetAPIErrorMessage (lres) + ' ('+ IntToStr (lres) + ')');
    {$ENDIF}

    GetLastKeyAccess := 0
  end;

  {$IFDEF TRACE}
  FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.SaveRegistry (const Filename : TFilename) : Integer;
type
  ByteArray = array [0..4065] of byte;

var
  fstream : TFileStream;

  function StreamWriteLn (const Line : AnsiString = '') : Integer;
  var
    outstr : AnsiString;
  begin
    if Assigned (fstream) Then begin
      outstr := Line + #13 + #10;

      fstream.Write (outstr [1], Length (outstr));
    end;

    Result := 0;
  end;

  function StreamWrite (const Line : AnsiString = '') : Integer;
  var
    outstr : AnsiString;
  begin
    if Assigned (fstream) Then begin
      outstr := Line;

      fstream.Write (outstr [1], Length (outstr));
    end;

    Result := 0;
  end;

  function SaveSubKey (SubKey : HKEY; const KeyName : String) : Integer;
  var
    i,
    vidx,
    sidx    : Integer;
    vsize,
    dsize,
    dwres,
    valtype : DWORD;
    value   : PChar;
    data    : Pointer;
    outstr  : AnsiString;
    valstr  : String;
    nextkey : HKEY;
  begin
    StreamWriteLn ('['+GetRootKeyName+'\'+KeyName+']');

    GetMem (value, 1024 + 1);
    GetMem (data, 2048);

    try
      vidx := 0;
      dwres := ERROR_SUCCESS;

      while (dwres = ERROR_SUCCESS) do begin
        dsize := 2048;
        vsize := 1024;

        dwres := RegEnumValue (SubKey, vidx, value, vsize, Nil, @valtype, data, @dsize);

        if (dwres = ERROR_SUCCESS) then begin
          if (dsize > 0) then begin
            outstr := '"'+StrPas (PCharArray (value)^)+'"';

            if (valtype = REG_DWORD) then
              StreamWriteLn (outstr + '=dword:'+IntToHex (DWORD (data^), 8))
            else if (valtype = REG_QWORD) then
              StreamWriteLn (outstr + '=qword:'+IntToHex (Int64 (data^), 8))
            else if (valtype = REG_BINARY) or (valtype = REG_MULTI_SZ) or (valtype = REG_EXPAND_SZ) then begin
              if (valtype = REG_BINARY) then
                outstr := outstr + '=hex:'
              else if (valtype = REG_EXPAND_SZ) then
                outstr := outstr + '=hex(2):'
              else if (valtype = REG_MULTI_SZ) then
                outstr := outstr + '=hex(7):';

              for i := 0 to (dsize - 1) do begin
                outstr := outstr + IntToHex (ByteArray (data^) [i], 2);

                if (i < (Integer (dsize) - 1)) then
                  outstr := outstr + ',';

                if (Length (outstr) > 77) then begin
                  StreamWriteLn (outstr+'\');
                  outstr := '  ';
                end;
              end;

              if (Length (outstr) > 0) then
                  StreamWriteLn (outstr);
            end else if (valtype = REG_SZ) then begin
              outstr := outstr + '="';

              if (dsize > 1) then begin
                PCharArray (data)^[(dsize - 1) div sizeof (char)] := #0;

                valstr := StrPas (PCharArray (data)^);

                for i := 1 to Length (valstr) do begin
                  if (valstr [i] = '"') then
                    outstr := outstr + '\"'
                  else
                    outstr := outstr + AnsiChar (valstr [i]);
                end;
              end;

              outstr := outstr + '"';

              StreamWriteLn (outstr);
            end;
          end;

          vidx := vidx + 1;
        end;
      end;

      StreamWriteLn;

      sidx := 0;
      dwres := 0;

      while (dwres = ERROR_SUCCESS) do begin
        vsize := 1024;
        dwres := RegEnumKeyEx (SubKey, sidx, value, vsize, Nil, Nil, Nil, Nil);

        if (dwres = ERROR_SUCCESS) then begin
          if (Length (KeyName + '\' + StrPas (value)) > 250) then
            dwres := ERROR_INVALID_DATA
          else begin
            dwres := RegOpenKeyEx (fKeyBase, PChar (KeyName + '\' + StrPas (value)), 0, fAccessTyp, nextkey);
            if (dwres = ERROR_SUCCESS) then begin
              SaveSubKey (nextkey, KeyName + '\' + StrPas (value));

              RegCloseKey (nextkey);
            end;

            Inc (sidx);
          end;
        end;
      end;
    finally
      FreeMem (data);
      FreeMem (value);
    end;

    Result := 0;
  end;

var
  res : Integer;
begin
  if (fKey = 0) then
    res := 2
  else begin
    res := 0;

    try
      fstream := TFileStream.Create (Filename, fmCreate);
    except
      res := -9;
      fstream := Nil;
    end;

    if Assigned (fstream) then begin
      try
        res := SaveSubKey (fKey, fKeyName);
      finally
        fstream.Free;
      end;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRegistryModule.RestoreRegistry (const Filename : TFilename; const CreateFlag : Boolean) : Integer;

type
  CharArray = array of char;
  ByteArray = array [0..4065] of byte;
  PByteArray = ^ByteArray;

var
  res,
  idx,
  sidx,
  size,
  vsize,
  stat,
  intwert    : Integer;
  dwres,
  dwvalue,
  keystat   : DWORD;
  longwert  : Int64;
  keyname,
  valuename : String;
  topkeystr,
  copykeystr : String;
  typstr,
  valstr    : String;
  buffer  : CharArray;
  fstream : TFileStream;
  subkey  : HKEY;
  value   : array [0..4096] of char;
  bytevalue : array [0..4096] of byte;
  ktime,
  ftime,
  ctime,
  atime   : FILETIME;

  {$ifdef Debug}
    dbgstr  : String;
  {$endif}
begin
  topkeystr := '';

  if (fKey = 0) then begin
    copykeystr := '';
    fAccessTyp := KEY_WRITE;
  end else begin
    copykeystr := fKeyName;
  end;

  res := 0;

  try
    fstream := TFileStream.Create (Filename, fmOpenRead or fmShareDenyWrite);
  except
    res := -9;
    fstream := Nil;
  end;

  if Assigned (fstream) then begin
    try
      GetFileTime (fstream.Handle, @ctime, @atime, @ftime);

      size := fstream.Size;

      try
        try
          SetLength (buffer, size);
          fstream.ReadBuffer (buffer [0], size);
        except
          dwres := 3;
        end;

        idx := 0;
        stat := -1;
        dwres := 0;
        subkey := 0;
        valuename := '';

        while (idx < size) and (dwres = 0) do begin
          if (buffer[idx] = '[') then begin
            RegCloseKey (subkey);
            keyname := '';
            stat := 0;
            subkey  := 0;
          end else if (buffer[idx] = ']') then begin
            {$ifdef Debug}
              if (keyname = 'HKEY_CURRENT_USER\Software\Zimbo-LVS\Systeme\Entwicklung\LVSENTW\DBGrids\LVSForm.BenSessionDBGrid') then
                sidx := 1;
            {$endif}

            if (Length (topkeystr) = 0) then
              topkeystr := keyname;

            if (Length (copykeystr) > 0) then begin
              Delete (keyname, 1, Length (topkeystr));
              keyname := copykeystr + keyname;
            end else begin
              if (Copy (keyname, 1, 4) = 'HKEY') then begin
                sidx := 1;
                while (sidx <= Length (keyname)) and (keyname [sidx] <> '\') do
                  Inc (sidx);

                Delete (keyname, 1, sidx);
              end;
            end;

            dwres := RegOpenKeyEx (fKeyBase, PChar (keyname), 0, fAccessTyp or KEY_QUERY_VALUE, subkey);
            if (dwres = ERROR_FILE_NOT_FOUND) then begin
              if (CreateFlag) then
                dwres := RegCreateKeyEx (fKeyBase, PChar (keyname), 0, Nil, REG_OPTION_NON_VOLATILE, fAccessTyp, Nil, subkey, @keystat)
              else
                dwres := 0;
            end;

            stat := -1;
          end else if (buffer[idx] = '"') then begin
            if (stat = -1) then begin
              valuename := '';
              stat := 1;
            end else if (stat = 1) then begin
              stat := 2;
            end else if (stat = 3) then begin
              valstr := '';
              stat := 4;
            end;
          end else if (buffer[idx] = '=') then begin
            stat := 3;
            typstr := '';
          end else if (buffer[idx] = ':') then begin
            valstr := '';
            stat := 4;
          end else if (buffer[idx] = #13) then begin
            {$ifdef Debug}
              if (valuename = 'BisBackTrackDate') then
                sidx := 1;
            {$endif}

            if (subkey <> 0) and (Length (valuename) > 0) then begin
              if (Length (typstr) = 0) then begin
                StrPCopy (value, Copy (valstr, 1, High (value) - 1));

                dwres := RegSetValueEx (subkey, PChar (valuename), 0, REG_SZ, @value, Length (valstr) + 1);
              end else if (typstr = 'dword') then begin
                if (TryStrToInt ('$'+valstr, intwert)) then begin
                  dwvalue := DWORD (intwert);

                  dwres := RegSetValueEx (subkey, PChar (valuename), 0, REG_DWORD, @dwvalue, Sizeof (DWORD));
                end;
              end else if (typstr = 'qword') then begin
                if (TryStrToInt64 ('$'+valstr, longwert)) then begin
                  dwres := RegSetValueEx (subkey, PChar (valuename), 0, REG_QWORD, @longwert, Sizeof (Int64));
                end;
              end else if (Copy (typstr,1,3) = 'hex') then begin
                sidx  := 1;
                vsize := 0;

                while (sidx <= Length (valstr)) do begin
                  if (TryStrToInt ('$'+Copy (valstr, sidx, 2), intwert)) then begin
                    bytevalue [vsize] := Byte (intwert and $00ff);
                    Inc (vsize);
                  end;

                  Inc (sidx, 2);
                  while (sidx <= Length (valstr)) and not (valstr [sidx] in ['0'..'9', 'a'..'f', 'A'..'F']) do Inc (sidx);
                end;

                if (typstr = 'hex') then
                  dwres := RegSetValueEx (subkey, PChar (valuename), 0, REG_BINARY, @bytevalue, vsize)
                else if (typstr = 'hex(2)') then
                  dwres := RegSetValueEx (subkey, PChar (valuename), 0, REG_EXPAND_SZ, @bytevalue, vsize)
                else if (typstr = 'hex(7)') then
                  dwres := RegSetValueEx (subkey, PChar (valuename), 0, REG_MULTI_SZ, @bytevalue, vsize);
              end;
            end;

            stat := -1;
          end else begin
            if (stat = 0) then
              keyname := keyname + buffer[idx]
            else if (stat = 1) then
              valuename := valuename + buffer[idx]
            else if (stat = 3) then
              typstr := typstr + buffer[idx]
            else if (stat = 4) then begin
              if (buffer[idx] = '\') then
                Inc (idx);

              valstr := valstr + buffer[idx]
            end;
          end;

          Inc (idx);
        end;

        RegCloseKey (subkey);

        if (dwres <> 0) then
          res := dwres;
      finally
      end;
    finally
      fstream.Free;
    end;
  end;

  Result := res;
end;

end.
