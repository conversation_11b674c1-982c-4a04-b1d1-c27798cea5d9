object PrinterForm: TPrinterForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'PrinterForm'
  ClientHeight = 520
  ClientWidth = 596
  Color = clBtnFace
  Constraints.MinHeight = 400
  Constraints.MinWidth = 500
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnKeyDown = FormKeyDown
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Splitter1: TSplitter
    Left = 0
    Top = 281
    Width = 596
    Height = 3
    Cursor = crVSplit
    Align = alTop
    ExplicitLeft = -8
    ExplicitTop = 280
    ExplicitWidth = 570
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 596
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    ExplicitWidth = 570
    DesignSize = (
      596
      41)
    object Label1: TLabel
      Left = 11
      Top = 14
      Width = 15
      Height = 13
      Caption = 'Art'
    end
    object Bevel2: TBevel
      Left = 8
      Top = 38
      Width = 580
      Height = 1
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 554
    end
    object ArtComboBox: TComboBox
      Left = 32
      Top = 11
      Width = 145
      Height = 21
      Style = csDropDownList
      ItemHeight = 13
      ItemIndex = 1
      TabOrder = 0
      Text = 'Labeldrucker'
      OnChange = ArtComboBoxChange
      Items.Strings = (
        'Alle'
        'Labeldrucker'
        'Laserdrucker')
    end
  end
  object PrinterPanel: TPanel
    Left = 0
    Top = 41
    Width = 596
    Height = 240
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      596
      240)
    object Label2: TLabel
      Left = 8
      Top = 8
      Width = 37
      Height = 13
      Caption = 'Drucker'
    end
    object PrinterDBGrid: TDBGridPro
      Left = 8
      Top = 28
      Width = 481
      Height = 206
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = PrinterDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      PopupMenu = PrinterDBGridPopupMenu
      ReadOnly = True
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      OnDrawColumnCell = JobDBGridDrawColumnCell
      OnDblClick = PrinterDBGridDblClick
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'Tahoma'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
    object OnlineButton: TButton
      Left = 496
      Top = 176
      Width = 92
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Online'
      TabOrder = 4
      OnClick = OnlineButtonClick
    end
    object OfflineButton: TButton
      Left = 497
      Top = 207
      Width = 92
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Offline'
      TabOrder = 5
      OnClick = OfflineButtonClick
    end
    object PrtNewButton: TButton
      Left = 496
      Top = 27
      Width = 92
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Neu...'
      TabOrder = 1
      OnClick = PrtChangeButtonClick
    end
    object PrtEditButton: TButton
      Left = 497
      Top = 58
      Width = 92
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Bearbeiten...'
      TabOrder = 2
      OnClick = PrtChangeButtonClick
    end
    object PrtDelButton: TButton
      Left = 496
      Top = 89
      Width = 92
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'L'#246'schen'
      TabOrder = 3
      OnClick = PrtDelButtonClick
    end
  end
  object PrintJobsPanel: TPanel
    Left = 0
    Top = 284
    Width = 596
    Height = 236
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      596
      236)
    object Bevel1: TBevel
      Left = 8
      Top = 195
      Width = 580
      Height = 1
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 270
      ExplicitWidth = 554
    end
    object Label3: TLabel
      Left = 8
      Top = 7
      Width = 69
      Height = 13
      Caption = 'Druckauftr'#228'ge'
    end
    object JobDBGrid: TDBGridPro
      Left = 8
      Top = 26
      Width = 481
      Height = 156
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = JobDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      ReadOnly = True
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      OnDrawColumnCell = JobDBGridDrawColumnCell
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'Tahoma'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
    object ReprintButton: TButton
      Left = 496
      Top = 26
      Width = 92
      Height = 25
      Anchors = [akTop, akRight]
      Caption = '&Nachdruck'
      TabOrder = 1
      OnClick = ReprintButtonClick
    end
    object DelJobButton: TButton
      Left = 496
      Top = 57
      Width = 92
      Height = 25
      Anchors = [akTop, akRight]
      Caption = '&L'#246'schen'
      TabOrder = 2
      OnClick = DelJobButtonClick
    end
    object CloseButton: TButton
      Left = 513
      Top = 203
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 3
      ExplicitLeft = 487
      ExplicitTop = 243
    end
  end
  object PrinterDataSource: TDataSource
    DataSet = PrinterADOQuery
    OnDataChange = PrinterDataSourceDataChange
    Left = 104
    Top = 72
  end
  object PrinterADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 136
    Top = 72
  end
  object JobDataSource: TDataSource
    DataSet = JobADOQuery
    OnDataChange = JobDataSourceDataChange
    Left = 96
    Top = 224
  end
  object JobADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 128
    Top = 224
  end
  object PrinterDBGridPopupMenu: TPopupMenu
    OnPopup = PrinterDBGridPopupMenuPopup
    Left = 184
    Top = 144
    object PrintPrintLabelMenuItem: TMenuItem
      Caption = 'Druckerkennung ausdrucken...'
      OnClick = PrintPrintLabelMenuItemClick
    end
    object PrintInitLabelMenuItem: TMenuItem
      Caption = 'Labeldrucker initialisieren...'
      OnClick = PrintInitLabelMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object PrinterPropertyMenuItem: TMenuItem
      Caption = 'Eigenschaften...'
      OnClick = PrinterPropertyMenuItemClick
    end
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 360
    Top = 16
  end
end
