unit EditAufWorkloadDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, DB, MemDS, DBAccess,
  Ora, OraSmart, StdCtrls, Menus, ACOList;

type
  TEditAufWorkloadsForm = class(TForm)
    AufWorkloadDBGrid: TDBGridPro;
    TopPanel: TPanel;
    BottomPanel: TPanel;
    DataPanel: TPanel;
    AufWorkloadQuery: TSmartQuery;
    AufWorkloadDataSource: TDataSource;
    CloseButton: TButton;
    Label1: TLabel;
    AuftragLabel: TLabel;
    GroupBox1: TGroupBox;
    WorkloadDBGrid: TDBGridPro;
    AddWorkloadButton: TButton;
    WorkloadDataSource: TDataSource;
    WorkloadQuery: TSmartQuery;
    Label3: TLabel;
    Bevel1: TBevel;
    AudWorkloadDBGridPopupMenu: TPopupMenu;
    EditAufWorkloadMenuItem: TMenuItem;
    N1: TMenuItem;
    DelAufWorkloadMenuItem: TMenuItem;
    ACOListForm1: TACOListForm;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure EditAufWorkloadMenuItemClick(Sender: TObject);
    procedure AudWorkloadDBGridPopupMenuPopup(Sender: TObject);
    procedure AddWorkloadButtonClick(Sender: TObject);
    procedure AufWorkloadDBGridDblClick(Sender: TObject);
    procedure FormActivate(Sender: TObject);
  private
    fRefAuf,
    fRefAufPos    : Integer;
    fRefMand,
    fRefSubMand,
    fRefLager     : Integer;
    fArt          : String;
  public
    function PrepareAuftrag      (const RefAuf,  RefAufPos : Integer) : Integer;
    function PrepareBestellung   (const RefBest, RefBestPos : Integer) : Integer;
    function PrepareWarenannahme (const RefWE,   RefWEPos : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses
  DatenModul, DBGridUtilModule, ConfigModul, FrontendACOModul, LVSDatenInterface, FrontendUtils,
  SprachModul, EditWorkloadDLG;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.09.2020
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditAufWorkloadsForm.AddWorkloadButtonClick(Sender: TObject);
var
  editfrom : TEditWorkloadForm;
begin
  if Assigned (WorkloadDBGrid.DataSource.DataSet) and (WorkloadDBGrid.DataSource.DataSet.Active) and (WorkloadDBGrid.DataSource.DataSet.RecNo > 0) then begin
    editfrom := TEditWorkloadForm.Create(Self);

    try
      if (fRefAuf > 0) then begin
        editfrom.PrepareOrderWorkload (WorkloadQuery.FieldByName('REF').AsInteger, fRefAuf, fRefAufPos);
      end;

      if (editfrom.ShowModal = mrOk) Then begin
        AufWorkloadDBGrid.Reload (editfrom.RefWorkload);
      end;
    finally
      editfrom.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.09.2020
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditAufWorkloadsForm.AudWorkloadDBGridPopupMenuPopup(Sender: TObject);
begin
  EditAufWorkloadMenuItem.Enabled := False;
  DelAufWorkloadMenuItem.Enabled := False;

  if Assigned (AufWorkloadDBGrid.DataSource.DataSet) and (AufWorkloadDBGrid.DataSource.DataSet.Active) and (AufWorkloadDBGrid.DataSource.DataSet.RecNo > 0) then begin
    EditAufWorkloadMenuItem.Enabled := True;
    DelAufWorkloadMenuItem.Enabled  := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 07.09.2020
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditAufWorkloadsForm.AufWorkloadDBGridDblClick(Sender: TObject);
begin
  if Assigned (AufWorkloadDBGrid.DataSource.DataSet) and (AufWorkloadDBGrid.DataSource.DataSet.Active) and (AufWorkloadDBGrid.DataSource.DataSet.RecNo > 0) then begin
    if (EditAufWorkloadMenuItem.Visible) then
      EditAufWorkloadMenuItemClick (Sender);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.09.2020
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditAufWorkloadsForm.EditAufWorkloadMenuItemClick(Sender: TObject);
var
  editfrom : TEditWorkloadForm;
begin
  if Assigned (AufWorkloadDBGrid.DataSource.DataSet) and (AufWorkloadDBGrid.DataSource.DataSet.Active) and (AufWorkloadDBGrid.DataSource.DataSet.RecNo > 0) then begin
    editfrom := TEditWorkloadForm.Create(Self);

    try
      if (fRefAuf > 0) then begin
        editfrom.PrepareOrder (AufWorkloadQuery.FieldByName('REF').AsInteger);
      end;

      if (editfrom.ShowModal = mrOk) Then begin
        AufWorkloadDBGrid.Reload (editfrom.RefWorkload);
      end;
    finally
      editfrom.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.09.2024
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditAufWorkloadsForm.FormActivate(Sender: TObject);
begin
  if Assigned (FrontendACOModule) then
    FrontendACOModule.SetBerechtigungen (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.09.2020
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditAufWorkloadsForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  WorkloadQuery.Close;
  AufWorkloadQuery.Close;

  if Assigned (DBGridUtils) Then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.09.2020
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditAufWorkloadsForm.FormCreate(Sender: TObject);
begin
  fRefAuf    := -1;
  fRefAufPos := -1;

  AufWorkloadQuery.Session := LVSDatenModul.OraMainSession;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AuftragLabel);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.09.2020
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditAufWorkloadsForm.FormShow(Sender: TObject);
begin
  LVSConfigModul.RestoreFormInfo (Self);

  AufWorkloadQuery.Open;

  WorkloadQuery.Open;

  WorkloadDBGrid.SetColumnVisible ('AUTO_ADD', false);
  WorkloadDBGrid.SetColumnVisible ('USE_FOR_INVOICE', false);
  WorkloadDBGrid.SetColumnVisible ('USE_FOR_DELIVERY', false);
  WorkloadDBGrid.SetColumnVisible ('USE_FOR_BILLING', false);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.09.2020
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function TEditAufWorkloadsForm.PrepareAuftrag (const RefAuf, RefAufPos : Integer) : Integer;
var
  query : TSmartQuery;
begin
  fRefAuf    := RefAuf;
  fRefAufPos := RefAufPos;

  query := TSmartQuery.Create (Self);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Add ('select * from V_AUFTRAG where REF=:ref');
    query.Params [0].Value := fRefAuf;

    query.Open;

    fRefMand      := query.FieldByName('REF_MAND').AsInteger;
    fRefSubMand   := DBGetReferenz (query.FieldByName('REF_SUB_MAND'));
    fRefLager     := query.FieldByName('REF_LAGER').AsInteger;
    fArt          := query.FieldByName('AUFTRAGSART').AsString;

    AuftragLabel.Caption := query.FieldByName('AUFTRAG_NR').AsString;
    AuftragLabel.Caption := AuftragLabel.Caption + ' / ' + query.FieldByName('KUNDEN_NAME').AsString;

    query.Close;
  finally
    query.Free;
  end;

  if (RefAufPos > 0) then begin
    AufWorkloadQuery.SQL.Add('select * from V_PCD_AUFTRAG_WORKLOAD where REF_AUF_KOPF=:ref and (REF_AUF_POS is null or REF_AUF_POS=:ref_pos)');
    AufWorkloadQuery.ParamByName ('ref_pos').Value := fRefAufPos;
    AufWorkloadQuery.ParamByName ('ref').Value := fRefAuf;
  end else begin
    AufWorkloadQuery.SQL.Add('select * from V_PCD_AUFTRAG_WORKLOAD where REF_AUF_KOPF=:ref');
    AufWorkloadQuery.Params [0].Value := fRefAuf;
  end;

  if (fRefSubMand > 0) then begin
    WorkloadQuery.SQL.Add ('select * from V_PCD_WORKLOAD_TEMPLATE where STATUS=''AKT'' and (REF_MAND is null or REF_MAND=:ref_mand) and (REF_SUB_MAND is null or REF_SUB_MAND=:ref_sub_mand)');
    WorkloadQuery.Params.ParamByName ('ref_sub_mand').Value := fRefSubMand;
  end else begin
    WorkloadQuery.SQL.Add ('select * from V_PCD_WORKLOAD_TEMPLATE where REF_SUB_MAND is null and (REF_MAND is null or REF_MAND=:ref_mand)');
  end;
  WorkloadQuery.Params.ParamByName ('ref_mand').Value := fRefMand;

  WorkloadQuery.SQL.Add ('and (REF_LAGER is null or REF_LAGER=:ref_lager) and (PROCESS_TYPE is null or PROCESS_TYPE=:art)');
  WorkloadQuery.Params.ParamByName ('ref_lager').Value := fRefLager;
  WorkloadQuery.Params.ParamByName ('art').Value := fArt;

  if (fRefAufPos > 0) then
    WorkloadQuery.SQL.Add ('and PROCESS_STEP=''AUF_POS''')
  else
    WorkloadQuery.SQL.Add ('and PROCESS_STEP=''AUF''');

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.09.2020
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function TEditAufWorkloadsForm.PrepareBestellung   (const RefBest, RefBestPos : Integer) : Integer;
begin
  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.09.2020
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function TEditAufWorkloadsForm.PrepareWarenannahme (const RefWE,   RefWEPos : Integer) : Integer;
begin
  Result := 0;
end;

end.
