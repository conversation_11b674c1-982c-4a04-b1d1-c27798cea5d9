unit DoPrintDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls, PrinterUtils;

type
  TDoPrintForm = class;

  TPrintFunction = function (Sender : TDoPrintForm) : Integer of object;

  TDoPrintForm = class(TForm)
    Panel2: TPanel;
    Label8: TLabel;
    PrinterComboBox: TComboBoxPro;
    PrintButton: TButton;
    CloseButton: TButton;
    procedure PrintButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
  private
    fData          : TObject;

    fPrintFunction : TPrintFunction;
    fPortArray     : array [0..255] of TPrinterPorts;

    function GetPrinterPort : TPrinterPorts;
  public
    property PrinterPort : TPrinterPorts read GetPrinterPort;
    property Data        : TObject read fData write fData;

    procedure Prepare (const RefLager : Integer; const DefPrinterName, Format : String; const PrintFunction : TPrintFunction);
  end;

implementation

{$R *.dfm}

uses
  PrintModul, SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDoPrintForm.FormCreate(Sender: TObject);
begin
  fData := Nil;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, PrinterComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDoPrintForm.FormDestroy(Sender: TObject);
begin
  if Assigned (fData) then
    fData.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TDoPrintForm.GetPrinterPort : TPrinterPorts;
begin
  Result := fPortArray [PrinterComboBox.ItemIndex];
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDoPrintForm.Prepare (const RefLager : Integer; const DefPrinterName, Format : String; const PrintFunction : TPrintFunction);
begin
  fPrintFunction := PrintFunction;

  PrintModule.LoadPrinterCombobox (RefLager, Format, PrinterComboBox, fPortArray, DefPrinterName, True, (RefLager <> -1));

  (*
  if (PrinterComboBox.Items.Count = 0) Then begin
    PrinterComboBox.ItemIndex := -1;
    PrinterComboBox.Text := '';
  end else begin
    PrinterComboBox.ItemIndex := PrinterComboBox.IndexOf (DefPrinterName, 0, True);
    if (PrinterComboBox.ItemIndex = -1) then
      PrinterComboBox.ItemIndex := 0;
  end;
  *)
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDoPrintForm.PrintButtonClick (Sender: TObject);
var
  res     : Integer;
  fname,
  errtext : String;
begin
  with fPortArray [PrinterComboBox.ItemIndex] do
    res := OpenPrinterPort (Port, Model, FileOutput, User, Passwd, fname, errtext);

  if (res <> 0) then
    MessageDlg(FormatMessageText (1360, [errtext]), mtError, [mbOK], 0)
  else begin
    res := BeginPrinting ('VPE-Label',errtext);

    if (res <> 0) then
      MessageDlg(FormatMessageText (1360, [errtext]), mtError, [mbOK], 0)
    else begin
      res := fPrintFunction (Self);

      EndPrinting;
    end;

    ClosePrinterPort;
  end;
end;

end.
