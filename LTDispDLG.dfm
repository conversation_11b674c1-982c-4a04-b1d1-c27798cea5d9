object LTDisplayForm: TLTDisplayForm
  Left = 336
  Top = 268
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = #220'bersicht Ladungstr'#228'ger'
  ClientHeight = 461
  ClientWidth = 787
  Color = clBtnFace
  Constraints.MinHeight = 439
  Constraints.MinWidth = 780
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnKeyDown = FormKeyDown
  OnResize = FormResize
  OnShow = FormShow
  DesignSize = (
    787
    461)
  TextHeight = 13
  object Bevel1: TBevel
    Left = 8
    Top = 422
    Width = 771
    Height = 10
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 94
    Height = 13
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Caption = 'Ladungstr'#228'gertypen'
  end
  object LTDBGrid: TDBGridPro
    Left = 8
    Top = 23
    Width = 684
    Height = 182
    Hint = 
      'Order with the left mouse button, searching with the right mouse' +
      ' button'
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = LTDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = LTDBGridPopupMenu
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDblClick = LTDBGridDblClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 705
    Top = 430
    Width = 73
    Height = 25
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 7
  end
  object NeuLTButton: TButton
    Left = 698
    Top = 23
    Width = 83
    Height = 24
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akTop, akRight]
    Caption = '&Neu...'
    TabOrder = 1
    OnClick = NeuLTButtonClick
  end
  object DelLTButton: TButton
    Left = 698
    Top = 180
    Width = 83
    Height = 25
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akRight, akBottom]
    Caption = '&L'#246'schen'
    TabOrder = 4
    OnClick = DelLTButtonClick
  end
  object EinsatzGroupBox: TGroupBox
    Left = 8
    Top = 213
    Width = 337
    Height = 203
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akLeft, akBottom]
    Caption = 'Einsatzm'#246'glichkeiten'
    TabOrder = 5
    DesignSize = (
      337
      203)
    object EinsatzCommitButton: TButton
      Left = 168
      Top = 168
      Width = 73
      Height = 23
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      TabOrder = 0
      OnClick = EinsatzCommitButtonClick
    end
    object EinsatzRollbackButton: TButton
      Left = 254
      Top = 168
      Width = 73
      Height = 23
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akRight, akBottom]
      Caption = '&Verwerfen'
      TabOrder = 1
      OnClick = EinsatzRollbackButtonClick
    end
    object EinsatzCheckListBox: TTabCheckListBox
      Left = 8
      Top = 23
      Width = 318
      Height = 140
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 20
      Style = lbOwnerDrawFixed
      TabOrder = 2
      OnClick = EinsatzCheckListBoxClick
      OnDrawItem = ListBoxDrawItem
    end
  end
  object TypGroupBox: TGroupBox
    Left = 360
    Top = 211
    Width = 326
    Height = 203
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akLeft, akBottom]
    Caption = 'Zul'#228'ssige LP-Typen f'#252'r den Ladungstr'#228'ger'
    TabOrder = 6
    DesignSize = (
      326
      203)
    object ZuordnungRollbackButton: TButton
      Left = 241
      Top = 170
      Width = 74
      Height = 23
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akRight, akBottom]
      Caption = '&Verwerfen'
      TabOrder = 0
      OnClick = ZuordnungRollbackButtonClick
    end
    object ZuordnungCommitButton: TButton
      Left = 155
      Top = 170
      Width = 74
      Height = 23
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      TabOrder = 1
      OnClick = ZuordnungCommitButtonClick
    end
    object ZuordnungCheckListBox: TTabCheckListBox
      Left = 7
      Top = 23
      Width = 308
      Height = 142
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 20
      Style = lbOwnerDrawFixed
      TabOrder = 2
      TabWidth = 1
      OnClick = ZuordnungCheckListBoxClick
      OnDrawItem = ListBoxDrawItem
    end
  end
  object EditLTButton: TButton
    Left = 696
    Top = 92
    Width = 83
    Height = 24
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akTop, akRight]
    Caption = 'Bearbeiten...'
    TabOrder = 3
    OnClick = EditLTButtonClick
  end
  object CopyLTButton: TButton
    Left = 696
    Top = 53
    Width = 83
    Height = 24
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akTop, akRight]
    Caption = 'Kopieren...'
    TabOrder = 2
    OnClick = EditLTButtonClick
  end
  object LTQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 448
    Top = 24
  end
  object LTDataSource: TDataSource
    DataSet = LTQuery
    OnDataChange = LTDataSourceDataChange
    Left = 416
    Top = 24
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 488
    Top = 24
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 852
    Top = 84
  end
  object LTDBGridPopupMenu: TPopupMenu
    OnPopup = LTDBGridPopupMenuPopup
    Left = 224
    Top = 96
    object StdLTMenuItem: TMenuItem
      Caption = 'Als Standard-LT im Lager definieren'
      OnClick = StdLTMenuItemClick
    end
    object StdLTEinlagerMenuItem: TMenuItem
      Caption = 'Als Standard-LT f'#252'r die Einlagerung definieren'
      OnClick = StdLTEinlagerMenuItemClick
    end
    object StdLTKommMenuItem: TMenuItem
      Caption = 'Als Standard-LT f'#252'r die Kommissionierung definieren'
      OnClick = StdLTKommMenuItemClick
    end
    object StdLTNVEMenuItem: TMenuItem
      Caption = 'Als Standard-LT f'#252'r NVEs definieren'
      OnClick = StdLTNVEMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object EditRangeMenuItem: TMenuItem
      Caption = 'Allgemeiner Nummerkreis bearbeiten...'
      OnClick = EditLTRangeMenuItemClick
    end
    object CreateLTRangeMenuItem: TMenuItem
      Caption = 'Nummerkreis f'#252'r das LT anlegen...'
      OnClick = EditLTRangeMenuItemClick
    end
    object EditLTRangeMenuItem: TMenuItem
      Caption = 'Nummerkreis des LTs bearbeiten...'
      OnClick = EditLTRangeMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object Etikettendrucken1: TMenuItem
      Caption = 'Etiketten drucken...'
      OnClick = Etikettendrucken1Click
    end
    object TMenuItem
      Caption = '-'
    end
  end
end
