﻿unit SpedSendConfigDLG;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Data.DB, Vcl.ExtCtrls, DBAccess, Ora, MemDS, Vcl.Grids, Vcl.DBGrids, SMDBGrid, DBGridPro,
  Vcl.ComCtrls, Vcl.StdCtrls;

type
  TSpedSendConfigForm = class(TForm)
    SpedSendConfigDBGrid: TDBGridPro;
    SpedSendConfigQuery: TOraQuery;
    SpedSendConfigDataSource: TOraDataSource;
    TopPanel: TPanel;
    BattomPanel: TPanel;
    CloseButton: TButton;
    PageControl1: TPageControl;
    TabSheet1: TTabSheet;
    procedure FormActivate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    fRefSped    : Integer;
    fRefSubMand : Integer;
  public
    function Prepare (const RefSped, RefSubMand : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses
  OraSmart, DatenModul, DBGridUtilModule, ConfigModul, SprachModul;

procedure TSpedSendConfigForm.FormActivate(Sender: TObject);
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.RestoreFormInfo (Self);
end;

procedure TSpedSendConfigForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  SpedSendConfigQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

procedure TSpedSendConfigForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, FehlerStatusLabel);
  {$endif}
end;

procedure TSpedSendConfigForm.FormShow(Sender: TObject);
begin
  SpedSendConfigQuery.Open;
end;

function TSpedSendConfigForm.Prepare (const RefSped, RefSubMand : Integer) : Integer;
var
  query : TSmartQuery;
begin
  fRefSped    := RefSped;
  fRefSubMand := RefSubMand;

  query := LVSDatenModul.CreateSmartQuery (Self, 'Prepare');

  try
  finally
    query.Free;
  end;

  SpedSendConfigQuery.SQL.Add ('select * from V_PCD_SPED_VERSAND_CONFIG where REF_SPED=:ref_sped');
  SpedSendConfigQuery.ParamByName ('ref_sped').Value := fRefSped;

  if (RefSubMand > 0) then begin
    SpedSendConfigQuery.SQL.Add ('and REF_SUB_MAND=:ref_sub_mand');
    SpedSendConfigQuery.ParamByName ('ref_sub_mand').Value := fRefSped;
  end;
end;

end.
