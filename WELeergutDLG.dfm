object WELeergutForm: TWELeergutForm
  Left = 350
  Top = 183
  BorderIcons = [biSystemMenu]
  Caption = 'WELeergutForm'
  ClientHeight = 288
  ClientWidth = 788
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poMainFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnHide = FormHide
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  DesignSize = (
    788
    288)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 16
    Width = 96
    Height = 13
    Caption = 'Lademittel-Konto'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object LeerKontoLabel: TLabel
    Left = 128
    Top = 16
    Width = 75
    Height = 13
    Caption = 'LeerKontoLabel'
  end
  object LeerBewDBGrid: TDBGridPro
    Left = 8
    Top = 32
    Width = 771
    Height = 217
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = DataSource1
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDblClick = KorrButtonClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object Button1: TButton
    Left = 683
    Top = 255
    Width = 95
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    ModalResult = 1
    TabOrder = 1
  end
  object LeerGutButton: TButton
    Left = 8
    Top = 255
    Width = 95
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Hinzuf'#252'gen...'
    TabOrder = 2
    OnClick = LeerGutButtonClick
  end
  object KorrButton: TButton
    Left = 112
    Top = 255
    Width = 95
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Korrektur...'
    TabOrder = 3
    OnClick = KorrButtonClick
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 688
    Top = 56
  end
  object DataSource1: TDataSource
    DataSet = ADOQuery1
    OnDataChange = DataSource1DataChange
    Left = 592
    Top = 56
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 640
    Top = 56
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 736
    Top = 56
  end
end
