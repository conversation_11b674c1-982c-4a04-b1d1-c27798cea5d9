unit QRCodeDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls;

type
  TQRCodeForm = class(TForm)
    QRPaintBox: TPaintBox;
    CloseButton: TButton;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure QRPaintBoxPaint(Sender: TObject);
  private
    fContend      : String;
    fQRCodeBitmap : TBitmap;

    procedure Update;
  public
    property Contend : String read fContend write fContend;
  end;

implementation

{$R *.dfm}

uses
  DelphiZXingQRCode;

procedure TQRCodeForm.FormCreate(Sender: TObject);
begin
  fQRCodeBitmap := TBitmap.Create;
end;

procedure TQRCodeForm.FormDestroy(Sender: TObject);
begin
  fQRCodeBitmap.Free;
end;

procedure TQRCodeForm.FormShow(Sender: TObject);
begin
  Update;
end;

procedure TQRCodeForm.QRPaintBoxPaint(Sender: TObject);
var
  Scale: Double;
begin
  QRPaintBox.Canvas.Brush.Color := clWhite;
  QRPaintBox.Canvas.FillRect(Rect(0, 0, QRPaintBox.Width, QRPaintBox.Height));
  if ((fQRCodeBitmap.Width > 0) and (fQRCodeBitmap.Height > 0)) then
  begin
    if (QRPaintBox.Width < QRPaintBox.Height) then
    begin
      Scale := QRPaintBox.Width / fQRCodeBitmap.Width;
    end else
    begin
      Scale := QRPaintBox.Height / fQRCodeBitmap.Height;
    end;
    QRPaintBox.Canvas.StretchDraw(Rect(0, 0, Trunc(Scale * fQRCodeBitmap.Width), Trunc(Scale * fQRCodeBitmap.Height)), fQRCodeBitmap);
  end;
end;

procedure TQRCodeForm.Update;
var
  QRCode: TDelphiZXingQRCode;
  Row, Column: Integer;
begin
  QRCode := TDelphiZXingQRCode.Create;
  try
    QRCode.Data := fContend;
    //QRCode.Encoding := TQRCodeEncoding(cmbEncoding.ItemIndex);
    QRCode.QuietZone := 4;
    fQRCodeBitmap.SetSize(QRCode.Rows, QRCode.Columns);
    for Row := 0 to QRCode.Rows - 1 do
    begin
      for Column := 0 to QRCode.Columns - 1 do
      begin
        if (QRCode.IsBlack[Row, Column]) then
        begin
          fQRCodeBitmap.Canvas.Pixels[Column, Row] := clBlack;
        end else
        begin
          fQRCodeBitmap.Canvas.Pixels[Column, Row] := clWhite;
        end;
      end;
    end;
  finally
    QRCode.Free;
  end;

  QRPaintBox.Repaint;
end;

end.
