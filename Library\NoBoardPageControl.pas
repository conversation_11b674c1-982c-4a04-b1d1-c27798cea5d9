unit NoBoardPageControl;

interface

uses
  Classes, Windows, Messages, ComCtrls, CommCtrl;

type
  TNoBoarderPageControl = class(TPageControl)
  private
    fSinglePage : Boolean;

    procedure TCMAdjustRect(var Msg: TMessage); message TCM_ADJUSTRECT;
  public
    property SinglePage : <PERSON><PERSON><PERSON> read fSinglePage write fSinglePage;
  end;

procedure Register;

implementation

procedure Register;
begin
  RegisterComponents ('c+s', [TNoBoarderPageControl]);
end;

procedure TNoBoarderPageControl.TCMAdjustRect(var Msg: TMessage);
begin
  inherited;

  if (fSinglePage) then begin
    if Msg.WParam = 0 then
      InflateRect(PRect(Msg.LParam)^, 4, 4)
    else
      InflateRect(PRect(Msg.LParam)^, -4, -4);
  end;
end;

end.
