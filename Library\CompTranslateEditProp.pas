unit CompTranslateEditProp;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls,
  Menus, DesignIntf, DesignEditors;

type
  TSprachEditor = class(TComponentEditor)
  public
    { Public declarations }
    procedure Edit; override;
    procedure ExecuteVerb(Index: Integer); override;
    function GetVerb(Index: Integer): string; override;
    function GetVerbCount: Integer; override;
    procedure PrepareItem(Index: Integer; const AItem: TMenuItem); reintroduce; overload;
  end;

  TLangDefFileEditor = class (TStringProperty)
  public
    procedure Edit; override;
    function GetAttributes: TPropertyAttributes; override;
  end;


procedure Register;

function GetProjectDir : String;

implementation

uses
{$ifdef TRACE}
  Trace,
{$endif}
  Dialogs,
  ToolsAPI,
  CompTranslate, CompTranslateEdit, Forms;

procedure Register;
begin
  RegisterPropertyEditor  (TypeInfo (String), TCompTranslate, 'LanguageFile', TLangDefFileEditor);
  RegisterComponentEditor (TCompTranslate, TSprachEditor);
  RegisterComponentEditor (TCompTranslateForm, TSprachEditor);
end;

function GetProjectDir : String;
var
  idx : Integer;
  path  : String;
  AModuleServices: IOTAModuleServices;
  AModule: IOTAModule;
  AProjectGroup: IOTAProjectGroup;
begin
  path := '';

  AModuleServices := (BorlandIDEServices as IOTAModuleServices);

  idx := 0;
  while (idx < AModuleServices.ModuleCount) do begin
    OutputDebugString(PChar ('idx: '+IntToStr(idx)));

    AModule := AModuleServices.Modules[idx];

    if (AModule.QueryInterface(IOTAProjectGroup, AProjectGroup) = S_OK) then begin
      path := ExtractFileDir (AProjectGroup.ActiveProject.FileName);

      break;
    end;

    Inc (idx);
  end;

  Result := path;
end;

procedure TLangDefFileEditor.Edit;
var
  MPFileOpen: TOpenDialog;
begin
  MPFileOpen := TOpenDialog.Create(Application);
  MPFileOpen.Filename := GetValue;
  MPFileOpen.Filter := 'XML (*.xml)|*.xml';
  MPFileOpen.Options := MPFileOpen.Options + [ofPathMustExist, ofFileMustExist];

  try
    if MPFileOpen.Execute then SetValue(MPFileOpen.Filename);
  finally
    MPFileOpen.Free;
  end;
end;

function TLangDefFileEditor.GetAttributes: TPropertyAttributes;
begin
  Result := [paDialog];
end;


procedure TSprachEditor.Edit;
var
  AModuleServices: IOTAModuleServices;
  AModule: IOTAModule;
  idx: integer;
  AProjectGroup: IOTAProjectGroup;
  editor : TCompTranslateEditForm;
begin
{$ifdef TRACE}
  ProcedureStart ('TSprachEditor.Edit');
{$endif}

  if Assigned (Component) then begin
    if (Component is TCompTranslate) then begin
      (Component as TCompTranslate).CheckFile;

      Application.CreateForm (TCompTranslateEditForm, editor);

      if Assigned (editor) then begin
        editor.TransOwner := Component as TCompTranslate;
        editor.OwnerForm := Nil;

        AModuleServices := (BorlandIDEServices as IOTAModuleServices);

        idx := 0;
        while (idx < AModuleServices.ModuleCount) do begin
          OutputDebugString(PChar ('idx: '+IntToStr(idx)));

          AModule := AModuleServices.Modules[idx];

          if (AModule.QueryInterface(IOTAProjectGroup, AProjectGroup) = S_OK) then begin
            editor.ProjektDir := ExtractFileDir (AProjectGroup.ActiveProject.FileName);

            OutputDebugString(PChar ('Filename: '+AProjectGroup.ActiveProject.FileName));
          end;

          Inc (idx);
        end;

        editor.DoEditor;

        editor.Release;
      end;

      if Assigned (Designer) then
        Designer.Modified;
    end else if (Component is TCompTranslateForm) then begin
      if not Assigned ((Component as TCompTranslateForm).Master) then
        raise eTransCompError.Create (1,'Keine Master-Komponente definiert');

      (Component as TCompTranslateForm).Master.CheckFile;

      Application.CreateForm (TCompTranslateEditForm, editor);

      if Assigned (editor) then begin
        editor.TransOwner := (Component as TCompTranslateForm).Master;

        if Assigned ((Component as TCompTranslateForm).OwnerForm) Then
          editor.OwnerForm := (Component as TCompTranslateForm).OwnerForm
        else if Assigned ((Component as TCompTranslateForm).OwnerModule) Then
          editor.OwnerForm := (Component as TCompTranslateForm).OwnerModule
        else
          editor.OwnerForm := Nil;

        editor.ProjektDir := ExtractFileDir ((BorlandIDEServices as IOTAModuleServices).CurrentModule.FileName);

        editor.DoEditor;

        editor.Release;
      end;

      if Assigned (Designer) then begin
        Designer.Modified;

        Designer.SelectComponent ((Component as TCompTranslateForm).Master);
        Designer.Modified;
      end;
    end;
  end;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

procedure TSprachEditor.ExecuteVerb(Index: Integer);
begin
{$ifdef TRACE}
  ProcedureStart ('TSprachEditor.Edit');
{$endif}

  case Index of
    0: Edit;
  end;

  if Assigned (Designer) then
    Designer.Modified;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

function TSprachEditor.GetVerb(Index: Integer): string;
begin
{$ifdef TRACE}
  ProcedureStart ('TSprachEditor.Edit');
{$endif}

  case Index of
    0 : Result := 'Edit';
  end;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

function TSprachEditor.GetVerbCount: Integer;
begin
{$ifdef TRACE}
  ProcedureStart ('TSprachEditor.Edit');
{$endif}

  Result := inherited GetVerbCount + 1;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

procedure TSprachEditor.PrepareItem(Index: Integer; const AItem: TMenuItem);
begin
{$ifdef TRACE}
  ProcedureStart ('TSprachEditor.Edit');
{$endif}

  case Index of
    0: AItem.Enabled := True;
  end;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

end.
