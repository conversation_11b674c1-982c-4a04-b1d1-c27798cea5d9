﻿//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : SprachModul
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/XE11/SprachModul.pas $
// $Revision: 23 $
// $Modtime: 15.09.23 13:21 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Ver<PERSON><PERSON> der sprachabhängigen Texte der Anwendung
//*****************************************************************************
unit SprachModul;

interface

uses
  Windows, Messages, SysUtils, Classes, CompTranslate, Contnrs, Forms;

type
  TSprachFormList = class (TComponentList)
    fTranslateList : TObjectList;
  protected
    procedure Notify (Ptr: Pointer; Action: TListNotification); override;
  public
    constructor Create; overload;
    destructor Destroy; override;
  end;

  TLVSSprachModul = class(TDataModule)
    LVSCompTranslate: TCompTranslate;

    procedure DataModuleCreate(Sender: TObject);
    procedure DataModuleDestroy(Sender: TObject);
    procedure LVSCompTranslateError(const ErrorLocation, ErrorInfo: string);
  private
    fAktSprache : String;
    fFormList : TSprachFormList;

    {$ifdef TranslateHelper}
      WindowHandle : hWnd;
      procedure WinProc (var WndMessage : TMessage);
    {$endif}

    procedure SetAktSprache (const Sprache : String);
    function  GetAktSprache : String;
  public
    property AktSprache : String read GetAktSprache write SetAktSprache;

    procedure InitForm         (OwenForm   : TForm;       LangChangeNotify : TNotifyEvent = Nil);
    procedure InitModule       (OwenModule : TDataModule; LangChangeNotify : TNotifyEvent = Nil);
    procedure InitFrame        (OwenFrame  : TFrame;      LangChangeNotify : TNotifyEvent = Nil);

    procedure SetConfigPath    (const Path : String);
    procedure SetNoTranslate   (OwenForm   : TForm; Component : TComponent); overload;
    procedure SetNoTranslate   (OwenFrame  : TFrame; Component : TComponent); overload;
    procedure SetTranslate     (OwenForm   : TForm; Component : TComponent);

    function  GetFormTranslate (OwenForm   : TForm) : TCompTranslateForm;

    function GetFixtext (const FixName : String; const ItemIndex : Integer = -1) : String;

    {$ifdef TranslateHelper}
      procedure ShowEditor (OwenForm : TForm);
    {$endif}
  end;

var
  LVSSprachModul: TLVSSprachModul;

implementation

{$R *.dfm}

uses
  {$ifdef Trace}
  Trace,
  {$endif}

  Win32Utils, ErrorTracking

  {$ifdef TranslateHelper}
    ,CompTranslateEdit
  {$endif}

  ,ConfigModul
  ,ResourceText;

type
  TTranslateListEntry = class (TObject)
  public
    SprachForm       : TForm;
    SprachModule     : TDataModule;
    SprachFrame      : TFrame;
    SprachTranslater : TCompTranslateForm;
    NoTranslateList  : TObjectList;
    TranslateList    : TObjectList;

    constructor Create;
    destructor Destroy; override;
  end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TTranslateListEntry.Create;
begin
  inherited Create;

  SprachForm   := Nil;
  SprachModule := Nil;

  SprachTranslater := Nil;

  TranslateList   := TObjectList.Create;
  NoTranslateList := TObjectList.Create;
end;
//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
destructor TTranslateListEntry.Destroy;
begin
  if Assigned (TranslateList) then begin
    while (TranslateList.Count > 0) do begin
      if Assigned (TranslateList [0]) then
        TranslateList [0].Free;

      TranslateList.Delete (0);
    end;

    TranslateList.Free;
  end;

  TranslateList := Nil;

  if Assigned (NoTranslateList) then begin
    while (NoTranslateList.Count > 0) do
      NoTranslateList.Extract (NoTranslateList [0]);

    NoTranslateList.Free;
  end;

  NoTranslateList := Nil;

  inherited Destroy;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TSprachFormList.Create;
begin
  inherited Create;

  fTranslateList := TObjectList.Create;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
destructor TSprachFormList.Destroy;
begin
  if Assigned (fTranslateList) then
    fTranslateList.Free;

  fTranslateList := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSprachFormList.Notify(Ptr: Pointer; Action: TListNotification);
var
  idx      : Integer;
  tranfrom : TTranslateListEntry;
begin
  if (Action = lnExtracted) then begin
    idx := 0;
    tranfrom := Nil;

    while (idx < fTranslateList.Count) do begin
      if (TTranslateListEntry (fTranslateList [idx]).SprachForm = TForm (Ptr)) then begin
        tranfrom := TTranslateListEntry (fTranslateList [idx]);

        break;
      end;

      Inc (idx);
    end;

    if Assigned (tranfrom) then begin
      fTranslateList.Extract (tranfrom);

      tranfrom.Free;
    end;
  end;

  inherited Notify (Ptr, Action);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSSprachModul.DataModuleCreate(Sender: TObject);
var
  lang : DWORD;
begin
  if Assigned (LVSConfigModul) and (Length (LVSConfigModul.GetConfigDir) > 0) then
    LVSCompTranslate.LanguageFile := LVSConfigModul.GetConfigDir + ExtractFileName (LVSCompTranslate.LanguageFile)
  else
    LVSCompTranslate.LanguageFile := ExtractFilePath (ParamStr (0)) + ExtractFileName (LVSCompTranslate.LanguageFile);

  fFormList := TSprachFormList.Create;

  {$ifdef TranslateHelper}
    WindowHandle := Classes.AllocateHWnd (WinProc);
  {$endif}

  lang := GetUserDefaultUILanguage;

  if (lang = $407) then
    LVSCompTranslate.Language := 0
  else
    LVSCompTranslate.Language := 1;
end;

//******************************************************************************
//* Function Name: DataModuleDestroy
//* Author       : Stefan Graf
//* Datum        : 14.04.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSSprachModul.DataModuleDestroy(Sender: TObject);
begin
  {$ifdef TranslateHelper}
    Classes.DeallocatehWnd (WindowHandle);
  {$endif}

  if Assigned (fFormList) then
    fFormList.Free;

  fFormList := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSSprachModul.GetAktSprache : String;
begin
  Result := LVSCompTranslate.LangTypes [LVSCompTranslate.Language];
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSSprachModul.SetAktSprache (const Sprache : String);
begin
  if (fAktSprache <> Sprache) then begin
    fAktSprache := Sprache;

    if (fAktSprache = 'DE') then
      LVSCompTranslate.Language := 0
    else if (fAktSprache = 'EN') then
      LVSCompTranslate.Language := 1
    else if (fAktSprache = 'DA') then
      LVSCompTranslate.Language := 2
    else if (fAktSprache = 'FR') then
      LVSCompTranslate.Language := 4
    else if (fAktSprache = 'PL') then
      LVSCompTranslate.Language := 3
    else if (fAktSprache = 'ES') then
      LVSCompTranslate.Language := 5
    else if (fAktSprache = 'CS') then
      LVSCompTranslate.Language := 6
    else if (fAktSprache = 'UK') then
      LVSCompTranslate.Language := 7;

    UpdateResourceText ('rsYes', 1167);
    UpdateResourceText ('rsNo', 1163);
    UpdateResourceText ('rsAlle', 1020);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSSprachModul.GetFixtext (const FixName : String; const ItemIndex : Integer) : String;
begin
  Result := LVSCompTranslate.GetFixtext (FixName, ItemIndex)
end;

{$ifdef TranslateHelper}
  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  procedure TLVSSprachModul.ShowEditor (OwenForm : TForm);
  var
    editor   : TCompTranslateEditForm;
    idx      : Integer;
    tranfrom : TTranslateListEntry;
  begin
    try
      editor := TCompTranslateEditForm.Create (Nil);

      try
        editor.TransOwner := LVSCompTranslate;
        editor.OwnerForm := OwenForm;

        editor.ScanCompType := [tctButton, tctLabel, tctPanel, tctMenuItem, tctTabSheet, tctCheckBox, tctRadioButton, tctGroupBox, tctListView];

        tranfrom := Nil;

        if Assigned (OwenForm) then begin
          idx := 0;

          if (OwenForm is TForm) Then begin
            while (idx < fFormList.fTranslateList.Count) do begin
              if (TTranslateListEntry (fFormList.fTranslateList [idx]).SprachForm = OwenForm) then begin
                tranfrom := TTranslateListEntry (fFormList.fTranslateList [idx]);

                break;
              end;

              Inc (idx);
            end;
          (*
          end else if (OwenForm is TDataModule)then begin
            while (idx < fFormList.fTranslateList.Count) do begin
              if (TTranslateListEntry (fFormList.fTranslateList [idx]).SprachModule = OwenForm) then begin
                tranfrom := TTranslateListEntry (fFormList.fTranslateList [idx]);

                break;
              end;

              Inc (idx);
            end;
          *)
          end;
        end;

        if Assigned (tranfrom) then begin
          editor.TranslateList   := tranfrom.TranslateList;
          editor.NoTranslateList := tranfrom.NoTranslateList;
        end;

        editor.DoEditor;
      finally
        editor.Free;
      end;
    except
    end;
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  procedure TLVSSprachModul.WinProc (var WndMessage : TMessage);
  var
    actform  : TForm;

    {$j+}
    const timerstat : integer = 0;
    {$j-}
  begin
    with WndMessage do begin
      if (Msg = WM_USER + 1) then begin
        if (timerstat = 0) then begin
          timerstat := timerstat + 1;

          try
            if (WndMessage.LParam = 0) then
              actform := Nil
            else
              actform := TForm (WndMessage.LParam);

            ShowEditor (actform);
          finally
            timerstat := timerstat - 1;
          end;
        end;
      end;
    end;
  end;
{$endif}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSSprachModul.InitForm (OwenForm : TForm; LangChangeNotify : TNotifyEvent);
var
  idx      : Integer;
  tranfrom : TTranslateListEntry;
begin
  idx := 0;
  tranfrom := Nil;

  while (idx < fFormList.fTranslateList.Count) and not Assigned (tranfrom) do begin
    if (TTranslateListEntry (fFormList.fTranslateList [idx]).SprachForm = OwenForm) then
      tranfrom := fFormList.fTranslateList [idx] as TTranslateListEntry
    else
      Inc (idx);
  end;

  if not Assigned (tranfrom) then begin
    fFormList.Add (OwenForm);

    tranfrom := TTranslateListEntry.Create;

    tranfrom.SprachForm := OwenForm;

    tranfrom.SprachTranslater := TCompTranslateForm.Create (OwenForm);
    tranfrom.SprachTranslater.Master := LVSCompTranslate;
    tranfrom.SprachTranslater.OnChangeLanguage := LangChangeNotify;

    fFormList.fTranslateList.Add (tranfrom);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSSprachModul.InitModule (OwenModule : TDataModule; LangChangeNotify : TNotifyEvent);
var
  idx      : Integer;
  tranfrom : TTranslateListEntry;
begin
  idx := 0;
  tranfrom := Nil;

  while (idx < fFormList.fTranslateList.Count) do begin
    if (TTranslateListEntry (fFormList.fTranslateList [idx]).SprachModule = OwenModule) then begin
      tranfrom := fFormList.fTranslateList [idx] as TTranslateListEntry;

      break;
    end;

    Inc (idx);
  end;

  if not Assigned (tranfrom) then begin
    fFormList.Add (OwenModule);

    tranfrom := TTranslateListEntry.Create;

    tranfrom.SprachModule := OwenModule;

    tranfrom.SprachTranslater := TCompTranslateForm.Create (OwenModule);
    tranfrom.SprachTranslater.Master := LVSCompTranslate;
    tranfrom.SprachTranslater.OnChangeLanguage := LangChangeNotify;

    fFormList.fTranslateList.Add (tranfrom);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSSprachModul.InitFrame (OwenFrame : TFrame; LangChangeNotify : TNotifyEvent);
var
  idx      : Integer;
  tranfrom : TTranslateListEntry;
begin
  idx := 0;
  tranfrom := Nil;

  while (idx < fFormList.fTranslateList.Count) do begin
    if (TTranslateListEntry (fFormList.fTranslateList [idx]).SprachFrame = OwenFrame) then begin
      tranfrom := fFormList.fTranslateList [idx] as TTranslateListEntry;

      break;
    end;

    Inc (idx);
  end;

  if not Assigned (tranfrom) then begin
    fFormList.Add (OwenFrame);

    tranfrom := TTranslateListEntry.Create;

    tranfrom.SprachFrame := OwenFrame;

    tranfrom.SprachTranslater := TCompTranslateForm.Create (OwenFrame);
    tranfrom.SprachTranslater.Master := LVSCompTranslate;
    tranfrom.SprachTranslater.OnChangeLanguage := LangChangeNotify;

    fFormList.fTranslateList.Add (tranfrom);
  end;

  LVSCompTranslate.UpdateFrameCaptions (OwenFrame);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 03.04.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSSprachModul.LVSCompTranslateError(const ErrorLocation, ErrorInfo: string);
begin
  if Assigned (ErrorTrackingModule) then
    ErrorTrackingModule.WriteErrorLog (ErrorLocation, ErrorInfo);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSSprachModul.SetTranslate (OwenForm : TForm; Component : TComponent);
var
  idx      : Integer;
  tranfrom : TTranslateListEntry;
begin
  idx := 0;
  tranfrom := Nil;

  while (idx < fFormList.fTranslateList.Count) do begin
    if (TTranslateListEntry (fFormList.fTranslateList [idx]).SprachForm = OwenForm) then begin
      tranfrom := fFormList.fTranslateList [idx] as TTranslateListEntry;

      break;
    end;

    Inc (idx);
  end;

  if Assigned (tranfrom) then begin
    tranfrom.TranslateList.Add (Component);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSSprachModul.SetConfigPath (const Path : String);
begin
  if (Length (Path) > 0) then
    LVSCompTranslate.LanguageFile := Path + ExtractFileName (LVSCompTranslate.LanguageFile)
  else
    LVSCompTranslate.LanguageFile := ExtractFilePath (ParamStr (0)) + ExtractFileName (LVSCompTranslate.LanguageFile);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSSprachModul.SetNoTranslate (OwenForm : TForm; Component : TComponent);
var
  idx      : Integer;
  tranfrom : TTranslateListEntry;
begin
  idx := 0;
  tranfrom := Nil;

  while (idx < fFormList.fTranslateList.Count) do begin
    if (TTranslateListEntry (fFormList.fTranslateList [idx]).SprachForm = OwenForm) then begin
      tranfrom := fFormList.fTranslateList [idx] as TTranslateListEntry;

      break;
    end;

    Inc (idx);
  end;

  if Assigned (tranfrom) then begin
    tranfrom.NoTranslateList.Add (Component);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSSprachModul.SetNoTranslate (OwenFrame : TFrame; Component : TComponent);
var
  idx      : Integer;
  tranfrom : TTranslateListEntry;
begin
  idx := 0;
  tranfrom := Nil;

  while (idx < fFormList.fTranslateList.Count) do begin
    if (TTranslateListEntry (fFormList.fTranslateList [idx]).SprachFrame = OwenFrame) then begin
      tranfrom := fFormList.fTranslateList [idx] as TTranslateListEntry;

      break;
    end;

    Inc (idx);
  end;

  if Assigned (tranfrom) then begin
    tranfrom.NoTranslateList.Add (Component);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSSprachModul.GetFormTranslate (OwenForm : TForm) : TCompTranslateForm;
var
  idx      : Integer;
  tranfrom : TTranslateListEntry;
begin
  idx := 0;
  tranfrom := Nil;

  while (idx < fFormList.fTranslateList.Count) do begin
    if (TTranslateListEntry (fFormList.fTranslateList [idx]).SprachForm = OwenForm) then begin
      tranfrom := fFormList.fTranslateList [idx] as TTranslateListEntry;

      break;
    end;

    Inc (idx);
  end;

  if Assigned (tranfrom) then
    Result := tranfrom.SprachTranslater
  else
    Result := Nil;
end;

{$ifdef TranslateHelper}
  //******************************************************************************
  var
    HookHandle  : Cardinal = 0;

  function KeyboardHookProc(nCode: Integer; VirtualKey, KeyStroke : Integer) : LRESULT; stdcall; forward;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function InstallHook : Boolean;
  begin
    {$ifdef Trace}
      FunctionStart ('InstallHook');
    {$endif}

    Result := False;
    if HookHandle = 0 then begin
  //Erstmal Hook installieren
  //First install the hook
      HookHandle := SetWindowsHookEx(WH_KEYBOARD, @KeyboardHookProc, 0, GetCurrentThreadId);
  //Uebergebenes Fensterhandle sichern
  //Save the given window handle
      Result := TRUE;
    end;

    {$ifdef Trace}
      FunctionStop (Result);
    {$endif}
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function UninstallHook: Boolean;
  begin
  //Hook aus der Hookchain entfernen
  //Uninstall hook from hook chain
    if (HookHandle <> 0) then
      Result := UnhookWindowsHookEx(HookHandle)
    else Result := True;

    HookHandle := 0;
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function KeyboardHookProc (nCode: Integer; VirtualKey, KeyStroke : Integer) : LRESULT; stdcall;
  begin
    {$ifdef Trace}
      FunctionStart ('KeyboardHookProc');
    {$endif}

    if (nCode < 0) then
      Result := CallNextHookEx (HookHandle, nCode, VirtualKey, KeyStroke)
    else if not Assigned (LVSSprachModul) then
      Result := CallNextHookEx (HookHandle, nCode, VirtualKey, KeyStroke)
    else begin
      if (VirtualKey = VK_F12) and ((KeyStroke and (1 shl 30)) <> 0) then begin
        Result := 1;
        PostMessage (LVSSprachModul.WindowHandle, WM_USER + 1, 0, lParam (Screen.ActiveForm));
      end else begin
        Result := CallNextHookEx (HookHandle, nCode, VirtualKey, KeyStroke);
      end;
    end;

    {$ifdef Trace}
      FunctionStop (Result);
    {$endif}
  end;

  initialization
    InstallHook;

  finalization
    UninstallHook;
{$endif}

end.
