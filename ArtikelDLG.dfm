object ArtikelForm: TArtikelForm
  Left = 177
  Top = 251
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Artikel Stammdaten'
  ClientHeight = 1068
  ClientWidth = 1079
  Color = clBtnFace
  Constraints.MinHeight = 600
  Constraints.MinWidth = 680
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Menu = MainMenu1
  Position = poMainFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnHide = FormHide
  OnResize = FormResize
  OnShow = FormShow
  TextHeight = 13
  object MainSplitter: TSplitter
    Left = 0
    Top = 764
    Width = 1079
    Height = 4
    Cursor = crVSplit
    Align = alBottom
    ExplicitTop = 761
    ExplicitWidth = 1073
  end
  object KopfPanel: TPanel
    Left = 0
    Top = 0
    Width = 1079
    Height = 121
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      1079
      121)
    object AuswahlGroupBox: TGroupBox
      Left = 7
      Top = 4
      Width = 1064
      Height = 109
      Anchors = [akLeft, akTop, akRight, akBottom]
      Caption = 'Auswahl'
      TabOrder = 0
      DesignSize = (
        1064
        109)
      object Label9: TLabel
        Left = 332
        Top = 19
        Width = 45
        Height = 13
        Alignment = taRightJustify
        Caption = 'Artikel-Art'
      end
      object Label6: TLabel
        Left = 17
        Top = 47
        Width = 67
        Height = 13
        Alignment = taRightJustify
        Caption = 'Untermandant'
      end
      object Label10: TLabel
        Left = 310
        Top = 47
        Width = 67
        Height = 13
        Alignment = taRightJustify
        Caption = 'Artikel-Gruppe'
      end
      object Label2: TLabel
        Left = 40
        Top = 19
        Width = 42
        Height = 13
        Alignment = taRightJustify
        Caption = 'Mandant'
      end
      object Label20: TLabel
        Left = 748
        Top = 47
        Width = 36
        Height = 13
        Alignment = taRightJustify
        Anchors = [akTop, akRight]
        Caption = 'Marken'
        ExplicitLeft = 742
      end
      object Label16: TLabel
        Left = 740
        Top = 19
        Width = 44
        Height = 13
        Alignment = taRightJustify
        Anchors = [akTop, akRight]
        Caption = 'Hersteller'
        ExplicitLeft = 734
      end
      object ArtComboBox: TComboBox
        Left = 382
        Top = 16
        Width = 319
        Height = 21
        Style = csDropDownList
        Anchors = [akLeft, akTop, akRight]
        ItemIndex = 2
        TabOrder = 2
        Text = 'Aktivierte und gelistete Artikel'
        OnChange = ChangeQuery
        Items.Strings = (
          'Alle Artikel'
          'Aktivierte Artikel'
          'Aktivierte und gelistete Artikel'
          'Deaktivierte Artikel'
          'Gel'#246'schte Artikel'
          'Neue Artikel von heute'
          'Aktivierte Artikel ohne Lagerbereich'
          'Aktivierte Artikel ohne Komm-Platz'
          'Aktivierte Artikel ohne Listung'
          'Alle Artikel mit Bestand im Lager bzw. Niederlassung')
      end
      object SubMandComboBox: TComboBoxPro
        Left = 88
        Top = 44
        Width = 200
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 1
        OnChange = SubMandComboBoxChange
      end
      object ARGrpComboBox: TComboBoxPro
        Left = 382
        Top = 44
        Width = 319
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 3
        OnChange = ChangeQuery
      end
      object MandantComboBox: TComboBoxPro
        Left = 88
        Top = 16
        Width = 200
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 0
        OnChange = MandantComboBoxChange
      end
      object ArManfComboBox: TComboBoxPro
        Left = 788
        Top = 16
        Width = 267
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akTop, akRight]
        ItemHeight = 15
        TabOrder = 4
        OnChange = ChangeQuery
      end
      object ArBrandComboBox: TComboBoxPro
        Left = 788
        Top = 44
        Width = 267
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akTop, akRight]
        ItemHeight = 15
        TabOrder = 5
        OnChange = ChangeQuery
      end
      object AlleRadioButton: TRadioButton
        Left = 118
        Top = 82
        Width = 92
        Height = 17
        Anchors = [akLeft, akBottom]
        Caption = 'Alle Artikel'
        Checked = True
        TabOrder = 6
        TabStop = True
        OnClick = ChangeQuery
      end
      object NurSetRadioButton: TRadioButton
        Left = 216
        Top = 82
        Width = 96
        Height = 17
        Anchors = [akLeft, akBottom]
        Caption = 'Nur Setartikel'
        TabOrder = 7
        OnClick = ChangeQuery
      end
      object OhneSetRadioButton: TRadioButton
        Left = 318
        Top = 82
        Width = 113
        Height = 17
        Anchors = [akLeft, akBottom]
        Caption = 'Ohne Setartikel'
        TabOrder = 8
        OnClick = ChangeQuery
      end
      object NurLTRadioButton: TRadioButton
        Left = 432
        Top = 82
        Width = 113
        Height = 17
        Anchors = [akLeft, akBottom]
        Caption = 'Nur Ladehilfsmitte'
        TabOrder = 9
        OnClick = ChangeQuery
      end
      object NurMultiColliRadioButton: TRadioButton
        Left = 551
        Top = 82
        Width = 113
        Height = 17
        Anchors = [akLeft, akBottom]
        Caption = 'Nur Multi-Colli'
        TabOrder = 10
        OnClick = ChangeQuery
      end
      object NurTextArRadioButton: TRadioButton
        Left = 655
        Top = 82
        Width = 113
        Height = 17
        Anchors = [akLeft, akBottom]
        Caption = 'Nur Text-Artikel'
        TabOrder = 11
        OnClick = ChangeQuery
      end
    end
  end
  object DetailPanel: TPanel
    Left = 0
    Top = 768
    Width = 1079
    Height = 300
    Align = alBottom
    BevelOuter = bvNone
    Constraints.MinHeight = 300
    TabOrder = 1
    OnResize = DetailPanelResize
    DesignSize = (
      1079
      300)
    object Label5: TLabel
      Left = 16
      Top = 112
      Width = 98
      Height = 13
      Caption = 'Kommissionier-Pl'#228'tze'
    end
    object PageControl1: TPageControl
      AlignWithMargins = True
      Left = 8
      Top = 0
      Width = 1063
      Height = 260
      Margins.Left = 8
      Margins.Top = 0
      Margins.Right = 8
      Margins.Bottom = 40
      ActivePage = NachschubTabSheet
      Align = alClient
      TabOrder = 1
      object BereichTabSheet: TTabSheet
        Caption = 'Lagerbereich'
        ImageIndex = 3
        OnShow = BereichTabSheetShow
        object Splitter1: TSplitter
          Left = 513
          Top = 0
          Height = 232
          OnCanResize = Splitter1CanResize
          ExplicitHeight = 237
        end
        object Panel3: TPanel
          Left = 0
          Top = 0
          Width = 513
          Height = 232
          Align = alLeft
          BevelOuter = bvNone
          Constraints.MinWidth = 300
          TabOrder = 0
          DesignSize = (
            513
            232)
          object Label7: TLabel
            Left = 8
            Top = 8
            Width = 57
            Height = 13
            Caption = 'Lagerlistung'
          end
          object LBStringGrid: TStringGridPro
            Left = 8
            Top = 27
            Width = 489
            Height = 171
            Anchors = [akLeft, akTop, akRight, akBottom]
            ColCount = 9
            DefaultColWidth = 20
            DefaultRowHeight = 18
            Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
            TabOrder = 0
            OnDblClick = ChangeLBButtonClick
            OnPostDrawCell = StringGridStatusPostDrawCell
            TitelTexte.Strings = (
              ''
              'Status'
              'Lager'
              'Listungs-Art'
              'Lagerungs-Art'
              'Fix-Bereich'
              'Fix-Lagerplatz'
              'Auto. auf LEs packen (Typ)'
              'Ist Leergut')
            TitelFont.Charset = DEFAULT_CHARSET
            TitelFont.Color = clWindowText
            TitelFont.Height = -11
            TitelFont.Name = 'MS Sans Serif'
            TitelFont.Style = []
            ColWidths = (
              20
              34
              55
              59
              74
              60
              77
              39
              39)
          end
          object AddLBButton: TButton
            Left = 8
            Top = 203
            Width = 95
            Height = 25
            Anchors = [akLeft, akBottom]
            Caption = 'Hinzuf'#252'gen...'
            TabOrder = 1
            OnClick = ChangeLBButtonClick
          end
          object ChangeLBButton: TButton
            Left = 109
            Top = 203
            Width = 95
            Height = 25
            Anchors = [akLeft, akBottom]
            Caption = 'Bearbeiten...'
            TabOrder = 2
            OnClick = ChangeLBButtonClick
          end
          object DelLBButton: TButton
            Left = 213
            Top = 203
            Width = 95
            Height = 25
            Anchors = [akLeft, akBottom]
            Caption = 'L'#246'schen...'
            TabOrder = 3
            OnClick = DelLBButtonClick
          end
        end
        object Panel6: TPanel
          Left = 516
          Top = 0
          Width = 539
          Height = 232
          Align = alClient
          BevelOuter = bvNone
          Constraints.MinWidth = 500
          TabOrder = 1
          DesignSize = (
            539
            232)
          object Label22: TLabel
            Left = 16
            Top = 8
            Width = 56
            Height = 13
            Caption = 'Einlagerung'
          end
          object EinlagerStringGrid: TStringGridPro
            Left = 16
            Top = 27
            Width = 510
            Height = 171
            Anchors = [akLeft, akTop, akRight, akBottom]
            ColCount = 12
            DefaultColWidth = 20
            DefaultRowHeight = 18
            RowCount = 10
            Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
            PopupMenu = EinlagerStringGridPopupMenu
            TabOrder = 0
            OnDblClick = ChangeEinlButtonClick
            OnPostDrawCell = StringGridStatusPostDrawCell
            TitelTexte.Strings = (
              ''
              'Status'
              'Mandant'
              'Lager'
              'Artikel Gruppe'
              'LT-Typ'
              'Bereich'
              'Platz'
              'Prio'
              'Auto Einl.'
              'Big'
              'Sperr')
            TitelFont.Charset = DEFAULT_CHARSET
            TitelFont.Color = clWindowText
            TitelFont.Height = -11
            TitelFont.Name = 'MS Sans Serif'
            TitelFont.Style = []
            ColWidths = (
              20
              54
              89
              85
              105
              65
              78
              20
              20
              20
              20
              20)
          end
          object ChangeEinlButton: TButton
            Left = 228
            Top = 203
            Width = 95
            Height = 25
            Anchors = [akLeft, akBottom]
            Caption = 'Bearbeiten...'
            TabOrder = 3
            OnClick = ChangeEinlButtonClick
          end
          object AddAllgEinlButton: TButton
            Left = 16
            Top = 203
            Width = 95
            Height = 25
            Anchors = [akLeft, akBottom]
            Caption = 'Neu allgemein...'
            TabOrder = 1
            OnClick = ChangeEinlButtonClick
          end
          object DelEinlButton: TButton
            Left = 333
            Top = 203
            Width = 95
            Height = 25
            Anchors = [akLeft, akBottom]
            Caption = 'L'#246'schen...'
            TabOrder = 4
            OnClick = DelEinlButtonClick
          end
          object AddArtikelEinlButton: TButton
            Left = 123
            Top = 203
            Width = 95
            Height = 25
            Anchors = [akLeft, akBottom]
            Caption = 'Neu f'#252'r Artikel...'
            TabOrder = 2
            OnClick = ChangeEinlButtonClick
          end
        end
      end
      object VPETabSheet: TTabSheet
        Caption = 'Verpackungseinheiten'
        ImageIndex = 1
        OnResize = VPETabSheetResize
        OnShow = VPETabSheetShow
        DesignSize = (
          1055
          232)
        object VPEStringGrid: TStringGridPro
          Left = 8
          Top = 35
          Width = 1041
          Height = 158
          Anchors = [akLeft, akTop, akRight, akBottom]
          ColCount = 22
          DefaultColWidth = 20
          DefaultRowHeight = 18
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'MS Sans Serif'
          Font.Style = []
          Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
          ParentFont = False
          PopupMenu = EinheitPopupMenu
          TabOrder = 0
          OnDblClick = VPEStringGridDblClick
          OnPostDrawCell = VPEStringGridDrawCell
          TitelFont.Charset = DEFAULT_CHARSET
          TitelFont.Color = clWindowText
          TitelFont.Height = -11
          TitelFont.Name = 'MS Sans Serif'
          TitelFont.Style = []
          ColWidths = (
            10
            20
            22
            43
            39
            82
            75
            66
            60
            45
            76
            76
            32
            32
            32
            32
            28
            37
            40
            37
            29
            78)
        end
        object NewVPEButton: TButton
          Left = 8
          Top = 202
          Width = 75
          Height = 25
          Anchors = [akLeft, akBottom]
          Caption = 'Hinzuf'#252'gen...'
          TabOrder = 1
          OnClick = NewVPEButtonClick
        end
        object ChangeVPEButton: TButton
          Left = 96
          Top = 202
          Width = 75
          Height = 25
          Anchors = [akLeft, akBottom]
          Caption = 'Bearbeiten...'
          TabOrder = 2
          OnClick = ChangeVPEButtonClick
        end
        object DelVPEButton: TButton
          Left = 183
          Top = 202
          Width = 75
          Height = 25
          Anchors = [akLeft, akBottom]
          Caption = 'L'#246'schen...'
          TabOrder = 3
          OnClick = DelVPEButtonClick
        end
        object PrintEAN128Button: TButton
          Left = 909
          Top = 263
          Width = 140
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = 'EAN128 drucken...'
          TabOrder = 4
          OnClick = PrintLabelButtonClick
        end
        object VPEDispStatComboBox: TComboBox
          Left = 8
          Top = 8
          Width = 313
          Height = 21
          Style = csDropDownList
          ItemIndex = 1
          TabOrder = 5
          Text = 'Nur die aktiven Verpackungseinheiten anzeigen'
          OnChange = VPEDispStatComboBoxChange
          Items.Strings = (
            'Alle Verpackungseinheiten anzeigen'
            'Nur die aktiven Verpackungseinheiten anzeigen'
            'Nur die gel'#246'schten Verpackungseinheiten anzeigen'
            'Nur die veralteten Verpackungseinheiten anzeigen')
        end
      end
      object PictureTabSheet: TTabSheet
        Caption = 'Artikel-Bilder'
        ImageIndex = 6
        OnShow = PictureTabSheetShow
        object ArtikelImage: TImage32
          Left = 0
          Top = 0
          Width = 228
          Height = 232
          Align = alClient
          Bitmap.ResamplerClassName = 'TDraftResampler'
          BitmapAlign = baCenter
          Color = clWhite
          ParentColor = False
          Scale = 1.000000000000000000
          ScaleMode = smOptimal
          TabOrder = 0
        end
        object Panel7: TPanel
          Left = 228
          Top = 0
          Width = 827
          Height = 232
          Align = alRight
          BevelOuter = bvNone
          TabOrder = 1
          DesignSize = (
            827
            232)
          object Label23: TLabel
            Left = 18
            Top = 8
            Width = 20
            Height = 13
            Caption = 'Bild:'
          end
          object PicFilenameLabel: TLabel
            Left = 24
            Top = 27
            Width = 83
            Height = 13
            Caption = 'PicFilenameLabel'
            PopupMenu = PicFilenameLabelPopupMenu
          end
          object PicURLLabel: TLabel
            Left = 24
            Top = 59
            Width = 63
            Height = 13
            Caption = 'PicURLLabel'
            PopupMenu = PicFilenameLabelPopupMenu
          end
          object Bevel4: TBevel
            Left = 19
            Top = 24
            Width = 800
            Height = 21
            Anchors = [akLeft, akTop, akRight]
          end
          object Bevel5: TBevel
            Left = 19
            Top = 55
            Width = 800
            Height = 21
            Anchors = [akLeft, akTop, akRight]
          end
          object PicFilenameButton: TButton
            Left = 18
            Top = 86
            Width = 156
            Height = 25
            Caption = 'Bilddatei ausw'#228'hlen...'
            TabOrder = 0
            OnClick = PicFilenameButtonClick
          end
          object PicURLButton: TButton
            Left = 184
            Top = 86
            Width = 156
            Height = 25
            Caption = 'URL '#228'ndern...'
            TabOrder = 1
            OnClick = PicURLButtonClick
          end
          object PicReloadButton: TButton
            Left = 18
            Top = 198
            Width = 75
            Height = 25
            Anchors = [akLeft, akBottom]
            Caption = 'Neu laden'
            TabOrder = 2
            OnClick = PicReloadButtonClick
          end
          object PicDeleteButton: TButton
            Left = 696
            Top = 86
            Width = 123
            Height = 25
            Anchors = [akTop, akRight]
            Caption = 'Bild entfernen'
            TabOrder = 3
            OnClick = PicDeleteButtonClick
          end
        end
      end
      object SetPosTabSheet: TTabSheet
        Caption = 'Set Positionen'
        ImageIndex = 5
        OnShow = SetPosTabSheetShow
        DesignSize = (
          1055
          232)
        object Label17: TLabel
          Left = 8
          Top = 72
          Width = 121
          Height = 13
          Caption = 'Setpositionen des Artikels'
        end
        object Label18: TLabel
          Left = 8
          Top = 8
          Width = 50
          Height = 13
          Caption = 'Set Name:'
        end
        object SetNameLabel: TLabel
          Left = 108
          Top = 8
          Width = 83
          Height = 13
          Caption = 'SetNameLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object Label19: TLabel
          Left = 8
          Top = 32
          Width = 84
          Height = 13
          Caption = 'Set Bezeichnung:'
        end
        object SetDescLabel: TLabel
          Left = 108
          Top = 32
          Width = 67
          Height = 13
          Caption = 'SetDescLabel'
        end
        object Bevel3: TBevel
          Left = 3
          Top = 51
          Width = 1043
          Height = 7
          Anchors = [akLeft, akTop, akRight]
          Shape = bsTopLine
          ExplicitWidth = 1040
        end
        object Label27: TLabel
          Left = 378
          Top = 8
          Width = 65
          Height = 13
          Alignment = taRightJustify
          Caption = 'Kennzeichen:'
        end
        object SetMarkLabel: TLabel
          Left = 448
          Top = 8
          Width = 79
          Height = 13
          Caption = 'SetMarkLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object SetPosDBGrid: TDBGridPro
          Left = 8
          Top = 88
          Width = 1038
          Height = 135
          Anchors = [akLeft, akTop, akRight, akBottom]
          DataSource = SetPosDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
          PopupMenu = SetPosPopupMenu
          ReadOnly = True
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          OnDblClick = ChangeSetPosMenuItemClick
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'Tahoma'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\CS'
          RegistrySection = 'DBGrids'
          WidthOfIndicator = 11
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
        end
        object EditArtikelSetButton: TButton
          Left = 783
          Top = 3
          Width = 129
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Set Daten '#228'ndern'
          TabOrder = 1
          OnClick = EditArtikelSetButtonClick
        end
        object PrintArtikelSetButton: TButton
          Left = 918
          Top = 3
          Width = 129
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Set Daten drucken'
          TabOrder = 2
          OnClick = PrintArtikelSetButtonClick
        end
      end
      object LieferantTabSheet: TTabSheet
        Caption = 'Lieferant'
        ImageIndex = 4
        OnResize = LieferantTabSheetResize
        OnShow = LieferantTabSheetShow
        DesignSize = (
          1055
          232)
        object Label14: TLabel
          Left = 8
          Top = 32
          Width = 110
          Height = 13
          Caption = 'Lieferanten des Artikels'
        end
        object LieferantDBGrid: TDBGridPro
          Left = 8
          Top = 48
          Width = 1038
          Height = 175
          Anchors = [akLeft, akTop, akRight, akBottom]
          DataSource = LieferantDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
          ReadOnly = True
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'Tahoma'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\CS'
          RegistrySection = 'DBGrids'
          WidthOfIndicator = 11
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
        end
      end
      object KommLPTabSheet: TTabSheet
        Caption = 'Kommissionierpl'#228'tze'
        ImageIndex = 2
        OnResize = KommLPTabSheetResize
        OnShow = KommLPTabSheetShow
        DesignSize = (
          1055
          232)
        object Label11: TLabel
          Left = 8
          Top = 64
          Width = 98
          Height = 13
          Caption = 'Kommissionier-Pl'#228'tze'
        end
        object Label12: TLabel
          Left = 256
          Top = 64
          Width = 32
          Height = 13
          Caption = 'Einheit'
        end
        object Label8: TLabel
          Left = 8
          Top = 8
          Width = 27
          Height = 13
          Caption = 'Lager'
        end
        object Bevel1: TBevel
          Left = 8
          Top = 56
          Width = 1040
          Height = 9
          Anchors = [akLeft, akTop, akRight]
          Shape = bsTopLine
          ExplicitWidth = 1037
        end
        object Label1: TLabel
          Left = 377
          Top = 8
          Width = 46
          Height = 13
          Caption = 'Verf'#252'gbar'
        end
        object KommLPStringGrid: TStringGridPro
          Left = 377
          Top = 80
          Width = 590
          Height = 147
          Anchors = [akLeft, akTop, akRight, akBottom]
          ColCount = 8
          DefaultColWidth = 20
          DefaultRowHeight = 18
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'MS Sans Serif'
          Font.Style = []
          Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
          ParentFont = False
          PopupMenu = KommLPStringGridPopupMenu
          TabOrder = 6
          OnDragDrop = KommLPStringGridDragDrop
          OnDragOver = KommLPStringGridDragOver
          OnMouseDown = KommLPStringGridMouseDown
          TitelFont.Charset = DEFAULT_CHARSET
          TitelFont.Color = clWindowText
          TitelFont.Height = -11
          TitelFont.Name = 'MS Sans Serif'
          TitelFont.Style = []
          ColWidths = (
            20
            102
            100
            81
            108
            93
            30
            30)
        end
        object KommLPListBox: TListBox
          Left = 8
          Top = 82
          Width = 242
          Height = 147
          Style = lbOwnerDrawFixed
          Anchors = [akLeft, akTop, akBottom]
          DragMode = dmAutomatic
          ItemHeight = 13
          TabOrder = 2
          OnClick = KommLPListBoxClick
          OnDragDrop = KommLPListBoxDragDrop
          OnDragOver = KommLPListBoxDragOver
          OnDrawItem = KommLPListBoxDrawItem
          OnMouseDown = KommLPListBoxMouseDown
        end
        object KommLPAssigndButton: TButton
          Left = 256
          Top = 112
          Width = 75
          Height = 25
          Caption = '>>'
          TabOrder = 4
          OnClick = KommLPAssigndButtonClick
        end
        object KommVPEComboBox: TComboBoxPro
          Left = 256
          Top = 80
          Width = 105
          Height = 21
          Style = csDropDownList
          TabOrder = 3
        end
        object KommLPRemoveButton: TButton
          Left = 256
          Top = 144
          Width = 75
          Height = 25
          Caption = '<<'
          TabOrder = 5
          OnClick = KommLPRemoveButtonClick
        end
        object KommLagerComboBox: TComboBoxPro
          Left = 8
          Top = 24
          Width = 353
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 0
          OnChange = KommLagerComboBoxChange
        end
        object KommLBComboBox: TComboBoxPro
          Left = 377
          Top = 24
          Width = 672
          Height = 22
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 1
          OnChange = KommLBComboBoxChange
        end
        object PrintKommLPButton: TButton
          Left = 974
          Top = 80
          Width = 75
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Drucken...'
          TabOrder = 7
          OnClick = PrintKommLPButtonClick
        end
      end
      object QSTabSheet: TTabSheet
        Caption = 'Qualit'#228'tskontrolle'
        ImageIndex = 3
        OnShow = QSTabSheetShow
        DesignSize = (
          1055
          232)
        object Label3: TLabel
          Left = 8
          Top = 13
          Width = 53
          Height = 13
          Caption = 'QS-Gruppe'
        end
        object ARQSGroupLabel: TLabel
          Left = 85
          Top = 13
          Width = 100
          Height = 13
          Caption = 'ARQSGroupLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object ChangeQSButton: TButton
          Left = 951
          Top = 82
          Width = 95
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = #196'ndern...'
          TabOrder = 0
          OnClick = ChangeQSButtonClick
        end
        object QSDatenGroupBox: TGroupBox
          Left = 3
          Top = 39
          Width = 1043
          Height = 190
          Anchors = [akLeft, akTop, akRight, akBottom]
          Caption = 'Daten'
          TabOrder = 1
          DesignSize = (
            1043
            190)
          object Label4: TLabel
            Left = 258
            Top = 14
            Width = 89
            Height = 13
            Caption = 'Qualit'#228'tspr'#252'fungen'
          end
          object Label13: TLabel
            Left = 8
            Top = 30
            Width = 52
            Height = 13
            Caption = 'WE-Check'
          end
          object QSCheckWELabel: TLabel
            Left = 98
            Top = 30
            Width = 105
            Height = 13
            Caption = 'QSCheckWELabel'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = 'MS Sans Serif'
            Font.Style = [fsBold]
            ParentFont = False
          end
          object Label15: TLabel
            Left = 8
            Top = 50
            Width = 56
            Height = 13
            Caption = 'RET-Check'
          end
          object QSCheckRETLabel: TLabel
            Left = 98
            Top = 50
            Width = 110
            Height = 13
            Caption = 'QSCheckRETLabel'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = 'MS Sans Serif'
            Font.Style = [fsBold]
            ParentFont = False
          end
          object Label21: TLabel
            Left = 8
            Top = 70
            Width = 52
            Height = 13
            Caption = 'WA-Check'
          end
          object QSCheckWALabel: TLabel
            Left = 98
            Top = 70
            Width = 105
            Height = 13
            Caption = 'QSCheckWALabel'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = 'MS Sans Serif'
            Font.Style = [fsBold]
            ParentFont = False
          end
          object Label25: TLabel
            Left = 8
            Top = 90
            Width = 67
            Height = 13
            Caption = 'KOMM-Check'
          end
          object QSCheckKOMMLabel: TLabel
            Left = 98
            Top = 90
            Width = 122
            Height = 13
            Caption = 'QSCheckKOMMLabel'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = 'MS Sans Serif'
            Font.Style = [fsBold]
            ParentFont = False
          end
          object CheckStringGrid: TStringGridPro
            Left = 257
            Top = 30
            Width = 773
            Height = 149
            Anchors = [akLeft, akTop, akRight, akBottom]
            ColCount = 4
            DefaultColWidth = 20
            DefaultRowHeight = 18
            Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRowSelect]
            TabOrder = 0
            TitelFont.Charset = DEFAULT_CHARSET
            TitelFont.Color = clWindowText
            TitelFont.Height = -11
            TitelFont.Name = 'Tahoma'
            TitelFont.Style = []
            ColWidths = (
              20
              92
              182
              221)
          end
        end
      end
      object EmpfTabSheet: TTabSheet
        Caption = 'Kundenspezifische Infos'
        ImageIndex = 7
        TabVisible = False
        OnShow = EmpfTabSheetShow
        DesignSize = (
          1055
          232)
        object Label24: TLabel
          Left = 8
          Top = 8
          Width = 263
          Height = 13
          Caption = 'Spezifische Artikeldaten f'#252'r bestimmte Warenempf'#228'nger'
        end
        object DBGridPro1: TDBGridPro
          Left = 8
          Top = 24
          Width = 1038
          Height = 202
          Anchors = [akLeft, akTop, akRight, akBottom]
          DataSource = EmpfDataDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
          PopupMenu = EmpfDataPopupMenu
          ReadOnly = True
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'Tahoma'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\CS'
          RegistrySection = 'DBGrids'
          WidthOfIndicator = 11
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
        end
      end
      object LabelTabSheet: TTabSheet
        Caption = 'Etiketten'
        ImageIndex = 8
        OnResize = LabelTabSheetResize
        OnShow = LabelTabSheetShow
        DesignSize = (
          1055
          232)
        object Label26: TLabel
          Left = 8
          Top = 8
          Width = 192
          Height = 13
          Caption = 'Etiketten f'#252'r bestimmte Warenempf'#228'nger'
        end
        object LabelFormatDBGrid: TDBGridPro
          Left = 8
          Top = 24
          Width = 1038
          Height = 205
          Anchors = [akLeft, akTop, akRight, akBottom]
          DataSource = LabelFormatDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
          ReadOnly = True
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'Tahoma'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\CS'
          RegistrySection = 'DBGrids'
          WidthOfIndicator = 11
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
        end
      end
      object ErsatzTabSheet: TTabSheet
        Caption = 'Ersatzartikel'
        ImageIndex = 9
        OnShow = ErsatzTabSheetShow
        DesignSize = (
          1055
          232)
        object Label28: TLabel
          Left = 8
          Top = 8
          Width = 116
          Height = 13
          Caption = 'Ersatzartikel Definitionen'
        end
        object ErsatzDBGrid: TDBGridPro
          Left = 8
          Top = 24
          Width = 1038
          Height = 205
          Anchors = [akLeft, akTop, akRight, akBottom]
          DataSource = ErsatzDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
          ReadOnly = True
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'Tahoma'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\CS'
          RegistrySection = 'DBGrids'
          WidthOfIndicator = 11
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
        end
      end
      object NachschubTabSheet: TTabSheet
        Caption = 'Nachschub'
        ImageIndex = 10
        OnShow = NachschubTabSheetShow
        object Label29: TLabel
          AlignWithMargins = True
          Left = 3
          Top = 38
          Width = 106
          Height = 13
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Caption = 'Nachschub-Parameter'
        end
        object NachschubDBGrid: TDBGridPro
          Left = 0
          Top = 57
          Width = 1055
          Height = 175
          Align = alBottom
          DataSource = NachschubDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
          PopupMenu = NachschubPopupMenu
          ReadOnly = True
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          OnDblClick = NachschubDBGridDblClick
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'Tahoma'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\CS'
          RegistrySection = 'DBGrids'
          WidthOfIndicator = 11
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
        end
      end
      object DocTabSheet: TTabSheet
        Caption = 'Dokumente'
        ImageIndex = 11
        TabVisible = False
        DesignSize = (
          1055
          232)
        object Label30: TLabel
          Left = 8
          Top = 8
          Width = 137
          Height = 13
          Caption = 'Dokumente zu diesem Artikel'
        end
        object DocDBGrid: TDBGridPro
          Left = 8
          Top = 24
          Width = 1038
          Height = 205
          Anchors = [akLeft, akTop, akRight, akBottom]
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
          ReadOnly = True
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'Tahoma'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\CS'
          RegistrySection = 'DBGrids'
          WidthOfIndicator = 11
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
        end
      end
    end
    object CloseButton: TButton
      Left = 996
      Top = 270
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
  end
  object ArtikelPanel: TPanel
    AlignWithMargins = True
    Left = 8
    Top = 121
    Width = 1063
    Height = 643
    Margins.Left = 8
    Margins.Top = 0
    Margins.Right = 8
    Margins.Bottom = 0
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 2
    object ArtikelDBGrid: TDBGridPro
      Left = 0
      Top = 0
      Width = 1063
      Height = 592
      Align = alClient
      Constraints.MinHeight = 150
      DataSource = ArtikelDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit, dgMultiSelect]
      PopupMenu = ArtikelPopupMenu
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      OnDrawColumnCell = ArtikelDBGridDrawColumnCell
      OnDblClick = ArtikelDBGridDblClick
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoCheckBoxSelect, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 29
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
      OnColumnSort = ArtikelGridColumnSort
    end
    object EditArtikelPanel: TPanel
      Left = 0
      Top = 592
      Width = 1063
      Height = 51
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 1
      DesignSize = (
        1063
        51)
      object Bevel2: TBevel
        Left = 0
        Top = 41
        Width = 1063
        Height = 12
        Anchors = [akLeft, akRight, akBottom]
        Shape = bsTopLine
        ExplicitWidth = 1057
      end
      object NewArtikelButton: TButton
        Left = 8
        Top = 8
        Width = 75
        Height = 25
        Caption = 'Hinzuf'#252'gen...'
        TabOrder = 0
        OnClick = NewArtikelButtonClick
      end
      object ChangeArtikelButton: TButton
        Left = 96
        Top = 8
        Width = 75
        Height = 25
        Caption = 'Bearbeiten...'
        TabOrder = 1
        OnClick = ChangeArtikelButtonClick
      end
      object DelArtikelButton: TButton
        Left = 184
        Top = 8
        Width = 75
        Height = 25
        Caption = 'L'#246'schen...'
        TabOrder = 2
        OnClick = DelArtikelButtonClick
      end
      object PrintArtikelButton: TButton
        Left = 272
        Top = 8
        Width = 75
        Height = 25
        Caption = 'Drucken...'
        TabOrder = 3
        OnClick = PrintArtikelButtonClick
      end
    end
  end
  object ArtikelDataSource: TDataSource
    DataSet = ARDataSet
    OnDataChange = ArtikelDataSourceDataChange
    Left = 296
    Top = 120
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 192
    Top = 144
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 240
    Top = 208
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 368
    Top = 136
  end
  object EinheitPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = EinheitPopupMenuPopup
    Left = 412
    Top = 583
    object EinheitCopyColMenuItem: TMenuItem
      Caption = 'Kopieren'
      ImageIndex = 26
      OnClick = StringGridCopyColMenuItemClick
    end
    object EinheitColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      ImageIndex = 27
      OnClick = StringGridColOptimalMenuItemClick
    end
    object N21: TMenuItem
      Caption = '-'
    end
    object AddEinheitMenuItem: TMenuItem
      Caption = 'Verpackungseinheit hinzuf'#252'gen...'
      OnClick = AddEinheitMenuItemClick
    end
    object N20: TMenuItem
      Caption = '-'
    end
    object AddColliEinheitMenuItem: TMenuItem
      Caption = 'Colli-Einheit hinzuf'#252'gen...'
      OnClick = AddEinheitMenuItemClick
    end
    object AddBarcodeMenuItem: TMenuItem
      Caption = 'Barcode hinzuf'#252'gen...'
      OnClick = AddBarcodeMenuItemClick
    end
    object N19: TMenuItem
      Caption = '-'
    end
    object ChangeEinheitMenuItem: TMenuItem
      Caption = 'Bearbeiten...'
      OnClick = ChangeEinheitMenuItemClick
    end
    object DeleteEinheitMenuItem: TMenuItem
      Caption = 'L'#246'schen...'
      ImageIndex = 23
      OnClick = DeleteEinheitMenuItemClick
    end
    object N11: TMenuItem
      Caption = '-'
    end
    object EditLocEinheitMenuItem: TMenuItem
      Caption = 'Niederlassungsbezoge Daten...'
      OnClick = EditLocEinheitMenuItemClick
    end
    object N5: TMenuItem
      Caption = '-'
    end
    object ActivateEinheitMenuItem: TMenuItem
      Caption = 'Verpackungseinheit aktivieren...'
      OnClick = ActivateEinheitMenuItemClick
    end
    object N6: TMenuItem
      Caption = '-'
    end
    object PrintPalMenuItem: TMenuItem
      Caption = 'Palettebeschriftung drucken...'
      OnClick = PrintLabelButtonClick
    end
    object PrintEANMenuItem: TMenuItem
      Caption = 'EAN drucken...'
      OnClick = PrintLabelButtonClick
    end
    object PrintEAN128MenuItem: TMenuItem
      Caption = 'EAN128 drucken...'
      OnClick = PrintLabelButtonClick
    end
    object PrintBarcodeMenuItem: TMenuItem
      Caption = 'Barcode drucken...'
      OnClick = PrintLabelButtonClick
    end
    object PrintNVEMenuItem: TMenuItem
      Caption = 'NVE-Etikett erzeugen...'
      OnClick = PrintNVEMenuItemClick
    end
    object N4: TMenuItem
      Caption = '-'
    end
    object ShowArChangeHistMenuItem: TMenuItem
      Caption = 'Zu- und Abg'#228'nge...'
      OnClick = Bewegungshistorie1Click
    end
    object ShowArMoveHistMenuItem: TMenuItem
      Caption = 'Bewegungs-Historie...'
      OnClick = ShowArMoveHistMenuItemClick
    end
  end
  object ArtikelPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = ArtikelPopupMenuPopup
    Left = 472
    Top = 128
    object AddArtikelMenuItem: TMenuItem
      Caption = 'Neuen Artikel anlegen...'
      OnClick = AddArtikelMenuItemClick
    end
    object AddArtikelSetMenuItem: TMenuItem
      Caption = 'Neues Artikelset anlegen...'
      OnClick = AddArtikelMenuItemClick
    end
    object N9: TMenuItem
      Caption = '-'
    end
    object CopyArtikelMenuItem: TMenuItem
      Caption = 'Artikel kopieren...'
      ShortCut = 16470
      OnClick = CopyArtikelMenuItemClick
    end
    object EditArtikelMenuItem: TMenuItem
      Caption = 'Artikel bearbeiten...'
      OnClick = EditArtikelMenuItemClick
    end
    object DeleteArtikelMenuItem: TMenuItem
      Caption = 'Artikel l'#246'schen...'
      ImageIndex = 23
      ShortCut = 46
      OnClick = DeleteArtikelMenuItemClick
    end
    object N17: TMenuItem
      Caption = '-'
    end
    object ArtikelToSetMenuItem: TMenuItem
      Caption = 'Artikel in einen Setartikel umwandeln...'
      OnClick = ArtikelToSetMenuItemClick
    end
    object SetToArtikelMenuItem: TMenuItem
      Caption = 'Setartikel in ein einfach Artikel umwandlen'
      OnClick = SetToArtikelMenuItemClick
    end
    object N18: TMenuItem
      Caption = '-'
    end
    object VorhalteMengenMenuItem: TMenuItem
      Caption = 'Vorhaltemengen besetzen...'
      OnClick = VorhalteMengenMenuItemClick
    end
    object N8: TMenuItem
      Caption = '-'
    end
    object ShowArtikelPictureMenuItem: TMenuItem
      Caption = 'Bild anzeigen...'
      ImageIndex = 24
      OnClick = ShowArtikelPictureMenuItemClick
    end
    object N7: TMenuItem
      Caption = '-'
    end
    object ActivateArtikelMenuItem: TMenuItem
      Caption = 'Aktivieren'
      OnClick = ArtikelStatusMenuItemClick
    end
    object DeactivateArtikelMenuItem: TMenuItem
      Caption = 'Deaktivieren'
      ImageIndex = 22
      OnClick = ArtikelStatusMenuItemClick
    end
    object N10: TMenuItem
      Caption = '-'
    end
    object MeldeERPBestandEinMenuItem: TMenuItem
      Caption = 'Bestandsmeldung an ERP ein'
      OnClick = MeldeERPBestandEinMenuItemClick
    end
    object MeldeERPBestandAusMenuItem: TMenuItem
      Caption = 'Bestandsmeldung an ERP aus'
      OnClick = MeldeERPBestandEinMenuItemClick
    end
    object N12: TMenuItem
      Caption = '-'
    end
    object ArtikelExeclBerichteMenuItem: TMenuItem
      Caption = 'Excel-Berichte...'
    end
    object N15: TMenuItem
      Caption = '-'
    end
    object BuildArSEtProdOrderMenuItem: TMenuItem
      Caption = 'Produktionsauftrag erstellen...'
      OnClick = BuildArSEtProdOrderMenuItemClick
    end
  end
  object MainMenu1: TMainMenu
    Left = 136
    Top = 136
    object Datei1: TMenuItem
      Caption = '&Datei'
      object Drucken1: TMenuItem
        Caption = 'Drucken...'
        OnClick = Drucken1Click
      end
      object N2: TMenuItem
        Caption = '-'
      end
      object Ende1: TMenuItem
        Caption = 'Ende'
        OnClick = Ende1Click
      end
    end
    object Bearbeiten3: TMenuItem
      Caption = '&Bearbeiten'
      object ArtikeldatenImportierenMenuItem: TMenuItem
        Caption = 'Artikeldaten importieren...'
        OnClick = ArtikeldatenImportierenMenuItemClick
      end
      object N3: TMenuItem
        Caption = '-'
      end
      object Aktualisieren1: TMenuItem
        Caption = 'Aktualisieren'
        ShortCut = 116
        OnClick = Aktualisieren1Click
      end
    end
    object Suchen1: TMenuItem
      Caption = '&Suchen'
      object NachArtikelNr1: TMenuItem
        Caption = 'Nach Artikel-Nr...'
        OnClick = NachArtikelNr1Click
      end
      object NachEAN1: TMenuItem
        Caption = 'Nach EAN...'
        OnClick = NachEAN1Click
      end
      object FindDuplicatEANMenuItem: TMenuItem
        Caption = 'Nach doppelten EANs'
        OnClick = FindDuplicatEANMenuItemClick
      end
    end
    object Artikel1: TMenuItem
      Caption = '&Artikel'
      object Verpackungsarten1: TMenuItem
        Caption = 'Verpackungsarten...'
        OnClick = Verpackungsarten1Click
      end
      object N1: TMenuItem
        Caption = '-'
      end
      object NeuerArtikel1: TMenuItem
        Caption = 'Neuer Artikel...'
        OnClick = NeuerArtikel1Click
      end
      object NewVPEMainMenuItem: TMenuItem
        Caption = 'Neue Verpackungseinheit..'
        OnClick = NewVPEMainMenuItemClick
      end
    end
    object Vorhaltemengen1: TMenuItem
      Caption = 'Vorhaltemengen'
      Visible = False
      object Allgemein1: TMenuItem
        Caption = 'Allgemein...'
        OnClick = Allgemein1Click
      end
      object ProArtikelgruppe1: TMenuItem
        Caption = 'Pro Artikelgruppe...'
      end
    end
  end
  object LieferantDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 768
    Top = 648
  end
  object LieferantDataSource: TDataSource
    DataSet = LieferantDataSet
    Left = 736
    Top = 648
  end
  object SetPosDataSource: TDataSource
    DataSet = SetPosDataSet
    Left = 736
    Top = 680
  end
  object SetPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 768
    Top = 680
  end
  object SetPosPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = SetPosPopupMenuPopup
    Left = 464
    Top = 592
    object NewSetPosMenuItem: TMenuItem
      Caption = 'Neue Setposition'
      OnClick = NewSetPosMenuItemClick
    end
    object ChangeSetPosMenuItem: TMenuItem
      Caption = 'Setposition bearbeiten'
      OnClick = ChangeSetPosMenuItemClick
    end
    object N16: TMenuItem
      Caption = '-'
    end
    object DelSetPosMenuItem: TMenuItem
      Caption = 'Setposition l'#246'schen'
      ImageIndex = 23
      ShortCut = 46
      OnClick = DelSetPosMenuItemClick
    end
  end
  object PicFilenameLabelPopupMenu: TPopupMenu
    Left = 624
    Top = 576
    object Kopieren1: TMenuItem
      Caption = 'Kopieren'
      OnClick = Kopieren1Click
    end
  end
  object EmpfDataDataSource: TDataSource
    DataSet = EmpfDataDataSet
    Left = 864
    Top = 600
  end
  object EmpfDataDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 896
    Top = 600
  end
  object EmpfDataPopupMenu: TPopupMenu
    Left = 576
    Top = 552
    object AddEmpfDataMenuItem: TMenuItem
      Caption = 'Hinzuf'#252'gen...'
      OnClick = AddEmpfDataMenuItemClick
    end
    object N14: TMenuItem
      Caption = '-'
    end
    object EditEmpfDataMenuItem: TMenuItem
      Caption = #196'ndern...'
    end
    object N13: TMenuItem
      Caption = '-'
    end
    object DeleteEmpfDataMenuItem: TMenuItem
      Caption = 'L'#246'schen...'
    end
  end
  object ARDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 264
    Top = 144
  end
  object LabelFormatDataSource: TDataSource
    DataSet = LabelFormatDataSet
    Left = 632
    Top = 704
  end
  object LabelFormatDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 608
    Top = 640
  end
  object ErsatzDataSource: TDataSource
    DataSet = ErsatzDataSet
    Left = 736
    Top = 616
  end
  object ErsatzDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 768
    Top = 616
  end
  object KommLPStringGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = KommLPStringGridPopupMenuPopup
    Left = 896
    Top = 704
    object KommLPCopyColMenuitem: TMenuItem
      Caption = 'Kopieren'
      ImageIndex = 26
      OnClick = StringGridCopyColMenuItemClick
    end
    object KommLPColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      ImageIndex = 27
      OnClick = StringGridColOptimalMenuItemClick
    end
    object N22: TMenuItem
      Caption = '-'
    end
    object KommLPNachParamMenuItem: TMenuItem
      Caption = 'Nachschub-Parameter...'
      OnClick = KommLPNachParamMenuItemClick
    end
  end
  object EinlagerStringGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = EinlagerStringGridPopupMenuPopup
    Left = 96
    Top = 296
    object EinlagerColCopyMenuItem: TMenuItem
      Caption = 'Kopieren'
      ImageIndex = 26
      OnClick = StringGridCopyColMenuItemClick
    end
    object EinlagerColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      ImageIndex = 27
      OnClick = StringGridColOptimalMenuItemClick
    end
  end
  object NachschubDataSource: TDataSource
    DataSet = NachschubDataSet
    Left = 864
    Top = 568
  end
  object NachschubDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 896
    Top = 568
  end
  object NachschubPopupMenu: TPopupMenu
    OnPopup = NachschubPopupMenuPopup
    Left = 216
    Top = 569
    object NeuNachMenuItem: TMenuItem
      Caption = 'Neu...'
      OnClick = NeuNachMenuItemClick
    end
    object EditNachMenuItem: TMenuItem
      Caption = 'Bearbeiten...'
      OnClick = NachschubDBGridDblClick
    end
    object N24: TMenuItem
      Caption = '-'
    end
    object ImportNachMenuItem: TMenuItem
      Caption = 'Importieren...'
      OnClick = ImportNachMenuItemClick
    end
    object N23: TMenuItem
      Caption = '-'
    end
    object NachActivMenuItem: TMenuItem
      Caption = 'Aktivieren..'
    end
    object NachDeactivMenuItem: TMenuItem
      Caption = 'Deaktivieren...'
    end
    object N25: TMenuItem
      Caption = '-'
    end
    object DelNachMenuItem: TMenuItem
      Caption = 'L'#246'schen'
    end
  end
end
