object LeergutKontoChangeForm: TLeergutKontoChangeForm
  Left = 447
  Top = 167
  Anchors = [akRight, akBottom]
  BorderStyle = bsDialog
  Caption = 'Neues Ladehilfsmittelkonto anlegen'
  ClientHeight = 580
  ClientWidth = 365
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  DesignSize = (
    365
    580)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 152
    Top = 168
    Width = 59
    Height = 13
    Caption = 'Konto-Name'
  end
  object Label2: TLabel
    Left = 8
    Top = 376
    Width = 68
    Height = 13
    Caption = 'Konto-Besitzer'
  end
  object Label3: TLabel
    Left = 8
    Top = 216
    Width = 65
    Height = 13
    Caption = 'Beschreibung'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 317
    Width = 345
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel2: TBevel
    Left = 8
    Top = 530
    Width = 345
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object Label4: TLabel
    Left = 8
    Top = 112
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Bevel3: TBevel
    Left = 8
    Top = 160
    Width = 345
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label5: TLabel
    Left = 8
    Top = 56
    Width = 67
    Height = 13
    Caption = 'Niederlassung'
  end
  object Label11: TLabel
    Left = 8
    Top = 7
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Bevel4: TBevel
    Left = 3
    Top = 106
    Width = 362
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label6: TLabel
    Left = 8
    Top = 168
    Width = 42
    Height = 13
    Caption = 'Konto-Nr'
  end
  object Bevel5: TBevel
    Left = 8
    Top = 373
    Width = 345
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label7: TLabel
    Left = 8
    Top = 327
    Width = 189
    Height = 13
    Caption = 'Ladehilfsmittelbestand f'#252'r Untermandant'
  end
  object Bevel6: TBevel
    Left = 8
    Top = 261
    Width = 345
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label8: TLabel
    Left = 8
    Top = 267
    Width = 198
    Height = 13
    Caption = 'Ladehilfsmittelkonto f'#252'r diesen Lieferanten'
  end
  object NameEdit: TEdit
    Left = 152
    Top = 184
    Width = 201
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 4
    Text = 'NameEdit'
  end
  object OkButton: TButton
    Left = 192
    Top = 546
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 10
  end
  object AbortButton: TButton
    Left = 280
    Top = 546
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 11
  end
  object BeschreibungEdit: TEdit
    Left = 8
    Top = 232
    Width = 345
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 5
    Text = 'BeschreibungEdit'
  end
  object BesitzerMemo: TMemo
    Left = 8
    Top = 392
    Width = 345
    Height = 73
    Anchors = [akLeft, akTop, akRight]
    Lines.Strings = (
      'BesitzerMemo')
    TabOrder = 8
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 128
    Width = 345
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 80
    ItemHeight = 16
    TabOrder = 2
  end
  object MirrorCheckBox: TCheckBox
    Left = 8
    Top = 480
    Width = 345
    Height = 32
    Caption = 'Gespieltes Konto'#13#10'Buchung im WE als Abg'#228'nge und im WA als Zug'#228'ng'
    TabOrder = 9
    WordWrap = True
  end
  object LocComboBox: TComboBoxPro
    Left = 8
    Top = 72
    Width = 345
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 120
    AutoPrepare = False
    ColumeCount = 1
    ItemHeight = 16
    TabOrder = 1
    OnChange = LocComboBoxChange
  end
  object MandComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 345
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 120
    AutoPrepare = False
    ColumeCount = 1
    ItemHeight = 16
    TabOrder = 0
    OnChange = MandComboBoxChange
  end
  object NrEdit: TEdit
    Left = 8
    Top = 184
    Width = 129
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 3
    Text = 'NameEdit'
  end
  object BesSubmandComboBox: TComboBoxPro
    Left = 8
    Top = 344
    Width = 345
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 120
    AutoPrepare = False
    ColumeCount = 1
    ItemHeight = 16
    TabOrder = 7
  end
  object LieferantComboBox: TComboBoxPro
    Left = 8
    Top = 284
    Width = 345
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 120
    AutoPrepare = False
    ColumeCount = 1
    ItemHeight = 16
    TabOrder = 6
  end
end
