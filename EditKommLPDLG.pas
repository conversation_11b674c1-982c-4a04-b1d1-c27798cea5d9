unit EditKommLPDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls;

type
  TEditKommLPForm = class(TForm)
    AnzahlEdit: TEdit;
    Label1: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel1: TBevel;
    FolgeEdit: TEdit;
    Label2: TLabel;
    procedure NumEditKeyPress(Sender: TObject; var Key: Char);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

procedure TEditKommLPForm.NumEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in ['0'..'9', #8,^C,^V]) then
    Key := #0;
end;

end.
