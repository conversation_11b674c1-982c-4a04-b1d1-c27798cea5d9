﻿//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : ConfigModul
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/XE11/ConfigModul.pas $
// $Revision: 129 $
// $Modtime: 17.01.24 8:25 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Verwaltung der Anwendungs-Konfiguration
//*****************************************************************************
unit ConfigModul;

{$i compilers.inc}

interface

uses
  Types, SysUtils, Classes, Graphics, Forms, Grids, DBGridPro, Vcl.Themes;

type
  TDBGridConfig = record
    OddColor,
    EvenColor,
    TextColor,
    TitleColor,
    TitleBackgnd,
    HiddenSelColor  : TColor;
    AlternateColors : Boolean;
    TitleWrap       : Boolean;
    FontName,
    TitleFont       : String;
    FontSize,
    TitleSize       : Integer;
    GridFetchAll    : Boolean;
    GridAutoUpdate  : Boolean;
  end;

  TLagerConfig = record
  end;

  TLocationConfig = record
    Options              : String;
    FlagCheckEAN128Daten : Boolean;
  end;

  TMandantConfig = record
    Mandant                     : String;
    RefMandant                  : Integer;
    FlagAnnahmeUnterVorbehalt   : Boolean;
    FlagLieferantenVerwaltung   : Boolean;
    FlagWEOhneBestellung        : Boolean;
    FlagRETOhneBestellung       : Boolean;
    FlagChangeArtikelStammdaten : Boolean;
    FlagDMS                     : Boolean;
    OptConfig                   : String;
  end;

  TMandantInfo = record
    Mandant       : String;
    RefMandant    : Integer;
    Beschreibung  : String;
  end;

  TFrontendConfig = record
    cfgStatIcons : Boolean;
    cfgUsePrintingProcess  : Boolean;
    ScanErrSound : String;
  end;

  TPasswordRestriction = record
    MinLength    : Integer;
    AlphaCase    : boolean;
    AlphaNum     : boolean;
    SpecialChar  : boolean;
  end;

  TLVSConfigModul = class(TDataModule)
    procedure DataModuleCreate(Sender: TObject);
  private
    fKundenID          : Integer;
    fRefProject        : Integer;
    fProject           : String;
    fDisconnectTimeout : Integer;
    fLocation          : String;

    fTestFlag          : Boolean;
    fUseLocationListing: Boolean;
    fMaxPLSRuntime     : Integer;

    fUmgebung          : String;
    fDebugMode         : Boolean;
    fSectionItems      : TStringList;
    fSectionName       : String;
    fMasterRegKeyName  : String;
    fMasterLogPath     : String;
    fConfigPath        : String;
    fRegKeyName        : String;
    fUserRegSubKeyName : String;
    fDBUserPrefix      : String;
    fDBColInvisible    : String;
    fDBTraceLevel      : Integer;

    fUseQS            : Boolean;
    fUseMHD           : Boolean;
    fUseHerstellDatum : Boolean;
    fUseCharge        : Boolean;
    fUseORGAInvent    : Boolean;
    fUseSpedDepots    : Boolean;
    fUseSpedTour      : Boolean;
    fUseBisSpedTour   : Boolean;
    fUseArtikelSet    : Boolean;
    fUseColorSize     : Boolean;
    fUseGewicht       : Boolean;
    fUseInhaltArtikel : Boolean;
    fUseArtikelCollis : Boolean;
    fPrjArtikelCollis : Boolean;
    fUseVarianten     : Boolean;
    fUseSubMandanten  : Boolean;
    fUseTrader        : Boolean;
    fUsePfandgut      : Boolean;
    fUseLPTiefe       : Boolean;
    fUseWiegeID       : Boolean;
    fUseLifeCycle     : Boolean;
    fUseLTAuftrag     : Boolean;
    fUseAbrechnung    : Boolean;
    fUseMultiSelectBestellung : Boolean;
    fUseWENVE         : Boolean;
    fUseWEHU          : Boolean;
    fUseGewichtsWare  : Boolean;
    fUseArtikelUnit   : Boolean;
    fUseCrossDock     : Boolean;
    fUseBestandID     : Boolean;
    fUseBesCategory   : Boolean;
    fUseRetourenTab   : Boolean;
    fUseABCKlassen    : Boolean;
    fUseManfArtikelNr : Boolean;
    fUseSortiment     : Boolean;
    fUseGefahrgut     : Boolean;
    fUseKennzeichnung : Boolean;
    fUseBestandForcast: Boolean;
    fUseOrderTemplates: Boolean;
    fUseZollAbwicklung: Boolean;
    fUseMainArtikel   : Boolean;
    fUseChangeWEDatum : Boolean;
    fUseTeilLieferung : Boolean;
    fUseTeilPlanung   : Boolean;
    fUseVorRes        : Boolean;
    fUseValueServices : Boolean;

    fDoPrintJobs      : Boolean;

    fDoPrintLog       : Boolean;
    fDoSendITLog      : Boolean;
    fDoLabelPrintLog  : Integer;

    fUseLeitstand  : Boolean;
    fRefLeitstand  : Integer;
    fLeistandName  : String;
    fXPSFilePath   : String;

    {$ifndef LVS}
      fOSUserName : String;
    {$endif}

    fDashboardURL   : String;
    fDashboardLogin : String;

    fRemoteSupportProgram : String;

    fPasswordRestriction : TPasswordRestriction;

    procedure Init;

    procedure SetRefLeitstand (const RefLeitstand : Integer);
    procedure SetSectionName (const SectionName : String);
    procedure SetRegKeyName  (const RefKeyName : String);
  public
    DBGrids      : TDBGridConfig;

    FrontendConfig   : TFrontendConfig;
    LocationConfig   : TLocationConfig;

    property PasswordRestriction : TPasswordRestriction read fPasswordRestriction;

    //DBUserPrefix wird beim Login vor den Benutzernamen gesetzt
    property DBUserPrefix      : String      read fDBUserPrefix      write fDBUserPrefix;
    property Umgebung          : String      read fUmgebung          write fUmgebung;
    property SectionName       : String      read fSectionName       write SetSectionName;
    property MasterRegKeyName  : String      read fMasterRegKeyName  write fMasterRegKeyName;
    property RegKeyName        : String      read fRegKeyName        write SetRegKeyName;
    property UserRegSubKeyName : String      read fUserRegSubKeyName;
    property SectionItems      : TStringList read fSectionItems;
    property IsDebugMode       : Boolean     read fDebugMode;
    property DBColInvisible    : String      read fDBColInvisible;
    property DBTraceLavel      : Integer     read fDBTraceLevel;
    property DisconnectTimeout : Integer     read fDisconnectTimeout;

    property DashboardURL   : String read fDashboardURL;
    property DashboardLogin : String read fDashboardLogin;

    property RemoteSupportProgram : String read fRemoteSupportProgram;

    {$ifndef LVS}
      property OSUserName : String read fOSUserName;
    {$endif}


    property UseLeitstand  : boolean read fUseLeitstand write fUseLeitstand;
    property RefLeitstand  : Integer read fRefLeitstand write SetRefLeitstand;
    property LocationName  : String  read fLocation;
    property LeitstandName : String  read fLeistandName;

    property XPSFilePath   : String read fXPSFilePath;

    property RefProject       : Integer read fRefProject;
    property Project          : String  read fProject;

    property KundenID         : Integer read fKundenID;

    property TestFlag         : Boolean read fTestFlag;
    property MaxPLSRuntime    : Integer read fMaxPLSRuntime;

    property UseLocationListing : Boolean read fUseLocationListing;


    property DoPrintLog       : boolean read fDoPrintLog;
    property DoSendITLog      : boolean read fDoSendITLog;
    property DoLabelPrintLog  : Integer read fDoLabelPrintLog;
    property DoPrintJobs      : boolean read fDoPrintJobs;


    property UseMHD            : boolean read fUseMHD;
    property UseHerstellDatum  : boolean read fUseHerstellDatum;
    property UseCharge         : boolean read fUseCharge;
    property UseORGAInvent     : boolean read fUseORGAInvent;
    property UseQS             : boolean read fUseQS;
    property UseArtikelSet     : boolean read fUseArtikelSet;
    property UseColorSize      : boolean read fUseColorSize;
    property UseGewicht        : boolean read fUseGewicht;
    property UseInhaltArtikel  : boolean read fUseInhaltArtikel;
    property UseArtikelCollis  : boolean read fUseArtikelCollis;
    property PrjArtikelCollis  : boolean read fPrjArtikelCollis;
    property UseSpedDepots     : boolean read fUseSpedDepots;
    property UseSpedTour       : boolean read fUseSpedTour;
    property UseBisSpedTour    : boolean read fUseBisSpedTour;
    property UseVarianten      : boolean read fUseVarianten;
    property UseSubMandanten   : boolean read fUseSubMandanten;
    property UseTrader         : boolean read fUseTrader;
    property UsePfandgut       : boolean read fUsePfandgut;
    property UseLPTiefe        : boolean read fUseLPTiefe;
    property UseWiegeID        : boolean read fUseWiegeID;
    property UseLifeCycle      : boolean read fUseLifeCycle;
    property UseLTAuftrag      : boolean read fUseLTAuftrag;
    property UseAbrechnung     : boolean read fUseAbrechnung;
    property UseWENVE          : Boolean read fUseWENVE;
    property UseWEHU           : Boolean read fUseWEHU;
    property UseGewichtsWare   : Boolean read fUseGewichtsWare;
    property UseArtikelUnit    : Boolean read fUseArtikelUnit;
    property UseCrossDock      : Boolean read fUseCrossDock;
    property UseBestandID      : Boolean read fUseBestandID;
    property UseBesCategory    : boolean read fUseBesCategory;
    property UseMultiSelBest   : boolean read fUseMultiSelectBestellung;
    property UseRetourenTab    : boolean read fUseRetourenTab;
    property UseABCKlassen     : boolean read fUseABCKlassen;
    property UseGefahrgut      : boolean read fUseGefahrgut;
    property UseManfArtikelNr  : boolean read fUseManfArtikelNr;
    property UseSortiment      : boolean read fUseSortiment;
    property UseKennzeichnung  : boolean read fUseKennzeichnung;
    property UseBestandForcast : boolean read fUseBestandForcast;
    property UseOrderTemplates : boolean read fUseOrderTemplates;
    property UseZollAbwicklung : boolean read fUseZollAbwicklung;
    property UseMainArtikel    : boolean read fUseMainArtikel;
    property UseChangeWEDatum  : boolean read fUseChangeWEDatum;
    property UseTeilLieferung  : boolean read fUseTeilLieferung;
    property UseTeilPlanung    : boolean read fUseTeilPlanung;
    property UseVorRes         : boolean read fUseVorRes;
    property UseValueServices  : boolean read fUseValueServices;



    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;

    function SetLocationConfig (const RefLocation : Integer) : Integer;

    function ReadMandantConfig (const MandantName : String; var MandantConfig : TMandantConfig) : Integer; overload;
    function ReadMandantInfo   (const MandantName : String; var MandantInfo   : TMandantInfo) : Integer;

    function GetConfigDir     : String;
    function GetMasterLogDir  : String;
    function GetMasterDataDir : String;

    function GetSessionLogDir   (const Section : String = '') : String;
    function GetSessionDataDir  (const Section : String = '') : String;
    function GetUserFileDir     (const Section : String = '') : String;

    procedure ReadConfig;
    procedure WriteConfig;

    procedure SaveFormInfo      (Form : TCustomForm); overload;
    procedure SaveFormParameter (Form : TCustomForm; const ParamName : String; const ParamValue : String); overload;
    procedure SaveFormParameter (Form : TCustomForm; const ParamName : String; const ParamValue : Integer); overload;
    procedure SaveFormParameter (Form : TCustomForm; const ParamName : String; const ParamValue : Boolean); overload;
    procedure RestoreFormInfo   (Form : TCustomForm); overload;
    function  ReadFormParameter (Form : TCustomForm; const ParamName : String; var ParamValue : String; const DefaultValue : String = '') : Integer; overload;
    function  ReadFormParameter (Form : TCustomForm; const ParamName : String; var ParamValue : Integer; const DefaultValue : Integer = -1) : Integer; overload;
    function  ReadFormParameter (Form : TCustomForm; const ParamName : String; var ParamValue : Boolean; const DefaultValue : Boolean = False) : Integer; overload;
    function  CheckFormParameter(Form : TCustomForm; const ValueName : String) : Integer;

    procedure SaveGridInfo (Grid : TDBGridPro; const GridOwner : string = ''); overload;
    procedure SaveGridInfo (Owner : TForm; Grid : TStringGrid); overload;

    procedure RestoreGridInfo (Grid : TDBGridPro; const GridOwner : string = ''); overload;
    procedure RestoreGridInfo (Owner : TForm; Grid : TStringGrid); overload;

    procedure ConfigForm (Form : TForm = Nil);

    function CheckLocationOptions (const OptIdx : Integer) : Boolean;
  end;

var
  LVSConfigModul: TLVSConfigModul;

implementation

uses
  Windows, IniFiles, Printers,
  //LVSFrontLogin,
  RegistryUtils, TerminalServices,
  DatenModul, ADODB, ErrorTracking, LVSGlobalDaten, StringGridPro, ComCtrls, FrontendUtils;

{$R *.dfm}


//******************************************************************************
//* Function Name: MakePath
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Erzeugt ein Verzeichnis
//******************************************************************************
//* Return Value :
//******************************************************************************
PROCEDURE  MakePath (pathname : String);
Var
  strpos : Word;
  {$ifdef Debug} dbgstr : String; {$endif}
Begin
  If Not (DirectoryExists (pathname)) Then Begin
    strpos:=Length (pathname);
    While (strpos > 1) And (pathname [strpos] <> '\') And (pathname [strpos] <> ':') Do Dec (strpos);

    If (pathname [strpos] = '\') Then Begin
      MakePath (Copy (pathname,1,strpos - 1));

      {$ifdef Debug}
        dbgstr := 'pathname:>'+pathname+'<';
        OutputDebugString (PChar (@dbgstr));
      {$endif}

      MkDir (pathname);
    End;  (* of If *)
  End;  (* of If *)
End;


//******************************************************************************
//* Function Name: ReadConfig
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TLVSConfigModul.Create(AOwner: TComponent);
var
  {$ifndef LVS}
    csize : DWORD;
    cbuf  : array [0..255] of char;
  {$else}
    inifile    : TIniFile;
    baseconfig : TStringList;
  {$endif}
begin
  inherited Create (AOwner);

  Init;

  fSectionItems := TStringList.Create;


  {$ifndef LVS}
    csize := sizeof (cbuf) - 1;
    GetUserName (@cbuf, csize);
    fOSUserName := StrPas (cbuf);
  {$else}
    try
      {$ifdef StoreLogix}
        inifile := TIniFile.Create (ExtractFilePath (ParamStr (0))+'storelogix.ini');
      {$else}
        inifile := TIniFile.Create (ExtractFilePath (ParamStr (0))+'systeme.ini');
      {$endif}

      if Assigned (inifile) then begin
        fConfigPath       := inifile.ReadString ('Config', 'CfgFiles', '');

        baseconfig := TStringList.Create;

        try
          inifile.ReadSectionValues ('Config', baseconfig);

          if (baseconfig.IndexOfName ('TestSystem') <> -1) and (baseconfig.Values['TestSystem'] = '1') then
            fTestFlag := True;
        finally
          baseconfig.Free;
        end;

        inifile.Free;
      end;
    except
    end;
  {$endif}
end;

//******************************************************************************
//* Function Name: ReadConfig
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
destructor TLVSConfigModul.Destroy;
begin
  if Assigned (fSectionItems) then
    fSectionItems.Free;

  fSectionItems := Nil;

  inherited Destroy;
end;

//******************************************************************************
//* Function Name: Init
//* Author       : Stefan Graf
//* Datum        : 14.03.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.Init;
var
  tmppath : array [0..MAX_PATH] of char;
begin
  fDBTraceLevel  := 9;
  
  fDBColInvisible := '';

  {$ifdef DELPHIXE10_UP}
    Windows.GetTempPath (MAX_PATH, @tmppath);
  {$else}
    GetTempPath (sizeof (tmppath), tmppath);
  {$endif}

  fTestFlag         := False;
  fMaxPLSRuntime    := -1;

  fUseLocationListing := False;

  fXPSFilePath      := tmppath;

  fKundenID         := -1;
  fRefProject       := -1;
  fProject          := '';
  fDashboardURL     := '';
  fDashboardLogin   := '';
  fDisconnectTimeout:= -1;

  fRemoteSupportProgram := '';

  fUseQS            := False;
  fUseArtikelSet    := False;
  fUseColorSize     := False;
  fUseSubMandanten  := False;
  fUseTrader        := False;
  fUsePfandgut      := False;
  fUseHerstellDatum := False;
  fUseLPTiefe       := False;
  fUseWiegeID       := False;
  fUseLifeCycle     := False;
  fUseLTAuftrag     := False;
  fUseAbrechnung    := False;
  fUseMultiSelectBestellung := False;
  fUseWENVE         := True;
  fUseWEHU          := False;
  fUseGewichtsWare  := True;
  fUseArtikelUnit   := False;
  fUseSortiment     := False;
  fUseKennzeichnung := False;
  fUseBestandForcast:= False;
  fUseOrderTemplates:= False;
  fUseZollAbwicklung:= False;
  fUseMainArtikel   := False;
  fUseChangeWEDatum := False;
  fUseCrossDock     := False;
  fUseBestandID     := False;
  fUseBesCategory   := False;
  fUseRetourenTab   := False;
  fUseABCKlassen    := False;
  fUseGefahrgut     := False;
  fUseManfArtikelNr := False;
  fUseTeilLieferung := False;
  fUseTeilPlanung   := True;
  fUseVorRes        := False;
  fUseValueServices := False;

  fDoPrintLog       := False;
  fDoSendITLog      := False;
  fDoLabelPrintLog  := 0;
  fDoPrintJobs      := False;

  fUseMHD           := True;
  fUseCharge        := True;
  fUseORGAInvent    := True;
  fUseInhaltArtikel := True;
  fUseArtikelCollis := False;
  fPrjArtikelCollis := False;
  fUseGewicht       := True;
  fUseSpedDepots    := True;
  fUseSpedTour      := True;
  fUseBisSpedTour   := False;
  fUseVarianten     := True;

  fPasswordRestriction.MinLength   := 0;
  fPasswordRestriction.AlphaCase   := False;
  fPasswordRestriction.AlphaNum    := False;
  fPasswordRestriction.SpecialChar := False;
end;

//******************************************************************************
//* Function Name: ReadConfig
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.SetRegKeyName (const RefKeyName : String);
begin
  fRegKeyName := RefKeyName;
end;

//******************************************************************************
//* Function Name: ReadConfig
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.SetRefLeitstand (const RefLeitstand : Integer);
var
  query : TADOQuery;
begin
  if (fRefLeitstand <> RefLeitstand) then begin
    fRefLeitstand := RefLeitstand;

    if (fRefLeitstand = -1) then
      fLeistandName := ''
    else begin
      query := TADOQuery.Create (Nil);

      try
        query.Connection := LVSDatenModul.MainADOConnection;

        query.SQL.Add ('select REF,NAME from V_LEITSTAND where REF='+IntToStr (fRefLeitstand));

        try
          query.Open;

          fLeistandName := query.Fields [1].AsString;

          query.Close;
        except
          fRefLeitstand := -1;
          fLeistandName := '';
        end;
      finally
        query.Free;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name: SetSectionName
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.SetSectionName (const SectionName : String);
var
  path,
  valstr     : String;
  csvlist    : TStringList;
  baseconfig : TStringList;
  intwert    : Integer;
  inifile    : TIniFile;
begin
  if (fSectionName <> SectionName) then begin
    Init;

    fSectionName := SectionName;

    fSectionItems.Clear;

    try
      inifile := Nil;

      try
        {$ifdef StoreLogix}
          inifile := TIniFile.Create (ExtractFilePath (ParamStr (0))+'storelogix.ini');
        {$else}
          inifile := TIniFile.Create (ExtractFilePath (ParamStr (0))+'systeme.ini');
        {$endif}

        baseconfig := TStringList.Create;

        try
          inifile.ReadSectionValues ('Config', baseconfig);

          valstr := baseconfig.Values ['RemoteSupportProgram'];
          if (Length (valstr) > 0) then begin
            fRemoteSupportProgram := valstr;
          end;

          if (baseconfig.IndexOfName ('TestSystem') <> -1) and (baseconfig.Values['TestSystem'] = '1') then
            fTestFlag := True
          else if (baseconfig.IndexOfName ('TestUser') <> -1) and (Pos (UpperCase (OSUserName), UpperCase (baseconfig.Values['TestUser'])) > 0) then
            fTestFlag := True;

          if fTestFlag then begin
            if (baseconfig.IndexOfName ('MaxPLSRuntime') <> -1) then begin
              if TryStrToInt (baseconfig.Values ['MaxPLSRuntime'], intwert) then
                fMaxPLSRuntime := intwert;
            end;
          end;
        finally
          baseconfig.Free;
        end;

        if (Length (fSectionName) > 0) then begin
          inifile.ReadSectionValues (SectionName, fSectionItems);

          fDBUserPrefix := fSectionItems.Values ['DBUserPrefix'];

          //Die Passwort-Restriktionen auslesen
          //Format: MaxLen;1=AlphaCase;1=AlphaNum;1=SpecialChar
          valstr := fSectionItems.Values ['PasswordRestriction'];

          if (Length (valstr) > 0) then begin
            csvlist := TStringList.Create;
            csvlist.Delimiter := ';';
            csvlist.DelimitedText := valstr;

            if TryStrToInt (csvlist [0], intwert) and (intwert > 0) then begin
              fPasswordRestriction.MinLength   := intwert;

              if (csvlist.Count > 1) and (csvlist [1] = '1') then
                fPasswordRestriction.AlphaCase   := True;

              if (csvlist.Count > 2) and (csvlist [2] = '1') then
                fPasswordRestriction.AlphaNum    := True;

              if (csvlist.Count > 3) and (csvlist [3] = '1') then
                fPasswordRestriction.SpecialChar := True;
            end;
          end;

          valstr := fSectionItems.Values ['DebugMode'];
          if (valstr = '1') then
            fDebugMode := True
          else fDebugMode := False;

          path := fSectionItems.Values ['XPSFilePath'];
          if (Length (path) > 0) then begin
            MakePath (path);

            fXPSFilePath := path;
          end;

          path := fSectionItems.Values ['ErrorLog'];
          if (Length (path) > 0) then begin
            if (path [Length (path)] <> '\') then path := path + '\';

            MakePath (path);
          end;

          valstr := fSectionItems.Values ['DBTraceLevel'];
          if (Length (valstr) > 0) then begin
            if TryStrToInt (valstr, intwert) then
              fDBTraceLevel := intwert;
          end;

          fDBColInvisible := fSectionItems.Values ['DBColInvisible'];

          valstr := fSectionItems.Values ['KundenID'];
          if (Length (valstr) > 0) then begin
            if TryStrToInt (valstr, intwert) then
              fKundenID := intwert;
          end;

          valstr := fSectionItems.Values ['Project'];
          if (Length (valstr) > 0) then begin
            fProject := valstr;
          end;

          valstr := fSectionItems.Values ['DashboardURL'];
          if (Length (valstr) > 0) then begin
            fDashboardURL := valstr;
          end;

          valstr := fSectionItems.Values ['DashboardLogin'];
          if (Length (valstr) > 0) then begin
            fDashboardLogin := valstr;
          end;

          valstr := fSectionItems.Values ['RefProject'];
          if (Length (valstr) > 0) then begin
            if TryStrToInt (valstr, intwert) then
              fRefProject := intwert;
          end;

          valstr := fSectionItems.Values ['DisconnectTimeout'];
          if (Length (valstr) > 0) then begin
            if TryStrToInt (valstr, intwert) then
              fDisconnectTimeout := intwert;
          end;

          valstr := fSectionItems.Values ['UseQS'];
          if (valstr = '') then
            fUseQS := False
          else if (valstr = '1') then
            fUseQS := True
          else
            fUseQS := False;

          valstr := fSectionItems.Values ['UseGewicht'];
          if (valstr = '') or (valstr = '1') then
            fUseGewicht := True
          else
            fUseGewicht := False;

          valstr := fSectionItems.Values ['UseInhaltArtikel'];
          if (valstr = '') or (valstr = '1') then
            fUseInhaltArtikel := True
          else
            fUseInhaltArtikel := False;

          valstr := fSectionItems.Values ['UseArtikelCollis'];
          if (valstr = '1') then
            fPrjArtikelCollis := True
          else
            fPrjArtikelCollis := False;
          fUseArtikelCollis := fPrjArtikelCollis;

          valstr := fSectionItems.Values ['UseMHD'];
          if (valstr = '') or (valstr = '1') then
            fUseMHD := True
          else
            fUseMHD := False;

          valstr := fSectionItems.Values ['UseCharge'];
          if (valstr = '') or (valstr = '1') then
            fUseCharge := True
          else
            fUseCharge := False;

          valstr := fSectionItems.Values ['UseORGAInvent'];
          if (valstr = '') or (valstr = '1') then
            fUseORGAInvent := True
          else
            fUseORGAInvent := False;

          valstr := fSectionItems.Values ['UseSpedDepots'];
          if (valstr = '') or (valstr = '1') then
            fUseSpedDepots := True
          else
            fUseSpedDepots := False;

          valstr := fSectionItems.Values ['UseSpedTour'];
          if (valstr = '') or (valstr = '1') then
            fUseSpedTour := True
          else
            fUseSpedTour := False;

          valstr := fSectionItems.Values ['UseBisSpedTour'];
          if (valstr = '') or (valstr = '1') then
            fUseBisSpedTour := True
          else
            fUseBisSpedTour := False;

          valstr := fSectionItems.Values ['UseVarianten'];
          if (valstr = '') or (valstr = '1') then
            fUseVarianten := True
          else
            fUseVarianten := False;

          valstr := fSectionItems.Values ['UseColorSize'];
          if (valstr = '1') then
            fUseColorSize := True
          else
            fUseColorSize := False;

          valstr := fSectionItems.Values ['UseArtikelSet'];
          if (valstr = '1') then
            fUseArtikelSet := True
          else
            fUseArtikelSet := False;

          valstr := fSectionItems.Values ['UseSubMandanten'];
          if (valstr = '1') then
            fUseSubMandanten := True
          else
            fUseSubMandanten := False;

          valstr := fSectionItems.Values ['UseTrader'];
          if (valstr = '1') then
            fUseTrader := True
          else
            fUseTrader := False;

          valstr := fSectionItems.Values ['UsePfandgut'];
          if (valstr = '1') then
            fUsePfandgut := True
          else
            fUsePfandgut := False;

          valstr := fSectionItems.Values ['UseHerstellDatum'];
          if (valstr = '1') then
            fUseHerstellDatum := True
          else
            fUseHerstellDatum := False;

          valstr := fSectionItems.Values ['UseLPTiefe'];
          if (valstr = '1') then
            fUseLPTiefe := True
          else
            fUseLPTiefe := False;

          valstr := fSectionItems.Values ['UseWiegeID'];
          if (valstr = '1') then
            fUseWiegeID := True
          else
            fUseWiegeID := False;

          valstr := fSectionItems.Values ['UseLifeCycle'];
          if (valstr = '1') then
            fUseLifeCycle := True
          else
            fUseLifeCycle := False;

          valstr := fSectionItems.Values ['UseLTAuftrag'];
          if (valstr = '1') then
            fUseLTAuftrag := True
          else
            fUseLTAuftrag := False;

          valstr := fSectionItems.Values ['UseArtikelUnit'];
          if (valstr = '1') then
            fUseArtikelUnit := True
          else
            fUseArtikelUnit := False;

          valstr := fSectionItems.Values ['UseSortiment'];
          if (valstr = '1') then
            fUseSortiment := True
          else
            fUseSortiment := False;

          valstr := fSectionItems.Values ['UseGefahrgut'];
          if (valstr = '1') then
            fUseGefahrgut := True
          else
            fUseGefahrgut := False;

          valstr := fSectionItems.Values ['UseKennzeichnung'];
          if (valstr = '1') then
            fUseKennzeichnung := True
          else
            fUseKennzeichnung := False;

          valstr := fSectionItems.Values ['UseCrossDock'];
          if (valstr = '1') then
            fUseCrossDock := True
          else
            fUseCrossDock := False;

          valstr := fSectionItems.Values ['UseBestandID'];
          if (valstr = '1') then
            fUseBestandID := True
          else
            fUseBestandID := False;

          valstr := fSectionItems.Values ['UseBesCategory'];
          if (valstr = '1') then
            fUseBesCategory := True
          else
            fUseBesCategory := False;


          valstr := fSectionItems.Values ['UseAbrechnung'];
          if (valstr = '1') then
            fUseAbrechnung := True
          else
            fUseAbrechnung := False;

          valstr := fSectionItems.Values ['UseRetourenTab'];
          if (valstr = '1') then
            fUseRetourenTab := True
          else
            fUseRetourenTab := False;

          valstr := fSectionItems.Values ['UseABCKlassen'];
          if (valstr = '1') then
            fUseABCKlassen := True
          else
            fUseABCKlassen := False;

          valstr := fSectionItems.Values ['UseManfArtikelNr'];
          if (valstr = '1') then
            fUseManfArtikelNr := True
          else
            fUseManfArtikelNr := False;

          valstr := fSectionItems.Values ['UseMultiSelectBestellung'];
          if (valstr = '1') then
            fUseMultiSelectBestellung := True
          else
            fUseMultiSelectBestellung := False;

          valstr := fSectionItems.Values ['UseWENVE'];
          if (valstr = '') or (valstr = '1') then
            fUseWENVE := True
          else
            fUseWENVE := False;

          valstr := fSectionItems.Values ['UseWEHU'];
          if (valstr = '1') then
            fUseWEHU := True
          else
            fUseWEHU := False;

          valstr := fSectionItems.Values ['UseGewichtsWare'];
          if (valstr = '') or (valstr = '1') then
            fUseGewichtsWare := True
          else
            fUseGewichtsWare := False;

          valstr := fSectionItems.Values ['UseOrderTemplates'];
          if (valstr = '1') then
            fUseOrderTemplates := True
          else
            fUseOrderTemplates := False;

          valstr := fSectionItems.Values ['UseZollAbwicklung'];
          if (valstr = '1') then
            fUseZollAbwicklung := True
          else
            fUseZollAbwicklung := False;

          valstr := fSectionItems.Values ['UseMainArtikel'];
          if (valstr = '1') then
            fUseMainArtikel := True
          else
            fUseMainArtikel := False;
          valstr := fSectionItems.Values ['UseMainArtikel'];
          if (valstr = '1') then
            fUseMainArtikel := True
          else
            fUseMainArtikel := False;

          valstr := fSectionItems.Values ['UseChangeWEDatum'];
          if (valstr = '1') then
            fUseChangeWEDatum := True
          else
            fUseChangeWEDatum := False;

          valstr := fSectionItems.Values ['UseTeilLieferung'];
          if (valstr = '1') then
            fUseTeilLieferung := True
          else
            fUseTeilLieferung := False;

          //Default ist True
          valstr := fSectionItems.Values ['UseTeilPlanung'];
          if (valstr = '') or (valstr = '1') then
            fUseTeilPlanung := True
          else
            fUseTeilPlanung := False;

          valstr := fSectionItems.Values ['UseVorRes'];
          if (valstr = '1') then
            fUseVorRes := True
          else
            fUseVorRes := False;

          valstr := fSectionItems.Values ['UseValueServices'];
          if (valstr = '1') then
            fUseValueServices := True
          else
            fUseValueServices := False;

          valstr := fSectionItems.Values ['DoPrintLog'];
          if (valstr = '1') then
            fDoPrintLog := True
          else
            fDoPrintLog := False;

          valstr := fSectionItems.Values ['DoSendITLog'];
          if (valstr = '1') then
            fDoSendITLog := True
          else
            fDoSendITLog := False;

          valstr := fSectionItems.Values ['DoLabelPrintLog'];
          if (valstr = '1') then
            fDoLabelPrintLog := 1
          else if (valstr = '2') then
            fDoLabelPrintLog := 2
          else
            fDoLabelPrintLog := 0;

          valstr := fSectionItems.Values ['DoPrintJobs'];
          if (valstr = '1') then
            fDoPrintJobs := True
          else
            fDoPrintJobs := False;

          if Assigned (ErrorTrackingModule) then
            ErrorTrackingModule.ErrorLogFile := path + ChangeFileExt (ExtractFileName (ExtractFileName (ParamStr (0))), '.err');
        end;
      finally
        inifile.Free;
      end;
    except
    end;
  end;
end;

//******************************************************************************
//* Function Name: ReadConfig
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.ReadConfig;
var
  intwert : Integer;
  strwert : String;
  subreg  : TRegistryModule;
begin
  subreg := TRegistryModule.Create;

  try
    if (subreg.OpenKey (HKEY_CURRENT_USER, fRegKeyName, KEY_READ, False) = 0) Then begin
      if (subreg.ReadRegValue ('StatIcons', intwert) = 0) then
        FrontendConfig.cfgStatIcons := (intwert <> 0);

      if (subreg.ReadRegValue ('UsePrintingProcess', intwert) = 0) then
        FrontendConfig.cfgUsePrintingProcess := (intwert <> 0);

      FrontendConfig.ScanErrSound := subreg.ReadRegValue ('ScanErrSound');

      if (Length (FrontendConfig.ScanErrSound) = 0) then
        FrontendConfig.ScanErrSound := GetEnvironmentVariable ('SystemRoot') + '\Media\chimes.wav';

      if (subreg.ReadRegValue ('Style', intwert) = 0 ) then begin
        case intwert of
        1 : TStyleManager.TrySetStyle('Windows10');
        2 : TStyleManager.TrySetStyle('Windows10 Dark');
        3 : TStyleManager.TrySetStyle('Windows10 SlateGray');
        4 : TStyleManager.TrySetStyle('Windows10 Blue');
        5 : TStyleManager.TrySetStyle('Windows10 Green');
        6 : TStyleManager.TrySetStyle('Windows10 Purple');
        end;
      end;
    end;
  finally
    subreg.Free;
  end;

  DBGrids.GridFetchAll   := True;
  DBGrids.GridAutoUpdate := True;
  DBGrids.AlternateColors:= False;
  DBGrids.OddColor       := clWindow;
  DBGrids.TextColor      := clWindowText;
  DBGrids.EvenColor      := clWindow;
  DBGrids.HiddenSelColor := clInactiveCaption;
  DBGrids.FontName       := '';
  DBGrids.FontSize       := 10;

  DBGrids.TitleWrap  := True;
  DBGrids.TitleFont  := '';
  DBGrids.TitleSize  := 10;
  DBGrids.TitleColor := clWindowText;

  subreg := TRegistryModule.Create;

  Try
    if (subreg.OpenKey (HKEY_CURRENT_USER, fRegKeyName + '\DBGrids', KEY_READ, False) = 0) Then begin
      with DBGrids do begin
        if (subreg.ReadRegValue ('OddColor', intwert) = 0) then
          OddColor := TColor (intwert);

        if (subreg.ReadRegValue ('HiddenSelColor', intwert) = 0) then
          HiddenSelColor := TColor (intwert);

        if (subreg.ReadRegValue ('TextColor', intwert) = 0) then
          TextColor := TColor (intwert);

        if (subreg.ReadRegValue ('EvenColor', intwert) = 0) then
          EvenColor := TColor (intwert);

        if (subreg.ReadRegValue ('AlternateColors', AlternateColors) <> 0) Then
          AlternateColors := False;

        if (subreg.ReadRegValue ('FontSize', intwert) = 0) Then
          FontSize := intwert;

        if (subreg.ReadRegValue ('FontName', strwert) = 0) Then
          FontName := strwert;

        if (subreg.ReadRegValue ('TitleSize', intwert) = 0) Then
          TitleSize := intwert;

        if (subreg.ReadRegValue ('TitleFont', strwert) = 0) Then
          TitleFont := strwert;

        if (subreg.ReadRegValue ('TitleColor', intwert) = 0) then
          TitleColor := TColor (intwert);

        if (subreg.ReadRegValue ('TitleWrap', TitleWrap) <> 0) Then
          TitleWrap := False;

        if (subreg.ReadRegValue ('GridFetchAll', GridFetchAll) <> 0) Then
          GridFetchAll := True;

        if (subreg.ReadRegValue ('GridAutoUpdate', GridAutoUpdate) <> 0) Then
          GridAutoUpdate := True;
      end;
    end;
  Finally
    subreg.Free;
  End;
end;

//******************************************************************************
//* Function Name: WriteConfig
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.WriteConfig;
var
  subreg : TRegistryModule;
begin
  subreg := TRegistryModule.Create;

  try
    if (subreg.OpenKey (HKEY_CURRENT_USER, fRegKeyName, KEY_READ or KEY_WRITE, True) = 0) Then begin
      subreg.WriteRegValue ('StatIcons', Ord (FrontendConfig.cfgStatIcons));
      subreg.WriteRegValue ('UsePrintingProcess', Ord (FrontendConfig.cfgUsePrintingProcess));
      subreg.WriteRegValue ('ScanErrSound', FrontendConfig.ScanErrSound);
    end;
  finally
    subreg.Free;
  end;

  subreg := TRegistryModule.Create;
  try
    if (subreg.OpenKey (HKEY_CURRENT_USER, fRegKeyName + '\DBGrids', KEY_READ or KEY_WRITE, True) = 0) Then begin
      with DBGrids do begin
        subreg.WriteRegValue ('OddColor', Ord (OddColor));
        subreg.WriteRegValue ('TextColor', Ord (TextColor));
        subreg.WriteRegValue ('HiddenSelColor', Ord (HiddenSelColor));
        subreg.WriteRegValue ('EvenColor', Ord (EvenColor));
        subreg.WriteRegValue ('AlternateColors', AlternateColors);
        subreg.WriteRegValue ('FontSize', FontSize);
        subreg.WriteRegValue ('FontName', FontName);
        subreg.WriteRegValue ('TitleWrap', TitleWrap);
        subreg.WriteRegValue ('TitleColor', Ord (TitleColor));
        subreg.WriteRegValue ('TitleFont', TitleFont);
        subreg.WriteRegValue ('TitleSize', TitleSize);
        subreg.WriteRegValue ('GridFetchAll', GridFetchAll);
        subreg.WriteRegValue ('GridAutoUpdate', GridAutoUpdate);
      end;
    end;
  finally
    subreg.Free;
  end;
end;

//******************************************************************************
//* Function Name: GetConfigDir
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert einen Path zum Config-Verzeichnis.
//*                Da liget dann z. B. die lvsmain.xml
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.GetConfigDir : String;
var
  ts      : TTerminalService;
  logdir,
  basedir : String;
begin
  basedir := fConfigPath;

  //Wenn kein Log-Verzeichniss definiert ist, wird der aktuelle Programmpath genommen
  if (Length (basedir) = 0) then
    basedir := ExtractFilePath (ParamStr (0));

  //Das letzte Zeichen muss ein \ sein
  if (basedir [Length (basedir)] <> '\') then
    basedir := basedir + '\';

  Result := basedir;
end;

//******************************************************************************
//* Function Name: GetSessionLogDir
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert einen Path zum Logging-Verzeichnis.
//*                Es wird geprüft ob es sich um eine Terminalserver-Session
//*                handelt. Wenn ja, wird für jeden Client ein eigenes
//*                Unterverzeichnis angelegt.
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.GetMasterLogDir : String;
var
  ts      : TTerminalService;
  logdir,
  basedir : String;
begin
  basedir := fMasterLogPath;

  //Wenn kein Log-Verzeichniss definiert ist, wird der aktuelle Programmpath genommen
  if (Length (basedir) = 0) then
    basedir := ExtractFilePath (ParamStr (0));

  //Das letzte Zeichen muss ein \ sein
  if (basedir [Length (basedir)] <> '\') then
    basedir := basedir + '\';

  ts := TTerminalService.Create;

  try
    if (ts.IsRemoteSession) then begin
      logdir := basedir + 'Log\' + OSUserName + '\';
    end else begin
      logdir := basedir + 'Log\';
    end;
  finally
    ts.Free;
  end;

  Result := logdir;
end;

//******************************************************************************
//* Function Name: GetMasterDataDir
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert einen Path zum Daten-Verzeichnis.
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.GetMasterDataDir : String;
var
  logdir,
  basedir : String;
begin
  basedir := fMasterLogPath;

  //Wenn kein Log-Verzeichniss definiert ist, wird der aktuelle Programmpath genommen
  if (Length (basedir) = 0) then
    basedir := ExtractFilePath (ParamStr (0));

  //Das letzte Zeichen muss ein \ sein
  if (basedir [Length (basedir)] <> '\') then
    basedir := basedir + '\';

  logdir := basedir + 'Daten\';

  Result := logdir;
end;

//******************************************************************************
//* Function Name: GetSessionLogDir
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert einen Path zum Logging-Verzeichnis.
//*                Es wird geprüft ob es sich um eine Terminalserver-Session
//*                handelt. Wenn ja, wird für jeden Client ein eigenes
//*                Unterverzeichnis angelegt.
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.GetSessionLogDir (const Section : String) : String;
var
  ts      : TTerminalService;
  logdir,
  basedir : String;

  {$ifdef Debug} dbgstr : String; {$endif}
begin
  basedir := fSectionItems.Values['LogFiles'];

  //Wenn kein Log-Verzeichniss definiert ist, wird der aktuelle Programmpath genommen
  if (Length (basedir) = 0) then
    basedir := ExtractFilePath (ParamStr (0));

  //Das letzte Zeichen muss ein \ sein
  if (basedir [Length (basedir)] <> '\') then
    basedir := basedir + '\';

  if (Length (fMasterLogPath) = 0) then
    fMasterLogPath := basedir;

  if (Length (Section) > 0) then
    logdir := basedir + 'Log\' + Section + '\'
  else begin
    ts := TTerminalService.Create;

    try
      if (ts.IsRemoteSession) then begin
        (*
          {$ifdef Debug}
            dbgstr := 'ts.ClientName:>'+ts.ClientName+'<';
            OutputDebugString (PAnsiChar (@dbgstr[1]));
          {$endif}

          logdir := basedir + 'Log\' + ts.ClientName + '\';
        *)

        {$ifdef Debug}
          dbgstr := 'UserName:>'+OSUserName+'<';
          OutputDebugString (PChar (@dbgstr[1]));
        {$endif}

        logdir := basedir + 'Log\' + OSUserName + '\';
      end else begin
        logdir := basedir + 'Log\';
      end;
    finally
      ts.Free;
    end;
  end;

  {$ifdef Debug}
    dbgstr := 'logdir:>'+logdir+'<';
    OutputDebugString (PChar (@dbgstr[1]));
  {$endif}

  //Ggf. das Verzeichnis anlegen
  if not (FileExists (logdir)) then
    MakePath (logdir);

  Result := logdir;
end;

//******************************************************************************
//* Function Name: GetSessionDataDir
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert einen Path zum Daten-Verzeichnis.
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.GetSessionDataDir (const Section : String) : String;
var
  basedir : String;
begin
  basedir := fSectionItems.Values['DataFiles'];

  //Wenn kein Datenfile-Verzeichniss definiert ist, dann den DatenPath suchen
  if (Length (basedir) = 0) then
    basedir := fSectionItems.Values['DatenPath'];

  //Wenn kein Daten-Verzeichniss definiert ist, wird der aktuelle Programmpath genommen
  if (Length (basedir) = 0) then
    basedir := ExtractFilePath (ParamStr (0));

  //Das letzte Zeichen muss ein \ sein
  if (basedir [Length (basedir)] <> '\') then
    basedir := basedir + '\';

  Result := basedir;
end;

//******************************************************************************
//* Function Name: GetUserFileDir
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert einen Path zum Verzeichnis für Benutzer-Files
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.GetUserFileDir (const Section : String) : String;
var
  basedir : String;
begin
  basedir := fSectionItems.Values['UserFiles'];

  //Das letzte Zeichen muss ein \ sein
  if (Length (basedir) > 0) and (basedir [Length (basedir)] <> '\') then
    basedir := basedir + '\';

  Result := basedir;
end;

//******************************************************************************
//* Function Name: SaveFormInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.SaveFormInfo (Form : TCustomForm);
var
  keyname : String;
  subreg  : TRegistryModule;
begin
  if (Length (fLocation) = 0) then
    keyname := fRegKeyName + '\Forms\' + Form.Name
  else keyname := fRegKeyName + '\' + fLocation + '\Forms\' + Form.Name;

  subreg := TRegistryModule.Create;
  try
    if (subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ or KEY_WRITE, True) = 0) Then begin
      subreg.WriteRegValue ('WinState', Ord (Form.WindowState));
      subreg.WriteRegValue ('Top', Form.Top);
      subreg.WriteRegValue ('Left', Form.Left);
      subreg.WriteRegValue ('Width', Form.Width);
      subreg.WriteRegValue ('Height', Form.Height);
    end;
  finally
    subreg.Free;
  end;
end;

//******************************************************************************
//* Function Name: SaveFormInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.SaveFormParameter (Form : TCustomForm; const ParamName : String; const ParamValue : String);
var
  keyname : String;
  subreg : TRegistryModule;
begin
  if (Length (fLocation) = 0) then
    keyname := fRegKeyName + '\Forms\' + Form.Name
  else keyname := fRegKeyName + '\' + fLocation + '\Forms\' + Form.Name;

  subreg := TRegistryModule.Create;
  try
    if (subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ or KEY_WRITE, True) = 0) Then begin
      subreg.WriteRegValue (ParamName, ParamValue);
    end;
  finally
    subreg.Free;
  end;
end;

//******************************************************************************
//* Function Name: SaveFormInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.SaveFormParameter (Form : TCustomForm; const ParamName : String; const ParamValue : Integer);
var
  keyname : String;
  subreg : TRegistryModule;
begin
  if (Length (fLocation) = 0) then
    keyname := fRegKeyName + '\Forms\' + Form.Name
  else keyname := fRegKeyName + '\' + fLocation + '\Forms\' + Form.Name;

  subreg := TRegistryModule.Create;
  try
    if (subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ or KEY_WRITE, True) = 0) Then begin
      subreg.WriteRegValue (ParamName, ParamValue);
    end;
  finally
    subreg.Free;
  end;
end;

//******************************************************************************
//* Function Name: SaveFormInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.SaveFormParameter (Form : TCustomForm; const ParamName : String; const ParamValue : Boolean);
var
  keyname : String;
  subreg : TRegistryModule;
begin
  if (Length (fLocation) = 0) then
    keyname := fRegKeyName + '\Forms\' + Form.Name
  else keyname := fRegKeyName + '\' + fLocation + '\Forms\' + Form.Name;

  subreg := TRegistryModule.Create;
  try
    if (subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ or KEY_WRITE, True) = 0) Then begin
      subreg.WriteRegValue (ParamName, ParamValue);
    end;
  finally
    subreg.Free;
  end;
end;

//******************************************************************************
//* Function Name: RestoreFormInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.CheckFormParameter (Form : TCustomForm; const ValueName : String) : Integer;
var
  res     : Integer;
  subreg  : TRegistryModule;
  keyname : STring;
begin
  if (Length (fLocation) = 0) then
    keyname := fRegKeyName + '\Forms\' + Form.Name
  else keyname := fRegKeyName + '\' + fLocation + '\Forms\' + Form.Name;

  subreg := TRegistryModule.Create;

  try
    res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);

    if (res <> 0) and (Length (fLocation) > 0)  then begin
      keyname := fRegKeyName + '\Forms\' + Form.Name;

      res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);
    end;

    if (res <> 0) then
      Result := -1
    else begin
      Result := subreg.CheckRegValue (ValueName);
    end;
  finally
    subreg.Free;
  end;
end;

//******************************************************************************
//* Function Name: RestoreFormInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.RestoreFormInfo (Form : TCustomForm);
var
  subreg  : TRegistryModule;
  keyname : String;
  res,
  intwert : Integer;
  winstat : TWindowState;
  desrect : TRect;
begin
  UserLog.Write ('RestoreFormInfo: '+Form.Name);
  UserLog.Write ('    Create: Top='+IntToStr (Form.Top)+', Left='+IntToStr (Form.Left)+', Width='+IntToStr (Form.Width)+', Height='+IntToStr (Form.Height));

  if (Length (fLocation) = 0) then
    keyname := fRegKeyName + '\Forms\' + Form.Name
  else keyname := fRegKeyName + '\' + fLocation + '\Forms\' + Form.Name;

  subreg := TRegistryModule.Create;

  try
    res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);

    if (res <> 0) and (Length (fLocation) > 0)  then begin
      keyname := fRegKeyName + '\Forms\' + Form.Name;

      res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);
    end;

    if (res = 0) then begin
      if (subreg.ReadRegValue ('WinState', intwert) <> 0) then
        winstat := wsNormal
      else winstat := TWindowState (intwert);

      Form.WindowState := winstat;

      if (subreg.ReadRegValue ('Top', intwert) = 0) Then
        //Form.Top := intwert;
      if (subreg.ReadRegValue ('Left', intwert) = 0) Then
        //Form.Left := intwert;
      if (subreg.ReadRegValue ('Width', intwert) = 0) and (intwert > 0) Then
        Form.Width := intwert;
      if (subreg.ReadRegValue ('Height', intwert) = 0) and (intwert > 0) Then
        Form.Height := intwert;
    end;

    UserLog.Write ('       Reg: Top='+IntToStr (Form.Top)+', Left='+IntToStr (Form.Left)+', Width='+IntToStr (Form.Width)+', Height='+IntToStr (Form.Height));

    // Prüfung ob das Fenster noch im Desktop liegt
    desrect := Screen.DesktopRect;
    if Form.Left < desrect.Left then
    begin
      Form.MakeFullyVisible(Screen.Monitors[0]);

      UserLog.Write ('      Left: Top='+IntToStr (Form.Top)+', Left='+IntToStr (Form.Left)+', Width='+IntToStr (Form.Width)+', Height='+IntToStr (Form.Height));
    end;
    if Form.Left > desrect.Width then
    begin
      Form.MakeFullyVisible(Screen.Monitors[Screen.MonitorCount - 1]);

      UserLog.Write ('     Width: Top='+IntToStr (Form.Top)+', Left='+IntToStr (Form.Left)+', Width='+IntToStr (Form.Width)+', Height='+IntToStr (Form.Height));
    end;

  finally
    subreg.Free;
  end;
end;

//******************************************************************************
//* Function Name: RestoreFormInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.ReadFormParameter (Form : TCustomForm; const ParamName : String; var ParamValue : String; const DefaultValue : String) : Integer;
var
  subreg  : TRegistryModule;
  strwert,
  keyname : String;
  res     : Integer;
begin
  if (Length (fLocation) = 0) then
    keyname := fRegKeyName + '\Forms\' + Form.Name
  else keyname := fRegKeyName + '\' + fLocation + '\Forms\' + Form.Name;

  subreg := TRegistryModule.Create;

  try
    res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);

    if (res <> 0) and (Length (fLocation) > 0) then begin
      keyname := fRegKeyName + '\Forms\' + Form.Name;

      res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);
    end;

    if (res <> 0) then
      Result := -1
    else begin
      Result := 0;

      if (subreg.ReadRegValue (ParamName, strwert) = 0) then
        ParamValue := strwert
      else
        ParamValue := DefaultValue;
    end;
  finally
    subreg.Free;
  end;
end;

//******************************************************************************
//* Function Name: RestoreFormInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.ReadFormParameter (Form : TCustomForm; const ParamName : String; var ParamValue : Integer; const DefaultValue : Integer) : Integer;
var
  subreg  : TRegistryModule;
  keyname : String;
  intwert : Integer;
  res     : Integer;
begin
  if (Length (fLocation) = 0) then
    keyname := fRegKeyName + '\Forms\' + Form.Name
  else keyname := fRegKeyName + '\' + fLocation + '\Forms\' + Form.Name;

  subreg := TRegistryModule.Create;

  try
    res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);

    if (res <> 0) and (Length (fLocation) > 0)  then begin
      keyname := fRegKeyName + '\Forms\' + Form.Name;

      res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);
    end;

    if (res <> 0) then
      Result := -1
    else begin
      Result := 0;

      if (subreg.ReadRegValue (ParamName, intwert) = 0) then
        ParamValue := intwert
      else
        ParamValue := DefaultValue;
    end;
  finally
    subreg.Free;
  end;
end;

//******************************************************************************
//* Function Name: RestoreFormInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.ReadFormParameter (Form : TCustomForm; const ParamName : String; var ParamValue : Boolean; const DefaultValue : Boolean) : Integer;
var
  subreg   : TRegistryModule;
  keyname  : String;
  boolwert : boolean;
  res      : Integer;
begin
  if (Length (fLocation) = 0) then
    keyname := fRegKeyName + '\Forms\' + Form.Name
  else keyname := fRegKeyName + '\' + fLocation + '\Forms\' + Form.Name;

  subreg := TRegistryModule.Create;

  try
    res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);

    if (res <> 0) and (Length (fLocation) > 0)  then begin
      keyname := fRegKeyName + '\Forms\' + Form.Name;

      res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);
    end;

    if (res <> 0) then
      Result := -1
    else begin
      Result := 0;

      if (subreg.ReadRegValue (ParamName, boolwert) = 0) then
        ParamValue := boolwert
      else
        ParamValue := DefaultValue;
    end;
  finally
    subreg.Free;
  end;
end;

//******************************************************************************
//* Function Name: SaveGridInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.SaveGridInfo (Grid : TDBGridPro; const GridOwner : String);
var
  subreg   : TRegistryModule;
  keyname,
  formname : String;
begin
  if Assigned (Grid) then begin
    if (Length(GridOwner) = 0) then
      formname := Grid.Owner.Name
    else formname := GridOwner;

    if (Length (fLocation) = 0) then
      keyname := fRegKeyName + '\DBGrids\' + formname + '.' + Grid.Name
    else keyname := fRegKeyName + '\' + fLocation + '\DBGrids\' + formname + '.' + Grid.Name;

    subreg := TRegistryModule.Create;

    try
      if (subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ or KEY_WRITE, True) = 0) Then begin
        subreg.WriteRegValue ('Columns', Grid.DataToString);
      end;
    finally
      subreg.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name: SaveGridInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.SaveGridInfo (Owner : TForm; Grid : TStringGrid);
var
  idx    : Integer;
  colstr : String;
begin
  for idx := 0 to Grid.ColCount - 1 do begin
    if (idx = 0) then
      colstr := IntToStr (Grid.ColWidths [idx])
    else
      colstr := colstr + ';' + IntToStr (Grid.ColWidths [idx]);
  end;

  SaveFormParameter (Owner, Grid.Name, colstr);
end;

//******************************************************************************
//* Function Name: RestoreGridInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.RestoreGridInfo (Grid : TDBGridPro; const GridOwner : String);
var
  subreg   : TRegistryModule;
  colstr,
  keyname,
  formname : String;
  res      : Integer;
begin
  if Assigned (Grid) then begin
    if (Length(GridOwner) = 0) then
      formname := Grid.Owner.Name
    else formname := GridOwner;

    if (Length (fLocation) = 0) then
      keyname := fRegKeyName + '\DBGrids\' + formname + '.' + Grid.Name
    else keyname := fRegKeyName + '\' + fLocation + '\DBGrids\' + formname + '.' + Grid.Name;

    subreg := TRegistryModule.Create;

    try
      res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);

      if (res <> 0) and (Length (fLocation) > 0) then begin
        keyname := fRegKeyName + '\DBGrids\' + formname + '.' + Grid.Name;

        res := subreg.OpenKey (HKEY_CURRENT_USER, keyname, KEY_READ, False);
      end;

      if (res = 0) then begin
        if (subreg.ReadRegValue ('Columns', colstr) = 0) then
          Grid.StringToData (colstr);
      end;
    finally
      subreg.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name: RestoreGridInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.RestoreGridInfo (Owner : TForm; Grid : TStringGrid);
var
  slist  : TStringList;
  idx,
  iwert  : Integer;
  colstr : String;
begin
  LVSConfigModul.ReadFormParameter (Owner, Grid.Name, colstr);

  if (Length (colstr) > 0) then begin
    slist := TStringList.Create;

    try
      slist.Delimiter := ';';
      slist.DelimitedText := colstr;

      for idx := 0 to slist.Count - 1 do begin
        if TryStrToInt (slist [idx], iwert) then begin
          if (idx < Grid.ColCount) then
            Grid.ColWidths [idx] := iwert;
        end;
      end;
    finally
      slist.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name: ReadMandantConfig
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.ReadMandantConfig (const MandantName : String; var MandantConfig : TMandantConfig) : Integer;
var
  res   : Integer;
  query : TADOQuery;

  //******************************************************************************
  //* Function Name: ReadConfig
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function ReadConfig (const Name : String; var StrWert : String; var NumWert : Integer) : Integer;
  var
    res : integer;
  begin
    res := 0;

    StrWert := '';
    NumWert := 0;

    query.Parameters.ParamByName('name').Value := Name;

    try
      query.Open;

      if (query.RecordCount > 0) Then begin
        NumWert := query.Fields [0].AsInteger;
        StrWert := query.Fields [1].AsString;
      end;

      query.Close;
    except
      res := -9;
    end;

    Result := res;
  end;

var
  strwert : String;
  numwert : Integer;
begin
  res := 0;

  FillChar (MandantConfig, 0, sizeof (TMandantConfig));

  MandantConfig.FlagAnnahmeUnterVorbehalt   := false;
  MandantConfig.FlagLieferantenVerwaltung   := false;
  MandantConfig.FlagWEOhneBestellung        := false;
  MandantConfig.FlagRETOhneBestellung       := false;
  MandantConfig.FlagChangeArtikelStammdaten := false;
  MandantConfig.FlagDMS                     := false;

  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select REF,NAME,CONFIG_OPT from V_MANDANT where NAME=:mand');
    query.Parameters.ParamByName('mand').Value := MandantName;

    try
      query.Open;

      MandantConfig.RefMandant := query.Fields [0].AsInteger;
      MandantConfig.Mandant    := query.Fields [1].AsString;
      MandantConfig.OptConfig  := query.Fields [2].AsString;

      query.Close;
    except
      res := -9;
    end;

    query.SQL.Clear;
    query.SQL.Add ('select NUMVALUE, STRVALUE from "'+LVSDatenModul.Schema+'".V_SYS_CONFIG where MANDANT=:mand and NAME=:name');
    query.Parameters.ParamByName('mand').Value := MandantName;

    if (res = 0) then begin
      res := ReadConfig ('WE_MENGE_VORBEHALT', strwert, numwert);
      if (res = 0) then
        MandantConfig.FlagAnnahmeUnterVorbehalt := (numwert <> 0);
    end;

    if (res = 0) then begin
      res := ReadConfig ('LIEFERANTEN_VERWALTUNG', strwert, numwert);
      if (res = 0) then
        MandantConfig.FlagLieferantenVerwaltung := (numwert <> 0);
    end;

    if (res = 0) then begin
      res := ReadConfig ('WE_OHNE_BESTELLUNG', strwert, numwert);
      if (res = 0) then
        MandantConfig.FlagWEOhneBestellung := (numwert <> 0);
    end;

    if (res = 0) then begin
      res := ReadConfig ('RET_OHNE_BESTELLUNG', strwert, numwert);
      if (res = 0) then
        MandantConfig.FlagRETOhneBestellung := (numwert <> 0);
    end;

    if (res = 0) then begin
      res := ReadConfig ('CHANGE_AR_STAMMDATEN', strwert, numwert);
      if (res = 0) then
        MandantConfig.FlagChangeArtikelStammdaten := (numwert <> 0);
    end;

    if (res = 0) then begin
      res := ReadConfig ('USE_DMS', strwert, numwert);
      if (res = 0) then
        MandantConfig.FlagDMS := (numwert <> 0);
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: ReadMandantInfo
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Auslesen der Mandatendaten
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.ReadMandantInfo (const MandantName : String; var MandantInfo : TMandantInfo) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  res := 0;

  query := TADOQuery.Create (Nil);
  query.Connection := LVSDatenModul.MainADOConnection;

  query.SQL.Clear;
  query.SQL.Add ('select REF,NAME,BESCHREIBUNG from V_MANDANT where NAME=:mand');
  query.Parameters.ParamByName('mand').Value := MandantName;

  try
    query.Open;

    MandantInfo.RefMandant   := query.Fields [0].AsInteger;
    MandantInfo.Mandant      := query.Fields [1].AsString;
    MandantInfo.Beschreibung := query.Fields [2].AsString;

    query.Close;
  except
    res := -9;
  end;

  query.Free;

  Result := res;
end;

//******************************************************************************
//* Function Name: DataModuleCreate
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Auslesen der Basis-Konfigurationswerte aus der INI-Datei
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.DataModuleCreate(Sender: TObject);
var
  inifile : TIniFile;
begin
  fLocation := '';

  try
    {$ifdef StoreLogix}
      inifile := TIniFile.Create (ExtractFilePath (ParamStr (0))+'storelogix.ini');
    {$else}
      inifile := TIniFile.Create (ExtractFilePath (ParamStr (0))+'systeme.ini');
    {$endif}

    fMasterRegKeyName := inifile.ReadString ('Config', 'MasterRegKey', 'Zimbo-LVS');
    fMasterLogPath    := inifile.ReadString ('Config', 'LogFiles', '');
    fConfigPath       := inifile.ReadString ('Config', 'CfgFiles', '');
    fUmgebung         := inifile.ReadString ('Config', 'Umgebung', 'Zimbo');

    inifile.Free;

    if (Length (fMasterLogPath) > 0) and (fMasterLogPath [Length (fMasterLogPath)] <> '\') then
      fMasterLogPath := fMasterLogPath + '\';
  except
    fMasterRegKeyName := 'Zimbo-LVS';
    fUmgebung         := 'Zimbo';
    fMasterLogPath    := '';
  end;

  FrontendConfig.cfgStatIcons := True;
end;

//******************************************************************************
//* Function Name: SetLocationConfig
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Auslesen der Basis-Konfigurationswerte der Lokation
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.SetLocationConfig (const RefLocation : Integer) : Integer;
var
  res   : Integer;
  query : TADOQuery;

  //******************************************************************************
  //* Function Name: ReadConfig
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function ReadConfig (const Name : String; var StrWert : String; var NumWert : Integer) : Integer;
  begin
    query.SQL.Clear;
    query.SQL.Add ('select NUMVALUE, STRVALUE from "'+LVSDatenModul.Schema+'".V_SYS_CONFIG where REF_LOCATION=:ref_loc and NAME=:name');
    query.Parameters.ParamByName('ref_loc').Value := RefLocation;
    query.Parameters.ParamByName('name').Value := Name;

    res := 0;

    StrWert := '';
    NumWert := 0;

    try
      query.Open;

      if (query.RecordCount > 0) Then begin
        NumWert := query.Fields [0].AsInteger;
        StrWert := query.Fields [1].AsString;
      end;

      query.Close;
    except
      res := -9;
    end;

    Result := res;
  end;

var
  strwert : String;
  numwert : Integer;
begin
  res := 0;

  fUseLocationListing := False;

  Printers.Printer.Refresh;

  //Der Schema-Owner hat immer und überall die Debug-Optionen
  if (LVSDatenModul.Schema = LVSDatenModul.DBUser) then
    fDebugMode := true;

  FillChar (LocationConfig, 0, sizeof (TLocationConfig));

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select loc.NAME,cfg.* from "'+LVSDatenModul.Schema+'".V_LOCATION loc,"'+LVSDatenModul.Schema+'".V_LOCATION_CONFIG cfg where cfg.REF_LOCATION=loc.REF and loc.REF=:ref_loc');
    query.Parameters.ParamByName('ref_loc').Value := RefLocation;

    try
      query.Open;

      fLocation := query.FieldByName('NAME').AsString;

      if not (query.FieldByName('USE_MHD').IsNull) then
        fUseMHD := query.FieldByName('USE_MHD').AsString ='1';

      if not (query.FieldByName('USE_CHARGE').IsNull) then
        fUseCharge := query.FieldByName('USE_CHARGE').AsString ='1';

      if not (query.FieldByName('COL_INVISIBLE').IsNull) then
        fSectionItems.Values ['DBColInvisible'] := fSectionItems.Values ['DBColInvisible'] + query.FieldByName('COL_INVISIBLE').AsString;

      if not (query.FieldByName('USE_BES_FORCAST').IsNull) then
        fUseBestandForcast := query.FieldByName('USE_BES_FORCAST').AsString ='1';

      if not (query.FieldByName('USE_BESCATEGORY').IsNull) then
        fUseBesCategory := query.FieldByName('USE_BESCATEGORY').AsString ='1';

      if not (query.FieldByName('USE_BESTANDID').IsNull) then
        fUseBestandID := query.FieldByName('USE_BESTANDID').AsString ='1';

      if (query.FieldByName('USE_ARTIKELCOLLIS').IsNull) then
        fUseArtikelCollis := fPrjArtikelCollis
      else
        fUseArtikelCollis := query.FieldByName('USE_ARTIKELCOLLIS').AsString ='1';


      if not (query.FieldByName('USE_SUBMANDANTEN').IsNull) then
        fUseSubMandanten := query.FieldByName('USE_SUBMANDANTEN').AsString ='1'
      else begin
        strwert := fSectionItems.Values ['UseSubMandanten'];
        if (strwert = '1') then
          fUseSubMandanten := True
        else
          fUseSubMandanten := False;
      end;

      if Assigned (query.FindField('USE_LOCATION_LISTING')) and not (query.FieldByName('USE_LOCATION_LISTING').IsNull) then
        fUseLocationListing := query.FieldByName('USE_LOCATION_LISTING').AsString ='1';

      LocationConfig.Options := query.FieldByName('CONFIG_OPT').AsString;

      query.Close;
    except
    end;

    if (res = 0) then begin
      res := ReadConfig ('CHECK_EAN128_DATEN', strwert, numwert);
      if (res = 0) then
        LocationConfig.FlagCheckEAN128Daten := (numwert <> 0);
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: CheckLocationOptions
//* Author       : Stefan Graf
//* Datum        : 05.08.2018
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSConfigModul.CheckLocationOptions (const OptIdx : Integer) : Boolean;
begin
  if (Length (LocationConfig.Options) >= OptIdx) then
    Result := (LocationConfig.Options [OptIdx] > '0')
  else
    Result := False;
end;

//******************************************************************************
//* Function Name: ConfigForm
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Auslesen der Basis-Konfigurationswerte der Lokation
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSConfigModul.ConfigForm (Form : TForm);
var
  i : Integer;
begin
  if Assigned (Form) then begin
    for i:=0 to Form.ComponentCount - 1 do begin
      if Form.Components [i] is TListView then begin
        with Form.Components [i] as TListView do begin
          Font.Name := DBGrids.FontName;
          Font.Size := DBGrids.FontSize;
          Canvas.Font.Name := DBGrids.FontName;
          Canvas.Font.Size := DBGrids.FontSize;

          (*
          TitelFont.Size := DBGrids.TitleSize;
          Canvas.Font.Size := DBGrids.TitleSize;
          RowHeights [0] := Canvas.TextHeight ('A') + 4;

          Canvas.Font.Size := Font.Size;
          *)
        end;
      end else if Form.Components [i] is TStringGridPro then begin
        with Form.Components [i] as TStringGridPro do begin
          if (DBGrids.AlternateColors) then begin
            GridStyle.EvenColor := DBGrids.EvenColor;
            GridStyle.OddColor := DBGrids.OddColor;
          end;

          Font.Name := DBGrids.FontName;
          Font.Size := DBGrids.FontSize;
          Canvas.Font.Size := DBGrids.FontSize;
          DefaultRowHeight := Canvas.TextHeight ('A') + 4;

          TitelFont.Name := DBGrids.TitleFont;
          TitelFont.Size := DBGrids.TitleSize;
          Canvas.Font.Size := DBGrids.TitleSize;
          RowHeights [0] := Canvas.TextHeight ('A') + 4;

          Canvas.Font.Size := Font.Size;
        end;
      end else if Form.Components [i] is TStringGrid then begin
        with Form.Components [i] as TStringGrid do begin
          Font.Name := DBGrids.FontName;
          Font.Size := DBGrids.FontSize;
          Canvas.Font.Name := DBGrids.FontName;
          Canvas.Font.Size := DBGrids.FontSize;

          DefaultRowHeight := Canvas.TextHeight ('A') + 4;
        end;
      end;
    end;
  end else begin
    for i:=0 to Application.ComponentCount - 1 do begin
      if Application.Components [i] is TForm then begin
        ConfigForm (Application.Components [i] as TForm);
      end;
    end;
  end;
end;

end.
