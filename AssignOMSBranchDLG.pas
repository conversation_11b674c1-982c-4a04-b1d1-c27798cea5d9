unit AssignOMSBranchDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls;

type
  TAssignOMSBranchForm = class(TForm)
    GrundLabel: TLabel;
    StoreComboBox: TComboBoxPro;
    OkButton: TButton;
    AbortButton: TButton;
    Label1: TLabel;
    OrderLabel: TLabel;
    Bevel1: TBevel;
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormDestroy(Sender: TObject);
  private
    fRefOrder   : Integer;
  public
    procedure Prepare (const RefOrder : Integer);
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DB, ADODB, DatenModul, FrontendUtils, FrontendMainUtils, LVSDatenInterface, LVSIFCInterface,
  SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.02.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAssignOMSBranchForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res : Integer;
begin
  res := 0;
  
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    if (fRefOrder > 0) then
      res := AssigneOMSBranch (fRefOrder, GetComboBoxRef(StoreComboBox), 'Manuell');

    if (res = 0) then
      CanClose := True
    else begin
       CanClose := False;

      MessageDLG ('Fehler beim Zuweisen des Packplatzes' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.02.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAssignOMSBranchForm.FormCreate(Sender: TObject);
begin
  fRefOrder   := -1;

  Label1.Caption     := '';
  OrderLabel.Caption := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, ABCComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.02.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAssignOMSBranchForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (StoreComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.02.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAssignOMSBranchForm.Prepare (const RefOrder : Integer);
var
  query : TADOQuery;
begin
  fRefOrder   := RefOrder;

  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (fRefOrder > 0) then begin
      Label1.Caption := GetResourceText (1627);

      query.SQL.Add ('select REF,ORDER_NR from V_OMS_ORDER where REF=:ref');
      query.Parameters [0].Value := fRefOrder;

      query.Open;

      if (query.RecordCount > 0) then begin
        OrderLabel.Caption := query.Fields [1].AsString;
      end;

      query.Close;
    end;

    ClearComboBoxObjects (StoreComboBox);

    query.SQL.Clear;
    query.SQL.Add ('select REF,NAME,DESCRIPTION from V_OMS_BRANCH where STATUS=''AKT''');

    query.Open;

    while not (query.EOF) do begin
      StoreComboBox.AddItem (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboboxRef.Create (query.Fields [0].AsInteger));

      query.Next;
    end;

    query.Close;
  finally
    query.Free;
  end;
end;

end.
