object DepotForm: TDepotForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Depots'
  ClientHeight = 294
  ClientWidth = 562
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    562
    294)
  PixelsPerInch = 96
  TextHeight = 13
  object Bevel1: TBevel
    Left = 8
    Top = 247
    Width = 548
    Height = 8
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object NewButton: TButton
    Left = 469
    Top = 8
    Width = 83
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Neu...'
    TabOrder = 1
    OnClick = NewButtonClick
  end
  object EditButton: TButton
    Left = 469
    Top = 39
    Width = 83
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Bearbeiten...'
    TabOrder = 2
    OnClick = DepotDBGridDblClick
  end
  object DelButton: TButton
    Left = 469
    Top = 88
    Width = 83
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'L'#246'schen...'
    TabOrder = 3
    OnClick = DelButtonClick
  end
  object DepotDBGrid: TDBGridPro
    Left = 8
    Top = 8
    Width = 455
    Height = 231
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = DepotDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    OnDblClick = DepotDBGridDblClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\CS'
    RegistrySection = 'DBGrids'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 469
    Top = 259
    Width = 83
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 4
  end
  object DepotQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 288
    Top = 88
  end
  object DepotDataSource: TDataSource
    DataSet = DepotQuery
    Left = 280
    Top = 152
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 352
    Top = 88
  end
end
