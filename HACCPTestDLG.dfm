object HACCPTestForm: THACCPTestForm
  Left = 275
  Top = 136
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'HACCPTestForm'
  ClientHeight = 408
  ClientWidth = 722
  Color = clBtnFace
  Constraints.MinHeight = 380
  Constraints.MinWidth = 600
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnHide = FormHide
  OnShow = FormShow
  DesignSize = (
    722
    408)
  PixelsPerInch = 96
  TextHeight = 13
  object CloseButton: TButton
    Left = 638
    Top = 375
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 0
    ExplicitLeft = 549
    ExplicitTop = 322
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 8
    Width = 705
    Height = 358
    ActivePage = TemplateTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 1
    ExplicitWidth = 616
    ExplicitHeight = 305
    object TestTabSheet: TTabSheet
      Caption = 'Pr'#252'fungen'
      OnShow = TestTabSheetShow
      ExplicitWidth = 608
      ExplicitHeight = 277
      DesignSize = (
        697
        330)
      object HACCPDBGrid: TDBGridPro
        Left = 0
        Top = 0
        Width = 578
        Height = 330
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = HACCPDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
        PopupMenu = HACCPTestPopupMenu
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        OnDblClick = HACCPDBGridDblClick
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'MS Sans Serif'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object Button4: TButton
        Left = 590
        Top = 31
        Width = 100
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Deaktivieren'
        TabOrder = 1
        OnClick = Button4Click
        ExplicitLeft = 501
      end
      object Button5: TButton
        Left = 590
        Top = 88
        Width = 100
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'L'#246'schen...'
        TabOrder = 2
        OnClick = Lschen1Click
        ExplicitLeft = 501
      end
      object Button3: TButton
        Left = 590
        Top = 0
        Width = 100
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Bearbeiten...'
        TabOrder = 3
        OnClick = Button3Click
        ExplicitLeft = 501
      end
    end
    object TemplateTabSheet: TTabSheet
      Caption = 'Pr'#252'f-Vorlagen'
      ImageIndex = 1
      OnShow = TemplateTabSheetShow
      ExplicitWidth = 608
      ExplicitHeight = 277
      object HACCPTempDBGrid: TDBGridPro
        Left = 0
        Top = 0
        Width = 511
        Height = 330
        Align = alClient
        DataSource = HACCPTempDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit, dgMultiSelect]
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        OnDblClick = HACCPTempDBGridDblClick
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'MS Sans Serif'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoCheckBoxSelect, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        OnSelectionChange = HACCPTempDBGridChangeSelection
        OnChangeSelection = HACCPTempDBGridChangeSelection
        WidthOfIndicator = 23
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object Panel1: TPanel
        Left = 511
        Top = 0
        Width = 186
        Height = 330
        Align = alRight
        BevelOuter = bvNone
        TabOrder = 1
        ExplicitLeft = 422
        ExplicitHeight = 277
        object Label1: TLabel
          Left = 8
          Top = 244
          Width = 27
          Height = 13
          Caption = 'Lager'
        end
        object Label2: TLabel
          Left = 8
          Top = 140
          Width = 42
          Height = 13
          Caption = 'Mandant'
        end
        object Bevel1: TBevel
          Left = 8
          Top = 114
          Width = 172
          Height = 9
          Shape = bsTopLine
        end
        object Label3: TLabel
          Left = 8
          Top = 188
          Width = 67
          Height = 13
          Caption = 'Untermandant'
        end
        object TemplCreateButton: TButton
          Left = 8
          Top = 292
          Width = 174
          Height = 25
          Caption = 'Pr'#252'fungen anlegen'
          TabOrder = 0
          OnClick = TemplCreateButtonClick
        end
        object HACCPLagerComboBox: TComboBoxPro
          Left = 8
          Top = 260
          Width = 175
          Height = 22
          Style = csOwnerDrawFixed
          ColWidth = 80
          ItemHeight = 16
          TabOrder = 5
          OnChange = AnlegenComboBoxChange
        end
        object TemplNewButton: TButton
          Left = 8
          Top = 0
          Width = 171
          Height = 25
          Caption = 'Neue Vorlage...'
          TabOrder = 1
          OnClick = TemplNewButtonClick
        end
        object TemplChangeButton: TButton
          Left = 6
          Top = 31
          Width = 171
          Height = 25
          Caption = 'Vorlage bearbeiten...'
          TabOrder = 6
          OnClick = TemplChangeButtonClick
        end
        object MandantComboBox: TComboBoxPro
          Left = 8
          Top = 156
          Width = 175
          Height = 22
          Style = csOwnerDrawFixed
          ColWidth = 80
          ItemHeight = 16
          TabOrder = 3
          OnChange = MandantComboBoxChange
        end
        object TemplDelButton: TButton
          Left = 7
          Top = 80
          Width = 171
          Height = 25
          Caption = 'Vorlage l'#246'schen...'
          TabOrder = 2
          OnClick = TemplDelButtonClick
        end
        object SubMandantComboBox: TComboBoxPro
          Left = 8
          Top = 204
          Width = 175
          Height = 22
          Style = csOwnerDrawFixed
          ColWidth = 80
          ItemHeight = 16
          TabOrder = 4
        end
      end
    end
  end
  object HACCPADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 176
    Top = 56
  end
  object HACCPDataSource: TDataSource
    DataSet = HACCPADOQuery
    OnDataChange = HACCPDataSourceDataChange
    Left = 184
    Top = 96
  end
  object HACCPTempADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 220
    Top = 56
  end
  object HACCPTempDataSource: TDataSource
    DataSet = HACCPTempADOQuery
    OnDataChange = HACCPTempDataSourceDataChange
    Left = 228
    Top = 96
  end
  object HACCPTestPopupMenu: TPopupMenu
    OnPopup = HACCPTestPopupMenuPopup
    Left = 116
    Top = 120
    object Aktivieren1: TMenuItem
      Caption = 'Aktivieren'
      OnClick = Aktivieren1Click
    end
    object Deaktivieren1: TMenuItem
      Caption = 'Deaktivieren'
      OnClick = Deaktivieren1Click
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object Lschen1: TMenuItem
      Caption = 'L'#246'schen...'
      OnClick = Lschen1Click
    end
  end
end
