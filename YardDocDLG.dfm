object YardDocForm: TYardDocForm
  Left = 0
  Top = 0
  Anchors = [akLeft, akTop, akBottom]
  BorderStyle = bsDialog
  Caption = 'Rampen-Scheine erzeugen'
  ClientHeight = 326
  ClientWidth = 562
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  ExplicitHeight = 240
  DesignSize = (
    562
    326)
  PixelsPerInch = 96
  TextHeight = 13
  object Label8: TLabel
    Left = 8
    Top = 278
    Width = 66
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Label-Drucker'
    ExplicitTop = 258
  end
  object OkButton: TButton
    Left = 395
    Top = 293
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 4
  end
  object AbortButton: TButton
    Left = 480
    Top = 293
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
  object PrinterComboBox: TComboBoxPro
    Left = 8
    Top = 295
    Width = 314
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akBottom]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 3
  end
  object KopfPanel: TPanel
    Left = 0
    Top = 0
    Width = 562
    Height = 55
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      562
      55)
    object Label2: TLabel
      Left = 8
      Top = 16
      Width = 63
      Height = 13
      Caption = 'WE-Nummer:'
    end
    object WENrLabel: TLabel
      Left = 106
      Top = 16
      Width = 59
      Height = 13
      Caption = 'WENrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Bevel1: TBevel
      Left = 6
      Top = 49
      Width = 551
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label3: TLabel
      Left = 8
      Top = 32
      Width = 78
      Height = 13
      Caption = 'Bestell-Nummer:'
    end
    object WeBestNrLabel: TLabel
      Left = 106
      Top = 32
      Width = 85
      Height = 13
      Caption = 'WeBestNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object RampenScheinPanel: TPanel
    Left = 0
    Top = 55
    Width = 562
    Height = 46
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    object Label1: TLabel
      Left = 8
      Top = 1
      Width = 110
      Height = 13
      Caption = 'Anzahl Rampenscheine'
    end
    object YardDocCountEdit: TEdit
      Left = 8
      Top = 19
      Width = 97
      Height = 21
      TabOrder = 0
      Text = '0'
    end
    object YardDocCountUpDown: TUpDown
      Left = 105
      Top = 19
      Width = 16
      Height = 21
      Associate = YardDocCountEdit
      TabOrder = 1
    end
  end
  object EtikettenPanel: TPanel
    Left = 0
    Top = 101
    Width = 562
    Height = 171
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      562
      171)
    object PrintMultiColliEANRadioGroup: TRadioGroup
      Left = 8
      Top = 6
      Width = 547
      Height = 149
      Anchors = [akLeft, akTop, akRight]
      Caption = 'EAN Etiketten'
      ItemIndex = 0
      Items.Strings = (
        'Keine EAN Label ausdrucken'
        'Alle Multi-Colli-EAN Label der Bestellung ausdrucken'
        'Nur offene Multi-Colli-EAN Label der Bestellung ausdrucken'
        'Alle EAN Label der Bestellung ausdrucken'
        'Nur offene EAN Label der Bestellung ausdrucken'
        'Alle EAN Label des WE ausdrucken')
      TabOrder = 0
    end
  end
end
