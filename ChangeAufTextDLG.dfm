object ChangeAufTextForm: TChangeAufTextForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Kommissionierhinweis '#228'ndern'
  ClientHeight = 280
  ClientWidth = 759
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    759
    280)
  TextHeight = 13
  object Bevel2: TBevel
    Left = 8
    Top = 184
    Width = 743
    Height = 2
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object OkButton: TButton
    Left = 586
    Top = 247
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'OK'
    Default = True
    ModalResult = 1
    TabOrder = 4
    ExplicitTop = 194
  end
  object AbortButton: TButton
    Left = 676
    Top = 247
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
    ExplicitTop = 194
  end
  object KopfPanel: TPanel
    Left = 0
    Top = 0
    Width = 759
    Height = 63
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      759
      63)
    object AufTextLabel: TLabel
      Left = 8
      Top = 8
      Width = 37
      Height = 13
      Caption = 'Auftrag'
    end
    object Label3: TLabel
      Left = 8
      Top = 32
      Width = 30
      Height = 13
      Caption = 'Kunde'
    end
    object KundeNrLabel: TLabel
      Left = 112
      Top = 32
      Width = 66
      Height = 13
      Caption = 'KundeNrLabel'
    end
    object AufNrLabel: TLabel
      Left = 112
      Top = 8
      Width = 53
      Height = 13
      Caption = 'AufNrLabel'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 56
      Width = 743
      Height = 2
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
  end
  object HintPanel: TPanel
    Left = 0
    Top = 121
    Width = 759
    Height = 58
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    ExplicitTop = 119
    DesignSize = (
      759
      58)
    object Label1: TLabel
      Left = 8
      Top = 13
      Width = 101
      Height = 13
      Caption = 'Kommissionierhinweis'
    end
    object KommTextEdit: TEdit
      Left = 8
      Top = 29
      Width = 743
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 256
      TabOrder = 0
      Text = 'KommTextEdit'
    end
  end
  object WarningPanel: TPanel
    Left = 0
    Top = 63
    Width = 759
    Height = 58
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    Visible = False
    DesignSize = (
      759
      58)
    object Label2: TLabel
      Left = 8
      Top = 13
      Width = 66
      Height = 13
      Caption = 'Warn-Hinweis'
    end
    object WarnTextEdit: TEdit
      Left = 8
      Top = 29
      Width = 743
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 256
      TabOrder = 0
      Text = 'WarnTextEdit'
    end
  end
  object PackPanel: TPanel
    Left = 0
    Top = 179
    Width = 759
    Height = 58
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    ExplicitLeft = -8
    ExplicitTop = 95
    DesignSize = (
      759
      58)
    object Label4: TLabel
      Left = 8
      Top = 10
      Width = 62
      Height = 13
      Caption = 'Pack-Hinweis'
    end
    object PackTextEdit: TEdit
      Left = 8
      Top = 29
      Width = 743
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 256
      TabOrder = 0
      Text = 'PackTextEdit'
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 272
    Top = 256
  end
end
