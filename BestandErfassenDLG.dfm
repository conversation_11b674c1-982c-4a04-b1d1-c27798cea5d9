object BestandErfassenForm: TBestandErfassenForm
  Left = 636
  Top = 187
  BorderStyle = bsDialog
  Caption = 'Nacherfassen der vereinnahmten Menge'
  ClientHeight = 687
  ClientWidth = 430
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poMainFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    430
    687)
  TextHeight = 13
  object Bevel2: TBevel
    Left = 5
    Top = 619
    Width = 417
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 382
  end
  object FehlerLabel: TLabel
    Left = 8
    Top = 627
    Width = 409
    Height = 18
    Alignment = taCenter
    Anchors = [akLeft, akRight, akBottom]
    AutoSize = False
    Caption = 'FehlerLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -13
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
    ExplicitTop = 390
  end
  object Label11: TLabel
    Left = 16
    Top = 577
    Width = 78
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = #196'nderungsgrund'
    ExplicitTop = 339
  end
  object AbortButton: TButton
    Left = 346
    Top = 656
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 16
  end
  object OkButton: TButton
    Left = 259
    Top = 656
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 15
  end
  object InfoPanel: TPanel
    Left = 0
    Top = 35
    Width = 430
    Height = 137
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      430
      137)
    object Label1: TLabel
      Left = 16
      Top = 4
      Width = 62
      Height = 13
      Caption = 'Artikel-Nr.:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label2: TLabel
      Left = 16
      Top = 20
      Width = 70
      Height = 13
      Caption = 'Artikel-Text:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label3: TLabel
      Left = 16
      Top = 56
      Width = 64
      Height = 13
      Caption = 'Bestell-Nr.:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label4: TLabel
      Left = 16
      Top = 72
      Width = 87
      Height = 13
      Caption = 'Wareneingang:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Bevel1: TBevel
      Left = 8
      Top = 40
      Width = 417
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 48
    end
    object ArtikelNrLabel: TLabel
      Left = 112
      Top = 4
      Width = 66
      Height = 13
      Caption = 'ArtikelNrLabel'
    end
    object ArtikelTextLabel: TLabel
      Left = 112
      Top = 20
      Width = 76
      Height = 13
      Caption = 'ArtikelTextLabel'
    end
    object BestellNrLabel: TLabel
      Left = 112
      Top = 56
      Width = 68
      Height = 13
      Caption = 'BestellNrLabel'
    end
    object WENrLabel: TLabel
      Left = 112
      Top = 72
      Width = 55
      Height = 13
      Caption = 'WENrLabel'
    end
    object Bevel3: TBevel
      Left = 5
      Top = 139
      Width = 417
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label8: TLabel
      Left = 248
      Top = 56
      Width = 50
      Height = 13
      Caption = 'Position:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object BestellPosLabel: TLabel
      Left = 304
      Top = 56
      Width = 75
      Height = 13
      Caption = 'BestellPosLabel'
    end
    object Label7: TLabel
      Left = 16
      Top = 104
      Width = 41
      Height = 13
      Caption = 'LE-Nr.:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LENrLabel: TLabel
      Left = 112
      Top = 104
      Width = 50
      Height = 13
      Caption = 'LENrLabel'
    end
    object Bevel4: TBevel
      Left = 5
      Top = 96
      Width = 417
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label13: TLabel
      Left = 16
      Top = 120
      Width = 41
      Height = 13
      Caption = 'LP-Nr.:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LPNrLabel: TLabel
      Left = 112
      Top = 120
      Width = 50
      Height = 13
      Caption = 'LPNrLabel'
    end
  end
  object MengePanel: TPanel
    Left = 0
    Top = 172
    Width = 430
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    object Label5: TLabel
      Left = 16
      Top = 8
      Width = 33
      Height = 13
      Caption = 'Menge'
    end
    object MengeEdit: TEdit
      Left = 80
      Top = 5
      Width = 81
      Height = 21
      TabOrder = 0
      Text = '0'
      OnChange = EditChange
      OnExit = MengeEditExit
      OnKeyPress = MengeEditKeyPress
    end
    object MengeUpDown: TIntegerUpDown
      Left = 161
      Top = 5
      Width = 16
      Height = 21
      Associate = MengeEdit
      Max = 999999
      TabOrder = 1
      OnChangingEx = MengeUpDownChangingEx
    end
  end
  object MHDPanel: TPanel
    Left = 0
    Top = 265
    Width = 430
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 6
    object MHDLabel: TLabel
      Left = 15
      Top = 14
      Width = 25
      Height = 13
      Caption = 'MHD'
    end
    object Label14: TLabel
      Left = 178
      Top = 13
      Width = 55
      Height = 13
      Alignment = taRightJustify
      Caption = 'Herstellt am'
    end
    object MHDDutyLabel: TLabel
      Left = 65
      Top = 8
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object MHDEdit: TEdit
      Left = 80
      Top = 10
      Width = 81
      Height = 21
      TabOrder = 0
      Text = 'MHDEdit'
      OnChange = EditChange
      OnExit = MHDEditExit
      OnKeyPress = MHDEditKeyPress
    end
    object ProdDatumEdit: TEdit
      Left = 239
      Top = 10
      Width = 81
      Height = 21
      TabOrder = 1
      Text = 'ProdDatumEdit'
      OnChange = EditChange
      OnExit = ProdDatumEditExit
      OnKeyPress = MHDEditKeyPress
    end
  end
  object ChargePanel: TPanel
    Left = 0
    Top = 296
    Width = 430
    Height = 35
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 7
    object Label10: TLabel
      Left = 16
      Top = 13
      Width = 34
      Height = 13
      Caption = 'Charge'
    end
    object ChargeDutyLabel: TLabel
      Left = 65
      Top = 9
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object ChargeEdit: TEdit
      Left = 80
      Top = 11
      Width = 121
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'ChargeEdit'
      OnChange = EditChange
    end
    object CreateChargeButton: TButton
      Left = 207
      Top = 11
      Width = 202
      Height = 21
      Caption = 'Neue Chargennummer erzeugen'
      TabOrder = 1
      OnClick = CreateChargeButtonClick
    end
  end
  object TrennPanel: TPanel
    Left = 0
    Top = 331
    Width = 430
    Height = 11
    Align = alTop
    Anchors = [akLeft, akRight, akBottom]
    BevelOuter = bvNone
    TabOrder = 8
    DesignSize = (
      430
      11)
    object Bevel6: TBevel
      Left = 5
      Top = 6
      Width = 417
      Height = 3
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
  end
  object VariantePanel: TPanel
    Left = 0
    Top = 342
    Width = 430
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 9
    DesignSize = (
      430
      41)
    object Label12: TLabel
      Left = 16
      Top = 9
      Width = 54
      Height = 13
      Caption = 'Ausf'#252'hrung'
    end
    object Bevel5: TBevel
      Left = 5
      Top = 36
      Width = 417
      Height = 7
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object VarEdit: TEdit
      Left = 80
      Top = 6
      Width = 337
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 128
      TabOrder = 0
      Text = 'VarEdit'
    end
  end
  object GrundComboBox: TComboBox
    Left = 16
    Top = 593
    Width = 401
    Height = 21
    Hint = 'Grund f'#252'r die Bestands'#228'nderung'
    Style = csDropDownList
    Anchors = [akLeft, akRight, akBottom]
    MaxLength = 64
    TabOrder = 14
    OnChange = GrundComboBoxChange
    OnDropDown = GrundComboBoxDropDown
    Items.Strings = (
      'Ware mangelhaft')
  end
  object GewichtPanel: TPanel
    Left = 0
    Top = 234
    Width = 430
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    object Label6: TLabel
      Left = 16
      Top = 11
      Width = 39
      Height = 13
      Caption = 'Gewicht'
    end
    object Label9: TLabel
      Left = 164
      Top = 11
      Width = 12
      Height = 13
      Caption = 'kg'
    end
    object GewichDutyLabel: TLabel
      Left = 65
      Top = 6
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object GewichtEdit: TEdit
      Left = 80
      Top = 8
      Width = 81
      Height = 21
      TabOrder = 0
      Text = 'GewichtEdit'
      OnChange = EditChange
      OnExit = GewichtEditExit
      OnKeyPress = DoubleEditKeyPress
    end
  end
  object UnitsPanel: TPanel
    Left = 0
    Top = 203
    Width = 430
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    object Label15: TLabel
      Left = 16
      Top = 11
      Width = 56
      Height = 13
      Caption = 'Gesamtma'#223
    end
    object UnitLabel: TLabel
      Left = 168
      Top = 11
      Width = 45
      Height = 13
      Caption = 'UnitLabel'
    end
    object UnitsEdit: TEdit
      Left = 80
      Top = 8
      Width = 81
      Height = 21
      TabOrder = 0
      Text = 'UnitsEdit'
      OnChange = EditChange
      OnExit = UnitsEditExit
      OnKeyPress = DoubleEditKeyPress
    end
  end
  object BestandIDPanel: TPanel
    Left = 0
    Top = 424
    Width = 430
    Height = 37
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 11
    DesignSize = (
      430
      37)
    object Label16: TLabel
      Left = 16
      Top = 9
      Width = 53
      Height = 13
      Caption = 'Bestand-ID'
    end
    object Bevel7: TBevel
      Left = 5
      Top = 35
      Width = 417
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object BestandIDEdit: TEdit
      Left = 80
      Top = 6
      Width = 337
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 0
      Text = 'BestandIDEdit'
    end
  end
  object SubmandPanel: TPanel
    Left = 0
    Top = 8
    Width = 430
    Height = 27
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      430
      27)
    object Label17: TLabel
      Left = 16
      Top = 6
      Width = 84
      Height = 13
      Caption = 'Untermandant:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object SubmandLabel: TLabel
      Left = 112
      Top = 6
      Width = 71
      Height = 13
      Caption = 'SubmandLabel'
    end
    object Bevel8: TBevel
      Left = 8
      Top = 25
      Width = 417
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 33
    end
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 430
    Height = 8
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
  end
  object NVEPanel: TPanel
    Left = 0
    Top = 383
    Width = 430
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 10
    DesignSize = (
      430
      41)
    object Label18: TLabel
      Left = 16
      Top = 9
      Width = 56
      Height = 13
      Caption = 'NVE im WE'
    end
    object Bevel9: TBevel
      Left = 5
      Top = 36
      Width = 417
      Height = 7
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label20: TLabel
      Left = 228
      Top = 9
      Width = 67
      Height = 13
      Alignment = taRightJustify
      Caption = 'HU-Nr. im WE'
    end
    object NVEEdit: TEdit
      Left = 80
      Top = 6
      Width = 121
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 18
      TabOrder = 0
      Text = 'NVEEdit'
    end
    object HUNrEdit: TEdit
      Left = 300
      Top = 6
      Width = 121
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 1
      Text = 'HUNrEdit'
    end
  end
  object WEDatumPanel: TPanel
    Left = 0
    Top = 535
    Width = 430
    Height = 37
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 13
    DesignSize = (
      430
      37)
    object Label19: TLabel
      Left = 16
      Top = 12
      Width = 52
      Height = 13
      Caption = 'WE-Datum'
    end
    object Bevel10: TBevel
      Left = 5
      Top = 35
      Width = 417
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object WEDatePicker: TDateTimePicker
      Left = 80
      Top = 7
      Width = 81
      Height = 21
      Date = 43059.000000000000000000
      Time = 0.700141365741728800
      TabOrder = 0
    end
    object WETimePicker: TDateTimePicker
      Left = 178
      Top = 7
      Width = 81
      Height = 21
      Date = 43059.000000000000000000
      Time = 0.700141365741728800
      Kind = dtkTime
      TabOrder = 1
    end
  end
  object SerialPanel: TPanel
    Left = 0
    Top = 498
    Width = 430
    Height = 37
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 17
    DesignSize = (
      430
      37)
    object Label21: TLabel
      Left = 16
      Top = 9
      Width = 47
      Height = 13
      Caption = 'Serien-Nr.'
    end
    object Bevel11: TBevel
      Left = 5
      Top = 35
      Width = 417
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object SerialEdit: TEdit
      Left = 80
      Top = 6
      Width = 337
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 0
      Text = 'SerialEdit'
    end
  end
  object ProjectPanel: TPanel
    Left = 0
    Top = 461
    Width = 430
    Height = 37
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 12
    DesignSize = (
      430
      37)
    object Label22: TLabel
      Left = 16
      Top = 9
      Width = 47
      Height = 13
      Caption = 'Projekt-ID'
    end
    object Bevel12: TBevel
      Left = 5
      Top = 35
      Width = 417
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object ProjectIDEdit: TEdit
      Left = 80
      Top = 6
      Width = 337
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 0
      Text = 'ProjectIDEdit'
    end
  end
end
