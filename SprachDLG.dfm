object ChangeSpracheForm: TChangeSpracheForm
  Left = 438
  Top = 487
  BorderStyle = bsDialog
  Caption = 'Sprache ausw'#228'hlen'
  ClientHeight = 156
  ClientWidth = 219
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poMainFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  DesignSize = (
    219
    156)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 124
    Height = 13
    Caption = 'Sprache in der Oberfl'#228'che'
  end
  object Label2: TLabel
    Left = 8
    Top = 64
    Width = 125
    Height = 13
    Caption = 'Sprache in der Datenbank'
    Visible = False
  end
  object SprachComboBox: TComboBoxPro
    Left = 8
    Top = 27
    Width = 201
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 40
    AutoPrepare = False
    ColumeCount = 2
    ItemHeight = 15
    ItemIndex = 0
    TabOrder = 0
    Text = 'DE|Deutsch'
    Items.Strings = (
      'DE|Deutsch'
      'EN|Englisch'
      'DA|D'#195#164'nisch'
      'POL|Polnisch'
      'FR|Franz'#246'sich'
      'SP|Spanisch')
  end
  object OkButton: TButton
    Left = 56
    Top = 124
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = '&Ok'
    Default = True
    ModalResult = 1
    TabOrder = 2
  end
  object AbortButton: TButton
    Left = 136
    Top = 124
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = '&Abbrechen'
    ModalResult = 3
    TabOrder = 3
  end
  object DBSprachComboBox: TComboBoxPro
    Left = 10
    Top = 80
    Width = 201
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 40
    AutoPrepare = False
    ItemHeight = 16
    TabOrder = 1
    Visible = False
  end
end
