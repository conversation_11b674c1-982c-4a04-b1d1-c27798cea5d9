object PickAnzeigeModule: TPickAnzeigeModule
  OldCreateOrder = False
  OnCreate = DataModuleCreate
  OnDestroy = DataModuleDestroy
  Height = 234
  Width = 177
  object WSocket1: TWSocket
    LineEnd = #13#10
    Proto = 'tcp'
    LocalAddr = '0.0.0.0'
    LocalAddr6 = '::'
    LocalPort = '0'
    SocksLevel = '5'
    ExclusiveAddr = False
    ComponentOptions = []
    ReqVerLow = 1
    ReqVerHigh = 1
    OnDataSent = WSocket1DataSent
    SocketErrs = wsErrTech
    Left = 40
    Top = 24
  end
  object ADOQuery1: TADOQuery
    Connection = ADOConnection1
    Parameters = <>
    Left = 40
    Top = 128
  end
  object ADOConnection1: TADOConnection
    ConnectionString = 
      'Provider=OraOLEDB.Oracle.1;Persist Security Info=False;Data Sour' +
      'ce=LVSTEST'
    Provider = 'OraOLEDB.Oracle.1'
    Left = 40
    Top = 72
  end
  object ADOQuery2: TADOQuery
    Connection = ADOConnection1
    Parameters = <>
    Left = 40
    Top = 176
  end
end
