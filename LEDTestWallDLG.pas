unit LEDTestWallDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls;

type
  TLEDTestWallForm = class(TForm)
    Panel1: TPanel;
    Panel2: TPanel;
    Panel3: TPanel;
    Panel4: TPanel;
    Panel5: TPanel;
    Panel6: TPanel;
    Panel7: TPanel;
    Panel8: TPanel;
  private
    { Private-Deklarationen }
  public
    function SetLED (const LEDNr : Integer; const LEDStat : String) : Integer;
  end;

implementation

{$R *.dfm}

function TLEDTestWallForm.SetLED (const LEDNr : Integer; const LEDStat : String) : Integer;
var
  ledcolor : TColor;
begin
  if (LEDStat [1] = '0') then
    ledcolor := clBtnFace
  else if (LEDStat [1] = '1') then
    ledcolor := clRed
  else if (LEDStat [1] = '2') then
    ledcolor := clGreen
  else if (LEDStat [1] = '3') then
    ledcolor := clBlue
  else
    ledcolor := clBtnFace;


  if (LEDNr = 1) then
    Panel1.color := ledcolor
  else if (LEDNr = 2) then
    Panel2.color := ledcolor
  else if (LEDNr = 3) then
    Panel3.color := ledcolor
  else if (LEDNr = 4) then
    Panel4.color := ledcolor
  else if (LEDNr = 5) then
    Panel4.color := ledcolor
  else if (LEDNr = 6) then
    Panel4.color := ledcolor
  else if (LEDNr = 7) then
    Panel4.color := ledcolor;

  Result := 0;
end;

end.
