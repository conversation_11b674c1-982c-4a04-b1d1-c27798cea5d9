object LVSDatenModul: TLVSDatenModul
  OnCreate = DataModuleCreate
  OnDestroy = DataModuleDestroy
  Height = 224
  Width = 418
  object MainADOConnection: TADOConnection
    ConnectionString = 
      'Provider=OraOLEDB.Oracle.1;Password=mittelgar;Persist Security I' +
      'nfo=True;User ID=lvsentw;Data Source=lvsentw_64.rzhit.win'
    LoginPrompt = False
    Provider = 'OraOLEDB.Oracle.1'
    AfterConnect = MainADOConnectionAfterConnect
    BeforeConnect = MainADOConnectionBeforeConnect
    AfterDisconnect = MainADOConnectionAfterDisconnect
    BeforeDisconnect = MainADOConnectionBeforeDisconnect
    OnExecuteComplete = MainADOConnectionExecuteComplete
    OnWillExecute = MainADOConnectionWillExecute
    Left = 68
    Top = 21
  end
  object ADOCommand1: TADOCommand
    Connection = MainADOConnection
    Parameters = <>
    ParamCheck = False
    Left = 144
    Top = 88
  end
  object ADOQuery1: TADOQuery
    Connection = MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 64
    Top = 88
  end
  object OraMainSession: TOraSession
    Options.UseUnicode = True
    AfterConnect = OraMainSessionAfterConnect
    Left = 296
    Top = 16
  end
  object OraSQL1: TOraSQL
    Session = OraMainSession
    BeforeExecute = OraSQL1BeforeExecute
    Left = 296
    Top = 120
  end
  object OraSQLMonitor1: TOraSQLMonitor
    Active = False
    Left = 296
    Top = 64
  end
end
