object InvPosResForm: TInvPosResForm
  Left = 386
  Top = 127
  Caption = 'Inventurz'#228'hlungen zur'#252'ckmelden'
  ClientHeight = 575
  ClientWidth = 734
  Color = clBtnFace
  Constraints.MinHeight = 613
  Constraints.MinWidth = 750
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 8
    Height = 575
    Align = alLeft
    BevelOuter = bvNone
    TabOrder = 0
  end
  object Panel2: TPanel
    Left = 726
    Top = 0
    Width = 8
    Height = 575
    Align = alRight
    BevelOuter = bvNone
    TabOrder = 1
  end
  object Panel3: TPanel
    Left = 8
    Top = 0
    Width = 718
    Height = 575
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      718
      575)
    object Label4: TLabel
      Left = 0
      Top = 154
      Width = 165
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Bereits erfasste Inventurergebnisse'
    end
    object Bevel1: TBevel
      Left = 0
      Top = 148
      Width = 728
      Height = 1
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Label1: TLabel
      Left = 0
      Top = 0
      Width = 87
      Height = 13
      Caption = 'Inventurpositionen'
    end
    object ErgebnisseStringGrid: TStringGridPro
      Left = 0
      Top = 170
      Width = 633
      Height = 119
      Anchors = [akLeft, akRight, akBottom]
      ColCount = 15
      DefaultRowHeight = 18
      FixedCols = 0
      RowCount = 2
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goColSizing, goRowSelect]
      PopupMenu = InvErgPopupMenu
      TabOrder = 0
      OnClick = ErgebnisseStringGridClick
      TitelFont.Charset = DEFAULT_CHARSET
      TitelFont.Color = clWindowText
      TitelFont.Height = -11
      TitelFont.Name = 'MS Sans Serif'
      TitelFont.Style = []
      ColWidths = (
        64
        64
        64
        64
        64
        64
        64
        64
        64
        64
        64
        64
        64
        64
        64)
    end
    object InvPosDBGridPro: TDBGridPro
      Left = 0
      Top = 16
      Width = 633
      Height = 125
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = InvPosDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      TabOrder = 3
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
      OnColumnSort = InvPosDBGridProColumnSort
    end
    object InvDelButton: TButton
      Left = 639
      Top = 170
      Width = 75
      Height = 24
      Anchors = [akRight, akBottom]
      Caption = 'L'#246'schen...'
      TabOrder = 4
      OnClick = InvDelButtonOnClick
    end
    object NeuButton: TButton
      Left = 639
      Top = 16
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Neu'
      TabOrder = 1
      OnClick = OnClickNeu
    end
    object LoeschenButton: TButton
      Left = 639
      Top = 48
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'L'#246'schen...'
      TabOrder = 2
      OnClick = OnClickLoeschen
    end
    object DetailGroupBox: TGroupBox
      Left = 0
      Top = 301
      Width = 725
      Height = 232
      Anchors = [akLeft, akRight, akBottom]
      Caption = 'Inventurergebnis hinzuf'#252'gen'
      TabOrder = 5
      DesignSize = (
        725
        232)
      object Label3: TLabel
        Left = 10
        Top = 130
        Width = 47
        Height = 13
        Caption = 'Menge Ist'
      end
      object Label6: TLabel
        Left = 142
        Top = 130
        Width = 53
        Height = 13
        Caption = 'Gewicht Ist'
      end
      object Label7: TLabel
        Left = 310
        Top = 131
        Width = 39
        Height = 13
        Caption = 'MHD Ist'
      end
      object Label10: TLabel
        Left = 456
        Top = 131
        Width = 48
        Height = 13
        Caption = 'Charge Ist'
      end
      object Label12: TLabel
        Left = 310
        Top = 72
        Width = 32
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'Einheit'
      end
      object Label13: TLabel
        Left = 223
        Top = 151
        Width = 12
        Height = 13
        Caption = 'kg'
      end
      object Label2: TLabel
        Left = 456
        Top = 72
        Width = 30
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'LE-Nr.'
      end
      object Label5: TLabel
        Left = 8
        Top = 72
        Width = 29
        Height = 13
        Caption = 'Artikel'
      end
      object Label8: TLabel
        Left = 456
        Top = 16
        Width = 49
        Height = 13
        Caption = 'Lagerplatz'
      end
      object LB: TLabel
        Left = 9
        Top = 16
        Width = 62
        Height = 13
        Caption = 'Lagerbereich'
      end
      object Bevel2: TBevel
        Left = 8
        Top = 64
        Width = 706
        Height = 9
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object Bevel3: TBevel
        Left = 8
        Top = 120
        Width = 706
        Height = 9
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object MengeEdit: TEdit
        Left = 10
        Top = 146
        Width = 105
        Height = 21
        MaxLength = 22
        TabOrder = 3
        Text = '0'
        OnChange = MengeEditChange
        OnKeyPress = MengeEditKeyPress
      end
      object UpdateButton: TButton
        Left = 550
        Top = 199
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = #220'bernehmen'
        TabOrder = 8
        OnClick = UpdateButtonClick
      end
      object GewichtEdit: TEdit
        Left = 142
        Top = 146
        Width = 77
        Height = 21
        MaxLength = 12
        TabOrder = 5
        Text = 'GewichtEdit'
        OnChange = DataChange
        OnExit = GewichtEditExit
      end
      object MengeUpDown: TIntegerUpDown
        Left = 115
        Top = 146
        Width = 16
        Height = 21
        Associate = MengeEdit
        Max = 1000
        TabOrder = 4
        OnChanging = MengeUpDownChanging
      end
      object MHDEdit: TEdit
        Left = 310
        Top = 147
        Width = 121
        Height = 21
        MaxLength = 10
        TabOrder = 6
        Text = 'MHDEdit'
        OnChange = DataChange
        OnExit = MHDEditExit
        OnKeyPress = MHDEditKeyPress
      end
      object ChargeEdit: TEdit
        Left = 456
        Top = 147
        Width = 121
        Height = 21
        MaxLength = 32
        TabOrder = 7
        Text = 'ChargeEdit'
        OnChange = DataChange
      end
      object ClearButton: TButton
        Left = 637
        Top = 199
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Verwerfen'
        TabOrder = 9
        OnClick = ClearButtonClick
      end
      object EinheitComboBox: TComboBoxPro
        Left = 310
        Top = 88
        Width = 131
        Height = 22
        Style = csOwnerDrawFixed
        Anchors = [akTop, akRight]
        ItemHeight = 16
        TabOrder = 1
        OnChange = DataChange
      end
      object LEEdit: TEdit
        Left = 456
        Top = 88
        Width = 139
        Height = 21
        Anchors = [akTop, akRight]
        MaxLength = 22
        TabOrder = 2
        Text = 'LEEdit'
        OnKeyPress = LEEditKeyPress
      end
      object LBComboBox: TComboBoxPro
        Left = 8
        Top = 32
        Width = 433
        Height = 22
        Style = csOwnerDrawFixed
        ColWidth = 120
        ItemHeight = 16
        TabOrder = 0
        OnChange = LBComboBoxChange
      end
      object ArtikelComboBox: TComboBoxPro
        Left = 8
        Top = 88
        Width = 281
        Height = 22
        Style = csOwnerDrawFixed
        ColWidth = 60
        ItemHeight = 16
        TabOrder = 10
        OnChange = ArtikelComboBoxChange
      end
      object LPComboBox: TComboBoxPro
        Left = 456
        Top = 32
        Width = 257
        Height = 22
        Style = csOwnerDrawFixed
        ItemHeight = 16
        TabOrder = 11
      end
    end
    object AbortButton: TButton
      Left = 649
      Top = 544
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 6
    end
    object OkButton: TButton
      Left = 563
      Top = 544
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Ok'
      Default = True
      ModalResult = 1
      TabOrder = 7
    end
    object InvAendButton: TButton
      Left = 639
      Top = 200
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #196'ndern'
      TabOrder = 8
      OnClick = InvAendButtonClick
    end
  end
  object KommPosDataSource: TDataSource
    OnDataChange = KommPosDataSourceDataChange
    Left = 624
    Top = 112
  end
  object InvPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 648
    Top = 40
  end
  object ADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 752
    Top = 144
  end
  object ArtLPBetterADODataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 136
    Top = 280
  end
  object ArtLPDataSource: TDataSource
    DataSet = ArtLPBetterADODataSet
    OnDataChange = ArtLPDataSourceDataChange
    Left = 184
    Top = 280
  end
  object ErgebnisseBetterADODataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 512
    Top = 112
  end
  object LEBetterADODataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 248
    Top = 48
  end
  object InvPosDataSource: TDataSource
    DataSet = InvPosDataSet
    OnDataChange = InvPosOnDataChange
    Left = 592
    Top = 72
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 400
    Top = 224
  end
  object InvErgPopupMenu: TPopupMenu
    Left = 472
    Top = 152
    object InvErgDelPopUpMenuItem: TMenuItem
      Caption = 'Inventurergebnis l'#246'schen...'
      OnClick = InvDelButtonOnClick
    end
  end
  object BetterADODataSet1: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 472
    Top = 248
  end
end
