unit BuildMasterNVEDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, ExtCtrls, DB, ADODB;

type
  TBuildMasterNVEForm = class(TForm)
    Panel1: TPanel;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    LTInhaltDBGrid: TDBGridPro;
    Panel2: TPanel;
    OkButton: TButton;
    NVEDataSource: TDataSource;
    NVEQuery: TADOQuery;
    AbortButton: TButton;
    VorgangLabel: TLabel;
    Label5: TLabel;
    Label6: TLabel;
    AuftragCheckBox: TCheckBox;
    NewMasterButton: TButton;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure AuftragCheckBoxClick(Sender: TObject);
    procedure NewMasterButtonClick(Sender: TObject);
  private
    fRefNVE       : Integer;
    fRefMasterNVE : Integer;
    fRefLiefAdr   : Integer;
    fRefAuftrag   : Integer;
    fRefMand      : Integer;
    fRefSubMand   : Integer;
    fPLZ          : String;
    fKdNr         : String;

    procedure UpdateNVEQuery (Sender: TObject);
  public
    property RefMasterNVE : Integer read fRefMasterNVE;

    procedure Prepare (const RefAuftrag, RefNVE : Integer);
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DatenModul, ConfigModul, DBGridUtilModule, FrontendUtils, LVSDatenInterface;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBuildMasterNVEForm.Prepare (const RefAuftrag, RefNVE : Integer);
var
  query : TADOQuery;
begin
  fRefNVE     := RefNVE;
  fRefAuftrag := RefAuftrag;

  if (fRefAuftrag > 0) then begin
    query := TADOQuery.Create (Self);

    try
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Clear;
      query.SQL.Add ('select a.REF_MAND, a.REF_SUB_MAND, a.KUNDEN_NR, adr.REF_EMPF_ADR, adr.PLZ from V_AUFTRAG a, V_AUFTRAG_ADR adr where adr.REF=a.REF_LIEFER_ADR and a.REF='+IntToStr (fRefAuftrag));

      query.Open;

      fRefMand    := DBGetReferenz (query.FieldByName ('REF_MAND'));
      fRefSubMand := DBGetReferenz (query.FieldByName ('REF_SUB_MAND'));
      fKdNr       := query.FieldByName ('KUNDEN_NR').AsString;
      fPLZ        := query.FieldByName ('PLZ').AsString;
      fRefLiefAdr := DBGetReferenz (query.FieldByName ('REF_EMPF_ADR'));

      query.Close;
    finally
      query.Free;
    end;

    UpdateNVEQuery (Nil);
  end;
end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBuildMasterNVEForm.UpdateNVEQuery (Sender: TObject);
begin
  NVEQuery.SQL.Clear;

  NVEQuery.SQL.Add ('select nve.REF,a.AUFTRAG_NR,nve.NVE_NR,nve.NVE_TYPE,nve.GESAMT_VPE,nve.NETTO_GEWICHT from V_NVE nve, V_AUFTRAG a');
  NVEQuery.SQL.Add ('where nve.OPT_MASTER_NVE=''1'' and nve.STATUS in (''ANG'',''AKT'',''WA'',''FIN'') and a.REF=nve.REF_AUF_KOPF');

  if AuftragCheckBox.Checked then
    NVEQuery.SQL.Add ('and nve.REF_AUF_KOPF='+IntToStr (fRefAuftrag))
  else if (fRefSubMand > 0) then
    NVEQuery.SQL.Add ('and nve.REF_AUF_KOPF in (select REF from V_AUFTRAG where REF_SUB_MAND='+IntToStr (fRefSubMand)+' and REF_LIEFER_ADR in (select REF from V_AUFTRAG_ADR where PLZ='+#39+fPLZ+#39+'))')
  else
    NVEQuery.SQL.Add ('and nve.REF_AUF_KOPF in (select REF from V_AUFTRAG where REF_MAND='+IntToStr (fRefMand)+' and KUNDEN_NR='+#39+fKdNr+#39+' and REF_LIEFER_ADR in (select REF from V_AUFTRAG_ADR where PLZ='+#39+fPLZ+#39+'))');

  NVEQuery.SQL.Add ('and nve.REF<>'+IntToStr (fRefNVE)+' order by nve.NETTO_GEWICHT');

  NVEQuery.Open;

  DBGridUtils.SetGewichtDisplayFunctions (NVEQuery, 'NETTO_GEWICHT');
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 19.05.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBuildMasterNVEForm.AuftragCheckBoxClick(Sender: TObject);
begin
  UpdateNVEQuery (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBuildMasterNVEForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  if (NVEQuery.Active) and (NVEQuery.RecNo <> -1) then
    fRefMasterNVE := DBGetReferenz (NVEQuery.FieldByName ('REF'))
  else
    fRefMasterNVE := -1;

  NVEQuery.Close;

  if Assigned (DBGridUtils) Then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBuildMasterNVEForm.FormCreate(Sender: TObject);
begin
  fRefNVE       := -1;
  fRefMasterNVE := -1;
  fRefAuftrag   := -1;
  fRefLiefAdr   := -1;

  Label1.Caption := '';
  Label2.Caption := '';
  Label3.Caption := '';
  Label4.Caption := '';

  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBuildMasterNVEForm.FormShow(Sender: TObject);
var
  query : TADOQuery;
begin
  LVSConfigModul.RestoreFormInfo (Self);

  Label1.Caption := 'Auftrag:';
  Label3.Caption := 'Kunde:';
  Label5.Caption := 'Anliefer Adresse:';

  query := TADOQuery.Create (Self);

  try
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select a.*,adr.REF_EMPF_ADR,adr.NAME1,adr.STRASSE,adr.LAND,adr.PLZ, adr.ORT from V_AUFTRAG a, V_AUFTRAG_ADR adr where adr.REF=a.REF_LIEFER_ADR and a.REF='+IntToStr (fRefAuftrag));

    query.Open;

    fRefAuftrag := query.FieldByName ('REF').AsInteger;
    fRefLiefAdr := DBGetReferenz (query.FieldByName ('REF_EMPF_ADR'));

    Label2.Caption := query.FieldByName ('AUFTRAG_NR').AsString;

    if query.FieldByName ('KUNDEN_NR').IsNull then
      Label4.Caption := query.FieldByName ('KUNDEN_NAME').AsString
    else
      Label4.Caption := query.FieldByName ('KUNDEN_NR').AsString + ' / ' + query.FieldByName ('KUNDEN_NAME').AsString;

    Label6.Caption := query.FieldByName ('NAME1').AsString + ' / '+query.FieldByName ('STRASSE').AsString + ' / '+query.FieldByName ('PLZ').AsString+' '+query.FieldByName ('ORT').AsString;

    query.Close;

    AuftragCheckBox.Enabled := (fRefLiefAdr > 0);
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 22.09.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBuildMasterNVEForm.NewMasterButtonClick(Sender: TObject);
var
  res,
  refnve : Integer;
  nve_nr : String;
begin
  res := CreateMasterNVE (fRefAuftrag, -1, refnve, nve_nr);

  if (res <> 0) then
    MessageDLG ('Fehler beim Erzeugen einer neuen Master-NVE' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
  else begin
    UpdateNVEQuery (Sender);

    NVEQuery.Locate('REF', refnve, []);
  end;
end;

end.
