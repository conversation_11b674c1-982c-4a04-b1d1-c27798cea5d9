object ShowAufPosSetComponentForm: TShowAufPosSetComponentForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Auslistung der Set Bestandteile'
  ClientHeight = 335
  ClientWidth = 767
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    767
    335)
  PixelsPerInch = 96
  TextHeight = 13
  object AufTextLabel: TLabel
    Left = 8
    Top = 8
    Width = 58
    Height = 13
    Caption = 'Auftragpos.'
  end
  object AufPosLabel: TLabel
    Left = 88
    Top = 8
    Width = 69
    Height = 13
    Caption = 'AufPosLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label3: TLabel
    Left = 8
    Top = 32
    Width = 30
    Height = 13
    Caption = 'Artikel'
  end
  object ArtikelLabel: TLabel
    Left = 88
    Top = 32
    Width = 68
    Height = 13
    Caption = 'ArtikelLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label5: TLabel
    Left = 8
    Top = 64
    Width = 81
    Height = 13
    Caption = 'Erfasste Mengen'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 56
    Width = 751
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 819
  end
  object SetComponentDBGrid: TDBGridPro
    Left = 8
    Top = 80
    Width = 751
    Height = 209
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = SetComponentDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 684
    Top = 302
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 1
  end
  object SetComponentDataSource: TDataSource
    DataSet = SetComponentDataSet
    Left = 616
    Top = 120
  end
  object SetComponentDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 656
    Top = 120
  end
end
