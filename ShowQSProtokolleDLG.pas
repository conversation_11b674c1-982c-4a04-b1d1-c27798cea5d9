unit ShowQSProtokolleDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, BetterADODataSet, StdCtrls, ComboBoxPro, Grids, DBGrids,
  SMDBGrid, DBGridPro, ExtCtrls, ComCtrls;

type
  TShowQSProtokolleForm = class(TForm)
    Label1: TLabel;
    ArtikelLabel: TLabel;
    RevBewDBGrid: TDBGridPro;
    RevBewDataSet: TBetterADODataSet;
    RevBewDataSource: TDataSource;
    CloseButton: TButton;
    Label2: TLabel;
    Label4: TLabel;
    LagerLabel: TLabel;
    Label6: TLabel;
    MandLabel: TLabel;
    DispGroupBox: TGroupBox;
    DatumGroupBox: TGroupBox;
    VonDateTimePicker: TDateTimePicker;
    Label5: TLabel;
    BisDateTimePicker: TDateTimePicker;
    Bevel1: TBevel;
    procedure RevInvComboBoxChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormHide(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure DateTimePickerChange(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure CheckBoxClick(Sender: TObject);
    procedure FormResize(Sender: TObject);
    function RevBewDBGridColumnSort(Sender: TCustomDBGridPro;
      const ColumnName: string): string;
  private
    fMandRef    : Integer;
    fLagerRef   : Integer;
    fArtikelNr  : String;

    procedure DisplayOnShow (var Message: TMessage); message WM_USER + 1;
  public
    procedure Prepare (const RefArtikelEinheit : Integer); overload;
    procedure Prepare (const RefLager, RefArtikelEinheit : Integer); overload;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DatenModul, ConfigModul, DBGridUtilModule;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowQSProtokolleForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowQSProtokolleForm.FormCreate(Sender: TObject);
begin
  VonDateTimePicker.Date := Trunc (Now - 7);
  BisDateTimePicker.Date := Trunc (Now);

  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowQSProtokolleForm.FormHide(Sender: TObject);
begin
  RevBewDataSet.Close;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowQSProtokolleForm.FormResize(Sender: TObject);
begin
  DatumGroupBox.Width := ((ClientWidth - 4 * DatumGroupBox.Left) div 2);

  DispGroupBox.Width := DatumGroupBox.Width;
  DispGroupBox.Left  := 3 * DatumGroupBox.Left + DatumGroupBox.Width;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowQSProtokolleForm.DisplayOnShow (var Message: TMessage);
begin
  Update;
  
  RevInvComboBoxChange (Nil);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowQSProtokolleForm.FormShow(Sender: TObject);
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.RestoreFormInfo (Self);

  PostMessage (Handle, WM_USER + 1, 0, 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowQSProtokolleForm.Prepare (const RefArtikelEinheit : Integer);
var
  query   : TADOQuery;
begin
  fLagerRef   := -1;

  query := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select a.ARTIKEL_NR,a.ARTIKEL_TEXT,m.REF,m.NAME,m.BESCHREIBUNG,ae.STUECK_KENNZEICHEN');
    query.SQL.Add (',(select ARTIKEL_NR from V_ARTIKEL where REF=(select REF_AR from V_ARTIKEL_EINHEIT where REF_INHALT=ae.REF and ROWNUM=1)) as UM_ARTIKEL_NR');
    query.SQL.Add ('from V_ARTIKEL a, V_ARTIKEL_EINHEIT ae, V_MANDANT m where m.REF=a.REF_MAND and a.REF=ae.REF_AR and ae.REF='+IntToStr (RefArtikelEinheit));

    try
      query.Open;

      if (query.Fields [6].IsNull) then
        fArtikelNr := query.Fields [0].AsString
      else
        fArtikelNr := query.Fields [6].AsString;

      fMandRef   := query.Fields [2].AsInteger;

      MandLabel.Caption    := query.Fields [3].AsString + ' / ' + query.Fields [4].AsString;
      LagerLabel.Caption   := 'Alle';
      ArtikelLabel.Caption := fArtikelNr + ' / ' + query.Fields [1].AsString;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowQSProtokolleForm.Prepare (const RefLager, RefArtikelEinheit : Integer);
var
  query   : TADOQuery;
begin
  fLagerRef   := RefLager;

  query := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select l.NAME,l.BESCHREIBUNG,a.ARTIKEL_NR,a.ARTIKEL_TEXT,m.REF,m.NAME,m.BESCHREIBUNG,ae.STUECK_KENNZEICHEN');
    query.SQL.Add (',(select ARTIKEL_NR from V_ARTIKEL where REF=(select REF_AR from V_ARTIKEL_EINHEIT where REF_INHALT=ae.REF and ROWNUM=1)) as UM_ARTIKEL_NR');
    query.SQL.Add ('from V_ARTIKEL a, V_ARTIKEL_EINHEIT ae, V_LAGER l, V_MANDANT m where m.REF=a.REF_MAND and a.REF=ae.REF_AR and ae.REF='+IntToStr (RefArtikelEinheit)+' and l.REF='+IntToStr (RefLager));

    try
      query.Open;

      if (query.Fields [8].IsNull) then
        fArtikelNr := query.Fields [2].AsString
      else
        fArtikelNr := query.Fields [8].AsString;

      fMandRef   := query.Fields [4].AsInteger;

      MandLabel.Caption   := query.Fields [5].AsString + ' / ' + query.Fields [6].AsString;
      LagerLabel.Caption   := query.Fields [0].AsString + ' / ' + query.Fields [1].AsString;
      ArtikelLabel.Caption := fArtikelNr + ' / ' + query.Fields [3].AsString;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TShowQSProtokolleForm.RevBewDBGridColumnSort(
  Sender: TCustomDBGridPro; const ColumnName: string): string;
begin
  if (ColumnName = 'BEW_DATUM') then
    Result := ColumnName + ',REF'
  else
    Result := ColumnName;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowQSProtokolleForm.RevInvComboBoxChange(Sender: TObject);
begin
  RevBewDataSet.Close;

  RevBewDataSet.CommandText := 'select * from V_PCD_QUALITY_CHECK where CREATE_DATE between '+BuildOracleDateString(VonDateTimePicker.Date, True)+' and '+BuildOracleDateString(BisDateTimePicker.Date, False, True)+' and REF_MAND='+IntToStr (fMandRef)+' and ARTIKEL_NR='+#39+fArtikelNr+#39;

  if (fLagerRef <> -1) then begin
    RevBewDataSet.CommandText := RevBewDataSet.CommandText + ' and REF_LAGER=:ref_lager';
    RevBewDataSet.Parameters.ParamByName('ref_lager').Value := fLagerRef;
  end;

  (*
  exclstr := '';
  if not (WECheckBox.Checked) then begin
    if (Length (exclstr) > 0) then exclstr := exclstr + ',';
    exclstr := exclstr + '''WE''';
  end;

  if not (WACheckBox.Checked) then begin
    if (Length (exclstr) > 0) then exclstr := exclstr + ',';
    exclstr := exclstr + '''KOMM'',''LIEF-RET'',''CROSSDOCK'',''AUF_CHANGE'',''AUF_KORREKTUR''';
  end;

  if not (RETCheckBox.Checked) then begin
    if (Length (exclstr) > 0) then exclstr := exclstr + ',';
    exclstr := exclstr + '''RET''';
  end;

  if not (KORRCheckBox.Checked) then begin
    if (Length (exclstr) > 0) then exclstr := exclstr + ',';
    exclstr := exclstr + '''CREATE'',''BES_KORREKTUR'',''BES_DEL''';
  end;

  if (Length (exclstr) > 0) then
    RevBewDataSet.CommandText := RevBewDataSet.CommandText + (' and BEW_ART not in ('+exclstr+')');
  *)

  RevBewDataSet.CommandText := RevBewDataSet.CommandText + ' order by CREATE_DATE desc';

  RevBewDataSet.Tag := 1;

  try
    RevBewDataSet.Open;

    RevBewDBGrid.SetColumnVisible ('UPDATE_COUNT', False);
    RevBewDBGrid.SetColumnVisible ('MANDANT', False);
    RevBewDBGrid.SetColumnVisible ('LAGER', (fLagerRef = -1));

    DBGridUtils.SetGewichtDisplayFunctions (RevBewDataSet, 'NETTO_GEWICHT');
  finally
    RevBewDataSet.Tag := 0;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowQSProtokolleForm.CheckBoxClick(Sender: TObject);
begin
  Update;
  
  RevInvComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowQSProtokolleForm.DateTimePickerChange(Sender: TObject);
begin
  if not ((Sender as TDateTimePicker).DroppedDown) then begin
    if (Sender = BisDateTimePicker) Then begin
      if (BisDateTimePicker.Date < VonDateTimePicker.Date) then begin
        VonDateTimePicker.Time := 0;
        VonDateTimePicker.Date := Trunc (BisDateTimePicker.Date);
      end;

      VonDateTimePicker.MaxDate := Trunc (BisDateTimePicker.Date);
    end;

    RevInvComboBoxChange (Sender);
  end;
end;

end.
