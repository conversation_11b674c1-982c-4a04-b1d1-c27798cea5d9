unit LVSInvoiceInterface;

interface

function BillingAbrechnen (const RefMand, RefSubMand, RefLager, RefBillingMand : Integer; var RefInvoice : Integer) : Integer;

implementation

uses
  DB, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, LVSGlobalDaten, DatenModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 12.06.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function BillingAbrechnen (const RefMand, RefSubMand, RefLager, RefBillingMand : Integer; var RefInvoice : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  RefInvoice := -1;
  
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_INVOICE.CREATE_BILLING_INVOICE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, RefMand);
    Parameters.CreateParameter('pRefSubMand',ftInteger,pdInput, 12, GetPLSQLParameter (RefSubMand));
    Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, GetPLSQLParameter (RefLager));
    Parameters.CreateParameter('pRefBillingMand',ftInteger,pdInput, 12, GetPLSQLParameter (RefBillingMand));

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    RefInvoice := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  Result := dbres;
end;

end.
