object Form1: TForm1
  Left = 0
  Top = 0
  Caption = 'Form1'
  ClientHeight = 562
  ClientWidth = 916
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  DesignSize = (
    916
    562)
  PixelsPerInch = 96
  TextHeight = 13
  object GroupBox1: TGroupBox
    Left = 8
    Top = 8
    Width = 900
    Height = 249
    Anchors = [akLeft, akTop, akRight]
    Caption = 'GroupBox1'
    TabOrder = 0
    DesignSize = (
      900
      249)
    object Label1: TLabel
      Left = 448
      Top = 21
      Width = 76
      Height = 13
      Caption = 'Erkannte Fehler'
    end
    object Button3: TButton
      Left = 11
      Top = 213
      Width = 179
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'Ergebnisse '#252'bernehmen'
      TabOrder = 0
    end
    object ErrorDBGrid: TDBGridPro
      Left = 448
      Top = 37
      Width = 444
      Height = 201
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = ResultErrorDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      ReadOnly = True
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
  end
  object PickResultDBGrid: TDBGridPro
    Left = 8
    Top = 288
    Width = 900
    Height = 235
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = PickResultDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object Button1: TButton
    Left = 833
    Top = 529
    Width = 75
    Height = 25
    Caption = 'Schlie'#223'en'
    TabOrder = 2
  end
  object PickResultQuery: TSmartQuery
    Left = 672
    Top = 528
  end
  object ResultErrorQuery: TSmartQuery
    Left = 568
    Top = 528
  end
  object PickResultDataSource: TDataSource
    DataSet = PickResultQuery
    Left = 704
    Top = 528
  end
  object ResultErrorDataSource: TDataSource
    DataSet = ResultErrorQuery
    Left = 600
    Top = 528
  end
end
