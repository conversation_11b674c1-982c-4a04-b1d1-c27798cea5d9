object AbcConfEditForm: TAbcConfEditForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'ABC-Profil'
  ClientHeight = 651
  ClientWidth = 281
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnKeyDown = FormKeyDown
  DesignSize = (
    281
    651)
  TextHeight = 15
  object NameLabel: TLabel
    Left = 8
    Top = 8
    Width = 89
    Height = 15
    Caption = 'Name des Profils'
  end
  object AKlasseLabel: TLabel
    Left = 9
    Top = 72
    Width = 84
    Height = 15
    Caption = 'Klassengr'#246#223'e A:'
  end
  object BKlasseLabel: TLabel
    Left = 8
    Top = 104
    Width = 83
    Height = 15
    Caption = 'Klassengr'#246#223'e B:'
  end
  object ObereGrenzeBevel: TBevel
    Left = 8
    Top = 144
    Width = 265
    Height = 12
    Anchors = []
    Shape = bsTopLine
    ExplicitTop = 137
  end
  object BisLabel: TLabel
    Left = 140
    Top = 159
    Width = 15
    Height = 15
    Caption = 'Bis'
  end
  object VonLabel: TLabel
    Left = 12
    Top = 159
    Width = 20
    Height = 15
    Caption = 'Von'
  end
  object UntereGrenzeBevel: TBevel
    Left = 8
    Top = 320
    Width = 265
    Height = 12
    Anchors = []
    Shape = bsTopLine
    ExplicitTop = 305
  end
  object MandantLabel: TLabel
    Left = 8
    Top = 323
    Width = 64
    Height = 15
    Caption = 'Mandanten:'
  end
  object AuftragsartLabel: TLabel
    Left = 8
    Top = 473
    Width = 75
    Height = 15
    Caption = 'Auftragsarten:'
  end
  object AProzentLabel: TLabel
    Left = 151
    Top = 72
    Width = 10
    Height = 15
    Caption = '%'
  end
  object BProzentLabel: TLabel
    Left = 151
    Top = 104
    Width = 10
    Height = 15
    Caption = '%'
  end
  object NameEdit: TEdit
    Left = 8
    Top = 24
    Width = 265
    Height = 23
    TabOrder = 0
    Text = 'NameEdit'
  end
  object AKlasseEdit: TEdit
    Left = 99
    Top = 69
    Width = 46
    Height = 23
    TabOrder = 1
  end
  object BKlasseEdit: TEdit
    Left = 98
    Top = 101
    Width = 47
    Height = 23
    TabOrder = 2
  end
  object BisDateTimePicker: TDateTimePicker
    Left = 164
    Top = 155
    Width = 85
    Height = 21
    Date = 38189.000000000000000000
    Time = 0.713017673602735200
    TabOrder = 3
  end
  object VonDateTimePicker: TDateTimePicker
    Left = 42
    Top = 155
    Width = 85
    Height = 21
    Date = 38189.000000000000000000
    Time = 0.712940011559112500
    TabOrder = 4
  end
  object TypRadioGroup: TRadioGroup
    Left = 8
    Top = 192
    Width = 265
    Height = 105
    Hint = 'Bewertet die Artikel nach diesem Prameter'
    Caption = 'Eingrenzung nach:'
    ItemIndex = 0
    Items.Strings = (
      'Order-Positionen'
      'Menge'
      'Gewicht')
    TabOrder = 5
  end
  object MandantCheckListBox: TCheckListBox
    Left = 8
    Top = 344
    Width = 265
    Height = 120
    ItemHeight = 15
    TabOrder = 6
    OnClickCheck = MandantCheckListBoxClickCheck
  end
  object AuftragsartCheckListBox: TCheckListBox
    Left = 8
    Top = 494
    Width = 265
    Height = 120
    ItemHeight = 15
    TabOrder = 7
    OnClickCheck = AuftragsartCheckListBoxClickCheck
  end
  object AbortButton: TButton
    Left = 198
    Top = 620
    Width = 75
    Height = 25
    Caption = 'Abbrechen'
    TabOrder = 8
    OnClick = AbortButtonClick
  end
  object OkButton: TButton
    Left = 117
    Top = 620
    Width = 75
    Height = 25
    Caption = 'OK'
    TabOrder = 9
    OnClick = OkButtonClick
  end
  object ABCEditQuery: TOraQuery
    Session = LVSDatenModul.OraMainSession
    Left = 232
    Top = 120
  end
end
