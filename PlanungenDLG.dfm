object PlanungenForm: TPlanungenForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu]
  Caption = 'Planungen'
  ClientHeight = 405
  ClientWidth = 744
  Color = clBtnFace
  Constraints.MinHeight = 420
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poMainFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    744
    405)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 48
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Label2: TLabel
    Left = 8
    Top = 4
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Label3: TLabel
    Left = 8
    Top = 133
    Width = 66
    Height = 13
    Caption = 'Vorplanungen'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 91
    Width = 728
    Height = 7
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 64
    Width = 728
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 80
    ItemHeight = 15
    TabOrder = 1
    OnChange = LagerComboBoxChange
  end
  object MandantComboBox: TComboBoxPro
    Left = 8
    Top = 20
    Width = 728
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 80
    Enabled = False
    ItemHeight = 15
    TabOrder = 0
  end
  object PlanungenDBGrid: TDBGridPro
    Left = 8
    Top = 152
    Width = 617
    Height = 204
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = PlanungenDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 2
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    OnDblClick = PlanungenDBGridDblClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\CS'
    RegistrySection = 'DBGrids'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object NewButton: TButton
    Left = 634
    Top = 152
    Width = 105
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Neu..'
    TabOrder = 3
    OnClick = NewButtonClick
  end
  object ChangeButton: TButton
    Left = 634
    Top = 183
    Width = 105
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Bearbeiten...'
    TabOrder = 4
    OnClick = ChangeButtonClick
  end
  object ActiveButton: TButton
    Left = 634
    Top = 300
    Width = 105
    Height = 25
    Anchors = [akTop, akRight]
    Caption = #220'bernehmen...'
    TabOrder = 5
    OnClick = ActiveButtonClick
  end
  object DelButton: TButton
    Left = 634
    Top = 331
    Width = 105
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'L'#246'schen...'
    TabOrder = 6
    OnClick = DelButtonClick
    ExplicitTop = 324
  end
  object CloseButton: TButton
    Left = 634
    Top = 372
    Width = 105
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    TabOrder = 7
    OnClick = CloseButtonClick
  end
  object StatusComboBox: TComboBox
    Left = 8
    Top = 104
    Width = 289
    Height = 21
    Style = csDropDownList
    ItemHeight = 13
    ItemIndex = 0
    TabOrder = 8
    Text = 'Alle anzeigen'
    OnChange = StatusComboBoxChange
    Items.Strings = (
      'Alle anzeigen'
      'Nur die offenen Planungen anzeigen'
      'Nur die abgeschlossenen Planungen anzeigen')
  end
  object CopyButton: TButton
    Left = 634
    Top = 214
    Width = 105
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Stand kopieren...'
    TabOrder = 9
    OnClick = CopyButtonClick
  end
  object ViewButton: TButton
    Left = 634
    Top = 245
    Width = 105
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Planung anzeigen...'
    TabOrder = 10
    OnClick = ViewButtonClick
  end
  object PlanungenQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 256
    Top = 144
  end
  object PlanungenDataSource: TDataSource
    DataSet = PlanungenQuery
    OnDataChange = PlanungenDataSourceDataChange
    Left = 216
    Top = 144
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 296
    Top = 144
  end
end
