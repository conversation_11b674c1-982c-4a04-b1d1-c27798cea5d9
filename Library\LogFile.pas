﻿//******************************************************************************
//* Modul Name: LogFile
//* Author    : <PERSON> Graf
//******************************************************************************
//* $Revision: 28 $
//* $Date: 10.09.20 9:33 $
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
unit LogFile;

interface

uses
  Windows, SysUtils, Classes, Quick.Logger, Quick.Logger.Provider.Files;

type
  TLogLevels   = (clNormal, clNetDetail, clScanDetail, clDebug, clError, clTrace);
  TLogLevelSet = set of TLogLevels;

const
  TLogLevelStr : array [0..5] of String = ('Normal','NetDetail','ScanDetail','Debug','Error','Trace');

type
  TLogFile = class (TLogger)
  private
    fLogFileIsOpen : Boolean;
    fLogFileName   : String;
    fLogSession    : String;
    fLogLevel      : TLogLevelSet;
    fTraceLevel    : Integer;
    fLastError     : String;
    fLogDate       : TDateTime;
    fLogSize       : Integer;
    fLogCount      : Integer;
    fLogRotation   : Boolean;
    fLogDateName   : Boolean;

    fLogNamePart   : String;
    fLogExtPart    : String;

    fLogFileCount      : Integer;
    fLogFileLastOpen   : TDateTime;
    fLogFileLastAccess : TDateTime;

    fLoggerProvider : TLogFileProvider;

    procedure SetLogFileName (const Filename : String);
    procedure SetLogRotation (const Flag :Boolean);
    procedure SetLogDateName (const Flag :Boolean);
  public
    property LastError  : String    read fLastError;
    property IsOpen     : boolean   read fLogFileIsOpen;
    property LastOpen   : TDateTime read fLogFileLastOpen;
    property LastAccess : TDateTime read fLogFileLastAccess;

    constructor Create;
    destructor Destroy; override;

    procedure SetTraceLevel (const Level : Integer);

    function Open : Integer; overload;
    function Open    (const Filename : String) : Integer; overload;
    function Close : Integer;
    function Write   (const TextStr : String) : Integer; overload;
    function Write   (const Level : TLogLevels; const TextStr : String) : Integer; overload;
    function Logging (const Level : TLogLevels; const TextStr : String) : Integer; overload;
    function Logging (const Level : TLogLevels; const SectionStr, TextStr : String) : Integer; overload;

    function MultiLineLogging (const TextStr : String) : Integer;

    function TracingEnter (const TextStr : String) : Integer;
    function TracingLeave (const TextStr : String) : Integer;
    function Tracing      (const TextStr : String) : Integer;

    function ErrorLogging (const TextStr : String) : Integer;

    function CheckLogRotation : Boolean;
  published
    property TraceLevel  : Integer      read fTraceLevel  write SetTraceLevel;
    property LogSession  : String       read fLogSession  write fLogSession;
    property LogFileName : String       read fLogFileName write SetLogFileName;
    property LogDate     : TDateTime    read fLogDate;
    property LogLevel    : TLogLevelSet read fLogLevel    write fLogLevel;
    property LogRotation : Boolean      read fLogRotation write SetLogRotation;
    property LogDateName : Boolean      read fLogDateName write SetLogDateName;
    property LogSize     : Integer      read fLogSize     write fLogSize;
    property LogCount    : Integer      read fLogCount    write fLogCount;
  end;

var
  MasterLog : TLogFile;

implementation

uses
  {$ifdef Trace}
    Trace,
  {$endif}

   StringUtils, Win32Utils;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TLogFile.Create;
begin
  inherited Create;

  fLogDate       := 0;
  fLastError     := '';

  fLogCount      := 1;
  fLogFileCount  := -1;

  fLogRotation   := false;
  fLogFileIsOpen := FALSE;
  fLogFileLastOpen := 0;
  fLogFileName   := '';
  fLogSession    := '';
  fTraceLevel    := 1;
  fLogLevel      := [clNormal, clError];

  fLoggerProvider := TLogFileProvider.Create;

  Providers.Add (fLoggerProvider);
  with fLoggerProvider do begin
    DailyRotate := True;
    MaxFileSizeInMB := 25;
    LogLevel := LOG_ALL;
    MaxRotateFiles := 25;
    TimePrecission := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
destructor TLogFile.Destroy;
begin
  fLogFileIsOpen := FALSE;

  inherited Destroy;
end;

procedure TLogFile.SetTraceLevel (const Level : Integer);
begin
  if (Level > 0) then
    fTraceLevel := Level
  else
    fTraceLevel := 1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.Open : Integer;
begin
{$ifdef Trace}
  FunctionStart ('TLogFile.Open');
{$endif}

  fLastError := '';

  if (fLogFileIsOpen) Then begin
    CheckLogRotation;

    Result := 0
  end else begin
    if (Length (fLogFileName) = 0) Then
      Result := -5
    else begin
      Result := Open (fLogFileName)
    end;
  end;

{$ifdef Trace}
  FunctionStop (Result);
{$endif}
end;

procedure TLogFile.SetLogRotation (const Flag :Boolean);
begin
  if (fLogRotation <> Flag) then begin
    if (Length (fLogFileName) = 0) then
      fLogRotation := Flag
    else if fLogRotation then begin
      fLogRotation := False;

      SetLogFileName (fLogNamePart + fLogExtPart)
    end else begin
      fLogRotation := true;

      SetLogFileName (fLogFileName)
    end;
  end;
end;

procedure TLogFile.SetLogDateName (const Flag :Boolean);
begin
  if (fLogDateName <> Flag) then begin
    if (Length (fLogFileName) = 0) then
      fLogDateName := Flag
    else if fLogDateName then begin
      fLogDateName := False;

      SetLogFileName (fLogNamePart + fLogExtPart)
    end else begin
      fLogDateName := true;

      SetLogFileName (fLogFileName)
    end;
  end;
end;

procedure TLogFile.SetLogFileName (const Filename : String);
var
  fname  : String;
  fmask  : String;
  sr,
  lastsr : TSearchRec;
  res    : DWORD;
  strpos : Integer;
  cflag  : boolean;
begin
  if (fLogRotation) then begin
    cflag := False;

    if not (fLogDateName) then begin
      fLogExtPart  := ExtractFileExt(Filename);
      fLogNamePart := copy (Filename, 1, Length (Filename) - Length (fLogExtPart));
    end;

    if (fLogFileCount = -1) then begin
      FillChar (lastsr, sizeof (TSearchRec), 0);

      fmask := fLogNamePart + '_*' + fLogExtPart;

      res := FindFirst (fmask, faArchive ,sr);
      if (res = 0) then begin
        while (res = 0) do begin
          if (CompareFileTime (sr.FindData.ftLastWriteTime, lastsr.FindData.ftLastWriteTime) >= 0) then begin
            lastsr := sr;
          end;

          res := FindNext (sr);
        end;
      end;
      FindClose (sr);


      if (Length (lastsr.Name) = 0) then
        fLogFileCount := 0
      else begin
        strpos := Length (lastsr.Name) - 5;

        {$ifdef Unicode}
        if (CharInSet (lastsr.Name [strpos], ['0'..'9'])) and CharInSet (lastsr.Name [strpos + 1], ['0'..'9']) then
        {$else}
        if (lastsr.Name [strpos] in ['0'..'9']) and (lastsr.Name [strpos + 1] in ['0'..'9']) then
        {$endif}
          fLogFileCount := (ord (lastsr.Name [strpos]) - ord ('0')) * 10 + (ord (lastsr.Name [strpos + 1]) - ord ('0'))
        else
          fLogFileCount := 0;

        if (lastsr.Size > fLogSize) then begin
          cflag := true;

          fLogFileCount := (fLogFileCount + 1) mod fLogCount;
        end;
      end;
    end;

    fname := fLogNamePart + '_' + FormatIntToStr (fLogFileCount, 2) + fLogExtPart;

    if (cflag) then
      DeleteFile (fname);
  end else if fLogDateName then begin
    fLogExtPart  := ExtractFileExt(Filename);
    fLogNamePart := copy (Filename, 1, Length (Filename) - Length (fLogExtPart));

    fLogDate := Now;

    fname := fLogNamePart + '_'+FormatDateTime ('yyyymmdd', fLogDate) + fLogExtPart;
  end else begin
    fname := Filename;
  end;

  if (fLogFileName <> fname) then begin
    if not fLogFileIsOpen then
      fLogFileName := fname
    else begin
      Close;

      fLogFileName := fname;

      Open;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.CheckLogRotation : Boolean;
begin
  Result := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.Open (const Filename : String) : Integer;
var
  pathstr : String;
begin
{$ifdef Trace}
  FunctionStart ('TLogFile.Open');
  TraceParameter ('Filename', Filename);
{$endif}

  Result := 0;
  fLastError := '';

  pathstr := ExtractFilePath (Filename);

  if not (DirectoryExists (pathstr)) then
    ForceDirectories (pathstr);

  fLoggerProvider.FileName := FileName;
  fLoggerProvider.RotatedFilesPath := ExtractFilePath (FileName) + 'RotatedLogs';

  if (clTrace in fLogLevel) then
    fLoggerProvider.LogLevel := fLoggerProvider.LogLevel + [etTrace];

  if (clDebug in fLogLevel) then
    fLoggerProvider.LogLevel := fLoggerProvider.LogLevel + [etDebug, etTrace];

  fLoggerProvider.Enabled := True;

  fLogFileIsOpen     := true;
  fLogFileLastOpen   := Now;
  fLogFileLastAccess := Now;
{$ifdef Trace}
  FunctionStop (Result);
{$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.Close : Integer;
begin
{$ifdef Trace}
  FunctionStart ('TLogFile.Close');
{$endif}

  fLastError := '';

  fLogFileIsOpen := false;

  fLoggerProvider.Enabled := false;
{$ifdef Trace}
  FunctionStop (Result);
{$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.MultiLineLogging (const TextStr : String) : Integer;
var
  strpos  : Integer;
  infostr : String;
begin
  Result := 0;

  infostr := TextStr;

  //CR+LF am Ende abschneiden
  if (Length (infostr) > 1) and (Copy (infostr, Length (infostr) - 1, 2) = #13+#10) then
    Delete (infostr, Length (infostr) - 1, 2);

  strpos := Pos (^M, infostr);
  if (strpos = 0) then begin
    Info (infostr);
  end else begin
    while (strpos > 0) do begin
      Info (Copy (infostr, 1, strpos - 1));

      Delete (infostr, 1, strpos);

      strpos := Pos (^M, infostr);
    end;

    if (Length (infostr) > 0) then begin
      Info (infostr)
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.Write (const Level : TLogLevels; const TextStr : String) : Integer;
begin
  fLastError := '';

  if (Level in fLogLevel) then
    Info (TLogLevelStr [ord (Level)]+';'+TextStr)
  else
    Result := 0;

  fLogFileLastAccess := Now;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.Logging (const Level : TLogLevels; const TextStr : String) : Integer;
var
  flag : Boolean;
begin
  Result := 0;

  if (Level in fLogLevel) then begin
    Info (TLogLevelStr [ord (Level)]+';'+TextStr);
  end;

  fLogFileLastAccess := Now;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.Logging (const Level : TLogLevels; const SectionStr, TextStr : String) : Integer;
var
  flag : Boolean;
begin
  Result := 0;

  if (Level in fLogLevel) then begin
    Info (TLogLevelStr [ord (Level)]+';'+SectionStr+';'+TextStr);
  end;

  fLogFileLastAccess := Now;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.Tracing (const TextStr : String) : Integer;
var
  flag : Boolean;
begin
  Result := 0;

  Trace (StringOfChar (' ', fTraceLevel) + TextStr);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.TracingEnter (const TextStr : String) : Integer;
var
  flag : Boolean;
begin
  Result := 0;

  Trace (StringOfChar (' ', fTraceLevel) + TextStr);

  Inc (fTraceLevel);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.TracingLeave (const TextStr : String) : Integer;
var
  flag : Boolean;
begin
  Result := 0;

  if (fTraceLevel > 1) then
    Dec (fTraceLevel);

  Trace (StringOfChar (' ', fTraceLevel) + TextStr);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.Write (const TextStr : String) : Integer;
begin
{$ifdef Trace}
  FunctionStart ('TLogFile.Write');
  TraceParameter ('TextStr', TextStr);
{$endif}

  Info (TextStr);

  fLogFileLastAccess := Now;

{$ifdef Trace}
  FunctionStop (Result);
{$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLogFile.ErrorLogging (const TextStr : String) : Integer;
begin
{$ifdef Trace}
  FunctionStart ('TLogFile.ErrorLogging');
  TraceParameter ('TextStr', TextStr);
{$endif}

  Error (TextStr);

  fLogFileLastAccess := Now;

{$ifdef Trace}
  FunctionStop (Result);
{$endif}
end;

initialization
  MasterLog := Nil;

finalization
  if Assigned (MasterLog) then
    FreeAndNil (MasterLog);

end.
