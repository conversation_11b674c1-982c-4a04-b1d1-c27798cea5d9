unit DMSDatenInterface;

interface

function GetDokumentSeq     (const RefMand : Integer; var ArchivePath : String; var SeqNr : Integer) : Integer;

function InsertLieferschein       (const AufRef  : Integer; const Filename : String; var Ref : Integer) : Integer;
function InsertSammelLieferschein (const LiefRef : Integer; const Filename : String; var Ref : Integer) : Integer;
function InsertProforma           (const AufRef  : Integer; const Filename : String; var Ref : Integer) : Integer;
function InsertVerladeliste       (const ListRef : Integer; const Filename : String; var Ref : Integer) : Integer;
function InsertFrachtbrief        (const AufRef  : Integer; const Filename : String; var Ref : Integer) : Integer;
function InsertDokument           (const RefMand : Integer; const RefGroup : Integer; const Description, Filename : String; var Ref : Integer) : Integer; overload;
function InsertDokument           (const RefMand : Integer; const RefGroup : Integer; const Title, Description, Filename : String; var Ref : Integer) : Integer; overload;
function InsertDokumentIndex      (const RefDoc  : Integer; const RefGroup : Integer; const IndexID, IndexValue : String) : Integer;

function FindLieferschein         (const AufRef  : Integer; var Description, Filename : String; var Ref : Integer) : Integer;
function FindSammelLieferschein   (const LiefRef : Integer; var Description, Filename : String; var Ref : Integer) : Integer;
function FindProforma             (const AufRef  : Integer; var Description, Filename : String; var Ref : Integer) : Integer;
function FindVerladeliste         (const ListRef : Integer; var Description, Filename : String; var Ref : Integer) : Integer;
function FindFrachtbrief          (const ListRef : Integer; var Description, Filename : String; var Ref : Integer) : Integer;

function FindWaWiLieferschein     (const RefMand : Integer; const AufNr : String;  var Description, Filename : String; var Ref : Integer) : Integer;
function FindWaWiProforma         (const RefMand : Integer; const AufNr : String;  var Description, Filename : String; var Ref : Integer) : Integer;
function FindWaWiRechnung         (const RefMand : Integer; const AufNr : String;  var Description, Filename : String; var Ref : Integer) : Integer;
function FindDokument             (const RefMand : Integer; const AufNr, DocType : String;  var Description, Filename : String; var Ref : Integer) : Integer; overload;
function FindDokument             (const RefDoc : Integer; var Description, Filename : String; var Ref : Integer) : Integer; overload;
function FindDokument             (const RefDoc : Integer; var Title, Description, Filename : String; var Ref : Integer) : Integer; overload;

implementation

uses
  {$ifdef TRACE}
    Trace,
  {$endif}

  Windows, Classes,

  {$IFDEF VER360}
    System.IOUtils,
  {$endif}

  SysUtils, DB, ADODB, DatenModul, Variants, LVSGlobalDaten;

function GetDMSTempFileName : String;
var
  fname : String;
begin
  fname := System.IOUtils.TPath.GetTempFileName;

  Result := ExtractFilePath (fname)+'dms'+ChangeFileExt (ExtractFileName (fname), '.pdf');
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetDokumentSeq (const RefMand : Integer; var ArchivePath : String; var SeqNr : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.GET_NEXT_DOCUMENT_SEQ';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 32, RefMand);

    Parameters.CreateParameter('oArchiveDir',ftString,pdOutput, 256, NULL);
    Parameters.CreateParameter('oArchiveSeq',ftInteger,pdOutput, 32, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.FindParam ('oArchiveDir').Value) then
      ArchivePath := ''
    else ArchivePath := StoredProcedure.Parameters.ParamValues ['oArchiveDir'];

    if VarIsNull (StoredProcedure.Parameters.FindParam ('oArchiveSeq').Value) then
      SeqNr := -1
    else SeqNr := StoredProcedure.Parameters.ParamValues ['oArchiveSeq'];
  end else begin
    ArchivePath := '';
    SeqNr := -1;
  end;

  StoredProcedure.Free;

  Result := dbres;
end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertLieferschein (const AufRef : Integer; const Filename : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.INSERT_LIEFERSCHEIN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 32, AufRef);

    Parameters.CreateParameter('pFilename',ftString,pdInput, 256, Filename);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else Ref := -1;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertFrachtbrief (const AufRef : Integer; const Filename : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.INSERT_FRACHTBRIEF';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 32, AufRef);

    Parameters.CreateParameter('pFilename',ftString,pdInput, 256, Filename);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else Ref := -1;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 26.01.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertDokument (const RefMand : Integer; const RefGroup : Integer; const Description, Filename : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  try
    with StoredProcedure do begin
      ProcedureName := 'PA_DMS_VERWALTUNG.INSERT_DOKUMENT';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 32, RefMand);
      Parameters.CreateParameter('pRefGroup',ftInteger,pdInput, 32, GetPLSQLParameter (RefGroup));
      Parameters.CreateParameter('pDescription',ftString,pdInput, 256, Description);
      Parameters.CreateParameter('pFilename',ftString,pdInput, 256, Filename);
      Parameters.CreateParameter('pContend',ftBlob,pdInput, 4000, NULL);

      Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

    if (dbres = 0) then begin
      if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
        Ref := -1
      else Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
    end else Ref := -1;
  finally
    StoredProcedure.Free;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 26.01.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertDokument (const RefMand : Integer; const RefGroup : Integer; const Title, Description, Filename : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  try
    with StoredProcedure do begin
      ProcedureName := 'PA_DMS_VERWALTUNG.INSERT_DOKUMENT';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 32, RefMand);
      Parameters.CreateParameter('pRefGroup',ftInteger,pdInput, 32, GetPLSQLParameter (RefGroup));
      Parameters.CreateParameter('pTitle',ftString,pdInput, 64, copy (Title, 1, 64));
      Parameters.CreateParameter('pDescription',ftString,pdInput, 1024, copy (Description, 1, 1024));
      Parameters.CreateParameter('pFilename',ftString,pdInput, 256, Filename);
      Parameters.CreateParameter('pContend',ftBlob,pdInput, 4000, NULL);

      Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

    if (dbres = 0) then begin
      if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
        Ref := -1
      else Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
    end else Ref := -1;
  finally
    StoredProcedure.Free;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 26.01.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertDokumentIndex (const RefDoc  : Integer; const RefGroup : Integer; const IndexID, IndexValue : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  try
    with StoredProcedure do begin
      ProcedureName := 'PA_DMS_VERWALTUNG.INSERT_INDEX';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRefFile',ftInteger,pdInput, 32, RefDoc);
      Parameters.CreateParameter('pRefGroup',ftInteger,pdInput, 32, GetPLSQLParameter (RefGroup));
      Parameters.CreateParameter('pIndexID',ftString,pdInput, 32, IndexID);
      Parameters.CreateParameter('pIndexValue',ftString,pdInput, 32, IndexValue);

      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
  finally
    StoredProcedure.Free;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertSammelLieferschein (const LiefRef : Integer; const Filename : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.INSERT_SAMMEL_LIEFERSCHEIN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 32, LiefRef);

    Parameters.CreateParameter('pFilename',ftString,pdInput, 256, Filename);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else Ref := -1;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertProforma (const AufRef : Integer; const Filename : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.INSERT_PROFORMA';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 32, AufRef);

    (*
    Parameters.CreateParameter('pContend', ftBlob, pdInput, 1000000, NULL);
    Parameters.ParamByName('pContend').LoadFromFile (Filename, ftBlob);
    *)

    Parameters.CreateParameter('pFilename',ftString,pdInput, 256, Filename);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else Ref := -1;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertVerladeliste (const ListRef : Integer; const Filename : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.INSERT_VERLADELISTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefListe',ftInteger,pdInput, 32, ListRef);

    (*
    Parameters.CreateParameter('pContend', ftBlob, pdInput, 1000000, NULL);
    Parameters.ParamByName('pContend').LoadFromFile (Filename, ftBlob);
    *)

    Parameters.CreateParameter('pFilename',ftString,pdInput, 256, Filename);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else Ref := -1;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindLieferschein (const AufRef : Integer; var Description, Filename : String; var Ref : Integer) : Integer;
var
  len,
  dbres : Integer;
  path,
  cname : array [0..255] of char;
  StoredProcedure : TADOStoredProc;
  BinData: OleVariant;
  DataPtr: Pointer;
  Stream: TFileStream;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.GET_LIEFERSCHEIN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 32, AufRef);
    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oDescription',ftString,pdOutput, 1024, '');
    Parameters.CreateParameter('oContend',ftBlob,pdOutput, 1000000, NULL);
    Parameters.CreateParameter('oFilename',ftString,pdOutput, 1024, '');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.ParamByName('oDescription').Value) Then
      Description := ''
    else begin
      Description := StoredProcedure.Parameters.ParamValues ['oDescription'];
    end;

    if VarIsNull (StoredProcedure.Parameters.ParamByName('oContend').Value) Then begin
      if VarIsNull (StoredProcedure.Parameters.ParamByName('oFilename').Value) Then
        Filename := ''
      else
        Filename := StoredProcedure.Parameters.ParamValues ['oFilename'];
    end else begin
      Filename := GetDMSTempFileName;

      BinData := StoredProcedure.Parameters.ParamByName('oContend').Value;
      DataPtr := VarArrayLock(BinData);
      try
        Stream := TFileStream.Create (FileName, fmCreate);
        try
          len := VarArrayHighBound (BinData, 1) - VarArrayLowBound (BinData, 1) + 1;
          Stream.Write (DataPtr^, len);
        finally
          Stream.Free;
        end;
      finally
        VarArrayUnlock(BinData);
      end;
    end;

    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else
      Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else begin
    Ref := -1;
    Filename := '';
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindSammelLieferschein (const LiefRef : Integer; var Description, Filename : String; var Ref : Integer) : Integer;
var
  len,
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
  BinData: OleVariant;
  DataPtr: Pointer;
  Stream: TFileStream;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.GET_SAMMEL_LIEFERSCHEIN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefLief',ftInteger,pdInput, 32, LiefRef);
    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oDescription',ftString,pdOutput, 1024, '');
    Parameters.CreateParameter('oContend',ftBlob,pdOutput, 1000000, NULL);
    Parameters.CreateParameter('oFilename',ftString,pdOutput, 1024, '');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.ParamByName('oDescription').Value) Then
      Description := ''
    else begin
      Description := StoredProcedure.Parameters.ParamValues ['oDescription'];
    end;

    if VarIsNull (StoredProcedure.Parameters.ParamByName('oContend').Value) Then begin
      if VarIsNull (StoredProcedure.Parameters.ParamByName('oFilename').Value) Then
        Filename := ''
      else
        Filename := StoredProcedure.Parameters.ParamValues ['oFilename'];
    end else begin
      Filename := GetDMSTempFileName;

      BinData := StoredProcedure.Parameters.ParamByName('oContend').Value;
      DataPtr := VarArrayLock(BinData);
      try
        Stream := TFileStream.Create (FileName, fmCreate);
        try
          len := VarArrayHighBound (BinData, 1) - VarArrayLowBound (BinData, 1) + 1;
          Stream.Write (DataPtr^, len);
        finally
          Stream.Free;
        end;
      finally
        VarArrayUnlock(BinData);
      end;
    end;

    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else
      Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else begin
    Ref := -1;
    Filename := '';
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindProforma       (const AufRef  : Integer; var Description, Filename : String; var Ref : Integer) : Integer;
var
  len,
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
  BinData: OleVariant;
  DataPtr: Pointer;
  Stream: TFileStream;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.GET_PROFORMA';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 32, AufRef);
    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oDescription',ftString,pdOutput, 1024, '');
    Parameters.CreateParameter('oContend',ftBlob,pdOutput, 1000000, NULL);
    Parameters.CreateParameter('oFilename',ftString,pdOutput, 1024, '');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.ParamByName('oDescription').Value) Then
      Description := ''
    else begin
      Description := StoredProcedure.Parameters.ParamValues ['oDescription'];
    end;

    if VarIsNull (StoredProcedure.Parameters.ParamByName('oContend').Value) Then begin
      if VarIsNull (StoredProcedure.Parameters.ParamByName('oFilename').Value) Then
        Filename := ''
      else
        Filename := StoredProcedure.Parameters.ParamValues ['oFilename'];
    end else begin
      Filename := GetDMSTempFileName;

      BinData := StoredProcedure.Parameters.ParamByName('oContend').Value;
      DataPtr := VarArrayLock(BinData);
      try
        Stream := TFileStream.Create (FileName, fmCreate);
        try
          len := VarArrayHighBound (BinData, 1) - VarArrayLowBound (BinData, 1) + 1;
          Stream.Write (DataPtr^, len);
        finally
          Stream.Free;
        end;
      finally
        VarArrayUnlock(BinData);
      end;
    end;

    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else
      Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else begin
    Ref := -1;
    Filename := '';
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindWaWiLieferschein  (const RefMand : Integer; const AufNr : String;  var Description, Filename : String; var Ref : Integer) : Integer;
var
  len,
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
  BinData: OleVariant;
  DataPtr: Pointer;
  Stream: TFileStream;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.GET_LIEFERSCHEIN_WAWI';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 32, RefMand);
    Parameters.CreateParameter('pAuftragNr',ftString,pdInput, AuftragNrSize, AufNr);
    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oDescription',ftString,pdOutput, 1024, '');
    Parameters.CreateParameter('oContend',ftBlob,pdOutput, 1000000, NULL);
    Parameters.CreateParameter('oFilename',ftString,pdOutput, 1024, '');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.ParamByName('oDescription').Value) Then
      Description := ''
    else begin
      Description := StoredProcedure.Parameters.ParamValues ['oDescription'];
    end;

    if VarIsNull (StoredProcedure.Parameters.ParamByName('oContend').Value) Then begin
      if VarIsNull (StoredProcedure.Parameters.ParamByName('oFilename').Value) Then
        Filename := ''
      else
        Filename := StoredProcedure.Parameters.ParamValues ['oFilename'];
    end else begin
      Filename := GetDMSTempFileName;

      BinData := StoredProcedure.Parameters.ParamByName('oContend').Value;
      DataPtr := VarArrayLock(BinData);
      try
        Stream := TFileStream.Create (FileName, fmCreate);
        try
          len := VarArrayHighBound (BinData, 1) - VarArrayLowBound (BinData, 1) + 1;
          Stream.Write (DataPtr^, len);
        finally
          Stream.Free;
        end;
      finally
        VarArrayUnlock(BinData);
      end;
    end;

    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else
      Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else begin
    Ref := -1;
    Filename := '';
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindWaWiProforma  (const RefMand : Integer; const AufNr : String;  var Description, Filename : String; var Ref : Integer) : Integer;
var
  len,
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
  BinData: OleVariant;
  DataPtr: Pointer;
  Stream: TFileStream;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.GET_PROFORMA_WAWI';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 32, RefMand);
    Parameters.CreateParameter('pAuftragNr',ftString,pdInput, AuftragNrSize, AufNr);
    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oDescription',ftString,pdOutput, 1024, '');
    Parameters.CreateParameter('oContend',ftBlob,pdOutput, 1000000, NULL);
    Parameters.CreateParameter('oFilename',ftString,pdOutput, 1024, '');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.ParamByName('oDescription').Value) Then
      Description := ''
    else begin
      Description := StoredProcedure.Parameters.ParamValues ['oDescription'];
    end;

    if VarIsNull (StoredProcedure.Parameters.ParamByName('oContend').Value) Then begin
      if VarIsNull (StoredProcedure.Parameters.ParamByName('oFilename').Value) Then
        Filename := ''
      else
        Filename := StoredProcedure.Parameters.ParamValues ['oFilename'];
    end else begin
      Filename := GetDMSTempFileName;

      BinData := StoredProcedure.Parameters.ParamByName('oContend').Value;
      DataPtr := VarArrayLock(BinData);
      try
        Stream := TFileStream.Create (FileName, fmCreate);
        try
          len := VarArrayHighBound (BinData, 1) - VarArrayLowBound (BinData, 1) + 1;
          Stream.Write (DataPtr^, len);
        finally
          Stream.Free;
        end;
      finally
        VarArrayUnlock(BinData);
      end;
    end;

    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else
      Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else begin
    Ref := -1;
    Filename := '';
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindWaWiRechnung (const RefMand : Integer; const AufNr : String;  var Description, Filename : String; var Ref : Integer) : Integer;
var
  len,
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
  BinData: OleVariant;
  DataPtr: Pointer;
  Stream: TFileStream;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.GET_RECHNUNG_WAWI';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 32, RefMand);
    Parameters.CreateParameter('pAuftragNr',ftString,pdInput, AuftragNrSize, AufNr);
    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oDescription',ftString,pdOutput, 1024, '');
    Parameters.CreateParameter('oContend',ftBlob,pdOutput, 1000000, NULL);
    Parameters.CreateParameter('oFilename',ftString,pdOutput, 1024, '');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.ParamByName('oDescription').Value) Then
      Description := ''
    else begin
      Description := StoredProcedure.Parameters.ParamValues ['oDescription'];
    end;

    if VarIsNull (StoredProcedure.Parameters.ParamByName('oContend').Value) Then begin
      if VarIsNull (StoredProcedure.Parameters.ParamByName('oFilename').Value) Then
        Filename := ''
      else
        Filename := StoredProcedure.Parameters.ParamValues ['oFilename'];
    end else begin
      Filename := GetDMSTempFileName;

      BinData := StoredProcedure.Parameters.ParamByName('oContend').Value;
      DataPtr := VarArrayLock(BinData);
      try
        Stream := TFileStream.Create (FileName, fmCreate);
        try
          len := VarArrayHighBound (BinData, 1) - VarArrayLowBound (BinData, 1) + 1;
          Stream.Write (DataPtr^, len);
        finally
          Stream.Free;
        end;
      finally
        VarArrayUnlock(BinData);
      end;
    end;

    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else
      Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else begin
    Ref := -1;
    Filename := '';
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindVerladeliste (const ListRef : Integer; var Description, Filename : String; var Ref : Integer) : Integer;
var
  len,
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
  BinData: OleVariant;
  DataPtr: Pointer;
  Stream: TFileStream;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.GET_VERLADELISTE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefListe',ftInteger,pdInput, 32, ListRef);
    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oDescription',ftString,pdOutput, 1024, '');
    Parameters.CreateParameter('oContend',ftBlob,pdOutput, 1000000, NULL);
    Parameters.CreateParameter('oFilename',ftString,pdOutput, 1024, '');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.ParamByName('oDescription').Value) Then
      Description := ''
    else begin
      Description := StoredProcedure.Parameters.ParamValues ['oDescription'];
    end;

    if VarIsNull (StoredProcedure.Parameters.ParamByName('oContend').Value) Then begin
      if VarIsNull (StoredProcedure.Parameters.ParamByName('oFilename').Value) Then
        Filename := ''
      else
        Filename := StoredProcedure.Parameters.ParamValues ['oFilename'];
    end else begin
      Filename := GetDMSTempFileName;

      BinData := StoredProcedure.Parameters.ParamByName('oContend').Value;
      DataPtr := VarArrayLock(BinData);
      try
        Stream := TFileStream.Create (FileName, fmCreate);
        try
          len := VarArrayHighBound (BinData, 1) - VarArrayLowBound (BinData, 1) + 1;
          Stream.Write (DataPtr^, len);
        finally
          Stream.Free;
        end;
      finally
        VarArrayUnlock(BinData);
      end;
    end;

    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else
      Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else begin
    Ref := -1;
    Filename := '';
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindFrachtbrief (const ListRef : Integer; var Description, Filename : String; var Ref : Integer) : Integer;
var
  len,
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
  BinData: OleVariant;
  DataPtr: Pointer;
  Stream: TFileStream;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.GET_FRACHTBRIEF';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefListe',ftInteger,pdInput, 32, ListRef);
    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oDescription',ftString,pdOutput, 1024, '');
    Parameters.CreateParameter('oContend',ftBlob,pdOutput, 1000000, NULL);
    Parameters.CreateParameter('oFilename',ftString,pdOutput, 1024, '');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.ParamByName('oDescription').Value) Then
      Description := ''
    else begin
      Description := StoredProcedure.Parameters.ParamValues ['oDescription'];
    end;

    if VarIsNull (StoredProcedure.Parameters.ParamByName('oContend').Value) Then begin
      if VarIsNull (StoredProcedure.Parameters.ParamByName('oFilename').Value) Then
        Filename := ''
      else
        Filename := StoredProcedure.Parameters.ParamValues ['oFilename'];
    end else begin
      Filename := GetDMSTempFileName;

      BinData := StoredProcedure.Parameters.ParamByName('oContend').Value;
      DataPtr := VarArrayLock(BinData);
      try
        Stream := TFileStream.Create (FileName, fmCreate);
        try
          len := VarArrayHighBound (BinData, 1) - VarArrayLowBound (BinData, 1) + 1;
          Stream.Write (DataPtr^, len);
        finally
          Stream.Free;
        end;
      finally
        VarArrayUnlock(BinData);
      end;
    end;

    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else
      Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else begin
    Ref := -1;
    Filename := '';
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindDokument (const RefMand : Integer; const AufNr, DocType : String;  var Description, Filename : String; var Ref : Integer) : Integer;
var
  len,
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
  BinData: OleVariant;
  DataPtr: Pointer;
  Stream: TFileStream;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.GET_ORDER_DOCUMENT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 32, RefMand);
    Parameters.CreateParameter('pAuftragNr',ftString,pdInput, AuftragNrSize, AufNr);
    Parameters.CreateParameter('pDocType',ftString,pdInput, 32, DocType);
    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oDescription',ftString,pdOutput, 1024, '');
    Parameters.CreateParameter('oContend',ftBlob,pdOutput, 1000000, NULL);
    Parameters.CreateParameter('oFilename',ftString,pdOutput, 1024, '');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if VarIsNull (StoredProcedure.Parameters.ParamByName('oDescription').Value) Then
      Description := ''
    else begin
      Description := StoredProcedure.Parameters.ParamValues ['oDescription'];
    end;

    if VarIsNull (StoredProcedure.Parameters.ParamByName('oContend').Value) Then begin
      if VarIsNull (StoredProcedure.Parameters.ParamByName('oFilename').Value) Then
        Filename := ''
      else
        Filename := StoredProcedure.Parameters.ParamValues ['oFilename'];
    end else begin
      Filename := GetDMSTempFileName;

      BinData := StoredProcedure.Parameters.ParamByName('oContend').Value;
      DataPtr := VarArrayLock(BinData);
      try
        Stream := TFileStream.Create (FileName, fmCreate);
        try
          len := VarArrayHighBound (BinData, 1) - VarArrayLowBound (BinData, 1) + 1;
          Stream.Write (DataPtr^, len);
        finally
          Stream.Free;
        end;
      finally
        VarArrayUnlock(BinData);
      end;
    end;

    if VarIsNull (StoredProcedure.Parameters.FindParam ('oRef').Value) then
      Ref := -1
    else
      Ref := StoredProcedure.Parameters.ParamValues ['oRef'];
  end else begin
    Ref := -1;
    Filename := '';
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 07.02.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindDokument (const RefDoc : Integer; var Description, Filename : String; var Ref : Integer) : Integer;
var
  len,
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
  BinData: OleVariant;
  DataPtr: Pointer;
  Stream: TFileStream;
begin
  {$ifdef Trace}
    FunctionStart ('FindDokument');
    TraceParameter ('RefDoc', RefDoc);
  {$endif}

  Ref := -1;
  Description := '';
  Filename := '';

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_DMS_VERWALTUNG.GET_DOKUMENT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefDoc',ftInteger,pdInput, 32, RefDoc);
    Parameters.CreateParameter('oDescription',ftString,pdOutput, 1024, '');
    Parameters.CreateParameter('oContend',ftBlob,pdOutput, 1000000, NULL);
    Parameters.CreateParameter('oFilename',ftString,pdOutput, 1024, '');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    Ref := RefDoc;

    if VarIsNull (StoredProcedure.Parameters.ParamByName('oDescription').Value) Then
      Description := ''
    else begin
      Description := StoredProcedure.Parameters.ParamValues ['oDescription'];
    end;

    if VarIsNull (StoredProcedure.Parameters.ParamByName('oContend').Value) Then begin
      if VarIsNull (StoredProcedure.Parameters.ParamByName('oFilename').Value) Then
        Filename := ''
      else
        Filename := StoredProcedure.Parameters.ParamValues ['oFilename'];
    end else begin
      Filename := GetDMSTempFileName;

      BinData := StoredProcedure.Parameters.ParamByName('oContend').Value;
      DataPtr := VarArrayLock(BinData);
      try
        Stream := TFileStream.Create (FileName, fmCreate);
        try
          len := VarArrayHighBound (BinData, 1) - VarArrayLowBound (BinData, 1) + 1;
          Stream.Write (DataPtr^, len);
        finally
          Stream.Free;
        end;
      finally
        VarArrayUnlock(BinData);
      end;
    end;
  end else begin
    Ref := -1;
    Filename := '';
  end;

  StoredProcedure.Free;

  Result := dbres;

  {$ifdef Trace}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 27.01.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindDokument (const RefDoc : Integer; var Title, Description, Filename : String; var Ref : Integer) : Integer;
var
  len,
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
  BinData: OleVariant;
  DataPtr: Pointer;
  Stream: TFileStream;
begin
  {$ifdef Trace}
    FunctionStart ('FindDokument');
    TraceParameter ('RefDoc', RefDoc);
  {$endif}

  Ref := -1;
  Title := '';
  Description := '';
  Filename := '';

  StoredProcedure := TADOStoredProc.Create (Nil);

  try
    with StoredProcedure do begin
      ProcedureName := 'PA_DMS_VERWALTUNG.GET_DOKUMENT';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

      Parameters.CreateParameter('pRefDoc',ftInteger,pdInput, 32, RefDoc);
      Parameters.CreateParameter('oTitle',ftString,pdOutput, 64, '');
      Parameters.CreateParameter('oDescription',ftString,pdOutput, 1024, '');
      Parameters.CreateParameter('oContend',ftBlob,pdOutput, 1000000, NULL);
      Parameters.CreateParameter('oFilename',ftString,pdOutput, 1024, '');

      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

    if (dbres = 0) then begin
      Ref := RefDoc;

      if VarIsNull (StoredProcedure.Parameters.ParamByName('oTitle').Value) Then
        Title := ''
      else begin
        Title := StoredProcedure.Parameters.ParamValues ['oTitle'];
      end;

      if VarIsNull (StoredProcedure.Parameters.ParamByName('oDescription').Value) Then
        Description := ''
      else begin
        Description := StoredProcedure.Parameters.ParamValues ['oDescription'];
      end;

      if VarIsNull (StoredProcedure.Parameters.ParamByName('oContend').Value) Then begin
        if VarIsNull (StoredProcedure.Parameters.ParamByName('oFilename').Value) Then
          Filename := ''
        else
          Filename := StoredProcedure.Parameters.ParamValues ['oFilename'];
      end else begin
        Filename := GetDMSTempFileName;

        BinData := StoredProcedure.Parameters.ParamByName('oContend').Value;
        DataPtr := VarArrayLock(BinData);
        try
          Stream := TFileStream.Create (FileName, fmCreate);
          try
            len := VarArrayHighBound (BinData, 1) - VarArrayLowBound (BinData, 1) + 1;
            Stream.Write (DataPtr^, len);
          finally
            Stream.Free;
          end;
        finally
          VarArrayUnlock(BinData);
        end;
      end;
    end else begin
      Ref := -1;
      Filename := '';
    end;
  finally
    StoredProcedure.Free;
  end;

  Result := dbres;
end;

end.
