object MHDUeberhangListeForm: TMHDUeberhangListeForm
  Left = 0
  Top = 0
  Caption = '<PERSON><PERSON><PERSON><PERSON> einer '#220'berhangliste'
  ClientHeight = 654
  ClientWidth = 880
  Color = clBtnFace
  Constraints.MinHeight = 530
  Constraints.MinWidth = 850
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnResize = FormResize
  OnShow = FormShow
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 880
    Height = 217
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    ExplicitWidth = 874
    DesignSize = (
      880
      217)
    object Label3: TLabel
      Left = 8
      Top = 200
      Width = 45
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Best'#228'nde'
      ExplicitTop = 168
    end
    object Label5: TLabel
      Left = 8
      Top = 11
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object Label6: TLabel
      Left = 8
      Top = 39
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Label8: TLabel
      Left = 8
      Top = 80
      Width = 64
      Height = 13
      Caption = 'Artikelgruppe'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 66
      Width = 858
      Height = 7
      Shape = bsTopLine
    end
    object Label9: TLabel
      Left = 8
      Top = 112
      Width = 43
      Height = 13
      Caption = 'Lieferant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 88
      Top = 8
      Width = 784
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = MandantComboBoxChange
      ExplicitWidth = 778
    end
    object LagerComboBox: TComboBoxPro
      Left = 88
      Top = 36
      Width = 784
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 1
      OnChange = LagerComboBoxChange
      ExplicitWidth = 778
    end
    object VorlaufGroupBox: TGroupBox
      Left = 88
      Top = 137
      Width = 784
      Height = 54
      Anchors = [akLeft, akTop, akRight]
      Caption = 'Vorlauf in Tagen'
      TabOrder = 4
      ExplicitWidth = 778
      DesignSize = (
        784
        54)
      object Label2: TLabel
        Left = 175
        Top = 25
        Width = 24
        Height = 13
        Anchors = [akLeft, akBottom]
        Caption = 'Tage'
      end
      object Label7: TLabel
        Left = 748
        Top = 25
        Width = 24
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'Tage'
        ExplicitLeft = 742
      end
      object MHDVorlaufRadioButton: TRadioButton
        Left = 8
        Top = 24
        Width = 136
        Height = 17
        Caption = 'vor Ablaufdatum'
        Checked = True
        TabOrder = 0
        TabStop = True
        OnClick = VorlaufRadioButtonClick
      end
      object MHDVorlaufEdit: TEdit
        Left = 116
        Top = 22
        Width = 36
        Height = 21
        Anchors = [akLeft, akBottom]
        MaxLength = 4
        TabOrder = 1
        Text = '0'
        OnChange = MHDVorlaufEditChange
      end
      object MHDVorlaufUpDown: TIntegerUpDown
        Left = 152
        Top = 22
        Width = 16
        Height = 21
        Anchors = [akLeft, akBottom]
        Associate = MHDVorlaufEdit
        Min = -120
        Max = 9999
        TabOrder = 2
      end
      object RLZKOMMVorlaufEdit: TEdit
        Left = 690
        Top = 22
        Width = 36
        Height = 21
        Anchors = [akTop, akRight]
        MaxLength = 4
        TabOrder = 3
        Text = '0'
        OnChange = MHDVorlaufEditChange
        ExplicitLeft = 684
      end
      object RLZKOMMVorlaufUpDown: TIntegerUpDown
        Left = 726
        Top = 22
        Width = 16
        Height = 21
        Anchors = [akTop, akRight]
        Associate = RLZKOMMVorlaufEdit
        Min = -120
        Max = 9999
        TabOrder = 4
        ExplicitLeft = 720
      end
      object RLZKOMMVorlaufRadioButton: TRadioButton
        Left = 550
        Top = 24
        Width = 136
        Height = 17
        Anchors = [akTop, akRight]
        Caption = 'vor Restlaufzeit Komm'
        TabOrder = 5
        OnClick = VorlaufRadioButtonClick
        ExplicitLeft = 544
      end
      object WARLZVorlaufPanel: TPanel
        Left = 245
        Top = 10
        Width = 228
        Height = 41
        BevelOuter = bvNone
        TabOrder = 6
        DesignSize = (
          228
          41)
        object Label1: TLabel
          Left = 194
          Top = 15
          Width = 24
          Height = 13
          Anchors = [akLeft, akBottom]
          Caption = 'Tage'
        end
        object WARLZVorlaufRadioButton: TRadioButton
          Left = 9
          Top = 14
          Width = 136
          Height = 17
          Caption = 'vor Restlaufzeit WA'
          TabOrder = 0
          OnClick = VorlaufRadioButtonClick
        end
        object WARLZVorlaufEdit: TEdit
          Left = 138
          Top = 12
          Width = 36
          Height = 21
          Anchors = [akLeft, akBottom]
          MaxLength = 4
          TabOrder = 1
          Text = '0'
          OnChange = MHDVorlaufEditChange
        end
        object WARLZVorlaufUpDown: TIntegerUpDown
          Left = 174
          Top = 12
          Width = 16
          Height = 21
          Anchors = [akLeft, akBottom]
          Associate = WARLZVorlaufEdit
          Min = -120
          Max = 9999
          TabOrder = 2
        end
      end
    end
    object ArGrpComboBox: TComboBoxPro
      Left = 88
      Top = 77
      Width = 784
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 2
      OnChange = ArGrpComboBoxChange
      ExplicitWidth = 778
    end
    object LieferantComboBox: TComboBoxPro
      Left = 88
      Top = 109
      Width = 784
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 3
      OnChange = ArGrpComboBoxChange
      ExplicitWidth = 778
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 610
    Width = 880
    Height = 44
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    ExplicitTop = 593
    ExplicitWidth = 874
    DesignSize = (
      880
      44)
    object ExportButton: TSpeedButton
      Left = 8
      Top = 10
      Width = 121
      Height = 25
      Caption = 'Excel Export....'
      Glyph.Data = {
        36030000424D3603000000000000360000002800000010000000100000000100
        18000000000000030000C21E0000C21E00000000000000000000FFFFFFE2C0AA
        CC8D66C07140BC6B36BC6B36BC6B36BC6A36BC6A36BB6A35BB6A35BB6935BD6E
        3BCA8B63E3C2AEFFFFFFFFFFFFC57C4DF8F2EBF7ECDFF6EBDEF6EADEF6EADCF6
        EADCFAF3EBFAF3EBFAF2EAFCF7F3FCF8F4FEFEFDC37A4DFFFFFFFFFFFFC27740
        F5EBDFFCE4D1FCE4D1FCE4D1FCE4D1FCE4D1FCE4D1FCE4D1FCE4D1FCE4D1FCE4
        D1FDFBF8BC6B37FFFFFFFFFFFFC37C42F7EDE3FCE4D1FCE4D1FCE4D1FCE4D1FC
        E4D1FCE4D1FCE4D1FCE4D1FCE4D1FCE4D1FBF7F4BD6C37FFFFFFFFFFFFC68046
        F7F0E6FCE4D1FCE4D1E5D9C2689E705796655997666CA073FCE4D1FCE4D1FCE4
        D1FCF9F5C1743CFFFFFF3284491A7533197533197433448A52619B6BBBD6C378
        BB8461AB6A579664FCE2CCFBE0C9FBE1C8FDFAF7C37A41FFFFFFA3C8AD1B7533
        5BA06E49965C47905BC7DDCD5DB67167AE75448D581B7533FCE2CDFBE1CBFBE1
        C9FBF7F2C78045FFFFFFFFFFFF8281431F783748915DC7DDCD6AC08471B68244
        8E59B1C1A1FBE4D0FBE3CCFADFC7FADFC6FAF2EAC88448FFFFFFFFFFFFC48C4F
        619E71C5DCCC76C99773BC87438D58559360F5E0CCFBE1CCFAE0C7F9DDC3F8DC
        C2FAF4EDC8864BFFFFFFFAFCFA718C55C0D9C882D3A36DC18A5495634B966051
        9764679A68F4DCC3F8DCC2F6DABDF6D8BBFAF4EFC8874CFFFFFF80B28EB5D3BE
        9CDAB574C8955495634A935F5DA47459A16E509764629762E9D1B4F3D4B5F1D2
        B3F8F4F0C6864CFFFFFF5B9C6E568C57539666549563A1B9958DAE832E7F422E
        7F413A844836824590B490F7F2ECFBF7F3F5EFE9C38048FFFFFFFFFFFFC88D52
        F9F5F1FCE3CDFBE3CEFBE3CDFBE2CBF9E0C8F8DCC2F5D6BAFDFBF8FCE6CDFAE5
        C9E2B684D5A884FFFFFFFFFFFFCA925AFAF6F2FAE0C7FBE1C9FBE2C9FBE0C8F9
        DFC5F8DBC1F4D6B8FFFBF8F6D8B4E1B07DDC9669FDFBFAFFFFFFFFFFFFD2A274
        F8F3EDF8F4EEF8F4EDF8F3EDF8F3EDF8F3EDF8F2ECF7F2ECF2E6D7E2B27DDC98
        6BFDFBFAFFFFFFFFFFFFFFFFFFE8CEB9D7AA7CCC945BCA9055CA9055CA9055CA
        9155CB9055C98F55CF9D69DDB190FDFBFAFFFFFFFFFFFFFFFFFF}
      OnClick = ExportButtonClick
    end
    object CloseButton: TButton
      Left = 766
      Top = 10
      Width = 106
      Height = 25
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 0
      ExplicitLeft = 760
    end
  end
  object Panel3: TPanel
    Left = 0
    Top = 217
    Width = 8
    Height = 393
    Align = alLeft
    BevelOuter = bvNone
    TabOrder = 2
    ExplicitHeight = 376
  end
  object Panel4: TPanel
    Left = 872
    Top = 217
    Width = 8
    Height = 393
    Align = alRight
    BevelOuter = bvNone
    TabOrder = 3
    ExplicitLeft = 866
    ExplicitHeight = 376
  end
  object Panel5: TPanel
    Left = 8
    Top = 217
    Width = 864
    Height = 393
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 4
    ExplicitWidth = 858
    ExplicitHeight = 376
    object Splitter1: TSplitter
      Left = 0
      Top = 150
      Width = 864
      Height = 4
      Cursor = crVSplit
      Align = alTop
      ExplicitTop = 96
      ExplicitWidth = 745
    end
    object Panel6: TPanel
      Left = 0
      Top = 154
      Width = 864
      Height = 60
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 0
      ExplicitWidth = 858
      DesignSize = (
        864
        60)
      object Label4: TLabel
        Left = 0
        Top = 44
        Width = 148
        Height = 13
        Anchors = [akLeft, akBottom]
        Caption = 'Positionen in der '#220'berhangliste'
      end
      object AddToListButton: TSpeedButton
        Left = 6
        Top = 6
        Width = 121
        Height = 25
        Caption = #220'bernehmen'
        Glyph.Data = {
          36030000424D3603000000000000360000002800000010000000100000000100
          18000000000000030000C21E0000C21E00000000000000000000FFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF57
          8C5B508553FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFF558F59488F4D458C4A4A814DFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFAFCFB56955B51995779
          C07E76BF7C468D4B427C45FAFBFAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFF77B07C43904B65AD6C7DC2827AC1804B92502E72335E9061FFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD6E8D74E9A5581
          C5877EC385317A36FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFF45954C85C78C82C68936823DFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF4A9E538A
          CA9187C98E3C8A43FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFF50A6598ECC958BCB9342924AFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF56AD5F93
          CF9A90CE98489A50FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFF5BB46596D29F94D09C4EA257FFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF5FBB6A9A
          D4A398D3A153AA5DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFF63C06F9ED6A79CD4A559B263FFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF66C572A2
          D8ABA0D7A95DB868FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFF68C77467C67365C27062BE6DFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF}
        OnClick = AddToListButtonClick
      end
      object LoadListButton: TButton
        Left = 759
        Top = 6
        Width = 106
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Laden...'
        TabOrder = 0
        OnClick = LoadListButtonClick
        ExplicitLeft = 753
      end
      object SaveListButton: TButton
        Left = 648
        Top = 6
        Width = 105
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Speichern...'
        TabOrder = 1
        OnClick = SaveListButtonClick
        ExplicitLeft = 642
      end
    end
    object ListeStringGrid: TStringGridPro
      Left = 0
      Top = 214
      Width = 864
      Height = 179
      Align = alClient
      ColCount = 13
      DefaultColWidth = 20
      DefaultRowHeight = 17
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
      PopupMenu = ListPopupMenu
      TabOrder = 1
      OnBeforeSort = ListeStringGridBeforeSort
      OnBeginSort = ListeStringGridBeginSort
      OnEndSort = ListeStringGridEndSort
      TitelTexte.Strings = (
        ''
        'Lager'
        'Artikel-Nr.'
        'Artikeltext'
        'Menge'
        'Einheit'
        'Inhalt'
        'Nettogewicht'
        'MHD'
        'Wert in '#8364
        'Lagerplatz'
        'vorbestellt durch'
        'Bemerkungen')
      TitelFont.Charset = DEFAULT_CHARSET
      TitelFont.Color = clWindowText
      TitelFont.Height = -11
      TitelFont.Name = 'Tahoma'
      TitelFont.Style = []
      ExplicitWidth = 858
      ExplicitHeight = 162
    end
    object BestandListView: TListView
      Left = 0
      Top = 0
      Width = 864
      Height = 150
      Align = alTop
      Columns = <
        item
          Caption = 'Lager'
          Width = 80
        end
        item
          Alignment = taRightJustify
          Caption = 'Artikel-Nr.'
          Width = 80
        end
        item
          Caption = 'Artikeltext'
          Width = 90
        end
        item
          Alignment = taRightJustify
          Caption = 'Menge'
          Width = 70
        end
        item
          Caption = 'Einheit'
        end
        item
          Alignment = taRightJustify
          Caption = 'Inhalt'
          Width = 70
        end
        item
          Alignment = taRightJustify
          Caption = 'Nettogewicht'
          Width = 80
        end
        item
          Caption = 'Herstelldatum'
        end
        item
          Caption = 'MHD'
          Width = 100
        end
        item
          Alignment = taRightJustify
          Caption = 'RLZ WA'
          Width = 80
        end
        item
          Alignment = taRightJustify
          Caption = 'RLZ Komm'
          Width = 80
        end
        item
          Caption = 'Wert in '#8364
        end
        item
          Caption = 'Lagerplatz'
        end>
      MultiSelect = True
      ReadOnly = True
      RowSelect = True
      TabOrder = 2
      ViewStyle = vsReport
      OnColumnClick = BestandListColSort
      OnCompare = BestandListViewCompare
      OnDblClick = AddToListButtonClick
      OnDeletion = BestandListViewDeletion
      OnKeyPress = BestandListViewKeyPress
      OnSelectItem = BestandListViewSelectItem
      ExplicitWidth = 858
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 584
    Top = 16
  end
  object ImageList1: TImageList
    Left = 400
    Top = 248
    Bitmap = {
      494C010102000800040010001000FFFFFFFFFF10FFFFFFFFFFFFFFFF424D3600
      0000000000003600000028000000400000001000000001002000000000000010
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000084848400FFFFFF0000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000084848400FFFFFF0000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000008484
      8400C6C6C600C6C6C600FFFFFF00000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000008484
      8400C6C6C600C6C6C600FFFFFF00000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000000000000000000084848400C6C6
      C600C6C6C600C6C6C600C6C6C600FFFFFF000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000000000000000000084848400C6C6
      C600C6C6C600C6C6C600C6C6C600FFFFFF000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000084848400C6C6C600C6C6
      C600C6C6C600C6C6C600C6C6C600C6C6C600FFFFFF0000000000000000000000
      00000000000000000000000000000000000000000000FFFFFF00FFFFFF00FFFF
      FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF0000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000000000000000000000000000000FFFFFF00FFFFFF00FFFF
      FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF0000000000000000000000
      0000000000000000000000000000000000000000000084848400C6C6C600C6C6
      C600C6C6C600C6C6C600C6C6C600C6C6C600FFFFFF0000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000000000000000000084848400C6C6
      C600C6C6C600C6C6C600C6C6C600FFFFFF000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000000000000000000084848400C6C6
      C600C6C6C600C6C6C600C6C6C600FFFFFF000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000008484
      8400C6C6C600C6C6C600FFFFFF00000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000008484
      8400C6C6C600C6C6C600FFFFFF00000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000084848400FFFFFF0000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000084848400FFFFFF0000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000424D3E000000000000003E000000
      2800000040000000100000000100010000000000800000000000000000000000
      000000000000000000000000FFFFFF00FFFFFFFF00000000F3FFFFFF00000000
      F3FFFFFF00000000E1FFFFFF00000000E1FFFFFF00000000C0FFFFFF00000000
      C0FFFFFF00000000807F807F00000000807F807F00000000FFFFC0FF00000000
      FFFFC0FF00000000FFFFE1FF00000000FFFFE1FF00000000FFFFF3FF00000000
      FFFFF3FF00000000FFFFFFFF0000000000000000000000000000000000000000
      000000000000}
  end
  object ListPopupMenu: TPopupMenu
    OnPopup = ListPopupMenuPopup
    Left = 712
    Top = 496
    object ListeCopyColMenuItem: TMenuItem
      Caption = 'Kopieren'
      OnClick = ListeCopyColMenuItemClick
    end
    object ListeColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Splatenbreite'
      OnClick = ListeColOptimalMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object OrderFromMenuItem: TMenuItem
      Caption = 'Bestellt durch...'
      OnClick = OrderFromMenuItemClick
    end
    object ListeRemarkMenuItem: TMenuItem
      Caption = 'Bemerkungen...'
      OnClick = ListeRemarkMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object ListeDelMenuItem: TMenuItem
      Caption = 'L'#246'schen'
      ShortCut = 46
      OnClick = ListeDelMenuItemClick
    end
  end
end
