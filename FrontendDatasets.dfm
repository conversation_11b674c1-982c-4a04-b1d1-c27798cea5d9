object FrontendDataModule: TFrontendDataModule
  Height = 783
  Width = 977
  object KommPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 76
    Top = 101
  end
  object AufPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 172
    Top = 93
  end
  object LagerLPDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    CursorType = ctKeyset
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 172
    Top = 133
  end
  object LagerResDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 80
    Top = 184
  end
  object LagerLEDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 80
    Top = 232
  end
  object BestPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 176
    Top = 184
  end
  object BestBestDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 176
    Top = 232
  end
  object LagerNVEDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 80
    Top = 280
  end
  object WANVEDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 536
    Top = 328
  end
  object LagerTAKopfDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 280
    Top = 88
  end
  object LagerTAPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 280
    Top = 144
  end
  object BestBesWEDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    OnFetchProgress = BestBesWEDataSetFetchProgress
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 304
    Top = 224
  end
  object WEDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 312
    Top = 280
  end
  object KommLPDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 72
    Top = 352
  end
  object VersDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 224
    Top = 360
  end
  object KommARLPDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 312
    Top = 360
  end
  object BenDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 424
    Top = 136
  end
  object WEBesPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 424
    Top = 216
  end
  object BenSessionDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 424
    Top = 288
  end
  object SessionDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 424
    Top = 344
  end
  object WarenBesDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 240
    Top = 16
  end
  object WarenBesBesDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <
      item
        Name = 'ref_lager'
        DataType = ftInteger
        Value = Null
      end
      item
        Name = 'ref_ae'
        DataType = ftInteger
        Value = Null
      end
      item
        Name = 'variante'
        DataType = ftString
        Size = -1
        Value = Null
      end
      item
        Name = 'bereich'
        DataType = ftString
        Size = -1
        Value = Null
      end>
    IndexDefs = <>
    Left = 416
    Top = 408
  end
  object ManLiefDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 56
    Top = 432
  end
  object ManLiefPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 152
    Top = 440
  end
  object KommARBesDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 264
    Top = 440
  end
  object WarenBesPoolDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 408
    Top = 456
  end
  object WADataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 472
    Top = 24
  end
  object LieferungDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 464
    Top = 80
  end
  object LiefNVEDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 504
    Top = 176
  end
  object VersAufDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 368
    Top = 160
  end
  object LEInhaltDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 80
    Top = 8
  end
  object NVEInhaltDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 344
    Top = 32
  end
  object InvDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 112
    Top = 488
  end
  object InvPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 32
    Top = 488
  end
  object InvDiffDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 192
    Top = 520
  end
  object IFCErrorDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 280
    Top = 528
  end
  object InvResDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 376
    Top = 520
  end
  object PackKopfDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 32
    Top = 552
  end
  object PackPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 112
    Top = 560
  end
  object VerladeDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 512
    Top = 536
  end
  object VerladePosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 536
    Top = 600
  end
  object MDEDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 512
    Top = 256
  end
  object KommGrpDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 128
    Top = 352
  end
  object KommGrpAufDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 112
    Top = 408
  end
  object KommGrpAufPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 184
    Top = 408
  end
  object LiefRetDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 504
    Top = 176
  end
  object LiefRetPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 504
    Top = 176
  end
  object BatchKopfDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 208
    Top = 576
  end
  object BatchPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 312
    Top = 576
  end
  object QualDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 496
    Top = 384
  end
  object WEAvisDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 256
    Top = 200
  end
  object WEAvisPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 240
    Top = 240
  end
  object WATourDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 536
    Top = 24
  end
  object WATourAufDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 528
    Top = 72
  end
  object LagerLPLEDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 680
    Top = 32
  end
  object LagerLPLEBesDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 680
    Top = 80
  end
  object LagerLPBesDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 680
    Top = 136
  end
  object WASpedAufDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 624
    Top = 520
  end
  object BatchKommPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 360
    Top = 624
  end
  object NachKopfDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 720
    Top = 392
  end
  object NachPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 728
    Top = 448
  end
  object NachPosResDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 744
    Top = 504
  end
  object NachPosPlanDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 768
    Top = 552
  end
  object VorplanDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 716
    Top = 253
  end
  object LifeObjDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 648
    Top = 312
  end
  object LifeArtikelDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 648
    Top = 360
  end
  object WELTPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 368
    Top = 216
  end
  object AufLTPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 372
    Top = 101
  end
  object VerpackenPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 868
    Top = 213
  end
  object VerpackenAufDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 780
    Top = 205
  end
  object ForcastDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 844
    Top = 301
  end
  object LEKommInhaltDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 152
    Top = 8
  end
  object InvPlanPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 152
    Top = 488
  end
  object InvPosSollDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 80
    Top = 520
  end
  object InvPosIstDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 32
    Top = 520
  end
  object KommLoadKopfDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 604
    Top = 197
  end
  object KommLoadPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 604
    Top = 245
  end
  object AufTourDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 844
    Top = 357
  end
  object AufTourAufDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 844
    Top = 421
  end
  object AufTourAufposDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 844
    Top = 477
  end
  object RetourePosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 580
    Top = 477
  end
  object RetoureAvisPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 868
    Top = 69
  end
  object RetoureLEDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 868
    Top = 117
  end
  object RetoureLEInhaltDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 868
    Top = 165
  end
  object RetoureBestandDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 804
    Top = 21
  end
  object WANVEBestandDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 560
    Top = 368
  end
  object AufSpedTourDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 868
    Top = 549
  end
  object AufSpedTourPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 868
    Top = 605
  end
end
