object EditLPForm: TEditLPForm
  Left = 340
  Top = 174
  BorderStyle = bsDialog
  Caption = 'Eigenschaften des Lagerplatzes '#228'ndern'
  ClientHeight = 634
  ClientWidth = 354
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    354
    634)
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 56
    Width = 91
    Height = 13
    Caption = 'Lagerplatz-Nummer'
  end
  object Label2: TLabel
    Left = 8
    Top = 112
    Width = 28
    Height = 13
    Caption = 'Reihe'
  end
  object Label3: TLabel
    Left = 128
    Top = 112
    Width = 20
    Height = 13
    Caption = 'Feld'
  end
  object Label4: TLabel
    Left = 184
    Top = 112
    Width = 24
    Height = 13
    Caption = 'Fach'
  end
  object Label5: TLabel
    Left = 296
    Top = 111
    Width = 24
    Height = 13
    Caption = 'Tiefe'
  end
  object Label6: TLabel
    Left = 152
    Top = 56
    Width = 62
    Height = 13
    Caption = 'Bezeichnung'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 104
    Width = 339
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 329
  end
  object Bevel2: TBevel
    Left = 8
    Top = 480
    Width = 339
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 329
  end
  object Label7: TLabel
    Left = 8
    Top = 379
    Width = 74
    Height = 13
    Caption = 'Max. Anzahl LT'
  end
  object Label8: TLabel
    Left = 8
    Top = 216
    Width = 65
    Height = 13
    Caption = 'Lagerplatz-Art'
  end
  object Bevel3: TBevel
    Left = 7
    Top = 208
    Width = 339
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 329
  end
  object Bevel4: TBevel
    Left = 7
    Top = 589
    Width = 339
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 314
    ExplicitWidth = 329
  end
  object Label9: TLabel
    Left = 8
    Top = 492
    Width = 63
    Height = 13
    Caption = 'Folgenummer'
  end
  object Bevel5: TBevel
    Left = 7
    Top = 518
    Width = 339
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 329
  end
  object Label10: TLabel
    Left = 8
    Top = 529
    Width = 94
    Height = 13
    Caption = 'System-Koordinaten'
  end
  object Label11: TLabel
    Left = 8
    Top = 562
    Width = 67
    Height = 13
    Caption = 'WWS-Platznr.'
  end
  object Bevel6: TBevel
    Left = 7
    Top = 368
    Width = 339
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 329
  end
  object Label12: TLabel
    Left = 240
    Top = 112
    Width = 31
    Height = 13
    Caption = 'Ebene'
  end
  object Label13: TLabel
    Left = 8
    Top = 451
    Width = 67
    Height = 13
    Caption = 'Belegungsprio'
  end
  object Bevel7: TBevel
    Left = 7
    Top = 440
    Width = 339
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 329
  end
  object Label14: TLabel
    Left = 174
    Top = 447
    Width = 95
    Height = 26
    Caption = '999=zuerst belegen'#13#10'    0=zuletzt belegen'
  end
  object Label16: TLabel
    Left = 9
    Top = 270
    Width = 62
    Height = 13
    Caption = 'H'#246'henklasse'
  end
  object Label17: TLabel
    Left = 174
    Top = 379
    Width = 87
    Height = 13
    Caption = 'Max. Belastbarkeit'
  end
  object Label18: TLabel
    Left = 323
    Top = 379
    Width = 12
    Height = 13
    Caption = 'kg'
  end
  object Bevel8: TBevel
    Left = 7
    Top = 317
    Width = 339
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 329
  end
  object Label24: TLabel
    Left = 8
    Top = 324
    Width = 30
    Height = 13
    Caption = 'L'#228'nge'
  end
  object Label25: TLabel
    Left = 104
    Top = 324
    Width = 24
    Height = 13
    Caption = 'Tiefe'
  end
  object Label33: TLabel
    Left = 208
    Top = 324
    Width = 26
    Height = 13
    Caption = 'H'#246'he'
  end
  object Label34: TLabel
    Left = 8
    Top = 8
    Width = 90
    Height = 13
    Caption = 'Lagerbereichszone'
  end
  object Bevel9: TBevel
    Left = 7
    Top = 155
    Width = 339
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 329
  end
  object Label15: TLabel
    Left = 128
    Top = 165
    Width = 40
    Height = 13
    Caption = 'Barcode'
  end
  object Label19: TLabel
    Left = 70
    Top = 344
    Width = 16
    Height = 13
    Caption = 'mm'
  end
  object Label20: TLabel
    Left = 166
    Top = 344
    Width = 16
    Height = 13
    Caption = 'mm'
  end
  object Label21: TLabel
    Left = 270
    Top = 344
    Width = 16
    Height = 13
    Caption = 'mm'
  end
  object Label22: TLabel
    Left = 7
    Top = 165
    Width = 59
    Height = 13
    Caption = 'Stellplatz Nr.'
  end
  object Label23: TLabel
    Left = 8
    Top = 406
    Width = 83
    Height = 13
    Caption = 'Max. Stapelfaktor'
  end
  object Label26: TLabel
    Left = 174
    Top = 270
    Width = 55
    Height = 13
    Caption = 'ABC-Klasse'
  end
  object Bevel10: TBevel
    Left = 7
    Top = 262
    Width = 339
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 329
  end
  object NrEdit: TEdit
    Left = 8
    Top = 72
    Width = 121
    Height = 21
    Color = clBtnFace
    MaxLength = 32
    TabOrder = 1
    Text = 'NrEdit'
    OnKeyPress = NrEditKeyPress
  end
  object ReiheEdit: TEdit
    Left = 8
    Top = 128
    Width = 66
    Height = 21
    Enabled = False
    MaxLength = 5
    TabOrder = 3
    Text = 'ReiheEdit'
    OnExit = RegalKoorEditExit
  end
  object FeldEdit: TEdit
    Left = 128
    Top = 128
    Width = 40
    Height = 21
    Enabled = False
    MaxLength = 3
    TabOrder = 4
    Text = 'FeldEdit'
    OnExit = RegalKoorEditExit
    OnKeyPress = IntegerEditKeyPress
  end
  object FachEdit: TEdit
    Left = 184
    Top = 128
    Width = 40
    Height = 21
    Enabled = False
    MaxLength = 3
    TabOrder = 5
    Text = 'FachEdit'
    OnExit = RegalKoorEditExit
    OnKeyPress = IntegerEditKeyPress
  end
  object TiefeEdit: TEdit
    Left = 296
    Top = 127
    Width = 40
    Height = 21
    Enabled = False
    MaxLength = 3
    TabOrder = 7
    Text = 'TiefeEdit'
    OnExit = RegalKoorEditExit
    OnKeyPress = IntegerEditKeyPress
  end
  object OKButton: TButton
    Left = 182
    Top = 599
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 27
  end
  object AbortButton: TButton
    Left = 270
    Top = 599
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 28
  end
  object NameEdit: TEdit
    Left = 152
    Top = 72
    Width = 184
    Height = 21
    MaxLength = 32
    TabOrder = 2
    Text = 'NameEdit'
  end
  object LTAnzEdit: TEdit
    Left = 104
    Top = 376
    Width = 40
    Height = 21
    MaxLength = 4
    TabOrder = 17
    Text = '0'
    OnKeyPress = IntegerEditKeyPress
  end
  object LTAnzUpDown: TIntegerUpDown
    Left = 144
    Top = 376
    Width = 17
    Height = 21
    Associate = LTAnzEdit
    Min = -1
    Max = 9999
    TabOrder = 18
    Thousands = False
    OnClick = LTAnzUpDownClick
  end
  object LPArtComboBox: TComboBoxPro
    Left = 8
    Top = 232
    Width = 337
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 11
  end
  object FolgeEdit: TEdit
    Left = 128
    Top = 488
    Width = 121
    Height = 21
    TabOrder = 24
    Text = 'FolgeEdit'
    OnKeyPress = IntegerEditKeyPress
  end
  object RBGKoorEdit: TEdit
    Left = 128
    Top = 526
    Width = 121
    Height = 21
    TabOrder = 25
    Text = 'RBGKoorEdit'
  end
  object WWSPlatzEdit: TEdit
    Left = 128
    Top = 558
    Width = 121
    Height = 21
    TabOrder = 26
    Text = 'WWSPlatzEdit'
  end
  object EbeneEdit: TEdit
    Left = 240
    Top = 128
    Width = 40
    Height = 21
    Enabled = False
    MaxLength = 3
    TabOrder = 6
    Text = 'EbeneEdit'
    OnExit = RegalKoorEditExit
    OnKeyPress = IntegerEditKeyPress
  end
  object LPPrioEdit: TEdit
    Left = 104
    Top = 448
    Width = 40
    Height = 21
    MaxLength = 3
    TabOrder = 22
    Text = '0'
    OnKeyPress = IntegerEditKeyPress
  end
  object LPPrioUpDown: TIntegerUpDown
    Left = 144
    Top = 448
    Width = 16
    Height = 21
    Associate = LPPrioEdit
    Max = 999
    TabOrder = 23
  end
  object LPHKLComboBox: TComboBoxPro
    Left = 8
    Top = 286
    Width = 150
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 12
  end
  object MaxGewichtEdit: TEdit
    Left = 267
    Top = 376
    Width = 52
    Height = 21
    TabOrder = 19
    Text = 'MaxGewichtEdit'
    OnKeyPress = IntegerEditKeyPress
  end
  object FBLEdit: TEdit
    Left = 8
    Top = 340
    Width = 60
    Height = 21
    TabOrder = 14
    Text = 'FBLEdit'
    OnKeyPress = IntegerEditKeyPress
  end
  object FBBEdit: TEdit
    Left = 104
    Top = 340
    Width = 60
    Height = 21
    TabOrder = 15
    Text = 'Edit3'
    OnKeyPress = IntegerEditKeyPress
  end
  object FBHEdit: TEdit
    Left = 208
    Top = 340
    Width = 60
    Height = 21
    TabOrder = 16
    Text = 'Edit3'
    OnKeyPress = IntegerEditKeyPress
  end
  object LBZoneComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 337
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 0
  end
  object BarcodeEdit: TEdit
    Left = 128
    Top = 181
    Width = 129
    Height = 21
    MaxLength = 32
    TabOrder = 9
    Text = 'BarcodeEdit'
  end
  object StellplatzNrEdit: TEdit
    Left = 7
    Top = 181
    Width = 91
    Height = 21
    MaxLength = 16
    TabOrder = 8
    Text = 'StellplatzNrEdit'
  end
  object StapelFaktorEdit: TEdit
    Left = 104
    Top = 403
    Width = 40
    Height = 21
    MaxLength = 1
    TabOrder = 20
    Text = '1'
    OnKeyPress = IntegerEditKeyPress
  end
  object StapelFaktorUpDown: TIntegerUpDown
    Left = 144
    Top = 403
    Width = 16
    Height = 21
    Associate = StapelFaktorEdit
    Min = 1
    Max = 9
    Position = 1
    TabOrder = 21
    Thousands = False
  end
  object ABCComboBox: TComboBox
    Left = 174
    Top = 286
    Width = 171
    Height = 21
    Style = csDropDownList
    Anchors = [akLeft, akTop, akRight]
    ItemIndex = 0
    TabOrder = 13
    Items.Strings = (
      ''
      'A'
      'B'
      'C')
  end
  object ScanableCheckBox: TCheckBox
    Left = 270
    Top = 183
    Width = 71
    Height = 17
    AllowGrayed = True
    Caption = 'Scanbar'
    State = cbGrayed
    TabOrder = 10
  end
end
