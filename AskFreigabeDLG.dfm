object AskFreigabeForm: TAskFreigabeForm
  Left = 415
  Top = 420
  BorderStyle = bsDialog
  Caption = 'Auftrag freigeben'
  ClientHeight = 724
  ClientWidth = 327
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    327
    724)
  TextHeight = 13
  object OkButton: TButton
    Left = 158
    Top = 694
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 9
  end
  object AbortButton: TButton
    Left = 246
    Top = 694
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 10
  end
  object BestandPanel: TPanel
    Left = 0
    Top = 363
    Width = 327
    Height = 73
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      327
      73)
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 254
      Height = 26
      Caption = 
        'Auftrag auch dann freigeben, wenn nicht ausreichend'#13'Bestand im L' +
        'ager vorhanden ist?'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 71
      Width = 315
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 288
    end
    object JaRadioButton: TRadioButton
      Left = 8
      Top = 48
      Width = 65
      Height = 17
      Caption = 'Ja'
      TabOrder = 0
    end
    object NeinRadioButton: TRadioButton
      Left = 120
      Top = 48
      Width = 65
      Height = 17
      Caption = 'Nein'
      TabOrder = 1
    end
  end
  object MHDTolPanel: TPanel
    Left = 0
    Top = 436
    Width = 327
    Height = 52
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    Visible = False
    DesignSize = (
      327
      52)
    object Label2: TLabel
      Left = 8
      Top = 4
      Width = 114
      Height = 13
      Caption = 'MHD-Toleranz in Tagen'
    end
    object Bevel2: TBevel
      Left = 8
      Top = 49
      Width = 315
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 288
    end
    object MHDTolEdit: TEdit
      Left = 8
      Top = 20
      Width = 129
      Height = 21
      MaxLength = 2
      TabOrder = 0
      Text = '0'
      OnKeyPress = MHDTolEditKeyPress
    end
    object MHDTolUpDown: TIntegerUpDown
      Left = 137
      Top = 20
      Width = 16
      Height = 21
      Associate = MHDTolEdit
      TabOrder = 1
    end
  end
  object KommPanel: TPanel
    Left = 0
    Top = 593
    Width = 327
    Height = 54
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 7
    DesignSize = (
      327
      54)
    object Label3: TLabel
      Left = 8
      Top = 8
      Width = 117
      Height = 13
      Caption = 'Kommissionierung durch:'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 51
      Width = 315
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 288
    end
    object KommUserComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 311
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 100
      TabOrder = 0
      OnDrawItem = KommUserComboBoxDrawItem
    end
  end
  object BatchPanel: TPanel
    Left = 0
    Top = 0
    Width = 327
    Height = 155
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    Visible = False
    DesignSize = (
      327
      155)
    object Label4: TLabel
      Left = 8
      Top = 10
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object Label5: TLabel
      Left = 8
      Top = 52
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Label6: TLabel
      Left = 8
      Top = 104
      Width = 95
      Height = 13
      Caption = 'Kommissionierdatum'
    end
    object Label7: TLabel
      Left = 129
      Top = 104
      Width = 40
      Height = 13
      Caption = 'Batchart'
    end
    object Bevel5: TBevel
      Left = 8
      Top = 98
      Width = 315
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 288
    end
    object Bevel6: TBevel
      Left = 8
      Top = 150
      Width = 315
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object MandantComboBox: TComboBoxPro
      Left = 8
      Top = 26
      Width = 311
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 68
      Width = 311
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 1
      OnChange = LagerComboBoxChange
    end
    object BatchArtComboBox: TComboBoxPro
      Left = 129
      Top = 120
      Width = 190
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 3
      OnChange = BatchArtComboBoxChange
    end
    object KommDateTimePicker: TDateTimePicker
      Left = 8
      Top = 120
      Width = 104
      Height = 21
      Date = 41106.000000000000000000
      Time = 0.766431458330771400
      TabOrder = 2
    end
  end
  object BatchSizePanel: TPanel
    Left = 0
    Top = 209
    Width = 327
    Height = 154
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    Visible = False
    DesignSize = (
      327
      154)
    object Label12: TLabel
      Left = 8
      Top = 2
      Width = 74
      Height = 13
      Caption = 'Anzahl Batches'
    end
    object Label13: TLabel
      Left = 213
      Top = 92
      Width = 52
      Height = 13
      Caption = 'Max. VPEs'
    end
    object Label14: TLabel
      Left = 248
      Top = 108
      Width = 63
      Height = 13
      Caption = 'pro Batchlauf'
    end
    object Label10: TLabel
      Left = 168
      Top = 108
      Width = 52
      Height = 13
      Caption = 'pro Auftrag'
    end
    object Label9: TLabel
      Left = 88
      Top = 108
      Width = 66
      Height = 13
      Caption = 'Max. Auftr'#228'ge'
    end
    object Label8: TLabel
      Left = 8
      Top = 108
      Width = 63
      Height = 13
      Caption = 'Min. Auftr'#228'ge'
    end
    object Label11: TLabel
      Left = 8
      Top = 44
      Width = 61
      Height = 13
      Caption = 'Batchgruppe'
    end
    object Bevel4: TBevel
      Left = 7
      Top = 151
      Width = 315
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object MaxBatchVPEUpDown: TIntegerUpDown
      Left = 300
      Top = 124
      Width = 16
      Height = 21
      Associate = MaxBatchVPEEdit
      Max = 9999
      Position = 1
      TabOrder = 11
    end
    object MaxBatchVPEEdit: TEdit
      Left = 245
      Top = 124
      Width = 55
      Height = 21
      MaxLength = 4
      TabOrder = 10
      Text = '1'
    end
    object MaxVPEUpDown: TIntegerUpDown
      Left = 223
      Top = 124
      Width = 16
      Height = 21
      Associate = MaxVPEEdit
      Max = 9999
      Position = 1
      TabOrder = 9
    end
    object MaxVPEEdit: TEdit
      Left = 168
      Top = 124
      Width = 55
      Height = 21
      MaxLength = 4
      TabOrder = 8
      Text = '1'
    end
    object MaxBatchAufUpDown: TIntegerUpDown
      Left = 143
      Top = 124
      Width = 16
      Height = 21
      Associate = MaxBatchAufEdit
      Min = 1
      Max = 9999
      Position = 1
      TabOrder = 7
    end
    object MaxBatchAufEdit: TEdit
      Left = 88
      Top = 124
      Width = 55
      Height = 21
      MaxLength = 4
      TabOrder = 6
      Text = '1'
    end
    object MinBatchAufUpDown: TIntegerUpDown
      Left = 63
      Top = 124
      Width = 16
      Height = 21
      Associate = MinBatchAufEdit
      Min = 1
      Max = 9999
      Position = 1
      TabOrder = 5
    end
    object MinBatchAufEdit: TEdit
      Left = 8
      Top = 124
      Width = 55
      Height = 21
      MaxLength = 4
      TabOrder = 4
      Text = '1'
    end
    object BatchGruppeComboBox: TComboBoxPro
      Left = 8
      Top = 60
      Width = 311
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 2
      Text = 'BatchArtComboBox'
    end
    object BatchAutoKommFreiCheckBox: TCheckBox
      Left = 131
      Top = 20
      Width = 192
      Height = 17
      Anchors = [akLeft, akTop, akRight]
      Caption = 'Kommissionierungen freigeben'
      TabOrder = 3
    end
    object BatchCountUpDown: TIntegerUpDown
      Left = 96
      Top = 18
      Width = 16
      Height = 21
      Associate = BatchCountEdit
      Max = 999
      Position = 1
      TabOrder = 1
    end
    object BatchCountEdit: TEdit
      Left = 8
      Top = 18
      Width = 88
      Height = 21
      MaxLength = 3
      TabOrder = 0
      Text = '1'
    end
  end
  object SpedPanel: TPanel
    Left = 0
    Top = 155
    Width = 327
    Height = 54
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    Visible = False
    DesignSize = (
      327
      54)
    object Bevel7: TBevel
      Left = 8
      Top = 50
      Width = 315
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 37
    end
    object Label15: TLabel
      Left = 8
      Top = 2
      Width = 44
      Height = 13
      Caption = 'Spedition'
    end
    object SpedComboBox: TComboBoxPro
      Left = 8
      Top = 18
      Width = 311
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 0
    end
  end
  object SplitKommPanel: TPanel
    Left = 0
    Top = 541
    Width = 327
    Height = 52
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 6
    Visible = False
    DesignSize = (
      327
      52)
    object Label16: TLabel
      Left = 8
      Top = 4
      Width = 161
      Height = 13
      Caption = 'Aufteilung des Komm-Laufes nach'
    end
    object Bevel8: TBevel
      Left = 8
      Top = 49
      Width = 315
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 288
    end
    object Label17: TLabel
      Left = 58
      Top = 23
      Width = 26
      Height = 13
      Caption = 'VPEs'
    end
    object SplitKommEdit: TEdit
      Left = 8
      Top = 20
      Width = 42
      Height = 21
      MaxLength = 4
      TabOrder = 0
      Text = '0'
      OnKeyPress = MHDTolEditKeyPress
    end
  end
  object AutoKommFreiPanel: TPanel
    Left = 0
    Top = 647
    Width = 327
    Height = 37
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 8
    DesignSize = (
      327
      37)
    object Bevel9: TBevel
      Left = 8
      Top = 33
      Width = 315
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 27
    end
    object AutoKommFreiCheckBox: TCheckBox
      Left = 7
      Top = 7
      Width = 312
      Height = 17
      Caption = 'Kommissionierungen automatisch freigeben'
      TabOrder = 0
    end
  end
  object PrioPanel: TPanel
    Left = 0
    Top = 488
    Width = 327
    Height = 53
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      327
      53)
    object Label18: TLabel
      Left = 8
      Top = 4
      Width = 18
      Height = 13
      Caption = 'Prio'
    end
    object Bevel10: TBevel
      Left = 8
      Top = 48
      Width = 315
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 44
    end
    object PrioEdit: TEdit
      Left = 8
      Top = 20
      Width = 121
      Height = 21
      TabOrder = 0
      Text = 'PrioEdit'
    end
  end
end
