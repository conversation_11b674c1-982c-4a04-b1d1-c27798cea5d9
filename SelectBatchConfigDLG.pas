unit SelectBatchConfigDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls;

type
  TSelectBatchConfigForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    Panel1: TPanel;
    Label7: TLabel;
    BatchArtComboBox: TComboBoxPro;
    Label11: TLabel;
    BatchGruppeComboBox: TComboBoxPro;
    KommPanel: TPanel;
    Label3: TLabel;
    Bevel1: TBevel;
    KommUserComboBox: TComboBoxPro;
    Bevel2: TBevel;
    SplitKommPanel: TPanel;
    Label16: TLabel;
    Bevel8: TBevel;
    Label17: TLabel;
    SplitKommEdit: TEdit;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormDestroy(Sender: TObject);
  private
    fBatchAblauf : String;
  public
    property BatchAblauf : String read fBatchAblauf write fBatchAblauf;
  end;

implementation

{$R *.dfm}

uses
  DB,ADODB,VCLUtilitys,FrontendUtils,FrontendImageModule,DatenModul,ConfigModul,ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectBatchConfigForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  LVSConfigModul.SaveFormParameter (Self, 'BatchArt', GetComboBoxRef (BatchArtComboBox));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectBatchConfigForm.FormCreate(Sender: TObject);
begin
  fBatchAblauf := '';

  SplitKommEdit.Text := '';
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectBatchConfigForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (BatchArtComboBox);
  ClearComboBoxObjects (KommUserComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectBatchConfigForm.FormShow(Sender: TObject);
var
  idx,
  ref,
  refben,
  refgrp,
  defidx,
  minvpe,
  maxvpe  : Integer;
  query   : TADOQuery;
begin
  if not (SplitKommPanel.Visible) then
    Height := Height - SplitKommPanel.Height;

  LoadGruppe     (KommUserComboBox, 'KOMM', '', LVSDatenModul.AktLocationRef);
  LoadUserGruppe (KommUserComboBox, 'KommUser%', LVSDatenModul.AktLocationRef);

  if (KommUserComboBox.Items.Count = 0) then begin
    KommPanel.Visible := False;

    Height := Height - KommPanel.Height;
  end else begin
    defidx := -1;

    idx := 0;
    while (idx < KommUserComboBox.Items.Count) do begin
      if Assigned (KommUserComboBox.Items.Objects [idx]) and TComboboxSysBenRef (KommUserComboBox.Items.Objects [idx]).Default then begin
        defidx := idx;
        break;
      end else
        Inc (idx);
    end;

    if (defidx = -1) then begin
      defidx := 0;
      KommUserComboBox.Items.Insert (defidx, GetResourceText (1020));
    end;
  end;


  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select * from V_BATCHLAUF_CONFIG where STATUS=''AKT'' and REF_MASTER_CONFIG is null and REF_LOCATION=:ref_loc');
    query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    if (LVSDatenModul.AktLagerRef = -1) then
      query.SQL.Add ('and REF_LAGER is null')
    else begin
      query.SQL.Add ('and (REF_LAGER is null or REF_LAGER=:ref_lager)');
      query.Parameters.ParamByName('ref_lager').Value := LVSDatenModul.AktLagerRef;
    end;

    if (Length (fBatchAblauf) > 0) then begin
      query.SQL.Add ('and ABLAUF_ART=:ablauf');
      query.Parameters.ParamByName('ablauf').Value := fBatchAblauf;
    end;

    query.SQL.Add ('order by REIHENFOLGE nulls last');

    try
      query.Open;

      while not (query.Eof) do begin
        refben := -1;
        refgrp := -1;

        if Assigned (query.FindField ('REF_KOMM_BEN')) then
          refben := DBGetIntegerNull (query.FieldByName ('REF_KOMM_BEN'));
        if Assigned (query.FindField ('REF_KOMM_GRP')) then
          refgrp := DBGetIntegerNull (query.FieldByName ('REF_KOMM_GRP'));
        if Assigned (query.FindField ('MIN_AUF_VPE')) then
          minvpe := DBGetIntegerNull (query.FieldByName ('MIN_AUF_VPE'));
        if Assigned (query.FindField ('MAX_AUF_VPE')) then
          maxvpe := DBGetIntegerNull (query.FieldByName ('MAX_AUF_VPE'));

        idx := BatchArtComboBox.Items.AddObject (query.FieldByName ('NAME').AsString,
                                                 TBatchconfigEntry.Create (query.FieldByName ('REF').AsInteger,
                                                                           query.FieldByName ('BATCH_AUFTRAG_ART').AsString,
                                                                           query.FieldByName ('ABLAUF_ART').AsString,
                                                                           minvpe,
                                                                           maxvpe,
                                                                           DBGetIntegerNull (query.FieldByName ('MAX_AUF_ANZ')),
                                                                           DBGetIntegerNull (query.FieldByName ('REF_KOMM_PLAN_GRP')),
                                                                           refben,
                                                                           refgrp
                                                                          )
                                                );

        query.Next;
      end;

      query.Close;
    except
    end;

    LVSConfigModul.ReadFormParameter (Self, 'BatchArt', ref, -1);

    if (ref = -1) then
      BatchArtComboBox.ItemIndex := 0
    else begin
      BatchArtComboBox.ItemIndex := FindComboboxRef (BatchArtComboBox, ref);
      if (BatchArtComboBox.ItemIndex = -1) then BatchArtComboBox.ItemIndex := 0;
    end;

    LoadComboxDBItems (BatchGruppeComboBox, 'AUFTRAG_BATCHLAUF', 'BATCH_GRUPPE', False);
    BatchGruppeComboBox.Items.Insert (0, '');
  finally
    query.Free;
  end;
end;

end.
