object AufKommPlanForm: TAufKommPlanForm
  Left = 0
  Top = 0
  Caption = 'Konfiguration der Auftragsplanung'
  ClientHeight = 471
  ClientWidth = 755
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    755
    471)
  TextHeight = 13
  object AufKommPlanDBGrid: TDBGridPro
    Left = 8
    Top = 8
    Width = 739
    Height = 145
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = AufKommPlanDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = AufKommPlanDBGridPopupMenu
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 672
    Top = 438
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 1
  end
  object ParamGroupBox: TGroupBox
    Left = 8
    Top = 159
    Width = 739
    Height = 273
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Parameter'
    TabOrder = 2
    DesignSize = (
      739
      273)
    object Label1: TLabel
      Left = 8
      Top = 111
      Width = 62
      Height = 13
      Caption = 'Lagerbereich'
    end
    object Label2: TLabel
      Left = 8
      Top = 47
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Label3: TLabel
      Left = 8
      Top = 139
      Width = 95
      Height = 13
      Caption = 'Lagerbereichs-Zone'
    end
    object Label4: TLabel
      Left = 8
      Top = 19
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object Label5: TLabel
      Left = 8
      Top = 83
      Width = 88
      Height = 13
      Caption = 'Auftragart / Batch'
    end
    object Label6: TLabel
      Left = 8
      Top = 176
      Width = 18
      Height = 13
      Caption = 'Prio'
    end
    object Label7: TLabel
      Left = 216
      Top = 203
      Width = 45
      Height = 13
      Alignment = taRightJustify
      Caption = 'Max. VPE'
    end
    object Label8: TLabel
      Left = 8
      Top = 203
      Width = 41
      Height = 13
      Caption = 'Min. VPE'
    end
    object Label9: TLabel
      Left = 8
      Top = 230
      Width = 57
      Height = 13
      Caption = 'Reihenfolge'
    end
    object Bevel1: TBevel
      Left = 6
      Top = 71
      Width = 727
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Bevel2: TBevel
      Left = 6
      Top = 164
      Width = 727
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label10: TLabel
      Left = 202
      Top = 176
      Width = 59
      Height = 13
      Alignment = taRightJustify
      Caption = 'Min.Bestand'
    end
    object QuashButton: TButton
      Left = 656
      Top = 239
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Verwerfen'
      TabOrder = 14
      OnClick = QuashButtonClick
    end
    object ApplyButton: TButton
      Left = 575
      Top = 239
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      TabOrder = 13
      OnClick = ApplyButtonClick
    end
    object BereichComboBox: TComboBoxPro
      Left = 120
      Top = 108
      Width = 385
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 3
      OnChange = BereichComboBoxChange
    end
    object VollPalCheckBox: TCheckBox
      Left = 352
      Top = 172
      Width = 200
      Height = 17
      Caption = 'Vollpaletten-Entnahme zul'#228'ssig'
      TabOrder = 9
    end
    object VPEKommCheckBox: TCheckBox
      Left = 352
      Top = 189
      Width = 200
      Height = 17
      Caption = 'VPE-Kommissionierung zul'#228'ssig'
      TabOrder = 10
    end
    object ItemKommCheckBox: TCheckBox
      Left = 352
      Top = 206
      Width = 200
      Height = 17
      Caption = 'St'#252'ckkommissionierung zul'#228'ssig'
      TabOrder = 11
    end
    object LagerComboBox: TComboBoxPro
      Left = 120
      Top = 44
      Width = 385
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 1
      OnChange = LagerComboBoxChange
    end
    object ZoneComboBox: TComboBoxPro
      Left = 120
      Top = 136
      Width = 385
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 4
    end
    object MandComboBox: TComboBoxPro
      Left = 120
      Top = 16
      Width = 385
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 0
    end
    object AufArtComboBox: TComboBoxPro
      Left = 120
      Top = 80
      Width = 385
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 2
    end
    object MinVPEEdit: TEdit
      Left = 120
      Top = 200
      Width = 42
      Height = 21
      TabOrder = 6
      Text = 'MinVPEEdit'
    end
    object PrioEdit: TEdit
      Left = 120
      Top = 173
      Width = 42
      Height = 21
      TabOrder = 5
      Text = 'PrioEdit'
    end
    object MaxVPEEdit: TEdit
      Left = 267
      Top = 200
      Width = 58
      Height = 21
      MaxLength = 9
      TabOrder = 7
      Text = 'MaxVPEEdit'
    end
    object SortComboBox: TComboBox
      Left = 120
      Top = 227
      Width = 205
      Height = 21
      Style = csDropDownList
      TabOrder = 8
      Items.Strings = (
        'Kleinste Mengen zuerst'
        'Gr'#246#223'te Mengen zuerst'
        'Erst passen dann kleinste Menge'
        'Erst passen dann gr'#246#223'e Menge')
    end
    object KommZoneCheckBox: TCheckBox
      Left = 352
      Top = 229
      Width = 200
      Height = 17
      AllowGrayed = True
      Caption = 'Pro Zone ein Komm-Lauf'
      State = cbGrayed
      TabOrder = 12
    end
    object MinBesEdit: TEdit
      Left = 267
      Top = 173
      Width = 58
      Height = 21
      MaxLength = 9
      TabOrder = 15
      Text = 'MinBesEdit'
    end
    object ErsatzCheckBox: TCheckBox
      Left = 551
      Top = 176
      Width = 180
      Height = 17
      Caption = 'Nur f'#252'r Ersatzpicks'
      TabOrder = 16
    end
    object NachCheckBox: TCheckBox
      Left = 551
      Top = 192
      Width = 180
      Height = 17
      Caption = 'Nur f'#252'r Nachkommissionierung'
      TabOrder = 17
    end
  end
  object AufKommPlanQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 168
    Top = 48
  end
  object AufKommPlanDataSource: TDataSource
    DataSet = AufKommPlanQuery
    OnDataChange = AufKommPlanDataSourceDataChange
    Left = 200
    Top = 48
  end
  object AufKommPlanDBGridPopupMenu: TPopupMenu
    OnPopup = AufKommPlanDBGridPopupMenuPopup
    Left = 248
    Top = 48
    object RelNeuMenuItem: TMenuItem
      Caption = 'Neu'
      OnClick = RelNeuMenuItemClick
    end
    object RelCopyMenuItem: TMenuItem
      Caption = 'Kopieren'
      OnClick = RelNeuMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object RelActiveMenuItem: TMenuItem
      Caption = 'Aktivieren'
      OnClick = RelActiveMenuItemClick
    end
    object RelDeactiveMenuItem: TMenuItem
      Caption = 'Deaktivieren'
      OnClick = RelDeactiveMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object RelDelMenuItem: TMenuItem
      Caption = 'L'#246'schen'
      OnClick = RelDelMenuItemClick
    end
  end
end
