unit DisplayWELTDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, ExtCtrls,
  StringGridPro, VCLUtilitys, Menus;

type
  TDisplayWELTForm = class;

  TDeleteRowEvent = function (Sender : TDisplayWELTForm; const Index, Ref : Integer) : Integer of object;
  TLoadRowEvent   = function (Sender : TDisplayWELTForm; const LENr : String) : Integer of object;

  TDisplayWELTGridRef = class (TGridRef)
    Index : Integer;
    RefAr : Integer;

    constructor Create (const RecRef, RecIndex, RecRefAr : Integer);
  end;

  TDisplayWELTForm = class(TForm)
    Panel1: TPanel;
    Panel2: TPanel;
    CloseButton: TButton;
    Label1: TLabel;
    LENrLabel: TLabel;
    Label2: TLabel;
    LETypLabel: TLabel;
    LTInhaltStringGrid: TStringGridPro;
    LTInhaltStringGridPopupMenu: TPopupMenu;
    MandCopyColMenuitem: TMenuItem;
    MandColOptimalMenuItem: TMenuItem;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure LTInhaltStringGridBeforeSort(Sender: TStringGridPro;
      SortCol: Integer; SortDir: Boolean);
    procedure StringGridCopyColMenuItemClick(Sender: TObject);
    procedure StringGridColOptimalMenuItemClick(Sender: TObject);
    procedure LTInhaltStringGridPopupMenuPopup(Sender: TObject);
  private
    fLENr           : String;
    fRefLT          : Integer;
    fRefLager       : Integer;
    fMHDGridCol     : Integer;
    fChargeGridCol  : Integer;
    fGewichtGridCol : Integer;
    fFachGridCol    : Integer;
    eDeleteRow      : TDeleteRowEvent;
    eLoadRow        : TLoadRowEvent;

    procedure SetLENr (const LENr : String);
  public
    property RefLager : Integer read fRefLager write fRefLager;
    property RefLT    : Integer read fRefLT    write fRefLT;
    property LENr     : String  read fLENr     write SetLENr;

    property MHDGridCol     : Integer read fMHDGridCol;
    property GewichtGridCol : Integer read fGewichtGridCol;
    property ChargeGridCol  : Integer read fChargeGridCol;
    property FachGridCol    : Integer read fFachGridCol;

    property DeleteRow : TDeleteRowEvent read eDeleteRow write eDeleteRow;
    property LoadRow   : TLoadRowEvent   read eLoadRow   write eLoadRow;
  end;

implementation

{$R *.dfm}

uses
  DatenModul, DBGridUtilModule, ConfigModul, SprachModul, ResourceText, Clipbrd;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TDisplayWELTGridRef.Create (const RecRef, RecIndex, RecRefAr : Integer);
begin
  inherited Create (RecRef);

  Index := RecIndex;
  RefAr := RecRefAr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDisplayWELTForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  LVSConfigModul.SaveGridInfo (Self, LTInhaltStringGrid);
  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDisplayWELTForm.FormCreate(Sender: TObject);
begin
  fLENr := '';
  fRefLT := -1;

  fFachGridCol    := -1;

  LVSConfigModul.ConfigForm (Self);

  if not (LVSConfigModul.UseGewicht) then
    fGewichtGridCol := -1
  else begin
    LTInhaltStringGrid.ColCount := LTInhaltStringGrid.ColCount + 1;

    fGewichtGridCol := LTInhaltStringGrid.ColCount - 1;
    LTInhaltStringGrid.TitelTexte.Add (GetResourceText (1095));
  end;

  if not (LVSConfigModul.UseMHD) then
    fMHDGridCol := -1
  else begin
    LTInhaltStringGrid.ColCount := LTInhaltStringGrid.ColCount + 1;

    fMHDGridCol := LTInhaltStringGrid.ColCount - 1;
    LTInhaltStringGrid.TitelTexte.Add (GetResourceText (1095));
  end;

  if not (LVSConfigModul.UseCharge) then
    fChargeGridCol := -1
  else begin
    LTInhaltStringGrid.ColCount := LTInhaltStringGrid.ColCount + 1;

    fChargeGridCol := LTInhaltStringGrid.ColCount - 1;
    LTInhaltStringGrid.TitelTexte.Add (GetResourceText (1096));
  end;

  LTInhaltStringGrid.RowCount := LTInhaltStringGrid.FixedRows + 1;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LENrLabel);
    LVSSprachModul.SetNoTranslate (Self, LETypLabel);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDisplayWELTForm.SetLENr (const LENr : String);
begin
  fLENr := LENr;

  if not ((Length (fLENr) = 9) and (fLENr[1] = '9')) then
    fFachGridCol := -1
  else begin
    LTInhaltStringGrid.ColCount := LTInhaltStringGrid.ColCount + 1;

    fFachGridCol := LTInhaltStringGrid.ColCount - 1;
    LTInhaltStringGrid.TitelTexte.Add ('Fach');
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDisplayWELTForm.FormShow(Sender: TObject);
var
  rowidx : Integer;
  query  : TADOQuery;
begin
  LVSConfigModul.RestoreFormInfo  (Self);

  LVSConfigModul.RestoreGridInfo (Self, LTInhaltStringGrid);

  if (fGewichtGridCol <> -1) and (LTInhaltStringGrid.ColWidths [fGewichtGridCol] = LTInhaltStringGrid.DefaultColWidth) then
    LTInhaltStringGrid.ColWidths [fGewichtGridCol] := LTInhaltStringGrid.Canvas.TextWidth ('99.999,999') + 6;

  if (fMHDGridCol <> -1) and (LTInhaltStringGrid.ColWidths [fMHDGridCol] = LTInhaltStringGrid.DefaultColWidth) then
    LTInhaltStringGrid.ColWidths [fMHDGridCol] := LTInhaltStringGrid.Canvas.TextWidth ('99.99.9999') + 6;

  if (fChargeGridCol <> -1) and (LTInhaltStringGrid.ColWidths [fChargeGridCol] = LTInhaltStringGrid.DefaultColWidth) then
    LTInhaltStringGrid.ColWidths [fChargeGridCol] := LTInhaltStringGrid.Canvas.TextWidth ('9999999999') + 6;

  if (fFachGridCol <> -1) and (LTInhaltStringGrid.ColWidths [fFachGridCol] = LTInhaltStringGrid.DefaultColWidth) then
    LTInhaltStringGrid.ColWidths [fFachGridCol] := LTInhaltStringGrid.Canvas.TextWidth (LTInhaltStringGrid.TitelTexte [fFachGridCol]) + 6;

  if (fRefLT > 0) or (Length (fLENr) > 0) then begin
    eLoadRow (Self, fLENr);

    query := TADOQuery.Create (Self);

    try
      query.Connection := LVSDatenModul.MainADOConnection;


      query.SQl.Add ('select * from V_LE where');

      if (fRefLT > 0) then
        query.SQL.Add ('REF_LE='+IntToStr (fRefLT))
      else
        query.SQL.Add ('LE_NR='+#39+fLENr+#39+' and REF_LAGER='+IntToStr (fRefLager));

      query.Open;

      LENrLabel.Caption  := query.FieldByName('LE_NR').AsString;
      LETypLabel.Caption := query.FieldByName('LE_TYPE').AsString;

      query.Close;


      query.SQL.Clear;
      query.SQL.Add ('select * from V_LE_INHALT');

      if (fRefLT > 0) then
        query.SQL.Add ('where REF_LE='+IntToStr (fRefLT))
      else
        query.SQL.Add ('where LE_NR='+#39+fLENr+#39+' and REF_LAGER='+IntToStr (fRefLager));

      query.SQL.Add ('order by lpad (ARTIKEL_NR, 32, '' '')');

      query.Open;

      rowidx := LTInhaltStringGrid.RowCount - 1;

      if (Length (LTInhaltStringGrid.Cells [1, rowidx]) > 0) then
       Inc (rowidx);

      while not (query.Eof) do begin
        LTInhaltStringGrid.Rows [rowidx].Objects[0] := TDisplayWELTGridRef.Create (-1, query.FieldByName ('REF_LE_WE_POS').AsInteger, query.FieldByName ('REF_AR').AsInteger);

        LTInhaltStringGrid.Cells [1, rowidx] := query.FieldByName ('ARTIKEL_NR').AsString;
        LTInhaltStringGrid.Cells [2, rowidx] := query.FieldByName ('ARTIKEL_TEXT').AsString;
        LTInhaltStringGrid.Cells [3, rowidx] := query.FieldByName ('EAN').AsString;
        LTInhaltStringGrid.Cells [4, rowidx] := query.FieldByName ('AR_VARIANTE').AsString;
        LTInhaltStringGrid.Cells [5, rowidx] := query.FieldByName ('MENGE_FREI').AsString;
        LTInhaltStringGrid.Cells [6, rowidx] := query.FieldByName ('EINHEIT').AsString;

        if (fGewichtGridCol <> -1) then begin
          if (query.FieldByName ('NETTO_GEWICHT').IsNull) then
            LTInhaltStringGrid.Cells [fGewichtGridCol, rowidx] := ''
          else
            LTInhaltStringGrid.Cells [fGewichtGridCol, rowidx] := query.FieldByName ('NETTO_GEWICHT').AsString;
        end;

        if (fMHDGridCol <> -1) then
          LTInhaltStringGrid.Cells [fMHDGridCol, rowidx] := query.FieldByName ('MHD').AsString;

        if (fChargeGridCol <> -1) then
          LTInhaltStringGrid.Cells [fChargeGridCol, rowidx] := query.FieldByName ('CHARGE').AsString;

        if (fFachGridCol <> -1) then
          LTInhaltStringGrid.Cells [fFachGridCol, rowidx] := query.FieldByName ('FACH_POS').AsString;

        Inc (rowidx);

        query.Next;
      end;

      query.Close;

      if (rowidx > LTInhaltStringGrid.FixedRows) then
        LTInhaltStringGrid.RowCount := rowidx
      else begin
        LTInhaltStringGrid.RowCount := LTInhaltStringGrid.FixedRows + 1;
        LTInhaltStringGrid.Rows [LTInhaltStringGrid.FixedRows].Clear;
      end;
    finally
      query.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDisplayWELTForm.LTInhaltStringGridBeforeSort(Sender: TStringGridPro; SortCol: Integer; SortDir: Boolean);
begin
  if (SortCol = 1) then
    Sender.SortArt := soRigthString
  else if (SortCol = 4) then
    Sender.SortArt := soInteger
  else if (SortCol = 6) then
    Sender.SortArt := soFloat
  else if (fMHDGridCol <> -1) and (SortCol = fMHDGridCol) then
    Sender.SortArt := soDateTime
  else
    Sender.SortArt := soString;
end;

procedure TDisplayWELTForm.LTInhaltStringGridPopupMenuPopup(Sender: TObject);
begin

end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.01.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDisplayWELTForm.StringGridColOptimalMenuItemClick(Sender: TObject);
var
  popup : TPopupMenu;
begin
  popup := ((Sender as TMenuItem).GetParentMenu as TPopupMenu);

  if Assigned (popup) and Assigned (popup.PopupComponent) and (popup.PopupComponent is TStringGridPro) then begin
    with (popup.PopupComponent as TStringGridPro) do
      SetGridOptimalColWidth;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.01.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TDisplayWELTForm.StringGridCopyColMenuItemClick(Sender: TObject);
var
  acol,
  arow    : Integer;
  popup   : TPopupMenu;
  clipstr : String;
begin
  popup := ((Sender as TMenuItem).GetParentMenu as TPopupMenu);

  if Assigned (popup) and Assigned (popup.PopupComponent) and (popup.PopupComponent is TStringGridPro) then begin
    with (popup.PopupComponent as TStringGridPro) do begin
      MouseToCell (MouseX, MouseY, acol, arow);

      if (acol >= FixedCols) and (acol < ColCount) and (arow >= FixedRows) and (arow < RowCount) then begin
        clipstr := Cells [acol, arow];

        Clipboard.SetTextBuf (PChar (clipstr));
      end;
    end;
  end;
end;

end.
