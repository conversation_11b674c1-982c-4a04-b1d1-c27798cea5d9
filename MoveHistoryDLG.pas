unit MoveHistoryDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, ExtCtrls, DB, ADODB,
  ComCtrls;

type
  TMoveHistoryForm = class(TForm)
    Panel1: TPanel;
    Panel2: TPanel;
    MoveDBGrid: TDBGridPro;
    CloseButton: TButton;
    Label1: TLabel;
    Label2: TLabel;
    MoveQuery: TADOQuery;
    Date: TDataSource;
    DatumPanel: TPanel;
    Label6: TLabel;
    Label7: TLabel;
    VonDateTimePicker: TDateTimePicker;
    BisDateTimePicker: TDateTimePicker;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    function MoveDBGridColumnSort(Sender: TCustomDBGridPro; const ColumnName: string): string;
    procedure DateTimePickerChange(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    RefLE        : Integer;
    RefLP        : Integer;
    RefBes       : Integer;
    RefLager     : Integer;
    RefArEinheit : Integer;
  end;

implementation

{$R *.dfm}

Uses
  ADOInt, ConfigModul, DatenModul, DBGridUtilModule, LVSSecurity, SprachModul, ResourceText;

//******************************************************************************
//* Function Name: KundenLoeschen
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMoveHistoryForm.DateTimePickerChange(Sender: TObject);
begin
  if not ((Sender as TDateTimePicker).DroppedDown) then begin
    if (Sender = BisDateTimePicker) Then begin
      if (BisDateTimePicker.Date < VonDateTimePicker.Date) then begin
        VonDateTimePicker.Time := 0;
        VonDateTimePicker.Date := Trunc (BisDateTimePicker.Date);
      end;

      VonDateTimePicker.MaxDate := Trunc (BisDateTimePicker.Date);
    end;

    MoveQuery.Close;

    MoveQuery.Parameters.ParamByName('von').ParameterObject.Type_ := adDBTimeStamp;
    MoveQuery.Parameters.ParamByName('von').Value := VonDateTimePicker.Date;
    MoveQuery.Parameters.ParamByName('bis').ParameterObject.Type_ := adDBTimeStamp;
    MoveQuery.Parameters.ParamByName('bis').Value := BisDateTimePicker.Date;

    MoveQuery.Open;
  end;
end;

//******************************************************************************
//* Function Name: KundenLoeschen
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMoveHistoryForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  MoveQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name: KundenLoeschen
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMoveHistoryForm.FormCreate(Sender: TObject);
begin
  RefLE := -1;
  RefLP := -1;

  RefBes       := -1;
  RefLager     := -1;
  RefArEinheit := -1;

  VonDateTimePicker.Date := Trunc(Now - 7);
  BisDateTimePicker.Date := Trunc(Now);
  VonDateTimePicker.Time := 0;
  VonDateTimePicker.MaxDate := BisDateTimePicker.Date;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  if Assigned (LVSSecurityModule) and Assigned (LVSSecurityModule.ACOModul) then begin
    LVSSecurityModule.ACOModul.SetBerechtigungen (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, Label1);
    LVSSprachModul.SetNoTranslate (Self, Label2);
  {$endif}
end;

//******************************************************************************
//* Function Name: KundenLoeschen
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMoveHistoryForm.FormShow(Sender: TObject);
var
  multicolli : Boolean;
  query : TADOQuery;
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.RestoreFormInfo (Self);

  DatumPanel.Visible := (RefArEinheit > 0);

  MoveQuery.SQL.Clear;

  if (RefLE <> -1) then begin
    Label1.Caption := GetResourceText (1098);
    MoveQuery.SQL.Add ('select * from V_LE_MOVES where REF_LE=:ref_le order by MOVE_DATE desc');
    MoveQuery.Parameters.ParamByName('ref_le').Value := RefLE;
  end else if (RefLP <> -1) then begin
    Label1.Caption := GetResourceText (1730);
    MoveQuery.SQL.Add ('select * from V_LE_MOVES where (REF_LP_FROM=:ref_lp_from or REF_LP_TO=:ref_lp_to) order by MOVE_DATE desc');
    MoveQuery.Parameters.ParamByName('ref_lp_from').Value := RefLP;
    MoveQuery.Parameters.ParamByName('ref_lp_to').Value := RefLP;
  end else if (RefBes <> -1) then begin
    Label1.Caption := GetResourceText (1731);
    MoveQuery.SQL.Add ('select * from V_BESTAND_MOVES where REF_BESTAND=:ref_bes order by MOVE_DATE desc');
    MoveQuery.Parameters.ParamByName('ref_bes').Value := RefBes;
  end else if (RefArEinheit <> -1) then begin
    Label1.Caption := GetResourceText (1732);

    query := TADOQuery.Create (Self);
    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select ARTIKEL_NR, ARTIKEL_TEXT, OPT_MULTI_COLLI from V_ARTIKEL_LISTE where REF_AR_EINHEIT=:ref_ae');
      query.Parameters.ParamByName('ref_ae').Value := RefArEinheit;

      query.Open;

      multicolli := query.Fields [2].AsString = '1';

      Label2.Caption := query.Fields [0].AsString + ' / '+query.Fields [1].AsString;

      query.Close;
    finally
      query.Free;
    end;

    if (RefLager > 0) then begin
      MoveQuery.SQL.Add ('select * from V_BESTAND_MOVES where (REF_LAGER_FROM=:ref_lager_from or REF_LAGER_TO=:ref_lager_to)');

      MoveQuery.Parameters.ParamByName('ref_lager_from').Value := RefLager;
      MoveQuery.Parameters.ParamByName('ref_lager_to').Value := RefLager;
    end else begin
      MoveQuery.SQL.Add ('select * from V_BESTAND_MOVES where (REF_LAGER_FROM in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc_1) or REF_LAGER_TO in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc_2))');
      MoveQuery.Parameters.ParamByName('ref_loc_1').Value := LVSDatenModul.AktLocationRef;
      MoveQuery.Parameters.ParamByName('ref_loc_2').Value := LVSDatenModul.AktLocationRef;
    end;

    if (multicolli) then
      MoveQuery.SQL.Add ('and REF_AR_EINHEIT in (select REF_SET_AR_EINHEIT from V_ARTIKEL_EINHEIT_COLLI where REF_MASTER_AR_EINHEIT=:ref_ae)')
    else MoveQuery.SQL.Add ('and REF_AR_EINHEIT=:ref_ae');

    MoveQuery.SQL.Add ('and (MOVE_DATE between :von and :bis) order by MOVE_DATE desc');

    MoveQuery.Parameters.ParamByName('ref_ae').Value := RefArEinheit;
    MoveQuery.Parameters.ParamByName('von').ParameterObject.Type_ := adDBTimeStamp;
    MoveQuery.Parameters.ParamByName('von').Value := VonDateTimePicker.Date;
    MoveQuery.Parameters.ParamByName('bis').ParameterObject.Type_ := adDBTimeStamp;
    MoveQuery.Parameters.ParamByName('bis').Value := BisDateTimePicker.Date;
  end else begin
    Label1.Caption := '';
    Label2.Caption := '';
  end;

  if (Length (MoveQuery.SQL.Text) > 0) then begin
    if (MoveDBGrid.SortColumns[0].ColumnIndex = -1) then
      MoveDBGrid.SetSortColumn(0, 'MOVE_DATE', False);

    MoveQuery.Open;
  end;

  if (RefLE <> -1) then
    Label2.Caption := MoveQuery.FieldByName ('LE_NR').AsString
  else if (RefLP <> -1) then begin
    if (MoveQuery.FieldByName ('REF_LP_FROM').AsInteger = RefLP) then
      Label2.Caption := MoveQuery.FieldByName ('LP_KOOR_FROM').AsString + ' / '+MoveQuery.FieldByName ('LP_FROM').AsString
    else
      Label2.Caption := MoveQuery.FieldByName ('LP_KOOR_TO').AsString + ' / '+MoveQuery.FieldByName ('LP_KOOR_TO').AsString
  end else if (RefBes <> -1) then begin
    Label2.Caption := MoveQuery.FieldByName ('ARTIKEL_NR').AsString + ' / '+MoveQuery.FieldByName ('ARTIKEL_TEXT').AsString
  end;
end;

//******************************************************************************
//* Function Name: MoveDBGridColumnSort
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TMoveHistoryForm.MoveDBGridColumnSort(Sender: TCustomDBGridPro; const ColumnName: string): string;
begin
  if (ColumnName = 'MOVE_DATE') then
    Result := 'REF'
  else
    Result := ColumnName;
end;

end.
