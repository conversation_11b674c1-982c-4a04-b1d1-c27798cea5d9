object EditSysTextForm: TEditSysTextForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'EditSysTextForm'
  ClientHeight = 452
  ClientWidth = 472
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    472
    452)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 13
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Label3: TLabel
    Left = 112
    Top = 57
    Width = 41
    Height = 13
    Caption = 'Text-Nr.'
  end
  object Label4: TLabel
    Left = 8
    Top = 200
    Width = 22
    Height = 13
    Caption = 'Text'
  end
  object Label5: TLabel
    Left = 8
    Top = 102
    Width = 57
    Height = 13
    Caption = 'Reihenfolge'
  end
  object Label6: TLabel
    Left = 215
    Top = 57
    Width = 45
    Height = 13
    Caption = 'Definition'
  end
  object Label7: TLabel
    Left = 112
    Top = 102
    Width = 52
    Height = 13
    Caption = 'Verlinkt mit'
  end
  object Label8: TLabel
    Left = 216
    Top = 102
    Width = 79
    Height = 13
    Caption = 'Referenziert auf'
  end
  object Label2: TLabel
    Left = 8
    Top = 157
    Width = 39
    Height = 13
    Caption = 'Sprache'
  end
  object Label9: TLabel
    Left = 8
    Top = 320
    Width = 44
    Height = 13
    Caption = 'Variablen'
  end
  object Label10: TLabel
    Left = 8
    Top = 364
    Width = 43
    Height = 13
    Caption = 'Attribute'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 408
    Width = 456
    Height = 6
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 370
  end
  object Label11: TLabel
    Left = 8
    Top = 59
    Width = 40
    Height = 13
    Caption = 'Auswahl'
  end
  object Bevel2: TBevel
    Left = 8
    Top = 149
    Width = 456
    Height = 8
    Shape = bsTopLine
  end
  object MandComboBox: TComboBoxPro
    Left = 8
    Top = 29
    Width = 456
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 0
  end
  object TextMemo: TMemo
    Left = 8
    Top = 216
    Width = 456
    Height = 89
    Anchors = [akLeft, akTop, akRight]
    Lines.Strings = (
      'TextMemo')
    MaxLength = 1024
    TabOrder = 8
  end
  object NrEdit: TEdit
    Left = 112
    Top = 73
    Width = 57
    Height = 21
    MaxLength = 6
    TabOrder = 2
    Text = 'NrEdit'
    OnKeyPress = NumberEditKeyPress
  end
  object OkButton: TButton
    Left = 308
    Top = 419
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    Default = True
    ModalResult = 1
    TabOrder = 12
  end
  object AbortButton: TButton
    Left = 389
    Top = 419
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 13
  end
  object SelEdit: TEdit
    Left = 8
    Top = 118
    Width = 57
    Height = 21
    MaxLength = 6
    TabOrder = 4
    Text = 'SelEdit'
    OnKeyPress = NumberEditKeyPress
  end
  object DefEdit: TEdit
    Left = 216
    Top = 75
    Width = 248
    Height = 21
    MaxLength = 64
    TabOrder = 3
    Text = 'DefEdit'
  end
  object LinkEdit: TEdit
    Left = 112
    Top = 118
    Width = 74
    Height = 21
    MaxLength = 6
    TabOrder = 5
    Text = 'LinkEdit'
    OnKeyPress = NumberEditKeyPress
  end
  object RefEdit: TEdit
    Left = 216
    Top = 118
    Width = 74
    Height = 21
    MaxLength = 6
    TabOrder = 6
    Text = 'RefEdit'
    OnKeyPress = NumberEditKeyPress
  end
  object LangComboBox: TComboBoxPro
    Left = 8
    Top = 173
    Width = 456
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 7
  end
  object DefaultCheckBox: TCheckBox
    Left = 8
    Top = 423
    Width = 97
    Height = 17
    Anchors = [akLeft, akBottom]
    Caption = 'Default Eintrag'
    TabOrder = 11
  end
  object VariableEdit: TEdit
    Left = 8
    Top = 336
    Width = 456
    Height = 21
    Hint = 
      'Variablen werden bei der Auswahl automatisch abgefragt und an St' +
      'elle von %1, %2 oder %3 im Text eingesetzt'
    MaxLength = 64
    TabOrder = 9
    Text = 'VariableEdit'
  end
  object AttributEdit: TEdit
    Left = 8
    Top = 380
    Width = 456
    Height = 21
    Hint = 
      'Variablen werden bei der Auswahl automatisch abgefragt und an St' +
      'elle von %1, %2 oder %3 im Text eingesetzt'
    MaxLength = 64
    TabOrder = 10
    Text = 'AttributEdit'
  end
  object SubAreaEdit: TEdit
    Left = 8
    Top = 75
    Width = 89
    Height = 21
    MaxLength = 32
    TabOrder = 1
    Text = 'SubAreaEdit'
  end
end
