object CreateDayLicForm: TCreateDayLicForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Tageslizenz buchen'
  ClientHeight = 248
  ClientWidth = 583
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  TextHeight = 13
  object TopPanel: TPanel
    Left = 0
    Top = 0
    Width = 583
    Height = 193
    Align = alClient
    TabOrder = 0
    DesignSize = (
      583
      193)
    object LicenceGroupBox: TGroupBox
      Left = 8
      Top = 4
      Width = 567
      Height = 165
      Anchors = [akLeft, akTop, akRight, akBottom]
      Caption = 'Weitere Tagslizenzenzen kostenpflichtig buchen'
      TabOrder = 0
      DesignSize = (
        567
        165)
      object Label2: TLabel
        Left = 24
        Top = 30
        Width = 32
        Height = 13
        Caption = 'Anzahl'
      end
      object Label3: TLabel
        Left = 24
        Top = 75
        Width = 102
        Height = 19
        Caption = 'Gesamtpreis'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object PriceLabel: TLabel
        Left = 158
        Top = 75
        Width = 85
        Height = 19
        Caption = 'PriceLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label4: TLabel
        Left = 24
        Top = 120
        Width = 118
        Height = 13
        Caption = 'Interner Kundenvermerk'
      end
      object Label1: TLabel
        Left = 255
        Top = 30
        Width = 114
        Height = 15
        Alignment = taRightJustify
        Anchors = [akTop, akRight]
        AutoSize = False
        Caption = 'Aktivierungsdatum'
      end
      object LicCountEdit: TEdit
        Left = 158
        Top = 27
        Width = 49
        Height = 21
        TabOrder = 0
        Text = '1'
        OnChange = LicCountEditChange
      end
      object LicCountUpDown: TUpDown
        Left = 207
        Top = 27
        Width = 17
        Height = 21
        Associate = LicCountEdit
        Position = 1
        TabOrder = 1
      end
      object HintEdit: TEdit
        Left = 158
        Top = 118
        Width = 395
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 2
        Text = 'HintEdit'
      end
      object FromDatePicker: TAdvDateTimePicker
        Left = 381
        Top = 27
        Width = 172
        Height = 21
        Anchors = [akTop, akRight]
        Date = 45267.000000000000000000
        Format = ''
        Time = 0.508020833331102000
        DoubleBuffered = True
        Kind = dkDateTime
        ParentDoubleBuffered = False
        TabOrder = 3
        BorderStyle = bsSingle
        Ctl3D = True
        DateTime = 45267.508020833330000000
        Version = '1.3.6.6'
        LabelFont.Charset = DEFAULT_CHARSET
        LabelFont.Color = clWindowText
        LabelFont.Height = -11
        LabelFont.Name = 'Tahoma'
        LabelFont.Style = []
      end
    end
  end
  object BottomPanel: TPanel
    Left = 0
    Top = 193
    Width = 583
    Height = 55
    Align = alBottom
    TabOrder = 1
    DesignSize = (
      583
      55)
    object NettoLabel: TLabel
      Left = 32
      Top = 24
      Width = 227
      Height = 13
      Caption = '* es handelt sich ausschlie'#223'lich um Nettopreise.'
    end
    object CreateButton: TButton
      Left = 304
      Top = 20
      Width = 179
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Kostenpflichtig buchen'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 0
      OnClick = CreateButtonClick
    end
    object AbortButton: TButton
      Left = 497
      Top = 20
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
  end
end
