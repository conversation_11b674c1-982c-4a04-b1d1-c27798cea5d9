﻿unit AuftragPosVerpackenDLG;

{$i compilers.inc}

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, Grids, DBGrids, SMDBGrid, DBGridPro, StdCtrls, ExtCtrls,
  ComCtrls, BarCodeScanner, FrontendUtils, Menus, GR32_Image, LogFile,
  ACOList, WaagenModul

  {$IFDEF VER360}
    ,System.Actions, Vcl.ActnList
  {$ELSE}
    {$IFDEF VER350}
      ,System.Actions
    {$else}
      ,ActnList
    {$endif}
  {$endif}
  ;

type
  TAuftragPosVerpackenForm = class(TForm)
    InhaltPanel: TPanel;
    Label1: TLabel;
    AuftragNrLabel: TLabel;
    Label3: TLabel;
    WarenempfLabel: TLabel;
    Label2: TLabel;
    NVELabel: TLabel;
    Bevel1: TBevel;
    ButtonPanel: TPanel;
    Bevel2: TBevel;
    AbortButton: TButton;
    VerpackenButton: TButton;
    FehlerPanel: TPanel;
    Timer1: TTimer;
    SpedLabel: TLabel;
    VerpackAufPosListViewPopupMenu: TPopupMenu;
    ChangeGewichtMenuItem: TMenuItem;
    InfoPanel: TPanel;
    VerpackungLabel: TLabel;
    PresentLabel: TLabel;
    VPEAnzLabel: TLabel;
    Label4: TLabel;
    BruttoGewichtEdit: TEdit;
    Label5: TLabel;
    SelectAllMenuItem: TMenuItem;
    N1: TMenuItem;
    DeselectAllMenuItem: TMenuItem;
    NewNVEButton: TButton;
    VersandKartonLabel: TLabel;
    ListPanel: TPanel;
    ArtikelImagePanel: TPanel;
    ArtikelImage: TImage32;
    VerpackAufPosListView: TListView;
    WaBoxePanel: TPanel;
    Bevel3: TBevel;
    Label7: TLabel;
    UsedBoxLabel: TLabel;
    Label8: TLabel;
    LSTextLabel: TLabel;
    EmmasbocPanel: TPanel;
    Label9: TLabel;
    EmmasboxResLabel: TLabel;
    Label10: TLabel;
    LieferDatumLabel: TLabel;
    SubMandantPanel: TPanel;
    SubMandantLabel: TLabel;
    Bevel5: TBevel;
    VerpackMengeLabel: TLabel;
    AbmessungPanel: TPanel;
    LTLength: TEdit;
    LTWidth: TEdit;
    LTHeigth: TEdit;
    Label11: TLabel;
    Label12: TLabel;
    Label13: TLabel;
    Label14: TLabel;
    FehlerButton: TButton;
    ActionList1: TActionList;
    ErrorConfirmAction: TAction;
    ColumnsResizeMenuItem: TMenuItem;
    N2: TMenuItem;
    FachLabel: TLabel;
    SpedPanel: TPanel;
    ChangeSpedButton: TButton;
    Label6: TLabel;
    SpedImage: TImage;
    Bevel4: TBevel;
    Bevel7: TBevel;
    PrintIDLabelMenuItem: TMenuItem;
    PrintEANLabelMenuItem: TMenuItem;
    N3: TMenuItem;
    ACOListForm1: TACOListForm;
    ReprintAdvertisingButton: TButton;
    ContainerLabel: TLabel;
    PrintBarcodeLabelMenuItem: TMenuItem;
    PackHinweisLabel: TLabel;
    PackHintLabel: TLabel;
    WaagePanel: TPanel;
    WaageLabel: TLabel;
    WaageGewichtLabel: TLabel;
    WaagePaintBox: TPaintBox;
    PaintBox2: TPaintBox;
    SummenPanel: TPanel;
    SummeVPELabel: TLabel;
    WaageLEDPanel: TPanel;
    N4: TMenuItem;
    BulkPackMenuItem: TMenuItem;
    N5: TMenuItem;
    Zellekopieren1: TMenuItem;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure VerpackAufPosListViewDeletion(Sender: TObject; Item: TListItem);
    procedure Timer1Timer(Sender: TObject);
    procedure VerpackAufPosListViewMouseUp(Sender: TObject;
      Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
    procedure ChangeSpedButtonClick(Sender: TObject);
    procedure VerpackAufPosListViewClick(Sender: TObject);
    procedure ChangeGewichtMenuItemClick(Sender: TObject);
    procedure BruttoGewichtEditKeyPress(Sender: TObject; var Key: Char);
    procedure BruttoGewichtEditChange(Sender: TObject);
    procedure SelectAllMenuItemClick(Sender: TObject);
    procedure DeselectAllMenuItemClick(Sender: TObject);
    procedure VerpackAufPosListViewPopupMenuPopup(Sender: TObject);
    procedure NewNVEButtonClick(Sender: TObject);
    procedure VerpackAufPosListViewChange(Sender: TObject; Item: TListItem;
      Change: TItemChange);
    procedure ListPanelResize(Sender: TObject);
    procedure VerpackAufPosListViewKeyPress(Sender: TObject; var Key: Char);
    procedure VerpackAufPosListViewKeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
    procedure LTDimKeyPress(Sender: TObject; var Key: Char);
    procedure ErrorConfirmActionExecute(Sender: TObject);
    procedure ColumnsResizeMenuItemClick(Sender: TObject);
    procedure FormActivate(Sender: TObject);
    procedure VerpackAufPosListViewMouseDown(Sender: TObject;
      Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
    procedure FormDestroy(Sender: TObject);
    procedure VerpackAufPosListViewColumnClick(Sender: TObject;
      Column: TListColumn);
    procedure VerpackAufPosListViewCompare(Sender: TObject; Item1,
      Item2: TListItem; Data: Integer; var Compare: Integer);
    procedure PrintLabelMenuItemClick(Sender: TObject);
    procedure ReprintAdvertisingButtonClick(Sender: TObject);
    procedure WaagePanelDblClick(Sender: TObject);
    procedure WaagePaintBoxPaint(Sender: TObject);
    procedure VerpackAufPosListViewResize(Sender: TObject);
    procedure Zellekopieren1Click(Sender: TObject);
  private
    fRefSped       : Integer;
    fRefSpedProd   : Integer;
    fManRefSped    : Integer;
    fRefMand       : Integer;
    fRefSubMand    : Integer;
    fRefLager      : Integer;
    fRefNVE        : Integer;
    fRefAuftrag    : Integer;
    fRefAufKommPos : Integer;
    fRefPackplatz  : Integer;
    fLager,
    fMandant,
    fIFCSped,
    fVersandArt        : String;
    fLiefPLZ,
    fISOLand           : String;
    fRefLTType         : Integer;
    fLTTara            : Integer;     //Taragewicht der Verpackung
    fNVENr             : String;
    fNVESerial         : String;
    fPackHinweis       : String;
    fWarnHinweis       : String;
    fPackHintHeight    : Integer;
    fInfoPanelHeight   : Integer;
    fAusland           : Boolean;
    fOptCOD            : Boolean;
    fCanChangeNVE      : Boolean;
    fOnlyCloseComplete : Boolean;
    fSpedInfos         : TSpedInfos;
    fShippingUnits     : Integer;     //Gesamtanzahl der Packstücke im Auftrag (z. B. für DB Schenker oder DHL2MH
    fNeedShippingUnits : Boolean;     //Gesamtanzahl der Packstücke muss vor dem Verpacken bekannt sein, für 1 von n Packstücke
    fSerialMinLen        : Integer;
    fSerialMaxLen        : Integer;
    fSerialUniqueAllOver : Boolean;
    fVerpackArt          : String;    //Art der Verpackung
    fVerpackArtFix       : Boolean;   //=true: Neutrale Verpakungen dürfen nicht genutzt werden
    fFlagPackFolge       : Boolean;   //das Feld PACK_FOLGE ist in VQ_ARTIKEL enthalten
    fFlagArPackText      : Boolean;   //das Feld OPT_VERPACKEN ist in VQ_ARTIKEL enthalten
    fRefContainer        : Integer;   //In diesen Container (Master NVE) werden die Pakete gepackt

    fListViewSortCol     : Integer;
    fListViewSortDir     : Integer;

    fLastPicturePath   : String;
    fGewichtCol        : Integer;

    fPackTimeFlag      : Boolean;
    fGroupPos          : Boolean;
    fAutoClose         : Boolean;
    fAutoFinsih        : Boolean;  //Abschliessen nach dem letzten Pack
    fErrorConfirmed    : Boolean;  //Fehler müssen bestätigt werden
    fSelectPackLT      : Boolean;
    fAddVPEVerpacken   : Boolean;
    fUseSerial         : Boolean;
    fOnePackage        : Boolean;
    fScanBestandID     : Boolean;

    fMengeStr          : String;
    fSollMengeCol      : TListColumn;
    fMengeCol          : TListColumn;
    fMengeSug          : TListColumn;   //Spalte für den Verpackungsvorschlag
    fSerialCol         : TListColumn;
    fChargeCol         : TListColumn;

    fRefBoxType        : Integer;  //Dieser LT-Type wird per Scan ausgewählt
    fRefAufLTType      : Integer;  //Verpackungart aus dem Auftrag
    fRefAufLTTara      : Integer;  //Taragewicht der Verpackung aus dem Auftrag
    fAufLTName         : String;
    fFachNr            : Integer;
    fScanPackMenge     : Integer;  //Die Menge soll verpackt werden

    fSumVPE            : Integer;

    fOldWaageGewicht   : Integer;
    fWiegeThread       : TWiegeThread;

    {$ifdef TraceVerpacken}
      verpacklog : TLogFile;
    {$endif}

    fSelectedColumnIndex : Integer;

    procedure AppIdle (Sender: TObject; var Done: Boolean);

    procedure ReloadForm (Sender: TObject);
    procedure VerpackAufPosListCheckedChange (Sender: TObject; Item : TListItem);
    procedure ScannerErfassung (var Message: TMessage); message WM_USER + SCANNER_DETECT;

    procedure UpdateVerpackAufPosListView (var Message: TMessage); message WM_USER + 10;
    procedure CloseMessage                (var Message: TMessage); message WM_USER + 99;
    procedure AbortMessage                (var Message: TMessage); message WM_USER + 98;
    procedure UpdateSpeditionInfos;
    procedure ShowSpedInfoLT (const SpedInfos : TSpedInfos);
    procedure ShowErrorPanel (const ErrorText : String);
    procedure CheckShippingUnits;
  public
    property RefPackplatz      : Integer read fRefPackplatz      write fRefPackplatz;
    property RefNVE            : Integer read fRefNVE            write fRefNVE;
    property RefAuftrag        : Integer read fRefAuftrag        write fRefAuftrag;
    property RefAufKommPos     : Integer read fRefAufKommPos     write fRefAufKommPos;
    property OnlyCloseComplete : Boolean read fOnlyCloseComplete write fOnlyCloseComplete;
    property FachNr            : Integer read fFachNr            write fFachNr;
    property ScanPackMenge     : Integer read fScanPackMenge     write fScanPackMenge;
  end;

implementation

{$R *.dfm}

uses
  {$if CompilerVersion > 30.0}
    System.UITypes,
  {$ifend}

  mmSystem, VCLUtilitys, OraSmart,
  ConfigModul, LVSConst, DatenModul, DBGridUtilModule, LVSGlobalDaten, LVSDatenInterface, StringUtils,
  VersandAbwicklung, PrintModul, PrinterUtils, DMSDatenInterface, PDFPreviewDLG, FrontendACOModul,
  ErrorTracking, ResourceText, SelectSpeditionTouchDLG, LVSSecurity, KeyboardUtils, EAN128Utils,
  ArtikelIdentInputDLG, SprachModul, InputLHMSerial,InfoWinForm, AddKommInfoDLG, FrontendDialogs,MessageExDLG,
  VerlaufDLG, FrontendMessages, ChangeAufTourDLG, ValueAddedDLG, EANLabelPrintDLG, LablePrinterUtils, LVSDatenaustausch
  , ClipBrd
  ;

type
  TVerpackEntry = class (TComboBoxRef)
    Done          : Boolean;
    RefAufPos     : Integer;
    RefAr         : Integer;
    RefEinheit    : Integer;
    RefArEinheit  : Integer;
    GesamtMenge   : Integer;
    BruttoGewicht : Double;
    RefSped       : Integer;
    RefSpedProd   : Integer;
    L,B,H         : Integer;
    Spedition     : String;
    Charge        : String;
    Picture       : String;
    OptSerial     : Boolean;
    SerialDefine  : Boolean;
    OptUmverpackt : Boolean;
    EAN           : String;
    Barcode       : String;
    Serial        : String;
    BestandID     : String;
    PackHint      : String;

    constructor Create (const pRef, pRefAufPos, pRefAr, pRefEinheit, pRefArEinheit, pGesamtMenge : Integer; const pBruttoGewicht : Double; const pRefSped, pRefSpedProd : Integer; const pSpedition, pCharge, pPicture, pPackHint, pBestandID : String; const pOptSerial, pOptUmverpackt : Boolean; const pL, pB, pH : Integer);
  end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TVerpackEntry.Create (const pRef, pRefAufPos, pRefAr, pRefEinheit, pRefArEinheit, pGesamtMenge : Integer; const pBruttoGewicht : Double; const pRefSped, pRefSpedProd : Integer; const pSpedition, pCharge, pPicture, pPackHint, pBestandID : String; const pOptSerial, pOptUmverpackt : Boolean; const pL, pB, pH : Integer);
begin
  inherited Create (pRef);

  Done          := False;
  EAN           := '';
  Barcode       := '';
  Serial        := '';

  Charge        := pCharge;
  Picture       := pPicture;
  PackHint      := pPackHint;
  Spedition     := pSpedition;
  RefAufPos     := pRefAufPos;
  RefSped       := pRefSped;
  RefSpedProd   := pRefSpedProd;
  RefAr         := pRefAr;
  RefEinheit    := pRefEinheit;
  RefArEinheit  := pRefArEinheit;
  BestandID     := pBestandID;
  GesamtMenge   := pGesamtMenge;
  BruttoGewicht := pBruttoGewicht;
  OptSerial     := pOptSerial;
  OptUmverpackt := pOptUmverpackt;
  L             := pL;
  B             := pB;
  H             := pH;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.11.2014
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.BruttoGewichtEditChange(Sender: TObject);
begin
  FehlerPanel.Tag := 0;
  //FehlerPanel.Visible := False;

  BruttoGewichtEdit.Color := clWindow;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.11.2014
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.BruttoGewichtEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (CharInSet (Key, [#8,#9,^C,^V,'0'..'9',','])) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.NewNVEButtonClick(Sender: TObject);
begin
  fRefNVE       := -1;

  if (fSpedInfos.RefDefaultLT > 0) then begin
    fRefLTType := fSpedInfos.RefDefaultLT;
    fLTTara    := fSpedInfos.DefaultLTTara;

    VersandKartonLabel.Caption := fSpedInfos.DefaultLT;
  end else if (fRefAufLTType > 0) then begin
    fRefLTType    := fRefAufLTType;
    fLTTara       := fRefAufLTTara;

    VersandKartonLabel.Caption := fAufLTName;
  end;

  fNVESerial := '';
  fNVENr     := '';

  NVELabel.Caption := '';

  NewNVEButton.Enabled := (fRefNVE > 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.01.2022
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.ShowSpedInfoLT (const SpedInfos : TSpedInfos);
begin
  VersandKartonLabel.Caption := fSpedInfos.DefaultLT;

  if (fSpedInfos.PresetWeight) or AbmessungPanel.Visible then begin
    if (fSpedInfos.DefaultLTL > 0) then
      LTLength.Text := IntToStr (fSpedInfos.DefaultLTL div 10);

    if (fSpedInfos.DefaultLTB > 0) then
      LTWidth.Text := IntToStr (fSpedInfos.DefaultLTB div 10);

    if (fSpedInfos.DefaultLTH > 0) then
      LTHeigth.Text := IntToStr (fSpedInfos.DefaultLTH div 10);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.01.2022
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.PrintLabelMenuItemClick(Sender: TObject);
var
  idx,
  res        : Integer;
  eanprtform : TEANLabelPrintForm;
begin
  idx := VerpackAufPosListView.ItemIndex;

  if (idx <> -1) and Assigned (VerpackAufPosListView.Items [idx].Data) then begin
    eanprtform := TEANLabelPrintForm.Create(Self);

    try
      eanprtform.Prepare(UserReg.ReadRegValue ('VPE-PRINTER'), fRefMand, fRefLager, isShiftDown);

      if (Sender = PrintEANLabelMenuItem) then
        eanprtform.EANTabSheet.TabVisible := True
      else if (Sender = PrintBarcodeLabelMenuItem) then
        eanprtform.BarcodeTabSheet.TabVisible := True
      else
        eanprtform.PackTabSheet.TabVisible := True;

      res := GetArtikelInfos (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefAr, TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit, eanprtform.ArtikelInfo);

      eanprtform.ArtNrLabel.Caption := eanprtform.ArtikelInfo.ArtikelNr;
      eanprtform.ArtTextLabel.Caption := eanprtform.ArtikelInfo.ArtikelText;

      eanprtform.ArtEANLabel.Caption := eanprtform.ArtikelInfo.BasisEANCode;
      eanprtform.BarcodeLabel.Caption := eanprtform.ArtikelInfo.BasisBarcode;

      eanprtform.BestandIDEdit.Enabled := False;
      eanprtform.BestandIDEdit.Text := TVerpackEntry (VerpackAufPosListView.Items [idx].Data).BestandID;

      eanprtform.SerialEdit.Enabled := False;
      eanprtform.SerialEdit.Text := TVerpackEntry (VerpackAufPosListView.Items [idx].Data).Serial;

      if isShiftDown or (eanprtform.Preview) or (eanprtform.ArtikelInfo.GewichtFlag) then
        eanprtform.ShowModal
      else
        eanprtform.DoPrint (Sender);

      UserReg.WriteRegValue ('VPE-PRINTER', eanprtform.GetSelectedPrinter);
    finally
      eanprtform.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.05.2018
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.ListPanelResize(Sender: TObject);
begin
  if (ArtikelImagePanel.Visible) then begin
    ArtikelImagePanel.Height := ListPanel.ClientHeight - ArtikelImagePanel.Top - 4;
    ArtikelImagePanel.Width := ArtikelImagePanel.Height;
    ArtikelImagePanel.Left := ListPanel.ClientWidth - ArtikelImagePanel.Width - 8;

    VerpackAufPosListView.Height := ListPanel.ClientHeight - VerpackAufPosListView.Top - 4;
    VerpackAufPosListView.Width := ArtikelImagePanel.Left - VerpackAufPosListView.Left - 16;
  end else begin
    VerpackAufPosListView.Height := ListPanel.ClientHeight - VerpackAufPosListView.Top - 4;
    VerpackAufPosListView.Width := ListPanel.ClientWidth - 2 * VerpackAufPosListView.Left;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.LTDimKeyPress(Sender: TObject; var Key: Char);
begin
  if Key = #13 then
    Self.SelectNext (TCustomEdit(Sender), true, true)
  else if not (CharInSet (Key, [#8,^C,^V,'0'..'9'])) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 23.10.2014
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.ChangeGewichtMenuItemClick(Sender: TObject);
var
  i,
  idx,
  anz,
  menge  : Integer;
  gw,
  fgw    : Double;
  gwstr,
  numstr : String;
begin
  idx := VerpackAufPosListView.ItemIndex;

  if (idx <> -1) and Assigned (VerpackAufPosListView.Items [idx].Data) then begin
    if (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).BruttoGewicht <= 0) then
      gwstr := ''
    else gwstr := Format ('%4.5f', [TVerpackEntry (VerpackAufPosListView.Items [idx].Data).BruttoGewicht / 1000]);

    if InputQuery (GetResourceText (1317), GetResourceText (1212), gwstr) then begin
      if (Length (gwstr) = 0) then
        TVerpackEntry (VerpackAufPosListView.Items [VerpackAufPosListView.ItemIndex].Data).BruttoGewicht := -1
      else if (TryStrToFloat (gwstr, fgw)) then
        TVerpackEntry (VerpackAufPosListView.Items [VerpackAufPosListView.ItemIndex].Data).BruttoGewicht := fgw * 1000;

      if (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).BruttoGewicht > 0) then
        VerpackAufPosListView.Items [idx].SubItems [fGewichtCol] := Format ('%.5f kg', [TVerpackEntry (VerpackAufPosListView.Items [idx].Data).BruttoGewicht / 1000])
      else VerpackAufPosListView.Items [idx].SubItems [fGewichtCol] := '';

      gw := 0;
      anz := 0;

      for i := 0 to VerpackAufPosListView.Items.Count - 1 do begin
        if (VerpackAufPosListView.Items [i].Checked) then begin
          if (TVerpackEntry (VerpackAufPosListView.Items [i].Data).BruttoGewicht > 0) then begin
            anz := anz + 1;

            if not Assigned (fMengeCol) then begin
              gw := gw +  TVerpackEntry (VerpackAufPosListView.Items [i].Data).BruttoGewicht;
            end else begin
              //Prüfen, ob alles in der Position verpackt wurde
              numstr := VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1];

              if (Length (numstr) > 0) and TryStrToInt(numstr, menge) then begin
                gw := gw + TVerpackEntry (VerpackAufPosListView.Items [i].Data).BruttoGewicht * menge;
              end;
            end;
          end;
        end;
      end;

      if (anz = 0) then
        BruttoGewichtEdit.Text := ''
      else if (gw <= 0) then
        BruttoGewichtEdit.Text := ''
      else begin
        if (fLTTara > 0) then
          gw := gw + fLTTara;

        BruttoGewichtEdit.Text := Format ('%.3f', [gw / 1000]);
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.UpdateSpeditionInfos;
var
  h       : Integer;
  dispstr : String;
begin
  if (fRefSped > 0) then begin
    GetSpedInfos (fRefSped, fRefSpedProd, fAusland, fSpedInfos);

    dispstr := fSpedInfos.SpedName;

    if (Length (fVersandArt) > 0) and (copy (fVersandArt, 1, 6) <> '$ROLE$')  and (fVersandArt <> '~') then
      dispstr := dispstr + ' ('+fVersandArt+')';

    if (fOptCOD) then
      dispstr := dispstr + ' ('+GetResourceText (1629)+')';

    SpedLabel.Caption := dispstr;

    if (fSpedInfos.LTDimRequired and not AbmessungPanel.Visible) then
      AbmessungPanel.Visible := true;

    LTLength.Enabled := fSpedInfos.LTDimRequired;
    LTWidth.Enabled  := fSpedInfos.LTDimRequired;
    LTHeigth.Enabled := fSpedInfos.LTDimRequired;

    fNeedShippingUnits := fSpedInfos.NeedShippingUnits;

    CheckShippingUnits;

    if (fSpedInfos.SpedLogo = 'DHL') then begin
      SpedPanel.Color := RGB (255, 204, 000);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_dhl');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'DHL-EXPRESS') then begin
      SpedPanel.Color := RGB (255, 204, 000);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_dhl_express');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'POST') then begin
      SpedPanel.Color := RGB (255, 207, 17);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_post');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'POST_CH') then begin
      SpedPanel.Color := RGB (252, 210, 5);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_post_ch');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'POST_AT') then begin
      SpedPanel.Color := RGB (255, 221, 0);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_post_at');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'POST_FR') then begin
      SpedPanel.Color := RGB (250, 166, 37);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_post_fr');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'POST_NL') then begin
      SpedPanel.Color := RGB (248, 170, 0);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_post_nl');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'DPD') then begin
      SpedPanel.Color := RGB ($C9, $00, $33);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_dpd');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'BRT') then begin
      SpedPanel.Color := RGB (225, 6, 40);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_brt');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'IDS') then begin
      SpedPanel.Color := RGB (0, 107, 171);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_ids');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'HERMES') then begin
      SpedPanel.Color := RGB (1, 171, 220);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_hermes');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'HEYWORLD') then begin
      SpedPanel.Color := RGB (1, 171, 220);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_heyworld');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'GLS') then begin
      SpedPanel.Color := RGB (0, 107, 171);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_gls');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'UPS') then begin
      SpedPanel.Color := RGB (0, 24, 83);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_ups');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'PRIME') then begin
      SpedPanel.Color := RGB (255, 153, 0);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_prime');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'SCHENKER') then begin
      SpedPanel.Color := RGB (255, 0, 0);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_schenker');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'FEDEX') then begin
      SpedPanel.Color := RGB (255, 0, 0);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_fedex');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'DACHSER') then begin
      SpedPanel.Color := RGB (250, 201, 11);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_dachser');
      SpedImage.Visible := true;
    end else begin
      SpedPanel.Color := clBtnFace;
      SpedImage.Visible := False;
    end;

    if not (SpedImage.Visible) then
      SpedLabel.Left := SpedImage.Left
    else
      SpedLabel.Left := SpedImage.Left + SpedImage.Width + 24;

    if (fSpedInfos.SpedColor = -1) then
      SpedPanel.Color := clBtnFace
    else
      SpedPanel.Color := fSpedInfos.SpedColor;

    if (SpedPanel.Color > 0) then begin
      //Helligkeit:
      h := trunc (GetRValue (SpedPanel.Color) * 0.3 + GetGValue (SpedPanel.Color) * 0.59 + GetBValue (SpedPanel.Color) * 0.11);

      if (trunc (GetRValue (SpedPanel.Color) * 0.3 + GetGValue (SpedPanel.Color) * 0.59 + GetBValue (SpedPanel.Color) * 0.11) > 128) then begin
        Label6.Font.Color := clWindowText;
        SpedLabel.Font.Color := clWindowText;
      end else begin
        Label6.Font.Color := clWhite;
        SpedLabel.Font.Color := clWhite;
      end;
    end else begin
      Label6.Font.Color := clWindowText;
      SpedLabel.Font.Color := clWindowText;
    end;

    //Nur wenn noch kein Karton erfasst wurde, wird der Default-LT des Spediteurs übernommen
    if (fRefBoxType <= 0) and (fSpedInfos.RefDefaultLT > 0) then begin
      fRefLTType := fSpedInfos.RefDefaultLT;
      fLTTara    := fSpedInfos.DefaultLTTara;

      ShowSpedInfoLT (fSpedInfos);
    end else if (fRefBoxType <= 0) and (fRefAufLTType > 0) then begin
      fRefLTType := fRefAufLTType;
      fLTTara    := fRefAufLTTara;

      VersandKartonLabel.Caption := fAufLTName;
    end;
  end else begin
    Label6.Font.Color := clWindowText;
    SpedLabel.Font.Color := clWindowText;
    SpedLabel.Caption := '';
    SpedPanel.Color := clBtnFace;
    SpedImage.Visible := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.ChangeSpedButtonClick(Sender: TObject);
var
  selfrom : TSelectSpeditionTouchForm;
begin
  FehlerPanel.Tag := 0;
  //FehlerPanel.Visible := False;

  selfrom := TSelectSpeditionTouchForm.Create (Self);

  try
    selfrom.RefMand      := fRefMand;
    selfrom.RefPackplatz := fRefPackplatz;
    selfrom.RefSped      := fRefSped;

    if (selfrom.ShowModal = mrOk) then begin
      fRefSpedProd := -1;

      fManRefSped  := selfrom.RefSped;
      fRefSped     := fManRefSped;

      UpdateSpeditionInfos;
    end;
  finally
    selfrom.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.10.2017
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.DeselectAllMenuItemClick(Sender: TObject);
var
  idx : Integer;
begin
  if fAddVPEVerpacken then begin
    idx := 0;

    while (idx < VerpackAufPosListView.Items.Count) do begin
      VerpackAufPosListView.Items [idx].Checked := False;

      Inc (idx);
    end;

    VerpackAufPosListCheckedChange (Sender, VerpackAufPosListView.Items [0]);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.ErrorConfirmActionExecute(Sender: TObject);
begin
  FehlerPanel.Tag := 0;
  //FehlerPanel.Visible := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 11.06.2018
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.AppIdle (Sender: TObject; var Done: Boolean);
var
  idx,
  menge  : Integer;
  flag   : Boolean;
  numstr : String;
begin
  Application.OnIdle := nil;

  if (fAutoClose) then begin
    idx := 0;
    flag := true;

    while (idx < VerpackAufPosListView.Items.Count) do begin
      if not (VerpackAufPosListView.Items [idx].Checked) then begin
        flag := false;
        break;
      end else if Assigned (fMengeCol) then begin
        //Prüfen, ob alles in der Position verpackt wurde
        numstr := VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1];

        if (Length (numstr) = 0) or not TryStrToInt(numstr, menge) or (menge < TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge) then begin
          flag := false;
          break;
        end;
      end;

      Inc (idx);
    end;

    if flag then begin
      ModalResult := mrOk;
      //Close;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.11.2019
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.FormActivate(Sender: TObject);
var
  res : Integer;
begin
  FehlerPanel.Visible := False;

  if LVSDatenModul.FunctionExits ('PA_VERPACKEN', 'VERPACKEN_BEGIN') then begin
    res := VerpackenBegin (fRefPackplatz, fRefAuftrag);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 15.07.2014
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.FormClose(Sender: TObject; var Action: TCloseAction);
var
  idx  : Integer;
  line : String;
begin
  fWiegeThread.Terminate;

  fAutoClose := False;
  Application.OnIdle := nil;

  for idx := 0 to VerpackAufPosListView.Columns.Count - 1 do begin
    if (idx = 0) then
      line := IntToStr (VerpackAufPosListView.Columns [idx].Width)
    else
      line := line + ';' + IntToStr (VerpackAufPosListView.Columns [idx].Width);
  end;

  LVSConfigModul.SaveFormParameter (Self, 'VerpackAufPosListView', line);

  LVSConfigModul.SaveFormInfo (Self);

  {$ifdef TraceVerpacken}
    verpacklog.Write ('TAuftragPosVerpackenForm.FormClose');
    verpacklog.CheckLogRotation;
  {$endif}
end;

//******************************************************************************
//* Function Name: ShowErrorPanel
//* Author       : Stefan Graf
//* Datum        : 02.02.2021
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.ShowErrorPanel (const ErrorText : String);
var
  lockflag : Boolean;
begin
  {$ifdef TraceVerpacken}
    verpacklog.Write ('TAuftragPosVerpackenForm.Error:'+ErrorText);
  {$endif}

  lockflag := LockWindowUpdate (Application.MainForm.Handle);

  try
    FehlerButton.Visible := fErrorConfirmed;
    FehlerPanel.Caption := ErrorText;
    FehlerPanel.Visible := True;
  finally
    if lockflag then
      LockWindowUpdate(0);
  end;

  FehlerPanel.Tag := 1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 15.07.2014
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  idx,
  ref,
  lblres,
  anzpos,
  anznve,
  anzpack,
  anzsvr,
  nveref,
  refwa,
  dlgres,
  akpref,
  menge,
  countdo,
  refsendnr,
  summenge,
  rest,
  sumrest,
  sumcheck,
  sumuncheck      : Integer;
  l, b, h         : Integer;
  gw,
  floatwert       : Double;
  nvenr,
  spedart,
  dummystr,
  errmsg,
  logline,
  numstr,
  vpstat,
  lhmstat,
  snrstr,
  versstr,
  respapp,
  fname,
  prterrmsg       : String;
  query           : TADOQuery;
  dbok,
  dlgok,
  respflag,
  verflag,
  verpflag,
  opt_ls_print,
  opt_lhm_serial,
  opt_enter_snr,
  opt_snr_needed,
  opt_inhalt_liste : Boolean;
  opt_cn23,
  opt_proforma     : String;
  prostr,
  cfgstr,
  auflang          : String;
  kopie,
  cfgint           : Integer;
  prtinfo          : TPrinterPorts;
  lhmform          : TInputLHMSerialForm;
  infowin          : TInfoWin;
  msgform          : TMessageForm;
  verlauf          : TVerlaufForm;
  forminfo         : TFormInfos;
begin
  FehlerPanel.Tag := 0;
  //FehlerPanel.Visible := False;

  res    := 0;
  lblres := 0;
  gw     := -1;
  snrstr := '';
  verpflag := false;

  verlauf := Nil;

  if ((ModalResult <> mrOk) or AbortButton.Focused) then begin
    {$ifdef TraceVerpacken}
      verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: Abort');
    {$endif}

    CanClose := True
  end else begin
    {$ifdef TraceVerpacken}
      verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', fRefPackplatz='+IntToStr (fRefPackplatz));
    {$endif}

    //Die Gesamtmenge der verpackten Artikel ermitteln
    idx := 0;
    sumrest    := 0;
    summenge   := 0;
    sumcheck   := 0;
    sumuncheck := 0;

    verpflag := True;

    while (idx < VerpackAufPosListView.Items.Count ) do begin
      if VerpackAufPosListView.Items [idx].Checked then begin
        Inc (sumcheck);

        if not (Assigned (VerpackAufPosListView.Items [idx].Data) and (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).OptUmverpackt)) then
          verpflag := false;

        if Assigned (fMengeCol) then begin
          //Prüfen ob die Mengenangaben i.O. und > 0 sind
          if TryStrToInt (VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1], menge) and (menge > 0) then begin
            summenge := summenge + menge;

            if TryStrToInt (VerpackAufPosListView.Items [idx].SubItems [fSollMengeCol.Index - 1], rest) and (rest > 0) then
              sumrest := sumrest + (rest - menge);
          end else begin
            res := -8;

            ShowErrorPanel (GetResourceText (1116));

            VerpackAufPosListView.ItemIndex := idx;
            break;
          end;
        end else begin
          summenge := summenge + 1;
        end;
      end else begin
        sumrest := sumrest + 1;
        Inc (sumuncheck);
      end;

      Inc (idx);
    end;

    //Es muss mindestens ein Artikel zum Verpacken ausgewählt werden
    if (sumcheck = 0) or (summenge = 0) then begin
      res := -15;

      ShowErrorPanel (GetResourceText (1782));
    end;

    //Es wurde noch kein Versender ausgewählt
    if (res = 0) and (fRefSped <= 0) then begin
      res := -6;

      ShowErrorPanel (GetResourceText (1253));
    end;

    if (res = 0) then begin
      CheckShippingUnits;

      if (fNeedShippingUnits and (fShippingUnits <= 0)) then begin
        res := -34;

        ShowErrorPanel (GetResourceText (1824));
      end;
    end;

    //Wenn der Versender nur einzelne Packstücke annnimmt, darf auf der Palette auch nur ein Teil liegen (z. B. DHL2MH)
    if (res = 0) and (fSpedInfos.SingleColliPack) and (summenge > 1) then begin
      res := -9;

      ShowErrorPanel (GetResourceText (1372));
    end else if (fOnePackage and ((sumuncheck > 0) or (sumrest > 0))) then begin
      msgform := TMessageForm.Create (Self);

      try
        msgform.Caption := GetResourceText (1519);;
        msgform.SetIcon (mtConfirmation);
        msgform.SetButton (msgform.Button3, rsYes, mrYes);
        msgform.SetButton (msgform.Button1, rsNo, mrNo);
        msgform.SetButton (msgform.Button2, GetResourceText (1346), mrCancel);

        msgform.MessageLabel.Font.Size := 14;
        msgform.MessageLabel.Caption := FormatMessageText (1522, []);

        //Abfragen ob ein weitere Versandkarton gebildet werden soll
        if (msgform.ShowModal (msgform.Button2, 0) <> mrYes) then begin
          res := -99;
        end;
      finally
        msgform.Free;
      end;
    end;

    //Prüfen, ob ein Versandkarton ausgewählt werden muss
    if (res = 0) and (fSelectPackLT and (fRefLTType = -1)) and not (verpflag) then begin
      res := -7;

      ShowErrorPanel (GetResourceText (1366));
    end;

    //Prüfen, ob die Abmessungen fehlen
    if (res = 0) and (fSpedInfos.LTDimRequired and ((Length (LTLength.Text) = 0) or (Length (LTWidth.Text) = 0) or (Length (LTHeigth.Text) = 0))) then begin
      res := -7;

      ShowErrorPanel (GetResourceText (1364));

      if (Length (LTLength.Text) = 0) and LTLength.CanFocus then
        LTLength.SetFocus
      else if (Length (LTWidth.Text) = 0) and LTWidth.CanFocus then
        LTWidth.SetFocus
      else if (Length (LTHeigth.Text) = 0) and LTHeigth.CanFocus then
        LTHeigth.SetFocus;
    end;

    if (res = 0) then begin
      if (Length (BruttoGewichtEdit.Text) = 0) then begin
        gw := fSpedInfos.DefaultGewicht;

        if (gw <= 0) then begin
          res := -2;

          BruttoGewichtEdit.SetFocus;
          BruttoGewichtEdit.Color := clRed;

          ShowErrorPanel (GetResourceText (1255));
        end;
      end else if TryStrToFloat (BruttoGewichtEdit.Text, floatwert) then begin
        if (floatwert < 9999) then
          gw := floatwert * 1000
        else begin
          res := -2;

          BruttoGewichtEdit.SetFocus;
          BruttoGewichtEdit.Color := clRed;

          ShowErrorPanel (FormatMessageText (1389, []));
        end;
      end else begin
        res := -2;

        BruttoGewichtEdit.SetFocus;
        BruttoGewichtEdit.Color := clRed;

        ShowErrorPanel (GetResourceText (1254));
      end;
    end;

    if (res <> 0) then begin
      CanClose := False;

      Beep;
      PlaySound (PChar (LVSConfigModul.FrontendConfig.ScanErrSound), 0, SND_ASYNC);
    end else begin
      Screen.Cursor := crSQLWait;

      infowin := TInfoWin.Create (Self);

      try
        infowin.Label1.Caption := GetResourceText (1518);

        infowin.BeginShowModal;

        query := TADOQuery.Create (Self);

        try
          query.LockType := ltReadOnly;
          query.Connection := LVSDatenModul.MainADOConnection;

          if LVSDatenModul.ViewColumnExits ('V_SPED_CONFIG', 'OPT_PROFORMA') then
            prostr := 'nvl (aufcfg.OPT_PROFORMA, spedcfg.OPT_PROFORMA)'
          else
            prostr := 'aufcfg.OPT_PROFORMA';


          query.SQL.Add ('select'
                         +' nvl (plan.OPT_AUTO_PRINT_LS,''1'') as OPT_AUTO_PRINT_LS'
                         +',nvl (lt.OPT_LHM_SERIAL, ''0'') as OPT_LHM_SERIAL'
                         +',nvl (scfg.OPT_ENTER_SHIPMEND_NO, ''0'') as OPT_ENTER_SHIPMEND_NO'
                         +',nvl (plan.OPT_AUTO_PRINT_NVE_INHALT, ''0'') as OPT_AUTO_PRINT_NVE_INHALT'
                         +',plan.OPT_ABSCHLUSS_LHM_ERFASSEN'
                         +',mcfg.CONFIG_OPT'
                         +',scfg.OPT_SENDUNGS_NR'
                         +',scfg.OPT_CN23'
                         +','+prostr+' as OPT_PROFORMA'
                         +',auf.SPRACHE'
                         +' from'
                         +'  VQ_AUFTRAG auf'
                         +'  left outer join V_AUFTRAG_ART_CONFIG aufcfg on (aufcfg.REF=auf.REF_ART_CONFIG)'
                         +'  inner join V_MANDANT_CONFIG mcfg on (mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND))'
                         +'  left outer join V_LT_TYPEN lt on (lt.REF=nvl (:ref_lt, auf.REF_LT))'
                         +'  left outer join V_AUFTRAG_ART_PLANUNG plan on (plan.REF=auf.REF_ART_PLANUNG)'
                         +'  left outer join V_SPEDITIONEN sped on (sped.REF=:ref_sped)'
                         +'  left outer join V_SPED_CONFIG scfg on (scfg.REF_SPED=sped.REF)'
                         +' where auf.REF=:ref');
          query.Parameters.ParamByName('ref').Value := fRefAuftrag;
          query.Parameters.ParamByName('ref_sped').Value := fRefSped;

          if (fRefLTType > 0) then
            query.Parameters.ParamByName('ref_lt').Value := fRefLTType
          else begin
            query.Parameters.ParamByName('ref_lt').DataType := ftInteger;
            query.Parameters.ParamByName('ref_lt').Value := NULL;
          end;

          query.Open;

          opt_ls_print     := (query.Fields [0].AsString = '1');
          opt_lhm_serial   := (query.Fields [1].AsString > '0');
          opt_enter_snr    := (query.Fields [2].AsString > '0');
          opt_inhalt_liste := (query.Fields [3].AsString > '0');
          opt_snr_needed   := (query.FieldByName ('OPT_SENDUNGS_NR').AsString > '0');
          opt_cn23         := query.FieldByName ('OPT_CN23').AsString;
          opt_proforma     := query.FieldByName ('OPT_PROFORMA').AsString;

          vpstat  := '0';
          lhmstat := '0';

          //Nur wenn die LHM-Erfassung überhaupt vorgesehen ist
          if (query.FieldByName ('OPT_ABSCHLUSS_LHM_ERFASSEN').IsNull) then begin
            lhmstat := copy (query.FieldByName ('CONFIG_OPT').AsString, cMandLHMAutoErfassen, 1);
            vpstat  := copy (query.FieldByName ('CONFIG_OPT').AsString, cMandVerpackungErfassen, 1);
          end else begin
            lhmstat := copy (query.FieldByName ('OPT_ABSCHLUSS_LHM_ERFASSEN').AsString, 1, 1);
            vpstat  := copy (query.FieldByName ('OPT_ABSCHLUSS_LHM_ERFASSEN').AsString, 2, 1);
          end;

          auflang := query.FieldByName ('SPRACHE').AsString;

          query.Close;

          dbok := True;
          countdo := 0;

          //Prüfen, ob noch was zu verpacken ist
          idx := 0;

          while (idx < VerpackAufPosListView.Items.Count ) do begin
            if VerpackAufPosListView.Items [idx].Checked then begin
              if not (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).Done) then begin
                Inc (countdo);
                dbok := False;
              end;
            end;

            Inc (idx);
          end;

          if (dbok) then
            nveref := fRefNVE
          else begin
            //NVE anlegen
            if (fRefNVE <> -1) then
              nveref := fRefNVE
            else begin
              query.SQL.Clear;
              query.SQL.Add ('select REF from V_WARENAUSGANG where STATUS not in (''ABG'',''STO'') and REF_AUFTRAG=:ref');
              query.Parameters.ParamByName('ref').Value := fRefAuftrag;

              query.Open;

              if (query.Fields [0].IsNull) then
                refwa := -1
              else refwa := query.Fields [0].AsInteger;

              query.Close;

              res := CreateWANVE (fRefAuftrag, refwa, fRefLTType, nveref, nvenr);

              if (res <> 0) then
                errmsg := FormatMessageText (1003, [LVSDatenModul.LastLVSErrorText])
              else begin
                //Wenn die Serial schon bekannt ist, hier setzen
                if (Length (fNVESerial) > 0) then
                  res := SetNVELHMSerial (nveref, fNVESerial);

                if (res = 0) then
                  res := SetNVESpedition (nveref, fRefSped, fRefSpedProd);

                if (res <> 0) then
                  errmsg := FormatMessageText (1009, [LVSDatenModul.LastLVSErrorText])
                else begin
                  fRefNVE := nveref;
                  fNVENr  := nvenr;
                end;
              end;
            end;

            {$ifdef TraceVerpacken}
              verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', fRefNVE='+IntToStr (fRefNVE)+', fNVENr='+fNVENr);
            {$endif}

            dlgres := mrOk;

            if (res = 0) Then begin
              //Wenn die LHM Seriel benötigt wird, hier einlesen
              if (opt_lhm_serial and (Length (fNVESerial) = 0)) then begin
                {$ifdef TraceVerpacken}
                  verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', TInputLHMSerialForm: opt_lhm_serial='+BoolToStr (opt_lhm_serial, true)+', fNVESerial='+fNVESerial);
                {$endif}

                lhmform := TInputLHMSerialForm.Create (Self);

                try
                  lhmform.Prepare (fRefNVE, 1);

                  dlgres := lhmform.ShowModal;

                  if (dlgres = mrOk) then begin
                    if (Length (lhmform.LHMSerialEdit.Text) > 0) then
                      fNVESerial := lhmform.LHMSerialEdit.Text;
                  end;
                finally
                  lhmform.Release;
                end;
              end;

              if (Length (fNVESerial) > 0) then begin
                res := SetNVELHMSerial (fRefNVE, fNVESerial);

                if (res <> 0) then
                  errmsg := 'Fehler beim Setzen der LHM-Seriennummer'+#13+#13+LVSDatenModul.LastLVSErrorText;
              end;
            end;

            if (res = 0) and (dlgres = mrOk) then begin

              if (countdo > 3) then begin
                verlauf := TVerlaufForm.Create (Self);

                verlauf.Label1.Visible := True;

                verlauf.Caption :=        GetResourceText (1519);
                verlauf.Label1.Caption := GetResourceText (1520);

                verlauf.ProgressBar1.Max := countdo;

                verlauf.BeginShowModal;
              end;

              //Wenn ja alles in einer ganzen Transaktion verpacken
              while (res = 0) and not (dbok) do begin
                LVSDatenModul.BeginTransaction (LVSDatenModul.MainADOConnection);

                try
                  idx := 0;

                  //Die einzelnen Position verpacken
                  while (idx < VerpackAufPosListView.Items.Count ) and (res = 0) do begin
                    if VerpackAufPosListView.Items [idx].Checked then begin
                      ref := TVerpackEntry (VerpackAufPosListView.Items [idx].Data).Ref;

                      if not Assigned (fMengeCol) then
                        menge := 1
                      else begin
                        numstr := VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1];

                        if (Length (numstr) = 0) or not TryStrToInt(numstr, menge) then
                          menge := -1;
                      end;

                      if (menge > 0) then begin
                        {$ifdef TraceVerpacken}
                          verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', fRefNVE='+IntToStr (fRefNVE)+', ref='+IntToStr (ref)+', RefArEinheit='+IntToStr (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit)+', menge='+IntToStr (menge));
                        {$endif}

                        if (Length (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).Serial) = 0) then
                          res := EinheitVerpacken (fRefPackplatz, nveref, ref, menge, akpref)
                        else begin
                          res := EinheitVerpacken (fRefPackplatz, nveref, ref, menge, akpref);

                          if (res = 0) then
                            res := InsertKommPosInfo (-1, akpref, 'SERIAL', TVerpackEntry (VerpackAufPosListView.Items [idx].Data).Serial);
                        end;

                        {$ifdef TraceVerpacken}
                          verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', fRefNVE='+IntToStr (fRefNVE)+', res='+IntToStr (res)+', akpref='+IntToStr (akpref));
                        {$endif}
                      end;
                    end;

                    Inc (idx);

                    if Assigned (verlauf) then begin
                      verlauf.ProgressBar1.StepIt;
                      verlauf.UpdateModal;

                      if verlauf.AbortFlag then
                        res := 99
                    end;
                  end;

                  //Das Gewicht noch setzen
                  if (res = 0) then
                    if not (AbmessungPanel.Visible) then
                      res := SetNVEVPEGewicht (nveref, -1, -1,  Round (gw))
                    else begin
                      l := -1; b:= -1; h:= -1;

                      if (Length (LTLength.Text) > 0) and TryStrToInt (LTLength.Text, l) then
                        l := l * 10;

                      if (Length (LTWidth.Text) > 0) and TryStrToInt (LTWidth.Text, b) then
                        b := b * 10;

                      if (Length (LTHeigth.Text) > 0) and TryStrToInt (LTHeigth.Text, h) then
                        h := h * 10;

                      res := SetNVEVPEGewicht (nveref, -1, -1,  Round (gw), l, b ,h)
                    end;

                  if (res <> 0) then begin
                    LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);

                    {$ifdef TraceVerpacken}
                      verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: Rollback');
                    {$endif}

                    if (res = 99) then
                      errmsg := FormatMessageText (1395, [])
                    else
                      errmsg := FormatMessageText (1004, [LVSDatenModul.LastLVSErrorText]);

                    //Verhindern, dass die selbe NVE-Nummer nochmals genutzt wird
                    lblres := GetConfigSequenzNummer (fRefMand, -1, fRefLager, 'NVE_NUMMER', dummystr);

                    fRefNVE := -1;
                    fNVENr  := '';
                  end else begin
                    LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trCommit);

                    {$ifdef TraceVerpacken}
                      verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: Commit');
                    {$endif}

                    //Alle markierten Positionen sind jetzt verpacket
                    idx := 0;

                    while (idx < VerpackAufPosListView.Items.Count ) do begin
                      if VerpackAufPosListView.Items [idx].Checked then begin
                        if not Assigned (fMengeCol) then
                          TVerpackEntry (VerpackAufPosListView.Items [idx].Data).Done := True
                        else begin
                          numstr := VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1];

                          if (Length (numstr) = 0) or not TryStrToInt(numstr, menge) then begin
                            VerpackAufPosListView.Items [idx].Checked := False;
                          end else if (menge < TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge) then begin
                            TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge := TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge - menge;

                            VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1] := '';
                            VerpackAufPosListView.Items [idx].SubItems [fSollMengeCol.Index - 1] := IntToStr (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge);

                            VerpackAufPosListView.Items [idx].Checked := False;
                          end else begin
                            TVerpackEntry (VerpackAufPosListView.Items [idx].Data).Done := True;
                          end;
                        end;
                      end;

                      Inc (idx);
                    end;
                  end;

                  dbok := True;
                except
                  on E: EOracleRetryException do begin
                    ErrorTrackingModule.WriteErrorLog ('EOracleRetryException AuftragPosVerpackenForm.FormCloseQuery', e.ClassName + ' : ' + e.Message);
                  end;

                  on  E: Exception do begin
                    res := -9;

                    errmsg := FormatMessageText (1122, []);

                    LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);

                    ErrorTrackingModule.WriteErrorLog ('Exception AuftragPosVerpackenForm.FormCloseQuery', e.ClassName + ' : ' + e.Message);
                  end;
                end;
              end;

              if Assigned (verlauf) then begin
                verlauf.EndShowModal;
                verlauf.Release;

                verlauf := Nil;
              end;
            end;
          end;

          if (res = 0) and (dlgres = mrOk) then begin
            anzpos  := 0;
            anzpack := 0;

            //Prüfen ob es noch unverpackte Positionen gibt
            query.SQL.Clear;
            query.SQL.Add ('select count (REF) from VQ_AUFTRAG_KOMM_POS where nvl (MENGE_VERPACKT, 0) < nvl (MENGE_PICK, 0) and REF_AUF_KOPF=:ref');
            query.Parameters.ParamByName('ref').Value := fRefAuftrag;

            query.Open;

            anzpos  := query.Fields [0].AsInteger;
            anzpack := query.Fields [0].AsInteger;

            query.Close;

            if (anzpos = 0) then begin
              //Prüfen, ob es noch offene Auftragspositionen gibt
              query.SQL.Clear;
              query.SQL.Add ('select count (ap.REF) from VQ_AUFTRAG_POS ap, VQ_ARTIKEL ar where ar.REF=ap.REF_AR and nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (ar.OPT_AUTO_WA_BUCHUNG, ''0'')=''0'' and nvl (ap.MENGE_SOLL, 0) > nvl (ap.MENGE_GESAMT, 0) and ap.REF_AUF_KOPF=:ref');
              query.Parameters.ParamByName('ref').Value := fRefAuftrag;

              query.Open;

              anzpos := query.Fields [0].AsInteger;

              query.Close;
            end;

            {$ifdef TraceVerpacken}
              verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', anzpos='+IntToStr (anzpos)+', anzpack='+IntToStr (anzpack));
            {$endif}

            if (fOptCOD and (res = 0) and (anzpos = 0)) then begin
              //Bei Nachnahme-Sendungen muss der Rechnungsbetrag vor dem Drucke des Etikettes neue bestimmt werden
              res := CalcAuftragRechnung (fRefAuftrag);
            end;

            if (res = 0) then begin
              if (fRefNVE = -1) then begin
                errmsg := FormatMessageText (1063, []);
              end else begin
                if Assigned (PrintModule.PrintLog) then begin
                  logline := 'TAuftragPosVerpackenForm.FormCloseQuery;'+AuftragNrLabel.Caption+';'+PrintModule.NVELabelPrinter.Port+';'+PrintModule.NVELabelPrinter.Name+';'+IntToStr (PrintModule.NVELabelPrinter.Ref);
                  PrintLogging (logline);
                end;

                //Drucken der Lables und Versandpapiere
                if (PrintModule.NVELabelPrinter.Ref > 0) then begin
                  lblres := PrintModule.LoadPrinter (-1, PrintModule.NVELabelPrinter.Ref, '', prtinfo)
                end else if (Length (PrintModule.NVELabelPrinter.Port) = 0) then begin
                  if (Length (PrintModule.NVELabelPrinter.Name) > 0) then
                    lblres := PrintModule.LoadPrinter (-1, PrintModule.NVELabelPrinter.Name, prtinfo)
                  else begin
                    prtinfo.Ref    := -1;
                    prtinfo.PrtTyp := '';
                    prtinfo.Name   := '';
                    prtinfo.Port   := '';
                    prtinfo.IsOnline := True;
                  end;
                end else begin
                  prtinfo.Ref    := -1;
                  prtinfo.PrtTyp := 'LASER';
                  prtinfo.Name   := PrintModule.NVELabelPrinter.Name;
                  prtinfo.Port   := PrintModule.NVELabelPrinter.Port;
                  prtinfo.IsOnline := True;
                end;

                if (lblres <> 0) then
                  errmsg := FormatMessageText (1120, [PrintModule.NVELabelPrinter.Name])
                else if not (prtinfo.IsOnline) then begin
                  lblres := -12;
                  errmsg := FormatMessageText (1119, [PrintModule.NVELabelPrinter.Name])
                end else begin
                  infowin.Label1.Caption := FormatMessageText (1390, []);

                  lblres := StartCreateVersandInfos (Self, nveref, prtinfo, respapp, respflag, prterrmsg);

                  if (lblres <> 0) then begin
                    {$ifdef TraceVerpacken}
                      verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', VersandLabelError: lblres='+IntToStr (lblres)+' prterrmsg='+prterrmsg);
                    {$endif}
                  end else begin
                    (*Geht so nicht mehr, da die Versandinfos der NVE noch nicht bekannt sind
                    if (not (fOptCOD) and (anzpos = 0) and not (isShiftDown)) then begin
                      //Erst wenn alles kommissioniert und verpackt wurde, kann der Lieferschein geschrieben werden
                      lblres := VersandPapiereNachdruck (fRefAuftrag, false, true, opt_ls_print, errmsg);
                    end;
                    *)

                    if not (respflag) then begin
                      lblres := 0;

                      query.SQL.Clear;
                      query.SQL.Add ('select SENDUNGS_NR from V_NVE_01 where REF=:ref');
                      query.Parameters.ParamByName('ref').Value := nveref;

                      query.Open;

                      snrstr := query.Fields[0].AsString;

                      query.Close;

                      {$ifdef TraceVerpacken}
                        verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', VersandLabel: snrstr='+snrstr);
                      {$endif}
                    end else begin
                      //Das Ergebniss der Labelerzeugung abwarten
                      lblres := FinishCreateVersandInfos (Self, nveref, respapp, fSpedInfos.SpedKennung, snrstr, prterrmsg);

                      {$ifdef TraceVerpacken}
                        verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', VersandLabel: snrstr='+snrstr+', lblres='+IntToStr (lblres)+', prterrmsg='+prterrmsg);
                      {$endif}
                    end;
                  end;

                  if (lblres <> 0) then begin
                    CheckConfigParameter (fRefMand, -1, fRefLager, 'SENDIT_FEHLER_LABEL', cfgint, 0);

                    if (cfgint = 0) then begin
                      if (Length (errmsg) > 0) then errmsg := errmsg + #13 + #13;
                      errmsg := errmsg + FormatMessageText (1005, [IntToStr (lblres), prterrmsg]);
                    end else begin
                      PrintVersandFehlerLabel (Self, nveref, prtinfo, prterrmsg, dummystr);

                      if (Length (dummystr) > 0) then begin
                        if (Length (errmsg) > 0) then errmsg := errmsg + #13 + #13;
                        errmsg := errmsg + FormatMessageText (1005, [IntToStr (lblres), prterrmsg]);
                      end;
                    end;
                  end;
                end;

                if Assigned (PrintModule.PrintLog) and (lblres <> 0) then begin
                  logline := ';lblres;'+IntToStr (lblres);
                  PrintLogging (logline);
                end;

                if (res = 0) and (LVSConfigModul.UseGefahrgut) then begin
                  query.SQL.Clear;
                  query.SQL.Add ('select distinct (NVE_FORMULAR) from V_NVE_GEFAHRSTOFFE where NVE_FORMULAR is not null and REF_NVE=:ref_nve');
                  query.Parameters.ParamByName('ref_nve').Value := nveref;

                  try
                    query.Open;

                    while not (query.Eof) and (res = 0) do begin
                      res := DetectFormular (fRefMand, fRefLager, -1, '', '', prtinfo.Model, query.Fields [0].AsString, forminfo);

                      if (res <> 0) Then begin
                        prterrmsg := FormatMessageText (1083, [query.Fields [0].AsString, prtinfo.Model])
                      end else begin
                        res := OpenPrinterPort (prtinfo.Port, prtinfo.Model, prtinfo.FileOutput, prtinfo.User, prtinfo.Passwd, fname, prterrmsg);

                        if (res = 0) then begin
                          res := BeginPrinting (query.Fields [0].AsString, prterrmsg);

                          if (res = 0) then begin
                            res := PrintFile (LabelTemplatePath+forminfo.FormularName, prterrmsg);

                            EndPrinting;
                          end;

                          ClosePrinterPort;
                        end;
                      end;

                      query.Next;
                    end;

                    query.Close;
                  except
                  end;
                end;

                dlgres := mrOk;

                //Wenn die Sendungsnummer eingegeben werden muss oder benötigt und noch nichts vorhanden ist
                if (lblres = 0) and (opt_enter_snr or (opt_snr_needed and (Length (snrstr) = 0))) then begin
                  {$ifdef TraceVerpacken}
                    verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', TInputLHMSerialForm: opt_enter_snr='+BoolToStr (opt_enter_snr, true)+', opt_snr_needed='+BoolToStr (opt_snr_needed, true)+', snrstr='+snrstr);
                  {$endif}

                  lhmform := TInputLHMSerialForm.Create (Self);

                  try
                    lhmform.Prepare (nveref, 2);

                    dlgres := lhmform.ShowModal;

                    if (dlgres = mrOk) then begin
                      if (Length (lhmform.LHMSerialEdit.Text) > 0) then begin
                        snrstr := lhmform.LHMSerialEdit.Text;

                        if (lhmform.VersenderPanel.Visible) then
                          versstr := lhmform.VersenderEdit.Text
                        else
                          versstr := fSpedInfos.SpedKennung;

                        res := SetNVESendungsNrURL (fRefNVE, fSpedInfos.SpedKennung, versstr, snrstr, '', lhmform.TrackingNrEdit.Text, refsendnr);

                        if (res <> 0) then
                          errmsg := FormatMessageText (1630, [LVSDatenModul.LastLVSErrorText]);
                      end;
                    end;
                  finally
                    lhmform.Release;
                  end;
                end;

                //Nur wenn das Drucken geklappt hat
                if (res = 0) and (lblres = 0) then begin
                  if (opt_inhalt_liste) then begin
                    infowin.Label1.Caption := FormatMessageText (1391, []);
                    Application.ProcessMessages;

                    //Automatisch die Inhaltsliste der NVE drucken
                    if (fRefSubMand > 0) then
                      lblres := PrintModule.PrintReport('', fRefSubMand, fRefLager, auflang, 'NVE-INHALT', '', ['REF:' + IntToStr (nveref)], errmsg, False, 0)
                    else
                      lblres := PrintModule.PrintReport('', fRefMand, fRefLager, auflang, 'NVE-INHALT', '', ['REF:' + IntToStr (nveref)], errmsg, False, 0);
                  end;

                  if not (IsLandEU (fISOLand, fLiefPLZ)) then begin
                    if ((lblres = 0) and (opt_cn23 > '0')) then begin
                      infowin.Label1.Caption := FormatMessageText (1854, []);
                      Application.ProcessMessages;

                      if (Length (opt_cn23) > 0) and TryStrToInt (opt_cn23, kopie) then
                        kopie := kopie - 1;

                      //Automatisch das CN23 für die NVE drucken
                      if (fRefSubMand > 0) then
                        lblres := PrintModule.PrintReport('', fRefSubMand, fRefLager, auflang, 'ZOLL_CN23', '', ['REF:' + IntToStr (nveref)], errmsg, False, kopie)
                      else
                        lblres := PrintModule.PrintReport('', fRefMand, fRefLager, auflang, 'ZOLL_CN23', '', ['REF:' + IntToStr (nveref)], errmsg, False, kopie);
                    end;

                    if ((lblres = 0) and (opt_proforma > '0')) then begin
                      infowin.Label1.Caption := FormatMessageText (1855, []);
                      Application.ProcessMessages;

                      if (Length (opt_proforma) > 0) and TryStrToInt (opt_proforma, kopie) then
                        kopie := kopie - 1;

                      //Automatisch die Proformarechnung für die NVE drucken
                      if (fRefSubMand > 0) then
                        lblres := PrintModule.PrintReport('', fRefSubMand, fRefLager, auflang, 'PROFORMA_RECHNUNG', '', ['REF:' + IntToStr (nveref)], errmsg, False, kopie)
                      else
                        lblres := PrintModule.PrintReport('', fRefMand, fRefLager, auflang, 'PROFORMA_RECHNUNG', '', ['REF:' + IntToStr (nveref)], errmsg, False, kopie);
                    end;
                  end;

                  //Leerguterfassung nur dann aufrufen, wenn keine Verpackung erfasst wurde
                  if (((lhmstat > '0') and ((vpstat = '1') and not fSelectPackLT)) or (((vpstat = '1') and not fSelectPackLT))) then begin
                    res := DoAuftragLHM (Self, fRefAuftrag, fRefAuftrag, True, dlgok);

                    if not (dlgok) then
                      dlgres := mrAbort;
                  end;

                  {$ifdef TraceVerpacken}
                    verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', vor VersandPapiere: anzpos='+IntToStr (anzpos));
                  {$endif}

                  //Muss jetzt immer erst nach dem Etikett gedruckt werden
                  if (anzpos = 0) then begin
                    infowin.Label1.Caption := FormatMessageText (1392, []);
                    Application.ProcessMessages;

                    lblres := VersandPapiereNachdruck (fRefAuftrag, false, false, true, true, true, errmsg);

                    {$ifdef TraceVerpacken}
                      verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', VersandPapiere: lblres='+IntToStr (lblres)+', errmsg='+errmsg);
                    {$endif}
                  end;

                  if (LVSConfigModul.UseValueServices) then begin
                    //Begleitzettel für Value Added Servcies drucken
                    anzsvr := 0;

                    query.SQL.Clear;
                    query.SQL.Add ('select count (svr.REF) from V_AUFTRAG_VALUE_SERVICE svr where svr.REF_AUF_KOPF=:ref');
                    query.Parameters.ParamByName('ref').Value := fRefAuftrag;

                    try
                      query.Open;

                      anzsvr := query.Fields [0].AsInteger;

                      query.Close;
                    except
                    end;

                    if (anzsvr > 0) then begin
                      infowin.Label1.Caption := FormatMessageText (1774, []);
                      Application.ProcessMessages;

                      //Automatisch die Liste mit den Values added services drucken
                      if (fRefSubMand > 0) then
                        lblres := PrintModule.PrintReport('', fRefSubMand, fRefLager, '', 'VALUE_SERVICES_LIST', '', ['REF:' + IntToStr (fRefAuftrag)], errmsg, False, 0)
                      else
                        lblres := PrintModule.PrintReport('', fRefMand, fRefLager, '', 'VALUE_SERVICES_LIST', '', ['REF:' + IntToStr (fRefAuftrag)], errmsg, False, 0);

                      {$ifdef TraceVerpacken}
                        verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', Value Added Services: lblres='+IntToStr (lblres)+', errmsg='+errmsg);
                      {$endif}
                    end;
                  end;

                  if (lblres = 0) and (not opt_snr_needed or (Length (snrstr) > 0)) then begin
                    {$ifdef TraceVerpacken}
                      verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', NVEVerpackt');
                    {$endif}

                    //Nur wenn das Etikett erzeugt wurde, darf die NVE verpackt und abgeschlossen werden
                    res := NVEVerpackt (nveref);

                    if (res <> 0) then begin
                      if (Length (errmsg) > 0) then errmsg := errmsg + #13 + #13;
                      errmsg := errmsg + FormatMessageText (1008, [LVSDatenModul.LastLVSErrorText])
                    end else begin
                      {$ifdef TraceVerpacken}
                        verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', NVEBereitstellen');
                      {$endif}

                      //NVE im Versand bereitstellen
                      res := NVEBereitstellen (nveref);

                      if (res <> 0) then begin
                        if (Length (errmsg) > 0) then errmsg := errmsg + #13 + #13;
                        errmsg := errmsg + FormatMessageText (1010, [LVSDatenModul.LastLVSErrorText]);
                      end;
                    end;
                  end;
                end;

                //Auch wenn das Drucken nicht geklappt hat, kann der Auftrag gefinisched werden
                if (res = 0) and (anzpos = 0) then begin
                  if (dlgres = mrOk) then begin
                    //Prüfen ob es noch unverpackte NVE gibt, auf den noch was liegt
                    query.SQL.Clear;
                    query.SQL.Add ('select count (nve.REF) from V_NVE_01 nve where nve.REF<>:ref_nve and nve.REF_AUF_KOPF=:ref_auf and nve.STATUS not in (''DEL'', ''ABG'') and ((select count (*) from V_LAGER_NVE_BESTAND where nvl (MENGE, 0) > 0 and REF_NVE=nve.REF) > 0)');
                    query.Parameters.ParamByName('ref_auf').Value := fRefAuftrag;
                    query.Parameters.ParamByName('ref_nve').Value := fRefNVE;

                    query.Open;

                    anznve := query.Fields [0].AsInteger;

                    query.Close;

                    {$ifdef TraceVerpacken}
                      verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', anznve='+IntToStr (anznve)+', snrstr='+snrstr);
                    {$endif}

                    if Assigned (PrintModule.PrintLog) then begin
                      logline := ';lblres='+IntToStr (lblres)+';anznve='+IntToStr (anznve)+';snrstr='+snrstr;
                      PrintLogging (logline);
                    end;

                    if (lblres <> 0) or (anznve > 0) or (UpperCase (fVersandArt) = 'EMMASBOX') or (opt_snr_needed and (Length (snrstr) = 0)) then begin
                      //Wenn ja steht der Auftrag nur im Warenausgang
                      res := AuftragWarenausgang (fRefAuftrag);

                      if (res <> 0) then begin
                        if (Length (errmsg) > 0) then errmsg := errmsg + #13 + #13;
                        errmsg := errmsg + FormatMessageText (1007, [LVSDatenModul.LastLVSErrorText]);
                      end;
                    end else begin
                      infowin.Label1.Caption := FormatMessageText (1393, []);
                      Application.ProcessMessages;

                      //Der gesamte Auftrag ist jetzt verpackt
                      res := AuftragVerpackt (fRefAuftrag);

                      if (res <> 0) then begin
                        if (Length (errmsg) > 0) then errmsg := errmsg + #13 + #13;
                        errmsg := errmsg + FormatMessageText (1007, [LVSDatenModul.LastLVSErrorText]);
                      end;
                    end;
                  end else begin
                    //res := AuftragVerteilt (fRefAuftrag);
                  end;
                end;

                if (res = 0) then begin
                  fRefNVE := -1;
                  fNVENr  := '';

                  //Alle Verpackten Positionen aus der Liste löschen
                  idx := 0;

                  while (idx < VerpackAufPosListView.Items.Count) do begin
                    if not (VerpackAufPosListView.Items [idx].Checked) then
                      Inc (idx)
                    else begin
                      VerpackAufPosListView.Items.Delete (idx)
                    end;
                  end;
                end;
              end;
            end;
          end;
        finally
          query.Free;
        end;

        if Assigned (infowin) then begin
          infowin.EndShowModal;
          infowin.Close;
        end;
      finally
        if Assigned (infowin) then
          infowin.Free;

        Screen.Cursor := crDefault;
      end;

      if (res = 0) and (lblres = 0) then begin
        if fAusland then begin
          if IsLandEU (fISOLand, fLiefPLZ) then
            GetConfigDaten (fRefMand, LVSDatenModul.AktLocationRef, fRefLager, 'VERSANDHINWEIS_EU_EXPORT', cfgstr, cfgint)
          else
            GetConfigDaten (fRefMand, LVSDatenModul.AktLocationRef, fRefLager, 'VERSANDHINWEIS_EXPORT', cfgstr, cfgint);

          if (Length (cfgstr) > 0) then
            FrontendMessages.MessageDLG (cfgstr, mtInformation, [mbOk], 0);
        end;

        if not fAutoFinsih and (anzpack = 0) then begin
          CanClose := false;

          VerpackenButton.Enabled := False;
          ArtikelImage.Visible := false;

          AbortButton.Caption := GetResourceText(1326);
          AbortButton.ModalResult := mrOk;
        end else begin
          CanClose := not (fOnlyCloseComplete) or (anzpack = 0);
        end;
      end else if (Length (errmsg) > 0) then begin
        CanClose := False;

        {$ifdef TraceVerpacken}
          verpacklog.Write ('TAuftragPosVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', errmsg='+errmsg);
        {$endif}

        ErrorTrackingModule.WriteErrorLogNoDB('TAuftragPosVerpackenForm.FormCloseQuery', errmsg);

        FrontendMessages.MessageDLG (errmsg, mtError, [mbOk], 0);
      end;

      if not CanClose then begin
        ReloadForm (Sender);

        if (fSpedInfos.RefDefaultLT > 0) then begin
          fRefLTType := fSpedInfos.RefDefaultLT;
          fLTTara    := fSpedInfos.DefaultLTTara;

          ShowSpedInfoLT (fSpedInfos);
        end else if (fRefAufLTType > 0) then begin
          fRefLTType := fRefAufLTType;
          fLTTara    := fRefAufLTTara;

          VersandKartonLabel.Caption := fAufLTName;
        end else begin
          fRefLTType := -1;
          fLTTara    := -1;
          VersandKartonLabel.Caption := '';
        end;

        fRefNVE     := -1;
        fRefBoxType := -1;

        fNVENr      := '';
        fNVESerial  := '';
        fMengeStr   := '';

        NVELabel.Caption           := '';
        VPEAnzLabel.Caption        := '';
        VerpackMengeLabel.Caption  := '';

        LTLength.Text := '';
        LTWidth.Text := '';
        LTHeigth.Text := '';
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 15.07.2014
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.FormCreate(Sender: TObject);
begin
  fRefNVE        := -1;
  fRefAuftrag    := -1;
  fRefAufKommPos := -1;
  fRefSped       := -1;
  fRefSpedProd   := -1;
  fLiefPLZ       := '';
  fISOLand       := '';
  fManRefSped    := -1;
  fRefLTType     := -1;
  fLTTara        := -1;
  fRefAufLTType  := -1;
  fRefAufLTTara  := -1;
  fAufLTName     := '';
  fFachNr        := -1;
  fRefContainer  := -1;

  fAutoFinsih        := True;
  fErrorConfirmed    := False;
  fAutoClose         := False;
  fCanChangeNVE      := False;
  fOnlyCloseComplete := False;
  fAddVPEVerpacken   := False;

  fPackHintHeight  := PresentLabel.Height;
  fInfoPanelHeight := InfoPanel.Height;

  SpedLabel.Caption := '';
  SpedPanel.Caption := '';

  SummeVPELabel.Caption := '';

  FehlerPanel.Tag := 0;
  FehlerPanel.Caption := '';

  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  fLastPicturePath := '';
  ArtikelImage.Visible := False;

  ContainerLabel.Caption := '';
  PresentLabel.Caption := '';
  PackHinweisLabel.Caption := '';
  PackHintLabel.Caption := '';
  VerpackMengeLabel.Caption := '';
  VersandKartonLabel.Caption := '';

  AbmessungPanel.BevelOuter := bvNone;

  {$ifdef TraceVerpacken}
    verpacklog := TLogFile.Create;

    verpacklog.LogSize     := 1000000;
    verpacklog.LogCount    := 4;
    verpacklog.LogRotation := true;

    verpacklog.LogFileName := LVSConfigModul.GetSessionLogDir + 'verpacklog.log';
    verpacklog.Open;
  {$endif}

  WaagePanel.Visible := false;
  WaageLabel.Caption := '';
  WaageGewichtLabel.Caption := '';

  fOldWaageGewicht := 0;
  fWiegeThread := TWiegeThread.Create (true);

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AuftragNrLabel);
    LVSSprachModul.SetNoTranslate (Self, WarenempfLabel);
    LVSSprachModul.SetNoTranslate (Self, VerpackungLabel);
    LVSSprachModul.SetNoTranslate (Self, VersandKartonLabel);
    LVSSprachModul.SetNoTranslate (Self, PresentLabel);
    LVSSprachModul.SetNoTranslate (Self, PackHinweisLabel);
    LVSSprachModul.SetNoTranslate (Self, PackHintLabel);
    LVSSprachModul.SetNoTranslate (Self, VerpackMengeLabel);
    LVSSprachModul.SetNoTranslate (Self, VPEAnzLabel);
    LVSSprachModul.SetNoTranslate (Self, SpedLabel);
    LVSSprachModul.SetNoTranslate (Self, NVELabel);
    LVSSprachModul.SetNoTranslate (Self, FehlerPanel);
    LVSSprachModul.SetNoTranslate (Self, UsedBoxLabel);
  {$endif}

  if Assigned (LVSSecurityModule) and Assigned (LVSSecurityModule.ACOModul) then begin
    LVSSecurityModule.ACOModul.SetBerechtigungen (Self);
  end;
end;

procedure TAuftragPosVerpackenForm.FormDestroy(Sender: TObject);
begin
  {$ifdef TraceVerpacken}
    verpacklog.Close;

    verpacklog.Free;
  {$endif}

  try
    fWiegeThread.Free;
  except
  end;
end;

//******************************************************************************
//* Function Name: FormKeyPress
//* Author       : Stefan Graf
//* Datum        : 04.10.2018
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListViewKeyPress(Sender: TObject; var Key: Char);
var
  ok     : Boolean;
  i,
  anz,
  menge,
  idx1,
  idx2   : Integer;
  gw     : Double;
  numstr : String;
begin
  idx1 := -1;
  idx2 := -1;

  if Assigned (VerpackAufPosListView.Selected) then
    idx1 := VerpackAufPosListView.Selected.Index;
  if Assigned (VerpackAufPosListView.ItemFocused) then
    idx2 := VerpackAufPosListView.ItemFocused.Index;

  if Assigned (VerpackAufPosListView.Selected) then begin
    ok     := False;
    numstr := fMengeStr;

    if ((Key = #8) or (Key = #127)) and (Length (fMengeStr) > 0) then begin
      numstr := Copy (numstr, 1, Length (numstr) - 1);
      Key := #0;
      ok := True;
    end else if (Key = ' ') then begin
      if (fAddVPEVerpacken) then begin
        ok := True;
        numstr := '';

        if not (VerpackAufPosListView.Selected.Checked) then begin
          if not (TVerpackEntry (VerpackAufPosListView.Selected.Data).SerialDefine) then begin
            TVerpackEntry (VerpackAufPosListView.Selected.Data).Serial := '';

            if Assigned (fSerialCol) then
              VerpackAufPosListView.Selected.SubItems [fSerialCol.Index - 1] := '';
          end;
        end else if Assigned (fMengeCol) then begin
          numstr := IntToStr (TVerpackEntry (VerpackAufPosListView.Selected.Data).GesamtMenge);

          VerpackAufPosListView.Selected.SubItems [fMengeCol.Index - 1] := numstr;

          VerpackAufPosListCheckedChange (Sender, VerpackAufPosListView.Selected);
        end;
      end;
    end else if ((Key >= '0') and (Key <= '9')) then begin
      if Assigned (fMengeCol) then begin
        numstr := numstr + Key;
        Key := #0;

        if not (TryStrToInt (numstr, menge)) then
          Beep
        else if ((VerpackAufPosListView.ItemIndex >= 0) and (menge > TVerpackEntry (VerpackAufPosListView.Items [VerpackAufPosListView.ItemIndex].Data).GesamtMenge)) then
          Beep
        else
          ok := True;
      end;
    end;

    if ok then begin
      if Assigned (fMengeCol) then begin
        if (not VerpackAufPosListView.Selected.Checked and (Length (numstr) > 0)) then begin
          VerpackAufPosListView.Selected.Checked := True;

          VerpackAufPosListView.Selected.SubItems [fMengeCol.Index - 1] := numstr;

          VerpackAufPosListCheckedChange (Sender, VerpackAufPosListView.Selected);
        end else if (VerpackAufPosListView.Selected.Checked and (Length (numstr) = 0)) then begin
          VerpackAufPosListView.Selected.Checked := False;

          VerpackAufPosListView.Selected.SubItems [fMengeCol.Index - 1] := '';

          VerpackAufPosListCheckedChange (Sender, VerpackAufPosListView.Selected);
        end else begin
          anz := 0;
          gw  := 0;

          VerpackAufPosListView.Selected.SubItems [fMengeCol.Index - 1] := numstr;

          if (fMengeStr <> numstr) then begin
            for i := 0 to VerpackAufPosListView.Items.Count - 1 do begin
              if (VerpackAufPosListView.Items [i].Checked) then begin
                if not Assigned (fMengeCol) then
                  menge := 1
                else if not TryStrToInt (VerpackAufPosListView.Items [i].SubItems [fMengeCol.Index - 1], menge) then
                  menge := 0;

                if (TVerpackEntry (VerpackAufPosListView.Items [i].Data).BruttoGewicht > 0) then
                  gw := gw + (TVerpackEntry (VerpackAufPosListView.Items [i].Data).BruttoGewicht * menge);

                anz := anz + menge;
              end;
            end;

            if (anz = 0) then begin
              VPEAnzLabel.Caption    := '';
              BruttoGewichtEdit.Text := '';
            end else begin
              VPEAnzLabel.Caption    := IntToStr (anz);

              if (fLTTara > 0) then
                gw := gw + fLTTara;

              if (gw <= 0) then
                BruttoGewichtEdit.Text := ''
              else
                BruttoGewichtEdit.Text := Format ('%.3f', [gw / 1000]);
            end;
          end;
        end;

        fMengeStr := numstr;

        VerpackMengeLabel.Caption := fMengeStr;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 20.06.2018
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.ReloadForm (Sender: TObject);
var
  nvestr   : String;
  query    : TADOQuery;
begin
  if WaBoxePanel.Visible then begin
    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Clear;
      query.SQL.Add ('select * from V_NVE_01 where STATUS<>''DEL'' and REF_AUF_KOPF=:ref and REF_KOMM_LE is null');
      query.Parameters.ParamByName('ref').Value := fRefAuftrag;

      query.Open;

      nvestr := '';

      while not (query.Eof) do begin
        if (Length (nvestr) > 0) then nvestr := nvestr + ', ';

        if not (Assigned (query.FindField ('LHM_SERIAL'))) or query.FieldByName ('LHM_SERIAL').IsNull then
          nvestr := nvestr + 'N'+copy (query.FindField ('NVE_NR').AsString, Length (query.FindField ('NVE_NR').AsString) - 4)
        else
          nvestr := nvestr + query.FieldByName ('LHM_SERIAL').AsString;

        query.Next;
      end;
      query.Close;

      UsedBoxLabel.Caption := nvestr;
    finally
      query.Free;
    end;
  end;

  VerpackenButton.Enabled := (VerpackAufPosListView.Items.Count > 0);

  VerpackAufPosListView.SetFocus;

  if (VerpackAufPosListView.Items.Count > 0) then begin
    VerpackAufPosListView.Selected    := VerpackAufPosListView.Items [0];
    VerpackAufPosListView.ItemFocused := VerpackAufPosListView.Selected;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 24.03.2023
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.ReprintAdvertisingButtonClick (Sender: TObject);
var
  res : Integer;
begin
  res := ReprintAdvertising (fRefAuftrag);

  if (res <> 0) then
    FrontendMessages.MessageDLG('Fehler beim Nachdrucken der Werbung' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 15.07.2014
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.FormShow(Sender: TObject);
var
  i,
  t,
  res,
  anz,
  idx,
  iwert,
  selidx,
  refprod    : Integer;
  colstr,
  datastr,
  nvestr,
  besid,
  packsug,
  packhint   : String;
  slist      : TStringList;
  first      : Boolean;
  query      : TSmartQuery;
  item       : TListItem;
  selitem    : TListItem;
  col        : TListColumn;
  field      : TField;
  packtextflag,
  verpflag   : Boolean;
  addSqlstr,
  dispcols   : String;
begin
  res := 0;

  {$ifdef TraceVerpacken}
    verpacklog.Write ('TAuftragPosVerpackenForm.FormShow');
  {$endif}

  fMengeStr := '';

  VerpackungLabel.Caption    := '';
  VersandKartonLabel.Caption := '';
  VerpackMengeLabel.Caption  := '';

  AbmessungPanel.Visible := false;

  LTLength.Text := '';
  LTWidth.Text  := '';
  LTHeigth.Text := '';

  fSelectPackLT := False;

  if (aoTouch in AppOptions) Then begin
    ButtonPanel.Height := 80;

    AbortButton.Font.Size := 24;
    AbortButton.Width     := 200;
    AbortButton.Height    := 50;
    AbortButton.Top       := (AbortButton.Parent.ClientHeight - AbortButton.Height) div 2;
    AbortButton.Left      := AbortButton.Parent.ClientWidth - 8 - AbortButton.Width;

    ReprintAdvertisingButton.Font.Size := AbortButton.Font.Size;
    ReprintAdvertisingButton.Width     := GetTextWidth (AbortButton.Font, ReprintAdvertisingButton.Caption) + 2 * 16;
    ReprintAdvertisingButton.Height    := AbortButton.Height;
    ReprintAdvertisingButton.Top       := AbortButton.Top;
    ReprintAdvertisingButton.Left      := AbortButton.Left - 30 - ReprintAdvertisingButton.Width;

    VerpackenButton.Font.Size := AbortButton.Font.Size;
    VerpackenButton.Width     := AbortButton.Width;
    VerpackenButton.Height    := AbortButton.Height;
    VerpackenButton.Top       := AbortButton.Top;
    //VerpackenButton.Left      := AbortButton.Left - 30 - VerpackenButton.Width;
  end;

  query := LVSDatenModul.CreateSmartQuery (Self, 'FormShow');

  try
    if (fRefPackplatz > 0) then begin
      query.SQL.Clear;
      query.SQL.Add ('select * from V_WA_PACKPLATZ where REF=:ref');
      query.Params.ParamByName('ref').Value := fRefPackplatz;

      query.Open;

      if not Assigned (query.FindField ('OPT_SHOW_USED_LT')) then
        WaBoxePanel.Visible := False
      else
        WaBoxePanel.Visible := (query.FieldByName ('OPT_SHOW_USED_LT').AsString = '1');

      if Assigned (query.FindField ('OPT_GROUP_PACK_POS')) then
        fGroupPos := (query.FieldByName ('OPT_GROUP_PACK_POS').AsString = '1')
      else fGroupPos := False;

      if Assigned (query.FindField ('OPT_REUSE_NVE')) then
        fCanChangeNVE := (query.FieldByName ('OPT_REUSE_NVE').AsString = '1')
      else fCanChangeNVE := False;

      if Assigned (query.FindField ('OPT_VPE_SELECT_ALLOWED')) then
        fAddVPEVerpacken := (query.FieldByName ('OPT_VPE_SELECT_ALLOWED').AsString = '1');

      if Assigned (query.FindField ('OPT_SELECT_PACK_LT')) then begin
        if (not fSelectPackLT and (query.FieldByName ('OPT_SELECT_PACK_LT').AsString = '1')) then
          fSelectPackLT := true;
      end;

      if Assigned (query.FindField ('OPT_AUTO_CLOSE')) then
        fAutoClose := (query.FieldByName ('OPT_AUTO_CLOSE').AsString = '1');

      if Assigned (query.FindField ('OPT_AUTO_FINISH')) then
        fAutoFinsih := (query.FieldByName ('OPT_AUTO_FINISH').AsString = '1');

      if Assigned (query.FindField ('OPT_ERROR_CONFIRMED')) then
        fErrorConfirmed := (query.FieldByName ('OPT_ERROR_CONFIRMED').AsString = '1');

      if Assigned (query.FindField ('OPT_DIM_PACK_LT')) then
        AbmessungPanel.Visible := (query.FieldByName ('OPT_DIM_PACK_LT').AsString = '1');

      if Assigned (query.FindField ('SCALES_TYPE')) then begin
        fWiegeThread.WaageIP    := query.FieldByName ('SCALES_IP').AsString;
        fWiegeThread.WaagePort  := DBGetIntegerNull (query.FieldByName ('SCALES_IP_PORT'));
        fWiegeThread.WaageType  := query.FieldByName ('SCALES_TYPE').AsString;

        if (Length (fWiegeThread.WaageType) > 0) and (Length (fWiegeThread.WaageIP) > 0) then begin
          WaagePanel.Visible := true;
          WaageLabel.Caption := fWiegeThread.WaageIP;
        end;
      end;

      query.Close;
    end;

    if (fFachNr <= 0) then
      FachLabel.Caption := ''
    else FachLabel.Caption := FormatResourceText (1628, [IntToStr (fFachNr)]);

    NewNVEButton.Visible := fCanChangeNVE;

    NVELabel.Caption := '';

    if (fRefNVE = -1) and (fCanChangeNVE) then begin
      query.SQL.Clear;
      query.SQL.Add ('select REF,NVE_NR from V_NVE_01 where STATUS<>''DEL'' and REF_AUF_KOPF=:ref');
      query.Params.ParamByName('ref').Value := fRefAuftrag;
      query.Open;

      if not (query.Fields [0].IsNull) then begin
        fRefNVE := query.Fields [0].AsInteger;
        NVELabel.Caption := query.Fields [1].AsString;
      end;

      query.Close;
    end;

    query.SQL.Clear;
    query.SQL.Add ('select a.*,mcfg.*,nvl (prod.OPT_NACHNAHME, av.OPT_NACHNAHME) as OPT_NACHNAHME,av.VERSAND_ART,adr.LAND,adr.LAND_ISO,adr.NAME1 as ADR_NAME1,adr.NAME2 as ADR_NAME2,lt.NAME as LT_NAME'
                 + ',l.PLZ,l.LAND as LAGER_LAND,m.CONFIG_OPT,sped.IFC_KENNZEICHEN,adr.COLLI_ADR_TEXT,aq.ANLIEFERZEIT_VON,m.NAME as SUB_MANDANT,av.SHIPPING_UNITS,scfg.OPT_MULTI_SHIPMEND'
                 + ',coalesce (slt.TARA_GEWICHT, plt.TARA_GEWICHT,lt.TARA_GEWICHT) as TARA_GEWICHT,coalesce (slt.L, plt.L,lt.L) as L,coalesce (slt.B, plt.B,lt.B) as B,coalesce (slt.H, plt.H,lt.H) as H'
                 +' ,at.PACK_TEXT,aq.REF_PACKMITTEL'
                 +' ,plt.NAME as PACKMITTEL, plt.TARA_GEWICHT as PACKMITTEL_LT_TARA, lt.TARA_GEWICHT as LT_TARA, slt.NAME as SPED_LT, slt.TARA_GEWICHT as SPED_LT_TARA'
                 +' ,acfg.OPT_ONE_PACKAGE,slt.REF as REF_SPED_LT'
                 + ',(select count (*) from VQ_ARTIKEL where nvl (OPT_SERIAL, ''0'')>''0'' and REF in (select REF_AR from VQ_AUFTRAG_POS where REF_AUF_KOPF=a.REF)) as USE_SERIAL,tim.PACK_BEGIN'
                 + ' from V_AUFTRAG a inner join VQ_AUFTRAG aq on (aq.REF=a.REF) inner join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF) inner join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR)'
                 + ' inner join VQ_AUFTRAG_TEXTE at on (at.REF_AUF_KOPF=a.REF)'
                 + ' inner join V_AUFTRAG_TIMING tim on (tim.REF_AUF_KOPF=a.REF)'
                 + ' inner join V_LAGER l on (l.REF=a.REF_LAGER)'
                 + ' inner join V_MANDANT m on ((a.REF_SUB_MAND is not null and m.REF=a.REF_SUB_MAND) or (a.REF_SUB_MAND is null and m.REF=a.REF_MAND))'
                 + ' inner join V_MANDANT_CONFIG mcfg on (mcfg.REF_MAND=m.REF)'
                 + ' left outer join V_LT_TYPEN lt on (lt.REF=a.REF_LT)'
                 + ' left outer join V_LT_TYPEN plt on (plt.REF=aq.REF_PACKMITTEL)'
                 + ' left outer join V_SPEDITIONEN sped on (sped.REF=av.REF_SPED)'
                 + ' left outer join V_SPED_CONFIG scfg on (scfg.REF_SPED=sped.REF)'
                 + ' left outer join V_LT_TYPEN slt on (slt.REF=scfg.REF_DEFAULT_LT)'
                 + ' left outer join V_SPED_PRODUKTE prod on (prod.REF=av.REF_SPED_PRODUKT)'
                 + ' left outer join V_AUFTRAG_ART_CONFIG acfg on (acfg.REF=a.REF_ART_CONFIG)'
                 + ' where a.REF=:ref');
    query.Params.ParamByName('ref').Value := fRefAuftrag;

    query.Open;

    ArtikelImagePanel.Visible := CheckOpt(query.FieldByName('CONFIG_OPT').AsString, cMandArPictureVerpack);

    fOptCOD             := (query.FieldByName('OPT_NACHNAHME').AsString > '0');
    fUseSerial          := query.FieldByName('USE_SERIAL').AsInteger > 0;
    fOnePackage         := query.FieldByName('OPT_ONE_PACKAGE').AsString > '0';
    fNeedShippingUnits  := query.FieldByName('OPT_MULTI_SHIPMEND').AsString = '2';
    fShippingUnits      := DBGetIntegerNull (query.FieldByName('SHIPPING_UNITS'));

    if (query.FieldByName('LAGER_LAND').IsNull) then
      fAusland := not (query.FieldByName('LAND_ISO').IsNull or (query.FieldByName('LAND_ISO').AsString = 'DE'))
    else fAusland := (query.FieldByName('LAND_ISO').AsString <> query.FieldByName('LAGER_LAND').AsString);

    fRefMand    := query.FieldByName('REF_MAND').AsInteger;
    fRefSubMand := DBGetReferenz (query.FieldByName('REF_SUB_MAND'));
    fMandant    := query.FieldByName('MANDANT').AsString;
    fRefLager   := query.FieldByName('REF_LAGER').AsInteger;
    fLager      := query.FieldByName('LAGER').AsString;
    fIFCSped    := query.FieldByName('IFC_KENNZEICHEN').AsString;
    fVersandArt := query.FieldByName('VERSAND_ART').AsString;

    {$ifdef TraceVerpacken}
      verpacklog.Write ('TAuftragPosVerpackenForm.FormShow: fRefAuftrag='+IntToStr (fRefAuftrag)+', fRefNVE='+IntToStr (fRefNVE)+', fRefMand='+IntToStr (fRefMand)+', fRefLager='+IntToStr (fRefLager)+', fVersandArt='+fVersandArt+', fIFCSped='+fIFCSped+', fOnePackage='+BoolToStr (fOnePackage, true)+', fGroupPos='+BoolToStr (fGroupPos, true));
    {$endif}

    fPackTimeFlag := query.FieldByName('PACK_BEGIN').IsNull;

    //Wenn der Mandant die Verpackungserfassung erzwingt
    if (not fSelectPackLT and CheckOpt (query.FieldByName('CONFIG_OPT').AsString, cMandVerpackungErfassen)) then
      fSelectPackLT := true;

    fSerialMinLen        := -1;
    fSerialMaxLen        := -1;
    fSerialUniqueAllOver := False;

    if Assigned (query.FindField ('SERIAL_UNIQUE_ALL_OVER')) then begin
      fSerialMinLen := DBGetIntegerNull (query.FindField ('SERIAL_MIN_LEN'));
      fSerialMaxLen := DBGetIntegerNull (query.FindField ('SERIAL_MAX_LEN'));

      fSerialUniqueAllOver := (query.FindField ('SERIAL_UNIQUE_ALL_OVER').AsString > '0');
    end;

    if (query.FieldByName('REF_SUB_MAND').IsNull) then begin
      SubMandantPanel.Visible := false;
      Height := Height - SubMandantPanel.Height;
    end else begin
      SubMandantLabel.Caption := query.FieldByName('SUB_MANDANT').AsString;
    end;

    //Wenn der Versandkarton gewählt werden muss
    if (fSelectPackLT) then begin
      fRefLTType := -1;
      fLTTara    := -1;

      if not (query.FieldByName('PACKMITTEL').IsNull) then
        VerpackungLabel.Caption := query.FieldByName('PACKMITTEL').AsString
      else if not (query.FieldByName('LT_NAME').IsNull) then
        VerpackungLabel.Caption := query.FieldByName('LT_NAME').AsString;
    end else begin
      //Ansonsten die Vorgaben aus dem Auftrag
      if not (query.FieldByName('REF_SPED_LT').IsNull) then begin
        VerpackungLabel.Caption := query.FieldByName('SPED_LT').AsString;
        fRefLTType := DBGetIntegerNull (query.FieldByName('REF_SPED_LT'));
        fLTTara    := DBGetIntegerNull (query.FieldByName('SPED_LT_TARA'));
      end else if not (query.FieldByName('REF_PACKMITTEL').IsNull) then begin
        VersandKartonLabel.Caption := query.FieldByName('PACKMITTEL').AsString;
        fRefLTType := DBGetIntegerNull (query.FieldByName('REF_PACKMITTEL'));
        fLTTara    := DBGetIntegerNull (query.FieldByName('PACKMITTEL_LT_TARA'));
      end else begin
        VersandKartonLabel.Caption := query.FieldByName('LT_NAME').AsString;
        fRefLTType := DBGetIntegerNull (query.FieldByName('REF_LT'));
        fLTTara    := DBGetIntegerNull (query.FieldByName('LT_TARA'));
      end;

      fLTTara  := DBGetIntegerNull (query.FieldByName('TARA_GEWICHT'));

      fRefAufLTType := fRefLTType;
      fRefAufLTTara := fLTTara;
      fAufLTName := VerpackungLabel.Caption;

      if (fSpedInfos.PresetWeight or AbmessungPanel.Visible) then begin
        if (query.FieldByName('L').IsNull) then
          LTLength.Text := ''
        else LTLength.Text := IntToStr (query.FieldByName('L').AsInteger div 10);

        if (query.FieldByName('B').IsNull) then
          LTWidth.Text := ''
        else LTWidth.Text := IntToStr (query.FieldByName('B').AsInteger div 10);

        if (query.FieldByName('H').IsNull) then
          LTHeigth.Text := ''
        else LTHeigth.Text := IntToStr (query.FieldByName('H').AsInteger div 10);
      end;
    end;

    fLiefPLZ   := query.FieldByName('PLZ').AsString;
    fISOLand   := query.FieldByName('LAND_ISO').AsString;

    AuftragNrLabel.Caption := query.FieldByName('AUFTRAG_NR').AsString;
    field := query.FindField ('KD_KOMM_NR');
    if Assigned (field) and not field.IsNull then
      AuftragNrLabel.Caption := AuftragNrLabel.Caption + ' (' + field.AsString + ')';

    if (LVSConfigModul.KundenID = 2094) then begin
      //Getnow
      WarenempfLabel.Caption := query.FieldByName('ADR_NAME1').AsString;

      if not (query.FieldByName('ADR_NAME2').IsNull) then
        WarenempfLabel.Caption := query.FieldByName('ADR_NAME2').AsString;

      t := query.FieldByName('ANLIEFERZEIT_VON').AsInteger;

      try
        LieferDatumLabel.Caption := query.FieldByName('LIEFER_DATUM').AsString + ' ' + FormatIntToStr (t div 100, 2)+':'+FormatIntToStr (t mod 100, 2);;
      except
        LieferDatumLabel.Caption := '??.??.????' + ' ' + FormatIntToStr (t div 100, 2)+':'+FormatIntToStr (t mod 100, 2);
      end;
    end else begin
      if (query.FieldByName('KUNDEN_NR').IsNull) then
        WarenempfLabel.Caption := ''
      else
        WarenempfLabel.Caption := query.FieldByName('KUNDEN_NR').AsString + ' / ';

      WarenempfLabel.Caption := WarenempfLabel.Caption + query.FieldByName('KUNDEN_NAME').AsString + ' / ' + query.FieldByName('LAND').AsString + ' ('+query.FieldByName('LAND_ISO').AsString+')';

      try
        LieferDatumLabel.Caption := query.FieldByName('LIEFER_DATUM').AsString;
      except
        LieferDatumLabel.Caption := '??.??.????';
      end;
    end;

    LSTextLabel.Caption := query.FieldByName('LIEFER_TEXT').AsString;


    if (Pos ('PRESENT;', query.FieldByName('DRUCKART').AsString) > 0) then
      fPackHinweis := GetResourceText (1316)
    else if not (query.FieldByName('PACK_TEXT').IsNull) then
      fPackHinweis := query.FieldByName('PACK_TEXT').AsString
    else if Assigned (query.FindField ('DEFAULT_PACK_HINWEIS')) and not (query.FieldByName('DEFAULT_PACK_HINWEIS').IsNull) then
      fPackHinweis := query.FieldByName('DEFAULT_PACK_HINWEIS').AsString
    else
      fPackHinweis := '';

    if (UpperCase (fVersandArt) <> 'EMMASBOX') then begin
      EmmasbocPanel.Visible := False;
      Height := Height - EmmasbocPanel.Height;
    end else begin
      EmmasboxResLabel.Caption := '';

      slist := TStringList.Create;

      try
        slist.Delimiter := ';';
        slist.DelimitedText := query.FieldByName('COLLI_ADR_TEXT').AsString;

        for i:=0 to slist.Count - 1 do begin
          if TryStrToInt (copy (slist [i], 4), iwert) and (iwert > 0) then begin
            if (Copy (slist [i], 1, 2) = 'AS') then
              datastr := 'Ambiente klein'
            else if (Copy (slist [i], 1, 2) = 'AL') then
              datastr := 'Ambiente groß'
            else if (Copy (slist [i], 1, 2) = 'FS') then
              datastr := 'Frische klein'
            else if (Copy (slist [i], 1, 2) = 'FL') then
              datastr := 'Frische groß'
            else if (Copy (slist [i], 1, 2) = 'CS') then
              datastr := 'TK klein'
            else if (Copy (slist [i], 1, 2) = 'CL') then
              datastr := 'TK groß';

            if (Length (EmmasboxResLabel.Caption) > 0) then
              EmmasboxResLabel.Caption := EmmasboxResLabel.Caption + ', ';

            EmmasboxResLabel.Caption := EmmasboxResLabel.Caption + IntToStr (iwert) + ' x ' + datastr;
          end;
        end;
      finally
        slist.Free;
      end;
    end;

    query.Close;

    //Prüfen ob nur IDs gescannt werden dürfen
    fScanBestandID := False;

    query.SQL.Clear;
    query.SQL.Add ('select plan.* from VQ_AUFTRAG auf left outer join V_AUFTRAG_ART_PLANUNG plan on (plan.REF=auf.REF_ART_PLANUNG) where auf.REF=:ref');
    query.Params.ParamByName('ref').Value := fRefAuftrag;

    query.Open;

    if Assigned (query.FindField('OPT_BESTAND_ID')) then
      fScanBestandID := (query.FieldByName ('OPT_BESTAND_ID').AsString > '0');

    query.Close;

    //Prüfen ob eine bestimmte Verpackungskarton-Art vorgegeben ist
    fVerpackArt := '';
    fVerpackArtFix := false;

    query.SQL.Clear;
    query.SQL.Add ('select * from V_AUFTRAG_VERSAND where REF_AUF_KOPF=:ref');
    query.Params.ParamByName('ref').Value := fRefAuftrag;

    try
      query.Open;

      //Wenn es das Feld gar nicht gibt, dann nicht
      if not Assigned (query.FindField ('LT_PACKING_GROUP')) then
        fVerpackArt := ''
      else
        fVerpackArt := query.FieldByName ('LT_PACKING_GROUP').AsString;

      query.Close;
    except
    end;

    query.SQL.Clear;
    query.SQL.Add ('select cfg.* from V_AUFTRAG_ART_CONFIG cfg, VQ_AUFTRAG auf where cfg.REF=auf.REF_ART_CONFIG and auf.REF=:ref');
    query.Params.ParamByName('ref').Value := fRefAuftrag;

    try
      query.Open;

      //Wenn es das Feld gar nicht gibt, dann nicht
      if not Assigned (query.FindField ('LT_PACKING_GROUP')) then
        fVerpackArtFix := false
      else
        fVerpackArtFix := not (query.FieldByName ('LT_PACKING_GROUP').IsNull);
      query.Close;
    except
    end;


    //Warnhinweise aus den Gefahrstoff-Zuordnung ermitteln
    fWarnHinweis := '';

    if (LVSConfigModul.UseGefahrgut) then begin
      query.SQL.Clear;
      query.SQL.Add ('select distinct (PACK_HINT) from V_AUFTRAG_GEFAHRSTOFFE where PACK_HINT is not null and REF=:ref');
      query.Params.ParamByName('ref').Value := fRefAuftrag;

      try
        query.Open;

        while not (query.Eof) do begin
          if (Length (fWarnHinweis) > 0) then fWarnHinweis := fWarnHinweis + ', ';

          fWarnHinweis := fWarnHinweis + query.FieldByName('PACK_HINT').AsString;

          query.Next;
        end;

        query.Close;
      except
      end;
    end;

    ReloadForm (Sender);

    selidx  := -1;
    selitem := nil;

    VPEAnzLabel.Caption := '';
    BruttoGewichtEdit.Text := '';

    if (res = 0) then begin
      col := VerpackAufPosListView.Columns.Add;
      col.Caption := GetResourceText (1084);

      fListViewSortCol := -1;
      fListViewSortDir := 0;
      
      fFlagPackFolge  := false;
      fFlagArPackText := false;

      //Prüfen ob es in VQ_ARTIKEL das Feld PACK_FOLGE gibt, wird zum Sortieren benutzt
      query.SQL.Clear;
      query.SQL.Add ('select * from VQ_ARTIKEL where ROWNUM=0');

      query.Open;

      if Assigned (query.FindField ('PACK_FOLGE')) then
        fFlagPackFolge := true;

      if Assigned (query.FindField ('OPT_VERPACKEN')) then
        fFlagArPackText := true;

      query.Close;

      //Prüfen ob Pack_text in V_AUFTRAG_POS vorhanden ist
      query.SQL.Clear;
      query.SQL.Add ('select * from V_AUFTRAG_POS where ROWNUM=0');

      query.Open;

      if Assigned (query.FindField ('PACK_TEXT')) then
        packtextflag := true;

      query.Close;

      if (fRefSubMand > 0) then
        CheckConfigParameter (fRefSubMand, -1, fRefLager, 'PACK_GRID_COLS', dispcols)
      else
        CheckConfigParameter (fRefMand, -1, fRefLager, 'PACK_GRID_COLS', dispcols);

      if packtextflag then
      begin
        addSqlstr := ',ap.PACK_TEXT as POS_PACK_TEXT';
      end
      else
      begin
        addSqlstr := ',NULL as POS_PACK_TEXT';
      end;


      //Alle noch offenen Verpackungspositieon finden
      query.SQL.BeginUpdate;
      query.SQL.Clear;
      query.SQL.Add ('select vkp.*'
                    +',nvl(vkp.BRUTTO_GEWICHT,vkp.NETTO_GEWICHT)*1000 as BRUTTO_GEWICHT_GRAMM'
                    +',pic.PICTURE_PATH,ar.OPT_SERIAL,ae.L,ae.B,ae.H,ae.OPT_READY_FOR_SHIP,ae.EAN,ae.BARCODE'
                    +addSqlstr
                    );

      if (fUseSerial) then
        query.SQL.Add (',(select listagg (INFO_TEXT, '','') within group (order by INFO_TEXT) from (select distinct (INFO_TEXT) from V_AUFTRAG_KOMM_POS_INFO where INFO_ART=''SERIAL'' and REF_AUF_KOMM_POS=vkp.REF)) as SERIAL_NR')
      else
       query.SQL.Add (',null as SERIAL_NR');

      query.SQL.Add ('from V_PCD_VERPACKEN_KOMM_POS vkp'+
                     ' inner join VQ_ARTIKEL ar on (ar.REF=vkp.REF_PICK_AR)'+
                     ' inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=vkp.REF_AR_EINHEIT)'+
                     ' left outer join VQ_ARTIKEL_PICTURE pic on (pic.REF=ar.REF_PICTURE)'+
                     ' left outer join V_ARTIKEL_GRUPPE grp on (grp.REF=ar.REF_GRUPPE)'+
                     ' left outer join V_ARTIKEL_SORTIMENT sor on (sor.REF=grp.REF_SORTIMENT)'+
                     ' left outer join V_AUFTRAG_POS ap on (ap.REF=vkp.REF_AUF_POS)'+
                     ' where vkp.REF_AUF_KOPF=:ref');
      if fFlagPackFolge then
        query.SQL.Add ('order by sor.PACK_FOLGE nulls last, ar.PACK_FOLGE nulls last, lpad (vkp.ARTIKEL_NR, 32, '' ''), vkp.COLLI_NAME')
      else
        query.SQL.Add ('order by sor.PACK_FOLGE nulls last, lpad (vkp.ARTIKEL_NR, 32, '' ''), vkp.COLLI_NAME');
      query.SQL.EndUpdate;

      query.Params.ParamByName('ref').Value := fRefAuftrag;

      selidx := -1;

      VerpackAufPosListView.Clear;

      Screen.Cursor := crSQLWait;
      VerpackAufPosListView.Items.BeginUpdate;

      first := true;

      fMengeCol  := Nil;
      fMengeSug  := Nil;
      fSerialCol := Nil;
      fChargeCol := Nil;

      fSumVPE := 0;

      try
        query.Open;

        if (query.RecordCount > 0) then begin
          {$ifdef TraceVerpacken}
            verpacklog.Write ('TAuftragPosVerpackenForm.FormShow: RecordCount='+IntToStr (query.RecordCount));
          {$endif}

          while not (query.Eof) do begin
            verpflag := query.FieldByName('OPT_READY_FOR_SHIP').AsString = '1';

            anz := query.FieldByName('MENGE_PICK').AsInteger - query.FieldByName('MENGE_VERPACKT').AsInteger;

            if (anz > 0) then begin
              fSumVPE := fSumVPE + anz;

              i := 1;

              repeat
                item := VerpackAufPosListView.Items.Add;

                if (selidx = -1) and (fRefAufKommPos <> -1) and (query.FieldByName('REF').AsInteger = fRefAufKommPos) then begin
                  selidx := item.Index;
                  item.Checked := True;
                  selitem := item;
                end;

                item.Caption := query.FieldByName ('ARTIKEL_NR').AsString;

                refprod := -1;

                if Assigned (query.FindField ('REF_SPED_PRODUKT')) then
                  refprod := DBGetIntegerNull (query.FieldByName ('REF_SPED_PRODUKT'));

                if Assigned (query.FindField ('VERPACK_HINWEIS')) then
                  packhint := query.FieldByName ('VERPACK_HINWEIS').AsString;

                if (Assigned (query.FindField ('POS_PACK_TEXT'))) and (not query.FindField ('POS_PACK_TEXT').IsNull) then
                begin
                  packhint := query.FieldByName ('POS_PACK_TEXT').AsString;
                end;

                if Assigned (query.FindField ('BESTAND_ID')) then
                  besid := query.FieldByName ('BESTAND_ID').AsString;


                if fGroupPos and not fScanBestandID then begin
                  if first then begin
                    fSollMengeCol := VerpackAufPosListView.Columns.Add;
                    fSollMengeCol.Caption := GetResourceText (1521);
                    fSollMengeCol.Alignment := taRightJustify;
                    fSollMengeCol.AutoSize := True;
                  end;

                  if first then begin
                    fMengeCol := VerpackAufPosListView.Columns.Add;
                    fMengeCol.Caption := GetResourceText (1522);
                    fMengeCol.Alignment := taRightJustify;
                    fMengeCol.AutoSize := True;
                  end;

                  if query.FieldByName ('OPT_SERIAL').AsString > '0' then begin
                    Inc (i);

                    item.Data := TVerpackEntry.Create(query.FieldByName ('REF').AsInteger, query.FieldByName ('REF_AUF_POS').AsInteger, query.FieldByName ('REF_PICK_AR').AsInteger, query.FieldByName ('REF_PICK_EINHEIT').AsInteger, query.FieldByName ('REF_AR_EINHEIT').AsInteger, 1, DBGetDoubleNull (query.FieldByName ('BRUTTO_GEWICHT_GRAMM')), DBGetIntegerNull (query.FieldByName ('REF_SPED')), refprod, query.FieldByName ('SPEDITION').AsString, query.FieldByName ('CHARGE').AsString, query.FieldByName ('PICTURE_PATH').AsString, packhint, besid, True, verpflag, DBGetIntegerNull (query.FieldByName ('L')), DBGetIntegerNull (query.FieldByName ('B')), DBGetIntegerNull (query.FieldByName ('H')));
                    item.SubItems.Add (IntToStr (1));
                  end else begin
                    Inc (i, anz);

                    item.Data := TVerpackEntry.Create(query.FieldByName ('REF').AsInteger, query.FieldByName ('REF_AUF_POS').AsInteger, query.FieldByName ('REF_PICK_AR').AsInteger, query.FieldByName ('REF_PICK_EINHEIT').AsInteger, query.FieldByName ('REF_AR_EINHEIT').AsInteger, anz, DBGetDoubleNull (query.FieldByName ('BRUTTO_GEWICHT_GRAMM')), DBGetIntegerNull (query.FieldByName ('REF_SPED')), refprod, query.FieldByName ('SPEDITION').AsString, query.FieldByName ('CHARGE').AsString, query.FieldByName ('PICTURE_PATH').AsString, packhint, besid, false, verpflag, DBGetIntegerNull (query.FieldByName ('L')), DBGetIntegerNull (query.FieldByName ('B')), DBGetIntegerNull (query.FieldByName ('H')));
                    item.SubItems.Add (IntToStr (anz));
                  end;

                  item.SubItems.Add ('');
                end else begin
                  Inc (i);

                  item.Data := TVerpackEntry.Create(query.FieldByName ('REF').AsInteger, query.FieldByName ('REF_AUF_POS').AsInteger, query.FieldByName ('REF_PICK_AR').AsInteger, query.FieldByName ('REF_PICK_EINHEIT').AsInteger, query.FieldByName ('REF_AR_EINHEIT').AsInteger, 1, DBGetDoubleNull (query.FieldByName ('BRUTTO_GEWICHT_GRAMM')), DBGetIntegerNull (query.FieldByName ('REF_SPED')), refprod, query.FieldByName ('SPEDITION').AsString, query.FieldByName ('CHARGE').AsString, query.FieldByName ('PICTURE_PATH').AsString, packhint, besid, query.FieldByName ('OPT_SERIAL').AsString > '0', verpflag, DBGetIntegerNull (query.FieldByName ('L')), DBGetIntegerNull (query.FieldByName ('B')), DBGetIntegerNull (query.FieldByName ('H')));
                end;

                if Assigned (query.FindField ('PACK_SUGGESTION')) then begin
                  if first then begin
                    fMengeSug := VerpackAufPosListView.Columns.Add;
                    fMengeSug.Caption := 'Pack-Vorschlag';
                    fMengeSug.Alignment := taRightJustify;
                    fMengeSug.AutoSize := True;
                  end;

                  item.SubItems.Add (query.FieldByName ('PACK_SUGGESTION').AsString);
                end;

                TVerpackEntry (item.Data).EAN     := query.FieldByName ('EAN').AsString;
                TVerpackEntry (item.Data).Barcode := query.FieldByName ('BARCODE').AsString;

                field := query.FindField ('ARTIKEL_NR_MANDANT');
                if Assigned (field) then begin
                  if first then begin
                    col := VerpackAufPosListView.Columns.Add;
                    col.Caption := GetResourceText (1220);
                    col.AutoSize := True;
                  end;

                  item.SubItems.Add (field.AsString);
                end;

                field := query.FindField ('ARTIKEL_NR_HERSTELLER');
                if Assigned (field) then begin
                  if first then begin
                    col := VerpackAufPosListView.Columns.Add;
                    col.Caption := GetResourceText (1195);
                    col.AutoSize := True;
                  end;

                  item.SubItems.Add (field.AsString);
                end;

                field := query.FindField ('EAN');
                if Assigned (field) then begin
                  if first then begin
                    col := VerpackAufPosListView.Columns.Add;
                    col.Caption := GetResourceText (1127);
                    col.AutoSize := True;
                  end;

                  item.SubItems.Add (field.AsString);
                end;

                if (LVSConfigModul.UseBestandID) then begin
                  field := query.FindField ('BESTAND_ID');
                  if Assigned (field) then begin
                    if first then begin
                      col := VerpackAufPosListView.Columns.Add;
                      col.Caption := GetResourceText (1836);
                      col.AutoSize := True;
                    end;

                    item.SubItems.Add (field.AsString);
                  end;
                end;

                if (LVSConfigModul.UseBesCategory) then begin
                  field := query.FindField ('BESTAND_CATEGORY');
                  if Assigned (field) then begin
                    if first then begin
                      col := VerpackAufPosListView.Columns.Add;
                      col.Caption := GetResourceText (1837);
                      col.AutoSize := True;
                    end;

                    item.SubItems.Add (field.AsString);
                  end;
                end;

                if first then begin
                  col := VerpackAufPosListView.Columns.Add;
                  col.Caption := GetResourceText (1085);
                end;
                item.SubItems.Add (query.FieldByName ('ARTIKEL_TEXT').AsString);

                if first then begin
                  col := VerpackAufPosListView.Columns.Add;
                  col.Caption := GetResourceText (1086);
                  col.AutoSize := True;
                end;
                item.SubItems.Add (query.FieldByName ('EINHEIT').AsString);

                field := query.FindField ('VERPACK_HINWEIS');
                if Assigned (field) then begin
                  if (Pos ('VERPACK_HINWEIS', dispcols) > 0) then begin
                    if first then begin
                      col := VerpackAufPosListView.Columns.Add;
                      col.Caption := GetResourceText (1874);
                      col.AutoSize := True;
                    end;

                    item.SubItems.Add (field.AsString);
                  end;
                end;

                field := query.FindField ('REPLACED_FOR_ARTIKEL_NR');
                if Assigned (field) then begin
                  if first then begin
                    col := VerpackAufPosListView.Columns.Add;
                    col.Caption := GetResourceText (1371);
                    col.AutoSize := True;
                  end;

                  item.SubItems.Add (field.AsString);
                end;

                field := query.FindField ('REPLACED_FOR_ARTIKEL_TEXT');
                if Assigned (field) then begin
                  if first then begin
                    col := VerpackAufPosListView.Columns.Add;
                    col.Caption := GetResourceText (1371);
                    col.AutoSize := True;
                  end;

                  item.SubItems.Add (field.AsString);
                end;

                if first then begin
                  col := VerpackAufPosListView.Columns.Add;
                  col.Caption := GetResourceText (1314);
                  col.AutoSize := True;
                end;
                item.SubItems.Add (query.FieldByName ('SPEDITION').AsString);

                if (LVSConfigModul.UseArtikelCollis) then begin
                  if first then begin
                    col := VerpackAufPosListView.Columns.Add;
                    col.Caption :=  GetResourceText (1079);
                    col.AutoSize := True;
                  end;

                  item.SubItems.Add (query.FieldByName ('COLLI_NAME').AsString);
                end;

                if (LVSConfigModul.UseMHD) then begin
                  field := query.FindField ('MHD');
                  if Assigned (field) then begin
                    if first then begin
                      col := VerpackAufPosListView.Columns.Add;
                      col.Caption := GetResourceText (1095);
                      col.AutoSize := True;
                    end;

                    item.SubItems.Add (field.AsString);
                  end;
                end;

                if (LVSConfigModul.UseCharge) then begin
                  field := query.FindField ('CHARGE');
                  if Assigned (field) then begin
                    if first then begin
                      fChargeCol := VerpackAufPosListView.Columns.Add;
                      fChargeCol.Caption := GetResourceText (1096);
                      fChargeCol.AutoSize := True;
                    end;

                    item.SubItems.Add (field.AsString);
                  end;
                end;

                if (fUseSerial) then begin
                  if first then begin
                    fSerialCol := VerpackAufPosListView.Columns.Add;
                    fSerialCol.Caption := GetResourceText (1367);
                    fSerialCol.AutoSize := True;
                  end;

                  if (query.FieldByName ('SERIAL_NR').IsNull) Then begin
                    TVerpackEntry (item.Data).SerialDefine := False;
                    TVerpackEntry (item.Data).Serial := '';
                  end else begin
                    TVerpackEntry (item.Data).SerialDefine := True;
                    TVerpackEntry (item.Data).Serial := query.FieldByName ('SERIAL_NR').AsString;
                  end;

                  item.SubItems.Add (TVerpackEntry (item.Data).Serial);
                end;

                if first then begin
                  col := VerpackAufPosListView.Columns.Add;
                  col.Caption := GetResourceText (1315);
                  col.AutoSize := TRue;
                end;
                item.SubItems.Add (query.FieldByName ('KOMM_LE_NR').AsString);

                if Assigned (query.FindField ('L')) then begin
                  datastr := '';

                  if not (query.FieldByName ('L').IsNull) then
                    datastr := query.FieldByName ('L').AsString;

                  if not (query.FieldByName ('B').IsNull) then begin
                    if (Length (datastr) > 0) then datastr := datastr + 'x';

                    datastr := datastr + query.FieldByName ('B').AsString;
                  end;

                  if not (query.FieldByName ('H').IsNull) then begin
                    if (Length (datastr) > 0) then datastr := datastr + 'x';

                    datastr := datastr + query.FieldByName ('H').AsString;
                  end;

                  if first then begin
                    col := VerpackAufPosListView.Columns.Add;
                    col.Caption := GetResourceText (1207);
                  end;
                  item.SubItems.Add (datastr);
                end;

                if first then begin
                  col := VerpackAufPosListView.Columns.Add;
                  col.Caption := GetResourceText (1133);
                  col.Alignment := taRightJustify;
                end;

                if not (query.FieldByName ('BRUTTO_GEWICHT').IsNull) then
                  fGewichtCol := item.SubItems.Add (Format ('%.5f kg', [query.FieldByName ('BRUTTO_GEWICHT').AsFloat]))
                else if not (query.FieldByName ('NETTO_GEWICHT').IsNull) then
                  fGewichtCol := item.SubItems.Add (Format ('%.5f kg', [query.FieldByName ('NETTO_GEWICHT').AsFloat]))
                else
                  fGewichtCol := item.SubItems.Add ('');

                first := False;
              until (i > anz);
            end;

            query.Next;
          end;

          query.Close;
        end;
      finally
        VerpackAufPosListView.Items.EndUpdate;

        Screen.Cursor := crDefault;
      end;

      SummeVPELabel.Caption := 'Summer VPEs: ' + IntToStr (fSumVPE);

      if (LVSSecurityModule.CheckExecuteRecht (fMandant, fLager, '', 'SelectAllVerpacken')) then
        VerpackAufPosListView.Checkboxes := true
      else
        VerpackAufPosListView.Checkboxes := not Assigned (fMengeCol);

      slist := TStringList.Create;

      try
        slist.Delimiter := ';';

        LVSConfigModul.ReadFormParameter (Self, 'VerpackAufPosListView', colstr);

        if (Length (colstr) = 0) then
          ColumnsResizeMenuItemClick (Sender)
        else begin
          slist.DelimitedText := colstr;
          for idx := 0 to slist.Count - 1 do begin
            if TryStrToInt (slist [idx], iwert) then begin
              //RTE verhindern wenn Wert ungültig
              if (idx < VerpackAufPosListView.Columns.Count) and (iwert > 0) and (iwert < VerpackAufPosListView.Width div 2) then
                VerpackAufPosListView.Columns [idx].Width := iwert;
            end;
          end;
        end;
      finally
        slist.Free;
      end;
    end;

    if Assigned (fWiegeThread) and (Length (fWiegeThread.WaageType) > 0) then
      fWiegeThread.Resume;
  finally
    query.Free;
  end;

  //Hiermit kann die manuelle Auswahl grundsätzlich unterbunden werden, egal was pro Packplatz konfiguriert wurde
  if not (LVSSecurityModule.CheckExecuteRecht (fMandant, fLager, '', 'AddVPEVerpacken')) then
    fAddVPEVerpacken := False;

  NewNVEButton.Enabled := (fRefNVE > 0);

  ChangeSpedButton.Visible := LVSSecurityModule.CheckChangeRecht (fMandant, fLager, '', 'VerpackenChangeSpedition');

  LVSConfigModul.ConfigForm (Self);

  LVSConfigModul.RestoreFormInfo (Self);

  if (res = 0) then
    res := AssigneAuftragPackplatz (fRefPackplatz, fRefAuftrag);

  if (res <> 0) then begin
    ModalResult := mrAbort;
    Close;
  end else begin
    ListPanelResize (Sender);

    VerpackenButton.Enabled := (VerpackAufPosListView.Items.Count > 0);

    VerpackAufPosListView.SetFocus;

    if (VerpackAufPosListView.Items.Count > 0) then begin
      if (selidx >= 0) and (selidx < VerpackAufPosListView.Items.Count) then
        VerpackAufPosListView.Selected    := VerpackAufPosListView.Items [selidx]
      else
        VerpackAufPosListView.Selected    := VerpackAufPosListView.Items [0];

      VerpackAufPosListView.ItemFocused := VerpackAufPosListView.Selected;

      if VerpackAufPosListView.Selected.Checked then begin
        if Assigned (fMengeCol) then begin
          if (fScanPackMenge > 0) then
            VerpackAufPosListView.Selected.SubItems [fMengeCol.Index - 1] := IntToStr (fScanPackMenge)
          else
            VerpackAufPosListView.Selected.SubItems [fMengeCol.Index - 1] := '1';
        end;
      end;

      VerpackAufPosListCheckedChange (Nil, VerpackAufPosListView.Selected);
    end;

    if (VerpackAufPosListView.Height < 10) then
      Height := Height + (10 - VerpackAufPosListView.Height);

    //Damit nicht aus versehen mit Enter verpackt wird
    if (fGroupPos) then
      VerpackenButton.Default := False;

    Application.OnIdle := AppIdle;
  end;

  PostMessage (Handle, WM_USER + 10, 0, 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListCheckedChange (Sender: TObject; Item : TListItem);
var
  i,
  anz,
  maxl,
  maxb,
  maxh,
  menge,
  lastidx     : Integer;
  gw          : Double;
  identfrom   : TArtikelIdentInputForm;
  servicefrom : TValueAddedForm;
  serialform  : TAddKommInfoForm;
begin
  if Assigned (Item) and (Item.Checked) and Assigned (Item.Data) then begin
    if (fRefSped = -1) then begin
      fRefSped     := TVerpackEntry (Item.Data).RefSped;
      fRefSpedProd := TVerpackEntry (Item.Data).RefSpedProd;

      UpdateSpeditionInfos;
    end else if (TVerpackEntry (Item.Data).RefSped > 0) and (fManRefSped = -1) and (fRefSped <> TVerpackEntry (Item.Data).RefSped) then begin
      Item.Checked := False;
    end;

    if Item.Checked then begin
      if (TVerpackEntry (Item.Data).OptSerial and (Length (TVerpackEntry (Item.Data).Serial) = 0)) then begin
        serialform := TAddKommInfoForm.Create (Self);

        try
          serialform.Prepare (TVerpackEntry (Item.Data).Ref, TVerpackEntry (Item.Data).GesamtMenge, 'AUFTRAG_KOMM_POS_INFO');
          serialform.InfoArtComboBox.ItemIndex := 1;

          serialform.SerialMinLen := fSerialMinLen;
          serialform.SerialMaxLen := fSerialMaxLen;

          for i:=0 to (VerpackAufPosListView.Items.Count - 1) do begin
            if (VerpackAufPosListView.Items [i] <> Item) and Assigned (VerpackAufPosListView.Items [i].Data) and (Length (TVerpackEntry (VerpackAufPosListView.Items[i].Data).Serial) > 0) then
              serialform.SerialList.Add (TVerpackEntry (VerpackAufPosListView.Items[i].Data).Serial);
          end;

          if (serialform.ShowModal <> mrOk) then
            Item.Checked := False
          else begin
            if (Length (serialform.SerialInfoMemo.Text) = 0) then
              Item.Checked := False
            else begin
              TVerpackEntry (Item.Data).Serial := serialform.Serial;

              if Assigned (fSerialCol) then
                Item.SubItems [fSerialCol.Index - 1] := TVerpackEntry (Item.Data).Serial;
            end;
          end;
        finally
          serialform.Release;
        end;
      end else begin
        if not (TVerpackEntry (Item.Data).SerialDefine) then begin
          TVerpackEntry (Item.Data).Serial := '';

          if Assigned (fSerialCol) then
            Item.SubItems [fSerialCol.Index - 1] := TVerpackEntry (Item.Data).Serial;
        end;
      end;
    end;
  end;

  if Assigned (Item) and not (Item.Checked) and Assigned (fMengeCol) then begin
    Item.SubItems [fMengeCol.Index - 1] := '';
  end;

  anz := 0;
  maxl := -1;
  maxb := -1;
  maxh := -1;

  gw := 0;

  lastidx := -1;

  for i := 0 to VerpackAufPosListView.Items.Count - 1 do begin
    if (VerpackAufPosListView.Items [i].Checked) then begin
      if not Assigned (fMengeCol) then
        menge := 1
      else if not TryStrToInt (VerpackAufPosListView.Items [i].SubItems [fMengeCol.Index - 1], menge) then
        menge := 0;

      if (TVerpackEntry (VerpackAufPosListView.Items [i].Data).BruttoGewicht > 0) then
        gw := gw + (TVerpackEntry (VerpackAufPosListView.Items [i].Data).BruttoGewicht * menge);

      if (TVerpackEntry (VerpackAufPosListView.Items [i].Data).L > 0) and (TVerpackEntry (VerpackAufPosListView.Items [i].Data).L > maxl) then
        maxl := TVerpackEntry (VerpackAufPosListView.Items [i].Data).L;

      if (TVerpackEntry (VerpackAufPosListView.Items [i].Data).B > 0) and (TVerpackEntry (VerpackAufPosListView.Items [i].Data).B > maxb) then
        maxb := TVerpackEntry (VerpackAufPosListView.Items [i].Data).B;

      if (TVerpackEntry (VerpackAufPosListView.Items [i].Data).H > 0) and (TVerpackEntry (VerpackAufPosListView.Items [i].Data).H > maxh) then
        maxh := TVerpackEntry (VerpackAufPosListView.Items [i].Data).H;

      anz := anz + menge;
      lastidx := i;
    end;
  end;

  if (fLTTara > 0) then
    gw := gw + fLTTara;

  if (anz = 0) then begin
    if (fManRefSped = -1) then begin
      fRefSped     := -1;
      fRefSpedProd := -1;
      fSpedInfos.SpedName := '';

      Label6.Font.Color := clWindowText;
      SpedLabel.Font.Color := clWindowText;
      SpedLabel.Caption := '';
      SpedPanel.Color := clBtnFace;
      SpedImage.Visible := False;
    end;

    VPEAnzLabel.Caption    := '';
    BruttoGewichtEdit.Text := '';
  end else begin
    //Sonderfall Home24, nur ein Artikel pro Sendung in die Schweiz
    if (LVSConfigModul.KundenID = 2042) and (fISOLand = 'CH') and (anz > 1) then begin
      //Home24
      Item.Checked := False;

      FehlerPanel.Tag := 1;
      FehlerPanel.Visible := True;
      FehlerButton.Visible := fErrorConfirmed;
      FehlerPanel.Caption := 'Für Schweiz darf nur jeweils ein Artikel verpackt werden';
    end else if (fSpedInfos.SpedMaxGw > 0) and (gw > fSpedInfos.SpedMaxGw) then begin
      Item.Checked := False;

      ShowErrorPanel (FormatMessageText (1523, [Format ('%4.1f', [fSpedInfos.SpedMaxGw / 1000])]));
    end else begin
      VPEAnzLabel.Caption := IntToStr (anz);

      if (gw <= 0) then
        BruttoGewichtEdit.Text := ''
      else
        BruttoGewichtEdit.Text := Format ('%.3f', [gw / 1000]);

      //Die maximalen Abmessungen nur dann übernehmen, wenn keine Versand-Karton gescannt wurde
      if (fRefBoxType <= 0) and fSpedInfos.PresetWeight then begin
        if (maxl > 0) then
          LTLength.Text := IntToStr (maxl div 10);

        if (maxb > 0) then
          LTWidth.Text := IntToStr (maxb div 10);

        if (maxh > 0) then
          LTHeigth.Text := IntToStr (maxh div 10);
      end;
    end;
  end;

  if (LVSConfigModul.UseKennzeichnung) then begin
    if Assigned (Item) and Assigned (Item.Data) and (Item.Checked) then begin
      identfrom := TArtikelIdentInputForm.Create (Self);

      try
        identfrom.PrepareWA (TVerpackEntry (Item.Data).Ref);

        if (identfrom.OpenInfoCount > 0) then
          identfrom.ShowModal;

      finally
        identfrom.Release;
      end;
    end;
  end;

  if (LVSConfigModul.UseValueServices) then begin
    if Assigned (Item) and Assigned (Item.Data) and (Item.Checked) then begin
      servicefrom := TValueAddedForm.Create (Self);

      try
        servicefrom.PrepareWA (fRefAuftrag, TVerpackEntry (Item.Data).RefAufPos);

        if (servicefrom.OpenServiceCount > 0) then
          servicefrom.ShowModal;

      finally
        servicefrom.Release;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.05.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListViewChange(Sender: TObject; Item: TListItem; Change: TItemChange);
begin
  if (Change = ctState) and Assigned (Item) then begin
    if Assigned (fMengeCol) and (VerpackAufPosListView.ItemIndex >= 0) then begin
      fMengeStr := VerpackAufPosListView.Items [VerpackAufPosListView.ItemIndex].SubItems [fMengeCol.Index - 1];

      VerpackMengeLabel.Caption := fMengeStr;
    end;

    if Assigned (Item.Data) and (Length (TVerpackEntry (Item.Data).PackHint) > 0) then begin
      if (Length (fPackHinweis) = 0) and (Length (fWarnHinweis) = 0) then begin
        PackHintLabel.Caption := TVerpackEntry (Item.Data).PackHint;
      end else if (Length (fWarnHinweis) = 0) then begin
        PackHinweisLabel.Caption := fPackHinweis;
        PackHintLabel.Caption := TVerpackEntry (Item.Data).PackHint;
      end else begin
        PresentLabel.Caption := fWarnHinweis;
        PackHinweisLabel.Caption := fPackHinweis;
        PackHintLabel.Caption := TVerpackEntry (Item.Data).PackHint;
      end;
    end else if (Length (fWarnHinweis) > 0) and (Length (fPackHinweis) > 0) then begin
      PresentLabel.Caption := fWarnHinweis;
      PackHinweisLabel.Caption := fPackHinweis;
    end else if (Length (fPackHinweis) > 0) then begin
      PackHinweisLabel.Caption := fPackHinweis;
    end else begin
      PresentLabel.Caption := '';
      PackHinweisLabel.Caption := '';
      PackHintLabel.Caption := '';
    end;

    if (ArtikelImagePanel.Visible) then begin
      if not Assigned (Item.Data) or (Length (TVerpackEntry (Item.Data).Picture) = 0) then begin
        fLastPicturePath := '';
        ArtikelImage.Visible := False;
      end else if (LowerCase (copy (TVerpackEntry (Item.Data).Picture, 1, 7)) = 'file://') then begin
        if (copy (TVerpackEntry (Item.Data).Picture, 8) <> fLastPicturePath) then begin
          ArtikelImage.Visible := False;

          ShowArtikelPicture (ArtikelImage, copy (TVerpackEntry (Item.Data).Picture, 8));

          fLastPicturePath := copy (TVerpackEntry (Item.Data).Picture, 8);
        end;
      end else begin
        fLastPicturePath := '';
        ArtikelImage.Visible := False;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListViewClick(Sender: TObject);
begin
  FehlerPanel.Tag := 0;
  //FehlerPanel.Visible := False;
  FehlerPanel.Caption := '';
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.07.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListViewCompare(Sender: TObject; Item1, Item2: TListItem; Data: Integer; var Compare: Integer);
var
  i:integer;
begin
  if (fListViewSortCol = -1) then
    Compare := 0
  else if fListViewSortCol = 0 then begin
    if (fListViewSortDir = 0) then
      Compare := CompareText(Item1.Caption, Item2.Caption)
    else
      Compare := CompareText(Item2.Caption, Item1.Caption);
  end else begin
    i := fListViewSortCol -1;

    if (fListViewSortDir = 0) then
      Compare := CompareText(Item1.SubItems[i], Item2.SubItems[i])
    else
      Compare := CompareText(Item2.SubItems[i], Item1.SubItems[i]);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.07.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListViewColumnClick(Sender: TObject; Column: TListColumn);
begin
  if (fListViewSortCol = -1) or (fListViewSortCol <> Column.Index) then begin
    fListViewSortCol := Column.Index;
    fListViewSortDir := 0;
  end else if (fListViewSortDir = 0) then
    fListViewSortDir := 1
  else
    fListViewSortDir := 0;

  (Sender as TCustomListView).AlphaSort;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListViewDeletion (Sender: TObject; Item: TListItem);
begin
  if Assigned (Item.Data) then
    TVerpackEntry (Item.Data).Free;

  Item.Data := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListViewKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
var
  ch : Char;
begin
  if (Key = VK_DELETE) then begin
    ch := #127;

    VerpackAufPosListViewKeyPress (Sender, ch);

    Key := 0;
  end else if (Key = Ord (' ')) and not fAddVPEVerpacken then begin
    Key := 0;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListViewMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
var
   Item: TListItem;
   HitTest: THitTests;

 procedure prepareCellCopy;
  var
    startingX,
    ColumnIndex : Integer;
  begin
    if not (Button = TMouseButton.mbRight) then
    begin
      exit;
    end;

    startingX := VerpackAufPosListView.Left;
    for ColumnIndex := 0 to VerpackAufPosListView.Columns.Count - 1 do
    begin
      if (X >= startingX) and (X < (startingX + VerpackAufPosListView.Columns[ColumnIndex].Width)) then
      begin
        fSelectedColumnIndex := ColumnIndex-1;
        break;
      end;
      startingX := startingX + VerpackAufPosListView.Columns[ColumnIndex].Width;
    end;
  end;
begin
  prepareCellCopy;
  // Welchem Item gehört die CheckBox
  Item := VerpackAufPosListView.GetItemAt(x, y);

  // Was wurde vom Item genau angeklickt
  HitTest := VerpackAufPosListView.GetHitTestInfoAt(x, y);

  // Falls ein Item angeklickt wurde und davon die Checkbox
  if Assigned (Item) and (HitTest = [htOnStateIcon]) then begin
    if not fAddVPEVerpacken then begin
      if (Item.Checked) then
        Item.Checked := False;
    end;
  end;

  if (Button = mbRight) and (HitTest <> [htOnStateIcon]) then begin
    with Mouse.CursorPos do
      VerpackAufPosListViewPopupMenu.Popup(X, Y);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.10.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListViewMouseUp(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
var
   Item: TListItem;
   HitTest: THitTests;
begin
   // Welchem Item gehört die CheckBox
   Item := VerpackAufPosListView.GetItemAt(x, y);

   // Was wurde vom Item genau angeklickt
   HitTest := VerpackAufPosListView.GetHitTestInfoAt(x, y);

   // Falls ein Item angeklickt wurde und davon die Checkbox
   if Assigned (Item) and (HitTest = [htOnStateIcon]) then begin
     if not fAddVPEVerpacken then begin
       if (Item.Checked) then
         Item.Checked := False;
     end else begin
       if (Item.Checked and fGroupPos) then begin
         if (Assigned (fMengeCol) and Assigned (Item.Data)) then begin
           fMengeStr := IntToStr (TVerpackEntry (Item.Data).GesamtMenge);

           Item.SubItems [fMengeCol.Index - 1] := fMengeStr;

           VerpackMengeLabel.Caption := fMengeStr;
         end;
       end;
     end;

     VerpackAufPosListCheckedChange (Sender, Item);
   end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.10.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListViewPopupMenuPopup(Sender: TObject);
begin
  ColumnsResizeMenuItem.Enabled := (VerpackAufPosListView.Columns.Count > 0);

  if not (VerpackAufPosListView.Checkboxes) then begin
    SelectAllMenuItem.Visible := false;
    DeselectAllMenuItem.Visible := false;
  end else if fAddVPEVerpacken then begin
    SelectAllMenuItem.Enabled := True;
    DeselectAllMenuItem.Enabled := True;
  end else begin
    SelectAllMenuItem.Enabled := False;
    DeselectAllMenuItem.Enabled := False;
  end;

  PrintEANLabelMenuItem.Enabled     := false;
  PrintBarcodeLabelMenuItem.Enabled := false;
  PrintIDLabelMenuItem.Enabled      := false;

  if Assigned (VerpackAufPosListView.Selected) and Assigned (VerpackAufPosListView.Selected.Data) then begin
    PrintEANLabelMenuItem.Enabled     := (Length (TVerpackEntry (VerpackAufPosListView.Selected.Data).EAN) > 0);
    PrintBarcodeLabelMenuItem.Enabled := (Length (TVerpackEntry (VerpackAufPosListView.Selected.Data).Barcode) > 0);
    PrintIDLabelMenuItem.Enabled      := (Length (TVerpackEntry (VerpackAufPosListView.Selected.Data).BestandID) > 0);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 30.11.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.VerpackAufPosListViewResize(Sender: TObject);
var
  cw,
  idx : Integer;
begin
  if (VerpackAufPosListView.Columns.Count > 0) then begin
    cw := 0;
    idx := 0;

    while ((idx < VerpackAufPosListView.Columns.Count) and (VerpackAufPosListView.Columns [idx] <> fSollMengeCol)) do begin
      cw := cw + VerpackAufPosListView.Columns [idx].Width;

      Inc (idx);
    end;

    if (VerpackAufPosListView.Columns [idx] = fSollMengeCol) then
      cw := cw + VerpackAufPosListView.Columns [idx].Width - 8;

    SummeVPELabel.Left := VerpackAufPosListView.Left + cw;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 29.11.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.WaagePaintBoxPaint(Sender: TObject);
var
  bm : TBitmap;
begin
  bm := TBitmap.Create;

  try
    bm.handle := loadbitmap(hinstance, 'waage');

    BitBlt (WaagePaintBox.Canvas.Handle, 0, 0, bm.Width, bm.Height, bm.Canvas.Handle, 0, 0, SrcCopy);
  finally
    bm.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 29.11.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.WaagePanelDblClick(Sender: TObject);
begin
  if (fOldWaageGewicht > 0) then
    BruttoGewichtEdit.Text := Format ('%7.3f', [fOldWaageGewicht / 1000.0]);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.ScannerErfassung (var Message: TMessage);
var
  i,
  res,
  idx       : Integer;
  arnr,
  einh,
  nvenr,
  sendnr,
  ltname,
  spedstr,
  numstr    : string;
  refar,
  reflt,
  refae,
  refnve,
  barlen,
  menge,
  mtrmenge,
  packmenge : Integer;
  gw        : Double;
  errmsg    : string;
  done,
  flag      : Boolean;
  arinfo    : TArtikelInfo;
  barcode   : TEANBarcode;
  barinfo   : TBarcodeInfos;
  query     : TSmartQuery;
begin
  if (FehlerPanel.Visible and fErrorConfirmed) then begin
    Beep;
    PlaySound (PChar (LVSConfigModul.FrontendConfig.ScanErrSound), 0, SND_ASYNC);
  end else begin
    {$ifdef TraceVerpacken}
      verpacklog.Write ('TAuftragPosVerpackenForm.ScannerErfassung:'+ScanCode);
    {$endif}

    FehlerPanel.Tag := 0;
    FehlerPanel.Visible := False;
    FehlerPanel.Caption := '';

    errmsg := '';

    done  := False;
    reflt := -1;
    refnve := -1;
    barlen := Length (ScanCode);

    if ((barlen > 5) and (ScanCode[1] = Code128ID) and (copy (ScanCode, 2, 5) = '#KBD-')) then begin  //Keyboard Codes
      done := True;

      if (copy (ScanCode, 7, 4) = 'CR-#') then
        PostMessage (Handle, WM_USER + 99, 0, 0)
      else if (copy (ScanCode, 7, 5) = 'ESC-#') then
        PostMessage (Handle, WM_USER + 98, 0, 0);
    end else if ((barlen > 5) and (ScanCode[1] = Code128ID) and (copy (ScanCode, 2, 6) = '#SPED-') and (copy (ScanCode, barlen, 1) = '#')) then begin
      //Versender per Scanner erfassen
      done := True;

      spedstr := copy (ScanCode, 8, Length (ScanCode) - 1 - 8);

      query := LVSDatenModul.CreateSmartQuery (Self, 'ScannerErfassung_Sped');

      try
        query.SQL.Add ( 'select sped.REF, sped.NAME, (select count (*) from V_WA_PACKPLATZ_REL_SPED where OPT_SELECT=''1'' and REF_PACKPLATZ=:ref_pack and REF_SPED=sped.REF) from V_SPEDITIONEN sped where sped.STATUS<>''DEL'' and'
                       +' sped.REF_LAGER=:ref_lager and Upper (sped.NAME)=Upper (:name)');
        query.Params.ParamByName ('ref_lager').Value := fRefLager;
        query.Params.ParamByName ('ref_pack').Value := fRefPackplatz;
        query.Params.ParamByName ('name').Value := spedstr;

        try
          query.Open;

          if (query.Fields [0].IsNull) then
            errmsg := FormatMessageText (1396, [])
          else if (query.Fields [2].AsInteger = 0) then
            errmsg := FormatMessageText (1397, [query.Fields [1].AsString])
          else begin
            fRefSpedProd := -1;
            fManRefSped  := query.Fields [0].AsInteger;
            fRefSped     := query.Fields [0].AsInteger;

            UpdateSpeditionInfos;
          end;
        except
          errmsg := FormatMessageText (1394, []);
        end;

        query.Close;
      finally
        query.Free;
      end;
    end else if (Length (ScanCode) >= 12) and (ScanCode [1] = EAN13ID) then begin
      res := DetectLTTypeEAN (copy (ScanCode, 2, 13), reflt, ltname);

      if (res = 0) and (reflt > 0) then
        done := True;
    end else if (barlen > 5) and (ScanCode [1] = Code128ID) and (Copy (ScanCode, 2, 2) ='51') and (ScanCode [barlen] = GetLELPCheckChar (copy (ScanCode, 2, barlen - 2))) then begin
      res := DetectLTTypeID (copy (ScanCode, 4, Length (ScanCode) - 4), reflt, ltname);

      if (res = 0) and (reflt > 0) then
        done := True;
    end else begin
      //Ansonsten einfach auf gut Glück nachsehen, ob es sich viellicht doch um einen LT-Barcode handelt :-)
      res := DetectLTTypeEAN (copy (ScanCode, 2), reflt, ltname);

      if (res = 0) and (reflt > 0) then
        done := True;
    end;

    if (done) then begin
      if (reflt > 0) then begin
        VerpackungLabel.Caption := '';

        fRefLTType  := reflt;
        fRefBoxType := reflt;

        {$ifdef TraceVerpacken}
          verpacklog.Write ('TAuftragPosVerpackenForm.ScannerErfassung: fRefLTType='+IntToStr (fRefLTType));
        {$endif}

        query := LVSDatenModul.CreateSmartQuery (Self, 'ScannerErfassung_reflt');

        try
          query.SQL.Add ('select * from V_LT_TYPEN where REF=:ref');
          query.Params [0].Value := reflt;

          query.Open;

          if Assigned (query.FindField('LT_PACKING_GROUP')) and (Length (fVerpackArt) > 0) and (fVerpackArtFix or not (query.FieldByName ('LT_PACKING_GROUP').IsNull)) and (query.FieldByName ('LT_PACKING_GROUP').AsString <> fVerpackArt) Then begin
            errmsg := FormatMessageText (1768, []);
          end else begin
            VersandKartonLabel.Caption := query.FieldByName('NAME').AsString;

            fLTTara := DBGetIntegerNull (query.FieldByName ('TARA_GEWICHT'));

            if (fSpedInfos.PresetWeight) then begin
              if (query.FieldByName('L').IsNull) then
                LTLength.Text := ''
              else LTLength.Text := IntToStr (query.FieldByName('L').AsInteger div 10);

              if (query.FieldByName('B').IsNull) then
                LTWidth.Text := ''
              else LTWidth.Text := IntToStr (query.FieldByName('B').AsInteger div 10);

              if (query.FieldByName('H').IsNull) then
                LTHeigth.Text := ''
              else LTHeigth.Text := IntToStr (query.FieldByName('H').AsInteger div 10);
            end;

            gw := 0;

            for i := 0 to VerpackAufPosListView.Items.Count - 1 do begin
              if (VerpackAufPosListView.Items [i].Checked) then begin
                if not Assigned (fMengeCol) then
                  menge := 1
                else if not TryStrToInt (VerpackAufPosListView.Items [i].SubItems [fMengeCol.Index - 1], menge) then
                  menge := 0;

                if (TVerpackEntry (VerpackAufPosListView.Items [i].Data).BruttoGewicht > 0) then
                  gw := gw + (TVerpackEntry (VerpackAufPosListView.Items [i].Data).BruttoGewicht * menge);
              end;
            end;

            if (gw <= 0) then
              BruttoGewichtEdit.Text := ''
            else begin
              if (fLTTara > 0) then
                gw := gw + fLTTara;

              BruttoGewichtEdit.Text := Format ('%.3f', [gw / 1000]);

              if (fSpedInfos.SpedMaxGw > 0) and (gw > fSpedInfos.SpedMaxGw) then
                ShowErrorPanel (FormatMessageText (1523, [Format ('%4.1f', [fSpedInfos.SpedMaxGw / 1000])]));
            end;
          end;

          query.Close;
        finally
          query.Free;
        end;
      end;
    end else if (Length(ScanCode) > 3) and (ScanCode[1] = EAN128ID) and (ScanCode [2] = '0') and (ScanCode [3] = '0') then begin  //NVE
      nvenr := Copy (ScanCode, 4, 18);

      query := LVSDatenModul.CreateSmartQuery (Self, 'ScannerErfassung_NVE');

      try
        query.SQL.Add ('select * from V_NVE_01 where REF_LAGER=:ref_lager and NVE_NR=:nvenr');
        query.Params.ParamByName ('ref_lager').Value := fRefLager;
        query.Params.ParamByName ('nvenr').Value := nvenr;

        try
          query.Open;

          if (query.RecordCount > 0) then begin
            if (query.FieldByName ('REF_AUF_KOPF').AsInteger <> fRefAuftrag) then
              errmsg := FormatMessageText (1746, [])
            else begin
              refnve := query.FieldByName ('REF').AsInteger;

              NVELabel.Caption := query.FieldByName ('NVE_NR').AsString;
            end;
          end else begin
            //NVE von DHL usw. suchen
            query.Close;

            query.SQL.Add ('select * from V_NVE_01 where REF_LAGER=:ref_lager and SENDUNGS_NR=:sendnr');
            query.Params.ParamByName ('ref_lager').Value := fRefLager;
            query.Params.ParamByName ('sendnr').Value := '00'+nvenr;

            query.Open;

            if (query.RecordCount > 0) then begin
              if (query.FieldByName ('REF_AUF_KOPF').AsInteger <> fRefAuftrag) then
                errmsg := FormatMessageText (1746, [])
              else begin
                refnve := query.FieldByName ('REF').AsInteger;

                NVELabel.Caption := query.FieldByName ('NVE_NR').AsString + ' (' + query.FieldByName ('SENDUNGS_NR').AsString+')';
              end;
            end;
          end;

          if (refnve > 0) then begin
            fRefNVE := refnve;

            if (fRefSped <> query.FieldByName ('REF_SPED').AsInteger) then begin
              fRefSped       := query.FieldByName ('REF_SPED').AsInteger;
              fRefSpedProd   := DBGetReferenz (query.FieldByName ('REF_SPED_PRODUKT'));

              UpdateSpeditionInfos;
            end;
          end;

          query.Close;
        except
          errmsg := FormatMessageText (1544, []);
        end;
      finally
        query.Free;
      end;
    end else if (Length(ScanCode) > 10) and (ScanCode[1] = Code128ID) and (ScanCode[2] = '%') then begin //DPD-Packlabel
      sendnr := Copy (ScanCode, 10, 14);

      query := LVSDatenModul.CreateSmartQuery (Self, 'ScannerErfassung_DPD');

      try
        query.SQL.Add ('select * from V_NVE_01 where REF_LAGER=:ref_lager and SENDUNGS_NR=:sendnr');
        query.Params.ParamByName ('ref_lager').Value := fRefLager;
        query.Params.ParamByName ('sendnr').Value := sendnr;

        try
          query.Open;

          if (query.RecordCount > 0) then begin
            if (query.FieldByName ('REF_AUF_KOPF').AsInteger <> fRefAuftrag) then
              errmsg := FormatMessageText (1746, [])
            else begin
              refnve := query.FieldByName ('REF').AsInteger;

              NVELabel.Caption := query.FieldByName ('NVE_NR').AsString + ' (' + query.FieldByName ('SENDUNGS_NR').AsString+')';
            end;
          end;

          if (refnve > 0) then begin
            fRefNVE := refnve;

            if (fRefSped <> query.FieldByName ('REF_SPED').AsInteger) then begin
              fRefSped       := query.FieldByName ('REF_SPED').AsInteger;
              fRefSpedProd   := DBGetReferenz (query.FieldByName ('REF_SPED_PRODUKT'));

              UpdateSpeditionInfos;
            end;
          end;

          query.Close;
        except
          errmsg := FormatMessageText (1544, []);
        end;
      finally
        query.Free;
      end;
    end else begin
      res := DetectAuftragArtikelBarcode (fRefAuftrag, refar, refae, arnr, einh, barcode, barinfo, errmsg);

      if (res = 0) and (Length (errmsg) = 0) then begin
        {$ifdef TraceVerpacken}
          verpacklog.Write ('TAuftragPosVerpackenForm.ScannerErfassung: refar='+IntToStr (refar)+', refae='+IntToStr (refae)+', arnr='+arnr+', einh='+einh+', BoxSerial='+barcode.BoxSerial+', EAN='+barcode.EAN);
        {$endif}

        if (Length (barcode.BoxSerial) > 0) then begin
          res := DetectLTSerialTyp (LVSDatenModul.AktLocationRef, fRefLager, barcode.BoxSerial, reflt);

          if (reflt > 0) then begin
            fRefLTType  := reflt;
            fNVESerial  := barcode.BoxSerial;

            query := LVSDatenModul.CreateSmartQuery (Self, 'ScannerErfassung_reflt');

            try
              query.SQL.Add ('select TARA_GEWICHT from V_LT_TYPEN where REF=:ref');
              query.Params [0].Value := fRefLTType;

              query.Open;

              fLTTara := DBGetIntegerNull (query.Fields [0]);

              query.Close;

              query.SQL.Clear;
              query.SQL.Add('select lt.REF,lt.NAME,nve.REF,nve.NVE_NR from V_LT_TYPEN lt left outer join V_NVE_01 nve on (nve.LHM_SERIAL=:serial and nve.REF_AUF_KOPF=:ref_auf) where lt.REF=:ref_lt');
              query.Params.ParamByName('ref_lt').Value := fRefLTType;
              query.Params.ParamByName('ref_auf').Value := fRefAuftrag;
              query.Params.ParamByName('serial').Value := fNVESerial;

              query.Open;

              VersandKartonLabel.Caption := query.Fields [1].AsString + ' ('+barcode.BoxSerial+')';

              if not (query.Fields [3].IsNull) then begin
                fRefNVE := query.Fields [2].AsInteger;
                fNVENr  := query.Fields [3].AsString;

                NVELabel.Caption := fNVENr;
              end;

              query.Close;
            finally
              query.Free;
            end;
          end;
        end else begin
          arinfo := TArtikelInfo.Create;

          try
            idx := -1;
            packmenge := 1;

            if (Length (barinfo.BestandID) > 0) then begin
              //Nach der ID suchen
              idx := 0;

              if Assigned (fMengeCol) then begin
                while (idx < VerpackAufPosListView.Items.Count) do begin
                  if (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).BestandID = barinfo.BestandID) then begin
                    //Die bisher schon Mengen berücksichtigen
                    if (Length (VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1]) = 0) then
                      menge := 0
                    else if not TryStrToInt (VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1], menge) then
                      menge := 99999999;

                    if ((menge + 1) <= TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge) then
                      break;
                  end;

                  Inc (idx);
                end;
              end else begin
                while (idx < VerpackAufPosListView.Items.Count) do begin
                  if not (VerpackAufPosListView.Items [idx].Checked) then begin
                    if (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).BestandID = barinfo.BestandID) then
                      break;
                  end;

                  Inc (idx);
                end;
              end;
            end else if fScanBestandID then begin
              errmsg := FormatMessageText (1813, []);
            end else begin
              mtrmenge := -1;

              res := GetArtikelInfos (refar, refae, arinfo);

              //Sonderfall GBL Fulfillment, Rehau Artikel in MTR
              if ((LVSConfigModul.KundenID = 2122) and (arinfo.Einheit = 'MTR')) then begin
                query := LVSDatenModul.CreateSmartQuery (Self, 'ScannerErfassung_Rehau');

                try
                  query.SQL.Add ('select INHALT_ANZAHL from VQ_ARTIKEL_EINHEIT where STATUS=''AKT'' and REF_AR=:ref_ar and INHALT_ANZAHL is not null order by INHALT_ANZAHL asc');
                  query.Params [0].Value := refar;

                  query.Open;

                  if (query.RecordCount > 0) then
                    mtrmenge := query.Fields [0].AsInteger;

                  query.Close;
                finally
                  query.Free;
                end;
              end;


              if (res <> 0) then
                errmsg := FormatMessageText (1530, [])
              else begin
                {$ifdef TraceVerpacken}
                  verpacklog.Write ('TAuftragPosVerpackenForm.ScannerErfassung: refar='+IntToStr (refar)+', refae='+IntToStr (refae)+', RefArtikel='+IntToStr (arinfo.RefArtikel)+', RefEinheit='+IntToStr (arinfo.RefEinheit)+', RefArtikelEinheit='+IntToStr (arinfo.RefArtikelEinheit));
                {$endif}

                idx := 0;
                packmenge := 0;

                if Assigned (fMengeCol) and (VerpackAufPosListView.ItemIndex >= 0) then begin
                  while (idx < VerpackAufPosListView.Items.Count) do begin
                    if (
                         (
                           arinfo.IsColliFlag
                           and
                           (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit = arinfo.RefArtikelEinheit)
                         )
                         or
                         (
                           not arinfo.IsColliFlag
                           and
                           (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefAr = arinfo.RefArtikel)
                           and
                           (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefEinheit = arinfo.RefEinheit)
                         )
                         or
                         (
                           not arinfo.IsColliFlag
                           and
                           (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit = arinfo.RefInhalt)
                         )
                      )
                    then begin
                      if (Length (VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1]) = 0) then
                        menge := 0
                      else if not TryStrToInt (VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1], menge) then
                        menge := 99999999;

                      if ((mtrmenge > 0) and ((mtrmenge + menge) <= TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge)) then
                        packmenge := mtrmenge
                      else if (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit = arinfo.RefInhalt) then
                        packmenge := arinfo.InhaltAnzahl
                      else if (arinfo.UVPAnzahl > 0) and ((menge + arinfo.UVPAnzahl) <= TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge) then
                        packmenge := arinfo.UVPAnzahl
                      else
                        packmenge := 1;

                      if ((menge + packmenge) <= TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge) then begin
                        break
                      end;
                    end;

                    Inc (idx);
                  end;
                end else begin
                  if (LVSConfigModul.UseCharge) and (arinfo.ChargeArt = 'P') then begin
                    while (idx < VerpackAufPosListView.Items.Count) do begin
                      if Assigned (fMengeCol) or not (VerpackAufPosListView.Items [idx].Checked) and
                        (
                         (
                           arinfo.IsColliFlag
                           and
                           (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit = arinfo.RefArtikelEinheit)
                         )
                         or
                         (
                           not arinfo.IsColliFlag
                           and
                           (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefAr = arinfo.RefArtikel)
                           and
                           (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefEinheit = arinfo.RefEinheit)
                         )
                         or
                         (
                           not arinfo.IsColliFlag
                           and
                           (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit = arinfo.RefInhalt)
                         )
                        )
                        and (
                              (Length (barcode.Charge) = 0)
                              or
                              (Length (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).Charge) = 0)
                              or
                              (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).Charge = barcode.Charge)
                            )
                      then begin
                        if not Assigned (fMengeCol) then
                          menge := 1
                        else if (Length (VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1]) = 0) then
                          menge := 0
                        else if not TryStrToInt (VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1], menge) then
                          menge := 99999999;

                        if ((mtrmenge > 0) and ((mtrmenge + menge) <= TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge)) then
                          packmenge := mtrmenge
                        else if (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit = arinfo.RefInhalt) then
                          packmenge := arinfo.InhaltAnzahl
                        else
                          packmenge := 1;

                        if (packmenge <= TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge) then begin
                          break
                        end;
                      end else
                        Inc (idx);
                    end;
                  end else begin
                    while (idx < VerpackAufPosListView.Items.Count) do begin
                      if Assigned (fMengeCol) or not (VerpackAufPosListView.Items [idx].Checked) and
                         (
                           (
                             arinfo.IsColliFlag
                             and
                             (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit = arinfo.RefArtikelEinheit)
                           )
                           or
                           (
                             not arinfo.IsColliFlag
                             and
                             (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefAr = arinfo.RefArtikel)
                             and
                             (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefEinheit = arinfo.RefEinheit)
                           )
                           or
                           (
                             not arinfo.IsColliFlag
                             and
                             (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit = arinfo.RefInhalt)
                           )
                         )
                      then begin
                        if not Assigned (fMengeCol) then
                          menge := 1
                        else if (Length (VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1]) = 0) then
                          menge := 0
                        else if not TryStrToInt (VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1], menge) then
                          menge := 99999999;

                        if ((mtrmenge > 0) and ((mtrmenge + menge) <= TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge)) then
                          packmenge := mtrmenge
                        else if (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit = arinfo.RefInhalt) then
                          packmenge := arinfo.InhaltAnzahl
                        else
                          packmenge := 1;

                        if (packmenge <= TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge) then begin
                          break
                        end;
                      end else
                        Inc (idx);
                    end;
                  end;
                end;
              end;
            end;

            if (Length (errmsg) = 0) then begin
              if (idx <> -1) and (idx < VerpackAufPosListView.Items.Count) then begin
                VerpackAufPosListView.ClearSelection;

                VerpackAufPosListView.Selected := VerpackAufPosListView.Items [idx];
                VerpackAufPosListView.ItemFocused := VerpackAufPosListView.Items [idx];
                VerpackAufPosListView.Items [idx].MakeVisible (True);

                //if (LVSConfigModul.UseCharge) and (arinfo.ChargeArt = 'P') and (Length (barcode.Charge) = 0) then
                //  errmsg := 'Für den Artikel muss eine Charge angegeben werden'
                //else begin
                  //VerpackAufPosListView.ItemIndex := idx;
                  if Assigned (fMengeCol) then begin
                    if not (VerpackAufPosListView.Items [idx].Checked) then begin
                      VerpackAufPosListView.Items [idx].Checked := True;

                      VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1] := IntToStr (packmenge);

                      VerpackAufPosListCheckedChange (Nil, VerpackAufPosListView.Items [idx]);
                    end else begin
                      if TryStrToInt (VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1], menge) then begin
                        if ((menge + 1) > TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge) then begin
                          if (arinfo.IsColliFlag) then
                            errmsg := FormatMessageText (1744, [arinfo.ArtikelNr + '/' + arinfo.ColliName])
                          else
                            errmsg := FormatMessageText (1744, [arinfo.ArtikelNr]);
                        end else begin
                          Inc (menge, packmenge);

                          VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1] := IntToStr (menge);

                          gw := 0;

                          for i := 0 to VerpackAufPosListView.Items.Count - 1 do begin
                            if (VerpackAufPosListView.Items [i].Checked) then begin
                              if not Assigned (fMengeCol) then
                                menge := 1
                              else if not TryStrToInt (VerpackAufPosListView.Items [i].SubItems [fMengeCol.Index - 1], menge) then
                                menge := 0;

                              if (TVerpackEntry (VerpackAufPosListView.Items [i].Data).BruttoGewicht > 0) then
                                gw := gw + (TVerpackEntry (VerpackAufPosListView.Items [i].Data).BruttoGewicht * menge);
                            end;
                          end;

                          if (fLTTara > 0) then
                            gw := gw + fLTTara;

                          if (fSpedInfos.SpedMaxGw > 0) and (gw > fSpedInfos.SpedMaxGw) then begin
                            Dec (menge);

                            VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1] := IntToStr (menge);

                            ShowErrorPanel (FormatMessageText (1523, [Format ('%4.1f', [fSpedInfos.SpedMaxGw / 1000])]));
                          end else if (gw <= 0) then
                            BruttoGewichtEdit.Text := ''
                          else
                            BruttoGewichtEdit.Text := Format ('%.3f', [gw / 1000]);
                        end;
                      end;
                    end;

                    fMengeStr := VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1];

                    VerpackMengeLabel.Caption := fMengeStr;
                  end else begin
                    VerpackAufPosListView.Items [idx].Checked := True;

                    if (idx < (VerpackAufPosListView.Items.Count - 1)) then
                      VerpackAufPosListView.Selected := VerpackAufPosListView.Items [idx + 1]
                    else
                      VerpackAufPosListView.Selected := VerpackAufPosListView.Items [idx];

                    VerpackAufPosListView.ItemFocused := VerpackAufPosListView.Selected;

                    VerpackAufPosListCheckedChange (Nil, VerpackAufPosListView.Selected);
                  end;
                //end;

                if (fAutoClose) then begin
                  idx := 0;
                  flag := true;

                  while (idx < VerpackAufPosListView.Items.Count) do begin
                    if not (VerpackAufPosListView.Items [idx].Checked) then begin
                      flag := false;
                      break;
                    end else if Assigned (fMengeCol) then begin
                      //Prüfen, ob alles in der Position verpackt wurde
                      numstr := VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1];

                      if (Length (numstr) = 0) or not TryStrToInt(numstr, menge) or (menge < TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge) then begin
                        flag := false;
                        break;
                      end;
                    end;

                    Inc (idx);
                  end;

                  if flag then begin
                    ModalResult := mrOk;
                  end;
                end;
              end else begin
                if (Length (barinfo.BestandID) > 0) then
                  errmsg := FormatMessageText (1814, [arinfo.ArtikelNr, barinfo.BestandID])
                else if (LVSConfigModul.UseCharge) and (arinfo.ChargeArt = 'P') then begin
                  if (arinfo.IsColliFlag) then
                    errmsg := FormatMessageText (1745, [arinfo.ArtikelNr + '/' + arinfo.ColliName, barcode.Charge])
                  else
                    errmsg := FormatMessageText (1745, [arinfo.ArtikelNr, barcode.Charge]);
                end else begin
                  if (arinfo.IsColliFlag) then
                    errmsg := FormatMessageText (1744, [arinfo.ArtikelNr + '/' + arinfo.ColliName])
                  else
                    errmsg := FormatMessageText (1744, [arinfo.ArtikelNr]);
                end;
              end;
            end;

          finally
            arinfo.Free;
          end;
        end;
      end else if (Length(ScanCode) > 8) then begin
        sendnr := Copy (ScanCode, 2);

        query := LVSDatenModul.CreateSmartQuery (Self, 'ScannerErfassung_sendnr');

        try
          query.SQL.Add ('select * from V_NVE_01 where REF_LAGER=:ref_lager and SENDUNGS_NR=:sendnr');
          query.Params.ParamByName ('ref_lager').Value := fRefLager;
          query.Params.ParamByName ('sendnr').Value := sendnr;

          try
            query.Open;

            if (query.RecordCount > 0) then begin
              if (query.FieldByName ('REF_AUF_KOPF').AsInteger <> fRefAuftrag) then
                errmsg := 'Das Packstück gehört nicht zu diesem Auftrag'
              else begin
                refnve := query.FieldByName ('REF').AsInteger;

                NVELabel.Caption := query.FieldByName ('NVE_NR').AsString + ' (' + query.FieldByName ('SENDUNGS_NR').AsString+')';
              end;
            end;

            if (refnve > 0) then begin
              fRefNVE := refnve;

              if (fRefSped <> query.FieldByName ('REF_SPED').AsInteger) then begin
                fRefSped       := query.FieldByName ('REF_SPED').AsInteger;
                fRefSpedProd   := DBGetReferenz (query.FieldByName ('REF_SPED_PRODUKT'));

                UpdateSpeditionInfos;
              end;
            end else if (Length (errmsg) = 0) then
              errmsg := FormatMessageText (1531, []);

            query.Close;
          except
            errmsg := FormatMessageText (1544, []);
          end;
        finally
          query.Free;
        end;
      end else
        errmsg := FormatMessageText (1531, []);
    end;

    if (Length (errmsg) > 0) then begin
      {$ifdef TraceVerpacken}
        verpacklog.Write ('TAuftragPosVerpackenForm.ScannerErfassung: errmsg='+errmsg);
      {$endif}

      ShowErrorPanel (errmsg);

      Beep;
      PlaySound (PChar (LVSConfigModul.FrontendConfig.ScanErrSound), 0, SND_ASYNC);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.10.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.SelectAllMenuItemClick(Sender: TObject);
var
  idx    : Integer;
  numstr : String;
begin
  if fAddVPEVerpacken then begin
    Screen.Cursor := crHourGlass;
    VerpackAufPosListView.Items.BeginUpdate;

    try
      idx := 0;

      while (idx < VerpackAufPosListView.Items.Count) do begin
        if not VerpackAufPosListView.Items [idx].Checked then begin
          if (fGroupPos) then begin
            if (Assigned (fMengeCol) and Assigned (VerpackAufPosListView.Items [idx].Data)) then begin
              numstr := IntToStr (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).GesamtMenge);

              VerpackAufPosListView.Items [idx].SubItems [fMengeCol.Index - 1] := numstr;
            end;
          end;

          VerpackAufPosListView.Items [idx].Checked := True;
          VerpackAufPosListCheckedChange (Sender, VerpackAufPosListView.Items [idx]);
        end;

        Inc (idx);
      end;
    finally
      VerpackAufPosListView.Items.EndUpdate;
      Screen.Cursor := crDefault;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.ColumnsResizeMenuItemClick(Sender: TObject);
var
  w,
  r,
  idx,
  maxw : Integer;
begin
  for idx := 0 to VerpackAufPosListView.Columns.Count - 1 do begin
    VerpackAufPosListView.Columns.BeginUpdate;

    try
      maxw := VerpackAufPosListView.StringWidth (VerpackAufPosListView.Columns.Items[idx].Caption);

      if (VerpackAufPosListView.Items.Count > 0) then begin
        for r := 0 to VerpackAufPosListView.Items.Count - 1 do begin
          if (idx = 0) then
            w := VerpackAufPosListView.StringWidth (VerpackAufPosListView.Items[r].Caption)
          else
            w := VerpackAufPosListView.StringWidth (VerpackAufPosListView.Items[r].SubItems [idx - 1]);

          if (w > maxw) then
            maxw := w;
        end;
      end;

      if (idx > 0) then
        VerpackAufPosListView.Columns [idx].Width := maxw + 16
      else if (VerpackAufPosListView.Checkboxes) then
        VerpackAufPosListView.Columns [idx].Width := maxw + 32
      else
        VerpackAufPosListView.Columns [idx].Width := maxw + 24;
    finally
      VerpackAufPosListView.Columns.EndUpdate;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.Timer1Timer(Sender: TObject);
begin
  if (FehlerPanel.Tag <> 0) and not FehlerPanel.Visible then begin
    FehlerPanel.Visible := True;
    FehlerButton.Visible := fErrorConfirmed;
  end else if (FehlerPanel.Tag = 0) and FehlerPanel.Visible then begin
    FehlerPanel.Visible := False;
  end;

  if (FehlerPanel.Visible) then begin
    if (FehlerPanel.Tag = 1) then begin
      FehlerPanel.Color := clRed;
      FehlerPanel.Tag   := 2;
    end else begin
      FehlerPanel.Color := clBtnFace;
      FehlerPanel.Tag   := 1;
    end;
  end;

  if Assigned (fWiegeThread) then begin
    if not fWiegeThread.WaageOnline then begin
      if (WaageLEDPanel.Color <> clMaroon) then
        WaageLEDPanel.Color := clMaroon;
    end else begin
      if (WaageLEDPanel.Color <> clLime) then
        WaageLEDPanel.Color := clLime;

      if (fWiegeThread.Weight <> fOldWaageGewicht) then begin
        fOldWaageGewicht := fWiegeThread.Weight;

        if (fOldWaageGewicht > 0) then
          WaageGewichtLabel.Caption := Format ('%7.3f', [fOldWaageGewicht / 1000.0]) + ' kg'
        else
          WaageGewichtLabel.Caption := '---' + ' kg';
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.CheckShippingUnits;
var
  res,
  count      : Integer;
  changeform : TChangeAuftragTourForm;
begin
  if (fNeedShippingUnits and (fShippingUnits <= 0)) then begin
    changeform := TChangeAuftragTourForm.Create (Self);

    try
      changeform.Caption := GetResourceText (1178);
      changeform.GrundPanel.Visible := False;
      changeform.PageControl1.ActivePage := changeform.ShippingUnitsTabSheet;

      changeform.Prepare (fRefAuftrag, -1);

      if (changeform.ShowModal = mrOk) then begin
        if (changeform.ShippingUnitsUpDown.Position <= 0) then
          count := -1
        else count := changeform.ShippingUnitsUpDown.Position;

        if (count <= 0) then
          FrontendMessages.MessageDLG(FormatMessageText (1767, []), mtError, [mbOK], 0)
        else begin
          res := SetAuftragShippingUnits (fRefAuftrag, count, 'packing');

          if (res = 0) then
            fShippingUnits := count
          else
            FrontendMessages.MessageDLG(FormatResourceText (1007, []) + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
        end;
      end;
    finally
      changeform.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.UpdateVerpackAufPosListView (var Message: TMessage);
var
  msgtext    : String;
  msgform    : TMessageForm;
  dlgres     : Integer;
  query      : TADOQuery;
begin
  Update;

  CheckShippingUnits;

  msgtext := '';

  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from V_AUFTRAG_TEXTE where REF_AUF_KOPF=:ref');

    query.Parameters.ParamByName('ref').Value := fRefAuftrag;

    try
      query.Open;

      if Assigned (query.FindField('POPUP_TEXT_BEFORE_PACK')) then
        msgtext := query.FieldByName ('POPUP_TEXT_BEFORE_PACK').AsString;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;

  if (Length (msgtext) > 0) then begin
    msgform := TMessageForm.Create (Self);

    try
      msgform.Button1.Visible := False;
      msgform.Button3.Visible := False;

      msgform.Caption := GetResourceText (1820);
      msgform.SetIcon (mtInformation);
      msgform.SetButton (msgform.Button2, GetResourceText (1615), mrOk);

      msgform.MessageLabel.Font.Size := 14;
      msgform.MessageLabel.Caption := msgtext;

      dlgres := msgform.ShowModal (msgform.Button2, 0);
    finally
      msgform.Free;
    end;
  end;

  if (VerpackAufPosListView.ItemIndex >= 0) then
    VerpackAufPosListViewChange (Nil, VerpackAufPosListView.Items [VerpackAufPosListView.ItemIndex], ctState);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.12.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.CloseMessage (var Message: TMessage);
begin
  ModalResult := mrOk;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.01.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragPosVerpackenForm.AbortMessage (var Message: TMessage);
begin
  ModalResult := mrAbort;
end;

procedure TAuftragPosVerpackenForm.Zellekopieren1Click(Sender: TObject);
begin
  if fSelectedColumnIndex = -1 then
  begin
    ClipBoard.AsText := VerpackAufPosListView.Items[VerpackAufPosListView.ItemIndex].Caption;
  end
  else
  begin
    ClipBoard.AsText := VerpackAufPosListView.Items[VerpackAufPosListView.ItemIndex].SubItems.Strings[fSelectedColumnIndex];
  end;
end;

end.
