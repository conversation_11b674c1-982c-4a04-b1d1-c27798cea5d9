object EditPackplatzForm: TEditPackplatzForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Packplatz bearbeiten'
  ClientHeight = 576
  ClientWidth = 522
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    522
    576)
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 112
    Width = 27
    Height = 13
    Caption = 'Name'
  end
  object Label2: TLabel
    Left = 8
    Top = 8
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Bevel1: TBevel
    Left = 6
    Top = 100
    Width = 510
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 334
  end
  object Label3: TLabel
    Left = 8
    Top = 160
    Width = 64
    Height = 13
    Caption = 'Beschreibung'
  end
  object Label4: TLabel
    Left = 8
    Top = 56
    Width = 56
    Height = 13
    Caption = 'WA-Bereich'
  end
  object NameEdit: TEdit
    Left = 8
    Top = 128
    Width = 506
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 2
    Text = 'NameEdit'
  end
  object OkButton: TButton
    Left = 351
    Top = 543
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    ModalResult = 1
    TabOrder = 5
  end
  object AbortButton: TButton
    Left = 439
    Top = 543
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 6
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 27
    Width = 506
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 0
    OnChange = LagerComboBoxChange
  end
  object DescEdit: TEdit
    Left = 8
    Top = 176
    Width = 506
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 3
    Text = 'DescEdit'
  end
  object WAComboBox: TComboBoxPro
    Left = 8
    Top = 72
    Width = 506
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 1
    OnChange = WAComboBoxChange
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 224
    Width = 506
    Height = 313
    ActivePage = TabSheet2
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 4
    object TabSheet1: TTabSheet
      Caption = 'Optionen'
      DesignSize = (
        498
        285)
      object Bevel2: TBevel
        Left = 3
        Top = 61
        Width = 492
        Height = 8
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object Bevel3: TBevel
        Left = 3
        Top = 195
        Width = 492
        Height = 8
        Anchors = [akLeft, akRight, akBottom]
        Shape = bsTopLine
        ExplicitTop = 149
      end
      object Bevel4: TBevel
        Left = 3
        Top = 254
        Width = 492
        Height = 8
        Anchors = [akLeft, akRight, akBottom]
        Shape = bsTopLine
        ExplicitTop = 208
      end
      object Label8: TLabel
        Left = 280
        Top = 207
        Width = 138
        Height = 13
        Anchors = [akLeft, akRight, akBottom]
        Caption = 'Lagerplatz f'#252'r das Packmittel'
        ExplicitTop = 161
      end
      object SperrgutCheckBox: TCheckBox
        Left = 8
        Top = 16
        Width = 297
        Height = 17
        Caption = 'F'#252'r Sperrgut geeignet'
        TabOrder = 0
      end
      object SingleCheckBox: TCheckBox
        Left = 8
        Top = 38
        Width = 297
        Height = 17
        Caption = 'Nur Einzelsendungen verpacken'
        TabOrder = 1
      end
      object ClearingCheckBox: TCheckBox
        Left = 8
        Top = 68
        Width = 297
        Height = 17
        Caption = 'Kl'#228'rf'#228'lle bearbeiten'
        TabOrder = 2
      end
      object ErrorConfirmedCheckBox: TCheckBox
        Left = 8
        Top = 261
        Width = 297
        Height = 17
        Anchors = [akLeft, akBottom]
        Caption = 'Fehlermeldungen m'#252'ssen best'#228'tigt werden'
        TabOrder = 10
      end
      object ArtikelStartCheckBox: TCheckBox
        Left = 8
        Top = 151
        Width = 473
        Height = 16
        Caption = 'Verpacken kann durch scannen eines Artikel begonnen werden'
        TabOrder = 5
      end
      object LEChangeConfirmeCheckBox: TCheckBox
        Left = 8
        Top = 120
        Width = 473
        Height = 17
        Caption = 'Best'#228'tigung bei LE wechsel'
        TabOrder = 4
      end
      object LTBestandCheckBox: TCheckBox
        Left = 8
        Top = 206
        Width = 241
        Height = 17
        Anchors = [akLeft, akBottom]
        Caption = 'Packmittelbestand am Platz verwalten'
        TabOrder = 7
      end
      object LTNachschubCheckBox: TCheckBox
        Left = 8
        Top = 229
        Width = 241
        Height = 17
        Anchors = [akLeft, akBottom]
        Caption = 'Nachschub f'#252'r Packmittelbestand'
        TabOrder = 8
      end
      object LTPlatzComboBox: TComboBoxPro
        Left = 280
        Top = 223
        Width = 215
        Height = 22
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akRight, akBottom]
        TabOrder = 9
      end
      object LTStartCheckBox: TCheckBox
        Left = 8
        Top = 172
        Width = 473
        Height = 17
        Caption = 'Verpacken kann durch Scannen einer LE begonnen werden'
        TabOrder = 6
      end
      object NewLEAllowedCheckBox: TCheckBox
        Left = 8
        Top = 96
        Width = 473
        Height = 18
        Caption = 'Neue LE kann per Scanning aufgenommen werden'
        TabOrder = 3
      end
    end
    object TabSheet5: TTabSheet
      Caption = 'Verpacken'
      ImageIndex = 5
      DesignSize = (
        498
        285)
      object Bevel5: TBevel
        Left = 3
        Top = 61
        Width = 492
        Height = 8
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object Bevel6: TBevel
        Left = 3
        Top = 141
        Width = 492
        Height = 8
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object SelectVPECheckBox: TCheckBox
        Left = 8
        Top = 38
        Width = 433
        Height = 17
        Caption = 'Manuelle Auswahl von VPEs zul'#228'ssig'
        TabOrder = 1
      end
      object SelPackmittelCheckBox: TCheckBox
        Left = 8
        Top = 146
        Width = 433
        Height = 17
        Caption = 'Packmittel muss ausgew'#228'hlt werden'
        TabOrder = 5
      end
      object NoFehlPackCheckBox: TCheckBox
        Left = 8
        Top = 70
        Width = 433
        Height = 17
        Caption = 'Nur vollst'#228'ndig kommissionierte Auftr'#228'ge verpacken'
        TabOrder = 2
      end
      object FullPackCheckBox: TCheckBox
        Left = 8
        Top = 92
        Width = 433
        Height = 17
        Caption = 'Verpackungsdialog erst schlie'#223'en, wenn alles verpackt ist'
        TabOrder = 3
      end
      object PackAutoCloseCheckBox: TCheckBox
        Left = 8
        Top = 114
        Width = 433
        Height = 17
        Caption = 'Verpackung automatisch abschlie'#223'en'
        TabOrder = 4
      end
      object GroupPosCheckBox: TCheckBox
        Left = 8
        Top = 16
        Width = 433
        Height = 17
        Caption = 'Positionen artikelweise gruppieren'
        TabOrder = 0
      end
      object DimPackmittelCheckBox: TCheckBox
        Left = 8
        Top = 168
        Width = 433
        Height = 17
        Caption = 'Abmessungen der Packmittel anzeigen'
        TabOrder = 6
      end
    end
    object BatchTabSheet: TTabSheet
      Caption = 'Batchverteilung'
      ImageIndex = 4
      DesignSize = (
        498
        285)
      object Label7: TLabel
        Left = 8
        Top = 64
        Width = 96
        Height = 13
        Caption = 'Put to Light Anzeige'
      end
      object PtoLComboBox: TComboBoxPro
        Left = 8
        Top = 80
        Width = 483
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 1
      end
      object BatchVerteilCheckBox: TCheckBox
        Left = 8
        Top = 16
        Width = 483
        Height = 17
        Anchors = [akLeft, akTop, akRight]
        Caption = 'An diesem Packplatz ist eine Batchverteilung m'#246'glich'
        TabOrder = 0
      end
      object UseVertLTCheckBox: TCheckBox
        Left = 8
        Top = 144
        Width = 473
        Height = 17
        Caption = 'Verteilbeh'#228'lter nutzen'
        TabOrder = 2
      end
    end
    object TabSheet2: TTabSheet
      Caption = 'W'#228'hlbare Speditionen'
      ImageIndex = 1
      object SelectSpedCheckListBox: TCheckListBox
        Left = 0
        Top = 0
        Width = 498
        Height = 285
        Align = alClient
        ItemHeight = 13
        Style = lbOwnerDrawFixed
        TabOrder = 0
        OnDrawItem = SelectSpedCheckListBoxDrawItem
      end
    end
    object TabSheet3: TTabSheet
      Caption = 'Zul'#228'ssige Speditionen'
      ImageIndex = 2
      object PackSpedCheckListBox: TCheckListBox
        Left = 0
        Top = 0
        Width = 498
        Height = 285
        Align = alClient
        ItemHeight = 13
        Style = lbOwnerDrawFixed
        TabOrder = 0
        OnDrawItem = SelectSpedCheckListBoxDrawItem
      end
    end
    object TabSheet4: TTabSheet
      Caption = 'L'#228'nder'
      ImageIndex = 3
      DesignSize = (
        498
        285)
      object Label5: TLabel
        Left = 8
        Top = 8
        Width = 80
        Height = 13
        Caption = 'Zul'#228'ssige L'#228'nder'
      end
      object Label6: TLabel
        Left = 8
        Top = 64
        Width = 118
        Height = 13
        Caption = 'Ausgeschlossene L'#228'nder'
      end
      object IncCountryEdit: TEdit
        Left = 8
        Top = 24
        Width = 481
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 0
        Text = 'IncCountryEdit'
        OnKeyPress = CountryEditKeyPress
      end
      object ExcCountryEdit: TEdit
        Left = 8
        Top = 80
        Width = 481
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 1
        Text = 'ExcCountryEdit'
        OnKeyPress = CountryEditKeyPress
      end
    end
  end
end
