object ACOListEditForm: TACOListEditForm
  Left = 275
  Top = 261
  Caption = 'ACOListForm'
  ClientHeight = 386
  ClientWidth = 1310
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Menu = MainMenu1
  OldCreateOrder = False
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    1310
    386)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 40
    Height = 13
    Caption = 'Formular'
  end
  object Label2: TLabel
    Left = 8
    Top = 56
    Width = 44
    Height = 13
    Caption = 'Elemente'
  end
  object Label3: TLabel
    Left = 408
    Top = 8
    Width = 75
    Height = 13
    Caption = 'AccessControlls'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 344
    Width = 1144
    Height = 10
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object OkButton: TButton
    Left = 917
    Top = 357
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 0
    OnClick = OkButtonClick
  end
  object FormComboBox: TComboBox
    Left = 8
    Top = 24
    Width = 345
    Height = 21
    ItemHeight = 0
    TabOrder = 1
    Text = 'FormComboBox'
    OnChange = FormComboBoxChange
  end
  object CompListBox: TListBox
    Left = 8
    Top = 73
    Width = 345
    Height = 265
    Style = lbOwnerDrawFixed
    Anchors = [akLeft, akTop, akBottom]
    ItemHeight = 13
    Sorted = True
    TabOrder = 2
    OnClick = CompListBoxClick
    OnDrawItem = CompListBoxDrawItem
  end
  object ACOStringGrid: TStringGrid
    Left = 406
    Top = 24
    Width = 900
    Height = 314
    Anchors = [akLeft, akTop, akRight, akBottom]
    ColCount = 7
    DefaultColWidth = 20
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goEditing]
    TabOrder = 3
    OnSelectCell = FieldStringGridSelectCell
    OnSetEditText = ACOStringGridSetEditText
    ColWidths = (
      20
      60
      102
      93
      134
      200
      150)
    RowHeights = (
      24
      24
      24
      24
      24)
  end
  object AddACOButton: TButton
    Left = 359
    Top = 104
    Width = 41
    Height = 25
    Caption = '>>'
    TabOrder = 4
    OnClick = AddACOButtonClick
  end
  object Button2: TButton
    Left = 359
    Top = 152
    Width = 41
    Height = 25
    Caption = '<<'
    TabOrder = 5
  end
  object Button3: TButton
    Left = 1077
    Top = 357
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    TabOrder = 6
    OnClick = Button3Click
  end
  object AbortButton: TButton
    Left = 997
    Top = 357
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 7
  end
  object AddFormACOButton: TButton
    Left = 359
    Top = 22
    Width = 41
    Height = 25
    Caption = '>>'
    TabOrder = 8
    OnClick = AddFormACOButtonClick
  end
  object MainMenu1: TMainMenu
    Left = 240
    Top = 16
    object Close1: TMenuItem
      Caption = 'Datei'
      object Schliessen1: TMenuItem
        Caption = 'Schliessen'
        OnClick = Schliessen1Click
      end
    end
    object Einfgen1: TMenuItem
      Caption = 'Einf'#252'gen'
      object ACOEintrag1: TMenuItem
        Caption = 'ACO-Eintrag'
        OnClick = ACOEintrag1Click
      end
    end
  end
end
