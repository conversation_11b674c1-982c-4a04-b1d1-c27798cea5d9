object BestellVereinnahmungForm: TBestellVereinnahmungForm
  Left = 430
  Top = 240
  BorderIcons = [biSystemMenu]
  Caption = 'BestellVereinnahmungForm'
  ClientHeight = 812
  ClientWidth = 925
  Color = clBtnFace
  Constraints.MinHeight = 600
  Constraints.MinWidth = 500
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poMainFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnKeyDown = FormKeyDown
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  TextHeight = 13
  object DatenPanel: TPanel
    Left = 0
    Top = 0
    Width = 925
    Height = 81
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    OnResize = DatenPanelResize
    DesignSize = (
      925
      81)
    object Label6: TLabel
      Left = 16
      Top = 11
      Width = 19
      Height = 13
      Caption = 'Von'
    end
    object Label7: TLabel
      Left = 152
      Top = 11
      Width = 14
      Height = 13
      Caption = 'Bis'
    end
    object Bevel1: TBevel
      Left = 6
      Top = 36
      Width = 913
      Height = 8
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label2: TLabel
      Left = 16
      Top = 44
      Width = 44
      Height = 13
      Caption = 'Lieferant:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
    end
    object Label1: TLabel
      Left = 348
      Top = 44
      Width = 52
      Height = 13
      Caption = 'Bestellung:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
    end
    object Label3: TLabel
      Left = 728
      Top = 44
      Width = 60
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'Lieferschein:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
      ExplicitLeft = 577
    end
    object Label5: TLabel
      Left = 728
      Top = 11
      Width = 38
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'Anzeige'
      ExplicitLeft = 540
    end
    object Label14: TLabel
      Left = 16
      Top = 63
      Width = 56
      Height = 13
      Caption = 'Retoure f'#252'r:'
    end
    object LSNrLable: TLabel
      Left = 794
      Top = 44
      Width = 60
      Height = 13
      Caption = 'LSNrLable'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object BestellNrLabel: TLabel
      Left = 410
      Top = 44
      Width = 83
      Height = 13
      Caption = 'BestellNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LieferantQImage: TImage
      Left = 310
      Top = 43
      Width = 32
      Height = 32
      Transparent = True
    end
    object Label16: TLabel
      Left = 348
      Top = 63
      Width = 37
      Height = 13
      Caption = 'Hinweis'
    end
    object BestellHintLabel: TLabel
      Left = 410
      Top = 63
      Width = 93
      Height = 13
      Caption = 'BestellHintLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label19: TLabel
      Left = 728
      Top = 63
      Width = 60
      Height = 13
      Anchors = [akLeft, akTop, akRight]
      Caption = 'WE-Bereich:'
    end
    object WEBereichLabel: TLabel
      Left = 794
      Top = 63
      Width = 95
      Height = 13
      Caption = 'WEBereichLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object VonDateTimePicker: TDateTimePicker
      Left = 40
      Top = 7
      Width = 89
      Height = 21
      Date = 38180.000000000000000000
      Time = 0.896834571758518000
      TabOrder = 0
      OnChange = ChangeDateRage
    end
    object BisDateTimePicker: TDateTimePicker
      Left = 176
      Top = 7
      Width = 89
      Height = 21
      Date = 38180.000000000000000000
      Time = 0.896923368061834500
      TabOrder = 1
      OnChange = ChangeDateRage
    end
    object StaticText1: TStaticText
      Left = 81
      Top = 44
      Width = 69
      Height = 17
      BevelInner = bvLowered
      BevelOuter = bvRaised
      Caption = 'StaticText1'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 3
    end
    object StatComboBox: TComboBox
      Left = 772
      Top = 8
      Width = 145
      Height = 21
      Style = csDropDownList
      Anchors = [akTop, akRight]
      ItemIndex = 1
      TabOrder = 2
      Text = 'Offene Positionen'
      OnChange = StatComboBoxChange
      Items.Strings = (
        'Alle Positionen'
        'Offene Positionen'
        'Erfasste Positionen')
    end
    object RetInfoStaticText: TStaticText
      Left = 81
      Top = 63
      Width = 104
      Height = 17
      BevelInner = bvLowered
      BevelOuter = bvRaised
      Caption = 'RetInfoStaticText'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 4
    end
  end
  object EingabePanel: TPanel
    Left = 0
    Top = 326
    Width = 925
    Height = 431
    Align = alBottom
    BevelOuter = bvNone
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = []
    ParentBackground = False
    ParentFont = False
    TabOrder = 6
    OnResize = EingabePanelResize
    DesignSize = (
      925
      431)
    object Label4: TLabel
      Left = 385
      Top = 7
      Width = 328
      Height = 13
      Caption = 
        'Alle erfassten Positionen f'#252'r diesen Artikel im aktuellen Warene' +
        'ingang'
    end
    object Label20: TLabel
      Left = 242
      Top = 7
      Width = 116
      Height = 13
      Caption = 'Aktuell erfasstes Leergut'
    end
    object GroupBox1: TGroupBox
      AlignWithMargins = True
      Left = 3
      Top = 3
      Width = 233
      Height = 425
      Align = alLeft
      Caption = 'Position erfassen '
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      DesignSize = (
        233
        425)
      object BesUeberButton: TButton
        Left = 147
        Top = 397
        Width = 80
        Height = 25
        Anchors = [akLeft, akBottom]
        Caption = #220'bernehmen'
        TabOrder = 9
        OnClick = BesUeberButtonClick
      end
      object BesClearButton: TButton
        Left = 63
        Top = 397
        Width = 75
        Height = 25
        Anchors = [akLeft, akBottom]
        Caption = 'Verwerfen'
        TabOrder = 10
        OnClick = BesClearButtonClick
      end
      object EditDataPanel: TPanel
        Left = 2
        Top = 123
        Width = 229
        Height = 140
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 5
        object Label8: TLabel
          Left = 4
          Top = 5
          Width = 33
          Height = 13
          Caption = 'Menge'
        end
        object Label9: TLabel
          Left = 4
          Top = 30
          Width = 39
          Height = 13
          Caption = 'Gewicht'
        end
        object Label12: TLabel
          Left = 165
          Top = 30
          Width = 12
          Height = 13
          Caption = 'kg'
        end
        object MHDLabel: TLabel
          Left = 4
          Top = 55
          Width = 25
          Height = 13
          Caption = 'MHD'
        end
        object Label10: TLabel
          Left = 4
          Top = 80
          Width = 34
          Height = 13
          Caption = 'Charge'
        end
        object EinheitLabel: TLabel
          Left = 166
          Top = 5
          Width = 59
          Height = 17
          AutoSize = False
          Caption = 'EinheitLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'MS Sans Serif'
          Font.Style = []
          ParentFont = False
        end
        object ChargeDutyLabel: TLabel
          Left = 55
          Top = 76
          Width = 11
          Height = 23
          Caption = '*'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Symbol'
          Font.Style = [fsBold]
          ParentFont = False
          Visible = False
        end
        object MHDDutyLabel: TLabel
          Left = 55
          Top = 50
          Width = 11
          Height = 23
          Caption = '*'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Symbol'
          Font.Style = [fsBold]
          ParentFont = False
          Visible = False
        end
        object GewichtDutyLabel: TLabel
          Left = 55
          Top = 25
          Width = 11
          Height = 23
          Caption = '*'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Symbol'
          Font.Style = [fsBold]
          ParentFont = False
          Visible = False
        end
        object Label23: TLabel
          Left = 55
          Top = 0
          Width = 11
          Height = 23
          Caption = '*'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Symbol'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object MengeEdit: TEdit
          Left = 68
          Top = 2
          Width = 73
          Height = 21
          TabOrder = 0
          Text = '0'
          OnChange = PosEditChange
          OnExit = MengeEditExit
          OnKeyPress = MengeEditKeyPress
        end
        object MengeUpDown: TIntegerUpDown
          Left = 141
          Top = 2
          Width = 16
          Height = 21
          Associate = MengeEdit
          Max = 99999
          TabOrder = 1
          OnChangingEx = MengeUpDownChangingEx
        end
        object GewichtEdit: TEdit
          Left = 68
          Top = 27
          Width = 89
          Height = 21
          TabOrder = 2
          Text = 'GewichtEdit'
          OnChange = PosEditChange
          OnExit = GewichtEditExit
          OnKeyPress = GewichtEditKeyPress
        end
        object MHDEdit: TEdit
          Left = 68
          Top = 52
          Width = 89
          Height = 21
          TabOrder = 3
          Text = 'MHDEdit'
          OnChange = PosEditChange
          OnExit = MHDEditExit
          OnKeyPress = MHDEditKeyPress
        end
        object ChargeEdit: TEdit
          Left = 68
          Top = 77
          Width = 89
          Height = 21
          MaxLength = 32
          TabOrder = 4
          Text = 'ChargeEdit'
          OnChange = PosEditChange
          OnExit = ChargeEditExit
        end
        object SerialPanel: TPanel
          Left = 0
          Top = 101
          Width = 229
          Height = 39
          Align = alBottom
          BevelOuter = bvNone
          TabOrder = 5
          object Label25: TLabel
            Left = 4
            Top = 9
            Width = 42
            Height = 13
            Caption = 'Seriennr.'
          end
          object SerialDutyLabel: TLabel
            Left = 55
            Top = 5
            Width = 11
            Height = 23
            Caption = '*'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -19
            Font.Name = 'Symbol'
            Font.Style = [fsBold]
            ParentFont = False
            Visible = False
          end
          object SerialEdit: TEdit
            Left = 68
            Top = 6
            Width = 157
            Height = 21
            MaxLength = 32
            TabOrder = 0
            Text = 'SerialEdit'
            OnChange = PosEditChange
            OnExit = ChargeEditExit
          end
        end
      end
      object Panel3: TPanel
        Left = 2
        Top = 15
        Width = 229
        Height = 8
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 0
      end
      object EditLTPanel: TPanel
        Left = 2
        Top = 23
        Width = 229
        Height = 25
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 1
        object Label13: TLabel
          Left = 4
          Top = 3
          Width = 30
          Height = 13
          Caption = 'LT-Nr.'
        end
        object LTEdit: TEdit
          Left = 68
          Top = 0
          Width = 141
          Height = 21
          MaxLength = 32
          TabOrder = 0
          Text = 'LTEdit'
          OnKeyPress = EditKeyPress
        end
      end
      object WiegeIDPanel: TPanel
        Left = 2
        Top = 98
        Width = 229
        Height = 25
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 4
        object Label15: TLabel
          Left = 4
          Top = 3
          Width = 45
          Height = 13
          Caption = 'Wiege-ID'
        end
        object WiegeIDEdit: TEdit
          Left = 68
          Top = 0
          Width = 141
          Height = 21
          MaxLength = 32
          TabOrder = 0
          Text = 'WiegeIDEdit'
          OnKeyPress = EditKeyPress
        end
      end
      object GrundPanel: TPanel
        Left = 2
        Top = 313
        Width = 229
        Height = 25
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 7
        DesignSize = (
          229
          25)
        object GrundLabel: TLabel
          Left = 4
          Top = 7
          Width = 29
          Height = 13
          Anchors = [akLeft, akBottom]
          Caption = 'Grund'
        end
        object GrundComboBox: TComboBox
          Left = 68
          Top = 4
          Width = 157
          Height = 21
          Style = csDropDownList
          Anchors = [akLeft, akBottom]
          MaxLength = 64
          TabOrder = 0
        end
      end
      object NVEPanel: TPanel
        Left = 2
        Top = 73
        Width = 229
        Height = 25
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 3
        object LTTypLabel: TLabel
          Left = 4
          Top = 3
          Width = 22
          Height = 13
          Caption = 'NVE'
        end
        object NVEEdit: TEdit
          Left = 68
          Top = 0
          Width = 141
          Height = 21
          MaxLength = 18
          TabOrder = 0
          Text = 'NVEEdit'
          OnChange = PosEditChange
          OnExit = NVEEditExit
          OnKeyPress = NVEEditKeyPress
        end
      end
      object ArEinheitOptPanel: TPanel
        Left = 2
        Top = 338
        Width = 229
        Height = 41
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 8
        object BigItemCheckBox: TCheckBox
          Left = 6
          Top = 24
          Width = 219
          Height = 17
          Caption = 'Der Artikel ist ein Big item'
          TabOrder = 0
        end
        object SperrgutCheckBox: TCheckBox
          Left = 6
          Top = 6
          Width = 219
          Height = 17
          Caption = 'Der Artikel ist Sperrgut'
          TabOrder = 1
        end
      end
      object PalHeightPanel: TPanel
        Left = 2
        Top = 263
        Width = 229
        Height = 25
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 6
        DesignSize = (
          229
          25)
        object Label21: TLabel
          Left = 4
          Top = 7
          Width = 44
          Height = 13
          Anchors = [akLeft, akBottom]
          Caption = 'Pal-H'#246'he'
        end
        object Label22: TLabel
          Left = 165
          Top = 6
          Width = 16
          Height = 13
          Caption = 'mm'
        end
        object PalHeightEdit: TEdit
          Left = 68
          Top = 2
          Width = 89
          Height = 21
          TabOrder = 0
          Text = '0'
          OnKeyPress = PalHeightEditKeyPress
        end
      end
      object HUPanel: TPanel
        Left = 2
        Top = 48
        Width = 229
        Height = 25
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 2
        object Label24: TLabel
          Left = 4
          Top = 3
          Width = 33
          Height = 13
          Caption = 'HU-Nr.'
        end
        object HUNrEdit: TEdit
          Left = 68
          Top = 0
          Width = 141
          Height = 21
          MaxLength = 18
          TabOrder = 0
          Text = 'HUNrEdit'
          OnChange = PosEditChange
          OnExit = NVEEditExit
          OnKeyPress = NVEEditKeyPress
        end
      end
      object CategoryPanel: TPanel
        Left = 2
        Top = 288
        Width = 229
        Height = 25
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 11
        Visible = False
        DesignSize = (
          229
          25)
        object CategoryLabel: TLabel
          Left = 4
          Top = 7
          Width = 45
          Height = 13
          Anchors = [akLeft, akBottom]
          Caption = 'Kategorie'
        end
        object CategoryComboBox: TComboBox
          Left = 68
          Top = 4
          Width = 157
          Height = 21
          Style = csDropDownList
          Anchors = [akLeft, akBottom]
          MaxLength = 64
          TabOrder = 0
        end
      end
    end
    object BestPosStringGrid: TStringGridPro
      AlignWithMargins = True
      Left = 385
      Top = 24
      Width = 341
      Height = 404
      Margins.Left = 8
      Margins.Top = 24
      Margins.Right = 8
      Align = alClient
      ColCount = 2
      DefaultColWidth = 20
      DefaultRowHeight = 18
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goColSizing, goRowSelect]
      ParentFont = False
      PopupMenu = BestPosStringGridPopupMenu
      TabOrder = 1
      GridStyle.OddColor = clInfoBk
      TitelTexte.Strings = (
        ''
        'Menge'
        'Gewicht')
      TitelFont.Charset = DEFAULT_CHARSET
      TitelFont.Color = clWindowText
      TitelFont.Height = -11
      TitelFont.Name = 'MS Sans Serif'
      TitelFont.Style = []
      ColWidths = (
        20
        66)
    end
    object ArtikelImagePanel: TPanel
      AlignWithMargins = True
      Left = 737
      Top = 24
      Width = 185
      Height = 404
      Margins.Top = 24
      Align = alRight
      BevelOuter = bvSpace
      Color = clWhite
      ParentBackground = False
      TabOrder = 2
      OnResize = ArtikelImagePanelResize
      object ArtikelImage: TImage32
        Left = 1
        Top = 1
        Width = 183
        Height = 402
        Align = alClient
        Bitmap.ResamplerClassName = 'TDraftResampler'
        BitmapAlign = baCenter
        Scale = 1.000000000000000000
        ScaleMode = smOptimal
        TabOrder = 0
      end
    end
    object LeerListBox: TListBox
      AlignWithMargins = True
      Left = 242
      Top = 24
      Width = 132
      Height = 371
      Margins.Top = 24
      Margins.Bottom = 36
      Align = alLeft
      BevelEdges = [beTop, beRight, beBottom]
      ItemHeight = 13
      TabOrder = 3
    end
    object ResetLeerButton: TButton
      Left = 242
      Top = 400
      Width = 132
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'Leergut zur'#252'cksetzen'
      TabOrder = 4
      OnClick = ResetLeerButtonClick
    end
  end
  object BestStringGrid: TStringGridPro
    AlignWithMargins = True
    Left = 8
    Top = 81
    Width = 909
    Height = 105
    Margins.Left = 8
    Margins.Top = 0
    Margins.Right = 8
    Margins.Bottom = 0
    Align = alClient
    ColCount = 10
    Constraints.MinHeight = 70
    DefaultColWidth = 20
    DefaultRowHeight = 18
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = []
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goColSizing, goRowSelect]
    ParentFont = False
    PopupMenu = ArtikelGridPopupMenu
    TabOrder = 1
    OnClick = BestStringGridClick
    OnDblClick = BestStringGridDblClick
    OnKeyPress = BestStringGridKeyPress
    OnBeforeSort = BestStringGridBeforeSort
    GridStyle.OddColor = clInfoBk
    TitelTexte.Strings = (
      ''
      'Artikel Nr.'
      'Artikel Text'
      'Ausf'#252'hrung'
      'Bestelldatum'
      'Bestellnr.'
      'Lieferdatum'
      'Menge Soll'
      'Einheit'
      'Menge Erfasst')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'MS Sans Serif'
    TitelFont.Style = []
  end
  object FehlerLabel: TPanel
    Left = 0
    Top = 186
    Width = 925
    Height = 24
    Align = alBottom
    BevelOuter = bvNone
    Caption = 'FehlerLabel'
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 2
  end
  object WELEPanel: TPanel
    Left = 0
    Top = 251
    Width = 925
    Height = 34
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      925
      34)
    object Label11: TLabel
      Left = 8
      Top = 13
      Width = 147
      Height = 13
      Caption = 'Sammel-LE f'#252'r die Einlagerung:'
    end
    object WELELabel: TLabel
      Left = 176
      Top = 6
      Width = 137
      Height = 29
      Caption = 'WELELabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -24
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      PopupMenu = WELEPopupMenu
    end
    object ChangeWELEButton: TButton
      Left = 748
      Top = 5
      Width = 169
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Neue Einlagerung-LE anlegen'
      TabOrder = 1
      OnClick = ChangeWELEButtonClick
    end
    object LELTComboBox: TComboBoxPro
      Left = 527
      Top = 7
      Width = 213
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akTop, akRight]
      ItemHeight = 15
      TabOrder = 0
      OnChange = LELTComboBoxChange
    end
  end
  object ButtonPanel: TPanel
    Left = 0
    Top = 757
    Width = 925
    Height = 55
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 7
    DesignSize = (
      925
      55)
    object EinlagerHintLabel: TLabel
      Left = 147
      Top = 6
      Width = 167
      Height = 24
      Caption = 'EinlagerHintLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -18
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object OkButton: TButton
      Left = 751
      Top = 4
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Ok'
      ModalResult = 1
      TabOrder = 1
    end
    object AbortButton: TButton
      Left = 842
      Top = 4
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 2
    end
    object StatusBar1: TStatusBar
      Left = 0
      Top = 36
      Width = 925
      Height = 19
      Panels = <
        item
          Width = 200
        end
        item
          Width = 200
        end
        item
          Width = 250
        end>
      PopupMenu = StatusPopupMenu
    end
    object EinlagerButton: TButton
      Left = 3
      Top = 6
      Width = 130
      Height = 25
      Caption = 'Einlagern'
      TabOrder = 0
      OnClick = EinlagerButtonClick
    end
  end
  object WELTPanel: TPanel
    Left = 0
    Top = 210
    Width = 925
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 4
    object Label17: TLabel
      Left = 10
      Top = 17
      Width = 84
      Height = 13
      Caption = 'Aktueller LE Type'
    end
    object LTComboBox: TComboBoxPro
      Left = 176
      Top = 14
      Width = 213
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 0
      OnChange = LELTComboBoxChange
    end
  end
  object EinlagerLBPanel: TPanel
    Left = 0
    Top = 285
    Width = 925
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      925
      41)
    object Label18: TLabel
      Left = 8
      Top = 16
      Width = 55
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Buchen auf'
    end
    object BereichComboBox: TComboBoxPro
      Left = 94
      Top = 13
      Width = 295
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akBottom]
      TabOrder = 0
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 412
    Top = 572
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 332
    Top = 572
  end
  object ArtikelGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = ArtikelGridPopupMenuPopup
    Left = 200
    Top = 144
    object BestColCopyMenuItem: TMenuItem
      Caption = 'Kopieren'
      ImageIndex = 26
      OnClick = StrinGridColCopyMenuItemClick
    end
    object BestColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      ImageIndex = 27
      OnClick = BestColOptimalMenuItemClick
    end
    object N9: TMenuItem
      Caption = '-'
    end
    object NeuerArtikel1: TMenuItem
      Caption = 'Neuer Artikel...'
      ShortCut = 115
      OnClick = NeuerArtikel1Click
    end
    object N3: TMenuItem
      Caption = '-'
    end
    object Annahmeverweigern1: TMenuItem
      Caption = 'Annahme verweigern...'
      ImageIndex = 22
      OnClick = Annahmeverweigern1Click
    end
    object N11: TMenuItem
      Caption = '-'
    end
    object BesBulkMenuItem: TMenuItem
      Caption = 'Bulk Annahme...'
      ShortCut = 118
      OnClick = BesBulkMenuItemClick
    end
    object N12: TMenuItem
      Caption = '-'
    end
    object ShowArtikelPictureMenuItem: TMenuItem
      Caption = 'Artikelbild anzeigen...'
      ImageIndex = 24
      OnClick = ShowArtikelPictureMenuItemClick
    end
    object N6: TMenuItem
      Caption = '-'
    end
    object PrintBestEAN13MenuItem: TMenuItem
      Caption = 'EAN13 Label nachdrucken...'
      ImageIndex = 9
      OnClick = PrintBestEAN13MenuItemClick
    end
    object ChangeStammdatenMenuItem: TMenuItem
      Caption = 'Lagerstammdaten bearbeiten...'
      OnClick = ChangeStammdatenMenuItemClick
    end
    object TMenuItem
      Caption = '-'
    end
    object BestExcelExportMenuItem: TMenuItem
      Caption = 'Export nach Excel...'
      ImageIndex = 12
      OnClick = BestExcelExportMenuItemClick
    end
    object N8: TMenuItem
      Caption = '-'
    end
    object Suchen1: TMenuItem
      Caption = 'Suchen...'
      ImageIndex = 16
      ShortCut = 16454
      OnClick = Suchen1Click
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object SetArTextFilerMenuItem: TMenuItem
      Caption = 'Artikeltext filtern...'
      OnClick = SetArTextFilerMenuItemClick
    end
    object ResetArTextFilerMenuItem: TMenuItem
      Caption = 'Filter aufheben...'
      OnClick = ResetArTextFilerMenuItemClick
    end
    object N13: TMenuItem
      Caption = '-'
    end
    object Nichtsortieren1: TMenuItem
      Caption = 'Nicht sortieren'
      OnClick = Nichtsortieren1Click
    end
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 448
    Top = 576
  end
  object BestPosStringGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = BestPosStringGridPopupMenuPopup
    Left = 464
    Top = 384
    object WEPosColCopyMenuItem: TMenuItem
      Caption = 'Kopieren'
      ImageIndex = 26
      OnClick = WEPosColCopyMenuItemClick
    end
    object WEPosColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      ImageIndex = 27
      OnClick = WEPosColOptimalMenuItemClick
    end
    object N14: TMenuItem
      Caption = '-'
    end
    object ShowLTInhaltMenuItem: TMenuItem
      Caption = 'Alle Artikel auf diesem LT anzeigen...'
      ImageIndex = 15
      OnClick = ShowLTInhaltMenuItemClick
    end
    object N4: TMenuItem
      Caption = '-'
    end
    object WEPosPrintEAN13MenuItem: TMenuItem
      Caption = 'EAN13-Etiketten drucken...'
      OnClick = WEPosPrintEANMenuItemClick
    end
    object WEPosPrintEAN128MenuItem: TMenuItem
      Caption = 'EAN128-Etiketten drucken...'
      OnClick = WEPosPrintEANMenuItemClick
    end
    object N7: TMenuItem
      Caption = '-'
    end
    object WEPosPrintBestandMenuItem: TMenuItem
      Caption = 'Bestandlabel drucken'
      OnClick = WEPosPrintBestandMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object DelBestPosErfassenMenuItem: TMenuItem
      Caption = 'L'#246'schen...'
      ImageIndex = 23
      OnClick = DelBestPosErfassenMenuItemClick
    end
  end
  object WELEPopupMenu: TPopupMenu
    OnPopup = WELEPopupMenuPopup
    Left = 264
    Top = 224
    object ShowAktLTInhaltMenuItem: TMenuItem
      Caption = 'Inhalt der LE anzeigen...'
      OnClick = ShowAktLTInhaltMenuItemClick
    end
    object N5: TMenuItem
      Caption = '-'
    end
    object IncWELEFachNrMenuItem: TMenuItem
      Caption = 'Fachnr. erh'#246'hen'
      OnClick = WELEFachNrMenuItemClick
    end
    object DecWELEFachNrMenuItem: TMenuItem
      Caption = 'Fachnr. verringern'
      OnClick = WELEFachNrMenuItemClick
    end
  end
  object StatusPopupMenu: TPopupMenu
    OnPopup = StatusPopupMenuPopup
    Left = 528
    Top = 536
    object StatusAutoScanBuchOnMenuItem: TMenuItem
      Caption = 'Automatisch Scannerbuchung ein'
      OnClick = StatusMenuItemClick
    end
    object StatusAutoScanBuchOffMenuItem: TMenuItem
      Caption = 'Automatisch Scannerbuchung aus'
      OnClick = StatusMenuItemClick
    end
    object N10: TMenuItem
      Caption = '-'
    end
    object StatusAutoCloseWELEOnMenuItem: TMenuItem
      Caption = 'WE-LE nach Buchen schlie'#223'en ein'
      OnClick = StatusMenuItemClick
    end
    object StatusAutoCloseWELEOffMenuItem: TMenuItem
      Caption = 'WE-LE nach Buchen schlie'#223'en aus'
      OnClick = StatusMenuItemClick
    end
  end
end
