object EditArtikelSetForm: TEditArtikelSetForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Artikelset Daten '#228'ndern'
  ClientHeight = 383
  ClientWidth = 555
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  DesignSize = (
    555
    383)
  PixelsPerInch = 96
  TextHeight = 13
  object Name: TLabel
    Left = 8
    Top = 8
    Width = 27
    Height = 13
    Caption = 'Name'
  end
  object Label2: TLabel
    Left = 8
    Top = 58
    Width = 64
    Height = 13
    Caption = 'Beschreibung'
  end
  object Label1: TLabel
    Left = 8
    Top = 233
    Width = 36
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Hinweis'
    ExplicitTop = 172
  end
  object Label3: TLabel
    Left = 400
    Top = 8
    Width = 60
    Height = 13
    Caption = 'Kennzeichen'
  end
  object NameEdit: TEdit
    Left = 8
    Top = 24
    Width = 377
    Height = 21
    MaxLength = 64
    TabOrder = 0
    Text = 'NameEdit'
  end
  object DescEdit: TEdit
    Left = 8
    Top = 74
    Width = 539
    Height = 21
    MaxLength = 200
    TabOrder = 2
    Text = 'DescEdit'
  end
  object HintMemo: TMemo
    Left = 8
    Top = 249
    Width = 539
    Height = 89
    Anchors = [akLeft, akRight, akBottom]
    Lines.Strings = (
      'HintMemo')
    MaxLength = 256
    TabOrder = 7
  end
  object OkButton: TButton
    Left = 384
    Top = 350
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 8
  end
  object AbortButton: TButton
    Left = 472
    Top = 350
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 9
  end
  object ManfSetListCheckBox: TCheckBox
    Left = 8
    Top = 109
    Width = 193
    Height = 17
    Caption = 'Produktions-St'#252'ckliste'
    TabOrder = 3
  end
  object SetDissolveCheckBox: TCheckBox
    Left = 8
    Top = 132
    Width = 193
    Height = 17
    Caption = 'Set bei Auftrags'#252'bernahme aufl'#246'sen'
    TabOrder = 4
  end
  object VorKommCheckBox: TCheckBox
    Left = 8
    Top = 155
    Width = 305
    Height = 17
    Caption = 'Vorkommissionier Planung'
    TabOrder = 5
  end
  object SetCompactCheckBox: TCheckBox
    Left = 8
    Top = 178
    Width = 305
    Height = 17
    Caption = 'Set Verdichtung bei Auftrags'#252'bernahme'
    TabOrder = 6
  end
  object MarkEdit: TEdit
    Left = 400
    Top = 24
    Width = 147
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 32
    TabOrder = 1
    Text = 'MarkEdit'
  end
  object KommNoSplitCheckBox: TCheckBox
    Left = 8
    Top = 201
    Width = 305
    Height = 17
    Caption = 'F'#252'r die Kommissionierung nicht aufl'#246'sen'
    TabOrder = 10
  end
end
