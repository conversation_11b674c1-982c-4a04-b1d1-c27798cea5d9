object CompTranslateEditForm: TCompTranslateEditForm
  Left = 235
  Top = 188
  Caption = 'CompTranslateEditForm'
  ClientHeight = 633
  ClientWidth = 691
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Menu = MainMenu1
  OnActivate = FormActivate
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    691
    633)
  TextHeight = 13
  object CountLabel: TLabel
    Left = 8
    Top = 607
    Width = 54
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'CountLabel'
    ExplicitTop = 580
  end
  object Label1: TLabel
    Left = 8
    Top = 45
    Width = 49
    Height = 13
    Caption = 'Eintrag-Art'
  end
  object Label2: TLabel
    Left = 272
    Top = 45
    Width = 40
    Height = 13
    Caption = 'Formular'
  end
  object FieldStringGrid: TStringGrid
    Left = 8
    Top = 96
    Width = 679
    Height = 501
    Anchors = [akLeft, akTop, akRight, akBottom]
    ColCount = 4
    DefaultColWidth = 20
    DefaultRowHeight = 20
    Font.Charset = EASTEUROPE_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = []
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goEditing, goTabs]
    ParentFont = False
    PopupMenu = GridPopupMenu
    TabOrder = 4
    OnMouseUp = FieldStringGridMouseUp
    OnSelectCell = FieldStringGridSelectCell
    OnSetEditText = FieldStringGridSetEditText
    ExplicitWidth = 673
    ExplicitHeight = 474
    ColWidths = (
      20
      196
      59
      39)
  end
  object OkButton: TButton
    Left = 523
    Top = 603
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    ModalResult = 1
    TabOrder = 6
    ExplicitLeft = 517
    ExplicitTop = 576
  end
  object ScanButton: TButton
    Left = 8
    Top = 8
    Width = 75
    Height = 25
    Caption = 'Durchsuchen'
    TabOrder = 0
    OnClick = ScanButtonClick
  end
  object AbortButton: TButton
    Left = 608
    Top = 603
    Width = 79
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 7
    ExplicitLeft = 602
    ExplicitTop = 576
  end
  object DeleteButton: TButton
    Left = 170
    Top = 8
    Width = 75
    Height = 25
    Caption = 'L'#246'schen'
    TabOrder = 2
    OnClick = DeleteButtonClick
  end
  object RenameButton: TButton
    Left = 89
    Top = 8
    Width = 75
    Height = 25
    Caption = 'Umbenennen'
    TabOrder = 1
    OnClick = RenameButtonClick
  end
  object FilterComboBox: TComboBox
    Left = 8
    Top = 61
    Width = 237
    Height = 21
    TabOrder = 3
    Text = 'FilterComboBox'
    OnChange = FilterComboBoxChange
  end
  object FormComboBox: TComboBox
    Left = 272
    Top = 61
    Width = 415
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 5
    Text = 'FormComboBox'
    OnChange = FilterComboBoxChange
    ExplicitWidth = 409
  end
  object MainMenu1: TMainMenu
    Left = 352
    Top = 360
    object Close1: TMenuItem
      Caption = 'Datei'
      object ffnen1: TMenuItem
        Caption = #214'ffnen ...'
      end
      object Sichern1: TMenuItem
        Caption = 'Sichern'
        ShortCut = 16467
        OnClick = Sichern1Click
      end
      object Sichernals1: TMenuItem
        Caption = 'Sichern als ...'
        OnClick = Sichernals1Click
      end
      object N2: TMenuItem
        Caption = '-'
      end
      object Import1: TMenuItem
        Caption = 'Import...'
        OnClick = Import1Click
      end
      object Export1: TMenuItem
        Caption = 'Export...'
        OnClick = Export1Click
      end
      object N6: TMenuItem
        Caption = '-'
      end
      object LangImportMenuItem: TMenuItem
        Caption = 'Sprache importieren'
        OnClick = LangImportMenuItemClick
      end
      object LangExportMenuItem: TMenuItem
        Caption = 'Sprache exportieren...'
        OnClick = LangExportMenuItemClick
      end
      object N1: TMenuItem
        Caption = '-'
      end
      object Schliessen1: TMenuItem
        Caption = 'Schlie'#223'en'
        OnClick = Schliessen1Click
      end
    end
    object Neu1: TMenuItem
      Caption = 'Neue Eintr'#228'ge'
      object Fixtext1: TMenuItem
        Caption = 'Fixtext...'
        OnClick = Button1Click
      end
      object ResourceText1: TMenuItem
        Caption = 'Resource Text'
        OnClick = ResourceText1Click
      end
      object MessageText1: TMenuItem
        Caption = 'Message Text'
        OnClick = MessageText1Click
      end
    end
    object Reorganisieren1: TMenuItem
      Caption = 'Reorganisieren'
      object Doppelteentfernen1: TMenuItem
        Caption = 'Doppelte entfernen'
        OnClick = Doppelteentfernen1Click
      end
    end
  end
  object OpenDialog1: TOpenDialog
    DefaultExt = '*.rc'
    Filter = 'Resource|*.rc'
    Left = 400
    Top = 328
  end
  object SaveDialog1: TSaveDialog
    DefaultExt = 'xml'
    Filter = 'XML|*.xml|CSV|*.csv|Alle Files|*.*'
    FilterIndex = 0
    Left = 448
    Top = 360
  end
  object GridPopupMenu: TPopupMenu
    OnPopup = GridPopupMenuPopup
    Left = 192
    Top = 176
    object SameEntryMenuItem: TMenuItem
      Caption = #196'hnlicher Eintrag anlegen...'
      OnClick = SameEntryMenuItemClick
    end
    object N4: TMenuItem
      Caption = '-'
    end
    object ChangeIndexMenuItem: TMenuItem
      Caption = 'Index '#228'ndern...'
      OnClick = ChangeIndexMenuItemClick
    end
    object N5: TMenuItem
      Caption = '-'
    end
    object Suchen1: TMenuItem
      Caption = 'Schl'#252'ssel suchen...'
      OnClick = Suchen1Click
    end
    object extsuchen1: TMenuItem
      Caption = 'Text suchen...'
      ShortCut = 16454
      OnClick = extsuchen1Click
    end
    object extweitersuchen1: TMenuItem
      Caption = 'Text weiter suchen...'
      ShortCut = 114
      OnClick = extweitersuchen1Click
    end
    object N3: TMenuItem
      Caption = '-'
    end
    object Einfgen1: TMenuItem
      Caption = 'Einf'#252'gen'
      ShortCut = 16470
      OnClick = Einfgen1Click
    end
  end
end
