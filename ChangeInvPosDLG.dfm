object ChangeInvPosForm: TChangeInvPosForm
  Left = 218
  Top = 132
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'ChangeInvPosForm'
  ClientHeight = 545
  ClientWidth = 966
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 497
    Width = 966
    Height = 48
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      966
      48)
    object CloseButton: TButton
      Left = 883
      Top = 12
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
  end
  object Panel3: TPanel
    Left = 0
    Top = 35
    Width = 966
    Height = 462
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 1
    object Label2: TLabel
      Left = 523
      Top = 3
      Width = 91
      Height = 13
      Caption = 'Inventur-Positionen'
    end
    object Splitter1: TSplitter
      Left = 465
      Top = 0
      Height = 462
      OnMoved = Splitter1Moved
      ExplicitLeft = 383
      ExplicitTop = 6
      ExplicitHeight = 333
    end
    object InvPosDBGrid: TDBGridPro
      AlignWithMargins = True
      Left = 523
      Top = 20
      Width = 435
      Height = 442
      Margins.Left = 0
      Margins.Top = 20
      Margins.Right = 8
      Margins.Bottom = 0
      Align = alClient
      DataSource = InvPosDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit, dgMultiSelect]
      ReadOnly = True
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      OnDragDrop = InvPosDBGridDragDrop
      OnDragOver = InvPosDBGridDragOver
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoCheckBoxSelect, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 23
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
      OnColumnSort = SortGridColumn
    end
    object PageControl1: TPageControl
      Left = 0
      Top = 0
      Width = 465
      Height = 462
      ActivePage = LPTabSheet
      Align = alLeft
      TabOrder = 0
      OnChange = PageControlEnter
      OnEnter = PageControlEnter
      object LPTabSheet: TTabSheet
        Caption = 'Lagerpl'#228'tze'
        OnShow = LPTabSheetShow
        DesignSize = (
          457
          434)
        object Label1: TLabel
          Left = 0
          Top = 0
          Width = 62
          Height = 13
          Caption = 'Lagerbereich'
        end
        object Label5: TLabel
          Left = 0
          Top = 42
          Width = 28
          Height = 13
          Caption = 'Reihe'
        end
        object Label6: TLabel
          Left = 333
          Top = 42
          Width = 118
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'Letzte Bestands-Inventur'
        end
        object Label7: TLabel
          Left = 191
          Top = 42
          Width = 97
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'Letzte Platz-Inventur'
        end
        object LPLBComboBox: TComboBoxPro
          Left = 0
          Top = 16
          Width = 455
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 120
          ItemHeight = 15
          TabOrder = 0
          OnChange = LPLBComboBoxChange
        end
        object LPDBGrid: TDBGridPro
          Left = 0
          Top = 176
          Width = 455
          Height = 256
          Anchors = [akLeft, akTop, akRight, akBottom]
          DataSource = LPDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit, dgMultiSelect]
          ReadOnly = True
          TabOrder = 8
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          OnDblClick = LPDBGridDblClick
          OnMouseDown = LPDBGridMouseDown
          OnMouseUp = LPDBGridMouseUp
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'MS Sans Serif'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoCheckBoxSelect, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\Scalabium'
          RegistrySection = 'SMDBGrid'
          WidthOfIndicator = 23
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
          OnColumnSort = SortGridColumn
        end
        object KommLPCheckBox: TCheckBox
          Left = 0
          Top = 131
          Width = 448
          Height = 17
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Nur Kommissionierpl'#228'tze ausw'#228'hlen'
          TabOrder = 6
          OnClick = KommLPCheckBoxClick
        end
        object LPReiheComboBox: TComboBoxPro
          Left = 0
          Top = 58
          Width = 137
          Height = 21
          Style = csDropDownList
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 120
          ItemHeight = 13
          TabOrder = 1
          OnChange = LPReiheComboBoxChange
        end
        object InvStockComboBox: TComboBox
          Left = 333
          Top = 58
          Width = 122
          Height = 21
          Style = csDropDownList
          Anchors = [akTop, akRight]
          ItemHeight = 13
          PopupMenu = InvDatePopupMenu
          TabOrder = 3
          OnChange = LPReiheComboBoxChange
          Items.Strings = (
            ''
            'Datum'
            'Noch nie'
            '> 7 Tage'
            '> 4 Wochen'
            '> 3 Monate'
            '> 6 Monate'
            '> 1 Jahr')
        end
        object InvCheckBox: TCheckBox
          Left = 0
          Top = 151
          Width = 448
          Height = 17
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Nur Pl'#228'tze, die keiner offenen Inventur enthalten'
          Checked = True
          State = cbChecked
          TabOrder = 7
          OnClick = KommLPCheckBoxClick
        end
        object LPSubMandCheckBox: TCheckBox
          Left = 0
          Top = 91
          Width = 448
          Height = 17
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Nur Pl'#228'tze, auf den Ware von Untermandanten liegt'
          TabOrder = 4
          OnClick = KommLPCheckBoxClick
        end
        object LPBelegtCheckBox: TCheckBox
          Left = 0
          Top = 111
          Width = 448
          Height = 17
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Nur belegte Pl'#228'tze'
          Enabled = False
          TabOrder = 5
          OnClick = KommLPCheckBoxClick
        end
        object InvPlaceComboBox: TComboBox
          Left = 191
          Top = 58
          Width = 122
          Height = 21
          Style = csDropDownList
          Anchors = [akTop, akRight]
          ItemHeight = 13
          PopupMenu = InvDatePopupMenu
          TabOrder = 2
          OnChange = LPReiheComboBoxChange
          Items.Strings = (
            ''
            'Datum'
            'Noch nie'
            '> 7 Tage'
            '> 4 Wochen'
            '> 3 Monate'
            '> 6 Monate'
            '> 1 Jahr')
        end
      end
      object ARTabSheet: TTabSheet
        Caption = 'Artikel'
        ImageIndex = 1
        OnShow = ARTabSheetShow
        DesignSize = (
          457
          434)
        object Label3: TLabel
          Left = 0
          Top = 0
          Width = 62
          Height = 13
          Caption = 'Lagerbereich'
        end
        object ArtDBGrid: TDBGridPro
          Left = 0
          Top = 71
          Width = 456
          Height = 361
          Anchors = [akLeft, akTop, akRight, akBottom]
          DataSource = ArtDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit, dgMultiSelect]
          ReadOnly = True
          TabOrder = 2
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          OnDblClick = ArtDBGridDblClick
          OnMouseDown = ArtDBGridMouseDown
          OnMouseUp = ArtDBGridMouseUp
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'MS Sans Serif'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoCheckBoxSelect, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\Scalabium'
          RegistrySection = 'SMDBGrid'
          WidthOfIndicator = 23
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
          OnColumnSort = SortGridColumn
        end
        object LBArComboBox: TComboBoxPro
          Left = 0
          Top = 16
          Width = 456
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 120
          ItemHeight = 15
          TabOrder = 0
          OnChange = LBArComboBoxChange
        end
        object ListedARCheckBox: TCheckBox
          Left = 0
          Top = 46
          Width = 305
          Height = 17
          Caption = 'Nur gelistet Artikel ausw'#228'hlen'
          Checked = True
          State = cbChecked
          TabOrder = 1
          OnClick = ListedARCheckBoxClick
        end
      end
      object LETabSheet: TTabSheet
        Caption = 'LEs'
        ImageIndex = 2
        OnShow = LETabSheetShow
        DesignSize = (
          457
          434)
        object Label4: TLabel
          Left = 0
          Top = 0
          Width = 62
          Height = 13
          Caption = 'Lagerbereich'
        end
        object LEDBGrid: TDBGridPro
          Left = -1
          Top = 71
          Width = 455
          Height = 361
          Anchors = [akLeft, akTop, akRight, akBottom]
          DataSource = LEDataSource
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit, dgMultiSelect]
          ReadOnly = True
          TabOrder = 2
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'MS Sans Serif'
          TitleFont.Style = []
          OnDblClick = LEDBGridDblClick
          OnMouseDown = LPDBGridMouseDown
          OnMouseUp = LPDBGridMouseUp
          Flat = False
          BandsFont.Charset = DEFAULT_CHARSET
          BandsFont.Color = clWindowText
          BandsFont.Height = -11
          BandsFont.Name = 'MS Sans Serif'
          BandsFont.Style = []
          Groupings = <>
          GridStyle.Style = gsCustom
          GridStyle.OddColor = clWindow
          GridStyle.EvenColor = clWindow
          TitleHeight.PixelCount = 24
          FooterColor = clBtnFace
          ExOptions = [eoCheckBoxSelect, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
          RegistryKey = 'Software\Scalabium'
          RegistrySection = 'SMDBGrid'
          WidthOfIndicator = 23
          DefaultRowHeight = 17
          ScrollBars = ssHorizontal
          ColCount = 2
          RowCount = 2
          OnColumnSort = SortGridColumn
        end
        object LELBComboBox: TComboBoxPro
          Left = 0
          Top = 16
          Width = 455
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 120
          ItemHeight = 15
          TabOrder = 0
          OnChange = LELBComboBoxChange
        end
        object LEBelegtCheckBox: TCheckBox
          Left = 0
          Top = 43
          Width = 448
          Height = 17
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Nur belegte LEs'
          TabOrder = 1
          OnClick = LEBelegtCheckBoxClick
        end
      end
    end
    object Panel2: TPanel
      Left = 468
      Top = 0
      Width = 55
      Height = 462
      Align = alLeft
      BevelOuter = bvNone
      TabOrder = 2
      object HinzufButton: TButton
        Left = 6
        Top = 248
        Width = 41
        Height = 25
        Hint = 'Alle ausgew'#228'hlten Eintr'#228'ge hinzuf'#252'gen'
        Caption = '>'
        TabOrder = 0
        OnClick = HinzufButtonClick
      end
      object EntfButton: TButton
        Left = 6
        Top = 280
        Width = 41
        Height = 25
        Caption = '<'
        TabOrder = 1
        OnClick = EntfButtonClick
      end
      object AllesHinzufButton: TButton
        Left = 6
        Top = 344
        Width = 41
        Height = 25
        Hint = 'Alle Eintr'#228'ge aus der Liste hinzuf'#252'gen'
        Caption = '>>'
        TabOrder = 2
        OnClick = AllesHinzufButtonClick
      end
      object AllesEntfButton: TButton
        Left = 6
        Top = 376
        Width = 41
        Height = 25
        Hint = 'Alle markierten Position entfernen'
        Caption = '<<'
        TabOrder = 3
        OnClick = AllesEntfButtonClick
      end
    end
  end
  object Panel4: TPanel
    Left = 0
    Top = 0
    Width = 966
    Height = 35
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
  end
  object LPQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 380
    Top = 17
  end
  object InvPosQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 592
    Top = 128
  end
  object LPDataSource: TDataSource
    DataSet = LPQuery
    Left = 196
    Top = 17
  end
  object InvPosDataSource: TDataSource
    DataSet = InvPosQuery
    Left = 552
    Top = 136
  end
  object ArtQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 424
    Top = 16
  end
  object ArtDataSource: TDataSource
    DataSet = ArtQuery
    Left = 232
    Top = 16
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 288
    Top = 16
  end
  object LEDataSource: TDataSource
    DataSet = LEQuery
    Left = 156
    Top = 17
  end
  object LEQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 332
    Top = 17
  end
  object InvDatePopupMenu: TPopupMenu
    Left = 328
    Top = 232
    object SelInvDateMenuItem: TMenuItem
      Caption = 'Festes Datum w'#228'hlen...'
      OnClick = SelInvDateMenuItemClick
    end
  end
end
