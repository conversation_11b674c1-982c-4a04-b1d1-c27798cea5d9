unit Win32Utils;

interface

uses Windows;

function GetAPIErrorMessage (const ErrorCode : DWORD) : String;
function NTSetPrivilege(sPrivilege: string; bEnabled: boolean): boolean;
function ForceForegroundWindow(hwnd: THandle): boolean;

{$EXTERNALSYM GetSystemDefaultUILanguage}
function GetSystemDefaultUILanguage: LANGID; stdcall;

{$EXTERNALSYM GetUserDefaultUILanguage}
function GetUserDefaultUILanguage: LANGID; stdcall;

implementation

uses
  SysUtils;

function GetSystemDefaultUILanguage; external kernel32 name 'GetSystemDefaultUILanguage';
function GetUserDefaultUILanguage; external kernel32 name 'GetUserDefaultUILanguage';

function GetAPIErrorMessage (const ErrorCode : DWORD) : String;
var
  i       : Integer;
  dwanz   : DWORD;
  errstr  : String;
  perrstr : array [0..255] of char;
begin
  errstr := '';

  dwanz := FormatMessage (FORMAT_MESSAGE_FROM_SYSTEM, Nil, ErrorCode, LANG_NEUTRAL, @perrstr, sizeof (perrstr), Nil);

  if (dwanz > 0) then begin
    for i:=0 to dwanz do begin
      if (perrstr [i] >= ' ') then
        errstr := errstr + perrstr [i]
      else errstr := errstr + ' ';
    end;
  end;
  
  GetAPIErrorMessage := errstr;
end;

function NTSetPrivilege(sPrivilege: string; bEnabled: boolean): boolean;
var
  hToken: THandle; 
  TokenPriv: TOKEN_PRIVILEGES; 
  PrevTokenPriv: TOKEN_PRIVILEGES; 
  ReturnLength: Cardinal;
  err : DWORD;
begin
  Result := True;
  // obtain the processes token
  if OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES or TOKEN_QUERY, hToken) then begin
    try
      // Get the locally unique identifier (LUID) .
      if LookupPrivilegeValue(nil, PChar(sPrivilege), TokenPriv.Privileges[0].Luid) then begin
        TokenPriv.PrivilegeCount := 1; // one privilege to set

        case bEnabled of
        true: TokenPriv.Privileges[0].Attributes := SE_PRIVILEGE_ENABLED;
        false: TokenPriv.Privileges[0].Attributes := 0;
        end;

        ReturnLength := 0; // replaces a var parameter
        PrevTokenPriv := TokenPriv;

        // enable or disable the privilege

        AdjustTokenPrivileges(hToken, False, TokenPriv, SizeOf(PrevTokenPriv), PrevTokenPriv, ReturnLength);
      end;
    finally
      CloseHandle(hToken);
    end;
  end;

  err := GetLastError;

  // test the return value of AdjustTokenPrivileges.
  Result := (err = ERROR_SUCCESS);
end;

function ForceForegroundWindow(hwnd: THandle): boolean;
{
found here:
http://delphi.newswhat.com/geoxml/forumhistorythread?groupname=borland.public.delphi.rtl.win32&messageid=<EMAIL>
}
const
  SPI_GETFOREGROUNDLOCKTIMEOUT = $2000;
  SPI_SETFOREGROUNDLOCKTIMEOUT = $2001;
var
  ForegroundThreadID: DWORD;
  ThisThreadID: DWORD;
  timeout: DWORD;
begin
  if IsIconic(hwnd) then ShowWindow(hwnd, SW_RESTORE);
  if GetForegroundWindow = hwnd then Result := true
  else begin
    // Windows 98/2000 doesn't want to foreground a window when some other
    // window has keyboard focus

    if ((Win32Platform = VER_PLATFORM_WIN32_NT) and (Win32MajorVersion > 4)) or
       ((Win32Platform = VER_PLATFORM_WIN32_WINDOWS) and ((Win32MajorVersion > 4) or
                                                          ((Win32MajorVersion = 4) and (Win32MinorVersion > 0)))) then begin
      // Code from Karl E. Peterson, www.mvps.org/vb/sample.htm
      // Converted to Delphi by Ray Lischner
      // Published in The Delphi Magazine 55, page 16

      Result := false;
      ForegroundThreadID := GetWindowThreadProcessID(GetForegroundWindow,nil);
      ThisThreadID := GetWindowThreadPRocessId(hwnd,nil);
      if AttachThreadInput(ThisThreadID, ForegroundThreadID, true) then
      begin
        BringWindowToTop(hwnd); // IE 5.5 related hack
        SetForegroundWindow(hwnd);
        AttachThreadInput(ThisThreadID, ForegroundThreadID, false);  // bingo
        Result := (GetForegroundWindow = hwnd);
      end;
      if not Result then begin
        // Code by Daniel P. Stasinski

        SystemParametersInfo(SPI_GETFOREGROUNDLOCKTIMEOUT, 0, @timeout, 0);
        SystemParametersInfo(SPI_SETFOREGROUNDLOCKTIMEOUT, 0, TObject(0), SPIF_SENDCHANGE);
        BringWindowToTop(hwnd); // IE 5.5 related hack
        SetForegroundWindow(hWnd);
        SystemParametersInfo(SPI_SETFOREGROUNDLOCKTIMEOUT, 0, TObject(timeout), SPIF_SENDCHANGE);
      end;
    end
    else begin
      BringWindowToTop(hwnd); // IE 5.5 related hack
      SetForegroundWindow(hwnd);
    end;

    Result := (GetForegroundWindow = hwnd);
  end;
end; { ForceForegroundWindow }

end.
