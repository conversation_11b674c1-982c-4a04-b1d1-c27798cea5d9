﻿unit AuftragVerpackenDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, CheckLst, DB, ADODB, Grids, StringGridPro, FrontendUtils,
  ComCtrls, BarCodeScanner, GR32_Image, LogFile, WaagenModul;

type
  TAuftragVerpackenForm = class(TForm)
    ADOQuery1: TADOQuery;
    ButtonPanel: TPanel;
    AbortButton: TButton;
    OkButton: TButton;
    Panel1: TPanel;
    BruttoGewichtEdit: TEdit;
    Label4: TLabel;
    Label5: TLabel;
    VersandKartonLabel: TLabel;
    Bevel2: TBevel;
    VPEAnzLabel: TLabel;
    Panel2: TPanel;
    Label1: TLabel;
    AuftragNrLabel: TLabel;
    Label3: TLabel;
    WarenempfLabel: TLabel;
    LabelLENVE: TLabel;
    NVELabel: TLabel;
    Bevel1: TBevel;
    InhaltPanel: TPanel;
    Positionen: TLabel;
    FehlerPanel: TPanel;
    ArtikelImagePanel: TPanel;
    ArtikelImage: TImage32;
    InhaltListView: TListView;
    SpedPanel: TPanel;
    Label6: TLabel;
    SpedLabel: TLabel;
    SpedImage: TImage;
    ChangeSpedButton: TButton;
    Bevel3: TBevel;
    VerpackungLabel: TLabel;
    WaagePanel: TPanel;
    WaageLabel: TLabel;
    WaageGewichtLabel: TLabel;
    WaagePaintBox: TPaintBox;
    WaageLEDPanel: TPanel;
    AbmessungPanel: TPanel;
    Label11: TLabel;
    Label12: TLabel;
    Label13: TLabel;
    Label14: TLabel;
    PaintBox2: TPaintBox;
    LTLength: TEdit;
    LTWidth: TEdit;
    LTHeigth: TEdit;
    Timer1: TTimer;
    FehlerButton: TButton;
    PresentLabel: TLabel;
    PackHinweisLabel: TLabel;
    PackHintLabel: TLabel;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure BruttoGewichtEditChange(Sender: TObject);
    procedure FormResize(Sender: TObject);
    procedure ChangeSpedButtonClick(Sender: TObject);
    procedure BruttoGewichtEditKeyPress(Sender: TObject; var Key: Char);
    procedure InhaltListViewChange(Sender: TObject; Item: TListItem; Change: TItemChange);
    procedure InhaltPanelResize(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure Timer1Timer(Sender: TObject);
    procedure InhaltListViewCustomDrawSubItem(Sender: TCustomListView; Item: TListItem; SubItem: Integer; State: TCustomDrawState;
      var DefaultDraw: Boolean);
    procedure FormActivate(Sender: TObject);
  private
    fRefSped       : Integer;
    fRefSpedProd   : Integer;
    fManRefSped    : Integer;
    fRefNVE        : Integer;
    fRefKommLE     : Integer;
    fRefAuftrag    : Integer;
    fLager,
    fMandant,
    fVerpackArt,
    fVersandArt,
    fISOLand       : String;
    fRefMand       : Integer;
    fRefSubMand    : Integer;
    fRefLager      : Integer;
    fRefPackplatz  : Integer;
    fSelectPackLT  : Boolean;
    fOptCOD,
    fAusland       : Boolean;
    fBruttoGewicht : Integer;
    fRefLTType     : Integer;
    fSpedInfos     : TSpedInfos;
    fRefBoxType    : Integer;  //Dieser LT-Type wird per Scan ausgewählt
    fRefAufLTType  : Integer;  //Verpackungart aus dem Auftrag
    fRefAufLTTara  : Integer;  //Taragewicht der Verpackung aus dem Auftrag
    fAufLTName     : String;
    fLTTara        : Integer;     //Taragewicht der Verpackung
    fPackHinweis   : String;
    fWarnHinweis   : String;

    fErrorConfirmed : Boolean;  //Fehler müssen bestätigt werden


    fOldWaageGewicht   : Integer;
    fWiegeThread       : TWiegeThread;

    {$ifdef TraceVerpacken}
      verpacklog : TLogFile;
    {$endif}

    procedure ShowErrorPanel (const ErrorText : String);

    procedure ScannerErfassung (var Message: TMessage); message WM_USER + SCANNER_DETECT;
    procedure UpdateSpeditionInfos;

    procedure CloseMessage                (var Message: TMessage); message WM_USER + 99;
    procedure AbortMessage                (var Message: TMessage); message WM_USER + 98;
  public
    property RefNVE       : Integer read fRefNVE;
    property RefAuftrag   : Integer read fRefAuftrag;
    property RefPackplatz : Integer read fRefPackplatz;

    function Prepare (const RefPackplatz, RefAuftrag, RefNVE, RefKommLE : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses
  {$if CompilerVersion > 30.0}
    System.UITypes,
  {$ifend}

  mmSystem, GraphUtil, VCLUtilitys, ConfigModul, LVSConst, DatenModul, LVSGlobalDaten, LVSDatenInterface,
  VersandAbwicklung, PrintModul, PrinterUtils, DMSDatenInterface, PDFPreviewDLG,
  ErrorTracking, SelectSpeditionTouchDLG, LVSSecurity, InputLHMSerial, ResourceText,
  SprachModul, VerlaufDLG;

type
  TVerpackEntry = class (TComboBoxRef)
    RefArEinheit  : Integer;
    Picture       : String;
    Menge         : Integer;
    BruttoGewicht : Integer;

    constructor Create (const pRef, pRefArEinheit, pMenge, pBruttoGewicht : Integer; const pPicture : String);
  end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.UpdateSpeditionInfos;
var
  h       : Integer;
  dispstr : String;
begin
  if (fRefSped > 0) then begin
    GetSpedInfos (fRefSped, fRefSpedProd, fAusland, fSpedInfos);

    dispstr := fSpedInfos.SpedName;

    if (Length (fVersandArt) > 0) and (copy (fVersandArt, 1, 6) <> '$ROLE$')  and (fVersandArt <> '~') then
      dispstr := dispstr + ' ('+fVersandArt+')';

    if (fOptCOD) then
      dispstr := dispstr + ' ('+GetResourceText (1629)+')';

    SpedLabel.Caption := dispstr;

    if (fSpedInfos.SpedLogo = 'DHL') then begin
      SpedPanel.Color := RGB (255, 204, 000);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_dhl');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'POST') then begin
      SpedPanel.Color := RGB (255, 207, 17);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_post');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'POST_CH') then begin
      SpedPanel.Color := RGB (252, 210, 5);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_post_ch');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'POST_AT') then begin
      SpedPanel.Color := RGB (255, 221, 0);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_post_at');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'POST_FR') then begin
      SpedPanel.Color := RGB (250, 166, 37);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_post_fr');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'POST_NL') then begin
      SpedPanel.Color := RGB (248, 170, 0);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_post_nl');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'DPD') then begin
      SpedPanel.Color := RGB ($C9, $00, $33);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_dpd');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'BRT') then begin
      SpedPanel.Color := RGB (225, 6, 40);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_brt');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'IDS') then begin
      SpedPanel.Color := RGB (0, 107, 171);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_ids');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'HERMES') then begin
      SpedPanel.Color := RGB (1, 171, 220);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_hermes');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'GLS') then begin
      SpedPanel.Color := RGB (0, 107, 171);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_gls');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'UPS') then begin
      SpedPanel.Color := RGB (0, 24, 83);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_ups');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'PRIME') then begin
      SpedPanel.Color := RGB (255, 153, 0);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_prime');
      SpedImage.Visible := true;
    end else if (fSpedInfos.SpedLogo = 'SCHENKER') then begin
      SpedPanel.Color := RGB (255, 0, 0);
      SpedImage.Picture.Bitmap.Handle := loadbitmap(hinstance, 'sped_logo_schenker');
      SpedImage.Visible := true;
    end else begin
      SpedPanel.Color := clBtnFace;
      SpedImage.Visible := False;
    end;

    if not (SpedImage.Visible) then
      SpedLabel.Left := SpedImage.Left
    else
      SpedLabel.Left := SpedImage.Left + SpedImage.Width + 24;

    if (fSpedInfos.SpedColor = -1) then
      SpedPanel.Color := clBtnFace
    else
      SpedPanel.Color := fSpedInfos.SpedColor;

    if (SpedPanel.Color > 0) then begin
      //Helligkeit:
      h := trunc (GetRValue (SpedPanel.Color) * 0.3 + GetGValue (SpedPanel.Color) * 0.59 + GetBValue (SpedPanel.Color) * 0.11);

      if (trunc (GetRValue (SpedPanel.Color) * 0.3 + GetGValue (SpedPanel.Color) * 0.59 + GetBValue (SpedPanel.Color) * 0.11) > 128) then begin
        SpedLabel.Font.Color := clWindowText;
      end else begin
        SpedLabel.Font.Color := clWhite;
      end;
    end else begin
      SpedLabel.Font.Color := clWindowText;
    end;

    if (fSpedInfos.LTDimRequired and not AbmessungPanel.Visible) then
      AbmessungPanel.Visible := true;

    LTLength.Enabled := fSpedInfos.LTDimRequired;
    LTWidth.Enabled  := fSpedInfos.LTDimRequired;
    LTHeigth.Enabled := fSpedInfos.LTDimRequired;

    //Nur wenn noch kein Karton erfasst wurde, wird der Default-LT des Spediteurs übernommen
    if (fRefBoxType <= 0) and (fSpedInfos.RefDefaultLT > 0) then begin
      fRefLTType := fSpedInfos.RefDefaultLT;
      fLTTara    := fSpedInfos.DefaultLTTara;

      VersandKartonLabel.Caption := fSpedInfos.DefaultLT;

      if (fSpedInfos.PresetWeight) then begin
        if (fSpedInfos.DefaultLTL > 0) then
          LTLength.Text := IntToStr (fSpedInfos.DefaultLTL div 10);

        if (fSpedInfos.DefaultLTB > 0) then
          LTWidth.Text := IntToStr (fSpedInfos.DefaultLTB div 10);

        if (fSpedInfos.DefaultLTH > 0) then
          LTHeigth.Text := IntToStr (fSpedInfos.DefaultLTH div 10);
      end;
    end else if (fRefAufLTType > 0) then begin
      fRefLTType := fRefAufLTType;
      fLTTara    := fRefAufLTTara;

      VersandKartonLabel.Caption := fAufLTName;
    end;
  end else begin
    fSpedInfos.RefSped       := -1;
    fSpedInfos.RefSpedProd   := -1;
    fSpedInfos.RefDefaultLT  := -1;
    fSpedInfos.LTDimRequired := false;

    SpedLabel.Font.Color := clWindowText;
    SpedLabel.Caption := '';
    SpedPanel.Color := clBtnFace;
    SpedImage.Visible := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TVerpackEntry.Create (const pRef, pRefArEinheit, pMenge, pBruttoGewicht : Integer; const pPicture : String);
begin
  inherited Create (pRef);

  Menge         := pMenge;
  RefArEinheit  := pRefArEinheit;
  BruttoGewicht := pBruttoGewicht;
  Picture       := pPicture;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.BruttoGewichtEditChange(Sender: TObject);
begin
  FehlerPanel.Tag := 0;
  FehlerPanel.Visible := False;

  BruttoGewichtEdit.Color := clWindow;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.BruttoGewichtEditKeyPress(Sender: TObject;
  var Key: Char);
begin
  if not (Key in [#8,#9,^C,^V,'0'..'9',',']) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.ChangeSpedButtonClick(Sender: TObject);
var
  selfrom : TSelectSpeditionTouchForm;
begin
  selfrom := TSelectSpeditionTouchForm.Create (Self);

  try
    selfrom.RefMand      := fRefMand;
    selfrom.RefPackplatz := fRefPackplatz;
    selfrom.RefSped      := fRefSped;

    if (selfrom.ShowModal = mrOk) then begin
      fManRefSped := selfrom.RefSped;

      GetSpedInfos (fManRefSped, -1, fAusland, fSpedInfos);

      fRefSped     := fManRefSped;
      fRefSpedProd := -1;

      UpdateSpeditionInfos;

      if (fBruttoGewicht <= 0) and (fSpedInfos.DefaultGewicht > 0) then
        BruttoGewichtEdit.Text := Format ('%7.5f', [fSpedInfos.DefaultGewicht/1000]);
    end;
  finally
    selfrom.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 20.02.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.FormActivate(Sender: TObject);
var
  res : Integer;
begin
  if LVSDatenModul.FunctionExits ('PA_VERPACKEN', 'VERPACKEN_BEGIN') then begin
    res := VerpackenBegin (fRefPackplatz, fRefAuftrag);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.FormClose(Sender: TObject; var Action: TCloseAction);
var
  idx : Integer;
  line : String;
begin
  fWiegeThread.Terminate;

  for idx := 0 to InhaltListView.Columns.Count - 1 do begin
    if (idx = 0) then
      line := IntToStr (InhaltListView.Columns [idx].Width)
    else
      line := line + ';' + IntToStr (InhaltListView.Columns [idx].Width);
  end;

  LVSConfigModul.SaveFormParameter (Self, 'InhaltListView', line);

  LVSConfigModul.SaveFormInfo (Self);

  {$ifdef TraceVerpacken}
    verpacklog.Write ('TAuftragVerpackenForm.FormClose');
    verpacklog.CheckLogRotation;
  {$endif}
end;

//******************************************************************************
//* Function Name: ShowErrorPanel
//* Author       : Stefan Graf
//* Datum        : 02.02.2021
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.ShowErrorPanel (const ErrorText : String);
var
  lockflag : Boolean;
begin
  {$ifdef TraceVerpacken}
    verpacklog.Write ('TAuftragVerpackenForm.Error:'+ErrorText);
  {$endif}

  lockflag := LockWindowUpdate (Application.MainForm.Handle);

  try
    FehlerButton.Visible := fErrorConfirmed;
    FehlerPanel.Caption := ErrorText;
    FehlerPanel.Visible := True;
  finally
    if lockflag then
      LockWindowUpdate(0);
  end;

  FehlerPanel.Tag := 1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  gw,
  res,
  ref,
  menge,
  lblres,
  anznve,
  anzpos,
  anzpack,
  dmsref,
  akpref,
  kopie,
  nveref,
  refwa,
  l, b, h,                   //Die Abmessungen
  dlgres,
  cfgint       : Integer;
  respflag     : Boolean;
  dummystr,
  aufstat      : String;
  fname,
  nvenr,
  snrstr,
  respapp,
  logline,
  errmsg,
  cfgstr,
  comstr       : String;
  errtxt       : String;
  floatwert    : Double;
  dbok         : Boolean;
  query        : TADOQuery;
  nvequery     : TADOQuery;
  prtinfo      : TPrinterPorts;
  lhmform      : TInputLHMSerialForm;
  verlauf      : TVerlaufForm;
begin
  res := 0;

  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    CanClose := False;

    verlauf := nil;

    res := 0;
    gw := -1;
    lblres := 0;

    anznve  := 0;
    anzpos  := 0;
    anzpack := 0;

    if (res = 0) and not (fRefSped > 0) then begin
      res := -7;

      ShowErrorPanel (GetResourceText (1253));
    end;

    //Prüfen, ob die Abmessungen fehlen
    if (res = 0) and (fSpedInfos.LTDimRequired and ((Length (LTLength.Text) = 0) or (Length (LTWidth.Text) = 0) or (Length (LTHeigth.Text) = 0))) then begin
      res := -7;

      ShowErrorPanel (GetResourceText (1364));

      if (Length (LTLength.Text) = 0) and LTLength.CanFocus then
        LTLength.SetFocus
      else if (Length (LTWidth.Text) = 0) and LTWidth.CanFocus then
        LTWidth.SetFocus
      else if (Length (LTHeigth.Text) = 0) and LTHeigth.CanFocus then
        LTHeigth.SetFocus;
    end;

    if (res = 0) then begin
      if (Length (LTLength.Text) > 0) and TryStrToInt (LTLength.Text, l) then
        l := l * 10
      else if (fSpedInfos.RefDefaultLT > 0) then
        l := fSpedInfos.DefaultLTL
      else
        l := -1;

      if (Length (LTWidth.Text) > 0) and TryStrToInt (LTWidth.Text, b) then
        b := b * 10
      else if (fSpedInfos.RefDefaultLT > 0) then
        b := fSpedInfos.DefaultLTB
      else
        b := -1;

      if (Length (LTHeigth.Text) > 0) and TryStrToInt (LTHeigth.Text, h) then
        h := h * 10
      else if (fSpedInfos.RefDefaultLT > 0) then
        h := fSpedInfos.DefaultLTH
      else
        h := -1;

      if (Length (BruttoGewichtEdit.Text) = 0) then
        gw := -1
      else if TryStrToFloat (BruttoGewichtEdit.Text, floatwert) then begin
        if (floatwert < 9999) then
          gw := Round (floatwert * 1000)
        else begin
          res := -2;

          ShowErrorPanel (FormatMessageText (1389, []));

          BruttoGewichtEdit.SetFocus;
          BruttoGewichtEdit.Color := clRed;
        end;
      end else begin
        res := -2;

        ShowErrorPanel (GetResourceText (1254));

        BruttoGewichtEdit.SetFocus;
        BruttoGewichtEdit.Color := clRed;
      end;
    end;

    if (fRefLTType <= 0) then begin
      res := -7;

      ShowErrorPanel (GetResourceText (1366));
    end;

    if (res <> 0) then
      CanClose := False
    else if (fSpedInfos.GewichtPflich and (gw <= 0)) then begin
      CanClose := False;

      if fAusland then
        ShowErrorPanel ('Das Gesamtgewicht muss bei Exportsendungen angegeben werden')
      else  ShowErrorPanel (GetResourceText (1255));

      BruttoGewichtEdit.SetFocus;
      BruttoGewichtEdit.Color := clRed;
    end else begin
      CanClose := False;

      Screen.Cursor := crSQLWait;

      try
        dlgres := mrCancel;

        if (fRefPackplatz > 0) then begin
          res := AssigneAuftragPackplatz (fRefPackplatz, fRefAuftrag);

          if (res <> 0) then
            errmsg := FormatMessageText (1792, [LVSDatenModul.LastLVSErrorText]);
        end;

        if (fRefNVE <= 0) then begin
          query  := TADOQuery.Create (Self);

          try
            query.LockType := ltReadOnly;
            query.Connection := LVSDatenModul.MainADOConnection;

            query.SQL.Add ('select REF from V_WARENAUSGANG where STATUS not in (''ABG'',''STO'') and REF_AUFTRAG=:ref');
            query.Parameters.ParamByName('ref').Value := fRefAuftrag;

            query.Open;

            if (query.Fields [0].IsNull) then
              refwa := -1
            else refwa := query.Fields [0].AsInteger;

            query.Close;


            if (fRefKommLE > 0) then begin
              //Alles auf der Komm-LE verpacke
              query.SQL.Clear;
              query.SQL.Add ('select REF, nvl (MENGE_PICK, 0) - nvl (MENGE_VERPACKT, 0) from V_AUFTRAG_KOMM_POS_01 where nvl (MENGE_PICK, 0) > nvl (MENGE_VERPACKT, 0) and REF_KOMM_LE=:ref_le and REF_AUF_KOPF=:ref_auf');
              query.Parameters.ParamByName('ref_auf').Value := fRefAuftrag;
              query.Parameters.ParamByName('ref_le').Value := fRefKommLE;

              dbok := false;

              //Wenn ja alles in einer ganzen Transaktion verpacken
              while (res = 0) and not (dbok) do begin
                LVSDatenModul.BeginTransaction (LVSDatenModul.MainADOConnection);

                try
                  res := CreateWANVE (fRefAuftrag, refwa, fRefLTType, nveref, nvenr);

                  if (res <> 0) then
                    errmsg := FormatMessageText (1003, [LVSDatenModul.LastLVSErrorText])
                  else begin
                    res := SetNVESpedition (nveref, fRefSped, fRefSpedProd);

                    if (res <> 0) then
                      errmsg := FormatMessageText (1009, [LVSDatenModul.LastLVSErrorText]);
                  end;

                  if (res = 0) then begin
                    query.Open;

                    //Die einzelnen Position verpacken
                    while not (query.Eof) and (res = 0) do begin
                      ref   := query.Fields [0].AsInteger;
                      menge := query.Fields [1].AsInteger;

                      if (menge > 0) then begin
                        {$ifdef TraceVerpacken}
                          verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', nveref='+IntToStr (nveref)+', ref='+IntToStr (ref)+', menge='+IntToStr (menge));
                        {$endif}

                        res := EinheitVerpacken (fRefPackplatz, nveref, ref, menge, akpref);

                        {$ifdef TraceVerpacken}
                          verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', nveref='+IntToStr (nveref)+', res='+IntToStr (res)+', akpref='+IntToStr (akpref));
                        {$endif}
                      end;

                      if Assigned (verlauf) then begin
                        verlauf.ProgressBar1.StepIt;
                        verlauf.UpdateModal;

                        if verlauf.AbortFlag then
                          res := 99;
                      end;

                      query.Next;
                    end;

                    if (res <> 0) then begin
                      LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);

                      {$ifdef TraceVerpacken}
                        verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: Rollback');
                      {$endif}

                      if (res = 99) then
                        errmsg := FormatMessageText (1395, [])
                      else
                        errmsg := FormatMessageText (1004, [LVSDatenModul.LastLVSErrorText]);

                      //Verhindern, dass die selbe NVE-Nummer nochmals genutzt wird
                      lblres := GetConfigSequenzNummer (fRefMand, -1, fRefLager, 'NVE_NUMMER', dummystr);

                      fRefNVE := -1;
                    end else begin
                      LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trCommit);

                      fRefNVE := nveref;

                      {$ifdef TraceVerpacken}
                        verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: Commit');
                      {$endif}
                    end;

                    dbok := True;
                  end;
                except
                  on E: EOracleRetryException do begin
                    ErrorTrackingModule.WriteErrorLog ('EOracleRetryException AuftragPosVerpackenForm.FormCloseQuery', e.ClassName + ' : ' + e.Message);
                  end;

                  on  E: Exception do begin
                    res := -9;

                    errmsg := FormatMessageText (1122, []);

                    LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);

                    ErrorTrackingModule.WriteErrorLog ('Exception AuftragPosVerpackenForm.FormCloseQuery', e.ClassName + ' : ' + e.Message);
                  end;
                end;

                query.Close;
              end;
            end;
          finally
            query.Free;
          end;
        end else begin
          res := SetNVELTType (fRefNVE, fRefLTType);

          if (res <> 0) then
            MessageDLG('Fehler beim Ändern der Verpacksart der NVE' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
        end;

        if (res <> 0) then
          MessageDLG (errmsg, mtError, [mbOK], 0)
        else if (fRefNVE > 0) then begin
          //Prüfen, ob für das LHM der NVE eine Serialnummer angegeben werden muss
          lhmform := TInputLHMSerialForm.Create (Self);

          try
            lhmform.Prepare (fRefNVE, 1);

            //Nur anzeigen, wenn es auch gefordert ist
            if ((lhmform.OptLHMSerial = #0) or (lhmform.OptLHMSerial = '0')) then
              dlgres := mrOk
            else begin
              {$ifdef TraceVerpacken}
                verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', TInputLHMSerialForm: OptLHMSerial='+lhmform.OptLHMSerial);
              {$endif}

              dlgres := lhmform.ShowModal;

              if (dlgres = mrOk) then begin
                if (Length (lhmform.LHMSerialEdit.Text) > 0) then begin
                  res := SetNVELHMSerial (fRefNVE, lhmform.LHMSerialEdit.Text);

                  if (res <> 0) then
                    MessageDLG ('Fehler beim Setzen der LHM-Seriennummer'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOk], 0);
                end;
              end;
            end;
          finally
            lhmform.Release;
          end;

          if (res = 0) and (dlgres = mrOk) then begin
            res := SetNVESpedition (fRefNVE, fRefSped);

            if (res = 0) then
              res := SetNVEVPEGewicht (fRefNVE, -1, -1,  gw, l, b ,h);

            if (res <> 0) then
              MessageDLG('Fehler beim Verpacken der NVE' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
          end;
        end;

        if (res = 0) and (dlgres = mrOk) then begin
          if (fAusland and not (IsLandEU (fISOLand)) and not (fSpedInfos.AutoExport)) then begin
            CanClose := True;
            MessageDLG ('Für Sendungen in nicht EU-Länder müssen die Versanddokumente manuell erzeugt werden', mtInformation, [mbOK], 0)
          end else begin
            query  := TADOQuery.Create (Self);

            try
              query.LockType := ltReadOnly;
              query.Connection := LVSDatenModul.MainADOConnection;

              anzpos  := 0;
              anzpack := 0;

              query.SQL.Clear;
              query.SQL.Add ('select STATUS from VQ_AUFTRAG where REF=:ref');
              query.Parameters.ParamByName('ref').Value := fRefAuftrag;

              query.Open;

              aufstat := query.Fields [0].AsString;

              query.Close;

              if (res = 0) and (dlgres = mrOk) then begin
                try
                  respflag := false;

                  {$ifdef TraceVerpacken}
                     verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery;'+PrintModule.NVELabelPrinter.Port+';'+PrintModule.NVELabelPrinter.Name+';'+IntToStr (PrintModule.NVELabelPrinter.Ref));
                  {$endif}

                  if Assigned (PrintModule.PrintLog) then begin
                    logline := 'TAuftragVerpackenForm.FormCloseQuery;'+PrintModule.NVELabelPrinter.Port+';'+PrintModule.NVELabelPrinter.Name+';'+IntToStr (PrintModule.NVELabelPrinter.Ref);
                    PrintLogging (logline);
                  end;

                  //Drucken der Lables und Versandpapiere
                  if (PrintModule.NVELabelPrinter.Ref > 0) then begin
                    lblres := PrintModule.LoadPrinter (-1, PrintModule.NVELabelPrinter.Ref, '', prtinfo)
                  end else if (Length (PrintModule.NVELabelPrinter.Port) = 0) then begin
                    if (Length (PrintModule.NVELabelPrinter.Name) > 0) then
                      lblres := PrintModule.LoadPrinter (-1, PrintModule.NVELabelPrinter.Name, prtinfo)
                    else begin
                      prtinfo.Ref    := -1;
                      prtinfo.PrtTyp := '';
                      prtinfo.Name   := '';
                      prtinfo.Port   := '';
                      prtinfo.IsOnline := True;
                    end;
                  end else begin
                    prtinfo.Ref    := -1;
                    prtinfo.PrtTyp := 'LASER';
                    prtinfo.Name   := PrintModule.NVELabelPrinter.Name;
                    prtinfo.Port   := PrintModule.NVELabelPrinter.Port;
                    prtinfo.IsOnline := True;
                  end;

                  if (lblres <> 0) then
                    MessageDlg(FormatMessageText (1675, [PrintModule.NVELabelPrinter.Name]), mtError, [mbOK], 0)
                  else begin
                    lblres := StartCreateVersandInfos (Self, fRefNVE, prtinfo, respapp, respflag, errtxt);

                    if (lblres <> 0) then begin
                      respflag := false;

                      {$ifdef TraceVerpacken}
                        verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', VersandLabelError: lblres='+IntToStr (lblres)+' errtxt='+errtxt);
                      {$endif}
                    end;
                  end;

                  //Erst auf die Label-Rückmeldung warten
                  if respflag then begin
                    lblres := FinishCreateVersandInfos (Self, fRefNVE, respapp, fSpedInfos.SpedKennung, snrstr, errtxt);

                    {$ifdef TraceVerpacken}
                      verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', VersandLabel: snrstr='+snrstr+', lblres='+IntToStr (lblres)+', errtxt='+errtxt);
                    {$endif}
                  end else begin
                    query.SQL.Clear;
                    query.SQL.Add ('select SENDUNGS_NR from V_NVE_01 where REF=:ref');
                    query.Parameters.ParamByName('ref').Value := fRefNVE;

                    query.Open;

                    snrstr := query.Fields[0].AsString;

                    query.Close;

                    {$ifdef TraceVerpacken}
                      verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', VersandLabel: snrstr='+snrstr);
                    {$endif}
                  end;

                  if (res = 0) and (lblres <> 0) then begin
                    if (res = 0) then res := lblres;

                    if (Length (errmsg) > 0) then errmsg := errmsg + #13 + #13;
                    errmsg := errmsg + FormatMessageText (1005, [IntToStr (lblres), errtxt]);

                    CheckConfigParameter (fRefMand, -1, fRefLager, 'SENDIT_FEHLER_LABEL', cfgint, 0);

                    if (cfgint = 1) then begin
                      PrintVersandFehlerLabel (Self, fRefNVE, prtinfo, errtxt, dummystr);

                      if (Length (dummystr) > 0) then begin
                        if (Length (errmsg) > 0) then errmsg := errmsg + #13 + #13;
                        errmsg := errmsg + FormatMessageText (1005, [IntToStr (lblres), errtxt]);
                      end;
                    end;

                    MessageDLG (errmsg, mtError, [mbOK], 0);
                  end;
                except
                  on  E: Exception do begin
                    res := -9;
                    ErrorTrackingModule.WriteErrorLog ('TAuftragVerpackenForm.FormCloseQuery', e.ClassName + ' : ' + e.Message+#13+#10+query.SQL.Text);

                    MessageDLG('Fehler beim Lesen der Auftragsdaten', mtError, [mbOK], 0);

                    {$ifdef TraceVerpacken}
                      verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery.Exception 1: '+ e.ClassName + ' : ' + e.Message+#13+#10+query.SQL.Text);
                    {$endif}
                  end;
                end;

                if (res = 0) then begin
                  //Prüfen, ob für das LHM der NVE eine Serialnummer angegeben werden muss
                  lhmform := TInputLHMSerialForm.Create (Self);

                  try
                    lhmform.Prepare (fRefNVE, 2);

                    if (lhmform.OptShipmendNo = '0') then
                      dlgres := mrOk
                    else if (Length (snrstr) = 0) then begin
                      {$ifdef TraceVerpacken}
                        verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', TInputLHMSerialForm: OptShipmendNo='+lhmform.OptShipmendNo+', snrstr='+snrstr);
                      {$endif}

                      dlgres := lhmform.ShowModal;

                      if (dlgres = mrOk) then begin
                        if (Length (lhmform.LHMSerialEdit.Text) > 0) then begin
                          snrstr := lhmform.LHMSerialEdit.Text;

                          res := SetNVESendungsNr (fRefNVE, 'storelogix', lhmform.SpedLabel.Caption, snrstr);

                          if (res <> 0) then
                            MessageDLG (FormatMessageText (1630, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOk], 0);
                        end;
                      end;
                    end;
                  finally
                    lhmform.Release;
                  end;
                end;

                if (res = 0) and (not fSpedInfos.NeedShippingNo or (Length (snrstr) > 0)) then begin
                  res := NVEVerpackt (fRefNVE, -1, -1,  gw);

                  {$ifdef TraceVerpacken}
                    verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', NVEVerpackt: res='+IntToStr (res));
                  {$endif}

                  if (res <> 0) then
                    MessageDLG('Fehler beim Verpacken der NVE' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
                end;

                if (res = 0) then begin
                  Screen.Cursor := crHourGlass;

                  try
                    //Prüfen ob es noch unverpackte NVE gibt, auf den noch was liegt
                    query.SQL.Clear;
                    query.SQL.Add ('select count (nve.REF) from V_NVE_01 nve where nve.REF<>:ref_nve and nve.REF_AUF_KOPF=:ref_auf and nve.STATUS not in (''DEL'', ''ABG'') and ((select count (*) from V_LAGER_NVE_BESTAND where REF_NVE=nve.REF) > 0)');
                    query.Parameters.ParamByName('ref_auf').Value := fRefAuftrag;
                    query.Parameters.ParamByName('ref_nve').Value := fRefNVE;

                    query.Open;

                    anznve := query.Fields [0].AsInteger;

                    query.Close;

                    //Prüfen ob es noch unverpackte Positionen gibt
                    query.SQL.Clear;
                    query.SQL.Add ('select count (REF) from VQ_AUFTRAG_KOMM_POS where nvl (MENGE_VERPACKT, 0) < nvl (MENGE_PICK, 0) and REF_AUF_KOPF=:ref');
                    query.Parameters.ParamByName('ref').Value := fRefAuftrag;

                    query.Open;

                    anzpos  := query.Fields [0].AsInteger;
                    anzpack := query.Fields [0].AsInteger;

                    query.Close;

                    if (anzpos = 0) then begin
                      //Prüfen, ob es noch offene Auftragspositionen gibt
                      query.SQL.Clear;
                      query.SQL.Add ('select count (ap.REF) from VQ_AUFTRAG_POS ap, VQ_ARTIKEL ar where ar.REF=ap.REF_AR and nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (ar.OPT_AUTO_WA_BUCHUNG, ''0'')=''0'' and nvl (ap.MENGE_SOLL, 0) > nvl (ap.MENGE_GESAMT, 0) and ap.REF_AUF_KOPF=:ref');
                      query.Parameters.ParamByName('ref').Value := fRefAuftrag;

                      query.Open;

                      anzpos := query.Fields [0].AsInteger;

                      query.Close;
                    end;
                  finally
                    Screen.Cursor := crDefault;
                  end;

                  {$ifdef TraceVerpacken}
                    verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', aufstat='+aufstat+', anznve='+IntToStr (anznve)+', anzpos='+IntToStr (anzpos)+', anzpack='+IntToStr (anzpack));
                  {$endif}

                  if Assigned (PrintModule.PrintLog) then begin
                    logline := 'TAuftragVerpackenForm.FormCloseQuery;anznve='+IntToStr (anznve)+';anzpos='+IntToStr (anzpos)+';anzpack='+IntToStr (anzpack);
                    PrintLogging (logline);
                  end;
                end;

                if (res = 0) and (lblres = 0) then begin
                  //Nur wenn das Label gedruckt werden konnte und alle NVEs des Auftrags abgeschlosse sind, wird der Lieferschein gedruckt
                  if (res = 0) and (anznve = 0) and (anzpos = 0) then begin
                    lblres := VersandPapiereNachdruck (fRefAuftrag, false, false, true, true, false, errtxt);

                    {$ifdef TraceVerpacken}
                      verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+', VersandPapiere: lblres='+IntToStr (lblres)+', errmsg='+errmsg);
                    {$endif}

                    if (lblres <> 0) then begin
                      if (res = 0) then res := lblres;

                      MessageDLG('Fehler '+IntToStr (res)+' beim Drucken der Versandpapiere' + #13 + #13 + errtxt, mtError, [mbOK], 0);
                    end;
                  end;
                end;

                if (aufstat <> 'ABG') then begin
                  Screen.Cursor := crSQLWait;

                  try
                    if (lblres <> 0) or (anznve > 0) or (anzpos > 0) then begin
                      //Wenn ja steht der Auftrag nur im Warenausgang
                      res := AuftragWarenausgang (fRefAuftrag);

                      {$ifdef TraceVerpacken}
                        verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+' > AuftragWarenausgang res='+IntToStr (res));
                      {$endif}

                      if (res <> 0) then begin
                        MessageDLG(FormatMessageText (1631, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
                      end;
                    end else begin
                      //Der gesamte Auftrag ist jetzt verpackt
                      res := AuftragVerpackt (fRefAuftrag);

                      {$ifdef TraceVerpacken}
                        verpacklog.Write ('TAuftragVerpackenForm.FormCloseQuery: fRefAuftrag='+IntToStr (fRefAuftrag)+' > AuftragVerpackt res='+IntToStr (res));
                      {$endif}

                      if (res <> 0) then begin
                        MessageDLG(FormatMessageText (1632, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
                      end;
                    end;
                  finally
                    Screen.Cursor := crDefault;
                  end;
                end;

                if (res = 0) and fAusland then begin
                  if IsLandEU (fISOLand) then
                    GetConfigDaten (fRefMand, LVSDatenModul.AktLocationRef, fRefLager, 'VERSANDHINWEIS_EU_EXPORT', cfgstr, cfgint)
                  else
                    GetConfigDaten (fRefMand, LVSDatenModul.AktLocationRef, fRefLager, 'VERSANDHINWEIS_EXPORT', cfgstr, cfgint);

                  if (Length (cfgstr) > 0) then
                    MessageDLG (cfgstr, mtInformation, [mbOk], 0);

                  CanClose := (res = 0);
                end else begin
                  CanClose := (res = 0);
                end;
              end;
            finally
              query.Free;
            end;
          end;
        end;
      finally
        Screen.Cursor := crDefault;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.FormCreate(Sender: TObject);
begin
  fRefNVE       := -1;
  fRefMand      := -1;
  fRefAuftrag   := -1;
  fRefPackplatz := -1;
  fRefLTType    := -1;
  fRefAufLTType := -1;
  fRefAufLTTara := -1;
  fLTTara       := -1;

  fAufLTName    := '';
  fPackHinweis  := '';
  fWarnHinweis  := '';

  VerpackungLabel.Caption := '';
  VersandKartonLabel.Caption := '';
  PresentLabel.Caption := '';
  PackHinweisLabel.Caption := '';
  PackHintLabel.Caption := '';
  BruttoGewichtEdit.Text := '';
  fErrorConfirmed    := False;

  AbmessungPanel.Visible    := false;
  AbmessungPanel.BevelOuter := bvNone;
  LTLength.Text := '';
  LTWidth.Text := '';
  LTHeigth.Text := '';

  {$ifdef TraceVerpacken}
    verpacklog := TLogFile.Create;

    verpacklog.LogSize     := 1000000;
    verpacklog.LogCount    := 4;
    verpacklog.LogRotation := true;

    verpacklog.LogFileName := LVSConfigModul.GetSessionLogDir + 'verpacklog.log';
    verpacklog.Open;
  {$endif}

  WaagePanel.Visible := false;
  WaageLabel.Caption := '';
  WaageGewichtLabel.Caption := '';

  fOldWaageGewicht := 0;
  fWiegeThread := TWiegeThread.Create (true);

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AuftragNrLabel);
    LVSSprachModul.SetNoTranslate (Self, WarenempfLabel);
    LVSSprachModul.SetNoTranslate (Self, NVELabel);
    LVSSprachModul.SetNoTranslate (Self, SpedLabel);
    LVSSprachModul.SetNoTranslate (Self, PresentLabel);
    LVSSprachModul.SetNoTranslate (Self, VersandKartonLabel);
  {$endif}
end;

procedure TAuftragVerpackenForm.FormDestroy(Sender: TObject);
begin
  {$ifdef TraceVerpacken}
    verpacklog.Close;

    verpacklog.Free;
  {$endif}

  fWiegeThread.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.FormResize(Sender: TObject);
begin
  if (ChangeSpedButton.Visible) then
    WarenempfLabel.Width := ChangeSpedButton.Left - WarenempfLabel.Left - 8
  else WarenempfLabel.Width := WarenempfLabel.Parent.ClientWidth - WarenempfLabel.Left - 8;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.FormShow(Sender: TObject);
var
  query      : TADOQuery;
begin
  LVSConfigModul.ConfigForm (Self);
  LVSConfigModul.RestoreFormInfo (Self);

  if not (ArtikelImagePanel.Visible) then begin
    InhaltListView.Width := InhaltPanel.ClientWidth - 2 * InhaltListView.Left;
  end;

  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (fRefPackplatz > 0) then begin
      query.SQL.Clear;
      query.SQL.Add ('select * from V_WA_PACKPLATZ where REF=:ref');
      query.Parameters.ParamByName('ref').Value := fRefPackplatz;

      query.Open;

      if Assigned (query.FindField ('OPT_SELECT_PACK_LT')) then begin
        if (not fSelectPackLT and (query.FieldByName ('OPT_SELECT_PACK_LT').AsString = '1')) then
          fSelectPackLT := true;
      end;

      if Assigned (query.FindField ('OPT_ERROR_CONFIRMED')) then
        fErrorConfirmed := (query.FieldByName ('OPT_ERROR_CONFIRMED').AsString = '1');

      if Assigned (query.FindField ('OPT_DIM_PACK_LT')) then
        AbmessungPanel.Visible := (query.FieldByName ('OPT_DIM_PACK_LT').AsString = '1');

      if Assigned (query.FindField ('SCALES_TYPE')) then begin
        fWiegeThread.WaageIP    := query.FieldByName ('SCALES_IP').AsString;
        fWiegeThread.WaagePort  := DBGetIntegerNull (query.FieldByName ('SCALES_IP_PORT'));
        fWiegeThread.WaageType  := query.FieldByName ('SCALES_TYPE').AsString;

        if (Length (fWiegeThread.WaageType) > 0) and (Length (fWiegeThread.WaageIP) > 0) then begin
          WaagePanel.Visible := true;
          WaageLabel.Caption := fWiegeThread.WaageIP;
        end;
      end;

      query.Close;
    end;
  finally
    query.Free;
  end;

  if (aoTouch in AppOptions) Then begin
    if (ChangeSpedButton.Visible) then begin
      ChangeSpedButton.Font.Size := 16;
      ChangeSpedButton.Width     := 240;
      ChangeSpedButton.Height    := 40;
      ChangeSpedButton.Top       := Bevel1.Top - 8 - ChangeSpedButton.Height;
      ChangeSpedButton.Left      := ChangeSpedButton.Parent.ClientWidth - 8 - ChangeSpedButton.Width;
    end;

    ButtonPanel.Height := 80;

    AbortButton.Font.Size := 24;
    AbortButton.Width     := 200;
    AbortButton.Height    := 50;
    AbortButton.Top       := (AbortButton.Parent.ClientHeight - AbortButton.Height) div 2;
    AbortButton.Left      := AbortButton.Parent.ClientWidth - 8 - AbortButton.Width;

    OkButton.Font.Size := AbortButton.Font.Size;
    OkButton.Width     := AbortButton.Width;
    OkButton.Height    := AbortButton.Height;
    OkButton.Top       := AbortButton.Top;
    OkButton.Left      := AbortButton.Left - 30 - OkButton.Width;
  end;

  FormResize (Sender);

  InhaltPanelResize (Sender);

  if Assigned (fWiegeThread) and (Length (fWiegeThread.WaageType) > 0) then
    fWiegeThread.Resume;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 11.05.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TAuftragVerpackenForm.Prepare (const RefPackplatz, RefAuftrag, RefNVE, RefKommLE : Integer) : Integer;
var
  res,
  idx,
  maxw,
  iwert,
  celcount : Integer;
  r, w, gw : Integer;
  colstr,
  textstr  : String;
  first,
  optmaster: boolean;
  item     : TListItem;
  col      : TListColumn;
  slist    : TStringList;
  query    : TADOQuery;
  field    : TField;
  dispcols : String;
begin
  res := 0;

  fRefPackplatz := RefPackplatz;
  fRefAuftrag   := RefAuftrag;
  fRefNVE       := RefNVE;
  fRefKommLE    := RefKommLE;

  fVerpackArt := '';

  fBruttoGewicht := -1;

  optmaster := false;

  FehlerPanel.Tag := 0;
  FehlerPanel.Visible := False;
  FehlerPanel.Caption := '';

  AbmessungPanel.Visible := false;

  if (RefNVE > 0) then begin
    LabelLENVE.Caption := GetResourceText (1642);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select'
                      +' nve.NVE_NR,nve.REF_AUF_KOPF,nve.BRUTTO_GEWICHT,a.REF_MAND,a.REF_SUB_MAND,a.AUFTRAG_NR,a.KUNDEN_NR,a.KUNDEN_NAME,adr.LAND,adr.LAND_ISO'
                      +',lt.NAME as LT_NAME,plt.NAME as PACKMITTEL,nve.NVE_TYPE,nlt.TARA_GEWICHT as NVE_TARA_GEWICHT'
                      +',a.DRUCKART,nve.OPT_SPERRGUT,a.MANDANT,a.LAGER,l.LAND as LAGER_LAND,a.REF_LAGER,sped.REF as REF_SPED'
                      +',a.REF_LT,aq.REF_PACKMITTEL,nve.OPT_MASTER_NVE,(select sum (MENGE) from V_LAGER_NVE_BESTAND where REF_NVE=nve.REF) as VPE_ANZ,m.CONFIG_OPT'
                      +',at.PACK_TEXT,lt.TARA_GEWICHT as LT_TARA_GEWICHT, plt.TARA_GEWICHT as PACK_TARA_GEWICHT,nve.REF_LT_TYP'
                      +',coalesce (slt.TARA_GEWICHT, plt.TARA_GEWICHT,lt.TARA_GEWICHT) as TARA_GEWICHT,coalesce (slt.L, plt.L,lt.L) as L,coalesce (slt.B, plt.B,lt.B) as B,coalesce (slt.H, plt.H,lt.H) as H'
                      +',plt.NAME as PACKMITTEL, plt.TARA_GEWICHT as PACKMITTEL_LT_TARA, lt.TARA_GEWICHT as LT_TARA, slt.REF as REF_SPED_LT, slt.NAME as SPED_LT, slt.TARA_GEWICHT as SPED_LT_TARA'
                      +',(select sum (BRUTTO_GEWICHT) from V_LAGER_NVE_BESTAND where REF_NVE=nve.REF) as BESTAND_BRUTTO_GEWICHT'
                      +' from'
                      +' V_NVE_01 nve'
                      +' inner join V_LAGER_ADR l on (l.REF=nve.REF_LAGER)'
                      +' inner join V_LT_TYPEN nlt on (nlt.REF=nve.REF_LT_TYP)'
                      +' left outer join V_AUFTRAG a on (a.REF=nve.REF_AUF_KOPF)'
                      +' left outer join VQ_AUFTRAG_TEXTE at on (at.REF_AUF_KOPF=a.REF)'
                      +' left outer join VQ_AUFTRAG aq on (aq.REF=a.REF)'
                      +' left outer join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF)'
                      +' left outer join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR)'
                      +' left outer join V_MANDANT m on ((a.REF_SUB_MAND is not null and m.REF=a.REF_SUB_MAND) or (m.REF=a.REF_MAND))'
                      +' left outer join V_LT_TYPEN plt on (plt.REF=aq.REF_PACKMITTEL)'
                      +' left outer join V_LT_TYPEN lt on (lt.REF=a.REF_LT)'
                      +' left outer join V_SPEDITIONEN sped on (sped.REF=nvl (nve.REF_SPED,av.REF_SPED))'
                      +' left outer join V_SPED_CONFIG spedcfg on (spedcfg.REF_SPED=sped.REF)'
                      +' left outer join V_LT_TYPEN slt on (slt.REF=spedcfg.REF_DEFAULT_LT)'
                      +' where nve.REF=:ref');
    ADOQuery1.Parameters.ParamByName('ref').Value := fRefNVE;

    ADOQuery1.Open;

    if (ADOQuery1.RecordCount = 0) then begin
      res := -22;
      MessageDLG ('Ungültige NVE ('+IntToStr (fRefNVE)+')', mtError, [mbOk], 0);
    end else begin
      ArtikelImagePanel.Visible := CheckOpt(ADOQuery1.FieldByName('CONFIG_OPT').AsString, cMandArPictureVerpack);

      if (ADOQuery1.FieldByName('LAGER_LAND').IsNull) then
        fAusland := (ADOQuery1.FieldByName('LAND_ISO').AsString <> 'DE')
      else fAusland := (ADOQuery1.FieldByName('LAND_ISO').AsString <> ADOQuery1.FieldByName('LAGER_LAND').AsString);

      fRefMand     := DBGetReferenz (ADOQuery1.FieldByName('REF_MAND'));
      fRefSubMand  := DBGetReferenz (ADOQuery1.FieldByName('REF_SUB_MAND'));
      fMandant     := ADOQuery1.FieldByName('MANDANT').AsString;
      fRefLager    := DBGetReferenz (ADOQuery1.FieldByName('REF_LAGER'));
      fLager       := ADOQuery1.FieldByName('LAGER').AsString;
      fRefSped     := DBGetReferenz (ADOQuery1.FieldByName('REF_SPED'));
      fRefSpedProd := -1;
      fRefAuftrag  := ADOQuery1.FieldByName('REF_AUF_KOPF').AsInteger;
      fISOLand     := ADOQuery1.FieldByName('LAND_ISO').AsString;
      fRefLTType   := DBGetReferenz (ADOQuery1.FieldByName('REF_LT_TYP'));

      optmaster := ADOQuery1.FieldByName('OPT_MASTER_NVE').AsString = '1';

      GetSpedInfos (fRefSped, fRefSpedProd, fAusland, fSpedInfos);
      UpdateSpeditionInfos;

      //Wenn der Versandkarton gewählt werden muss
      if not (ADOQuery1.FieldByName('REF_LT_TYP').IsNull) then begin
        VerpackungLabel.Caption := ADOQuery1.FieldByName('NVE_TYPE').AsString;

        if not (ADOQuery1.FieldByName('L').IsNull) then
          LTLength.Text := IntToStr (ADOQuery1.FieldByName('L').AsInteger div 10);

        if not (ADOQuery1.FieldByName('B').IsNull) then
          LTWidth.Text := IntToStr (ADOQuery1.FieldByName('B').AsInteger div 10);

        if not (ADOQuery1.FieldByName('H').IsNull) then
          LTHeigth.Text := IntToStr (ADOQuery1.FieldByName('H').AsInteger div 10);
      end else if (fSelectPackLT) then begin
        fRefLTType := -1;

        if not (ADOQuery1.FieldByName('PACKMITTEL').IsNull) then
          VerpackungLabel.Caption := ADOQuery1.FieldByName('PACKMITTEL').AsString
        else
          VerpackungLabel.Caption := '';
      end else begin
        if not ADOQuery1.FieldByName('REF_LT_TYP').IsNull then begin
          VerpackungLabel.Caption := ADOQuery1.FieldByName('NVE_TYPE').AsString;
          fLTTara    := DBGetIntegerNull (ADOQuery1.FieldByName('NVE_TARA_GEWICHT'));
        end else begin
          //Ansonsten die Vorgaben aus dem Auftrag
          if not (ADOQuery1.FieldByName('REF_SPED_LT').IsNull) then begin
            VerpackungLabel.Caption := ADOQuery1.FieldByName('SPED_LT').AsString;
            fRefLTType := DBGetIntegerNull (ADOQuery1.FieldByName('REF_SPED_LT'));
            fLTTara    := DBGetIntegerNull (ADOQuery1.FieldByName('SPED_LT_TARA'));
          end else if not (ADOQuery1.FieldByName('REF_PACKMITTEL').IsNull) then begin
            VerpackungLabel.Caption := ADOQuery1.FieldByName('PACKMITTEL').AsString;
            fRefLTType := DBGetIntegerNull (ADOQuery1.FieldByName('REF_PACKMITTEL'));
            fLTTara    := DBGetIntegerNull (ADOQuery1.FieldByName('PACKMITTEL_LT_TARA'));
          end else if not (ADOQuery1.FieldByName('REF_LT').IsNull) then begin
            VerpackungLabel.Caption := ADOQuery1.FieldByName('LT_NAME').AsString;
            fRefLTType := DBGetIntegerNull (ADOQuery1.FieldByName('REF_LT'));
            fLTTara    := DBGetIntegerNull (ADOQuery1.FieldByName('LT_TARA'));
          end;
        end;

        fRefAufLTType := fRefLTType;
        fRefAufLTTara := fLTTara;

        fAufLTName := VerpackungLabel.Caption;

        if not (ADOQuery1.FieldByName('L').IsNull) then
          LTLength.Text := IntToStr (ADOQuery1.FieldByName('L').AsInteger div 10);

        if not (ADOQuery1.FieldByName('B').IsNull) then
          LTWidth.Text := IntToStr (ADOQuery1.FieldByName('B').AsInteger div 10);

        if not (ADOQuery1.FieldByName('H').IsNull) then
          LTHeigth.Text := IntToStr (ADOQuery1.FieldByName('H').AsInteger div 10);
      end;

      fBruttoGewicht := DBGetIntegerNull (ADOQuery1.FieldByName('BESTAND_BRUTTO_GEWICHT'));

      if (fBruttoGewicht <= 0) and (fSpedInfos.DefaultGewicht > 0) then begin
        fBruttoGewicht := fSpedInfos.DefaultGewicht;

        if (fLTTara > 0) then
          fBruttoGewicht := fBruttoGewicht - fLTTara;
      end;

      if (fRefAuftrag <= 0) then begin
        res := -22;
        MessageDLG ('Ungültige Auftragszuordung ('+IntToStr (fRefNVE)+')', mtError, [mbOk], 0);
      end else begin
        AuftragNrLabel.Caption := ADOQuery1.FieldByName('AUFTRAG_NR').AsString;

        if (ADOQuery1.FieldByName('KUNDEN_NR').IsNull) then
          WarenempfLabel.Caption := ''
        else
          WarenempfLabel.Caption := ADOQuery1.FieldByName('KUNDEN_NR').AsString + ' / ';

        WarenempfLabel.Caption := WarenempfLabel.Caption + ADOQuery1.FieldByName('KUNDEN_NAME').AsString + ' / ' + ADOQuery1.FieldByName('LAND').AsString + ' ('+ADOQuery1.FieldByName('LAND_ISO').AsString+')';

        NVELabel.Caption           := ADOQuery1.FieldByName('NVE_NR').AsString;
        VersandKartonLabel.Caption := ADOQuery1.FieldByName('LT_NAME').AsString;

        if (ADOQuery1.FieldByName('VPE_ANZ').AsInteger > 1) then
          VPEAnzLabel.Caption        := FormatResourceText (1640, [ADOQuery1.FieldByName('VPE_ANZ').AsString])
        else if (ADOQuery1.FieldByName('VPE_ANZ').IsNull) then
          VPEAnzLabel.Caption        := GetResourceText(1639)
        else
          VPEAnzLabel.Caption        := FormatResourceText (1641, [ADOQuery1.FieldByName('VPE_ANZ').AsString]);

        if (Pos ('PRESENT;', ADOQuery1.FieldByName('DRUCKART').AsString) > 0) then
          PresentLabel.Caption := GetResourceText(1638)
        else if not (ADOQuery1.FieldByName('PACK_TEXT').IsNull) then
          PresentLabel.Caption := ADOQuery1.FieldByName('PACK_TEXT').AsString
        else if Assigned (ADOQuery1.FindField ('DEFAULT_PACK_HINWEIS')) and not (ADOQuery1.FieldByName('DEFAULT_PACK_HINWEIS').IsNull) then
          PresentLabel.Caption := ADOQuery1.FieldByName('DEFAULT_PACK_HINWEIS').AsString
        else
          PresentLabel.Caption := '';

        if (fBruttoGewicht <= 0) then
          BruttoGewichtEdit.Text := ''
        else begin
          gw := fBruttoGewicht;

          if (fLTTara > 0) then
            gw := gw + fLTTara;

          BruttoGewichtEdit.Text := Format ('%7.3f', [gw/1000]);
        end;
      end;
    end;

    ADOQuery1.Close;
  end else if ((fRefAuftrag > 0) and (fRefKommLE > 0)) then begin
    LabelLENVE.Caption := GetResourceText (1840);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select'
                      +' a.REF_LT,le.LE_NR,a.REF_MAND,a.REF_SUB_MAND,a.AUFTRAG_NR,a.KUNDEN_NR,a.KUNDEN_NAME,adr.LAND,adr.LAND_ISO,lt.NAME as LT_NAME,a.DRUCKART,a.MANDANT,a.LAGER,l.LAND as LAGER_LAND,a.REF_LAGER,sped.REF as REF_SPED,m.CONFIG_OPT'
                      +',at.PACK_TEXT,lt.TARA_GEWICHT as LT_TARA_GEWICHT, plt.TARA_GEWICHT as PACK_TARA_GEWICHT,a.REF_PACKMITTEL'
                      +',coalesce (slt.TARA_GEWICHT, plt.TARA_GEWICHT,lt.TARA_GEWICHT) as TARA_GEWICHT,coalesce (slt.L, plt.L,lt.L) as L,coalesce (slt.B, plt.B,lt.B) as B,coalesce (slt.H, plt.H,lt.H) as H'
                      +',plt.NAME as PACKMITTEL, plt.TARA_GEWICHT as PACKMITTEL_LT_TARA, lt.TARA_GEWICHT as LT_TARA, slt.REF as REF_SPED_LT, slt.NAME as SPED_LT, slt.TARA_GEWICHT as SPED_LT_TARA'
                      +',(select sum (MENGE_PICK) from V_AUFTRAG_KOMM_POS where REF_KOMM_LE=le.REF and REF_AUF_KOPF=a.REF) as VPE_ANZ'
                      +',(select sum (GEWICHT_IST) from V_AUFTRAG_KOMM_POS where REF_KOMM_LE=le.REF and REF_AUF_KOPF=a.REF) as BRUTTO_GEWICHT'
                      +' from V_AUFTRAG a'
                      +' inner join VQ_AUFTRAG_TEXTE at on (at.REF_AUF_KOPF=a.REF)'
                      +' inner join VQ_LAGER_LE le on (le.REF=:ref_le)'
                      +' inner join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF)'
                      +' inner join V_LAGER_ADR l on (l.REF=a.REF_LAGER)'
                      +' inner join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR)'
                      +' inner join V_MANDANT m on ((a.REF_SUB_MAND is not null and m.REF=a.REF_SUB_MAND) or (m.REF=a.REF_MAND))'
                      +' left outer join V_LT_TYPEN plt on (plt.REF=a.REF_PACKMITTEL)'
                      +' left outer join V_LT_TYPEN lt on (lt.REF=a.REF_LT)'
                      +' left outer join V_SPEDITIONEN sped on (sped.REF=av.REF_SPED)'
                      +' left outer join V_SPED_CONFIG spedcfg on (spedcfg.REF_SPED=sped.REF)'
                      +' left outer join V_LT_TYPEN slt on (slt.REF=spedcfg.REF_DEFAULT_LT)'
                      +' where a.REF=:ref_auf');
    ADOQuery1.Parameters.ParamByName('ref_auf').Value := RefAuftrag;
    ADOQuery1.Parameters.ParamByName('ref_le').Value := fRefKommLE;

    ADOQuery1.Open;

    if (ADOQuery1.RecordCount = 0) then begin
      res := -22;
      MessageDLG ('Ungültige Komm-LE ('+IntToStr (fRefKommLE)+')', mtError, [mbOk], 0);
    end else begin
      ArtikelImagePanel.Visible := CheckOpt(ADOQuery1.FieldByName('CONFIG_OPT').AsString, cMandArPictureVerpack);

      if (ADOQuery1.FieldByName('LAGER_LAND').IsNull) then
        fAusland := (ADOQuery1.FieldByName('LAND_ISO').AsString <> 'DE')
      else fAusland := (ADOQuery1.FieldByName('LAND_ISO').AsString <> ADOQuery1.FieldByName('LAGER_LAND').AsString);

      fRefMand     := DBGetReferenz (ADOQuery1.FieldByName('REF_MAND'));
      fRefSubMand  := DBGetReferenz (ADOQuery1.FieldByName('REF_SUB_MAND'));
      fMandant     := ADOQuery1.FieldByName('MANDANT').AsString;
      fRefLager    := DBGetReferenz (ADOQuery1.FieldByName('REF_LAGER'));
      fLager       := ADOQuery1.FieldByName('LAGER').AsString;
      fRefSped     := DBGetReferenz (ADOQuery1.FieldByName('REF_SPED'));
      fRefLTType   := DBGetReferenz (ADOQuery1.FieldByName('REF_LT'));
      fRefSpedProd := -1;
      fISOLand     := ADOQuery1.FieldByName('LAND_ISO').AsString;

      optmaster := false;

      GetSpedInfos (fRefSped, fRefSpedProd, fAusland, fSpedInfos);

      UpdateSpeditionInfos;

      AuftragNrLabel.Caption := ADOQuery1.FieldByName('AUFTRAG_NR').AsString;

      if (ADOQuery1.FieldByName('KUNDEN_NR').IsNull) then
        WarenempfLabel.Caption := ''
      else
        WarenempfLabel.Caption := ADOQuery1.FieldByName('KUNDEN_NR').AsString + ' / ';

      WarenempfLabel.Caption := WarenempfLabel.Caption + ADOQuery1.FieldByName('KUNDEN_NAME').AsString + ' / ' + ADOQuery1.FieldByName('LAND').AsString + ' ('+ADOQuery1.FieldByName('LAND_ISO').AsString+')';

      if (RefNVE > 0) then
        NVELabel.Caption := ADOQuery1.FieldByName('NVE_NR').AsString
      else if (fRefKommLE > 0) then
        NVELabel.Caption := ADOQuery1.FieldByName('LE_NR').AsString
      else
        NVELabel.Caption := '';

      VersandKartonLabel.Caption := ADOQuery1.FieldByName('LT_NAME').AsString;

      if (ADOQuery1.FieldByName('VPE_ANZ').AsInteger > 1) then
        VPEAnzLabel.Caption := FormatResourceText (1640, [ADOQuery1.FieldByName('VPE_ANZ').AsString])
      else if (ADOQuery1.FieldByName('VPE_ANZ').IsNull) then
        VPEAnzLabel.Caption := GetResourceText(1639)
      else
        VPEAnzLabel.Caption := FormatResourceText (1641, [ADOQuery1.FieldByName('VPE_ANZ').AsString]);

      if (Pos ('PRESENT;', ADOQuery1.FieldByName('DRUCKART').AsString) > 0) then
        PresentLabel.Caption := GetResourceText(1638)
      else if not (ADOQuery1.FieldByName('PACK_TEXT').IsNull) then
        PresentLabel.Caption := ADOQuery1.FieldByName('PACK_TEXT').AsString
      else if Assigned (ADOQuery1.FindField ('DEFAULT_PACK_HINWEIS')) and not (ADOQuery1.FieldByName('DEFAULT_PACK_HINWEIS').IsNull) then
        PresentLabel.Caption := ADOQuery1.FieldByName('DEFAULT_PACK_HINWEIS').AsString
      else
        PresentLabel.Caption := '';

      //Wenn der Versandkarton gewählt werden muss
      if (fSelectPackLT) then begin
        fRefLTType := -1;

        if not (ADOQuery1.FieldByName('PACKMITTEL').IsNull) then
          VerpackungLabel.Caption := ADOQuery1.FieldByName('PACKMITTEL').AsString
        else
          VerpackungLabel.Caption := '';
      end else begin
        //Ansonsten die Vorgaben aus dem Auftrag
        if not (ADOQuery1.FieldByName('REF_SPED_LT').IsNull) then begin
          VerpackungLabel.Caption := ADOQuery1.FieldByName('SPED_LT').AsString;
          fRefLTType := DBGetIntegerNull (ADOQuery1.FieldByName('REF_SPED_LT'));
          fLTTara    := DBGetIntegerNull (ADOQuery1.FieldByName('SPED_LT_TARA'));
        end else if not (ADOQuery1.FieldByName('REF_PACKMITTEL').IsNull) then begin
          VerpackungLabel.Caption := ADOQuery1.FieldByName('PACKMITTEL').AsString;
          fRefLTType := DBGetIntegerNull (ADOQuery1.FieldByName('REF_PACKMITTEL'));
          fLTTara    := DBGetIntegerNull (ADOQuery1.FieldByName('PACKMITTEL_LT_TARA'));
        end else if not (ADOQuery1.FieldByName('REF_LT').IsNull) then begin
          VerpackungLabel.Caption := ADOQuery1.FieldByName('LT_NAME').AsString;
          fRefLTType := DBGetIntegerNull (ADOQuery1.FieldByName('REF_LT'));
          fLTTara    := DBGetIntegerNull (ADOQuery1.FieldByName('LT_TARA'));
        end;

        fRefAufLTType := fRefLTType;
        fRefAufLTTara := fLTTara;
      end;

      if ADOQuery1.FieldByName('BRUTTO_GEWICHT').IsNull then
        fBruttoGewicht := -1
      else
        fBruttoGewicht := round (ADOQuery1.FieldByName('BRUTTO_GEWICHT').AsFloat * 1000);

      if (fBruttoGewicht <= 0) and (fSpedInfos.DefaultGewicht > 0) then begin
        fBruttoGewicht := fSpedInfos.DefaultGewicht;

        if (fLTTara > 0) then
          fBruttoGewicht := fBruttoGewicht - fLTTara;
      end;

      if (fBruttoGewicht <= 0) then
        BruttoGewichtEdit.Text := ''
      else begin
        gw := fBruttoGewicht;

        if (fLTTara > 0) then
          gw := gw + fLTTara;

        BruttoGewichtEdit.Text := Format ('%7.3f', [gw/1000]);
      end;
    end;

    ADOQuery1.Close;
  end else begin
    LabelLENVE.Caption := '';

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select'
                      +' a.*,adr.LAND,adr.LAND_ISO,lt.NAME as LT_NAME,l.LAND as LAGER_LAND,sped.REF as REF_SPED,av.REF_SPED_PRODUKT,at.PACK_TEXT'
                      +',(select sum (MENGE) from V_LAGER_NVE_BESTAND where REF_NVE in (select REF from V_LAGER_NVE where REF_AUF_KOPF=a.REF)) as VPE_ANZ'
                      +' from'
                      +'  V_AUFTRAG a'
                      +'  inner join VQ_AUFTRAG_TEXTE at on (at.REF_AUF_KOPF=a.REF)'
                      +'  inner join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF)'
                      +'  inner join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR)'
                      +'  inner join V_LAGER l on (l.REF=a.REF_LAGER)'
                      +'  left outer join V_LT_TYPEN lt on (lt.REF=a.REF_LT)'
                      +'  left outer join V_SPEDITIONEN sped on (sped.REF=av.REF_SPED)'
                      +'  left outer join V_SPED_CONFIG spedcfg on (spedcfg.REF_SPED=sped.REF)'
                      +' where a.REF=:ref'
                      );
    ADOQuery1.Parameters.ParamByName('ref').Value := fRefAuftrag;

    ADOQuery1.Open;

    if (ADOQuery1.FieldByName('LAGER_LAND').IsNull) then
      fAusland := (ADOQuery1.FieldByName('LAND_ISO').AsString <> 'DE')
    else fAusland := (ADOQuery1.FieldByName('LAND_ISO').AsString <> ADOQuery1.FieldByName('LAGER_LAND').AsString);

    fRefMand     := DBGetReferenz (ADOQuery1.FieldByName('REF_MAND'));
    fRefSubMand  := DBGetReferenz (ADOQuery1.FieldByName('REF_SUB_MAND'));
    fMandant     := ADOQuery1.FieldByName('MANDANT').AsString;
    fRefLager    := DBGetReferenz (ADOQuery1.FieldByName('REF_LAGER'));
    fLager       := ADOQuery1.FieldByName('LAGER').AsString;
    fRefSped     := DBGetReferenz (ADOQuery1.FieldByName('REF_SPED'));
    fRefSpedProd := DBGetReferenz (ADOQuery1.FieldByName('REF_SPED_PRODUKT'));
    fISOLand     := ADOQuery1.FieldByName('LAND_ISO').AsString;

    GetSpedInfos (fRefSped, fRefSpedProd, fAusland, fSpedInfos);

    UpdateSpeditionInfos;
    if (fBruttoGewicht <= 0) and (fSpedInfos.DefaultGewicht > 0) then
      BruttoGewichtEdit.Text := Format ('%7.3f', [fSpedInfos.DefaultGewicht/1000]);

    AuftragNrLabel.Caption := ADOQuery1.FieldByName('AUFTRAG_NR').AsString;

    if (ADOQuery1.FieldByName('KUNDEN_NR').IsNull) then
      WarenempfLabel.Caption := ''
    else
      WarenempfLabel.Caption := ADOQuery1.FieldByName('KUNDEN_NR').AsString + ' / ';

    WarenempfLabel.Caption := WarenempfLabel.Caption + ADOQuery1.FieldByName('KUNDEN_NAME').AsString + ' / ' + ADOQuery1.FieldByName('LAND').AsString + ' ('+ADOQuery1.FieldByName('LAND_ISO').AsString+')';
    VersandKartonLabel.Caption := ADOQuery1.FieldByName('LT_NAME').AsString;

    if (ADOQuery1.FieldByName('VPE_ANZ').AsInteger > 1) then
      VPEAnzLabel.Caption := FormatResourceText (1640, [ADOQuery1.FieldByName('VPE_ANZ').AsString])
    else if (ADOQuery1.FieldByName('VPE_ANZ').IsNull) then
      VPEAnzLabel.Caption := GetResourceText(1639)
    else
      VPEAnzLabel.Caption := FormatResourceText (1641, [ADOQuery1.FieldByName('VPE_ANZ').AsString]);


    if (Pos ('PRESENT;', ADOQuery1.FieldByName('DRUCKART').AsString) > 0) then
      PresentLabel.Caption := GetResourceText(1638)
    else if not (ADOQuery1.FieldByName('PACK_TEXT').IsNull) then
      PresentLabel.Caption := ADOQuery1.FieldByName('PACK_TEXT').AsString
    else if Assigned (ADOQuery1.FindField ('DEFAULT_PACK_HINWEIS')) and not (ADOQuery1.FieldByName('DEFAULT_PACK_HINWEIS').IsNull) then
      PresentLabel.Caption := ADOQuery1.FieldByName('DEFAULT_PACK_HINWEIS').AsString
    else
      PresentLabel.Caption := '';

    ADOQuery1.Close;

    if (fRefNVE = -1) then begin
      NVELabel.Caption := '';
      BruttoGewichtEdit.Text := '';
    end else begin
      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select * from V_NVE where REF=:ref');
      ADOQuery1.Parameters.ParamByName('ref').Value := fRefNVE;

      ADOQuery1.Open;

      NVELabel.Caption := ADOQuery1.FieldByName('NVE_NR').AsString;

      if not (ADOQuery1.FieldByName('BRUTTO_GEWICHT').IsNull) and (ADOQuery1.FieldByName('BRUTTO_GEWICHT').AsFloat > 0.001) then begin
        fBruttoGewicht := trunc (ADOQuery1.FieldByName('BRUTTO_GEWICHT').AsFloat * 1000);
        BruttoGewichtEdit.Text := ADOQuery1.FieldByName('BRUTTO_GEWICHT').AsString
      end else if (fSpedInfos.DefaultGewicht > 0) then
        BruttoGewichtEdit.Text := Format ('%4.5f', [fSpedInfos.DefaultGewicht / 1000])
      else
        BruttoGewichtEdit.Text := '';

      ADOQuery1.Close;
    end;
  end;

  if (res = 0) then begin
    if (fRefSubMand > 0) then
      CheckConfigParameter (fRefSubMand, -1, fRefLager, 'PACK_GRID_COLS', dispcols)
    else
      CheckConfigParameter (fRefMand, -1, fRefLager, 'PACK_GRID_COLS', dispcols);

    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      //Prüfen ob eine bestimmte Verpackungskarton-Art vorgegeben ist
      query.SQL.Clear;
      query.SQL.Add ('select * from V_AUFTRAG_VERSAND where REF_AUF_KOPF=:ref');
      query.Parameters.ParamByName('ref').Value := fRefAuftrag;

      try
        query.Open;

        //Wenn es das Feld gar nicht gibt, dann nicht
        if not Assigned (query.FindField ('LT_PACKING_GROUP')) then
          fVerpackArt := ''
        else
          fVerpackArt := query.FieldByName ('LT_PACKING_GROUP').AsString;

        query.Close;
      except
      end;
    finally
      query.Free;
    end;
  end;

  if (res = 0) then begin
    ADOQuery1.SQL.Clear;

    if (fRefNVE > 0) then begin
      if optmaster then begin
        ADOQuery1.SQL.Add ('select nve.*, pic.PICTURE_PATH from V_NVE_INHALT nve inner join VQ_ARTIKEL ar on (ar.REF=nve.REF_AR) left outer join VQ_ARTIKEL_PICTURE pic on (pic.REF=ar.REF_PICTURE) where nve.REF_NVE in (select REF from V_NVE_01 where REF_MASTER_NVE=:ref)');
        ADOQuery1.Parameters.ParamByName('ref').Value := fRefNVE;
      end else if (fRefNVE <> -1) then begin
        ADOQuery1.SQL.Add ('select'
                          +'   nve.*, pic.PICTURE_PATH, GETARTIKELTEXTEX (nve.REF_AR, pa_session_daten.getsprache, ''VERPACK_HINWEIS'') as VERPACK_HINWEIS'
                          +' from'
                          +'   V_NVE_INHALT nve'
                          +'   inner join VQ_ARTIKEL ar on (ar.REF=nve.REF_AR)'
                          +'   left outer join VQ_ARTIKEL_PICTURE pic on (pic.REF=ar.REF_PICTURE)'
                          +' where nve.REF_NVE=:ref');
        ADOQuery1.Parameters.ParamByName('ref').Value := fRefNVE;
      end;
    end else if (fRefAuftrag > 0) and (fRefKommLE > 0) then begin
      ADOQuery1.SQL.Add ('select akp.*,akp.MENGE_PICK as MENGE,akp.GEWICHT_IST as BRUTTO_GEWICHT, pic.PICTURE_PATH'
                        +' from V_PCD_AUFTRAG_KOMM_POS akp inner join VQ_ARTIKEL ar on (ar.REF=akp.REF_AR) left outer join VQ_ARTIKEL_PICTURE pic on (pic.REF=ar.REF_PICTURE) where akp.REF_AUF_KOPF=:ref_auf and akp.REF_KOMM_LE=:ref_le');
      ADOQuery1.Parameters.ParamByName('ref_auf').Value := fRefAuftrag;
      ADOQuery1.Parameters.ParamByName('ref_le').Value := fRefKommLE;
    end else begin
      ADOQuery1.SQL.Add ('select nve.*, pic.PICTURE_PATH from V_NVE_INHALT nve inner join VQ_ARTIKEL ar on (ar.REF=nve.REF_AR) left outer join VQ_ARTIKEL_PICTURE pic on (pic.REF=ar.REF_PICTURE)');
      ADOQuery1.SQL.Add ('where nve.REF_NVE in (select REF from V_NVE where REF_AUF_KOPF=:ref) order by LPAD (ar.ARTIKEL_NR, 16, ''0'')');
      ADOQuery1.Parameters.ParamByName('ref').Value := fRefAuftrag;
    end;

    ADOQuery1.Open;

    if (optmaster) then begin
      col := InhaltListView.Columns.Add;
      col.Caption := GetResourceText(1642);
      col.AutoSize := True;
    end;

    col := InhaltListView.Columns.Add;
    col.Caption := GetResourceText(1084);
    col.AutoSize := True;

    col := InhaltListView.Columns.Add;
    col.Caption := GetResourceText(1085);
    col.AutoSize := True;

    if (LVSConfigModul.UseArtikelCollis) then begin
      col := InhaltListView.Columns.Add;
      col.Caption := GetResourceText(1079);
      col.AutoSize := True;
    end;

    field := ADOQuery1.FindField ('VERPACK_HINWEIS');
    if Assigned (field) then begin
      if (Pos ('VERPACK_HINWEIS', dispcols) > 0) then begin
        col := InhaltListView.Columns.Add;
        col.Caption := GetResourceText (1874);
        col.AutoSize := True;
      end;
    end;

    col := InhaltListView.Columns.Add;
    col.Caption := GetResourceText(1107);
    col.AutoSize := True;

    col := InhaltListView.Columns.Add;
    col.Caption := GetResourceText(1086);
    col.AutoSize := True;

    col := InhaltListView.Columns.Add;
    col.Caption := GetResourceText(1134);
    col.AutoSize := True;


    while not ADOQuery1.Eof do begin
      item := InhaltListView.Items.Add;

      if (optmaster) then begin
        item.Caption := ADOQuery1.FieldByName ('NVE_NR').AsString;
        item.Data := TVerpackEntry.Create(ADOQuery1.FieldByName ('REF').AsInteger, ADOQuery1.FieldByName ('REF_AR_EINHEIT').AsInteger, DBGetIntegerNull (ADOQuery1.FieldByName ('BRUTTO_GEWICHT')), DBGetIntegerNull (ADOQuery1.FieldByName ('MENGE')), ADOQuery1.FieldByName ('PICTURE_PATH').AsString);

        item.SubItems.Add (ADOQuery1.FieldByName ('ARTIKEL_NR').AsString);
      end else begin
        item.Caption := ADOQuery1.FieldByName ('ARTIKEL_NR').AsString;
        item.Data := TVerpackEntry.Create(ADOQuery1.FieldByName ('REF').AsInteger, ADOQuery1.FieldByName ('REF_AR_EINHEIT').AsInteger, DBGetIntegerNull (ADOQuery1.FieldByName ('BRUTTO_GEWICHT')), DBGetIntegerNull (ADOQuery1.FieldByName ('MENGE')), ADOQuery1.FieldByName ('PICTURE_PATH').AsString);
      end;

      item.SubItems.Add (ADOQuery1.FieldByName ('ARTIKEL_TEXT').AsString);

      if (LVSConfigModul.UseArtikelCollis) then
        item.SubItems.Add (ADOQuery1.FieldByName ('COLLI_NAME').AsString);

      field := ADOQuery1.FindField ('VERPACK_HINWEIS');
      if Assigned (field) then begin
        if (Pos ('VERPACK_HINWEIS', dispcols) > 0) then begin
          item.SubItems.Add (field.AsString);
        end;
      end;

      item.SubItems.Add (ADOQuery1.FieldByName ('MENGE').AsString);
      item.SubItems.Add (ADOQuery1.FieldByName ('EINHEIT').AsString);

      if ADOQuery1.FieldByName ('BRUTTO_GEWICHT').IsNull or (ADOQuery1.FieldByName ('BRUTTO_GEWICHT').AsFloat = 0) then
        textstr := '---'
      else
        textstr := ADOQuery1.FieldByName ('BRUTTO_GEWICHT').AsString;

      item.SubItems.Add (textstr + ' kg');

      first := false;

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;

    slist := TStringList.Create;

    try
      slist.Delimiter := ';';

      LVSConfigModul.ReadFormParameter (Self, 'VerpackAufPosListView', colstr);

      if (Length (colstr) > 0) then begin
        slist.DelimitedText := colstr;
        for idx := 0 to slist.Count - 1 do begin
          if TryStrToInt (slist [idx], iwert) then begin
            if (idx < InhaltListView.Columns.Count) then
              InhaltListView.Columns [idx].Width := iwert;
          end;
        end;
      end else begin
        for idx := 0 to InhaltListView.Columns.Count - 1 do begin
          maxw := InhaltListView.StringWidth (InhaltListView.Columns.Items[idx].Caption);

          if (InhaltListView.Items.Count > 0) then begin
            for r := 0 to InhaltListView.Items.Count - 1 do begin
              if (idx = 0) then
                w := InhaltListView.StringWidth (InhaltListView.Items[r].Caption)
              else
                w := InhaltListView.StringWidth (InhaltListView.Items[r].SubItems [idx - 1]);

              if (w > maxw) then
                maxw := w;
            end;
          end;

          if (idx > 0) then
            InhaltListView.Columns [idx].Width := maxw + 16
          else if (InhaltListView.Checkboxes) then
            InhaltListView.Columns [idx].Width := maxw + 32
          else
            InhaltListView.Columns [idx].Width := maxw + 24;
        end;
      end;
    finally
      slist.Free;
    end;

    if (InhaltListView.Items.Count > 0) then
      InhaltListView.ItemIndex := 0
    else
      InhaltListView.ItemIndex := -1;

    ChangeSpedButton.Visible := LVSSecurityModule.CheckChangeRecht (fMandant, fLager, '', 'VerpackenChangeSpedition');
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.InhaltListViewChange(Sender: TObject; Item: TListItem; Change: TItemChange);
begin
  if (ArtikelImagePanel.Visible) and Assigned (Item) and Assigned (Item.Data) then begin
    ArtikelImage.Visible := False;
    if (Length (TVerpackEntry (Item.Data).Picture) > 0) then begin
      if (LowerCase (copy (TVerpackEntry (Item.Data).Picture, 1, 7)) = 'file://') then begin
        ShowArtikelPicture (ArtikelImage, copy (TVerpackEntry (Item.Data).Picture, 8));
      end;
    end;
  end;
end;

procedure TAuftragVerpackenForm.InhaltListViewCustomDrawSubItem(Sender: TCustomListView; Item: TListItem; SubItem: Integer; State: TCustomDrawState; var DefaultDraw: Boolean);
var
  i : Integer;
  r : TRect;
  s : String;
  fcolor : TColor;
begin
  if (SubItem > 0) and (SubItem <= Item.SubItems.Count) then begin
    if (copy (Item.SubItems [SubItem - 1], 1, 1) <> '!') then begin
      DefaultDraw := true;
    end else begin
      Sender.Canvas.Font.Color := clRed;
      Sender.Canvas.Font.Style := Sender.Canvas.Font.Style + [fsBold];

      r := Item.DisplayRect(drbounds);

      for i := 0 to SubItem-1 do begin
        r.Left  := r.Left + InhaltListView.Columns.Items[i].Width;
        r.Right := r.Left + InhaltListView.Columns.Items[i+1].Width;
      end;

      if cdsSelected in State then
        fcolor := GetShadowColor(fcolor, -25);

      Sender.Canvas.FillRect(r);

      r.Left := r.Left + 1;
      r.Right := r.Right - 1;
      r.Top := r.Top + 1;
      r.Bottom := r.Bottom - 1;

      s := item.SubItems[SubItem-1];
      Sender.Canvas.TextRect(r,s);

      DefaultDraw := false;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.05.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.InhaltPanelResize(Sender: TObject);
begin
  if (ArtikelImagePanel.Visible) then begin
    ArtikelImagePanel.Height := InhaltPanel.ClientHeight - ArtikelImagePanel.Top - 4;
    ArtikelImagePanel.Width := ArtikelImagePanel.Height;
    ArtikelImagePanel.Left := InhaltPanel.ClientWidth - ArtikelImagePanel.Width - 8;

    InhaltListView.Height := InhaltPanel.ClientHeight - InhaltListView.Top - 4;
    InhaltListView.Width := ArtikelImagePanel.Left - InhaltListView.Left - 16;
  end else begin
    InhaltListView.Height := InhaltPanel.ClientHeight - InhaltListView.Top - 4;
    InhaltListView.Width := InhaltPanel.ClientWidth - 2 * InhaltListView.Left;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.ScannerErfassung (var Message: TMessage);
var
  res,
  idx,
  reflt   : Integer;
  arnr,
  einh,
  spedstr,
  ltname  : string;
  refar,
  refae   : Integer;
  gw      : Integer;
  errmsg  : string;
  arinfo  : TArtikelInfo;
  query   : TADOQuery;
  done    : Boolean;
begin
  if (FehlerPanel.Visible and fErrorConfirmed) then begin
    Beep;
    PlaySound (PChar (LVSConfigModul.FrontendConfig.ScanErrSound), 0, SND_ASYNC);
  end else begin
    {$ifdef TraceVerpacken}
      verpacklog.Write ('TAuftragVerpackenForm.ScannerErfassung:'+ScanCode);
    {$endif}

    FehlerPanel.Tag := 0;
    FehlerPanel.Visible := False;
    FehlerPanel.Caption := '';

    res    := 0;
    reflt  := -1;
    done   := false;
    errmsg := '';

    if ((Length(ScanCode) > 5) and (ScanCode[1] = Code128ID) and (copy (ScanCode, 2, 5) = '#KBD-')) then begin  //Keyboard Codes
      done := true;

      if (copy (ScanCode, 7, 4) = 'CR-#') then
        PostMessage (Handle, WM_USER + 99, 0, 0)
      else if (copy (ScanCode, 7, 5) = 'ESC-#') then
        PostMessage (Handle, WM_USER + 98, 0, 0);
    end else if ((Length(ScanCode) > 5) and (ScanCode[1] = Code128ID) and (copy (ScanCode, 2, 6) = '#SPED-') and (copy (ScanCode, Length (ScanCode), 1) = '#')) then begin  //Keyboard Codes
      done := true;

      spedstr := copy (ScanCode, 8, Length (ScanCode) - 1 - 8);

      query := TADOQuery.Create (Self);

      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        query.SQL.Add ( 'select sped.REF, sped.NAME, (select count (*) from V_WA_PACKPLATZ_REL_SPED where OPT_SELECT=''1'' and REF_PACKPLATZ=:ref_pack and REF_SPED=sped.REF) from V_SPEDITIONEN sped where sped.STATUS<>''DEL'' and'
                       +' sped.REF_LAGER=:ref_lager and Upper (sped.NAME)=Upper (:name)');
        query.Parameters.ParamByName ('ref_lager').Value := fRefLager;
        query.Parameters.ParamByName ('ref_pack').Value := fRefPackplatz;
        query.Parameters.ParamByName ('name').Value := spedstr;

        try
          query.Open;

          if (query.Fields [0].IsNull) then
            errmsg := FormatMessageText (1396, [])
          else if (query.Fields [2].AsInteger = 0) then
            errmsg := FormatMessageText (1397, [query.Fields [1].AsString])
          else begin
            fRefSpedProd := -1;
            fManRefSped  := query.Fields [0].AsInteger;
            fRefSped     := query.Fields [0].AsInteger;

            UpdateSpeditionInfos;

            if (fBruttoGewicht <= 0) and (fSpedInfos.DefaultGewicht > 0) then
              BruttoGewichtEdit.Text := Format ('%7.3f', [fSpedInfos.DefaultGewicht/1000]);
          end;
        except
          errmsg := FormatMessageText (1394, []);
        end;

        query.Close;
      finally
        query.Free;
      end;
    end else if (Length (ScanCode) > 13) and ((ScanCode [1] = EAN13ID) or ((ScanCode [1] = Code128ID) and (Copy (ScanCode, 2, 2) <> '51'))) then begin
      res := DetectLTTypeEAN (copy (ScanCode, 2, 13), reflt, ltname);
    end else if (Length (ScanCode) > 5) and (ScanCode [1] = Code128ID) and (Copy (ScanCode, 2, 2) ='51') then begin
      res := DetectLTTypeID (copy (ScanCode, 4, Length (ScanCode) - 4), reflt, ltname);
    end;

    if (reflt > 0) then begin
      VerpackungLabel.Caption := '';

      query := TADOQuery.Create (Self);

      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        query.SQL.Add ('select * from V_LT_TYPEN where REF=:ref');
        query.Parameters [0].Value := reflt;

        query.Open;

        if Assigned (query.FindField('LT_PACKING_GROUP')) and (Length (fVerpackArt) > 0) and not (query.FieldByName ('LT_PACKING_GROUP').IsNull) and (query.FieldByName ('LT_PACKING_GROUP').AsString <> fVerpackArt) Then begin
          errmsg := FormatMessageText (1768, []);
        end else begin
          fRefLTType  := reflt;
          fRefBoxType := reflt;
          fLTTara     := DBGetIntegerNull (query.FieldByName('TARA_GEWICHT'));

          VersandKartonLabel.Caption := query.FieldByName('NAME').AsString;

          gw := fBruttoGewicht;

          if (fLTTara > 0) then
            gw := gw + fLTTara;

          BruttoGewichtEdit.Text := Format ('%7.3f', [gw / 1000.0]);

          if (fSpedInfos.PresetWeight) then begin
            if (query.FieldByName('L').IsNull) then
              LTLength.Text := IntToStr (query.FieldByName('L').AsInteger div 10);

            if (query.FieldByName('B').IsNull) then
              LTWidth.Text := IntToStr (query.FieldByName('B').AsInteger div 10);

            if (query.FieldByName('H').IsNull) then
              LTHeigth.Text := IntToStr (query.FieldByName('H').AsInteger div 10);
          end;
        end;

        query.Close;
      finally
        query.Free;
      end;
    end else if not done then begin
      res := DetectArtikelBarcode (fRefMand, refar, refae, arnr, einh, errmsg);

      if (res = 0) and (Length (errmsg) = 0) then begin
        arinfo := TArtikelInfo.Create;

        try
          res := GetArtikelInfos (refar, refae, arinfo);

          if (res <> 0) then
            errmsg := FormatMessageText (1530, [])
          else begin
            (*
            idx := 0;

            while (idx < VerpackAufPosListView.Items.Count) do begin
              if not (VerpackAufPosListView.Items [idx].Checked) and (TVerpackEntry (VerpackAufPosListView.Items [idx].Data).RefArEinheit = arinfo.RefArtikelEinheit) then
                break
              else
                Inc (idx);
            end;

            if (idx < VerpackAufPosListView.Items.Count) then begin
              //VerpackAufPosListView.ItemIndex := idx;
              VerpackAufPosListView.Items [idx].Checked := True;

              VerpackAufPosListView.ClearSelection;

              VerpackAufPosListView.Selected := VerpackAufPosListView.Items [idx];
              VerpackAufPosListView.ItemFocused := VerpackAufPosListView.Items [idx];
              VerpackAufPosListView.Items [idx].MakeVisible (True);

              VerpackAufPosListCheckedChange (Nil, VerpackAufPosListView.Items [idx]);
            end else begin
              errmsg := 'Keine offene Versandposition für '+arinfo.ArtikelNr;

              if (Length (arinfo.ColliName) > 0) then
                errmsg := errmsg + '/' + arinfo.ColliName;
            end;
            *)
          end;
        finally
          arinfo.Free;
        end;
      end else begin
        if (Length (errmsg) = 0) then
          errmsg := FormatMessageText (1531, []);
      end;
    end;

    if (Length (errmsg) > 0) then begin
      ShowErrorPanel (errmsg);
    end;
  end;
end;

procedure TAuftragVerpackenForm.Timer1Timer(Sender: TObject);
begin
  if (FehlerPanel.Tag <> 0) and not FehlerPanel.Visible then begin
    FehlerPanel.Visible := True;
    FehlerButton.Visible := fErrorConfirmed;
  end else if (FehlerPanel.Tag = 0) and FehlerPanel.Visible then begin
    FehlerPanel.Visible := False;
  end;

  if (FehlerPanel.Visible) then begin
    if (FehlerPanel.Tag = 1) then begin
      FehlerPanel.Color := clRed;
      FehlerPanel.Tag   := 2;
    end else begin
      FehlerPanel.Color := clBtnFace;
      FehlerPanel.Tag   := 1;
    end;
  end;

  if Assigned (fWiegeThread) then begin
    if not fWiegeThread.WaageOnline then begin
      if (WaageLEDPanel.Color <> clMaroon) then
        WaageLEDPanel.Color := clMaroon;
    end else begin
      if (WaageLEDPanel.Color <> clLime) then
        WaageLEDPanel.Color := clLime;

      if (fWiegeThread.Weight <> fOldWaageGewicht) then begin
        fOldWaageGewicht := fWiegeThread.Weight;

        if (fOldWaageGewicht > 0) then
          WaageGewichtLabel.Caption := Format ('%7.3f', [fOldWaageGewicht / 1000.0]) + ' kg'
        else
          WaageGewichtLabel.Caption := '---' + ' kg';
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 01.06.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.CloseMessage (var Message: TMessage);
begin
  ModalResult := mrOk;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 01.06.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAuftragVerpackenForm.AbortMessage (var Message: TMessage);
begin
  ModalResult := mrAbort;
end;

end.
