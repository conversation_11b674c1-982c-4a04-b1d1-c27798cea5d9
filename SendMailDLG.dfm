object SendMailForm: TSendMailForm
  Left = 0
  Top = 0
  Caption = 'Versenden als Mail'
  ClientHeight = 431
  ClientWidth = 812
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  TextHeight = 15
  object MailTextMemo: TMemo
    AlignWithMargins = True
    Left = 8
    Top = 161
    Width = 796
    Height = 221
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Align = alClient
    Lines.Strings = (
      'MailTextMemo')
    TabOrder = 0
    ExplicitTop = 156
    ExplicitHeight = 231
  end
  object Panel4: TPanel
    Left = 0
    Top = 390
    Width = 812
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      812
      41)
    object AbortButton: TButton
      Left = 729
      Top = 8
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Abbrechen'
      ModalResult = 2
      TabOrder = 0
    end
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 812
    Height = 153
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    object Bevel2: TBevel
      Left = 8
      Top = 149
      Width = 801
      Height = 72
      Shape = bsTopLine
    end
    object Panel2: TPanel
      Left = 145
      Top = 0
      Width = 667
      Height = 153
      Align = alRight
      BevelOuter = bvNone
      TabOrder = 0
      ExplicitLeft = 144
      ExplicitTop = 1
      ExplicitHeight = 151
      object ToPanel: TPanel
        Left = 0
        Top = 0
        Width = 667
        Height = 41
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 0
        DesignSize = (
          667
          41)
        object Label1: TLabel
          Left = 8
          Top = 13
          Width = 55
          Height = 15
          Caption = 'Senden an'
        end
        object ToEdit: TEdit
          Left = 90
          Top = 10
          Width = 570
          Height = 23
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 0
          Text = 'ToEdit'
        end
      end
      object Panel3: TPanel
        Left = 0
        Top = 41
        Width = 667
        Height = 32
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 1
        DesignSize = (
          667
          32)
        object Label3: TLabel
          Left = 8
          Top = 5
          Width = 16
          Height = 15
          Caption = 'CC'
        end
        object CCEdit: TEdit
          Left = 90
          Top = 2
          Width = 570
          Height = 23
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 0
          Text = 'Edit1'
        end
      end
      object Panel5: TPanel
        Left = 0
        Top = 73
        Width = 667
        Height = 32
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 2
        DesignSize = (
          667
          32)
        object Label2: TLabel
          Left = 8
          Top = 5
          Width = 23
          Height = 15
          Caption = 'BCC'
        end
        object Label5: TLabel
          Left = 355
          Top = 5
          Width = 59
          Height = 15
          Alignment = taRightJustify
          Anchors = [akTop, akRight]
          Caption = 'Antwort an'
        end
        object BCCEdit: TEdit
          Left = 90
          Top = 2
          Width = 239
          Height = 23
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 0
          Text = 'Edit1'
        end
        object ReplayEdit: TEdit
          Left = 421
          Top = 2
          Width = 239
          Height = 23
          Anchors = [akTop, akRight]
          TabOrder = 1
          Text = 'ReplayEdit'
        end
      end
      object Panel6: TPanel
        AlignWithMargins = True
        Left = 3
        Top = 108
        Width = 661
        Height = 41
        Margins.Bottom = 8
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 3
        ExplicitLeft = 0
        ExplicitTop = 105
        ExplicitWidth = 667
        DesignSize = (
          661
          41)
        object Label4: TLabel
          Left = 8
          Top = 13
          Width = 35
          Height = 15
          Caption = 'Betreff'
        end
        object Bevel1: TBevel
          Left = 4
          Top = 2
          Width = 661
          Height = 8
          Shape = bsTopLine
        end
        object SubEdit: TEdit
          Left = 90
          Top = 10
          Width = 564
          Height = 23
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 0
          Text = 'Edit1'
          ExplicitWidth = 570
        end
      end
    end
    object SendButton: TButton
      AlignWithMargins = True
      Left = 16
      Top = 16
      Width = 113
      Height = 121
      Margins.Left = 16
      Margins.Top = 16
      Margins.Right = 16
      Margins.Bottom = 16
      Align = alClient
      Caption = 'Senden'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 1
      OnClick = SendButtonClick
      ExplicitLeft = 17
      ExplicitTop = 17
      ExplicitWidth = 111
      ExplicitHeight = 119
    end
  end
end
