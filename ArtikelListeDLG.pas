unit ArtikelListeDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, Grids, DBGrids, SMDBGrid, DBGridPro, ExtCtrls,
  StdCtrls, BarCodeScanner, Menus, ComboBoxPro;

type
  TArtikelListeForm = class(TForm)
    Panel1: TPanel;
    FussPanel: TPanel;
    Panel3: TPanel;
    Panel4: TPanel;
    ArtikelDBGrid: TDBGridPro;
    ArtikelQuery: TADOQuery;
    ArtikelDataSource: TDataSource;
    OkButton: TButton;
    AbortButton: TButton;
    Label1: TLabel;
    INACheckBox: TCheckBox;
    BeschaffungCheckBox: TCheckBox;
    LieferantCheckBox: TCheckBox;
    ArtikelDBGridPopupMenu: TPopupMenu;
    ShowArtikelPictureMenuItem: TMenuItem;
    BestandCheckBox: TCheckBox;
    ListedCheckBox: TCheckBox;
    SubMandPanel: TPanel;
    SubMandComboBox: TComboBoxPro;
    Label11: TLabel;
    ARGrpPanel: TPanel;
    Label2: TLabel;
    ARGrpComboBox: TComboBoxPro;
    Bevel1: TBevel;
    procedure FormShow(Sender: TObject);
    procedure ArtikelDBGridDblClick(Sender: TObject);
    procedure FormHide(Sender: TObject);
    procedure ArtikelDBGridKeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
    procedure ArtikelQueryAfterClose(DataSet: TDataSet);
    function ArtikelDBGridColumnSort(Sender: TCustomDBGridPro; const ColumnName: String): String;
    procedure CheckBoxUpdateClick(Sender: TObject);
    procedure ShowArtikelPictureMenuItemClick(Sender: TObject);
    procedure ArtikelDBGridPopupMenuPopup(Sender: TObject);
    procedure FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
    procedure SubMandComboBoxChange(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure ARGrpComboBoxChange(Sender: TObject);
  private
    fRefMand      : Integer;
    fRefSubMand   : Integer;
    fRefLager     : Integer;
    fRefLieferant : Integer;
    fEAN          : String;
    fListeTyp     : String;

    fShowCollis      : Boolean;
    fShowMultiCollis : Boolean;

    fArtikelNr    : String;

    procedure UpdateQuery;

    procedure ScannerErfassung (var Message: TMessage); message WM_USER + SCANNER_DETECT;

    procedure ReloadGrid (var Message: TMessage); message WM_USER + 10;
  public
    property ListeTyp     : String  read fListeTyp     write fListeTyp;
    property RefMand      : Integer read fRefMand      write fRefMand;
    property RefSubMand   : Integer read fRefSubMand   write fRefSubMand;
    property RefLager     : Integer read fRefLager     write fRefLager;
    property RefLieferant : Integer read fRefLieferant write fRefLieferant;
    property ArtikelNr    : String  read fArtikelNr    write fArtikelNr;
    property EAN          : String  read fEAN          write fEAN;

    property ShowCollis      : Boolean read fShowCollis      write fShowCollis;
    property ShowMultiCollis : Boolean read fShowMultiCollis write fShowMultiCollis;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DatenModul, ConfigModul, FrontendImageModule, DBGridUtilModule,
  EAN128Utils, FrontendUtils, WebDLG, ImageDLG, SprachModul, ResourceText;

procedure TArtikelListeForm.ReloadGrid (var Message: TMessage);
begin
  Update;

  UpdateQuery;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.FormShow(Sender: TObject);
var
  flag    : Boolean;
  refsub  : Integer;
  mandstr : String;
begin
  if (fRefSubMand = -1) then
    mandstr := GetMandantName (fRefMand)
  else
    mandstr := GetMandantName (fRefMand) + ' / ' + GetMandantName (fRefSubMand);

  if (Length (fEAN) > 0) then
    Caption := FormatResourceText (1858, [fEAN])
  else if (fRefLager <> -1) then
    Caption := FormatResourceText (1228, [mandstr, GetLagerName (fRefLager)])
  else
    Caption := FormatResourceText (1228, [mandstr]);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.RestoreFormInfo (Self);

  LieferantCheckBox.Enabled := (fRefLieferant <> -1);

  if (SubMandPanel.Visible) then begin
    if (fRefSubMand = -1) then begin
      ClearComboBoxObjects (SubMandComboBox);
      SubMandComboBox.Enabled := False;
    end else begin
      refsub := GetComboBoxRef (SubMandComboBox);

      LoadSubMandantCombobox (SubMandComboBox, LVSDatenModul.AktLocationRef, fRefMand);

      if (fRefSubMand > 0) then begin
        SubMandComboBox.Enabled := False;
        SubMandComboBox.ItemIndex := FindComboboxRef (SubMandComboBox, fRefSubMand);
      end else begin
        SubMandComboBox.Enabled := True;

        if (refsub <= 0) then
          SubMandComboBox.ItemIndex := 0
        else begin
          SubMandComboBox.ItemIndex := FindComboboxRef (SubMandComboBox, refsub);
          if (SubMandComboBox.ItemIndex = -1) then SubMandComboBox.ItemIndex := 0;
        end;
      end;

      SubMandComboBoxChange (Sender);
    end;
  end;

  if Assigned (LVSConfigModul) then begin
    if BeschaffungCheckBox.Visible and BeschaffungCheckBox.Enabled then begin
      if (LVSConfigModul.ReadFormParameter(Self, fListeTyp+'BeschaffungCheckBox', flag, BeschaffungCheckBox.Checked) = 0) then begin
        if (BeschaffungCheckBox.Checked <> flag) then
          BeschaffungCheckBox.Checked := flag;
      end;
    end;

    if INACheckBox.Visible then begin
      if (LVSConfigModul.ReadFormParameter(Self, fListeTyp+'INACheckBox', flag, INACheckBox.Checked) = 0) then begin
        if (INACheckBox.Checked <> flag) then
          INACheckBox.Checked := flag;
      end;
    end;

    if ListedCheckBox.Visible then begin
      if (LVSConfigModul.ReadFormParameter(Self, fListeTyp+'ListedCheckBox', flag, ListedCheckBox.Checked) = 0) then begin
        if (ListedCheckBox.Checked <> flag) then
          ListedCheckBox.Checked := flag;
      end;
    end;

    if LieferantCheckBox.Visible then begin
      if (LVSConfigModul.ReadFormParameter(Self, fListeTyp+'LieferantCheckBox', flag, LieferantCheckBox.Checked) = 0) then begin
        if (LieferantCheckBox.Checked <> flag) then
          LieferantCheckBox.Checked := flag;
      end;
    end;

    if BestandCheckBox.Visible then begin
      if (LVSConfigModul.ReadFormParameter(Self, fListeTyp+'BestandCheckBox', flag, BestandCheckBox.Checked) = 0) then begin
        if (BestandCheckBox.Checked <> flag) then
          BestandCheckBox.Checked := flag;
      end;
    end;
  end;

  if not (ArtikelQuery.Active) then begin
    PostMessage (Handle, WM_USER + 10, 0, 0);
  end;

  ArtikelDBGrid.SetFocus;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.UpdateQuery;
begin
  ArtikelQuery.Close;

  ArtikelQuery.SQL.Clear;

  ArtikelQuery.SQL.Add ('select * from V_ARTIKEL_SUCHE where OPT_TEXT_ARTIKEL=''0'' and REF_MAND=:fRefMand');
  ArtikelQuery.Parameters.ParamByName('fRefMand').Value := fRefMand;

  if LVSConfigModul.UseLocationListing then begin
    ArtikelQuery.SQL.Add ('and REF in (select REF_AR from V_ARTIKEL_REL_LOCATION where STATUS=''AKT'' and REF_LOCATION=:ref_loc)');
    ArtikelQuery.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
  end;

  if ARGrpComboBox.Enabled and (ARGrpComboBox.ItemIndex > 0) Then begin
    ArtikelQuery.SQL.Add ('and (REF_GRUPPE=:ref_grp_1 or REF_GRUPPE in (select REF_GRUPPE from V_ARTIKEL_GRUPPE_REL_LEVEL where REF_PARENT=:ref_grp_2))');
    ArtikelQuery.Parameters.ParamByName ('ref_grp_1').Value := GetPLSQLParameter (GetComboBoxRef (ARGrpComboBox));
    ArtikelQuery.Parameters.ParamByName ('ref_grp_2').Value := GetPLSQLParameter (GetComboBoxRef (ARGrpComboBox));
  end;

  if LVSConfigModul.UseArtikelCollis then begin
    if not (fShowCollis) then
      ArtikelQuery.SQL.Add ('and OPT_IS_COLLI=''0''');

    if not (fShowMultiCollis) then
      ArtikelQuery.SQL.Add ('and OPT_MULTI_COLLI=''0''');
  end else begin
    ArtikelQuery.SQL.Add ('and OPT_IS_COLLI=''0''');
  end;

  if (SubMandPanel.Visible) then begin
    if (GetComboBoxRef (SubMandComboBox) > 0) then begin
      ArtikelQuery.SQL.Add ('and REF_SUB_MAND=:fRefSubMand');
      ArtikelQuery.Parameters.ParamByName('fRefSubMand').Value := GetComboBoxRef (SubMandComboBox);
    end else begin
      ArtikelQuery.SQL.Add ('and REF_SUB_MAND is null');
    end;
  end else if (fRefSubMand <> -1) then begin
    ArtikelQuery.SQL.Add ('and REF_SUB_MAND=:fRefSubMand');
    ArtikelQuery.Parameters.ParamByName('fRefSubMand').Value := fRefSubMand;
  end else begin
    ArtikelQuery.SQL.Add ('and REF_SUB_MAND is null');
  end;

  if (Length (fArtikelNr) > 0) then begin
    ArtikelQuery.SQL.Add ('and ARTIKEL_NR like :fArtikelNr');
    ArtikelQuery.Parameters.ParamByName('fArtikelNr').Value := fArtikelNr+'%';
  end;

  if (Length (fEAN) > 0) then begin
    ArtikelQuery.SQL.Add ('and EAN_SEARCH=:fEAN');
    ArtikelQuery.Parameters.ParamByName('fEAN').Value := fEAN;
  end;

  if (BeschaffungCheckBox.Checked) then
    ArtikelQuery.SQL.Add ('and nvl (OPT_BESCHAFFUNG,''0'')=''1''');

  if (LieferantCheckBox.Enabled and LieferantCheckBox.Checked) and (fRefLieferant <> -1) then begin
    ArtikelQuery.SQL.Add ('and REF in (select REF_AR from V_ARTIKEL_REL_LIEFERANT where REF_LIEFERANT=:fRefLieferant)');
    ArtikelQuery.Parameters.ParamByName('fRefLieferant').Value := fRefLieferant;
  end;

  if (INACheckBox.Checked) then
    ArtikelQuery.SQL.Add ('and STATUS in (''AKT'',''INA'')')
  else if (ListedCheckBox.Checked) then
    ArtikelQuery.SQL.Add ('and STATUS=''AKT''')
  else begin
    ArtikelQuery.SQL.Add ('and STATUS=''AKT'' and REF in (select REF_AR from V_ARTIKEL_REL_LAGER where REF_LAGER=:fRefLager)');
    ArtikelQuery.Parameters.ParamByName('fRefLager').Value := fRefLager;
  end;

  if (BestandCheckBox.Checked) then
    ArtikelQuery.SQL.Add ('and REF in (select REF_AR from VQ_LAGER_BESTAND where nvl (MENGE_FREI, 0) > 0)');

  with ArtikelDBGrid do begin
    if (SortColumns [0].ColumnIndex = -1) then
      SetSortColumn (0, 'ARTIKEL_NR');
  end;

  Screen.Cursor := crSQLWait;
  try
    ArtikelQuery.Open;
  finally
    Screen.Cursor := crDefault;
  end;

  ArtikelDBGrid.SetColumnVisible('OPT_*', False);

  if Assigned (LVSConfigModul) then begin
    ArtikelDBGrid.SetColumnVisible ('RLZ_*', LVSConfigModul.UseMHD);
    ArtikelDBGrid.SetColumnVisible ('FLAG_MHD_PFLICHT', LVSConfigModul.UseMHD);

    ArtikelDBGrid.SetColumnVisible ('FLAG_CHARGE_PFLICHT', LVSConfigModul.UseCharge);

    ArtikelDBGrid.SetColumnVisible ('FLAG_ORGA_INVENT', LVSConfigModul.UseORGAInvent);
  end;

  ArtikelDBGrid.SetColumnVisible('AKTIV_SEIT', False);
  ArtikelDBGrid.SetColumnVisible('INHALT_ANZAHL', False);
  ArtikelDBGrid.SetColumnVisible('STUECK_KENNZEICHEN', False);
  ArtikelDBGrid.SetColumnVisible('PICTURE_PATH', False);
  ArtikelDBGrid.SetColumnVisible('EAN_SEARCH', False);
  ArtikelDBGrid.SetColumnVisible('EAN_SEARCH', False);

  ArtikelDBGrid.SetColumnVisible('STATUS', INACheckBox.Checked);
  ArtikelDBGrid.SetColumnVisible('MANDANT', fRefMand <> -1);

  ArtikelDBGrid.SetColumnVisible('COLLI_NAME', LVSConfigModul.UseArtikelCollis);

  ArtikelDBGrid.SetColumnVisible('UNIT_EINHEIT', LVSConfigModul.UseArtikelUnit);
  ArtikelDBGrid.SetColumnVisible('UNIT_EINHEIT_NORM', LVSConfigModul.UseArtikelUnit);
  ArtikelDBGrid.SetColumnVisible('UNIT_EINHEIT_NETTO', LVSConfigModul.UseArtikelUnit);

  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetGewichtDisplayFunctions (ArtikelDBGrid.DataSource.DataSet, 'NETTO_GEWICHT', True);
    DBGridUtils.SetGewichtDisplayFunctions (ArtikelDBGrid.DataSource.DataSet, 'BRUTTO_GEWICHT', True);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.ArtikelDBGridDblClick(Sender: TObject);
begin
  ModalResult := mrOk;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.FormHide(Sender: TObject);
begin
  if Assigned (LVSConfigModul) then begin
    LVSConfigModul.SaveFormInfo (Self);

    if INACheckBox.Visible then
      LVSConfigModul.SaveFormParameter (Self, fListeTyp+'INACheckBox', INACheckBox.Checked);

    if ListedCheckBox.Visible then
      LVSConfigModul.SaveFormParameter (Self, fListeTyp+'ListedCheckBox', ListedCheckBox.Checked);

    if BeschaffungCheckBox.Visible then
      LVSConfigModul.SaveFormParameter (Self, fListeTyp+'BeschaffungCheckBox', BeschaffungCheckBox.Checked);

    if LieferantCheckBox.Visible then
      LVSConfigModul.SaveFormParameter (Self, fListeTyp+'LieferantCheckBox', LieferantCheckBox.Checked);

    if BestandCheckBox.Visible then
      LVSConfigModul.SaveFormParameter (Self, fListeTyp+'BestandCheckBox', BestandCheckBox.Checked);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
begin
  if (Key = VK_F5) then
    UpdateQuery;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.ArtikelDBGridKeyPress(Sender: TObject; var Key: Char);
begin
  if (Sender is TDBGridPro) then begin
    with (Sender as TDBGridPro) do begin
      if Assigned (DBGridUtils) then
        DBGridUtils.HotTrackKeyPress (Sender, Key);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.ArtikelDBGridPopupMenuPopup(Sender: TObject);
begin
  ShowArtikelPictureMenuItem.Enabled := (ArtikelDBGrid.DataSource.DataSet.Active) and (ArtikelDBGrid.DataSource.DataSet.RecNo <> -1) and not (ArtikelDBGrid.DataSource.DataSet.FieldByName('PICTURE_PATH').IsNull);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.FormCreate(Sender: TObject);
begin
  fShowCollis      := False;
  fShowMultiCollis := False;

  fEAN         := '';
  fArtikelNr   := '';
  RefLieferant := -1;

  fRefMand    := -1;
  fRefSubMand := -1;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (SubMandComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.ArtikelQueryAfterClose(DataSet: TDataSet);
begin
  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.CheckBoxUpdateClick(Sender: TObject);
begin
  if Visible and ArtikelQuery.Active then
    UpdateQuery;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.02.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.ARGrpComboBoxChange(Sender: TObject);
begin
  UpdateQuery;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TArtikelListeForm.ArtikelDBGridColumnSort(Sender: TCustomDBGridPro; const ColumnName: String): String;
begin
  if (ColumnName = 'ARTIKEL_NR') then
    Result := 'LPAD ('+ColumnName+',32,'' '')'
  else Result := ColumnName;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.ScannerErfassung (var Message: TMessage);
var
  res,
  refar,
  refae      : Integer;
  arnr,
  einh,
  errmsg     : String;
begin
  res := DetectArtikelBarcode (fRefMand, refar, refae, arnr, einh, errmsg);

  if (res = 0) and (Length (errmsg) = 0) then begin
    try
      if not (ArtikelDBGrid.DataSource.DataSet.Locate ('REF', refar, [])) then
        errmsg := 'Der Artikel ist in der Liste nicht vorhanden';
    except
      errmsg := 'Der Artikel konnte in der Liste nicht gefunden werden';
    end;
  end;

  if (Length (errmsg) > 0) then
    MessageDLG (errmsg, mtInformation	, [mbOk], 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.ShowArtikelPictureMenuItemClick(Sender: TObject);
var
  picpath : String;
  webform : TWebForm;
  imgform : TImageForm;
begin
  if (ArtikelDBGrid.DataSource.DataSet.Active) and (ArtikelDBGrid.DataSource.DataSet.RecNo <> -1) and not (ArtikelDBGrid.DataSource.DataSet.FieldByName('PICTURE_PATH').IsNull) then begin
    picpath := ArtikelDBGrid.DataSource.DataSet.FieldByName('PICTURE_PATH').AsString;

    if (LowerCase (copy (picpath, 1, 7)) = 'http://') or (LowerCase (copy (picpath, 1, 8)) = 'https://') then begin
      webform := TWebForm.Create (Self);
      webform.Name := 'ArtikelWebForm';

      try
        LVSConfigModul.RestoreFormInfo (webform);

        webform.Caption := GetResourceText (1230) + ' '+ArtikelDBGrid.DataSource.DataSet.FieldByName('ARTIKEL_NR').AsString+' / '+ArtikelDBGrid.DataSource.DataSet.FieldByName('ARTIKEL_TEXT').AsString;

        webform.ScaleFit := True;
        webform.WebBrowser.Navigate (picpath);

        webform.ShowModal;

        LVSConfigModul.SaveFormInfo (webform);
      finally
        webform.Release;
      end;
    end else if (LowerCase (copy (picpath, 1, 7)) = 'file://') then begin
      imgform := TImageForm.Create (Self);
      imgform.Name := 'ArtikelImageForm';

      try
        LVSConfigModul.RestoreFormInfo (imgform);

        imgform.Caption := GetResourceText (1230) + ' '+ArtikelDBGrid.DataSource.DataSet.FieldByName('ARTIKEL_NR').AsString+' / '+ArtikelDBGrid.DataSource.DataSet.FieldByName('ARTIKEL_TEXT').AsString;

        imgform.FileName := copy (picpath, 8);

        imgform.ShowModal;

        LVSConfigModul.SaveFormInfo (imgform);
      finally
        imgform.Release;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelListeForm.SubMandComboBoxChange(Sender: TObject);
var
  i        : Integer;
  blankstr : String;
  query    : TADOQuery;
begin
  if (ARGrpPanel.Visible) then begin
    ClearComboBoxObjects (ARGrpComboBox);
    ARGrpComboBox.Items.Add (GetResourceText (1020));

    query := TADOQuery.Create (Self);
    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select LEVEL, REF, NAME, BEZEICHNUNG from V_ARTIKEL_GRUPPE');

      if (SubMandPanel.Visible and (SubMandComboBox.ItemIndex >= 0)) then
        query.SQL.Add ('where REF_SUB_MAND='+IntToStr (GetComboBoxRef(SubMandComboBox)))
      else query.SQL.Add ('where REF_SUB_MAND is null and REF_MAND='+IntToStr (fRefMand));

      query.SQL.Add ('start with REF_PARENT is null connect by prior REF=REF_PARENT');
      query.SQL.Add ('order siblings by case when (substr (NAME,1,1) between ''0'' and ''9'') then lpad (NAME,9,'' '') else upper (NAME) end');

      try
        query.Open;

        while not (query.EOF) do begin
          blankstr := '';

          for i:= 0 to query.Fields [0].AsInteger do
            blankstr := blankstr + ' ';

          ARGrpComboBox.AddItem (blankstr+query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxRef.Create (query.Fields [1].AsInteger));

          query.Next;
        end;

        query.Close;
      except
      end;
    finally
      query.Free;
    end;

    ARGrpComboBox.Enabled := (ARGrpComboBox.Items.Count > 0);
    ARGrpComboBox.ItemIndex := 0;
  end;

  UpdateQuery;
end;

end.
