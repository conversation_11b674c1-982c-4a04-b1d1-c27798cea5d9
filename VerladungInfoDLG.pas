unit VerladungInfoDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TVerladungInfoForm = class(TForm)
    SealEdit: TEdit;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    VerlLabel: TLabel;
    SpedLabel: TLabel;
    Label7: TLabel;
    Bevel1: TBevel;
    Label8: TLabel;
    BridgeEdit: TEdit;
    Bevel2: TBevel;
    AbortButton: TButton;
    OkButton: TButton;
    Label9: TLabel;
    KFZEdit: TEdit;
    Label10: TLabel;
    FahrerEdit: TEdit;
    procedure FormCreate(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
    fRefVerl : Integer;
    fPflicht : Boolean;
  public
    property PflichtAngaben : Boolean read fPflicht write fPflicht;

    function Prepare (const RefVerl : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses
  DB, ADODB, DatenModul, VCLUtilitys, FrontendUtils, ConfigModul, SprachModul, ResourceText,
  FrontendMessages, LVSDatenInterface;

procedure TVerladungInfoForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res : integer;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := True;

    if (fPflicht and (Length (SealEdit.Text) = 0)) then begin
      CanClose := False;
      SealEdit.SetFocus;
    end else if (fPflicht and (Length (BridgeEdit.Text) = 0)) then begin
      CanClose := False;
      BridgeEdit.SetFocus;
    end;

    if (CanClose) then begin
      res := ChangeVerladungInfos (fRefVerl,
                                   KFZEdit.Text,
                                   FahrerEdit.Text,
                                   SealEdit.Text,
                                   BridgeEdit.Text);

      if (res <> 0) then begin
        CanClose := False;

        FrontendMessages.MessageDLG(FormatResourceText (1009, []) + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
      end;
    end;
  end;
end;

procedure TVerladungInfoForm.FormCreate(Sender: TObject);
begin
  fRefVerl := -1;
  fPflicht := False;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, MandantComboBox);
  {$endif}
end;

function TVerladungInfoForm.Prepare (const RefVerl : Integer) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  res := 0;

  fRefVerl := RefVerl;

  query := TADOQuery.Create (Self);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select verl.*,sped.NAME as SPED_NAME from V_WA_VERLADUNG verl left outer join V_SPEDITIONEN sped on (sped.REF=verl.REF_SPEDITION) where verl.REF=:ref');
    query.Parameters [0].Value := RefVerl;

    try
      query.Open;

      VerlLabel.Caption     := query.FieldByName ('VERLADE_NR').AsString;
      SpedLabel.Caption     := query.FieldByName ('SPED_NAME').AsString;

      KFZEdit.Text           := query.FieldByName ('KFZ_KENNZEICHEN').AsString;
      FahrerEdit.Text        := query.FieldByName ('FAHRER').AsString;
      SealEdit.Text          := query.FieldByName ('SEAL_NR').AsString;
      BridgeEdit.Text        := query.FieldByName ('BRIDGE_NR').AsString;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

end.
