object PrintNVEForm: TPrintNVEForm
  Left = 421
  Top = 263
  BorderStyle = bsDialog
  Caption = 'NVE-Label nachdrucken'
  ClientHeight = 154
  ClientWidth = 507
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnShow = FormShow
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'NVE-Nr.:'
  end
  object NVENrLabel: TLabel
    Left = 88
    Top = 8
    Width = 70
    Height = 13
    Caption = 'NVENrLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label2: TLabel
    Left = 8
    Top = 27
    Width = 60
    Height = 13
    Caption = 'NVE Anzahl:'
    Visible = False
  end
  object NVEAnzLabel: TLabel
    Left = 88
    Top = 27
    Width = 78
    Height = 13
    Caption = 'NVEAnzLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
    Visible = False
  end
  object Label3: TLabel
    Left = 8
    Top = 46
    Width = 72
    Height = 13
    Caption = 'NVE Spedition:'
    Visible = False
  end
  object NVESpeditionLabel: TLabel
    Left = 88
    Top = 46
    Width = 110
    Height = 13
    Caption = 'NVESpeditionLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
    Visible = False
  end
  object Panel2: TPanel
    Left = 0
    Top = 95
    Width = 507
    Height = 59
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    object Label8: TLabel
      Left = 8
      Top = 6
      Width = 38
      Height = 13
      Caption = 'Drucker'
    end
    object PrinterComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 305
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 0
      OnChange = PrinterComboBoxChange
    end
    object PrintButton: TButton
      Left = 319
      Top = 20
      Width = 75
      Height = 25
      Caption = 'Drucken'
      Default = True
      TabOrder = 1
      OnClick = PrintButtonClick
    end
    object CloseButton: TButton
      Left = 424
      Top = 20
      Width = 75
      Height = 25
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 2
    end
  end
  object PreviewCheckBox: TCheckBox
    Left = 8
    Top = 72
    Width = 313
    Height = 17
    Caption = 'Mit Vorschau drucken'
    TabOrder = 0
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 472
    Top = 8
  end
end
