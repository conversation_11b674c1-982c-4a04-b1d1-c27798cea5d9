﻿unit ImportArNachschubDLG;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.Grids, StringGridPro, Vcl.ExtCtrls, ComboBoxPro;

type
  TImportArNachschubForm = class(TForm)
    Bevel3: TBevel;
    ImportStringGrid: TStringGridPro;
    ReadButton: TButton;
    CreateButton: TButton;
    OpenDialog1: TOpenDialog;
    Label1: TLabel;
    LagerComboBox: TComboBoxPro;
    procedure ReadButtonClick(Sender: TObject);
    procedure CreateButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  DB, ADODB, VCLUtilitys, StringUtils, FrontendUtils, ConfigModul, DatenModul, LVSDatenInterface, VerlaufDLG, ResourceText;

procedure TImportArNachschubForm.CreateButtonClick(Sender: TObject);
var
  res,
  idx       : Integer;
  ref,
  refar,
  reflb,
  reflp,
  refzone   : Integer;
  minbes,
  maxbes    : Integer;
  infoform  : TVerlaufForm;
  query     : TADOQuery;
  ok        : Boolean;
  errmsg    : String;
begin
  infoform := TVerlaufForm.Create (Self);

  try
    infoform.Caption := GetResourceText (1796);
    infoform.Label1.Caption := GetResourceText (1796);

    infoform.Label2.Visible := True;
    infoform.Label4.Visible := True;
    infoform.Label2.Caption := GetResourceText (1131);

    infoform.BeginShowModal;

    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      Screen.Cursor := crSQLWait;

      try
        ok := false;

        repeat
          LVSDatenModul.BeginTransaction (LVSDatenModul.MainADOConnection);

          ok := true;

          try
            res := 0;
            errmsg := '';

            idx := ImportStringGrid.FixedRows;

            while (idx < ImportStringGrid.RowCount) and (res = 0) and not (infoform.AbortFlag) do begin
              reflb   := -1;
              refzone := -1;
              reflp   := -1;
              refar   := -1;

              if (Length (ImportStringGrid.Cells [1, idx]) > 0) then begin
                query.SQL.Clear;
                query.SQL.Add ('select REF from V_LB where REF_LAGER=:ref_lager and NAME=:name');
                query.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);
                query.Parameters.ParamByName('name').Value := ImportStringGrid.Cells [1, idx];

                query.Open;

                if not (query.Fields [0].IsNull) then
                  reflb := query.Fields [0].AsInteger
                else begin
                  res := 12;
                  errmsg := 'Unbekannter Lagerbereich - > ' + ImportStringGrid.Cells [1, idx];
                end;

                query.Close;

                if (res = 0) and (Length (ImportStringGrid.Cells [2, idx]) > 0) then begin
                  query.SQL.Clear;
                  query.SQL.Add ('select REF from V_LB_ZONE where REF_LB=:ref_lb and NAME=:name');
                  query.Parameters.ParamByName('ref_lb').Value := reflb;
                  query.Parameters.ParamByName('name').Value := ImportStringGrid.Cells [2, idx];

                  query.Open;

                  if not (query.Fields [0].IsNull) then
                    refzone := query.Fields [0].AsInteger
                  else begin
                    res := 12;
                    errmsg := 'Unbekannter Lagerbereichszone -> ' + ImportStringGrid.Cells [2, idx];
                  end;

                  query.Close;
                end;
              end;

              if (res = 0) and (Length (ImportStringGrid.Cells [4, idx]) > 0) then begin
                query.SQL.Clear;
                query.SQL.Add ('select REF from V_ARTIKEL where STATUS=''AKT'' and ARTIKEL_NR=:ar_nr');
                query.Parameters.ParamByName('ar_nr').Value := ImportStringGrid.Cells [4, idx];

                query.Open;

                if (query.RecordCount > 1) then begin
                  res := 15;
                  errmsg := 'Artikelnummer nicht eindeutig -> ' + ImportStringGrid.Cells [4, idx];
                end else if not (query.Fields [0].IsNull) then
                  refar := query.Fields [0].AsInteger
                else begin
                  res := 16;
                  errmsg := 'Unbekannter Artikelnummer  -> ' + ImportStringGrid.Cells [4, idx];
                end;

                query.Close;
              end;

              if (res = 0) and (Length (ImportStringGrid.Cells [5, idx]) > 0) then begin
                if not TryStrToInt (ImportStringGrid.Cells [5, idx], minbes) then begin
                  res := 20;
                  errmsg := 'Min. Bestand, ungültige Zahl';
                end;
              end;

              if (res = 0) and (Length (ImportStringGrid.Cells [6, idx]) > 0) then begin
                if not TryStrToInt (ImportStringGrid.Cells [6, idx], maxbes) then begin
                  res := 20;
                  errmsg := 'Max. Bestand, ungültige Zahl';
                end;
              end;

              if (res = 0) then begin
                res := InsertARLBNachschubParameter  (GetComboBoxRef (LagerComboBox),
                                                      -1,
                                                      refar,
                                                      reflb,
                                                      refzone,
                                                      reflp,
                                                      'VPE',
                                                      minbes,
                                                      maxbes,
                                                      -1,
                                                      -1,
                                                      ref);
              end;

              Inc (idx);
            end;

            (*
            if (Length (errmsg) > 0) then
              MessageDLG ('Fehler beim Verabeiten der Liste:'+#13+#13+errmsg, mtError, [mbOK], 0);
            *)

            if (res = 0) and not (infoform.AbortFlag) then begin
              LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trCommit);
            end else begin
              LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);

              if (res = 0) then
                MessageDLG (FormatMessageText (1710, []), mtError, [mbOK], 0)
              else if (Length (errmsg) > 0) then
                MessageDLG (FormatMessageText (1711, [IntToStr (idx), errmsg]), mtError, [mbOK], 0)
              else
                MessageDLG (FormatMessageText (1711, [IntToStr (idx), LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
            end;
          except
            on EOracleRetryException do;

            on Exception do begin
              res := -9;

              LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);

              Screen.Cursor := crDefault;

              MessageDLG (FormatMessageText (1711, []), mtError, [mbOK], 0);
            end;
          end;
        until (ok) or (res <> 0);
      finally
        Screen.Cursor := crDefault;
      end;
    finally
      infoform.EndShowModal;

      query.Free;
    end;
  finally
    infoform.Release;
  end;
end;

procedure TImportArNachschubForm.FormCreate(Sender: TObject);
begin
  LoadLagerCombobox (LagerComboBox, LVSDatenModul.AktLocation);

  if (LVSDatenModul.AktLagerRef > 0) then begin
    LagerComboBox.Enabled := false;
    LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, LVSDatenModul.AktLagerRef);
  end;
end;

procedure TImportArNachschubForm.ReadButtonClick(Sender: TObject);
var
  res,
  idx,
  feld,
  intwert,
  linecount,
  csvformat : Integer;
  instr     : AnsiString;
  utfline,
  errmsg    : String;
  impfile   : Textfile;
  strlist   : TStringList;
  oldmode   : Byte;
begin
  res := 0;

  errmsg := '';
  linecount := 0;

  if (OpenDialog1.Execute) then begin
    oldmode := FileMode;
    try
      FileMode := fmOpenRead + fmShareDenyWrite;

      AssignFile(impfile, OpenDialog1.FileName);

      Reset (impfile);
    finally
      FileMode := oldmode;
    end;

    if (IOResult <> 0) then
      errmsg := FormatMessageText (1714, [])
    else begin
      Screen.Cursor := crHourGlass;

      try
        idx := 0;

        strlist := TStringList.Create;

        Try
          //Kopfzeile mit den Beschreibungen lesen
          ReadLn (impfile, instr);

          Inc (linecount);

          if (IOResult <> 0) then begin
            res := -2;
            errmsg := FormatMessageText (1761, []);
          end else begin
            if (Pos (';', instr) > 0) and (Pos (',', instr) = 0) then
              strlist.Delimiter := ';'
            else if (Pos (',', instr) > 0) and (Pos (';', instr) = 0) then
              strlist.Delimiter := ','
            else
              errmsg := FormatMessageText (1760, []);
          end;

          if (Length (errmsg) = 0) then begin
            strlist.StrictDelimiter := True;

            strlist.DelimitedText := instr;

            while not Eof (impfile) and (Length (errmsg) = 0) do begin
              ReadLn (impfile, instr);

              if (IOResult <> 0) then begin
                res := -2;
                MessageDLG (FormatMessageText (1761, []), mtError, [mbOK], 0);
              end else begin
                Inc (linecount);

                utfline := Utf8ToAnsi (instr);

                //Nur übernehmen, wenn auch die Konvertierung geklappt hat
                if (Length (utfline) > 0) then
                  instr := utfline;

                strlist.DelimitedText := instr;

                ImportStringGrid.Cells [0, ImportStringGrid.FixedRows + idx] := FormatStr (IntToStr (idx + 1), -5, ' ');

                if (strlist.Count < 6) then
                  errmsg := FormatMessageText(1759, [IntToStr (6)])
                else begin
                  ImportStringGrid.Cells [1, ImportStringGrid.FixedRows + idx] := strlist [0];    //Bereich
                  ImportStringGrid.Cells [2, ImportStringGrid.FixedRows + idx] := strlist [1];    //Zone
                  ImportStringGrid.Cells [3, ImportStringGrid.FixedRows + idx] := strlist [2];    //LP
                  ImportStringGrid.Cells [4, ImportStringGrid.FixedRows + idx] := strlist [3];    //Artikel
                  ImportStringGrid.Cells [5, ImportStringGrid.FixedRows + idx] := strlist [4];    //Min. Bestand
                  ImportStringGrid.Cells [6, ImportStringGrid.FixedRows + idx] := strlist [5];    //Max. Bestand
                end;

                Inc (idx);
              end;

              ImportStringGrid.RowCount := ImportStringGrid.FixedRows + idx;
            end;
          end;
        finally
          strlist.Free;
        end;
      finally
        Closefile (impfile);

        Screen.Cursor := crDefault;
      end;

      if (Length (errmsg) > 0) then
        MessageDLG (FormatMessageText (1758, [IntToStr (linecount), errmsg]), mtError, [mbOK], 0)
    end;
  end;
end;

end.
