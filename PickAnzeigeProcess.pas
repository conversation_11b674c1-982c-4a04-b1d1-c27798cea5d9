unit PickAnzeigeProcess;

interface

uses
  SysUtils, Classes, OverbyteIcsWSocket, DB, ADODB, OverbyteIcsWndControl;

const
  CRYPT_CODE = 11342;

  type
  TAnzeigeEntry = record
    Ref  : Integer;
    LED  : String;
  end;

  TPickAnzeigeModule = class(TDataModule)
    WSocket1: TWSocket;
    ADOQuery1: TADOQuery;
    ADOConnection1: TADOConnection;
    ADOQuery2: TADOQuery;
    procedure WSocket1DataSent(Sender: TObject; ErrCode: Word);
    procedure DataModuleCreate(Sender: TObject);
    procedure DataModuleDestroy(Sender: TObject);
  private
    fFirstFlag     : Boolean;
    fPortNr        : Integer;
    fIPAddress     : String;
    fDBConnectInfo : String;
    fDBUser        : String;
    fDBPasswd      : String;

    fSendFlag      : Boolean;
    fAlertName     : String;
    fProjectName   : String;
    fCmdNr         : Integer;
    fAnzeigeBuffer : array [0..1023] of TAnzeigeEntry;

    function  CheckDatabase : Integer;
    function  SendPickCommand (const CmdStr : String) : Integer;
  public
    property PortNr    : Integer read fPortNr    write fPortNr;
    property IPAddress : String  read fIPAddress write fIPAddress;

    property DBConnectInfo : String read fDBConnectInfo write fDBConnectInfo;
    property DBUser        : String read fDBUser        write fDBUser;
    property DBPasswd      : String read fDBPasswd      write fDBPasswd;

    function DoPickAnzeigeUpdate : Integer;
  end;

var
  PickAnzeigeModule: TPickAnzeigeModule;

implementation

{$R *.dfm}

uses
  StringUtils, Forms, LogFile, EncryptUtils, LEDTestWallDLG;

function TPickAnzeigeModule.SendPickCommand (const CmdStr : String) : Integer;
var
  i,
  res,
  count  : Integer;
begin
  res := 0;

  if (WSocket1.State <> wsConnected) then begin
    if (Length (fIPAddress) > 0) then begin
      fCmdNr := 1;

      WSocket1.Proto   := 'tcp';
      WSocket1.Addr    := fIPAddress;
      WSocket1.Port    := IntToStr (fPortNr);

      try
        WSocket1.Connect;

        count := 100;

        while not (WSocket1.State = wsConnected) and (count > 0) do begin
          Application.ProcessMessages;
          Sleep (10);
          Dec (count);
        end;

        if (WSocket1.State = wsConnected) then begin
          for i := 0 to 1023 do begin
            fAnzeigeBuffer [i].Ref := i;
            fAnzeigeBuffer [i].LED := '';
          end;

          fSendFlag := False;

          WSocket1.SendStr (#2 + FormatIntToStr (fCmdNr, 2) + '001' + '001' + 'RS1' + #3);
          fCmdNr := (fCmdNr + 1) mod 100;

          count := 100;
          while not (fSendFlag) and (count > 0) do begin
            Application.ProcessMessages;
            Sleep (10);
            Dec (count);
          end;

          if not (fSendFlag) then
            res := -8;
        end else begin
          res := -5;
          WSocket1.Abort;
        end;
      except
        res := -4;
        WSocket1.Abort;
      end;
    end;
  end;

  if (res = 0) and (WSocket1.State = wsConnected) and (Length (CmdStr) > 0) then begin
    count := 100;

    while not (fSendFlag) and (count > 0) do begin
      Application.ProcessMessages;
      Sleep (10);
      Dec (count);
    end;

    if not (fSendFlag) then
      res := -8
    else begin
      fSendFlag := False;
      WSocket1.SendStr (#2 + FormatIntToStr (fCmdNr, 2) + '001' + '001' + CmdStr + #3);

      fCmdNr := (fCmdNr + 1) mod 100;
    end;
  end;

  Result := res;
end;

function TPickAnzeigeModule.CheckDatabase : Integer;
var
  res      : Integer;
  alertstr : String;
  query    : TADOQuery;
begin
  res := 0;

  if not (ADOConnection1.Connected) then begin
    ADOConnection1.ConnectionString := fDBConnectInfo;

    if Assigned (MasterLog) then MasterLog.Write ('Try connect to: '+fDBConnectInfo);

    try
      ADOConnection1.Open (fDBUser, Decrypt (fDBPasswd, CRYPT_CODE));

      if (ADOConnection1.Connected) then begin
        fFirstFlag := True;

        query := TADOQuery.Create (Self);

        try
          query.Connection := ADOConnection1;

          query.SQL.Add ('select BASE_PROCESS.getprojectname from dual');

          try
            query.Open;

            fProjectName := query.Fields [0].AsString;

            query.Close;
          except
            on  E: Exception do begin
              res := -9;
              if Assigned (MasterLog) then MasterLog.Write ('Error while reading project' + e.ClassName + ' : ' + e.Message);
            end;
          end;


          if (res = 0) then begin
            query.SQL.Clear;
            query.SQL.Add ('select * from ALERT_INFOS where PROJECT=BASE_PROCESS.getprojectname and ALERT_TYP=''PICKANZEIGE''');
            query.Open;

            alertstr := query.FieldByName ('ALERT_NAME').AsString;

            query.Close;

            if (Length (alertstr) > 0) then
              fAlertName := alertstr
            else begin
              fAlertName := fProjectName+'.'+'PICK';

              query.SQL.Clear;
              query.SQL.Add ('insert into ALERT_INFOS (PROJECT, ALERT_TYP, ALERT_NAME) values ('+#39+fProjectName+#39+',''PICKANZEIGE'', '+#39+fAlertName+#39+')');

              query.ExecSQL;
            end;
          end;
        finally
          query.Free;
        end;
      end;
    except
      on E: Exception do begin
        if Assigned (MasterLog) then MasterLog.Write ('Error while connect: '+E.Message);
      end;
    end;
  end;

  Result := 0;
end;

procedure TPickAnzeigeModule.DataModuleCreate(Sender: TObject);
begin
  fCmdNr := 1;
end;

procedure TPickAnzeigeModule.DataModuleDestroy(Sender: TObject);
begin
  ADOConnection1.Close;
  WSocket1.Close;
end;

function TPickAnzeigeModule.DoPickAnzeigeUpdate : Integer;
var
  res,
  ref : Integer;
begin
  res := 0;

  CheckDatabase;

  if (ADOConnection1.Connected) then begin
    ADOQuery1.SQL.Clear;

    if (fFirstFlag) then
      ADOQuery1.SQL.Add ('select * from PICKANZEIGE')
    else
      ADOQuery1.SQL.Add ('select * from PICKANZEIGE where UPDATE_FLAG=''1''');

    try
      ADOQuery1.Open;

      try
        while (ADOConnection1.Connected) and not (ADOQuery1.Eof) and (res = 0) do begin
          ref := ADOQuery1.FieldByName ('REF').AsInteger;

          if (fAnzeigeBuffer [ref].LED <> ADOQuery1.FieldByName ('LED').AsString) then begin
            if Assigned (MasterLog) then MasterLog.Write ('Update display: '+ADOQuery1.FieldByName ('ANZEIGE').AsString +' to ' +ADOQuery1.FieldByName ('LED').AsString);

            //LEDTestWallForm.SetLED (ADOQuery1.FieldByName ('FACH_NR').AsInteger, ADOQuery1.FieldByName ('LED').AsString);

            res := SendPickCommand ('DI' + FormatStr (ADOQuery1.FieldByName ('ANZEIGE').AsString, 16 , ' ') + '0001' + #27 + '4'+ADOQuery1.FieldByName ('LED').AsString);

            if (res = 0) then begin
              fAnzeigeBuffer [ref].LED := ADOQuery1.FieldByName ('LED').AsString;

              ADOQuery2.SQL.Clear;
              ADOQuery2.SQL.Add ('update PICKANZEIGE set UPDATE_FLAG=''0'', LAST_DISPLAY=sysdate where REF=:ref');
              ADOQuery2.Parameters [0].Value := ADOQuery1.FieldByName ('REF').AsInteger;

              ADOQuery2.ExecSQL;
              //ADOConnection1.CommitTrans;
            end else begin
              if Assigned (MasterLog) then MasterLog.Write ('Display error: '+IntToStr (res));
            end;
          end;

          if (ADOConnection1.Connected) then
            ADOQuery1.Next;
        end;

        if (res = 0) and (ADOConnection1.Connected) then
          fFirstFlag := False;
      finally
        if (ADOQuery1.Active) then
          ADOQuery1.Close;
      end;
    except
      on  E: Exception do begin
        res := -9;

        ADOConnection1.Close;

        if Assigned (MasterLog) then MasterLog.Write ('Error while query anzeige' + e.ClassName + ' : ' + e.Message);
      end;
    end;
  end;

  Result := 0;
end;

procedure TPickAnzeigeModule.WSocket1DataSent(Sender: TObject; ErrCode: Word);
begin
  fSendFlag := True;
end;

end.
