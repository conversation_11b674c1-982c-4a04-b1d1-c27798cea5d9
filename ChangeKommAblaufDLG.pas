unit ChangeKommAblaufDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro;

type
  TChangeKommAblaufForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    Label8: TLabel;
    KommAblaufComboBox: TComboBoxPro;
    procedure FormDestroy(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, FrontendUtils, SprachModul;

procedure TChangeKommAblaufForm.FormCreate(Sender: TObject);
begin
  LoadComboxDBItems (KommAblaufComboBox, 'LB', 'KOMM_ABLAUF');

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, KommAblaufComboBox);
  {$endif}
end;

procedure TChangeKommAblaufForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (KommAblaufComboBox);
end;

end.
