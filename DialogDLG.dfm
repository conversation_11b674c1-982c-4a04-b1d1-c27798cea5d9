object DialogSuppressedForm: TDialogSuppressedForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Dialoge'
  ClientHeight = 210
  ClientWidth = 389
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnShow = FormShow
  DesignSize = (
    389
    210)
  PixelsPerInch = 96
  TextHeight = 13
  object Bevel1: TBevel
    Left = 8
    Top = 163
    Width = 373
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 113
    ExplicitWidth = 435
  end
  object KommSollIstCheckBox: TCheckBox
    Left = 8
    Top = 104
    Width = 265
    Height = 17
    Caption = 'Kommissionierung: Ist = Soll setzen'
    TabOrder = 1
  end
  object AufFehleMengeCheckBox: TCheckBox
    Left = 8
    Top = 127
    Width = 265
    Height = 17
    Caption = 'Auftragabschluss: Fehlmengen '#252'bernehmen'
    Color = clBtnFace
    ParentColor = False
    TabOrder = 2
  end
  object OkButton: TButton
    Left = 218
    Top = 178
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 3
  end
  object AbortButton: TButton
    Left = 306
    Top = 178
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 4
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 389
    Height = 81
    Align = alTop
    BevelOuter = bvNone
    Color = clBtnHighlight
    Enabled = False
    ParentBackground = False
    TabOrder = 0
    DesignSize = (
      389
      81)
    object Label1: TLabel
      Left = 10
      Top = 8
      Width = 371
      Height = 48
      Anchors = [akLeft, akTop, akRight, akBottom]
      AutoSize = False
      Caption = 
        'Gewisse R'#252'ckfragdialoge in der Anwendung k'#246'nnen dauerhaft unterd' +
        'r'#252'ckt werden.'#13#10'Hier haben Sie die M'#246'glichkeit, diese Dialoge wie' +
        'der erscheinen zu lassen.'
      WordWrap = True
      ExplicitHeight = 33
    end
    object CheckBox1: TCheckBox
      Left = 212
      Top = 62
      Width = 169
      Height = 17
      TabStop = False
      Anchors = [akLeft, akBottom]
      Caption = 'Dialog wird angezeigt'
      Checked = True
      State = cbChecked
      TabOrder = 0
    end
    object CheckBox2: TCheckBox
      Left = 10
      Top = 62
      Width = 196
      Height = 17
      TabStop = False
      Anchors = [akLeft, akBottom]
      Caption = 'Der Dialog wird nicht angezeigt'
      TabOrder = 1
    end
  end
end
