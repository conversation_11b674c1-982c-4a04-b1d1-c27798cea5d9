unit DashBoardDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComCtrls;

type
  TDashBoardForm = class(TForm)
    CloseButton: TButton;
    UpdateTimer: TTimer;
    CockpitPageControl: TPageControl;
    TabSheet1: TTabSheet;
    ActualTabSheet: TTabSheet;
    GroupBox1: TGroupBox;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    AufAnzOpenLabel: TLabel;
    Bevel1: TBevel;
    AufAnzLabel: TLabel;
    AufVPELabel: TLabel;
    Label7: TLabel;
    AufPosAnzLabel: TLabel;
    Label5: TLabel;
    AufBesOpenLabel: TLabel;
    Label10: TLabel;
    AufFehlwareLabel: TLabel;
    GroupBox2: TGroupBox;
    GroupBox3: TGroupBox;
    GroupBox5: TGroupBox;
    AufStatistikListView: TListView;
    DashboardTabSheet: TTabSheet;
    Label4: TLabel;
    DashOrdersLabel: TLabel;
    Label8: TLabel;
    DashKommsLabel: TLabel;
    Label11: TLabel;
    Label12: TLabel;
    Label13: TLabel;
    Label14: TLabel;
    Label15: TLabel;
    DashTransLabel: TLabel;
    Label17: TLabel;
    DashNachschubLabel: TLabel;
    DashTimeLabel: TLabel;
    DashOrderVPELabel: TLabel;
    DashKommVPELabel: TLabel;
    DashTranVPELabel: TLabel;
    DashNachVPELabel: TLabel;
    Label6: TLabel;
    Label9: TLabel;
    Label16: TLabel;
    DashWAsLabel: TLabel;
    DashWAVPELabel: TLabel;
    procedure CloseButtonClick(Sender: TObject);
    procedure UpdateTimerTimer(Sender: TObject);
    procedure FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure TabSheet1Show(Sender: TObject);
    procedure DashboardTabSheetShow(Sender: TObject);
    procedure CockpitPageControlChange(Sender: TObject);
  private
    procedure UpdateDashBoard (Sender: TObject);
  public
    { Public-Deklarationen }
  end;

implementation

uses
  DB, ADODB, BetterADODataSet, DatenModul, SprachModul;

{$R *.dfm}

procedure TDashBoardForm.CloseButtonClick(Sender: TObject);
begin
  Close;
end;

procedure TDashBoardForm.UpdateDashBoard (Sender: TObject);
var
  query : TBetterADODataSet;
begin
  if (LVSDatenModul.MainADOConnection.Connected) then begin
    query := TBetterADODataSet.Create(Self);

    Screen.Cursor := crSQLWait;

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      if (CockpitPageControl.ActivePage = ActualTabSheet) then begin
        query.CommandText := 'select * from V_DASHBOARD_AUFTRAG where ';

        if (LVSDatenModul.AktLagerRef <> -1) then begin
          query.CommandText := query.CommandText + 'REF_LAGER=:ref_lager';
          query.Parameters.ParamByName ('ref_lager').Value := LVSDatenModul.AktLagerRef;
        end else begin
          query.CommandText := query.CommandText + 'REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc)';
          query.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
        end;

        if (LVSDatenModul.AktMandantRef <> -1) then begin
          query.CommandText := query.CommandText + ' and REF_MAND=:ref_mand';
          query.Parameters.ParamByName ('ref_mand').Value := LVSDatenModul.AktMandantRef;
        end;

        query.Open;

        AufAnzLabel.Caption := query.FieldByName ('COUNT_AUF').AsString;
        AufPosAnzLabel.Caption := query.FieldByName ('COUNT_AUF_POS').AsString;
        AufVPELabel.Caption := query.FieldByName ('SUM_VPE').AsString;
        AufAnzOpenLabel.Caption := query.FieldByName ('COUNT_OPEN_AUF').AsString + ' / ' +query.FieldByName ('SUM_OPEN_VPE').AsString+ ' VPEs';
        //AufBesOpenLabel.Caption := query.FieldByName ('COUNT_BESTAND_AUF').AsString + ' / ' +query.FieldByName ('SUM_BESTAND_VPE').AsString+ ' VPEs';
        //AufFehlwareLabel.Caption := query.FieldByName ('COUNT_FEHLWARE_AUF').AsString + ' / ' +query.FieldByName ('SUM_FEHLWARE_VPE').AsString+ ' VPEs';

        query.Close;
      end else if (CockpitPageControl.ActivePage = DashboardTabSheet) then begin
        query.CommandText := 'select * from V_DASHBOARD_AUFTRAG where ';

        if (LVSDatenModul.AktLagerRef <> -1) then begin
          query.CommandText := query.CommandText + 'REF_LAGER=:ref_lager';
          query.Parameters.ParamByName ('ref_lager').Value := LVSDatenModul.AktLagerRef;
        end else begin
          query.CommandText := query.CommandText + 'REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc)';
          query.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
        end;

        if (LVSDatenModul.AktMandantRef <> -1) then begin
          query.CommandText := query.CommandText + ' and REF_MAND=:ref_mand';
          query.Parameters.ParamByName ('ref_mand').Value := LVSDatenModul.AktMandantRef;
        end;

        query.Open;

        DashOrdersLabel.Caption := query.FieldByName ('COUNT_OPEN_AUF').AsString;
        DashOrderVPELabel.Caption := query.FieldByName ('SUM_OPEN_VPE').AsString;

        DashWAsLabel.Caption := query.FieldByName ('COUNT_OPEN_WA').AsString;
        DashWAVPELabel.Caption := query.FieldByName ('SUM_OPEN_WA_VPE').AsString;

        query.Close;

        query.CommandText := 'select * from V_DASHBOARD_KOMM where ';

        if (LVSDatenModul.AktLagerRef <> -1) then begin
          query.CommandText := query.CommandText + 'REF_LAGER=:ref_lager';
          query.Parameters.ParamByName ('ref_lager').Value := LVSDatenModul.AktLagerRef;
        end else begin
          query.CommandText := query.CommandText + 'REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc)';
          query.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
        end;

        if (LVSDatenModul.AktMandantRef <> -1) then begin
          query.CommandText := query.CommandText + ' and REF_MAND=:ref_mand';
          query.Parameters.ParamByName ('ref_mand').Value := LVSDatenModul.AktMandantRef;
        end;

        query.Open;

        DashKommsLabel.Caption := query.FieldByName ('COUNT_OPEN_KOMM').AsString;
        DashKommVPELabel.Caption := query.FieldByName ('SUM_OPEN_VPE').AsString;

        query.Close;

        query.CommandText := 'select * from V_DASHBOARD_NACHSCHUB where ';

        if (LVSDatenModul.AktLagerRef <> -1) then begin
          query.CommandText := query.CommandText + 'REF_LAGER=:ref_lager';
          query.Parameters.ParamByName ('ref_lager').Value := LVSDatenModul.AktLagerRef;
        end else begin
          query.CommandText := query.CommandText + 'REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc)';
          query.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
        end;

        query.Open;

        DashNachschubLabel.Caption := query.FieldByName ('COUNT_OPEN_NACH').AsString;
        DashNachVPELabel.Caption   := query.FieldByName ('SUM_OPEN_VPE').AsString;

        query.Close;

        DashTimeLabel.Caption := TimeToStr(Now);
      end;
    finally
      query.Free;

      Screen.Cursor := crDefault;
    end;
  end else begin
    AufAnzLabel.Caption := '---';
    AufVPELabel.Caption := '---';
  end;
end;

procedure TDashBoardForm.CockpitPageControlChange(Sender: TObject);
begin
  UpdateDashBoard (Sender);
end;

procedure TDashBoardForm.DashboardTabSheetShow(Sender: TObject);
begin
  DashOrdersLabel.Caption := '';
  DashOrderVPELabel.Caption := '';
  DashKommsLabel.Caption := '';
  DashKommVPELabel.Caption := '';
  DashWAsLabel.Caption := '';
  DashWAVPELabel.Caption := '';
  DashTransLabel.Caption := '';
  DashTranVPELabel.Caption := '';
  DashNachschubLabel.Caption := '';
  DashNachVPELabel.Caption := '';

  DashTimeLabel.Caption := '';
end;

procedure TDashBoardForm.FormCreate(Sender: TObject);
begin
  AufAnzLabel.Caption := '---';
  AufPosAnzLabel.Caption := '---';
  AufVPELabel.Caption := '---';
  AufAnzOpenLabel.Caption := '---';
  AufBesOpenLabel.Caption := '---';
  AufFehlwareLabel.Caption := '---';
  AufAnzLabel.Caption := '---';
  AufVPELabel.Caption := '---';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AufAnzLabel);
    LVSSprachModul.SetNoTranslate (Self, AufPosAnzLabel);
    LVSSprachModul.SetNoTranslate (Self, AufVPELabel);
    LVSSprachModul.SetNoTranslate (Self, AufAnzOpenLabel);
    LVSSprachModul.SetNoTranslate (Self, AufBesOpenLabel);
    LVSSprachModul.SetNoTranslate (Self, AufFehlwareLabel);
    LVSSprachModul.SetNoTranslate (Self, AufAnzLabel);
    LVSSprachModul.SetNoTranslate (Self, AufVPELabel);
    LVSSprachModul.SetNoTranslate (Self, DashOrdersLabel);
    LVSSprachModul.SetNoTranslate (Self, DashOrderVPELabel);
    LVSSprachModul.SetNoTranslate (Self, DashKommsLabel);
    LVSSprachModul.SetNoTranslate (Self, DashKommVPELabel);
    LVSSprachModul.SetNoTranslate (Self, DashWAsLabel);
    LVSSprachModul.SetNoTranslate (Self, DashWAVPELabel);
    LVSSprachModul.SetNoTranslate (Self, DashTransLabel);
    LVSSprachModul.SetNoTranslate (Self, DashTranVPELabel);
    LVSSprachModul.SetNoTranslate (Self, DashNachschubLabel);
    LVSSprachModul.SetNoTranslate (Self, DashNachVPELabel);

    LVSSprachModul.SetNoTranslate (Self, DashTimeLabel);
  {$endif}
end;

procedure TDashBoardForm.FormKeyDown(Sender: TObject; var Key: Word;
  Shift: TShiftState);
begin
  if (Key = VK_F5) then begin
    UpdateTimer.Enabled := False;

    UpdateDashBoard (Sender);

    UpdateTimer.Enabled := True;
  end;
end;

procedure TDashBoardForm.FormShow(Sender: TObject);
begin
  UpdateDashBoard (Sender);
end;

procedure TDashBoardForm.TabSheet1Show(Sender: TObject);
var
  query : TBetterADODataSet;
begin
  if (LVSDatenModul.MainADOConnection.Connected) then begin
    query := TBetterADODataSet.Create(Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;


      query.CommandText := 'select avg (count (ap.REF)), avg (sum (ap.MENGE_BESTELLT)) from AUFTRAG auf, AUFTRAG_POS ap where auf.REF=ap.REF_AUF_KOPF';
      query.CommandText := query.CommandText + ' and versand_datum between :from and :to';
      query.CommandText := query.CommandText + ' group by auf.AUFTRAGSART, auf.REF';
    finally
      query.Free;
    end;
  end;
end;

procedure TDashBoardForm.UpdateTimerTimer(Sender: TObject);
begin
  if Visible then
    UpdateDashBoard (Sender);
end;

end.
