object LTEditForm: TLTEditForm
  Left = 588
  Top = 268
  BorderStyle = bsDialog
  Caption = 'LTEditForm'
  ClientHeight = 781
  ClientWidth = 454
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    454
    781)
  TextHeight = 13
  object OkButton: TButton
    Left = 283
    Top = 751
    Width = 75
    Height = 25
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akRight, akBottom]
    Caption = 'OK'
    Default = True
    ModalResult = 1
    TabOrder = 4
  end
  object AbortButton: TButton
    Left = 371
    Top = 750
    Width = 75
    Height = 25
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = '&Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
  object MandantPanel: TPanel
    Left = 0
    Top = 0
    Width = 454
    Height = 53
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      454
      53)
    object Bevel5: TBevel
      Left = 8
      Top = 49
      Width = 437
      Height = 4
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label4: TLabel
      Left = 8
      Top = 8
      Width = 42
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 8
      Top = 23
      Width = 437
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      TabOrder = 0
    end
  end
  object TemplatePanel: TPanel
    Left = 0
    Top = 103
    Width = 454
    Height = 49
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      454
      49)
    object Label17: TLabel
      Left = 8
      Top = 3
      Width = 36
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Vorlage'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 45
      Width = 437
      Height = 4
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object TemplateComboBox: TComboBoxPro
      Left = 8
      Top = 18
      Width = 437
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      TabOrder = 0
      OnChange = TemplateComboBoxChange
    end
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 152
    Width = 454
    Height = 589
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      454
      589)
    object Label1: TLabel
      Left = 8
      Top = 3
      Width = 99
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Ladungstr'#228'ger Name'
    end
    object Label2: TLabel
      Left = 8
      Top = 275
      Width = 64
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Tara-Gewicht'
    end
    object Label5: TLabel
      Left = 8
      Top = 45
      Width = 136
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Ladungstr'#228'ger Beschreibung'
    end
    object Label6: TLabel
      Left = 101
      Top = 368
      Width = 12
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'kg'
    end
    object Bevel2: TBevel
      Left = 8
      Top = 269
      Width = 440
      Height = 9
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Bevel3: TBevel
      Left = 5
      Top = 341
      Width = 440
      Height = 9
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label8: TLabel
      Left = 8
      Top = 203
      Width = 46
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'EDI-Code'
    end
    object Label11: TLabel
      Left = 179
      Top = 349
      Width = 111
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      Caption = 'Max. Fassungsvolumen'
    end
    object Label12: TLabel
      Left = 287
      Top = 370
      Width = 11
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      Caption = 'm'#179
    end
    object Label13: TLabel
      Left = 8
      Top = 349
      Width = 89
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Max. Nettogewicht'
    end
    object Label14: TLabel
      Left = 98
      Top = 294
      Width = 12
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'kg'
    end
    object Label15: TLabel
      Left = 213
      Top = 203
      Width = 46
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Artikel-Nr.'
    end
    object Label16: TLabel
      Left = 367
      Top = 3
      Width = 57
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      Caption = 'Reihenfolge'
    end
    object Bevel6: TBevel
      Left = 7
      Top = 392
      Width = 440
      Height = 6
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label18: TLabel
      Left = 309
      Top = 203
      Width = 22
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      Caption = 'EAN'
    end
    object Bevel7: TBevel
      Left = 7
      Top = 133
      Width = 440
      Height = 9
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label20: TLabel
      Left = 125
      Top = 275
      Width = 30
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'L'#228'nge'
    end
    object Label21: TLabel
      Left = 179
      Top = 275
      Width = 27
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Breite'
    end
    object Label22: TLabel
      Left = 234
      Top = 275
      Width = 26
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'H'#246'he'
    end
    object Label25: TLabel
      Left = 282
      Top = 294
      Width = 16
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'mm'
    end
    object Label3: TLabel
      Left = 304
      Top = 370
      Width = 21
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      Caption = 'oder'
    end
    object Label7: TLabel
      Left = 404
      Top = 370
      Width = 8
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      Caption = '%'
    end
    object Label9: TLabel
      Left = 335
      Top = 349
      Width = 63
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      Caption = 'Max. F'#252'llgrad'
    end
    object Bevel9: TBevel
      Left = 7
      Top = 197
      Width = 440
      Height = 6
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label10: TLabel
      Left = 309
      Top = 275
      Width = 62
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'H'#246'henklasse'
    end
    object Label23: TLabel
      Left = 257
      Top = 3
      Width = 11
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'ID'
    end
    object Label24: TLabel
      Left = 8
      Top = 403
      Width = 128
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Ladehilfsmittelerwaltung als'
    end
    object Label27: TLabel
      Left = 367
      Top = 45
      Width = 55
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      Caption = 'Fachanzahl'
    end
    object Label28: TLabel
      Left = 257
      Top = 87
      Width = 113
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Verpackungsart-Gruppe'
    end
    object Label29: TLabel
      Left = 257
      Top = 45
      Width = 33
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      Caption = 'UN-Nr.'
    end
    object Label30: TLabel
      Left = 100
      Top = 318
      Width = 71
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Alignment = taRightJustify
      Caption = #220'berbreite in %'
    end
    object Label31: TLabel
      Left = 8
      Top = 87
      Width = 96
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Verwendungszweck'
    end
    object NameEdit: TEdit
      Left = 8
      Top = 19
      Width = 237
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 0
      Text = 'NameEdit'
      OnChange = ChangeNotify
    end
    object TaraGewichtEdit: TEdit
      Left = 8
      Top = 291
      Width = 87
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      MaxLength = 12
      TabOrder = 17
      Text = 'TaraGewichtEdit'
      OnChange = ChangeNotify
      OnKeyPress = TaraGewichtEditKeyPress
    end
    object BeschreibungEdit: TEdit
      Left = 8
      Top = 60
      Width = 237
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 4
      Text = 'BeschreibungEdit'
      OnChange = ChangeNotify
    end
    object EDIComboBox: TComboBoxPro
      Left = 8
      Top = 218
      Width = 190
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      TabOrder = 12
      OnChange = ChangeNotify
    end
    object MaxVolumenEdit: TEdit
      Left = 179
      Top = 365
      Width = 103
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      MaxLength = 12
      TabOrder = 25
      Text = 'VolumenEdit'
      OnChange = ChangeNotify
      OnExit = MaxVolumenEditExit
      OnKeyPress = VolumenEditKeyPress
    end
    object MaxKommGwEdit: TEdit
      Left = 8
      Top = 365
      Width = 87
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      MaxLength = 12
      TabOrder = 24
      Text = 'VolumenEdit'
      OnChange = ChangeNotify
      OnKeyPress = VolumenEditKeyPress
    end
    object ArtikelNrEdit: TEdit
      Left = 213
      Top = 218
      Width = 83
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 13
      Text = 'ArtikelNrEdit'
      OnChange = ChangeNotify
    end
    object FolgeEdit: TEdit
      Left = 367
      Top = 19
      Width = 56
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      MaxLength = 2
      TabOrder = 2
      Text = '0'
      OnChange = ChangeNotify
    end
    object FolgeUpDown: TUpDown
      Left = 423
      Top = 19
      Width = 15
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Associate = FolgeEdit
      Max = 9
      TabOrder = 3
    end
    object WEPflichtRadioGroup: TRadioGroup
      Left = 8
      Top = 505
      Width = 437
      Height = 39
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akRight, akBottom]
      Columns = 3
      Items.Strings = (
        'keine Pflicht'
        'Pflicht, 0 zul'#228'ssig'
        'Pflicht, 0 nicht zul'#228'ssig')
      TabOrder = 30
      OnClick = PflichtRadioGroupClick
    end
    object WEPflichtCheckBox: TCheckBox
      Left = 21
      Top = 505
      Width = 267
      Height = 17
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akBottom]
      Caption = 'Pflichtangabe im Wareneingang'
      TabOrder = 29
      OnClick = WEPflichtCheckBoxClick
    end
    object WAPflichtRadioGroup: TRadioGroup
      Left = 8
      Top = 551
      Width = 437
      Height = 39
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akRight, akBottom]
      Columns = 3
      Items.Strings = (
        'keine Pflicht'
        'Pflicht, 0 zul'#228'ssig'
        'Pflicht, 0 nicht zul'#228'ssig')
      TabOrder = 32
      OnClick = PflichtRadioGroupClick
    end
    object WAPflichtCheckBox: TCheckBox
      Left = 21
      Top = 550
      Width = 267
      Height = 17
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akBottom]
      Caption = 'Pflichtangabe im Warenausgang'
      TabOrder = 31
      OnClick = WAPflichtCheckBoxClick
    end
    object EANEdit: TEdit
      Left = 309
      Top = 218
      Width = 136
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      MaxLength = 32
      TabOrder = 14
      Text = 'EANEdit'
      OnChange = ChangeNotify
    end
    object PermaCheckBox: TCheckBox
      Left = 8
      Top = 140
      Width = 437
      Height = 16
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 
        'Der Ladungstr'#228'ger kann wieder verwendet werden und wird nie gel'#246 +
        'scht'
      TabOrder = 9
      OnClick = ChangeNotify
    end
    object LEdit: TEdit
      Left = 125
      Top = 291
      Width = 45
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      TabOrder = 18
      Text = 'LEdit'
      OnChange = ChangeNotify
      OnExit = EditAbmessungExit
      OnKeyPress = EditAbmessungKeyPress
    end
    object BEdit: TEdit
      Left = 179
      Top = 291
      Width = 45
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      TabOrder = 19
      Text = 'BEdit'
      OnChange = ChangeNotify
      OnExit = EditAbmessungExit
      OnKeyPress = EditAbmessungKeyPress
    end
    object HEdit: TEdit
      Left = 234
      Top = 291
      Width = 45
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      TabOrder = 20
      Text = 'HEdit'
      OnChange = ChangeNotify
      OnExit = EditAbmessungExit
      OnKeyPress = EditAbmessungKeyPress
    end
    object FillEdit: TEdit
      Left = 335
      Top = 365
      Width = 63
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      TabOrder = 26
      Text = 'FillEdit'
      OnChange = ChangeNotify
      OnExit = FillEditExit
      OnKeyPress = FillEditKeyPress
    end
    object AutoDeleteCheckBox: TCheckBox
      Left = 8
      Top = 158
      Width = 434
      Height = 17
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 
        'Leer gewordene LEs werden automatisch von den LPs abgebucht und ' +
        'gel'#246'scht'
      TabOrder = 10
      OnClick = ChangeNotify
    end
    object LTHKLComboBox: TComboBoxPro
      Left = 309
      Top = 291
      Width = 136
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 21
      OnChange = ChangeNotify
    end
    object IDEdit: TEdit
      Left = 257
      Top = 19
      Width = 91
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 16
      TabOrder = 1
      Text = 'IDEdit'
      OnChange = ChangeNotify
    end
    object PfandTauschRadioGroup: TRadioGroup
      Left = 9
      Top = 454
      Width = 437
      Height = 39
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      Caption = 'Art der R'#252'ckf'#252'hrung'
      Columns = 3
      ItemIndex = 0
      Items.Strings = (
        'Einweg'
        'Tausch'
        'Pfand')
      TabOrder = 28
      OnClick = ChangeNotify
    end
    object LTLeerComboBox: TComboBoxPro
      Left = 8
      Top = 419
      Width = 437
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      TabOrder = 27
      OnChange = ChangeNotify
    end
    object FachAnzEdit: TEdit
      Left = 367
      Top = 60
      Width = 54
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      MaxLength = 2
      TabOrder = 6
      Text = '0'
      OnChange = ChangeNotify
    end
    object RelocationCheckBox: TCheckBox
      Left = 8
      Top = 177
      Width = 430
      Height = 16
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 
        'Diese LT kann in andere L'#228'gern und Niederlassungen umgelagert we' +
        'rden'
      TabOrder = 11
      OnClick = ChangeNotify
    end
    object PackTypeGroupComboBox: TComboBoxPro
      Left = 257
      Top = 103
      Width = 188
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      TabOrder = 8
      OnChange = ChangeNotify
    end
    object LTBestandCheckBox: TCheckBox
      Left = 8
      Top = 247
      Width = 436
      Height = 16
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Ladehilsmittel wird bestandsgef'#252'hrt'
      TabOrder = 15
      OnClick = ChangeNotify
    end
    object UNNrEdit: TEdit
      Left = 257
      Top = 60
      Width = 54
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akTop, akRight]
      MaxLength = 4
      TabOrder = 5
      Text = '0'
      OnChange = ChangeNotify
    end
    object OversizeEdit: TEdit
      Left = 179
      Top = 315
      Width = 28
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      TabOrder = 22
      Text = '0'
      OnChange = ChangeNotify
      OnExit = EditAbmessungExit
      OnKeyPress = EditAbmessungKeyPress
    end
    object OversizeUpDown: TUpDown
      Left = 207
      Top = 315
      Width = 15
      Height = 21
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Associate = OversizeEdit
      Max = 300
      Increment = 10
      TabOrder = 23
    end
    object PurposeComboBox: TComboBoxPro
      Left = 7
      Top = 103
      Width = 228
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      TabOrder = 7
      OnChange = ChangeNotify
      Items.Strings = (
        ''
        'Verpackungsmaterial'
        'Sammel-Container'
        'Versandkarton')
    end
    object SerialCheckBox: TCheckBox
      Left = 213
      Top = 247
      Width = 230
      Height = 17
      Caption = 'Ladehilfmittel mit Seriennummern'
      TabOrder = 16
    end
  end
  object LagerPanel: TPanel
    Left = 0
    Top = 53
    Width = 454
    Height = 50
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      454
      50)
    object Bevel4: TBevel
      Left = 8
      Top = 45
      Width = 437
      Height = 4
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label19: TLabel
      Left = 8
      Top = 16
      Width = 27
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Lager'
    end
    object Label26: TLabel
      Left = 8
      Top = 3
      Width = 27
      Height = 13
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Lager'
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 19
      Width = 437
      Height = 22
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Style = csOwnerDrawFixed
      TabOrder = 0
      OnChange = LagerComboBoxChange
    end
  end
end
