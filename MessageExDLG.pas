unit MessageExDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

const
  mrTimeout = 99;
  mrNew     = 70;

type
  TMessageForm = class(TForm)
    Button1: TButton;
    Button2: TButton;
    Button3: TButton;
    MessageLabel: TLabel;
    TimeoutLabel: TLabel;
    IconImage: TImage;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    fBaseHeight    : Integer;
    fFocusedButton : TButton;
  public
    procedure SetIcon (const DlgType: TMsgDlgType);
    procedure SetButton (Button : TButton; const pCaption : String; const pModalResult : Integer);

    function ShowModal (const Timeout : DWORD) : Integer; reintroduce; overload;
    function ShowModal (FocusedButton : TButton; const Timeout : DWORD) : Integer; reintroduce; overload;
  end;

var
  MessageForm: TMessageForm;

implementation

{$R *.dfm}

uses Consts, StringUtils, CommCtrl;

var
  FocusMessages: Boolean = True;
  FocusCount: Integer = 0;

procedure TMessageForm.FormCreate(Sender: TObject);
begin
  fFocusedButton := Nil;
  fBaseHeight    := MessageLabel.Height;
end;

procedure TMessageForm.SetButton (Button : TButton; const pCaption : String; const pModalResult : Integer);
begin
  Button.Visible := True;
  Button.Caption := pCaption;
  Button.ModalResult := pModalResult;
end;

procedure TMessageForm.SetIcon (const DlgType: TMsgDlgType);
const
  WIconIDs: array[TMsgDlgType] of PWideChar = ( MakeIntResourceW (32515), MakeIntResourceW (32513), MakeIntResourceW (32516), MakeIntResourceW (32514), nil);
  IconIDs : array[TMsgDlgType] of PChar     = ( MakeIntResource (32515), MakeIntResource (32513), MakeIntResource (32516), MakeIntResource (32514), nil);
var
  h        : HICON;
begin
  if (IconIDs [DlgType] = Nil) then
    IconImage.Visible := False
  else begin
    IconImage.Visible := True;

    LoadIconMetric (0, WIconIDs [DlgType], LIM_LARGE, h);

    IconImage.Picture.Icon.Handle := h;

    //IconImage.Picture.Icon.Handle := LoadIcon(0, IconIDs [DlgType]);
  end;
end;

procedure TMessageForm.FormShow(Sender: TObject);
begin
  if ((MessageLabel.Left + MessageLabel.Width + 8) > ClientWidth) Then
    ClientWidth := (MessageLabel.Left + MessageLabel.Width + 8);
    
  if not (IconImage.Visible) then
    MessageLabel.Left := IconImage.Left;

  if (MessageLabel.Height > fBaseHeight) then
    Height := Height + (MessageLabel.Height - fBaseHeight);

  if Assigned (fFocusedButton) and fFocusedButton.Visible and fFocusedButton.Enabled then
    fFocusedButton.SetFocus
end;

function TMessageForm.ShowModal (const Timeout : DWORD) : Integer;
begin
  Result := ShowModal (Nil, Timeout);
end;

function TMessageForm.ShowModal (FocusedButton : TButton; const Timeout : DWORD) : Integer;
var
  tw, tl : Integer;
  WindowList: Pointer;
  SaveFocusCount: Integer;
  SaveCursor: TCursor;
  ActiveWindow: HWnd;
  res,
  sec,oldsec : Integer;
begin
  fFocusedButton := FocusedButton;

  tl := ClientWidth - 8;

  if Button2.Visible then begin
    tw := Canvas.TextWidth (Button2.Caption);
    if (tw < 75) then tw := 75 else tw := tw + 16;
    Button2.Width := tw;
    Button2.Left  := tl - Button2.Width;
    tl := Button2.Left - 16;
  end;

  if Button1.Visible then begin
    tw := Canvas.TextWidth (Button1.Caption);
    if (tw < 75) then tw := 75 else tw := tw + 16;
    Button1.Width := tw;
    Button1.Left  := tl - Button1.Width;
    tl := Button1.Left - 16;
  end;

  if Button3.Visible then begin
    tw := Canvas.TextWidth (Button3.Caption);
    if (tw < 75) then tw := 75 else tw := tw + 16;
    Button3.Width := tw;
    Button3.Left  := tl - Button3.Width;
    tl := Button3.Left - 16;
  end;

  CancelDrag;
  if Visible or not Enabled or (fsModal in FFormState) or
    (FormStyle = fsMDIChild) then
    raise EInvalidOperation.Create(SCannotShowModal);
  if GetCapture <> 0 then SendMessage(GetCapture, WM_CANCELMODE, 0, 0);
  ReleaseCapture;
  Application.ModalStarted;
  try
    SaveFocusCount := FocusCount;
    Include(FFormState, fsModal);
    ActiveWindow := GetActiveWindow;
    SaveCursor := Screen.Cursor;
    Screen.Cursor := crDefault;
    WindowList := DisableTaskWindows(0);

    try
      Show;
      try
        SendMessage(Handle, CM_ACTIVATE, 0, 0);
        ModalResult := 0;

        oldsec := -1;

        TimeoutLabel.Visible := (Timeout <> 0);

        repeat
          if (Timeout <> 0) then begin
            sec := (Timeout - GetTickCount) div 1000;

            if (sec <> oldsec) then begin
              oldsec := sec;

              if TimeoutLabel.Visible then
                TimeoutLabel.Caption := FormatStr (IntToStr (sec div 60),-2,'0')+':'+FormatStr (IntToStr (sec mod 60),-2,'0');
            end;
          end;

          Application.HandleMessage;
          if Application.Terminated then
            ModalResult := mrCancel
          else if ModalResult <> 0 then begin
            res := ModalResult;
            Close;
            ModalResult := res;
          end else if ((Timeout <> 0) and (GetTickCount > Timeout)) then
            ModalResult := mrTimeout;
        until (ModalResult <> 0);
        Result := ModalResult;
        SendMessage(Handle, CM_DEACTIVATE, 0, 0);
        if GetActiveWindow <> Handle then ActiveWindow := 0;
      finally
        Hide;
      end;
    finally
      EnableTaskWindows(WindowList);
      if ActiveWindow <> 0 then SetActiveWindow(ActiveWindow);
      FocusCount := SaveFocusCount;
      Exclude(FFormState, fsModal);
    end;
  finally
    Application.ModalFinished;
  end;
end;

end.
