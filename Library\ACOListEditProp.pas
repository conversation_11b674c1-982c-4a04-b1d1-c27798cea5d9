unit ACOListEditProp;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls,
  Menus, DesignIntf, DesignEditors;

type
  TACOListEditor = class(TComponentEditor)
  public
    { Public declarations }
    procedure Edit; override;
    procedure ExecuteVerb(Index: Integer); override;
    function GetVerb(Index: Integer): string; override;
    function GetVerbCount: Integer; override;
    procedure PrepareItem(Index: Integer; const AItem: TMenuItem); reintroduce; overload;
  end;

procedure Register;

implementation

uses
{$ifdef TRACE}
  Trace,
{$endif}
//  ExptIntf,
  ToolsAPI,
  ACOList, ACOListEdit, Forms;

type
  {$ifdef UNICODE}
    PDebugChar = PWideChar;
  {$else}
    PDebugChar = PAnsiChar;
  {$endif}

procedure Register;
begin
  RegisterComponentEditor (TACOListManager, TACOListEditor);
  RegisterComponentEditor (TACOListForm, TACOListEditor);
end;

procedure TACOListEditor.Edit;
var
  editor : TACOListEditForm;
  dbgstr : String;
begin
{$ifdef TRACE}
  ProcedureStart ('TACOListEditor.Edit');
{$endif}

  try
    if Assigned (Component) then begin
      if (Component is TACOListManager) then begin
        Application.CreateForm (TACOListEditForm, editor);

        if Assigned (editor) then begin
          editor.TransOwner := Component as TACOListManager;
          editor.OwnerForm  := Nil;
          editor.ProjektDir := ExtractFileDir ((BorlandIDEServices as IOTAModuleServices).CurrentModule.FileName);

          dbgstr := 'ProjektDir:'+editor.ProjektDir;
          OutputDebugString (PDebugChar (@dbgstr[1]));

          editor.DoEditor;

          editor.Release;
        end;

        if Assigned (Designer) then
          Designer.Modified;
      end else if (Component is TACOListForm) then begin
        if not Assigned ((Component as TACOListForm).Master) then
          raise eTransCompError.Create (1,'Keine Master-Komponente definiert');

        Application.CreateForm (TACOListEditForm, editor);

        if Assigned (editor) then begin
          editor.TransOwner := (Component as TACOListForm).Master;
          editor.OwnerForm  := (Component as TACOListForm).OwnerForm;
          editor.ProjektDir := ExtractFileDir ((BorlandIDEServices as IOTAModuleServices).CurrentModule.FileName);

          dbgstr := 'ProjektDir:'+editor.ProjektDir;
          OutputDebugString (PDebugChar (@dbgstr[1]));

          editor.DoEditor;

          editor.Free;
        end;

        if Assigned (Designer) then
          Designer.Modified;

        //Das Formular mit der Managemend-Komponente muss auch gesichert werden
        try
          if Assigned ((Component as TACOListForm).Master) then
            if Assigned ((Component as TACOListForm).Master.Owner) then
              if Assigned (TForm ((Component as TACOListForm).Master.Owner).Designer) then
                TForm ((Component as TACOListForm).Master.Owner).Designer.Modified;
        except
        end;
      end;
    end;
  except
  end;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

procedure TACOListEditor.ExecuteVerb(Index: Integer);
begin
{$ifdef TRACE}
  ProcedureStart ('TACOListEditor.Edit');
{$endif}

  case Index of
    0: Edit;
  end;

  if Assigned (Designer) then
    Designer.Modified;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

function TACOListEditor.GetVerb(Index: Integer): string;
begin
{$ifdef TRACE}
  ProcedureStart ('TACOListEditor.Edit');
{$endif}

  case Index of
    0 : Result := 'Edit ACO-Liste';
  end;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

function TACOListEditor.GetVerbCount: Integer;
begin
{$ifdef TRACE}
  ProcedureStart ('TACOListEditor.Edit');
{$endif}

  Result := inherited GetVerbCount + 1;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

procedure TACOListEditor.PrepareItem(Index: Integer; const AItem: TMenuItem);
begin
{$ifdef TRACE}
  ProcedureStart ('TACOListEditor.Edit');
{$endif}

  case Index of
    0: AItem.Enabled := True;
  end;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

end.
