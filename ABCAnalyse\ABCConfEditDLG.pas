unit ABCConfEditDLG;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.CheckLst, Vcl.ComCtrls, Data.DB, Data.Win.ADODB, MemDS, DBAccess, Ora,
  OraSmart, OraCall, System.Generics.Collections, ABCUtils;

type
  TAbcConfEditForm = class(TForm)
    NameLabel: TLabel;
    NameEdit: TEdit;
    AKlasseEdit: TEdit;
    AKlasseLabel: TLabel;
    BKlasseLabel: TLabel;
    BKlasseEdit: TEdit;
    ObereGrenzeBevel: TBevel;
    BisDateTimePicker: TDateTimePicker;
    BisLabel: TLabel;
    VonDateTimePicker: TDateTimePicker;
    VonLabel: TLabel;
    TypRadioGroup: TRadioGroup;
    UntereGrenzeBevel: TBevel;
    MandantCheckListBox: TCheckListBox;
    AuftragsartCheckListBox: TCheckListBox;
    MandantLabel: TLabel;
    AuftragsartLabel: TLabel;
    AbortButton: TButton;
    OkButton: TButton;
    ABCEditQuery: TOraQuery;
    AProzentLabel: TLabel;
    BProzentLabel: TLabel;
    procedure AbortButtonClick(Sender: TObject);
    procedure OkButtonClick(Sender: TObject);
    procedure FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
    procedure MandantCheckListBoxClickCheck(Sender: TObject);
    procedure AuftragsartCheckListBoxClickCheck(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    ConfData : TAbcConfData;
    procedure clearForm;
    procedure initConfData(const RefAbcConf: Integer);

    procedure saveToConfData;
    procedure fillFromConfData;

    procedure fillMandanten;
    procedure fillAuftragsarten;
    { Private-Deklarationen }
  public
    procedure Prepare(const RefAbcConf: Integer);
    { Public-Deklarationen }
  end;

implementation

uses
    DatenModul
  , LVSArtikelAbcInterface
  , System.StrUtils
  , ABCMainDLG
  , SprachModul
  , ResourceText
  ;

{$R *.dfm}

function getCheckListBoxSelectedCount(const CheckListBox: TCheckListBox): Integer;
var
  I : Integer;
begin
  Result := 0;
  for I := 0 to CheckListBox.Count -1 do
  begin
    if CheckListBox.Checked[I] then
    begin
      Inc(Result);
    end;
  end;
end;

{ Events }

procedure TAbcConfEditForm.OkButtonClick(Sender: TObject);
begin
  saveToConfData;
  ConfData.saveToDB;
  (self.Owner as TABCMainForm).ConfDBGrid.RefreshData;
  (self.Owner as TABCMainForm).PositionDBGrid.RefreshData;
  self.Close;
end;

procedure TAbcConfEditForm.AbortButtonClick(Sender: TObject);
begin
  self.Close;
end;

procedure TAbcConfEditForm.FormCreate(Sender: TObject);
begin
  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, MandantCheckListBox);
    LVSSprachModul.SetNoTranslate (Self, AuftragsartCheckListBox);
    LVSSprachModul.SetNoTranslate (Self, AProzentLabel);
    LVSSprachModul.SetNoTranslate (Self, BProzentLabel);
  {$endif}
end;

procedure TAbcConfEditForm.FormKeyDown(Sender: TObject; var Key: Word;
  Shift: TShiftState);
begin
  if (Key = VK_ESCAPE) then
  begin
    self.Close;
  end;
end;

procedure TAbcConfEditForm.MandantCheckListBoxClickCheck(Sender: TObject);
var
  SelectedCount : Integer;
begin
  SelectedCount := getCheckListBoxSelectedCount(MandantCheckListBox);
  if (SelectedCount > 1) and MandantCheckListBox.Checked[0] then
  begin
    MandantCheckListBox.Checked[0] := False;
  end
  else if SelectedCount = 0 then
  begin
    MandantCheckListBox.Checked[0] := True;
  end;
end;

procedure TAbcConfEditForm.AuftragsartCheckListBoxClickCheck(Sender: TObject);
var
  SelectedCount : Integer;
begin
  SelectedCount := getCheckListBoxSelectedCount(AuftragsartCheckListBox);
  if (SelectedCount > 1) and AuftragsartCheckListBox.Checked[0] then
  begin
    AuftragsartCheckListBox.Checked[0] := False;
  end
  else if SelectedCount = 0 then
  begin
    AuftragsartCheckListBox.Checked[0] := True;
  end;
end;

{ TABCEditForm }

procedure TAbcConfEditForm.Prepare(const RefAbcConf: Integer);
begin
  clearForm;

  fillMandanten;
  fillAuftragsarten;
  initConfData(RefAbcConf);
end;

procedure TAbcConfEditForm.initConfData(const RefAbcConf: Integer);
begin
  if not Assigned(ConfData) then
    ConfData := TAbcConfData.Create;

  if RefAbcConf <> -1 then
  begin
    ConfData.loadFromDB(RefAbcConf);
    fillFromConfData;
  end;
end;

procedure TAbcConfEditForm.clearForm;
begin
  NameEdit.Text := '';
  AKlasseEdit.Text := '';
  BKlasseEdit.Text := '';
  VonDateTimePicker.Date := Now;
  BisDateTimePicker.Date := Now;
  TypRadioGroup.ItemIndex := 0;
  MandantCheckListBox.Items.Clear;
  AuftragsartCheckListBox.Items.Clear;
end;

procedure TAbcConfEditForm.fillMandanten;
var
  Mandant   : string;
  Mandanten : TStringlist;
begin
  Mandanten := getMandanten;

  MandantCheckListBox.Items.Add(GetResourceText(1020));
  MandantCheckListBox.Checked[0] := True;
  for Mandant in Mandanten do
  begin
    MandantCheckListBox.Items.Add(Mandant);
  end;
end;

procedure TAbcConfEditForm.fillAuftragsarten;
var
  Auftragsart   : string;
  Auftragsarten : TStringlist;
begin
  Auftragsarten := getAuftragsarten;

  AuftragsartCheckListbox.Items.Add(GetResourceText(1020));
  AuftragsartCheckListBox.Checked[0] := True;
  for Auftragsart in Auftragsarten do
  begin
    AuftragsartCheckListBox.Items.Add(Auftragsart);
  end;
end;

procedure TAbcConfEditForm.fillFromConfData;
var
  I : Integer;
  Mandanten : TStringlist;
begin
  with ConfData do
  begin
    AKlasseEdit.Text := AKlasse.ToString;
    BKlasseEdit.Text := BKlasse.ToString;
    TypRadioGroup.ItemIndex := Ord(FilterTyp);

    NameEdit.Text := Name;

    VonDateTimePicker.Date := VonDatum;
    BisDateTimePicker.Date := BisDatum;

    Mandanten := getMandanten;
    if Mandanten.Count <> 0 then
    begin
      MandantCheckListBox.Checked[0] := False;
    end;
    for I := 0 to Mandanten.Count - 1 do
    begin
      MandantCheckListBox.Checked[MandantCheckListBox.Items.IndexOf(Mandanten.Strings[I])] := True;
    end;

    if Auftragsarten.Count <> 0 then
    begin
      AuftragsartCheckListBox.Checked[0] := False;
    end;
    for I := 0 to Auftragsarten.Count - 1 do
    begin
      AuftragsartCheckListBox.Checked[AuftragsartCheckListBox.Items.IndexOf(Auftragsarten.Strings[I])] := True;
    end;
  end;
end;

procedure TAbcConfEditForm.saveToConfData;
var
  I : Integer;
begin
  with ConfData do
  begin
    AKlasse := StrToInt(AKlasseEdit.Text);
    BKlasse := StrToInt(BKlasseEdit.Text);
    FilterTyp := TAbcFilterTyp(TypRadioGroup.ItemIndex);

    Name := NameEdit.Text;

    VonDatum := VonDateTimePicker.Date;
    BisDatum := BisDateTimePicker.Date;

    clearMandanten;
    if not MandantCheckListBox.Checked[0] then
    begin
      for I := 1 to MandantCheckListBox.Count - 1 do
      begin
        if MandantCheckListBox.Checked[I] then
        begin
          setMandant(MandantCheckListBox.Items[I]);
        end;
      end;
    end;

    Auftragsarten.Clear;
    if not AuftragsartCheckListBox.Checked[0] then
    begin
      for I := 1 to AuftragsartCheckListBox.Count - 1 do
      begin
        if AuftragsartCheckListBox.Checked[I] then
        begin
          Auftragsarten.Add(AuftragsartCheckListBox.Items[I]);
        end;
      end;
    end;
  end;
end;

end.
