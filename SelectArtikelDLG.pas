unit SelectArtikelDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, ComboBoxPro, DB, ADODB, BarCodeScanner;

type
  TSelectArtikelForm = class(TForm)
    ADOQuery1: TADOQuery;
    Panel1: TPanel;
    Label1: TLabel;
    Label5: TLabel;
    Bevel3: TBevel;
    Label3: TLabel;
    Bevel1: TBevel;
    MandantComboBox: TComboBoxPro;
    LagerComboBox: TComboBoxPro;
    ListedCheckBox: TCheckBox;
    ArtikelComboBox: TComboBoxPro;
    OkButton: TButton;
    AbortButton: TButton;
    ArtikelEdit: TEdit;
    FehlerLabel: TLabel;
    procedure LagerComboBoxChange(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure MandantComboBoxChange(Sender: TObject);
    procedure ListedCheckBoxClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure ArtikelComboBoxDropDown(Sender: TObject);
    procedure ArtikelComboBoxCloseUp(Sender: TObject);
    procedure EditChange(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
  private
    fOldArtikelAuswahl : String;

    function UpdateArtikelCombobox : Integer;

    procedure ScannerErfassung (var Message: TMessage); message WM_USER + SCANNER_DETECT;
  public
    procedure Prepare (Sender: TObject);
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DatenModul, PrintModul, FrontendUtils, LVSExport, ConfigModul, EAN128Utils, ResourceText,
  SprachModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectArtikelForm.Prepare (Sender: TObject);
begin
  LoadMandantCombobox (MandantComboBox);

  if (MandantComboBox.Items.Count = 1) then begin
    MandantComboBox.Enabled := False;
    MandantComboBox.ItemIndex := 0;

    MandantComboBox.OnChange (Sender);
  end else if (LVSDatenModul.AktMandantRef = -1) then
    MandantComboBox.ItemIndex := -1
  else begin
    MandantComboBox.Enabled := False;
    MandantComboBox.ItemIndex := FindComboboxRef(MandantComboBox, LVSDatenModul.AktMandantRef);

    MandantComboBox.OnChange (Sender);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectArtikelForm.ArtikelComboBoxCloseUp(Sender: TObject);
begin
  ArtikelComboBox.ColWidths [1] := 0;
  ArtikelComboBox.Prepare;
end;

procedure TSelectArtikelForm.ArtikelComboBoxDropDown(Sender: TObject);
var
  textstr  : String;
  i,
  len,
  wbox,
  armaxlen,
  vpemaxlen,
  collimaxlen : Integer;
begin
  if (ArtikelCombobox.Items.Count = 0) then
    UpdateArtikelCombobox
  else if (fOldArtikelAuswahl <> ArtikelEdit.Text) then
    UpdateArtikelCombobox;
  if (LVSConfigModul.UseArtikelCollis) then
    ArtikelComboBox.ColumeCount := 4
  else
    ArtikelComboBox.ColumeCount := 3;

  armaxlen    := ArtikelComboBox.ColWidths [1] - ArtikelComboBox.ColWidths [0] - 8;
  vpemaxlen   := 0;
  collimaxlen := 0;

  for i := 0 to ArtikelComboBox.Items.Count - 1 do begin
    textstr := ArtikelComboBox.GetItemText (i, 1);
    len := ArtikelComboBox.Canvas.TextWidth (textstr);

    if (len > armaxlen) then
      armaxlen := len;

    textstr := ArtikelComboBox.GetItemText (i, 2);
    len := ArtikelComboBox.Canvas.TextWidth (textstr);

    if (len > vpemaxlen) then
      vpemaxlen := len;

    if (ArtikelComboBox.ColumeCount = 4) then begin
      textstr := ArtikelComboBox.GetItemText (i, 3);
      len := ArtikelComboBox.Canvas.TextWidth (textstr);

      if (len > collimaxlen) then
        collimaxlen := len;
    end;
  end;

  //Dann werden die drei Punkte nicht mehr dargestellt
  armaxlen  := armaxlen + 8;
  vpemaxlen := vpemaxlen + 8;

  wbox := ArtikelComboBox.ColWidths [0] + armaxlen + vpemaxlen + collimaxlen + 32;

  ArtikelComboBox.ColWidths [1] := ArtikelComboBox.ColWidths [0] + armaxlen;

  if (collimaxlen > 0) then
    ArtikelComboBox.ColWidths [2] := ArtikelComboBox.ColWidths [1] + vpemaxlen;

  ArtikelComboBox.Perform(CB_SETDROPPEDWIDTH, wbox, 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectArtikelForm.EditChange(Sender: TObject);
begin
  FehlerLabel.Visible := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectArtikelForm.ListedCheckBoxClick(Sender: TObject);
begin
  LagerComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectArtikelForm.FormCreate(Sender: TObject);
begin
  fOldArtikelAuswahl := '';
  ArtikelEdit.Text := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, MandantComboBox);
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
  {$endif}
end;

procedure TSelectArtikelForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (MandantComboBox);
  ClearComboBoxObjects (LagerComboBox);
  ClearComboBoxObjects (ArtikelComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectArtikelForm.FormShow(Sender: TObject);
begin
  FehlerLabel.Visible := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TSelectArtikelForm.UpdateArtikelCombobox : Integer;
var
  ref,
  dlgres : Integer;
begin
  Screen.Cursor := crSQLWait;

  try
    if (ArtikelComboBox.ItemIndex = -1) then
      ref := -1
    else
      ref := GetComboBoxRef (ArtikelComboBox);

    ClearComboBoxObjects (ArtikelComboBox);

    ADOQuery1.SQL.Clear;
    if (LVSConfigModul.UseArtikelCollis) then
      ADOQuery1.SQL.Add ('select REF_AR_EINHEIT, ARTIKEL_NR||''|''||ARTIKEL_TEXT||''|''||EINHEIT||''|''||COLLI_NAME from V_ARTIKEL_LISTE where REF_MAND=:ref_mand')
    else
      ADOQuery1.SQL.Add ('select REF_AR_EINHEIT, ARTIKEL_NR||''|''||ARTIKEL_TEXT||''|''||EINHEIT from V_ARTIKEL_LISTE where REF_MAND=:ref_mand');
    ADOQuery1.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef(MandantComboBox);

    if (Length (ArtikelEdit.Text) > 0) then begin
      ADOQuery1.SQL.Add ('and (ARTIKEL_NR like :ar_nr)');
      ADOQuery1.Parameters.ParamByName('ar_nr').Value := ArtikelEdit.Text+'%';
    end;

    if not (ListedCheckBox.Checked) then begin
      ADOQuery1.SQL.Add ('and REF in (select REF_AR from V_ARTIKEL_REL_LAGER where nvl (OPT_DEPEND,''0'')=''0'' and STATUS in (''AKT'',''MAN'') and REF_LAGER');
      if (GetComboBoxRef (LagerComboBox) <> -1) then begin
        ADOQuery1.SQL.Add ('=:ref_lager)');
        ADOQuery1.Parameters.ParamByName('ref_lager').Value := GetComboBoxRef (LagerComboBox);
      end else begin
        ADOQuery1.SQL.Add ('in (select REF_LAGER from V_PCD_LAGER where REF_LOCATION=:ref_loc))');
        ADOQuery1.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
      end;
    end;

    if (LVSConfigModul.UseArtikelCollis) then
      ADOQuery1.SQL.Add ('order by LPAD (ARTIKEL_NR,32,'' ''), case when nvl (OPT_MULTI_COLLI, ''0'')=''0'' then 0 else 1 end, COLLI_NAME')
    else ADOQuery1.SQL.Add ('order by LPAD (ARTIKEL_NR,32,'' '')');

    try
      ADOQuery1.Open;

      if (ADOQuery1.RecordCount < 10000) then
        dlgres := mrYes
      else
        dlgres := MessageDLG (FormatMessageText (1364, [IntToStr (ADOQuery1.RecordCount)]), mtConfirmation, [mbYes, mbNo, mbCancel], 0);

      if (dlgres = mrYes) then begin
        ArtikelComboBox.Items.BeginUpdate;

        try
          ClearComboBoxObjects (ArtikelComboBox);

          while not (ADOQuery1.Eof) do begin
            ArtikelComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

            ADOQuery1.Next;
          end;
        finally
          ArtikelComboBox.Items.EndUpdate;
        end;
      end;

      ADOQuery1.Close;
    except
    end;

    if (ArtikelComboBox.Items.Count = 1) then
      ArtikelComboBox.ItemIndex := 0
    else if (ref = -1) then
      ArtikelComboBox.ItemIndex := -1
    else
      ArtikelComboBox.ItemIndex := FindComboboxRef (ArtikelComboBox, ref);
  finally
    Screen.Cursor := crDefault;
  end;

  fOldArtikelAuswahl := ArtikelEdit.Text;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectArtikelForm.LagerComboBoxChange(Sender: TObject);
begin
  if Visible and (Length (ArtikelEdit.Text) > 0) then
    UpdateArtikelCombobox;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectArtikelForm.MandantComboBoxChange(Sender: TObject);
begin
  LoadLagerCombobox (LagerComboBox, LVSDatenModul.AktLocation, GetComboBoxRef(MandantComboBox));

  if (LagerComboBox.Items.Count = 1) then
    LagerComboBox.ItemIndex := 0
  else begin
    LagerComboBox.Items.Insert (0, '');

    if (LVSDatenModul.AktLagerRef = -1) then
      LagerComboBox.ItemIndex := 0
    else begin
      LagerComboBox.Enabled := False;
      LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, LVSDatenModul.AktLagerRef);
    end;
  end;

  LagerComboBox.OnChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectArtikelForm.ScannerErfassung (var Message: TMessage);
var
  res,
  refar,
  refae      : Integer;
  arnr,
  einh,
  errmsg     : String;
  barcode    : TEANBarcode;
begin
  FehlerLabel.Visible := False;

  if (GetComboBoxRef (MandantComboBox) = -1) then begin
    FehlerLabel.Visible := True;
    FehlerLabel.Caption := FormatMessageText (1180, []);
    MandantComboBox.SetFocus;
  end else begin
    if (Length (ScanCode) > 10) and (ScanCode [1] = EAN13ID) then begin
      res := DetectArtikelBarcode (GetComboBoxRef (MandantComboBox), refar, refae, arnr, einh, errmsg);

      if (res = 0) and (Length (errmsg) = 0) then begin
        ArtikelEdit.SetFocus;
        ArtikelEdit.Text := arnr;

        UpdateArtikelCombobox;
      end else if (Length (errmsg) > 0) then begin
        FehlerLabel.Visible := True;
        FehlerLabel.Caption := errmsg;
      end else begin
        FehlerLabel.Visible := True;
        FehlerLabel.Caption := FormatMessageText (1584, []);
      end;
    end else if (Length (ScanCode) > 3) and (ScanCode [1] = EAN128ID) then begin
      res := DetectArtikelBarcode (GetComboBoxRef (MandantComboBox), refar, refae, arnr, einh, barcode, errmsg);

      if (res = 0) and (Length (errmsg) = 0) then begin
        ArtikelEdit.SetFocus;
        ArtikelEdit.Text := arnr;

        UpdateArtikelCombobox;
      end else if (Length (errmsg) > 0) then begin
        FehlerLabel.Visible := True;
        FehlerLabel.Caption := errmsg;
      end else begin
        FehlerLabel.Visible := True;
        FehlerLabel.Caption := FormatMessageText (1584, []);
      end;
    end else begin
      FehlerLabel.Visible := True;
      FehlerLabel.Caption := FormatMessageText (1500, []);
    end;
  end;
end;

end.
