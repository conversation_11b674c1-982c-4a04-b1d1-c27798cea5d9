object InvPosResFastForm: TInvPosResFastForm
  Left = 0
  Top = 0
  Caption = 'Schnellerfassung von Inventurergebnissen'
  ClientHeight = 408
  ClientWidth = 735
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poMainFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    735
    408)
  TextHeight = 13
  object DetailGroupBox: TGroupBox
    Left = 8
    Top = 10
    Width = 719
    Height = 359
    Anchors = [akLeft, akTop, akRight, akBottom]
    Caption = 'Inventurergebnis hinzuf'#252'gen'
    TabOrder = 0
    DesignSize = (
      719
      359)
    object Label3: TLabel
      Left = 8
      Top = 128
      Width = 48
      Height = 13
      Caption = 'Menge Ist'
    end
    object Label6: TLabel
      Left = 215
      Top = 128
      Width = 54
      Height = 13
      Alignment = taRightJustify
      Caption = 'Gewicht Ist'
    end
    object Label7: TLabel
      Left = 8
      Top = 158
      Width = 38
      Height = 13
      Caption = 'MHD Ist'
    end
    object Label10: TLabel
      Left = 218
      Top = 158
      Width = 51
      Height = 13
      Alignment = taRightJustify
      Caption = 'Charge Ist'
    end
    object Label13: TLabel
      Left = 381
      Top = 128
      Width = 11
      Height = 13
      Caption = 'kg'
    end
    object Label5: TLabel
      Left = 8
      Top = 35
      Width = 30
      Height = 13
      Caption = 'Artikel'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 80
      Width = 700
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label9: TLabel
      Left = 8
      Top = 90
      Width = 23
      Height = 13
      Caption = 'Preis'
    end
    object Bevel4: TBevel
      Left = 8
      Top = 112
      Width = 700
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 706
    end
    object Label2: TLabel
      Left = 8
      Top = 180
      Width = 83
      Height = 13
      Caption = 'Hergestellung am'
    end
    object Label1: TLabel
      Left = 216
      Top = 188
      Width = 53
      Height = 13
      Alignment = taRightJustify
      Caption = 'Projekt-Nr.'
    end
    object Label12: TLabel
      Left = 415
      Top = 188
      Width = 54
      Height = 13
      Alignment = taRightJustify
      Caption = 'Bestand-ID'
    end
    object MengeEdit: TEdit
      Left = 100
      Top = 125
      Width = 65
      Height = 21
      MaxLength = 22
      TabOrder = 4
      Text = '0'
      OnChange = MengeEditChange
      OnKeyPress = MengeEditKeyPress
    end
    object GewichtEdit: TEdit
      Left = 276
      Top = 125
      Width = 99
      Height = 21
      MaxLength = 12
      TabOrder = 6
      Text = 'GewichtEdit'
      OnExit = GewichtEditExit
      OnKeyPress = GewichtEditKeyPress
    end
    object MHDEdit: TEdit
      Left = 100
      Top = 155
      Width = 82
      Height = 21
      MaxLength = 10
      TabOrder = 7
      Text = 'MHDEdit'
      OnChange = DataChange
      OnExit = MHDEditExit
      OnKeyPress = MHDEditKeyPress
    end
    object ChargeEdit: TEdit
      Left = 276
      Top = 155
      Width = 121
      Height = 21
      MaxLength = 32
      TabOrder = 9
      Text = 'ChargeEdit'
      OnChange = DataChange
      OnKeyPress = ChargeEditKeyPress
    end
    object MengeUpDown: TIntegerUpDown
      Left = 165
      Top = 125
      Width = 16
      Height = 21
      Associate = MengeEdit
      Max = 10000
      TabOrder = 5
    end
    object AZPEdit: TEdit
      Left = 100
      Top = 87
      Width = 99
      Height = 21
      TabOrder = 3
      Text = 'AZPEdit'
      OnChange = DataChange
      OnKeyPress = AZPEditKeyPress
    end
    object ProdDatumEdit: TEdit
      Left = 100
      Top = 177
      Width = 82
      Height = 21
      MaxLength = 10
      TabOrder = 8
      Text = 'ProdDatumEdit'
      OnChange = DataChange
      OnExit = ProdDatumEditExit
      OnKeyPress = MHDEditKeyPress
    end
    object Panel1: TPanel
      Left = 2
      Top = 314
      Width = 715
      Height = 43
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 13
      DesignSize = (
        715
        43)
      object Bevel2: TBevel
        Left = 6
        Top = 2
        Width = 700
        Height = 8
        Shape = bsTopLine
      end
      object UpdateButton: TButton
        Left = 502
        Top = 10
        Width = 95
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = #220'bernehmen'
        TabOrder = 0
        OnClick = UpdateButtonClick
      end
      object ClearButton: TButton
        Left = 609
        Top = 10
        Width = 95
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Verwerfen'
        TabOrder = 1
        OnClick = ClearButtonClick
      end
    end
    object LELPPanel: TPanel
      Left = 2
      Top = 212
      Width = 715
      Height = 102
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 12
      object Label4: TLabel
        Left = 98
        Top = 10
        Width = 26
        Height = 13
        Caption = 'LE-Nr'
      end
      object LB: TLabel
        Left = 216
        Top = 10
        Width = 62
        Height = 13
        Caption = 'Lagerbereich'
      end
      object Label8: TLabel
        Left = 456
        Top = 10
        Width = 50
        Height = 13
        Caption = 'Lagerplatz'
      end
      object Label11: TLabel
        Left = 98
        Top = 53
        Width = 38
        Height = 13
        Caption = 'NVE-Nr.'
      end
      object Bevel1: TBevel
        Left = 6
        Top = 0
        Width = 700
        Height = 8
        Shape = bsTopLine
      end
      object LBComboBox: TComboBoxPro
        Left = 216
        Top = 26
        Width = 217
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 2
        OnChange = LBComboBoxChange
      end
      object LPComboBox: TComboBoxPro
        Left = 456
        Top = 26
        Width = 248
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 3
      end
      object LENrEdit: TEdit
        Left = 98
        Top = 26
        Width = 103
        Height = 21
        MaxLength = 9
        TabOrder = 0
        Text = 'LENrEdit'
        OnKeyPress = LENrEditKeyPress
      end
      object NVEEdit: TEdit
        Left = 98
        Top = 69
        Width = 103
        Height = 21
        MaxLength = 18
        TabOrder = 1
        Text = 'NVEEdit'
        OnKeyPress = LENrEditKeyPress
      end
    end
    object ArNrEdit: TEdit
      Left = 72
      Top = 32
      Width = 98
      Height = 21
      TabOrder = 0
      Text = 'ArNrEdit'
    end
    object ListedCheckBox: TCheckBox
      Left = 72
      Top = 58
      Width = 343
      Height = 17
      Caption = 'nicht gelistete Artikel auch auff'#252'hren'
      TabOrder = 2
    end
    object ArtikelComboBox: TComboBoxPro
      Left = 184
      Top = 32
      Width = 522
      Height = 21
      ItemDelimiter = #9
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 1
      OnChange = ArtikelComboBoxChange
      OnCloseUp = ArtikelComboBoxCloseUp
      OnDropDown = ArtikelComboBoxDropDown
    end
    object ProjectIDEdit: TEdit
      Left = 276
      Top = 185
      Width = 121
      Height = 21
      MaxLength = 32
      TabOrder = 10
      Text = 'ProjectIDEdit'
      OnChange = DataChange
      OnKeyPress = ChargeEditKeyPress
    end
    object BestandIDEdit: TEdit
      Left = 476
      Top = 185
      Width = 121
      Height = 21
      MaxLength = 32
      TabOrder = 11
      Text = 'BestandIDEdit'
      OnChange = DataChange
      OnKeyPress = ChargeEditKeyPress
    end
  end
  object CloseButton: TButton
    Left = 632
    Top = 375
    Width = 95
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Schlie'#223'en'
    ModalResult = 2
    TabOrder = 1
  end
  object ADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 584
    Top = 136
  end
end
