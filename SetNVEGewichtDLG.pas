unit SetNVEGewichtDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TSetNVEGewichtForm = class(TForm)
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    NVELabel: TLabel;
    EmpfLabel: TLabel;
    SpedLabel: TLabel;
    Label4: TLabel;
    AufLabel: TLabel;
    Bevel1: TBevel;
    AnzVPEEdit: TEdit;
    NettoEdit: TEdit;
    BruttoEdit: TEdit;
    Label5: TLabel;
    Label6: TLabel;
    Label7: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel2: TBevel;
    Label13: TLabel;
    LTLength: TEdit;
    Label11: TLabel;
    LTWidth: TEdit;
    Label12: TLabel;
    LTHeigth: TEdit;
    Label14: TLabel;
    Label8: TLabel;
    Label9: TLabel;
    SperrgutCheckBox: TCheckBox;
    procedure FormShow(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FloatEditKeyPress(Sender: TObject; var Key: Char);
    procedure IntEditKeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
  private
    fRefNVE      : Integer;
    fDimRequired : Boolean;
  public
    procedure Prepare (const RefNVE : Integer);
  end;

implementation

uses DB, ADODB, DatenModul, LVSDatenInterface, SprachModul, ResourceText;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 13.03.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetNVEGewichtForm.Prepare (const RefNVE : Integer);
begin
  fRefNVE := RefNVE;

  AnzVPEEdit.Enabled := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 13.03.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetNVEGewichtForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  anz,
  l,b,h,
  netto,
  brutto : Integer;
  f      : Double;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := True;

    l := -1; b := -1; h:= -1;
     
    netto  := -1;
    brutto := -1;

    if not (AnzVPEEdit.Enabled) or not (AnzVPEEdit.Visible) then
      anz := -1
    else if (Length (AnzVPEEdit.Text) = 0) then
      anz := -1
    else if not TryStrToInt(AnzVPEEdit.Text, anz) then begin
      CanClose := False;
      AnzVPEEdit.SetFocus;
    end;

    if CanClose then begin
      if not (NettoEdit.Enabled) or not (NettoEdit.Visible) then
        netto := -1
      else if (Length (NettoEdit.Text) = 0) then
        netto := -1
      else if TryStrToFloat (NettoEdit.Text, f) then
        netto := Round (f * 1000)
      else begin
        CanClose := False;
        NettoEdit.SetFocus;
      end;
    end;

    if CanClose then begin
      if not (BruttoEdit.Enabled) or not (BruttoEdit.Visible) then
        brutto := -1
      else if (Length (BruttoEdit.Text) = 0) then
        brutto := -1
      else if TryStrToFloat (BruttoEdit.Text, f) then
        brutto := Round (f * 1000)
      else begin
        CanClose := False;
        BruttoEdit.SetFocus;
      end;
    end;

    if CanClose then begin
      if not (LTLength.Enabled) or not (LTLength.Visible) then
        l := -1
      else if (Length (LTLength.Text) = 0) then
        l := -1
      else if TryStrToFloat (LTLength.Text, f) then
        l := Round (f * 10)
      else begin
        CanClose := False;
        LTLength.SetFocus;
      end;
    end;

    if CanClose then begin
      if not (LTWidth.Enabled) or not (LTWidth.Visible) then
        b := -1
      else if (Length (LTWidth.Text) = 0) then
        b := -1
      else if TryStrToFloat (LTWidth.Text, f) then
        b := Round (f * 10)
      else begin
        CanClose := False;
        LTWidth.SetFocus;
      end;
    end;

    if CanClose then begin
      if not (LTHeigth.Enabled) or not (LTHeigth.Visible) then
        h := -1
      else if (Length (LTHeigth.Text) = 0) then
        h := -1
      else if TryStrToFloat (LTHeigth.Text, f) then
        h := Round (f * 10)
      else begin
        CanClose := False;
        LTHeigth.SetFocus;
      end;

      if (fDimRequired) then begin
        if (l <= 0) then begin
          CanClose := False;
          LTLength.SetFocus;
        end else if (b <= 0) then begin
          CanClose := False;
          LTWidth.SetFocus;
        end else if (h <= 0) then begin
          CanClose := False;
          LTHeigth.SetFocus;
        end;
      end;
    end;

    if CanClose then begin
      if LTWidth.Enabled then
        res := SetNVEVPEGewicht (fRefNVE, anz, netto, brutto, l, b, h)
      else
        res := SetNVEVPEGewicht (fRefNVE, anz, netto, brutto);

      if (res = 0) then begin
        if (SperrgutCheckBox.Visible) then begin
          res := SetNVESperrgut (fRefNVE, SperrgutCheckBox.Checked);

          if (res <> 0) then begin
            CanClose := False;
            MessageDLG (FormatMessageText (1550, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
          end;
        end;
      end else begin
        CanClose := False;
        MessageDLG (FormatMessageText (1550, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end;
    end;
  end;

end;

procedure TSetNVEGewichtForm.FormCreate(Sender: TObject);
begin
  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AufLabel);
    LVSSprachModul.SetNoTranslate (Self, NVELabel);
    LVSSprachModul.SetNoTranslate (Self, EmpfLabel);
    LVSSprachModul.SetNoTranslate (Self, SpedLabel);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 13.03.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetNVEGewichtForm.IntEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,#9,^C,^V,'0'..'9']) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 13.03.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetNVEGewichtForm.FloatEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,#9,^C,^V,'0'..'9',',']) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 13.03.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSetNVEGewichtForm.FormShow(Sender: TObject);
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Self);

  try
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ( 'select nve.NVE_NR,nve.NETTO_GEWICHT,nve.BRUTTO_GEWICHT,nve.GESAMT_VPE,nve.L,nve.B,nve.H,a.AUFTRAG_NR,adr.NAME1,adr.STRASSE,adr.LAND,adr.PLZ,adr.ORT,sped.NAME as SPED_NAME,scfg.OPT_DIM_REQUIRED,nve.OPT_SPERRGUT'
                  + ' from V_NVE_01 nve inner join V_AUFTRAG a on (a.REF=nve.REF_AUF_KOPF) inner join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR) left outer join V_SPEDITIONEN sped on (sped.REF=nve.REF_SPED)'
                  + ' left outer join V_SPED_CONFIG scfg on (scfg.REF_SPED=nve.REF_SPED)'
                  + ' where nve.REF='+IntToStr (fRefNVE));

    query.Open;

    fDimRequired := (query.FieldByName ('OPT_DIM_REQUIRED').AsString = '1');

    NVELabel.Caption := query.FieldByName ('NVE_NR').AsString;
    AufLabel.Caption := query.FieldByName ('AUFTRAG_NR').AsString;
    SpedLabel.Caption := query.FieldByName ('SPED_NAME').AsString;

    EmpfLabel.Caption := query.FieldByName ('NAME1').AsString+', '+query.FieldByName ('PLZ').AsString+' '+query.FieldByName ('ORT').AsString+', '+query.FieldByName ('LAND').AsString;

    AnzVPEEdit.Text := query.FieldByName ('GESAMT_VPE').AsString;
    NettoEdit.Text  := Format ('%0.3f', [query.FieldByName ('NETTO_GEWICHT').AsFloat]);
    BruttoEdit.Text := Format ('%0.3f', [query.FieldByName ('BRUTTO_GEWICHT').AsFloat]);

    if (query.FieldByName ('L').IsNull) then
      LTLength.Text := ''
    else LTLength.Text := Format ('%0.1f', [query.FieldByName ('L').AsInteger / 10]);

    if (query.FieldByName ('B').IsNull) then
      LTWidth.Text := ''
    else LTWidth.Text  := Format ('%0.1f', [query.FieldByName ('B').AsInteger / 10]);

    if (query.FieldByName ('H').IsNull) then
      LTHeigth.Text := ''
    else LTHeigth.Text := Format ('%0.1f', [query.FieldByName ('H').AsInteger / 10]);

    SperrgutCheckBox.Checked := (query.FieldByName ('OPT_SPERRGUT').AsString = '1');
    
    //LTLength.Enabled := fDimRequired;
    //LTWidth.Enabled  := fDimRequired;
    //LTHeigth.Enabled := fDimRequired;

    query.Close;
  finally
    query.Free;
  end;
end;

end.
