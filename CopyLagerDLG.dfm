object CopyLagerForm: TCopyLagerForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'CopyLagerForm'
  ClientHeight = 437
  ClientWidth = 388
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    388
    437)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 180
    Width = 58
    Height = 13
    Caption = 'Lager-Name'
  end
  object Label2: TLabel
    Left = 103
    Top = 177
    Width = 64
    Height = 13
    Caption = 'Beschreibung'
  end
  object Bevel2: TBevel
    Left = 8
    Top = 168
    Width = 372
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel1: TBevel
    Left = 8
    Top = 280
    Width = 372
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel3: TBevel
    Left = 8
    Top = 397
    Width = 372
    Height = 11
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 209
    ExplicitWidth = 362
  end
  object Label3: TLabel
    Left = 8
    Top = 8
    Width = 222
    Height = 13
    Caption = 'Erstellen einer Kopie des bestehenden Lagers:'
  end
  object Label4: TLabel
    Left = 24
    Top = 35
    Width = 200
    Height = 52
    Caption = 
      'Es werden folgende Bereich kopiert'#13#10'  Stammdaten und Lager-Konfi' +
      'guration'#13#10'  Lagerplatztypen'#13#10'  Ladungstr'#228'gertypen und Verwendung' +
      'en'
  end
  object SourceNameLabel: TLabel
    Left = 236
    Top = 8
    Width = 101
    Height = 13
    Caption = 'SourceNameLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label5: TLabel
    Left = 8
    Top = 120
    Width = 67
    Height = 13
    Caption = 'Niederlassung'
  end
  object Bevel4: TBevel
    Left = 8
    Top = 112
    Width = 372
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label6: TLabel
    Left = 8
    Top = 226
    Width = 58
    Height = 13
    Caption = 'Betriebs-Nr.'
  end
  object Label9: TLabel
    Left = 103
    Top = 226
    Width = 16
    Height = 13
    Caption = 'ILN'
  end
  object NameEdit: TEdit
    Left = 8
    Top = 196
    Width = 89
    Height = 21
    MaxLength = 64
    TabOrder = 1
    Text = 'NameEdit'
  end
  object BeschreibungEdit: TEdit
    Left = 103
    Top = 196
    Width = 277
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 64
    TabOrder = 2
    Text = 'BeschreibungEdit'
  end
  object CopyACOCheckBox: TCheckBox
    Left = 8
    Top = 295
    Width = 372
    Height = 17
    Anchors = [akLeft, akTop, akRight]
    Caption = 'Zugriffrechte mit kopieren'
    TabOrder = 5
  end
  object OkButton: TButton
    Left = 216
    Top = 407
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 9
    ExplicitTop = 331
  end
  object AbortButton: TButton
    Left = 305
    Top = 407
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = '&Abbrechen'
    ModalResult = 3
    TabOrder = 10
    ExplicitTop = 331
  end
  object CopyStructCheckBox: TCheckBox
    Left = 8
    Top = 319
    Width = 372
    Height = 17
    Anchors = [akLeft, akTop, akRight]
    Caption = 'Lageraufbau mit kopieren'
    TabOrder = 6
    OnClick = CopyStructCheckBoxClick
  end
  object CopyZuordnungCheckBox: TCheckBox
    Left = 24
    Top = 342
    Width = 356
    Height = 17
    Anchors = [akLeft, akTop, akRight]
    Caption = 'Artikelzuordnungen mit kopieren'
    TabOrder = 7
  end
  object LocComboBox: TComboBoxPro
    Left = 8
    Top = 136
    Width = 372
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 80
    ItemHeight = 16
    TabOrder = 0
  end
  object CopySpedCheckBox: TCheckBox
    Left = 8
    Top = 365
    Width = 372
    Height = 17
    Anchors = [akLeft, akTop, akRight]
    Caption = 'Speditionen mit kopieren'
    TabOrder = 8
  end
  object BetriebEdit: TEdit
    Left = 8
    Top = 242
    Width = 89
    Height = 21
    MaxLength = 5
    TabOrder = 3
    Text = 'BetriebEdit'
  end
  object ILNEdit: TEdit
    Left = 103
    Top = 242
    Width = 130
    Height = 21
    TabOrder = 4
    Text = 'ILNEdit'
  end
end
