﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ProjectGuid>{DC16A1E6-04A6-426B-BCA6-DDA77C756AB8}</ProjectGuid>
        <MainSource>cslibrary.dpk</MainSource>
        <Base>True</Base>
        <Config Condition="'$(Config)'==''">Debug</Config>
        <TargetedPlatforms>558081</TargetedPlatforms>
        <AppType>Package</AppType>
        <FrameworkType>VCL</FrameworkType>
        <ProjectVersion>20.1</ProjectVersion>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Android' and '$(Base)'=='true') or '$(Base_Android)'!=''">
        <Base_Android>true</Base_Android>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Android64' and '$(Base)'=='true') or '$(Base_Android64)'!=''">
        <Base_Android64>true</Base_Android64>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='iOSDevice64' and '$(Base)'=='true') or '$(Base_iOSDevice64)'!=''">
        <Base_iOSDevice64>true</Base_iOSDevice64>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Base)'=='true') or '$(Base_Win64)'!=''">
        <Base_Win64>true</Base_Win64>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <DCC_E>false</DCC_E>
        <DCC_F>false</DCC_F>
        <DCC_K>false</DCC_K>
        <DCC_N>false</DCC_N>
        <DCC_S>false</DCC_S>
        <DCC_ImageBase>00400000</DCC_ImageBase>
        <GenDll>true</GenDll>
        <GenPackage>true</GenPackage>
        <SanitizedProjectName>cslibrary</SanitizedProjectName>
        <DCC_Description>common solutions Library</DCC_Description>
        <DCC_OutputNeverBuildDcps>true</DCC_OutputNeverBuildDcps>
        <DCC_Namespace>Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;System;Xml;Data;Datasnap;Web;Soap;DUnitX.Loggers.GUI;Winapi;Data.Win;$(DCC_Namespace)</DCC_Namespace>
        <VerInfo_Locale>1031</VerInfo_Locale>
        <VerInfo_Keys>CompanyName=;FileDescription=;FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=;ProductVersion=*******;Comments=;CFBundleName=</VerInfo_Keys>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Android)'!=''">
        <VerInfo_Keys>package=com.embarcadero.$(MSBuildProjectName);label=$(MSBuildProjectName);versionCode=1;versionName=1.0.0;persistent=False;restoreAnyVersion=False;installLocation=auto;largeHeap=False;theme=TitleBar;hardwareAccelerated=true;apiKey=</VerInfo_Keys>
        <BT_BuildType>Debug</BT_BuildType>
        <Android_LauncherIcon192>$(BDS)\bin\Artwork\Android\FM_LauncherIcon_192x192.png</Android_LauncherIcon192>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Android64)'!=''">
        <Android_LauncherIcon192>$(BDS)\bin\Artwork\Android\FM_LauncherIcon_192x192.png</Android_LauncherIcon192>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_iOSDevice64)'!=''">
        <iOS_AppStore1024>$(BDS)\bin\Artwork\iOS\iPhone\FM_ApplicationIcon_1024x1024.png</iOS_AppStore1024>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <BT_BuildType>Debug</BT_BuildType>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Keys>CompanyName=;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=;ProgramID=com.embarcadero.$(MSBuildProjectName)</VerInfo_Keys>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <Icon_MainIcon>cslibrary_Icon6.ico</Icon_MainIcon>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win64)'!=''">
        <Icon_MainIcon>cslibrary_Icon6.ico</Icon_MainIcon>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_DebugInformation>0</DCC_DebugInformation>
        <DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
        <DCC_RangeChecking>true</DCC_RangeChecking>
        <DCC_IntegerOverflowCheck>true</DCC_IntegerOverflowCheck>
    </PropertyGroup>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="BetterADODataSet.dcr"/>
        <DCCReference Include="CompTranslate.dcr"/>
        <DCCReference Include="DBGridPro.dcr"/>
        <DCCReference Include="ExtColorCombo.dcr"/>
        <DCCReference Include="ACOList.dcr"/>
        <DCCReference Include="rtl.dcp"/>
        <DCCReference Include="vcl.dcp"/>
        <DCCReference Include="dbrtl.dcp"/>
        <DCCReference Include="adortl.dcp"/>
        <DCCReference Include="vcldb.dcp"/>
        <DCCReference Include="designide.dcp"/>
        <DCCReference Include="vclactnband.dcp"/>
        <DCCReference Include="vclx.dcp"/>
        <DCCReference Include="madBasic_.dcp"/>
        <DCCReference Include="madDisAsm_.dcp"/>
        <DCCReference Include="madExcept_.dcp"/>
        <DCCReference Include="BetterADODataSet.PAS"/>
        <DCCReference Include="CompTranslate.pas"/>
        <DCCReference Include="DBGridPro.pas"/>
        <DCCReference Include="ExtColorCombo.pas"/>
        <DCCReference Include="CompTranslateEditProp.pas"/>
        <DCCReference Include="StringGridPro.pas"/>
        <DCCReference Include="ACOList.pas"/>
        <DCCReference Include="IntegerUpDown.pas"/>
        <DCCReference Include="ComboBoxPro.pas"/>
        <DCCReference Include="ACOListEditProp.pas"/>
        <DCCReference Include="CompTranslateEdit.pas">
            <Form>CompTranslateEditForm</Form>
        </DCCReference>
        <DCCReference Include="NoBoardPageControl.pas"/>
        <DCCReference Include="ColorButton.pas"/>
        <DCCReference Include="CustomPanelButton.pas"/>
        <DCCReference Include="Led.pas"/>
        <DCCReference Include="TabListBox.pas"/>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Release">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType>Package</Borland.ProjectType>
        <BorlandProject>
            <Delphi.Personality>
                <Source>
                    <Source Name="MainSource">cslibrary.dpk</Source>
                </Source>
            </Delphi.Personality>
            <Platforms>
                <Platform value="Android">False</Platform>
                <Platform value="Android64">True</Platform>
                <Platform value="iOSDevice32">False</Platform>
                <Platform value="iOSDevice64">True</Platform>
                <Platform value="iOSSimARM64">True</Platform>
                <Platform value="iOSSimulator">False</Platform>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">False</Platform>
            </Platforms>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
</Project>
