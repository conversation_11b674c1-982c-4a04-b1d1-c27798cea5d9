object EditArtikelEinlForm: TEditArtikelEinlForm
  Left = 617
  Top = 239
  BorderStyle = bsDialog
  Caption = 'Einlagerbereich ausw'#228'hlen'
  ClientHeight = 754
  ClientWidth = 459
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    459
    754)
  TextHeight = 13
  object OkButton: TButton
    Left = 287
    Top = 724
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 5
  end
  object AbortButton: TButton
    Left = 375
    Top = 724
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 2
    TabOrder = 6
  end
  object ArtikelPanel: TPanel
    Left = 0
    Top = 0
    Width = 459
    Height = 38
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      459
      38)
    object Label3: TLabel
      Left = 8
      Top = 8
      Width = 32
      Height = 13
      Caption = 'Artikel:'
    end
    object ArtikelLabel: TLabel
      Left = 56
      Top = 8
      Width = 55
      Height = 13
      Caption = 'ArtikelLabel'
    end
    object Bevel2: TBevel
      Left = 5
      Top = 32
      Width = 446
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 349
    end
  end
  object MandLagerPanel: TPanel
    Left = 0
    Top = 38
    Width = 459
    Height = 100
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      459
      100)
    object Label2: TLabel
      Left = 8
      Top = 48
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Label5: TLabel
      Left = 8
      Top = 1
      Width = 136
      Height = 13
      Caption = 'Mandant oder Untermandant'
    end
    object Bevel1: TBevel
      Left = 5
      Top = 95
      Width = 446
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 100
      ExplicitWidth = 349
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 64
      Width = 442
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 1
      OnChange = LagerComboBoxChange
    end
    object MandantComboBox: TComboBoxPro
      Left = 8
      Top = 17
      Width = 442
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
    object StriktCheckBox: TCheckBox
      Left = 264
      Top = -1
      Width = 183
      Height = 17
      Anchors = [akTop, akRight]
      Caption = 'Strikte Regel f'#252'r den Mandanten'
      TabOrder = 2
    end
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 256
    Width = 459
    Height = 462
    Align = alTop
    Anchors = [akLeft, akTop, akRight, akBottom]
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      459
      462)
    object Label6: TLabel
      Left = 8
      Top = 48
      Width = 68
      Height = 13
      Caption = 'Ladungstr'#228'ger'
    end
    object Label1: TLabel
      Left = 8
      Top = 6
      Width = 68
      Height = 13
      Caption = 'Lagerbereiche'
    end
    object LTComboBox: TComboBoxPro
      Left = 8
      Top = 64
      Width = 442
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 1
    end
    object PageControl1: TPageControl
      AlignWithMargins = True
      Left = 8
      Top = 192
      Width = 443
      Height = 267
      Margins.Left = 8
      Margins.Right = 8
      ActivePage = LagerungTabSheet
      Align = alBottom
      TabOrder = 4
      object LagerungTabSheet: TTabSheet
        Caption = 'Lagerung'
        DesignSize = (
          435
          239)
        object Bevel4: TBevel
          Left = 6
          Top = 56
          Width = 423
          Height = 4
          Anchors = [akLeft, akTop, akRight]
          Shape = bsTopLine
          ExplicitWidth = 326
        end
        object Label4: TLabel
          Left = 8
          Top = 64
          Width = 104
          Height = 13
          Caption = 'Automatisch einlagern'
        end
        object Label11: TLabel
          Left = 8
          Top = 8
          Width = 112
          Height = 13
          Caption = 'Prio bei der Einlagerung'
        end
        object Bevel6: TBevel
          Left = 6
          Top = 120
          Width = 423
          Height = 4
          Anchors = [akLeft, akTop, akRight]
          Shape = bsTopLine
        end
        object AutoStoreComboBox: TComboBox
          Left = 8
          Top = 83
          Width = 419
          Height = 21
          Style = csDropDownList
          Anchors = [akLeft, akTop, akRight]
          ItemIndex = 0
          TabOrder = 2
          Items.Strings = (
            ''
            'Nein'
            'Ja, unmittelbar'
            'Ja, nur Planung'
            'Ja, '#252'ber Einlagertransport')
        end
        object PrioEdit: TEdit
          Left = 8
          Top = 26
          Width = 79
          Height = 21
          TabOrder = 0
          Text = '0'
          OnChange = PrioEditChange
        end
        object PrioUpDown: TIntegerUpDown
          Left = 87
          Top = 26
          Width = 16
          Height = 21
          Associate = PrioEdit
          Min = -1
          Max = 999
          TabOrder = 1
        end
        object AutoContendCheckBox: TCheckBox
          Left = 8
          Top = 136
          Width = 417
          Height = 17
          Caption = 'Beim Einlagern automatisch auf den Inhaltsartikel umbuchen'
          Enabled = False
          TabOrder = 3
        end
      end
      object LPTabSheet: TTabSheet
        Caption = 'Lagerplatz'
        ImageIndex = 1
        OnShow = LPTabSheetShow
        object LPListBox: TListBox
          Left = 0
          Top = 0
          Width = 435
          Height = 239
          Align = alClient
          ItemHeight = 13
          TabOrder = 0
          OnClick = LPListBoxClick
        end
      end
    end
    object LBComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 442
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      DropDownCount = 16
      ItemHeight = 15
      TabOrder = 0
      OnChange = LBComboBoxChange
    end
    object BigItemRadioGroup: TRadioGroup
      Left = 8
      Top = 92
      Width = 442
      Height = 45
      Hint = 'D'#252'rfen Big Items gelagert werden'
      Anchors = [akLeft, akTop, akRight]
      Caption = 'Big Items'
      Columns = 4
      ItemIndex = 0
      Items.Strings = (
        'Frei'
        'Nicht zul'#228'ssig'
        'Auch zul'#228'ssig'
        'Nur zul'#228'ssig')
      TabOrder = 2
    end
    object SperrgutRadioGroup: TRadioGroup
      Left = 8
      Top = 141
      Width = 442
      Height = 45
      Hint = 'D'#252'rfen Sperrgut-Artikel gelagert werden'
      Anchors = [akLeft, akTop, akRight]
      Caption = 'Sperrgut'
      Columns = 4
      ItemIndex = 0
      Items.Strings = (
        'Frei'
        'Nicht zul'#228'ssig'
        'Auch zul'#228'ssig'
        'Nur zul'#228'ssig')
      TabOrder = 3
    end
  end
  object ArtikelGruppePanel: TPanel
    Left = 0
    Top = 138
    Width = 459
    Height = 59
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      459
      59)
    object Label7: TLabel
      Left = 8
      Top = 6
      Width = 62
      Height = 13
      Caption = 'Artikelgruppe'
    end
    object Bevel3: TBevel
      Left = 5
      Top = 56
      Width = 446
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 349
    end
    object ArtikelGrpComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 442
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      Enabled = False
      TabOrder = 0
    end
  end
  object CategoryPanel: TPanel
    Left = 0
    Top = 197
    Width = 459
    Height = 59
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      459
      59)
    object Label8: TLabel
      Left = 8
      Top = 6
      Width = 78
      Height = 13
      Caption = 'Bestandsqualit'#228't'
    end
    object Bevel5: TBevel
      Left = 5
      Top = 56
      Width = 446
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 349
    end
    object CategoryComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 442
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      Enabled = False
      TabOrder = 0
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 216
    Top = 88
  end
end
