object MandantRelLagerForm: TMandantRelLagerForm
  Left = 313
  Top = 186
  BorderStyle = bsDialog
  Caption = 'MandantRelLagerForm'
  ClientHeight = 243
  ClientWidth = 274
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  DesignSize = (
    274
    243)
  PixelsPerInch = 96
  TextHeight = 13
  object MandantCheckListBox: TCheckListBox
    Left = 8
    Top = 8
    Width = 259
    Height = 193
    ItemHeight = 13
    PopupMenu = ListBoxPopupMenu
    Style = lbOwnerDrawFixed
    TabOrder = 0
    OnDrawItem = MandantCheckListBoxDrawItem
  end
  object OkButton: TButton
    Left = 104
    Top = 210
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 1
  end
  object AbortButton: TButton
    Left = 192
    Top = 210
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 2
  end
  object ListBoxPopupMenu: TPopupMenu
    Left = 104
    Top = 96
    object Konfigurationprfen1: TMenuItem
      Caption = 'Konfiguration pr'#252'fen...'
      OnClick = Konfigurationprfen1Click
    end
  end
end
