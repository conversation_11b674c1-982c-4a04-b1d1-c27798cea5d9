unit LVSVerladegruppenInterface;

interface

uses
  StdCtrls;

function SetVerladeGruppe(const VerladeGruppenName: string; RefLT, RefLoc, RefLager, RefMand: Integer; RefVerladeGruppe: Integer = -1): Integer;
function DeleteVerladeGruppe(RefVerladeGruppe: Integer): Integer;
function SetSpeditionVerladeGruppe(RefVerladeGruppe, RefSped: Integer): Integer;
procedure UnassignSpeditionVerladeGruppe(RefSped: Integer);

implementation

uses
  DB,
  ADODB,
  Ora,
  OraCall,
  DatenModul
  ;

function SetVerladeGruppe(const VerladeGruppenName: string; RefLT, RefLoc, RefLager, RefMand: Integer; RefVerladeGruppe: Integer = -1): Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_VERLADUNG.SET_VERLADE_GRUPPE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefVerlGrp', ftInteger, pdInput, 12, RefVerladeGruppe);
    Parameters.CreateParameter('pName', ftString, pdInput, 32, VerladeGruppenName);
    Parameters.CreateParameter('pRefLT',ftInteger, pdInput, 12, RefLT);
    Parameters.CreateParameter('pRefLoc',ftInteger, pdInput, 12, RefLoc);
    Parameters.CreateParameter('pRefLager',ftInteger, pdInput, 12, RefLager);
    Parameters.CreateParameter('pRefMand',ftInteger, pdInput, 12, RefMand);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

function DeleteVerladeGruppe(RefVerladeGruppe: Integer): Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_VERLADUNG.DELETE_VERLADE_GRUPPE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefVerlGrp',ftInteger, pdInput, 12, RefVerladeGruppe);

    Parameters.CreateParameter('oErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

function SetSpeditionVerladeGruppe(RefVerladeGruppe, RefSped: Integer): Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_VERLADUNG.SET_SPED_VERLADE_GRUPPE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefVerlGrp',ftInteger, pdInput, 12, RefVerladeGruppe);
    Parameters.CreateParameter('pRefSped',ftInteger, pdInput, 12, RefSped);

    Parameters.CreateParameter('oErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('oErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

procedure UnassignSpeditionVerladeGruppe(RefSped: Integer);
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;
    query.SQL.Text := 'UPDATE SPEDITIONEN SET REF_VERL_GRUPPE = NULL WHERE REF = :ref';
    query.Parameters.ParamByName('ref').Value := RefSped;
    query.ExecSQL;
  finally
    query.Free;
  end;
end;

end.
