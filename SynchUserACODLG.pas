unit SynchUserACODLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Buttons, ComboBoxPro, DB, ADODB, VCLUtilitys;

type
  TACOSyncData = class (TListBoxRef)
    GrpRef   : Integer;
    GrpID    : String;
    RechtStr : String;

    constructor Create (const RecRef, RecGrpRef : Integer; const RecGrpID, RecRechtStr : String);
  end;


  TSynchUserACOForm = class(TForm)
    SourceACOListBox: TListBox;
    CopyACOListBox: TListBox;
    Button1: TButton;
    OkButton: TButton;
    ACOAddButton: TBitBtn;
    ACORemoveButtton: TBitBtn;
    Label2: TLabel;
    SourceBenComboBox: TComboBoxPro;
    ADOQuery1: TADOQuery;
    procedure SourceBenComboBoxChange(Sender: TObject);
    procedure SourceACOListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
    procedure ACOAddButtonClick(Sender: TObject);
    procedure ACORemoveButttonClick(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    BenRef : Integer;
  end;

implementation

{$R *.dfm}

uses DatenModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TACOSyncData.Create (const RecRef, RecGrpRef : Integer; const RecGrpID, RecRechtStr : String);
begin
  inherited Create (RecRef);

  GrpRef   := RecGrpRef;
  GrpID    := RecGrpID;
  RechtStr := RecRechtStr;
end;

procedure TSynchUserACOForm.ACOAddButtonClick(Sender: TObject);
var
  idx : Integer;
begin
  if (SourceACOListBox.ItemIndex <> -1) then begin
    CopyACOListBox.Items.AddObject (SourceACOListBox.Items [SourceACOListBox.ItemIndex], SourceACOListBox.Items.Objects [SourceACOListBox.ItemIndex]);

    idx := SourceACOListBox.ItemIndex;

    SourceACOListBox.Items.Delete(idx);

    if (idx < SourceACOListBox.Items.Count) then
      SourceACOListBox.ItemIndex := idx
    else
      SourceACOListBox.ItemIndex := SourceACOListBox.Items.Count - 1;
  end;
end;

procedure TSynchUserACOForm.ACORemoveButttonClick(Sender: TObject);
var
  idx : Integer;
begin
  if (CopyACOListBox.ItemIndex <> -1) then begin
    SourceACOListBox.Items.AddObject (CopyACOListBox.Items [CopyACOListBox.ItemIndex], CopyACOListBox.Items.Objects [CopyACOListBox.ItemIndex]);

    idx := CopyACOListBox.ItemIndex;

    CopyACOListBox.Items.Delete(idx);

    if (idx < CopyACOListBox.Items.Count) then
      CopyACOListBox.ItemIndex := idx
    else
      CopyACOListBox.ItemIndex := CopyACOListBox.Items.Count - 1;
  end;
end;

procedure TSynchUserACOForm.SourceACOListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
var
  line : String;
  strpos : Integer;
begin
  line := (Control as TListBox).Items [Index];
  strpos := Pos ('|', line);

  with (Control as TListBox).Canvas do begin
    FillRect(Rect);

    if (strpos = 0) then
      TextOut (Rect.Left + 2, Rect.Top, line)
    else begin
      TextOut (Rect.Left + 2, Rect.Top, Copy (line,1,strpos - 1));
      TextOut (Rect.Right - 50, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
    end;
  end;
end;

procedure TSynchUserACOForm.SourceBenComboBoxChange(Sender: TObject);
begin
  SourceACOListBox.Clear;
  CopyACOListBox.Clear;

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select g.REF, g.GROUPE_NAME, g.GROUPE_ID from V_GRANTED_USER_GROUPS g');
  ADOQuery1.SQL.Add ('where g.REF in (select REF_GRP from V_SYS_REL_BEN_GRP where REF_BEN=:ref_source_ben)');
  ADOQuery1.SQL.Add ('and ((select REF from V_SYS_REL_BEN_GRP where REF_BEN=:ref_dest_ben and REF_GRP=g.REF) is null)');
  ADOQuery1.SQL.Add ('order by g.GROUPE_NAME');
  ADOQuery1.Parameters.ParamByName('ref_source_ben').Value := GetComboBoxRef(SourceBenComboBox);
  ADOQuery1.Parameters.ParamByName('ref_dest_ben').Value := BenRef;

  ADOQuery1.Open;

  while not (ADOQuery1.Eof) do begin
    SourceACOListBox.AddItem (ADOQuery1.Fields [1].AsString+' ('+ADOQuery1.Fields [2].AsString + ')', TACOSyncData.Create(-1, ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [2].AsString, ''));

    ADOQuery1.Next;
  end;

  ADOQuery1.Close;

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select al.REF_ACO, nvl (a.BESCHREIBUNG, a.NAME), al.RECHTE_FLAGS from V_SYS_ACO_LIST al, V_SYS_ACO a');
  ADOQuery1.SQL.Add ('where a.REF=al.REF_ACO and al.REF_BEN=:ref_source_ben');
  ADOQuery1.SQL.Add ('and (((select REF from V_SYS_ACO_LIST where REF_BEN=:ref_dest_ben_1 and REF_ACO=a.REF) is null)');
  ADOQuery1.SQL.Add ('or al.RECHTE_FLAGS<>PA_SYS_BENUTZER.GET_OBJECT_RECHTE (:ref_dest_ben_2, al.REF_ACO))');
  ADOQuery1.SQL.Add ('order by a.OBJECT_NAME');
  ADOQuery1.Parameters.ParamByName('ref_source_ben').Value := GetComboBoxRef(SourceBenComboBox);
  ADOQuery1.Parameters.ParamByName('ref_dest_ben_1').Value := BenRef;
  ADOQuery1.Parameters.ParamByName('ref_dest_ben_2').Value := BenRef;

  ADOQuery1.Open;

  while not (ADOQuery1.Eof) do begin
    SourceACOListBox.AddItem (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TACOSyncData.Create(ADOQuery1.Fields [0].AsInteger, -1, '', ADOQuery1.Fields [2].AsString));

    ADOQuery1.Next;
  end;

  ADOQuery1.Close;
end;

end.
