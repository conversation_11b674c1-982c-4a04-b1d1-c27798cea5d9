unit NewUserDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, ComboBoxPro;

type
  TNewUserForm = class(TForm)
    UserIDEdit: TEdit;
    Label1: TLabel;
    UserNameEdit: TEdit;
    Label2: TLabel;
    AdminCheckBox: TCheckBox;
    UserFunkEdit: TEdit;
    Label3: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel1: TBevel;
    Bevel2: TBevel;
    Bevel3: TBevel;
    Label6: TLabel;
    DomComboBox: TComboBox;
    Label7: TLabel;
    DomUserEdit: TEdit;
    FirmaComboBox: TComboBoxPro;
    Label13: TLabel;
    Label14: TLabel;
    UserMailEdit: TEdit;
    Label4: TLabel;
    UserNumIDEdit: TEdit;
    Label16: TLabel;
    ShortNameEdit: TEdit;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure UserNumIDEditKeyPress(Sender: TObject; var Key: Char);
    procedure UserIDEditKeyPress(Sender: TObject; var Key: Char);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

procedure TNewUserForm.FormCloseQuery(Sender: TObject;
  var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := False;

    if (Length (UserIDEdit.Text) = 0) then
      UserIDEdit.SetFocus
    else if FirmaComboBox.Enabled and (FirmaComboBox.ItemIndex = -1) then
      FirmaComboBox.SetFocus
    else CanClose := True;
  end;
end;

procedure TNewUserForm.FormCreate(Sender: TObject);
begin
  UserIDEdit.Text    := '';
  UserNumIDEdit.Text := '';
  ShortNameEdit.Text := '';
  UserNameEdit.Text  := '';
  DomComboBox.Text   := '';
  DomUserEdit.Text   := '';
  UserFunkEdit.Text  := '';
  UserMailEdit.Text  := '';
end;

procedure TNewUserForm.UserIDEditKeyPress(Sender: TObject; var Key: Char);
begin
  if (Key in ['a'..'z']) then
    Key := Chr (Ord (Key) - (Ord ('a') - Ord ('A')));
end;

procedure TNewUserForm.UserNumIDEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (key in ['0'..'9',#8,#9,^V,^C,^X]) then
    key := #0;
end;

end.
