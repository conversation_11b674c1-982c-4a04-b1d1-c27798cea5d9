object NumericKeypad: TNumericKeypad
  Left = 754
  Top = 256
  BorderIcons = []
  BorderStyle = bsDialog
  Caption = 'NumericKeypad'
  ClientHeight = 260
  ClientWidth = 208
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -13
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Scaled = False
  OnCreate = FormCreate
  OnShow = FormShow
  TextHeight = 16
  object Numpad: TDrawGrid
    Left = 0
    Top = 0
    Width = 208
    Height = 260
    Align = alClient
    ColCount = 4
    DefaultColWidth = 50
    DefaultRowHeight = 50
    DefaultDrawing = False
    FixedCols = 0
    FixedRows = 0
    Font.Charset = ANSI_CHARSET
    Font.Color = clWindowText
    Font.Height = -20
    Font.Name = 'Arial'
    Font.Style = [fsBold]
    Options = [goFixedVertLine, goFixedHorzLine, goRangeSelect]
    ParentFont = False
    TabOrder = 0
    OnClick = NumpadClick
    OnDrawCell = NumpadDrawCell
  end
  object Timer: TTimer
    Interval = 100
    OnTimer = TimerTimer
    Left = 60
    Top = 20
  end
end
