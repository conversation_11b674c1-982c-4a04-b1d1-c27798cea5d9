object ManRetoureErfassenForm: TManRetoureErfassenForm
  Left = 323
  Top = 349
  BorderStyle = bsDialog
  Caption = 'Manueller Lieferschein erfassen'
  ClientHeight = 483
  ClientWidth = 779
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    779
    483)
  TextHeight = 13
  object Label1: TLabel
    Left = 16
    Top = 8
    Width = 72
    Height = 13
    Caption = 'Lieferant-Nr:'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label2: TLabel
    Left = 16
    Top = 24
    Width = 91
    Height = 13
    Caption = 'Lieferant-Name:'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object KundenNrLabel: TLabel
    Left = 120
    Top = 8
    Width = 74
    Height = 13
    Caption = 'KundenNrLabel'
  end
  object KundenNameLabel: TLabel
    Left = 120
    Top = 24
    Width = 91
    Height = 13
    Caption = 'KundenNameLabel'
  end
  object Label5: TLabel
    Left = 16
    Top = 56
    Width = 22
    Height = 13
    Caption = 'EAN'
  end
  object Label6: TLabel
    Left = 304
    Top = 56
    Width = 66
    Height = 13
    Caption = 'Artikelnummer'
  end
  object Label7: TLabel
    Left = 16
    Top = 115
    Width = 33
    Height = 13
    Caption = 'Menge'
  end
  object Label8: TLabel
    Left = 168
    Top = 115
    Width = 65
    Height = 13
    Caption = 'Gewicht in kg'
  end
  object Label9: TLabel
    Left = 16
    Top = 176
    Width = 25
    Height = 13
    Caption = 'MHD'
  end
  object Label10: TLabel
    Left = 168
    Top = 176
    Width = 34
    Height = 13
    Caption = 'Charge'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 224
    Width = 761
    Height = 9
    Shape = bsTopLine
  end
  object Bevel2: TBevel
    Left = 8
    Top = 49
    Width = 761
    Height = 9
    Shape = bsTopLine
  end
  object Label11: TLabel
    Left = 304
    Top = 115
    Width = 23
    Height = 13
    Caption = 'Preis'
    Visible = False
  end
  object Label3: TLabel
    Left = 456
    Top = 56
    Width = 53
    Height = 13
    Caption = 'Artikel-Text'
  end
  object Bevel3: TBevel
    Left = 8
    Top = 105
    Width = 761
    Height = 9
    Shape = bsTopLine
  end
  object Bevel4: TBevel
    Left = 8
    Top = 166
    Width = 761
    Height = 9
    Shape = bsTopLine
  end
  object ErfasstStringGrid: TStringGridPro
    Left = 8
    Top = 272
    Width = 761
    Height = 145
    ColCount = 7
    DefaultColWidth = 20
    DefaultRowHeight = 20
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
    PopupMenu = ErfasstStringGridPopupMenu
    TabOrder = 14
    TitelTexte.Strings = (
      ''
      'Artikel-Nr'
      'Artikel-Text'
      'Menge'
      'Einheit'
      'Gewicht'
      'Preis')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'MS Sans Serif'
    TitelFont.Style = []
    ColWidths = (
      20
      49
      59
      39
      38
      45
      29)
  end
  object AbortButton: TButton
    Left = 694
    Top = 432
    Width = 75
    Height = 25
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 16
  end
  object OkButton: TButton
    Left = 606
    Top = 432
    Width = 75
    Height = 25
    Caption = 'Ok'
    ModalResult = 1
    TabOrder = 15
  end
  object EANEdit: TEdit
    Left = 16
    Top = 72
    Width = 273
    Height = 21
    TabOrder = 1
    Text = 'EANEdit'
    OnChange = InputChange
    OnExit = EANEditExit
    OnKeyPress = EANEditKeyPress
  end
  object ArtikelNrEdit: TEdit
    Left = 305
    Top = 72
    Width = 89
    Height = 21
    TabOrder = 2
    Text = 'ArtikelNrEdit'
    OnChange = InputChange
    OnExit = ArtikelNrEditExit
    OnKeyPress = ArtikelNrEditKeyPress
  end
  object MengeEdit: TEdit
    Left = 16
    Top = 131
    Width = 105
    Height = 21
    TabOrder = 5
    Text = '0'
    OnChange = InputChange
    OnExit = MengeEditExit
  end
  object GewichtEdit: TEdit
    Left = 168
    Top = 131
    Width = 121
    Height = 21
    TabOrder = 7
    Text = 'GewichtEdit'
    OnChange = InputChange
    OnKeyPress = GewichtEditKeyPress
  end
  object MHDEdit: TEdit
    Left = 16
    Top = 192
    Width = 121
    Height = 21
    TabOrder = 10
    Text = 'MHDEdit'
    OnChange = InputChange
    OnExit = MHDEditExit
  end
  object InsertButton: TButton
    Left = 677
    Top = 232
    Width = 91
    Height = 25
    Caption = #220'bernehmen'
    TabOrder = 12
    OnClick = InsertButtonClick
  end
  object ChargeEdit: TEdit
    Left = 168
    Top = 192
    Width = 121
    Height = 21
    TabOrder = 11
    Text = 'ChargeEdit'
    OnChange = InputChange
  end
  object PreisEdit: TEdit
    Left = 304
    Top = 131
    Width = 121
    Height = 21
    TabOrder = 8
    Text = 'PreisEdit'
    Visible = False
    OnChange = InputChange
    OnKeyPress = PreisEditKeyPress
  end
  object MengeUpDown: TIntegerUpDown
    Left = 121
    Top = 131
    Width = 16
    Height = 21
    Associate = MengeEdit
    Max = 999
    TabOrder = 6
    OnExit = MengeEditExit
  end
  object ArtikelTextEdit: TEdit
    Left = 456
    Top = 72
    Width = 305
    Height = 21
    TabStop = False
    Enabled = False
    ReadOnly = True
    TabOrder = 4
    Text = 'ArtikelTextEdit'
  end
  object ListArtikelButton: TButton
    Left = 400
    Top = 72
    Width = 41
    Height = 21
    Caption = '...'
    TabOrder = 3
    Visible = False
  end
  object FehlerLabel: TPanel
    Left = 16
    Top = 232
    Width = 649
    Height = 25
    BevelOuter = bvNone
    Caption = 'FehlerLabel'
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 13
  end
  object StatusBar1: TStatusBar
    Left = 0
    Top = 464
    Width = 779
    Height = 19
    Panels = <
      item
        Width = 200
      end>
  end
  object STPanel: TPanel
    Left = 8
    Top = 432
    Width = 329
    Height = 25
    BevelOuter = bvNone
    Caption = 'St'#252'ckartikel'
    Color = clLime
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 18
  end
  object BestandCheckBox: TCheckBox
    Left = 456
    Top = 128
    Width = 305
    Height = 27
    Caption = 'Mit Bestandsbuchung'
    Checked = True
    State = cbChecked
    TabOrder = 9
  end
  object NoWECheckCheckBox: TCheckBox
    Left = 574
    Top = 7
    Width = 197
    Height = 17
    Anchors = [akTop, akRight]
    Caption = 'Retoure ohne Lieferung zul'#228'ssig'
    TabOrder = 0
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 440
    Top = 8
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    OnChangeLanguage = CompTranslateForm1ChangeLanguage
    Left = 408
    Top = 8
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 376
    Top = 8
  end
  object ErfasstStringGridPopupMenu: TPopupMenu
    OnPopup = ErfasstStringGridPopupMenuPopup
    Left = 352
    Top = 320
    object OptColmunWidthMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      OnClick = OptColmunWidthMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object EditPosMenuItem: TMenuItem
      Caption = 'Bearbeiten'
      OnClick = EditPosMenuItemClick
    end
    object DelPosMenuItem: TMenuItem
      Caption = 'L'#246'schen...'
      OnClick = DelPosMenuItemClick
    end
  end
end
