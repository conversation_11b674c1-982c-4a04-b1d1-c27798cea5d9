object EditLieferantForm: TEditLieferantForm
  Left = 357
  Top = 408
  BorderStyle = bsDialog
  Caption = 'EditLieferantForm'
  ClientHeight = 550
  ClientWidth = 346
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    346
    550)
  TextHeight = 13
  object AbortButton: TButton
    Left = 262
    Top = 517
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 4
  end
  object OkButton: TButton
    Left = 176
    Top = 517
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 3
  end
  object MandPanel: TPanel
    Left = 0
    Top = 0
    Width = 346
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label9: TLabel
      Left = 8
      Top = 3
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 8
      Top = 19
      Width = 329
      Height = 22
      Style = csOwnerDrawFixed
      ColWidth = 120
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 100
    Width = 346
    Height = 405
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      346
      405)
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 67
      Height = 13
      Caption = 'Lieferanten-Nr'
    end
    object Label2: TLabel
      Left = 8
      Top = 56
      Width = 67
      Height = 13
      Caption = 'Lieferantname'
    end
    object Bevel2: TBevel
      Left = 8
      Top = 3
      Width = 329
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object NrEdit: TEdit
      Left = 8
      Top = 24
      Width = 329
      Height = 21
      TabOrder = 0
      Text = 'NrEdit'
      OnKeyPress = NrEditKeyPress
    end
    object NameEdit: TEdit
      Left = 8
      Top = 72
      Width = 329
      Height = 21
      TabOrder = 1
      Text = 'NameEdit'
    end
    object GroupBox1: TGroupBox
      Left = 8
      Top = 107
      Width = 330
      Height = 151
      Caption = 'Anschrift'
      TabOrder = 2
      object Label3: TLabel
        Left = 8
        Top = 16
        Width = 69
        Height = 13
        Caption = 'Namenszusatz'
      end
      object Label4: TLabel
        Left = 8
        Top = 58
        Width = 35
        Height = 13
        Caption = 'Strasse'
      end
      object PLZ: TLabel
        Left = 8
        Top = 100
        Width = 20
        Height = 13
        Caption = 'PLZ'
      end
      object Label5: TLabel
        Left = 254
        Top = 100
        Width = 24
        Height = 13
        Caption = 'Land'
      end
      object Label6: TLabel
        Left = 79
        Top = 100
        Width = 14
        Height = 13
        Caption = 'Ort'
      end
      object NameZusatzEdit: TEdit
        Left = 8
        Top = 32
        Width = 305
        Height = 21
        MaxLength = 64
        TabOrder = 0
        Text = 'NameZusatzEdit'
      end
      object StrasseEdit: TEdit
        Left = 8
        Top = 74
        Width = 305
        Height = 21
        MaxLength = 64
        TabOrder = 1
        Text = 'StrasseEdit'
      end
      object PLZEdit: TEdit
        Left = 8
        Top = 116
        Width = 59
        Height = 21
        MaxLength = 12
        TabOrder = 2
        Text = 'PLZEdit'
      end
      object OrtEdit: TEdit
        Left = 79
        Top = 116
        Width = 164
        Height = 21
        MaxLength = 64
        TabOrder = 3
        Text = 'OrtEdit'
      end
      object LandEdit: TEdit
        Left = 254
        Top = 116
        Width = 59
        Height = 21
        MaxLength = 12
        TabOrder = 4
        Text = 'LandEdit'
      end
    end
    object PageControl1: TPageControl
      Left = 8
      Top = 264
      Width = 330
      Height = 136
      ActivePage = OriginTabSheet
      TabOrder = 3
      object AnlieferTabSheet: TTabSheet
        Caption = 'Anlieferung'
        object Label7: TLabel
          Left = 4
          Top = 4
          Width = 64
          Height = 13
          Caption = 'Anlieferung in'
        end
        object LagerCheckListBox: TCheckListBox
          Left = 4
          Top = 22
          Width = 315
          Height = 84
          Style = lbOwnerDrawFixed
          TabOrder = 0
          OnDrawItem = LagerCheckListBoxDrawItem
        end
      end
      object FilialTabSheet: TTabSheet
        Caption = 'Filiale'
        ImageIndex = 1
        object Label8: TLabel
          Left = 4
          Top = 34
          Width = 32
          Height = 13
          Caption = 'Filialnr.'
        end
        object IsFilialeCheckBox: TCheckBox
          Left = 4
          Top = 4
          Width = 270
          Height = 17
          Caption = 'Der Lieferant ist eine angebundene Filiale'
          TabOrder = 0
        end
        object FilialNrEdit: TEdit
          Left = 4
          Top = 50
          Width = 121
          Height = 21
          TabOrder = 1
          Text = 'FilialNrEdit'
        end
      end
      object QSTabSheet: TTabSheet
        Caption = 'Qualit'#228't'
        ImageIndex = 2
        object QualityRadioGroup: TRadioGroup
          Left = 3
          Top = 8
          Width = 166
          Height = 94
          Caption = 'Anlieferqualit'#228't'
          Items.Strings = (
            'A-Lieferant'
            'B-Lieferant'
            'C-Lieferant')
          TabOrder = 0
        end
      end
      object OriginTabSheet: TTabSheet
        Caption = 'Herkunft'
        ImageIndex = 3
        DesignSize = (
          322
          108)
        object Label11: TLabel
          Left = 3
          Top = 5
          Width = 63
          Height = 13
          Caption = 'Herkunfsland'
        end
        object OriginComboBox: TComboBoxPro
          Left = 4
          Top = 24
          Width = 305
          Height = 22
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 0
        end
      end
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 50
    Width = 346
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    object Label10: TLabel
      Left = 8
      Top = 3
      Width = 67
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 8
      Top = 19
      Width = 329
      Height = 22
      Style = csOwnerDrawFixed
      ColWidth = 120
      TabOrder = 0
    end
  end
end
