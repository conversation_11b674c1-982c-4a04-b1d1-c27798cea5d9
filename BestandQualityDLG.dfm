object BestandQualityForm: TBestandQualityForm
  Left = 586
  Top = 383
  BorderStyle = bsDialog
  Caption = 'Bestandsqualifikation '#228'ndern'
  ClientHeight = 302
  ClientWidth = 422
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poMainFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    422
    302)
  TextHeight = 13
  object OkButton: TButton
    Left = 252
    Top = 269
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'OK'
    Default = True
    ModalResult = 1
    TabOrder = 0
  end
  object AbortButton: TButton
    Left = 339
    Top = 269
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 1
  end
  object ArtikelGroupBox: TGroupBox
    Left = 8
    Top = 8
    Width = 408
    Height = 112
    Anchors = [akLeft, akTop, akRight]
    Caption = 'Bestand'
    TabOrder = 2
    object Panel1: TPanel
      Left = 2
      Top = 15
      Width = 404
      Height = 49
      Align = alClient
      BevelOuter = bvNone
      TabOrder = 0
      object Label1: TLabel
        Left = 8
        Top = 4
        Width = 71
        Height = 13
        Caption = 'Artikel-Nummer'
      end
      object Label2: TLabel
        Left = 124
        Top = 4
        Width = 94
        Height = 13
        Caption = 'Artikel-Bezeichnung'
      end
      object StaticText1: TStaticText
        Left = 8
        Top = 20
        Width = 100
        Height = 17
        AutoSize = False
        BevelInner = bvLowered
        BevelOuter = bvRaised
        BorderStyle = sbsSunken
        Caption = 'StaticText1'
        TabOrder = 0
      end
      object StaticText2: TStaticText
        Left = 124
        Top = 20
        Width = 269
        Height = 18
        AutoSize = False
        BevelInner = bvLowered
        BevelOuter = bvRaised
        BorderStyle = sbsSunken
        Caption = 'StaticText2'
        TabOrder = 1
      end
    end
    object MHDChargePanel: TPanel
      Left = 2
      Top = 64
      Width = 404
      Height = 46
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 1
      object Label3: TLabel
        Left = 8
        Top = 0
        Width = 25
        Height = 13
        Caption = 'MHD'
      end
      object Label4: TLabel
        Left = 124
        Top = 0
        Width = 34
        Height = 13
        Caption = 'Charge'
      end
      object StaticText3: TStaticText
        Left = 8
        Top = 16
        Width = 100
        Height = 17
        AutoSize = False
        BevelInner = bvLowered
        BevelOuter = bvRaised
        BorderStyle = sbsSunken
        Caption = 'StaticText3'
        TabOrder = 0
      end
      object StaticText4: TStaticText
        Left = 124
        Top = 16
        Width = 269
        Height = 18
        AutoSize = False
        BevelInner = bvLowered
        BevelOuter = bvRaised
        BorderStyle = sbsSunken
        Caption = 'StaticText4'
        TabOrder = 1
      end
    end
  end
  object GroupBox2: TGroupBox
    Left = 8
    Top = 128
    Width = 408
    Height = 131
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Umbuchung von'
    TabOrder = 3
    object Label6: TLabel
      Left = 8
      Top = 22
      Width = 79
      Height = 13
      Caption = 'Anzahl Einheiten'
    end
    object Label7: TLabel
      Left = 126
      Top = 22
      Width = 88
      Height = 13
      Caption = 'Bestandskategorie'
    end
    object Label5: TLabel
      Left = 8
      Top = 72
      Width = 29
      Height = 13
      Caption = 'Grund'
    end
    object MengeEdit: TEdit
      Left = 8
      Top = 38
      Width = 79
      Height = 21
      TabOrder = 0
      Text = '0'
      OnChange = MengeEditChange
      OnExit = MengeEditExit
      OnKeyPress = NumberKeyPress
    end
    object CategoryComboBox: TComboBox
      Left = 126
      Top = 38
      Width = 269
      Height = 21
      Style = csDropDownList
      TabOrder = 2
    end
    object MengeUpDown: TIntegerUpDown
      Left = 87
      Top = 38
      Width = 17
      Height = 21
      Associate = MengeEdit
      TabOrder = 1
      Thousands = False
    end
    object GrundComboBox: TComboBox
      Left = 8
      Top = 88
      Width = 387
      Height = 21
      MaxLength = 64
      TabOrder = 3
      Text = 'GrundComboBox'
      OnChange = GrundComboBoxChange
      Items.Strings = (
        'Ware mangelhaft')
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 200
    Top = 264
  end
end
