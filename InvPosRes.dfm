object InvPosResForm: TInvPosResForm
  Left = 508
  Top = 216
  Caption = 'Inventurz'#228'hlungen zur'#252'ckmelden'
  ClientHeight = 767
  ClientWidth = 734
  Color = clBtnFace
  Constraints.MinHeight = 613
  Constraints.MinWidth = 750
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnHide = FormHide
  OnShow = FormShow
  TextHeight = 13
  object PlanSplitter: TSplitter
    Left = 0
    Top = 169
    Width = 734
    Height = 3
    Cursor = crVSplit
    Align = alTop
    Beveled = True
  end
  object PosSplitter: TSplitter
    Left = 0
    Top = 330
    Width = 734
    Height = 3
    Cursor = crVSplit
    Align = alTop
    Beveled = True
    ExplicitTop = 333
  end
  object ResultPanel: TPanel
    Left = 0
    Top = 333
    Width = 734
    Height = 434
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      734
      434)
    object Label4: TLabel
      Left = 8
      Top = 2
      Width = 165
      Height = 13
      Caption = 'Bereits erfasste Inventurergebnisse'
    end
    object InvDelButton: TButton
      Left = 655
      Top = 54
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'L'#246'schen...'
      TabOrder = 2
      OnClick = InvDelButtonOnClick
    end
    object DetailGroupBox: TGroupBox
      Left = 0
      Top = 142
      Width = 733
      Height = 254
      Anchors = [akLeft, akRight, akBottom]
      Caption = 'Inventurergebnis hinzuf'#252'gen'
      TabOrder = 3
      DesignSize = (
        733
        254)
      object Label3: TLabel
        Left = 8
        Top = 184
        Width = 47
        Height = 13
        Caption = 'Menge Ist'
      end
      object Label6: TLabel
        Left = 209
        Top = 184
        Width = 53
        Height = 13
        Caption = 'Gewicht Ist'
      end
      object Label7: TLabel
        Left = 8
        Top = 214
        Width = 39
        Height = 13
        Caption = 'MHD Ist'
      end
      object Label10: TLabel
        Left = 209
        Top = 214
        Width = 48
        Height = 13
        Caption = 'Charge Ist'
      end
      object Label12: TLabel
        Left = 318
        Top = 72
        Width = 32
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'Einheit'
        ExplicitLeft = 310
      end
      object Label13: TLabel
        Left = 381
        Top = 184
        Width = 12
        Height = 13
        Caption = 'kg'
      end
      object Label2: TLabel
        Left = 464
        Top = 72
        Width = 30
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'LE-Nr.'
        ExplicitLeft = 456
      end
      object Label5: TLabel
        Left = 8
        Top = 72
        Width = 29
        Height = 13
        Caption = 'Artikel'
      end
      object Label8: TLabel
        Left = 464
        Top = 16
        Width = 49
        Height = 13
        Caption = 'Lagerplatz'
      end
      object LB: TLabel
        Left = 9
        Top = 16
        Width = 62
        Height = 13
        Caption = 'Lagerbereich'
      end
      object Bevel2: TBevel
        Left = 8
        Top = 64
        Width = 714
        Height = 9
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
        ExplicitWidth = 706
      end
      object Bevel3: TBevel
        Left = 8
        Top = 120
        Width = 714
        Height = 9
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
        ExplicitWidth = 706
      end
      object Label9: TLabel
        Left = 8
        Top = 138
        Width = 23
        Height = 13
        Caption = 'Preis'
      end
      object Bevel4: TBevel
        Left = 8
        Top = 168
        Width = 714
        Height = 9
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
        ExplicitWidth = 706
      end
      object MengeEdit: TEdit
        Left = 74
        Top = 181
        Width = 105
        Height = 21
        MaxLength = 22
        TabOrder = 6
        Text = '0'
        OnChange = MengeEditChange
        OnKeyPress = MengeEditKeyPress
      end
      object UpdateButton: TButton
        Left = 564
        Top = 221
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = #220'bernehmen'
        TabOrder = 10
        OnClick = UpdateButtonClick
      end
      object GewichtEdit: TEdit
        Left = 276
        Top = 181
        Width = 99
        Height = 21
        MaxLength = 12
        TabOrder = 7
        Text = 'GewichtEdit'
        OnChange = DataChange
        OnExit = GewichtEditExit
      end
      object MHDEdit: TEdit
        Left = 74
        Top = 211
        Width = 121
        Height = 21
        MaxLength = 10
        TabOrder = 8
        Text = 'MHDEdit'
        OnChange = DataChange
        OnExit = MHDEditExit
        OnKeyPress = MHDEditKeyPress
      end
      object ChargeEdit: TEdit
        Left = 276
        Top = 211
        Width = 121
        Height = 21
        MaxLength = 32
        TabOrder = 9
        Text = 'ChargeEdit'
        OnChange = DataChange
      end
      object ClearButton: TButton
        Left = 645
        Top = 221
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Verwerfen'
        TabOrder = 11
        OnClick = ClearButtonClick
      end
      object EinheitComboBox: TComboBoxPro
        Left = 318
        Top = 88
        Width = 131
        Height = 22
        Style = csOwnerDrawFixed
        Anchors = [akTop, akRight]
        TabOrder = 3
        OnChange = DataChange
        OnSelect = EinheitComboBoxSelect
      end
      object LEEdit: TEdit
        Left = 464
        Top = 88
        Width = 256
        Height = 21
        Anchors = [akTop, akRight]
        MaxLength = 22
        TabOrder = 4
        Text = 'LEEdit'
        OnKeyPress = LEEditKeyPress
      end
      object LBComboBox: TComboBoxPro
        Left = 8
        Top = 32
        Width = 441
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 0
        OnChange = LBComboBoxChange
      end
      object ArtikelComboBox: TComboBoxPro
        Left = 8
        Top = 88
        Width = 281
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 2
        OnChange = ArtikelComboBoxChange
      end
      object LPComboBox: TComboBoxPro
        Left = 464
        Top = 32
        Width = 256
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 1
      end
      object MengeUpDown: TIntegerUpDown
        Left = 179
        Top = 181
        Width = 16
        Height = 21
        Associate = MengeEdit
        Max = 100000
        TabOrder = 12
      end
      object AZPEdit: TEdit
        Left = 74
        Top = 135
        Width = 121
        Height = 21
        TabOrder = 5
        Text = 'AZPEdit'
        OnChange = DataChange
        OnExit = AZPEditExit
        OnKeyPress = AZPEditKeyPress
      end
    end
    object InvAendButton: TButton
      Left = 655
      Top = 21
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = #196'ndern'
      TabOrder = 1
      OnClick = InvAendButtonClick
    end
    object InvResDBGridPro: TDBGridPro
      Left = 8
      Top = 21
      Width = 641
      Height = 111
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = InvResultDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      OnDrawColumnCell = StatusDBGridDrawColumnCell
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
      OnColumnSort = InvResDBGridProColumnSort
    end
    object CloseButton: TButton
      Left = 656
      Top = 403
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Schlie'#223'en'
      ModalResult = 2
      TabOrder = 4
    end
  end
  object PlanPanel: TPanel
    Left = 0
    Top = 0
    Width = 734
    Height = 169
    Align = alTop
    BevelOuter = bvNone
    Constraints.MinHeight = 150
    TabOrder = 1
    DesignSize = (
      734
      169)
    object Label11: TLabel
      Left = 7
      Top = 2
      Width = 95
      Height = 13
      Caption = 'Geplante Positionen'
    end
    object InvPlanPosDBGrid: TDBGridPro
      Left = 8
      Top = 16
      Width = 641
      Height = 145
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = InvPlanPosDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      OnDrawColumnCell = StatusDBGridDrawColumnCell
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
      OnColumnSort = InvPosDBGridProColumnSort
    end
  end
  object PosPanel: TPanel
    Left = 0
    Top = 172
    Width = 734
    Height = 158
    Align = alTop
    BevelOuter = bvNone
    Constraints.MinHeight = 150
    TabOrder = 2
    DesignSize = (
      734
      158)
    object Label1: TLabel
      Left = 8
      Top = 6
      Width = 87
      Height = 13
      Caption = 'Inventurpositionen'
    end
    object InvPosDBGridPro: TDBGridPro
      Left = 9
      Top = 25
      Width = 640
      Height = 123
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = InvPosDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      OnDrawColumnCell = StatusDBGridDrawColumnCell
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
      OnColumnSort = InvPosDBGridProColumnSort
    end
  end
  object KommPosDataSource: TDataSource
    Left = 624
    Top = 112
  end
  object ADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 752
    Top = 144
  end
  object ArtLPBetterADODataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 224
    Top = 296
  end
  object ArtLPDataSource: TDataSource
    DataSet = ArtLPBetterADODataSet
    OnDataChange = ArtLPDataSourceDataChange
    Left = 264
    Top = 296
  end
  object InvResultDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 528
    Top = 104
  end
  object LEBetterADODataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 248
    Top = 48
  end
  object InvPosDataSource: TDataSource
    DataSet = InvPosDataSet
    OnDataChange = InvPosOnDataChange
    Left = 472
    Top = 232
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 688
    Top = 248
  end
  object InvErgPopupMenu: TPopupMenu
    Left = 472
    Top = 152
    object InvErgDelPopUpMenuItem: TMenuItem
      Caption = 'Inventurergebnis l'#246'schen...'
      OnClick = InvDelButtonOnClick
    end
  end
  object BetterADODataSet1: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 688
    Top = 280
  end
  object InvResultDataSource: TDataSource
    DataSet = InvResultDataSet
    OnDataChange = InvResultDataSourceDataChange
    Left = 392
    Top = 48
  end
  object InvPlanPosDataSource: TDataSource
    DataSet = InvPlanPosDataSet
    OnDataChange = InvPlanPosDataSourceDataChange
    Left = 336
    Top = 40
  end
  object InvPosDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 440
    Top = 232
  end
  object InvPlanPosDataSet: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 304
    Top = 40
  end
end
