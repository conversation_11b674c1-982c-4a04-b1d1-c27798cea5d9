object EditEinheitBarcodeForm: TEditEinheitBarcodeForm
  Left = 232
  Top = 92
  ClientHeight = 230
  ClientWidth = 404
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    404
    230)
  TextHeight = 13
  object Label5: TLabel
    Left = 8
    Top = 88
    Width = 22
    Height = 13
    Caption = 'EAN'
  end
  object Label22: TLabel
    Left = 136
    Top = 89
    Width = 40
    Height = 13
    Caption = 'Barcode'
  end
  object Bevel1: TBevel
    Left = 6
    Top = 74
    Width = 394
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label1: TLabel
    Left = 16
    Top = 16
    Width = 32
    Height = 13
    Caption = 'Artikel:'
  end
  object ArtikelLabel: TLabel
    Left = 80
    Top = 16
    Width = 68
    Height = 13
    Caption = 'ArtikelLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label3: TLabel
    Left = 16
    Top = 40
    Width = 35
    Height = 13
    Caption = 'Einheit:'
  end
  object VPELabel: TLabel
    Left = 80
    Top = 40
    Width = 56
    Height = 13
    Caption = 'VPELabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Bevel2: TBevel
    Left = 6
    Top = 186
    Width = 394
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 138
  end
  object Label2: TLabel
    Left = 8
    Top = 140
    Width = 64
    Height = 13
    Caption = 'PZN-Nummer'
  end
  object Label4: TLabel
    Left = 136
    Top = 140
    Width = 25
    Height = 13
    Caption = 'ASIN'
  end
  object OkButton: TButton
    Left = 238
    Top = 198
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 4
    OnClick = OkButtonClick
  end
  object AbortButton: TButton
    Left = 323
    Top = 198
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
  object EANEdit: TEdit
    Left = 8
    Top = 107
    Width = 111
    Height = 21
    MaxLength = 32
    TabOrder = 0
    Text = 'Edit'
    OnChange = EANEditChange
    OnExit = EANEditExit
    OnKeyPress = EANEditKeyPress
  end
  object BarcodeEdit: TEdit
    Left = 136
    Top = 108
    Width = 260
    Height = 21
    MaxLength = 32
    TabOrder = 1
    Text = 'Edit'
    OnExit = EANEditExit
  end
  object PZNEdit: TEdit
    Left = 8
    Top = 159
    Width = 111
    Height = 21
    MaxLength = 8
    TabOrder = 2
    Text = 'Edit'
    OnExit = EANEditExit
  end
  object ASINEdit: TEdit
    Left = 136
    Top = 159
    Width = 111
    Height = 21
    MaxLength = 10
    TabOrder = 3
    Text = 'Edit'
    OnExit = EANEditExit
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 360
    Top = 8
  end
end
