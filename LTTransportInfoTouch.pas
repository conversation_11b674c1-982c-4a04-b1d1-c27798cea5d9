unit LTTransportInfoTouch;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TEinlagerArt = (eaNone, eaDirket, eaTransport, eaBereitstellung, eaCrossdock);

  TLTTransportInfoForm = class(TForm)
    BereitButton: TButton;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    LTLabel: TLabel;
    ArtikelLabel: TLabel;
    MengeLabel: TLabel;
    BereichLabel: TLabel;
    ZoneLabel: TLabel;
    Bevel1: TBevel;
    Label5: TLabel;
    Bevel2: TBevel;
    Label4: TLabel;
    VorgangLabel: TLabel;
    CrossButton: TButton;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure BereitButtonClick(Sender: TObject);
    procedure CrossButtonClick(Sender: TObject);
  private
    fEinlagerArt : TEinlagerArt;
  public
    property EinlagerArt : TEinlagerArt read fEinlagerArt write fEinlagerArt;
  end;

implementation

uses SprachModul;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLTTransportInfoForm.CrossButtonClick(Sender: TObject);
begin
  fEinlagerArt := eaCrossdock;
  ModalResult := mrOk;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLTTransportInfoForm.FormCreate(Sender: TObject);
begin
  fEinlagerArt := eaNone;

  ArtikelLabel.Caption := '';
  MengeLabel.Caption   := '';
  VorgangLabel.Caption := '';
  BereichLabel.Caption := '';
  ZoneLabel.Caption    := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, VorgangLabel);
    LVSSprachModul.SetNoTranslate (Self, BereichLabel);
    LVSSprachModul.SetNoTranslate (Self, ZoneLabel);
    LVSSprachModul.SetNoTranslate (Self, LTLabel);
    LVSSprachModul.SetNoTranslate (Self, ArtikelLabel);
    LVSSprachModul.SetNoTranslate (Self, MengeLabel);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLTTransportInfoForm.FormShow(Sender: TObject);
begin
  //Button zentrieren
  if not (CrossButton.Visible) then
    BereitButton.Left := (ClientWidth div 2) - BereitButton.Width div 2;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLTTransportInfoForm.BereitButtonClick(Sender: TObject);
begin
  fEinlagerArt := eaBereitstellung;
  ModalResult := mrOk;
end;

end.
