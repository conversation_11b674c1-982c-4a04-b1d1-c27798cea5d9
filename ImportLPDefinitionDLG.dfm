object ImportLPDefinitionForm: TImportLPDefinitionForm
  Left = 0
  Top = 0
  Caption = 'Impo<PERSON> von <PERSON>-Definitionen'
  ClientHeight = 684
  ClientWidth = 1050
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  DesignSize = (
    1050
    684)
  TextHeight = 13
  object Label2: TLabel
    Left = 8
    Top = 56
    Width = 122
    Height = 13
    Caption = 'Lagerzone in dem Bereich'
  end
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 94
    Height = 13
    Caption = 'Neuer Lagerbereich'
  end
  object Label28: TLabel
    Left = 8
    Top = 104
    Width = 69
    Height = 13
    Caption = 'Lagerplatz-Art'
  end
  object Label24: TLabel
    Left = 8
    Top = 179
    Width = 29
    Height = 13
    Caption = 'L'#228'nge'
  end
  object Label25: TLabel
    Left = 8
    Top = 206
    Width = 24
    Height = 13
    Caption = 'Tiefe'
  end
  object Label33: TLabel
    Left = 8
    Top = 233
    Width = 25
    Height = 13
    Caption = 'H'#246'he'
  end
  object Label36: TLabel
    Left = 143
    Top = 179
    Width = 16
    Height = 13
    Caption = 'mm'
  end
  object Label37: TLabel
    Left = 143
    Top = 206
    Width = 16
    Height = 13
    Caption = 'mm'
  end
  object Label38: TLabel
    Left = 143
    Top = 233
    Width = 16
    Height = 13
    Caption = 'mm'
  end
  object Image1: TImage
    Left = 191
    Top = 160
    Width = 282
    Height = 103
    Center = True
    Proportional = True
    Transparent = True
  end
  object Label35: TLabel
    Left = 8
    Top = 279
    Width = 78
    Height = 13
    Caption = 'Max. Anzahl LTs'
  end
  object Label29: TLabel
    Left = 128
    Top = 279
    Width = 89
    Height = 13
    Caption = 'Max. Belastbarkeit'
  end
  object Label30: TLabel
    Left = 203
    Top = 298
    Width = 11
    Height = 13
    Caption = 'kg'
  end
  object Label31: TLabel
    Left = 240
    Top = 279
    Width = 67
    Height = 13
    Caption = 'Belegungsprio'
  end
  object Label32: TLabel
    Left = 296
    Top = 290
    Width = 99
    Height = 26
    Caption = '999=zuerst belegen'#13#10'    0=zuletzt belegen'
  end
  object Label3: TLabel
    Left = 8
    Top = 332
    Width = 53
    Height = 13
    Caption = 'Start LP-Nr'
  end
  object Label4: TLabel
    Left = 128
    Top = 332
    Width = 80
    Height = 13
    Caption = 'Offset pro Regal'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 150
    Width = 1036
    Height = 15
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 451
  end
  object Bevel2: TBevel
    Left = 8
    Top = 269
    Width = 1036
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 408
  end
  object Bevel3: TBevel
    Left = 8
    Top = 379
    Width = 1036
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 408
  end
  object Bevel4: TBevel
    Left = 8
    Top = 322
    Width = 1036
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 408
  end
  object LBZoneComboBox: TComboBoxPro
    Left = 8
    Top = 72
    Width = 1034
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 0
  end
  object LBComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 1034
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 1
    OnChange = LBComboBoxChange
  end
  object LPArtComboBox: TComboBoxPro
    Left = 8
    Top = 120
    Width = 1034
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 80
    ItemHeight = 15
    TabOrder = 2
  end
  object FBLEdit: TEdit
    Left = 60
    Top = 176
    Width = 77
    Height = 21
    TabOrder = 3
    Text = 'FBLEdit'
    OnKeyPress = IntEditKeyPress
  end
  object FBBEdit: TEdit
    Left = 60
    Top = 203
    Width = 77
    Height = 21
    TabOrder = 4
    Text = 'FBBEdit'
    OnKeyPress = IntEditKeyPress
  end
  object FBHEdit: TEdit
    Left = 60
    Top = 230
    Width = 77
    Height = 21
    TabOrder = 5
    Text = 'FBHEdit'
    OnKeyPress = IntEditKeyPress
  end
  object LTAnzEdit: TEdit
    Left = 8
    Top = 295
    Width = 75
    Height = 21
    TabOrder = 6
    Text = '0'
    OnKeyPress = IntEditKeyPress
  end
  object LTAnzUpDown: TIntegerUpDown
    Left = 83
    Top = 295
    Width = 16
    Height = 21
    Associate = LTAnzEdit
    Max = 9999
    TabOrder = 7
  end
  object MaxGewichtEdit: TEdit
    Left = 128
    Top = 295
    Width = 70
    Height = 21
    TabOrder = 8
    Text = 'MaxGewichtEdit'
    OnKeyPress = IntEditKeyPress
  end
  object PrioEdit: TEdit
    Left = 240
    Top = 295
    Width = 36
    Height = 21
    MaxLength = 3
    TabOrder = 9
    Text = '0'
    OnKeyPress = IntEditKeyPress
  end
  object PrioUpDown: TIntegerUpDown
    Left = 276
    Top = 295
    Width = 16
    Height = 21
    Associate = PrioEdit
    Max = 999
    TabOrder = 10
  end
  object ReadButton: TButton
    Left = 8
    Top = 652
    Width = 75
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Einlesen'
    TabOrder = 14
    OnClick = ReadButtonClick
  end
  object ImportStringGrid: TStringGridPro
    Left = 9
    Top = 391
    Width = 1033
    Height = 255
    Anchors = [akLeft, akTop, akRight, akBottom]
    ColCount = 22
    DefaultColWidth = 40
    DefaultRowHeight = 16
    PopupMenu = ImportStringGridPopupMenu
    TabOrder = 13
    OnBeforeSort = ImportStringGridBeforeSort
    OnBeginSort = ImportStringGridBeginSort
    OnEndSort = ImportStringGridEndSort
    TitelTexte.Strings = (
      ''
      'Bereich'
      'Zone'
      'Reihe'
      'Feld'
      'Platz'
      'Ebene'
      'Bezeichnung'
      'LP-Nr.'
      'Folgenr.'
      'Stellplatznr.'
      'Barcode-ID'
      'HKL'
      'Typ'
      'Kapazit'#228't'
      'Sperr'
      'Stapelh'#246'he'
      'ABC-Klasse'
      'Scanbar'
      'L'
      'B'
      'H')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'Tahoma'
    TitelFont.Style = []
    ExplicitWidth = 1015
    ColWidths = (
      32
      60
      50
      31
      30
      28
      36
      74
      52
      52
      60
      60
      39
      43
      55
      33
      61
      61
      49
      28
      27
      28)
  end
  object CreateButton: TButton
    Left = 776
    Top = 653
    Width = 265
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Erzeugen'
    TabOrder = 15
    OnClick = CreateButtonClick
    ExplicitLeft = 688
  end
  object LPNummerEdit: TEdit
    Left = 8
    Top = 348
    Width = 75
    Height = 21
    TabOrder = 11
    Text = 'LPNummerEdit'
    OnKeyPress = IntEditKeyPress
  end
  object RegalOffsetEdit: TEdit
    Left = 128
    Top = 348
    Width = 75
    Height = 21
    TabOrder = 12
    Text = 'RegalOffsetEdit'
    OnKeyPress = IntEditKeyPress
  end
  object LPNrFolgeRadioButton: TRadioButton
    Left = 253
    Top = 342
    Width = 164
    Height = 17
    Caption = 'Folgenr. = Platznr.'
    Checked = True
    TabOrder = 16
    TabStop = True
  end
  object GridFolgeRadioButton: TRadioButton
    Left = 253
    Top = 358
    Width = 164
    Height = 17
    Caption = 'Folgenr. = Grid-Reihenfolge'
    TabOrder = 17
  end
  object ImpFolgeRadioButton: TRadioButton
    Left = 253
    Top = 326
    Width = 164
    Height = 17
    Caption = 'Folgenr. aus Import'
    TabOrder = 18
  end
  object CreateOnlyNewCheckBox: TCheckBox
    Left = 440
    Top = 326
    Width = 249
    Height = 17
    Caption = 'Nur neue Pl'#228'tze anlegen, keien Update'
    Checked = True
    State = cbChecked
    TabOrder = 19
  end
  object AbortErrorCheckBox: TCheckBox
    Left = 440
    Top = 349
    Width = 273
    Height = 17
    Caption = 'Abbruch beim ersten Fehler'
    Checked = True
    State = cbChecked
    TabOrder = 20
  end
  object OpenDialog1: TOpenDialog
    DefaultExt = '*.csv'
    Filter = 'CSV-Dateien|*.csv'
    Left = 360
    Top = 536
  end
  object ImportStringGridPopupMenu: TPopupMenu
    Left = 208
    Top = 552
    object Reihenneubestimmen1: TMenuItem
      Caption = 'Reihen neu bestimmen'
      OnClick = Reihenneubestimmen1Click
    end
  end
end
