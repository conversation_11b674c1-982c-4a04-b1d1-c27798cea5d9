unit FrontendODACDatasets;

interface

uses
  <PERSON>ys<PERSON><PERSON><PERSON>, Classes, DB, MemDS, DBA<PERSON>ess, Ora, OraSmart, DatenModul;

type
  TFrontendODACDataModule = class(TDataModule)
    AufKopfDataSet: TSmartQuery;
    LagerBesDataSet: TSmartQuery;
    LagerREVDataSet: TSmartQuery;
    WADataSet: TSmartQuery;
    RetoureAvisDataSet: TSmartQuery;
    KommKopfDataSet: TSmartQuery;
    OMSOrderQuery: TSmartQuery;
    OMSOrderPosQuery: TSmartQuery;
    BatchKopfDataSet: TSmartQuery;
    WarenBesPoolDataSet: TSmartQuery;
    WarenBesDataSet: TSmartQuery;
    WarenBesBesDataSet: TSmartQuery;
    RetoureDataSet: TSmartQuery;
    ProtoDataSet: TSmartQuery;
    ZollBesDataset: TSmartQuery;
    ZollBesMoveDataset: TSmartQuery;
    procedure BeforeExecute(Sender: TObject);
    procedure AfterExecute(Sender: TObject; Result: Boolean);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

var
  FrontendODACDataModule: TFrontendODACDataModule;

implementation

{$R *.dfm}

uses
  Controls, Forms;

procedure TFrontendODACDataModule.AfterExecute(Sender: TObject; Result: Boolean);
begin
  Screen.Cursor := crDefault;
end;

procedure TFrontendODACDataModule.BeforeExecute(Sender: TObject);
begin
  if not ((Screen.Cursor = crSQLWait) or (Screen.Cursor = crHourGlass)) then
    Screen.Cursor := crSQLWait;
end;

end.
