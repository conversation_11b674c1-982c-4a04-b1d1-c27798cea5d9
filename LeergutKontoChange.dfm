object LeergutKontoChangeForm: TLeergutKontoChangeForm
  Left = 447
  Top = 167
  BorderStyle = bsDialog
  Caption = 'Neues Leergutkonto anlegen'
  ClientHeight = 232
  ClientWidth = 447
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 16
    Width = 59
    Height = 13
    Caption = 'Konto-Name'
  end
  object Label2: TLabel
    Left = 8
    Top = 72
    Width = 68
    Height = 13
    Caption = 'Konto-Besitzer'
  end
  object Label3: TLabel
    Left = 8
    Top = 128
    Width = 65
    Height = 13
    Caption = 'Beschreibung'
  end
  object NameEdit: TEdit
    Left = 8
    Top = 32
    Width = 121
    Height = 21
    TabOrder = 0
    Text = 'NameEdit'
  end
  object BesitzerEdit: TEdit
    Left = 8
    Top = 88
    Width = 345
    Height = 21
    TabOrder = 1
    Text = 'BesitzerEdit'
  end
  object OkButton: TButton
    Left = 248
    Top = 192
    Width = 75
    Height = 25
    Caption = 'Anlegen'
    ModalResult = 1
    TabOrder = 3
  end
  object Button2: TButton
    Left = 360
    Top = 192
    Width = 75
    Height = 25
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 4
  end
  object BeschreibungEdit: TEdit
    Left = 8
    Top = 144
    Width = 345
    Height = 21
    TabOrder = 2
    Text = 'BeschreibungEdit'
  end
end
