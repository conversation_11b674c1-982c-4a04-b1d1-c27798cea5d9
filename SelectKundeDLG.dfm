object SelectKundeForm: TSelectKundeForm
  Left = 426
  Top = 388
  Caption = 'Kundennummer ausw'#228'hlen'
  ClientHeight = 510
  ClientWidth = 634
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    634
    510)
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 104
    Width = 37
    Height = 13
    Caption = 'Kunden'
  end
  object Label2: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 96
    Width = 617
    Height = 4
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 583
  end
  object Label3: TLabel
    Left = 8
    Top = 52
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Bevel2: TBevel
    Left = 8
    Top = 363
    Width = 617
    Height = 4
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitWidth = 583
  end
  object Label4: TLabel
    Left = 8
    Top = 376
    Width = 43
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Bestellnr.'
  end
  object Bevel3: TBevel
    Left = 8
    Top = 467
    Width = 617
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 352
    ExplicitWidth = 593
  end
  object Bevel4: TBevel
    Left = 8
    Top = 403
    Width = 617
    Height = 4
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitWidth = 583
  end
  object Label5: TLabel
    Left = 8
    Top = 416
    Width = 74
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Lieferscheintext'
  end
  object Label6: TLabel
    Left = 240
    Top = 376
    Width = 46
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Auftragnr.'
  end
  object Label7: TLabel
    Left = 8
    Top = 443
    Width = 62
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Adresszusatz'
  end
  object KundenDBGrid: TDBGridPro
    Left = 9
    Top = 120
    Width = 617
    Height = 237
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = KundenDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 2
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnKeyPress = KundenDBGridKeyPress
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
    OnColumnSort = KundenDBGridColumnSort
  end
  object OkButton: TButton
    Left = 462
    Top = 477
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 7
  end
  object AbortButton: TButton
    Left = 550
    Top = 477
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 8
  end
  object MandantComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 617
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 120
    TabOrder = 0
    OnChange = MandantComboBoxChange
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 69
    Width = 618
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 120
    TabOrder = 1
    OnChange = LagerComboBoxChange
  end
  object BestNrEdit: TEdit
    Left = 88
    Top = 373
    Width = 129
    Height = 21
    Anchors = [akLeft, akBottom]
    MaxLength = 32
    TabOrder = 3
    Text = 'BestNrEdit'
  end
  object LSTextEdit: TEdit
    Left = 88
    Top = 413
    Width = 537
    Height = 21
    Anchors = [akLeft, akRight, akBottom]
    MaxLength = 256
    TabOrder = 5
    Text = 'BestNrEdit'
  end
  object AufNrEdit: TEdit
    Left = 305
    Top = 373
    Width = 144
    Height = 21
    Anchors = [akLeft, akBottom]
    MaxLength = 32
    TabOrder = 4
    Text = 'BestNrEdit'
  end
  object LSHintEdit: TEdit
    Left = 88
    Top = 440
    Width = 538
    Height = 21
    Anchors = [akLeft, akRight, akBottom]
    MaxLength = 64
    TabOrder = 6
    Text = 'BestNrEdit'
  end
  object AllLiefCheckBox: TCheckBox
    Left = 462
    Top = 100
    Width = 164
    Height = 17
    Caption = 'Alle Lieferanten anzeigen'
    TabOrder = 9
    OnClick = AllLiefCheckBoxClick
  end
  object KundenQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 280
    Top = 120
  end
  object KundenDataSource: TDataSource
    DataSet = KundenQuery
    Left = 240
    Top = 120
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 544
    Top = 296
  end
end
