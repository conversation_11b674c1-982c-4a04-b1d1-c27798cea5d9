object TestEAN128Form: TTestEAN128Form
  Left = 584
  Top = 220
  BorderStyle = bsDialog
  Caption = 'Auswertung der EAN128 Barcodes'
  ClientHeight = 450
  ClientWidth = 610
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  DesignSize = (
    610
    450)
  TextHeight = 13
  object Label21: TLabel
    Left = 389
    Top = 360
    Width = 61
    Height = 13
    Caption = 'Scanner-Typ'
  end
  object OkButton: TButton
    Left = 526
    Top = 403
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'OK'
    Default = True
    ModalResult = 1
    TabOrder = 0
    OnClick = OkButtonClick
  end
  object StatusBar1: TStatusBar
    Left = 0
    Top = 431
    Width = 610
    Height = 19
    Panels = <
      item
        Width = 500
      end
      item
        Width = 50
      end>
  end
  object FehlerLabel: TPanel
    Left = 8
    Top = 313
    Width = 593
    Height = 33
    BevelOuter = bvNone
    Caption = 'FehlerLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 2
  end
  object DelButton: TButton
    Left = 8
    Top = 403
    Width = 75
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'L'#246'schen'
    TabOrder = 3
    OnClick = DelButtonClick
  end
  object ScannerComboBox: TComboBox
    Left = 456
    Top = 357
    Width = 145
    Height = 21
    Style = csDropDownList
    ItemIndex = 0
    TabOrder = 4
    Text = 'Symbol LS1908'
    OnChange = ScannerComboBoxChange
    Items.Strings = (
      'Symbol LS1908'
      'Baracode 2604')
  end
  object PageControl1: TPageControl
    Left = 9
    Top = 8
    Width = 593
    Height = 297
    ActivePage = TabSheet1
    TabOrder = 5
    object TabSheet1: TTabSheet
      Caption = 'EAN128 Produkt'
      DesignSize = (
        585
        269)
      object GroupBox1: TGroupBox
        Left = 13
        Top = 3
        Width = 569
        Height = 249
        Anchors = [akLeft, akTop, akRight]
        Caption = ' EAN128-Infos '
        TabOrder = 0
        object Label1: TLabel
          Left = 16
          Top = 16
          Width = 43
          Height = 13
          Caption = 'EAN (01)'
        end
        object Label2: TLabel
          Left = 16
          Top = 58
          Width = 46
          Height = 13
          Caption = 'MHD (15)'
        end
        object Label3: TLabel
          Left = 192
          Top = 58
          Width = 55
          Height = 13
          Caption = 'Charge (10)'
        end
        object Label4: TLabel
          Left = 16
          Top = 104
          Width = 43
          Height = 13
          Caption = 'NVE (00)'
        end
        object Label5: TLabel
          Left = 16
          Top = 148
          Width = 95
          Height = 13
          Caption = 'Nettogewicht (310x)'
        end
        object Label6: TLabel
          Left = 16
          Top = 201
          Width = 33
          Height = 13
          Caption = 'Menge'
        end
        object Label8: TLabel
          Left = 192
          Top = 201
          Width = 65
          Height = 13
          Caption = 'Einheiten (37)'
        end
        object Label9: TLabel
          Left = 118
          Top = 167
          Width = 12
          Height = 13
          Caption = 'kg'
        end
        object Label10: TLabel
          Left = 192
          Top = 104
          Width = 97
          Height = 13
          Caption = 'Bruttogewicht (330x)'
        end
        object Label11: TLabel
          Left = 296
          Top = 123
          Width = 12
          Height = 13
          Caption = 'kg'
        end
        object Label12: TLabel
          Left = 192
          Top = 16
          Width = 97
          Height = 13
          Caption = 'Enthaltene EAN (02)'
        end
        object Label13: TLabel
          Left = 368
          Top = 16
          Width = 76
          Height = 13
          Caption = 'Lieferant-Nr (97)'
        end
        object Label19: TLabel
          Left = 368
          Top = 58
          Width = 84
          Height = 13
          Caption = 'Verfallsdatum (17)'
        end
        object Label20: TLabel
          Left = 368
          Top = 104
          Width = 113
          Height = 13
          Caption = 'Verpackungsdatum (13)'
        end
        object Label22: TLabel
          Left = 368
          Top = 200
          Width = 70
          Height = 13
          Caption = 'Bestellnr. (400)'
        end
        object Label33: TLabel
          Left = 368
          Top = 148
          Width = 88
          Height = 13
          Caption = 'Seriennummer (21)'
        end
        object Label23: TLabel
          Left = 192
          Top = 148
          Width = 78
          Height = 13
          Caption = 'Produkt ID (240)'
        end
        object EANEdit: TEdit
          Left = 16
          Top = 32
          Width = 121
          Height = 21
          TabOrder = 0
          Text = 'EANEdit'
          OnChange = ChangeInput
          OnKeyPress = EANEditKeyPress
        end
        object MHDEdit: TEdit
          Left = 16
          Top = 74
          Width = 121
          Height = 21
          TabOrder = 1
          Text = 'MHDEdit'
          OnChange = ChangeInput
          OnKeyPress = MHDEditKeyPress
        end
        object ChargeEdit: TEdit
          Left = 192
          Top = 74
          Width = 121
          Height = 21
          TabOrder = 2
          Text = 'ChargeEdit'
          OnChange = ChangeInput
        end
        object NVEEdit: TEdit
          Left = 16
          Top = 120
          Width = 121
          Height = 21
          TabOrder = 3
          Text = 'NVEEdit'
          OnChange = ChangeInput
        end
        object NettoEdit: TEdit
          Left = 16
          Top = 164
          Width = 97
          Height = 21
          TabOrder = 4
          Text = 'NettoEdit'
          OnChange = ChangeInput
          OnKeyPress = NettoEditKeyPress
        end
        object MengeEdit: TEdit
          Left = 16
          Top = 217
          Width = 73
          Height = 21
          TabOrder = 5
          Text = '0'
          OnChange = ChangeInput
          OnKeyPress = MengeEditKeyPress
        end
        object EinheitEdit: TEdit
          Left = 192
          Top = 217
          Width = 121
          Height = 21
          TabStop = False
          Enabled = False
          TabOrder = 6
          Text = 'EinheitEdit'
        end
        object BruttoEdit: TEdit
          Left = 192
          Top = 120
          Width = 97
          Height = 21
          Enabled = False
          TabOrder = 7
          Text = 'BruttoEdit'
        end
        object InhaltEANEdit: TEdit
          Left = 192
          Top = 32
          Width = 121
          Height = 21
          Enabled = False
          TabOrder = 8
          Text = 'InhaltEANEdit'
        end
        object MengeUpDown: TIntegerUpDown
          Left = 89
          Top = 217
          Width = 16
          Height = 21
          Associate = MengeEdit
          Max = 10000
          TabOrder = 9
        end
        object Edit1: TEdit
          Left = 368
          Top = 32
          Width = 121
          Height = 21
          Enabled = False
          TabOrder = 10
          Text = 'LFEdit'
        end
        object VerDateEdit: TEdit
          Left = 368
          Top = 74
          Width = 121
          Height = 21
          TabOrder = 11
          Text = 'VerDateEdit'
        end
        object PackDateEdit: TEdit
          Left = 368
          Top = 120
          Width = 121
          Height = 21
          TabOrder = 12
          Text = 'PackDateEdit'
        end
        object BestNrEdit: TEdit
          Left = 368
          Top = 216
          Width = 121
          Height = 21
          TabOrder = 13
          Text = 'BestNrEdit'
        end
        object SerialEdit: TEdit
          Left = 368
          Top = 164
          Width = 121
          Height = 21
          TabOrder = 14
          Text = 'SerialEdit'
        end
        object ProduktIDEdit: TEdit
          Left = 192
          Top = 164
          Width = 121
          Height = 21
          TabOrder = 15
          Text = 'ProduktIDEdit'
        end
      end
    end
    object TabSheet2: TTabSheet
      Caption = 'EAN128 ORGA Invent'
      ImageIndex = 1
      object GroupBox2: TGroupBox
        Left = 8
        Top = 8
        Width = 569
        Height = 257
        Caption = 'Verarbeitung'
        TabOrder = 0
        object Label7: TLabel
          Left = 8
          Top = 16
          Width = 95
          Height = 13
          Caption = 'Ursprungsland (422)'
        end
        object Label14: TLabel
          Left = 8
          Top = 64
          Width = 99
          Height = 13
          Caption = '1. Verarbeitung (423)'
        end
        object Label15: TLabel
          Left = 152
          Top = 64
          Width = 87
          Height = 13
          Caption = 'Verarbeitung (424)'
        end
        object Label16: TLabel
          Left = 8
          Top = 112
          Width = 75
          Height = 13
          Caption = 'Zerlegung (425)'
        end
        object Label17: TLabel
          Left = 320
          Top = 64
          Width = 132
          Height = 13
          Caption = 'Gesamte Verarbeitung (426)'
        end
        object Label18: TLabel
          Left = 8
          Top = 160
          Width = 97
          Height = 13
          Caption = 'Zulassungsnummern'
        end
        object Edit2: TEdit
          Left = 8
          Top = 32
          Width = 121
          Height = 21
          TabOrder = 0
          Text = 'Edit2'
        end
        object Edit3: TEdit
          Left = 8
          Top = 80
          Width = 121
          Height = 21
          TabOrder = 1
          Text = 'Edit3'
        end
        object Edit4: TEdit
          Left = 152
          Top = 80
          Width = 121
          Height = 21
          TabOrder = 2
          Text = 'Edit4'
        end
        object Edit5: TEdit
          Left = 8
          Top = 128
          Width = 121
          Height = 21
          TabOrder = 3
          Text = 'Edit5'
        end
        object Edit6: TEdit
          Left = 320
          Top = 80
          Width = 121
          Height = 21
          TabOrder = 4
          Text = 'Edit6'
        end
        object Edit7: TEdit
          Left = 8
          Top = 176
          Width = 100
          Height = 21
          TabOrder = 5
          Text = 'Edit7'
        end
        object Edit8: TEdit
          Left = 120
          Top = 176
          Width = 100
          Height = 21
          TabOrder = 6
          Text = 'Edit7'
        end
        object Edit9: TEdit
          Left = 232
          Top = 176
          Width = 100
          Height = 21
          TabOrder = 7
          Text = 'Edit7'
        end
        object Edit10: TEdit
          Left = 344
          Top = 176
          Width = 100
          Height = 21
          TabOrder = 8
          Text = 'Edit7'
        end
        object Edit11: TEdit
          Left = 456
          Top = 176
          Width = 100
          Height = 21
          TabOrder = 9
          Text = 'Edit7'
        end
      end
    end
    object TabSheet3: TTabSheet
      Caption = 'EAN 26er & 28er (Gewicht)'
      ImageIndex = 2
      object Label24: TLabel
        Left = 8
        Top = 72
        Width = 73
        Height = 13
        Caption = 'Artikel-Nr (28er)'
      end
      object Label25: TLabel
        Left = 160
        Top = 72
        Width = 69
        Height = 13
        Caption = 'Gewicht (28er)'
      end
      object Label28: TLabel
        Left = 8
        Top = 136
        Width = 73
        Height = 13
        Caption = 'Artikel-Nr (26er)'
      end
      object Label29: TLabel
        Left = 155
        Top = 136
        Width = 69
        Height = 13
        Caption = 'Gewicht (26er)'
      end
      object Label31: TLabel
        Left = 8
        Top = 8
        Width = 73
        Height = 13
        Caption = 'Artikel-Nr (29er)'
      end
      object Label32: TLabel
        Left = 160
        Top = 8
        Width = 69
        Height = 13
        Caption = 'Gewicht (29er)'
      end
      object ArtNr28Edit: TEdit
        Left = 8
        Top = 88
        Width = 121
        Height = 21
        TabOrder = 0
        Text = 'ArtNr28Edit'
      end
      object Gewicht28Edit: TEdit
        Left = 160
        Top = 88
        Width = 121
        Height = 21
        TabOrder = 1
        Text = 'Gewicht28Edit'
      end
      object ArtNr26Edit: TEdit
        Left = 8
        Top = 152
        Width = 121
        Height = 21
        TabOrder = 2
        Text = 'ArtNrEdit'
      end
      object Gewicht26Edit: TEdit
        Left = 155
        Top = 152
        Width = 121
        Height = 21
        TabOrder = 3
        Text = 'Gewicht26Edit'
      end
      object ArtNr29Edit: TEdit
        Left = 8
        Top = 24
        Width = 121
        Height = 21
        TabOrder = 4
        Text = 'ArtNr28Edit'
      end
      object Gewicht29Edit: TEdit
        Left = 160
        Top = 24
        Width = 121
        Height = 21
        TabOrder = 5
        Text = 'Gewicht29Edit'
      end
    end
    object TabSheet4: TTabSheet
      Caption = 'EAN 23er (Preis)'
      ImageIndex = 3
      object Label26: TLabel
        Left = 8
        Top = 8
        Width = 73
        Height = 13
        Caption = 'Artikel-Nr (23er)'
      end
      object Label27: TLabel
        Left = 160
        Top = 8
        Width = 53
        Height = 13
        Caption = 'Preis (23er)'
      end
      object PreisArtNrEdit: TEdit
        Left = 8
        Top = 24
        Width = 121
        Height = 21
        TabOrder = 0
        Text = 'ArtNrEdit'
      end
      object PreisEdit: TEdit
        Left = 160
        Top = 24
        Width = 121
        Height = 21
        TabOrder = 1
        Text = 'Edit'
      end
    end
    object TabSheet5: TTabSheet
      Caption = 'Kundenspezifische Infos'
      ImageIndex = 4
      object Label30: TLabel
        Left = 8
        Top = 8
        Width = 55
        Height = 13
        Caption = 'LE-Nummer'
      end
      object LENrEdit: TEdit
        Left = 8
        Top = 24
        Width = 121
        Height = 21
        TabOrder = 0
        Text = 'LENrEdit'
      end
    end
    object BarTabSheet: TTabSheet
      Caption = 'Barcode'
      ImageIndex = 5
      DesignSize = (
        585
        269)
      object BarMemo: TMemo
        Left = 3
        Top = 3
        Width = 579
        Height = 106
        Anchors = [akLeft, akTop, akRight]
        Lines.Strings = (
          'BarMemo')
        TabOrder = 0
      end
      object HexBarMemo: TMemo
        Left = 3
        Top = 115
        Width = 579
        Height = 151
        Anchors = [akLeft, akTop, akRight, akBottom]
        Lines.Strings = (
          'BarMemo')
        TabOrder = 1
      end
    end
  end
  object ArCheckButton: TButton
    Left = 93
    Top = 403
    Width = 115
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Artikel '#252'berpr'#252'fen'
    TabOrder = 6
    OnClick = ArCheckButtonClick
  end
  object PrintProtoButton: TButton
    Left = 217
    Top = 403
    Width = 115
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Protokoll drucken...'
    TabOrder = 7
    OnClick = PrintProtoButtonClick
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 536
    Top = 24
  end
  object ADOQuery1: TADOQuery
    Parameters = <>
    Left = 540
    Top = 72
  end
end
