unit TerminalServices;

interface

uses
  Windows, SysUtils, WinSock, WTSAPI32;

type

  tWTSQuerySessionInformation = function(hServer: THandle; SessionId: DWORD;
    WTSInfoClass: TWtsInfoClass; var ppBuffer: Pointer; var pBytesReturned:
DWORD): BOOL; stdcall;

  tWTSFreeMemory = procedure(pMemory: Pointer); stdcall;

  tTerminalService = class
  private
    WTSQuerySessionInformation: tWTSQuerySessionInformation;
    WTSFreeMemory: tWTSFreeMemory;
    WTSAPI32: hInst;
    Initialised: boolean;
    procedure GetEntryPoints;
    procedure Initialise;
  public
    function IsAPIAvailable: boolean;
    function GetConnectStatus : TWtsConnectStateClass;
    class function IsRemoteSession: boolean;
    function ClientIPAddress: string;
    function ClientName: string;
    function ClientSessionId : ULONG;
    constructor Create;
    destructor Destroy; override;
  end;

function CitrixClientName : String;

implementation

uses
  <PERSON>mObj, Variants;

{ tTerminalService }

function tTerminalService.ClientIPAddress: string;
var
  ReturnPointer: pointer;
  ByteCount: cardinal;
  ClientAddress: WTS_Client_Address;
  x: integer;
  IPAddress: string;
begin

  Initialise;

  WTSQuerySessionInformation(
    WTS_CURRENT_SERVER_HANDLE,
    WTS_CURRENT_SESSION,
    WTSClientAddress,
    ReturnPointer,
    ByteCount);

  ClientAddress := WTS_CLIENT_ADDRESS(ReturnPointer^);
  IPAddress := '';

  if ClientAddress.AddressFamily = AF_INET then begin
    for x:= 2 to 5 do begin
      IPAddress := IPAddress + IntToStr(ClientAddress.Address[x]);
      if x < 5 then IPAddress := IPAddress + '.';
    end;
  end;
  Result := IPAddress;

  WTSFreeMemory (ReturnPointer);
end;

function tTerminalService.ClientSessionId : ULONG;
var
  ByteCount: cardinal;
  ReturnPointer: pointer;
begin

  Initialise;

  WTSQuerySessionInformation(
    WTS_CURRENT_SERVER_HANDLE,
    WTS_CURRENT_SESSION,
    WTSSessionId,
    ReturnPointer,
    ByteCount);

  Result := ULONG (ReturnPointer^);

  WTSFreeMemory (ReturnPointer);
end;

function tTerminalService.ClientName: string;
var
  ReturnPointer: pointer;
  ByteCount: cardinal;
begin
  Initialise;

  WTSQuerySessionInformation(
    WTS_CURRENT_SERVER_HANDLE,
    WTS_CURRENT_SESSION,
    WTSClientName,
    ReturnPointer,
    ByteCount);

  Result := StrPas (PChar (ReturnPointer));;

  WTSFreeMemory (ReturnPointer);
end;

function tTerminalService.GetConnectStatus : TWtsConnectStateClass;
var
  ReturnPointer: pointer;
  ByteCount: cardinal;
begin
  Initialise;

  if (WTSQuerySessionInformation(
    WTS_CURRENT_SERVER_HANDLE,
    WTS_CURRENT_SESSION,
    WTSConnectState,
    ReturnPointer,
    ByteCount)) then begin

    Result := TWtsConnectStateClass (ReturnPointer^);

    WTSFreeMemory (ReturnPointer);
  end else begin
    Result := WTSDown;
  end;
end;

constructor tTerminalService.Create;
begin
  WTSAPI32 := LoadLibrary('WTSAPI32.DLL');
end;

destructor tTerminalService.Destroy;
begin
  if WTSAPI32 <> 0 then
    FreeLibrary(WTSAPI32);
  inherited;
end;

procedure tTerminalService.GetEntryPoints;
begin
  {$ifdef UNICODE}
    WTSQuerySessionInformation := GetProcAddress(WTSAPI32, pchar('WTSQuerySessionInformationW'));
  {$else}
    WTSQuerySessionInformation := GetProcAddress(WTSAPI32, pchar('WTSQuerySessionInformationA'));
  {$endif}
  if not assigned( WTSQuerySessionInformation ) then
    raise Exception.Create('Could not load function WTSQuerySessionInformation');

  WTSFreeMemory := GetProcAddress(WTSAPI32, pchar('WTSFreeMemory'));
  if not assigned( WTSFreeMemory ) then
    raise Exception.Create('Could not load function WTSFreeMemory');
end;

procedure tTerminalService.Initialise;
begin
  if Initialised then exit;
  GetEntryPoints;
  Initialised := True;
end;

function tTerminalService.IsAPIAvailable: boolean;
begin
  Result := WTSAPI32 <> 0;
end;

class function tTerminalService.IsRemoteSession: boolean;
Const sm_RemoteSession = $1000; { from WinUser.h }
begin
  Result := GetSystemMetrics(SM_RemoteSession) <> 0;
end;

const
  MetaFrameWinFarmObject = 1;
  MetaFrameWinAppObject  = 3;

function CitrixClientName : String;
var
  (*
  i       : Integer;
  objapp  : Variant;
  *)
  objfarm : Variant;
begin
  Result := '';

  try
    objfarm := CreateOleObject ('MetaFrameCOM.MetaFrameFarm');

    Result := Result + '1';

    objfarm.Initialize (MetaFrameWinAppObject);
    Result := Result + '2';

    Result := Result + ';' + objfarm.ClientName;

    Result := Result + '4';

    (*
    For i:=0 to objfarm.Sessions - 1 do begin
      Result := Result + '3';
      Result := Result + ';' + objfarm.Session [i].ClientName;
    end;
    *)
  except
    Result := Result + '9';
  end;
end;

end.
