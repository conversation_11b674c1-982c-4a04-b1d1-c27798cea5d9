unit FrontendImageModule;

{$i compilers.inc}

interface

uses
  SysUtils, Classes, Controls,

  {$IFDEF VER360}
    System.ImageList, Vcl.ImgList
  {$ELSE}
    {$IFDEF VER350}
      System.ImageList, Vcl.ImgList
    {$else}
      ImgList
    {$endif}
  {$endif}
  ;

type
  TImageModule = class(TDataModule)
    FrontendImageList: TImageList;
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

var
  ImageModule: TImageModule;

implementation

{$R *.dfm}

{$ifndef DELPHIXE10_UP}
  uses
    unCustomImageDrawHook;
{$endif}

end.
