object InventurLagerplatzForm: TInventurLagerplatzForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Inventur eines Lager/Komm-Platzes'
  ClientHeight = 265
  ClientWidth = 428
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    428
    265)
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 48
    Width = 22
    Height = 13
    Caption = 'MHD'
  end
  object Label2: TLabel
    Left = 152
    Top = 48
    Width = 35
    Height = 13
    Caption = 'Charge'
  end
  object Label3: TLabel
    Left = 8
    Top = 96
    Width = 32
    Height = 13
    Caption = 'Menge'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 200
    Width = 412
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 248
    ExplicitWidth = 410
  end
  object Label4: TLabel
    Left = 8
    Top = 5
    Width = 49
    Height = 13
    Caption = 'Artikel-Nr.'
  end
  object Label5: TLabel
    Left = 152
    Top = 5
    Width = 56
    Height = 13
    Caption = 'Artikel-Text'
  end
  object Label6: TLabel
    Left = 71
    Top = 5
    Width = 18
    Height = 13
    Caption = 'VPE'
  end
  object Label7: TLabel
    Left = 152
    Top = 96
    Width = 38
    Height = 13
    Caption = 'Gewicht'
  end
  object Label8: TLabel
    Left = 280
    Top = 115
    Width = 11
    Height = 13
    Caption = 'kg'
  end
  object MHDEdit: TEdit
    Left = 8
    Top = 64
    Width = 121
    Height = 21
    TabOrder = 0
    Text = 'MHDEdit'
    OnChange = InputChange
    OnExit = MHDEditExit
    OnKeyPress = MHDEditKeyPress
  end
  object ChargeEdit: TEdit
    Left = 152
    Top = 64
    Width = 266
    Height = 21
    TabOrder = 1
    Text = 'ChargeEdit'
    OnChange = InputChange
  end
  object MengeEdit: TEdit
    Left = 8
    Top = 112
    Width = 97
    Height = 21
    TabOrder = 2
    Text = '0'
    OnChange = InputChange
  end
  object AssignButton: TButton
    Left = 248
    Top = 215
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    TabOrder = 3
    OnClick = AssignButtonClick
  end
  object AbortButton: TButton
    Left = 345
    Top = 215
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Beenden'
    ModalResult = 3
    TabOrder = 4
  end
  object StatusBar1: TStatusBar
    Left = 0
    Top = 246
    Width = 428
    Height = 19
    Panels = <
      item
        Width = 50
      end>
  end
  object ArtikelNrEdit: TEdit
    Left = 8
    Top = 21
    Width = 57
    Height = 21
    TabOrder = 6
    Text = 'ArtikelNrEdit'
  end
  object ArtikelTextEdit: TEdit
    Left = 152
    Top = 21
    Width = 266
    Height = 21
    Enabled = False
    TabOrder = 7
    Text = 'ArtikelTextEdit'
  end
  object VPEComboBox: TComboBox
    Left = 71
    Top = 21
    Width = 58
    Height = 21
    TabOrder = 8
    Text = 'VPEComboBox'
  end
  object FehlerLabel: TPanel
    Left = 8
    Top = 152
    Width = 412
    Height = 33
    Anchors = [akLeft, akRight, akBottom]
    BevelOuter = bvNone
    Caption = 'FehlerLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 9
  end
  object GewichtEdit: TEdit
    Left = 152
    Top = 112
    Width = 121
    Height = 21
    TabOrder = 10
    Text = 'GewichtEdit'
    OnChange = InputChange
    OnExit = GewichtEditExit
    OnKeyPress = GewichtEditKeyPress
  end
  object ClearButton: TButton
    Left = 8
    Top = 215
    Width = 75
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'L'#246'schen'
    TabOrder = 11
    OnClick = ClearButtonClick
  end
  object MengeUpDown: TIntegerUpDown
    Left = 105
    Top = 112
    Width = 16
    Height = 21
    Associate = MengeEdit
    TabOrder = 12
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 344
    Top = 96
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 392
    Top = 104
  end
end
