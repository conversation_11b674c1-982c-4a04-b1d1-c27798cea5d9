object ShowAuftragChangesForm: TShowAuftragChangesForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Auftrags'#228'nderungen'
  ClientHeight = 443
  ClientWidth = 839
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Splitter1: TSplitter
    Left = 0
    Top = 177
    Width = 839
    Height = 3
    Cursor = crVSplit
    Align = alTop
    ExplicitWidth = 225
  end
  object KopfPanel: TPanel
    Left = 0
    Top = 0
    Width = 839
    Height = 177
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      839
      177)
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 51
      Height = 13
      Caption = 'Auftragnr.'
    end
    object Label3: TLabel
      Left = 8
      Top = 24
      Width = 52
      Height = 13
      Caption = 'Empf'#228'nger'
    end
    object AuftragNrLabel: TLabel
      Left = 104
      Top = 8
      Width = 85
      Height = 13
      Caption = 'AuftragNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object WarenempfLabel: TLabel
      Left = 104
      Top = 24
      Width = 96
      Height = 13
      Caption = 'WarenempfLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object AufChangeDBGrid: TDBGridPro
      Left = 8
      Top = 56
      Width = 823
      Height = 114
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = AufChangeDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
      ReadOnly = True
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'Tahoma'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
      OnColumnSort = AuftPosChangesDBGridColumnSort
    end
  end
  object PosPanel: TPanel
    Left = 0
    Top = 180
    Width = 839
    Height = 222
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      839
      222)
    object Label2: TLabel
      Left = 8
      Top = 6
      Width = 143
      Height = 13
      Caption = #196'nderungen in den Positionen'
    end
    object AuftPosChangesDBGrid: TDBGridPro
      Left = 8
      Top = 26
      Width = 823
      Height = 186
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = AuftragPosDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
      ReadOnly = True
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'Tahoma'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
      OnColumnSort = AuftPosChangesDBGridColumnSort
    end
  end
  object FussPanel: TPanel
    Left = 0
    Top = 402
    Width = 839
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      839
      41)
    object CloseButton: TButton
      Left = 756
      Top = 8
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 0
    end
  end
  object AufPosChangesDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 464
    Top = 56
  end
  object AuftragPosDataSource: TDataSource
    DataSet = AufPosChangesDataSet
    Left = 512
    Top = 56
  end
  object AufChangeDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    IndexDefs = <>
    Left = 464
    Top = 16
  end
  object AufChangeDataSource: TDataSource
    DataSet = AufChangeDataSet
    Left = 512
    Top = 16
  end
end
