unit ZollAbwicklungDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, DB, ADODB, BetterADODataSet, Grids, DBGrids,
  SMDBGrid, DBGridPro, ExtCtrls;

type
  TZollAbwicklungForm = class(TForm)
    Panel1: TPanel;
    Panel3: TPanel;
    VerzollDBGrid: TDBGridPro;
    CloseButton: TButton;
    VerzollDataSource: TDataSource;
    VerzollDataSet: TBetterADODataSet;
    MandPanel: TPanel;
    Label2: TLabel;
    MandantComboBox: TComboBoxPro;
    SubMandPanel: TPanel;
    Label11: TLabel;
    SubMandComboBox: TComboBoxPro;
    Label1: TLabel;
    procedure MandantComboBoxChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure SubMandComboBoxChange(Sender: TObject);
  private
    procedure UpdateVerzollQuery;
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, FrontEndUtils, DBGridUtilModule, DatenModul, ConfigModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TZollAbwicklungForm.UpdateVerzollQuery;
begin
  VerzollDataSet.Close;

  VerzollDataSet.CommandText := 'select * from V_PCD_BESTAND_VERZOLLUNG where REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc)';
  VerzollDataSet.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

  if (GetComboBoxRef (MandantComboBox) > 0) then begin
    VerzollDataSet.CommandText := VerzollDataSet.CommandText + ' and REF_MAND=:ref_mand';
    VerzollDataSet.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandantComboBox);
  end;

  if (SubMandPanel.Visible and (GetComboBoxRef (SubMandComboBox) > 0)) then begin
    VerzollDataSet.CommandText := VerzollDataSet.CommandText + ' and REF_SUB_MAND=:ref_sub_mand';
    VerzollDataSet.Parameters.ParamByName('ref_sub_mand').Value := GetComboBoxRef (SubMandComboBox);
  end;

  VerzollDataSet.Open;

  DBGridUtils.SetGewichtDisplayFunctions(VerzollDBGrid.DataSource.DataSet, 'NETTO_GEWICHT');
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TZollAbwicklungForm.FormClose(Sender: TObject;
  var Action: TCloseAction);
begin
  VerzollDataSet.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TZollAbwicklungForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TZollAbwicklungForm.FormShow(Sender: TObject);
begin
  LoadMandantCombobox (MandantComboBox);

  if (LVSDatenModul.AktMandantRef > 0) then
    MandantComboBox.ItemIndex := FindComboboxRef(MandantComboBox, LVSDatenModul.AktMandantRef);

  MandantComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TZollAbwicklungForm.MandantComboBoxChange(Sender: TObject);
begin
  if (SubMandPanel.Visible) then begin
    ClearComboBoxObjects (SubMandComboBox);

    LoadSubMandantCombobox (SubMandComboBox, LVSDatenModul.AktLocationRef, GetComboBoxRef(MandantComboBox));

    if (SubMandComboBox.Items.Count = 0) then
      SubMandComboBox.Enabled := False
    else begin
      SubMandComboBox.Enabled := True;

      SubMandComboBox.Items.Insert (0, MandantComboBox.GetItemText(-1, 0)+'|'+MandantComboBox.GetItemText(-1, 1));
      SubMandComboBox.ItemIndex := -1;
    end;
  end;

  UpdateVerzollQuery;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TZollAbwicklungForm.SubMandComboBoxChange(Sender: TObject);
begin
  UpdateVerzollQuery;
end;

end.
