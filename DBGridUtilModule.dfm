object DBGridUtils: TDBGridUtils
  OnCreate = DataModuleCreate
  OnDestroy = DataModuleDestroy
  Height = 178
  Width = 304
  object GridPopupMenu: TPopupMenu
    AutoPopup = False
    Images = ImageModule.FrontendImageList
    OnPopup = GridPopupMenuPopup
    Left = 32
    Top = 16
    object DBGridAlleMarkierenMenuItem: TMenuItem
      Caption = 'Alle markieren'
      OnClick = DBGridAlleMarkierenMenuItemClick
    end
    object DBGridKeineMarkierenMenuItem: TMenuItem
      Caption = 'Markierung aufheben'
      OnClick = DBGridKeineMarkierenMenuItemClick
    end
    object N5: TMenuItem
      Caption = '-'
    end
    object DBGridMarkierteDatenExport: TMenuItem
      Caption = 'Markierte Daten exportieren...'
      ImageIndex = 12
      OnClick = DatenExportClick
    end
    object DBGridAlleDatenExport: TMenuItem
      Caption = 'Alle Daten exportieren...'
      ImageIndex = 12
      OnClick = DatenExportClick
    end
    object N7: TMenuItem
      Caption = '-'
    end
    object DBGridMarkierteDatenPrint: TMenuItem
      Caption = 'Markierte Daten drucken...'
      OnClick = DatenPrintClick
    end
    object DBGridAlleDatenPrint: TMenuItem
      Caption = 'Alle Daten drucken...'
      ImageIndex = 3
      OnClick = DatenPrintClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object DBGridLayoutMenuItem: TMenuItem
      Caption = 'Spalten Anordnung'
      object DBGridSaveLayoutMenuItem: TMenuItem
        Caption = 'Speichern...'
        OnClick = DBGridSaveLayoutMenuItemClick
      end
      object N9: TMenuItem
        Caption = '-'
      end
      object DBGridBaseLayoutMenuItem: TMenuItem
        Caption = 'Base'
        RadioItem = True
        object DBGridUseLayoutMenuItem: TMenuItem
          Caption = 'Anwenden'
          OnClick = DBGridUseLayoutMenuItemClick
        end
        object DBGridUpdateLayoutMenuItem: TMenuItem
          Caption = 'Aktualisieren'
          OnClick = DBGridUpdateLayoutMenuItemClick
        end
        object DBGridDelLayoutMenuItem: TMenuItem
          Caption = 'L'#246'schen'
          OnClick = DBGridDelLayoutMenuItemClick
        end
      end
    end
    object N10: TMenuItem
      Caption = '-'
    end
    object DBGridSpaltenAusblenden: TMenuItem
      Caption = 'Spalten ausblenden...'
      OnClick = DBGridSpaltenAusblendenClick
    end
    object DBGridOptimaleSpaltenbreite: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      ImageIndex = 27
      OnClick = DBGridOptimaleSpaltenbreiteClick
    end
    object DBGridOriginalReihenfolge: TMenuItem
      Caption = 'Original Reihenfolge'
      OnClick = DBGridOriginalReihenfolgeClick
    end
    object DBGridDefaultColumns: TMenuItem
      Caption = 'Default Spaltenauswahl'
      OnClick = DBGridDefaultColumnsClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object DBGridSortierungAufheben: TMenuItem
      Caption = 'Sortierung aufheben'
      OnClick = DBGridSortierungAufhebenClick
    end
    object N3: TMenuItem
      Caption = '-'
    end
    object Queryanzeigen1: TMenuItem
      Caption = 'Query anzeigen...'
      OnClick = Queryanzeigen1Click
    end
    object DBGridRecordCountMenuItem: TMenuItem
      Caption = 'Anzahl Datens'#228'tze:'
    end
  end
  object ColPopupMenu: TPopupMenu
    AutoPopup = False
    OnPopup = ColPopupMenuPopup
    Left = 32
    Top = 64
    object CopyColDatenPopupMenu: TMenuItem
      Caption = 'Zelleninhalt in die Zwischenablage kopieren'
      ImageIndex = 13
      ShortCut = 16451
      OnClick = DBGridColCopyMenu
    end
    object CopyRowDatenPopupMenu: TMenuItem
      Caption = 'Zeile in die Zwischenablage kopieren'
      OnClick = CopyRowDatenPopupMenuClick
    end
    object N8: TMenuItem
      Caption = '-'
    end
    object LoadSelectedMenuItem: TMenuItem
      Caption = 'Markierung aus Datei laden...'
      OnClick = LoadSelectedMenuItemClick
    end
    object N4: TMenuItem
      Caption = '-'
    end
    object ArtikelstammMenuItem: TMenuItem
      Caption = 'Artikelstamm..'
      OnClick = ArtikelstammMenuItemClick
    end
    object N6: TMenuItem
      Caption = '-'
    end
    object FilterColDatenPopUpMenu: TMenuItem
      Caption = 'Datens'#228'tze suchen oder filtern...'
      ImageIndex = 16
      ShortCut = 16454
      OnClick = DBGridSearchFilterMenu
    end
    object ResetFilterColDatenPopUpMenu: TMenuItem
      Caption = 'Datensatzfilter aufheben'
      ImageIndex = 17
      OnClick = DBGridResetFilterMenu
    end
    object RepeatFindenColDatenPopUpMenu: TMenuItem
      Caption = 'Weitersuchen'
      ImageIndex = 18
      ShortCut = 114
      OnClick = DBGridRepeatFindMenu
    end
  end
  object ExportSaveDialog: TSaveDialog
    Filter = 'CSV-Datei|*.csv'
    Options = [ofHideReadOnly, ofExtensionDifferent, ofEnableSizing]
    OnCanClose = ExportSaveDialogCanClose
    Left = 104
    Top = 72
  end
  object PrintDialog1: TPrintDialog
    Left = 104
    Top = 17
  end
  object PreviewPrinter1: TPreviewPrinter
    Orientation = poPortrait
    TextOptions.DrawStyle = dsStandard
    TextOptions.MarginLeft = 1.000000000000000000
    TextOptions.MarginTop = 1.000000000000000000
    TextOptions.MarginRight = 1.000000000000000000
    TextOptions.MarginBottom = 1.000000000000000000
    TextOptions.BodyFont.Charset = DEFAULT_CHARSET
    TextOptions.BodyFont.Color = clWindowText
    TextOptions.BodyFont.Height = -13
    TextOptions.BodyFont.Name = 'Arial'
    TextOptions.BodyFont.Style = []
    TextOptions.HeaderFont.Charset = DEFAULT_CHARSET
    TextOptions.HeaderFont.Color = clWindowText
    TextOptions.HeaderFont.Height = -24
    TextOptions.HeaderFont.Name = 'Times New Roman'
    TextOptions.HeaderFont.Style = [fsBold]
    TextOptions.FooterFont.Charset = DEFAULT_CHARSET
    TextOptions.FooterFont.Color = clWindowText
    TextOptions.FooterFont.Height = -13
    TextOptions.FooterFont.Name = 'Times New Roman'
    TextOptions.FooterFont.Style = [fsItalic]
    TextOptions.PageNumFont.Charset = DEFAULT_CHARSET
    TextOptions.PageNumFont.Color = clWindowText
    TextOptions.PageNumFont.Height = -13
    TextOptions.PageNumFont.Name = 'Times New Roman'
    TextOptions.PageNumFont.Style = [fsItalic]
    TextOptions.HeaderMargin = 0.500000000000000000
    TextOptions.FooterMargin = 0.750000000000000000
    TextOptions.HeaderAlign = taCenter
    TextOptions.FooterAlign = taCenter
    TextOptions.PrintPageNumber = pnBottom
    TextOptions.PageNumAlign = taRightJustify
    TextOptions.PageNumText = 'Page %d'
    Units = unInches
    ShowGrid = False
    ZoomOption = zoFitToPage
    ZoomVal = 100
    OnPreviewShow = PreviewPrinter1PreviewShow
    OnPreviewClose = PreviewPrinter1PreviewClose
    Left = 168
    Top = 41
  end
end
