unit TestPickAnzeigeDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, DB, ADODB, ExtCtrls, ComboBoxPro;

type
  TTestPickAnzeigeForm = class(TForm)
    DisplayComboBox: TComboBoxPro;
    AnzeigeAusButton: TButton;
    StationComboBox: TComboBoxPro;
    AnzeigeEinButton: TButton;
    AnzeigeBlinkButton: TButton;
    Label1: TLabel;
    Label2: TLabel;
    Bevel1: TBevel;
    CloseButton: TButton;
    Label25: TLabel;
    PickByLightColorComboBox: TComboBox;
    Bevel2: TBevel;
    RundlaufButton: TButton;
    Timer1: TTimer;
    procedure AnzeigeAusButtonClick(Sender: TObject);
    procedure AnzeigeEinButtonClick(Sender: TObject);
    procedure <PERSON>ze<PERSON><PERSON>link<PERSON>uttonClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure StationComboBoxChange(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure RundlaufButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure Timer1Timer(Sender: TObject);
    procedure PickByLightColorComboBoxChange(Sender: TObject);
    procedure DisplayComboBoxChange(Sender: TObject);
  private
    fStation : String;
    fColor   : Integer;

    fFachAnzahl     : Integer;
    fRundLaufNummer : Integer;
    fRundLauf       : Boolean;
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DatenModul, LVSPickAnzeige, LVSDatenInterface, ConfigModul, FrontendUtils;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTestPickAnzeigeForm.AnzeigeAusButtonClick(Sender: TObject);
begin
  SetPickAnzeigeAus (StationComboBox.GetItemText, PickByLightColorComboBox.ItemIndex + 1, GetComboBoxRef (DisplayComboBox))
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTestPickAnzeigeForm.AnzeigeEinButtonClick(Sender: TObject);
begin
  SetPickAnzeigeEin (StationComboBox.GetItemText, PickByLightColorComboBox.ItemIndex + 1, GetComboBoxRef (DisplayComboBox))
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTestPickAnzeigeForm.DisplayComboBoxChange(Sender: TObject);
begin
  AnzeigeAusButton.Enabled := False;
  AnzeigeEinButton.Enabled := False;
  AnzeigeBlinkButton.Enabled := False;

  if (GetComboBoxRef (DisplayComboBox) <> -1) then begin
    AnzeigeAusButton.Enabled   := True;

    AnzeigeEinButton.Enabled   := (PickByLightColorComboBox.ItemIndex >= 0);
    AnzeigeBlinkButton.Enabled := AnzeigeEinButton.Enabled;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTestPickAnzeigeForm.AnzeigeBlinkButtonClick(Sender: TObject);
begin
  SetPickAnzeigeBlink (StationComboBox.GetItemText, PickByLightColorComboBox.ItemIndex + 1, GetComboBoxRef (DisplayComboBox))
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTestPickAnzeigeForm.RundlaufButtonClick(Sender: TObject);
begin
  if (fRundLauf) then begin
    fRundLauf := False;

    StationComboBox.Enabled    := True;
    DisplayComboBox.Enabled    := True;
    AnzeigeAusButton.Enabled   := True;
    AnzeigeEinButton.Enabled   := True;
    AnzeigeBlinkButton.Enabled := True;

    SetPickAnzeigeAus (StationComboBox.GetItemText, -1, -1);

    RundlaufButton.Caption := 'Rundlauftest starten';
  end else begin
    SetPickAnzeigeAus (StationComboBox.GetItemText, -1, -1);

    fRundLaufNummer := GetComboBoxRef (DisplayComboBox, 1);
    fRundLauf := True;

    RundlaufButton.Caption := 'Rundlauftest stoppen';

    StationComboBox.Enabled    := False;
    DisplayComboBox.Enabled    := False;
    AnzeigeAusButton.Enabled   := False;
    AnzeigeEinButton.Enabled   := False;
    AnzeigeBlinkButton.Enabled := False;

    SetPickAnzeigeEin (StationComboBox.GetItemText, PickByLightColorComboBox.ItemIndex + 1, fRundLaufNummer);
    DisplayComboBox.ItemIndex := FindComboboxRef (DisplayComboBox, fRundLaufNummer);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTestPickAnzeigeForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  Screen.Cursor := crDefault;

  ClearComboBoxObjects (DisplayComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTestPickAnzeigeForm.FormCreate(Sender: TObject);
begin
  fRundLauf := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTestPickAnzeigeForm.FormShow(Sender: TObject);
var
  idx,
  selidx  : Integer;
  strwert : String;
  query   : TADOQuery;
begin
  ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'PICK_TO_LIGHT_STATION', fStation);

  if (ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'PICK_TO_LIGHT_COLOR', strwert) <> 0) then
    fColor := -1
  else if (Length (strwert) = 0) then
    fColor := -1
  else if not TryStrToInt (strwert, fColor) then
    fColor := -1;

  if (fColor > 0) and (fColor < PickByLightColorComboBox.Items.Count) then
    PickByLightColorComboBox.ItemIndex := fColor - 1
  else
    PickByLightColorComboBox.ItemIndex := -1;

  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select REF,NAME,BEZEICHNUNG from V_PICKANZEIGE_ZONE where REF_LAGER='+IntToStr (LVSDatenModul.AktLagerRef)+' order by NAME');

    query.Open;

    selidx := -1;

    while not (query.Eof) do begin
      idx := StationComboBox.AddItemIndex (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

      if (query.Fields [1].AsString = fStation) then
        selidx := idx;

      query.Next;
    end;

    query.Close;
  finally
    query.Free;
  end;

  if (selidx = -1) then
    StationComboBox.ItemIndex := 0
  else
    StationComboBox.ItemIndex := selidx;

  StationComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTestPickAnzeigeForm.PickByLightColorComboBoxChange(Sender: TObject);
begin
  if (fRundLauf) then begin
    SetPickAnzeigeAus (StationComboBox.GetItemText, -1, -1);

    SetPickAnzeigeEin (StationComboBox.GetItemText, PickByLightColorComboBox.ItemIndex + 1, fRundLaufNummer);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTestPickAnzeigeForm.StationComboBoxChange(Sender: TObject);
var
  query : TADOQuery;
begin
  ClearComboBoxObjects (DisplayComboBox);

  if (GetComboBoxRef (StationComboBox) = -1) then begin
    DisplayComboBox.Enabled := False;
    RundlaufButton.Enabled := False;
  end else begin
    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select FACH_ANZAHL from V_PICKANZEIGE_ZONE where REF='+GetComboBoxRefStr (StationComboBox));

      query.Open;
      fFachAnzahl := query.Fields [0].AsInteger;
      query.Close;

      query.SQL.Clear;
      query.SQL.Add ('select ANZEIGE, FACH_NR from V_PICKANZEIGE where REF_ZONE='+GetComboBoxRefStr (StationComboBox) + ' order by FACH_NR');

      query.Open;

      while not (query.Eof) do begin
        DisplayComboBox.AddItem (query.Fields [0].AsString+'|Fach '+query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [1].AsInteger));

        query.Next;
      end;

      query.Close;
    finally
      query.Free;
    end;

    if (DisplayComboBox.Items.Count = 0) then begin
      DisplayComboBox.Enabled := False;
      RundlaufButton.Enabled := False;
    end else begin
      DisplayComboBox.Enabled := True;
      RundlaufButton.Enabled := True;

      DisplayComboBox.ItemIndex := 0;
    end;
  end;

  DisplayComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TTestPickAnzeigeForm.Timer1Timer(Sender: TObject);
begin
  if (fRundLauf) then begin
    SetPickAnzeigeAus (StationComboBox.GetItemText, PickByLightColorComboBox.ItemIndex + 1, fRundLaufNummer);

    if (DisplayComboBox.ItemIndex < DisplayComboBox.Items.Count) then
      fRundLaufNummer := GetComboBoxRef (DisplayComboBox, DisplayComboBox.ItemIndex + 1)
    else
      fRundLaufNummer := GetComboBoxRef (DisplayComboBox, 1);

    SetPickAnzeigeEin (StationComboBox.GetItemText, PickByLightColorComboBox.ItemIndex + 1, fRundLaufNummer);
    DisplayComboBox.ItemIndex := FindComboboxRef (DisplayComboBox, fRundLaufNummer);
  end;
end;

end.
