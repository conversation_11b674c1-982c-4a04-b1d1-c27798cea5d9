﻿//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : LVSDatenInterface
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/XE11/LVSIFCInterface.pas $
// $Revision: 66 $
// $Modtime: 17.01.24 9:19 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Datenbankinterface für die Bestandsfunktionen
//*****************************************************************************
unit LVSIFCInterface;

interface

uses Classes, DB, ADODB;

type
  TUnitOfMeasure = (uomNone, uomDay, uomMonth, uomYear);

  TArtikel = record
    RefAr         : Integer;

    Valid : Integer;
    SubMandant : String;
    SubMandantID  : String;
    ArtikelNr : String;
    ArtikelSetNr : String;
    ArtikelGruppe : String;
    ArtikelGruppeNr : Integer;
    BasisEinheit : String;
    StammEinheit : String;
    Hersteller : String;
    HerstellerArtikelNr : String;
    MandantArtikelNr : String;
    HerkunftsLand : String;
    Brand : String;
    Sortiment: String;
    LieferanArtikelNr : String;
    ZusatzNr : String;
    VersandArt : String;
    VersandSpedition : String;
    OptMHD : String;
    OptMHDNew : String;     //Nur für neue Artikel
    OptCharge : String;
    OptChargeAuto : String;
    OptChargeUnique : String;
    OptChargeNew : String;  //Nur für neue Artikel
    OptSerial : String;
    OptAutoWABuchen : String;
    OptListung : String;
    OptChargeSumme : String;
    OptNoBestand : Integer;
    RLZWE : Integer;
    RLZWA : Integer;
    RLZKOM : Integer;
    RLZProd : Integer;
    RLZUoM : TUnitOfMeasure;
    PicturPath : String;
    TaricNr : String;
    GradPlato : Integer;
    Klasse : String;
    ABCKlasse : String;
  end;

  TArtikelText = record
    Valid : Integer;
    Sprache     : String;
    ArtikelText : String;
    ArtikelTextLang : String;
    ArtikelInfo : String;
    ArtikelHint : String;
    ArtikelReturn : String;
    ArtikelVerpack  : String;
    ArtikelVersand  : String;
    ArtikelKomm     : String;
  end;

  TArtikelSetPos = record
    Valid     : Integer;
    ArtikelNr : String;
    Menge     : Integer;
  end;

  TArtikelEinheit = record
    Valid : Integer;
    IsColli : Boolean;
    OptMaster : String;
    OptSperrgut : String;
    OptWE : String;
    OptGewicht : String;
    OptCutting : String;
    Einheit : String;
    InhaltEinheit : String;
    VerpackEinheit : String;
    VariantID : String;
    EAN : String;
    EANInhalt : String;
    BarcodeInhalt : String;
    InhaltMenge : Integer;
    EANPalette : String;
    Barcode : String;
    ColliName : String;
    LTEDICode : String;
    LTType : String;
    PalFaktor : Integer;
    LagenFaktor : Integer;
    AnzahlCollis : Integer;
    GewichtFaktor : Integer;
    NettoGewicht : Double;
    BruttoGewicht : Double;
    InhaltNettoGewicht : Double;
    InhaltBruttoGewicht : Double;
    TaraGewicht : Integer;
    PalHeight : Integer;
    Filling : Integer;
    AbmessungFaktor : Integer;
    L,B,H : Integer;
    Inhalt_L,Inhalt_B,Inhalt_H : Integer;
    Size : String;
    Color : String;
    UnitEinheit : Integer;
    UnitNorm : Integer;
    UnitNetto : Integer;
    LagerBereich : String;
    KommLP : String;
    KommLPFolgeNr : Integer;
    Preis : Integer;
    InhaltPreis : Integer;
    PreisEinheit : Integer;
    VersandArt : String;
    VersandSpedition : String;
    MinBestand : Integer;
    MinOrder : Integer;
    StapelFaktor : Integer;
    KommFolge : Integer;
    PALStapelFaktor : Integer;
    PALKommFolge : Integer;
    PALNettoGewicht : Integer;
    PALBruttoGewicht : Integer;
    VollPalEinheit : String;
    PalPickOption : String;
    GebindeEinheit : String;
    GebindePreis : Integer;
    Gebinde_L,Gebinde_B,Gebinde_H : Integer;
    EANGebinde : String;
    BarcodeGebinde : String;
    GebindeInhaltMenge : Integer;
    GebindeNettoGewicht : Double;
    VerpackungArt : String;
    VerpackungForm : String;
    StabelFolge : integer;
    SetArtikelNr : String;
    SetMenge : Integer;
  end;

  TAdresse = record
    Adressart : String;
    Valid : Char;
    Nr : String;
    Pruefziffer : Integer;
    Anrede : String;
    Vorname : String;
    Nachname : String;
    Firma : String;
    Name1 : String;
    Name2 : String;
    NameZusatz : String;
    Strasse : String;
    HausNr : String;
    Strasse_2 : String;
    Land : String;
    Plz : String;
    Ort : String;
    FilialNr : String;
    ILN : String;
    Ansprechpartner : String;
    Telefon : String;
    Fax : String;
    Email : String;
    ColliAdr : String;
    UstID : String;
    EORI : String;
  end;

  TAuftragPos = record
    Status : Integer;
    PosNr : Integer;
    Referenz : String;
    AuftragNr : String;
    ArtikelNr : String;
    VariantID : String;
    EAN : String;
    PosWaehrung : String;
    VPE : String;
    SollMenge : Integer;
    SollGewicht : Integer;
    MHDMin : String;
    MHDMuss : String;
    Charge : String;
    VkPreis : Integer;
    BlPreis : Integer;
    Variante : String;
    HUNr : String;
    NVENr : String;
    OutHUNr : String;
    OutNVENr : String;
    BundleNr : String;
    StockLocation : String;
    KundenOption : String;
    ArtikelArt : Integer;
    PosArtikelText : String;
    KommHinweis : String;
    MandantArtikelNr : String;
    BestellArtikelNr : String;
    KundenArtikelNr : String;
    CrossdockBestellNr : String;
    InhaltsStoffe : String;
    Kalibrierung : String;
    NettoBetrag : Integer;
    PreisEinheit : Integer;
    MWSTSatz : Integer;
    EKVerzollung : Boolean;
    Category : String;

    Artikel        : TArtikel;
    ArtikelEinheit : TArtikelEinheit;
    ArtikelText    : TArtikelText;
  end;

  TAuftragKopf = record
    RecordID : String;
    Status : Integer;
    Quelle : String;
    CostCenter : String;
    Mandant : String;
    SubMandant : String;
    SubMandantID : String;
    Trader : String;
    KundenNr : String;
    SammelNr : String;
    LieferantenNr : String;
    Auftragsart : String;
    BuildAuftragNr : Boolean;
    AuftragNr : String;
    AuftragNrListe : String;
    CreateAuftragNr : Boolean;
    IFCAuftragNr : String;
    AuftragReferenz : String;
    AuftragProcessNr : String;
    Prio : Integer;
    AuftragDatum : String;
    LieferDatum : String;
    KommDatum : String;
    BestellDatum : String;
    BestellNr : String;
    LieferNr : String;
    RechnungsNr : String;
    ZahlungsArt : String;
    AnlieferzeitVon : String;
    AnlieferzeitBis : String;
    Anlieferzeit2Von : String;
    Anlieferzeit2Bis : String;
    PackLager : String;
    AuslieferLager : String;
    KommNr : String;
    TourNr : String;
    SpeditionNr : String;
    Spedition : String;
    SpedProdukt : String;
    VersandArt : String;
    VersandHinweis: String;
    Ladungstraeger : String;
    Packmittel : String;
    KommArt : String;
    Druckart : String;
    Sprache : String;
    Waehrung : String;
    ZusatzPack : String;
    ZusatzLS : String;
    ZusatzVers : String;
    ZusatzKomm : String;
    LSKopfText : String;
    LSFussText : String;
    Options : String;
    DESADV : String;
    LogistikPlatform : String;
    Selbstabholer : String;
    OptNachnahme : Char;
    OptBarzahler : Char;
    OptSelbstabholer : Char;
    LSAnWare : String;
    WarenWert : Integer;
    NettoBetrag : Integer;
    BruttoBetrag : Integer;
    MWSTSatz : Integer;
    Instructions : String;
    FieldSales : String;
    OfficeSales : String;
    SupplierNo : String;
    OutboundLager : String;
    LieferBedingung : String;
    LieferHinweis : String;

    KundenAdr : TAdresse;
    LieferAdr : TAdresse;

    AnzAufPos : Integer;
    AufPos : array [0..1024] of TAuftragPos;
  end;

  TBestellPos = record
    Status : Integer;
    PosNr : Integer;
    Referenz : String;
    ArtikelNr : String;
    MandantArtikelNr : String;
    VPE : String;
    VariantID : String;
    EAN : String;
    SollMenge : Integer;
    SollGewicht : Integer;      //Netto-Gewicht in Gramm
    MHDMin : TDateTime;
    MHDMuss : TDateTime;
    Charge : String;
    Variante : String;
    NVENr : String;
    HUNr : String;
    ProjectId : String;
    WiegeId : String;
    AufReferenz : String;
    CostCenter : String;
    HerstellDatum : TDateTime;
    EingangDatum : TDateTime;
    PalHeight : Integer;            //Palettenhöhe in mm
    SperrOpt : String;
    SperrGrund : String;

    Artikel        : TArtikel;
    ArtikelEinheit : TArtikelEinheit;
  end;

  TBestellKopf = record
    RecordID : String;
    Status : Integer;
    RefMandant    : Integer;
    RefSubMandant : Integer;
    RefLager      : Integer;
    //Mandant : String;
    //SubMandant : String;
    //Lager : String;
    Art : String;
    LieferscheinNr : String;
    Lieferant : String;
    LieferantenNr : String;
    BestellDatum : String;
    LieferDatum : String;
    BestellNr : String;
    BestellReferenz : String;
    Hinweis : String;
    ProjectId : String;

    AnzBestPos : Integer;
    BestPos : array [0..1024] of TBestellPos;
  end;

  TRetAvisKopf = record
    RecordID : String;
    Status : Integer;
    Mandant : String;
    SubMandant : String;
    RetoureLager : String;
    KundenNr : String;
    AVISNummer : String;
    Auftragsart : String;
    AuftragNr : String;
    AuftragReferenz : String;
    AuftragDatum : String;
    LieferDatum : String;
    VersandDatum : String;
    BestellDatum : String;
    BestellNr : String;
    LieferNr : String;
    RechnungsNr : String;
    ZahlungsArt : String;
    AuslieferLager : String;
    KommNr : String;
    TourNr : String;
    SpeditionNr : String;
    Spedition : String;
    VersandArt : String;
    VersandHinweis: String;
    TrackinID: String;

    KundenAdr : TAdresse;
    LieferAdr : TAdresse;

    AnzAufPos : Integer;
    AufPos : array [0..1024] of TAuftragPos;
  end;

  TLieferAvisPos = record
    Status : Integer;
    PosNr : Integer;
    Referenz : String;
    BestellNr : String;
    BestellPosNr : integer;
    ArtikelNr : String;
    VPE : String;
    VariantID : String;
    EAN : String;
    HUNr : String;
    NVENr : String;
    Menge : Integer;
    Gewicht : Integer;
    MHD : String;
    Charge : String;
    Variante : String;

    Artikel        : TArtikel;
    ArtikelEinheit : TArtikelEinheit;
  end;

  TLieferAvisKopf = record
    Status : Integer;
    Mandant : String;
    SubMandant : String;
    Lager : String;
    AVISArt : String;
    AVISNummer : String;
    AVISDatum : String;
    LieferDatum : String;
    LieferNr : String;
    LieferentNr : String;
    Lieferent : String;
    SpeditionNr : String;
    Spedition : String;

    LieferAdr : TAdresse;

    AnzAVISPos : Integer;
    AVISPos : array [0..1024] of TLieferAvisPos;
  end;

  TWarenempf = record
    Mandant : String;
    SubMandant : String;
    KundenNr : String;
    Bezeichung : String;
    Sprache : String;
    Waehrung : String;
    AnlieferzeitVon : integer;
    AnlieferzeitBis : integer;
    LieferText : String;
    RechnungText : String;
    KommText : String;
    LieferKopien : integer;
    KommArt : String;
    ILN : String;
    LTTyp : String;
  end;

  TBestandPos = record
    SubMandant : String;
    EAN : String;
    ArtikelNr : String;
    MandantArtikelNr : String;
    Einheit : String;
    Menge : Integer;
    MengeCutting : Integer;
    SumMenge : Integer;
    NettoGewicht : Integer;
    VPEorNettoGewicht : Integer;
    SumNettoGewicht : Integer;
    MHD : TDateTime;
    HerstellDatum : TDateTime;
    AnnahmeDatum : TDateTime;
    Charge : String;
    LEName : String;
    LENr : String;
    LEFachPos : Integer;
    LTEDICode : String;
    LTType : String;
    NVENr : String;
    RefAR : Integer;
    RefVPE : Integer;
    WiegeID : String;
    HerstellTag : Integer;
    LPName : String;
    LPNr : String;
    HUNr : String;
    SerialNr : String;
    RefLP : Integer;
    RefLT : Integer;
    RefLE : Integer;
  end;

  TTracking = record
    AuftragNr   : String;
    Versender   : String;
    NVENr       : String;
    SendungsNr  : String;
    TrackingURL : String;
  end;

procedure ClearIFCDaten (var AuftragKopf : TAuftragKopf); overload;
procedure ClearIFCDaten (var AuftragPos : TAuftragPos); overload;
procedure ClearIFCDaten (var AuftragAdr : TAdresse; const SetDefault : boolean = False); overload;
procedure ClearIFCDaten (var Warenempf : TWarenempf; const SetDefault : boolean = False); overload;
procedure ClearIFCDaten (var BestellKopf : TBestellKopf); overload;
procedure ClearIFCDaten (var BestellPos : TBestellPos); overload;
procedure ClearIFCDaten (var BestandPos : TBestandPos; const SetDefault : boolean = False); overload;
procedure ClearDaten (var Artikel : TArtikel); overload;
procedure ClearDaten (var ArtikelText : TArtikelText); overload;
procedure ClearDaten (var ArtikelEinheit : TArtikelEinheit); overload;
procedure ClearDaten (var ArtikelSetPos : TArtikelSetPos); overload;
procedure ClearIFCDaten (var RetAvisKopf : TRetAvisKopf); overload;
procedure ClearIFCDaten (var Tracking : TTracking); overload;
procedure ClearIFCDaten (var LieferAvisKopf : TLieferAvisKopf); overload;
procedure ClearIFCDaten (var LieferAvisPos : TLieferAvisPos); overload;

function StartArtikelUpdate (const RefAR : Integer) : Integer;
function FinishArtikelUpdate (const RefAR : Integer) : Integer;

function ImportAuftragKopf   (const AuftragKopf : TAuftragKopf; var RefKopf : Integer) : Integer;
function ImportAuftragAdr    (const RefKopf : Integer; const AuftragAdr : TAdresse) : Integer;
function ImportAuftragPos    (const RefKopf : Integer; const AuftragPos : TAuftragPos; var RefPos : Integer) : Integer;

function SetAuftragLieferNr     (const RefKopf : Integer; const LieferNr : String) : Integer;
function SetAuftragVersandDaten (const RefKopf : Integer; const Art, Spedition, COD, Barzahler, Hinweis : String) : Integer;

function ImportBestellKopf (const BestKopf : TBestellKopf; var RefKopf : Integer) : Integer;
function ImportBestellPos  (const RefKopf : Integer; const BestPosNr : Integer; const BestellPos : TBestellPos; var RefPos : Integer) : Integer;

function ImportRetourenAvisKopf (const RetAvisKopf : TRetAvisKopf; var RefKopf : Integer) : Integer;
function ImportRetourenAvisAdr  (const RefKopf : Integer; const AuftragAdr : TAdresse) : Integer;
function ImportRetourenAvisPos  (const RefKopf : Integer; const AvisPosNr : Integer; const AuftragPos : TAuftragPos; var RefPos : Integer) : Integer;
function AcceptRetourenAvis     (const RefKopf : Integer) : Integer;

function ImportLieferAvisKopf (const LieferAvisKopf : TLieferAvisKopf; var RefKopf : Integer) : Integer;
function ImportLieferAvisPos  (const RefKopf : Integer; const AvisPosNr : Integer; const AvisPos : TLieferAvisPos; var RefPos : Integer) : Integer;

function ImportWarenempf     (const Warenempf : TWarenempf; var RefEmpf : Integer) : Integer;
function ImportWarenempfAdr  (const RefEmpf : Integer; const AuftragAdr : TAdresse) : Integer;

function ImportBestandPos (const RefInvRes : Integer; BestandPos : TBestandPos; var RefPos : Integer) : Integer;

function CreateSpedRoutingVersion   (const RefSped : Integer; var RefVer : Integer) : Integer;
function ActivateSpedRoutingVersion (const RefSped, RefVer : Integer) : Integer;
function ImportSpedRouting          (const RefVer, RefSped : Integer; const Land, PLZVon, PLZBis, Relation, FeinRelation, DepotNr, Gaytway : String; var RefRouting : Integer) : Integer;

function ImportOMSOrderKopf   (const AuftragKopf : TAuftragKopf; var RefOrder : Integer) : Integer;
function ImportOMSOrderAdr    (const RefOrder : Integer; const AuftragAdr : TAdresse) : Integer;
function ImportOMSOrderPos    (const RefOrder : Integer; const AuftragPos : TAuftragPos; var RefPos : Integer) : Integer;
function OMSOrderAccept       (const RefOrder : Integer) : Integer;
function AssigneOMSBranch     (const RefOrder, RefBranch : Integer; const Regel : String) : Integer;

implementation

uses
  SysUtils, DatenModul, Variants, LVSGlobalDaten, ResourceText;

procedure ClearDaten (var Artikel : TArtikel); overload;
begin
  Artikel.RefAr          := -1;

  Artikel.Valid := 0;
  Artikel.SubMandant := '';
  Artikel.SubMandantID  := '';
  Artikel.ArtikelNr := '';
  Artikel.ArtikelSetNr := '';
  Artikel.ArtikelGruppeNr := -1;
  Artikel.ArtikelGruppe := '~';
  Artikel.Hersteller := '';
  Artikel.Brand := '~';
  Artikel.HerstellerArtikelNr := '~';
  Artikel.MandantArtikelNr := '~';
  Artikel.HerkunftsLand := '~';
  Artikel.LieferanArtikelNr := '~';
  Artikel.ZusatzNr := '~';
  Artikel.Sortiment := '~';
  Artikel.VersandArt := '~';
  Artikel.VersandSpedition := '~';
  Artikel.OptMHD := '~';
  Artikel.OptMHDNew := '~';
  Artikel.OptCharge := '~';
  Artikel.OptChargeAuto := '~';
  Artikel.OptChargeUnique := '~';
  Artikel.OptChargeSumme := '~';
  Artikel.OptSerial := '';
  Artikel.OptAutoWABuchen := '0';
  Artikel.OptNoBestand := 0;
  Artikel.OptListung := '0';
  Artikel.RLZWE   := -1;
  Artikel.RLZWA   := -1;
  Artikel.RLZKOM  := -1;
  Artikel.RLZProd := -1;
  Artikel.RLZUoM  := uomNone;
  Artikel.PicturPath := '';
  Artikel.BasisEinheit := '';
  Artikel.StammEinheit := '';
  Artikel.TaricNr := '~';
  Artikel.GradPlato := -1;
  Artikel.Klasse := '~';
  Artikel.ABCKlasse := '~';
  Artikel.VersandArt := '~';
  Artikel.VersandSpedition := '~';
end;

procedure ClearDaten (var ArtikelText : TArtikelText); overload;
begin
  ArtikelText.Valid := 0;
  ArtikelText.Sprache         := '';
  ArtikelText.ArtikelText     := '~';
  ArtikelText.ArtikelTextLang := '~';
  ArtikelText.ArtikelInfo     := '~';
  ArtikelText.ArtikelHint     := '~';
  ArtikelText.ArtikelReturn   := '~';
  ArtikelText.ArtikelVerpack  := '~';
  ArtikelText.ArtikelVersand  := '~';
  ArtikelText.ArtikelKomm     := '~';
end;

procedure ClearDaten (var ArtikelEinheit : TArtikelEinheit); overload;
begin
  ArtikelEinheit.Valid := 0;
  ArtikelEinheit.IsColli := False;
  ArtikelEinheit.OptMaster := '';
  ArtikelEinheit.OptSperrgut := '';
  ArtikelEinheit.OptWE := '~';
  ArtikelEinheit.OptGewicht := '~';
  ArtikelEinheit.OptCutting := '~';
  ArtikelEinheit.Einheit := '';
  ArtikelEinheit.InhaltEinheit := '';
  ArtikelEinheit.VerpackEinheit := '';
  ArtikelEinheit.VariantID := '';
  ArtikelEinheit.EAN := '~';
  ArtikelEinheit.EANInhalt := '~';
  ArtikelEinheit.BarcodeInhalt := '~';
  ArtikelEinheit.EANPalette := '~';
  ArtikelEinheit.Barcode := '~';
  ArtikelEinheit.ColliName := '~';
  ArtikelEinheit.LTEDICode := '~';
  ArtikelEinheit.LTType := '~';
  ArtikelEinheit.NettoGewicht := -1;
  ArtikelEinheit.BruttoGewicht := -1;
  ArtikelEinheit.TaraGewicht := -1;
  ArtikelEinheit.InhaltNettoGewicht := -1;
  ArtikelEinheit.InhaltBruttoGewicht := -1;
  ArtikelEinheit.Filling := -1;
  ArtikelEinheit.PalHeight := -1;
  ArtikelEinheit.L := -1;
  ArtikelEinheit.B := -1;
  ArtikelEinheit.H := -1;
  ArtikelEinheit.Inhalt_L := -1;
  ArtikelEinheit.Inhalt_B := -1;
  ArtikelEinheit.Inhalt_H := -1;
  ArtikelEinheit.Size := '';
  ArtikelEinheit.Color := '';
  ArtikelEinheit.PalFaktor := -1;
  ArtikelEinheit.LagenFaktor := -1;
  ArtikelEinheit.AnzahlCollis := -1;
  ArtikelEinheit.UnitEinheit := -1;
  ArtikelEinheit.UnitNorm := -1;
  ArtikelEinheit.UnitNetto := -1;
  ArtikelEinheit.LagerBereich := '';
  ArtikelEinheit.KommLP := '';
  ArtikelEinheit.KommLPFolgeNr := -1;
  ArtikelEinheit.InhaltMenge := -1;
  ArtikelEinheit.Preis := -1;
  ArtikelEinheit.PreisEinheit := -1;
  ArtikelEinheit.VersandArt := '~';
  ArtikelEinheit.VersandSpedition := '~';
  ArtikelEinheit.MinBestand := -1;
  ArtikelEinheit.MinOrder := -1;
  ArtikelEinheit.StapelFaktor := -1;
  ArtikelEinheit.KommFolge := -1;
  ArtikelEinheit.PALStapelFaktor := -1;
  ArtikelEinheit.PALKommFolge := -1;
  ArtikelEinheit.PALNettoGewicht := -1;
  ArtikelEinheit.PALBruttoGewicht := -1;
  ArtikelEinheit.VollPalEinheit := '';
  ArtikelEinheit.PalPickOption := '';
  ArtikelEinheit.GebindeEinheit := '';
  ArtikelEinheit.GebindePreis := -1;
  ArtikelEinheit.Gebinde_L := -1;
  ArtikelEinheit.Gebinde_B := -1;
  ArtikelEinheit.Gebinde_H := -1;
  ArtikelEinheit.EANGebinde := '~';
  ArtikelEinheit.BarcodeGebinde := '~';
  ArtikelEinheit.GebindeInhaltMenge := -1;
  ArtikelEinheit.GebindeNettoGewicht := -1;
  ArtikelEinheit.VerpackungArt := '~';
  ArtikelEinheit.VerpackungForm := '~';
  ArtikelEinheit.VersandArt := '~';
  ArtikelEinheit.StabelFolge := -1;
  ArtikelEinheit.SetArtikelNr := '';
  ArtikelEinheit.SetMenge := -1;

  ArtikelEinheit.GewichtFaktor   := 1;
  ArtikelEinheit.AbmessungFaktor := 1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearDaten (var ArtikelSetPos : TArtikelSetPos);
begin
  ArtikelSetPos.Valid     := 0;;
  ArtikelSetPos.ArtikelNr := '';
  ArtikelSetPos.Menge     := -1;
end;
//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearIFCDaten (var AuftragKopf : TAuftragKopf);
begin
  AuftragKopf.RecordID := '';
  AuftragKopf.Status := -1;
  AuftragKopf.Quelle := '';
  AuftragKopf.CostCenter := '';
  AuftragKopf.Mandant := '';
  AuftragKopf.SubMandant := '';
  AuftragKopf.SubMandantID := '';
  AuftragKopf.Trader := '';
  AuftragKopf.KundenNr := '';
  AuftragKopf.SammelNr := '';
  AuftragKopf.LieferantenNr := '';
  AuftragKopf.Auftragsart := '';
  AuftragKopf.AuftragNr := '';
  AuftragKopf.AuftragNrListe := '';
  AuftragKopf.CreateAuftragNr := false;
  AuftragKopf.IFCAuftragNr := '';
  AuftragKopf.AuftragReferenz := '';
  AuftragKopf.AuftragProcessNr := '';
  AuftragKopf.Prio := -1;
  AuftragKopf.AuftragDatum := '';
  AuftragKopf.LieferDatum := '';
  AuftragKopf.KommDatum := '';
  AuftragKopf.BestellDatum := '';
  AuftragKopf.BestellNr := '';
  AuftragKopf.LieferNr := '';
  AuftragKopf.RechnungsNr := '';
  AuftragKopf.ZahlungsArt := '';
  AuftragKopf.AnlieferzeitVon := '';
  AuftragKopf.AnlieferzeitBis := '';
  AuftragKopf.Anlieferzeit2Von := '';
  AuftragKopf.Anlieferzeit2Bis := '';
  AuftragKopf.PackLager := '';
  AuftragKopf.AuslieferLager := '';
  AuftragKopf.KommNr := '';
  AuftragKopf.TourNr := '';
  AuftragKopf.SpeditionNr := '';
  AuftragKopf.Spedition := '';
  AuftragKopf.SpedProdukt := '';
  AuftragKopf.VersandArt := '';
  AuftragKopf.VersandHinweis := '';
  AuftragKopf.Ladungstraeger := '';
  AuftragKopf.Packmittel := '';
  AuftragKopf.KommArt := '';
  AuftragKopf.Druckart := '';
  AuftragKopf.Sprache := '';
  AuftragKopf.Waehrung := '';
  AuftragKopf.ZusatzPack := '';
  AuftragKopf.ZusatzLS := '';
  AuftragKopf.ZusatzVers := '';
  AuftragKopf.ZusatzKomm := '';
  AuftragKopf.LSKopfText := '';
  AuftragKopf.LSFussText := '';
  AuftragKopf.Options := '';
  AuftragKopf.DESADV := '';
  AuftragKopf.LogistikPlatform := '';
  AuftragKopf.Selbstabholer := '';
  AuftragKopf.OptNachnahme := #0;
  AuftragKopf.OptBarzahler := #0;
  AuftragKopf.OptSelbstabholer := #0;
  AuftragKopf.LSAnWare := '';
  AuftragKopf.WarenWert := -1;
  AuftragKopf.NettoBetrag := -1;
  AuftragKopf.BruttoBetrag := -1;
  AuftragKopf.MWSTSatz := -1;
  AuftragKopf.Instructions := '';
  AuftragKopf.FieldSales := '';
  AuftragKopf.OfficeSales := '';
  AuftragKopf.SupplierNo := '';
  AuftragKopf.OutboundLager := '';
  AuftragKopf.LieferBedingung := '';
  AuftragKopf.LieferHinweis := '';

  AuftragKopf.BuildAuftragNr := false;

  ClearIFCDaten (AuftragKopf.KundenAdr);
  ClearIFCDaten (AuftragKopf.LieferAdr);

  AuftragKopf.AnzAufPos := 0;
  ClearIFCDaten (AuftragKopf.AufPos [AuftragKopf.AnzAufPos]);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearIFCDaten (var AuftragPos : TAuftragPos);
begin
  AuftragPos.Status := -1;
  AuftragPos.PosNr := -1;
  AuftragPos.Referenz := '';
  AuftragPos.AuftragNr := '';
  AuftragPos.ArtikelNr := '';
  AuftragPos.VariantID := '';
  AuftragPos.EAN := '';
  AuftragPos.PosWaehrung := '';
  AuftragPos.VPE := '';
  AuftragPos.SollMenge := -1;
  AuftragPos.SollGewicht := -1;
  AuftragPos.MHDMin := '';
  AuftragPos.MHDMuss := '';
  AuftragPos.Charge := '';
  AuftragPos.VkPreis := -1;
  AuftragPos.BlPreis := -1;
  AuftragPos.Variante := '';
  AuftragPos.HUNr := '';
  AuftragPos.NVENr := '';
  AuftragPos.OutHUNr := '';
  AuftragPos.OutNVENr := '';
  AuftragPos.BundleNr := '';
  AuftragPos.KundenOption := '';
  AuftragPos.StockLocation := '';
  AuftragPos.ArtikelArt := -1;
  AuftragPos.PosArtikelText := '';
  AuftragPos.KommHinweis := '';
  AuftragPos.MandantArtikelNr := '';
  AuftragPos.BestellArtikelNr := '';
  AuftragPos.KundenArtikelNr := '';
  AuftragPos.CrossdockBestellNr := '';
  AuftragPos.InhaltsStoffe := '';
  AuftragPos.Kalibrierung := '';
  AuftragPos.NettoBetrag := -1;
  AuftragPos.PreisEinheit := -1;
  AuftragPos.MWSTSatz := -1;
  AuftragPos.EKVerzollung := false;
  AuftragPos.Category := '~';

  ClearDaten (AuftragPos.Artikel);
  ClearDaten (AuftragPos.ArtikelEinheit);
  ClearDaten (AuftragPos.ArtikelText);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearIFCDaten (var AuftragAdr : TAdresse; const SetDefault : boolean);
begin
   AuftragAdr.Adressart := '';
   AuftragAdr.Valid := #0;

   AuftragAdr.Firma    := '';
   AuftragAdr.Vorname  := '';
   AuftragAdr.Nachname := '';
   AuftragAdr.Name1    := '';
   AuftragAdr.HausNr   := '';

   if SetDefault then begin
     AuftragAdr.Nr := '~';
     AuftragAdr.Pruefziffer := -99;
     AuftragAdr.Anrede := '~';
     AuftragAdr.Name2 := '~';
     AuftragAdr.NameZusatz := '~';
     AuftragAdr.Strasse := '~';
     AuftragAdr.HausNr := '';
     AuftragAdr.Strasse_2 := '~';
     AuftragAdr.Land := '~';
     AuftragAdr.Plz := '~';
     AuftragAdr.Ort := '~';
     AuftragAdr.FilialNr := '~';
     AuftragAdr.ILN := '~';
     AuftragAdr.Ansprechpartner := '~';
     AuftragAdr.Telefon := '~';
     AuftragAdr.Fax := '~';
     AuftragAdr.Email := '~';
     AuftragAdr.ColliAdr := '~';
     AuftragAdr.UstID := '~';
     AuftragAdr.EORI := '~';
   end else begin
     AuftragAdr.Nr := '';
     AuftragAdr.Pruefziffer := -1;
     AuftragAdr.Anrede := '';
     AuftragAdr.Name2 := '';
     AuftragAdr.NameZusatz := '';
     AuftragAdr.Strasse := '';
     AuftragAdr.Strasse_2 := '';
     AuftragAdr.Land := '';
     AuftragAdr.Plz := '';
     AuftragAdr.Ort := '';
     AuftragAdr.FilialNr := '';
     AuftragAdr.ILN := '';
     AuftragAdr.Ansprechpartner := '';
     AuftragAdr.Telefon := '';
     AuftragAdr.Fax := '';
     AuftragAdr.Email := '';
     AuftragAdr.ColliAdr := '';
     AuftragAdr.UstID := '';
     AuftragAdr.EORI := '';
   end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearIFCDaten (var BestellKopf : TBestellKopf); overload;
begin
  BestellKopf.RecordID := '';
  BestellKopf.Status := -1;
  //BestellKopf.Mandant := '';
  //BestellKopf.SubMandant := '';
  //BestellKopf.Lager := '';
  BestellKopf.RefMandant := -1;
  BestellKopf.RefSubMandant := -1;
  BestellKopf.RefLager := -1;
  BestellKopf.Art := '';
  BestellKopf.Lieferant := '';
  BestellKopf.LieferscheinNr := '~';
  BestellKopf.LieferantenNr := '';
  BestellKopf.BestellDatum := '';
  BestellKopf.LieferDatum := '';
  BestellKopf.BestellNr := '';
  BestellKopf.BestellReferenz := '';
  BestellKopf.Hinweis := '~';
  BestellKopf.ProjectId := '';

  BestellKopf.AnzBestPos := 0;
  ClearIFCDaten (BestellKopf.BestPos [BestellKopf.AnzBestPos]);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearIFCDaten (var BestellPos : TBestellPos); overload;
begin
  BestellPos.Status := -1;
  BestellPos.PosNr := -1;
  BestellPos.Referenz := '';
  BestellPos.ArtikelNr := '';
  BestellPos.MandantArtikelNr := '';
  BestellPos.VPE := '';
  BestellPos.VariantID := '';
  BestellPos.EAN := '';
  BestellPos.SollMenge := -1;
  BestellPos.SollGewicht := -1;
  BestellPos.MHDMin := 0;
  BestellPos.MHDMuss := 0;
  BestellPos.Charge := '';
  BestellPos.Variante := '';
  BestellPos.NVENr := '';
  BestellPos.HUNr := '';
  BestellPos.ProjectId := '';
  BestellPos.WiegeId := '';
  BestellPos.AufReferenz := '';
  BestellPos.CostCenter := '';
  BestellPos.HerstellDatum := 0;
  BestellPos.EingangDatum := 0;
  BestellPos.PalHeight := -1;
  BestellPos.SperrOpt := '';
  BestellPos.SperrGrund := '';

  ClearDaten (BestellPos.Artikel);
  ClearDaten (BestellPos.ArtikelEinheit);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearIFCDaten (var Warenempf : TWarenempf; const SetDefault : boolean); overload;
begin
   Warenempf.Mandant    := '';
   Warenempf.SubMandant := '';
   Warenempf.KundenNr   := '';
   Warenempf.Bezeichung := '';

   if (SetDefault) then begin
     Warenempf.Sprache := '~';
     Warenempf.Waehrung := '~';
     Warenempf.AnlieferzeitVon := -99;
     Warenempf.AnlieferzeitBis := -99;
     Warenempf.LieferText := '~';
     Warenempf.RechnungText := '~';
     Warenempf.KommText := '~';
     Warenempf.LieferKopien := -99;
     Warenempf.KommArt := '~';
     Warenempf.ILN := '~';
     Warenempf.LTTyp := '~';
   end else begin
     Warenempf.Sprache := '';
     Warenempf.Waehrung := '';
     Warenempf.AnlieferzeitVon := -1;
     Warenempf.AnlieferzeitBis := -1;
     Warenempf.LieferText := '';
     Warenempf.RechnungText := '';
     Warenempf.KommText := '';
     Warenempf.LieferKopien := -1;
     Warenempf.KommArt := '';
     Warenempf.ILN := '';
     Warenempf.LTTyp := '';
   end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearIFCDaten (var BestandPos : TBestandPos; const SetDefault : boolean = False);
begin
  BestandPos.SubMandant := '';
  BestandPos.EAN := '';
  BestandPos.ArtikelNr := '';
  BestandPos.MandantArtikelNr := '';
  BestandPos.Einheit := '';
  BestandPos.Menge := -1;
  BestandPos.MengeCutting := -1;
  BestandPos.NettoGewicht := -1;
  BestandPos.VPEorNettoGewicht := -1;
  BestandPos.MHD := 0;
  BestandPos.Charge := '';
  BestandPos.HerstellDatum := 0;
  BestandPos.AnnahmeDatum := 0;
  BestandPos.LTEDICode := '';
  BestandPos.LTType := '';
  BestandPos.LEName := '';
  BestandPos.LENr := '';
  BestandPos.LEFachPos := -1;
  BestandPos.NVENr := '';
  BestandPos.WiegeID := '';
  BestandPos.SumMenge := -1;
  BestandPos.LPName := '';
  BestandPos.LPNr := '';
  BestandPos.HUNr := '';
  BestandPos.SerialNr := '';
  BestandPos.RefLP := -1;
  BestandPos.RefLE := -1;
  BestandPos.RefLT := -1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearIFCDaten (var RetAvisKopf : TRetAvisKopf); overload;
begin
  RetAvisKopf.RecordID := '';
  RetAvisKopf.Status := -1;
  RetAvisKopf.Mandant := '';
  RetAvisKopf.SubMandant := '';
  RetAvisKopf.RetoureLager := '';
  RetAvisKopf.KundenNr := '';
  RetAvisKopf.AVISNummer := '';
  RetAvisKopf.Auftragsart := '';
  RetAvisKopf.AuftragNr := '';
  RetAvisKopf.AuftragReferenz := '';
  RetAvisKopf.AuftragDatum := '';
  RetAvisKopf.LieferDatum := '';
  RetAvisKopf.VersandDatum := '';
  RetAvisKopf.BestellDatum := '';
  RetAvisKopf.BestellNr := '';
  RetAvisKopf.LieferNr := '';
  RetAvisKopf.RechnungsNr := '';
  RetAvisKopf.ZahlungsArt := '';
  RetAvisKopf.AuslieferLager := '';
  RetAvisKopf.KommNr := '';
  RetAvisKopf.SpeditionNr := '';
  RetAvisKopf.Spedition := '';
  RetAvisKopf.VersandArt := '';
  RetAvisKopf.VersandHinweis:= '';
  RetAvisKopf.TrackinID := '';

  ClearIFCDaten (RetAvisKopf.LieferAdr);

  RetAvisKopf.AnzAufPos := 0;
  ClearIFCDaten (RetAvisKopf.AufPos [RetAvisKopf.AnzAufPos]);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearIFCDaten (var Tracking : TTracking);
begin
  Tracking.AuftragNr   := '';
  Tracking.Versender   := '';
  Tracking.NVENr       := '';
  Tracking.SendungsNr  := '';
  Tracking.TrackingURL := '';
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.03.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearIFCDaten (var LieferAvisKopf : TLieferAvisKopf); overload;
begin
  LieferAvisKopf.Status := -1;
  LieferAvisKopf.Mandant := '';
  LieferAvisKopf.SubMandant := '';
  LieferAvisKopf.Lager := '';
  LieferAvisKopf.AVISArt := '';
  LieferAvisKopf.AVISNummer := '';
  LieferAvisKopf.AVISDatum := '';
  LieferAvisKopf.LieferDatum := '';
  LieferAvisKopf.LieferNr := '';
  LieferAvisKopf.LieferentNr := '';
  LieferAvisKopf.Lieferent := '';
  LieferAvisKopf.SpeditionNr := '';
  LieferAvisKopf.Spedition := '';

  ClearIFCDaten (LieferAvisKopf.LieferAdr);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.03.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearIFCDaten (var LieferAvisPos : TLieferAvisPos); overload;
begin
  LieferAvisPos.Status := -1;
  LieferAvisPos.PosNr := -1;
  LieferAvisPos.Referenz := '';
  LieferAvisPos.BestellNr := '';
  LieferAvisPos.BestellPosNr := -1;
  LieferAvisPos.ArtikelNr := '';
  LieferAvisPos.VPE := '';
  LieferAvisPos.VariantID := '';
  LieferAvisPos.EAN := '';
  LieferAvisPos.HUNr := '';
  LieferAvisPos.NVENr := '';
  LieferAvisPos.Menge := -1;
  LieferAvisPos.Gewicht := -1;
  LieferAvisPos.MHD := '';
  LieferAvisPos.Charge := '';
  LieferAvisPos.Variante := '';

  (*
  ClearIFCDaten (LieferAvisPos.Artikel);
  ClearIFCDaten (LieferAvisPos.ArtikelEinheit);
  *)
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function StartArtikelUpdate (const RefAR : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_ARTIKEL.START_UPDATE_ARTIKEL';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefAR);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FinishArtikelUpdate (const RefAR : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_ARTIKEL.FINISH_UPDATE_ARTIKEL';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefAR);
    Parameters.CreateParameter('pCompleteFlag',ftString,pdInput, 1, '1');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportAuftragKopf (const AuftragKopf : TAuftragKopf; var RefKopf : Integer) : Integer;
var
  dbres   : Integer;
  reflif  : Integer;
  StoredProcedure : TADOStoredProc;
begin
  reflif := -1;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LIEFERUNG.INSERT_LIEFERUNG';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pMandant',ftString,pdInput, 32, AuftragKopf.Mandant);
    Parameters.CreateParameter('pSubMandant',ftString,pdInput, 32, AuftragKopf.SubMandant);
    Parameters.CreateParameter('pKundennr',ftString,pdInput, 32, copy (AuftragKopf.KundenNr, 1, 32));
    Parameters.CreateParameter('pVersandDate',ftString,pdInput, 32, AuftragKopf.KommDatum);
    Parameters.CreateParameter('pLieferDate',ftString,pdInput, 32, AuftragKopf.LieferDatum);
    Parameters.CreateParameter('pLagerKomm',ftString,pdInput, 32, AuftragKopf.PackLager);
    Parameters.CreateParameter('pLagerLief',ftString,pdInput, 32, AuftragKopf.AuslieferLager);
    Parameters.CreateParameter('pTourNr',ftString,pdInput, 32, AuftragKopf.TourNr);

    Parameters.CreateParameter('oSeqNr',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    reflif   := StoredProcedure.Parameters.ParamValues ['oSeqNr'];
  end;

  StoredProcedure.Free;


  if (dbres = 0) then begin
    StoredProcedure := TADOStoredProc.Create (Nil);

    with StoredProcedure do begin
      ProcedureName := 'PA_AUFTRAG.INSERT_AUFTRAG';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

      Parameters.CreateParameter('pSender',ftString,pdInput, 32, Copy ('PCD:'+LVSDatenModul.AktUser, 1, 32));
      Parameters.CreateParameter('pLiefRef',ftInteger,pdInput, 12, reflif);
      Parameters.CreateParameter('pMandant',ftString,pdInput, 32, AuftragKopf.Mandant);
      Parameters.CreateParameter('pSubMandant',ftString,pdInput, 32, AuftragKopf.SubMandant);
      Parameters.CreateParameter('pKundennr',ftString,pdInput, 32, copy (AuftragKopf.KundenNr, 1, 32));
      Parameters.CreateParameter('pLagerPack',ftString,pdInput, 32, AuftragKopf.PackLager);
      Parameters.CreateParameter('pSprache',ftString,pdInput, 5, AuftragKopf.Sprache);
      Parameters.CreateParameter('pWaehrung',ftString,pdInput, 5, AuftragKopf.Waehrung);
      Parameters.CreateParameter('pAuftragNr',ftString,pdInput, AuftragNrSize, AuftragKopf.AuftragNr);
      Parameters.CreateParameter('pBestNr',ftString,pdInput, 32, copy (AuftragKopf.BestellNr,1, 32));
      Parameters.CreateParameter('pKommNr',ftString,pdInput, 32, AuftragKopf.KommNr);
      Parameters.CreateParameter('pLieferNr',ftString,pdInput, 32, AuftragKopf.LieferNr);
      Parameters.CreateParameter('pKommDate',ftString,pdInput, 32, AuftragKopf.KommDatum);
      Parameters.CreateParameter('pLieferDate',ftString,pdInput, 32, AuftragKopf.LieferDatum);
      Parameters.CreateParameter('pLieferText',ftString,pdInput, 1024, copy (AuftragKopf.ZusatzLS, 1, 1024));
      Parameters.CreateParameter('pLT',ftString,pdInput, 32, AuftragKopf.Ladungstraeger);
      Parameters.CreateParameter('pPackmittel',ftString,pdInput, 32, AuftragKopf.Packmittel);
      Parameters.CreateParameter('pKommArt',ftString,pdInput, 8, AuftragKopf.KommArt);
      Parameters.CreateParameter('pAuftragsart',ftString,pdInput, 32, AuftragKopf.Auftragsart);
      Parameters.CreateParameter('pBestDate',ftString,pdInput, 32, AuftragKopf.BestellDatum);
      Parameters.CreateParameter('pAnlieferZeitVon',ftInteger,pdInput, 12, NULL);
      Parameters.CreateParameter('pAnlieferZeitBis',ftInteger,pdInput, 12, NULL);
      Parameters.CreateParameter('pAnlieferZeit2Von',ftInteger,pdInput, 12, NULL);
      Parameters.CreateParameter('pAnlieferZeit2Bis',ftInteger,pdInput, 12, NULL);
      Parameters.CreateParameter('pLagerLief',ftString,pdInput, 32, AuftragKopf.AuslieferLager);
      Parameters.CreateParameter('pSpedition',ftString,pdInput, 32, AuftragKopf.SpeditionNr);
      Parameters.CreateParameter('pTourNr',ftString,pdInput, 32, AuftragKopf.TourNr);
      Parameters.CreateParameter('pDruckart',ftString,pdInput, 32, AuftragKopf.Druckart);
      Parameters.CreateParameter('pKommText',ftString,pdInput, 256, copy (AuftragKopf.ZusatzKomm, 1, 256));
      Parameters.CreateParameter('pOptions',ftString,pdInput, 128, AuftragKopf.Options);
      Parameters.CreateParameter('pSammelNr',ftString,pdInput, 32, AuftragKopf.SammelNr);
      Parameters.CreateParameter('pLieferantenNr',ftString,pdInput, 32, AuftragKopf.LieferantenNr);

      Parameters.CreateParameter('oSeqNr',ftInteger,pdOutput, 12, NULL);

      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

    if (dbres = 0) then begin
      RefKopf   := StoredProcedure.Parameters.ParamValues ['oSeqNr'];

      if (Length (AuftragKopf.Trader) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_TRADING';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pPartner',ftString,pdInput, 32, AuftragKopf.Trader);
          Parameters.CreateParameter('pKundenNr',ftString,pdInput, 32, NULL);
          Parameters.CreateParameter('pBestellNr',ftString,pdInput, 32, NULL);
          Parameters.CreateParameter('pAuftragNr',ftString,pdInput, 32, NULL);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (Length (AuftragKopf.IFCAuftragNr) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_AUFTRAG_IFC_AUFTRAG_NR';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pIfcAuftragNr',ftString,pdInput, 32, AuftragKopf.IFCAuftragNr);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (Length (AuftragKopf.AuftragReferenz) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_REFERENZ';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pAufReferenz',ftString,pdInput, 32, copy (AuftragKopf.AuftragReferenz, 1, 32));

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (Length (AuftragKopf.AuftragNrListe) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_AUFTRAG_NR_LISTE';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pAufReferenz',ftString,pdInput, 256, copy (AuftragKopf.AuftragNrListe, 1, 256));

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (Length (AuftragKopf.AuftragProcessNr) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_PROCESS_NR';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pProcessNr',ftString,pdInput, 32, copy (AuftragKopf.AuftragProcessNr, 1, 32));

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (Length (AuftragKopf.Quelle) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_QUELLE';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pAufQuelle',ftString,pdInput, 32, AuftragKopf.Quelle);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (Length (AuftragKopf.CostCenter) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_AUFTRAG_CONST_CENTER';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pCostCenter',ftString,pdInput, 32, AuftragKopf.CostCenter);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (Length (AuftragKopf.Instructions) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_AUFTRAG_INSTRUCTION';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pInstruction',ftString,pdInput, 256, AuftragKopf.Instructions);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and ((Length (AuftragKopf.FieldSales) > 0) or (Length (AuftragKopf.OfficeSales) > 0)) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_AUFTRAG_SALES_STUFF';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pFieldSales',ftString,pdInput, 32, AuftragKopf.FieldSales);
          Parameters.CreateParameter('pOfficSales',ftString,pdInput, 32, AuftragKopf.OfficeSales);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (Length (AuftragKopf.SupplierNo) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_AUFTRAG_SUPPLIER_NO';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pSupplierNo',ftString,pdInput, 32, AuftragKopf.SupplierNo);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (Length (AuftragKopf.OutboundLager) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_AUFTRAG_OUTBOUND_LAGER';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pLagerName',ftString,pdInput, 32, AuftragKopf.OutboundLager);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (Length (AuftragKopf.LieferBedingung) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_AUFTRAG_TERMS_OF_DELIVERY';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pTerms',ftString,pdInput, 64, AuftragKopf.LieferBedingung);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (Length (AuftragKopf.LieferHinweis) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_AUFTRAG_LIEFER_HINWEIS';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pHinweis',ftString,pdInput, 128, AuftragKopf.LieferHinweis);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and ((Length (AuftragKopf.ZusatzPack) > 0) or (Length (AuftragKopf.ZusatzVers) > 0)) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_TEXTE';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pSprache',ftString,pdInput, 32, AuftragKopf.Sprache);
          Parameters.CreateParameter('pLieferText',ftString,pdInput, 1024, copy (AuftragKopf.ZusatzLS, 1, 1024));
          Parameters.CreateParameter('pKommText',ftString,pdInput, 1024, copy (AuftragKopf.ZusatzKomm, 1, 1024));
          Parameters.CreateParameter('pVersandHint',ftString,pdInput, 1024, copy (AuftragKopf.ZusatzVers, 1, 1024));
          Parameters.CreateParameter('pPackHint',ftString,pdInput, 1024, copy (AuftragKopf.ZusatzPack, 1, 1024));

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) and (AuftragKopf.Prio <> -1) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_PRIO';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (AuftragKopf.Prio));
          Parameters.CreateParameter('pGrund',ftString,pdInput, 1024, NULL);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;

      if (dbres = 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG.BEGIN_UPDATE_AUFTRAG';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
      end;
    end;

    StoredProcedure.Free;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 12.02.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetAuftragLieferNr (const RefKopf : Integer; const LieferNr : String) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_AUFTRAG.SET_LIEFERSCHEIN_NR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pAufRef',ftInteger,pdInput, 12, RefKopf);

    Parameters.CreateParameter('pLieferscheinNr',ftString,pdInput, 32, LieferNr);


    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 12.02.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetAuftragVersandDaten (const RefKopf : Integer; const Art, Spedition, COD, Barzahler, Hinweis : String) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_AUFTRAG.SET_VERSAND_DATEN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pAufRef',ftInteger,pdInput, 12, RefKopf);

    Parameters.CreateParameter('pVersandArt',ftString,pdInput, 32, Art);
    Parameters.CreateParameter('pSpedition',ftString,pdInput, 32, Spedition);
    Parameters.CreateParameter('pNachname',ftString,pdInput, 1, COD);
    Parameters.CreateParameter('pBarzahler',ftString,pdInput, 1, Barzahler);
    Parameters.CreateParameter('pHinweis',ftString,pdInput, 64, Copy (Hinweis, 1, 64));


    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportAuftragAdr (const RefKopf : Integer; const AuftragAdr : TAdresse) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_AUFTRAG.INSERT_ADRESSE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pAufRef',ftInteger,pdInput, 12, RefKopf);

    Parameters.CreateParameter('pArt',ftString,pdInput, 16, AuftragAdr.Adressart);
    Parameters.CreateParameter('pValid',ftString,pdInput, 8, AuftragAdr.Valid);

    Parameters.CreateParameter('pNummer',ftString,pdInput, 64, AuftragAdr.Nr);

    if (AuftragAdr.Pruefziffer = -1) then
      Parameters.CreateParameter('pPruefziffer',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPruefziffer',ftInteger,pdInput, 12, AuftragAdr.Pruefziffer);

    Parameters.CreateParameter('pAnrede',ftString,pdInput, 32, AuftragAdr.Anrede);
    Parameters.CreateParameter('pName1',ftString,pdInput, 64, copy (AuftragAdr.Name1, 1, 64));
    Parameters.CreateParameter('pName2',ftString,pdInput, 64, copy (AuftragAdr.Name2, 1, 64));
    Parameters.CreateParameter('pNameZusatz',ftString,pdInput, 64, copy (AuftragAdr.NameZusatz, 1, 64));
    Parameters.CreateParameter('pStrasse',ftString,pdInput, 64, copy (AuftragAdr.Strasse, 1, 64));
    Parameters.CreateParameter('pStrasse2',ftString,pdInput, 64, copy (AuftragAdr.Strasse_2, 1, 64));
    Parameters.CreateParameter('pLand',ftString,pdInput, 16, AuftragAdr.Land);
    Parameters.CreateParameter('pPlz',ftString,pdInput, 12, copy (AuftragAdr.Plz, 1, 12));
    Parameters.CreateParameter('pOrt',ftString,pdInput, 128, AuftragAdr.Ort);
    Parameters.CreateParameter('pFilialnr',ftString,pdInput, 32, AuftragAdr.FilialNr);
    Parameters.CreateParameter('pIln',ftString,pdInput, 32, AuftragAdr.ILN);
    Parameters.CreateParameter('pContact',ftString,pdInput, 64, AuftragAdr.Ansprechpartner);
    Parameters.CreateParameter('pTelefon',ftString,pdInput, 32, AuftragAdr.Telefon);
    Parameters.CreateParameter('pFax',ftString,pdInput, 32, AuftragAdr.Fax);
    Parameters.CreateParameter('pEmail',ftString,pdInput, 64, AuftragAdr.Email);
    Parameters.CreateParameter('pColliAdr',ftString,pdInput, 64, AuftragAdr.ColliAdr);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) and (Length (AuftragAdr.UstID) > 0) and (AuftragAdr.UstID <> '~') then begin
    StoredProcedure.Free;

    StoredProcedure := TADOStoredProc.Create (Nil);

    with StoredProcedure do begin
      ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_ADRESSE_UST_ID';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

      Parameters.CreateParameter('pAufRef',ftInteger,pdInput, 12, RefKopf);
      Parameters.CreateParameter('pArt',ftString,pdInput, 16, AuftragAdr.Adressart);
      Parameters.CreateParameter('pUSTId',ftString,pdInput, 32, copy (AuftragAdr.UstID, 1, 32));

      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
  end;

  if (dbres = 0) and (Length (AuftragAdr.EORI) > 0) and (AuftragAdr.EORI <> '~') then begin
    StoredProcedure.Free;

    StoredProcedure := TADOStoredProc.Create (Nil);

    with StoredProcedure do begin
      ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_ADRESSE_EORI';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

      Parameters.CreateParameter('pAufRef',ftInteger,pdInput, 12, RefKopf);
      Parameters.CreateParameter('pArt',ftString,pdInput, 16, AuftragAdr.Adressart);
      Parameters.CreateParameter('pEORI',ftString,pdInput, 32, copy (AuftragAdr.EORI, 1, 32));

      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportAuftragPos (const RefKopf : Integer; const AuftragPos : TAuftragPos; var RefPos : Integer) : Integer;
var
  dbres    : Integer;
  StoredProcedure : TADOStoredProc;
  arnr     : String;
  selquery : TADOQuery;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    if (Length (AuftragPos.VariantID) > 0) then begin
      ProcedureName := 'PA_AUFTRAG.INSERT_AUFTRAG_POS';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

      Parameters.CreateParameter('pAufRef',ftInteger,pdInput, 12, RefKopf);

      Parameters.CreateParameter('pPosNr',ftInteger,pdInput, 12, GetPLSQLParameter (AuftragPos.PosNr));
      Parameters.CreateParameter('pPosReferenz',ftString,pdInput, 32, AuftragPos.Referenz);
      Parameters.CreateParameter('pVariantID',ftString,pdInput, ArtikelNrSize, AuftragPos.VariantID);
      Parameters.CreateParameter('pMenge',ftInteger,pdInput, 12, GetPLSQLParameter (AuftragPos.SollMenge));
      Parameters.CreateParameter('pGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (AuftragPos.SollGewicht));

      Parameters.CreateParameter('pMinMHD',ftString,pdInput, 32, AuftragPos.MHDMin);
      Parameters.CreateParameter('pFixMHD',ftString,pdInput, 32, AuftragPos.MHDMuss);
      Parameters.CreateParameter('pCharge',ftString,pdInput, 32, AuftragPos.Charge);
      Parameters.CreateParameter('pArt',ftInteger,pdInput, 12, GetPLSQLParameter (AuftragPos.ArtikelArt));

      Parameters.CreateParameter('pKommText',ftString,pdInput, 256, AuftragPos.KommHinweis);
      Parameters.CreateParameter('pBestellArNr',ftString,pdInput, ArtikelNrSize, AuftragPos.BestellArtikelNr);
      Parameters.CreateParameter('pKundenArNr',ftString,pdInput, ArtikelNrSize, AuftragPos.KundenArtikelNr);
      Parameters.CreateParameter('pCrossBestNr',ftString,pdInput, 32, AuftragPos.CrossdockBestellNr);

      Parameters.CreateParameter('pOptions',ftString,pdInput, 32, NULL);

      Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, NULL);

      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end else begin
      arnr := AuftragPos.ArtikelNr;

      if (Length (AuftragPos.MandantArtikelNr) > 0) then begin
        selquery  := TADOQuery.Create (Nil);

        try
          selquery.LockType := ltReadOnly;
          selquery.Connection  := LVSDatenModul.MainADOConnection;

          selquery.SQL.Add ('select ar.ARTIKEL_NR from VQ_ARTIKEL ar, VQ_AUFTRAG auf where ar.STATUS in (''AKT'') and ar.REF_MAND=auf.REF_MAND and nvl (ar.REF_SUB_MAND,-1)=nvl (auf.REF_SUB_MAND,-1) and auf.REF=:ref_auf and ar.ARTIKEL_NR_MANDANT=:ar_nr');
          selquery.Parameters.ParamByName ('ar_nr').Value := AuftragPos.MandantArtikelNr;
          selquery.Parameters.ParamByName ('ref_auf').Value := RefKopf;

          selquery.Open;

          if not (selquery.Fields[0].IsNull) then
            arnr := selquery.Fields[0].AsString;

          selquery.Close;
        finally
          selquery.Free;
        end;
      end;

      if (dbres = 0) then begin
        ProcedureName := 'PA_AUFTRAG.INSERT_AUFTRAG_POS';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pAufRef',ftInteger,pdInput, 12, RefKopf);

        if (AuftragPos.PosNr = -1) then
          Parameters.CreateParameter('pPosNr',ftInteger,pdInput, 12, NULL)
        else
          Parameters.CreateParameter('pPosNr',ftInteger,pdInput, 12, AuftragPos.PosNr);

        Parameters.CreateParameter('pPosReferenz',ftString,pdInput, 32, AuftragPos.Referenz);
        Parameters.CreateParameter('pArtikel',ftString,pdInput, ArtikelNrSize, arnr);
        Parameters.CreateParameter('pEAN',ftString,pdInput, 32, AuftragPos.EAN);
        Parameters.CreateParameter('pVariante',ftString,pdInput, 32, AuftragPos.Variante);
        Parameters.CreateParameter('pVPE',ftString,pdInput, 32, AuftragPos.VPE);

        if (AuftragPos.SollMenge = -1) then
          Parameters.CreateParameter('pMenge',ftInteger,pdInput, 12, NULL)
        else
          Parameters.CreateParameter('pMenge',ftInteger,pdInput, 12, AuftragPos.SollMenge);

        if (AuftragPos.SollGewicht = -1) then
          Parameters.CreateParameter('pGewicht',ftInteger,pdInput, 12, NULL)
        else
          Parameters.CreateParameter('pGewicht',ftInteger,pdInput, 12, AuftragPos.SollGewicht);

        Parameters.CreateParameter('pMinMHD',ftString,pdInput, 32, AuftragPos.MHDMin);
        Parameters.CreateParameter('pFixMHD',ftString,pdInput, 32, AuftragPos.MHDMuss);
        Parameters.CreateParameter('pCharge',ftString,pdInput, 32, AuftragPos.Charge);

        if (AuftragPos.ArtikelArt = -1) then
          Parameters.CreateParameter('pArt',ftInteger,pdInput, 12, NULL)
        else
          Parameters.CreateParameter('pArt',ftInteger,pdInput, 12, AuftragPos.ArtikelArt);

        Parameters.CreateParameter('pKommText',ftString,pdInput, 256, AuftragPos.KommHinweis);
        Parameters.CreateParameter('pBestellArNr',ftString,pdInput, ArtikelNrSize, AuftragPos.BestellArtikelNr);
        Parameters.CreateParameter('pKundenArNr',ftString,pdInput, ArtikelNrSize, AuftragPos.KundenArtikelNr);
        Parameters.CreateParameter('pCrossBestNr',ftString,pdInput, 32, AuftragPos.CrossdockBestellNr);

        Parameters.CreateParameter('pOptions',ftString,pdInput, 32, NULL);

        Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, NULL);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;
    end;
  end;

  if (dbres = 0) then begin
    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

    if (dbres = 0) then begin
      RefPos := StoredProcedure.Parameters.ParamValues ['oRef'];
    end;
  end;

  StoredProcedure.Free;


  if (dbres = 0) then begin
    if (AuftragPos.NettoBetrag > 0) then begin
      StoredProcedure := TADOStoredProc.Create (Nil);

      with StoredProcedure do begin
        ProcedureName := 'PA_AUFTRAG_RECHNUNG.INSERT_AUFTRAG_POS_RECHNUNG';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pNettoBetrag',ftInteger,pdInput, 12, AuftragPos.NettoBetrag);
        Parameters.CreateParameter('pBruttoBetrag',ftInteger,pdInput, 12, NULL);
        Parameters.CreateParameter('pPreisEinheit',ftInteger,pdInput, 12, NULL);
        Parameters.CreateParameter('pMWST',ftInteger,pdInput, 12, NULL);

        if AuftragPos.EKVerzollung then
          Parameters.CreateParameter('pOptions',ftString,pdInput, 64, 'EK;');

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

      StoredProcedure.Free;
    end;
  end;

  if (dbres = 0) and (AuftragPos.Category <> '~') then begin
    StoredProcedure := TADOStoredProc.Create (Nil);

    with StoredProcedure do begin
      ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_POS_CATEGORY';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

      Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
      Parameters.CreateParameter('pCategoryDesc',ftString,pdInput, 32, AuftragPos.Category);

      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

    StoredProcedure.Free;
  end;

  if (dbres = 0) then begin
    if (Length (AuftragPos.BundleNr) > 0) then begin
      StoredProcedure := TADOStoredProc.Create (Nil);

      with StoredProcedure do begin
        ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_POS_BUNDLE_NR';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pBundelNr',ftString,pdInput, 32, AuftragPos.BundleNr);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

      StoredProcedure.Free;
    end;
  end;

  if (dbres = 0) then begin
    if (Length (AuftragPos.AuftragNr) > 0) then begin
      StoredProcedure := TADOStoredProc.Create (Nil);

      with StoredProcedure do begin
        ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_AUFTRAG_POS_AUFTRAG_NR';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pAuftragNr',ftString,pdInput, 32, AuftragPos.AuftragNr);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

      StoredProcedure.Free;
    end;
  end;

  if (dbres = 0) then begin
    if (Length (AuftragPos.StockLocation) > 0) then begin
      StoredProcedure := TADOStoredProc.Create (Nil);

      with StoredProcedure do begin
        ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_POS_STOCK_LOC';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pText',ftString,pdInput, 32, AuftragPos.StockLocation);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

      StoredProcedure.Free;
    end;
  end;


  if (dbres = 0) then begin
    if (Length (AuftragPos.KundenOption) > 0) then begin
      StoredProcedure := TADOStoredProc.Create (Nil);

      with StoredProcedure do begin
        ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_POS_KD_OPT';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pOption',ftString,pdInput, 32, AuftragPos.KundenOption);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

      StoredProcedure.Free;
    end;
  end;

  if (dbres = 0) then begin
    if (Length (AuftragPos.PosArtikelText) > 0) then begin
      StoredProcedure := TADOStoredProc.Create (Nil);

      with StoredProcedure do begin
        ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_POS_ARTIKEL_TEXT';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pText',ftString,pdInput, 64, AuftragPos.PosArtikelText);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

      StoredProcedure.Free;
    end;
  end;

  if (dbres = 0) then begin
    if (Length (AuftragPos.NVENr) > 0) then begin
      StoredProcedure := TADOStoredProc.Create (Nil);

      with StoredProcedure do begin
        ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_POS_NVE';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pNVENr',ftString,pdInput, 32, AuftragPos.NVENr);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

      StoredProcedure.Free;
    end;
  end;

  if (dbres = 0) then begin
    if (Length (AuftragPos.HUNr) > 0) then begin
      StoredProcedure := TADOStoredProc.Create (Nil);

      with StoredProcedure do begin
        ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_POS_HU';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pHUNr',ftString,pdInput, 32, AuftragPos.HUNr);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

      StoredProcedure.Free;
    end;
  end;

  if (dbres = 0) then begin
    if (Length (AuftragPos.OutNVENr) > 0) and (AuftragPos.OutNVENr <> '~') then begin
      StoredProcedure := TADOStoredProc.Create (Nil);

      with StoredProcedure do begin
        ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_POS_NVE_OUT';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pNVENr',ftString,pdInput, 32, AuftragPos.OutNVENr);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

      StoredProcedure.Free;
    end;
  end;

  if (dbres = 0) then begin
    if (Length (AuftragPos.OutHUNr) > 0) and (AuftragPos.OutHUNr <> '~') then begin
      StoredProcedure := TADOStoredProc.Create (Nil);

      with StoredProcedure do begin
        ProcedureName := 'PA_AUFTRAG.SET_AUFTRAG_POS_HU_OUT';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pHUNr',ftString,pdInput, 32, AuftragPos.OutHUNr);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

      StoredProcedure.Free;
    end;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportWarenempf     (const Warenempf : TWarenempf; var RefEmpf : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  RefEmpf := -1;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_WARENEMPF.INSERT_WARENEMPF';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pSender',ftString,pdInput, 32, Copy ('PCD:'+LVSDatenModul.AktUser, 1, 32));
    Parameters.CreateParameter('pMandant',ftString,pdInput, 32, Warenempf.Mandant);
    Parameters.CreateParameter('pSubMandant',ftString,pdInput, 32, Warenempf.SubMandant);
    Parameters.CreateParameter('pKdNr',ftString,pdInput, 32, Warenempf.KundenNr);
    Parameters.CreateParameter('pSprache',ftString,pdInput, 5, Warenempf.Sprache);
    Parameters.CreateParameter('pWaehrung',ftString,pdInput, 5, Warenempf.Waehrung);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 64, copy (Warenempf.Bezeichung, 1, 64));
    Parameters.CreateParameter('pILN',ftString,pdInput, 32, Warenempf.ILN);
    Parameters.CreateParameter('pLSText',ftString,pdInput, 128, copy (Warenempf.LieferText, 1, 128));
    Parameters.CreateParameter('pKommText',ftString,pdInput, 128, copy (Warenempf.KommText, 1, 128));

    if (Warenempf.LieferKopien = -1) then
      Parameters.CreateParameter('pLSCopy',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLSCopy',ftInteger,pdInput, 12, Warenempf.LieferKopien);

    if (Warenempf.AnlieferzeitVon = -1) then
      Parameters.CreateParameter('pLfZeitVon',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLfZeitVon',ftInteger,pdInput, 12, Warenempf.AnlieferzeitVon);

    if (Warenempf.AnlieferzeitBis = -1) then
      Parameters.CreateParameter('pLfZeitBis',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLfZeitBis',ftInteger,pdInput, 12, Warenempf.AnlieferzeitBis);

    Parameters.CreateParameter('pLTBeladung',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pLTTyp',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pAbholer',ftString,pdInput, 8, NULL);
    Parameters.CreateParameter('pPfandAbgabe',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pAvisOpt',ftString,pdInput, 8, NULL);
    Parameters.CreateParameter('pAvisMailAddr',ftString,pdInput, 8, NULL);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    RefEmpf   := StoredProcedure.Parameters.ParamValues ['oRef'];
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportWarenempfAdr  (const RefEmpf : Integer; const AuftragAdr : TAdresse) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_WARENEMPF.INSERT_WARENEMPF_ADR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pEmpfRef',ftInteger,pdInput, 12, RefEmpf);

    Parameters.CreateParameter('pArt',ftString,pdInput, 16, AuftragAdr.Adressart);

    Parameters.CreateParameter('pNummer',ftString,pdInput, 64, AuftragAdr.Nr);

    Parameters.CreateParameter('pName1',ftString,pdInput, 64, AuftragAdr.Name1);
    Parameters.CreateParameter('pName2',ftString,pdInput, 64, AuftragAdr.Name2);
    Parameters.CreateParameter('pNameZusatz',ftString,pdInput, 64, AuftragAdr.NameZusatz);
    Parameters.CreateParameter('pStrasse',ftString,pdInput, 64, AuftragAdr.Strasse);
    Parameters.CreateParameter('pLand',ftString,pdInput, 16, copy (AuftragAdr.Land, 1, 16));
    Parameters.CreateParameter('pPlz',ftString,pdInput, 12, copy (AuftragAdr.Plz, 1, 12));
    Parameters.CreateParameter('pOrt',ftString,pdInput, 128, AuftragAdr.Ort);
    Parameters.CreateParameter('pFilialnr',ftString,pdInput, 32, AuftragAdr.FilialNr);
    Parameters.CreateParameter('pIln',ftString,pdInput, 32, AuftragAdr.ILN);
    Parameters.CreateParameter('pContact',ftString,pdInput, 64, AuftragAdr.Ansprechpartner);
    Parameters.CreateParameter('pTelefon',ftString,pdInput, 32, AuftragAdr.Telefon);
    Parameters.CreateParameter('pFax',ftString,pdInput, 32, AuftragAdr.Fax);
    Parameters.CreateParameter('pEmail',ftString,pdInput, 64, AuftragAdr.Email);
    Parameters.CreateParameter('pColliAdr',ftString,pdInput, 64, AuftragAdr.ColliAdr);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportBestandPos (const RefInvRes : Integer; BestandPos : TBestandPos; var RefPos : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_INVENTUR.INSERT_INV_RESULT_EX';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefKopf',ftInteger,pdInput, 12, RefInvRes);
    Parameters.CreateParameter('pRefTerm',ftInteger,pdInput, 12, NULL);
    Parameters.CreateParameter('pRefPos',ftInteger,pdInput, 12, NULL);

    Parameters.CreateParameter('pRefAR',ftInteger,pdInput, 12, BestandPos.RefAR);
    Parameters.CreateParameter('pRefVPE',ftInteger,pdInput, 12, BestandPos.RefVPE);

    Parameters.CreateParameter('pHerstellDatum',ftDateTime,pdInput, 64,  GetPLSQLParameter (BestandPos.HerstellDatum));
    Parameters.CreateParameter('pMHD',ftDateTime,pdInput, 64,  GetPLSQLParameter (BestandPos.MHD));
    Parameters.CreateParameter('pCharge',ftString,pdInput, 64, BestandPos.Charge);

    if (BestandPos.MengeCutting > 0) then
      Parameters.CreateParameter('pMengeFrei',ftInteger,pdInput, 12, GetPLSQLParameter (BestandPos.MengeCutting))
    else
      Parameters.CreateParameter('pMengeFrei',ftInteger,pdInput, 12, GetPLSQLParameter (BestandPos.Menge));

    Parameters.CreateParameter('pMengeSperr',ftInteger,pdInput, 12, NULL);
    Parameters.CreateParameter('pGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (BestandPos.NettoGewicht));

    Parameters.CreateParameter('pRefLB',ftInteger,pdInput, 12, NULL);
    Parameters.CreateParameter('pRefLP',ftInteger,pdInput, 12, GetPLSQLParameter (BestandPos.RefLP));
    Parameters.CreateParameter('pRefLE',ftInteger,pdInput, 12, GetPLSQLParameter (BestandPos.RefLE));

    Parameters.CreateParameter('pLENr',ftString,pdInput, 32, BestandPos.LENr);

    if (BestandPos.LEFachPos = -1) then
      Parameters.CreateParameter('pFachPos', ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFachPos', ftInteger,pdInput, 12, BestandPos.LEFachPos);

    Parameters.CreateParameter('pVariante',ftString,pdInput, 64, NULL);
    Parameters.CreateParameter('pWiegeID',ftString,pdInput, 32, BestandPos.WiegeID);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then
    RefPos := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateSpedRoutingVersion (const RefSped : Integer; var RefVer : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  RefVer := -1;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.CREATE_ROUTING_VERSION';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefSped',ftInteger,pdInput, 12, RefSped);

    Parameters.CreateParameter('oRefVer',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then
    RefVer := StoredProcedure.Parameters.ParamValues ['oRefVer'];

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: ActivateSpedRoutingVersion
//* Author       : Stefan Graf
//* Datum        : 17.03.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ActivateSpedRoutingVersion (const RefSped, RefVer : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.ACTIVATE_ROUTING_VERSION';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefSped',ftInteger,pdInput, 12, GetPLSQLParameter (RefSped));
    Parameters.CreateParameter('pRefVer',ftInteger,pdInput, 12, GetPLSQLParameter (RefVer));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportSpedRouting (const RefVer, RefSped : Integer; const Land, PLZVon, PLZBis, Relation, FeinRelation, DepotNr, Gaytway : String; var RefRouting : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  RefRouting := -1;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.INSERT_ROUTING';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefVer',ftInteger,pdInput, 12, RefVer);
    Parameters.CreateParameter('pRefSped',ftInteger,pdInput, 12, RefSped);

    Parameters.CreateParameter('pLand',ftString,pdInput, 64, Land);
    Parameters.CreateParameter('pPLZVon',ftString,pdInput, 64, PLZVon);
    Parameters.CreateParameter('pPLZBis',ftString,pdInput, 64, PLZBis);
    Parameters.CreateParameter('pRelation',ftString,pdInput, 64, Relation);
    Parameters.CreateParameter('pFeinRelation',ftString,pdInput, 64, FeinRelation);
    Parameters.CreateParameter('pDepotNr',ftString,pdInput, 64, DepotNr);
    Parameters.CreateParameter('pGateway',ftString,pdInput, 64, Gaytway);

    Parameters.CreateParameter('oRefRouting',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then
    RefRouting := StoredProcedure.Parameters.ParamValues ['oRefRouting'];

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportBestellKopf (const BestKopf : TBestellKopf; var RefKopf : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_BESTELL.INSERT_BESTELLUNG';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pSender',ftString,pdInput, 32, Copy ('PCD:'+LVSDatenModul.AktUser, 1, 32));
    Parameters.CreateParameter('pRefMandant',ftInteger,pdInput, 12, BestKopf.RefMandant);
    Parameters.CreateParameter('pRefSubMandant',ftInteger,pdInput, 12, GetPLSQLParameter (BestKopf.RefSubMandant));
    Parameters.CreateParameter('pLager',ftInteger,pdInput, 32, BestKopf.RefLager);
    Parameters.CreateParameter('pArt',ftString,pdInput, 32, copy (BestKopf.Art, 1, 32));
    Parameters.CreateParameter('pLieferantNr',ftString,pdInput, 32, BestKopf.LieferantenNr);
    Parameters.CreateParameter('pLieferantName',ftString,pdInput, 64, copy (BestKopf.Lieferant, 1, 64));
    Parameters.CreateParameter('pBestNr',ftString,pdInput, 64,  copy (BestKopf.BestellNr,1 , 64));
    Parameters.CreateParameter('pBestRef',ftString,pdInput, 64, copy (BestKopf.BestellReferenz,1 , 64));

    if (Length (BestKopf.BestellDatum) = 0) then
      Parameters.CreateParameter('pBestDate',ftString,pdInput, 32, NULL)
    else
      Parameters.CreateParameter('pBestDate',ftString,pdInput, 32, BestKopf.BestellDatum);

    if (Length (BestKopf.LieferDatum) = 0) then
      Parameters.CreateParameter('pLieferDate',ftString,pdInput, 32, NULL)
    else
      Parameters.CreateParameter('pLieferDate',ftString,pdInput, 32, BestKopf.LieferDatum);

    Parameters.CreateParameter('pLieferZeit',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pLSNr',ftString,pdInput, 32, copy (BestKopf.LieferscheinNr, 1, 32));
    Parameters.CreateParameter('pHinweis',ftString,pdInput, 32, copy (BestKopf.Hinweis, 1, 32));

    Parameters.CreateParameter('oSeqNr',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    RefKopf   := StoredProcedure.Parameters.ParamValues ['oSeqNr'];

    if (dbres = 0) and (Length (BestKopf.ProjectId) > 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_PROJECT_ID';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestRef',ftInteger,pdInput, 12, RefKopf);
        Parameters.CreateParameter('pProjectID',ftString,pdInput, 32, BestKopf.ProjectId);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (dbres = 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.BEGIN_UPDATE_BESTELLUNG';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefKopf);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportBestellPos  (const RefKopf : Integer; const BestPosNr : Integer; const BestellPos : TBestellPos; var RefPos : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  RefPos := -1;

  with StoredProcedure do begin
    ProcedureName := 'PA_BESTELL.INSERT_BESTELL_POS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pBestRef',ftInteger,pdInput, 12, RefKopf);
    Parameters.CreateParameter('pPosNr',ftInteger,pdInput, 12, GetPLSQLParameter (BestPosNr));
    Parameters.CreateParameter('pBestPosRef',ftString,pdInput, 32, NULL);

    if (Length (BestellPos.MandantArtikelNr) > 0) then
      Parameters.CreateParameter('pArtikel',ftString,pdInput, ArtikelNrSize, BestellPos.MandantArtikelNr)
    else
      Parameters.CreateParameter('pArtikel',ftString,pdInput, ArtikelNrSize, BestellPos.ArtikelNr);

    Parameters.CreateParameter('pVariante',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pVPE',ftString,pdInput, 32, BestellPos.VPE);
    Parameters.CreateParameter('pEAN',ftString,pdInput, 32, BestellPos.EAN);
    Parameters.CreateParameter('pMenge',ftInteger,pdInput, 12, GetPLSQLParameter (BestellPos.SollMenge));
    Parameters.CreateParameter('pGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (BestellPos.SollGewicht));
    Parameters.CreateParameter('pBesVPE',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pBesFaktor',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('oRefPos',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    RefPos   := StoredProcedure.Parameters.ParamValues ['oRefPos'];

    if ((BestellPos.MHDMin > 0) or (BestellPos.MHDMuss > 0)) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_MHD';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pMHDMin',ftDateTime,pdInput, 32, NULL);
        Parameters.CreateParameter('pMHDFix',ftDateTime,pdInput, 32, GetPLSQLParameter (BestellPos.MHDMuss));

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (dbres = 0) and (Length (BestellPos.Charge) > 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_CHARGE';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pChargeSoll',ftString,pdInput, 32, BestellPos.Charge);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (dbres = 0) and (Length (BestellPos.NVENr) > 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_NVE';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pNVE',ftString,pdInput, 32, BestellPos.NVENr);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (dbres = 0) and (Length (BestellPos.HUNr) > 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_HU';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pHUNr',ftString,pdInput, 32, BestellPos.HUNr);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (dbres = 0) and (BestellPos.PalHeight > 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_PAL_HEIGHT';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pPalHeight',ftInteger,pdInput, 32, BestellPos.PalHeight);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (dbres = 0) and (Length (BestellPos.ProjectId) > 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_PROJECT_ID';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pProjectID',ftString,pdInput, 32, BestellPos.ProjectId);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (dbres = 0) and (Length (BestellPos.WiegeId) > 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_WIEGE_ID';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pWiegeID',ftString,pdInput, 32, BestellPos.WiegeId);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (dbres = 0) and (Length (BestellPos.AufReferenz) > 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_AUF_REFERENZE';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pAufReferenze',ftString,pdInput, 32, BestellPos.AufReferenz);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (dbres = 0) and (Length (BestellPos.CostCenter) > 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_COST_CENTER';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pCostCenter',ftString,pdInput, 32, BestellPos.CostCenter);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (BestellPos.EingangDatum > 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_EINGANG_DATUM';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pInDate',ftDateTime,pdInput, 32, GetPLSQLParameter (BestellPos.EingangDatum));

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (BestellPos.HerstellDatum > 0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_HERSTELL_DATUM';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pProdDate',ftDateTime,pdInput, 32, GetPLSQLParameter (BestellPos.HerstellDatum));

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;

    if (BestellPos.SperrOpt > #0) then begin
      with StoredProcedure do begin
        ProcedureName := 'PA_BESTELL.SET_BESTELL_POS_SPERR_WE';

        Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

        Parameters.CreateParameter('pBestPosRef',ftInteger,pdInput, 12, RefPos);
        Parameters.CreateParameter('pSperrOpt',ftString,pdInput, 32, BestellPos.SperrOpt);
        Parameters.CreateParameter('pSperrGrund',ftString,pdInput, 64, BestellPos.SperrGrund);

        Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
        Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
      end;

      dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
    end;
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportRetourenAvisKopf (const RetAvisKopf : TRetAvisKopf; var RefKopf : Integer) : Integer;
var
  dbres   : Integer;
  dt              : TDateTime;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_RETOURE.IFC_INSERT_RETOUREN_AVIS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pSender',ftString,pdInput, 32, Copy ('PCD:'+LVSDatenModul.AktUser, 1, 32));
    Parameters.CreateParameter('pMandant',ftString,pdInput, 32, RetAvisKopf.Mandant);
    Parameters.CreateParameter('pSubMandant',ftString,pdInput, 32, RetAvisKopf.SubMandant);
    Parameters.CreateParameter('pRetoureLager',ftString,pdInput, 32, RetAvisKopf.RetoureLager);
    Parameters.CreateParameter('pKundennr',ftString,pdInput, 32, copy (RetAvisKopf.KundenNr, 1, 32));
    Parameters.CreateParameter('pAvisNr',ftString,pdInput, 32, RetAvisKopf.AVISNummer);

    if (Length (RetAvisKopf.VersandDatum) = 0) then
      Parameters.CreateParameter('pRetoureDate',ftString,pdInput, 32, NULL)
    else if not TryStrToDate (RetAvisKopf.VersandDatum, dt) then
      Parameters.CreateParameter('pRetoureDate',ftString,pdInput, 32, NULL)
    else
      Parameters.CreateParameter('pRetoureDate',ftDateTime,pdInput, 32, dt);

    Parameters.CreateParameter('pOrigBestNr',ftString,pdInput, 32, RetAvisKopf.BestellNr);
    Parameters.CreateParameter('pOrigAuftragNr',ftString,pdInput, 32, RetAvisKopf.AuftragNr);
    Parameters.CreateParameter('pVersandLager',ftString,pdInput, 32, RetAvisKopf.AuslieferLager);
    Parameters.CreateParameter('pVersandArt',ftString,pdInput, 32, NULL);

    if (Length (RetAvisKopf.VersandDatum) = 0) then
      Parameters.CreateParameter('pVersandDate',ftString,pdInput, 32, NULL)
    else if not TryStrToDate (RetAvisKopf.VersandDatum, dt) then
      Parameters.CreateParameter('pVersandDate',ftString,pdInput, 32, NULL)
    else
      Parameters.CreateParameter('pVersandDate',ftDateTime,pdInput, 32, dt);

    Parameters.CreateParameter('pRetoureStatus',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRetoureArt',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRetoureCode',ftString,pdInput, 1024, NULL);
    Parameters.CreateParameter('pRetoureGrund',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pBarcodeID',ftString,pdInput, 32, RetAvisKopf.TrackinID);
    Parameters.CreateParameter('pVersion',ftString,pdInput, 8, NULL);

    Parameters.CreateParameter('oRefAvis',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    RefKopf   := StoredProcedure.Parameters.ParamValues ['oRefAvis'];
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportRetourenAvisAdr  (const RefKopf : Integer; const AuftragAdr : TAdresse) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_RETOURE.INSERT_AVIS_ADRESSE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAvis',ftInteger,pdInput, 12, RefKopf);

    Parameters.CreateParameter('pArt',ftString,pdInput, 16, AuftragAdr.Adressart);
    Parameters.CreateParameter('pValid',ftString,pdInput, 8, AuftragAdr.Valid);

    Parameters.CreateParameter('pNummer',ftString,pdInput, 64, AuftragAdr.Nr);

    if (AuftragAdr.Pruefziffer = -1) then
      Parameters.CreateParameter('pPruefziffer',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPruefziffer',ftInteger,pdInput, 12, AuftragAdr.Pruefziffer);

    Parameters.CreateParameter('pAnrede',ftString,pdInput, 32, AuftragAdr.Anrede);
    Parameters.CreateParameter('pName1',ftString,pdInput, 64, copy (AuftragAdr.Name1, 1, 64));
    Parameters.CreateParameter('pName2',ftString,pdInput, 64, copy (AuftragAdr.Name2, 1, 64));
    Parameters.CreateParameter('pNameZusatz',ftString,pdInput, 64, copy (AuftragAdr.NameZusatz, 1, 64));
    Parameters.CreateParameter('pStrasse',ftString,pdInput, 64, copy (AuftragAdr.Strasse, 1, 64));
    Parameters.CreateParameter('pStrasse2',ftString,pdInput, 64, copy (AuftragAdr.Strasse_2, 1, 64));
    Parameters.CreateParameter('pLand',ftString,pdInput, 16, copy (AuftragAdr.Land, 1, 16));
    Parameters.CreateParameter('pPlz',ftString,pdInput, 12, copy (AuftragAdr.Plz, 1, 12));
    Parameters.CreateParameter('pOrt',ftString,pdInput, 128, AuftragAdr.Ort);
    Parameters.CreateParameter('pFilialnr',ftString,pdInput, 32, AuftragAdr.FilialNr);
    Parameters.CreateParameter('pIln',ftString,pdInput, 32, AuftragAdr.ILN);
    Parameters.CreateParameter('pContact',ftString,pdInput, 64, AuftragAdr.Ansprechpartner);
    Parameters.CreateParameter('pTelefon',ftString,pdInput, 32, AuftragAdr.Telefon);
    Parameters.CreateParameter('pFax',ftString,pdInput, 32, AuftragAdr.Fax);
    Parameters.CreateParameter('pEmail',ftString,pdInput, 64, AuftragAdr.Email);
    Parameters.CreateParameter('pColliAdr',ftString,pdInput, 64, AuftragAdr.ColliAdr);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportRetourenAvisPos  (const RefKopf : Integer; const AvisPosNr : Integer; const AuftragPos : TAuftragPos; var RefPos : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_RETOURE.IFC_INSERT_RETOUREN_AVIS_POS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAvis',ftInteger,pdInput, 12, RefKopf);

    Parameters.CreateParameter('pPosNr',ftInteger,pdInput, 12, GetPLSQLParameter (AvisPosNr));

    Parameters.CreateParameter('pOrigAuftragNr',ftString,pdInput, 12, NULL);
    Parameters.CreateParameter('pOrigInvoiceNr',ftString,pdInput, 12, NULL);
    Parameters.CreateParameter('pOrigPosNr',ftInteger,pdInput, 12, GetPLSQLParameter (AuftragPos.PosNr));
    Parameters.CreateParameter('pPosReferenz',ftString,pdInput, 32, AuftragPos.Referenz);
    Parameters.CreateParameter('pArtikel',ftString,pdInput, ArtikelNrSize, AuftragPos.ArtikelNr);
    Parameters.CreateParameter('pVPE',ftString,pdInput, 32, AuftragPos.VPE);
    Parameters.CreateParameter('pVariante',ftString,pdInput, 32, AuftragPos.Variante);

    Parameters.CreateParameter('pMenge',ftInteger,pdInput, 12, GetPLSQLParameter (AuftragPos.SollMenge));

    Parameters.CreateParameter('pRetoureArt',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRetoureCode',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRetoureGrund',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pVersender',ftString,pdInput, 32, AuftragPos.KommHinweis);
    Parameters.CreateParameter('pAgreement',ftString,pdInput, ArtikelNrSize, AuftragPos.BestellArtikelNr);
    Parameters.CreateParameter('pHinweis',ftString,pdInput, ArtikelNrSize, AuftragPos.KundenArtikelNr);

    Parameters.CreateParameter('oRefAvisPos',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    RefPos := StoredProcedure.Parameters.ParamValues ['oRefAvisPos'];
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: AcceptRetourenAvis
//* Author       : Stefan Graf
//* Datum        : 08.05.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function AcceptRetourenAvis (const RefKopf : Integer) : Integer;
var
  dbres   : Integer;
  dt              : TDateTime;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_RETOURE.ACCEPT_RETOUREN_AVIS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pRefAvis',ftInteger,pdInput, 12, RefKopf);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.03.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportLieferAvisKopf (const LieferAvisKopf : TLieferAvisKopf; var RefKopf : Integer) : Integer;
var
  dbres   : Integer;
  dt              : TDateTime;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LIEFERAVIS.INSERT_LIEFERAVIS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pSender',ftString,pdInput, 32, Copy ('PCD:'+LVSDatenModul.AktUser, 1, 32));
    Parameters.CreateParameter('pMandant',ftString,pdInput, 32, LieferAvisKopf.Mandant);
    Parameters.CreateParameter('pSubMandant',ftString,pdInput, 32,  LieferAvisKopf.SubMandant);
    Parameters.CreateParameter('pLager',ftString,pdInput, 32, LieferAvisKopf.Lager);
    Parameters.CreateParameter('pArt',ftString,pdInput, 32, LieferAvisKopf.AVISArt);
    Parameters.CreateParameter('pAvisNr',ftString,pdInput, 32, LieferAvisKopf.AVISNummer);

    if (Length (LieferAvisKopf.AVISDatum) = 0) then
      Parameters.CreateParameter('pAVISDatum',ftString,pdInput, 32, NULL)
    else if not TryStrToDate (LieferAvisKopf.AVISDatum, dt) then
      Parameters.CreateParameter('pAVISDatum',ftString,pdInput, 32, NULL)
    else
      Parameters.CreateParameter('pAVISDatum',ftDateTime,pdInput, 32, dt);

    Parameters.CreateParameter('pLieferantNr',ftString,pdInput, 32, LieferAvisKopf.LieferentNr);
    Parameters.CreateParameter('pLieferantName',ftString,pdInput, 32, LieferAvisKopf.Lieferent);
    Parameters.CreateParameter('pLieferNr',ftString,pdInput, 32, LieferAvisKopf.LieferNr);

    if (Length (LieferAvisKopf.LieferDatum) = 0) then
      Parameters.CreateParameter('pLieferDate',ftString,pdInput, 32, NULL)
    else if not TryStrToDate (LieferAvisKopf.LieferDatum, dt) then
      Parameters.CreateParameter('pLieferDate',ftString,pdInput, 32, NULL)
    else
      Parameters.CreateParameter('pLieferDate',ftDateTime,pdInput, 32, dt);

    Parameters.CreateParameter('pOptions',ftString,pdInput, 32, NULL);

    Parameters.CreateParameter('pRefWEBereich',ftString,pdInput, 8, NULL);

    Parameters.CreateParameter('oRefAvis',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    RefKopf   := StoredProcedure.Parameters.ParamValues ['oRefAvis'];
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.03.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportLieferAvisPos  (const RefKopf : Integer; const AvisPosNr : Integer; const AvisPos : TLieferAvisPos; var RefPos : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LIEFERAVIS.INSERT_LIEFERAVIS_POS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAvis',ftInteger,pdInput, 12, RefKopf);

    Parameters.CreateParameter('pPosNr',ftInteger,pdInput, 12, GetPLSQLParameter (AvisPosNr));

    Parameters.CreateParameter('pAuftragNr',ftString,pdInput, AuftragNrSize, NULL);
    Parameters.CreateParameter('pAuftragPos',ftInteger,pdInput, 12, NULL);
    Parameters.CreateParameter('pBestellNr',ftString,pdInput, 12, AvisPos.BestellNr);
    Parameters.CreateParameter('pBestellPos',ftInteger,pdInput, 12, AvisPos.BestellPosNr);
    Parameters.CreateParameter('pArtikel',ftString,pdInput, ArtikelNrSize, AvisPos.ArtikelNr);
    Parameters.CreateParameter('pVariante',ftString,pdInput, 32, AvisPos.Variante);
    Parameters.CreateParameter('pVPE',ftString,pdInput, 32, AvisPos.VPE);

    Parameters.CreateParameter('pMenge',ftInteger,pdInput, 12, GetPLSQLParameter (AvisPos.Menge));

    Parameters.CreateParameter('pUnits',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pGewicht',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pMHD',ftString,pdInput, 32, AvisPos.MHD);
    Parameters.CreateParameter('pCharge',ftString,pdInput, 32, AvisPos.Charge);
    Parameters.CreateParameter('pLTName',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pNVENr',ftString,pdInput, 32, AvisPos.NVENr);
    Parameters.CreateParameter('pBestandID',ftString,pdInput, 32, NULL);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    RefPos := StoredProcedure.Parameters.ParamValues ['oRef'];
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportOMSOrderKopf (const AuftragKopf : TAuftragKopf; var RefOrder : Integer) : Integer;
var
  dbres   : Integer;
  refmand  : Integer;
  StoredProcedure : TADOStoredProc;
  selquery  : TADOQuery;
begin
  dbres := 0;

  RefOrder := -1;

  refmand := -1;

  selquery  := TADOQuery.Create (Nil);

  try
    selquery.LockType := ltReadOnly;
    selquery.Connection  := LVSDatenModul.MainADOConnection;

    selquery.SQL.Add ('select REF from V_MANDANT where STATUS in (''ANG'',''AKT'') and NAME=:name');
    selquery.Parameters [0].Value := AuftragKopf.Mandant;

    selquery.Open;

    if (selquery.RecordCount = 0) then begin
      dbres := 4;
      LVSDatenModul.LastLVSErrorCode := 4;
      LVSDatenModul.LastLVSErrorText := 'Mandant unbekannt';
    end else if (selquery.RecordCount > 1) then begin
      dbres := 4;
      LVSDatenModul.LastLVSErrorCode := 4;
      LVSDatenModul.LastLVSErrorText := 'Mandant nicht eindeutig';
    end else begin
      refmand := selquery.Fields [0].AsInteger;
    end;

    selquery.Close;
  finally
    selquery.Free;
  end;

  if (dbres = 0) and (refmand > 0) then begin
    StoredProcedure := TADOStoredProc.Create (Nil);

    with StoredProcedure do begin
      ProcedureName := 'PA_OMS_ORDER.INSERT_ORDER';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

      Parameters.CreateParameter('pSender',ftString,pdInput, 32, Copy ('PCD:'+LVSDatenModul.AktUser, 1, 32));
      Parameters.CreateParameter('pSource',ftString,pdInput, 12, null);
      Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, refmand);
      Parameters.CreateParameter('pKundennr',ftString,pdInput, 32, copy (AuftragKopf.KundenNr, 1, 32));
      Parameters.CreateParameter('pSprache',ftString,pdInput, 5, AuftragKopf.Sprache);
      Parameters.CreateParameter('pAuftragNr',ftString,pdInput, AuftragNrSize, AuftragKopf.AuftragNr);
      Parameters.CreateParameter('pAufReferenz',ftString,pdInput, 32, AuftragKopf.AuftragReferenz);
      Parameters.CreateParameter('pBestNr',ftString,pdInput, 32, AuftragKopf.BestellNr);
      Parameters.CreateParameter('pKundenRef',ftString,pdInput, 32, null);
      Parameters.CreateParameter('pAuftragsart',ftString,pdInput, 32, AuftragKopf.Auftragsart);
      Parameters.CreateParameter('pLieferNr',ftString,pdInput, 32, AuftragKopf.LieferNr);
      Parameters.CreateParameter('pBestDate',ftString,pdInput, 32, AuftragKopf.BestellDatum);
      Parameters.CreateParameter('pLieferDate',ftString,pdInput, 32, AuftragKopf.LieferDatum);
      Parameters.CreateParameter('pAnlieferZeitVon',ftInteger,pdInput, 12, NULL);
      Parameters.CreateParameter('pAnlieferZeitBis',ftInteger,pdInput, 12, NULL);
      Parameters.CreateParameter('pSpedition',ftString,pdInput, 32, AuftragKopf.SpeditionNr);
      Parameters.CreateParameter('pLT',ftString,pdInput, 32, AuftragKopf.Ladungstraeger);
      Parameters.CreateParameter('pPackmittel',ftString,pdInput, 32, AuftragKopf.Packmittel);
      Parameters.CreateParameter('pOptions',ftString,pdInput, 128, AuftragKopf.Options);

      Parameters.CreateParameter('oRefAuf',ftInteger,pdOutput, 12, NULL);

      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

    if (dbres = 0) then begin
      RefOrder   := StoredProcedure.Parameters.ParamValues ['oRefAuf'];

      (*
      if (Length (AuftragKopf.IFCAuftragNr) > 0) then begin
        StoredProcedure.Free;

        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG_VERWALTUNG.SET_AUFTRAG_IFC_AUFTRAG_NR';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefKopf);
          Parameters.CreateParameter('pIfcAuftragNr',ftString,pdInput, 32, AuftragKopf.IFCAuftragNr);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;
      end;
      *)
    end;

    StoredProcedure.Free;
  end;

  Result := dbres;
end;

function ImportOMSOrderAdr    (const RefOrder : Integer; const AuftragAdr : TAdresse) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_OMS_ORDER.INSERT_ADRESSE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pAufRef',ftInteger,pdInput, 12, RefOrder);

    Parameters.CreateParameter('pArt',ftString,pdInput, 16, AuftragAdr.Adressart);
    Parameters.CreateParameter('pValid',ftString,pdInput, 8, AuftragAdr.Valid);

    Parameters.CreateParameter('pNummer',ftString,pdInput, 64, AuftragAdr.Nr);

    Parameters.CreateParameter('pAnrede',ftString,pdInput, 32, AuftragAdr.Anrede);
    Parameters.CreateParameter('pCompany',ftString,pdInput, 64, copy (AuftragAdr.Firma, 1, 64));
    Parameters.CreateParameter('pName1',ftString,pdInput, 64, copy (AuftragAdr.Name1, 1, 64));
    Parameters.CreateParameter('pName2',ftString,pdInput, 64, copy (AuftragAdr.Name2, 1, 64));
    Parameters.CreateParameter('pNameZusatz',ftString,pdInput, 64, copy (AuftragAdr.NameZusatz, 1, 64));
    Parameters.CreateParameter('pStrasse',ftString,pdInput, 64, copy (AuftragAdr.Strasse, 1, 64));
    Parameters.CreateParameter('pStrasse2',ftString,pdInput, 64, copy (AuftragAdr.Strasse_2, 1, 64));
    Parameters.CreateParameter('pLand',ftString,pdInput, 16, AuftragAdr.Land);
    Parameters.CreateParameter('pPlz',ftString,pdInput, 12, copy (AuftragAdr.Plz, 1, 12));
    Parameters.CreateParameter('pOrt',ftString,pdInput, 128, AuftragAdr.Ort);
    Parameters.CreateParameter('pFilialnr',ftString,pdInput, 32, AuftragAdr.FilialNr);
    Parameters.CreateParameter('pIln',ftString,pdInput, 32, AuftragAdr.ILN);
    Parameters.CreateParameter('pContact',ftString,pdInput, 64, AuftragAdr.Ansprechpartner);
    Parameters.CreateParameter('pTelefon',ftString,pdInput, 32, AuftragAdr.Telefon);
    Parameters.CreateParameter('pFax',ftString,pdInput, 32, AuftragAdr.Fax);
    Parameters.CreateParameter('pEmail',ftString,pdInput, 64, AuftragAdr.Email);

    Parameters.CreateParameter('oRefAdr',ftInteger,pdOutput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

function ImportOMSOrderPos    (const RefOrder : Integer; const AuftragPos : TAuftragPos; var RefPos : Integer) : Integer;
var
  dbres   : Integer;
  StoredProcedure : TADOStoredProc;
  aeref     : integer;
  selquery  : TADOQuery;
begin
  dbres := 0;

  selquery  := TADOQuery.Create (Nil);

  try
    selquery.LockType := ltReadOnly;
    selquery.Connection  := LVSDatenModul.MainADOConnection;

    aeref := -1;

    if (Length (AuftragPos.VariantID) > 0) then begin
      selquery.SQL.Add ('select REF from V_ARTIKEL_EINHEIT where STATUS=''AKT'' and VARIANT_ID=:id');
      selquery.Parameters [0].Value := AuftragPos.VariantID;
    end else if (Length (AuftragPos.EAN) > 0) then begin
      selquery.SQL.Add ('select REF_AR_EINHEIT from V_ARTIKEL_SUCHE where EAN_SEARCH=:ean');
      selquery.Parameters [0].Value := AuftragPos.EAN;
    end else begin
      selquery.SQL.Add ('select REF_AR_EINHEIT from V_ARTIKEL_SUCHE where ARTIKEL_NR=:ar_nr');
      selquery.Parameters [0].Value := AuftragPos.ArtikelNr;
    end;

    if (Length (selquery.SQL.Text) = 0) then begin
      dbres := 4;
      LVSDatenModul.LastLVSErrorCode := 4;
      LVSDatenModul.LastLVSErrorText := 'Artikel muss angegeben werden';
    end else begin
      selquery.Open;

      if (selquery.RecordCount = 0) then begin
        dbres := 4;
        LVSDatenModul.LastLVSErrorCode := 4;
        LVSDatenModul.LastLVSErrorText := FormatMessageText (1506, []);
      end else if (selquery.RecordCount > 1) then begin
        dbres := 4;
        LVSDatenModul.LastLVSErrorCode := 4;
        LVSDatenModul.LastLVSErrorText := FormatMessageText (1507, []);
      end else begin
        aeref := selquery.Fields [0].AsInteger;
      end;

      selquery.Close;
    end;
  finally
    selquery.Free;
  end;

  if (dbres = 0) and (aeref > 0) Then begin
    StoredProcedure := TADOStoredProc.Create (Nil);

    with StoredProcedure do begin
      ProcedureName := 'PA_OMS_ORDER.INSERT_ORDER_POS';

      Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

      Parameters.CreateParameter('pAufRef',ftInteger,pdInput, 12, RefOrder);

      if (AuftragPos.PosNr = -1) then
        Parameters.CreateParameter('pPosNr',ftInteger,pdInput, 12, NULL)
      else
        Parameters.CreateParameter('pPosNr',ftInteger,pdInput, 12, AuftragPos.PosNr);

      Parameters.CreateParameter('pPosReferenz',ftString,pdInput, 32, AuftragPos.Referenz);
      Parameters.CreateParameter('pRefAE',ftString,pdInput, 32, aeref);

      if (AuftragPos.SollMenge = -1) then
        Parameters.CreateParameter('pMenge',ftInteger,pdInput, 12, NULL)
      else
        Parameters.CreateParameter('pMenge',ftInteger,pdInput, 12, AuftragPos.SollMenge);

      Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, NULL);

      Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
      Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
    end;

    dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

    if (dbres = 0) then begin
      RefPos := StoredProcedure.Parameters.ParamValues ['oRef'];
    end;

    StoredProcedure.Free;

    (*
    if (dbres = 0) then begin
      if (AuftragPos.NettoBetrag > 0) then begin
        StoredProcedure := TADOStoredProc.Create (Nil);

        with StoredProcedure do begin
          ProcedureName := 'PA_AUFTRAG_RECHNUNG.INSERT_AUFTRAG_POS_RECHNUNG';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

          Parameters.CreateParameter('pAufPosRef',ftInteger,pdInput, 12, RefPos);
          Parameters.CreateParameter('pNettoBetrag',ftInteger,pdInput, 12, AuftragPos.NettoBetrag);
          Parameters.CreateParameter('pBruttoBetrag',ftInteger,pdInput, 12, NULL);
          Parameters.CreateParameter('pPreisEinheit',ftInteger,pdInput, 12, NULL);
          Parameters.CreateParameter('pMWST',ftInteger,pdInput, 12, NULL);

          if AuftragPos.EKVerzollung then
            Parameters.CreateParameter('pOptions',ftString,pdInput, 64, 'EK;');

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
        end;

        dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

        StoredProcedure.Free;
      end;
    end;
    *)
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function OMSOrderAccept (const RefOrder : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_OMS_ORDER.ACCEPT_ORDER';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefOrder);
    Parameters.CreateParameter('pStatus',ftString,pdInput, 8, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function AssigneOMSBranch (const RefOrder, RefBranch : Integer; const Regel : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_OMS_VERWALTUNG.ASSIGN_ORDER';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pOrderRef',ftInteger,pdInput, 12, RefOrder);
    Parameters.CreateParameter('pRefBranch',ftInteger,pdInput, 12, RefBranch);
    Parameters.CreateParameter('pRule',ftString,pdInput, 16, Regel);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

end.
