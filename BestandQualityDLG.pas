//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : BestandQualityDLG
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 12.04.2015
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/BestandQualityDLG.pas $
// $Revision: 9 $
// $Modtime: 12.01.22 16:27 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : <PERSON><PERSON><PERSON>and bzw Teilbestandes festlegen
//*****************************************************************************
unit BestandQualityDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, DB, ADODB, CompTranslate, BarCodeScanner, ExtCtrls,
  IntegerUpDown;

type
  TBestandQualityForm = class(TForm)
    OkButton: TButton;
    ADOQuery1: TADOQuery;
    AbortButton: TButton;
    ArtikelGroupBox: TGroupBox;
    GroupBox2: TGroupBox;
    Label6: TLabel;
    Label7: TLabel;
    MengeEdit: TEdit;
    CategoryComboBox: TComboBox;
    Panel1: TPanel;
    Label1: TLabel;
    StaticText1: TStaticText;
    Label2: TLabel;
    StaticText2: TStaticText;
    MHDChargePanel: TPanel;
    Label3: TLabel;
    StaticText3: TStaticText;
    Label4: TLabel;
    StaticText4: TStaticText;
    MengeUpDown: TIntegerUpDown;
    GrundComboBox: TComboBox;
    Label5: TLabel;
    procedure FormShow(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure MengeEditChange(Sender: TObject);
    procedure NumberKeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
    procedure GrundComboBoxChange(Sender: TObject);
    procedure MengeEditExit(Sender: TObject);
  private
    RefMand,
    RefSubMand : Integer;
    RefLager   : Integer;
    RefBesCat  : Integer;
    fGrundPflicht : Boolean;
    fSperrBestand : Boolean;
    fGrund        : String;
  public
    BestandReferenz : Integer;

    property Grund        : String  read fGrund;
    property GrundPflicht : Boolean read fGrundPflicht write fGrundPflicht;
  end;

implementation

uses VCLUtilitys, DatenModul, ConfigModul, SprachModul, FrontendUtils, LVSDatenInterface;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBestandQualityForm.FormShow(Sender: TObject);
var
  ref,
  dbres : Integer;
begin
  dbres := 0;

  if not (MHDChargePanel.Visible) Then begin
    ArtikelGroupBox.Height := ArtikelGroupBox.Height - MHDChargePanel.Height;
    Height := Height - MHDChargePanel.Height;
  end;

  MengeEdit.Text := '';
  StaticText1.Caption := '';
  StaticText2.Caption := '';
  StaticText3.Caption := '';
  StaticText4.Caption := '';

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select bes.REF_LAGER,bes.REF_MAND,ar.REF_SUB_MAND,bes.ARTIKEL_NR,bes.ARTIKEL_TEXT,bes.MHD,bes.CHARGE,bes.MENGE_FREI,bes.MENGE_SPERR,bes.REF_CATEGORY');
  ADOQuery1.SQL.Add ('from V_BES bes, V_ARTIKEL ar, V_ARTIKEL_EINHEIT ae where ar.REF=bes.REF_AR and ae.REF=bes.REF_AR_EINHEIT and bes.REF=:ref_bes');
  ADOQuery1.Parameters.ParamByName('ref_bes').Value := BestandReferenz;

  try
    ADOQuery1.Open;

    RefLager   := ADOQuery1.Fields [0].AsInteger;
    RefMand    := ADOQuery1.Fields [1].AsInteger;
    RefSubMand := DBGetReferenz (ADOQuery1.Fields [2]);
    RefBesCat  := DBGetReferenz (ADOQuery1.FieldByName ('REF_CATEGORY'));

    MengeUpDown.Min      := 1;

    if (ADOQuery1.FieldByName ('MENGE_SPERR').IsNull) then begin
      fSperrBestand := False;

      MengeUpDown.Max      := ADOQuery1.FieldByName ('MENGE_FREI').AsInteger;
      MengeUpDown.Position := ADOQuery1.FieldByName ('MENGE_FREI').AsInteger;
    end else begin
      fSperrBestand := True;

      MengeUpDown.Max      := ADOQuery1.FieldByName ('MENGE_SPERR').AsInteger;
      MengeUpDown.Position := ADOQuery1.FieldByName ('MENGE_SPERR').AsInteger;
    end;

    StaticText1.Caption := ADOQuery1.Fields [3].AsString;
    StaticText2.Caption := ADOQuery1.Fields [4].AsString;
    StaticText3.Caption := ADOQuery1.Fields [5].AsString;
    StaticText4.Caption := ADOQuery1.Fields [6].AsString;

    ADOQuery1.Close;

    ClearComboBoxObjects (CategoryComboBox);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF, NAME, DEFAULT_CATEGORY from V_LAGER_BESTAND_CATEGORY where STATUS=''AKT'' and USE_FOR_BES=''1'' and REF_MAND=:ref_mand');
    ADOQuery1.SQL.Add ('and (REF_LAGER=:ref_lager or (REF_LAGER is null and REF_LOCATION is null) or (REF_LAGER is null and REF_LOCATION=:ref_loc))');
    ADOQuery1.Parameters.ParamByName('ref_mand').Value := RefMand;
    ADOQuery1.Parameters.ParamByName('ref_lager').Value := RefLager;
    ADOQuery1.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    if (fSperrBestand) then
      ADOQuery1.SQL.Add ('and USE_FOR_MENGE_SPERR=''1''')
    else ADOQuery1.SQL.Add ('and USE_FOR_MENGE_FREI=''1''');

    if (RefSubMand = -1) then
      ADOQuery1.SQL.Add ('and (REF_SUB_MAND is null)')
    else begin
      ADOQuery1.SQL.Add ('and (REF_SUB_MAND is null or (REF_SUB_MAND=:ref_sub_mand))');
      ADOQuery1.Parameters.ParamByName('ref_sub_mand').Value := RefSubMand;
    end;

    //Die Standart-Categotry mit REF=0 immer als erstes
    ADOQuery1.SQL.Add ('order by REF_SUB_MAND nulls last, case when REF<=0 then 0 else null end asc nulls last, REIHENFOLGE nulls last');

    try
      ADOQuery1.Open;

      while (dbres = 0) and not (ADOQuery1.EOF) do begin
        if (CategoryComboBox.Items.IndexOf (ADOQuery1.Fields [1].AsString) = -1) then begin
          ref := ADOQuery1.Fields [0].AsInteger;

          //Die Standard-Categotry ist immer -1
          if (ref = 0) or (ADOQuery1.Fields [2].AsString = '1') then
            ref := -1;

          CategoryComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString, TComboBoxRef.Create (ref));
        end;

        ADOQuery1.Next;
      end;

      ADOQuery1.Close;

      if (GetComboBoxRef(CategoryCombobox, 0) <> -1) then
        CategoryCombobox.Items.Insert (0, 'Normal');

      if (RefBesCat = -1) then
        CategoryComboBox.ItemIndex := 0
      else begin
        CategoryComboBox.ItemIndex := FindComboboxRef (CategoryComboBox, RefBesCat);
        if (CategoryComboBox.ItemIndex = -1) then CategoryComboBox.ItemIndex := 0;
      end;
    except
      dbres := -9;
    end;
  except
    dbres := -9;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBestandQualityForm.GrundComboBoxChange(Sender: TObject);
begin
  GrundComboBox.Color := clWindow;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBestandQualityForm.NumberKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,^C,^V,'0'..'9'])  then begin
    Key := #0;
    Beep;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBestandQualityForm.MengeEditChange(Sender: TObject);
begin
  MengeEdit.Color := clWindow;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBestandQualityForm.MengeEditExit(Sender: TObject);
var
  intwert : Integer;
begin
  if (not AbortButton.Focused and (Length (MengeEdit.Text) > 0)) then begin
    if not TryStrToInt (MengeEdit.Text, intwert) then begin
      MengeEdit.Color := clRed;
      MengeEdit.SetFocus;
    end else if (intwert < MengeUpDown.Min) or (intwert > MengeUpDown.Max) then begin
      MengeEdit.Color := clRed;
      MengeEdit.SetFocus;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBestandQualityForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  intwert : Integer;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    CanClose := False;

    if (Length (MengeEdit.Text) = 0) Then begin
      MengeEdit.Color := clRed;
      MengeEdit.SetFocus
    end else if not TryStrToInt (MengeEdit.Text, intwert) then begin
      MengeEdit.Color := clRed;
      MengeEdit.SetFocus
    end else if (intwert <= 0) then begin
      MengeEdit.Color := clRed;
      MengeEdit.SetFocus
    end else if (fGrundPflicht and (Length (GrundComboBox.Text) = 0)) then begin
      GrundComboBox.Color := clRed;
      GrundComboBox.SetFocus
    end else begin
      if (GrundComboBox.ItemIndex >= 0) and Assigned (GrundComboBox.Items.Objects [GrundComboBox.ItemIndex]) and (Length (TComboboxSysTexte (GrundComboBox.Items.Objects [GrundComboBox.ItemIndex]).Definition) > 0) then
        fGrund := TComboboxSysTexte (GrundComboBox.Items.Objects [GrundComboBox.ItemIndex]).Definition+':'+GrundComboBox.Text
      else
        fGrund := GrundComboBox.Text;

      CanClose := True;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TBestandQualityForm.FormCreate(Sender: TObject);
begin
  fGrundPflicht := True;

  MHDChargePanel.Visible := (LVSConfigModul.UseMHD or LVSConfigModul.UseCharge);
  Label3.Visible := LVSConfigModul.UseMHD;
  StaticText3.Visible := LVSConfigModul.UseMHD;

  Label4.Visible := LVSConfigModul.UseCharge;
  StaticText4.Visible := LVSConfigModul.UseCharge;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, StaticText1);
    LVSSprachModul.SetNoTranslate (Self, StaticText2);
    LVSSprachModul.SetNoTranslate (Self, StaticText3);
    LVSSprachModul.SetNoTranslate (Self, StaticText4);
    LVSSprachModul.SetNoTranslate (Self, CategoryComboBox);
  {$endif}
end;

end.
