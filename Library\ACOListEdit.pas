﻿//******************************************************************************
//* Modul Name: CompTranslateEdit
//* Author    : <PERSON>
//******************************************************************************
//* $Revision: 14 $
//* $Date: 2.05.17 16:42 $
//******************************************************************************
//* Description: Editor f�r die �bersetzungstexte
//******************************************************************************
{$undef Trace}
unit ACOListEdit;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms, ACOList,
  Dialogs, StdCtrls, ComCtrls, Grids, Menus, ExtCtrls;

type
  {$ifdef UNICODE}
    PDebugChar = PWideChar;
  {$else}
    PDebugChar = PAnsiChar;
  {$endif}

  TACOListEditForm = class(TForm)
    OkButton: TButton;
    MainMenu1: TMainMenu;
    Close1: TMenuItem;
    Schliessen1: TMenuItem;
    FormComboBox: TComboBox;
    CompListBox: TListBox;
    ACOStringGrid: TStringGrid;
    AddACOButton: TButton;
    Button2: TButton;
    Button3: TButton;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    AbortButton: TButton;
    Bevel1: TBevel;
    Einfgen1: TMenuItem;
    ACOEintrag1: TMenuItem;
    AddFormACOButton: TButton;
    procedure ScanButtonClick(Sender: TObject);
    procedure Schliessen1Click(Sender: TObject);
    procedure FieldStringGridSelectCell(Sender: TObject; ACol, ARow: Integer; var CanSelect: Boolean);
    procedure Sichernals1Click(Sender: TObject);
    procedure FieldStringGridSetEditText(Sender: TObject; ACol,
      ARow: Integer; const Value: String);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure OkButtonClick(Sender: TObject);
    procedure AddACOButtonClick(Sender: TObject);
    procedure Button3Click(Sender: TObject);
    procedure Button2Click(Sender: TObject);
    procedure FormComboBoxChange(Sender: TObject);
    procedure CompListBoxDrawItem(Control: TWinControl; Index: Integer;
      Rect: TRect; State: TOwnerDrawState);
    procedure FormShow(Sender: TObject);
    procedure ACOStringGridSetEditText(Sender: TObject; ACol,
      ARow: Integer; const Value: String);
    procedure FormCreate(Sender: TObject);
    procedure CompListBoxClick(Sender: TObject);
    procedure ACOEintrag1Click(Sender: TObject);
    procedure AddFormACOButtonClick(Sender: TObject);
  private
    ChangeFlag : Boolean;
    AktForm    : String;

    fMaxID     : Integer;

    procedure SaveChanges;
    procedure CheckChanges;
  public
    TransOwner : TACOListManager;
    OwnerForm  : TComponent;
    ProjektDir : String;

    procedure DoEditor;
  end;

implementation

{$ifdef TRACE}
uses
  Trace
  ;
{$endif}

{$R *.dfm}

var
  dbgstr : String;

//**********************************************************************
//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.DoEditor;
var
  idx    : Integer;
begin
  fMaxID := 1;

  idx := 0;

  while (idx < TransOwner.ACOList.Count) do begin
    if (TransOwner.ACOList[idx].ACOID > fMaxID) then
      fMaxID := TransOwner.ACOList[idx].ACOID;

    Inc (idx);
  end;

  FormComboBox.Clear;

  FormComboBox.Items.Add ('');

  idx := 0;
  while (idx < TransOwner.FormList.Count) do begin
    if Assigned (TACOListForm (TransOwner.FormList.Items [idx]).OwnerForm) then
      FormComboBox.Items.AddObject (TACOListForm (TransOwner.FormList.Items [idx]).OwnerForm.Name, TACOListForm (TransOwner.FormList.Items [idx]).OwnerForm);

    Inc (idx);
  end;

  if Assigned (OwnerForm) then begin
    Caption   := 'AccessControl Editor f�r Formular ' + OwnerForm.Name;

    OutputDebugString ('TACOListEditForm.DoEditor');
    dbgstr := 'OwnerForm.Name:'+OwnerForm.Name;
    OutputDebugString (PDebugChar (dbgstr));

    FormComboBox.ItemIndex := FormComboBox.Items.IndexOf (OwnerForm.Name);
  end else begin
    Caption   := 'AcessControl Editor';

    FormComboBox.ItemIndex := 0;
  end;

  dbgstr := 'ItemIndex:'+IntToStr (FormComboBox.ItemIndex);
  OutputDebugString (PDebugChar (dbgstr));

  FormComboBoxChange (FormComboBox);

  dbgstr := 'ItemIndex:'+IntToStr (FormComboBox.ItemIndex);
  OutputDebugString (PDebugChar (dbgstr));

  ChangeFlag := False;

  ShowModal;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.ScanButtonClick(Sender: TObject);
begin
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.Schliessen1Click(Sender: TObject);
begin
  Close;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.FieldStringGridSelectCell(Sender: TObject; ACol, ARow: Integer; var CanSelect: Boolean);
begin
  if (ACol > 2) then
    CanSelect := True
  else CanSelect := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.Sichernals1Click(Sender: TObject);
begin
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.FieldStringGridSetEditText(Sender: TObject; ACol, ARow: Integer; const Value: String);
begin
  ChangeFlag := True;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  CheckChanges;

  CanClose := not (ChangeFlag);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.OkButtonClick(Sender: TObject);
begin
  SaveChanges;
  ModalResult := mrOk;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.AddACOButtonClick(Sender: TObject);
var
  idx     : Integer;
  id,
  maxid   : Integer;
  found   : Boolean;
  typstr,
  textstr,
  namestr : String;
  strpos  : Integer;
begin
  if (CompListBox.ItemIndex <> -1) then begin
    namestr := CompListBox.Items [CompListBox.ItemIndex];
    strpos := Pos ('|', namestr);

    if (strpos = 0) Then
      typstr := ''
    else begin
      typstr  := Copy (namestr, 1, strpos - 1);
      namestr := Copy (namestr, strpos + 1, Length (namestr) - strpos);

      strpos := Pos ('|', namestr);
      if (strpos > 0) then begin
        textstr := Copy (namestr, strpos + 1, Length (namestr) - strpos);
        namestr := Copy (namestr, 1, strpos - 1);
      end;
    end;

    with ACOStringGrid do begin
      idx := FixedRows;
      found := False;

      while (idx < RowCount) and not (found) do begin
        if (Cells [2, idx] = namestr) Then
          found := True
        else Inc (idx);
      end;

      if (found) then begin
        Selection := TGridRect(Rect(5, idx, 5, idx));
      end else begin
        ChangeFlag := True;

        if (RowCount = (FixedRows + 1)) and (Length (Cells [1, FixedRows]) = 0) Then
          idx := FixedRows
        else begin
          idx := RowCount;
          RowCount := RowCount + 1;
        end;

        Cells [1, idx] := typstr;
        Cells [2, idx] := IntToStr (fMaxID + 1);
        Cells [3, idx] := '';
        Cells [4, idx] := namestr;
        Cells [5, idx] := textstr;
        Cells [6, idx] := '';

        Inc (fMaxID);
      end;

      Selection := TGridRect(Rect(5, idx, 5, idx));
      AddACOButton.Enabled := False;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.AddFormACOButtonClick(Sender: TObject);
var
  idx   : Integer;
  found : Boolean;
begin
  with ACOStringGrid do begin
    idx := FixedRows;
    found := False;

    while (idx < RowCount) and not (found) do begin
      if (Cells [2, idx] = FormComboBox.Text) Then
        found := True
      else Inc (idx);
    end;

    if (found) then begin
      Selection := TGridRect(Rect(5, idx, 5, idx));
    end else begin
      ChangeFlag := True;

      if (RowCount = (FixedRows + 1)) and (Length (Cells [1, FixedRows]) = 0) Then
        idx := FixedRows
      else begin
        idx := RowCount;
        RowCount := RowCount + 1;
      end;

      Cells [1, idx] := 'TForm';
      Cells [2, idx] := IntToStr (fMaxID + 1);
      Cells [4, idx] := FormComboBox.Text;
      Cells [5, idx] := FormComboBox.Text;
      Cells [6, idx] := '';

      Inc (fMaxID);
    end;

    Selection := TGridRect(Rect(5, idx, 5, idx));
    AddACOButton.Enabled := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.Button3Click(Sender: TObject);
begin
  SaveChanges;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.SaveChanges;
var
  i, id : Integer;
  idx   : Integer;
  entry : TACOListEntry;
begin
{$ifdef TRACE}
  ProcedureStart ('TACOListEditForm.SaveChanges');
{$endif}

  for i:=ACOStringGrid.FixedRows to (ACOStringGrid.RowCount - 1) do begin
    if (Length (ACOStringGrid.Cells [2, i]) = 0) then
      id := -1
    else if not (TryStrToInt (ACOStringGrid.Cells [2, i], id)) then
      id := -1;

    {$ifdef TRACE}
      TraceString ('id='+IntToStr (idx));
    {$endif}

    if (id > 0) then
      idx := TransOwner.ACOList.FindACOIndex (id)
    else
      idx := TransOwner.ACOList.FindACOIndex (FormComboBox.Text, ACOStringGrid.Cells [4, i]);

    if (idx = -1) then begin
      entry := TACOListEntry.Create;

      entry.FormName := FormComboBox.Text;
      entry.CompType := ACOStringGrid.Cells [1, i];
      entry.CompName := ACOStringGrid.Cells [4, i];
      entry.ACOText  := ACOStringGrid.Cells [5, i];
      entry.ACOID    := id;
      entry.ACOGroup := ACOStringGrid.Cells [3, i];
      entry.ComponentPath := ACOStringGrid.Cells [6, i];

      entry.ACOName  := entry.FormName+'.'+entry.CompName;

      TransOwner.ACOList.Add (entry);
    end else if (id > 0) then begin
      entry := TransOwner.ACOList [idx];

      entry.FormName := FormComboBox.Text;
      entry.ACOName  := entry.FormName+'.'+entry.CompName;

      entry.CompName := ACOStringGrid.Cells [4, i];
      entry.ACOText  := ACOStringGrid.Cells [5, i];
      entry.ACOGroup := ACOStringGrid.Cells [3, i];
      entry.ComponentPath := ACOStringGrid.Cells [6, i];

      TransOwner.ACOList [idx] := entry;
    end else begin
      entry := TransOwner.ACOList [idx];

      entry.ACOText  := ACOStringGrid.Cells [5, i];
      entry.ACOGroup := ACOStringGrid.Cells [3, i];
      entry.ComponentPath := ACOStringGrid.Cells [6, i];

      TransOwner.ACOList [idx] := entry;
    end;
  end;

  ChangeFlag := False;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.Button2Click(Sender: TObject);
begin
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.CheckChanges;
var
  res : Integer;
  dbgstr : String;
begin
  OutputDebugString ('TACOListEditForm.CheckChanges 1');
  dbgstr := 'ChangeFlag:'+IntToStr (Ord (ChangeFlag));
  OutputDebugString (PDebugChar (@dbgstr[1]));

  if (ChangeFlag) Then begin
    res := MessageDlg('�nderungen �bernehmen', mtConfirmation, [mbYes,mbNo,mbCancel], 0);

    if (res = mrYes) Then
      SaveChanges
    else if (res = mrNo) then ChangeFlag := False;
  end;

  dbgstr := 'ChangeFlag:'+IntToStr (Ord (ChangeFlag));
  OutputDebugString (PDebugChar (@dbgstr[1]));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEditForm.FormComboBoxChange(Sender: TObject);
var
  i,
  idx  : Integer;
  form : TForm;
begin
  OutputDebugString ('TACOListEditForm.FormComboBoxChange 1');
  dbgstr := 'ChangeFlag:'+IntToStr (Ord (ChangeFlag));
  OutputDebugString (PDebugChar (@dbgstr[1]));

  CheckChanges;

  OutputDebugString ('TACOListEditForm.FormComboBoxChange 1');
  dbgstr := 'ChangeFlag:'+IntToStr (Ord (ChangeFlag));
  OutputDebugString (PDebugChar (@dbgstr[1]));

  if (ChangeFlag) Then
    FormComboBox.ItemIndex := FormComboBox.Items.IndexOf(AktForm)
  else begin
    form := TForm (FormComboBox.Items.Objects [FormComboBox.ItemIndex]);
    AktForm := FormComboBox.Text;

    idx := ACOStringGrid.FixedRows;

    CompListBox.Clear;

    if Assigned (form) Then begin
      if (form.ComponentCount > 0) Then begin
        for i:=0 to form.ComponentCount - 1 do begin
          if form.Components [i] is TButton then begin
            CompListBox.Items.Add ('TButton|'+ form.Components [i].Name+'|'+(form.Components [i] as TButton).Caption);
          end else if form.Components [i] is TTabSheet then begin
            CompListBox.Items.Add ('TTabSheet|'+ form.Components [i].Name+'|'+(form.Components [i] as TTabSheet).Caption);
          end else if form.Components [i] is TPanel then begin
            CompListBox.Items.Add ('TPanel|'+ form.Components [i].Name+'|');
          end else if form.Components [i] is TMenuItem then begin
            if ((form.Components [i] as TMenuItem).Caption <> '-') then
              CompListBox.Items.Add ('TMenuItem|'+ form.Components [i].Name+'|'+(form.Components [i] as TMenuItem).Caption);
          end;
        end;
      end;

      if (TransOwner.ACOList.Count > 0) then begin
        for i:=0 to (TransOwner.ACOList.Count - 1) do begin
          if (TransOwner.ACOList.Items [i].FormName = form.Name) then begin
            ACOStringGrid.Cells [1, idx] := TransOwner.ACOList.Items [i].CompType;

            if (TransOwner.ACOList.Items [i].ACOID > 0) Then
              ACOStringGrid.Cells [2, idx] := IntToStr (TransOwner.ACOList.Items [i].ACOID)
            else ACOStringGrid.Cells [2, idx] := '';

            ACOStringGrid.Cells [3, idx] := TransOwner.ACOList.Items [i].ACOGroup;
            ACOStringGrid.Cells [4, idx] := TransOwner.ACOList.Items [i].CompName;
            ACOStringGrid.Cells [5, idx] := TransOwner.ACOList.Items [i].ACOText;
            ACOStringGrid.Cells [6, idx] := TransOwner.ACOList.Items [i].ComponentPath;

            Inc (idx);
          end;
        end;
      end;
    end else begin
      if (TransOwner.ACOList.Count > 0) then begin
        for i:=0 to (TransOwner.ACOList.Count - 1) do begin
          if (Length (TransOwner.ACOList.Items [i].FormName) = 0) then begin
            ACOStringGrid.Cells [1, idx] := TransOwner.ACOList.Items [i].CompType;

            if (TransOwner.ACOList.Items [i].ACOID > 0) Then
              ACOStringGrid.Cells [2, idx] := IntToStr (TransOwner.ACOList.Items [i].ACOID)
            else ACOStringGrid.Cells [2, idx] := '';

            ACOStringGrid.Cells [3, idx] := TransOwner.ACOList.Items [i].ACOGroup;
            ACOStringGrid.Cells [4, idx] := TransOwner.ACOList.Items [i].CompName;
            ACOStringGrid.Cells [5, idx] := TransOwner.ACOList.Items [i].ACOText;
            ACOStringGrid.Cells [6, idx] := TransOwner.ACOList.Items [i].ComponentPath;

            Inc (idx);
          end;
        end;
      end;
    end;

    if (idx <> ACOStringGrid.FixedRows) Then
      ACOStringGrid.RowCount := idx
    else begin
      ACOStringGrid.RowCount := ACOStringGrid.FixedRows + 1;
      ACOStringGrid.Rows [ACOStringGrid.FixedRows].Clear;
    end;

    ChangeFlag := False;
  end;
end;

procedure TACOListEditForm.CompListBoxDrawItem(Control: TWinControl;
  Index: Integer; Rect: TRect; State: TOwnerDrawState);
var
  line : String;
  strpos : Integer;
begin
  line := (Control as TListBox).Items [Index];

  with (Control as TListBox).Canvas do begin
    FillRect(Rect);

    strpos := Pos ('|', line);
    if (strpos = 0) then
      TextOut (Rect.Left, Rect.Top, line)
    else begin
      TextOut (Rect.Left, Rect.Top, Copy (line,1,strpos - 1));

      line := Copy (line,strpos + 1, Length (line) - strpos);

      strpos := Pos ('|', line);
      if (strpos = 0) Then
        TextOut (Rect.Left + 80, Rect.Top, line)
      else begin
        TextOut (Rect.Left + 80, Rect.Top, Copy (line,1,strpos - 1));
        TextOut (Rect.Left + 200, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
      end;
    end;
  end;
end;

procedure TACOListEditForm.FormShow(Sender: TObject);
begin
  ChangeFlag := False;
  ACOStringGrid.Selection := TGridRect(Rect(-1,-1,-1,-1));
  AddACOButton.Enabled := False;
end;

procedure TACOListEditForm.ACOStringGridSetEditText(Sender: TObject; ACol, ARow: Integer; const Value: String);
begin
  ChangeFlag := True;
end;

procedure TACOListEditForm.FormCreate(Sender: TObject);
begin
  ChangeFlag := False;

  ACOStringGrid.ColCount := 7;

  ACOStringGrid.Cells [1,0] := 'Type';
  ACOStringGrid.Cells [2,0] := 'ACO ID';
  ACOStringGrid.Cells [3,0] := 'ACO Gruppe';
  ACOStringGrid.Cells [4,0] := 'ACO Name';
  ACOStringGrid.Cells [5,0] := 'Beschreibung';
  ACOStringGrid.Cells [6,0] := 'Komponenten-Pfad';
end;

procedure TACOListEditForm.CompListBoxClick(Sender: TObject);
var
  idx     : Integer;
  found   : Boolean;
  typstr,
  textstr,
  namestr : String;
  strpos  : Integer;
begin
  if (CompListBox.ItemIndex <> -1) then begin
    namestr := CompListBox.Items [CompListBox.ItemIndex];
    strpos := Pos ('|', namestr);

    if (strpos = 0) Then
      typstr := ''
    else begin
      typstr  := Copy (namestr, 1, strpos - 1);
      namestr := Copy (namestr, strpos + 1, Length (namestr) - strpos);

      strpos := Pos ('|', namestr);
      if (strpos > 0) then begin
        textstr := Copy (namestr, strpos + 1, Length (namestr) - strpos);
        namestr := Copy (namestr, 1, strpos - 1);
      end;
    end;

    with ACOStringGrid do begin
      idx := FixedRows;
      found := False;

      while (idx < RowCount) and not (found) do begin
        if (Cells [2, idx] = namestr) Then
          found := True
        else
          Inc (idx);
      end;

      if (found) then begin
        if (idx > (FixedRows + 5)) then
          TopRow := idx - 5
        else
          TopRow := FixedRows;

        Selection := TGridRect(Rect(5, idx, 5, idx));
        AddACOButton.Enabled := False;
      end else begin
        Selection := TGridRect(Rect(-1,-1,-1,-1));
        AddACOButton.Enabled := True;
      end;
    end;
  end;
end;

procedure TACOListEditForm.ACOEintrag1Click(Sender: TObject);
var
  idx : Integer;
  namestr : String;
begin
  namestr := InputBox ('Neue ACO-Eintrag', 'Name des ACOs', '');

  if (Length (namestr) > 0) Then begin
    ChangeFlag := True;

    with ACOStringGrid do begin
      if (RowCount = (FixedRows + 1)) and (Length (Cells [1, FixedRows]) = 0) Then
        idx := FixedRows
      else begin
        idx := RowCount;
        RowCount := RowCount + 1;
      end;

      Cells [1, idx] := 'ACO';
      Cells [2, idx] := IntToStr (fMaxID + 1);
      Cells [4, idx] := namestr;
      Cells [5, idx] := '';
      Cells [6, idx] := '';

      Inc (fMaxID);

      Selection := TGridRect(Rect(5, idx, 5, idx));
      Row := idx;
    end;
  end;
end;

initialization
{$ifdef TRACE}
  OpenTrace ('c:\temp\ACOListEdit.trc');
{$endif}

finalization
{$ifdef TRACE}
  CloseTrace;
{$endif}

end.
