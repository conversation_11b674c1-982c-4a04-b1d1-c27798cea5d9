object EditAuftragAddressForm: TEditAuftragAddressForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Adresse '#228'ndern'
  ClientHeight = 523
  ClientWidth = 603
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    603
    523)
  TextHeight = 13
  object Label4: TLabel
    Left = 8
    Top = 24
    Width = 51
    Height = 13
    Caption = 'Auftragnr.'
  end
  object AuftragLabel: TLabel
    Left = 80
    Top = 24
    Width = 73
    Height = 13
    Caption = 'AuftragLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label5: TLabel
    Left = 8
    Top = 8
    Width = 44
    Height = 13
    Caption = 'Kundenr.'
  end
  object KdNrLabel: TLabel
    Left = 80
    Top = 8
    Width = 56
    Height = 13
    Caption = 'KdNrLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Bevel2: TBevel
    Left = 8
    Top = 48
    Width = 587
    Height = 11
    Shape = bsTopLine
  end
  object AdresseGroupBox1: TGroupBox
    Left = 8
    Top = 144
    Width = 586
    Height = 342
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Adresse'
    TabOrder = 0
    DesignSize = (
      586
      342)
    object StrasseLabel: TLabel
      Left = 8
      Top = 190
      Width = 81
      Height = 13
      Caption = 'Strasse, Hausnr.'
    end
    object LandLabel: TLabel
      Left = 456
      Top = 244
      Width = 23
      Height = 13
      Caption = 'Land'
    end
    object OrtLabel: TLabel
      Left = 112
      Top = 244
      Width = 16
      Height = 13
      Caption = 'Ort'
    end
    object PLZLabel: TLabel
      Left = 8
      Top = 244
      Width = 54
      Height = 13
      Caption = 'Postleitzahl'
    end
    object Label1: TLabel
      Left = 8
      Top = 52
      Width = 84
      Height = 13
      Caption = 'Name, erste Zeile'
    end
    object Bevel1: TBevel
      Left = 6
      Top = 44
      Width = 574
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label2: TLabel
      Left = 8
      Top = 96
      Width = 64
      Height = 13
      Caption = 'Adresszusatz'
    end
    object Label3: TLabel
      Left = 296
      Top = 52
      Width = 90
      Height = 13
      Caption = 'Name, zweite Zeile'
    end
    object Label6: TLabel
      Left = 296
      Top = 190
      Width = 71
      Height = 13
      Caption = 'Strasse Zusatz'
    end
    object Bevel3: TBevel
      Left = 6
      Top = 290
      Width = 574
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label7: TLabel
      Left = 8
      Top = 296
      Width = 18
      Height = 13
      Caption = 'Mail'
    end
    object Label8: TLabel
      Left = 352
      Top = 296
      Width = 36
      Height = 13
      Caption = 'Telefon'
    end
    object Label9: TLabel
      Left = 406
      Top = 96
      Width = 16
      Height = 13
      Caption = 'ILN'
    end
    object Label10: TLabel
      Left = 296
      Top = 96
      Width = 26
      Height = 13
      Caption = 'Filiale'
    end
    object Bevel4: TBevel
      Left = 6
      Top = 183
      Width = 574
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label11: TLabel
      Left = 503
      Top = 96
      Width = 55
      Height = 13
      Caption = 'Packetshop'
    end
    object Label12: TLabel
      Left = 391
      Top = 244
      Width = 26
      Height = 13
      Caption = 'Staat'
    end
    object Label13: TLabel
      Left = 230
      Top = 190
      Width = 15
      Height = 13
      Caption = 'Nr.'
    end
    object Label14: TLabel
      Left = 476
      Top = 296
      Width = 31
      Height = 13
      Caption = 'Handy'
    end
    object Label15: TLabel
      Left = 296
      Top = 136
      Width = 64
      Height = 13
      Caption = 'UST-ID (VAT)'
    end
    object Label16: TLabel
      Left = 406
      Top = 136
      Width = 25
      Height = 13
      Caption = 'EORI'
    end
    object Bevel5: TBevel
      Left = 4
      Top = 237
      Width = 574
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object StrasseEdit: TEdit
      Left = 8
      Top = 206
      Width = 214
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 10
    end
    object OrtEdit: TEdit
      Left = 112
      Top = 260
      Width = 265
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 14
    end
    object LandComboBox: TComboBox
      Left = 456
      Top = 260
      Width = 122
      Height = 21
      Style = csDropDownList
      Anchors = [akTop, akRight]
      MaxLength = 2
      TabOrder = 16
      OnChange = LandComboBoxChange
    end
    object PLZEdit: TEdit
      Left = 8
      Top = 260
      Width = 81
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 12
      PopupMenu = PLZPopupMenu
      TabOrder = 13
    end
    object AdrArtComboBox: TComboBox
      Left = 8
      Top = 16
      Width = 214
      Height = 21
      ItemIndex = 0
      TabOrder = 0
      Text = 'Kunden-Anschrift'
      OnChange = AdrArtComboBoxChange
      Items.Strings = (
        'Kunden-Anschrift'
        'Liefer-Anschrift')
    end
    object Name1Edit: TEdit
      Left = 8
      Top = 68
      Width = 276
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 2
      Text = 'Name1Edit'
    end
    object Name2Edit: TEdit
      Left = 296
      Top = 68
      Width = 282
      Height = 21
      Anchors = [akTop, akRight]
      TabOrder = 3
      Text = 'Name2Edit'
    end
    object ZusatzEdit: TEdit
      Left = 8
      Top = 112
      Width = 276
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 4
      Text = 'ZusatzEdit'
    end
    object StrasseZusatzEdit: TEdit
      Left = 296
      Top = 206
      Width = 282
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 12
    end
    object MailEdit: TEdit
      Left = 8
      Top = 312
      Width = 314
      Height = 21
      TabOrder = 17
      Text = 'MailEdit'
    end
    object TelefonEdit: TEdit
      Left = 352
      Top = 312
      Width = 102
      Height = 21
      TabOrder = 18
      Text = '+9999999999999'
    end
    object ILNEdit: TEdit
      Left = 406
      Top = 112
      Width = 85
      Height = 21
      TabOrder = 6
      Text = 'ILNEdit'
    end
    object FilialEdit: TEdit
      Left = 296
      Top = 112
      Width = 98
      Height = 21
      TabOrder = 5
      Text = 'FilialEdit'
    end
    object ShopEdit: TEdit
      Left = 503
      Top = 112
      Width = 75
      Height = 21
      TabOrder = 7
      Text = 'ShopEdit'
    end
    object StateEdit: TEdit
      Left = 391
      Top = 260
      Width = 55
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 6
      TabOrder = 15
    end
    object AdrCheckBox: TCheckBox
      Left = 496
      Top = 18
      Width = 82
      Height = 17
      Anchors = [akTop, akRight]
      Caption = #220'berpr'#252'ft'
      TabOrder = 1
    end
    object HausNrEdit: TEdit
      Left = 230
      Top = 205
      Width = 54
      Height = 21
      TabOrder = 11
      Text = 'HausNrEdit'
    end
    object MobileEdit: TEdit
      Left = 476
      Top = 312
      Width = 102
      Height = 21
      TabOrder = 19
      Text = 'TelefonEdit'
    end
    object VATEdit: TEdit
      Left = 296
      Top = 152
      Width = 98
      Height = 21
      TabOrder = 8
      Text = 'VATEdit'
    end
    object EORIEdit: TEdit
      Left = 406
      Top = 152
      Width = 172
      Height = 21
      MaxLength = 16
      TabOrder = 9
      Text = 'EORIEdit'
    end
  end
  object Abbrechen: TButton
    Left = 519
    Top = 491
    Width = 75
    Height = 24
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 2
  end
  object OkButton: TButton
    Left = 430
    Top = 491
    Width = 75
    Height = 24
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    Default = True
    ModalResult = 1
    TabOrder = 1
  end
  object ProposalPanel: TPanel
    Left = 8
    Top = 56
    Width = 587
    Height = 64
    Anchors = [akLeft, akTop, akRight]
    BevelOuter = bvLowered
    TabOrder = 3
    object ProposalLabel: TLabel
      Left = 8
      Top = 9
      Width = 66
      Height = 13
      Caption = 'ProposalLabel'
    end
    object AdrCopyButton: TButton
      AlignWithMargins = True
      Left = 5
      Top = 33
      Width = 577
      Height = 26
      Margins.Left = 4
      Margins.Top = 4
      Margins.Right = 4
      Margins.Bottom = 4
      Align = alBottom
      Caption = 'Adresse '#252'bernehmen'
      TabOrder = 0
      OnClick = AdrCopyButtonClick
    end
  end
  object PLZPopupMenu: TPopupMenu
    OnPopup = PLZPopupMenuPopup
    Left = 176
    Top = 288
    object PLZStreetMenuItem: TMenuItem
      Caption = 'Stra'#223'en'#252'bersicht...'
      OnClick = PLZStreetMenuItemClick
    end
  end
end
