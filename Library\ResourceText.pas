unit ResourceText;

interface

const
  rsComboboxAlle = 1020;

{$J+}
const  rsYes  : string = 'Ja';
const  rsNo   : string = 'Nein';
const  rsAlle : string = 'Alle';

function GetResourceText     (const ResourceNr   : Integer) : String;
function FormatResourceText  (const ResourceNr   : Integer; Parameter : array of String) : String; overload;
function FormatResourceText  (const ResourceText : String; Parameter : array of String) : String; overload;
function FormatResourceParam (const ResourceNr   : Integer; Parameter : String) : String;
function FormatMessageText   (const MessageNr    : Integer; Parameter : array of String) : String; overload;
function FormatMessageText   (const MessageText  : String; Parameter : array of String) : String; overload;

function UpdateResourceText (const VarInstanc : String; const ResIndex : Integer) : Integer;

implementation

uses
  {$ifdef Trace}
    Trace,
  {$endif}

  {$ifdef LVS}
    SprachModul,
  {$else}
    SprachModulUnit,
  {$endif}

  CompTranslate,

  Classes,
  SysUtils;

var
  CompTranslate : TCompTranslate;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetResourceText (const ResourceNr : Integer) : String;
var
  line,
  outline   : String;
  strpos    : Integer;
  transinfo : TCompCaptions;
begin
  if not Assigned (CompTranslate) then begin
    {$ifdef LVS}
      CompTranslate := LVSSprachModul.LVSCompTranslate;
    {$else}
      CompTranslate := SprachModulForm.CompTranslate;
    {$endif}
  end;

  transinfo := CompTranslate.FindEntry (tlRestext, ResourceNr);

  if not Assigned (transinfo) then
    line := '?!?'+' (#' + IntToStr (ResourceNr)+')'
  else if (Length (transinfo.Texte [CompTranslate.Language]) > 0) Then
    line := transinfo.Texte [CompTranslate.Language]
  else begin
    if (Length (transinfo.Texte [CompTranslate.DefaultLanguage]) = 0) then
      line := ''
    else
      line := '???'+' (#' + IntToStr (ResourceNr)+')'
  end;

  if (Length (line) = 0) then
    outline := '#' + IntToStr (ResourceNr)
  else begin
    strpos := 1;
    while (strpos <= Length (line)) do begin
      if (line [strpos] = '^') then begin
        if (line [strpos + 1] = '^') then begin
          outline := outline + '$';
          Inc (strpos);
        end else begin
          Inc (strpos);

          if (UpCase (line [strpos]) = 'M') Then
            outline := outline + #13;
        end;
      end else begin
        outline := outline + line [strpos];
      end;

      Inc (strpos);
    end;
  end;

  Result := outline;

  {$ifdef Trace}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FormatResourceText (const ResourceNr : Integer; Parameter : array of String) : String;
var
  idx,
  strpos : Integer;
  line,
  outline : String;
  transinfo : TCompCaptions;
begin
  {$ifdef Trace}
    FunctionStart (TRACE_DEBUG, 'FormatResourceText');
    TraceParameter (TRACE_DEBUG, 'ResourceNr', ResourceNr);
  {$endif}

  if not Assigned (CompTranslate) then begin
    {$ifdef LVS}
      CompTranslate := LVSSprachModul.LVSCompTranslate;
    {$else}
      CompTranslate := SprachModulForm.CompTranslate;
    {$endif}
  end;

  outline := '';

  transinfo := CompTranslate.FindEntry (tlRestext, ResourceNr);

  if not Assigned (transinfo) then
    line := '?!?' + ' (#' + IntToStr (ResourceNr) + ')'
  else if (Length (transinfo.Texte [CompTranslate.Language]) > 0) Then
    line := transinfo.Texte [CompTranslate.Language]
  else begin
    if (Length (transinfo.Texte [CompTranslate.DefaultLanguage]) = 0) then
      line := ''
    else
      line := '???' + ' (#' + IntToStr (ResourceNr) + ')'
  end;

  if (Length (line) = 0) then
    outline := '#' + IntToStr (ResourceNr)
  else begin
    strpos := 1;
    while (strpos <= Length (line)) do begin
      if (line [strpos] = '$') then begin
        if (line [strpos + 1] = '$') then begin
          outline := outline + '$';
          Inc (strpos);
        end else begin
          Inc (strpos);

          if (line [strpos] >= '0') and (line [strpos] <='9') then begin
            idx := Ord (line [strpos]) - Ord ('0');

            if (idx >= 0) and (idx <= High (Parameter)) then
              outline := outline + Parameter [idx];
          end;
        end;
      end else if (line [strpos] = '^') then begin
        if (line [strpos + 1] = '^') then begin
          outline := outline + '$';
          Inc (strpos);
        end else begin
          Inc (strpos);

          if (UpCase (line [strpos]) = 'M') Then
            outline := outline + #13;
        end;
      end else begin
        outline := outline + line [strpos];
      end;

      Inc (strpos);
    end;
  end;

  Result := outline;

  {$ifdef Trace}
    FunctionStop (Result);
  {$endif}
end;

function FormatResourceText  (const ResourceText : String; Parameter : array of String) : String;
var
  idx,
  strpos : Integer;
  line,
  outline : String;
  transinfo : TCompCaptions;
begin
  {$ifdef Trace}
    FunctionStart (TRACE_DEBUG, 'FormatResourceText');
    TraceParameter (TRACE_DEBUG, 'ResourceText', ResourceText);
  {$endif}

  line := ResourceText;

  if (Length (line) = 0) then
    outline := ''
  else begin
    strpos := 1;
    while (strpos <= Length (line)) do begin
      if (line [strpos] = '$') then begin
        if (line [strpos + 1] = '$') then begin
          outline := outline + '$';
          Inc (strpos);
        end else begin
          Inc (strpos);

          if (line [strpos] >= '0') and (line [strpos] <='9') then begin
            idx := Ord (line [strpos]) - Ord ('0');

            if (idx >= 0) and (idx <= High (Parameter)) then
              outline := outline + Parameter [idx];
          end;
        end;
      end else if (line [strpos] = '^') then begin
        if (line [strpos + 1] = '^') then begin
          outline := outline + '$';
          Inc (strpos);
        end else begin
          Inc (strpos);

          if (UpCase (line [strpos]) = 'M') Then
            outline := outline + #13;
        end;
      end else begin
        outline := outline + line [strpos];
      end;

      Inc (strpos);
    end;
  end;

  Result := outline;

  {$ifdef Trace}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FormatMessageText (const MessageNr : Integer; Parameter : array of String) : String;
var
  idx,
  strpos : Integer;
  line,
  outline : String;
  transinfo : TCompCaptions;
begin
  {$ifdef Trace}
    FunctionStart (TRACE_DEBUG, 'FormatMessageText');
    TraceParameter (TRACE_DEBUG, 'MessageNr', MessageNr);
  {$endif}

  outline := '';

  if not Assigned (CompTranslate) then begin
    {$ifdef LVS}
      CompTranslate := LVSSprachModul.LVSCompTranslate;
    {$else}
      CompTranslate := SprachModulForm.CompTranslate;
    {$endif}
  end;

  transinfo := CompTranslate.FindEntry (tlMessagetext, MessageNr);

  if not Assigned (transinfo) then
    line := '?&?' + ' (#' + IntToStr (MessageNr) + ')'
  else if (Length (transinfo.Texte [CompTranslate.Language]) > 0) Then
    line := transinfo.Texte [CompTranslate.Language]
  else begin
    if (Length (transinfo.Texte [CompTranslate.DefaultLanguage]) = 0) then
      line := ''
    else
      line := '???' + ' (#' + IntToStr (MessageNr) + ')'
  end;

  if (Length (line) = 0) then
    outline := '#' + IntToStr (MessageNr)
  else begin
    strpos := 1;
    while (strpos <= Length (line)) do begin
      if (line [strpos] = '$') then begin
        if (line [strpos + 1] = '$') then begin
          outline := outline + '$';
          Inc (strpos);
        end else begin
          Inc (strpos);

          if (line [strpos] >= '0') and (line [strpos] <='9') then begin
            idx := Ord (line [strpos]) - Ord ('0');

            if (idx >= 0) and (idx <= High (Parameter)) then
              outline := outline + Parameter [idx];
          end;
        end;
      end else if (line [strpos] = '^') then begin
        if (line [strpos + 1] = '^') then begin
          outline := outline + '$';
          Inc (strpos);
        end else begin
          Inc (strpos);

          if (UpCase (line [strpos]) = 'M') Then
            outline := outline + #13;
        end;
      end else begin
        outline := outline + line [strpos];
      end;

      Inc (strpos);
    end;
  end;

  Result := outline;

  {$ifdef Trace}
    FunctionStop (Result);
  {$endif}
end;

function FormatMessageText   (const MessageText  : String; Parameter : array of String) : String; overload;
var
  idx,
  strpos : Integer;
  line,
  outline : String;
  transinfo : TCompCaptions;
begin
  {$ifdef Trace}
    FunctionStart (TRACE_DEBUG, 'FormatMessageText');
    TraceParameter (TRACE_DEBUG, 'MessageText', MessageText);
  {$endif}

  outline := '';

  line := MessageText;

  if (Length (line) = 0) then
    outline := ''
  else begin
    strpos := 1;
    while (strpos <= Length (line)) do begin
      if (line [strpos] = '$') then begin
        if (line [strpos + 1] = '$') then begin
          outline := outline + '$';
          Inc (strpos);
        end else begin
          Inc (strpos);

          if (line [strpos] >= '0') and (line [strpos] <='9') then begin
            idx := Ord (line [strpos]) - Ord ('0');

            if (idx >= 0) and (idx <= High (Parameter)) then
              outline := outline + Parameter [idx];
          end;
        end;
      end else if (line [strpos] = '^') then begin
        if (line [strpos + 1] = '^') then begin
          outline := outline + '$';
          Inc (strpos);
        end else begin
          Inc (strpos);

          if (UpCase (line [strpos]) = 'M') Then
            outline := outline + #13;
        end;
      end else begin
        outline := outline + line [strpos];
      end;

      Inc (strpos);
    end;
  end;

  Result := outline;

  {$ifdef Trace}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FormatResourceParam (const ResourceNr : Integer; Parameter : String) : String;
var
  i        : Integer;
  slist    : TStringList;
  paramstr : array of string;
begin
  {$ifdef Trace}
    FunctionStart (TRACE_DEBUG, 'FormatResourceText');
    TraceParameter (TRACE_DEBUG, 'ResourceNr', ResourceNr);
    TraceParameter (TRACE_DEBUG, 'Parameter', Parameter);
  {$endif}

  if (Pos (';', Parameter) = 0) then
    Result := FormatResourceText (ResourceNr, [Parameter])
  else begin
    slist := TStringList.Create;

    try
      slist.Delimiter := ';';
      slist.StrictDelimiter := True;

      slist.DelimitedText := Parameter;

      SetLength (paramstr, slist.Count);

      for i:= 0 to slist.Count - 1 do
        paramstr [i] := slist [i];
    finally
      slist.Free;
    end;

    Result := FormatResourceText (ResourceNr, paramstr);
  end;

  {$ifdef Trace}
    FunctionStop (Result);
  {$endif}
end;

function UpdateResourceText (const VarInstanc : String; const ResIndex : Integer) : Integer;
begin
  if (VarInstanc = 'rsYes') then
    rsYes  := GetResourceText (ResIndex)
  else if (VarInstanc = 'rsNo') then
    rsNo   := GetResourceText (ResIndex)
  else if (VarInstanc = 'rsAlle') then
    rsAlle := GetResourceText (ResIndex);

  Result := 0;
end;

initialization
  CompTranslate := Nil;

end.
