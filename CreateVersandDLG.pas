unit CreateVersandDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, DB, ADODB, ComboBoxPro;

type
  TCreateVersandForm = class(TForm)
    Label16: TLabel;
    KundenDBGrid: TDBGridPro;
    GroupBox1: TGroupBox;
    Label1: TLabel;
    Name1Edit: TEdit;
    Label3: TLabel;
    Name2Edit: TEdit;
    Label2: TLabel;
    ZusatzEdit: TEdit;
    StrasseLabel: TLabel;
    StrasseEdit: TEdit;
    Label6: TLabel;
    StrasseZusatzEdit: TEdit;
    PLZLabel: TLabel;
    PLZEdit: TEdit;
    OrtLabel: TLabel;
    OrtEdit: TEdit;
    LandLabel: TLabel;
    LandComboBox: TComboBox;
    Label4: TLabel;
    SpedComboBox: TComboBoxPro;
    KundenQuery: TADOQuery;
    KundenDataSource: TDataSource;
    Button1: TButton;
    Button2: TButton;
  private
    fRefLager : Integer;
    fRefMand  : Integer;
  public
    procedure Prepare (const RefMand, RefLager : Integer);
  end;

implementation

{$R *.dfm}

procedure TCreateVersandForm.Prepare;
begin
  fRefMand  := RefMand;
  fRefLager := RefLager;

  KundenQuery.SQL.Add ('select * from V_WARENEMPF_LIEFER_ADR where STATUS=''AKT'' and REF_MAND=:ref_mand');
  KundenQuery.SQL.Add ('and REF in (select REF_WARENEMPF from V_WARENEMPF_REL_KOMM where REF_LOCATION=(select REF_LOCATION from V_LOCATION_REL_LAGER where REF_LAGER=:ref_lager) and OPT_AUSLAGERUNG=''1'')');
  KundenQuery.Parameters.ParamByName('ref_mand').Value := fRefMand;
  KundenQuery.Parameters.ParamByName('ref_lager').Value := fRefLager;
end;

end.
