object LeergutKontoForm: TLeergutKontoForm
  Left = 400
  Top = 154
  BorderIcons = [biSystemMenu]
  Caption = #220'bersicht Lademittel-Konten'
  ClientHeight = 589
  ClientWidth = 870
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poMainFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnHide = FormHide
  OnKeyDown = FormKeyDown
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Splitter1: TSplitter
    Left = 0
    Top = 369
    Width = 870
    Height = 3
    Cursor = crVSplit
    Align = alTop
    ExplicitTop = 324
    ExplicitWidth = 224
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 870
    Height = 369
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      870
      369)
    object Label1: TLabel
      Left = 8
      Top = 5
      Width = 85
      Height = 13
      Caption = 'Lademittel-Konten'
    end
    object LeerKontoDBGrid: TDBGridPro
      Left = 8
      Top = 20
      Width = 721
      Height = 345
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = LeerKontoDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
      ReadOnly = True
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      OnDblClick = LeerKontoDBGridDblClick
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
    object NeuButton: TButton
      Left = 741
      Top = 20
      Width = 121
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Neu...'
      TabOrder = 1
      OnClick = NeuButtonClick
    end
    object PrintKontoButton: TButton
      Left = 741
      Top = 309
      Width = 121
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Konto drucken...'
      TabOrder = 2
      OnClick = PrintKontoButtonClick
    end
    object DelButton: TButton
      Left = 741
      Top = 111
      Width = 121
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'L'#246'schen...'
      TabOrder = 3
      OnClick = DelButtonClick
    end
    object PrintBerichtButton: TButton
      Left = 741
      Top = 340
      Width = 121
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Tagsbericht drucken...'
      TabOrder = 4
      OnClick = PrintBerichtButtonClick
    end
    object EditButton: TButton
      Left = 741
      Top = 55
      Width = 121
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Bearbeiten...'
      TabOrder = 5
      OnClick = LeerKontoDBGridDblClick
    end
    object BalanceKontoButton: TButton
      Left = 741
      Top = 262
      Width = 121
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Konto ausgleichen...'
      TabOrder = 6
      OnClick = BalanceKontoButtonClick
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 372
    Width = 870
    Height = 176
    Align = alClient
    BevelOuter = bvNone
    Caption = 'Panel2'
    TabOrder = 1
    DesignSize = (
      870
      176)
    object Label2: TLabel
      Left = 8
      Top = -342
      Width = 96
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Lademittel-Best'#228'nde'
    end
    object Label3: TLabel
      Left = 8
      Top = 9
      Width = 39
      Height = 13
      Caption = 'Bestand'
    end
    object LeerBesDBGrid: TDBGridPro
      Left = 8
      Top = 24
      Width = 721
      Height = 145
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = LeerBestandDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
      ReadOnly = True
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
    object KorrekturButton: TButton
      Left = 741
      Top = 113
      Width = 121
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Korrekturbuchung...'
      TabOrder = 1
      OnClick = KorrekturButtonClick
    end
    object BewButton: TButton
      Left = 741
      Top = 24
      Width = 121
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Bewegungen...'
      TabOrder = 2
      OnClick = BewButtonClick
    end
    object TauschButton: TButton
      Left = 741
      Top = 144
      Width = 121
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Tauschbuchung...'
      TabOrder = 3
      OnClick = TauschButtonClick
    end
  end
  object Panel3: TPanel
    Left = 0
    Top = 548
    Width = 870
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      870
      41)
    object Bevel1: TBevel
      Left = 8
      Top = 2
      Width = 854
      Height = 4
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object CloseButton: TButton
      Left = 772
      Top = 10
      Width = 90
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Ok'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
  end
  object LeerKontoADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 592
    Top = 48
  end
  object LeerKontoDataSource: TDataSource
    DataSet = LeerKontoADOQuery
    OnDataChange = LeerKontoDataSourceDataChange
    Left = 560
    Top = 48
  end
  object ACOListForm1: TACOListForm
    Left = 512
    Top = 48
  end
  object ADOQuery3: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 792
    Top = 160
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 176
    Top = 392
  end
  object DataSource2: TDataSource
    DataSet = ADOQuery2
    Left = 136
    Top = 392
  end
  object LeerBestandQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 224
    Top = 392
  end
  object LeerBestandDataSource: TDataSource
    DataSet = LeerBestandQuery
    Left = 136
    Top = 392
  end
end
