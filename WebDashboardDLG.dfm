object WebDashboardForm: TWebDashboardForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Lager Dashboard'
  ClientHeight = 907
  ClientWidth = 1664
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnResize = FormResize
  TextHeight = 13
  object WebBrowser: TAdvWebBrowser
    Left = 0
    Top = 0
    Width = 1664
    Height = 907
    Align = alClient
    ParentDoubleBuffered = False
    DoubleBuffered = True
    TabOrder = 0
    Settings.EnableContextMenu = True
    Settings.EnableShowDebugConsole = True
    Settings.EnableAcceleratorKeys = True
    Settings.AllowExternalDrop = True
    Settings.UsePopupMenuAsContextMenu = False
    ExplicitLeft = 528
    ExplicitTop = 464
    ExplicitWidth = 500
    ExplicitHeight = 350
  end
  object WebBrowserPopupMenu: TPopupMenu
    Left = 272
    Top = 344
    object WindowModeMenuItem: TMenuItem
      Caption = 'Im Festern darstellen...'
      OnClick = WindowModeMenuItemClick
    end
    object FullscreenModeMenuItem: TMenuItem
      Caption = 'Als Fullscreen darstellen...'
      OnClick = FullscreenModeMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object CloseMenuItem: TMenuItem
      Caption = 'Schlie'#223'en'
      OnClick = CloseMenuItemClick
    end
  end
end
