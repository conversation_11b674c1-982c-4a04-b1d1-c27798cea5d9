unit EditEmpfArtikelDatenDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, ExtCtrls, DB, ADODB;

type
  TEditEmpfArtikelDatenDLGForm = class(TForm)
    EmpfPanel: TPanel;
    EmpfKopfPanel: TPanel;
    Label12: TLabel;
    Bevel1: TBevel;
    EmpfNameLabel: TLabel;
    EmpfNrEdit: TEdit;
    ShowEmpfGridButton: TButton;
    EmpfGridPanel: TPanel;
    Label1: TLabel;
    AllEmpfCheckBox: TCheckBox;
    KundenDBGrid: TDBGridPro;
    GroupBox1: TGroupBox;
    FussPanel: TPanel;
    OkButton: TButton;
    AbortButton: TButton;
    KundenQuery: TADOQuery;
    KundenDataSource: TDataSource;
    ArtikelNrEdit: TEdit;
    ArtikelEinheitEdit: TEdit;
    ArtikelTextEdit: TEdit;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    Panel1: TPanel;
    Label5: TLabel;
    ArtikelLabel: TLabel;
    procedure ShowEmpfGridButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure KundenDataSourceDataChange(Sender: TObject; Field: TField);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
    fRef        : Integer;
    fRefAR      : Integer;
    fRefMand    : Integer;
    fRefSubMand : Integer;
    fRefEmpf    : Integer;

    procedure UpdateKundenQuery;
  public
    procedure Prepare (const Ref : Integer); overload;
    procedure Prepare (const RefMand, RefSubMand, RefAR : Integer); overload;
  end;

implementation

{$R *.dfm}

uses
  GraphUtil, VCLUtilitys, DatenModul, ConfigModul, ResourceText,
  DBGridUtilModule;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditEmpfArtikelDatenDLGForm.Prepare (const Ref : Integer);
var
  query : TADOQuery;
begin
  fRef := Ref;

  if (fRef > 0) then begin
    query := TADOQuery.Create (Self);

    try
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select ar.REF_MAND, ar.REF_SUB_MAND, ar.ARTIKEL_NR, ar.ARTIKEL_TEXT, rel.* from V_ARTIKEL_REL_WARENEMPF rel, V_ARTIKEL ar where rel.REF=:ref and ar.REF=rel.REF_AR');
      query.Parameters [0].Value := fRef;

      query.Open;

      fRefAR      := query.FieldByName ('REF_AR').AsInteger;
      fRefMand    := query.FieldByName ('REF_MAND').AsInteger;
      fRefSubMand := query.FieldByName ('REF_SUB_MAND').AsInteger;

      ArtikelLabel.Caption  := query.FieldByName ('ARTIKEL_NR').AsString + ' / ' + query.FieldByName ('ARTIKEL_TEXT').AsString;
      EmpfNameLabel.Caption := query.FieldByName ('KUNDEN_KURZ_BEZEICHNUNG').AsString;

      EmpfNrEdit.Text := query.FieldByName ('KUNDEN_NR').AsString;

      ArtikelNrEdit.Text := query.FieldByName ('KUNDEN_ARTIKEL_NR').AsString;
      ArtikelEinheitEdit.Text := query.FieldByName ('KUNDEN_ARTIKEL_EINHEIT').AsString;
      ArtikelTextEdit.Text := query.FieldByName ('KUNDEN_ARTIKEL_TEXT').AsString;

      query.Close;
    finally
      query.Free;
    end;

    EmpfNrEdit.Enabled := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditEmpfArtikelDatenDLGForm.Prepare (const RefMand, RefSubMand, RefAR : Integer);
var
  query : TADOQuery;
begin
  fRefAR := RefAR;

  if (fRefAR > 0) then begin
    query := TADOQuery.Create (Self);

    try
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select ar.REF_MAND, ar.REF_SUB_MAND, ar.ARTIKEL_NR, ar.ARTIKEL_TEXT from V_ARTIKEL ar where ar.REF=:ref');
      query.Parameters [0].Value := fRefAR;

      query.Open;

      fRefMand    := query.FieldByName ('REF_MAND').AsInteger;
      fRefSubMand := query.FieldByName ('REF_SUB_MAND').AsInteger;

      ArtikelLabel.Caption := query.FieldByName ('ARTIKEL_NR').AsString + ' / ' + query.FieldByName ('ARTIKEL_TEXT').AsString;

      query.Close;
    finally
      query.Free;
    end;
  end else begin
    fRefMand := RefMand;
    fRefMand := RefSubMand;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditEmpfArtikelDatenDLGForm.UpdateKundenQuery;
begin
  KundenQuery.Close;

  KundenQuery.SQL.Clear;

  KundenQuery.SQL.Add ('select * from V_WARENEMPF where STATUS=''AKT'' and REF_MAND=:ref_mand and KUNDEN_NR is not null');
  KundenQuery.Parameters.ParamByName('ref_mand').Value := fRefMand;

  with KundenDBGrid do begin
    if (SortColumns [0].ColumnIndex = -1) then
      SetSortColumn (0, 'KUNDEN_NR');
  end;

  try
    KundenQuery.Open;
  except
  end;

  KundenDBGrid.SetColumnVisible ('STATUS', False);
  KundenDBGrid.SetColumnVisible ('MANDANT', False);
  KundenDBGrid.SetColumnVisible ('ANZAHL_KOPIEN_LS', False);
  KundenDBGrid.SetColumnVisible ('ANNAHMEZEIT_VON', False);
  KundenDBGrid.SetColumnVisible ('ANNAHMEZEIT_BIS', False);
  KundenDBGrid.SetColumnVisible ('LIEFERSCHEINTEXT', False);
  KundenDBGrid.SetColumnVisible ('LT_BELADUNG', False);
  KundenDBGrid.SetColumnVisible ('LT_BELADUNG', False);
  KundenDBGrid.SetColumnVisible ('SELBSTABHOLER', False);
  KundenDBGrid.SetColumnVisible ('LT_NAME', False);
  KundenDBGrid.SetColumnVisible ('LT_EDI_CODE', False);
  KundenDBGrid.SetColumnVisible ('PFAND_ABGABE', False);
  KundenDBGrid.SetColumnVisible ('AVIS_OPTIONS', False);
  KundenDBGrid.SetColumnVisible ('OPT_FILIALE', False);
  KundenDBGrid.SetColumnVisible ('MAIL_ADDR_AVIS', False);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditEmpfArtikelDatenDLGForm.FormClose(Sender: TObject; var Action: TCloseAction);
var
  h : Integer;
begin
  KundenQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if (EmpfGridPanel.Visible) then begin
    EmpfGridPanel.Visible := False;

    h := EmpfGridPanel.Height;

    EmpfPanel.Height := EmpfPanel.Height - h;
    Height := Height - h;
  end;

  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditEmpfArtikelDatenDLGForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := False;

    if (fRefEmpf <= 0) then
    else
      CanClose := True;

    if (CanClose) then begin
      
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditEmpfArtikelDatenDLGForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  fRef     := -1;
  fRefAR   := -1;
  fRefMand := -1;

  EmpfNameLabel.Caption := '';

  EmpfNrEdit.Text         := '';
  ArtikelNrEdit.Text      := '';
  ArtikelEinheitEdit.Text := '';
  ArtikelTextEdit.Text    := '';

  EmpfGridPanel.Color := GetShadowColor (EmpfPanel.Color, -25);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditEmpfArtikelDatenDLGForm.FormShow(Sender: TObject);
var
  h : Integer;
begin
  if EmpfGridPanel.Visible then begin
    ShowEmpfGridButton.Caption := GetResourceText (1175);
  end else begin
    h := EmpfGridPanel.Height;
    EmpfPanel.Height := EmpfPanel.Height - h;
    Height := Height - h;

    ShowEmpfGridButton.Caption := GetResourceText (1176);
  end;
end;

procedure TEditEmpfArtikelDatenDLGForm.KundenDataSourceDataChange(Sender: TObject; Field: TField);
begin
  if (EmpfGridPanel.Visible) then begin
    if KundenQuery.Active and (KundenQuery.RecNo > 0) then begin
      if (KundenDBGrid.Enabled) then begin
        fRefEmpf    := KundenQuery.FieldByName('REF').AsInteger;

        EmpfNrEdit.Text := KundenQuery.FieldByName('KUNDEN_NR').AsString;
        EmpfNameLabel.Caption := KundenQuery.FieldByName ('KURZ_BEZEICHNUNG').AsString + ' / ' + KundenQuery.FieldByName ('ORT').AsString;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditEmpfArtikelDatenDLGForm.ShowEmpfGridButtonClick(Sender: TObject);
var
  h : Integer;
begin
  if (EmpfGridPanel.Visible) then begin
    KundenQuery.Close;

    EmpfGridPanel.Visible := False;

    h := EmpfGridPanel.Height;

    EmpfPanel.Height := EmpfPanel.Height - h;
    Height := Height - h;

    ShowEmpfGridButton.Caption := GetResourceText (1176);

    if EmpfNrEdit.Enabled then
      EmpfNrEdit.SetFocus;
  end else begin
    Height := Height + EmpfGridPanel.Height;
    EmpfPanel.Height := EmpfPanel.Height + EmpfGridPanel.Height;

    EmpfGridPanel.Visible := True;

    ShowEmpfGridButton.Caption := GetResourceText (1175);

    UpdateKundenQuery;

    if KundenDBGrid.Enabled then
      KundenDBGrid.SetFocus;
  end;
end;

end.
