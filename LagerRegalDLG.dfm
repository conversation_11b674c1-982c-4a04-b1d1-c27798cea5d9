object LagerRegalForm: TLagerRegalForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Regaldaten'
  ClientHeight = 473
  ClientWidth = 758
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    758
    473)
  PixelsPerInch = 96
  TextHeight = 13
  object RegalDBGrid: TDBGridPro
    Left = 8
    Top = 8
    Width = 743
    Height = 195
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = RegalDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object GroupBox1: TGroupBox
    Left = 7
    Top = 209
    Width = 743
    Height = 210
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Regal Daten'
    TabOrder = 1
    DesignSize = (
      743
      210)
    object Label5: TLabel
      Left = 16
      Top = 32
      Width = 60
      Height = 13
      Caption = 'Bezeichnung'
    end
    object ApplyButton: TButton
      Left = 578
      Top = 177
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      TabOrder = 0
      OnClick = ApplyButtonClick
    end
    object QuashButton: TButton
      Left = 659
      Top = 177
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Verwerfen'
      TabOrder = 1
      OnClick = QuashButtonClick
    end
    object DescEdit: TEdit
      Left = 16
      Top = 48
      Width = 305
      Height = 21
      MaxLength = 32
      TabOrder = 2
      Text = 'DescEdit'
      OnChange = DatenChange
    end
    object GroupBox2: TGroupBox
      Left = 16
      Top = 88
      Width = 718
      Height = 73
      Caption = 'Zul'#228'ssige Regalbelastung (ab Ebene 1)'
      TabOrder = 3
      object Label1: TLabel
        Left = 16
        Top = 20
        Width = 112
        Height = 13
        Caption = 'Maximale Last pro Fach'
      end
      object Label3: TLabel
        Left = 144
        Top = 39
        Width = 11
        Height = 13
        Caption = 'kg'
      end
      object Label6: TLabel
        Left = 184
        Top = 20
        Width = 119
        Height = 13
        Caption = 'Maximale Last pro Ebene'
      end
      object Label7: TLabel
        Left = 311
        Top = 39
        Width = 11
        Height = 13
        Caption = 'kg'
      end
      object Label2: TLabel
        Left = 352
        Top = 20
        Width = 109
        Height = 13
        Caption = 'Maximale Last pro Feld'
      end
      object Label4: TLabel
        Left = 480
        Top = 39
        Width = 11
        Height = 13
        Caption = 'kg'
      end
      object FachLastEdit: TEdit
        Left = 16
        Top = 36
        Width = 121
        Height = 21
        MaxLength = 6
        TabOrder = 0
        Text = 'FachLastEdit'
        OnChange = DatenChange
        OnKeyPress = IntgerEditKeyPress
      end
      object EbeneLastEdit: TEdit
        Left = 184
        Top = 36
        Width = 121
        Height = 21
        MaxLength = 6
        TabOrder = 1
        Text = 'EbeneLastEdit'
        OnChange = DatenChange
        OnKeyPress = IntgerEditKeyPress
      end
      object FeldLastEdit: TEdit
        Left = 352
        Top = 36
        Width = 121
        Height = 21
        MaxLength = 6
        TabOrder = 2
        Text = 'FeldLastEdit'
        OnChange = DatenChange
        OnKeyPress = IntgerEditKeyPress
      end
    end
  end
  object CloseButton: TButton
    Left = 676
    Top = 440
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 2
  end
  object RegalQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 168
    Top = 48
  end
  object RegalDataSource: TDataSource
    DataSet = RegalQuery
    OnDataChange = RegalDataSourceDataChange
    Left = 200
    Top = 48
  end
end
