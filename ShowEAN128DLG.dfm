object ShowEAN128Form: TShowEAN128Form
  Left = 533
  Top = 158
  BorderIcons = [biSystemMenu]
  BorderStyle = bsDialog
  Caption = 'ShowEAN128Form'
  ClientHeight = 566
  ClientWidth = 591
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poMainFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  DesignSize = (
    591
    566)
  PixelsPerInch = 96
  TextHeight = 13
  object Label7: TLabel
    Left = 8
    Top = 8
    Width = 91
    Height = 13
    Caption = 'Warenerwartungen'
  end
  object GroupBox1: TGroupBox
    Left = 8
    Top = 184
    Width = 577
    Height = 273
    Anchors = [akLeft, akTop, akRight]
    Caption = ' EAN128-Infos '
    TabOrder = 0
    object Label1: TLabel
      Left = 16
      Top = 16
      Width = 43
      Height = 13
      Caption = 'EAN (01)'
    end
    object Label2: TLabel
      Left = 16
      Top = 58
      Width = 46
      Height = 13
      Caption = 'MHD (15)'
    end
    object Label3: TLabel
      Left = 192
      Top = 58
      Width = 55
      Height = 13
      Caption = 'Charge (10)'
    end
    object Label4: TLabel
      Left = 16
      Top = 115
      Width = 43
      Height = 13
      Caption = 'NVE (00)'
    end
    object Label5: TLabel
      Left = 16
      Top = 219
      Width = 95
      Height = 13
      Caption = 'Nettogewicht (310x)'
    end
    object Label6: TLabel
      Left = 16
      Top = 169
      Width = 33
      Height = 13
      Caption = 'Menge'
    end
    object Label8: TLabel
      Left = 192
      Top = 169
      Width = 65
      Height = 13
      Caption = 'Einheiten (37)'
    end
    object Label9: TLabel
      Left = 121
      Top = 239
      Width = 12
      Height = 13
      Caption = 'kg'
    end
    object Label10: TLabel
      Left = 192
      Top = 115
      Width = 97
      Height = 13
      Caption = 'Bruttogewicht (330x)'
    end
    object Label11: TLabel
      Left = 296
      Top = 135
      Width = 12
      Height = 13
      Caption = 'kg'
    end
    object Label12: TLabel
      Left = 192
      Top = 16
      Width = 97
      Height = 13
      Caption = 'Enthaltene EAN (02)'
    end
    object Label13: TLabel
      Left = 368
      Top = 16
      Width = 76
      Height = 13
      Caption = 'Lieferant-Nr (97)'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 104
      Width = 561
      Height = 9
      Shape = bsTopLine
    end
    object Bevel2: TBevel
      Left = 8
      Top = 160
      Width = 561
      Height = 9
      Shape = bsTopLine
    end
    object EANEdit: TEdit
      Left = 16
      Top = 32
      Width = 153
      Height = 21
      TabOrder = 0
      Text = 'EANEdit'
      OnChange = ChangeInput
      OnKeyPress = EANEditKeyPress
    end
    object MHDEdit: TEdit
      Left = 16
      Top = 74
      Width = 153
      Height = 21
      TabOrder = 1
      Text = 'MHDEdit'
      OnChange = ChangeInput
      OnExit = MHDEditExit
      OnKeyPress = MHDEditKeyPress
    end
    object ChargeEdit: TEdit
      Left = 192
      Top = 74
      Width = 121
      Height = 21
      TabOrder = 2
      Text = 'ChargeEdit'
      OnChange = ChangeInput
    end
    object NVEEdit: TEdit
      Left = 16
      Top = 131
      Width = 153
      Height = 21
      TabOrder = 3
      Text = 'NVEEdit'
      OnChange = ChangeInput
    end
    object GewichtEdit: TEdit
      Left = 16
      Top = 235
      Width = 97
      Height = 21
      TabOrder = 4
      Text = 'GewichtEdit'
      OnChange = ChangeInput
      OnKeyPress = GewichtEditKeyPress
    end
    object MengeEdit: TEdit
      Left = 16
      Top = 185
      Width = 73
      Height = 21
      TabOrder = 5
      Text = '0'
      OnChange = ChangeInput
      OnExit = MengeEditExit
      OnKeyPress = MengeEditKeyPress
    end
    object EinheitEdit: TEdit
      Left = 192
      Top = 185
      Width = 121
      Height = 21
      TabStop = False
      Enabled = False
      TabOrder = 6
      Text = 'EinheitEdit'
    end
    object BruttoEdit: TEdit
      Left = 192
      Top = 131
      Width = 97
      Height = 21
      Enabled = False
      TabOrder = 7
      Text = 'BruttoEdit'
    end
    object InhaltEANEdit: TEdit
      Left = 192
      Top = 32
      Width = 121
      Height = 21
      Enabled = False
      TabOrder = 8
      Text = 'InhaltEANEdit'
    end
    object MengeUpDown: TIntegerUpDown
      Left = 89
      Top = 185
      Width = 16
      Height = 21
      Associate = MengeEdit
      Max = 10000
      TabOrder = 9
      OnChangingEx = MengeUpDownChangingEx
    end
  end
  object AbortButton: TButton
    Left = 511
    Top = 516
    Width = 75
    Height = 25
    Anchors = [akTop, akRight]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 1
  end
  object OkButton: TButton
    Left = 423
    Top = 516
    Width = 75
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'OK'
    Default = True
    ModalResult = 1
    TabOrder = 2
  end
  object StatusBar1: TStatusBar
    Left = 0
    Top = 547
    Width = 591
    Height = 19
    Panels = <
      item
        Width = 200
      end
      item
        Width = 50
      end
      item
        Width = 50
      end>
  end
  object PosStringGrid: TStringGridPro
    Left = 8
    Top = 24
    Width = 576
    Height = 153
    Anchors = [akLeft, akTop, akRight]
    ColCount = 9
    DefaultColWidth = 20
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -13
    Font.Name = 'MS Sans Serif'
    Font.Style = []
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goRowSelect]
    ParentFont = False
    TabOrder = 4
    GridStyle.OddColor = clInfoBk
    TitelTexte.Strings = (
      ''
      'Bestellnummer'
      'Artikel-Nr'
      'Artikel-Text'
      'Ausf'#252'hrung'
      'Menge Soll'
      'Gw Soll'
      'Menge Ist'
      'Gw Ist')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'MS Sans Serif'
    TitelFont.Style = [fsBold]
    ColWidths = (
      20
      91
      64
      75
      71
      70
      50
      61
      44)
  end
  object FehlerLabel: TPanel
    Left = 8
    Top = 464
    Width = 577
    Height = 33
    Caption = 'FehlerLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 5
  end
  object LFEdit: TEdit
    Left = 376
    Top = 216
    Width = 121
    Height = 21
    Enabled = False
    TabOrder = 6
    Text = 'LFEdit'
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 8
    Top = 504
  end
end
