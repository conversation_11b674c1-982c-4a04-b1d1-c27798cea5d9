unit VASInputDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TVASInputForm = class(TForm)
    VASScrollBox: TScrollBox;
    TopPanel: TPanel;
    BottomPanel: TPanel;
    AbortButton: TButton;
    OkButton: TButton;
    Bevel1: TBevel;
    Label1: TLabel;
    Label2: TLabel;
    RetoureLabel: TLabel;
    ArtikelLabel: TLabel;
    Bevel2: TBevel;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    fRefMand,
    fRefSubMand,
    fRefLager     : Integer;
    fRefWE,
    fRefWEPos     : Integer;
    fInputList    : TList;
  public
    function PrepareReturn (const RefRet : Integer; const ProcTyp, ProcStep : String; var EntryCount : Integer) : Integer;
    function PrepareReturnPos (const RefRetPos : Integer; const ProcTyp, ProcStep : String; var EntryCount : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses
  Ora, OraSmart, DatenModul, DBGridUtilModule, ConfigModul, LVSDatenInterface, FrontendUtils,
  SprachModul, ResourceText, VASInputFRM;

procedure TVASInputForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  ref,
  idx,
  menge,
  reftmepl : Integer;
  stat,
  inhalt   : String;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    idx := 0;
    res := 0;

    menge  := -1;
    inhalt := '';
    stat   := '';

    while (idx < fInputList.Count) and (res = 0) do begin
      reftmepl := TVASInputFrame (fInputList [idx]).RefTempl;

      if TVASInputFrame (fInputList [idx]).CheckboxPanel.Visible then begin
        if (TVASInputFrame (fInputList [idx]).VASCheckBox.State = cbUnchecked) then
          stat := '0'
        else if (TVASInputFrame (fInputList [idx]).VASCheckBox.State = cbChecked) then
          stat := '1'
        else
          stat := '';
      end else if TVASInputFrame (fInputList [idx]).EditPanel.Visible then begin
        if TVASInputFrame (fInputList [idx]).VASQuantityEdit.Visible then begin
          if (Length (TVASInputFrame (fInputList [idx]).VASQuantityEdit.Text) = 0) then
            menge  := -1
          else begin
            stat  := '1';
            menge := TVASInputFrame (fInputList [idx]).VASQuantityUpDown.Position;
          end;
        end;

        if TVASInputFrame (fInputList [idx]).VASEdit.Visible then begin
          inhalt := TVASInputFrame (fInputList [idx]).VASEdit.Text;

          if (Length (inhalt) > 0) then
            stat  := '1';
        end;
      end;

      res := InsertWEWorkload (fRefWE, fRefWEPos, reftmepl, -1, menge, stat, inhalt, '', '', '', ref);

      Inc (idx);
    end;

    CanClose := (res = 0);
  end;
end;

procedure TVASInputForm.FormCreate(Sender: TObject);
begin
  fInputList := TList.Create;
end;

procedure TVASInputForm.FormShow(Sender: TObject);
begin
  Label2.Visible := ArtikelLabel.Visible;
end;

function TVASInputForm.PrepareReturnPos (const RefRetPos : Integer; const ProcTyp, ProcStep : String; var EntryCount : Integer) : Integer;
var
  inptyp,
  textstr   : String;
  strlist   : TStringList;
  inpframe  : TVASInputFrame;
  query     : TSmartQuery;
  selquery  : TSmartQuery;
begin
  fRefWEPos := RefRetPos;

  EntryCount := 0;

  query := TSmartQuery.Create (Self);
  selquery := TSmartQuery.Create (Self);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    selquery.ReadOnly := True;
    selquery.Session := LVSDatenModul.OraMainSession;

    selquery.SQL.Add ('select * from V_WE_WORKLOAD where REF_WE_POS=:ref_we_pos and REF_WORKLOAD_TEMPLATE=:ref_templ');
    selquery.Params.ParamByName ('ref_we_pos').Value := RefRetPos;

    query.SQL.Add ('select we.REF as REF_WE,we.REF_MAND,we.REF_SUB_MAND,we.REF_LAGER,we.EINGANGS_NR,we.ART,wrp.ARTIKEL_NR,wrp.ARTIKEL_TEXT from V_WE_RET_POS wrp, VQ_WARENEINGANG we where we.REF=wrp.REF_WE and wrp.REF=:ref');
    query.Params.ParamByName ('ref').Value := RefRetPos;

    try
      query.Open;

      fRefWE      := query.FieldbyName ('REF_WE').AsInteger;
      fRefMand    := query.FieldbyName ('REF_MAND').AsInteger;
      fRefSubMand := DBGetReferenz (query.FieldbyName ('REF_SUB_MAND'));
      fRefLager   := query.FieldbyName ('REF_LAGER').AsInteger;

      RetoureLabel.Caption := query.FieldbyName ('EINGANGS_NR').AsString;
      ArtikelLabel.Caption := query.FieldbyName ('ARTIKEL_NR').AsString + ' / ' + query.FieldbyName ('ARTIKEL_TEXT').AsString;

      if (query.FieldbyName ('ART').AsString = 'LEER') then
        Label1.Caption := 'Leergut:'
      else if (query.FieldbyName ('ART').AsString = 'RET') then
        Label1.Caption := GetResourceText (1099)
      else
        Label1.Caption := GetResourceText (1297)+':';

      query.Close;

      query.SQL.Clear;
      query.SQL.Add ('select * from V_PCD_WORKLOAD_TEMPLATE where instr (PROCESS_STEP, :step) > 0 and STATUS=''AKT''');
      query.Params.ParamByName ('step').Value := ProcStep;

      if (fRefSubMand > 0) then begin
        query.SQL.Add ('and (REF_MAND is null or REF_MAND=:ref_mand) and (REF_SUB_MAND is null or REF_SUB_MAND=:ref_sub_mand)');
        query.Params.ParamByName ('ref_sub_mand').Value := fRefSubMand;
      end else begin
        query.SQL.Add ('and REF_SUB_MAND is null and (REF_MAND is null or REF_MAND=:ref_mand)');
      end;
      query.Params.ParamByName ('ref_mand').Value := fRefMand;

      query.SQL.Add ('and (REF_LAGER is null or REF_LAGER=:ref_lager) and (PROCESS_TYPE is null or PROCESS_TYPE=:art) order by SEQUENCE asc nulls last');
      query.Params.ParamByName ('ref_lager').Value := fRefLager;
      query.Params.ParamByName ('art').Value := ProcTyp;

      query.Open;

      while not query.Eof do begin
        if not Assigned (query.FindField ('RESULT_TYPE')) or query.FieldByName ('RESULT_TYPE').IsNull then
          inptyp := 'CHECK'
        else
          inptyp := query.FieldByName ('RESULT_TYPE').AsString;

        inpframe := TVASInputFrame.Create (Self);
        inpframe.Name := 'VASInputFrame_'+IntToStr (ComponentCount);
        inpframe.Parent := VASScrollBox;

        if not (query.FieldbyName ('INFO_TEXT').IsNull) then
          textstr := query.FieldbyName ('INFO_TEXT').AsString
        else
          textstr := query.FieldbyName ('NAME').AsString;

        inpframe.RefTempl := query.FieldbyName ('REF').AsInteger;

        if (inptyp = 'CHECK') then begin
          inpframe.CheckboxPanel.Visible := true;
          inpframe.Height := inpframe.CheckboxPanel.Height;

          inpframe.VASCheckBox.Caption := textstr;
        end else if (inptyp = 'QUANTITY') then begin
          inpframe.EditPanel.Visible := true;
          inpframe.Height := inpframe.EditPanel.Height;

          inpframe.VASEdit.Visible := false;
          inpframe.VASEditLabel.Visible := false;

          inpframe.VASQuantityLabel.Caption := textstr;
          inpframe.VASQuantityEdit.Text := '';
        end else if (inptyp = 'INPUT') then begin
          inpframe.EditPanel.Visible := true;
          inpframe.Height := inpframe.EditPanel.Height;

          inpframe.VASQuantityEdit.Visible := false;
          inpframe.VASQuantityLabel.Visible := false;
          inpframe.VASQuantityUpDown.Visible := false;

          inpframe.VASEditLabel.Caption := textstr;
          inpframe.VASEdit.Text := '';
        end else begin
          inpframe.EditPanel.Visible := true;
          inpframe.Height := inpframe.EditPanel.Height;

          strlist := TStringList.Create;

          try
            strlist.Delimiter := ';';
            strlist.StrictDelimiter := true;
            strlist.DelimitedText := textstr;

            if (strlist.Count > 1) then begin
              inpframe.VASEditLabel.Caption := strlist [0];
              inpframe.VASQuantityLabel.Caption := strlist [1];
            end else if (strlist.Count > 0) then begin
              inpframe.VASEditLabel.Caption := strlist [0];
              inpframe.VASQuantityLabel.Caption := strlist [0];
            end else begin
              inpframe.VASEditLabel.Caption := textstr;
              inpframe.VASQuantityLabel.Caption := textstr;
            end;
          finally
            strlist.Free;
          end;

          inpframe.VASEdit.Text := '';
          inpframe.VASQuantityEdit.Text := '';
        end;

        fInputList.Add (inpframe);

        selquery.Params.ParamByName ('ref_templ').Value := query.FieldbyName ('REF').AsInteger;

        selquery.Open;

        if inpframe.CheckboxPanel.Visible then begin
          if selquery.FieldByName ('CHECK_RESULT').IsNull then
            inpframe.VASCheckBox.State := cbGrayed
          else
            inpframe.VASCheckBox.Checked := selquery.FieldByName ('CHECK_RESULT').AsString = '1';
        end else begin
          inpframe.VASEdit.Text      := selquery.FieldByName ('INFO_TEXT').AsString;

          if selquery.FieldByName ('QUANTITY').IsNull then
            inpframe.VASQuantityEdit.Text := ''
          else
            inpframe.VASQuantityUpDown.Position := selquery.FieldByName ('QUANTITY').AsInteger;
        end;

        selquery.Close;

        Inc (EntryCount);

        query.Next;
      end;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;

  (*
  inpframe := TVASInputFrame.Create (Self);
  inpframe.Name := 'VASInputFrame_'+IntToStr (ComponentCount);
  inpframe.Parent := VASScrollBox;
  Inc (EntryCount);

  inpframe.VASCheckBox.Caption := 'Test 1';

  inpframe := TVASInputFrame.Create (Self);
  inpframe.Name := 'VASInputFrame_'+IntToStr (ComponentCount);
  inpframe.Parent := VASScrollBox;
  Inc (EntryCount);

  inpframe.VASCheckBox.Caption := 'Test 3';

  inpframe := TVASInputFrame.Create (Self);
  inpframe.Name := 'VASInputFrame_'+IntToStr (ComponentCount);
  inpframe.Parent := VASScrollBox;

  inpframe.VASCheckBox.Caption := 'Test 3';
  Inc (EntryCount);
  *)

  Result := 0;
end;

function TVASInputForm.PrepareReturn (const RefRet : Integer; const ProcTyp, ProcStep : String; var EntryCount : Integer) : Integer;
var
  inptyp,
  textstr   : String;
  strlist   : TStringList;
  inpframe  : TVASInputFrame;
  query     : TSmartQuery;
  selquery  : TSmartQuery;
begin
  fRefWE    := RefRet;
  fRefWEPos := -1;

  EntryCount := 0;

  ArtikelLabel.Visible := false;

  query := TSmartQuery.Create (Self);
  selquery := TSmartQuery.Create (Self);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    selquery.ReadOnly := True;
    selquery.Session := LVSDatenModul.OraMainSession;

    selquery.SQL.Add ('select * from V_WE_WORKLOAD where REF_WE_POS is null and REF_WE=:ref_we and REF_WORKLOAD_TEMPLATE=:ref_templ');
    selquery.Params.ParamByName ('ref_we').Value := RefRet;

    query.SQL.Add ('select we.REF as REF_WE,we.REF_MAND,we.REF_SUB_MAND,we.REF_LAGER,we.EINGANGS_NR,we.ART from VQ_WARENEINGANG we where we.REF=:ref');
    query.Params.ParamByName ('ref').Value := RefRet;

    try
      query.Open;

      fRefWE      := query.FieldbyName ('REF_WE').AsInteger;
      fRefMand    := query.FieldbyName ('REF_MAND').AsInteger;
      fRefSubMand := DBGetReferenz (query.FieldbyName ('REF_SUB_MAND'));
      fRefLager   := query.FieldbyName ('REF_LAGER').AsInteger;

      RetoureLabel.Caption := query.FieldbyName ('EINGANGS_NR').AsString;

      if (query.FieldbyName ('ART').AsString = 'LEER') then
        Label1.Caption := 'Leergut:'
      else if (query.FieldbyName ('ART').AsString = 'RET') then
        Label1.Caption := GetResourceText (1099)
      else
        Label1.Caption := GetResourceText (1297)+':';

      query.Close;

      query.SQL.Clear;
      query.SQL.Add ('select * from V_PCD_WORKLOAD_TEMPLATE where instr (PROCESS_STEP, :step) > 0 and STATUS=''AKT''');
      query.Params.ParamByName ('step').Value := ProcStep;

      if (fRefSubMand > 0) then begin
        query.SQL.Add ('and (REF_MAND is null or REF_MAND=:ref_mand) and (REF_SUB_MAND is null or REF_SUB_MAND=:ref_sub_mand)');
        query.Params.ParamByName ('ref_sub_mand').Value := fRefSubMand;
      end else begin
        query.SQL.Add ('and REF_SUB_MAND is null and (REF_MAND is null or REF_MAND=:ref_mand)');
      end;
      query.Params.ParamByName ('ref_mand').Value := fRefMand;

      query.SQL.Add ('and (REF_LAGER is null or REF_LAGER=:ref_lager) and (PROCESS_TYPE is null or PROCESS_TYPE=:art) order by SEQUENCE asc nulls last');
      query.Params.ParamByName ('ref_lager').Value := fRefLager;
      query.Params.ParamByName ('art').Value := ProcTyp;

      query.Open;

      while not query.Eof do begin
        inpframe := TVASInputFrame.Create (Self);
        inpframe.Name := 'VASInputFrame_'+IntToStr (ComponentCount);
        inpframe.Parent := VASScrollBox;

        inpframe.RefTempl := query.FieldbyName ('REF').AsInteger;

        if not Assigned (query.FindField ('RESULT_TYPE')) or query.FieldByName ('RESULT_TYPE').IsNull then
          inptyp := 'CHECK'
        else
          inptyp := query.FieldByName ('RESULT_TYPE').AsString;

        if not (query.FieldbyName ('INFO_TEXT').IsNull) then
          textstr := query.FieldbyName ('INFO_TEXT').AsString
        else
          textstr := query.FieldbyName ('NAME').AsString;

        if (inptyp = 'CHECK') then begin
          inpframe.CheckboxPanel.Visible := true;
          inpframe.Height := inpframe.CheckboxPanel.Height;

          inpframe.VASCheckBox.Caption := textstr;
        end else if (inptyp = 'QUANTITY') then begin
          inpframe.EditPanel.Visible := true;
          inpframe.Height := inpframe.EditPanel.Height;

          inpframe.VASEdit.Visible := false;
          inpframe.VASEditLabel.Visible := false;

          inpframe.VASQuantityEdit.Left := inpframe.VASCheckBox.Left;
          inpframe.VASQuantityLabel.Left := inpframe.VASCheckBox.Left;

          inpframe.VASQuantityLabel.Caption := textstr;
          inpframe.VASQuantityEdit.Text := '';
        end else if (inptyp = 'INPUT') then begin
          inpframe.EditPanel.Visible := true;
          inpframe.Height := inpframe.EditPanel.Height;

          inpframe.VASQuantityEdit.Visible := false;
          inpframe.VASQuantityLabel.Visible := false;
          inpframe.VASQuantityUpDown.Visible := false;

          inpframe.VASEditLabel.Caption := textstr;
          inpframe.VASEdit.Text := '';
        end else begin
          inpframe.EditPanel.Visible := true;
          inpframe.Height := inpframe.EditPanel.Height;

          strlist := TStringList.Create;

          try
            strlist.Delimiter := ';';
            strlist.StrictDelimiter := true;
            strlist.DelimitedText := textstr;

            if (strlist.Count > 1) then begin
              inpframe.VASEditLabel.Caption := strlist [0];
              inpframe.VASQuantityLabel.Caption := strlist [1];
            end else if (strlist.Count > 0) then begin
              inpframe.VASEditLabel.Caption := strlist [0];
              inpframe.VASQuantityLabel.Caption := strlist [0];
            end else begin
              inpframe.VASEditLabel.Caption := textstr;
              inpframe.VASQuantityLabel.Caption := textstr;
            end;
          finally
            strlist.Free;
          end;

          inpframe.VASEdit.Text := '';
          inpframe.VASQuantityEdit.Text := '';
        end;

        fInputList.Add (inpframe);

        selquery.Params.ParamByName ('ref_templ').Value := query.FieldbyName ('REF').AsInteger;

        selquery.Open;

        if inpframe.CheckboxPanel.Visible then begin
          if selquery.FieldByName ('CHECK_RESULT').IsNull then
            inpframe.VASCheckBox.State := cbGrayed
          else
            inpframe.VASCheckBox.Checked := selquery.FieldByName ('CHECK_RESULT').AsString = '1';
        end else begin
          inpframe.VASEdit.Text      := selquery.FieldByName ('INFO_TEXT').AsString;

          if selquery.FieldByName ('QUANTITY').IsNull then
            inpframe.VASQuantityEdit.Text := ''
          else
            inpframe.VASQuantityUpDown.Position := selquery.FieldByName ('QUANTITY').AsInteger;
        end;

        selquery.Close;

        Inc (EntryCount);

        query.Next;
      end;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;

  Result := 0;
end;

end.
