unit ChangeHistoryDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, ExtCtrls, DB, ADODB,
  Menus;

type
  TChangeHistoryForm = class(TForm)
    Panel1: TPanel;
    Panel2: TPanel;
    ChangeDBGrid: TDBGridPro;
    CloseButton: TButton;
    Label1: TLabel;
    Label2: TLabel;
    ChangeQuery: TADOQuery;
    ChangDataSource: TDataSource;
    ChangeDBGridPopupMenu: TPopupMenu;
    Drucken1: TMenuItem;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    function ChangeDBGridColumnSort(Sender: TCustomDBGridPro; const ColumnName: string): string;
    procedure Drucken1Click(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    RefLE  : Integer;
    RefLP  : Integer;
    RefBes : Integer;
    RefArEinheit : Integer;
  end;

implementation

{$R *.dfm}

Uses
  ConfigModul, DatenModul, DBGridUtilModule, LVSSecurity, PrintModul,
  SprachModul, ResourceText;

//******************************************************************************
//* Function Name: KundenLoeschen
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeHistoryForm.Drucken1Click(Sender: TObject);
var
  res    : Integer;
  errtxt : String;
begin
  res := PrintModule.PreparePreview;

  if (res = 0) then begin
    res := PrintModule.PrintReport ('', PrintModule.StdLaserPrinter.Port, '', -1, ChangeQuery.FieldByName('REF_MAND').AsInteger, ChangeQuery.FieldByName('REF_LAGER').AsInteger, '', 'BESTAND_MANIPULATION', '', ['BUCHUNGS_NR:' + ChangeQuery.FieldByName('BUCHUNGS_NR').AsString], errtxt, true);

    if (res <> 0) then
      MessageDLG(FormatMessageText (1193, [errtxt]), mtError, [mbOK], 0);

    PrintModule.BeginPreview;
  end;
end;

//******************************************************************************
//* Function Name: KundenLoeschen
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeHistoryForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  ChangeQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name: KundenLoeschen
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeHistoryForm.FormCreate(Sender: TObject);
begin
  RefLE  := -1;
  RefLP  := -1;
  RefBes := -1;
  RefArEinheit := -1;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  if Assigned (LVSSecurityModule) and Assigned (LVSSecurityModule.ACOModul) then begin
    LVSSecurityModule.ACOModul.SetBerechtigungen (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, Label1);
    LVSSprachModul.SetNoTranslate (Self, Label2);
  {$endif}
end;

//******************************************************************************
//* Function Name: KundenLoeschen
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TChangeHistoryForm.FormShow(Sender: TObject);
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.RestoreFormInfo (Self);

  ChangeQuery.SQL.Clear;

  if (RefLE <> -1) then begin
    Label1.Caption := GetResourceText (1098);
    ChangeQuery.SQL.Add ('select * from V_BESTAND_UPDATE where REF_BESTAND in (select REF from VQ_LAGER_BESTAND where REF_LE=:ref) order by CHANGE_DATE desc');
    ChangeQuery.Parameters [0].Value := RefLE;
  end else if (RefLP <> -1) then begin
    Label1.Caption := GetResourceText (1730);
    ChangeQuery.SQL.Add ('select * from V_BESTAND_UPDATE where REF_BESTAND in (select REF from VQ_LAGER_BESTAND where (REF_LP=:ref_2 or REF_LE in (select REF from VQ_LAGER_LE where REF_LP=:ref_1))) order by CHANGE_DATE desc');
    ChangeQuery.Parameters [0].Value := RefLP;
    ChangeQuery.Parameters [1].Value := RefLP;
  end else if (RefBes <> -1) then begin
    Label1.Caption := GetResourceText (1731);
    ChangeQuery.SQL.Add ('select * from V_BESTAND_UPDATE where REF_BESTAND=:ref order by CHANGE_DATE desc');
    ChangeQuery.Parameters [0].Value := RefBes;
  end else if (RefArEinheit <> -1) then begin
    Label1.Caption := GetResourceText (1731);
    ChangeQuery.SQL.Add ('select upd.* from V_BESTAND_UPDATE upd, VQ_ARTIKEL ar, VQ_ARTIKEL_EINHEIT ae where ar.REF=ae.REF_AR and upd.ARTIKEL_NR=ar.ARTIKEL_NR and ae.REF=:ref order by CHANGE_DATE desc');
    ChangeQuery.Parameters [0].Value := RefArEinheit;
  end else begin
    Label1.Caption := '';
    Label2.Caption := '';
  end;

  if (Length (ChangeQuery.SQL.Text) > 0) then begin
    if (ChangeDBGrid.SortColumns[0].ColumnIndex = -1) then
      ChangeDBGrid.SetSortColumn(0, 'CHANGE_DATE', False);

    ChangeQuery.Open;
  end;

  if (RefLE <> -1) then
  else if (RefLP <> -1) then begin
  end else if (RefBes <> -1) then begin
    Label2.Caption := ChangeQuery.FieldByName ('ARTIKEL_NR').AsString + ' / '+ChangeQuery.FieldByName ('ARTIKEL_TEXT').AsString
  end;
end;

//******************************************************************************
//* Function Name: KundenLoeschen
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TChangeHistoryForm.ChangeDBGridColumnSort(Sender: TCustomDBGridPro; const ColumnName: string): string;
begin
  if (ColumnName = 'MOVE_DATE') then
    Result := 'REF'
  else
    Result := ColumnName;
end;

end.
