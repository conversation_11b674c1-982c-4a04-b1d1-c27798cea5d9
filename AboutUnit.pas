﻿unit AboutUnit;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TAboutForm = class(TForm)
    Image1: TImage;
    Button1: TButton;
    PrgLabel: TLabel;
    VersionLabel: TLabel;
    VerPanel: TPanel;
    procedure FormShow(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

uses VerInfos;

{$R *.dfm}

procedure TAboutForm.FormShow(Sender: TObject);
var
  bm : TBitmap;
  filedate  : TDateTime;
begin
  FileAge (ParamStr (0), filedate);

  VerPanel.Caption := FileVersion (3,2) + ' from ' + DateToStr (filedate);

  bm := TBitmap.Create;
  bm.handle:=loadbitmap(hinstance,'aboutlogo');
  Image1.Picture.Assign (bm);
  bm.Free;

  PrgLabel.Caption := Application.Title;
  VersionLabel.Caption := 'Version : ' + FileVersion (4,2) + ' ('+CmdLine+')'
end;

end.
