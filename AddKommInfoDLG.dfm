object AddKommInfoForm: TAddKommInfoForm
  Left = 417
  Top = 268
  BorderStyle = bsDialog
  Caption = 'Zusatzinformationen zur Kommissionierposition erfassen'
  ClientHeight = 345
  ClientWidth = 569
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    569
    345)
  TextHeight = 13
  object InfoArtLabel: TLabel
    Left = 8
    Top = 8
    Width = 239
    Height = 13
    Caption = 'Art der Zusatzinformation zur Kommissionierposition'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 51
    Width = 553
    Height = 6
    Shape = bsTopLine
  end
  object InfoArtComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 553
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 120
    ItemHeight = 15
    TabOrder = 0
    OnChange = InfoArtComboBoxChange
    Items.Strings = (
      'ORGA-Invent-Angaben bei der Kommissionierung')
  end
  object AbortButton: TButton
    Left = 486
    Top = 312
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 3
  end
  object OkButton: TButton
    Left = 398
    Top = 312
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 2
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 63
    Width = 553
    Height = 240
    ActivePage = SerialTabSheet
    TabOrder = 1
    object OrgaInventTabSheet: TTabSheet
      Caption = 'OrgaInventTabSheet'
      OnShow = OrgaInventTabSheetShow
      DesignSize = (
        545
        212)
      object Label3: TLabel
        Left = 8
        Top = 12
        Width = 17
        Height = 13
        Caption = 'ES:'
      end
      object Label4: TLabel
        Left = 8
        Top = 38
        Width = 17
        Height = 13
        Caption = 'EZ:'
      end
      object Label5: TLabel
        Left = 8
        Top = 65
        Width = 17
        Height = 13
        Caption = 'EV:'
      end
      object Label2: TLabel
        Left = 8
        Top = 93
        Width = 72
        Height = 13
        Caption = 'Ident-Nummern'
      end
      object ESEdit: TEdit
        Left = 31
        Top = 8
        Width = 508
        Height = 21
        TabOrder = 0
        Text = 'ESEdit'
      end
      object EZEdit: TEdit
        Left = 31
        Top = 35
        Width = 508
        Height = 21
        TabOrder = 1
        Text = 'EZEdit'
      end
      object EVEdit: TEdit
        Left = 31
        Top = 62
        Width = 508
        Height = 21
        TabOrder = 2
        Text = 'EVEdit'
      end
      object OrgaInfoMemo: TMemo
        Left = 8
        Top = 108
        Width = 531
        Height = 101
        Anchors = [akLeft, akTop, akRight, akBottom]
        Lines.Strings = (
          'Memo1')
        TabOrder = 3
      end
    end
    object SerialTabSheet: TTabSheet
      Caption = 'SerialTabSheet'
      ImageIndex = 1
      OnShow = SerialTabSheetShow
      DesignSize = (
        545
        212)
      object Label1: TLabel
        Left = 8
        Top = 1
        Width = 121
        Height = 13
        Caption = 'Seriennummern-Nummern'
      end
      object SerialInfoMemo: TMemo
        Left = 8
        Top = 16
        Width = 531
        Height = 193
        Anchors = [akLeft, akTop, akRight, akBottom]
        Lines.Strings = (
          'Memo1')
        TabOrder = 0
      end
    end
  end
end
