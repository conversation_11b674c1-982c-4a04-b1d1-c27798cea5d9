(*        GREATIS DELPHI PAGES         *)
(* Copyright (C) 2001 Greatis Software *)
(*  web: http://www.greatisdelphi.com  *)
(*     e-mail: <EMAIL>      *)

unit TabListBox;

interface

uses
  Windows, Messages, SysUtils, Classes, Graphics, Controls, Forms, Dialogs,
  StdCtrls, CheckLst;

type
  TTabListBox = class(TListBox)
  protected
    procedure CreateParams(var Params: TCreateParams); override;
  end;

  TTabCheckListBox = class(TCheckListBox)
  protected
    procedure CreateParams(var Params: TCreateParams); override;
  end;

procedure Register;

implementation

procedure Register;
begin
  RegisterComponents('c+s', [TTabListBox, TTabCheckListBox]);
end;

procedure TTabListBox.CreateParams(var Params: TCreateParams);
begin
  inherited CreateParams(Params);
  Params.Style:=Params.Style or LBS_USETABSTOPS;
end;

procedure TTabCheckListBox.CreateParams(var Params: TCreateParams);
begin
  inherited CreateParams(Params);
  Params.Style:=Params.Style or LBS_USETABSTOPS;
end;

end.

