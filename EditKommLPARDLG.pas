unit EditKommLPARDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls;

type
  TEditKommLPARForm = class(TForm)
    KommLPLabel: TLabel;
    Bevel1: TBevel;
    AbortButton: TButton;
    OkButton: TButton;
    Bevel2: TBevel;
    AZPEdit: TEdit;
    Label3: TLabel;
    ARNrLabel: TLabel;
    Label2: TLabel;
    Label1: TLabel;
    Bevel3: TBevel;
    Label10: TLabel;
    AutoStoreComboBox: TComboBox;
    procedure NumEditKeyPress(Sender: TObject; var Key: Char);
    procedure FormShow(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
  private
    fRef : Integer;
  public
    property Ref : Integer read fRef write fRef;
  end;

implementation

{$R *.dfm}

uses
  DB, ADODB, VCLUtilitys, FrontendUtils, DatenModul, LVSDatenInterface, SprachModul;

procedure TEditKommLPARForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res     : Integer;
  autoein : Char;
begin
  res := 0;

  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    if (AutoStoreComboBox.ItemIndex = 0) then
      autoein := #0
    else if (AutoStoreComboBox.ItemIndex = 1) then
      autoein := '0'
    else if (AutoStoreComboBox.ItemIndex = 2) then
      autoein := '1'
    else if (AutoStoreComboBox.ItemIndex = 3) then
      autoein := '2'
    else
      autoein := #0;

    res := SetArtikelKommplatzDaten (fRef, autoein, AZPEdit.Text);

    if (res = 0) then
      CanClose := True
    else begin
      CanClose := False;
      MessageDLG('Fehler beim Setzen der Kommissionierplatzdaten' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
    end;
  end;

end;

procedure TEditKommLPARForm.FormCreate(Sender: TObject);
begin
  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, KommLPLabel);
    LVSSprachModul.SetNoTranslate (Self, ARNrLabel);
  {$endif}
end;

procedure TEditKommLPARForm.FormShow(Sender: TObject);
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select * from V_KOMM_LP_AR where REF='+IntToStr (fRef));

    query.Open;

    KommLPLabel.Caption := query.FieldByName ('LP_NR').AsString;
    ARNrLabel.Caption   := query.FieldByName ('ARTIKEL_NR').AsString + ' / ' + query.FieldByName ('ARTIKEL_TEXT').AsString;

    AZPEdit.Text      := query.FieldByName ('AR_AZP').AsString;

    if (query.FieldByName ('AUTO_EINLAGERUNG').IsNull) then
      AutoStoreComboBox.ItemIndex := 0
    else if (query.FieldByName ('AUTO_EINLAGERUNG').AsString = '0') then
      AutoStoreComboBox.ItemIndex := 1
    else if (query.FieldByName ('AUTO_EINLAGERUNG').AsString = '1') then
      AutoStoreComboBox.ItemIndex := 2
    else if (query.FieldByName ('AUTO_EINLAGERUNG').AsString = '2') then
      AutoStoreComboBox.ItemIndex := 3
    else
      AutoStoreComboBox.ItemIndex := 0;

    query.Close;
  finally
    query.Free;
  end;
end;

procedure TEditKommLPARForm.NumEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,^C,^V,'0'..'9']) then begin
    Key := #0;
  end;
end;

end.
