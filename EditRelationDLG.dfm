object EditRelationForm: TEditRelationForm
  Left = 638
  Top = 346
  BorderStyle = bsDialog
  Caption = 'WA-Relation bearbeiten'
  ClientHeight = 758
  ClientWidth = 551
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    551
    758)
  TextHeight = 13
  object Label1: TLabel
    Left = 320
    Top = 431
    Width = 42
    Height = 13
    Alignment = taRightJustify
    Caption = 'Stellplatz'
  end
  object Label2: TLabel
    Left = 8
    Top = 431
    Width = 46
    Height = 13
    Caption = 'WA-Zone'
  end
  object Label3: TLabel
    Left = 8
    Top = 148
    Width = 70
    Height = 13
    Caption = 'Relationsname'
  end
  object Bevel3: TBevel
    Left = 6
    Top = 415
    Width = 537
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel4: TBevel
    Left = 6
    Top = 98
    Width = 537
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label7: TLabel
    Left = 8
    Top = 52
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Label8: TLabel
    Left = 8
    Top = 196
    Width = 65
    Height = 13
    Caption = 'Beschreibung'
  end
  object Label9: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Bevel2: TBevel
    Left = 6
    Top = 490
    Width = 537
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label4: TLabel
    Left = 8
    Top = 505
    Width = 44
    Height = 13
    Caption = 'Spedition'
  end
  object Label5: TLabel
    Left = 368
    Top = 265
    Width = 40
    Height = 13
    Caption = 'Barcode'
  end
  object Bevel5: TBevel
    Left = 6
    Top = 528
    Width = 537
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label6: TLabel
    Left = 8
    Top = 391
    Width = 29
    Height = 13
    Caption = 'Depot'
  end
  object Label10: TLabel
    Left = 256
    Top = 148
    Width = 54
    Height = 13
    Caption = 'Hinweistext'
  end
  object Bevel7: TBevel
    Left = 6
    Top = 191
    Width = 537
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel1: TBevel
    Left = 6
    Top = 458
    Width = 537
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel6: TBevel
    Left = 6
    Top = 567
    Width = 537
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label13: TLabel
    Left = 8
    Top = 543
    Width = 48
    Height = 13
    Caption = 'Verladung'
  end
  object LPComboBox: TComboBoxPro
    Left = 368
    Top = 428
    Width = 175
    Height = 21
    CanItemEnable = True
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 12
    OnCloseUp = LPComboBoxCloseUp
    OnDropDown = LPComboBoxDropDown
    OnExit = LPComboBoxExit
  end
  object LBComboBox: TComboBoxPro
    Left = 72
    Top = 428
    Width = 225
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    PopupMenu = WAZonePopupMenu
    TabOrder = 11
    OnChange = LBComboBoxChange
  end
  object OKButton: TButton
    Left = 381
    Top = 726
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 21
  end
  object AbortButton: TButton
    Left = 468
    Top = 726
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 22
  end
  object NameEdit: TEdit
    Left = 8
    Top = 164
    Width = 242
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 32
    TabOrder = 3
    Text = 'NameEdit'
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 68
    Width = 534
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 1
    OnChange = LagerComboBoxChange
  end
  object BezEdit: TEdit
    Left = 8
    Top = 212
    Width = 534
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 64
    TabOrder = 5
    Text = 'BezEdit'
  end
  object MandantComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 534
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 0
    OnChange = MandantComboBoxChange
  end
  object AVISCheckBox: TCheckBox
    Left = 8
    Top = 579
    Width = 242
    Height = 17
    Caption = 'Nach Verladung Bordero senden'
    TabOrder = 15
  end
  object SpedComboBox: TComboBoxPro
    Left = 72
    Top = 502
    Width = 471
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 14
  end
  object LabelTextMemo: TMemo
    Left = 8
    Top = 240
    Width = 354
    Height = 65
    Anchors = [akLeft, akTop, akRight]
    Lines.Strings = (
      'LabelTextMemo')
    TabOrder = 6
  end
  object BarcodeEdit: TEdit
    Left = 368
    Top = 284
    Width = 175
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 7
    Text = 'BarcodeEdit'
  end
  object VerladungCheckBox: TCheckBox
    Left = 8
    Top = 597
    Width = 242
    Height = 17
    Caption = 'Verladungen automatisch anlegen'
    TabOrder = 16
  end
  object DepotComboBox: TComboBoxPro
    Left = 72
    Top = 388
    Width = 471
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 10
  end
  object HinweisEdit: TEdit
    Left = 256
    Top = 164
    Width = 287
    Height = 21
    TabOrder = 4
    Text = 'HinweisEdit'
  end
  object RadioPrioPanel: TPanel
    Left = 8
    Top = 315
    Width = 534
    Height = 62
    BevelOuter = bvNone
    TabOrder = 8
    object PrioRadioGroup: TRadioGroup
      Left = 0
      Top = 0
      Width = 534
      Height = 62
      Align = alClient
      Caption = 'Priorit'#228't'
      Columns = 4
      Items.Strings = (
        'Niedrig'
        'Normal'
        'Hoch'
        'Dringend')
      TabOrder = 0
    end
  end
  object EditPrioPanel: TPanel
    Left = 111
    Top = 275
    Width = 534
    Height = 62
    BevelOuter = bvNone
    TabOrder = 9
    object GroupBox1: TGroupBox
      Left = 0
      Top = 0
      Width = 534
      Height = 62
      Align = alClient
      Caption = 'Priorit'#228't'
      TabOrder = 0
      DesignSize = (
        534
        62)
      object Label11: TLabel
        Left = 71
        Top = 12
        Width = 446
        Height = 44
        Anchors = [akLeft, akTop, akRight, akBottom]
        AutoSize = False
        Caption = 
          'Die Priorit'#228't wird aufsteigend vergeben. Maximal ist eine Priori' +
          't'#228't von 999 m'#246'glich. '#13#10'Sinnvollerweise wird die Priorit'#228't in 10e' +
          'r-Schritten vergeben, damit bei der Auftragsplanung noch das Auf' +
          'tragsvolumen mit einfliesen kann'
        Layout = tlCenter
        WordWrap = True
      end
      object PrioEdit: TEdit
        Left = 8
        Top = 24
        Width = 41
        Height = 21
        TabOrder = 0
        Text = '0'
      end
      object PrioUpDown: TIntegerUpDown
        Left = 49
        Top = 24
        Width = 16
        Height = 21
        Associate = PrioEdit
        Min = -1
        Max = 999
        TabOrder = 1
        OnChangingEx = PrioUpDownChangingEx
        OnClick = PrioUpDownClick
      end
    end
  end
  object ArtRadioGroup: TRadioGroup
    Left = 6
    Top = 101
    Width = 537
    Height = 41
    Caption = 'Relationsart'
    Columns = 4
    Items.Strings = (
      'Verladerelation'
      'Zielrelation'
      #220'bergaberelation'
      'Packplatz')
    TabOrder = 2
    OnClick = ArtRadioGroupClick
  end
  object CheckBestCheckBox: TCheckBox
    Left = 265
    Top = 579
    Width = 272
    Height = 17
    Caption = 'Bestellnr. beim Verladen '#252'berpr'#252'fen'
    TabOrder = 19
  end
  object CheckBestLagerCheckBox: TCheckBox
    Left = 265
    Top = 594
    Width = 240
    Height = 17
    Caption = 'Nur lagerreine Verladung zul'#228'ssig'
    TabOrder = 18
  end
  object AvisGroupBox: TGroupBox
    Left = 8
    Top = 649
    Width = 535
    Height = 69
    Caption = 'Avisierung nach Lieferabschluss oder Verladung'
    TabOrder = 20
    object Label12: TLabel
      Left = 8
      Top = 24
      Width = 82
      Height = 13
      Caption = 'E-Mail Adresse(n)'
    end
    object AVISMailVerlCheckBox: TCheckBox
      Left = 347
      Top = 10
      Width = 185
      Height = 17
      Caption = 'Verladepapiere per Mail senden'
      TabOrder = 1
      OnClick = AVISMailCheckBoxClick
    end
    object AVISMailLSCheckBox: TCheckBox
      Left = 347
      Top = 50
      Width = 171
      Height = 17
      Caption = 'Lieferscheine per Mail senden'
      TabOrder = 3
    end
    object AvisMailEdit: TEdit
      Left = 8
      Top = 40
      Width = 321
      Height = 21
      MaxLength = 256
      TabOrder = 0
      Text = 'AvisMailEdit'
    end
    object AVISMailAvisCheckBox: TCheckBox
      Left = 347
      Top = 26
      Width = 171
      Height = 17
      Caption = 'Lieferavis per Mail senden'
      TabOrder = 2
      OnClick = AVISMailCheckBoxClick
    end
  end
  object AutoNVEVerladenCheckBox: TCheckBox
    Left = 8
    Top = 615
    Width = 251
    Height = 17
    Caption = 'NVEs bei Abschluss automatisch verladen'
    TabOrder = 17
  end
  object ClearingCheckBox: TCheckBox
    Left = 72
    Top = 467
    Width = 242
    Height = 17
    Caption = 'Kl'#228'rfall Abwicklung zul'#228'ssig'
    TabOrder = 13
  end
  object VerladeArtComboBox: TComboBoxPro
    Left = 72
    Top = 540
    Width = 471
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 23
  end
  object CheckVerladeListeCheckBox: TCheckBox
    Left = 265
    Top = 610
    Width = 240
    Height = 17
    Caption = 'Verladeliste drucken'
    TabOrder = 24
  end
  object CheckVerladeLiefCheckBox: TCheckBox
    Left = 265
    Top = 626
    Width = 240
    Height = 17
    Caption = 'Verladelieferschein drucken'
    TabOrder = 25
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 200
    Top = 384
  end
  object WAZonePopupMenu: TPopupMenu
    Left = 128
    Top = 384
    object NeueWAZone1: TMenuItem
      Caption = 'Neue WA-Zone...'
      OnClick = NeueWAZone1Click
    end
  end
end
