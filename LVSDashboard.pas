unit LVSDashboard;

interface

function DashboardLogin  (const ConnectString, UniqeUser, Password, SessionID : String) : Integer;
function DashboardLogout (const ConnectString, UniqeUser : String) : Integer;

implementation

uses
  MyDacVcl, MyAccess;

//******************************************************************************
//* Function Name: DashboardLogin
//* Author       : <PERSON>
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DashboardLogin (const ConnectString, UniqeUser, Password, SessionID : String) : Integer;
var
  res   : Integer;
  query : TMyQuery;
  dbcon : TMyConnection;
begin
  res := 0;

  if (Length (ConnectString) > 0) then begin
    dbcon := TMyConnection.Create (Nil);

    try
      try
        dbcon.ConnectString := ConnectString;

        dbcon.LoginPrompt := False;
        dbcon.Open;

        query := TMyQuery.Create (Nil);

        try
          query.Connection := dbcon;

          query.SQL.Add ('update sl_user set SESSION_ID=:id where UNIQUE_ID=:uid');
          query.ParamByName ('id').Value := SessionID;
          query.ParamByName ('uid').Value := UniqeUser;

          try
            dbcon.StartTransaction;

            query.Execute;

            dbcon.Commit;
          except
            dbcon.Rollback;

            res := -9;
          end;
        finally
          query.Free;
        end;

        dbcon.Close;
      except
        res := -2;
      end;
    finally
      dbcon.Free;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: DashboardLogout
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DashboardLogout (const ConnectString, UniqeUser : String) : Integer;
var
  res   : Integer;
  query : TMyQuery;
  dbcon : TMyConnection;
begin
  res := 0;

  if (Length (ConnectString) > 0) then begin
    dbcon := TMyConnection.Create (Nil);

    try
      try
        dbcon.ConnectString := ConnectString;

        dbcon.LoginPrompt := False;
        dbcon.Open;

        query := TMyQuery.Create (Nil);

        try
          query.Connection := dbcon;

          query.SQL.Add ('update sl_user set SESSION_ID=null where UNIQUE_ID=:uid');
          query.ParamByName ('uid').Value := UniqeUser;

          try
            dbcon.StartTransaction;

            query.Execute;

            dbcon.Commit;
          except
            dbcon.Rollback;

            res := -9;
          end;
        finally
          query.Free;
        end;

        dbcon.Close;
      except
        res := -2;
      end;
    finally
      dbcon.Free;
    end;
  end;

  Result := res;
end;

end.
