object ShowSpedAvisForm: TShowSpedAvisForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Speditions Avis'
  ClientHeight = 472
  ClientWidth = 907
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    907
    472)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 48
    Height = 13
    Caption = 'Spedition:'
  end
  object Label2: TLabel
    Left = 8
    Top = 24
    Width = 73
    Height = 13
    Caption = 'Versanddatum:'
  end
  object SpedLabel: TLabel
    Left = 112
    Top = 8
    Width = 58
    Height = 13
    Caption = 'SpedLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object DatumLabel: TLabel
    Left = 112
    Top = 24
    Width = 68
    Height = 13
    Caption = 'DatumLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Bevel1: TBevel
    Left = 8
    Top = 72
    Width = 891
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel2: TBevel
    Left = 8
    Top = 424
    Width = 891
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object SumWertLabel: TLabel
    Left = 485
    Top = 399
    Width = 83
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'SumWertLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object SumGwWertLabel: TLabel
    Left = 701
    Top = 399
    Width = 100
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'SumGwWertLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label3: TLabel
    Left = 8
    Top = 399
    Width = 44
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Summen:'
  end
  object Label4: TLabel
    Left = 8
    Top = 40
    Width = 64
    Height = 13
    Caption = 'Angelegt am:'
  end
  object CreateLabel: TLabel
    Left = 112
    Top = 40
    Width = 68
    Height = 13
    Caption = 'CreateLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object AvisPosDBGrid: TDBGridPro
    Left = 8
    Top = 87
    Width = 891
    Height = 306
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = AvisPosDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = AvisPosDBGridPopupMenu
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    OnColumnMoved = AvisPosDBGridColumnMoved
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\CS'
    RegistrySection = 'DBGrids'
    WidthOfIndicator = 11
    OnColWidthsChanged = AvisPosDBGridColWidthsChanged
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object Button1: TButton
    Left = 558
    Top = 439
    Width = 233
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Senden und schlie'#223'en'
    TabOrder = 1
    OnClick = Button1Click
  end
  object AbortButton: TButton
    Left = 806
    Top = 439
    Width = 93
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 2
  end
  object AvisPosADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 560
    Top = 48
  end
  object AvisPosDataSource: TDataSource
    DataSet = AvisPosADOQuery
    Left = 600
    Top = 48
  end
  object AvisPosDBGridPopupMenu: TPopupMenu
    Left = 352
    Top = 240
    object Positionbearbeiten1: TMenuItem
      Caption = 'Position bearbeiten...'
      OnClick = Positionbearbeiten1Click
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object Positionlschen1: TMenuItem
      Caption = 'Position l'#246'schen...'
      OnClick = Positionlschen1Click
    end
  end
end
