﻿unit LVSMandantDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, StdCtrls, Grids, StringGridPro, Menus, ExtCtrls,
  EditMandantDLG;

type
  TMandantForm = class(TForm)
    CloseButton: TButton;
    ADOQuery1: TADOQuery;
    MandPopupMenu: TPopupMenu;
    MandCheckConfigMenuItem: TMenuItem;
    MandConfigMenuItem: TMenuItem;
    MandCommMenuItem: TMenuItem;
    MandLagerMenuItem: TMenuItem;
    N1: TMenuItem;
    Bevel1: TBevel;
    MandantPanel: TPanel;
    Label1: TLabel;
    MandStringGrid: TStringGridPro;
    NeuButton: TButton;
    CopyButton: TButton;
    ChangeButton: TButton;
    DelButton: TButton;
    SubMandantPanel: TPanel;
    Untermandanten: TLabel;
    SubMandStringGrid: TStringGridPro;
    SubMandNewButton: TButton;
    SubMandCopyButton: TButton;
    SubMandChangeButton: TButton;
    SubMandDelButton: TButton;
    SubMandStringGridPopupMenu: TPopupMenu;
    SubMandDeactiveMenuItem: TMenuItem;
    SubMandActiveMenuItem: TMenuItem;
    SubMandDelMenuItem: TMenuItem;
    SubMandLocationMenuItem: TMenuItem;
    MandColOptimalMenuItem: TMenuItem;
    MandCopyColMenuitem: TMenuItem;
    N2: TMenuItem;
    N3: TMenuItem;
    SubMandCopyColMenuitem: TMenuItem;
    SubMandColOptimalMenuItem: TMenuItem;
    procedure FormShow(Sender: TObject);
    procedure MandStringGridDblClick(Sender: TObject);
    procedure NeuButtonClick(Sender: TObject);
    procedure ConfigButtonClick(Sender: TObject);
    procedure CommButtonClick(Sender: TObject);
    procedure LagerButtonClick(Sender: TObject);
    procedure CopyButtonClick(Sender: TObject);
    procedure MandCheckConfigMenuItemClick(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure MandStringGridClick(Sender: TObject);
    procedure SubMandStringGridDblClick(Sender: TObject);
    procedure SubMandNewButtonClick(Sender: TObject);
    procedure SubMandCopyButtonClick(Sender: TObject);
    procedure SubMandDelMenuItemClick(Sender: TObject);
    procedure SubMandStringGridPostDrawCell(Sender: TObject; ACol, ARow: Integer; Rect: TRect; State: TGridDrawState);
    procedure SubMandStringGridPopupMenuPopup(Sender: TObject);
    procedure SubMandActiveMenuItemClick(Sender: TObject);
    procedure SubMandDeactiveMenuItemClick(Sender: TObject);
    procedure SubMandLocationMenuItemClick(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure StringGridColOptimalMenuItemClick(Sender: TObject);
    procedure StringGridCopyColMenuItemClick(Sender: TObject);
    procedure MandPopupMenuPopup(Sender: TObject);
  private
    fConfigTab : Integer;

    procedure UpdateQuery (const SelectRef : Integer);
    procedure SubMandantUpdateQuery (const RefMand, SelectRef : Integer);
    function  GetMandantOptions (editform : TEditMandantForm) : String;
    function SetMandantAbsenderAdresse(editform: TEditMandantForm; ref: Integer): Integer;
    function SetMandantNiederlassungAdresse(editform: TEditMandantForm; ref: Integer): Integer;
  public
    { Public-Deklarationen }
  end;

implementation

uses
  VCLUtilitys, LVSConst, DatenModul, LVSLagertopologie,

  {$ifdef ConfigSetupNeu}
    ConfigSetupNeuDLG,
  {$else}
    ConfigSetupDLG,
  {$endif}

  KommunikationsPartnerDLG, MandantRelLagerDLG, FrontendImageModule, FrontendUtils,
  CheckConfigDLG, ConfigModul, LVSBenutzer, SprachModul, ResourceText, Clipbrd;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 26.03.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  LVSConfigModul.SaveGridInfo (Self, MandStringGrid);
  LVSConfigModul.SaveFormParameter(Self, 'MandStringGrid.SortCol', MandStringGrid.SortCol);

  if (SubMandantPanel.Visible) then begin
    LVSConfigModul.SaveGridInfo (Self, SubMandStringGrid);
    LVSConfigModul.SaveFormParameter(Self, 'SubMandStringGrid.SortCol', SubMandStringGrid.SortCol);
  end;

  LVSConfigModul.SaveFormInfo(Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.FormCreate(Sender: TObject);
begin
  fConfigTab := -1;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, MandStringGrid);
    LVSSprachModul.SetNoTranslate (Self, SubMandStringGrid);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.FormDestroy(Sender: TObject);
begin
  ClearGridObjects (MandStringGrid);
  ClearGridObjects (SubMandStringGrid);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.02.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TMandantForm.GetMandantOptions (editform : TEditMandantForm) : String;
var
  opt : String;
begin
  opt := '';

  if editform.ArtikelTabSheet.TabVisible then begin
    if editform.UseInternalArtikelNrCheckBox.Checked then
      opt := SetOpt (opt, cMandLocalArtikelnr, editform.UseInternalArtikelNrCheckBox.State);

    opt := SetOpt (opt, cMandUpdIFCArLagerDaten, editform.IFCStammdatenUpdateCheckBox.State);
  end;


  opt := SetOpt (opt, cMandBesCreateGrund, editform.BesCreateGrundCheckBox.State);

  opt := SetOpt (opt, cMandBesChangeGrund, editform.BesChangeGrundCheckBox.State);

  opt := SetOpt (opt, cMandBesDeleteGrund, editform.BesDelGrundCheckBox.State);

  opt := SetOpt (opt, cMandBesLockGrund, editform.BesLockGrundCheckBox.State);

  opt := SetOpt (opt, cMandBesLockID, editform.BesLockIDCheckBox.State);

  opt := SetOpt (opt, cMandBesUnlockGrund, editform.BesUnlockGrundCheckBox.State);

  opt := SetOpt (opt, cMandBesQSGrund, editform.BesQSGrundCheckBox.State);

  opt := SetOpt (opt, cMandBesCreateGrund, editform.BesCreateGrundCheckBox.State);

  opt := SetOpt (opt, cMandLiefRetBes, editform.LiefRetBesCheckBox.State);

  opt := SetOpt (opt, cMandLiefRetWE, editform.LiefRetWECheckBox.State);

  opt := SetOpt (opt, cMandInvEinzelScan, editform.InvEinzelScanCheckBox.State);

  opt := SetOpt (opt, cMandInvAssigneCountPos, editform.InvAssignPosCheckBox.State);

  opt := SetOpt (opt, cMandInvOldMHDBesAbgleich, editform.InvBesAbgleichOldMHDCheckBox.State);

  opt := SetOpt (opt, cMandInvANGBesAbgleich, editform.InvBesAbgleichANGCheckBox.State);

  opt := SetOpt (opt, cMandArPictureWE, editform.WEPicCheckBox.State);

  opt := SetOpt (opt, cMandWEBeschaffEinheit, editform.WEBeschaffCheckBox.State);

  opt := SetOpt (opt, cMandWEBBDFromPast, editform.WEBBDPastCheckBox.State);

  opt := SetOpt (opt, cMandWECheckLastMHD, editform.WELastMHDCheckBox.State);

  opt := SetOpt (opt, cMandWECheckLSNo, editform.WELSNoDutyCheckBox.State);
  opt := SetOpt (opt, cMandWECheckKFZNo, editform.WEKFZDutyCheckBox.State);
  opt := SetOpt (opt, cMandWECheckSupplier, editform.WESuppDutyCheckBox.State);
  opt := SetOpt (opt, cMandWECheckPackType, editform.WEPackTypeDutyCheckBox.State);

  opt := SetOpt (opt, cMandWECheckQSGrund, editform.WEQSGrundCheckBox.State);
  opt := SetOpt (opt, cMandWECategory, editform.WECategoryCheckBox.State);

  opt := SetOpt (opt, cMandWEChangeAREinheitOpt, editform.WEArEinheitOptCheckBox.State);

  opt := SetOpt (opt, cMandWEBedarfOpt, editform.WEBedarfCheckBox.State);

  opt := SetOpt (opt, cMandWEKommBestandOpt, editform.WEKommBestandCheckBox.State);

  opt := SetOpt (opt, cMandWENachsBestandOpt, editform.WENachsBestandCheckBox.State);

  opt := SetOpt (opt, cMandWELeerProPosOpt, editform.WELeerProPosCheckBox.State);

  opt := SetOpt (opt, cMandWEMehrMenge, editform.WEMehrMengeCheckBox.State);

  if (editform.AufPrioGroupBox.Visible and editform.AufPrioGroupBox.Enabled) then
    opt := SetOpt (opt, cMandIFCAufPrio, editform.IFCPrioCheckBox.State);

  opt := SetOpt (opt, cMandWEFillARData, editform.WEArtikelDatenCheckBox.State);
  opt := SetOpt (opt, cMandWEFillARMesData, editform.WEArAbmessungCheckBox.State);
  opt := SetOpt (opt, cMandWEFillARPalData, editform.WEArPalDatenCheckBox.State);
  opt := SetOpt (opt, cMandWEFillARAttData, editform.WEArAttrCheckBox.State);
  opt := SetOpt (opt, cMandWEFillARBarData, editform.WEArBarcodeCheckBox.State);
  opt := SetOpt (opt, cMandWEFillARShipData, editform.WEArShipperCheckBox.State);
  opt := SetOpt (opt, cMandWEFillARGefahr, editform.WEArGefahrstoffeCheckBox.State);
  opt := SetOpt (opt, cMandWEFillARKlasse, editform.WEArKlasseCheckBox.State);

  opt := SetOpt (opt, cMandRetSelectQuality, editform.RetQSGrundCheckBox.State);

  opt := SetOpt (opt, cMandRetSelectCategory, editform.RetCatAuswahlCheckBox.State);

  opt := SetOpt (opt, cMandRetAutoPrint, editform.RetAutoPrintCheckBox.State);

  opt := SetOpt (opt, cMandRetPrintLabel, editform.RetPrintLableCheckBox.State);
  opt := SetOpt (opt, cMandRetVAS, editform.RetVASCheckBox.State);

  opt := SetOpt (opt, cMandBesCreateGrund, editform.BesCreateGrundCheckBox.State);

  opt := SetOpt (opt, cMandRetGrundPflicht, editform.RetGrundPflichtCheckBox.State);
  opt := SetOpt (opt, cMandRetAvisAutoBuch, editform.RetAvisAutoBuchtCheckBox.State);

  opt := SetOpt (opt, cMandRetSelectGrund, editform.RetSelGrundCheckBox.State);
  opt := SetOpt (opt, cMandRetSelectZustand, editform.RetSelZustandCheckBox.State);
  opt := SetOpt (opt, cMandRetProcessPreGrading, editform.RetProcessPreGradingCheckBox.State);
  opt := SetOpt (opt, cMandRetZustandComment, editform.RetZustandCommentCheckBox.State);

  opt := SetOpt (opt, cMandArPictureVerteil, editform.VerteilPicCheckBox.State);

  opt := SetOpt (opt, cMandArPictureVerpack, editform.VerpacklPicCheckBox.State);

  opt := SetOpt (opt, cMandLHMErfassen, editform.WALHMCheckBox.State);

  opt := SetOpt (opt, cMandLHMAutoErfassen, editform.WALHMAutoCheckBox.State);
  opt := SetOpt (opt, cMandVerpackungErfassen, editform.VerpackungCheckBox.State);

  opt := SetOpt (opt, cMandUniqueChargen, editform.UniqueChargenCheckBox.State);
  opt := SetOpt (opt, cMandBestandBeleg, editform.BestandBelegCheckBox.State);

  opt := SetOpt (opt, cMandAbgleichAufEmpf, editform.IFCAbgleichAufEmpfCheckBox.State);
  opt := SetOpt (opt, cMandAufVorRes, editform.AufVorResCheckBox.State);
  opt := SetOpt (opt, cMandAufLiefAdr, editform.LieferAdrCheckBox.State);

  if not editform.AufCheckAdrCheckBox.Checked then
    opt := SetOpt (opt, cMandAdrCheck, '0')
  else begin
    if editform.AufAdrCorrStrCheckBox.Checked and editform.AufAdrCorrCityCheckBox.Checked then
      opt := SetOpt (opt, cMandAdrCheck, 'B')
    else if editform.AufAdrCorrStrCheckBox.Checked then
      opt := SetOpt (opt, cMandAdrCheck, '8')
    else if editform.AufAdrCorrCityCheckBox.Checked then
      opt := SetOpt (opt, cMandAdrCheck, '4')
    else if editform.AufAdrCorrCheckBox.Checked then
      opt := SetOpt (opt, cMandAdrCheck, '2')
    else
      opt := SetOpt (opt, cMandAdrCheck, '1');
  end;

  opt := SetOpt (opt, cMandStornoOrderGrund, editform.AufStornoGrundCheckBox.State);

  opt := SetOpt (opt, cMandDelOrderGrund, editform.AufDeleteGrundCheckBox.State);

  opt := SetOpt (opt, cMandBestNrUnique, editform.BestNrUniqueCheckBox.State);

  opt := SetOpt (opt, cMandBestEnabled, editform.BestEnabledCheckBox.State);

  opt := SetOpt (opt, cMandAufEnabled, editform.AufEnabledCheckBox.State);
  opt := SetOpt (opt, cMandAufSelEmpf, editform.AufEmpfSelectCheckBox.State);

  opt := SetOpt (opt, cMandLieferSchein, editform.LieferscheinCheckBox.State);
  opt := SetOpt (opt, cMandRetourenSchein, editform.RetourenScheinCheckBox.State);
  opt := SetOpt (opt, cMandRetourenLabel, editform.RetourenLabelCheckBox.State);

  opt := SetOpt (opt, cMandArContFactor, editform.ArContenIntCheckBox.State);

  opt := SetOpt (opt, cMandWEAnzPackSt, editform.WEAnzPackStCheckBox.State);

  opt := SetOpt (opt, cMandUseDefaultLT, editform.WEUseDefaultLTCheckBox.State);

  Result := opt;
end;

function TMandantForm.SetMandantAbsenderAdresse(editform: TEditMandantForm; ref: Integer): integer;
begin
  if (editform.MandantAbsenderFrame.ContactEdit.Visible) then begin
    result := DefMandantAbsAdr(ref,
      LVSDatenModul.AktLocationRef,
      editform.MandantAbsenderFrame.NameEdit.Text,
      editform.MandantAbsenderFrame.NameAddEdit.Text,
      editform.MandantAbsenderFrame.RoadEdit.Text,
      editform.MandantAbsenderFrame.PLZEdit.Text,
      editform.MandantAbsenderFrame.OrtEdit.Text,
      editform.MandantAbsenderFrame.LandEdit.Text,
      editform.MandantAbsenderFrame.ContactEdit.Text,
      editform.MandantAbsenderFrame.PhoneEdit.Text,
      editform.MandantAbsenderFrame.MailEdit.Text)
  end
  else begin
      result := DefMandantAbsAdr(ref,
      LVSDatenModul.AktLocationRef,
      editform.MandantAbsenderFrame.NameEdit.Text,
      editform.MandantAbsenderFrame.RoadEdit.Text,
      editform.MandantAbsenderFrame.PLZEdit.Text,
      editform.MandantAbsenderFrame.OrtEdit.Text,
      editform.MandantAbsenderFrame.LandEdit.Text);
  end;
end;

function TMandantForm.SetMandantNiederlassungAdresse(editform: TEditMandantForm; ref: Integer): Integer;
begin
  result := DefMandantLocationAdr (ref,
    LVSDatenModul.AktLocationRef,
    editform.MandantAdressFrame.NameEdit.Text,
    editform.MandantAdressFrame.NameAddEdit.Text,
    editform.MandantAdressFrame.RoadEdit.Text,
    editform.MandantAdressFrame.PLZEdit.Text,
    editform.MandantAdressFrame.OrtEdit.Text,
    editform.MandantAdressFrame.LandEdit.Text,
    editform.MandantAdressFrame.ContactEdit.Text,
    editform.MandantAdressFrame.PhoneEdit.Text,
    editform.MandantAdressFrame.MailEdit.Text);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.FormShow(Sender: TObject);
var
  intwert : Integer;
begin
  LVSConfigModul.RestoreFormInfo(Self);

  LVSConfigModul.RestoreGridInfo (Self, MandStringGrid);
  LVSConfigModul.ReadFormParameter(Self, 'MandStringGrid.SortCol', intwert, -1);
  if (intwert > 0) then
    MandStringGrid.SortCol := intwert
  else
    MandStringGrid.SortCol := 1;

  if (LVSConfigModul.UseSubMandanten) then begin
    LVSConfigModul.RestoreGridInfo (Self, SubMandStringGrid);
    LVSConfigModul.ReadFormParameter(Self, 'SubMandStringGrid.SortCol', intwert, -1);
    if (intwert > 0) then
      SubMandStringGrid.SortCol := intwert
    else
      SubMandStringGrid.SortCol := 2;
  end else begin
    SubMandantPanel.Visible := False;
    MandantPanel.Height := MandantPanel.Height + SubMandantPanel.Height;
  end;

  UpdateQuery (LVSDatenModul.AktMandantRef);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.MandCheckConfigMenuItemClick(Sender: TObject);

var
  checkform : TCheckConfigForm;
begin
  if Assigned (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]) then begin
    checkform := TCheckConfigForm.Create (Self);

    checkform.RefMand  := TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref;

    checkform.ShowModal;

    checkform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.UpdateQuery (const SelectRef : Integer);
var
  idx,
  selidx : Integer;
begin
  ClearGridObjects(MandStringGrid);

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF, NAME, BESCHREIBUNG from V_PCD_MANDANT');

  if (MandStringGrid.SortCol = 1) then
    ADOQuery1.SQL.Add ('order by NAME')
  else if (MandStringGrid.SortCol = 2) then ADOQuery1.SQL.Add ('order by BESCHREIBUNG');

  idx := 0;
  selidx := -1;

  ADOQuery1.Open;
  while not (ADOQuery1.Eof) do begin
    MandStringGrid.Cells [1,MandStringGrid.FixedRows + idx] := ADOQuery1.Fields [1].AsString;
    MandStringGrid.Cells [2,MandStringGrid.FixedRows + idx] := ADOQuery1.Fields [2].AsString;

    MandStringGrid.Rows[MandStringGrid.FixedRows + idx].Objects [0] := TGridRef.Create (ADOQuery1.Fields [0].AsInteger);

    if (ADOQuery1.Fields [0].AsInteger = SelectRef) then
      selidx := MandStringGrid.FixedRows + idx;

    Inc (idx);

    ADOQuery1.Next;
  end;
  ADOQuery1.Close;

  if (idx > 0) then
    MandStringGrid.RowCount := MandStringGrid.FixedRows + idx
  else begin
    MandStringGrid.RowCount := MandStringGrid.FixedRows + 1;
    MandStringGrid.Rows [MandStringGrid.FixedRows].Clear;
  end;

  ChangeButton.Enabled := (idx > 0);
  CopyButton.Enabled   := (idx > 0);
  DelButton.Enabled    := (idx > 0);

  MandConfigMenuItem.Enabled := (idx > 0);
  MandCommMenuItem.Enabled   := (idx > 0);
  MandLagerMenuItem.Enabled  := (idx > 0);

  if (selidx <> -1) then
    MandStringGrid.Row := selidx
  else MandStringGrid.Row := MandStringGrid.FixedRows;

  if (MandStringGrid.Row >= MandStringGrid.FixedRows) and Assigned (MandStringGrid.Rows[MandStringGrid.Row].Objects [0]) then
    SubMandantUpdateQuery (TGridRef (MandStringGrid.Rows[MandStringGrid.Row].Objects [0]).Ref, -1)
  else
    SubMandantUpdateQuery (-1, -1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.SubMandActiveMenuItemClick(Sender: TObject);
var
  res : Integer;
begin
  if (Assigned (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0])) then begin
    if (MessagedLG ('Wollen Sie den Untermandanten wirklich aktivieren?', mtConfirmation, [mbYes, mbNo, mbCancel], 0) = mrYes) then begin
      res := ActivateMandant (TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref);

      if (res <> 0) Then
        MessageDLG ('Fehler beim Aktivieren des Untermandanten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
      else begin
        SubMandantUpdateQuery (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row]).Ref)
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.SubMandantUpdateQuery (const RefMand, SelectRef : Integer);
var
  idx,
  selidx : Integer;
begin
  idx := 0;
  selidx := -1;

  if (RefMand <> -1) then begin
    ClearGridObjects(SubMandStringGrid);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select m.REF, m.STATUS, m.NAME, m.BESCHREIBUNG, rel.REF_LOCATION, m.ERP_ID from V_MANDANT m left outer join V_MANDANT_REL_LOCATION rel on (rel.REF_MAND=m.REF and rel.REF_LOCATION=:ref_loc) where m.REF_MASTER_MAND=:ref_mand');
    ADOQuery1.Parameters.ParamByName('ref_mand').Value := RefMand;
    ADOQuery1.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    //Normal sterbliche d�rfen gel�schte Mandanten nicht sehen ;-)
    if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then begin
      ADOQuery1.SQL.Add ('and m.STATUS<>''DEL''');

      if not (UserIsAdmin (LVSDatenModul.AktUser)) then begin
        ADOQuery1.SQL.Add ('and rel.REF_LOCATION is not null');
      end;
    end;

    if (SubMandStringGrid.SortCol = 1) then
      ADOQuery1.SQL.Add ('order by m.STATUS')
    else if (SubMandStringGrid.SortCol = 2) then
      ADOQuery1.SQL.Add ('order by upper (m.NAME)')
    else if (SubMandStringGrid.SortCol = 3) then
      ADOQuery1.SQL.Add ('order by upper (m.BESCHREIBUNG)')
    else if (SubMandStringGrid.SortCol = 4) then
      ADOQuery1.SQL.Add ('order by case when rel.REF_LOCATION is not null then 0 else 9 end')
    else
      ADOQuery1.SQL.Add ('order by upper (m.NAME)');

    ADOQuery1.Open;
    while not (ADOQuery1.Eof) do begin
      SubMandStringGrid.Cells [1,SubMandStringGrid.FixedRows + idx] := ADOQuery1.Fields [1].AsString;
      SubMandStringGrid.Cells [2,SubMandStringGrid.FixedRows + idx] := ADOQuery1.Fields [2].AsString;
      SubMandStringGrid.Cells [3,SubMandStringGrid.FixedRows + idx] := ADOQuery1.Fields [3].AsString;
      SubMandStringGrid.Cells [4,SubMandStringGrid.FixedRows + idx] := ADOQuery1.Fields [5].AsString;

      if (ADOQuery1.Fields [4].IsNull) then
        SubMandStringGrid.Cells [5,SubMandStringGrid.FixedRows + idx] := rsNo
      else
        SubMandStringGrid.Cells [5,SubMandStringGrid.FixedRows + idx] := rsYes;

      SubMandStringGrid.Rows[SubMandStringGrid.FixedRows + idx].Objects [0] := TGridRef.Create (ADOQuery1.Fields [0].AsInteger);

      if (ADOQuery1.Fields [0].AsInteger = SelectRef) then
        selidx := SubMandStringGrid.FixedRows + idx;

      Inc (idx);

      ADOQuery1.Next;
    end;
    ADOQuery1.Close;
  end;

  if (idx > 0) then
    SubMandStringGrid.RowCount := SubMandStringGrid.FixedRows + idx
  else begin
    SubMandStringGrid.RowCount := SubMandStringGrid.FixedRows + 1;
    SubMandStringGrid.Rows [SubMandStringGrid.FixedRows].Clear;
  end;

  SubMandNewButton.Enabled    := True;

  SubMandChangeButton.Enabled := (idx > 0);
  SubMandCopyButton.Enabled   := (idx > 0);
  SubMandDelButton.Enabled    := (idx > 0);

  if (selidx <> -1) then
    SubMandStringGrid.Row := selidx
  else SubMandStringGrid.Row := SubMandStringGrid.FixedRows;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.SubMandCopyButtonClick(Sender: TObject);
var
  ref,
  res,
  orgref      : Integer;
  opt,
  avisopt     : String;
  trok        : boolean;
  editform    : TEditMandantForm;
begin
  if (Assigned (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0])) then begin
    orgref := TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref;

    editform := TEditMandantForm.Create (Self);

    with editform do begin
      Caption := 'Untermandanten >'+SubMandStringGrid.Cells [2, SubMandStringGrid.Row]+'< kopieren';

      Prepare (orgref, TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, fConfigTab);
    end;

    if (editform.ShowModal = mrOK) then begin
      if (editform.PageControl1.ActivePage = editform.StammTabSheet) then
        fConfigTab := -1
      else
        fConfigTab := editform.OptionPageControl.ActivePageIndex;

      try
        res := CopyMandant   (orgref,
                              editform.NameEdit.Text,
                              editform.ShortNameEdit.Text,
                              editform.BeschreibungEdit.Text,
                              editform.ILNEdit.Text,
                              editform.ERPIDEdit.Text,
                              editform.DXFEdit.Text,
                              True,
                              ref);
      except
        res := -9;
      end;

      if (res = 0) then begin
        res := DefMandantAdr (ref,
                              editform.AdrEdit.Text,
                              editform.RoadEdit.Text,
                              editform.PLZEdit.Text,
                              editform.OrtEdit.Text,
                              editform.LandEdit.Text);

        if (res = 0) and editform.AbsTabSheet.TabVisible then begin
          res := SetMandantAbsenderAdresse(editform, ref);
        end;

        if (res = 0) and editform.AdrTabSheet.TabVisible then begin
          res := SetMandantNiederlassungAdresse(editform, ref);
        end;

        if (res = 0) and editform.MandDatenTabSheet.TabVisible then
          res := SetMandantStammdaten (ref,
                                       editform.StammGFEdit.Text,
                                       editform.StammContactEdit.Text,
                                       editform.StammMailEdit.Text,
                                       editform.StammURLEdit.Text,
                                       editform.StammTaxNrEdit.Text,
                                       editform.StammVATEdit.Text,
                                       editform.StammHREdit.Text);

        if (res = 0) and editform.MandDatenTabSheet.TabVisible then
          res := SetMandantBank (ref,
                                 editform.StammBankEdit.Text,
                                 editform.StammIBANEdit.Text,
                                 editform.StammBICEdit.Text);

        if (res = 0) and editform.StammEORIEdit.Visible then
          res := SetMandantEORI (ref, editform.StammEORIEdit.Text);

        if (res = 0) and editform.CurrencyComboBox.Visible then
          res := SetMandantCurrency (ref, GetComboBoxDBItemWert (editform.CurrencyComboBox));

        if (res = 0) and editform.MandDatenTabSheet.TabVisible then
          res := SetMandantLogo (ref, editform.StammLogoName);

        if (res = 0) and editform.AvisTabSheet.TabVisible then begin
          avisopt := '********';

          if (editform.AVISMailAvisCheckBox.Checked) then
            avisopt [4] := '1';

          if (editform.AVISMailLSCheckBox.Checked) then
            avisopt [3] := '1';

          res := SetMandantAvisierung (ref, editform.AvisMailEdit.Text, avisopt);
        end;

        if (res = 0) and  (editform.BioKontrollNrEdit.Visible) then
          res := SetMandantBioKontrollNr (ref, editform.BioKontrollNrEdit.Text);

        if (res = 0) and (editform.StdPickHintEdit.Visible or editform.StdPackHintEdit.Visible) then
          res := SetMandantStandartHints (ref, editform.StdPickHintEdit.Text, editform.StdPackHintEdit.Text);

        if (res = 0) and editform.ReturnHintMemo.Visible then
          res := SetMandantReturnHint (ref, editform.ReturnHintMemo.Text);

        if (res = 0) and editform.ArtikelTabSheet.TabVisible then begin
          if editform.ArtikelPrefixEdit.Visible then
            res := SetMandantArtikelConfig (ref,
                                            editform.ArtikelPrefixEdit.Text,
                                            editform.ArtikelSuffixEdit.Text,
                                            GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                            GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                            GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                            GetIntegerParameter (editform.ArtikelLenEdit.Text))
          else
            res := SetMandantArtikelConfig (ref,
                                            editform.ArtikelSuffixEdit.Text,
                                            GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                            GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                            GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                            GetIntegerParameter (editform.ArtikelLenEdit.Text));
        end;

        if (res = 0) and editform.ZollGroupBox.Visible then begin
          res := SetMandantZollDaten (ref,
                                      GetCheckboxStat(editform.AutoZollAnmeldungCheckBox),
                                      editform.BaseTaricNrEdit.Text);
        end;

        if (res = 0) and editform.InvOptionGroupBox.Visible then begin
          opt := '';

          opt := SetOpt (opt, 1, editform.InvOptSingleScanCheckBox.State);
          opt := SetOpt (opt, 2, editform.InvOptGoLPCheckBox.State);
          opt := SetOpt (opt, 3, editform.InvSuggestArtikelCheckBox.State);
          opt := SetOpt (opt, 6, editform.InvOptNurMengeCheckBox.State);
          opt := SetOpt (opt, 7, editform.InvSuggestLEBesCheckBox.State);

          res := SetMandantInvOption (ref, opt);
        end;

        if (res = 0) and editform.InvChkOptionGroupBox.Visible then begin
          opt := '';

          opt := SetOpt (opt, 1, editform.InvChkOptSingleScanCheckBox.State);
          opt := SetOpt (opt, 2, editform.InvChkOptGoLPCheckBox.State);
          opt := SetOpt (opt, 3, editform.InvChkSuggestArtikelCheckBox.State);
          opt := SetOpt (opt, 6, editform.InvChkOptNurMengeCheckBox.State);
          opt := SetOpt (opt, 7, editform.InvChkSuggestLEBesCheckBox.State);
          opt := SetOpt (opt, 8, editform.InvChkAreaCheckBox.State);

          res := SetMandantInvCheckOption (ref, opt);
        end;
      end;

      if (res = 0) Then begin
        SubMandantUpdateQuery (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, ref);
      end else begin
        MessageDLG ('Fehler beim Kopieren eines neuen Untermandanten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
      end;
    end;

    editform.Release;
  end;
end;

procedure TMandantForm.SubMandNewButtonClick(Sender: TObject);
var
  ref,
  res,
  refmand,
  refsubmand,
  refbesmand  : Integer;
  opt         : String;
  avisopt     : String;
  besMandName : String;
  trok        : boolean;
  editform    : TEditMandantForm;
begin
  res := 0;

  editform := TEditMandantForm.Create (Self);

  with editform do begin
    Caption := GetResourceText (1759);

    Prepare (-1, TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, fConfigTab);
  end;

  if (editform.ShowModal = mrOK) then begin
    if (editform.PageControl1.ActivePage = editform.StammTabSheet) then
      fConfigTab := -1
    else
      fConfigTab := editform.OptionPageControl.ActivePageIndex;

    if (editform.PageControl1.ActivePage = editform.StammTabSheet) then
      fConfigTab := -1
    else
      fConfigTab := editform.OptionPageControl.ActivePageIndex;

    opt := GetMandantOptions (editform);
    refbesmand := -1;

    //Die Transaktion wird beim Deadlock oder Packageerror nochmals wiederholt
    trok := False;

    repeat
      LVSDatenModul.BeginTransaction (LVSDatenModul.MainADOConnection);

      try
        res := CreateSubMandant (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref,
                                 editform.NameEdit.Text,
                                 editform.ShortNameEdit.Text,
                                 editform.BeschreibungEdit.Text,
                                 editform.ILNEdit.Text,
                                 editform.ERPIDEdit.Text,
                                 editform.DXFEdit.Text,
                                 refbesmand,
                                 opt,
                                 ref);

        if (res = 0) and editform.QSTabSheet.TabVisible then begin
          res := SetMandantDefaultQS (ref,
                                      GetComboBoxRef (editform.QSWEComboBox),
                                      GetComboBoxRef (editform.QSRETComboBox),
                                      -1,
                                      -1);
        end;

        if (res = 0) then begin
          res := DefMandantLocation (ref, LVSDatenModul.AktLocationRef, '1');
        end;

        if (res = 0) and editform.BesMandGroupBox.Visible and editform.BesMandantCheckListBox.Enabled then
        begin
          ClearMandantBesMandant(ref, LVSDatenModul.AktLagerRef);

          for besMandName in editform.BesMandantCheckListBox.Items do
          begin
            if editform.BesMandantCheckListBox.Checked[editform.BesMandantCheckListBox.Items.IndexOf(besMandName)] then
            begin
              res := InsertMandantBesMandant(ref, GetMandantRefFromName(besmandName), LVSDatenModul.AktLagerRef);
            end;
          end;
        end;

        if (res = 0) then begin
          res := DefMandantAdr (ref,
                                editform.AdrEdit.Text,
                                editform.RoadEdit.Text,
                                editform.PLZEdit.Text,
                                editform.OrtEdit.Text,
                                editform.LandEdit.Text);

        if (res = 0) and editform.AbsTabSheet.TabVisible then begin
          res := SetMandantAbsenderAdresse(editform, ref);
        end;

        if (res = 0) and editform.AdrTabSheet.TabVisible then begin
          res := SetMandantNiederlassungAdresse(editform, ref);
        end;

          if (res = 0) and editform.MandDatenTabSheet.TabVisible then
            res := SetMandantStammdaten (ref,
                                         editform.StammGFEdit.Text,
                                         editform.StammContactEdit.Text,
                                         editform.StammMailEdit.Text,
                                         editform.StammURLEdit.Text,
                                         editform.StammTaxNrEdit.Text,
                                         editform.StammVATEdit.Text,
                                         editform.StammHREdit.Text);

          if (res = 0) and editform.MandDatenTabSheet.TabVisible then
            res := SetMandantBank (ref,
                                   editform.StammBankEdit.Text,
                                   editform.StammIBANEdit.Text,
                                   editform.StammBICEdit.Text);

          if (res = 0) and editform.StammEORIEdit.Visible then
            res := SetMandantEORI (ref, editform.StammEORIEdit.Text);

          if (res = 0) and editform.CurrencyComboBox.Visible then
            res := SetMandantCurrency (ref, GetComboBoxDBItemWert (editform.CurrencyComboBox));

          if (res = 0) and editform.MandDatenTabSheet.TabVisible then
            res := SetMandantLogo (ref, editform.StammLogoName);

          if (res = 0) and editform.AvisTabSheet.TabVisible then begin
            avisopt := '********';

            if (editform.AVISMailAvisCheckBox.Checked) then
              avisopt [4] := '1';

            if (editform.AVISMailLSCheckBox.Checked) then
              avisopt [3] := '1';

            res := SetMandantAvisierung (ref, editform.AvisMailEdit.Text, avisopt);
          end;

          if (res = 0) and  (editform.BioKontrollNrEdit.Visible) then
            res := SetMandantBioKontrollNr (ref, editform.BioKontrollNrEdit.Text);

          if (res = 0) and (editform.StdPickHintEdit.Visible or editform.StdPackHintEdit.Visible) then
            res := SetMandantStandartHints (ref, editform.StdPickHintEdit.Text, editform.StdPackHintEdit.Text);

          if (res = 0) and editform.ReturnHintMemo.Visible then
            res := SetMandantReturnHint (ref, editform.ReturnHintMemo.Text);

          if (res = 0) and editform.ArtikelTabSheet.TabVisible then begin
            if editform.ArtikelPrefixEdit.Visible then
              res := SetMandantArtikelConfig (ref,
                                              editform.ArtikelPrefixEdit.Text,
                                              editform.ArtikelSuffixEdit.Text,
                                              GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                              GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                              GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                              GetIntegerParameter (editform.ArtikelLenEdit.Text))
            else
              res := SetMandantArtikelConfig (ref,
                                              editform.ArtikelSuffixEdit.Text,
                                              GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                              GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                              GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                              GetIntegerParameter (editform.ArtikelLenEdit.Text));
          end;

          if (res = 0) and editform.ZollGroupBox.Visible then begin
            res := SetMandantZollDaten (ref,
                                        GetCheckboxStat(editform.AutoZollAnmeldungCheckBox),
                                        editform.BaseTaricNrEdit.Text);
          end;

          if (res = 0) and editform.InvOptionGroupBox.Visible then begin
            opt := '';

            opt := SetOpt (opt, 1, editform.InvOptSingleScanCheckBox.State);
            opt := SetOpt (opt, 2, editform.InvOptGoLPCheckBox.State);
            opt := SetOpt (opt, 3, editform.InvSuggestArtikelCheckBox.State);
            opt := SetOpt (opt, 6, editform.InvOptNurMengeCheckBox.State);
            opt := SetOpt (opt, 7, editform.InvSuggestLEBesCheckBox.State);

            res := SetMandantInvOption (ref, opt);
          end;

          if (res = 0) and editform.InvChkOptionGroupBox.Visible then begin
            opt := '';

            opt := SetOpt (opt, 1, editform.InvChkOptSingleScanCheckBox.State);
            opt := SetOpt (opt, 2, editform.InvChkOptGoLPCheckBox.State);
            opt := SetOpt (opt, 3, editform.InvChkSuggestArtikelCheckBox.State);
            opt := SetOpt (opt, 6, editform.InvChkOptNurMengeCheckBox.State);
            opt := SetOpt (opt, 7, editform.InvChkSuggestLEBesCheckBox.State);

            res := SetMandantInvCheckOption (ref, opt);
          end;
        end;

        if (res = 0) then
          LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trCommit)
        else
          LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);

        trok := True;
      except
        on EOracleRetryException do;

        on Exception do begin
          res := -9;

          LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);
        end;
      end;

      if (res = 0) Then begin
        SubMandantUpdateQuery (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, ref);
      end else begin
        MessageDLG (FormatMessageText (1599, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
      end;
    until  (trok) or (res <> 0);
  end;

  editform.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.SubMandStringGridDblClick(Sender: TObject);
var
  res,
  ref,
  refmand,
  refsubmand,
  refbesmand  : Integer;
  opt         : String;
  avisopt     : String;
  besMandName : String;
  editform    : TEditMandantForm;
begin
  if (Assigned (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0])) then begin
    editform := TEditMandantForm.Create (Self);

    with editform do begin
      Caption := GetResourceText (1760);

      Prepare (TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref, TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, fConfigTab);
    end;

    if (editform.ShowModal = mrOK) then begin
      if (editform.PageControl1.ActivePage = editform.StammTabSheet) then
        fConfigTab := -1
      else
        fConfigTab := editform.OptionPageControl.ActivePageIndex;

      opt := GetMandantOptions (editform);
      refbesmand := -1;

      if (refbesmand = TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref) then
        refbesmand := -1;

      ref := TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref;

      res := ChangeMandant (ref,
                            editform.NameEdit.Text,
                            editform.ShortNameEdit.Text,
                            editform.BeschreibungEdit.Text,
                            editform.ILNEdit.Text,
                            editform.ERPIDEdit.Text,
                            editform.DXFEdit.Text,
                            refbesmand,
                            opt);

      if (res = 0) and editform.QSTabSheet.TabVisible then begin
        res := SetMandantDefaultQS (ref,
                                    GetComboBoxRef (editform.QSWEComboBox),
                                    GetComboBoxRef (editform.QSRETComboBox),
                                    -1,
                                    -1);
      end;

      if (res = 0) and editform.BesMandGroupBox.Visible and editform.BesMandantCheckListBox.Enabled then
      begin
        ClearMandantBesMandant(ref, LVSDatenModul.AktLagerRef);
        for besMandName in editform.BesMandantCheckListBox.Items do
        begin
          if editform.BesMandantCheckListBox.Checked[editform.BesMandantCheckListBox.Items.IndexOf(besMandName)] then
          begin
            res := InsertMandantBesMandant(ref, GetMandantRefFromName(besmandName), LVSDatenModul.AktLagerRef);
          end;
        end;
      end;

      if (res = 0) then begin
        res := DefMandantAdr (ref,
                              editform.AdrEdit.Text,
                              editform.RoadEdit.Text,
                              editform.PLZEdit.Text,
                              editform.OrtEdit.Text,
                              editform.LandEdit.Text);

        if (res = 0) and editform.AbsTabSheet.TabVisible then begin
          res := SetMandantAbsenderAdresse(editform, ref);
        end;

        if (res = 0) and editform.AdrTabSheet.TabVisible then begin
          res := SetMandantNiederlassungAdresse(editform, ref);
        end;

        if (res = 0) and editform.MandDatenTabSheet.TabVisible then
          res := SetMandantStammdaten (ref,
                                       editform.StammGFEdit.Text,
                                       editform.StammContactEdit.Text,
                                       editform.StammMailEdit.Text,
                                       editform.StammURLEdit.Text,
                                       editform.StammTaxNrEdit.Text,
                                       editform.StammVATEdit.Text,
                                       editform.StammHREdit.Text);

        if (res = 0) and editform.MandDatenTabSheet.TabVisible then
          res := SetMandantBank (ref,
                                 editform.StammBankEdit.Text,
                                 editform.StammIBANEdit.Text,
                                 editform.StammBICEdit.Text);

        if (res = 0) and editform.StammEORIEdit.Visible then
          res := SetMandantEORI (ref, editform.StammEORIEdit.Text);

        if (res = 0) and editform.CurrencyComboBox.Visible then
          res := SetMandantCurrency (ref, GetComboBoxDBItemWert (editform.CurrencyComboBox));

        if (res = 0) and editform.MandDatenTabSheet.TabVisible then
          res := SetMandantLogo (ref, editform.StammLogoName);

        if (res = 0) and editform.LSDatumMaxNumberBox.Visible then begin
          res := SetMandantLS_DATUM_MAX_TAGE(ref, editform.LSDatumMaxNumberBox.ValueInt);
        end;

        if (res = 0) and editform.AvisTabSheet.TabVisible then begin
          avisopt := '********';

          if (editform.AVISMailAvisCheckBox.Checked) then
            avisopt [4] := '1';

          if (editform.AVISMailLSCheckBox.Checked) then
            avisopt [3] := '1';

          res := SetMandantAvisierung (ref, editform.AvisMailEdit.Text, avisopt);
        end;

        if (res = 0) and  (editform.BioKontrollNrEdit.Visible) then
          res := SetMandantBioKontrollNr (ref, editform.BioKontrollNrEdit.Text);

        if (res = 0) and (editform.StdPickHintEdit.Visible or editform.StdPackHintEdit.Visible) then
          res := SetMandantStandartHints (ref, editform.StdPickHintEdit.Text, editform.StdPackHintEdit.Text);

        if (res = 0) and editform.ReturnHintMemo.Visible then
          res := SetMandantReturnHint (ref, editform.ReturnHintMemo.Text);

        if (res = 0) and editform.ArtikelTabSheet.TabVisible then begin
          if editform.ArtikelPrefixEdit.Visible then
            res := SetMandantArtikelConfig (TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref,
                                            editform.ArtikelPrefixEdit.Text,
                                            editform.ArtikelSuffixEdit.Text,
                                            GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                            GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                            GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                            GetIntegerParameter (editform.ArtikelLenEdit.Text))
          else
            res := SetMandantArtikelConfig (TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref,
                                            editform.ArtikelSuffixEdit.Text,
                                            GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                            GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                            GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                            GetIntegerParameter (editform.ArtikelLenEdit.Text));
        end;

        if (res = 0) and editform.ZollGroupBox.Visible then begin
          res := SetMandantZollDaten (TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref,
                                      GetCheckboxStat(editform.AutoZollAnmeldungCheckBox),
                                      editform.BaseTaricNrEdit.Text);
        end;

        if (res = 0) and editform.InvOptionGroupBox.Visible then begin
          opt := '';

          opt := SetOpt (opt, 1, editform.InvOptSingleScanCheckBox.State);
          opt := SetOpt (opt, 2, editform.InvOptGoLPCheckBox.State);
          opt := SetOpt (opt, 3, editform.InvSuggestArtikelCheckBox.State);
          opt := SetOpt (opt, 6, editform.InvOptNurMengeCheckBox.State);
          opt := SetOpt (opt, 7, editform.InvSuggestLEBesCheckBox.State);

          res := SetMandantInvOption (TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref, opt);
        end;

        if (res = 0) and editform.InvChkOptionGroupBox.Visible then begin
          opt := '';

          opt := SetOpt (opt, 1, editform.InvChkOptSingleScanCheckBox.State);
          opt := SetOpt (opt, 2, editform.InvChkOptGoLPCheckBox.State);
          opt := SetOpt (opt, 3, editform.InvChkSuggestArtikelCheckBox.State);
          opt := SetOpt (opt, 6, editform.InvChkOptNurMengeCheckBox.State);
          opt := SetOpt (opt, 7, editform.InvChkSuggestLEBesCheckBox.State);

          res := SetMandantInvCheckOption (TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref, opt);
        end;
    end;

      if (res = 0) then
        SubMandantUpdateQuery (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref)
      else
        MessageDLG ('Fehler beim ändern der Mandantendaten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
    end;

    editform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.SubMandStringGridPopupMenuPopup(Sender: TObject);
var
  col,
  row : Integer;
  clp : TPoint;
begin
  GetCursorPos (clp);
  clp := SubMandStringGrid.ScreenToClient (clp);

  SubMandStringGrid.MouseToCell (clp.X, clp.Y, col, row);

  SubMandDeactiveMenuItem.Enabled := False;
  SubMandActiveMenuItem.Enabled := False;
  SubMandDelMenuItem.Enabled := False;

  if (col = 0) and (row = 0) then begin
    SubMandCopyColMenuitem.Visible := False;
    SubMandColOptimalMenuItem.Visible := True;
  end else begin
    SubMandCopyColMenuitem.Visible := True;
    SubMandColOptimalMenuItem.Visible := False;

    if (Assigned (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0])) then begin
      SubMandDeactiveMenuItem.Enabled := SubMandStringGrid.Cells [1, SubMandStringGrid.Row] = 'ANG';
      SubMandActiveMenuItem.Enabled   := (SubMandStringGrid.Cells [1, SubMandStringGrid.Row] = 'INA') or (SubMandStringGrid.Cells [1, SubMandStringGrid.Row] = 'DEL');
      SubMandDelMenuItem.Enabled      := SubMandStringGrid.Cells [1, SubMandStringGrid.Row] = 'ANG';
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.SubMandStringGridPostDrawCell(Sender: TObject; ACol, ARow: Integer; Rect: TRect; State: TGridDrawState);
var
  bm : TBitmap;
begin
  if Assigned (SubMandStringGrid.Rows[ARow].Objects [0]) then begin
    if (ACol = 1) then begin
      bm := TBitmap.Create;
      try
        bm.Transparent := True;
        bm.TransparentMode := tmAuto;

        if (SubMandStringGrid.Cells [1,ARow] = 'ANG') then
          bm.handle := loadbitmap (hinstance, 'angelegt')
        else if (SubMandStringGrid.Cells[1, ARow] = 'INA') then
          bm.handle := loadbitmap (hinstance, 'storniert')
        else if (SubMandStringGrid.Cells[1, ARow] = 'DEL') then
          bm.handle := loadbitmap (hinstance, 'delete');

        bm.TransparentColor := bm.canvas.pixels[0, 0];

        SubMandStringGrid.Canvas.FillRect(rect);
        DrawStatusBitmap (SubMandStringGrid.Canvas, Rect, bm);
      finally
        bm.Free;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.01.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.MandPopupMenuPopup(Sender: TObject);
var
  col,
  row : Integer;
  clp : TPoint;
begin
  GetCursorPos (clp);
  clp := MandStringGrid.ScreenToClient (clp);

  MandStringGrid.MouseToCell (clp.X, clp.Y, col, row);

  MandConfigMenuItem.Enabled := False;
  MandCommMenuItem.Enabled := False;
  MandLagerMenuItem.Enabled := False;
  MandCheckConfigMenuItem.Enabled := False;

  if (col = 0) and (row = 0) then begin
    MandCopyColMenuitem.Visible := False;
    MandColOptimalMenuItem.Visible := True;
  end else begin
    MandCopyColMenuitem.Visible := True;
    MandColOptimalMenuItem.Visible := False;

    if (GetGridRef (MandStringGrid) > 0) then begin
      MandConfigMenuItem.Enabled := True;
      MandCommMenuItem.Enabled := True;
      MandLagerMenuItem.Enabled := True;
      MandCheckConfigMenuItem.Enabled := True;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.MandStringGridClick(Sender: TObject);
begin
  if Assigned (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]) then
    SubMandantUpdateQuery (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, -1)
  else begin
    SubMandStringGrid.RowCount := SubMandStringGrid.FixedRows + 1;
    SubMandStringGrid.Rows [SubMandStringGrid.FixedRows].Clear;

    SubMandNewButton.Enabled    := False;
    SubMandChangeButton.Enabled := False;
    SubMandCopyButton.Enabled   := False;
    SubMandDelButton.Enabled    := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.MandStringGridDblClick(Sender: TObject);
var
  res,
  ref      : Integer;
  opt      : String;
  avisopt  : String;
  editform : TEditMandantForm;
  besMandName : String;
begin
  editform := TEditMandantForm.Create (Self);

  with editform do begin
    Caption := GetResourceText (1760);

    Prepare (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, -1, fConfigTab);
  end;

  if (editform.ShowModal = mrOK) then begin
    if (editform.PageControl1.ActivePage = editform.StammTabSheet) then
      fConfigTab := -1
    else
      fConfigTab := editform.OptionPageControl.ActivePageIndex;

    opt := GetMandantOptions (editform);

    ref := TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref;

    res := ChangeMandant (ref,
                          editform.NameEdit.Text,
                          editform.ShortNameEdit.Text,
                          editform.BeschreibungEdit.Text,
                          editform.ILNEdit.Text,
                          editform.ERPIDEdit.Text,
                          editform.DXFEdit.Text,
                          -1,
                          opt);

    if (res = 0) and editform.QSTabSheet.TabVisible then begin
      res := SetMandantDefaultQS (ref,
                                  GetComboBoxRef (editform.QSWEComboBox),
                                  GetComboBoxRef (editform.QSRETComboBox),
                                  -1,
                                  -1);
    end;

    if (res = 0) and editform.BesMandGroupBox.Visible and editform.BesMandantCheckListBox.Enabled then
    begin
      ClearMandantBesMandant(ref, LVSDatenModul.AktLagerRef);
      for besMandName in editform.BesMandantCheckListBox.Items do
      begin
        if editform.BesMandantCheckListBox.Checked[editform.BesMandantCheckListBox.Items.IndexOf(besMandName)] then
        begin
          res := InsertMandantBesMandant(ref, GetMandantRefFromName(besmandName), LVSDatenModul.AktLagerRef);
        end;
      end;
    end;

    if (res = 0) then begin
      res := DefMandantAdr (ref,
                            editform.AdrEdit.Text,
                            editform.RoadEdit.Text,
                            editform.PLZEdit.Text,
                            editform.OrtEdit.Text,
                            editform.LandEdit.Text);

      if (res = 0) and editform.AbsTabSheet.TabVisible then begin
        res := SetMandantAbsenderAdresse(editform, ref);
      end;

      if (res = 0) and editform.AdrTabSheet.TabVisible then begin
        res := SetMandantNiederlassungAdresse(editform, ref);
      end;

      if (res = 0) and editform.MandDatenTabSheet.TabVisible then
        res := SetMandantStammdaten (ref,
                                     editform.StammGFEdit.Text,
                                     editform.StammContactEdit.Text,
                                     editform.StammMailEdit.Text,
                                     editform.StammURLEdit.Text,
                                     editform.StammTaxNrEdit.Text,
                                     editform.StammVATEdit.Text,
                                     editform.StammHREdit.Text);

      if (res = 0) and editform.MandDatenTabSheet.TabVisible then
        res := SetMandantBank (ref,
                               editform.StammBankEdit.Text,
                               editform.StammIBANEdit.Text,
                               editform.StammBICEdit.Text);

      if (res = 0) and editform.StammEORIEdit.Visible then
        res := SetMandantEORI (ref, editform.StammEORIEdit.Text);

      if (res = 0) and editform.CurrencyComboBox.Visible then
        res := SetMandantCurrency (ref, GetComboBoxDBItemWert (editform.CurrencyComboBox));

      if (res = 0) and editform.MandDatenTabSheet.TabVisible then
        res := SetMandantLogo (ref, editform.StammLogoName);

      if (res = 0) and editform.LSDatumMaxNumberBox.Visible then begin
        res := SetMandantLS_DATUM_MAX_TAGE(ref, editform.LSDatumMaxNumberBox.ValueInt);
      end;

      if (res = 0) and editform.AvisTabSheet.TabVisible then begin
        avisopt := '********';

        if (editform.AVISMailAvisCheckBox.Checked) then
          avisopt [4] := '1';

        if (editform.AVISMailLSCheckBox.Checked) then
          avisopt [3] := '1';

        res := SetMandantAvisierung (ref, editform.AvisMailEdit.Text, avisopt);
      end;

      if (res = 0) and  (editform.BioKontrollNrEdit.Visible) then
        res := SetMandantBioKontrollNr (ref, editform.BioKontrollNrEdit.Text);

      if (res = 0) and (editform.StdPickHintEdit.Visible or editform.StdPackHintEdit.Visible) then
        res := SetMandantStandartHints (ref, editform.StdPickHintEdit.Text, editform.StdPackHintEdit.Text);

      if (res = 0) and editform.ReturnHintMemo.Visible then
        res := SetMandantReturnHint (ref, editform.ReturnHintMemo.Text);
        
      if (res = 0) and editform.ArtikelTabSheet.TabVisible then begin
        if editform.ArtikelPrefixEdit.Visible then
          res := SetMandantArtikelConfig (ref,
                                          editform.ArtikelPrefixEdit.Text,
                                          editform.ArtikelSuffixEdit.Text,
                                          GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                          GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                          GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                          GetIntegerParameter (editform.ArtikelLenEdit.Text))
        else
          res := SetMandantArtikelConfig (ref,
                                          editform.ArtikelSuffixEdit.Text,
                                          GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                          GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                          GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                          GetIntegerParameter (editform.ArtikelLenEdit.Text));
      end;

      if (res = 0) and editform.ZollGroupBox.Visible then begin
        res := SetMandantZollDaten (ref,
                                    GetCheckboxStat(editform.AutoZollAnmeldungCheckBox),
                                    editform.BaseTaricNrEdit.Text);
      end;

      if (res = 0) and editform.InvOptionGroupBox.Visible then begin
        opt := '';

        opt := SetOpt (opt, 1, editform.InvOptSingleScanCheckBox.State);
        opt := SetOpt (opt, 2, editform.InvOptGoLPCheckBox.State);
        opt := SetOpt (opt, 3, editform.InvSuggestArtikelCheckBox.State);
        opt := SetOpt (opt, 6, editform.InvOptNurMengeCheckBox.State);
        opt := SetOpt (opt, 7, editform.InvSuggestLEBesCheckBox.State);

        res := SetMandantInvOption (ref, opt);
      end;

      if (res = 0) and editform.InvChkOptionGroupBox.Visible then begin
        opt := '';

        opt := SetOpt (opt, 1, editform.InvChkOptSingleScanCheckBox.State);
        opt := SetOpt (opt, 2, editform.InvChkOptGoLPCheckBox.State);
        opt := SetOpt (opt, 3, editform.InvChkSuggestArtikelCheckBox.State);
        opt := SetOpt (opt, 6, editform.InvChkOptNurMengeCheckBox.State);
        opt := SetOpt (opt, 7, editform.InvChkSuggestLEBesCheckBox.State);

        res := SetMandantInvCheckOption (ref, opt);
      end;

      if editform.PackTypeGroupComboBox.Visible and editform.PackTypeGroupComboBox.Enabled then
        res := SetMandantPackTypeGroup (ref, GetComboBoxItemWert (editform.PackTypeGroupComboBox))

    end;

    if (res = 0) then
      UpdateQuery (ref)
    else
      MessageDLG ('Fehler beim ändern der Mandantendaten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
  end;

  editform.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.NeuButtonClick(Sender: TObject);
var
  ref,
  res       : Integer;
  opt       : String;
  avisopt   : String;
  trok      : boolean;
  editform  : TEditMandantForm;
  besMandName : String;
begin
  editform := TEditMandantForm.Create (Self);

  with editform do begin
    Caption := GetResourceText (1761);

    Prepare (-1, -1, -1);
  end;

  if (editform.ShowModal = mrOK) then begin
    if (editform.PageControl1.ActivePage = editform.StammTabSheet) then
      fConfigTab := -1
    else
      fConfigTab := editform.OptionPageControl.ActivePageIndex;

    res := 0;

    opt := GetMandantOptions (editform);


    //Die Transaktion wird beim Deadlock oder Packageerror nochmals wiederholt
    trok := False;

    repeat
      LVSDatenModul.BeginTransaction (LVSDatenModul.MainADOConnection);

      try
        res := CreateMandant (LVSConfigModul.RefProject,
                              editform.NameEdit.Text,
                              editform.ShortNameEdit.Text,
                              editform.BeschreibungEdit.Text,
                              editform.ILNEdit.Text,
                              editform.ERPIDEdit.Text,
                              editform.DXFEdit.Text,
                              -1,
                              opt,
                              ref);

        if (res = 0) and editform.QSTabSheet.TabVisible then begin
          res := SetMandantDefaultQS (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref,
                                      GetComboBoxRef (editform.QSWEComboBox),
                                      GetComboBoxRef (editform.QSRETComboBox),
                                      -1,
                                      -1);
        end;

        if (res = 0) and editform.BesMandGroupBox.Visible and editform.BesMandantCheckListBox.Enabled then
        begin
          ClearMandantBesMandant(ref, LVSDatenModul.AktLagerRef);
          for besMandName in editform.BesMandantCheckListBox.Items do
          begin
            if editform.BesMandantCheckListBox.Checked[editform.BesMandantCheckListBox.Items.IndexOf(besMandName)] then
            begin
              res := InsertMandantBesMandant(ref, GetMandantRefFromName(besmandName), LVSDatenModul.AktLagerRef);
            end;
          end;
        end;

        if (res = 0) then begin
          res := DefMandantAdr (ref,
                                editform.AdrEdit.Text,
                                editform.RoadEdit.Text,
                                editform.PLZEdit.Text,
                                editform.OrtEdit.Text,
                                editform.LandEdit.Text);

        if (res = 0) and editform.AbsTabSheet.TabVisible then begin
          res := SetMandantAbsenderAdresse(editform, ref);
        end;

        if (res = 0) and editform.AdrTabSheet.TabVisible then begin
          res := SetMandantNiederlassungAdresse(editform, ref);
        end;

        if (res = 0) and editform.MandDatenTabSheet.TabVisible then
          res := SetMandantStammdaten (ref,
                                       editform.StammGFEdit.Text,
                                       editform.StammContactEdit.Text,
                                       editform.StammMailEdit.Text,
                                       editform.StammURLEdit.Text,
                                       editform.StammTaxNrEdit.Text,
                                       editform.StammVATEdit.Text,
                                       editform.StammHREdit.Text);

        if (res = 0) and editform.MandDatenTabSheet.TabVisible then
          res := SetMandantBank (ref,
                                 editform.StammBankEdit.Text,
                                 editform.StammIBANEdit.Text,
                                 editform.StammBICEdit.Text);

        if (res = 0) and editform.StammEORIEdit.Visible then
          res := SetMandantEORI (ref, editform.StammEORIEdit.Text);

        if (res = 0) and editform.CurrencyComboBox.Visible then
          res := SetMandantCurrency (ref, GetComboBoxDBItemWert (editform.CurrencyComboBox));

          if (res = 0) and editform.MandDatenTabSheet.TabVisible then
            res := SetMandantLogo (ref, editform.StammLogoName);

          if (res = 0) and editform.AvisTabSheet.TabVisible then begin
            avisopt := '********';

            if (editform.AVISMailAvisCheckBox.Checked) then
              avisopt [4] := '1';

            if (editform.AVISMailLSCheckBox.Checked) then
              avisopt [3] := '1';

            res := SetMandantAvisierung (ref, editform.AvisMailEdit.Text, avisopt);
          end;

          if (res = 0) and  (editform.BioKontrollNrEdit.Visible) then
            res := SetMandantBioKontrollNr (ref, editform.BioKontrollNrEdit.Text);

          if (res = 0) and (editform.StdPickHintEdit.Visible or editform.StdPackHintEdit.Visible) then
            res := SetMandantStandartHints (ref, editform.StdPickHintEdit.Text, editform.StdPackHintEdit.Text);

          if (res = 0) and editform.ReturnHintMemo.Visible then
            res := SetMandantReturnHint (ref, editform.ReturnHintMemo.Text);

          if (res = 0) and editform.ArtikelTabSheet.TabVisible then begin
            if editform.ArtikelPrefixEdit.Visible then
              res := SetMandantArtikelConfig (ref,
                                              editform.ArtikelPrefixEdit.Text,
                                              editform.ArtikelSuffixEdit.Text,
                                              GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                              GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                              GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                              GetIntegerParameter (editform.ArtikelLenEdit.Text))
            else
              res := SetMandantArtikelConfig (ref,
                                              editform.ArtikelSuffixEdit.Text,
                                              GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                              GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                              GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                              GetIntegerParameter (editform.ArtikelLenEdit.Text));
          end;

          if (res = 0) and editform.ZollGroupBox.Visible then begin
            res := SetMandantZollDaten (ref,
                                        GetCheckboxStat(editform.AutoZollAnmeldungCheckBox),
                                        editform.BaseTaricNrEdit.Text);
          end;

          if (res = 0) and editform.InvOptionGroupBox.Visible then begin
            opt := '';

            opt := SetOpt (opt, 1, editform.InvOptSingleScanCheckBox.State);
            opt := SetOpt (opt, 2, editform.InvOptGoLPCheckBox.State);
            opt := SetOpt (opt, 3, editform.InvSuggestArtikelCheckBox.State);
            opt := SetOpt (opt, 6, editform.InvOptNurMengeCheckBox.State);
            opt := SetOpt (opt, 7, editform.InvSuggestLEBesCheckBox.State);

            res := SetMandantInvOption (ref, opt);
          end;

          if (res = 0) and editform.InvChkOptionGroupBox.Visible then begin
            opt := '';

            opt := SetOpt (opt, 1, editform.InvChkOptSingleScanCheckBox.State);
            opt := SetOpt (opt, 2, editform.InvChkOptGoLPCheckBox.State);
            opt := SetOpt (opt, 3, editform.InvChkSuggestArtikelCheckBox.State);
            opt := SetOpt (opt, 6, editform.InvChkOptNurMengeCheckBox.State);
            opt := SetOpt (opt, 7, editform.InvChkSuggestLEBesCheckBox.State);

            res := SetMandantInvCheckOption (ref, opt);
          end;
        end;

        if (res = 0) then begin
          if (LVSDatenModul.AktLagerRef > 0) then
            res := DefMandantLager (ref, LVSDatenModul.AktLagerRef, '1')
          else if (LVSDatenModul.AktLocationRef > 0) then begin
            ADOQuery1.SQL.Clear;
            ADOQuery1.SQL.Add ('select REF from V_PCD_LAGER where REF_LOCATION='+IntToStr (LVSDatenModul.AktLocationRef));

            try
              ADOQuery1.Open;

              while not (ADOQuery1.Eof) and (res = 0) do begin
                res := DefMandantLager (ref, ADOQuery1.Fields [0].AsInteger, '1');

                ADOQuery1.Next;
              end;
              ADOQuery1.Close;
            except
              res := -9;
            end;
          end;
        end;

        if (res = 0) then
          LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trCommit)
        else
          LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);

        trok := True;
      except
        on EOracleRetryException do;

        on Exception do begin
          res := -9;

          LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);
        end;
      end;
    until (trok) or (res <> 0);

    if (res = 0) Then begin
      UpdateQuery (ref);
    end else begin
      MessageDLG ('Fehler beim Anlegen eines neuen Mandanten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
    end;
  end;

  editform.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.01.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.StringGridColOptimalMenuItemClick(Sender: TObject);
var
  popup : TPopupMenu;
begin
  popup := ((Sender as TMenuItem).GetParentMenu as TPopupMenu);

  if Assigned (popup) and Assigned (popup.PopupComponent) and (popup.PopupComponent is TStringGridPro) then begin
    with (popup.PopupComponent as TStringGridPro) do
      SetGridOptimalColWidth;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.01.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.StringGridCopyColMenuItemClick(Sender: TObject);
var
  acol,
  arow    : Integer;
  popup   : TPopupMenu;
  clipstr : String;
begin
  popup := ((Sender as TMenuItem).GetParentMenu as TPopupMenu);

  if Assigned (popup) and Assigned (popup.PopupComponent) and (popup.PopupComponent is TStringGridPro) then begin
    with (popup.PopupComponent as TStringGridPro) do begin
      MouseToCell (MouseX, MouseY, acol, arow);

      if (acol >= FixedCols) and (acol < ColCount) and (arow >= FixedRows) and (arow < RowCount) then begin
        clipstr := Cells [acol, arow];

        Clipboard.SetTextBuf (PChar (clipstr));
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.ConfigButtonClick(Sender: TObject);
var
  cfgsetfrom : TConfigSetupForm;
begin
  cfgsetfrom := TConfigSetupForm.Create (Self);

  cfgsetfrom.Label1.Caption := 'Mandant  : ' + MandStringGrid.Cells [1,MandStringGrid.Row];
  cfgsetfrom.Label2.Caption := '';

  cfgsetfrom.Prepare ('', False, FALSE, TRUE, MandStringGrid.Cells [1,MandStringGrid.Row]);
  cfgsetfrom.ShowModal;

  cfgsetfrom.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.CopyButtonClick(Sender: TObject);
var
  ref,
  res,
  orgref    : Integer;
  opt,
  avisopt   : String;
  editform  : TEditMandantForm;
  besMandName : String;
begin
  if (Assigned (MandStringGrid.Rows [MandStringGrid.Row].Objects [0])) then begin
    orgref := TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref;

    editform := TEditMandantForm.Create (Self);

    with editform do begin
      Caption := FormatResourceText (1762, [MandStringGrid.Cells [1, MandStringGrid.Row]]);

      Prepare (orgref, -1, -1);

      BesMandantCheckListBox.Visible := False;
    end;

    if (editform.ShowModal = mrOK) then begin
      if (editform.PageControl1.ActivePage = editform.StammTabSheet) then
        fConfigTab := -1
      else
        fConfigTab := editform.OptionPageControl.ActivePageIndex;

      try
        res := CopyMandant   (orgref,
                              editform.NameEdit.Text,
                              editform.ShortNameEdit.Text,
                              editform.BeschreibungEdit.Text,
                              editform.ILNEdit.Text,
                              editform.ERPIDEdit.Text,
                              editform.DXFEdit.Text,
                              True,
                              ref);
      except
        res := -9;
      end;

      if (res = 0) and editform.BesMandGroupBox.Visible and editform.BesMandantCheckListBox.Enabled then
      begin
        ClearMandantBesMandant(ref, LVSDatenModul.AktLagerRef);
        for besMandName in editform.BesMandantCheckListBox.Items do
        begin
          if editform.BesMandantCheckListBox.Checked[editform.BesMandantCheckListBox.Items.IndexOf(besMandName)] then
          begin
            res := InsertMandantBesMandant(ref, GetMandantRefFromName(besmandName), LVSDatenModul.AktLagerRef);
          end;
        end;
      end;

      if (res = 0) then begin
        res := DefMandantAdr (ref,
                              editform.AdrEdit.Text,
                              editform.RoadEdit.Text,
                              editform.PLZEdit.Text,
                              editform.OrtEdit.Text,
                              editform.LandEdit.Text);

        if (res = 0) and editform.AbsTabSheet.TabVisible then begin
          res := SetMandantAbsenderAdresse(editform, ref);
        end;

        if (res = 0) and editform.AdrTabSheet.TabVisible then begin
          res := SetMandantNiederlassungAdresse(editform, ref);
        end;

        if (res = 0) and editform.MandDatenTabSheet.TabVisible then
          res := SetMandantStammdaten (ref,
                                       editform.StammGFEdit.Text,
                                       editform.StammContactEdit.Text,
                                       editform.StammMailEdit.Text,
                                       editform.StammURLEdit.Text,
                                       editform.StammTaxNrEdit.Text,
                                       editform.StammVATEdit.Text,
                                       editform.StammHREdit.Text);

        if (res = 0) and editform.MandDatenTabSheet.TabVisible then
          res := SetMandantBank (ref,
                                 editform.StammBankEdit.Text,
                                 editform.StammIBANEdit.Text,
                                 editform.StammBICEdit.Text);

        if (res = 0) and editform.StammEORIEdit.Visible then
          res := SetMandantEORI (ref, editform.StammEORIEdit.Text);

        if (res = 0) and editform.CurrencyComboBox.Visible then
          res := SetMandantCurrency (ref, GetComboBoxDBItemWert (editform.CurrencyComboBox));

        if (res = 0) and editform.MandDatenTabSheet.TabVisible then
          res := SetMandantLogo (ref, editform.StammLogoName);

        if (res = 0) and editform.AvisTabSheet.TabVisible then begin
          avisopt := '********';

          if (editform.AVISMailAvisCheckBox.Checked) then
            avisopt [4] := '1';

          if (editform.AVISMailLSCheckBox.Checked) then
            avisopt [3] := '1';

          res := SetMandantAvisierung (ref, editform.AvisMailEdit.Text, avisopt);
        end;

        if (res = 0) and  (editform.BioKontrollNrEdit.Visible) then
          res := SetMandantBioKontrollNr (ref, editform.BioKontrollNrEdit.Text);

        if (res = 0) and (editform.StdPickHintEdit.Visible or editform.StdPackHintEdit.Visible) then
          res := SetMandantStandartHints (ref, editform.StdPickHintEdit.Text, editform.StdPackHintEdit.Text);

        if (res = 0) and editform.ReturnHintMemo.Visible then
          res := SetMandantReturnHint (ref, editform.ReturnHintMemo.Text);

        if (res = 0) and editform.ArtikelTabSheet.TabVisible then begin
          if editform.ArtikelPrefixEdit.Visible then
            res := SetMandantArtikelConfig (ref,
                                            editform.ArtikelPrefixEdit.Text,
                                            editform.ArtikelSuffixEdit.Text,
                                            GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                            GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                            GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                            GetIntegerParameter (editform.ArtikelLenEdit.Text))
          else
            res := SetMandantArtikelConfig (ref,
                                            editform.ArtikelSuffixEdit.Text,
                                            GetIntegerParameter (editform.ArtikelNextEdit.Text),
                                            GetIntegerParameter (editform.ArtikelStartEdit.Text),
                                            GetIntegerParameter (editform.ArtikelEndEdit.Text),
                                            GetIntegerParameter (editform.ArtikelLenEdit.Text));
        end;

        if (res = 0) and editform.ZollGroupBox.Visible then begin
          res := SetMandantZollDaten (ref,
                                      GetCheckboxStat(editform.AutoZollAnmeldungCheckBox),
                                      editform.BaseTaricNrEdit.Text);
        end;

        if (res = 0) and editform.InvOptionGroupBox.Visible then begin
          opt := '';

          opt := SetOpt (opt, 1, editform.InvOptSingleScanCheckBox.State);
          opt := SetOpt (opt, 2, editform.InvOptGoLPCheckBox.State);
          opt := SetOpt (opt, 3, editform.InvSuggestArtikelCheckBox.State);
          opt := SetOpt (opt, 6, editform.InvOptNurMengeCheckBox.State);
          opt := SetOpt (opt, 7, editform.InvSuggestLEBesCheckBox.State);

          res := SetMandantInvOption (ref, opt);
        end;

        if (res = 0) and editform.InvChkOptionGroupBox.Visible then begin
          opt := '';

          opt := SetOpt (opt, 1, editform.InvChkOptSingleScanCheckBox.State);
          opt := SetOpt (opt, 2, editform.InvChkOptGoLPCheckBox.State);
          opt := SetOpt (opt, 3, editform.InvChkSuggestArtikelCheckBox.State);
          opt := SetOpt (opt, 6, editform.InvChkOptNurMengeCheckBox.State);
          opt := SetOpt (opt, 7, editform.InvChkSuggestLEBesCheckBox.State);

          res := SetMandantInvCheckOption (ref, opt);
        end;
      end;

      if (res = 0) Then begin
        UpdateQuery (ref);
      end else begin
        MessageDLG ('Fehler beim Kopieren eines neuen Mandanten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
      end;
    end;

    editform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.CommButtonClick(Sender: TObject);
var
  partnerform : TKomPartnerForm;
begin
  if (MandStringGrid.Row <> -1) then begin
    partnerform := TKomPartnerForm.Create (Self);

    partnerform.Mandant := MandStringGrid.Cells [1,MandStringGrid.Row];
    partnerform.Lager   := '';

    try
      partnerform.ShowModal;
    except
    end;

    partnerform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.LagerButtonClick(Sender: TObject);
var
  res,
  idx     : Integer;
  relfrom : TMandantRelLagerForm;
begin
  relfrom := TMandantRelLagerForm.Create (Self);

  relfrom.RefMand := TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref;
  relfrom.Caption := 'Lager zum Mandanten ' + MandStringGrid.Cells [1,MandStringGrid.Row] + ' zuordnen';

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF,NAME,BESCHREIBUNG from V_PCD_LAGER order by NAME');

  try
    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      relfrom.MandantCheckListBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TListBoxRef.Create(ADOQuery1.Fields [0].AsInteger));

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF_LAGER from V_MANDANT_REL_LAGER where REF_MAND='+IntToStr (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref));

    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      idx := 0;

      while (idx < relfrom.MandantCheckListBox.Items.Count) do begin
        if (GetListBoxRef (relfrom.MandantCheckListBox, idx) = ADOQuery1.Fields [0].AsInteger) Then begin
          relfrom.MandantCheckListBox.Checked [idx] := True;

          idx := relfrom.MandantCheckListBox.Items.Count;
        end else Inc (idx);
      end;

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;
  except
  end;

  if (relfrom.ShowModal = mrOk) then begin
    idx := 0;
    res := 0;

    while (idx < relfrom.MandantCheckListBox.Items.Count) and (res = 0) do begin
      if (relfrom.MandantCheckListBox.Checked [idx]) then
        res := DefMandantLager (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, GetListBoxRef (relfrom.MandantCheckListBox, idx), '1')
      else res := DefMandantLager (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, GetListBoxRef (relfrom.MandantCheckListBox, idx), '0');

      Inc (Idx);
    end;

    if (res <> 0) then
      MessageDLG ('Fehler beim Zuordnen des Lagers '+relfrom.MandantCheckListBox.Items [idx]+' zum Mandanten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
  end;

  relfrom.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.SubMandDeactiveMenuItemClick(Sender: TObject);
var
  res : Integer;
begin
  if (Assigned (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0])) then begin
    if (MessagedLG ('Wollen Sie den Untermandanten wirklich deaktivieren?', mtConfirmation, [mbYes, mbNo, mbCancel], 0) = mrYes) then begin
      res := InactivateMandant (TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref);

      if (res <> 0) Then
        MessageDLG ('Fehler beim Deaktivieren des Untermandanten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
      else begin
        SubMandantUpdateQuery (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row]).Ref)
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.SubMandDelMenuItemClick(Sender: TObject);
var
  res,
  subref : Integer;
begin
  if (Assigned (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0])) then begin
    if (MessagedLG ('Wollen Sie den Untermandanten wirklich löschen?', mtConfirmation, [mbYes, mbNo, mbCancel], 0) = mrYes) then begin
      res := DeleteMandant (TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref);

      if (res <> 0) Then
        MessageDLG ('Fehler beim Löschen des Untermandanten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
      else begin
        if (SubMandStringGrid.RowCount = SubMandStringGrid.FixedRows + 1) then
          subref := -1
        else if (SubMandStringGrid.Row < (SubMandStringGrid.RowCount - 1)) then
          subref := TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row]).Ref
        else
          subref := TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row - 1]).Ref;

        SubMandantUpdateQuery (TGridRef (MandStringGrid.Rows [MandStringGrid.Row].Objects [0]).Ref, subref)
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TMandantForm.SubMandLocationMenuItemClick(Sender: TObject);
var
  res,
  idx     : Integer;
  relfrom : TMandantRelLagerForm;
begin
  relfrom := TMandantRelLagerForm.Create (Self);

  relfrom.RefMand := TGridRef (SubMandStringGrid.Rows [SubMandStringGrid.Row].Objects [0]).Ref;
  relfrom.Caption := 'Untermandanten ' + SubMandStringGrid.Cells [1,SubMandStringGrid.Row] + ' den Niederlassungen zuordnen';

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF,NAME,BESCHREIBUNG from V_PCD_LOCATION order by NAME');

  try
    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      relfrom.MandantCheckListBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TListBoxRef.Create(ADOQuery1.Fields [0].AsInteger));

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF_LOCATION from V_MANDANT_REL_LOCATION where REF_MAND='+IntToStr (relfrom.RefMand));

    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      idx := 0;

      while (idx < relfrom.MandantCheckListBox.Items.Count) do begin
        if (GetListBoxRef (relfrom.MandantCheckListBox, idx) = ADOQuery1.Fields [0].AsInteger) Then begin
          relfrom.MandantCheckListBox.Checked [idx] := True;

          break;
        end;

        Inc (idx);
      end;

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;
  except
  end;

  if (relfrom.ShowModal = mrOk) then begin
    idx := 0;
    res := 0;

    while (idx < relfrom.MandantCheckListBox.Items.Count) and (res = 0) do begin
      if (relfrom.MandantCheckListBox.Checked [idx]) then
        res := DefMandantLocation (relfrom.RefMand, GetListBoxRef (relfrom.MandantCheckListBox, idx), '1')
      else res := DefMandantLocation (relfrom.RefMand, GetListBoxRef (relfrom.MandantCheckListBox, idx), '0');

      Inc (Idx);
    end;

    if (res <> 0) then
      MessageDLG ('Fehler beim Zuordnen der Niederlassung '+relfrom.MandantCheckListBox.Items [idx]+' zum Untermandanten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
  end;

  relfrom.Release;
end;

end.
