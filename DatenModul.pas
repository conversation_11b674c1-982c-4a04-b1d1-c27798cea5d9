﻿//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : DatenModul
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/XE11/DatenModul.pas $
// $Revision: 145 $
// $Modtime: 9.01.24 8:29 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Zentrales Datenmodul für die Verbindung zur DB
//*****************************************************************************
{$WARN UNIT_PLATFORM OFF}
{$WARN SYMBOL_PLATFORM OFF}

unit DatenModul;

interface

uses
  Windows, Messages, SysUtils, Classes, Controls, DB, ADODB, ExtCtrls, DBAccess,
  Ora, DASQLMonitor, OraSQLMonitor, OraCall, OraSmart;

const
  DB_ERROR           = -1;

  dbOptWEBesPosRef   = 1;
  dbOptWEArEinheit   = 2;
  dbOptVertArEinheit = 3;
  dbOptBatchConfig   = 4;

type
  EOracleRetryException = class (Exception);

  TLVSDatenModul = class;

  TWaitThread = class (TThread)
  private
    OwnerDatenModul : TLVSDatenModul;

  protected
    procedure Execute; override;

  public
    constructor Create (DatenModul : TLVSDatenModul);
  end;

  TTransactionType = (trCommit, trRollback);

  TTransacSession = class
    fSession          : TObject;
    fTransactionLevel : Integer;
    fRollbackFlag     : Boolean;

    constructor Create;
  end;

  TLVSDatenModul = class(TDataModule)
    MainADOConnection: TADOConnection;
    ADOCommand1: TADOCommand;
    ADOQuery1: TADOQuery;
    OraMainSession: TOraSession;
    OraSQL1: TOraSQL;
    OraSQLMonitor1: TOraSQLMonitor;
    procedure MainADOConnectionAfterConnect(Sender: TObject);
    procedure MainADOConnectionBeforeDisconnect(Sender: TObject);
    procedure MainADOConnectionAfterDisconnect(Sender: TObject);
    procedure DataModuleCreate(Sender: TObject);
    procedure DataModuleDestroy(Sender: TObject);
    procedure MainADOConnectionWillExecute(Connection: TADOConnection;
      var CommandText: WideString; var CursorType: TCursorType;
      var LockType: TADOLockType; var CommandType: TCommandType;
      var ExecuteOptions: TExecuteOptions; var EventStatus: TEventStatus;
      const Command: _Command; const Recordset: _Recordset);
    procedure MainADOConnectionExecuteComplete(Connection: TADOConnection;
      RecordsAffected: Integer; const Error: Error;
      var EventStatus: TEventStatus; const Command: _Command;
      const Recordset: _Recordset);
    procedure MainADOConnectionBeforeConnect(Sender: TObject);
    procedure OraMainSessionAfterConnect(Sender: TObject);
    procedure OraSQL1BeforeExecute(Sender: TObject);
  private
    WindowHandle : hWnd;

    fSessionList      : TList;

    fLastBuchungsNr   : Integer;

    fDataSource       : String;
    fDBVersion        : Integer;
    fDBOptions        : String;

    fSQLExecStart     : DWORD;
    fAktSession       : String;
    fAktODACSession   : String;

    fMaxTraceFileSize  : Integer;
    fMaxTraceFileCount : Integer;
    fDBMSTraceCount    : Integer;

    fDBMSTraceEnabled : Boolean;

    fSQLTraceStream,
    fDBMSTraceStream  : TFileStream;

    fDBConnect    : TNotifyEvent;
    fDBDisconnect : TNotifyEvent;

    fDBQueryCount : Integer;
    DBQueryWait  : DWORD;
    AltCursor    : TCursor;

    fAktPLSTraceLevel : Integer;

    fIsConnected : Boolean;
    fOSAuthent   : Boolean;

    fAktUser,
    fAktDBUser,
    fAktUserNumID : String;

    fAktUserRef,
    fAktFirmaRef,
    fAktLagerRef,
    fAktLocationRef,
    fAktMandantRef,
    fMaxSessions    : Integer;

    fQueryStart       : Int64;  // Lauzeitmessung für TSmartQuerys
    fQueryStartTick   : Int64;

    WaitThread  : TWaitThread;


    procedure WinProc (var WndMessage:TMessage);

    function CheckTraceFileSize : Integer;

    function SetLocationName (const LocationName : String) : Integer;
    function SetLagerName    (const LagerName : String) : Integer;
    function SetMandantName  (const MandantName : String) : Integer;
    function SetUserName     (const UserName : String) : Integer;

    function InitBugReport (const HeaderLine : String) : TMemoryStream;

    procedure QueryAfterExecute(Sender: TObject; Result: Boolean);
    procedure QueryBeforeExecute(Sender: TObject);
  public
    Schema,
    System           : String;

    AktHost,
    AktFirma,
    ProxyUser,
    DBUser,
    DBPasswd,
    PrintDBUser,
    PrintDBPasswd,
    AktVersion,
    AktLager,
    AktLocation,
    AktSprache,
    AktMandant,
    AktConfigName,
    AktClientName    : String;

    LastLVSErrorCode : Integer;
    LastSQLErrorText : String;
    LastLVSErrorText : String;
    ConnectionString : String;

    property DataSource      : String  read fDataSource;
    property DatabaseVersion : Integer read fDBVersion;

    property IsConnected    : Boolean read fIsConnected;

    property AktUserRef     : integer read fAktUserRef;
    property AktFirmaRef    : integer read fAktFirmaRef;
    property AktLagerRef    : integer read fAktLagerRef;
    property AktLocationRef : integer read fAktLocationRef;
    property AktMandantRef  : integer read fAktMandantRef;
    property OSAuthent      : Boolean read fOSAuthent;
    property MaxSessions    : Integer read fMaxSessions;
    property LastBuchungsNr : Integer read fLastBuchungsNr;

    property AktUser        : String read fAktUser;
    property AktDBUser      : String read fAktDBUser;
    property AktUserNumID   : String read fAktUserNumID;

    property AktSessionID     : String read fAktSession;
    property AktODACSessionID : String read fAktODACSession;

    property DBQueryCount : Integer read fDBQueryCount;

    property MaxTraceFileCount : Integer read fMaxTraceFileCount write fMaxTraceFileCount;
    property MaxTraceFileSize  : Integer read fMaxTraceFileSize  write fMaxTraceFileSize;

    function CreateSmartQuery (AOwner: TComponent; const Name : String) : TSmartQuery;

    function SetSprache     (const Sprache : String) : Integer;

    function OpenDatabase (const UserName, DBUserName, Password : String; var ErrText : String) : Integer;
    function CloseDatabase : integer;

    function CheckLVSLogin  (const MandantName, LocationName, LagerName : String; var ErrorText : String) : Integer;
    function LVSLogin       (const MandantName, LocationName, LagerName : String) : Integer;
    function WriteProtokoll (const MandantName, LagerName, Art, Gruppe, Vorgang : String; const ErrorText : String) : Integer; overload;
    function WriteProtokoll (const MandantName, SubMandantName, LagerName, Art, Gruppe, Vorgang : String; const ErrorText : String) : Integer; overload;

    function ReinitSession : Integer;

    function BeginTransaction       (Session : TADOConnection) : Integer; overload;
    function GetTransactionLevel    (Session : TADOConnection) : Integer; overload;
    procedure ResetTransactionError  (Session : TADOConnection); overload;
    function EndTransaction         (Session : TADOConnection; const CommitFlag : TTransactionType; const ImmediateFlag : Boolean = False) : Integer; overload;

    function BeginTransaction       (Session : TOraSession) : Integer; overload;
    function GetTransactionLevel    (Session : TOraSession) : Integer; overload;
    procedure ResetTransactionError  (Session : TOraSession); overload;
    function EndTransaction         (Session : TOraSession; const CommitFlag : TTransactionType; const ImmediateFlag : Boolean = False) : Integer; overload;

    function CallStoreProcedure (StoredProcedure : TADOStoredProc) : Integer; overload;
    function CallStoreProcedure (StoredProcedure : TOraStoredProc) : Integer; overload;

    function CallStoreFunction (StoredProcedure : TADOStoredProc; const TraceFlag : Boolean = True) : Integer;

    function InitDBMSTrace (const TraceLevel : Integer) : Integer;
    function OpenDBMSTrace (const NewFlag : Boolean = False) : Integer;
    function CloseDBMSTrace : Integer;
    function ReadDBMSOutput    (const ProcName, ErrorText : String; ErrorCode : Integer; ReportStream : TStream = Nil) : Integer;
    function ReadOraDBMSOutput (const ProcName, ErrorText : String; ErrorCode : Integer; ReportStream : TStream = Nil) : Integer;

    procedure SetDBMSTracingOn;
    procedure SetDBMSTracingOff;

    function CheckDBOptions (const OptIdx : Integer) : Boolean;

    procedure TraceSQL (const Line : String);

    procedure SQLErrorHandler (const ErrorLocation, ErrorCode : String);

    procedure CloseAllDatasets;

    function  ViewExits       (const ViewName : String) : Boolean;
    function  ViewColumnExits (const ViewName, ColName : String) : Boolean;
    function  FunctionExits   (const PackageName, FunctName : String) : Boolean;
  published
    property OnDBConnect    : TNotifyEvent read fDBConnect    write fDBConnect;
    property OnDBDisconnect : TNotifyEvent read fDBDisconnect write fDBDisconnect;
  end;

function BuildOracleDateString   (const Datum : TDateTime; const DateFlag : Boolean = False; const MidnightFlag : Boolean = False) : String; overload;
function BuildOracleDateString   (const Datum : String; const DateFlag : Boolean = False) : String; overload;

function BuildSQLString          (const Line: string): string;

function GetPLSQLParameter (const RefParam : Integer) : Variant; overload;
function GetPLSQLParameter (const DTParam : TDateTime) : Variant; overload;
function GetPLSQLParameter (const RefParam : Char) : Variant; overload;
function GetPLSQLParameter (const BoolParam : Boolean) : Variant; overload;
function GetPLSQLParameter (const Int64Param : Int64) : Variant; overload;
function GetPLSQLDoubleParameter (const FloatParam : Double) : Variant; overload;

var
  LVSDatenModul: TLVSDatenModul;

implementation

uses
{$ifdef TRACE}
    Trace,
{$endif}

  VerInfos, StringUtils, Variants, ConfigModul, Forms, MessageModul, ChangePasswdDLG, ErrorTracking, madExcept, madNVBitmap, Dialogs,
  FrontendMessages, LVSSecurity, ACOModul, LVSBenutzer, BetterADODataSet, Timers, ResourceText, LVSGlobalDaten;

{$R *.dfm}

const
  CRLF = #13+#10;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetPLSQLParameter (const RefParam : Integer) : Variant;
begin
  if (RefParam = -1) then
    Result := NULL
  else
    Result := RefParam;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetPLSQLParameter (const RefParam : Char) : Variant;
begin
  if (RefParam = #0) then
    Result := NULL
  else
    Result := RefParam;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 08.01.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetPLSQLDoubleParameter (const FloatParam : Double) : Variant;
begin
  if (FloatParam < 0) then
    Result := NULL
  else
    Result := FloatParam;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 08.01.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetPLSQLParameter (const Int64Param : Int64) : Variant;
begin
  if (Int64Param < 0) then
    Result := NULL
  else
    Result := Int64Param;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetPLSQLParameter (const DTParam : TDateTime) : Variant;
begin
  //Alles was vor dem 1.1.1900 ist wird NULL
  if (DTParam <= 10) then
    Result := NULL
  else
    Result := DTParam;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetPLSQLParameter (const BoolParam : Boolean) : Variant; overload;
begin
  if (BoolParam) then
    Result := '1'
  else
    Result := '0';
end;

constructor TTransacSession.Create;
begin
  fSession          := Nil;
  fTransactionLevel := 0;
  fRollbackFlag     := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TWaitThread.Execute;
var
  dbgstr : String;
begin
  while not (Terminated) do begin
    with OwnerDatenModul do begin
      {$ifdef DEBUG}
        dbgstr := 'fDBQueryCount:'+IntToStr (fDBQueryCount);
        OutputDebugString (PChar (@dbgstr[1]));
      {$endif}

      if (fDBQueryCount > 0) and (DBQueryWait <> 0) and (GetTickCount > DBQueryWait) then begin
        DBQueryWait := 0;

        if ((Screen.Cursor = crSQLWait) or (Screen.Cursor = crHourGlass)) then
          altcursor := 1
        else begin
          {$ifdef DEBUG}
            OutputDebugString ('SetCursor');
          {$endif}

          altcursor := Screen.Cursor;
          Windows.SetCursor (Screen.Cursors [crSQLWait]);
        end;
      end;
    end;

    Sleep (1000);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TWaitThread.Create (DatenModul : TLVSDatenModul);
begin
  OwnerDatenModul := DatenModul;

  inherited Create (True);

  Resume;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function BuildOracleDateString (const Datum : String; const DateFlag : Boolean = False) : String;
begin
  if (DateFlag) then
    Result := 'TO_DATE (' + #39 + Datum + #39 + ',' + #39 + 'DD.MM.YYYY'+ #39 + ')'
  else Result := 'TO_DATE (' + #39 + Datum + #39 + ',' + #39 + 'DD.MM.YYYY HH24:MI:SS'+ #39 + ')'
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function BuildOracleDateString (const Datum : TDateTime; const DateFlag, MidnightFlag : Boolean) : String;
begin
  if (DateFlag) then
    Result := 'TO_DATE (' + #39 + FormatDateTime ('dd.mm.yyyy', Datum) + #39 + ',' + #39 + 'DD.MM.YYYY'+ #39 + ')'
  else if (MidnightFlag) then
    Result := 'TO_DATE (' + #39 + FormatDateTime ('dd.mm.yyyy', Datum) + ' 23:59:59'+#39 + ',' + #39 + 'DD.MM.YYYY HH24:MI:SS'+ #39 + ')'
  else Result := 'TO_DATE (' + #39 + FormatDateTime ('dd.mm.yyyy hh:nn:ss', Datum) + #39 + ',' + #39 + 'DD.MM.YYYY HH24:MI:SS'+ #39 + ')'
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function BuildSQLString(const Line: string): string;
var
  k: Integer;
  endflag : Boolean;
  //retstr: string;
begin
  if (Length (Line) = 0) Then
    Result := #39+#39
  else begin
    Result := '';

    endflag := False;

    for k:=1 to Length (Line) do begin
      (*
      if ((Ord (valstr [k]) < 32) or (Ord (valstr [k]) > 127)) then begin
        if (k = 1) then Result := Result + #39;
        Result := Result + #39 + '||CHR('+IntToStr (Ord (valstr [k]))+')';
        if (k < Length (valstr)) then
          Result := Result +'||'+#39
        else endflag := True;
      end else
      *)
      if (Line [k] = '&') then begin
        if (k = 1) then Result := Result + #39;
        Result := Result + #39 + '||CHR(38)';
        if (k < Length (Line)) then
          Result := Result +'||'+#39
        else endflag := True;
      end else if (Line [k] = '"') then begin
        if (k > 1) then Result := Result + #39 + '||';
        Result := Result + #39 + '||CHR (34)||' + #39;
        if (k < Length (Line)) then
          Result := Result +'||'+#39
        else endflag := True;
      end else if (Line [k] = #96) then begin
        if (k = 1) then Result := Result + #39;
        Result := Result + #39 + '||CHR(96)';
        if (k < Length (Line)) then
          Result := Result +'||'+#39
        else endflag := True;
      end else if (Line [k] = #39) then begin
        if (k = 1) then Result := Result + #39;
        Result := Result + #39;
        if (k < Length (Line)) then
          Result := Result + #39
        else endflag := True;
      end else begin
        if (k = 1) then Result := Result + #39;
        Result := Result + Line [k];
      end;
    end;

    if not (endflag) then
      Result := Result + #39;
  end;

  (*
  for k := 1 to Length(Line) do begin
    if ((Ord(Line[k]) < 32) or (Ord(Line[k]) > 127)) then begin
      retstr := retstr + #39 + '||CHR (' + IntToStr(Ord(Line[k])) + ')||' + #39;
    end else if (Line[k] = '&') then begin
      retstr := retstr + #39 + '||CHR (38)||' + #39;
    end else if (Line[k] = '"') then begin
      retstr := retstr + #39 + '||CHR (34)||' + #39;
    end else if (Line[k] = #96) then begin
      retstr := retstr + #39 + '||CHR (96)||' + #39;
    end else if (Line[k] = #39) then
      retstr := retstr + #39 + #39
    else retstr := retstr + Line[k];
  end;
  *)

  //BuildSQLString := retstr;
end;

procedure TLVSDatenModul.QueryAfterExecute(Sender: TObject; Result: Boolean);
var
  logstr  : AnsiString;
  loglen  : Integer;
  tick    : Int64;
begin
  tick := GetPerformanceCount;

  if Assigned (fDBMSTraceStream) then begin
    logstr := DateTimeToStr (Now) + ':' + (Sender as TCustomOraQuery).Name;

    if not Result then
      logstr := logstr + '='+ copy ((Sender as TCustomOraQuery).SQL.Text, 1, 50) + ' -> Error'
    else
      logstr := logstr + '='+ copy ((Sender as TCustomOraQuery).SQL.Text, 1, 50) + ' -> ' + IntToStr (GetPerformanceCountDiff (tick, fQueryStart) div 1000)+ ' ms';

    logstr := logstr + #13+#10;

    //logstr := logstr + DateTimeToStr (Now) + ':' + (Sender as TCustomOraQuery).Name + '='+ copy ((Sender as TCustomOraQuery).SQL.Text, 1, 50) + ' -> ' + IntToStr (GetTickCount64 - fQueryStartTick)+ ' ms' + #13+#10;
    loglen := Length (logstr);

    fDBMSTraceStream.Write (logstr[1], loglen);
  end;
end;

procedure TLVSDatenModul.QueryBeforeExecute(Sender: TObject);
var
  logstr  : AnsiString;
  loglen  : Integer;
begin
  (*
  if Assigned (fDBMSTraceStream) then begin
    logstr := DateTimeToStr (Now) + ':' + (Sender as TCustomOraQuery).Name + #13+#10;
    loglen := Length (logstr);

    fDBMSTraceStream.Write (logstr[1], loglen);
  end;
  *)
  fQueryStart     := GetPerformanceCount;
  fQueryStartTick := GetTickCount64;
end;

function TLVSDatenModul.CreateSmartQuery (AOwner: TComponent; const Name : String) : TSmartQuery;
begin
  Result := TSmartQuery.Create (AOwner);

  if not Assigned (AOwner) then
    Result.Name := Name
  else
    Result.Name := AOwner.Name+'_'+Name;

  Result.ReadOnly := True;
  Result.Session := OraMainSession;
  Result.AfterExecute := QueryAfterExecute;
  Result.BeforeExecute := QueryBeforeExecute;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.BeginTransaction (Session : TADOConnection) : Integer;
var
  idx   : Integer;
  entry : TTransacSession;
begin
  idx := 0;

  while (idx < fSessionList.Count) and (TTransacSession (fSessionList [idx]).fSession <> Session) do
    Inc (idx);

  if (idx < fSessionList.Count) then
    entry := TTransacSession (fSessionList [idx])
  else begin
    entry := TTransacSession.Create;

    entry.fSession := Session;

    fSessionList.Add (entry);
  end;

  if (entry.fTransactionLevel = 0) then begin
    entry.fRollbackFlag := False;
    Session.BeginTrans;
  end;

  Inc (entry.fTransactionLevel);

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.11.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.GetTransactionLevel    (Session : TADOConnection) : Integer;
var
  idx   : Integer;
begin
  idx := 0;

  while (idx < fSessionList.Count) and (TTransacSession (fSessionList [idx]).fSession <> Session) do
    Inc (idx);

  if (idx < fSessionList.Count) then
    Result := TTransacSession (fSessionList [idx]).fTransactionLevel
  else
    Result := -1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.11.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.ResetTransactionError  (Session : TADOConnection);
var
  idx   : Integer;
begin
  idx := 0;

  while (idx < fSessionList.Count) and (TTransacSession (fSessionList [idx]).fSession <> Session) do
    Inc (idx);

  if (idx < fSessionList.Count) then
    TTransacSession (fSessionList [idx]).fRollbackFlag := false;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.EndTransaction (Session : TADOConnection; const CommitFlag : TTransactionType; const ImmediateFlag : Boolean) : Integer;
var
  res,
  idx    : Integer;
  query  : TADOQuery;
  adocmd : TADOCommand;
  entry  : TTransacSession;
begin
  res := 0;

  idx := 0;

  while (idx < fSessionList.Count) and (TTransacSession (fSessionList [idx]).fSession <> Session) do
    Inc (idx);

  if (idx < fSessionList.Count) then
    entry := TTransacSession (fSessionList [idx])
  else
    raise ERangeError.CreateFmt('Transaction underflow',[]);

  if (entry.fTransactionLevel = 0) Then begin
    //Beim Rollback macht das nichts
    if (CommitFlag = trRollback) then
    else
      raise ERangeError.CreateFmt('Transaction underflow',[]);
  end else if ImmediateFlag then
    entry.fTransactionLevel := 0
  else Dec (entry.fTransactionLevel);

  if (CommitFlag = trRollback) then
    entry.fRollbackFlag := True;

  if (entry.fTransactionLevel = 0) then begin
    try
      if (entry.fRollbackFlag) then
        Session.RollbackTrans
      else begin
        query := TADOQuery.Create (Self);

        try
          query.LockType := ltReadOnly;
          query.Connection := Session;

          //Der aktuelle Session-ID auslesen
          query.SQL.Add ('select BUCHUNGS_NR from TMP_BUCHUNG');

          try
            query.Open;

            try
              if not query.Fields [0].IsNull then
                fLastBuchungsNr := query.Fields [0].AsInteger;
            except
              fLastBuchungsNr := -1;
            end;

            query.Close;
          except
            fLastBuchungsNr := -1;
          end;
        finally
          query.Free;
        end;

        Session.CommitTrans;

        adocmd := TADOCommand.Create (Self);

        try
          adocmd.Connection := Session;
          adocmd.CommandText := 'begin PA_SIGNAL.SendSignal; end;';

          {$ifndef SQLDebug}
            TraceSQL (adocmd.CommandText);
          {$endif}

          adocmd.Connection.BeginTrans;
          try
            adocmd.Execute;
          except
          end;

          //ReadDBMSOutput ('TLVSDatenModul.EndTransaction','',0);

          adocmd.Connection.CommitTrans;
        finally
          adocmd.Free;
        end;
      end;
    except
      on E: Exception do begin
        res := -9;

        Session.RollbackTrans;

        LastSQLErrorText := E.Message;
      end;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.BeginTransaction (Session : TOraSession) : Integer;
var
  idx   : Integer;
  entry : TTransacSession;
begin
  idx := 0;

  while (idx < fSessionList.Count) and (TTransacSession (fSessionList [idx]).fSession <> Session) do
    Inc (idx);

  if (idx < fSessionList.Count) then
    entry := TTransacSession (fSessionList [idx])
  else begin
    entry := TTransacSession.Create;

    entry.fSession := Session;

    fSessionList.Add (entry);
  end;

  if (entry.fTransactionLevel = 0) then begin
    entry.fRollbackFlag := False;
    Session.StartTransaction;
  end;

  Inc (entry.fTransactionLevel);

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.11.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.GetTransactionLevel    (Session : TOraSession) : Integer;
var
  idx   : Integer;
begin
  idx := 0;

  while (idx < fSessionList.Count) and (TTransacSession (fSessionList [idx]).fSession <> Session) do
    Inc (idx);

  if (idx < fSessionList.Count) then
    Result := TTransacSession (fSessionList [idx]).fTransactionLevel
  else
    Result := -1; 
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.11.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.ResetTransactionError  (Session : TOraSession);
var
  idx   : Integer;
begin
  idx := 0;

  while (idx < fSessionList.Count) and (TTransacSession (fSessionList [idx]).fSession <> Session) do
    Inc (idx);

  if (idx < fSessionList.Count) then
    TTransacSession (fSessionList [idx]).fRollbackFlag := false;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.EndTransaction (Session : TOraSession; const CommitFlag : TTransactionType; const ImmediateFlag : Boolean) : Integer;
var
  res,
  idx     : Integer;
  query   : TSmartQuery;
  odaccmd : TOraSQL;
  entry   : TTransacSession;
begin
  res := 0;

  idx := 0;

  while (idx < fSessionList.Count) and (TTransacSession (fSessionList [idx]).fSession <> Session) do
    Inc (idx);

  if (idx < fSessionList.Count) then
    entry := TTransacSession (fSessionList [idx])
  else
    raise ERangeError.CreateFmt('Transaction underflow',[]);

  if (entry.fTransactionLevel = 0) Then begin
    //Beim Rollback macht das nichts
    if (CommitFlag = trRollback) then
    else
      raise ERangeError.CreateFmt('Transaction underflow',[]);
  end else if ImmediateFlag then
    entry.fTransactionLevel := 0
  else Dec (entry.fTransactionLevel);

  if (CommitFlag = trRollback) then
    entry.fRollbackFlag := True;

  if (entry.fTransactionLevel = 0) then begin
    try
      if (entry.fRollbackFlag) then
        Session.Rollback
      else begin
        query := TSmartQuery.Create (Self);

        try
          query.Connection := Session;

          //Der aktuelle Session-ID auslesen
          query.SQL.Add ('select BUCHUNGS_NR from TMP_BUCHUNG');

          try
            query.Open;

            try
              if not query.Fields [0].IsNull then
                fLastBuchungsNr := query.Fields [0].AsInteger;
            except
              fLastBuchungsNr := -1;
            end;

            query.Close;
          except
            fLastBuchungsNr := -1;
          end;
        finally
          query.Free;
        end;

        Session.Commit;

        odaccmd := TOraSQL.Create (Self);

        try
          odaccmd.Connection := Session;
          odaccmd.SQL.Add ('begin PA_SIGNAL.SendSignal; end;');

          Session.StartTransaction;

          try
            odaccmd.Execute;
          except
          end;

          //ReadDBMSOutput ('TLVSDatenModul.EndTransaction','',0);

          Session.Commit;
        finally
          odaccmd.Free;
        end;
      end;
    except
      on E: Exception do begin
        res := -9;

        Session.Rollback;

        LastSQLErrorText := E.Message;
      end;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.ReinitSession : Integer;
var
  dbres   : Integer;
  adocmd  : TADOCommand;
  odaccmd : TOraSQL;
begin
  dbres := 0;

  adocmd := TADOCommand.Create (Self);

  try
    adocmd.Connection := MainADOConnection;
    adocmd.CommandText := 'begin dbms_session.reset_package; end;';

    {$ifndef SQLDebug}
      TraceSQL (adocmd.CommandText);
    {$endif}

    try
      adocmd.Execute;
    except
      dbres := -9;
    end;

    if (dbres = 0) then begin
      //Die zwei :: sind wichtig, damit der Paraser den nicht als Parameter erkennt
      adocmd.CommandText := 'declare res integer; begin BASE_TRACING.InitTracing; res::=BASE_TRACING.SetTraceDevice (1,''''); BASE_TRACING.SetTraceLevel ('+IntToStr (fAktPLSTraceLevel)+'); end;';

      {$ifndef SQLDebug}
        TraceSQL (adocmd.CommandText);
      {$endif}

      try
        adocmd.Prepared := False;
        adocmd.Execute;
      except
        dbres := -9;
      end;
    end;
  finally
    adocmd.Free;
  end;

  {$ifdef UseODAC}
    dbres := 0;

    if (Length (fAktODACSession) > 0) then begin
      odaccmd := TOraSQL.Create (Self);

      try
        odaccmd.Session := OraMainSession;
        odaccmd.SQL.Add ('begin dbms_session.reset_package; end;');

        {$ifndef SQLDebug}
          TraceSQL (odaccmd.SQl.Text);
        {$endif}

        try
          odaccmd.Execute;
        except
          dbres := -9;
        end;

        if (dbres = 0) then begin
          odaccmd.SQL.Clear;
          odaccmd.SQL.Add ('declare res integer; begin BASE_TRACING.InitTracing; res := BASE_TRACING.SetTraceDevice (1,''''); BASE_TRACING.SetTraceLevel ('+IntToStr (fAktPLSTraceLevel)+'); end;');

          {$ifndef SQLDebug}
            TraceSQL (odaccmd.SQl.Text);
          {$endif}

          try
            odaccmd.Execute;
          except
            dbres := -9;
          end;
        end;
      finally
        odaccmd.Free;
      end;
    end;
  {$endif}

  if (dbres = 0) then SetUserName     (AktUser);
  if (dbres = 0) then SetSprache      (AktSprache);
  if (dbres = 0) then SetMandantName  (AktMandant);
  if (dbres = 0) then SetLocationName (AktLocation);
  if (dbres = 0) then SetLagerName    (AktLager);

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.CheckLVSLogin (const MandantName, LocationName, LagerName : String; var ErrorText : String) : Integer;
var
  dbok            : Boolean;
  dbres           : Integer;
  param           : TParameter;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  dbok := False;
  ErrorText := '';

  StoredProcedure := Nil;

  while (dbres = 0) and not (dbok) do begin
    try
      StoredProcedure := TADOStoredProc.Create (Nil);

      try
        with StoredProcedure do begin
          Connection    := MainADOConnection;

          ProcedureName := 'PA_LOGIN.CheckLocationLogon';

          Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, NULL);

          Parameters.CreateParameter('pUserID', ftString, pdInput, 64, AktUser);
          Parameters.CreateParameter('pClient', ftString, pdInput, 64, AktClientName);
          Parameters.CreateParameter('pMandant', ftString, pdInput, 64, MandantName);
          Parameters.CreateParameter('pLocation', ftString, pdInput, 64, LocationName);
          Parameters.CreateParameter('pLager', ftString, pdInput, 64, LagerName);

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, NULL);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 255, NULL);
        end;

        dbres := CallStoreProcedure (StoredProcedure);

        param := StoredProcedure.Parameters.FindParam ('pErrorText');
        if Assigned (param) and not (param.Value = NULL) then
          ErrorText := param.Value;
      finally
        StoredProcedure.Free;
      end;

      dbok := True;
    except
      on E: EOracleRetryException do begin
        ErrorTrackingModule.WriteErrorLog ('EOracleRetryException LVSLogin', e.ClassName + ' : ' + e.Message);
      end;

      on E: Exception do begin
        dbres := -9;

        if Assigned (StoredProcedure) then begin
          EndTransaction (StoredProcedure.Connection, trRollback);

          ReadDBMSOutput ('TLVSDatenModul.LVSLogin',E.Message,-9);
        end;
      end;
    end;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.LVSLogin (const MandantName, LocationName, LagerName : String) : Integer;
var
  dbok            : Boolean;
  dbres           : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  dbok := False;

  while (dbres = 0) and not (dbok) do begin
    try
      ADOCommand1.CommandText := 'begin '
                                +'BASE_TRACING.InitTracing;'
                                +'PA_SESSION_DATEN.SetMandant ('+#39+MandantName+#39+');'
                                +'PA_SESSION_DATEN.SetLocation ('+#39+LocationName+#39+');'
                                +'PA_SESSION_DATEN.SetLager ('+#39+LagerName+#39+');'
                                +'end;';

      BeginTransaction (ADOCommand1.Connection);

      {$ifndef SQLDebug}
        TraceSQL (ADOCommand1.CommandText);
      {$endif}

      ADOCommand1.Execute;

      OraSQL1.SQL.Clear;
      OraSQL1.SQL.Add (ADOCommand1.CommandText);

      try
        OraSQL1.Execute;

        OraSQL1.Session.Commit;
      except
        OraSQL1.Session.Rollback;
      end;

      StoredProcedure := TADOStoredProc.Create (Nil);

      try
        with StoredProcedure do begin
          Connection    := MainADOConnection;

          if (AktUser <> AktDBUser) then begin
            ProcedureName := 'PA_LOGIN.UserLogonEx';

            Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, NULL);
            Parameters.CreateParameter('pUserID', ftString, pdInput, 64, AktUser);
          end else begin
            ProcedureName := 'PA_LOGIN.UserLogon';

            Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, NULL);
          end;

          Parameters.CreateParameter('pClient', ftString, pdInput, 64, AktClientName);
          Parameters.CreateParameter('pProgram',ftString, pdInput, 64, ExtractFileName (ParamStr (0)));
          Parameters.CreateParameter('pVersion',ftString, pdInput, 64, FileVersion (4, 2));

          Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, NULL);
          Parameters.CreateParameter('pErrorText',ftString,pdOutput, 255, NULL);
        end;

        dbres := CallStoreProcedure (StoredProcedure);
      finally
        StoredProcedure.Free;
      end;

      if (dbres <> 0) then begin
        EndTransaction (ADOCommand1.Connection, trRollback);
      end else begin
        EndTransaction (ADOCommand1.Connection, trCommit);

        AktLocation := LocationName;
        AktLager    := LagerName;
        AktMandant  := MandantName;

        ADOQuery1.SQL.Clear;

        if (Length (AktMandant) = 0) then begin
          if (Length (AktLager) = 0) and (Length (AktLocation) = 0) then
            ADOQuery1.SQL.Add ('select -1, -1, -1 from dual')
          else if (Length (AktLager) = 0) then
            ADOQuery1.SQL.Add ('select loc.REF, -1, -1 from V_LOCATION loc where loc.NAME='+#39+LocationName+#39)
          else ADOQuery1.SQL.Add ('select nvl ((select REF_LOCATION from V_LOCATION_REL_LAGER where REF_LAGER=l.REF), -1), -1, l.REF from V_LAGER l where l.NAME='+#39+LagerName+#39);
        end else begin
          if (Length (AktLager) = 0) and (Length (AktLocation) = 0) then
            ADOQuery1.SQL.Add ('select -1, m.REF, -1 from V_MANDANT m where m.NAME='+#39+MandantName+#39)
          else if (Length (AktLager) = 0) Then
            ADOQuery1.SQL.Add ('select loc.REF, m.REF from V_MANDANT m, V_LOCATION loc where m.NAME='+#39+MandantName+#39+' and loc.NAME='+#39+LocationName+#39)
          else ADOQuery1.SQL.Add ('select nvl ((select REF_LOCATION from V_LOCATION_REL_LAGER where REF_LAGER=l.REF), -1), m.REF, l.REF from V_LAGER l, V_MANDANT m where l.NAME='+#39+LagerName+#39+' and m.NAME='+#39+MandantName+#39);
        end;

        try
          ADOQuery1.Open;

          fAktLocationRef := ADOQuery1.Fields [0].AsInteger;

          if (Length (AktMandant) = 0) Then
            fAktMandantRef := -1
          else fAktMandantRef := ADOQuery1.Fields [1].AsInteger;

          if (Length (AktLager) = 0) Then
            fAktLagerRef := -1
          else fAktLagerRef := ADOQuery1.Fields [2].AsInteger;

          ADOQuery1.Close;
        except
          dbres := -9;
        end;
      end;

      dbok := True;
    except
      on E: EOracleRetryException do begin
        ErrorTrackingModule.WriteErrorLog ('EOracleRetryException LVSLogin', e.ClassName + ' : ' + e.Message);
      end;

      on E: Exception do begin
        dbres := -9;

        EndTransaction (ADOCommand1.Connection, trRollback);

        ReadDBMSOutput ('TLVSDatenModul.LVSLogin',E.Message,-9);
      end;
    end;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.SetLocationName (const LocationName : String) : Integer;
var
  dbres : Integer;
begin
  dbres := 0;

  if (Length (LocationName) = 0) then
    fAktLocationRef := -1
  else begin
    ADOQuery1.Close;
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF from V_LOCATION where NAME=:location');
    ADOQuery1.Parameters [0].Value := LocationName;

    try
      ADOQuery1.Open;

      fAktLocationRef := ADOQuery1.Fields [0].AsInteger;

      ADOQuery1.Close;
    except
      dbres := -9;
    end;
  end;

  if (dbres = 0) then begin
    ADOCommand1.CommandText := 'begin PA_SESSION_DATEN.SetLocation ('+#39+LocationName+#39+'); end;';

    {$ifndef SQLDebug}
      TraceSQL (ADOCommand1.CommandText);
    {$endif}

    try
      ADOCommand1.Execute;

      AktLocation := LocationName;
    except
      dbres := -9;
    end;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.SetLagerName (const LagerName : String) : Integer;
var
  dbres : Integer;
begin
  dbres := 0;

  if (Length (LagerName) = 0) then
    fAktLagerRef := -1
  else begin
    ADOQuery1.Close;
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF from V_LAGER where NAME=:lager');
    ADOQuery1.Parameters [0].Value := LagerName;

    try
      ADOQuery1.Open;

      fAktLagerRef := ADOQuery1.Fields [0].AsInteger;

      ADOQuery1.Close;
    except
      dbres := -9;
    end;
  end;
  
  if (dbres = 0) then begin
    ADOCommand1.CommandText := 'begin PA_SESSION_DATEN.SetLager ('+#39+LagerName+#39+'); end;';

    {$ifndef SQLDebug}
      TraceSQL (ADOCommand1.CommandText);
    {$endif}

    try
      ADOCommand1.Execute;

      AktLager := LagerName;
    except
      dbres := -9;
    end;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.SetMandantName (const MandantName : String) : Integer;
var
  dbres : Integer;
begin
  dbres := 0;

  if (Length (MandantName) = 0) then
    fAktMandantRef := -1
  else begin
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF from V_MANDANT where NAME=:mandant');
    ADOQuery1.Parameters [0].Value := MandantName;

    try
      ADOQuery1.Open;

      fAktMandantRef := ADOQuery1.Fields [0].AsInteger;

      ADOQuery1.Close;
    except
      dbres := -9;
    end;
  end;

  if (dbres = 0) then begin
    ADOCommand1.CommandText := 'begin PA_SESSION_DATEN.SetMandant ('+#39+MandantName+#39+'); end;';
    try
      ADOCommand1.Connection.BeginTrans;

      ADOCommand1.Execute;

      ADOCommand1.Connection.CommitTrans;

      AktMandant := MandantName;
    except
      dbres := -9;

      ADOCommand1.Connection.RollbackTrans;
    end;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.SetSprache (const Sprache : String) : Integer;
var
  dbres : Integer;
begin
  dbres := 0;

  ADOCommand1.CommandText := 'begin PA_SESSION_DATEN.SetSprache ('+#39+Sprache+#39+'); end;';

  {$ifndef SQLDebug}
    TraceSQL (ADOCommand1.CommandText);
  {$endif}

  try
    ADOCommand1.Execute;

    AktSprache := Sprache;

    OraSQL1.SQL.Clear;
    OraSQL1.SQL.Add ('begin PA_SESSION_DATEN.SetSprache (:sprache); end;');
    OraSQL1.Params[0].Value := Sprache;

    try
      OraSQL1.Execute;
    except
      dbres := -9;
    end;
  except
    dbres := -9;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.SetUserName (const UserName : String) : Integer;
var
  dbres : Integer;
begin
  dbres := 0;

  ADOCommand1.CommandText := 'begin PA_SESSION_DATEN.SetUserName ('+#39+UserName+#39+'); end;';

  {$ifndef SQLDebug}
    TraceSQL (ADOCommand1.CommandText);
  {$endif}

  try
    ADOCommand1.Execute;

    OraSQL1.SQL.Clear;
    OraSQL1.SQL.Add ('begin PA_SESSION_DATEN.SetUserName (:user_name); end;');
    OraSQL1.Params[0].Value := UserName;

    try
      OraSQL1.Execute;
    except
      dbres := -9;
    end;
  except
    dbres := -9;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.WriteProtokoll (const MandantName, LagerName, Art, Gruppe, Vorgang : String; const ErrorText : String) : Integer;
var
  dbres  : Integer;
  oracmd : TOraSQL;
begin
  dbres := 0;

  if (fIsConnected and OraMainSession.Connected) then begin
    oracmd := TOraSQL.Create (Self);

    try
      oracmd.Session := OraMainSession;

      try
        BeginTransaction (oracmd.Session);

        oracmd.SQL.Add ('begin PA_PROTOKOLL.PROTOKOLL (:mand,:lager,:user,:art,:grp,:vor,:text); end;');
        oracmd.Params.ParamByName ('mand').Value := MandantName;
        oracmd.Params.ParamByName ('lager').Value := LagerName;
        oracmd.Params.ParamByName ('user').Value := AktUser;
        oracmd.Params.ParamByName ('art').Value := Art;
        oracmd.Params.ParamByName ('grp').Value := Gruppe;
        oracmd.Params.ParamByName ('vor').Value := Vorgang;
        oracmd.Params.ParamByName ('text').Value := ErrorText;

        {$ifndef SQLDebug}
          TraceSQL (oracmd.SQL.Text);
        {$endif}

        oracmd.Execute;

        EndTransaction (oracmd.Session, trCommit);
      except
        on E: Exception do begin
          EndTransaction (oracmd.Session, trRollback);

          ReadDBMSOutput ('TLVSDatenModul.WriteProtokoll',E.Message,-9);

          dbres := -9;
        end;
      end;
    finally
      oracmd.Free;
    end;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.WriteProtokoll (const MandantName, SubMandantName, LagerName, Art, Gruppe, Vorgang : String; const ErrorText : String) : Integer;
var
  dbres  : Integer;
  oracmd : TOraSQL;
begin
  dbres := 0;

  if (fIsConnected and OraMainSession.Connected) then begin
    oracmd := TOraSQL.Create (Self);

    try
      oracmd.Session := OraMainSession;

      try
        BeginTransaction (oracmd.Session);

        oracmd.SQL.Add ('begin PA_PROTOKOLL.PROTOKOLL (:mand,:submand,:lager,:user,:art,:grp,:vor,:text); end;');
        oracmd.Params.ParamByName ('mand').Value := MandantName;
        oracmd.Params.ParamByName ('submand').Value := SubMandantName;
        oracmd.Params.ParamByName ('lager').Value := LagerName;
        oracmd.Params.ParamByName ('user').Value := AktUser;
        oracmd.Params.ParamByName ('art').Value := Art;
        oracmd.Params.ParamByName ('grp').Value := Gruppe;
        oracmd.Params.ParamByName ('vor').Value := Vorgang;
        oracmd.Params.ParamByName ('text').Value := ErrorText;

        {$ifndef SQLDebug}
          TraceSQL (oracmd.SQL.Text);
        {$endif}

        oracmd.Execute;

        EndTransaction (oracmd.Session, trCommit);
      except
        on E: Exception do begin
          EndTransaction (oracmd.Session, trRollback);

          ReadDBMSOutput ('TLVSDatenModul.WriteProtokoll',E.Message,-9);

          dbres := -9;
        end;
      end;
    finally
      oracmd.Free;
    end;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.OpenDatabase (const UserName, DBUserName, Password : String; var ErrText : String) : Integer;
var
  dbres,
  stridx     : Integer;
  ok,
  pwchange   : Boolean;
  errstr,
  dbstr,
  passwd     : String;
  strpos,
  errcode    : Integer;
  passwdform : TChangePasswdForm;
begin
{$ifdef TRACE}
  FunctionStart ('TLVSDatenModul.OpenDatabase');

  TraceParameter ('UserName  ', UserName);
  TraceParameter ('DBUserName', DBUserName);
  TraceParameter ('Password  ', '*****');
{$endif}

  dbres := 0;

  fDataSource := '';

  stridx := Pos ('Data Source=', ConnectionString);
  if (stridx > 0) then begin
    stridx := stridx + Length ('Data Source=');

    while (stridx <= Length (ConnectionString)) and (ConnectionString [stridx] <> ';') do begin
      fDataSource := fDataSource + ConnectionString [stridx];

      Inc (stridx);
    end;
  end;

  try
    try
      OraMainSession.Close;

      MainADOConnection.Close;

      MainADOConnection.Cancel;

      {$ifdef TRACE}
        TraceParameter ('ConnectionString', ConnectionString);
      {$endif}

      MainADOConnection.ConnectionString := ConnectionString;

      try
        fAktUser := UserName;

        MainADOConnection.Open (DBUserName, Password);
      except
        on E: Exception do begin
          strpos := Pos ('ORA-', E.Message);

          if (strpos = 0) Then
            raise
          else begin
            errstr := Copy (E.Message, strpos + 4, 5);
            errcode := StrToInt (errstr);

            if (errcode = 28001) then begin
              passwdform := TChangePasswdForm.Create (Self);

              passwdform.Height := passwdform.Height - passwdform.OldPanel.Height;
              passwdform.OldPanel.Visible := False;
              passwdform.OldPanel.Height := 0;

              passwdform.UserLabel.Caption := UserName;

              if Assigned (LVSConfigModul) then begin
                passwdform.MinLength   := LVSConfigModul.PasswordRestriction.MinLength;
                passwdform.AlphaCase   := LVSConfigModul.PasswordRestriction.AlphaCase;
                passwdform.AlphaNum    := LVSConfigModul.PasswordRestriction.AlphaNum;
                passwdform.SpecialChar := LVSConfigModul.PasswordRestriction.SpecialChar;
              end;

              passwdform.ChangeButton.Caption := 'Ok';

              if (passwdform.ShowModal <> mrOk) Then
                ok := False
              else begin
                ok := True;
                passwd := passwdform.NewPwEdit.Text;
              end;

              passwdform.Free;
            end else begin
              if (errcode = 28000) then
                ErrText := FormatMessageText (1018, [])
              else if (errcode = 1017) then
                ErrText := FormatMessageText (1019, []);

              dbres := errcode;
              raise;
            end;
          end;
        end;
      end;
    except
      if (dbres = 0) then begin
        dbres := -9;
        ErrText := FormatMessageText (1020, []);
      end;
    end;

    if (dbres = 0) Then begin
      {$ifdef UNICODE}
        OraMainSession.Options.UseUnicode := True;
        OraMainSession.Options.UnicodeEnvironment := True;
      {$endif}

      OraMainSession.Username := DBUserName;
      OraMainSession.Password := Password;
      OraMainSession.Server   := fDataSource;
      OraMainSession.LoginPrompt := False;

      try
        DBAccess.ChangeCursor := False;

        OraMainSession.Open;
      except
      end;

      fIsConnected := True;

      fOSAuthent := False;

      DBUser   := DBUserName;
      DBPasswd := Password;

      if (Length (UserName) > 0) and (UserName <> '/') Then begin
        fAktDBUser := DBUserName;

        ADOQuery1.SQL.Clear;

        ADOQuery1.SQL.Add ('select USER_ID,USER_NUM_ID from V_SYS_BEN where upper (USER_ID)=:user_name');
        ADOQuery1.Parameters [0].Value := UpperCase (UserName);

        ADOQuery1.Open;

        fAktUser      := ADOQuery1.Fields [0].AsString;
        fAktUserNumId := ADOQuery1.Fields [1].AsString;

        ADOQuery1.Close;
      end else begin
        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ('select USER from dual');

        ADOQuery1.Open;
        dbstr := ADOQuery1.Fields [0].AsString;
        ADOQuery1.Close;

        fAktDBUser   := dbstr;
        //DBPasswd := Password;

        ADOQuery1.SQL.Clear;

        if (Copy (UpperCase (dbstr), 1, 4) = 'OPS$') then
          ADOQuery1.SQL.Add ('select USER_ID,USER_NUM_ID from V_SYS_BEN where upper (NTS_USER_ID)=upper (:user_name)')
        else
          ADOQuery1.SQL.Add ('select USER_ID,USER_NUM_ID from V_SYS_BEN where upper (USER_ID)=upper (:user_name)');

        ADOQuery1.Parameters [0].Value := dbstr;

        ADOQuery1.Open;

        fAktUser      := ADOQuery1.Fields [0].AsString;
        fAktUserNumId := ADOQuery1.Fields [1].AsString;

        ADOQuery1.Close;

        if (Copy (UpperCase (dbstr), 1, 4) = 'OPS$') then
          fOSAuthent := True;
      end;

      SetUserName (AktUser);

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select REF,STATUS,SPRACHE,LAST_PASSWORD_SET,RESET_PASSWORD,FIRMA,REF_FIRMA,(select MAX_SESSIONS from V_SYS_FIRMA where REF=REF_FIRMA) as MAX_SESSIONS from V_SYS_BEN where STATUS<>''DEL'' and upper (USER_ID)=:userid');
      ADOQuery1.Parameters [0].Value := UpperCase (AktUser);

      ADOQuery1.Open;

      try
        pwchange := False;

        if (ADOQuery1.Fields [0].IsNull) Then begin
          dbres := 10;
          ErrText := FormatMessageText (1021, []);
        end else if (ADOQuery1.Fields [1].AsString <> 'AKT') then begin
          dbres := 11;
          ErrText := FormatMessageText (1022, []);
        end else begin
          if (ADOQuery1.Fields [6].IsNull) then begin
            AktFirma     := ADOQuery1.Fields [5].AsString;
            fAktFirmaRef := -1;
          end else begin
            AktFirma     := ADOQuery1.Fields [5].AsString;
            fAktFirmaRef := ADOQuery1.Fields [6].AsInteger;
          end;

          if (ADOQuery1.Fields [7].IsNull) then
            fMaxSessions := -1
          else
            fMaxSessions := ADOQuery1.Fields [7].AsInteger;

          fAktUserRef := ADOQuery1.Fields [0].AsInteger;

          if (fOSAuthent) then
            pwchange := False
          else
            pwchange := ADOQuery1.Fields [4].AsString = '1';

          dbres := SetSprache (ADOQuery1.Fields [2].AsString);
        end;

        ADOQuery1.Close;

        //Hier wird ggf. das Passwort geändert abgefragt, die aktuelle Verbindung bleibt aber mit dem alten PW bestehen
        if ((dbres = 0) and pwchange) then begin
          passwdform := TChangePasswdForm.Create (Self);

          try
            passwdform.Height := passwdform.Height - passwdform.OldPanel.Height;
            passwdform.OldPanel.Visible := False;
            passwdform.OldPanel.Height := 0;

            passwdform.UserLabel.Caption := UserName;

            if Assigned (LVSConfigModul) then begin
              passwdform.MinLength   := LVSConfigModul.PasswordRestriction.MinLength;
              passwdform.AlphaCase   := LVSConfigModul.PasswordRestriction.AlphaCase;
              passwdform.AlphaNum    := LVSConfigModul.PasswordRestriction.AlphaNum;
              passwdform.SpecialChar := LVSConfigModul.PasswordRestriction.SpecialChar;
            end;

            if (passwdform.ShowModal = mrOk) Then begin
              if (ChangeOwnPassword (AktUser, UpperCase (AktDBUser), Password, passwdform.NewPwEdit.Text,errstr) <> 0) then
                FrontendMessages.MessageDLG(FormatMessageText (1023, []) + #13 + #13 + errstr, mtError, [mbOK], 0)
              else
                DBPasswd := passwdform.NewPwEdit.Text;
            end;
          finally
            passwdform.Release;
          end;
        end;

        if (fOSAuthent) then begin
          PrintDBUser   := Schema + '_PRT';
          PrintDBPasswd := 'password';
        end else begin
          PrintDBUser   := AktDBUser;
          PrintDBPasswd := DBPasswd;
        end;
      except
        dbres := -9;
        ErrText := FormatMessageText (1020, []);
      end;

      ReadDBMSOutput ('TLVSDatenModul.OpenDatabase','',0);

      fAktLagerRef    := -1;
      fAktLocationRef := -1;
      fAktMandantRef  := -1;
    end;
  except
    dbres := -9;
    ErrText := FormatMessageText (1020, []);
  end;

  if (dbres <> 0) Then begin
    OraMainSession.Close;

    MainADOConnection.Cancel;
    MainADOConnection.Close;
  end;

  Result := dbres;

{$ifdef TRACE}
  FunctionStop (Result);
{$endif}
end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.CloseDatabase : Integer;
var
  idx : Integer;
begin
  idx := 0;

  try
    while (idx < MainADOConnection.DataSetCount) do begin
      if (MainADOConnection.DataSets [idx].Active) then
        MainADOConnection.DataSets [idx].Close;

      Inc (idx);
    end;

    OraMainSession.Close;

    MainADOConnection.Close;
  except
  end;

  fIsConnected := False;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.InitDBMSTrace (const TraceLevel : Integer) : Integer;
begin
  fAktPLSTraceLevel := TraceLevel;

  if (fIsConnected and ADOCommand1.Connection.Connected) then begin
    ADOCommand1.CommandText := 'declare res integer; begin BASE_TRACING.InitTracing; res := BASE_TRACING.SetTraceDevice (1,''''); BASE_TRACING.SetTraceLevel ('+IntToStr (fAktPLSTraceLevel)+'); end;';
    try
      ADOCommand1.Execute;
    except
    end;

    fDBMSTraceEnabled := True;
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.SetDBMSTracingOn;
begin
  if (fIsConnected and ADOCommand1.Connection.Connected) then begin
    ADOCommand1.CommandText := 'declare res integer; begin BASE_TRACING.InitTracing; res := BASE_TRACING.SetTraceDevice (1,''''); BASE_TRACING.SetTraceLevel ('+IntToStr (fAktPLSTraceLevel)+'); end;';
    try
      ADOCommand1.Execute;
    except
    end;

    fDBMSTraceEnabled := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.SetDBMSTracingOff;
begin
  if (fIsConnected and ADOCommand1.Connection.Connected) then begin
    ADOCommand1.CommandText := 'declare res integer; begin BASE_TRACING.InitTracing; res := BASE_TRACING.SetTraceDevice (98,''''); BASE_TRACING.SetTraceLevel ('+IntToStr (fAktPLSTraceLevel)+'); end;';
    try
      ADOCommand1.Execute;
    except
    end;

    fDBMSTraceEnabled := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.OpenDBMSTrace (const NewFlag : Boolean) : Integer;
var
  res,
  anz,
  count,
  strpos     : Integer;
  mask,
  fname,
  fbasename,
  logdir,
  exename    : String;
  outline    : AnsiString;
  createflag : Boolean;
  sr,
  lastsr     : TSearchRec;
  query      : TBetterADODataSet;
begin
  createflag := False;

  exename := ExtractFileName (ParamStr (0));

  if Assigned (LVSConfigModul) then
    logdir := LVSConfigModul.GetSessionLogDir
  else logdir := '';

  if not (DirectoryExists (logdir)) then
    ForceDirectories (logdir);

  if (fDBMSTraceCount = -1) then begin
    FillChar (lastsr, sizeof (TSearchRec), 0);

    mask := logdir + Copy (exename, 1, Pos ('.', exename) - 1) + '_dbmstrace_*.log';

    res := FindFirst (mask, faArchive ,sr);
    if (res = 0) then begin
      while (res = 0) do begin
        if (CompareFileTime (sr.FindData.ftLastWriteTime, lastsr.FindData.ftLastWriteTime) >= 0) then begin
          lastsr := sr;
        end;

        res := FindNext (sr);
      end;
    end;
    FindClose (sr);


    if (Length (lastsr.Name) = 0) then
      fDBMSTraceCount := 0
    else begin
      strpos := Length (lastsr.Name) - 5;

      {$ifdef Unicode}
      if (CharInSet (lastsr.Name [strpos], ['0'..'9'])) and CharInSet (lastsr.Name [strpos + 1], ['0'..'9']) then
      {$else}
      if (lastsr.Name [strpos] in ['0'..'9']) and (lastsr.Name [strpos + 1] in ['0'..'9']) then
      {$endif}
        fDBMSTraceCount := (ord (lastsr.Name [strpos]) - ord ('0')) * 10 + (ord (lastsr.Name [strpos + 1]) - ord ('0'))
      else fDBMSTraceCount := 0;

      if NewFlag or (lastsr.Size > fMaxTraceFileSize) then begin
        createflag := True;
        fDBMSTraceCount := (fDBMSTraceCount + 1) mod fMaxTraceFileCount;
      end;
    end;
  end else if (NewFlag) then begin
    createflag := True;
    fDBMSTraceCount := (fDBMSTraceCount + 1) mod fMaxTraceFileCount;
  end;

  fbasename := logdir + Copy (exename, 1, Pos ('.', exename) - 1) + '_dbmstrace_'+FormatIntToStr (fDBMSTraceCount, 2);

  try
    count := 0;

    repeat
      if (count = 0) then
        fname := fbasename + '.log'
      else
        fname := fbasename + '_' + IntToStr (count) + '.log';

      try
        if not (FileExists (fname)) or (createflag) then begin
          fDBMSTraceStream := TFileStream.Create (fname, fmCreate);
          fDBMSTraceStream.Free;
        end;

        fDBMSTraceStream := TFileStream.Create (fname, fmOpenWrite or fmShareDenyWrite);
      except
        Inc (count);

        fDBMSTraceStream := Nil;
      end;
    until (count > 8) or Assigned (fDBMSTraceStream);
  except
    fDBMSTraceStream := Nil;
  end;

  if Assigned (fDBMSTraceStream) then begin
    //Ans Ende der Datei
    fDBMSTraceStream.Seek (0, soFromEnd);

    query := TBetterADODataSet.Create (Self);
    try
      query.Connection := MainADOConnection;

      outline := CRLF + '*************** '+AnsiString (DateTimeToStr (Now))+' ***************' + CRLF;
      outline := outline + 'App:'+AnsiString (exename)+' / Version:'+AnsiString (FileVersion (4,3)) + CRLF;
      outline := outline + 'ADO:'+AnsiString (query.Version.OLEDBVersion) + ' / ' + AnsiString (query.Version.ProviderName) + ' / ' + AnsiString (query.Version.ProviderVersion) + CRLF + CRLF;
    finally
      query.Free;
    end;

    anz := fDBMSTraceStream.Write (outline [1], Length (outline));
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.OraMainSessionAfterConnect(Sender: TObject);
var
  query : TSmartQuery;
begin
  query := TSmartQuery.Create (Self);

  try
    query.Session := OraMainSession;

    //Der aktuelle Session-ID auslesen
    query.SQL.Clear;
    query.SQL.Add ('SELECT sys_context (''USERENV'', ''SESSIONID'') FROM dual');

    try
      query.Open;

      try
        fAktODACSession := query.Fields [0].AsString;
      except
        fAktODACSession := '';
      end;

      query.Close;
    except
      fAktODACSession := '';
    end;

    if (Length (Schema) > 0) Then begin
      query.SQL.Clear;
      query.SQL.Add ('ALTER SESSION SET CURRENT_SCHEMA="'+Schema+'"');

      try
        query.ExecSQL;
      except
      end;
    end;
  finally
    query.Free;
  end;
end;

procedure TLVSDatenModul.OraSQL1BeforeExecute(Sender: TObject);
begin

end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.CloseDBMSTrace : Integer;
var
  outline : AnsiString;
begin
  if Assigned (fDBMSTraceStream) then begin
    outline := '*************** '+AnsiString (DateTimeToStr (Now))+' ***************' + CRLF;
    fDBMSTraceStream.Write (outline [1], Length (outline));

    fDBMSTraceStream.Free;
  end;

  fDBMSTraceStream := Nil;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.CheckTraceFileSize : Integer;
var
  outline   : AnsiString;
  anz       : Integer;
  starttime : Int64;
begin
  if Assigned (fDBMSTraceStream) then begin
    starttime := GetPerformanceCount;

    if (fDBMSTraceStream.Size > fMaxTraceFileSize) then begin
      CloseDBMSTrace;

      OpenDBMSTrace (True);
    end;

    {$ifdef Debug}
      outline := 'Check: '+AnsiString (IntToStr (GetPerformanceCountDiff (starttime))) + ' us' + CRLF + CRLF;
      anz := fDBMSTraceStream.Write (PAnsiChar (outline)^, Length (outline));
    {$endif}
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.ReadDBMSOutput (const ProcName, ErrorText : String; ErrorCode : Integer; ReportStream : TStream) : Integer;
type
  OracleStr = array [0..255] of char;
var
  idx,
  anz,
  dumpsize,
  strlen,
  dbres           : Integer;
  StoredProcedure : TADOStoredProc;
  lineanz         : Integer;
  linebuf,
  outbuffer       : String;
  outline         : AnsiString;
  stop,
  lastflag        : Boolean;
  param           : TParameter;
  proctime,
  starttime       : Int64;
  savestream      : TMemoryStream;
begin
  dbres := 0;

  if (fIsConnected and MainADOConnection.Connected) then begin
    try
      dumpsize := 0;

      //Es wird erst mal alles in einem Memorystream zwischen gepuffert
      savestream := TMemoryStream.Create;

      try
        starttime := GetPerformanceCount;

        outline := '=== '+AnsiString (ProcName)+' at '+AnsiString (DateTimeToStr (Now)) + CRLF;

        anz := savestream.Write (PAnsiChar (outline)^, Length (outline));
        dumpsize := dumpsize + anz;

        StoredProcedure := TADOStoredProc.Create (Nil);

        try
          with StoredProcedure do begin
            Connection    := MainADOConnection;
            ProcedureName := 'PA_DEBUG.GET_LINES_BUFFER';

            Parameters.CreateParameter('lines',ftString, pdOutput, 16392, NULL);
            Parameters.CreateParameter('bufsize',ftInteger,pdInput, 12, 16000);
            Parameters.CreateParameter('numlines',ftInteger,pdOutput, 12, 0);
            Parameters.CreateParameter('lastline',ftInteger,pdOutput, 12, 0);
          end;

          try
            repeat
              lastflag := False;

              //dbms_output auslesen
              proctime := GetPerformanceCount;
              StoredProcedure.ExecProc;

              param := StoredProcedure.Parameters.FindParam ('numlines');
              if not (Assigned (param)) then
                lineanz := 0
              else begin
                if (param.Value = NULL) then
                  lineanz := 0
                else lineanz := param.Value;
              end;

              {$ifdef Debug}
                outline := '--'+ CRLF + '  GET_LINES_BUFFER: '+AnsiString (IntToStr (GetPerformanceCountDiff (proctime))) + ' us ('+AnsiString (IntToStr (lineanz))+' lines)' + CRLF + '--' + CRLF;
                anz := savestream.Write (PAnsiChar (outline)^, Length (outline));
                dumpsize := dumpsize + anz;
              {$endif}

              if (lineanz > 0) Then begin
                param := StoredProcedure.Parameters.FindParam ('lastline');
                if (Assigned (param)) then begin
                  if (param.Value = NULL) then
                    lastflag := false
                  else lastflag := (param.Value = '1');
                end;

                try
                  if (VarIsNull (StoredProcedure.Parameters.FindParam('lines').Value)) Then
                    linebuf := ''
                  else
                    linebuf := StoredProcedure.Parameters.ParamValues ['lines'];
                    //linebuf := StripString (StoredProcedure.Parameters.ParamValues ['lines']);
                except
                  linebuf := ''
                end;

                idx := 1;
                outline := '';
                strlen := Length (linebuf);
                stop := false;

                //Den Output in die einzelnen Zeilen zerlegen
                while not (stop) and (idx <= strlen) do begin
                  if (linebuf [idx] <> #0) then begin
                    outline := outline + AnsiString (linebuf [idx]);
                    Inc (idx);
                  end else begin
                    Inc (idx);
                    outline := outline + CRLF;

                    anz := savestream.Write (PAnsiChar (outline)^, Length (outline));
                    dumpsize := dumpsize + anz;

                    outline := '';

                    if (idx >= strlen) then
                      stop := true
                  end;
                end;

                //In den Zwischenpuffer schreiben
                if(Length (outline) > 0) then begin
                  outline := outline + CRLF;

                  anz := savestream.Write (PAnsiChar (outline)^, Length (outline));
                  dumpsize := dumpsize + anz;
                end;
              end;

              {$ifdef Debug}
                outline := '!-! ('+IntToStr (lineanz)+')' + CRLF;

                anz := savestream.Write (PAnsiChar (outline)^, Length (outline));
                dumpsize := dumpsize + anz;
              {$endif}
            until (lineanz = 0) or (lastflag);
          except
            dbres := -9;
          end;

          outline := '';

          if (ErrorCode <> 0) then begin
            outline := outline + '!!!!!!!!!!!!!!!!! ERROR ' + AnsiString (IntToStr (ErrorCode)) + '!!!!!!!!!!!!!!!!!' + CRLF;

            outline := outline + AnsiString (ErrorText) + CRLF;
          end;

          {$ifdef Debug}
            outline := outline + '=== '+AnsiString (ProcName) + ' ' + AnsiString (IntToStr (GetPerformanceCountDiff (starttime))) + ' us ('+AnsiString (IntToStr (dumpsize))+' Bytes)' + CRLF + CRLF;
          {$else}
            outline := outline + '=== '+AnsiString (ProcName) + CRLF + CRLF;
          {$endif}

          anz := savestream.Write (PAnsiChar (outline)^, Length (outline));
          dumpsize := dumpsize + anz;

          //Den ganzen Puffer in einem Vorgang ins Log schreiben, das geht viel schneller als einzelne Blöcke
          if Assigned (fDBMSTraceStream) then begin
            starttime := GetPerformanceCount;

            savestream.SaveToStream (fDBMSTraceStream);

            {$ifdef Debug}
              outline := 'DBMS Flush: '+AnsiString (IntToStr (GetPerformanceCountDiff (starttime))) + ' us ('+AnsiString (IntToStr (dumpsize))+' Bytes)' + CRLF + CRLF;
              anz := fDBMSTraceStream.Write (PAnsiChar (outline)^, Length (outline));
            {$endif}
          end;

          //Ggf. das Log auch in den Reportstream schreiben
          if Assigned (ReportStream) then begin
            starttime := GetPerformanceCount;

            savestream.SaveToStream (ReportStream);

            {$ifdef Debug}
              outline := 'Report Flush: '+AnsiString (IntToStr (GetPerformanceCountDiff (starttime))) + ' us ('+AnsiString (IntToStr (dumpsize))+' Bytes)' + CRLF + CRLF;
              anz := fDBMSTraceStream.Write (PAnsiChar (outline)^, Length (outline));
            {$endif}
          end;
        finally
          StoredProcedure.Free;
        end;
      finally
        savestream.Free;
      end;

      if Assigned (fDBMSTraceStream) then
        FlushFileBuffers (fDBMSTraceStream.Handle);

      CheckTraceFileSize;
    except
      dbres := -9;
    end;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.ReadOraDBMSOutput (const ProcName, ErrorText : String; ErrorCode : Integer; ReportStream : TStream) : Integer;
type
  OracleStr = array [0..255] of char;
var
  idx,
  anz,
  dumpsize,
  strlen,
  dbres           : Integer;
  StoredProcedure : TOraStoredProc;
  lineanz         : Integer;
  linebuf,
  outline         : AnsiString;
  stop,
  lastflag        : Boolean;
  param           : TOraParam;
  savestream      : TMemoryStream;
begin
  dbres := 0;

  if (OraMainSession.Connected) then begin
    try
      dumpsize := 0;

      //Es wird erst mal alles in einem Memorystream zwischen gepuffert
      savestream := TMemoryStream.Create;

      try
        //starttime := GetPerformanceCount;

        outline := '=== '+AnsiString (ProcName)+' at '+AnsiString (DateTimeToStr (Now)) + CRLF;

        anz := savestream.Write (PAnsiChar (outline)^, Length (outline));
        dumpsize := dumpsize + anz;

        StoredProcedure := TOraStoredProc.Create (Nil);

        try
          with StoredProcedure do begin
            Connection     := OraMainSession;
            StoredProcName := 'PA_DEBUG.GET_LINES_BUFFER';

            Params.CreateParam(ftString, 'lines', ptOutput);
            Params.CreateParam(ftInteger, 'bufsize', ptInput);
            Params.ParamValues ['bufsize'] := 16000;
            Params.CreateParam(ftString, 'deliminator', ptInput);
            Params.ParamValues ['deliminator'] := chr (1);
            Params.CreateParam(ftInteger, 'numlines', ptOutput);
            Params.CreateParam(ftInteger, 'lastline', ptOutput);
          end;

          try
            repeat
              lastflag := False;

              //dbms_output auslesen
              StoredProcedure.ExecProc;


              param := StoredProcedure.Params.FindParam ('numlines');
              if not (Assigned (param)) then
                lineanz := 0
              else begin
                if (param.Value = NULL) then
                  lineanz := 0
                else lineanz := param.Value;
              end;

              if (lineanz > 0) Then begin
                param := StoredProcedure.Params.FindParam ('lastline');
                if (Assigned (param)) then begin
                  if (param.Value = NULL) then
                    lastflag := false
                  else lastflag := (param.Value = '1');
                end;

                try
                  if (VarIsNull (StoredProcedure.Params.FindParam('lines').Value)) Then
                    linebuf := ''
                  else
                    param := StoredProcedure.Params.FindParam ('lines');
                    if (Assigned (param)) then
                      linebuf := AnsiString (param.Value);
                except
                  linebuf := ''
                end;

                idx := 1;
                outline := '';
                strlen := Length (linebuf);
                stop := false;

                //Den Output in die einzelnen Zeilen zerlegen
                while not (stop) and (idx <= strlen) do begin
                  if (linebuf [idx] <> #1) then begin
                    outline := outline + AnsiString (linebuf [idx]);
                    Inc (idx);
                  end else begin
                    Inc (idx);
                    outline := outline + #13+#10;

                    anz := savestream.Write (PAnsiChar (outline)^, Length (outline));
                    dumpsize := dumpsize + anz;

                    outline := '';

                    if (idx >= strlen) then
                      stop := true
                  end;
                end;

                //In den Zwischenpuffer schreiben
                if(Length (outline) > 0) then begin
                  outline := outline + #13+#10;

                  anz := savestream.Write (PAnsiChar (outline)^, Length (outline));
                  dumpsize := dumpsize + anz;
                end;
              end;

              {$ifdef Debug}
                outline := AnsiString ('!-! ('+IntToStr (lineanz)+')' + #13+#10);

                anz := savestream.Write (PAnsiChar (outline)^, Length (outline));
                dumpsize := dumpsize + anz;
              {$endif}
            until (lineanz = 0) or (lastflag);
          except
            dbres := DB_ERROR;
          end;

          outline := '';

          if (ErrorCode <> 0) then begin
            outline := outline + '!!!!!!!!!!!!!!!!! ERROR ' + AnsiString (IntToStr (ErrorCode)) + '!!!!!!!!!!!!!!!!!' + CRLF;

            outline := outline + AnsiString (ErrorText) + #13+#10;
          end;

          outline := outline + '=== '+AnsiString (ProcName) + #13+#10 + #13+#10;

          anz := savestream.Write (PAnsiChar (outline)^, Length (outline));
          dumpsize := dumpsize + anz;

          //Den ganzen Puffer in einem Vorgang ins Log schreiben, das geht viel schneller als einzelne Blöcke
          if Assigned (fDBMSTraceStream) then begin
            savestream.SaveToStream (fDBMSTraceStream);
          end;

          //Ggf. das Log auch in den Reportstream schreiben
          if Assigned (ReportStream) then begin
            savestream.SaveToStream (ReportStream);
          end;
        finally
          StoredProcedure.Free;
        end;
      finally
        savestream.Free;
      end;

      if Assigned (fDBMSTraceStream) then
        FlushFileBuffers (fDBMSTraceStream.Handle);

      CheckTraceFileSize;
    except
      dbres := DB_ERROR;
    end;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.TraceSQL (const Line : String);
var
  outline : AnsiString;
begin
  if Assigned (fSQLTraceStream) then begin
    outline := AnsiString (DateTimeToStr (Now)) + ' : ' + AnsiString (Line) + CRLF;
    fSQLTraceStream.Write (outline [1], Length (outline));
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.MainADOConnectionAfterConnect(Sender: TObject);
var
  logdir : String;
  cname  : array [0..255] of char;
  query  : TADOQuery;
begin
  fIsConnected := True;

  ADOCommand1.Connection := MainADOConnection;

  ProxyUser     := '';

  fSessionList.Clear;

  Screen.Cursor := crSQLWait;

  try
    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := MainADOConnection;

      //Der aktuelle Session-ID auslesen
      query.SQL.Add ('SELECT sys_context (''USERENV'', ''SESSIONID'') FROM dual');

      try
        query.Open;

        try
          fAktSession := query.Fields [0].AsString;
        except
          fAktSession := '';
        end;

        query.Close;
      except
        fAktSession := '';
      end;


      if (Length (Schema) > 0) Then begin
        query.SQL.Clear;
        query.SQL.Add ('ALTER SESSION SET CURRENT_SCHEMA="'+Schema+'"');

        try
          query.ExecSQL;
        except
        end;
      end;

      query.SQL.Clear;
      query.SQL.Add ('SELECT sys1.NUMVALUE, sys2.STRVALUE from V_SYS_PARAMETER sys1, V_SYS_PARAMETER sys2 where sys1.NAME=''DB_VERSION'' and sys2.NAME=''DB_OPTIONS''');

      try
        query.Open;

        try
          fDBVersion := query.Fields [0].AsInteger;
          fDBOptions := query.Fields [1].AsString;
        except
          fDBVersion := -1;
        end;

        query.Close;
      except
        fDBVersion := -1;
        fDBOptions := '';
      end;
    finally
      query.Free;
    end;

    if Assigned (LVSConfigModul) then
      logdir := LVSConfigModul.GetSessionLogDir
    else logdir := '';

    if not (DirectoryExists (logdir)) then begin
      StrPCopy (cname, logdir);
      CreateDirectory (cname, Nil);
    end;

    try
      fSQLTraceStream := TFileStream.Create (logdir + 'SQLTrace.log', fmCreate);
      fSQLTraceStream.Free;

      fSQLTraceStream := TFileStream.Create (logdir + 'SQLTrace.log', fmOpenWrite or fmShareDenyWrite);
    except
      fSQLTraceStream := Nil;
    end;

    if Assigned (LVSConfigModul) then
      InitDBMSTrace (LVSConfigModul.DBTraceLavel)
    else
      InitDBMSTrace (9);

    OpenDBMSTrace;

    if Assigned (fDBConnect) then
      fDBConnect (Self);
  finally
    Screen.Cursor := crDefault;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.MainADOConnectionBeforeDisconnect(Sender: TObject);
var
  dbres : Integer;
begin
  dbres := 0;

  fDBQueryCount := 0;
  DBQueryWait  := 0;

  if (fIsConnected and MainADOConnection.Connected) then begin
    ADOCommand1.CommandText := 'begin ';
    ADOCommand1.CommandText := ADOCommand1.CommandText + ' BASE_TRACING.InitTracing;';
    ADOCommand1.CommandText := ADOCommand1.CommandText + ' PA_LOGIN.USERLOGOFF;';
    ADOCommand1.CommandText := ADOCommand1.CommandText + 'end;';

    {$ifndef SQLDebug}
      TraceSQL (ADOCommand1.CommandText);
    {$endif}

    try
      ADOCommand1.Execute;
    except
      dbres := -9;
    end;

    ReadDBMSOutput ('PA_LOGIN.USERLOGOFF','',dbres);
  end;

  CloseDBMSTrace;

  while (fSessionList.Count > 0) do begin
    TTransacSession (fSessionList [0]).Free;

    fSessionList.Delete (0);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.MainADOConnectionAfterDisconnect(Sender: TObject);
begin
  if Assigned (fSQLTraceStream) then
    fSQLTraceStream.Free;

  fSQLTraceStream := Nil;

  if not (csDestroying in ComponentState) then begin
    if Assigned (fDBDisconnect) then
      fDBDisconnect (Self);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.DataModuleCreate(Sender: TObject);
begin
  fSessionList := TList.Create;

  fAktUser      := '';
  fAktDBUser    := '';
  fAktUserNumID := '';

  AktLager      := '';
  AktSprache    := 'DE';
  AktMandant    := '';
  AktClientName := '';

  ProxyUser     := '';

  fDBQueryCount := 0;
  DBQueryWait  := 0;

  fIsConnected := False;

  fDBMSTraceCount    := -1;

  fMaxTraceFileSize  := 10000000; //10 MB Pro Tracefile
  fMaxTraceFileCount := 4;

  fSQLTraceStream  := Nil;
  fDBMSTraceStream := Nil;

  WindowHandle := Classes.AllocateHWnd (WinProc);
//  WaitThread := TWaitThread.Create(Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.DataModuleDestroy(Sender: TObject);
begin
  if (MainADOConnection.Connected) then begin
    try
      OraMainSession.Close;
      
      MainADOConnection.Close;
    except
    end;
  end;

  Classes.DeallocatehWnd (WindowHandle);

  if Assigned (WaitThread) then
    WaitThread.Free;
  WaitThread := Nil;

  if Assigned (fSessionList) then
    fSessionList.Free;
  fSessionList := Nil;

  if Assigned (fSQLTraceStream) then
    fSQLTraceStream.Free;
  fSQLTraceStream := Nil;

  if Assigned (fDBMSTraceStream) then
    fDBMSTraceStream.Free;
  fDBMSTraceStream := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.CloseAllDatasets;
var
  idx : Integer;
begin
  idx := 0;
  while (idx < MainADOConnection.DataSetCount) do begin
    MainADOConnection.DataSets [idx].Cancel;
    MainADOConnection.DataSets [idx].Close;

    Inc (idx);
  end;

  idx := 0;
  while (idx < MainADOConnection.CommandCount) do begin
    MainADOConnection.Commands[idx].Cancel;

    Inc (idx);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.WinProc (var WndMessage:TMessage);
var
  idx : Integer;
begin
  with WndMessage do begin
    if (Msg = WM_USER + 1) then begin
      idx := 0;
      while (idx < MainADOConnection.DataSetCount) do begin
        try
          MainADOConnection.DataSets [idx].Cancel;
          MainADOConnection.DataSets [idx].Close;
        except
        end;

        Inc (idx);
      end;

      idx := 0;
      while (idx < MainADOConnection.CommandCount) do begin
        try
          MainADOConnection.Commands[idx].Cancel;
        except
        end;

        Inc (idx);
      end;

      try
        OraMainSession.Close;
        MainADOConnection.Close;
      except
      end;

      FrontendMessages.MessageDLG (FormatMessageText (1025, []), mtError, [mbOK], 0);
      Application.Terminate;
    end else begin
      Result:=DefWindowProc (WindowHandle,Msg,wParam,lParam)
    end;
  end;
end;
//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.InitBugReport (const HeaderLine : String) : TMemoryStream;
var
  bugrep : TMemoryStream;
  outstr : AnsiString;
begin
  try
    bugrep := TMemoryStream.Create;
  except
    bugrep := Nil;
  end;

  if Assigned (bugrep) then begin
    outstr :=                 'Schema:     ' + Schema;
    outstr := outstr + CRLF + 'DB-Session: ' + fAktSession;
    outstr := outstr + CRLF + 'OS-User:    ' + OSUserName;
    outstr := outstr + CRLF + 'SL-User:    ' + AktUser;
    outstr := outstr + CRLF + 'Mandant:    ' + AktMandant;
    outstr := outstr + CRLF + 'Lager:      ' + AktLager;
    outstr := outstr + CRLF + 'Leitstand:  ' + LVSConfigModul.LeitstandName;
    outstr := outstr + CRLF + 'Host:       ' + AktHost;
    outstr := outstr + CRLF + 'Client:     ' + AktClientName;
    outstr := outstr + CRLF + 'Version:    ' + AktVersion;
    outstr := outstr + CRLF + '--------------------------------------' + CRLF + CRLF;
    bugrep.Write (outstr[1], Length (outstr));
  end;

  Result := bugrep;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.CallStoreProcedure (StoredProcedure : TADOStoredProc) : Integer;
var
  dbres     : Integer;
  altcursor : TCursor;
  errstr    : String;
  count,
  strpos,
  orares,
  aktlevel,
  errcode   : Integer;
  line      : AnsiString;
  bugrep    : TMemoryStream;
  param     : TParameter;
begin
  orares := 0;
  dbres  := 0;

  bugrep := Nil;

  StoredProcedure.Connection := MainADOConnection;

  if (Screen.Cursor = crSQLWait) then
    altcursor := 1
  else begin
    altcursor := Screen.Cursor;
    Screen.Cursor := crSQLWait;
  end;

  try
    try
      count   := 0;
      errcode := 0;

      if (LVSConfigModul.TestFlag and (LVSConfigModul.MaxPLSRuntime > 0)) then begin
        ADOCommand1.CommandText := 'begin PA_TIMEOUT.StartTimeout ('+IntToStr (LVSConfigModul.MaxPLSRuntime)+'); end;';

        {$ifndef SQLDebug}
          TraceSQL (ADOCommand1.CommandText);
        {$endif}

        try
          ADOCommand1.Execute;
        except
        end;
      end;

      repeat
        BeginTransaction (StoredProcedure.Connection);

        try
          Inc (count);

          StoredProcedure.ExecProc;
          errcode := 0;
        except
          on E: Exception do begin
            strpos := Pos ('ORA-', E.Message);

            if (strpos = 0) Then
              raise
            else begin
              errstr := Copy (E.Message, strpos + 4, 5);
              errcode := StrToInt (errstr);

              if (errcode = 28) then begin
                orares := -9999;

                fIsConnected := False;

                try
                  StoredProcedure.Connection.Close;
                except
                end;
              end else begin
                if (count > 3) then begin
                  if (errcode = 4068) or (errcode = 4061) then begin
                    EndTransaction (StoredProcedure.Connection, trRollback, True);

                    ReInitSession;
                  end;

                  raise
                end else begin
                  if (errcode = 4068) or (errcode = 4061) then begin
                    aktlevel := GetTransactionLevel (StoredProcedure.Connection);

                    EndTransaction (StoredProcedure.Connection, trRollback, True);

                    try
                      bugrep := InitBugReport ('');

                      ReadDBMSOutput (StoredProcedure.ProcedureName, E.Message, -10, bugrep);
                      //Errorlog schreiben

                      if Assigned (bugrep) and Assigned (ErrorTrackingModule) then
                        ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);
                    finally
                      if Assigned (bugrep) then
                        bugrep.Free;
                    end;

                    ReInitSession;

                    if (aktlevel > 1) then
                      raise EOracleRetryException.Create (E.Message);
                  end else if (errcode = 60) then begin //deadlock detected
                    aktlevel := GetTransactionLevel (StoredProcedure.Connection);;

                    EndTransaction (StoredProcedure.Connection, trRollback, True);

                    try
                      bugrep := InitBugReport ('');

                      ReadDBMSOutput (StoredProcedure.ProcedureName, E.Message, -11, bugrep);
                      //Errorlog schreiben

                      if Assigned (bugrep) and Assigned (ErrorTrackingModule) then
                        ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);
                    finally
                      if Assigned (bugrep) then
                        bugrep.Free;
                    end;

                    if (aktlevel > 1) then
                      raise EOracleRetryException.Create (E.Message)
                    else
                      Sleep (Random (100));  //Zwischen 0 und 100ms warten
                  end else
                    raise;
                end;
              end;
            end;

            InitDBMSTrace (fAktPLSTraceLevel);
          end;
        end;
      until (errcode = 0) or (count > 3) or (orares <> 0);

      if (orares <> 0) then
        dbres := orares
      else begin
        param := StoredProcedure.Parameters.FindParam ('Result');
        if Assigned (param) then
          dbres := StoredProcedure.Parameters.ParamValues ['Result'];

        param := StoredProcedure.Parameters.FindParam ('pErrorCode');
        if Assigned (param) then begin
          if (param.Value = NULL) then
            LastLVSErrorCode := 0
          else LastLVSErrorCode := param.Value;
        end else LastLVSErrorCode := 0;

        param := StoredProcedure.Parameters.FindParam ('pErrorText');
        if Assigned (param) then begin
          if (param.Value = NULL) then
            LastLVSErrorText := ''
          else LastLVSErrorText := param.Value;
        end else LastLVSErrorText := '';
      end;
    except
      on EOracleRetryException do raise;

      on E: Exception do begin
        dbres := -9;

        LastLVSErrorText := E.Message;
      end;
    end;

    if (dbres = 0) Then begin
      if (LastLVSErrorCode = 0) then
        EndTransaction (StoredProcedure.Connection, trCommit)
      else
        EndTransaction (StoredProcedure.Connection, trRollback);

      if (fDBMSTraceEnabled) then
        ReadDBMSOutput (StoredProcedure.ProcedureName, LastLVSErrorText, dbres);
    end else if fIsConnected and StoredProcedure.Connection.Connected then begin
      EndTransaction (StoredProcedure.Connection, trRollback);

      if (dbres > 0) then begin
        try
          bugrep := InitBugReport ('');

          ReadDBMSOutput (StoredProcedure.ProcedureName, LastLVSErrorText, dbres, bugrep);

          //Errorlog schreiben
          if Assigned (bugrep) and Assigned (ErrorTrackingModule) then
            ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);
        finally
          if Assigned (bugrep) then
            bugrep.Free;
        end;
      end else begin
        //Mail versenden
        try
          bugrep := InitBugReport ('');

          ReadDBMSOutput (StoredProcedure.ProcedureName, LastLVSErrorText, dbres, bugrep);

          if Assigned (bugrep) then begin
            //Errorlog schreiben
            if Assigned (ErrorTrackingModule) then
              ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);

            //Mail versenden
            bugrep.Position := 0;

            SetLength(line, bugrep.Size);
            bugrep.Read(line[1], bugrep.Size);

            madExcept.AutoSendBugReport (String (line), ScreenShot (True), Nil);
          end;
        finally
          if Assigned (bugrep) then
            bugrep.Free;
        end;
      end;
    end;

    //Protokoll schreiben
    if (dbres < 0) then
      WriteProtokoll (AktMandant, AktLager, 'ERROR','SQLERR',StoredProcedure.ProcedureName,IntToStr (LastLVSErrorCode) +' : '+LastLVSErrorText)
    else if (dbres > 0) or (LastLVSErrorCode <> 0) then
      WriteProtokoll (AktMandant, AktLager, 'ERROR','APPERR',StoredProcedure.ProcedureName,IntToStr (LastLVSErrorCode) +' : '+LastLVSErrorText);


    if (dbres > 0) then
      dbres := dbres * -1
    else if (LastLVSErrorCode <> 0) Then dbres := LastLVSErrorCode;
  finally
    if (altcursor <> 1) then
      Screen.Cursor := altcursor;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.CallStoreProcedure (StoredProcedure : TOraStoredProc) : Integer;
var
  dbres     : Integer;
  altcursor : TCursor;
  errstr    : String;
  count,
  strpos,
  orares,
  aktlevel,
  errcode   : Integer;
  line      : AnsiString;
  bugrep    : TMemoryStream;
  param     : TOraParam;
begin
  orares := 0;
  dbres  := 0;

  bugrep := Nil;

  StoredProcedure.Connection := OraMainSession;

  if (Screen.Cursor = crSQLWait) then
    altcursor := 1
  else begin
    altcursor := Screen.Cursor;
    Screen.Cursor := crSQLWait;
  end;

  try
    try
      count   := 0;
      errcode := 0;

      repeat
        BeginTransaction (OraMainSession);

        try
          Inc (count);

          StoredProcedure.ExecProc;
          errcode := 0;
        except
          on E: Exception do begin
            strpos := Pos ('ORA-', E.Message);

            if (strpos = 0) Then
              raise
            else begin
              errstr := Copy (E.Message, strpos + 4, 5);
              errcode := StrToInt (errstr);

              if (errcode = 28) then begin
                orares := -9999;

                fIsConnected := False;

                try
                  StoredProcedure.Connection.Close;
                except
                end;
              end else begin
                if (count > 3) then begin
                  if (errcode = 4068) or (errcode = 4061) then begin
                    EndTransaction (OraMainSession, trRollback, True);

                    ReInitSession;
                  end;

                  raise
                end else begin
                  if (errcode = 4068) or (errcode = 4061) then begin
                    aktlevel := GetTransactionLevel (OraMainSession);

                    EndTransaction (OraMainSession, trRollback, True);

                    try
                      bugrep := InitBugReport ('');

                      ReadDBMSOutput (StoredProcedure.StoredProcName, E.Message, -10, bugrep);
                      //Errorlog schreiben

                      if Assigned (bugrep) and Assigned (ErrorTrackingModule) then
                        ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);
                    finally
                      if Assigned (bugrep) then
                        bugrep.Free;
                    end;

                    ReInitSession;

                    if (aktlevel > 1) then
                      raise EOracleRetryException.Create (E.Message);
                  end else if (errcode = 60) then begin //deadlock detected
                    aktlevel := GetTransactionLevel (OraMainSession);;

                    EndTransaction (OraMainSession, trRollback, True);

                    try
                      bugrep := InitBugReport ('');

                      ReadDBMSOutput (StoredProcedure.StoredProcName, E.Message, -11, bugrep);
                      //Errorlog schreiben

                      if Assigned (bugrep) and Assigned (ErrorTrackingModule) then
                        ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);
                    finally
                      if Assigned (bugrep) then
                        bugrep.Free;
                    end;

                    if (aktlevel > 1) then
                      raise EOracleRetryException.Create (E.Message)
                    else
                      Sleep (Random (100));  //Zwischen 0 und 100ms warten
                  end else
                    raise;
                end;
              end;
            end;

            InitDBMSTrace (fAktPLSTraceLevel);
          end;
        end;
      until (errcode = 0) or (count > 3) or (orares <> 0);

      if (orares <> 0) then
        dbres := orares
      else begin
        param := StoredProcedure.FindParam ('Result');
        if Assigned (param) then
          dbres := param.Value;

        param := StoredProcedure.FindParam ('pErrorCode');
        if Assigned (param) then begin
          if (param.Value = NULL) then
            LastLVSErrorCode := 0
          else LastLVSErrorCode := param.Value;
        end else LastLVSErrorCode := 0;

        param := StoredProcedure.FindParam ('pErrorText');
        if Assigned (param) then begin
          if (param.Value = NULL) then
            LastLVSErrorText := ''
          else LastLVSErrorText := param.Value;
        end else LastLVSErrorText := '';
      end;
    except
      on EOracleRetryException do raise;

      on E: Exception do begin
        dbres := -9;

        LastLVSErrorText := E.Message;
      end;
    end;

    if (dbres = 0) Then begin
      if (LastLVSErrorCode = 0) then
        EndTransaction (OraMainSession, trCommit)
      else
        EndTransaction (OraMainSession, trRollback);

      if (fDBMSTraceEnabled) then
        ReadOraDBMSOutput (StoredProcedure.StoredProcName, LastLVSErrorText, dbres);
    end else if fIsConnected and StoredProcedure.Connection.Connected then begin
      EndTransaction (OraMainSession, trRollback);

      if (dbres > 0) then begin
        try
          bugrep := InitBugReport ('');

          ReadOraDBMSOutput (StoredProcedure.StoredProcName, LastLVSErrorText, dbres, bugrep);

          //Errorlog schreiben
          if Assigned (bugrep) and Assigned (ErrorTrackingModule) then
            ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);
        finally
          if Assigned (bugrep) then
            bugrep.Free;
        end;
      end else begin
        //Mail versenden
        try
          bugrep := InitBugReport ('');

          ReadOraDBMSOutput (StoredProcedure.StoredProcName, LastLVSErrorText, dbres, bugrep);

          if Assigned (bugrep) then begin
            //Errorlog schreiben
            if Assigned (ErrorTrackingModule) then
              ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);

            //Mail versenden
            bugrep.Position := 0;

            SetLength(line, bugrep.Size);
            bugrep.Read(line[1], bugrep.Size);

            madExcept.AutoSendBugReport (String (line), ScreenShot (True), Nil);
          end;
        finally
          if Assigned (bugrep) then
            bugrep.Free;
        end;
      end;
    end;

    //Protokoll schreiben
    if (dbres < 0) then
      WriteProtokoll (AktMandant, AktLager, 'ERROR','SQLERR',StoredProcedure.StoredProcName,IntToStr (LastLVSErrorCode) +' : '+LastLVSErrorText)
    else if (dbres > 0) or (LastLVSErrorCode <> 0) then
      WriteProtokoll (AktMandant, AktLager, 'ERROR','APPERR',StoredProcedure.StoredProcName,IntToStr (LastLVSErrorCode) +' : '+LastLVSErrorText);


    if (dbres > 0) then
      dbres := dbres * -1
    else if (LastLVSErrorCode <> 0) Then dbres := LastLVSErrorCode;
  finally
    if (altcursor <> 1) then
      Screen.Cursor := altcursor;
  end;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.CallStoreFunction (StoredProcedure : TADOStoredProc; const TraceFlag : Boolean) : Integer;
var
  dbres     : Integer;
  altcursor : TCursor;
  errstr    : String;
  count,
  strpos,
  orares,
  aktlevel,
  errcode   : Integer;
  line      : String;
  bugrep    : TMemoryStream;
  param     : TParameter;
begin
  orares := 0;
  dbres  := 0;
  bugrep := Nil;
  
  StoredProcedure.Connection := MainADOConnection;

  if (Screen.Cursor = crSQLWait) then
    altcursor := 1
  else begin
    altcursor := Screen.Cursor;
    Screen.Cursor := crSQLWait;
  end;

  try
    count   := 0;
    errcode := 0;

    repeat
      BeginTransaction (StoredProcedure.Connection);



      try
        Inc (count);

        StoredProcedure.ExecProc;
        errcode := 0;
      except
        on E: Exception do begin
          strpos := Pos ('ORA-', E.Message);

          if (strpos = 0) Then
            raise
          else begin
            errstr := Copy (E.Message, strpos + 4, 5);
            errcode := StrToInt (errstr);

            if (count > 3) then begin
              if (errcode = 4068) or (errcode = 4061) then begin
                EndTransaction (StoredProcedure.Connection, trRollback, True);
                ReInitSession;
              end;

              raise
            end else begin
              if (errcode = 4068) or (errcode = 4061) then begin
                try
                  bugrep := InitBugReport ('');

                  ReadDBMSOutput (StoredProcedure.ProcedureName, E.Message, -10, bugrep);
                  //Errorlog schreiben

                  if Assigned (bugrep) and Assigned (ErrorTrackingModule) then
                    ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);
                finally
                  if Assigned (bugrep) then
                    bugrep.Free;
                end;

                aktlevel := GetTransactionLevel (StoredProcedure.Connection);

                EndTransaction (StoredProcedure.Connection, trRollback, True);
                ReInitSession;

                if (aktlevel > 1) then
                  raise EOracleRetryException.Create (E.Message);
              end else if (errcode = 60) then begin //deadlock detected
                try
                  bugrep := InitBugReport ('');

                  ReadDBMSOutput (StoredProcedure.ProcedureName, E.Message, -11, bugrep);
                  //Errorlog schreiben

                  if Assigned (bugrep) and Assigned (ErrorTrackingModule) then
                    ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);
                finally
                  if Assigned (bugrep) then
                    bugrep.Free;
                end;

                aktlevel := GetTransactionLevel (StoredProcedure.Connection);

                EndTransaction (StoredProcedure.Connection, trRollback, True);

                if (aktlevel > 1) then
                  raise EOracleRetryException.Create (E.Message)
                else
                  Sleep (Random (100));  //Zwischen 0 und 100ms warten
              end else raise;
            end;
          end;
        end;
      end;
    until (errcode = 0) or (count > 3) or (orares <> 0);

    if (orares <> 0) then
      dbres := orares
    else begin
      param := StoredProcedure.Parameters.FindParam ('pErrorCode');
      if Assigned (param) then begin
        if (param.Value = NULL) then
          LastLVSErrorCode := 0
        else LastLVSErrorCode := param.Value;
      end else LastLVSErrorCode := 0;

      param := StoredProcedure.Parameters.FindParam ('pErrorText');
      if Assigned (param) then begin
        if (param.Value = NULL) then
          LastLVSErrorText := ''
        else LastLVSErrorText := param.Value;
      end else LastLVSErrorText := '';
    end;
  except
    on EOracleRetryException do raise;

    on E: Exception do begin
      dbres := -9;

      LastLVSErrorText := E.Message;
    end;
  end;

  if (dbres = 0) Then begin
    if (TraceFlag) and fDBMSTraceEnabled then
      ReadDBMSOutput (StoredProcedure.ProcedureName, LastLVSErrorText, dbres);
  end else if (dbres > 0) Then begin
    try
      bugrep := InitBugReport ('');

      ReadDBMSOutput (StoredProcedure.ProcedureName, LastLVSErrorText, dbres, bugrep);

      //Errorlog schreiben
      if Assigned (bugrep) and Assigned (ErrorTrackingModule) then
        ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);

      InitDBMSTrace (fAktPLSTraceLevel);
    finally
      if Assigned (bugrep) then
        bugrep.Free;
    end;
  end else begin
    //Mail versenden
    try
      bugrep := InitBugReport ('');

      ReadDBMSOutput (StoredProcedure.ProcedureName, LastLVSErrorText, dbres, bugrep);

      if Assigned (bugrep) then begin
        //Errorlog schreiben
        if Assigned (ErrorTrackingModule) then
          ErrorTrackingModule.WriteErrorLog ('************** PL/SQL-Error ************', bugrep);

        //Mail versenden
        bugrep.Position := 0;

        SetLength(line, bugrep.Size);
        bugrep.Read(line[1], bugrep.Size);

        madExcept.AutoSendBugReport (line, ScreenShot (True), Nil);
      end;

      InitDBMSTrace (fAktPLSTraceLevel);
    finally
      if Assigned (bugrep) then
        bugrep.Free;
    end;
  end;

  if (dbres = 0) and (LastLVSErrorCode = 0) Then
    EndTransaction (StoredProcedure.Connection, trCommit)
  else begin
    EndTransaction (StoredProcedure.Connection, trRollback);
  end;

  //Protokoll schreiben
  if (dbres < 0) then
    WriteProtokoll (AktMandant, AktLager, 'ERROR','SQLERR',StoredProcedure.ProcedureName,IntToStr (LastLVSErrorCode) +' : '+LastLVSErrorText)
  else if (dbres > 0) or (LastLVSErrorCode <> 0) then
    WriteProtokoll (AktMandant, AktLager, 'ERROR','APPERR',StoredProcedure.ProcedureName,IntToStr (LastLVSErrorCode) +' : '+LastLVSErrorText);


  if (dbres > 0) then
    dbres := dbres * -1
  else if (LastLVSErrorCode <> 0) Then dbres := LastLVSErrorCode;

  if (altcursor <> 1) then
    Screen.Cursor := altcursor;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.MainADOConnectionWillExecute(
  Connection: TADOConnection; var CommandText: WideString;
  var CursorType: TCursorType; var LockType: TADOLockType;
  var CommandType: TCommandType; var ExecuteOptions: TExecuteOptions;
  var EventStatus: TEventStatus; const Command: _Command;
  const Recordset: _Recordset);
begin
//  OutputDebugString ('WillExecute');
//  dbgstr := 'fDBQueryCount:'+IntToStr (fDBQueryCount);
//  OutputDebugString (PAnsiChar (@dbgstr[1]));

  {$ifdef SQLDebug}
    if not Assigned (MessageModule) or (MessageModule.ADOQuery1.Recordset = Nil) or (RecordSet <> MessageModule.ADOQuery1.Recordset) then begin
      if Assigned (Command) then
        TraceSQL (Connection.Name +' : ' + Command.CommandText)
      else TraceSQL (Connection.Name +' : ' + CommandText);

      fSQLExecStart := GetTickCount;
    end;
  {$endif}

  if not Assigned (MessageModule) or (MessageModule.ADOQuery1.Recordset = Nil) or (RecordSet <> MessageModule.ADOQuery1.Recordset) then begin
    if (fDBQueryCount = 0) then begin
      if ((Screen.Cursor = crSQLWait) or (Screen.Cursor = crHourGlass)) then
        altcursor := 1
      else begin
        altcursor := Screen.Cursor;
        Screen.Cursor := crSQLWait;
      end;
    end;

    Inc (fDBQueryCount);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.MainADOConnectionExecuteComplete(
  Connection: TADOConnection; RecordsAffected: Integer; const Error: Error;
  var EventStatus: TEventStatus; const Command: _Command;
  const Recordset: _Recordset);
{$ifdef SQLDebug}
  var
    i,
    dauer : Integer;
{$endif}
begin
//  OutputDebugString ('ExecuteComplete');

  if not Assigned (MessageModule) or (MessageModule.ADOQuery1.Recordset = Nil) or (RecordSet <> MessageModule.ADOQuery1.Recordset) then begin
    Dec (fDBQueryCount);

    if (fDBQueryCount = 0) then begin
      DBQueryWait := 0;

      if (altcursor <> 1) then
        Screen.Cursor := altcursor;

      altcursor := 1;
    end;
  end;

  if Assigned (Error) then begin
    if Assigned (Command) then
      TraceSQL ('SQL-Error : '+Error.Description+#13+#10+'                       '+Command.CommandText)
    else
      TraceSQL ('SQL-Error : '+Error.Description);
  end;

  {$ifdef SQLDebug}
    if not Assigned (Error) then begin
      if (fSQLExecStart <= GetTickCount) then
        dauer := GetTickCount - fSQLExecStart
      else
        dauer := DWORD ($100000000 - Int64 (fSQLExecStart)) + GetTickCount;

      if Assigned (Recordset) and (Recordset.State = 1) then
        TraceSQL ('SQL-Dauer : ' + IntToStr (dauer) + ' ms (' + IntToStr (Recordset.RecordCount)+')')
      else TraceSQL ('SQL-Dauer : ' + IntToStr (dauer) + ' ms');
    end;
  {$endif}

//  dbgstr := 'fDBQueryCount:'+IntToStr (fDBQueryCount);
//  OutputDebugString (PAnsiChar (@dbgstr[1]));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.MainADOConnectionBeforeConnect(Sender: TObject);
begin
  fDBQueryCount := 0;
  DBQueryWait  := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLVSDatenModul.SQLErrorHandler (const ErrorLocation, ErrorCode : String);
var
  errstr  : String;
  strpos,
  errcode : Integer;
begin
  if Assigned (ErrorTrackingModule) then
    ErrorTrackingModule.WriteErrorLog ('*************** SQL-Error ******************', ErrorLocation + CRLF + CRLF + ErrorCode);

  strpos := Pos ('ORA-', ErrorCode);

  if (strpos > 0) Then begin
    errstr := Copy (ErrorCode, strpos + 4, 5);

    try
      errcode := StrToInt (errstr);
    except
      errcode := -1;
    end;

    if (errcode = 1012) then begin
      PostMessage (WindowHandle, WM_USER + 1, 0, 0);
    end else if (errcode = 3114) then begin
      PostMessage (WindowHandle, WM_USER + 1, 0, 0);
    end;
  end;
end;

//******************************************************************************
//* Function Name: CheckDBOptions
//* Author       : Stefan Graf
//* Datum        : 22.06.2014
//******************************************************************************
//* Description  : Prüfen, ob die Option <OptIdx> in DB_OPTIONS gesetzt ist
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.CheckDBOptions (const OptIdx : Integer) : Boolean;
begin
  if (Length (fDBOptions) >= OptIdx) then
    Result := (fDBOptions [OptIdx] > '0')
  else
    Result := False;
end;

//******************************************************************************
//* Function Name: ViewExits
//* Author       : Stefan Graf
//* Datum        : 03.03.2022
//******************************************************************************
//* Description  : Prüfen, ob die View im Schema existiert
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.ViewExits (const ViewName : String) : Boolean;
var
  query : TADOQuery;
begin
  Result := false;

  query := TADOQuery.Create (Self);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from all_views WHERE view_name=:name AND owner=:owner');
    query.Parameters.ParamByName('name').Value := ViewName;
    query.Parameters.ParamByName('owner').Value := Schema;

    try
      query.Open;

      Result := query.RecordCount > 0;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name: ViewColumnExits
//* Author       : Stefan Graf
//* Datum        : 03.03.2022
//******************************************************************************
//* Description  : Prüfen, ob die View im Schema existiert
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.ViewColumnExits (const ViewName, ColName : String) : Boolean;
var
  query : TADOQuery;
begin
  Result := false;

  query := TADOQuery.Create (Self);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from all_views WHERE view_name=:name AND owner=:owner');
    query.Parameters.ParamByName('name').Value := ViewName;
    query.Parameters.ParamByName('owner').Value := Schema;

    try
      query.Open;

      if (query.RecordCount = 0) then
        Result := false
      else if Assigned (query.FindField (ColName)) then
        Result := true
      else
        Result := false;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name: FunctionExits
//* Author       : Stefan Graf
//* Datum        : 03.03.2022
//******************************************************************************
//* Description  : Prüfen, ob die View im Schema existiert
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLVSDatenModul.FunctionExits (const PackageName, FunctName : String) : Boolean;
var
  query : TADOQuery;
begin
  Result := false;

  query := TADOQuery.Create (Self);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from ALL_PROCEDURES where OBJECT_NAME=:pack and PROCEDURE_NAME=:funct and OWNER=:owner');
    query.Parameters.ParamByName('pack').Value := PackageName;
    query.Parameters.ParamByName('funct').Value := FunctName;
    query.Parameters.ParamByName('owner').Value := Schema;

    try
      query.Open;

      Result := query.RecordCount > 0;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;
end;

end.
