unit PackplatzUsingFRM;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms, 
  Dialogs, StdCtrls, Buttons, ColorButton, ExtCtrls, CustomPanelButton;

type
  TPackplatzUsingFrame = class(TFrame)
    DescLabel: TLabel;
    PackplatzButton: TCustomPanelButton;
    procedure PackplatzButtonClick(Sender: TObject);
  private
    fDate       : TDateTime;
    fRefWAPlatz : Integer;
    fActiveFlag : Char;
  public
    property RefWAPlatz : Integer   read fRefWAP<PERSON> write fRefWAPlatz;
    property ActiveFlag : Char      read fActiveFlag write fActiveFlag;
    property Date       : TDateTime read fDate       write fDate;

    constructor Create (AOwner: TComponent); override;
  end;

implementation

{$R *.dfm}

constructor TPackplatzUsingFrame.Create (AOwner: TComponent);
begin
  inherited;
end;

procedure TPackplatzUsingFrame.PackplatzButtonClick(Sender: TObject);
begin
  if (<PERSON>platzButton.Clicked) then
    PackplatzButton.Color := clLime
  else PackplatzButton.Color := clBtnFace;
end;

end.
