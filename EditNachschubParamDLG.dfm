object EditNachschubParamForm: TEditNachschubParamForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Parameter f'#252'r die Nachschubsteuerung'
  ClientHeight = 597
  ClientWidth = 296
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    296
    597)
  TextHeight = 13
  object Bevel1: TBevel
    Left = 5
    Top = 555
    Width = 285
    Height = 4
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 472
  end
  object OkButton: TButton
    Left = 127
    Top = 566
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'OK'
    Default = True
    ModalResult = 1
    TabOrder = 7
  end
  object AbortButton: TButton
    Left = 215
    Top = 566
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 8
  end
  object KommLPPanel: TPanel
    Left = 0
    Top = 281
    Width = 296
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      296
      41)
    object Label5: TLabel
      Left = 8
      Top = 8
      Width = 59
      Height = 13
      Caption = 'Komm-Platz:'
    end
    object KommLPLabel: TLabel
      Left = 72
      Top = 8
      Width = 80
      Height = 13
      Caption = 'KommLPLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Bevel3: TBevel
      Left = 5
      Top = 32
      Width = 285
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 64
    end
  end
  object ArtikelPanel: TPanel
    Left = 0
    Top = 0
    Width = 296
    Height = 59
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      296
      59)
    object Label4: TLabel
      Left = 8
      Top = 4
      Width = 53
      Height = 13
      Caption = 'Artikel-Nr.:'
    end
    object ARNrLabel: TLabel
      Left = 80
      Top = 4
      Width = 62
      Height = 13
      Caption = 'ARNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object c: TBevel
      Left = 5
      Top = 47
      Width = 285
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 37
    end
    object Label8: TLabel
      Left = 8
      Top = 20
      Width = 54
      Height = 13
      Caption = 'Artikeltext:'
    end
    object ARTextLabel: TLabel
      Left = 80
      Top = 20
      Width = 74
      Height = 13
      Caption = 'ARTextLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object NachschubPanel: TPanel
    Left = 0
    Top = 322
    Width = 296
    Height = 227
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 6
    DesignSize = (
      296
      227)
    object NachsGroupBox: TGroupBox
      Left = 8
      Top = 75
      Width = 280
      Height = 151
      Anchors = [akLeft, akRight, akBottom]
      TabOrder = 0
      object Label2: TLabel
        Left = 8
        Top = 43
        Width = 134
        Height = 13
        Caption = 'Min. Bestand f'#252'r Nachschub'
      end
      object Label3: TLabel
        Left = 8
        Top = 101
        Width = 148
        Height = 13
        Caption = 'Min. Anzahl VPE f'#252'r Nachschub'
      end
      object Label6: TLabel
        Left = 8
        Top = 125
        Width = 152
        Height = 13
        Caption = 'Max. Anzahl VPE f'#252'r Nachschub'
      end
      object Label12: TLabel
        Left = 8
        Top = 67
        Width = 147
        Height = 13
        Caption = 'Max. Bestand nach Nachschub'
      end
      object MinBesEdit: TEdit
        Left = 184
        Top = 40
        Width = 65
        Height = 21
        TabOrder = 1
        Text = 'MinBesEdit'
        OnKeyPress = NumEditKeyPress
      end
      object MinVPEEdit: TEdit
        Left = 184
        Top = 98
        Width = 65
        Height = 21
        TabOrder = 3
        Text = 'MinVPEEdit'
        OnKeyPress = NumEditKeyPress
      end
      object MaxVPEEdit: TEdit
        Left = 184
        Top = 122
        Width = 65
        Height = 21
        TabOrder = 4
        Text = 'MaxVPEEdit'
        OnKeyPress = NumEditKeyPress
      end
      object AutoNachCheckBox: TCheckBox
        Left = 8
        Top = 8
        Width = 193
        Height = 17
        AllowGrayed = True
        Caption = 'Automatische Nachschubsteuerung'
        TabOrder = 0
        OnClick = AutoNachCheckBoxClick
      end
      object FullBesEdit: TEdit
        Left = 184
        Top = 64
        Width = 65
        Height = 21
        TabOrder = 2
        Text = 'FullBesEdit'
        OnKeyPress = NumEditKeyPress
      end
    end
    object NachschubArtPanel: TPanel
      Left = 0
      Top = 0
      Width = 296
      Height = 85
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 1
      DesignSize = (
        296
        85)
      object Label1: TLabel
        Left = 8
        Top = 0
        Width = 66
        Height = 13
        Caption = 'Nachschubart'
      end
      object NachArtComboBox: TComboBoxPro
        Left = 8
        Top = 18
        Width = 280
        Height = 21
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ItemHeight = 15
        TabOrder = 0
      end
      object MHDReinCheckBox: TCheckBox
        Left = 8
        Top = 53
        Width = 280
        Height = 17
        Caption = 'Nur MHD reinen Nachschub'
        TabOrder = 1
      end
    end
  end
  object LagerPanel: TPanel
    Left = 0
    Top = 59
    Width = 296
    Height = 61
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      296
      61)
    object Bevel4: TBevel
      Left = 5
      Top = 52
      Width = 285
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 32
    end
    object Label7: TLabel
      Left = 8
      Top = 0
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 16
      Width = 280
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = LagerComboBoxChange
    end
  end
  object PlatzPanel: TPanel
    Left = 0
    Top = 220
    Width = 296
    Height = 61
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    Visible = False
    DesignSize = (
      296
      61)
    object Bevel5: TBevel
      Left = 5
      Top = 52
      Width = 285
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 32
    end
    object Label9: TLabel
      Left = 8
      Top = 0
      Width = 50
      Height = 13
      Caption = 'Lagerplatz'
    end
    object LPComboBox: TComboBoxPro
      Left = 8
      Top = 16
      Width = 280
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
    end
  end
  object ZonePanel: TPanel
    Left = 0
    Top = 170
    Width = 296
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    Visible = False
    DesignSize = (
      296
      50)
    object Label10: TLabel
      Left = 8
      Top = 0
      Width = 24
      Height = 13
      Caption = 'Zone'
    end
    object ZoneComboBox: TComboBoxPro
      Left = 8
      Top = 16
      Width = 280
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = ZoneComboBoxChange
    end
  end
  object BereichPanel: TPanel
    Left = 0
    Top = 120
    Width = 296
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    Visible = False
    DesignSize = (
      296
      50)
    object Label11: TLabel
      Left = 8
      Top = 0
      Width = 35
      Height = 13
      Caption = 'Bereich'
    end
    object LBComboBox: TComboBoxPro
      Left = 8
      Top = 16
      Width = 280
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = LBComboBoxChange
    end
  end
end
