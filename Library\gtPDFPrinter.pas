{ *
  *   Gnostice PDFtoolkit v5.0
  *   Copyright(c)2009 - 2015
  *   Gnostice Information Technologies Private Limited http:
  *   www.gnostice.com
  *
}
{$I gtDefines.inc}
{$I gtPTKDefines.inc}
unit gtPDFPrinter;

interface

uses
  Classes, Controls, windows, Graphics, gtCstPDFDoc, gtExPDFDoc, gtExProPDFDoc,
  gtPDFConsts, gtPDFUtils, printers, Dialogs, gtPDFResStrs, SysUtils, WinSpool,
  math, winsvc, gtPDFDoc, gtMultiMonitorAwareness,
  PDIGDIPlus, ActiveX, gtStreamAdpt, Forms
{$IFDEF gtActiveX}
    , gtPDFClasses, Buttons
{$ENDIF}
    ;

const
  PrinterTitle = 'Gnostice PDFPrinter - ';

type

  TgtPDFPageScaling = (psNone, psFitPage);

  TgtDuplexPrintingMode = (dpmNone, dpmVertical, dpmHorizontal);

  TgtPrintOrientationType = (potPortrait, potLandScape);

  TgtColorMode = (cmColor, cmMonochrome);

  TgtTextOutputPrecision = (tpAlignment, tpDefault);

  TgtPrintQuality = (pqHigh, pqMedium, pqLow, pqDraft);

  TgtOnDuplexPrintingModeChange = procedure(Sender: TObject;
    var Continue: Boolean) of object;

  TgtOnBinSelectionByNameModeChange = procedure(Sender: TObject;
    var Continue: Boolean; BinName: string; var BinIndex: Integer) of object;

  TgtOnBinSelectionByIndexModeChange = procedure(Sender: TObject;
    var Continue: Boolean; var BinIndex: Integer) of object;

  TgtOnColorMonoChromePrintingModeChange = procedure(Sender: TObject;
    var Continue: Boolean) of object;

  TgtOnOrientationPrintingChange = procedure(Sender: TObject;
    var Continue: Boolean) of object;

  FHackExPro = class(TgtExProPDFDocument);

  TgtPrinterCapabilities = class
  private
    FColor: Boolean;
    FBinNames: TStringList;
    FDuplex: Boolean;
    FUseGDI: Boolean;
    FLandscape: Boolean;
    FPaperNames: TStringList;
    function GetPaperSize: Integer;
    procedure SetPaperSize(const Value: Integer);
    procedure SetPaperWidth(Value: Extended);
    procedure SetPaperHeight(Value: Extended);
    function GetInchesInTenthOfaMM(aValue: Extended): Extended;
  public
    constructor Create;
    destructor Destroy; override;
    property Duplex: Boolean read FDuplex;
    property UseGDI: Boolean read FUseGDI write FUseGDI default False;
    property BinNames: TStringList read FBinNames;
    property Color: Boolean read FColor;
    property Landscape: Boolean read FLandscape;
    property PaperNames: TStringList read FPaperNames;
    property PaperSize: Integer read GetPaperSize write SetPaperSize;
    property PaperWidth: Extended write SetPaperWidth;
    property PaperHeight: Extended write SetPaperHeight;
  end;

  TgtAdvancedPrinterSettings = class
  private
    FDuplexPrintingMode: TgtDuplexPrintingMode;
    FOnDuplexmodePrintingChange: TgtOnDuplexPrintingModeChange;
    FBinName: string;
    FOnBinSelectionByNameChange: TgtOnBinSelectionByNameModeChange;
    FBinIndex: Integer;
    FOnBinSelectionByIndexChange: TgtOnBinSelectionByIndexModeChange;
    FColor: TgtColorMode;
    FOnColorMonoChromePrintingChange: TgtOnColorMonoChromePrintingModeChange;
    FOrientation: TgtPrintOrientationType;
    FOnOrientationPrintingChange: TgtOnOrientationPrintingChange;
    FPrintQuality: TgtPrintQuality;

    procedure SetDuplexPrintingMode(const Value: TgtDuplexPrintingMode);
    procedure SetOnDuplexmodePrintingChange(const Value
      : TgtOnDuplexPrintingModeChange);

    procedure SetBinName(const Value: string);
    procedure SetOnBinSelectionByNameChange(const Value
      : TgtOnBinSelectionByNameModeChange);

    procedure SetBinIndex(Value: Integer);
    procedure SetOnBinSelectionByIndexChange(const Value
      : TgtOnBinSelectionByIndexModeChange);

    procedure SetColor(const Value: TgtColorMode);
    procedure SetOnColorMonoChromePrintingChange(const Value
      : TgtOnColorMonoChromePrintingModeChange);

    procedure SetOrientation(const Value: TgtPrintOrientationType);
    procedure SetOnPrintingOrientationChange(const Value
      : TgtOnOrientationPrintingChange);

    procedure SetPrintQuality(Value: TgtPrintQuality);

  public
    property DuplexPrintingMode: TgtDuplexPrintingMode read FDuplexPrintingMode
      write SetDuplexPrintingMode;
    property BinName: string read FBinName write SetBinName;
    property BinIndex: Integer read FBinIndex write SetBinIndex;
    property Color: TgtColorMode read FColor write SetColor;
    property Orientation: TgtPrintOrientationType read FOrientation
      write SetOrientation;
    property PrintQuality: TgtPrintQuality read FPrintQuality
      write SetPrintQuality;

    property OnDuplexmodePrintingChange: TgtOnDuplexPrintingModeChange
      read FOnDuplexmodePrintingChange write SetOnDuplexmodePrintingChange;

    property OnBinSelectionByNameChange: TgtOnBinSelectionByNameModeChange
      read FOnBinSelectionByNameChange write SetOnBinSelectionByNameChange;

    property OnBinSelectionByIndexChange: TgtOnBinSelectionByIndexModeChange
      read FOnBinSelectionByIndexChange write SetOnBinSelectionByIndexChange;

    property OnColorMonoChromePrintingChange
      : TgtOnColorMonoChromePrintingModeChange
      read FOnColorMonoChromePrintingChange
      write SetOnColorMonoChromePrintingChange;

    property OnOrientationPrintingChange: TgtOnOrientationPrintingChange
      read FOnOrientationPrintingChange write SetOnPrintingOrientationChange;

  end;

  TgtRenderingOption = (roRasterize);
  TgtRenderingOptions = Set of TgtRenderingOption;
  TgtPDFPrinter = class; // forward declaration

  { TgtPDFPrinter }
{$IFDEF gtActiveX}

  TgtPDFPrinter = class(TCustomControl)
{$ELSE}
{$IFDEF gtDelphiXE2Up}
  [ComponentPlatformsAttribute(pidWin32 or pidWin64)]
{$ENDIF}

  TgtPDFPrinter = class(TPrintDialog)
{$ENDIF}
  private
    FTextOutputPrecision: TgtTextOutputPrecision;
    FScaling: TgtPDFPageScaling;
    FPDFDocument: TgtCustomPDFDocument;
    FPrinterCapabilities: TgtPrinterCapabilities;
{$IFDEF gtDelphi2009Up}
    Device: String;
    Port: String;
    Driver: String;
{$ENDIF}
{$IFDEF gtActiveX}
    FPrintDlg: TPrintDialog;
    // . Following are required to draw the icon at design time.
    FIconBmp: Graphics.TBitmap;

    property IconBmp: Graphics.TBitmap read FIconBmp write FIconBmp;
    procedure SetCollate(Value: Boolean);
    procedure SetCopies(Value: Integer);
    procedure SetFromPage(Value: Integer);
    procedure SetMinPage(Value: Integer);
    procedure SetMaxPage(Value: Integer);
    procedure SetOptions(Value: TPrintDialogOptions);
    procedure SetPrintToFile(Value: Boolean);
    procedure SetPrintRange(Value: TPrintRange);
    procedure SetToPage(Value: Integer);
    procedure SetOnClose(Value: TNotifyEvent);
    procedure SetOnShow(Value: TNotifyEvent);

    function GetCollate: Boolean;
    function GetCopies: Integer;
    function GetFromPage: Integer;
    function GetMinPage: Integer;
    function GetMaxPage: Integer;
    function GetOptions: TPrintDialogOptions;
    function GetPrintToFile: Boolean;
    function GetPrintRange: TPrintRange;
    function GetToPage: Integer;
    function GetOnClose: TNotifyEvent;
    function GetOnShow: TNotifyEvent;
    procedure SetPrintCopies(const Value: Integer);
    function GetPrintCopies: Integer;
{$ENDIF}
    procedure SetAbout(const Value: string);
    procedure SetVersion(const Value: string);
    procedure SetShowSetupDialog(const Value: Boolean);
    procedure SetScaling(const Value: TgtPDFPageScaling);

    procedure SetPDFDocument(const Value: TgtCustomPDFDocument);
    procedure PopulateCapability;

    procedure AfterConstruction; override;
    function ServiceGetStatus(sMachine, sService: string): DWord;
  private
    FAdvancedPrinterSettings: TgtAdvancedPrinterSettings;
    FAutoRotate: Boolean;
    FAbout: string;
    FVersion: string;
    FTitle: WideString;
    FShowSetupDialog: Boolean;
    FRotationAngle: TgtRotationAngle;
    FMargin: TPoint;
    FLeft, FTop, FRight, FBottom: Integer;
    FIgnoreHardMargin: Boolean;
    FRenderingOptions: TgtRenderingOptions;
    FPageList: TgtPageNumbers;
    function GeneratePageMetafile(APageNo: Integer): TStream; overload; virtual;
    function GeneratePageMetafile(AMetafile: TMetafile): TMetafile;
      overload; virtual;
    function GeneratePageMetafile(APageNo: Integer; ADummyParam: Integer)
      : TMetafile; overload; virtual;
    function GeneratePageBitmap(APageNo, axDPI, ayDPI, aRotationAngle
      : Integer): TBitmap;

    procedure SetAdvancedPrinterSettings(const Value
      : TgtAdvancedPrinterSettings);
    procedure SetRotationAngle(const Value:TgtRotationAngle);

      procedure OnDuplexmodePrintingChange(Sender: TObject;
      var Continue: Boolean);
    procedure OnBinSelectionByNameChange(Sender: TObject; var Continue: Boolean;
      BinName: string; var BinIndex: Integer);
    procedure OnBinSelectionByIndexChange(Sender: TObject;
      var Continue: Boolean; var BinIndex: Integer);
    procedure OnColorMonoChromePrintingChange(Sender: TObject;
      var Continue: Boolean);
    procedure OnOrientationPrintingChange(Sender: TObject;
      var Continue: Boolean);
    procedure SetPages(const Value: string);
  protected
    procedure Notification(AComponent: TComponent;
      Operation: TOperation); override;

  public
    Printed: Boolean;
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;
    procedure PrintDoc(); overload; virtual;
    procedure PrintDoc(AMetafile: TMetafile); overload; virtual;
    procedure Print(LK: Integer);
    procedure Reset;
    function GetInstalledPrinters: TStrings;
    function SelectPrinterByIndex(APrinterIndex: Integer): Boolean;
    function SelectPrinterByName(APrinterName: string): Boolean;
    property PrinterCapabilities: TgtPrinterCapabilities
      read FPrinterCapabilities;
    property AdvancedPrinterSettings: TgtAdvancedPrinterSettings
      read FAdvancedPrinterSettings write SetAdvancedPrinterSettings;
    property RotationAngle: TgtRotationAngle read FRotationAngle
      write SetRotationAngle;
{$IFDEF gtActiveX}
    procedure Paint; override;
{$ENDIF}
  published
    property About: string read FAbout write SetAbout;
    property Title: WideString read FTitle write FTitle;
    property PDFDocument: TgtCustomPDFDocument read FPDFDocument
      write SetPDFDocument;
    property Scaling: TgtPDFPageScaling read FScaling write SetScaling
      default psFitPage;
    property Version: string read FVersion write SetVersion;
    property ShowSetupDialog: Boolean read FShowSetupDialog
      write SetShowSetupDialog default True;
    property TextOutputPrecision: TgtTextOutputPrecision
      read FTextOutputPrecision write FTextOutputPrecision default tpDefault;
    property AutoRotate: Boolean read FAutoRotate write FAutoRotate;
    property IgnoreHardMargin: Boolean read FIgnoreHardMargin
      write FIgnoreHardMargin;
    property RenderingOptions: TgtRenderingOptions read FRenderingOptions
      write FRenderingOptions;
    property Pages: string write SetPages;
{$IFDEF gtActiveX}
    property Copies: Integer read GetPrintCopies write SetPrintCopies;
    property Collate: Boolean read GetCollate write SetCollate default False;
    property FromPage: Integer read GetFromPage write SetFromPage default 0;
    property MinPage: Integer read GetMinPage write SetMinPage default 0;
    property MaxPage: Integer read GetMaxPage write SetMaxPage default 0;
    property Options: TPrintDialogOptions read GetOptions write SetOptions
      default [];
    property PrintToFile: Boolean read GetPrintToFile write SetPrintToFile
      default False;
    property PrintRange: TPrintRange read GetPrintRange write SetPrintRange
      default prAllPages;
    property ToPage: Integer read GetToPage write SetToPage default 0;
    property OnClose: TNotifyEvent read GetOnClose write SetOnClose;
    property OnShow: TNotifyEvent read GetOnShow write SetOnShow;
{$ENDIF}
  end;

implementation

var
{$IFNDEF gtDelphi2009Up}
  Device: array [0 .. 255] of char;
  Port: array [0 .. 255] of char;
  Driver: array [0 .. 255] of char;
{$ENDIF}
  Hmode: THandle;
  PDevMode: PDeviceMode;

  { TgtPDFPrinter }

constructor TgtPrinterCapabilities.Create;
begin
  FBinNames := TStringList.Create;
  FPaperNames := TStringList.Create;
end;

procedure TgtPDFPrinter.AfterConstruction;
var
  ByteCnt, StructCnt: DWord;
begin
  inherited;
  // if (ServiceGetStatus('','Spooler') = SERVICE_RUNNING) then
  // begin
  if Not(csDesigning in ComponentState) then
    PopulateCapability;
  // end;
end;

function TgtPDFPrinter.ServiceGetStatus(sMachine, sService: string): DWord;
var
  //
  // service control
  // manager handle
  schm,
  //
  // service handle
  schs: SC_Handle;
  //
  // service status
  ss: TServiceStatus;
  //
  // current service status
  dwStat: DWord;
begin
  dwStat := 1;

  // connect to the service
  // control manager
  schm := OpenSCManager(PChar(sMachine), Nil, SC_MANAGER_CONNECT);

  // if successful...
  if (schm > 0) then
  begin
    // open a handle to
    // the specified service
    schs := OpenService(schm, PChar(sService),
      // we want to
      // query service status
      SERVICE_QUERY_STATUS);

    // if successful...
    if (schs > 0) then
    begin
      // retrieve the current status
      // of the specified service
      if (QueryServiceStatus(schs, ss)) then
      begin
        dwStat := ss.dwCurrentState;
      end;

      // close service handle
      CloseServiceHandle(schs);
    end;

    // close service control
    // manager handle
    CloseServiceHandle(schm);
  end;

  Result := dwStat;
end;

constructor TgtPDFPrinter.Create(AOwner: TComponent);
var
  ByteCnt, StructCnt: DWord;
begin
  inherited;
  FAbout := SProductName;
  FVersion := CVersion;
  FTextOutputPrecision := tpDefault;
  FTitle := '';
  ByteCnt := 0;
  StructCnt := 0;
{$IFDEF gtActiveX}
  FIconBmp := Graphics.TBitmap.Create;
  FIconBmp.Transparent := True;
  FIconBmp.TransparentMode := tmAuto;
  SetBounds(Left, Top, AX_SIZE, AX_SIZE);
  Constraints.MinHeight := AX_SIZE;
  Constraints.MinWidth := AX_SIZE;
  Constraints.MaxHeight := AX_SIZE;
  Constraints.MaxWidth := AX_SIZE;
{$ENDIF}
{$IFDEF gtActiveX}
  FPrintDlg := TPrintDialog.Create(nil);
  FPrintDlg.Options := [poPageNums];
  FPrintDlg.Copies := 1;
  FPrintDlg.Collate := False;
{$ELSE}
  // if (ServiceGetStatus('','Spooler') = SERVICE_RUNNING) then
  // begin
  Options := [poPageNums];
  // Throws AV when no printer attached.
  // GPTK-459 Initialization failure on TgtPDFPrinter component
  // Commenting this Copies causes blank output when dialog is not shown
  // Copies := 1;
  Collate := True;
  Scaling := psFitPage; // Added because PsNone is not as common as psFitPage
  // end;
{$ENDIF}
  FShowSetupDialog := True;
  FPrinterCapabilities := TgtPrinterCapabilities.Create;
  FAdvancedPrinterSettings := TgtAdvancedPrinterSettings.Create;
  FAdvancedPrinterSettings.OnDuplexmodePrintingChange :=
    OnDuplexmodePrintingChange;
  FAdvancedPrinterSettings.OnBinSelectionByNameChange :=
    OnBinSelectionByNameChange;
  FAdvancedPrinterSettings.OnBinSelectionByIndexChange :=
    OnBinSelectionByIndexChange;
  FAdvancedPrinterSettings.OnColorMonoChromePrintingChange :=
    OnColorMonoChromePrintingChange;
  FAdvancedPrinterSettings.OnOrientationPrintingChange :=
    OnOrientationPrintingChange;
{$IFDEF gtDelphi2009Up}
  SetLength(Device, 256);
  SetLength(Port, 256);
  SetLength(Driver, 256);
{$ENDIF}
  // by default consider hard margin
  FIgnoreHardMargin := True;
end;

destructor TgtPDFPrinter.Destroy;
begin
  try
    if Assigned(FAdvancedPrinterSettings) then
      FreeAndNil(FAdvancedPrinterSettings);
    if Assigned(FPrinterCapabilities) then
      FreeAndNil(FPrinterCapabilities);
{$IFDEF gtActiveX}
    FPrintDlg.Free;
{$ENDIF}
{$IFDEF gtDelphi2009Up}
    Finalize(Device);
    Finalize(Port);
    Finalize(Driver);
{$ENDIF}
  except
  end;
  inherited;
end;

function TgtPDFPrinter.GeneratePageMetafile(APageNo: Integer): TStream;
var
  LPageSize: TgtPageSize;
  LPageGraphic: TMetafileCanvas;
  FScaled: Boolean;
  LRotation: Integer;
  I, J, LXRes, LYRes, LBottom, LRight: Integer;
  LScale, LScaleX, LScaleY: Double;
  LPDFDoc: TgtCustomPDFDocument;
  LPaperWidth, LPaperHeight: Cardinal;
  // LDPI: Integer;

  procedure SwapAB(var A, B: Integer);
  begin
    A := A + B;
    B := A - B;
    A := A - B;
  end;

begin
  if (GetLastError = ERROR_PRINT_CANCELLED) then
    Exit;
  FScaled := False;
  LRotation := 0;
  Result := nil;
  LPDFDoc := PDFDocument;
  LPDFDoc.MeasurementUnit := muPixels;
  LPageSize := LPDFDoc.GetPageSize(APageNo, muPixels);
  // Get the resolution of Printer
  LXRes := GetDeviceCaps(Printer.Handle, LOGPIXELSX);
  LYRes := GetDeviceCaps(Printer.Handle, LOGPIXELSY);
  // LDPI := GetWindowMonitorDPI(Application.Handle);

  LBottom := Round(LPageSize.Height * (LXRes / Screen.PixelsPerInch));
  LRight := Round(LPageSize.Width * (LYRes / Screen.PixelsPerInch));

  LPaperWidth := Printer.PageWidth;
  LPaperHeight := Printer.PageHeight;
  { 'AutoRotate' }
  if AutoRotate then
  begin
    if ((LPageSize.Height > LPageSize.Width) and (LPaperHeight < LPaperWidth))
      or (LPageSize.Height < LPageSize.Width) and (LPaperHeight > LPaperWidth)
    then
    begin
      // Rotate 270 and swap right and bottom
      LRotation := 270;
      SwapAB(LRight, LBottom);
    end;
  end;

  { 'Scaling' }
  if (FScaling = psFitPage) and
    ((LBottom > LPaperHeight) or (LRight > LPaperWidth)) then // Fit
  begin
    // Set the drawing size to printer's page size
    FScaled := True;
    LScaleX := LPaperWidth / LRight;
    LScaleY := LPaperHeight / LBottom;
    LScale := Min(LScaleX, LScaleY);
    LBottom := Round(LBottom * LScale);
    LRight := Round(LRight * LScale);
  end
  else if (FScaling = psFitPage) and
    ((LBottom < LPaperHeight) or (LRight < LPaperWidth)) then // Fit
  begin
    // Set the drawing size to printer's page size
    FScaled := True;
    LScaleX := LPaperWidth / LRight;
    LScaleY := LPaperHeight / LBottom;
    LScale := Min(LScaleX, LScaleY);
    LBottom := Round(LBottom * LScale);
    LRight := Round(LRight * LScale);
  end;

  { 'Margin' }

  // If scaled, then the image should fit even after shifting it.
  // If Original size, let it crop,etc if it does't fit.
  if IgnoreHardMargin or AutoRotate then
    Escape(Printer.Handle, GETPRINTINGOFFSET, 0, nil, @FMargin);

  if FScaled then
  begin
    FLeft := FMargin.X;
    FTop := FMargin.Y;
    // LBottom := LBottom - FMargin.Y;
    // LRight := LRight - FMargin.X;
  end
  else
  begin
    FLeft := FMargin.X;
    FTop := FMargin.Y;
  end;

  { 'Recalculate margin based on rotation' }
  // Also need to recalculate margins if the page is rotated
  // if (LRotation = 270) and (not FScaled) then
  // begin
  // FTop := Abs(Printer.PageHeight - FTop - Round(LPageSize.Width * (LYRes / 96)));
  // end;

  Result := TMemoryStream.Create;
  // Metafile is used here for text as text printing

  // Result.Height := LBottom;
  FRight := LRight;
  FBottom := LBottom;
  // Result.Width := LRight;

  FHackExPro(LPDFDoc).RenderToStream(Result, APageNo, LRight, LBottom, LXRes,
    LYRes, LRotation, True, True);
  // LPageGraphic.Free;

end;

procedure TgtPDFPrinter.PrintDoc;
var
  LK, LCopies, LI, LJ: Integer;
  OffsetStart: TPoint;
  { vvv MYA 12/02/2015 vvv }
  vOwner: TComponent;
  vForm: TForm;
  vFormEnabled, LEnded: Boolean;
  vCursor: TCursor;
begin
  while Printer.Printing do
  begin
    Continue;
  end;
  Printed := False;
  if (PDFDocument <> nil) and (PDFDocument.IsLoaded) then
  begin
{$IFDEF gtActiveX}
    if ShowSetupDialog and not FPrintDlg.Execute then
      Exit;
{$ELSE}
    if ShowSetupDialog and not Execute then
      Exit;
{$ENDIF}
    // Atleast print one copy
    if Copies <= 0 then
      Copies := 1;
    if PrintRange = prAllPages then
    begin
      FromPage := 1;
      ToPage := PDFDocument.PageCount;
    end;
    if FromPage > ToPage then
      FromPage := ToPage;
    if ToPage < FromPage then
      ToPage := FromPage;
    if FromPage < 0 then
      FromPage := 1;
    if FromPage > PDFDocument.PageCount then
      FromPage := PDFDocument.PageCount;
    if ToPage < 0 then
      ToPage := 1;
    if ToPage > PDFDocument.PageCount then
      ToPage := PDFDocument.PageCount;

    if FTitle = '' then
    begin
      Printer.Title := PDFDocument.Filename;
      if PDFDocument.DocInfo.Title <> '' then
        Printer.Title := PDFDocument.DocInfo.Title
      else
        Printer.Title := PrinterTitle + Printer.Title;
    end
    else
      Printer.Title := FTitle;
    OffsetStart := Point(0, 0);
    // if IgnoreHardMargin then
    // Escape(Printer.Handle, GETPRINTINGOFFSET, 0, nil, @FMargin);

    try
{$IFDEF gtActiveX}
      LCopies := FPrintDlg.Copies;
      FPrintDlg.Copies := 1;
{$ELSE}
      LCopies := Copies;
      Copies := 1;
{$ENDIF}

      Printer.Copies := 1;
    except
    end;

    Printer.BeginDoc;
    try
      { vvv Make sure the user hasn't cancelled the print, which would result in a "GenericError" - MYA 21/10/2014 vvv }
      (* To replicate this error, select a printer that would normally print to a
        file (e.g. PDF Creator or XPS Document Writer). Then click OK, but when
        prompted for the file name, click "Cancel"
      *)

      if (GetLastError <> ERROR_PRINT_CANCELLED) then
      // (GetLastError <> ERROR_INVALID_HANDLE) then
      begin
        { ^^^ Make sure the user hasn't cancelled the print, which would result in a "GenericError" - MYA 21/10/2014 ^^^ }
        { vvv "Invalid Pointer Operation" fix - MYA 12/02/2015 vvv }
        { Disable the form that initiated the print. If it's closed while }
        { the printing is going on (and the document destroyed), it will }
        { result in an "Invalid Pointer Operation" and subsequent access }
        { violations                                     - MYA 12/02/2015 }
        vForm := Nil;
        vCursor := crDefault;
        vOwner := PDFDocument.Owner;
        while Assigned(vOwner) do
        begin
          if vOwner is TForm then
          begin
            vForm := TForm(vOwner);
            Break;
          end
          else
            vOwner := vOwner.Owner;
        end;
        try
          if Assigned(vForm) then
          begin
            vCursor := Screen.Cursor;
            vFormEnabled := vForm.Enabled;
            vForm.Enabled := False;
            Screen.Cursor := crHourGlass;
            Application.ProcessMessages;
          end;
          // if IgnoreHardMargin then // Disable hard margin
          // OffsetWindowOrgEx(Printer.Canvas.Handle, OffsetStart.X,
          // OffsetStart.Y, @FMargin);
          // --------Collate
          if PrintRange <> prSelection then
          begin
            if (Collate) then
            begin
              for LI := 1 to LCopies do
              begin
                for LK := FromPage to ToPage do
                begin
                  Print(LK);
                  LEnded := False;
                  if (LK < ToPage) then
                    Printer.NewPage;
                end;
                Printer.EndDoc;
                LEnded := True;
                if (LI < LCopies) then
                begin
                  Printer.BeginDoc;
                end;
              end;
            end
            else
            begin
              for LK := FromPage to ToPage do
              begin
                for LI := 1 to LCopies do
                begin
                  Print(LK);
                  LEnded := False;
                  if (LK < ToPage) or (LI < LCopies) then
                    Printer.NewPage;
                end;
              end;
            end;
          end
          else
          begin
            if (Collate) then
            begin
              for LI := 1 to LCopies do
              begin
                LJ := 0;
                while LJ < Length(FPageList) do
                begin
                  LK := FPageList[LJ];
                  Print(LK);
                  LEnded := False;
                  Inc(LJ);
                  if (LJ < Length(FPageList)) then
                    Printer.NewPage;
                end;
                Printer.EndDoc;
                LEnded := True;
                if (LI < LCopies) then
                begin
                  Printer.BeginDoc;
                end;
              end;
            end
            else
            begin
              LJ := 0;
              while LJ < Length(FPageList) do
              begin
                LK := FPageList[LJ];
                Inc(LJ);
                for LI := 1 to LCopies do
                begin
                  Print(LK);
                  LEnded := False;
                  if (LJ < Length(FPageList)) or (LI < LCopies) then
                    Printer.NewPage;
                end;
              end;
            end;
          end;
          // ----------------COLLATE End
          Printed := True;
        finally
          if Assigned(vForm) then
          begin
            { Restore the original properties }
            Screen.Cursor := vCursor;
            vForm.Enabled := vFormEnabled;
            vForm := Nil;
            Application.ProcessMessages;
          end;
        end;
      end;
    finally
      if not LEnded then
        Printer.EndDoc;
    end;
  end;
end;

function TgtPDFPrinter.GeneratePageBitmap(APageNo, axDPI, ayDPI,
  aRotationAngle: Integer): TBitmap;
var
  LPageBitmap: TBitmap;
  LPageSize: TgtPageSize;
  FScaled: Boolean;
  LRotation: Integer;
  I, J, LXRes, LYRes, LBottom, LRight { , LDPI } : Integer;
  LPDFDoc: TgtCustomPDFDocument;
  LTempStream: TMemoryStream;
  LScale, LScaleX, LScaleY: Double;
  LPaperWidth, LPaperHeight: Cardinal;

  procedure SwapAB(var A, B: Integer);
  begin
    A := A + B;
    B := A - B;
    A := A - B;
  end;

begin
  if (GetLastError = ERROR_PRINT_CANCELLED) then
    Exit;
  FScaled := False;
  LRotation := 0;
  Result := nil;
  LPDFDoc := PDFDocument;
  LPDFDoc.MeasurementUnit := muPixels;
  LPageSize := LPDFDoc.GetPageSize(APageNo, muPixels);
  // Get the resolution of Printer
  LXRes := GetDeviceCaps(Printer.Handle, LOGPIXELSX);
  LYRes := GetDeviceCaps(Printer.Handle, LOGPIXELSY);
  // LDPI := GetWindowMonitorDPI(Application.Handle);

  LBottom := Round(LPageSize.Height * (LXRes / Screen.PixelsPerInch));
  LRight := Round(LPageSize.Width * (LYRes / Screen.PixelsPerInch));

  LPaperWidth := Printer.PageWidth;
  LPaperHeight := Printer.PageHeight;
  { 'AutoRotate' }
  if AutoRotate then
  begin
    if ((LPageSize.Height > LPageSize.Width) and (LPaperHeight < LPaperWidth))
      or (LPageSize.Height < LPageSize.Width) and (LPaperHeight > LPaperWidth)
    then
    begin
      // Rotate 270 and swap right and bottom
      LRotation := 270;
      SwapAB(LRight, LBottom);
    end;
  end;

  { 'Scaling' }
  if (FScaling = psFitPage) and
    ((LBottom > LPaperHeight) or (LRight > LPaperWidth)) then // Fit
  begin
    // Set the drawing size to printer's page size
    FScaled := True;
    LScaleX := LPaperWidth / LRight;
    LScaleY := LPaperHeight / LBottom;
    LScale := Min(LScaleX, LScaleY);
    LBottom := Round(LBottom * LScale);
    LRight := Round(LRight * LScale);
  end
  else if (FScaling = psFitPage) and
    ((LBottom < LPaperHeight) or (LRight < LPaperWidth)) then // Fit
  begin
    // Set the drawing size to printer's page size
    FScaled := True;
    LScaleX := LPaperWidth / LRight;
    LScaleY := LPaperHeight / LBottom;
    LScale := Min(LScaleX, LScaleY);
    LBottom := Round(LBottom * LScale);
    LRight := Round(LRight * LScale);
  end;

  { 'Margin' }

  // If scaled, then the image should fit even after shifting it.
  // If Original size, let it crop,etc if it does't fit.
  Escape(Printer.Handle, GETPRINTINGOFFSET, 0, nil, @FMargin);
  if FScaled then
  begin
    FLeft := FMargin.X;
    FTop := FMargin.Y;
    // LBottom := LBottom - FMargin.Y;
    // LRight := LRight - FMargin.X;
  end
  else
  begin
    FLeft := FMargin.X;
    FTop := FMargin.Y;
  end;

  { 'Recalculate margin based on rotation' }
  // Also need to recalculate margins if the page is rotated
  if (LRotation = 270) and (not FScaled) then
  begin
    FTop := Abs(Printer.PageHeight - FTop - Round(LPageSize.Width *
      (LYRes / 96)));
  end;

  FRight := LRight;
  FBottom := LBottom;

  LPageBitmap := TBitmap.Create;
  LPageBitmap.PixelFormat := pf24bit;
  LPageBitmap.Width := LRight;
  LPageBitmap.Height := LBottom;

{$IFDEF gtDelphi2009Up}
  LPageBitmap.Canvas.Lock;
{$ENDIF}
  // Render contents of page to bitmap
  TgtPDFDocument(PDFDocument).RenderToDC(LPageBitmap.Canvas.Handle,
    LPageBitmap.Width, LPageBitmap.Height, APageNo, axDPI, ayDPI,
    aRotationAngle, True, True);
{$IFDEF gtDelphi2009Up}
  LPageBitmap.Canvas.Unlock;
{$ENDIF}
  Result := LPageBitmap;
end;

function TgtPDFPrinter.GeneratePageMetafile(AMetafile: TMetafile): TMetafile;
var
  LPageGraphic: TMetafileCanvas;
  FScaled: Boolean;
  LRotation: Integer;
  I, J, LXRes, LYRes, LBottom, LRight: Integer;
  LPDFDoc: TgtCustomPDFDocument;
  LTempStream: TMemoryStream;
  LScale, LScaleX, LScaleY: Double;
  LPaperWidth, LPaperHeight: Cardinal;
  procedure SwapAB(var A, B: Integer);
  begin
    A := A + B;
    B := A - B;
    A := A - B;
  end;

begin
  if (GetLastError = ERROR_PRINT_CANCELLED) then
    Exit;
  FScaled := False;
  LRotation := 0;
  LPDFDoc := PDFDocument;
  // LPDFDoc.MeasurementUnit := muPoints;
  // Get the resolution of Printer
  LXRes := GetDeviceCaps(Printer.Handle, LOGPIXELSX);
  LYRes := GetDeviceCaps(Printer.Handle, LOGPIXELSY);

  LBottom := Round(AMetafile.Height * (LYRes / 96));
  LRight := Round(AMetafile.Width * (LXRes / 96));

  LPaperWidth := Printer.PageWidth;
  LPaperHeight := Printer.PageHeight;
  { 'AutoRotate' }
  if AutoRotate then
  begin
    if ((AMetafile.Height > AMetafile.Width) and (LPaperHeight < LPaperWidth))
      or (AMetafile.Height < AMetafile.Width) and (LPaperHeight > LPaperWidth)
    then
    begin
      // Rotate 270 and swap right and bottom
      LRotation := 270;
      SwapAB(LRight, LBottom);
    end;
  end;

  { 'Scaling' }
  if ((FScaling = psFitPage) or ((LBottom > LPaperHeight) or
    (LRight > LPaperWidth))) then // Fit
  begin
    // Set the drawing size to printer's page size
    FScaled := True;
    LScaleX := LPaperWidth / LRight;
    LScaleY := LPaperHeight / LBottom;
    LScale := Min(LScaleX, LScaleY);
    LBottom := Round(LBottom * LScale);
    LRight := Round(LRight * LScale);
  end;

  { 'Margin' }

  // If scaled, then the image should fit even after shifting it.
  // If Original size, let it crop,etc if it does't fit.
  Escape(Printer.Handle, GETPRINTINGOFFSET, 0, nil, @FMargin);
  if FScaled then
  begin
    FLeft := FMargin.X;
    FTop := FMargin.Y;
    LBottom := LBottom - FMargin.Y;
    LRight := LRight - FMargin.X;
  end
  else
  begin
    FLeft := FMargin.X;
    FTop := FMargin.Y;
  end;

  { 'Recalculate margin based on rotation' }
  // Also need to recalculate margins if the page is rotated
  if (LRotation = 270) and (not FScaled) then
  begin
    FTop := Printer.PageHeight - FTop - Round(AMetafile.Width * (LYRes / 96));
  end;

  Result := TMetafile.Create;
  // Metafile is used here for text as text printing
  Result.Height := LBottom;
  Result.Width := LRight;
  LPageGraphic := TMetafileCanvas.Create(Result, 0);
  LTempStream := TMemoryStream.Create;
  AMetafile.SaveToStream(LTempStream);
  LTempStream.Position := 0;
  FHackExPro(LPDFDoc).RenderImageToCanvas(LPageGraphic.Handle, LTempStream, 0,
    0, LRight, LBottom, LRotation);

  LPageGraphic.Free;
  LTempStream.Free;
end;

function TgtPDFPrinter.GetInstalledPrinters: TStrings;
begin
  Result := Printer.printers;
end;

function TgtPDFPrinter.SelectPrinterByIndex(APrinterIndex: Integer): Boolean;
begin
  if (APrinterIndex < -1) or (APrinterIndex > (Printer.printers.Count - 1)) then
    Result := False
  else
  begin
    Printer.PrinterIndex := APrinterIndex;
    PopulateCapability;
    Result := True;
  end;
end;

function TgtPDFPrinter.SelectPrinterByName(APrinterName: string): Boolean;
var
  LPrinterList: TStrings;
  LI: Integer;
  LPrinterFound: Boolean;
begin
  LPrinterFound := False;
  LPrinterList := Printer.printers;
  for LI := 0 to LPrinterList.Count - 1 do
  begin
    if LPrinterList.Strings[LI] = APrinterName then
    begin
      LPrinterFound := True;
      Break;
    end;
  end;
  if LPrinterFound then
  begin
    Printer.PrinterIndex := LI;
    PopulateCapability;
    Result := True;
  end
  else
    Result := False;
end;

procedure TgtPDFPrinter.Reset;
begin
  if (Assigned(PDFDocument)) and (PDFDocument.IsLoaded) then
    PDFDocument.Reset;
end;

procedure TgtPDFPrinter.SetAbout(const Value: string);
begin
  // FAbout := Value;
end;

procedure TgtPDFPrinter.SetVersion(const Value: string);
begin
  // FVersion := Value;
end;

procedure TgtPDFPrinter.SetShowSetupDialog(const Value: Boolean);
begin
  FShowSetupDialog := Value;
end;

procedure TgtPDFPrinter.SetScaling(const Value: TgtPDFPageScaling);
begin
  FScaling := Value;
end;

procedure TgtPDFPrinter.SetPages(const Value: string);
begin
  FPageList := gtCstPDFDoc.GetPages(Value, FPDFDocument.PageCount);
end;

procedure TgtPDFPrinter.SetPDFDocument(const Value: TgtCustomPDFDocument);
begin
  FPDFDocument := Value;
  if Assigned(FPDFDocument) and FPDFDocument.IsLoaded then
  begin
    MinPage := 1;
    MaxPage := FPDFDocument.PageCount;
  end;
end;

procedure TgtPDFPrinter.SetRotationAngle(const Value: TgtRotationAngle);
begin
FRotationAngle:=Value;
PDFDocument.RotatePages('1-',Value) ;
end;

procedure TgtPDFPrinter.Notification(AComponent: TComponent;
  Operation: TOperation);
begin
  inherited;
  if (AComponent = FPDFDocument) and (Operation = opRemove) then
    FPDFDocument := nil;
end;
{$IFDEF gtActiveX}

procedure TgtPDFPrinter.SetPrintCopies(const Value: Integer);
begin
  // if (ServiceGetStatus('','Spooler') = SERVICE_RUNNING) then
  // begin
  FPrintDlg.Copies := Value;
  // end;
end;

function TgtPDFPrinter.GetPrintCopies: Integer;
begin
  Result := FPrintDlg.Copies;
end;

function TgtPDFPrinter.GetCollate: Boolean;
begin
  Result := FPrintDlg.Collate;
end;

function TgtPDFPrinter.GetCopies: Integer;
begin
  Result := FPrintDlg.Copies;
end;

function TgtPDFPrinter.GetFromPage: Integer;
begin
  Result := FPrintDlg.FromPage;
end;

function TgtPDFPrinter.GetMaxPage: Integer;
begin
  Result := FPrintDlg.MaxPage;
end;

function TgtPDFPrinter.GetMinPage: Integer;
begin
  Result := FPrintDlg.MinPage;
end;

function TgtPDFPrinter.GetOptions: TPrintDialogOptions;
begin
  Result := FPrintDlg.Options;
end;

function TgtPDFPrinter.GetPrintRange: TPrintRange;
begin
  Result := FPrintDlg.PrintRange;
end;

function TgtPDFPrinter.GetPrintToFile: Boolean;
begin
  Result := FPrintDlg.PrintToFile;
end;

function TgtPDFPrinter.GetToPage: Integer;
begin
  Result := FPrintDlg.ToPage;
end;

procedure TgtPDFPrinter.SetCollate(Value: Boolean);
begin
  FPrintDlg.Collate := Value;
end;

procedure TgtPDFPrinter.SetCopies(Value: Integer);
begin
  FPrintDlg.Copies := Value;
end;

procedure TgtPDFPrinter.SetFromPage(Value: Integer);
begin
  FPrintDlg.FromPage := Value;
end;

procedure TgtPDFPrinter.SetMaxPage(Value: Integer);
begin
  FPrintDlg.MaxPage := Value;
end;

procedure TgtPDFPrinter.SetMinPage(Value: Integer);
begin
  FPrintDlg.MinPage := Value;
end;

procedure TgtPDFPrinter.SetOptions(Value: TPrintDialogOptions);
begin
  FPrintDlg.Options := Value;
end;

procedure TgtPDFPrinter.SetPrintRange(Value: TPrintRange);
begin
  FPrintDlg.PrintRange := Value;
end;

procedure TgtPDFPrinter.SetPrintToFile(Value: Boolean);
begin
  FPrintDlg.PrintToFile := Value;
end;

procedure TgtPDFPrinter.SetToPage(Value: Integer);
begin
  FPrintDlg.ToPage := Value;
end;

function TgtPDFPrinter.GetOnClose: TNotifyEvent;
begin
  Result := FPrintDlg.OnClose;
end;

function TgtPDFPrinter.GetOnShow: TNotifyEvent;
begin
  Result := FPrintDlg.OnShow;
end;

procedure TgtPDFPrinter.SetOnClose(Value: TNotifyEvent);
begin
  FPrintDlg.OnClose := Value;
end;

procedure TgtPDFPrinter.SetOnShow(Value: TNotifyEvent);
begin
  FPrintDlg.OnShow := Value;
end;

procedure TgtPDFPrinter.Paint;
begin
  inherited;
  DrawButtonFace(Canvas, Rect(0, 0, AX_SIZE, AX_SIZE), 1, bsNew, False,
    False, True);
  if Assigned(IconBmp) then
    IconBmp.LoadFromResourceID(HInstance, 3);
  Canvas.Draw(2, 2, IconBmp);
end;
{$ENDIF}

procedure TgtPDFPrinter.SetAdvancedPrinterSettings
  (const Value: TgtAdvancedPrinterSettings);
begin
  FAdvancedPrinterSettings := Value;
end;

procedure TgtPDFPrinter.OnDuplexmodePrintingChange(Sender: TObject;

  var Continue: Boolean);
begin
  Continue := PrinterCapabilities.Duplex;
end;

procedure TgtPDFPrinter.OnOrientationPrintingChange(Sender: TObject;

  var Continue: Boolean);
begin
  Continue := PrinterCapabilities.Landscape;
end;

procedure TgtPDFPrinter.OnColorMonoChromePrintingChange(Sender: TObject;

  var Continue: Boolean);
begin
  Continue := PrinterCapabilities.Color;
end;

procedure TgtPDFPrinter.OnBinSelectionByNameChange(Sender: TObject;

  var Continue: Boolean; BinName: string;

  var BinIndex: Integer);
var
  Status: Integer;
begin
  Status := PrinterCapabilities.BinNames.IndexOf(BinName);
  if Status <> -1 then
  begin
    Continue := True;
    BinIndex := Integer(PrinterCapabilities.BinNames.Objects[Status]);
  end
  else
    Continue := False;
end;

procedure TgtPDFPrinter.OnBinSelectionByIndexChange(Sender: TObject;

  var Continue: Boolean;

  var BinIndex: Integer);
var
  count1: Integer;
  CheckIndex: Boolean;
begin
  CheckIndex := False;
  (*
    for count1 := 0 to PrinterCapabilities.BinNames.Count - 1 do
    begin
    if BinIndex = Integer(PrinterCapabilities.BinNames.Objects[count1]) then
    begin
    CheckIndex := True;
    Break;
    end;
    end;
  *)
  if BinIndex < PrinterCapabilities.BinNames.Count then
  begin
    CheckIndex := True;
    BinIndex := Integer(PrinterCapabilities.BinNames.Objects[BinIndex]);
  end;
  if CheckIndex then
    Continue := True
  else
    Continue := False;
end;

procedure TgtPDFPrinter.PopulateCapability;
type
  BinName = array [0 .. 23] of char;
  BinNameArray = array [1 .. high(Integer) div sizeof(BinName)] of BinName;
  PBinName = ^BinNameArray;
  NumBinName = array [1 .. high(Integer) div sizeof(Word)] of Word;
  PNumBinName = ^NumBinName;
  PaperName = array [0 .. 63] of char;
  PaperNameArray = array [1 .. high(Integer) div sizeof(PaperName)
    ] of PaperName;
  PPaperName = ^PaperNameArray;
var
  Status: Integer;
  BufferSize: Integer;
  DeviceOut: PDeviceMode;
  DeviceIn: PDeviceMode;
  PBinNameT: PBinName;
  PNumBin: PNumBinName;
  BNum, Iterator: Integer;
  NoOfPaper: Integer;
  PPaperName1: PPaperName;
begin
  PBinNameT := nil;
  PNumBin := nil;
  PPaperName1 := nil;
  DeviceOut := nil;
  DeviceIn := nil;
{$IFDEF gtDelphi2009Up}
  // Printer issue needs to be fixed here
  // Using Wide String Device name for 2009 and above
  Printer.GetPrinter(PWideChar(Device), PWideChar(Driver), PChar(Port), Hmode);
{$ELSE}
  Printer.GetPrinter(Device, Driver, Port, Hmode);
{$ENDIF}
  { ----------- Checking Paper Names that Printer Supports----------------- }
{$IFDEF gtDelphi2009Up}
  NoOfPaper := DeviceCapabilities(PWideChar(Device), PWideChar(Port),
    DC_PAPERNAMES, nil, nil);
  if NoOfPaper > 0 then
    GetMem(PPaperName1, NoOfPaper * 128);
{$ELSE}
  NoOfPaper := DeviceCapabilitiesA(Device, Port, DC_PAPERNAMES, nil, nil);
  if NoOfPaper > 0 then
    GetMem(PPaperName1, NoOfPaper * 64);
{$ENDIF}
  PrinterCapabilities.FPaperNames.Clear;

  try
{$IFDEF gtDelphi2009Up}
    DeviceCapabilities(PWideChar(Device), PWideChar(Port), DC_PAPERNAMES,
      PWideChar(PPaperName1), nil);
{$ELSE}
    DeviceCapabilitiesA(Device, Port, DC_PAPERNAMES, PChar(PPaperName1), nil);
{$ENDIF}
    for Iterator := 1 to NoOfPaper do
    // Changed here from 0-1 coz of range check error
    begin
      PrinterCapabilities.FPaperNames.Add(PPaperName1^[Iterator]);
    end;
  finally
    FreeMem(PPaperName1);
  end;
  { --------------End of PaperName Checking for a Printer----------------- }

  { --------------Checking wether Printer Supports Landscape Printing--------- }
{$IFDEF gtDelphi2009Up}
  Status := DeviceCapabilities(PWideChar(Device), PWideChar(Port),
    DC_ORIENTATION, nil, nil);
{$ELSE}
  Status := DeviceCapabilitiesA(Device, Port, DC_ORIENTATION, nil, nil);
{$ENDIF}
  if Status = 0 then
    PrinterCapabilities.FLandscape := False
  else
    PrinterCapabilities.FLandscape := True;
  { --------------End of Landscape Printing Checking------------------------- }

  { ---------------Checking wether Printer Supports Duplex Printing---------- }
{$IFDEF gtDelphi2009Up}
  Status := WinSpool.DeviceCapabilities(PWideChar(Device), PWideChar(Port),
    DC_DUPLEX, nil, nil);
{$ELSE}
  Status := DeviceCapabilitiesA(Device, Port, DC_DUPLEX, nil, nil);
{$ENDIF}
  if Status = 1 then
    PrinterCapabilities.FDuplex := True
  else
    PrinterCapabilities.FDuplex := False;

  { ---------------End of Duplex Printing Checking-------------------------- }

  { ---------------Checking wether Printer Supports Color Printing---------- }
{$IFDEF gtDelphi2009Up}
  BufferSize := DocumentProperties(0, 0, PChar(Device), DeviceOut^,
    DeviceIn^, 0);
{$ELSE}
  BufferSize := DocumentProperties(0, 0, Device, DeviceOut^, DeviceIn^, 0);
{$ENDIF}
  if BufferSize >= 0 then
  begin
    GetMem(DeviceOut, BufferSize);
{$IFDEF gtDelphi2009Up}
    DocumentProperties(0, 0, PChar(Device), DeviceOut^, DeviceIn^,
      DM_OUT_BUFFER);
{$ELSE}
    DocumentProperties(0, 0, Device, DeviceOut^, DeviceIn^, DM_OUT_BUFFER);
{$ENDIF}
    if DeviceOut^.dmColor > 1 then
    begin
      FreeMem(DeviceOut);
      PrinterCapabilities.FColor := True
    end
    else
    begin
      FreeMem(DeviceOut);
      PrinterCapabilities.FColor := False;
    end;
  end
  else
    PrinterCapabilities.FColor := False;

  { ---------------End of Color Printing Checking-------------------------- }

  { ---------------Checking How many Bins Printer Supports ----------------- }
{$IFDEF gtDelphi2009Up}
  BNum := DeviceCapabilities(PWideChar(Device), PWideChar(Port), DC_BINNAMES,
    nil, nil);
{$ELSE}
  BNum := DeviceCapabilitiesA(Device, Port, DC_BINNAMES, nil, nil);
{$ENDIF}
  PrinterCapabilities.BinNames.Clear;
  if BNum > 0 then
  begin
    GetMem(PBinNameT, BNum * sizeof(BinName));
    GetMem(PNumBin, BNum * sizeof(Word));
    try
{$IFDEF gtDelphi2009Up}
      DeviceCapabilities(PWideChar(Device), PWideChar(Port), DC_BINNAMES,
        PWideChar(PBinNameT), nil);
      DeviceCapabilities(PWideChar(Device), PWideChar(Port), DC_BINS,
        PWideChar(PNumBin), nil);
{$ELSE}
      DeviceCapabilitiesA(Device, Port, DC_BINNAMES, PAnsiChar(PBinNameT), nil);
      DeviceCapabilitiesA(Device, Port, DC_BINS, PAnsiChar(PNumBin), nil);
{$ENDIF}
      for Iterator := 1 to BNum do
        PrinterCapabilities.BinNames.AddObject(PBinNameT^[Iterator],
          TObject(PNumBin^[Iterator]));
    except
    end;
    FreeMem(PBinNameT);
    FreeMem(PNumBin);
  end;
  { ---------------End of Bin Printing Checking----------------------------- }

end;

procedure TgtPDFPrinter.Print(LK: Integer);
var
  LPageMetafile: TMetafile;
  LStream: TStream;
  LBitmap: TBitmap;
begin
  if roRasterize in RenderingOptions then
  begin
    LStream := TMemoryStream.Create;
    LBitmap := GeneratePageBitmap(LK, 96, 96, 0);
    LBitmap.SaveToStream(LStream);
    if (FScaling = psFitPage) then
      FHackExPro(PDFDocument).RenderImageToCanvas(Printer.Canvas.Handle,
        LStream, 0, 0, FRight, FBottom, 0)
    else
      FHackExPro(PDFDocument).RenderImageToCanvas(Printer.Canvas.Handle,
        LStream, FLeft, FTop, FRight, FBottom, 0);
    if Assigned(LBitmap) then
      LBitmap.Free;
    if Assigned(LStream) then
      LStream.Free;
  end
  else if PrinterCapabilities.UseGDI then
  begin
    LPageMetafile := GeneratePageMetafile(LK, 0);
    Printer.Canvas.Draw(FLeft, FTop, LPageMetafile);
    if Assigned(LPageMetafile) then
      LPageMetafile.Free;
  end
  else
  begin
    LStream := GeneratePageMetafile(LK);
    LStream.Position := 0;
    if (FScaling = psFitPage) then
      FHackExPro(PDFDocument).RenderImageToCanvas(Printer.Canvas.Handle,
        LStream, 0, 0, FRight, FBottom, 0)
    else
      FHackExPro(PDFDocument).RenderImageToCanvas(Printer.Canvas.Handle,
        LStream, FLeft, FTop, FRight, FBottom, 0);
    if Assigned(LStream) then
      LStream.Free;
  end;
end;

procedure TgtPDFPrinter.PrintDoc(AMetafile: TMetafile);
var
  LK, LCopies, LI: Integer;
  OffsetStart: TPoint;
  { vvv MYA 12/02/2015 vvv }
  vOwner: TComponent;
  vForm: TForm;
  vFormEnabled: Boolean;
  vCursor: TCursor;
  LPageMetafile: TMetafile;
begin
  if (AMetafile <> nil) then
  begin
{$IFDEF gtActiveX}
    if ShowSetupDialog and not FPrintDlg.Execute then
      Exit;
{$ELSE}
    if ShowSetupDialog and not Execute then
      Exit;
{$ENDIF}
    // Atleast print one copy
    if Copies <= 0 then
      Copies := 1;

    OffsetStart := Point(0, 0);
    if IgnoreHardMargin then
      Escape(Printer.Handle, GETPRINTINGOFFSET, 0, nil, @FMargin);
{$IFDEF gtActiveX}
    LCopies := FPrintDlg.Copies;
    FPrintDlg.Copies := 1;
{$ELSE}
    LCopies := Copies;
    Copies := 1;
{$ENDIF}
    Printer.BeginDoc;
    try
      { vvv Make sure the user hasn't cancelled the print, which would result in a "GenericError" - MYA 21/10/2014 vvv }
      (* To replicate this error, select a printer that would normally print to a
        file (e.g. PDF Creator or XPS Document Writer). Then click OK, but when
        prompted for the file name, click "Cancel"
      *)

      if (GetLastError <> ERROR_PRINT_CANCELLED) then
      // (GetLastError <> ERROR_INVALID_HANDLE) then
      begin
        { ^^^ Make sure the user hasn't cancelled the print, which would result in a "GenericError" - MYA 21/10/2014 ^^^ }
        { vvv "Invalid Pointer Operation" fix - MYA 12/02/2015 vvv }
        { Disable the form that initiated the print. If it's closed while }
        { the printing is going on (and the document destroyed), it will }
        { result in an "Invalid Pointer Operation" and subsequent access }
        { violations                                     - MYA 12/02/2015 }
        // vForm := Nil;
        // vCursor := crDefault;
        // while Assigned(vOwner) do
        // begin
        // if vOwner is TForm then
        // begin
        // vForm := TForm(vOwner);
        // Break;
        // end
        // else
        // vOwner := vOwner.Owner;
        // end;
        try
          // if Assigned(vForm) then
          // begin
          // vCursor := Screen.Cursor;
          // vFormEnabled := vForm.Enabled;
          // vForm.Enabled := False;
          // Screen.Cursor := crHourGlass;
          // Application.ProcessMessages;
          // end;
          if IgnoreHardMargin then // Disable hard margin
            OffsetWindowOrgEx(Printer.Canvas.Handle, OffsetStart.X,
              OffsetStart.Y, @FMargin);

          for LI := 1 to LCopies do
          begin
            LPageMetafile := GeneratePageMetafile(AMetafile);
            Printer.Canvas.Draw(FTop, FLeft, LPageMetafile);
            if (LI < LCopies) then
              Printer.NewPage;
            if Assigned(LPageMetafile) then
              LPageMetafile.Free;
          end;

        finally
          // if Assigned(vForm) then
          // begin
          { Restore the original properties }
          // Screen.Cursor := vCursor;
          // vForm.Enabled := vFormEnabled;
          // vForm := Nil;
          // Application.ProcessMessages;
          // end;
        end;
      end;
    finally
      Printer.EndDoc;
    end;
  end;
end;

{ TgtPrinterAdvancedSetting }

{ -----------------Setting Duplex Printing for a Printing------------------- }

procedure TgtAdvancedPrinterSettings.SetDuplexPrintingMode
  (const Value: TgtDuplexPrintingMode);
var
  LDuplexSupported: Boolean;
begin

  LDuplexSupported := False;
  if Assigned(OnDuplexmodePrintingChange) then
    OnDuplexmodePrintingChange(Self, LDuplexSupported)
  else
    FDuplexPrintingMode := Value;
  if LDuplexSupported then
  begin
    FDuplexPrintingMode := Value;
    if Hmode <> 0 then
    begin
      PDevMode := GlobalLock(Hmode);
      if PDevMode <> nil then
      begin
        with PDevMode^ do
          try
            begin
              dmFields := dmFields or DM_DUPLEX;
              if Value = dpmVertical then
                dmDuplex := DMDUP_VERTICAL
              else if Value = dpmHorizontal then
                dmDuplex := DMDUP_HORIZONTAL
              else
                dmDuplex := DMDUP_SIMPLEX;
            end;
          finally
            GlobalUnlock(Hmode);
          end;
      end;
    end;
  end
  else
    // raise EUnsupportedDuplexError.create(SUnsupportedDuplex);
end;

{ ----------------------End Of Setting Duplex Printing for a Printer------------ }

{ -------------------Setting Bin by Name for a Printer-------------------------- }

procedure TgtAdvancedPrinterSettings.SetBinName(const Value: string);
var
  LBinSelectionByName: Boolean;
  LBinIndex: Integer;
begin
  LBinSelectionByName := False;
  LBinIndex := 0;
  if Assigned(OnBinSelectionByNameChange) then
    OnBinSelectionByNameChange(Self, LBinSelectionByName, Value, LBinIndex);
  if LBinSelectionByName then
  begin
    FBinName := Value;
    if Hmode <> 0 then
    begin
      PDevMode := GlobalLock(Hmode);
      with PDevMode^ do
        try
          begin
            dmFields := dmFields or DM_DEFAULTSOURCE;
            dmDefaultSource := LBinIndex;
          end;
        finally
          GlobalUnlock(Hmode);
        end;
    end;
  end
  else
    // raise EInvalidBinNameError.create(SInvalidBinName);
end;
{ ----------------------End of Setting Bin by Name for a Printer---------------- }

{ -----------------------Setting Bin by Index for a Printer--------------------- }

function TgtPrinterCapabilities.GetInchesInTenthOfaMM(aValue: Extended)
  : Extended;
begin
  Result := (aValue * 25.4) * 10;
end;

procedure TgtAdvancedPrinterSettings.SetBinIndex(Value: Integer);
var
  LBinSelectionByIndex: Boolean;
begin
  LBinSelectionByIndex := False;
  if Assigned(OnBinSelectionByIndexChange) then
    OnBinSelectionByIndexChange(Self, LBinSelectionByIndex, Value);
  if LBinSelectionByIndex then
  begin
    FBinIndex := Value;
    if Hmode <> 0 then
    begin
      PDevMode := GlobalLock(Hmode);
      with PDevMode^ do
        try
          begin
            dmFields := dmFields or DM_DEFAULTSOURCE;
            dmDefaultSource := Value;
          end;
        finally
          GlobalUnlock(Hmode);
        end;
    end;
  end
  else
    // raise EInvalidBinNameError.create(SInvalidBinIndex);
end;

{ ------------------------End of Setting Bin By Index for a printer------------- }

{ ------------------------Setting Printer Orientation--------------------------- }

procedure TgtAdvancedPrinterSettings.SetOrientation
  (const Value: TgtPrintOrientationType);
var
  LLandScapeCapability: Boolean;
  Orientation: Integer;
begin
  if Value = potPortrait then
    Orientation := 1
  else
    Orientation := 2;
  LLandScapeCapability := False;
  if Assigned(OnOrientationPrintingChange) then
    OnOrientationPrintingChange(Self, LLandScapeCapability);
  if not(LLandScapeCapability) and (Value = potLandScape) then
    // raise EUnsupportedLandScapeError.create(SUnsupportedOrientation)
  else
  begin
    FOrientation := Value;
    if Hmode <> 0 then
    begin
      PDevMode := GlobalLock(Hmode);
      with PDevMode^ do
        try
          begin
            dmFields := dmFields or DM_ORIENTATION;
            dmOrientation := Orientation;
          end;
        finally
          GlobalUnlock(Hmode);
        end;
    end;
  end;
end;
{ ---------------------End of Setting Printer Orientation---------------------- }

{ ---------------------Setting PrintQuality for a Printer---------------------- }

procedure TgtPrinterCapabilities.SetPaperHeight(Value: Extended);
begin
  if Hmode <> 0 then
  begin
    PDevMode := GlobalLock(Hmode);
    with PDevMode^ do
      try
        begin
          if dmPaperSize = DMPAPER_USER then
          begin
            dmFields := dmFields or DM_PAPERSIZE or DM_PAPERWIDTH or
              DM_PAPERLENGTH;
            dmPaperLength := Round(GetInchesInTenthOfaMM(Value));
          end;
        end;
      finally
        GlobalUnlock(Hmode);
      end;
  end;
end;

procedure TgtPrinterCapabilities.SetPaperWidth(Value: Extended);
begin
  if Hmode <> 0 then
  begin
    PDevMode := GlobalLock(Hmode);
    with PDevMode^ do
      try
        begin
          if dmPaperSize = DMPAPER_USER then
          begin
            dmFields := dmFields or DM_PAPERSIZE or DM_PAPERWIDTH or
              DM_PAPERLENGTH;
            dmPaperWidth := Round(GetInchesInTenthOfaMM(Value));
          end;
        end;
      finally
        GlobalUnlock(Hmode);
      end;
  end;
end;

procedure TgtAdvancedPrinterSettings.SetPrintQuality(Value: TgtPrintQuality);
begin
  if Hmode <> 0 then
  begin
    FPrintQuality := Value;
    PDevMode := GlobalLock(Hmode);
    with PDevMode^ do
      try
        begin
          dmFields := dmFields or DM_PRINTQUALITY;
          if Value = pqHigh then
            dmPrintQuality := short(DMRES_HIGH)
          else if Value = pqLow then
            dmPrintQuality := short(DMRES_LOW)
          else if Value = pqMedium then
            dmPrintQuality := short(DMRES_MEDIUM)
          else
            dmPrintQuality := short(DMRES_DRAFT);
        end;
      finally
        GlobalUnlock(Hmode);
      end;
  end;
end;

{ ---------------------End of Setting Print Quality for a Printer--------------- }

{ ---------------------Setting Color/Monochrome Printing for a Printer---------- }

procedure TgtAdvancedPrinterSettings.SetColor(const Value: TgtColorMode);
var
  LColorCapability: Boolean;
begin
  LColorCapability := False;
  if Assigned(OnColorMonoChromePrintingChange) then
    OnColorMonoChromePrintingChange(Self, LColorCapability);
  if LColorCapability = True then
  begin
    FColor := Value;
    if Hmode <> 0 then
    begin
      PDevMode := GlobalLock(Hmode);
      with PDevMode^ do
        try
          begin
            dmFields := dmFields or DM_COLOR;
            if Value = cmColor then
              dmColor := DMCOLOR_COLOR
            else
              dmColor := DMCOLOR_MONOCHROME;
          end;
        finally
          GlobalUnlock(Hmode);
        end;
    end;
  end;
end;

{ -------------End of Setting Color/MonoChrome printing for a Printer----------- }

procedure TgtAdvancedPrinterSettings.SetOnDuplexmodePrintingChange
  (const Value: TgtOnDuplexPrintingModeChange);
begin
  FOnDuplexmodePrintingChange := Value;
end;

procedure TgtAdvancedPrinterSettings.SetOnBinSelectionByNameChange
  (const Value: TgtOnBinSelectionByNameModeChange);
begin
  FOnBinSelectionByNameChange := Value;
end;

procedure TgtAdvancedPrinterSettings.SetOnBinSelectionByIndexChange
  (const Value: TgtOnBinSelectionByIndexModeChange);
begin
  FOnBinSelectionByIndexChange := Value;
end;

procedure TgtAdvancedPrinterSettings.SetOnColorMonoChromePrintingChange
  (const Value: TgtOnColorMonoChromePrintingModeChange);
begin
  FOnColorMonoChromePrintingChange := Value;
end;

procedure TgtAdvancedPrinterSettings.SetOnPrintingOrientationChange
  (const Value: TgtOnOrientationPrintingChange);
begin
  FOnOrientationPrintingChange := Value;
end;

destructor TgtPrinterCapabilities.Destroy;
begin
  if Assigned(FBinNames) then
    FreeAndNil(FBinNames);
  if Assigned(FPaperNames) then
    FreeAndNil(FPaperNames);
  inherited;
end;

function TgtPrinterCapabilities.GetPaperSize: Integer;
begin
  if Hmode <> 0 then
  begin
    PDevMode := GlobalLock(Hmode);
    if PDevMode <> nil then
    begin
      with PDevMode^ do
      begin
        try
          Result := PDevMode^.dmPaperSize;
        finally
          GlobalUnlock(Hmode);
        end;
      end;
    end;
  end;
end;

procedure TgtPrinterCapabilities.SetPaperSize(const Value: Integer);
begin
  if Hmode <> 0 then
  begin
    PDevMode := GlobalLock(Hmode);
    if PDevMode <> nil then
    begin
      with PDevMode^ do
      begin
        try
          dmPaperSize := Value;
          dmFields := PDevMode^.dmFields or DM_PAPERSIZE;
        finally
          GlobalUnlock(Hmode);
        end;
      end;
    end;
  end;
end;

function TgtPDFPrinter.GeneratePageMetafile(APageNo: Integer;
  ADummyParam: Integer): TMetafile;
var
  LPageSize: TgtPageSize;
  LPageGraphic: TMetafileCanvas;
  FScaled: Boolean;
  I, J, LXRes, LYRes, LBottom, LRight: Integer;
  LScale, LScaleX, LScaleY: Double;
  LPDFDoc: TgtCustomPDFDocument;
  LPaperWidth, LPaperHeight: Cardinal;
  // LDPI: Integer;
  LRotation: Integer;

  procedure SwapAB(var A, B: Integer);
  begin
    A := A + B;
    B := A - B;
    A := A - B;
  end;

begin
  if (GetLastError = ERROR_PRINT_CANCELLED) then
    Exit;
  FScaled := False;
  LRotation := 0;
  Result := nil;
  LPDFDoc := PDFDocument;
  LPDFDoc.MeasurementUnit := muPixels;
  LPageSize := LPDFDoc.GetPageSize(APageNo, muPixels);
  // Get the resolution of Printer
  LXRes := GetDeviceCaps(Printer.Handle, LOGPIXELSX);
  LYRes := GetDeviceCaps(Printer.Handle, LOGPIXELSY);
  // LDPI := GetWindowMonitorDPI(Application.Handle);

  LBottom := Round(LPageSize.Height * (LXRes / Screen.PixelsPerInch));
  LRight := Round(LPageSize.Width * (LYRes / Screen.PixelsPerInch));

  LPaperWidth := Printer.PageWidth;
  LPaperHeight := Printer.PageHeight;
  { 'AutoRotate' }
  if AutoRotate then
  begin
    if ((LPageSize.Height > LPageSize.Width) and (LPaperHeight < LPaperWidth))
      or (LPageSize.Height < LPageSize.Width) and (LPaperHeight > LPaperWidth)
    then
    begin
      // Rotate 270 and swap right and bottom
      LRotation := 270;
      SwapAB(LRight, LBottom);
    end;
  end;

  { 'Scaling' }
  if (FScaling = psFitPage) and
    ((LBottom > LPaperHeight) or (LRight > LPaperWidth)) then // Fit
  begin
    // Set the drawing size to printer's page size
    FScaled := True;
    LScaleX := LPaperWidth / LRight;
    LScaleY := LPaperHeight / LBottom;
    LScale := Min(LScaleX, LScaleY);
    LBottom := Round(LBottom * LScale);
    LRight := Round(LRight * LScale);
  end
  else if (FScaling = psFitPage) and
    ((LBottom < LPaperHeight) or (LRight < LPaperWidth)) then // Fit
  begin
    // Set the drawing size to printer's page size
    FScaled := True;
    LScaleX := LPaperWidth / LRight;
    LScaleY := LPaperHeight / LBottom;
    LScale := Min(LScaleX, LScaleY);
    LBottom := Round(LBottom * LScale);
    LRight := Round(LRight * LScale);
  end;

  { 'Margin' }

  // If scaled, then the image should fit even after shifting it.
  // If Original size, let it crop,etc if it does't fit.
  if IgnoreHardMargin or AutoRotate then
    Escape(Printer.Handle, GETPRINTINGOFFSET, 0, nil, @FMargin);

  if FScaled then
  begin
    FLeft := FMargin.X;
    FTop := FMargin.Y;
    // LBottom := LBottom - FMargin.Y;
    // LRight := LRight - FMargin.X;
  end
  else
  begin
    FLeft := FMargin.X;
    FTop := FMargin.Y;
  end;

  { 'Recalculate margin based on rotation' }
  // Also need to recalculate margins if the page is rotated
  // if (LRotation = 270) and (not FScaled) then
  // begin
  // FTop := Abs(Printer.PageHeight - FTop - Round(LPageSize.Width * (LYRes / 96)));
  // end;
  // Metafile is used here for text as text printing
  Result := TMetafile.Create;
  Result.Height := LBottom;
  FRight := LRight;
  FBottom := LBottom;
  Result.Width := LRight;

  LPageGraphic := TMetafileCanvas.Create(Result, 0);
  FHackExPro(LPDFDoc).RenderToDC(LPageGraphic.Handle, LRight, LBottom, APageNo,
    LXRes, LYRes, LRotation, True, True);
  LPageGraphic.Free;

end;

end.
