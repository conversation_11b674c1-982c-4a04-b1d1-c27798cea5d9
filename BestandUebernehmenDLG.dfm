object BestandUebForm: TBestandUebForm
  Left = 586
  Top = 383
  BorderStyle = bsDialog
  Caption = 'Warenbest'#228'nde auf Ladungstr'#228'ger buchen'
  ClientHeight = 293
  ClientWidth = 422
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poMainFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    422
    293)
  TextHeight = 13
  object OkButton: TButton
    Left = 252
    Top = 260
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'OK'
    Default = True
    ModalResult = 1
    TabOrder = 0
  end
  object AbortButton: TButton
    Left = 340
    Top = 260
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 1
  end
  object ArtikelGroupBox: TGroupBox
    Left = 8
    Top = 8
    Width = 408
    Height = 112
    Anchors = [akLeft, akTop, akRight]
    Caption = 'Bestand'
    TabOrder = 2
    object Panel1: TPanel
      Left = 2
      Top = 15
      Width = 404
      Height = 49
      Align = alClient
      BevelOuter = bvNone
      TabOrder = 0
      object Label1: TLabel
        Left = 8
        Top = 4
        Width = 71
        Height = 13
        Caption = 'Artikel-Nummer'
      end
      object Label2: TLabel
        Left = 124
        Top = 4
        Width = 94
        Height = 13
        Caption = 'Artikel-Bezeichnung'
      end
      object StaticText1: TStaticText
        Left = 8
        Top = 20
        Width = 100
        Height = 17
        AutoSize = False
        BevelInner = bvLowered
        BevelOuter = bvRaised
        BorderStyle = sbsSunken
        Caption = 'StaticText1'
        TabOrder = 0
      end
      object StaticText2: TStaticText
        Left = 124
        Top = 20
        Width = 261
        Height = 18
        AutoSize = False
        BevelInner = bvLowered
        BevelOuter = bvRaised
        BorderStyle = sbsSunken
        Caption = 'StaticText2'
        TabOrder = 1
      end
    end
    object MHDChargePanel: TPanel
      Left = 2
      Top = 64
      Width = 404
      Height = 46
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 1
      object Label3: TLabel
        Left = 8
        Top = 0
        Width = 25
        Height = 13
        Caption = 'MHD'
      end
      object Label4: TLabel
        Left = 124
        Top = 0
        Width = 34
        Height = 13
        Caption = 'Charge'
      end
      object StaticText3: TStaticText
        Left = 8
        Top = 16
        Width = 100
        Height = 17
        AutoSize = False
        BevelInner = bvLowered
        BevelOuter = bvRaised
        BorderStyle = sbsSunken
        Caption = 'StaticText3'
        TabOrder = 0
      end
      object StaticText4: TStaticText
        Left = 124
        Top = 16
        Width = 261
        Height = 18
        AutoSize = False
        BevelInner = bvLowered
        BevelOuter = bvRaised
        BorderStyle = sbsSunken
        Caption = 'StaticText4'
        TabOrder = 1
      end
    end
  end
  object GroupBox2: TGroupBox
    Left = 6
    Top = 129
    Width = 408
    Height = 125
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Ladungstr'#228'ger'
    TabOrder = 3
    object Label5: TLabel
      Left = 12
      Top = 24
      Width = 110
      Height = 13
      Caption = 'Ladungstr'#228'ger-Nummer'
    end
    object Label6: TLabel
      Left = 12
      Top = 72
      Width = 79
      Height = 13
      Caption = 'Anzahl Einheiten'
    end
    object Label7: TLabel
      Left = 277
      Top = 24
      Width = 84
      Height = 13
      Caption = 'Ladungstr'#228'ger-Art'
    end
    object LENrEdit: TEdit
      Left = 12
      Top = 40
      Width = 117
      Height = 21
      MaxLength = 12
      TabOrder = 0
      Text = 'LENrEdit'
      OnChange = LENrEditChange
      OnKeyPress = NumberKeyPress
    end
    object MengeEdit: TEdit
      Left = 12
      Top = 88
      Width = 117
      Height = 21
      TabOrder = 3
      Text = 'MengeEdit'
      OnChange = MengeEditChange
      OnKeyPress = NumberKeyPress
    end
    object LTComboBox: TComboBox
      Left = 277
      Top = 40
      Width = 116
      Height = 21
      Style = csDropDownList
      TabOrder = 2
    end
    object GenerateNrButton: TButton
      Left = 136
      Top = 40
      Width = 121
      Height = 21
      Caption = 'LE-Nummer erzeugen'
      TabOrder = 1
      OnClick = GenerateNrButtonClick
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 32
    Top = 272
  end
end
