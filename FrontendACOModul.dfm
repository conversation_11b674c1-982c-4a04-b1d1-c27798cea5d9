inherited FrontendACOModule: TFrontendACOModule
  Height = 88
  Width = 189
  object ACOListManager1: TACOListManager
    Left = 73
    Top = 6
    ACOItems = (
      'LVSForm;TTabSheet;LagerTabSheet;LVSForm.LagerTabSheet;;1;LAGER'
      
        'ArtikelForm;TButton;ChangeLBButton;ArtikelForm.ChangeLBButton;Ar' +
        'tikelStamm:Lagerbereich '#228'ndern;2;ARTIKEL'
      
        'LVSForm;TMenuItem;Benutzer1;LVSForm.Benutzer1;Men'#252':Verwaltung;3;' +
        'BEN'
      
        'LVSForm;TMenuItem;VerwaltenUser;LVSForm.VerwaltenUser;Men'#252':Benut' +
        'zerverwaltung;4;BEN'
      
        'LVSForm;TTabSheet;WETabSheet;LVSForm.WETabSheet;Tab:Wareneingang' +
        ';5;WE'
      
        'LVSForm;TButton;WEPrintButton;LVSForm.WEPrintButton;WE ausdrucke' +
        'n;6;WE'
      
        'LVSForm;TButton;CreateWEButton1;LVSForm.CreateWEButton1;Neuer WE' +
        ' anlegen;7;WE'
      
        'LVSForm;TButton;CreateWEButton2;LVSForm.CreateWEButton2;Neuer WE' +
        ' anlegen;8;WE'
      
        'LVSForm;TButton;CloseWEButton;LVSForm.CloseWEButton;WE abschlies' +
        'sen;9;WE'
      'LVSForm;TMenuItem;ArtikelMenu;LVSForm.ArtikelMenu;;10;ARTIKEL'
      'LVSForm;TMenuItem;Artikelstamm;LVSForm.Artikelstamm;;11;ARTIKEL'
      
        'LVSForm;TMenuItem;Ladungstraeger;LVSForm.Ladungstraeger;;12;LAGE' +
        'R'
      'LVSForm;TMenuItem;LagerMenu;LVSForm.LagerMenu;;13;LAGER'
      
        'LVSForm;TMenuItem;LagerTopologie;LVSForm.LagerTopologie;;14;LAGE' +
        'R'
      
        'LVSForm;TMenuItem;LeergutKonten;LVSForm.LeergutKonten;;15;LEERGU' +
        'T'
      
        'LVSForm;TMenuItem;Mandantenverwaltung;LVSForm.Mandantenverwaltun' +
        'g;;16;MANDANT'
      'LVSForm;TMenuItem;Relationen1;LVSForm.Relationen1;;17;TOUR'
      'LVSForm;TMenuItem;VPE1;LVSForm.VPE1;;18;ARTIKEL'
      'LVSForm;TTabSheet;WATabSheet;LVSForm.WATabSheet;;19;WA'
      'LVSForm;TTabSheet;VersTabSheet;LVSForm.VersTabSheet;;20;VERSAND'
      'LVSForm;TTabSheet;KommTabSheet;LVSForm.KommTabSheet;;21;KOMM'
      
        'LVSForm;TTabSheet;AuftragTabSheet;LVSForm.AuftragTabSheet;;22;AU' +
        'FTRAG'
      'LVSForm;TMenuItem;miRelationen;LVSForm.miRelationen;;23;TOUR'
      'LVSForm;TMenuItem;VPEMenuItem;LVSForm.VPEMenuItem;;24;ARTIKEL'
      
        'LVSForm;TTabSheet;WEBesTabSheet;LVSForm.WEBesTabSheet;Vereinnahm' +
        'ungen;25;WE'
      
        'LVSForm;TMenuItem;KonfigurationLager;LVSForm.KonfigurationLager;' +
        'Lager Konfigurationsparameter;26;CONFIG'
      
        'LVSForm;TMenuItem;KonfigurationMandant;LVSForm.KonfigurationMand' +
        'ant;Mandant Konfigurationsparameter;27;MANDANT'
      
        'LVSForm;TMenuItem;ConfigTemplates1;LVSForm.ConfigTemplates1;Conf' +
        'ig-Templates;28;CONFIG'
      
        'LVSForm;TTabSheet;LagerBesTabSheet;LVSForm.LagerBesTabSheet;Best' +
        'and;29;BESTAND'
      
        'LVSForm;TTabSheet;LagerLETabSheet;LVSForm.LagerLETabSheet;LE;30;' +
        'LE'
      
        'LVSForm;TTabSheet;LagerLPTabSheet;LVSForm.LagerLPTabSheet;Lagerp' +
        'l'#228'tze;31;LP'
      
        'LVSForm;TTabSheet;LagerNVETabSheet;LVSForm.LagerNVETabSheet;NVE;' +
        '32;NVE'
      
        'LVSForm;TTabSheet;LagerREVTabSheet;LVSForm.LagerREVTabSheet;Revi' +
        'sionsliste;33;REVISION'
      
        'LVSForm;TTabSheet;LagerResTabSheet;LVSForm.LagerResTabSheet;Best' +
        'andsreservierungen;34;BESTAND'
      
        'LVSForm;TTabSheet;LagerTATabSheet;LVSForm.LagerTATabSheet;Transp' +
        'ort;35;TRANSPORT'
      
        'LVSForm;TTabSheet;WEBestTabSheet;LVSForm.WEBestTabSheet;Warenerw' +
        'artung;36;WE'
      
        'LVSForm;TTabSheet;WEBuchTabSheet;LVSForm.WEBuchTabSheet;Warenein' +
        'gangsbuch;37;WE'
      
        'LVSForm;TTabSheet;VerwTabSheet;LVSForm.VerwTabSheet;Verwaltung;3' +
        '8;VERWALTUNG'
      
        'LVSForm;TTabSheet;BenTabSheet;LVSForm.BenTabSheet;Benutzer;39;BE' +
        'N'
      
        'LVSForm;TTabSheet;ProtoTabSheet;LVSForm.ProtoTabSheet;Protokoll;' +
        '40;PROTOKOLL'
      'LVSForm;TMenuItem;Setup1;LVSForm.Setup1;Einstellungen;41;SETUP'
      
        'LeergutKontoForm;TButton;ChangeButton;LeergutKontoForm.ChangeBut' +
        'ton;Leergut-Korrektur;42;'
      
        'LVSForm;TTabSheet;SessionTabSheet;LVSForm.SessionTabSheet;Datenb' +
        'ank-Sitzung;43;VERWALTUNG'
      
        'LVSForm;TTabSheet;WarenBesTabSheet;LVSForm.WarenBesTabSheet;Ware' +
        'nbestand;44;BESTAND'
      
        'LVSForm;TButton;ChangeWAButton;LVSForm.ChangeWAButton;Warenannah' +
        'me bearbeiten;45;WE'
      
        'LVSForm;TButton;WEAufLEButton;LVSForm.WEAufLEButton;Warenbest'#228'nd' +
        'e auf Ladungstr'#228'ger buchen...;46;WE'
      
        'LVSForm;TMenuItem;Passwortndern1;LVSForm.Passwortndern1;Passwort' +
        ' '#228'ndern;47;SETUP'
      
        'LVSForm;TButton;WEAufLPButton;LVSForm.WEAufLPButton;Warenbestand' +
        ' auf Lagerplatz buchen;48;WE'
      
        'LVSForm;TButton;WENacherfassenButton;LVSForm.WENacherfassenButto' +
        'n;Mengen und Gewicht erfassen;49;WE'
      
        'LVSForm;TMenuItem;HACCPTestMenu;LVSForm.HACCPTestMenu;HACCP-Test' +
        's;50;HACCP'
      
        'LVSForm;TMenuItem;MandantMenu;LVSForm.MandantMenu;Men'#252' Mandant;5' +
        '1;MANDANT'
      
        'LVSForm;TTabSheet;ManLiefTabSheet;LVSForm.ManLiefTabSheet;Person' +
        'alverkauf;52;'
      
        'LVSForm;TTabSheet;AufKopfTabSheet;LVSForm.AufKopfTabSheet;Auftr'#228 +
        'ge;53;AUFTRAG'
      
        'LVSForm;TMenuItem;BerichteMenu;LVSForm.BerichteMenu;Berichte;54;' +
        'BERICHT'
      
        'LVSForm;TMenuItem;agesstatistik1;LVSForm.agesstatistik1;Tagessta' +
        'tistik...;55;BERICHT'
      
        'LVSForm;TMenuItem;miReportFehlware;LVSForm.miReportFehlware;Fehl' +
        'ware...;56;BERICHT'
      
        'LVSForm;TTabSheet;KommAufTabSheet;LVSForm.KommAufTabSheet;Kommis' +
        'sionierauftr'#228'ge;57;KOMM'
      
        'LVSForm;TTabSheet;KommLPTabSheet;LVSForm.KommLPTabSheet;Kommissi' +
        'onier-Pl'#228'tze;58;KOMM'
      
        'LVSForm;TTabSheet;InvTabSheet;LVSForm.InvTabSheet;Inventur;59;IN' +
        'VENTUR'
      
        'LVSForm;TButton;KommUmlagerButton;LVSForm.KommUmlagerButton;Ladu' +
        'ngstr'#228'ger am Kommplatz umlagern;60;KOMM'
      
        'LVSForm;TButton;KommLblPrtButton;LVSForm.KommLblPrtButton;Kommla' +
        'bels drucken...;61;KOMM'
      
        'LVSForm;TButton;PrintLEButton2;LVSForm.PrintLEButton2;LE-Label d' +
        'rucken;62;LE'
      
        'LVSForm;TButton;PrintLEButton;LVSForm.PrintLEButton;LE-Label dru' +
        'cken;63;LE'
      
        'LVSForm;TButton;WEStornoButton;LVSForm.WEStornoButton;WE Stornie' +
        'ren;64;WE'
      
        'LVSForm;TButton;AddBesButton;LVSForm.AddBesButton;Bestand anlege' +
        'n;65;BESTAND'
      
        'LVSForm;TButton;DelBesButton;LVSForm.DelBesButton;Bestand l'#246'sche' +
        'n;66;BESTAND'
      
        'LVSForm;TButton;AddBesButton2;LVSForm.AddBesButton2;Warenbestand' +
        ' anlegen;67;BESTAND'
      
        'LVSForm;TButton;DelBesButton2;LVSForm.DelBesButton2;Warenbestand' +
        ' l'#246'schen;68;BESTAND'
      ';ACO;ChangeBestand;.ChangeBestand;Bestand '#228'ndern;69;BESTAND'
      ';ACO;ChangeArtikel;.ChangeArtikel;Artikel '#228'ndern;70;ARTIKEL'
      ';ACO;DeleteArtikel;.DeleteArtikel;Artikel l'#246'schen;71;ARTIKEL'
      
        'LVSForm;TButton;LEUmlagerButton;LVSForm.LEUmlagerButton;Ladungst' +
        'r'#228'ger umlagern;72;LE'
      
        'LVSForm;TButton;PrintLEButton3;LVSForm.PrintLEButton3;LE-Label d' +
        'rucken...;73;LE'
      
        'LVSForm;TButton;SendAvisButton;LVSForm.SendAvisButton;WE-Avis se' +
        'nden...;74;WE'
      
        'LVSForm;TButton;SendAvisButton2;LVSForm.SendAvisButton2;WE-Avis ' +
        'senden...;75;WE'
      
        'LVSForm;TButton;ChangeBesButton2;LVSForm.ChangeBesButton2;Bestan' +
        'd '#228'ndern...;76;BESTAND'
      
        'LVSForm;TMenuItem;Lager1;LVSForm.Lager1;Lagerauswertung;77;BERIC' +
        'HT'
      
        'LVSForm;TMenuItem;Abrechnung1;LVSForm.Abrechnung1;Bericht Lagera' +
        'brechnung;78;BERICHT'
      
        'LVSForm;TMenuItem;WEPosStornieren;LVSForm.WEPosStornieren;WE-Pos' +
        '. Stornieren;79;WE'
      
        'LVSForm;TMenuItem;WEReopenMenuItem;LVSForm.WEReopenMenuItem;WE w' +
        'ieder '#246'ffnen;80;WE'
      
        'LVSForm;TTabSheet;IFCProtoTabSheet;LVSForm.IFCProtoTabSheet;Date' +
        'ntransfer-Protokoll;81;VERWALTUNG'
      
        'LVSForm;TButton;IFCReloadButton;LVSForm.IFCReloadButton;IFC-Date' +
        'i neu laden;82;INTERFACE'
      
        'LVSForm;TButton;PrintEANButton;LVSForm.PrintEANButton;EAN128-Lab' +
        'les drucken...;83;EAN'
      
        'LVSForm;TButton;PrintEANButton2;LVSForm.PrintEANButton2;EAN128-L' +
        'ables drucken...;84;EAN'
      
        'LVSForm;TButton;CreateRetoureButton1;LVSForm.CreateRetoureButton' +
        '1;Neue Retourenannahme...;85;RETOUREN'
      
        'LVSForm;TButton;KommResetButton;LVSForm.KommResetButton;Komm. ne' +
        'u freigeben;86;KOMM'
      
        'LVSForm;TTabSheet;PackTabSheet;LVSForm.PackTabSheet;Packauftr'#228'ge' +
        ';87;AUFTRAG'
      
        'DispAuftragKommPosForm;TButton;DeleteButton;DispAuftragKommPosFo' +
        'rm.DeleteButton;Position l'#246'schen;88;KOMM'
      
        'DispAuftragKommPosForm;TButton;GewichtButton;DispAuftragKommPosF' +
        'orm.GewichtButton;Gewicht angeben;89;KOMM'
      
        'DispAuftragKommPosForm;TButton;MengeButton;DispAuftragKommPosFor' +
        'm.MengeButton;Menge '#228'ndern;90;KOMM'
      
        'LVSForm;TMenuItem;Warenempfnger1;LVSForm.Warenempfnger1;Warenemp' +
        'f'#228'nger;91;EMPF'
      
        'LVSForm;TMenuItem;Warenempfnger2;LVSForm.Warenempfnger2;Warenemp' +
        'f'#228'nger;92;EMPF'
      
        'LVSForm;TMenuItem;PackPopupTourundRelation;LVSForm.PackPopupTour' +
        'undRelation;Pack-Popup Tour und Relation;93;TOUR'
      
        'LVSForm;TMenuItem;Drucker1;LVSForm.Drucker1;Drucker Wartung;94;D' +
        'RUCKEN'
      
        'LVSForm;TButton;SperrBesButton2;LVSForm.SperrBesButton2;Bestand ' +
        'sperren;95;BESTAND'
      
        'LVSForm;TButton;FreeBesButton2;LVSForm.FreeBesButton2;Bestand fr' +
        'eigeben;96;BESTAND'
      
        'LVSForm;TMenuItem;Leistungsauswertung1;LVSForm.Leistungsauswertu' +
        'ng1;Komm-Leistungsauswertung;97;BERICHT'
      
        ';ACO;ChangeArtikelKomm;.ChangeArtikelKomm;Komm-Daten der Artikel' +
        ' '#228'ndern;98;ARTIKEL'
      
        'LVSForm;TMenuItem;Planunganlegen1;LVSForm.Planunganlegen1;Planun' +
        'g anlegen oder '#252'bernehmen;99;PLANUNG'
      
        ';ACO;ChangeKommUser;.ChangeKommUser;Darf Komm-User '#228'ndern;100;KO' +
        'MM'
      
        'LVSForm;TTabSheet;WAAuftragTabSheet;LVSForm.WAAuftragTabSheet;WA' +
        '-Abwicklung;101;WA'
      
        'LVSForm;TTabSheet;WAVerladungTabSheet;LVSForm.WAVerladungTabShee' +
        't;WA-Verladung;102;VERLADEN'
      ';ACO;ChangeAuftrag;.ChangeAuftrag;Auftrag '#228'ndern;103;AUFTRAG'
      
        'LVSForm;TButton;LieferPrtButton;LVSForm.LieferPrtButton;Liefersc' +
        'hein drucken...;104;LIEFERUNG'
      
        'LVSForm;TButton;ProformaPrintButton;LVSForm.ProformaPrintButton;' +
        'Proformarechnung drucken...;105;LIEFERUNG'
      ';ACO;DatenExport;.DatenExport;Daten exportieren;106;EXPORT'
      
        'LVSForm;TTabSheet;MDETabSheet;LVSForm.MDETabSheet;MDEs;107;VERWA' +
        'LTUNG'
      
        'LVSForm;TMenuItem;LieferantenBewertung1;LVSForm.LieferantenBewer' +
        'tung1;Lieferanten Bewertung...;108;BERICHT'
      
        'LVSForm;TButton;NachSchubButton;LVSForm.NachSchubButton;Nachschu' +
        'b...;109;NACHSCHUB'
      
        'LVSForm;TMenuItem;REWEFleischauszeichnung;LVSForm.REWEFleischaus' +
        'zeichnung;REWE Fleischauszeichnung...;110;'
      
        'BestellVereinnahmungForm;TMenuItem;NeuerArtikel1;BestellVereinna' +
        'hmungForm.NeuerArtikel1;Neuer Artikel...;111;WE'
      
        'LVSForm;TTabSheet;KommGrpTabSheet;LVSForm.KommGrpTabSheet;Kommis' +
        'sioniergruppen;112;KOMM'
      
        'LVSForm;TMenuItem;DelManLSPopupItem;LVSForm.DelManLSPopupItem;Ma' +
        'nueller Lieferschein l'#246'schen...;113;LIEFERUNG'
      
        'LVSForm;TButton;MDEMsgButton;LVSForm.MDEMsgButton;MDE Nachricht ' +
        'senden...;114;VERWALTUNG'
      
        'LVSForm;TMenuItem;BerichteAuswertungenMenuItem;LVSForm.BerichteA' +
        'uswertungenMenuItem;Auswertungen;115;BERICHT'
      
        'LVSForm;TButton;ChangeKommBenButton;LVSForm.ChangeKommBenButton;' +
        'Komm. wechseln...;116;KOMM'
      
        'LVSForm;TMenuItem;Warenausgang1;LVSForm.Warenausgang1;Warenausga' +
        'ng Bereicht;117;BERICHT'
      
        'LVSForm;TMenuItem;Wareneingang1;LVSForm.Wareneingang1;Wareneinga' +
        'ng Bereicht;118;BERICHT'
      
        'LVSForm;TMenuItem;Auswertung1;LVSForm.Auswertung1;Auftrag Auswer' +
        'tung Bericht;119;BERICHT'
      
        'LVSForm;TMenuItem;Rckverfolgung1;LVSForm.Rckverfolgung1;R'#252'ckverf' +
        'olgung Bericht;120;BERICHT'
      
        'LVSForm;TMenuItem;Bestandsliste1;LVSForm.Bestandsliste1;Bestands' +
        'liste Bericht;121;BERICHT'
      
        'LVSForm;TMenuItem;Stellplatzliste1;LVSForm.Stellplatzliste1;Stel' +
        'lplatzliste Bericht;122;BERICHT'
      
        'LVSForm;TMenuItem;MHDListe1;LVSForm.MHDListe1;MHD-Liste Bericht;' +
        '123;BERICHT'
      
        'LVSForm;TTabSheet;LiefRetTabSheet;LVSForm.LiefRetTabSheet;Liefer' +
        'anten-Retouren;124;LIEFERANT'
      
        'LVSForm;TMenuItem;ReportRetAnnahmeMenuItem;LVSForm.ReportRetAnna' +
        'hmeMenuItem;Retourenannahmen Bericht;125;RETOUREN'
      
        'LVSForm;TMenuItem;DelRetLSPopupItem;LVSForm.DelRetLSPopupItem;Re' +
        'tourenschein l'#246'schen...;126;RETOUREN'
      
        'LVSForm;TTabSheet;BatchTabSheet;LVSForm.BatchTabSheet;Batch-L'#228'uf' +
        'e;127;BATCH'
      
        'LVSForm;TMenuItem;SpedTourMenuItem;LVSForm.SpedTourMenuItem;Sped' +
        'ition und Auslieferung;128;SPEDITION'
      
        'LVSForm;TMenuItem;UmsatzAuswertung1;LVSForm.UmsatzAuswertung1;Um' +
        'satz Auswertung;129;BERICHT'
      
        'EditWATourForm;TPanel;TourDatenPanel;EditWATourForm.TourDatenPan' +
        'el;Tourdaten '#228'ndern;130;'
      
        'LVSForm;TMenuItem;BasisStammdaten1;LVSForm.BasisStammdaten1;Basi' +
        's-Stammdaten;131;STAMMDATEN'
      
        'LVSForm;TTabSheet;QualTabSheet;LVSForm.QualTabSheet;Qualit'#228'tssic' +
        'herung;132;QS'
      
        'ArtikelForm;TButton;AddLBButton;ArtikelForm.AddLBButton;ArtikelS' +
        'tamm:Lagerbereich hinzuf'#252'gen...;133;ARTIKEL'
      
        'ArtikelForm;TButton;DelLBButton;ArtikelForm.DelLBButton;ArtikelS' +
        'tamm:Lagerbereich l'#246'schen;134;ARTIKEL'
      
        'LVSForm;TTabSheet;WEAvisTabSheet;LVSForm.WEAvisTabSheet;Avisiert' +
        'e Sendungen;135;WE'
      
        'LVSForm;TTabSheet;WATourTabSheet;LVSForm.WATourTabSheet;Tourenpl' +
        'an;136;WA'
      
        'LVSForm;TMenuItem;Auftrgenach13001;LVSForm.Auftrgenach13001;Auft' +
        'r'#228'ge nach Auftragsschluss...;137;BERICHT'
      
        'LVSForm;TMenuItem;AuswertungNachkommissionierung1;LVSForm.Auswer' +
        'tungNachkommissionierung1;Auswertung Nachkommissionierung...;138' +
        ';BERICHT'
      
        'LVSForm;TMenuItem;Wareneingangsstatistik1;LVSForm.Wareneingangss' +
        'tatistik1;Warenannahme Auswertung...;139;BERICHT'
      
        'LVSForm;TMenuItem;WarenBesQSMenuItem;LVSForm.WarenBesQSMenuItem;' +
        'Qualit'#228'tspr'#252'fung anfordern...;140;QS'
      
        'LVSForm;TMenuItem;OverhangListMenuItem;LVSForm.OverhangListMenuI' +
        'tem;'#220'berhangliste;141;MHD'
      
        'DispAufKommPosForm;TMenuItem;ChangeKommPosMenuItem;DispAufKommPo' +
        'sForm.ChangeKommPosMenuItem;Abgeschlossene Position '#228'ndern;142;A' +
        'UFTRAG'
      
        'LVSForm;TMenuItem;WAChangeAufTourMenuItem;LVSForm.WAChangeAufTou' +
        'rMenuItem;Tour im Auftrag '#228'ndern;143;AUFTRAG'
      
        'LVSForm;TMenuItem;WACloseAuslieferungMenuItem;LVSForm.WACloseAus' +
        'lieferungMenuItem;Auslieferung abschlie'#223'en;144;WA'
      ';ACO;INVZ;.INVZ;Zwischeninventur anlegen;145;INV'
      ';ACO;INVBEST;.INVBEST;Inventur aus dem Bestand anlegen;146;INV'
      ';ACO;INVV;.INVV;Vollinventur im gesamten Lager;147;INV'
      
        'ArtikelForm;TTabSheet;QSTabSheet;ArtikelForm.QSTabSheet;ArtikelS' +
        'tamm:Qualit'#228'tskontrolle;148;ARTIKEL'
      
        'LVSForm;TMenuItem;AufChangeArtikelMenuItem;LVSForm.AufChangeArti' +
        'kelMenuItem;Ersatzartikel ausw'#228'hlen;149;AUFTRAG'
      
        'WarenempfForm;TButton;DeleteButton;WarenempfForm.DeleteButton;L'#246 +
        'schen;150;'
      
        'WarenempfForm;TButton;EditButton;WarenempfForm.EditButton;Bearbe' +
        'iten;151;'
      
        'WarenempfForm;TButton;NewButton;WarenempfForm.NewButton;Neue;152' +
        ';'
      
        'LVSForm;TMenuItem;AufChangeTourMenuItem;LVSForm.AufChangeTourMen' +
        'uItem;Tour in Auftr'#228'gen '#228'ndern;153;AUFTRAG'
      
        'LVSForm;TMenuItem;PackChangeTourMenuItem;LVSForm.PackChangeTourM' +
        'enuItem;Tour in Packlisten '#228'ndern;154;AUFTRAG'
      
        'LVSForm;TMenuItem;WarenBesDoQSMenuItem;LVSForm.WarenBesDoQSMenuI' +
        'tem;Qualit'#228'tspr'#252'fung anfordern;155;QS'
      
        'LVSForm;TMenuItem;WarenBesShowQSMenuItem;LVSForm.WarenBesShowQSM' +
        'enuItem;QS-Protokolle;156;QS'
      
        'LVSForm;TMenuItem;AufCreateBatchMenuItem;LVSForm.AufCreateBatchM' +
        'enuItem;Auftrag Batchlauf anlegen;157;BATCH'
      
        'LVSForm;TMenuItem;PackCreateBatchMenuItem;LVSForm.PackCreateBatc' +
        'hMenuItem;Packauftrag Batchlauf anlegen...;158;BATCH'
      
        'LVSForm;TMenuItem;AufCreateSammelMenuItem;LVSForm.AufCreateSamme' +
        'lMenuItem;Auftrag Sammellauf anlegen;159;BATCH'
      
        'LVSForm;TTabSheet;LagerNachschubTabSheet;LVSForm.LagerNachschubT' +
        'abSheet;Lager Nachschub;160;NACHSCHUB'
      
        'LVSForm;TMenuItem;BerichtWALeergut;LVSForm.BerichtWALeergut;Beri' +
        'cht WA Leergut;161;LEERGUT'
      
        'LVSForm;TMenuItem;BerichtWELeergut;LVSForm.BerichtWELeergut;Beri' +
        'cht WE Leergut;162;LEERGUT'
      
        'LVSForm;TButton;PrintManPickButton;LVSForm.PrintManPickButton;Pi' +
        'ckliste drucken;163;'
      
        'LVSForm;TMenuItem;AufDeleteMenuItem;LVSForm.AufDeleteMenuItem;Au' +
        'ftrag l'#246'schen;164;AUFTRAG'
      
        'LVSForm;TMenuItem;AufChangePrioMenuItem;LVSForm.AufChangePrioMen' +
        'uItem;Auftragpriorit'#228't '#228'ndern;165;AUFTRAG'
      
        'LVSForm;TMenuItem;AufCreateAufMenuItem;LVSForm.AufCreateAufMenuI' +
        'tem;Neuer Auftrag;166;AUFTRAG'
      
        'LVSForm;TMenuItem;AufEditAufMenuItem;LVSForm.AufEditAufMenuItem;' +
        'Auftrag bearbeiten;167;AUFTRAG'
      
        'LVSForm;TMenuItem;BerichtSpeditionsVoranmeldung;LVSForm.BerichtS' +
        'peditionsVoranmeldung;Speditions Voranmeldung;168;SPEDITION'
      
        'LVSForm;TMenuItem;ProAnsprechpartner1;LVSForm.ProAnsprechpartner' +
        '1;Pro Ansprechpartner;169;BERICHT'
      
        'LVSForm;TMenuItem;AufVorResAnMenuItem;LVSForm.AufVorResAnMenuIte' +
        'm;Vorreservierung anlegen;170;VORRES'
      
        'LVSForm;TMenuItem;AufVorResDelMenuItem;LVSForm.AufVorResDelMenuI' +
        'tem;Vorreservierung l'#246'schen;171;VORRES'
      
        'LVSForm;TMenuItem;PackplatzMainMenuItem;LVSForm.PackplatzMainMen' +
        'uItem;Verpackungspl'#228'tze verwalten;172;PACK'
      
        'SpeditionsAuftrag;ACO;ChangeBestand;SpeditionsAuftrag.ChangeBest' +
        'and;Bestand '#228'ndern;173;'
      
        'SpeditionsAuftrag;ACO;ChangeArtikel;SpeditionsAuftrag.ChangeArti' +
        'kel;Artikel '#228'ndern;174;'
      
        'SpeditionsAuftrag;ACO;DeleteArtikel;SpeditionsAuftrag.DeleteArti' +
        'kel;Artikel l'#246'schen;175;'
      
        'SpeditionsAuftrag;ACO;ChangeArtikelKomm;SpeditionsAuftrag.Change' +
        'ArtikelKomm;Komm-Daten der Artikel '#228'ndern;176;'
      
        'SpeditionsAuftrag;ACO;ChangeKommUser;SpeditionsAuftrag.ChangeKom' +
        'mUser;Darf Komm-User '#228'ndern;177;'
      
        'SpeditionsAuftrag;ACO;ChangeAuftrag;SpeditionsAuftrag.ChangeAuft' +
        'rag;Auftrag '#228'ndern;178;'
      
        'SpeditionsAuftrag;ACO;DatenExport;SpeditionsAuftrag.DatenExport;' +
        'Daten exportieren;179;'
      
        'SpeditionsAuftrag;ACO;SpeditionsAuftrag;SpeditionsAuftrag.Spedit' +
        'ionsAuftrag;Speditionsauftr'#228'ge hinzuf'#252'gen und verwalten;183;'
      
        ';ACO;Waren-Beschaffung;.Waren-Beschaffung;Funktionen f'#252'r die Bes' +
        'chaffung der Ware;184;BESCHAFFUNG'
      
        'LVSForm;TMenuItem;AufImportMenuItem;LVSForm.AufImportMenuItem;Au' +
        'ftragsdaten importieren;185;IMPORT'
      
        'LVSForm;TMenuItem;Depotsverwalten1;LVSForm.Depotsverwalten1;Depo' +
        'ts verwalten;186;DEPOT'
      
        'ArtikelForm;TMenuItem;PrintEAN128MenuItem;ArtikelForm.PrintEAN12' +
        '8MenuItem;ArtikelStamm:EAN128 drucken...;187;ARTIKEL'
      
        'ArtikelForm;TButton;PrintEAN128Button;ArtikelForm.PrintEAN128But' +
        'ton;ArtikelStamm:EAN128 drucken...;188;ARTIKEL'
      
        'BestellVereinnahmungForm;TMenuItem;WEPosPrintEAN128MenuItem;Best' +
        'ellVereinnahmungForm.WEPosPrintEAN128MenuItem;EAN128-Etiketten d' +
        'rucken...;189;WE'
      
        'LVSForm;TMenuItem;WEPosPrintEAN128MenuItem;LVSForm.WEPosPrintEAN' +
        '128MenuItem;EAN128-Etiketten drucken;190;DRUCKEN'
      
        'LVSForm;TMenuItem;WEPosPrintEAN13MenuItem;LVSForm.WEPosPrintEAN1' +
        '3MenuItem;EAN13-Etiketten drucken;191;DRUCKEN'
      
        'LVSForm;TMenuItem;BesPrintEAN128MenuItem;LVSForm.BesPrintEAN128M' +
        'enuItem;EAN128 drucken;192;DRUCKEN'
      
        'LVSForm;TMenuItem;BesPrintEAN13MenuItem;LVSForm.BesPrintEAN13Men' +
        'uItem;EAN13 Drucken;193;DRUCKEN'
      
        'BestellVereinnahmungForm;TMenuItem;WEPosPrintEAN13MenuItem;Beste' +
        'llVereinnahmungForm.WEPosPrintEAN13MenuItem;EAN13-Etiketten druc' +
        'ken;194;WE'
      
        'LVSForm;TMenuItem;LagerLEEinlagernMenuItem;LVSForm.LagerLEEinlag' +
        'ernMenuItem;Ladungstr'#228'ger einlagern;195;LE'
      
        'LVSForm;TMenuItem;LagerLEUmbuchenMenuItem;LVSForm.LagerLEUmbuche' +
        'nMenuItem;Ladungstr'#228'ger umbuchen;196;LE'
      
        'LVSForm;TMenuItem;LagerLEWEAvisMenuItem;LVSForm.LagerLEWEAvisMen' +
        'uItem;WE-Avis senden;197;WE'
      
        'LVSForm;TMenuItem;LagerLEDelPlanungMenuItem;LVSForm.LagerLEDelPl' +
        'anungMenuItem;Einlagerplanung l'#246'schen;198;EINLAGER'
      
        'LVSForm;TButton;BesAufLPButton;LVSForm.BesAufLPButton;Auf LP buc' +
        'hen;199;BESTAND'
      
        'LVSForm;TMenuItem;AufPosAddIsMengeMenuItem;LVSForm.AufPosAddIsMe' +
        'ngeMenuItem;Istmenge erh'#246'hen;200;AUFTRAG'
      
        'LVSForm;TMenuItem;BatchEditKommPosKommInfoMenuItem;LVSForm.Batch' +
        'EditKommPosKommInfoMenuItem;Kommissionierposition bearbeiten (Ba' +
        'tch);201;BATCH'
      
        'LVSForm;TMenuItem;WELoeschen;LVSForm.WELoeschen;WE L'#246'schen;202;W' +
        'E'
      
        'LVSForm;TMenuItem;InvPosAddArtikelMenuItem;LVSForm.InvPosAddArti' +
        'kelMenuItem;Inventur:Artikel hinzuf'#252'gen;203;INV'
      
        'LVSForm;TMenuItem;AufPopupdRelationMenuItem;LVSForm.AufPopupdRel' +
        'ationMenuItem;Relation...;204;RELATION'
      
        'LVSForm;TMenuItem;LagerRelationenMenuItem;LVSForm.LagerRelatione' +
        'nMenuItem;Relationen verwalten...;205;RELATION'
      
        'LVSForm;TButton;WAFrachtPrtButton;LVSForm.WAFrachtPrtButton;Frac' +
        'htbrief drucken;206;WA'
      
        'LVSForm;TMenuItem;AufEditAddInfosMenuItem;LVSForm.AufEditAddInfo' +
        'sMenuItem;Auftrag: Zusatzinfos erfassen;207;AUFTRAG'
      
        ';ACO;MultiSelectBestand;.MultiSelectBestand;MultiSelect beim Bes' +
        'tand;208;BESTAND'
      
        'LVSForm;TMenuItem;BerichtBesExcelExportMenuItem;LVSForm.BerichtB' +
        'esExcelExportMenuItem;Bericht: Bestand Export f'#252'r Excel;209;BERI' +
        'CHT'
      
        'LVSForm;TMenuItem;BerichtABCAnalyse;LVSForm.BerichtABCAnalyse;Be' +
        'richt:ABC-Analyse;210;BERICHT'
      
        'LVSForm;TMenuItem;Kommissionierung1;LVSForm.Kommissionierung1;Be' +
        'richt:Kommissionierung;211;BERICHT'
      
        'LVSForm;TMenuItem;UmschlagAnalyse1;LVSForm.UmschlagAnalyse1;Beri' +
        'cht:Umschlag Analyse;212;BERICHT'
      
        'BaseStammdatenForm;TTabSheet;LHMTabSheet;BaseStammdatenForm.LHMT' +
        'abSheet;Ladungstr'#228'ger;213;'
      
        'BaseStammdatenForm;TTabSheet;TextTabSheet;BaseStammdatenForm.Tex' +
        'tTabSheet;Texte;214;'
      
        'LVSForm;TMenuItem;WAPrintAllEAN128MenuItem;LVSForm.WAPrintAllEAN' +
        '128MenuItem;Alle EAN128 Etiketten im WA drucken;215;DRUCKEN'
      
        'LVSForm;TMenuItem;WAPrintAllEAN13MenuItem;LVSForm.WAPrintAllEAN1' +
        '3MenuItem;Alle EAN13 Etiketten im WA drucken...;216;DRUCKEN'
      
        'LVSForm;TMenuItem;AufPosPrintEAN128MenuItem;LVSForm.AufPosPrintE' +
        'AN128MenuItem;EAN128 Etiketten f'#252'r AufPos drucken;217;DRUCKEN'
      
        'LVSForm;TMenuItem;AufPosPrintEAN13MenuItem;LVSForm.AufPosPrintEA' +
        'N13MenuItem;EAN13 Etiketten f'#252'r Auf-Pos drucken;218;DRUCKEN'
      
        'LVSForm;TMenuItem;WAReprintEAN128MenuItem;LVSForm.WAReprintEAN12' +
        '8MenuItem;EAN128 Umetikettieren im WA;219;DRUCKEN'
      
        'LVSForm;TMenuItem;AufEditAddLiefPosMenuItem;LVSForm.AufEditAddLi' +
        'efPosMenuItem;Auftrag:Lieferposition erfassen;220;AUFTRAG'
      
        'LVSForm;TTabSheet;VorplanTabSheet;LVSForm.VorplanTabSheet;Vorpla' +
        'nungen;221;'
      
        'LVSForm;TMenuItem;KommPrintPickPlanMenuItem;LVSForm.KommPrintPic' +
        'kPlanMenuItem;Komm-Pickplan drucken;222;KOMM'
      
        'LVSForm;TTabSheet;LifeTabSheet;LVSForm.LifeTabSheet;Inventar;223' +
        ';LIFE'
      
        'LVSForm;TButton;CreateLTWeButton;LVSForm.CreateLTWeButton;Neue L' +
        'T Annahme;224;WE'
      
        'LVSForm;TMenuItem;AufChangeSpedMenuItem;LVSForm.AufChangeSpedMen' +
        'uItem;Spedition in Auftr'#228'gen '#228'ndern;225;AUFTRAG'
      
        'LVSForm;TMenuItem;KonfigurationKomm;LVSForm.KonfigurationKomm;La' +
        'ger Kommmissionierplanung;226;SETUP'
      
        'LVSForm;TMenuItem;PickToLightWartungMenuItem;LVSForm.PickToLight' +
        'WartungMenuItem;Pick To Light;227;LAGER'
      
        'LVSForm;TMenuItem;PrintDocIDLabelMenuItem;LVSForm.PrintDocIDLabe' +
        'lMenuItem;Doc-ID Barcodes drucken;228;DRUCKEN'
      
        'LVSForm;TMenuItem;PrintGebindeLabelMenuItem;LVSForm.PrintGebinde' +
        'LabelMenuItem;Gebindebarcodes drucken;229;DRUCKEN'
      
        'LVSForm;TMenuItem;DruckerundFormulareMenuItem;LVSForm.Druckerund' +
        'FormulareMenuItem;Drucker und Formulare;230;DRUCKEN'
      
        'LVSForm;TTabSheet;WAVerpackTabSheet;LVSForm.WAVerpackTabSheet;WA' +
        '-Verpacken;231;PACK'
      
        'LVSForm;TMenuItem;WEPrintRampenBelegMenuItem;LVSForm.WEPrintRamp' +
        'enBelegMenuItem;Rampen-Scheine nachdrucken;232;DRUCKEN'
      
        'LVSForm;TButton;WAPackNewBatchButton;LVSForm.WAPackNewBatchButto' +
        'n;WA:Neuer Batch w'#228'hlen;233;PACK'
      ';ACO;WEOverDelivery;.WEOverDelivery;WE-'#220'berlieferung;234;WE'
      
        'LVSForm;TMenuItem;LagerLECheckInventurMenuItem;LVSForm.LagerLECh' +
        'eckInventurMenuItem;LE-Check-Inventur anlegen;235;INV'
      
        'LVSForm;TMenuItem;LagerLPCheckInventurMenuItem;LVSForm.LagerLPCh' +
        'eckInventurMenuItem;LP-Check-Inventur anlegen;236;INV'
      
        'LVSForm;TMenuItem;WarenBesDoCheckInvMenuItem;LVSForm.WarenBesDoC' +
        'heckInvMenuItem;Bestand Check-Inventur anlegen;237;INV'
      
        'LVSForm;TMenuItem;AufResetAufMenuItem;LVSForm.AufResetAufMenuIte' +
        'm;Auftrag zur'#252'cksetzen;238;AUFTRAG'
      
        'LVSForm;TMenuItem;LagerLEDeleteMenuItem;LVSForm.LagerLEDeleteMen' +
        'uItem;Lager: Ladungstr'#228'ger l'#246'schen;239;LE'
      
        ';ACO;VerpackenChangeSpedition;.VerpackenChangeSpedition;Beim Ver' +
        'packen die Spedition '#228'ndern;240;VERPACKEN'
      
        'LVSForm;TMenuItem;NachschubEinlagernMenuItem;LVSForm.NachschubEi' +
        'nlagernMenuItem;Einlagern...;241;EINLAGER'
      
        'LVSForm;TMenuItem;NachschubResAbMenuItem;LVSForm.NachschubResAbM' +
        'enuItem;Nachschub abstellen...;242;NACHSCHUB'
      
        'LVSForm;TMenuItem;NachschubUpMenuItem;LVSForm.NachschubUpMenuIte' +
        'm;Nachschub aufnehmen...;243;NACHSCHUB'
      
        'LVSForm;TMenuItem;BasisStammdatenMenuItem;LVSForm.BasisStammdate' +
        'nMenuItem;Basis-Stammdaten;244;STAMMDATEN'
      
        'LVSForm;TMenuItem;DruckerWartungMenuItem;LVSForm.DruckerWartungM' +
        'enuItem;Drucker Wartung;245;DRUCKEN'
      
        'LVSForm;TTabSheet;KommLoadTabSheet;LVSForm.KommLoadTabSheet;Bere' +
        'itstellungen;246;KOMM'
      
        'LVSForm;TMenuItem;WARequestBillMenuItem;LVSForm.WARequestBillMen' +
        'uItem;WA:Rechnung anfordern;247;WA'
      
        'LVSForm;TMenuItem;AufStornoMenuItem;LVSForm.AufStornoMenuItem;Au' +
        'ftrag stornieren;248;AUFTRAG'
      
        ';ACO;AddVPEVerpacken;.AddVPEVerpacken;Weitere VPEs beim Verpacke' +
        'n hinzuf'#252'gen;249;VERPACKEN'
      
        'LVSForm;TButton;ReprintNVEButton;LVSForm.ReprintNVEButton;NVE-La' +
        'bel nachdrucken;250;DRUCKEN'
      
        'LVSForm;TButton;PrintNVEButton;LVSForm.PrintNVEButton;Alle NVE-L' +
        'abel drucken;251;DRUCKEN'
      
        'LVSForm;TButton;CloseWEAvisButton;LVSForm.CloseWEAvisButton;Ware' +
        'navis abschliessen;252;WE'
      
        'LVSForm;TButton;AufLeergutButton;LVSForm.AufLeergutButton;Auftra' +
        'g Ladehilfsmittel;253;AUFTRAG'
      
        'LVSForm;TMenuItem;PackplatzUsingMainMenuItem;LVSForm.PackplatzUs' +
        'ingMainMenuItem;Verpackungspl'#228'tze freigeben/sperren;254;PACK'
      
        'LVSForm;TMenuItem;LEPrintInventoryMenuItem;LVSForm.LEPrintInvent' +
        'oryMenuItem;LE-Bestand drucken;255;DRUCKEN'
      
        'LVSForm;TMenuItem;LPPrintInventoryMenuItem;LVSForm.LPPrintInvent' +
        'oryMenuItem;LP-Bestand drucken;256;DRUCKEN'
      
        'LVSForm;TMenuItem;WANVEAddInfosMenuItem;LVSForm.WANVEAddInfosMen' +
        'uItem;NVE Zusatzinfos erfassen;257;WA'
      
        'SpeditionenForm;TMenuItem;RoutingTabelleimportieren1;Speditionen' +
        'Form.RoutingTabelleimportieren1;Routing-Tabelle importieren...;2' +
        '58;SPEDITION'
      
        'LVSForm;TMenuItem;WarenBesCategoryMenuItem;LVSForm.WarenBesCateg' +
        'oryMenuItem;Bestandsqualifikation;259;BESTAND'
      
        'LVSForm;TTabSheet;TourplanungTabSheet;LVSForm.TourplanungTabShee' +
        't;Tourenvorplanung;260;AUFTRAG'
      
        'LVSForm;TTabSheet;RetoureTabSheet;LVSForm.RetoureTabSheet;Retour' +
        'en;261;RETOUREN'
      
        'LVSForm;TButton;RetoureStornoButton;LVSForm.RetoureStornoButton;' +
        'Retoure stornieren;262;RETOUREN'
      
        'LVSForm;TMenuItem;RetourePosStornoMenuItem;LVSForm.RetourePosSto' +
        'rnoMenuItem;Retourenpos. stornieren;263;RETOUREN'
      
        'LVSForm;TTabSheet;AuftragTourTabSheet;LVSForm.AuftragTourTabShee' +
        't;Auftragstouren;264;AUFTRAG'
      
        ';ACO;Leistungsdaten;.Leistungsdaten;Leistungsdaten der Benutzer ' +
        'anzeigen;265;BERICHT'
      
        'LVSForm;TTabSheet;RetoureAvisTabSheet;LVSForm.RetoureAvisTabShee' +
        't;Avisierte Retouren;266;RETOUREN'
      
        'LVSForm;TMenuItem;WEPrintDeliveryProtocolMenuItem;LVSForm.WEPrin' +
        'tDeliveryProtocolMenuItem;Anlieferprotokoll nachdrucken;267;WE'
      
        'LVSForm;TMenuItem;AufBatchClearingMenuItem;LVSForm.AufBatchClear' +
        'ingMenuItem;Zum Kl'#228'rfallplatz verschieben;268;AUFTRAG'
      
        'LVSForm;TButton;AufTourPlanenButton;LVSForm.AufTourPlanenButton;' +
        'Neue Touren planen;269;AUFTRAG'
      
        'LVSForm;TMenuItem;WECreateFollowMenuItem;LVSForm.WECreateFollowM' +
        'enuItem;WE: Folgenahme anlegen;270;WE'
      
        'RetoureVereinnahmungForm;TMenuItem;AddRetArtikelMenuItem;Retoure' +
        'VereinnahmungForm.AddRetArtikelMenuItem;Retoure: Neuer Artikel;2' +
        '71;RETOUREN'
      
        'RetoureVereinnahmungForm;TMenuItem;RejectRetArtikelMenuItem;Reto' +
        'ureVereinnahmungForm.RejectRetArtikelMenuItem;Retoure: Annahme v' +
        'erweigern;272;RETOUREN'
      
        'LVSForm;TMenuItem;WANVEAddToMasterMenuItem;LVSForm.WANVEAddToMas' +
        'terMenuItem;Zur Master-NVE hinzuf'#252'gen;273;WA'
      
        'BaseStammdatenForm;TTabSheet;RetZustandTabSheet;BaseStammdatenFo' +
        'rm.RetZustandTabSheet;Retouren Zustand;274;'
      ';ACO;RetourenAdmin;.RetourenAdmin;RetourenAdmin;275;RETOURE'
      
        'LVSForm;TTabSheet;LageriPunktTabSheet;LVSForm.LageriPunktTabShee' +
        't;Lager: iPunkt;276;LAGER'
      
        'LVSForm;TTabSheet;RetoureLETabSheet;LVSForm.RetoureLETabSheet;Re' +
        'toure: LEs;277;RETOUREN'
      
        'LVSForm;TMenuItem;AufMoveClearingMenuItem;LVSForm.AufMoveClearin' +
        'gMenuItem;Auftrag: Zum Packplatz f'#252'r Kl'#228'rf'#228'lle verschieben;278;A' +
        'UFTRAG'
      
        'LVSForm;TMenuItem;AufVerpackenMenuItem;LVSForm.AufVerpackenMenuI' +
        'tem;Auftrag: Verpacken;279;PACK'
      
        'LVSForm;TButton;CreateRetoureButton;LVSForm.CreateRetoureButton;' +
        'Retoure: Retoure anlegen;280;RETOUREN'
      
        'LVSForm;TButton;RetoureAvisCloseButton;LVSForm.RetoureAvisCloseB' +
        'utton;Retoure: Retourenavis abschlie'#223'en;281;RETOUREN'
      ';ACO;INVANG;.INVANG;Best'#228'nde anlegen;282;BESTAND'
      
        'LVSForm;TMenuItem;RetoureLEAusbuchenMenuItem;LVSForm.RetoureLEAu' +
        'sbuchenMenuItem;Retouren: LE ausbuchen;283;RETOUREN'
      
        'LVSForm;TMenuItem;RetoureLEUmbuchenMenuItem;LVSForm.RetoureLEUmb' +
        'uchenMenuItem;Retouren: LE umbuchen;284;RETOUREN'
      
        'LVSForm;TTabSheet;RetoureBestandTabSheet;LVSForm.RetoureBestandT' +
        'abSheet;Retouren: Retouenbest'#228'nde;285;RETOUREN'
      
        'BaseStammdatenForm;TTabSheet;BesCategoryTabSheet;BaseStammdatenF' +
        'orm.BesCategoryTabSheet;Folgeprozesse;286;'
      
        'LVSForm;TButton;AufFreiButton;LVSForm.AufFreiButton;Auftrag: Auf' +
        'trag freigeben;287;AUFTRAG'
      
        'LVSForm;TButton;AufCloseButton;LVSForm.AufCloseButton;Auftrag: A' +
        'uftrag abschlie'#223'en;288;AUFTRAG'
      
        'LVSForm;TButton;BatchFreiButton;LVSForm.BatchFreiButton;Batch: A' +
        'uftr'#228'ge freigeben;289;BATCH'
      
        'LVSForm;TButton;BatchPlanButton;LVSForm.BatchPlanButton;Batch: N' +
        #228'chste Batch planen;290;BATCH'
      
        'LVSForm;TMenuItem;AufAufPosVorResMenuItem;LVSForm.AufAufPosVorRe' +
        'sMenuItem;Auftragpos: Vorreservierung anlegen;291;VORRES'
      
        'ArtikelForm;TMenuItem;EditLocEinheitMenuItem;ArtikelForm.EditLoc' +
        'EinheitMenuItem;ArtikelStamm:Niederlassungsbezoge Daten;292;ARTI' +
        'KEL'
      
        ';ACO;WEChangeArtikelOpt;.WEChangeArtikelOpt;Artikel-OPT im WE '#228'n' +
        'dern;293;WE'
      
        'BestellVereinnahmungForm;TMenuItem;BesBulkMenuItem;BestellVerein' +
        'nahmungForm.BesBulkMenuItem;WE-Annahme:Bulk Annahme;294;WE'
      
        ';ACO;RetOverDelivery;.RetOverDelivery;Retouren '#220'berlieferung zul' +
        #228'ssig;295;RETOURE'
      
        'LVSForm;TMenuItem;BerichtLPAlleArtikelMenuItem;LVSForm.BerichtLP' +
        'AlleArtikelMenuItem;Bericht: Alle Artikel mit Komm.-Pl'#228'tze;296;B' +
        'ERICHT'
      
        'LVSForm;TMenuItem;BerichtLPAusgelisteteArtikelMenuItem;LVSForm.B' +
        'erichtLPAusgelisteteArtikelMenuItem;Bericht: Ausgelistete Artike' +
        'l mit Komm.-Pl'#228'tze;297;BERICHT'
      
        'LVSForm;TMenuItem;AufVorResDoMenuItem;LVSForm.AufVorResDoMenuIte' +
        'm;Vorreservierung anlegen;298;VORRES'
      
        'LVSForm;TMenuItem;VersandDashboardMenuItem;LVSForm.VersandDashbo' +
        'ardMenuItem;Dashboard Versand;299;DASHBOARD'
      
        'LVSForm;TMenuItem;BestBestEditInfosMenuItem;LVSForm.BestBestEdit' +
        'InfosMenuItem;Bestellung Zusatzinfos erfassen...;300;BESTELL'
      
        'LVSForm;TMenuItem;BerichtAuswertungInventurMenuItem;LVSForm.Beri' +
        'chtAuswertungInventurMenuItem;Bericht: Inventur-Auswertung;301;B' +
        'ERICHT'
      
        'LVSForm;TMenuItem;BerichtAuswertungTourMenuItem;LVSForm.BerichtA' +
        'uswertungTourMenuItem;Bericht: Tour Auswertung;302;BERICHT'
      
        'LVSForm;TMenuItem;BerichtWAStatistikMenuItem;LVSForm.BerichtWASt' +
        'atistikMenuItem;Bericht: Warenannahme Auswertung;303;BERICHT'
      
        'LVSForm;TMenuItem;KonfigurationAuftrag;LVSForm.KonfigurationAuft' +
        'rag;Konfiguration der Auftragsabwicklung;304;AUFTRAG'
      
        'LVSForm;TMenuItem;WANVEChangeGewichtMenuItem;LVSForm.WANVEChange' +
        'GewichtMenuItem;NVE Gewicht korrigieren;305;WA'
      
        'LVSForm;TMenuItem;WANVEAddVerladungMenuItem;LVSForm.WANVEAddVerl' +
        'adungMenuItem;NVE zur Verladung hinzuf'#252'gen;306;VERLADEN'
      
        'LVSForm;TMenuItem;NachschubBesAbMenuItem;LVSForm.NachschubBesAbM' +
        'enuItem;Nachschub Bestand abstellen;307;NACHSCHUB'
      
        'BestellVereinnahmungForm;TMenuItem;DelBestPosErfassenMenuItem;Be' +
        'stellVereinnahmungForm.DelBestPosErfassenMenuItem;WE-Erfassen: P' +
        'os l'#246'schen;308;WE'
      
        'LVSForm;TMenuItem;WEPosLoeschen;LVSForm.WEPosLoeschen;WE-Positio' +
        'n l'#246'schen;309;WE'
      
        'LVSForm;TMenuItem;WACreateWAMenuItem;LVSForm.WACreateWAMenuItem;' +
        'Manueller Versand anlegen;310;WA'
      
        'LVSForm;TMenuItem;WANVEChangeLTMenuItem;LVSForm.WANVEChangeLTMen' +
        'uItem;Ladungstr'#228'ger '#228'ndern;311;NVE'
      
        'LVSForm;TMenuItem;LagerBesPrintEAN128MenuItem;LVSForm.LagerBesPr' +
        'intEAN128MenuItem;EAN128 Etiketten drucken;312;DRUCKEN'
      
        'LVSForm;TMenuItem;LagerBesPrintEAN13MenuItem;LVSForm.LagerBesPri' +
        'ntEAN13MenuItem;EAN13 Etiketten drucken;313;DRUCKEN'
      
        'LVSForm;TMenuItem;AufResetAufPosMenuItem;LVSForm.AufResetAufPosM' +
        'enuItem;Auftragposition zur'#252'cksetzen;314;AUFTRAG'
      
        'LVSForm;TMenuItem;ArtikelEditGroupMenuItem;LVSForm.ArtikelEditGr' +
        'oupMenuItem;Artikelgruppen bearbeiten;315;ARTIKEL'
      
        'LVSForm;TMenuItem;WACreateMultiNVEMenuItem;LVSForm.WACreateMulti' +
        'NVEMenuItem;Mehrfach-NVE erzeugen;316;WA'
      
        'LVSForm;TMenuItem;LagerDashboardMenuItem;LVSForm.LagerDashboardM' +
        'enuItem;Lager Dashboard;317;DASHBOARD'
      
        'LVSForm;TMenuItem;LagerPlanBackTransportMenuItem;LVSForm.LagerPl' +
        'anBackTransportMenuItem;R'#252'cklagerungen in den Vorrat planen;318;' +
        'BACK'
      
        'LVSForm;TMenuItem;BestPosResetChargeMenuItem;LVSForm.BestPosRese' +
        'tChargeMenuItem;Vorbelegte Chargennr. zur'#252'cksetzen;319;BESTELL'
      
        'LVSForm;TMenuItem;BestBestStornoMenuItem;LVSForm.BestBestStornoM' +
        'enuItem;Bestellung stornieren;320;BEST'
      
        'SpeditionenForm;TMenuItem;SpedExportSendungMenuItem;SpeditionenF' +
        'orm.SpedExportSendungMenuItem;Aktuelle Sendungsdaten exportieren' +
        '...;321;SPEDITION'
      
        'LVSForm;TMenuItem;BatchReleaseStationMenuItem;LVSForm.BatchRelea' +
        'seStationMenuItem;Batch von der Verteilstation l'#246'sen;322;BATCH'
      
        'LVSForm;TMenuItem;WANVERemoveMasterMenuItem;LVSForm.WANVERemoveM' +
        'asterMenuItem;Von der Master-NVE l'#246'sen;323;WA'
      
        'LVSForm;TMenuItem;VerladungSecondMenuItem;LVSForm.VerladungSecon' +
        'dMenuItem;Zweit-Verladung anlegen;324;VERLADEN'
      
        'LVSForm;TMenuItem;KommPickStationMenuItem;LVSForm.KommPickStatio' +
        'nMenuItem;Picken;325;KOMM'
      
        'LVSForm;TMenuItem;ChangeLagerBesMenuItem;LVSForm.ChangeLagerBesM' +
        'enuItem;Bestand '#228'ndern...;326;BESTAND'
      
        'LVSForm;TMenuItem;ChangeLEBesMenuItem;LVSForm.ChangeLEBesMenuIte' +
        'm;Bestand '#228'ndern (LE);327;BESTAND'
      
        'LVSForm;TMenuItem;ChangeLELPBesMenuItem;LVSForm.ChangeLELPBesMen' +
        'uItem;Bestand '#228'ndern (LELP);328;BESTAND'
      
        'LVSForm;TMenuItem;ChangeLPBesMenuItem;LVSForm.ChangeLPBesMenuIte' +
        'm;Bestand '#228'ndern (LP);329;BESTAND'
      
        'LVSForm;TMenuItem;VerlClearingNVEItem;LVSForm.VerlClearingNVEIte' +
        'm;Auf Kl'#228'rfall-Verladung umbuchen;330;VERLADEN'
      
        'LVSForm;TMenuItem;BestChangeMenuItem;LVSForm.BestChangeMenuItem;' +
        'Warenerwartung bearbeiten;331;BESTELL'
      
        'LVSForm;TMenuItem;BestCreateMenuItem;LVSForm.BestCreateMenuItem;' +
        'Neue Warenerwartung;332;BESTELL'
      
        'LVSForm;TMenuItem;RetoureAvisTransferMenuItem;LVSForm.RetoureAvi' +
        'sTransferMenuItem;Ret Avis in ein anderes Lager umbuchen;333;RET' +
        'OUREN'
      
        'LVSForm;TMenuItem;WarenBesNachschubMenuItem;LVSForm.WarenBesNach' +
        'schubMenuItem;Nachschub anlegen;334;NACHSCHUB'
      
        'LVSForm;TMenuItem;AufChangeDeliveryDateMenuItem;LVSForm.AufChang' +
        'eDeliveryDateMenuItem;Versanddatum '#228'ndern;335;AUFTRAG'
      
        'LVSForm;TMenuItem;AufCopyAufMenuItem;LVSForm.AufCopyAufMenuItem;' +
        'Auftrag kopieren;336;AUFTRAG'
      
        'LVSForm;TMenuItem;WePosBearbeiten;LVSForm.WePosBearbeiten;WE-Pos' +
        'ition bearbeiten;337;WE'
      
        'LVSForm;TMenuItem;AufChangeRelMenuItem;LVSForm.AufChangeRelMenuI' +
        'tem;Verladerelation '#228'ndern;338;AUFTRAG'
      
        'LVSForm;TMenuItem;AufCreateTourMenuItem;LVSForm.AufCreateTourMen' +
        'uItem;Ausliefertour anlegen;339;AUFTRAG'
      
        ';ACO;EditArtikelGroup;.EditArtikelGroup;Artikelgruppen ver'#228'ndern' +
        ';340;ARTIKEL'
      
        'LVSForm;TMenuItem;ZollAbwicklungMenuItem;LVSForm.ZollAbwicklungM' +
        'enuItem;Zollabwicklung;341;ZOLL'
      
        'LVSForm;TMenuItem;KommPrintCustomerLabelMenuItem;LVSForm.KommPri' +
        'ntCustomerLabelMenuItem;Kundenspezifische Artikeletiketten druck' +
        'en;342;DRUCKEN'
      
        'LVSForm;TMenuItem;BerichtWELSLiefMenuItem;LVSForm.BerichtWELSLie' +
        'fMenuItem;Nach Lieferant und Lieferschein;343;BERICHT'
      
        'LVSForm;TMenuItem;AufReopenMenuItem;LVSForm.AufReopenMenuItem;Au' +
        'ftrag neu '#246'ffnen;344;AUFTRAG'
      
        'LVSForm;TMenuItem;LagerNVEDeleteMenuItem;LVSForm.LagerNVEDeleteM' +
        'enuItem;NVE l'#246'schen;345;NVE'
      
        'LVSForm;TMenuItem;WarenBesChargeAEMenuItem;LVSForm.WarenBesCharg' +
        'eAEMenuItem;Artikel eines Bestandes '#228'ndern;346;BESTAND'
      
        'CreateNachschubForm;TTabSheet;ListTabSheet;CreateNachschubForm.L' +
        'istTabSheet;Nachschub Listen importieren;347;NACHSCHUB'
      
        'LVSForm;TButton;WALeergutButton;LVSForm.WALeergutButton;WA Ladeh' +
        'ilfsmittel;348;LEERGUT'
      
        'LVSForm;TMenuItem;LagerBesChangeAEMenuItem;LVSForm.LagerBesChang' +
        'eAEMenuItem;Artikel '#228'ndern...;349;BESTAND'
      
        'SpeditionenForm;TMenuItem;SpedForcastMenuItem;SpeditionenForm.Sp' +
        'edForcastMenuItem;Speditions Voranmeldung erzeugen...;350;SPEDIT' +
        'ION'
      
        'LVSForm;TMenuItem;AufChangeAnlieferDateMenuItem;LVSForm.AufChang' +
        'eAnlieferDateMenuItem;Anlieferdatum '#228'ndern...;351;AUFTRAG'
      
        'LVSForm;TButton;WAPrintAlleNVEButton;LVSForm.WAPrintAlleNVEButto' +
        'n;Alle NVE-Label drucken;353;WA'
      
        'LVSForm;TMenuItem;BatchResetDistributeMenuItem;LVSForm.BatchRese' +
        'tDistributeMenuItem;Verteilung zur'#252'cksetzen;354;BATCH'
      
        'LVSForm;TMenuItem;WAPrintBillMenuItem;LVSForm.WAPrintBillMenuIte' +
        'm;Rechnung nachdrucken;355;WA'
      
        'LVSForm;TMenuItem;KommPosErsatzMenuItem;LVSForm.KommPosErsatzMen' +
        'uItem;Ersatzartikel buchen;356;KOMM'
      
        'LVSForm;TPanel;WANVEBestandPanel;LVSForm.WANVEBestandPanel;Grid ' +
        'f'#252'r NVE Inahlt im WA;357;WA'
      
        'LVSForm;TTabSheet;AuftragSpedTourTabSheet;LVSForm.AuftragSpedTou' +
        'rTabSheet;Sped Touren;358;AUFTRAG'
      
        'LVSForm;TButton;AufSpedTourSammelPlanButton;LVSForm.AufSpedTourS' +
        'ammelPlanButton;Tour gesammelt freigeben;359;AUFTRAG'
      
        'LVSForm;TTabSheet;AufSpedAufPosTabSheet;LVSForm.AufSpedAufPosTab' +
        'Sheet;Auftragspositionen in der Tour;360;AUFTRAG'
      
        'LVSForm;TTabSheet;AufSpedAufTabSheet;LVSForm.AufSpedAufTabSheet;' +
        'Auftr'#228'ge in der Tour;361;AUFTRAG'
      
        'LVSForm;TMenuItem;AufSpedTourCloseMenuItem;LVSForm.AufSpedTourCl' +
        'oseMenuItem;Tour vorzeitig abschlie'#223'en;362;AUFTRAG'
      
        'LVSForm;TMenuItem;KommPosSerialMenuItem;LVSForm.KommPosSerialMen' +
        'uItem;Seriennummer erfassen;363;KOMM'
      
        'LVSForm;TMenuItem;KommPosPrintNVEMenuItem;LVSForm.KommPosPrintNV' +
        'EMenuItem;NVE nachdrucken;364;KOMM'
      
        'LVSForm;TButton;AufKommNVEPrtButton;LVSForm.AufKommNVEPrtButton;' +
        'Alle NVE drucken;366;KOMM'
      
        'LVSForm;TMenuItem;KommPlanGroupMenuItem;LVSForm.KommPlanGroupMen' +
        'uItem;Kommissionier Gruppen;367;KOMM'
      
        'LVSForm;TButton;AufSpedTourPlanButton;LVSForm.AufSpedTourPlanBut' +
        'ton;Tour einzeln freigeben;368;AUFTRAG'
      
        'ArtikelForm;TMenuItem;VorhalteMengenMenuItem;ArtikelForm.Vorhalt' +
        'eMengenMenuItem;Vorhaltemengen besetzen;369;ARTIKEL'
      
        'LVSForm;TMenuItem;AufChangeShippingUnitsMenuItem;LVSForm.AufChan' +
        'geShippingUnitsMenuItem;Anzahl Versandeinheiten '#228'ndern;371;AUFTR' +
        'AG'
      
        'LVSForm;TButton;AufPruefButton;LVSForm.AufPruefButton;Bestand pr' +
        #252'fen;372;AUFTRAG'
      
        'LVSForm;TMenuItem;WANVEVerladungObsoletMenuItem;LVSForm.WANVEVer' +
        'ladungObsoletMenuItem;Weitere Verladung unterbinden;373;VERLADEN'
      
        'LVSForm;TMenuItem;KommPosChangeMengeMenuItem;LVSForm.KommPosChan' +
        'geMengeMenuItem;Menge '#228'ndern;374;KOMM'
      
        'DispAufKommPosForm;TMenuItem;ChangeKommPosMengeMenuItem;DispAufK' +
        'ommPosForm.ChangeKommPosMengeMenuItem;Menge '#228'ndern;375;AUFTRAG'
      
        'DispAufKommPosForm;TMenuItem;AddErsatzMenuItem;DispAufKommPosFor' +
        'm.AddErsatzMenuItem;Ersatzartikel erfassen;376;AUFTRAG'
      
        'LVSForm;TButton;KommPosChangeButton;LVSForm.KommPosChangeButton;' +
        'Position nachbearbeiten;377;KOMM'
      
        ';ACO;ChangePackUser;.ChangePackUser;Packuser '#228'ndern;378;VERPACKE' +
        'N'
      
        'LVSForm;TMenuItem;AufLockMenuItem;LVSForm.AufLockMenuItem;Auftra' +
        'g sperren;379;AUFTRAG'
      
        'LVSForm;TMenuItem;AufUnlockMenuItem;LVSForm.AufUnlockMenuItem;Au' +
        'ftrag entsperren;380;AUFTRAG'
      
        'LVSForm;TMenuItem;WAEmmasBoxMenuItem;LVSForm.WAEmmasBoxMenuItem;' +
        'Emmasbox Fachreservierungen;381;WA'
      
        'LVSForm;TMenuItem;BestandVerrechnungMenuItem;LVSForm.BestandVerr' +
        'echnungMenuItem;Verrechnung von Abverk'#228'ufen;382;BESTAND'
      
        'SpeditionenForm;TButton;DelSpedButton;SpeditionenForm.DelSpedBut' +
        'ton;Spediteur l'#246'schen;383;SPEDITION'
      
        'SpeditionenForm;TButton;EditSpedButton;SpeditionenForm.EditSpedB' +
        'utton;Spediteur bearbeiten;384;SPEDITION'
      
        'SpeditionenForm;TButton;NewSpedButton;SpeditionenForm.NewSpedBut' +
        'ton;Spediteur neu anlegen;385;SPEDITION'
      
        'LVSForm;TMenuItem;WANVEChangeSpedMenuItem;LVSForm.WANVEChangeSpe' +
        'dMenuItem;NVE Spedition '#228'ndern;386;WA'
      
        'LVSForm;TMenuItem;RetoureAvisImportMenuItem;LVSForm.RetoureAvisI' +
        'mportMenuItem;Import Retourenavise;387;RETOUREN'
      
        ';ACO;ShowArticlePrices;.ShowArticlePrices;Preise werden angzeigt' +
        ';388;ARTIKEL'
      
        'LVSForm;TMenuItem;AufChangeAvisMenuItem;LVSForm.AufChangeAvisMen' +
        'uItem;Sendungsavisierung '#228'ndern;389;AUFTRAG'
      
        'LVSForm;TMenuItem;NachPosMengeMenuItem;LVSForm.NachPosMengeMenuI' +
        'tem;Nachschubmenge reduzieren;390;NACHSCHUB'
      
        'LVSForm;TButton;NachschubPrintButton;LVSForm.NachschubPrintButto' +
        'n;Nachschubliste drucken;391;NACHSCHUB'
      
        'LVSForm;TButton;LagerNachBuchenButton;LVSForm.LagerNachBuchenBut' +
        'ton;Nachschub durchbuchen;392;NACHSCHUB'
      
        'LVSForm;TMenuItem;BatchPackMenuItem;LVSForm.BatchPackMenuItem;Ba' +
        'tch vollst'#228'ndig verpacken;393;BATCH'
      
        'LVSForm;TMenuItem;AufAssignPackMenuItem;LVSForm.AufAssignPackMen' +
        'uItem;Auftrag: Packplatz zuweisen;394;PACK'
      
        'LVSForm;TMenuItem;BatchAssignPackMenuItem;LVSForm.BatchAssignPac' +
        'kMenuItem;Batch: Packplatz zuweisen;395;PACK'
      
        'LVSForm;TMenuItem;WAAssignPackMenuItem;LVSForm.WAAssignPackMenuI' +
        'tem;WA: Packplatz zuweisen;396;PACK'
      
        'ArtikelForm;TTabSheet;ErsatzTabSheet;ArtikelForm.ErsatzTabSheet;' +
        'Ersatzartikel;397;ARTIKEL'
      
        'ArtikelForm;TTabSheet;LabelTabSheet;ArtikelForm.LabelTabSheet;Et' +
        'iketten;398;ARTIKEL'
      
        'LVSForm;TButton;MDEQRButton;LVSForm.MDEQRButton;QR-Code f'#252'r MDE ' +
        'Verkn'#252'pfung;399;VERWALTUNG'
      
        'LVSForm;TMenuItem;MDEPuttyMenuItem;LVSForm.MDEPuttyMenuItem;MDE ' +
        #252'ber Putty;400;MDE'
      
        'LVSForm;TButton;AuftragPrintButton;LVSForm.AuftragPrintButton;Au' +
        'ftrag drucken...;401;AUFTRAG'
      
        ';ACO;EAN als Auswahl;.EAN als Auswahl;EAN f'#252'r die Artieklauswahl' +
        ';402;ARTIKEL'
      
        'LVSForm;TButton;RetoureAvisSearchButton;LVSForm.RetoureAvisSearc' +
        'hButton;Retourenavis suchen;403;RETOUREN'
      
        'LVSForm;TButton;RetoureAvisSearch2Button;LVSForm.RetoureAvisSear' +
        'ch2Button;Retourenavis suchen;404;RETOUREN'
      
        'LVSForm;TMenuItem;KommPickResultMenuItem;LVSForm.KommPickResultM' +
        'enuItem;Pickergebnisse '#252'bernehmen;405;KOMM'
      
        'LVSForm;TMenuItem;BestImportMenuItem;LVSForm.BestImportMenuItem;' +
        'Warenerwartung Importieren;406;BESTELL'
      
        'LVSForm;TMenuItem;BestBestReopenMenuItem;LVSForm.BestBestReopenM' +
        'enuItem;Bestellung neu '#246'ffnen;407;BESTELL'
      
        'LVSForm;TMenuItem;WAImportTrackingMenuItem;LVSForm.WAImportTrack' +
        'ingMenuItem;Import Trackinginfos;408;WA'
      
        'LVSForm;TMenuItem;WarenBesChargeMHDChargeMenuItem;LVSForm.WarenB' +
        'esChargeMHDChargeMenuItem;MHD / Charge bearbeiten;409;BESTAND'
      
        'LVSForm;TMenuItem;LagerBesChangeMHDChargeMenuItem;LVSForm.LagerB' +
        'esChangeMHDChargeMenuItem;MHD / Charge bearbeiten;410;BESTAND'
      
        'LVSForm;TMenuItem;LEBesChangeCategoryMenuItem;LVSForm.LEBesChang' +
        'eCategoryMenuItem;Bestandskategorie '#228'ndern;411;BESTAND'
      
        'LVSForm;TMenuItem;LEBesChangeMHDChargeMenuItem;LVSForm.LEBesChan' +
        'geMHDChargeMenuItem;MHD / Charge bearbeiten;412;BESTAND'
      
        'LVSForm;TMenuItem;LagerBesChangeCategoryMenuItem;LVSForm.LagerBe' +
        'sChangeCategoryMenuItem;Bestandskategorie '#228'ndern;413;BESTAND'
      
        'LVSForm;TMenuItem;BestCopyMenuItem;LVSForm.BestCopyMenuItem;Ware' +
        'nerwartung kopieren;414;BESTELL'
      
        'LVSForm;TMenuItem;WartungSendITMenuItem;LVSForm.WartungSendITMen' +
        'uItem;SendIT;415;VERWALTUNG'
      
        'LVSForm;TMenuItem;NVESetSendungsNrMenuItems;LVSForm.NVESetSendun' +
        'gsNrMenuItems;Sendungsdaten setzen;416;WA'
      
        ';ACO;CanceMultipleOrders;.CanceMultipleOrders;Mehrere Auftr'#228'ge s' +
        'tornieren;417;AUFTRAG'
      
        'BestellVereinnahmungForm;TMenuItem;ChangeStammdatenMenuItem;Best' +
        'ellVereinnahmungForm.ChangeStammdatenMenuItem;Lagerstammdaten be' +
        'arbeiten...;418;WE'
      
        'LVSForm;TMenuItem;NachChangeRestrictionMenuItem;LVSForm.NachChan' +
        'geRestrictionMenuItem;Benutzer / Gruppe zuweisen;419;NACHSCHUB'
      
        'LVSForm;TMenuItem;ConfigBatchMenuItem;LVSForm.ConfigBatchMenuIte' +
        'm;Batchl'#228'ufe konfigurieren...;420;CONFIG'
      'LVSForm;TTabSheet;OMSTabSheet;LVSForm.OMSTabSheet;OMS;421;OMS'
      
        'SpeditionenForm;TMenuItem;SpedImportSendungMenuItem;SpeditionenF' +
        'orm.SpedImportSendungMenuItem;Sendungsdaten importieren;422;SPED' +
        'ITION'
      
        'LVSForm;TMenuItem;LPPrintShelfLabelMenuItem;LVSForm.LPPrintShelf' +
        'LabelMenuItem;Lagerplatzbeschriftung drucken;423;LAGER'
      
        'LVSForm;TTabSheet;WATrackingTabSheet;LVSForm.WATrackingTabSheet;' +
        'Paket-Erkennung;424;WA'
      
        'LVSForm;TMenuItem;AufAddWorkloadMenuItem;LVSForm.AufAddWorkloadM' +
        'enuItem;Aufwandsposition erfassen;425;AUFTRAG'
      
        'LVSForm;TMenuItem;AufPosAddWorkloadMenuItem;LVSForm.AufPosAddWor' +
        'kloadMenuItem;Aufwandsposition erfassen;426;AUFTRAG'
      
        ';ACO;MultiSelectWA;.MultiSelectWA;MultiSelect im Warenausgang;42' +
        '7;WA'
      
        'LVSForm;TButton;PrtLadeButton;LVSForm.PrtLadeButton;Ladeliste dr' +
        'ucken...;428;WA'
      
        'LVSForm;TMenuItem;KommChangeHinweisMenuItem;LVSForm.KommChangeHi' +
        'nweisMenuItem;Komm-Hinweis '#228'ndern;429;KOMM'
      
        'LVSForm;TMenuItem;AufChangeLTMenuItem;LVSForm.AufChangeLTMenuIte' +
        'm;Ladungstr'#228'ger und Packmittel '#228'ndern;430;AUFTRAG'
      
        'LVSForm;TMenuItem;KommChangePrioMenuItem;LVSForm.KommChangePrioM' +
        'enuItem;Prio '#228'ndern;431;KOMM'
      
        'LVSForm;TMenuItem;OverviewDashBoardMenuItem;LVSForm.OverviewDash' +
        'BoardMenuItem;Dashboard '#220'bersicht;432;DASHBOARD'
      
        ';ACO;OptionBesKorrektur;.OptionBesKorrektur;Auswahl Bestandskorr' +
        'ektur ja/nein;433;AUFTRAG'
      
        'LVSForm;TButton;InPosResButton;LVSForm.InPosResButton;Z'#228'hlergebn' +
        'isse erfassen...;434;INVENTUR'
      
        'LVSForm;TMenuItem;WAResendEDIMenuItem;LVSForm.WAResendEDIMenuIte' +
        'm;Abschlussmeldung erneut anstossen;435;WA'
      
        'ShowBestandResForm;TMenuItem;DelBesResMenuItem;ShowBestandResFor' +
        'm.DelBesResMenuItem;Reservierung l'#246'schen;436;BESTAND'
      
        'LVSForm;TMenuItem;SetupSendITMenuItem;LVSForm.SetupSendITMenuIte' +
        'm;SendIT Setup;437;SENDIT'
      
        'LVSForm;TButton;RevReloadButton;LVSForm.RevReloadButton;Revision' +
        ' aktualisieren;438;REVISION'
      
        'LVSForm;TMenuItem;VerladungInfoMenuItem;LVSForm.VerladungInfoMen' +
        'uItem;Infos zum Verladen erfassen;439;VERLADEN'
      
        'LVSForm;TButton;LagerLEPrintBlancoLTLabelsButton;LVSForm.LagerLE' +
        'PrintBlancoLTLabelsButton;Neue LE-Labels drucken;440;LE'
      
        'LVSForm;TMenuItem;DHLESSendungsNr1;LVSForm.DHLESSendungsNr1;DHL ' +
        'ES SendungsNr;441;TEST'
      
        'LVSForm;TMenuItem;KommPrintChecklistMenuItem;LVSForm.KommPrintCh' +
        'ecklistMenuItem;Kontroll-Schein drucken...;442;KOMM'
      
        'LVSForm;TMenuItem;RetoureBesActivMenuItem;LVSForm.RetoureBesActi' +
        'vMenuItem;Retourenbestand aktivieren;443;RETOURE'
      
        'LVSForm;TMenuItem;RetourePosQualifyMenuItem;LVSForm.RetourePosQu' +
        'alifyMenuItem;Retourenposition beurteilen...;444;RETOURE'
      
        'LVSForm;TMenuItem;WANVEReverseMenuItem;LVSForm.WANVEReverseMenuI' +
        'tem;NVE zur'#252'ckbuchen...;445;NVE'
      
        'LVSForm;TMenuItem;VerpackenAufPosBulkMenuItem;LVSForm.VerpackenA' +
        'ufPosBulkMenuItem;Bulk verpacken;446;PACK'
      
        'LVSForm;TMenuItem;RetoureCreateOrderMenuItem;LVSForm.RetoureCrea' +
        'teOrderMenuItem;R'#252'cksende Auftrag anlegen;447;RETOURE'
      
        'AuftragPosVerpackenForm;TMenuItem;PrintIDLabelMenuItem;AuftragPo' +
        'sVerpackenForm.PrintIDLabelMenuItem;Bestandslabel drucken;448;VE' +
        'RPACKEN'
      
        ';ACO;LEToPackplatz;.LEToPackplatz;LE an Packplatz ziehen;449;VER' +
        'PACKEN'
      
        'LVSForm;TButton;KommDoneButton;LVSForm.KommDoneButton;Komm. manu' +
        'ell abschliessen;450;KOMM'
      
        'LVSForm;TButton;KommBeginButton;LVSForm.KommBeginButton;Komm. be' +
        'ginnen;451;KOMM'
      
        'LVSForm;TMenuItem;AufChangeLagerMenuItem;LVSForm.AufChangeLagerM' +
        'enuItem;Auftrag in ein anderes Lager umbuchen;452;AUFTRAG'
      
        'LVSForm;TMenuItem;BestChangeLagerMenuItem;LVSForm.BestChangeLage' +
        'rMenuItem;Bestellung in ein anderes Lager umbuchen;453;BESTELLUN' +
        'G'
      ';ACO;MultiSelectLE;.MultiSelectLE;MultiSelect bei den LEs;454;LE'
      
        'LVSForm;TMenuItem;WANVEReopenMenuItem;LVSForm.WANVEReopenMenuIte' +
        'm;NVE wieder '#246'ffnen;455;NVE'
      
        'LVSForm;TButton;LEDisposeButton;LVSForm.LEDisposeButton;LE Entso' +
        'rgen;456;LE'
      
        ';ACO;ChangeAddress;.ChangeAddress;Adress'#228'nderungn zul'#228'ssig;457;A' +
        'UFTRAG'
      
        ';ACO;SelectAllVerpacken;.SelectAllVerpacken;Bei Artikel Gruppier' +
        'ung dennoch den Auswahlhacken anzeigen;458;VERPACKEN'
      
        'LVSForm;TButton;BucheAvisWEButton;LVSForm.BucheAvisWEButton;Avis' +
        ' vereinnahmen...;459;WW'
      
        ';ACO;ChangeStockSerialNr;.ChangeStockSerialNr;Seriennummern '#228'nde' +
        'rn;460;BESTAND'
      
        ';ACO;PackSwitchKommLE;.PackSwitchKommLE;Beim Packen die Komm-LE ' +
        'wechslen;461;VERPACKEN'
      
        ';ACO;ResetClosedOrders;.ResetClosedOrders;Abgeschlossene Auftr'#228'g' +
        'e zur'#252'cksetzen;462;AUFTRAG'
      
        'LVSForm;TMenuItem;OrderSendBillandHoleMenuItem;LVSForm.OrderSend' +
        'BillandHoleMenuItem;Auftrag mit Bill and Hold zur'#252'ckmelden;463;A' +
        'UFTRAG'
      
        ';ACO;WEScanNotOrdered;.WEScanNotOrdered;Scannen von nicht bestel' +
        'lten Artikel im WE;464;WE'
      
        ';ACO;RETScanUndelivered;.RETScanUndelivered;Scannen von nicht ge' +
        'lieferten Artikel in der Retoure;465;RETOURE'
      
        'LVSForm;TMenuItem;WEDMSAddPictureMenuItem;LVSForm.WEDMSAddPictur' +
        'eMenuItem;Foto hinzuf'#252'gen;466;WE'
      
        'LVSForm;TMenuItem;WEDMSAddDocumentMenuItem;LVSForm.WEDMSAddDocum' +
        'entMenuItem;Dokument hinzuf'#252'gen;467;WE'
      
        'LVSForm;TMenuItem;WEShowDMSMenuItem;LVSForm.WEShowDMSMenuItem;Do' +
        'kumente und Fotos anzeigen;468;WE'
      
        'LVSForm;TMenuItem;AufShowDMSContendMenuItem;LVSForm.AufShowDMSCo' +
        'ntendMenuItem;Fotos und Dokument anzeigen;469;AUFTRAG'
      
        'LVSForm;TMenuItem;AufNotifyToPackMenuItem;LVSForm.AufNotifyToPac' +
        'kMenuItem;Auftrag an Verpackungsautomat '#252'bergeben;470;AUFTRAG'
      
        'AuftragPosVerpackenForm;TButton;ReprintAdvertisingButton;Auftrag' +
        'PosVerpackenForm.ReprintAdvertisingButton;Werbung nachdrucken;47' +
        '1;VERPACKEN'
      
        'LVSForm;TMenuItem;WAReprintAdvertisingMenuItem;LVSForm.WAReprint' +
        'AdvertisingMenuItem;Werbung nachdrucken;472;VERPACKEN'
      
        'LVSForm;TMenuItem;InvBulkCountMenuItem;LVSForm.InvBulkCountMenuI' +
        'tem;Blockplatz erfassen;473;INVENTUR'
      
        ';ACO;ShowWESollMenge;.ShowWESollMenge;Beim WE-Erfassen die Sollm' +
        'enge anzeigen;474;WE'
      
        'LVSForm;TMenuItem;RetourePosVASMenuItem;LVSForm.RetourePosVASMen' +
        'uItem;Retoure Pos: VAS erfassen;475;RETOUREN'
      
        'LVSForm;TMenuItem;WATeilLiefMenuItem;LVSForm.WATeilLiefMenuItem;' +
        'WA Teillieferung anlegen;476;WA'
      
        'LVSForm;TMenuItem;RetShowDMSMenuItem;LVSForm.RetShowDMSMenuItem;' +
        'Ret: Dokumente und Fotos anzeigen;477;RETOUREN'
      
        'LVSForm;TMenuItem;RetDMSAddPictureMenuItem;LVSForm.RetDMSAddPict' +
        'ureMenuItem;Ret: Foto hinzuf'#252'gen;478;RETOUREN'
      
        'LVSForm;TMenuItem;RetDMSAddDocumentMenuItem;LVSForm.RetDMSAddDoc' +
        'umentMenuItem;Ret: Dokument hinzuf'#252'gen;479;RETOUREN'
      
        'LVSForm;TMenuItem;AufPartDeliveryMenuItem;LVSForm.AufPartDeliver' +
        'yMenuItem;Teillieferung m'#246'glich;480;AUFTRAG'
      
        'PrinterForm;TButton;PrtDelButton;PrinterForm.PrtDelButton;L'#246'sche' +
        'n;481;PRINTER'
      
        'PrinterForm;TButton;PrtEditButton;PrinterForm.PrtEditButton;Bear' +
        'beiten...;482;PRINTER'
      
        'PrinterForm;TButton;PrtNewButton;PrinterForm.PrtNewButton;Neu...' +
        ';483;PRINTER'
      
        'LVSForm;TMenuItem;KommSortMenuItem;LVSForm.KommSortMenuItem;Komm' +
        ' neu sortieren;484;KOMM'
      
        'LVSForm;TMenuItem;NachschubImportMenuItem;LVSForm.NachschubImpor' +
        'tMenuItem;Import Nachschub;485;NACHSCHUB'
      
        'LVSForm;TMenuItem;DayLicMenuItem;LVSForm.DayLicMenuItem;Tagesliz' +
        'enzen verwalten;486;LIZENZEN'
      
        'LVSForm;TButton;WALeerAnnahmeButton;LVSForm.WALeerAnnahmeButton;' +
        'Leergut R'#252'cknahme im WA;487;WA'
      
        'LVSForm;TMenuItem;WAPrintVDAMenuItem;LVSForm.WAPrintVDAMenuItem;' +
        'VDA-Label drucken;488;NVE'
      
        ';ACO;WEFullChangeAmount;.WEFullChangeAmount;Menge bei Vollpalett' +
        'en-Annahme '#228'ndern;489;WE'
      
        'LVSForm;TMenuItem;WAPrintVDAVPEMenuItem;LVSForm.WAPrintVDAVPEMen' +
        'uItem;VDA-Label pro Umkarton drucken;491;NVE'
      
        'LVSForm;TMenuItem;EvoScanMenuItem;LVSForm.EvoScanMenuItem;EvoSca' +
        'n Oberfl'#228'che aufrufen;492;EVOSCAN'
      
        'LVSForm;TButton;WAPrintNVEProformaButton;LVSForm.WAPrintNVEProfo' +
        'rmaButton;Proforma drucken...;493;NVE'
      
        'LVSForm;TButton;CreateLeergutWEButton;LVSForm.CreateLeergutWEBut' +
        'ton;Neue Leergutannahme;494;WE'
      
        'EditAufWorkloadsForm;TMenuItem;EditAufWorkloadMenuItem;EditAufWo' +
        'rkloadsForm.EditAufWorkloadMenuItem;Bearbeiten;495;VAS'
      
        'EditAufWorkloadsForm;TMenuItem;DelAufWorkloadMenuItem;EditAufWor' +
        'kloadsForm.DelAufWorkloadMenuItem;L'#246'schen;496;VAS'
      
        'LVSForm;TMenuItem;WARetourenSendungMenuItem;LVSForm.WARetourenSe' +
        'ndungMenuItem;DHL Retourenlabel erzeugen;497;WA'
      
        ';ACO;MultipleQSChecks;.MultipleQSChecks;Mehrfachauswahl f'#252'r QS-P' +
        'r'#252'fungen;498;QS'
      
        'LVSForm;TMenuItem;AufCreateSpedTourMenuItem;LVSForm.AufCreateSpe' +
        'dTourMenuItem;Speditionstour anlegen;499;AUFTRAG'
      
        ';ACO;RETScanBypass;.RETScanBypass;Erlaubt das Umgehen der Scanpf' +
        'licht bei der Artikelerfassung w'#228'hrend der Retourenannahme;500;R' +
        'ETOURE'
      
        'LVSForm;TTabSheet;LagerZollTabSheet;LVSForm.LagerZollTabSheet;Zo' +
        'll-Bestand;501;ZOLL')
  end
end
