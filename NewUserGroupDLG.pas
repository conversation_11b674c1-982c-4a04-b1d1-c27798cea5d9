unit NewUserGroupDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComboBoxPro;

type
  TNewUserGroupForm = class(TForm)
    Label1: TLabel;
    GrpIDEdit: TEdit;
    Label2: TLabel;
    GrpBezEdit: TEdit;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel3: TBevel;
    Bevel2: TBevel;
    GrpLocComboBox: TComboBoxPro;
    Label12: TLabel;
    AdminGroupCheckBox: TCheckBox;
    Bevel1: TBevel;
    GrpACORadioButton: TRadioButton;
    GrpKOMMRadioButton: TRadioButton;
    GrpNACHRadioButton: TRadioButton;
    GrpTRANSRadioButton: TRadioButton;
    Bevel4: TBevel;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys;

procedure TNewUserGroupForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := False;

    if (Length (GrpIDEdit.Text) = 0) then
      GrpIDEdit.SetFocus
    else CanClose := True;
  end;
end;

procedure TNewUserGroupForm.FormCreate(Sender: TObject);
begin
  GrpIDEdit.Text  := '';
  GrpBezEdit.Text := '';
end;

procedure TNewUserGroupForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (GrpLocComboBox);
end;

end.
