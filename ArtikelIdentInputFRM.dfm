object ArtikelIdentInputFrame: TArtikelIdentInputFrame
  Left = 0
  Top = 0
  Width = 680
  Height = 58
  TabOrder = 0
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 680
    Height = 58
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      680
      58)
    object InfoLabel: TLabel
      Left = 8
      Top = 8
      Width = 45
      Height = 13
      Caption = 'InfoLabel'
    end
    object InfoEdit: TEdit
      Left = 8
      Top = 27
      Width = 665
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      Text = 'InfoEdit'
      Visible = False
      OnChange = InfoEditChange
    end
    object InfoComboBox: TComboBoxPro
      Left = 8
      Top = 27
      Width = 665
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 1
      Visible = False
      OnChange = InfoEditChange
    end
    object InfoCheckBox: TCheckBox
      Left = 8
      Top = 27
      Width = 97
      Height = 17
      Caption = 'InfoCheckBox'
      TabOrder = 2
      Visible = False
    end
    object InfoMemo: TMemo
      Left = 8
      Top = 27
      Width = 665
      Height = 21
      Anchors = [akLeft, akTop, akRight, akBottom]
      Lines.Strings = (
        'InfoMemo')
      TabOrder = 3
      Visible = False
      OnChange = InfoEditChange
    end
  end
end
