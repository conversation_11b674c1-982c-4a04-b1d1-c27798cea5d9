unit PrinterDialog;

interface

uses
  Windows;

const
  PD_ALLPAGES = $00000000;
  PD_SELECTION = $00000001;
  PD_PAGENUMS = $00000002;
  PD_NOSELECTION = $00000004;
  PD_NOPAGENUMS = $00000008;
  PD_COLLATE = $00000010;
  PD_PRINTTOFILE = $00000020;
  PD_PRINTSETUP = $00000040;
  PD_NOWARNING = $00000080;
  PD_RETURNDC = $00000100;
  PD_RETURNIC = $00000200;
  PD_RETURNDEFAULT = $00000400;
  PD_SHOWHELP = $00000800;
  PD_ENABLEPRINTHOOK = $00001000;
  PD_ENABLESETUPHOOK = $00002000;
  PD_ENABLEPRINTTEMPLATE = $00004000;
  PD_ENABLESETUPTEMPLATE = $00008000;
  PD_ENABLEPRINTTEMPLATEHANDLE = $00010000;
  PD_ENABLESETUPTEMPLATEHANDLE = $00020000;
  PD_USEDEVMODECOPIES = $00040000;
  PD_USEDEVMODECOPIESANDCOLLATE = $00040000;
  PD_DISABLEPRINTTOFILE = $00080000;
  PD_HIDEPRINTTOFILE = $00100000;
  PD_NONETWORKBUTTON = $00200000;
  PD_CURRENTPAGE = $00400000;
  PD_NOCURRENTPAGE = $00800000;
  PD_EXCLUSIONFLAGS = $01000000;
  PD_USELARGETEMPLATE = $10000000;

  START_PAGE_GENERAL = $ffffffff;


type
  TPrintDlgEx = packed record
    lStructSize : Cardinal;
    hWndOwner : HWnd;
    hDevMode : Cardinal;
    hDevNames : Cardinal;
    hDC : HDC;
    Flags : Cardinal;
    Flags2 : Cardinal;
    ExclusionFlags : Cardinal;
    nPageRanges : DWord;
    nMaxPageRanges : DWord;
    lpPageRanges : Pointer;
    nMinPage : DWord;
    nMaxPage : DWord;
    nCopies : DWord;
    hInstance : Cardinal;
    lpPrintTemplateName : PAnsiChar;
    lpCallback : Pointer;
    nPropertyPages : Cardinal;
    lphPropertyPages : Cardinal;
    nStartPage : Cardinal;
    dwResultAction : Cardinal;
  end;

  TPageRange = packed record
    nFromPage, nToPage : DWord;
  end;

function PrintDlgEx (ParentWnd: HWND) : Integer;

implementation

type TPrintDlgExFunc = function (PDEx : Pointer): DWord; stdcall;


function PrintDlgEx (ParentWnd: HWND) : Integer;

var hComDlg32 : THandle;
    FuncPrintDlgEx : TPrintDlgExFunc;
    pdex : TPrintDlgEx;
    PageRangeArray : array[1..3] of TPageRange;
begin

  hComDlg32 := LoadLibrary('comdlg32.dll');
  if hComDlg32 = 0 then
    Result := -1
  else begin
    @FuncPrintDlgEx := GetProcAddress(hComDlg32,'PrintDlgExA');
    if not (Assigned (FuncPrintDlgEx)) then
      Result := -2
    else begin
      ZeroMemory(@pdex, sizeof(TPrintDlgEx));
      pdex.lStructSize := sizeof(TPrintDlgEx);
      pdex.hWndOwner := ParentWnd;
      pdex.Flags := PD_ALLPAGES;
      pdex.nPageRanges := 1;
      pdex.nMaxPageRanges := 3;
      PageRangeArray[1].nFromPage := 1;
      PageRangeArray[1].nToPage := 1;
      pdex.lpPageRanges := @PageRangeArray[1];
      pdex.nMinPage := 1;
      pdex.nMaxPage := 10;
      pdex.nCopies := 1;
      pdex.nStartPage := START_PAGE_GENERAL;

      Result := FuncPrintDlgEx (@pdex);
    end;

    FreeLibrary(hComDlg32);
  end;
end;

end.
