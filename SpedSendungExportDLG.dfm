object SpedSendungExport: TSpedSendungExport
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Sendungsdaten Exportieren'
  ClientHeight = 313
  ClientWidth = 517
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    517
    313)
  PixelsPerInch = 96
  TextHeight = 13
  object DatumLabel: TLabel
    Left = 8
    Top = 155
    Width = 73
    Height = 13
    Caption = 'Versand Datum'
  end
  object MandPanel: TPanel
    Left = 0
    Top = 49
    Width = 517
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      517
      50)
    object Label9: TLabel
      Left = 8
      Top = 8
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 499
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      ItemHeight = 16
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 99
    Width = 517
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      517
      50)
    object Label17: TLabel
      Left = 8
      Top = 8
      Width = 69
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 499
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      ItemHeight = 16
      TabOrder = 0
    end
  end
  object BottomPanel: TPanel
    Left = 0
    Top = 272
    Width = 517
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 6
    DesignSize = (
      517
      41)
    object AbortButton: TButton
      Left = 432
      Top = 8
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 0
    end
    object OkButton: TButton
      Left = 279
      Top = 8
      Width = 144
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Sendungen exportieren'
      ModalResult = 1
      TabOrder = 1
    end
  end
  object SpedPanel: TPanel
    Left = 0
    Top = 0
    Width = 517
    Height = 49
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 48
      Height = 13
      Caption = 'Spedition:'
    end
    object Label2: TLabel
      Left = 8
      Top = 24
      Width = 50
      Height = 13
      Caption = 'Exportart:'
    end
    object SpedLabel: TLabel
      Left = 72
      Top = 8
      Width = 49
      Height = 13
      Caption = 'SpedLabel'
    end
    object ExpArtLabel: TLabel
      Left = 72
      Top = 24
      Width = 58
      Height = 13
      Caption = 'ExpArtLabel'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 45
      Width = 499
      Height = 8
      Shape = bsTopLine
    end
  end
  object ProgressBar1: TProgressBar
    Left = 8
    Top = 249
    Width = 499
    Height = 17
    Anchors = [akLeft, akRight, akBottom]
    TabOrder = 5
  end
  object DatumDateTimePicker: TDateTimePicker
    Left = 8
    Top = 171
    Width = 105
    Height = 21
    Date = 38275.496297569450000000
    Time = 38275.496297569450000000
    TabOrder = 3
  end
  object ClosedCheckBox: TCheckBox
    Left = 8
    Top = 208
    Width = 473
    Height = 17
    Caption = 'Nur abgeschlossene Auftr'#228'ge exportieren'
    Checked = True
    State = cbChecked
    TabOrder = 4
  end
end
