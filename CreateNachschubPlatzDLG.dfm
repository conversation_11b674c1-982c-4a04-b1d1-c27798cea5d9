object CreateNachschubPlatzForm: TCreateNachschubPlatzForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Nachschubplatz anlegen'
  ClientHeight = 289
  ClientWidth = 574
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    574
    289)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 94
    Height = 13
    Caption = 'Neuer Lagerbereich'
  end
  object Label2: TLabel
    Left = 8
    Top = 56
    Width = 122
    Height = 13
    Caption = 'Lagerzone in dem Bereich'
  end
  object Label28: TLabel
    Left = 8
    Top = 104
    Width = 69
    Height = 13
    Caption = 'Lagerplatz-Art'
  end
  object Label3: TLabel
    Left = 8
    Top = 168
    Width = 70
    Height = 13
    Caption = 'Regal-Nummer'
  end
  object Label12: TLabel
    Left = 160
    Top = 168
    Width = 60
    Height = 13
    Caption = 'Bezeichnung'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 229
    Width = 558
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel2: TBevel
    Left = 8
    Top = 157
    Width = 558
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object LBComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 558
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 0
    OnChange = LBComboBoxChange
    ExplicitWidth = 548
  end
  object LBZoneComboBox: TComboBoxPro
    Left = 8
    Top = 72
    Width = 558
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 160
    ItemHeight = 15
    TabOrder = 1
    ExplicitWidth = 548
  end
  object LPArtComboBox: TComboBoxPro
    Left = 8
    Top = 120
    Width = 558
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 80
    ItemHeight = 15
    TabOrder = 2
    ExplicitWidth = 548
  end
  object RegalEdit: TEdit
    Left = 8
    Top = 184
    Width = 129
    Height = 21
    MaxLength = 3
    TabOrder = 3
    Text = 'RegalEdit'
  end
  object NameEdit: TEdit
    Left = 160
    Top = 184
    Width = 406
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 64
    TabOrder = 4
    Text = 'NameEdit'
  end
  object OkButton: TButton
    Left = 403
    Top = 256
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = '&Erzeugen'
    Default = True
    ModalResult = 1
    TabOrder = 5
    ExplicitLeft = 393
    ExplicitTop = 246
  end
  object AbortButton: TButton
    Left = 491
    Top = 256
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = '&Abbrechen'
    ModalResult = 3
    TabOrder = 6
    ExplicitLeft = 481
    ExplicitTop = 246
  end
end
