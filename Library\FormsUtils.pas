unit FormsUtils;

interface

uses StdCtrls;

procedure SetControlColor (Item : TEdit); overload;
procedure SetControlColor (Item : TComboBox); overload;

implementation

uses Windows, Graphics;

procedure SetControlColor (Item : TEdit);
begin
  if Item.Enabled then
    Item.Color := clWindow
  else Item.Color := clBtnFace;
end;

procedure SetControlColor (Item : TComboBox);
begin
  if Item.Enabled then
    Item.Color := clWindow
  else Item.Color := clBtnFace;
end;

end.

