object WEBuchEAN128Form: TWEBuchEAN128Form
  Left = 534
  Top = 215
  BorderStyle = bsDialog
  Caption = 'Vereinnahmung '#252'ber EAN128 Barcodes'
  ClientHeight = 454
  ClientWidth = 610
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  DesignSize = (
    610
    454)
  TextHeight = 13
  object Label21: TLabel
    Left = 8
    Top = 14
    Width = 80
    Height = 13
    Caption = 'Ladungstr'#228'gerart'
  end
  object OkButton: TButton
    Left = 442
    Top = 408
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'OK'
    Default = True
    TabOrder = 0
    OnClick = OkButtonClick
  end
  object StatusBar1: TStatusBar
    Left = 0
    Top = 435
    Width = 610
    Height = 19
    Panels = <
      item
        Width = 400
      end
      item
        Width = 50
      end
      item
        Width = 50
      end>
  end
  object FehlerLabel: TPanel
    Left = 8
    Top = 345
    Width = 593
    Height = 33
    BevelOuter = bvNone
    Caption = 'FehlerLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -14
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 2
  end
  object AbortButton: TButton
    Left = 528
    Top = 408
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 3
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 40
    Width = 593
    Height = 297
    ActivePage = TabSheet1
    TabOrder = 4
    object TabSheet1: TTabSheet
      Caption = 'Produkt'
      DesignSize = (
        585
        269)
      object GroupBox1: TGroupBox
        Left = 8
        Top = 9
        Width = 569
        Height = 249
        Anchors = [akLeft, akTop, akRight]
        Caption = ' EAN128-Infos '
        TabOrder = 0
        object Label1: TLabel
          Left = 16
          Top = 16
          Width = 43
          Height = 13
          Caption = 'EAN (01)'
        end
        object Label2: TLabel
          Left = 16
          Top = 60
          Width = 46
          Height = 13
          Caption = 'MHD (15)'
        end
        object Label3: TLabel
          Left = 192
          Top = 60
          Width = 55
          Height = 13
          Caption = 'Charge (10)'
        end
        object Label4: TLabel
          Left = 16
          Top = 106
          Width = 43
          Height = 13
          Caption = 'NVE (00)'
        end
        object Label5: TLabel
          Left = 16
          Top = 155
          Width = 95
          Height = 13
          Caption = 'Nettogewicht (310x)'
        end
        object Label6: TLabel
          Left = 16
          Top = 201
          Width = 33
          Height = 13
          Caption = 'Menge'
        end
        object Label8: TLabel
          Left = 192
          Top = 201
          Width = 65
          Height = 13
          Caption = 'Einheiten (37)'
        end
        object Label9: TLabel
          Left = 121
          Top = 175
          Width = 12
          Height = 13
          Caption = 'kg'
        end
        object Label10: TLabel
          Left = 192
          Top = 106
          Width = 97
          Height = 13
          Caption = 'Bruttogewicht (330x)'
        end
        object Label11: TLabel
          Left = 296
          Top = 125
          Width = 12
          Height = 13
          Caption = 'kg'
        end
        object Label12: TLabel
          Left = 192
          Top = 16
          Width = 97
          Height = 13
          Caption = 'Enthaltene EAN (02)'
        end
        object Label13: TLabel
          Left = 368
          Top = 16
          Width = 76
          Height = 13
          Caption = 'Lieferant-Nr (97)'
        end
        object Label19: TLabel
          Left = 368
          Top = 58
          Width = 84
          Height = 13
          Caption = 'Verfallsdatum (17)'
        end
        object Label20: TLabel
          Left = 368
          Top = 106
          Width = 113
          Height = 13
          Caption = 'Verpackungsdatum (13)'
        end
        object Label22: TLabel
          Left = 368
          Top = 200
          Width = 70
          Height = 13
          Caption = 'Bestellnr. (400)'
        end
        object EANEdit: TEdit
          Left = 16
          Top = 32
          Width = 121
          Height = 21
          TabOrder = 0
          Text = 'EANEdit'
          OnChange = ChangeInput
          OnKeyPress = EANEditKeyPress
        end
        object MHDEdit: TEdit
          Left = 16
          Top = 76
          Width = 121
          Height = 21
          TabOrder = 2
          Text = 'MHDEdit'
          OnChange = ChangeInput
          OnKeyPress = MHDEditKeyPress
        end
        object ChargeEdit: TEdit
          Left = 192
          Top = 76
          Width = 121
          Height = 21
          TabOrder = 3
          Text = 'ChargeEdit'
          OnChange = ChangeInput
        end
        object NVEEdit: TEdit
          Left = 16
          Top = 122
          Width = 121
          Height = 21
          TabOrder = 4
          Text = 'NVEEdit'
          OnChange = ChangeInput
          OnKeyPress = EANEditKeyPress
        end
        object NettoEdit: TEdit
          Left = 16
          Top = 171
          Width = 97
          Height = 21
          TabOrder = 6
          Text = 'NettoEdit'
          OnChange = ChangeInput
          OnKeyPress = NettoEditKeyPress
        end
        object MengeEdit: TEdit
          Left = 16
          Top = 217
          Width = 73
          Height = 21
          TabOrder = 7
          Text = '0'
          OnChange = ChangeInput
          OnKeyPress = MengeEditKeyPress
        end
        object EinheitEdit: TEdit
          Left = 192
          Top = 217
          Width = 121
          Height = 21
          TabStop = False
          Enabled = False
          TabOrder = 9
          Text = 'EinheitEdit'
          OnChange = ChangeInput
        end
        object BruttoEdit: TEdit
          Left = 192
          Top = 122
          Width = 97
          Height = 21
          TabOrder = 5
          Text = 'BruttoEdit'
          OnChange = ChangeInput
          OnKeyPress = NettoEditKeyPress
        end
        object InhaltEANEdit: TEdit
          Left = 192
          Top = 32
          Width = 121
          Height = 21
          TabOrder = 1
          Text = 'InhaltEANEdit'
          OnChange = ChangeInput
          OnKeyPress = EANEditKeyPress
        end
        object MengeUpDown: TIntegerUpDown
          Left = 89
          Top = 217
          Width = 16
          Height = 21
          Associate = MengeEdit
          Max = 10000
          TabOrder = 8
        end
        object LFEdit: TEdit
          Left = 368
          Top = 32
          Width = 121
          Height = 21
          Enabled = False
          TabOrder = 11
          Text = 'LFEdit'
        end
        object VerDateEdit: TEdit
          Left = 368
          Top = 74
          Width = 121
          Height = 21
          TabOrder = 12
          Text = 'VerDateEdit'
          OnChange = ChangeInput
        end
        object PackDateEdit: TEdit
          Left = 368
          Top = 122
          Width = 121
          Height = 21
          TabOrder = 13
          Text = 'PackDateEdit'
          OnChange = ChangeInput
        end
        object BestNrEdit: TEdit
          Left = 368
          Top = 216
          Width = 121
          Height = 21
          TabOrder = 10
          Text = 'BestNrEdit'
          OnChange = ChangeInput
          OnKeyPress = EANEditKeyPress
        end
      end
    end
    object TabSheet2: TTabSheet
      Caption = 'ORGA Invent'
      ImageIndex = 1
      object GroupBox2: TGroupBox
        Left = 8
        Top = 8
        Width = 569
        Height = 257
        Caption = 'Verarbeitung'
        TabOrder = 0
        object Label7: TLabel
          Left = 8
          Top = 16
          Width = 95
          Height = 13
          Caption = 'Ursprungsland (422)'
        end
        object Label14: TLabel
          Left = 8
          Top = 64
          Width = 99
          Height = 13
          Caption = '1. Verarbeitung (423)'
        end
        object Label15: TLabel
          Left = 152
          Top = 64
          Width = 87
          Height = 13
          Caption = 'Verarbeitung (424)'
        end
        object Label16: TLabel
          Left = 8
          Top = 112
          Width = 75
          Height = 13
          Caption = 'Zerlegung (425)'
        end
        object Label17: TLabel
          Left = 320
          Top = 64
          Width = 132
          Height = 13
          Caption = 'Gesamte Verarbeitung (426)'
        end
        object Label18: TLabel
          Left = 8
          Top = 160
          Width = 97
          Height = 13
          Caption = 'Zulassungsnummern'
        end
        object Edit2: TEdit
          Left = 8
          Top = 32
          Width = 121
          Height = 21
          TabOrder = 0
          Text = 'Edit2'
        end
        object Edit3: TEdit
          Left = 8
          Top = 80
          Width = 121
          Height = 21
          TabOrder = 1
          Text = 'Edit3'
        end
        object Edit4: TEdit
          Left = 152
          Top = 80
          Width = 121
          Height = 21
          TabOrder = 2
          Text = 'Edit4'
        end
        object Edit5: TEdit
          Left = 8
          Top = 128
          Width = 121
          Height = 21
          TabOrder = 4
          Text = 'Edit5'
        end
        object Edit6: TEdit
          Left = 320
          Top = 80
          Width = 121
          Height = 21
          TabOrder = 3
          Text = 'Edit6'
        end
        object Edit7: TEdit
          Left = 8
          Top = 176
          Width = 100
          Height = 21
          TabOrder = 5
          Text = 'Edit7'
        end
        object Edit8: TEdit
          Left = 120
          Top = 176
          Width = 100
          Height = 21
          TabOrder = 6
          Text = 'Edit7'
        end
        object Edit9: TEdit
          Left = 232
          Top = 176
          Width = 100
          Height = 21
          TabOrder = 7
          Text = 'Edit7'
        end
        object Edit10: TEdit
          Left = 344
          Top = 176
          Width = 100
          Height = 21
          TabOrder = 8
          Text = 'Edit7'
        end
        object Edit11: TEdit
          Left = 456
          Top = 176
          Width = 100
          Height = 21
          TabOrder = 9
          Text = 'Edit7'
        end
      end
    end
    object TabSheet3: TTabSheet
      Caption = 'EAN 28er'
      ImageIndex = 2
      object Label24: TLabel
        Left = 8
        Top = 8
        Width = 73
        Height = 13
        Caption = 'Artikel-Nr (28er)'
      end
      object Label25: TLabel
        Left = 160
        Top = 8
        Width = 69
        Height = 13
        Caption = 'Gewicht (28er)'
      end
      object ArtNrEdit: TEdit
        Left = 8
        Top = 24
        Width = 121
        Height = 21
        TabOrder = 0
        Text = 'ArtNrEdit'
      end
      object GewichtEdit: TEdit
        Left = 160
        Top = 24
        Width = 121
        Height = 21
        TabOrder = 1
        Text = 'Edit'
      end
    end
  end
  object ErrorButton: TButton
    Left = 152
    Top = 408
    Width = 115
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Falschauszeichnung'
    ModalResult = 5
    TabOrder = 5
  end
  object LTComboBox: TComboBoxPro
    Left = 104
    Top = 8
    Width = 497
    Height = 22
    Style = csOwnerDrawFixed
    ColWidth = 120
    TabOrder = 6
  end
  object VPEErrorButton: TButton
    Left = 8
    Top = 408
    Width = 131
    Height = 25
    Caption = 'Ohne Karton-EAN128'
    TabOrder = 7
    OnClick = VPEErrorButtonClick
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 536
    Top = 24
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 472
    Top = 24
  end
end
