object WEBulkForm: TWEBulkForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Bulk-Buchungen'
  ClientHeight = 474
  ClientWidth = 362
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    362
    474)
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 205
    Width = 51
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Anzahl LEs'
    ExplicitTop = 135
  end
  object Label2: TLabel
    Left = 164
    Top = 205
    Width = 33
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'LT-Typ'
  end
  object Label3: TLabel
    Left = 8
    Top = 257
    Width = 118
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Anzahl Umverpackungen'
    ExplicitTop = 187
  end
  object Label4: TLabel
    Left = 164
    Top = 257
    Width = 172
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Anzahl Einheiten pro Umverpackung'
  end
  object Bevel2: TBevel
    Left = 8
    Top = 431
    Width = 346
    Height = 6
    Anchors = [akLeft, akBottom]
    Shape = bsTopLine
    ExplicitTop = 373
  end
  object Label9: TLabel
    Left = 8
    Top = 350
    Width = 54
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Buchen auf'
    ExplicitTop = 280
  end
  object Label12: TLabel
    Left = 80
    Top = 205
    Width = 39
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'LE H'#246'he'
  end
  object Label13: TLabel
    Left = 131
    Top = 224
    Width = 16
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'mm'
  end
  object LTComboBox: TComboBoxPro
    Left = 164
    Top = 221
    Width = 190
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akBottom]
    ItemHeight = 15
    TabOrder = 6
    OnChange = EditChange
  end
  object LTAnzEdit: TEdit
    Left = 8
    Top = 221
    Width = 43
    Height = 21
    Anchors = [akLeft, akBottom]
    TabOrder = 3
    Text = '1'
    OnChange = EditChange
    OnKeyPress = IntEditKeyPress
  end
  object LTAnzUpDown: TUpDown
    Left = 51
    Top = 221
    Width = 16
    Height = 21
    Anchors = [akLeft, akBottom]
    Associate = LTAnzEdit
    Min = 1
    Position = 1
    TabOrder = 4
  end
  object UmPackEdit: TEdit
    Left = 8
    Top = 273
    Width = 65
    Height = 21
    Anchors = [akLeft, akBottom]
    TabOrder = 7
    Text = 'UmPackEdit'
    OnChange = EditChange
    OnExit = CheckPalFaktor
    OnKeyPress = IntEditKeyPress
  end
  object VPEEdit: TEdit
    Left = 164
    Top = 273
    Width = 65
    Height = 21
    Anchors = [akLeft, akBottom]
    TabOrder = 8
    Text = 'VPEEdit'
    OnChange = EditChange
    OnExit = CheckPalFaktor
    OnKeyPress = IntEditKeyPress
  end
  object OkButton: TButton
    Left = 191
    Top = 441
    Width = 75
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Ok'
    ModalResult = 1
    TabOrder = 12
  end
  object AbortButton: TButton
    Left = 279
    Top = 441
    Width = 75
    Height = 25
    Anchors = [akLeft, akBottom]
    Caption = 'Abbruch'
    ModalResult = 3
    TabOrder = 13
  end
  object FehlerLabel: TPanel
    Left = 8
    Top = 402
    Width = 346
    Height = 24
    Anchors = [akLeft, akBottom]
    BevelOuter = bvNone
    Caption = 'FehlerLabel'
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 11
  end
  object BereichComboBox: TComboBoxPro
    Left = 8
    Top = 366
    Width = 346
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akBottom]
    TabOrder = 10
  end
  object LabelRadioGroup: TRadioGroup
    Left = 8
    Top = 300
    Width = 346
    Height = 44
    Anchors = [akLeft, akBottom]
    Caption = 'Etiketten drucken'
    Columns = 4
    ItemIndex = 0
    Items.Strings = (
      'Keine'
      'LE'
      'Bestand'
      'LE + Bestand')
    TabOrder = 9
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 362
    Height = 123
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      362
      123)
    object Label6: TLabel
      Left = 8
      Top = 16
      Width = 20
      Height = 13
      Caption = 'WE:'
    end
    object WENrLabel: TLabel
      Left = 64
      Top = 16
      Width = 59
      Height = 13
      Caption = 'WENrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label8: TLabel
      Left = 8
      Top = 32
      Width = 49
      Height = 13
      Caption = 'Bestellnr.:'
    end
    object BestNrLabel: TLabel
      Left = 64
      Top = 32
      Width = 67
      Height = 13
      Caption = 'BestNrLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label5: TLabel
      Left = 8
      Top = 56
      Width = 34
      Height = 13
      Caption = 'Artikel:'
    end
    object ArtikelTextLabel: TLabel
      Left = 64
      Top = 56
      Width = 94
      Height = 13
      Caption = 'ArtikelTextLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object ArtikelEinheitLabel: TLabel
      Left = 64
      Top = 72
      Width = 106
      Height = 13
      Caption = 'ArtikelEinheitLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label14: TLabel
      Left = 8
      Top = 96
      Width = 32
      Height = 13
      Caption = 'Menge'
    end
    object Label7: TLabel
      Left = 64
      Top = 96
      Width = 20
      Height = 13
      Caption = 'Soll:'
    end
    object SollLabel: TLabel
      Left = 90
      Top = 96
      Width = 50
      Height = 13
      Caption = 'SollLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label10: TLabel
      Left = 136
      Top = 96
      Width = 74
      Height = 13
      Alignment = taRightJustify
      Caption = 'Bereits erfasst:'
    end
    object ErfasstLabel: TLabel
      Left = 216
      Top = 96
      Width = 69
      Height = 13
      Caption = 'ErfasstLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Bevel1: TBevel
      Left = 8
      Top = 117
      Width = 346
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
  end
  object MHDPanel: TPanel
    Left = 0
    Top = 123
    Width = 362
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    object MHDLabel: TLabel
      Left = 15
      Top = 7
      Width = 22
      Height = 13
      Caption = 'MHD'
    end
    object MHDDutyLabel: TLabel
      Left = 66
      Top = 1
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object MHDEdit: TEdit
      Left = 80
      Top = 3
      Width = 121
      Height = 21
      TabOrder = 0
      Text = 'MHDEdit'
      OnChange = EditChange
      OnExit = MHDEditExit
    end
  end
  object ChargePanel: TPanel
    Left = 0
    Top = 154
    Width = 362
    Height = 42
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      362
      42)
    object Label11: TLabel
      Left = 16
      Top = 9
      Width = 35
      Height = 13
      Caption = 'Charge'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 36
      Width = 346
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object ChargeDutyLabel: TLabel
      Left = 66
      Top = 5
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object ChargeEdit: TEdit
      Left = 80
      Top = 7
      Width = 121
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'ChargeEdit'
      OnChange = EditChange
    end
  end
  object PalHeightEdit: TEdit
    Left = 80
    Top = 221
    Width = 43
    Height = 21
    Anchors = [akLeft, akBottom]
    MaxLength = 4
    TabOrder = 5
    Text = '1'
    OnChange = EditChange
    OnKeyPress = IntEditKeyPress
  end
end
