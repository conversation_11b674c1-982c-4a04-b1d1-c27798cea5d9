{$ifdef UNICODE}
  {$STRINGCHECKS OFF}
{$endif}

{$IFDEF VER130}
  {$DEFINE DELPHI5}
  {$DEFINE DELPHI5_UP}
{$ENDIF}

{$IFDEF VER140}
  {$DEFINE DELPHI6}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI5_UP}
{$ENDIF}

{$IFDEF VER150}
  {$DEFINE DELPHI7}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI5_UP}
{$ENDIF}

{ defines for Delphi 2005}
{$IFDEF VER170}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2005}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
{$ENDIF}

{ defines for Delphi 2006}
{$IFDEF VER180}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
{$ENDIF}

{ defines for Delphi 2007}
{$IFDEF VER185}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
{$ENDIF}

{ defines for Delphi 2009}
{$IFDEF VER200}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
{$ENDIF}

{ defines for Delphi 2010}
{$IFDEF VER210}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
{$ENDIF}

{ defines for Delphi XE}
{$IFDEF VER220}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
{$ENDIF}

{ defines for Delphi XE2}
{$IFDEF VER230}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
{$ENDIF}

{ defines for Delphi XE3}
{$IFDEF VER240}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
{$ENDIF}

{ defines for Delphi XE4}
{$IFDEF VER250}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
{$ENDIF}

{ defines for Delphi XE5}
{$IFDEF VER260}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
{$ENDIF}

{ defines for Delphi XE6}
{$IFDEF VER270}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
{$ENDIF}

{ defines for Delphi XE7}
{$IFDEF VER280}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
{$ENDIF}

{ defines for Delphi XE8}
{$IFDEF VER290}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
{$ENDIF}

{ defines for Delphi XE10}
{$IFDEF VER300}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
  {$DEFINE DELPHIXE10_UP}
{$ENDIF}

{ defines for Delphi XE10.1}
{$IFDEF VER310}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
  {$DEFINE DELPHIXE10_UP}
{$ENDIF}

{ defines for Delphi XE10.2}
{$IFDEF VER320}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
  {$DEFINE DELPHIXE10_UP}
{$ENDIF}

{ defines for Delphi XE10.3}
{$IFDEF VER330}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
  {$DEFINE DELPHIXE10_UP}
{$ENDIF}

{ defines for Delphi XE10.4}
{$IFDEF VER340}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
  {$DEFINE DELPHIXE10_UP}
{$ENDIF}

{ defines for Delphi 11}
{$IFDEF VER350}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
  {$DEFINE DELPHIXE10_UP}
  {$DEFINE DELPHIXE11_UP}
{$ENDIF}

{ defines for Delphi 12}
{$IFDEF VER360}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI2006}
  {$DEFINE DELPHI1_UP}
  {$DEFINE DELPHI2_UP}
  {$DEFINE DELPHI3_UP}
  {$DEFINE DELPHI4_UP}
  {$DEFINE DELPHI5_UP}
  {$DEFINE DELPHI6_UP}
  {$DEFINE DELPHI7_UP}
  {$DEFINE DELPHI2005_UP}
  {$DEFINE DELPHI2006_UP}
  {$DEFINE DELPHI2007_UP}
  {$DEFINE DELPHI2009_UP}
  {$DEFINE DELPHIXE_UP}
  {$DEFINE DELPHIXE4_UP}
  {$DEFINE DELPHIXE10_UP}
  {$DEFINE DELPHIXE11_UP}
  {$DEFINE DELPHIXE12_UP}
{$ENDIF}

