object LagAbrAuswForm: TLagAbrAuswForm
  Left = 314
  Top = 107
  BorderStyle = bsDialog
  Caption = 'Lagerabrechnung auswerten'
  ClientHeight = 242
  ClientWidth = 407
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = OnFormCreate
  OnShow = FormShow
  DesignSize = (
    407
    242)
  PixelsPerInch = 96
  TextHeight = 13
  object GroupBox1: TGroupBox
    Left = 8
    Top = 135
    Width = 391
    Height = 73
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Zeitraum'
    TabOrder = 0
    ExplicitTop = 136
    object Label2: TLabel
      Left = 208
      Top = 32
      Width = 16
      Height = 13
      Caption = 'bis:'
    end
    object Label1: TLabel
      Left = 8
      Top = 32
      Width = 21
      Height = 13
      Caption = 'von:'
    end
    object vonDateTimePicker: TDateTimePicker
      Left = 40
      Top = 28
      Width = 113
      Height = 21
      Date = 38607.397274895830000000
      Time = 38607.397274895830000000
      TabOrder = 0
    end
    object bisDateTimePicker: TDateTimePicker
      Left = 232
      Top = 28
      Width = 113
      Height = 21
      Date = 38607.397296562500000000
      Time = 38607.397296562500000000
      TabOrder = 1
    end
  end
  object OKButton: TButton
    Left = 237
    Top = 212
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'OK'
    ModalResult = 1
    TabOrder = 1
    ExplicitTop = 178
  end
  object AbbrechenButton: TButton
    Left = 324
    Top = 212
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Abbrechen'
    ModalResult = 2
    TabOrder = 2
    ExplicitTop = 178
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 407
    Height = 33
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    object Label3: TLabel
      Left = 8
      Top = 8
      Width = 83
      Height = 13
      Caption = 'Auswertung '#252'ber:'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 27
      Width = 391
      Height = 8
      Shape = bsTopLine
    end
    object ArtLabel: TLabel
      Left = 105
      Top = 8
      Width = 48
      Height = 13
      Caption = 'ArtLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 82
    Width = 407
    Height = 49
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    ExplicitTop = 41
    object LagerLabel: TLabel
      Left = 8
      Top = 0
      Width = 30
      Height = 13
      Caption = 'Lager:'
    end
    object Bevel2: TBevel
      Left = 8
      Top = 42
      Width = 391
      Height = 8
      Shape = bsTopLine
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 17
      Width = 391
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 120
      ItemHeight = 15
      TabOrder = 0
    end
  end
  object SubMandantPanel: TPanel
    Left = 0
    Top = 33
    Width = 407
    Height = 49
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    Visible = False
    object Label4: TLabel
      Left = 8
      Top = 0
      Width = 70
      Height = 13
      Caption = 'Untermandant:'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 41
      Width = 391
      Height = 8
      Shape = bsTopLine
    end
    object SubMandComboBox: TComboBoxPro
      Left = 8
      Top = 17
      Width = 391
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 120
      ItemHeight = 15
      TabOrder = 0
    end
  end
end
