object RetoureBewertenForm: TRetoureBewertenForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Retoure bewerten'
  ClientHeight = 399
  ClientWidth = 440
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    440
    399)
  PixelsPerInch = 96
  TextHeight = 13
  object GrundLabel: TLabel
    Left = 8
    Top = 310
    Width = 134
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Abschliessender Kommentar'
  end
  object Bevel1: TBevel
    Left = 6
    Top = 295
    Width = 428
    Height = 6
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object NotOkButton: TButton
    Left = 202
    Top = 366
    Width = 145
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Pr'#252'fung nicht ok'
    Default = True
    ModalResult = 7
    TabOrder = 3
    OnClick = OkButtonClick
  end
  object AbortButton: TButton
    Left = 357
    Top = 366
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
  object GrundComboBox: TComboBox
    Left = 8
    Top = 329
    Width = 424
    Height = 21
    Anchors = [akLeft, akRight, akBottom]
    ItemHeight = 13
    MaxLength = 64
    TabOrder = 2
    Text = 'GrundComboBox'
    Items.Strings = (
      'MHD zu kurz'
      'Ware mangelhaft')
  end
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 440
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 34
      Height = 13
      Caption = 'Kunde:'
    end
    object Label2: TLabel
      Left = 8
      Top = 32
      Width = 34
      Height = 13
      Caption = 'Artikel:'
    end
    object KundeLabel: TLabel
      Left = 64
      Top = 8
      Width = 55
      Height = 13
      Caption = 'KundeLabel'
    end
    object ArtikelLabel: TLabel
      Left = 64
      Top = 32
      Width = 55
      Height = 13
      Caption = 'ArtikelLabel'
    end
  end
  object CheckPanel: TPanel
    Left = 0
    Top = 57
    Width = 440
    Height = 232
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    object FehlerLabel: TLabel
      Left = 0
      Top = 214
      Width = 440
      Height = 18
      Align = alBottom
      Alignment = taCenter
      AutoSize = False
      Caption = 'FehlerLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      ExplicitLeft = 7
      ExplicitTop = 8
      ExplicitWidth = 433
    end
    object ScrollBox1: TScrollBox
      Left = 0
      Top = 0
      Width = 440
      Height = 209
      Align = alTop
      Anchors = [akLeft, akTop, akRight, akBottom]
      BevelInner = bvNone
      BevelOuter = bvNone
      TabOrder = 0
      object HACCPPanel: TPanel
        Left = 0
        Top = 0
        Width = 436
        Height = 199
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 0
      end
    end
  end
  object OkButton: TButton
    Left = 43
    Top = 366
    Width = 145
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Pr'#252'fung ok'
    Default = True
    ModalResult = 1
    TabOrder = 4
    OnClick = OkButtonClick
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 328
    Top = 8
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 360
    Top = 8
  end
end
