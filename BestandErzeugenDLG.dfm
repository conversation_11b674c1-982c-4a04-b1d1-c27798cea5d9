object BestandErzeugenForm: TBestandErzeugenForm
  Left = 539
  Top = 215
  BorderStyle = bsDialog
  Caption = 'Neuanlage von Bestand'
  ClientHeight = 832
  ClientWidth = 432
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poMainFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    432
    832)
  TextHeight = 13
  object FehlerLabel: TLabel
    Left = 0
    Top = 773
    Width = 432
    Height = 18
    Align = alTop
    Alignment = taCenter
    AutoSize = False
    Caption = 'FehlerLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -13
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
    ExplicitLeft = 8
    ExplicitTop = 542
    ExplicitWidth = 415
  end
  object Label14: TLabel
    Left = 8
    Top = 811
    Width = 38
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Label14'
    ExplicitTop = 626
  end
  object AbortButton: TButton
    Left = 350
    Top = 799
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 17
  end
  object OkButton: TButton
    Left = 263
    Top = 799
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 16
  end
  object MandantPanel: TPanel
    Left = 0
    Top = 0
    Width = 432
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label12: TLabel
      Left = 8
      Top = 11
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 80
      Top = 10
      Width = 345
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object LagerPanel: TPanel
    Left = 0
    Top = 31
    Width = 432
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    object Label8: TLabel
      Left = 8
      Top = 11
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object LagerComboBox: TComboBoxPro
      Left = 80
      Top = 8
      Width = 345
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 0
      OnChange = LagerComboBoxChange
    end
  end
  object MandLagerPanel: TPanel
    Left = 0
    Top = 62
    Width = 432
    Height = 10
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    object Bevel5: TBevel
      Left = 4
      Top = 8
      Width = 421
      Height = 4
      Shape = bsTopLine
    end
  end
  object ArtikelPanel: TPanel
    Left = 0
    Top = 72
    Width = 432
    Height = 29
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      432
      29)
    object Label1: TLabel
      Left = 8
      Top = 11
      Width = 29
      Height = 13
      Caption = 'Artikel'
    end
    object ArtikelComboBox: TComboBoxPro
      Left = 184
      Top = 8
      Width = 241
      Height = 21
      ItemDelimiter = #9
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 1
      OnChange = ArtikelComboBoxChange
      OnCloseUp = ArtikelComboBoxCloseUp
      OnDropDown = ArtikelComboBoxDropDown
    end
    object ArNrEdit: TEdit
      Left = 80
      Top = 8
      Width = 98
      Height = 21
      TabOrder = 0
      Text = 'ArNrEdit'
      OnExit = ArNrEditExit
    end
  end
  object MengePanel: TPanel
    Left = 0
    Top = 201
    Width = 432
    Height = 56
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 6
    object Label5: TLabel
      Left = 8
      Top = 8
      Width = 33
      Height = 13
      Caption = 'Menge'
    end
    object Label6: TLabel
      Left = 8
      Top = 38
      Width = 39
      Height = 13
      Caption = 'Gewicht'
    end
    object Label9: TLabel
      Left = 164
      Top = 38
      Width = 12
      Height = 13
      Caption = 'kg'
    end
    object UnitLabel: TLabel
      Left = 344
      Top = 9
      Width = 45
      Height = 13
      Caption = 'UnitLabel'
    end
    object Label17: TLabel
      Left = 194
      Top = 8
      Width = 56
      Height = 13
      Caption = 'Gesamtma'#223
    end
    object GewichtDutyLabel: TLabel
      Left = 67
      Top = 33
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object Label22: TLabel
      Left = 67
      Top = 3
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object MengeEdit: TEdit
      Left = 80
      Top = 5
      Width = 81
      Height = 21
      TabOrder = 0
      Text = '0'
      OnChange = EditChange
      OnExit = MengeEditExit
      OnKeyPress = IntEditKeyPress
    end
    object MengeUpDown: TIntegerUpDown
      Left = 161
      Top = 5
      Width = 16
      Height = 21
      Associate = MengeEdit
      Max = 10000
      TabOrder = 1
      OnChangingEx = MengeUpDownChangingEx
    end
    object GewichtEdit: TEdit
      Left = 80
      Top = 35
      Width = 81
      Height = 21
      TabOrder = 2
      Text = 'GewichtEdit'
      OnChange = EditChange
      OnExit = GewichtEditExit
      OnKeyPress = FloatEditKeyPress
    end
    object UnitEdit: TEdit
      Left = 255
      Top = 6
      Width = 81
      Height = 21
      TabOrder = 3
      Text = 'UnitEdit'
      OnExit = UnitEditExit
      OnKeyPress = FloatEditKeyPress
    end
  end
  object MHDPanel: TPanel
    Left = 0
    Top = 257
    Width = 432
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 7
    object MHDLabel: TLabel
      Left = 8
      Top = 14
      Width = 25
      Height = 13
      Caption = 'MHD'
    end
    object Label15: TLabel
      Left = 185
      Top = 13
      Width = 64
      Height = 13
      Alignment = taRightJustify
      Caption = 'Herstelldatum'
    end
    object MHDDutyLabel: TLabel
      Left = 67
      Top = 7
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object MHDEdit: TEdit
      Left = 80
      Top = 10
      Width = 81
      Height = 21
      TabOrder = 0
      Text = 'MHDEdit'
      OnChange = EditChange
      OnExit = MHDEditExit
      OnKeyPress = MHDEditKeyPress
    end
    object ProdDatumEdit: TEdit
      Left = 255
      Top = 10
      Width = 81
      Height = 21
      TabOrder = 1
      Text = 'ProdDatumEdit'
      OnChange = EditChange
      OnExit = ProdDatumEditExit
      OnKeyPress = MHDEditKeyPress
    end
  end
  object ChargePanel: TPanel
    Left = 0
    Top = 288
    Width = 432
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 8
    object Label10: TLabel
      Left = 8
      Top = 13
      Width = 34
      Height = 13
      Caption = 'Charge'
    end
    object ChargeDutyLabel: TLabel
      Left = 67
      Top = 6
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object ChargeEdit: TEdit
      Left = 80
      Top = 10
      Width = 121
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'ChargeEdit'
      OnChange = EditChange
    end
    object CreateChargeButton: TButton
      Left = 232
      Top = 10
      Width = 190
      Height = 21
      Caption = 'Neue Chargennummer erzeugen'
      TabOrder = 1
      OnClick = CreateChargeButtonClick
    end
  end
  object TrennPanel: TPanel
    Left = 0
    Top = 412
    Width = 432
    Height = 10
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 13
    DesignSize = (
      432
      10)
    object Bevel6: TBevel
      Left = 5
      Top = 7
      Width = 422
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
  end
  object GrundPanel: TPanel
    Left = 0
    Top = 422
    Width = 432
    Height = 51
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 12
    DesignSize = (
      432
      51)
    object Label13: TLabel
      Left = 8
      Top = 4
      Width = 134
      Height = 13
      Caption = 'Grund der manuellen Anlage'
    end
    object Bevel1: TBevel
      Left = 4
      Top = 48
      Width = 422
      Height = 9
      Anchors = [akLeft, akRight]
      Shape = bsTopLine
      ExplicitTop = 51
    end
    object GrundComboBox: TComboBox
      Left = 8
      Top = 20
      Width = 417
      Height = 21
      Hint = 'Grund der manuellen Anlage'
      Style = csDropDownList
      MaxLength = 64
      TabOrder = 0
      OnChange = GrundComboBoxChange
    end
  end
  object LEPanel: TPanel
    Left = 0
    Top = 473
    Width = 432
    Height = 93
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 14
    DesignSize = (
      432
      93)
    object Label2: TLabel
      Left = 8
      Top = 4
      Width = 110
      Height = 13
      Caption = 'Ladungstr'#228'ger-Nummer'
    end
    object Label7: TLabel
      Left = 232
      Top = 4
      Width = 84
      Height = 13
      Caption = 'Ladungstr'#228'ger-Art'
    end
    object Bevel4: TBevel
      Left = 5
      Top = 89
      Width = 417
      Height = 9
      Anchors = [akLeft, akBottom]
      Shape = bsTopLine
      ExplicitTop = 85
    end
    object Label16: TLabel
      Left = 8
      Top = 46
      Width = 39
      Height = 13
      Caption = 'NVE-Nr.'
    end
    object Label20: TLabel
      Left = 168
      Top = 4
      Width = 42
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'LE H'#246'he'
    end
    object Label21: TLabel
      Left = 204
      Top = 24
      Width = 16
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'mm'
    end
    object Label23: TLabel
      Left = 232
      Top = 46
      Width = 67
      Height = 13
      Alignment = taRightJustify
      Caption = 'HU-Nr. im WE'
    end
    object LENrEdit: TEdit
      Left = 8
      Top = 20
      Width = 121
      Height = 21
      MaxLength = 9
      TabOrder = 0
      Text = 'LENrEdit'
      OnChange = LENrEditChange
    end
    object CreateLENrButton: TButton
      Left = 130
      Top = 20
      Width = 25
      Height = 21
      Caption = '...'
      TabOrder = 1
      OnClick = CreateLENrButtonClick
    end
    object LTComboBox: TComboBox
      Left = 232
      Top = 20
      Width = 193
      Height = 21
      Style = csDropDownList
      TabOrder = 3
      OnChange = LTComboBoxChange
    end
    object NVENrEdit: TEdit
      Left = 8
      Top = 62
      Width = 121
      Height = 21
      MaxLength = 18
      TabOrder = 4
      Text = 'NVENrEdit'
      OnExit = NVENrEditExit
      OnKeyPress = NVENrEditKeyPress
    end
    object PalHeightEdit: TEdit
      Left = 168
      Top = 20
      Width = 33
      Height = 21
      Anchors = [akLeft, akBottom]
      MaxLength = 4
      TabOrder = 2
      Text = '1'
      OnChange = EditChange
      OnKeyPress = IntEditKeyPress
    end
    object HUNrEdit: TEdit
      Left = 232
      Top = 62
      Width = 121
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 5
      Text = 'HUNrEdit'
    end
  end
  object EinlagerPanel: TPanel
    Left = 0
    Top = 566
    Width = 432
    Height = 207
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 15
    DesignSize = (
      432
      207)
    object Label3: TLabel
      Left = 8
      Top = 4
      Width = 62
      Height = 13
      Caption = 'Lagerbereich'
    end
    object Label4: TLabel
      Left = 8
      Top = 48
      Width = 49
      Height = 13
      Caption = 'Lagerplatz'
    end
    object Bevel2: TBevel
      Left = 5
      Top = 198
      Width = 422
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object LBComboBox: TComboBoxPro
      Left = 8
      Top = 20
      Width = 417
      Height = 22
      Style = csOwnerDrawFixed
      Enabled = False
      TabOrder = 0
      OnChange = LBComboBoxChange
    end
    object LPListBox: TListBox
      Left = 7
      Top = 64
      Width = 415
      Height = 97
      Style = lbOwnerDrawFixed
      Enabled = False
      ItemHeight = 13
      PopupMenu = LPListBoxPopupMenu
      TabOrder = 1
      OnClick = LPListBoxClick
      OnDrawItem = LPListBoxDrawItem
    end
    object LPFreeCheckBox: TCheckBox
      Left = 8
      Top = 170
      Width = 209
      Height = 17
      Caption = 'Nur freie Lagerpl'#228'tze anzeigen'
      Checked = True
      Enabled = False
      State = cbChecked
      TabOrder = 2
      OnClick = LPFreeCheckBoxClick
    end
  end
  object BestandIDPanel: TPanel
    Left = 0
    Top = 319
    Width = 432
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 9
    object Label18: TLabel
      Left = 8
      Top = 13
      Width = 53
      Height = 13
      Caption = 'Bestand-ID'
    end
    object IDDutyLabel: TLabel
      Left = 67
      Top = 6
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object BestandIDEdit: TEdit
      Left = 80
      Top = 10
      Width = 121
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'BestandIDEdit'
      OnChange = EditChange
    end
  end
  object VarPanel: TPanel
    Left = 0
    Top = 139
    Width = 432
    Height = 62
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      432
      62)
    object Label11: TLabel
      Left = 8
      Top = 9
      Width = 54
      Height = 13
      Caption = 'Ausf'#252'hrung'
    end
    object Bevel3: TBevel
      Left = 5
      Top = 53
      Width = 422
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 96
    end
    object VarianteEdit: TEdit
      Left = 80
      Top = 6
      Width = 345
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 128
      TabOrder = 0
      Text = 'VarianteEdit'
      OnChange = EditChange
    end
    object ListedCheckBox: TCheckBox
      Left = 79
      Top = 31
      Width = 343
      Height = 17
      Caption = 'nicht gelistete Artikel auch auff'#252'hren'
      TabOrder = 1
      OnClick = ListedCheckBoxClick
    end
  end
  object EANPanel: TPanel
    Left = 0
    Top = 101
    Width = 432
    Height = 38
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    Visible = False
    DesignSize = (
      432
      38)
    object Label19: TLabel
      Left = 8
      Top = 11
      Width = 22
      Height = 13
      Caption = 'EAN'
    end
    object EANEdit: TEdit
      Left = 80
      Top = 8
      Width = 98
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'EANEdit'
      OnExit = ArNrEditExit
    end
    object EANComboBox: TComboBoxPro
      Left = 184
      Top = 8
      Width = 241
      Height = 21
      ItemDelimiter = #9
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 1
      OnChange = ArtikelComboBoxChange
      OnCloseUp = EANComboBoxCloseUp
      OnDropDown = ArtikelComboBoxDropDown
    end
  end
  object SerialPanel: TPanel
    Left = 0
    Top = 381
    Width = 432
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 11
    object Label24: TLabel
      Left = 8
      Top = 13
      Width = 47
      Height = 13
      Caption = 'Serien-Nr.'
    end
    object SerialDutyLabel: TLabel
      Left = 67
      Top = 6
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object SerialEdit: TEdit
      Left = 80
      Top = 10
      Width = 121
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'SerialEdit'
      OnChange = EditChange
    end
  end
  object ProjectPanel: TPanel
    Left = 0
    Top = 350
    Width = 432
    Height = 31
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 10
    DesignSize = (
      432
      31)
    object Label25: TLabel
      Left = 8
      Top = 13
      Width = 47
      Height = 13
      Caption = 'Projekt-ID'
    end
    object ProjectIDEdit: TEdit
      Left = 80
      Top = 10
      Width = 121
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 0
      Text = 'ProjectIDEdit'
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 392
    Top = 216
  end
  object LPListBoxPopupMenu: TPopupMenu
    Left = 288
    Top = 544
    object FindLPMenuItem: TMenuItem
      Caption = 'Suchen...'
      ShortCut = 16454
      OnClick = FindLPMenuItemClick
    end
  end
end
