unit HACCPDaten;

interface

type
  TLeergut=record
    LTArt   : String;
    RefLT   : Integer;
    Zustand : String;
    Menge   : Integer;
  end;

  TLeergutFeld = array [0..31] of TLeergut;

  THACCPTest = record
    TestRef   : Integer;
    ResultArt : String;
    ResultObj : TObject;
    Default   : String;
    ErgebnisIndex : Integer;
    Ergebnis  : String;
    AddText   : String;
    Optional  : Boolean;
  end;

implementation

end.
