unit AnalyseArtikelDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, BetterADODataSet, Grids, DBGrids, SMDBGrid, DBGridPro,
  Menus, StdCtrls;

type
  TAnalyseArtikelForm = class(TForm)
    ArtikelEinheitDBGrid: TDBGridPro;
    ArtikelDataSource: TDataSource;
    ARDataSet: TBetterADODataSet;
    CloseButton: TButton;
    GridLabel: TLabel;
    ArtikelEinheitDBGridPopupMenu: TPopupMenu;
    Artikeleinhietbearbeiten1: TMenuItem;
    Artikeleinhietdeaktivieren1: TMenuItem;
    N1: TMenuItem;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure Artikeleinhietbearbeiten1Click(Sender: TObject);
    procedure Artikeleinhietdeaktivieren1Click(Sender: TObject);
  private
    fColEditPageIdx : Integer;
    fAeEditPageIdx  : Integer;

    fRefMand    : Integer;
    fRefSubMand : Integer;

    procedure UpdateQuery (Sender: TObject);
  public
    property RefMand    : Integer read fRefMand    write fRefMand;
    property RefSubMand : Integer read fRefSubMand write fRefSubMand;
  end;

implementation

{$R *.dfm}

uses
  DatenModul, DBGridUtilModule, ConfigModul, LVSDatenInterface, LVSArtikelInterface, FrontendUtils, ResourceText,
  EditArtikelEinheitDLG, EditColliEinheitDLG;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAnalyseArtikelForm.UpdateQuery (Sender: TObject);
begin
  ARDataSet.Close;

  ARDataSet.CommandText := 'select * from V_ARTIKEL_LISTE where STATUS=''AKT'' and REF_AR_EINHEIT in (select ae.REF from VQ_ARTIKEL_EINHEIT ae, VQ_ARTIKEL ar where ar.REF=ae.REF_AR and (select COUNT (aec.REF) from VQ_ARTIKEL_EINHEIT aec, VQ_ARTIKEL arc';
  ARDataSet.CommandText := ARDataSet.CommandText + ' where arc.REF=aec.REF_AR and arc.REF_MAND=ar.REF_MAND and nvl(arc.REF_SUB_MAND, -1)=nvl(ar.REF_SUB_MAND, -1) and aec.STATUS=''AKT'' and aec.EAN=ae.EAN) > 1)';

  if (fRefSubMand > 0) then begin
    ARDataSet.CommandText := ARDataSet.CommandText + ' and REF_SUB_MAND=:ref_sub_mand';
    ARDataSet.Parameters.ParamByName('ref_sub_mand').Value := fRefSubMand;
  end else if (fRefMand > 0) then begin
    ARDataSet.CommandText := ARDataSet.CommandText + ' and REF_MAND=:ref_mand and (REF_SUB_MAND is null or REF_SUB_MAND in (select REF_MAND from V_PCD_MANDANT_REL_LOCATION where REF_LOCATION=:ref_loc))';
    ARDataSet.Parameters.ParamByName('ref_mand').Value := fRefMand;
    ARDataset.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
  end;

  ARDataSet.Open;

  ArtikelEinheitDBGrid.SetColumnVisible ('OPT_*', False);

  ArtikelEinheitDBGrid.SetColumnVisible ('MANDANT', not (LVSDatenModul.AktMandantRef > 0));

  if not (LVSConfigModul.UseMHD) then begin
    ArtikelEinheitDBGrid.SetColumnVisible ('RLZ_WE', False);
    ArtikelEinheitDBGrid.SetColumnVisible ('RLZ_WA', False);
    ArtikelEinheitDBGrid.SetColumnVisible ('RLZ_KOMM', False);
  end;

  DBGridUtils.SetGewichtDisplayFunctions (ARDataSet, 'NETTO_GEWICHT', true);
  DBGridUtils.SetGewichtDisplayFunctions (ARDataSet, 'BRUTTO_GEWICHT', true);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAnalyseArtikelForm.Artikeleinhietbearbeiten1Click(Sender: TObject);
var
  ref         : Integer;
  showres     : Integer;
  aeeditform  : TEditArtikelEinheitenForm;
  coleditform : TEditColliEinheitenForm;
begin
  if ARDataSet.Active and (ARDataSet.RecNo > 0) then begin
    ref := ARDataSet.FieldByName ('REF_AR_EINHEIT').AsInteger;

    if (ref > 0) then begin
      if (ARDataSet.FieldByName ('OPT_IS_COLLI').AsString = '1') then begin
        coleditform := TEditColliEinheitenForm.Create (Self);

        if (fColEditPageIdx <> -1) then
          coleditform.PageControl1.ActivePageIndex := fColEditPageIdx
        else coleditform.PageControl1.ActivePage  := coleditform.PageControl1.FindNextPage (Nil, True, True);

        coleditform.Caption := GetResourceText (1172);
        coleditform.Prepare (ref, ARDataSet.FieldByName('REF_MASTER_AR_EINHEIT').AsInteger);

        showres := coleditform.ShowModal;

        fColEditPageIdx := coleditform.PageControl1.ActivePageIndex;

        coleditform.Free;
      end else begin
        aeeditform := TEditArtikelEinheitenForm.Create (Self);

        if (fAeEditPageIdx <> -1) then
          aeeditform.PageControl1.ActivePageIndex := fAeEditPageIdx
        else aeeditform.PageControl1.ActivePage  := aeeditform.PageControl1.FindNextPage (Nil, True, True);

        if not (LVSConfigModul.UseGewichtsWare) then
          aeeditform.GewichtCheckBox.Visible := False;

        if not (LVSConfigModul.UseArtikelUnit) then
          aeeditform.UnitPanel.Visible := False;

        aeeditform.Caption := GetResourceText (1173);
        aeeditform.Prepare (ref, ARDataSet.FieldByName('REF_MAND').AsInteger, ARDataSet.FieldByName('REF').AsInteger);

        showres := aeeditform.ShowModal;

        fAeEditPageIdx := aeeditform.PageControl1.ActivePageIndex;

        aeeditform.Free;
      end;

      if (showres = mrOk) then
        UpdateQuery (Sender);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 20.05.2016
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAnalyseArtikelForm.Artikeleinhietdeaktivieren1Click(Sender: TObject);
var
  ref,
  res        : Integer;
  dlgres     : Integer;
begin
  if ARDataSet.Active and (ARDataSet.RecNo > 0) then begin
    ref := ARDataSet.FieldByName ('REF_AR_EINHEIT').AsInteger;

    if (ref > 0) then begin
      if (ARDataSet.FieldByName ('OPT_IS_COLLI').AsString = '1') then
        dlgres := MessageDLG ('Diese Collieinheit wirklich deaktivieren?', mtConfirmation, [mbYes, mbNo], 0)
      else dlgres := MessageDLG ('Diese Verpackungseinheit wirklich deaktivieren?', mtConfirmation, [mbYes, mbNo], 0);

      if (dlgres = mrYes) then begin
        res := ArtikelVPEDeaktivieren (ref);

        if (res <> 0) then
          MessageDLG ('Fehler beim Deaktivieren der Verpackungseinheit'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
        else begin
          UpdateQuery (Sender);
        end;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAnalyseArtikelForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  ARDataSet.Close;

  if Assigned (DBGridUtils) Then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAnalyseArtikelForm.FormCreate(Sender: TObject);
begin
  fColEditPageIdx := -1;
  fAeEditPageIdx  := -1;

  fRefMand    := -1;
  fRefSubMand := -1;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  DBGridUtils.DisableArtikelstammMenuItem (ArtikelEinheitDBGridPopupMenu);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TAnalyseArtikelForm.FormShow(Sender: TObject);
begin
  LVSConfigModul.RestoreFormInfo (Self);

  UpdateQuery (Sender);
end;

end.
