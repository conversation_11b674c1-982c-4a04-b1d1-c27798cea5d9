﻿unit SpeditionenDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, StdCtrls, ExtCtrls, Grids, DBGrids, SMDBGrid, DBGridPro,
  ComCtrls, Menus, ACOList, FrontendImageModule;

type
  TSpeditionenForm = class(TForm)
    CloseButton: TButton;
    PageControl1: TPageControl;
    TabSheet1: TTabSheet;
    Speditionen: TTabSheet;
    NewSpedButton: TButton;
    DelSpedButton: TButton;
    EditSpedButton: TButton;
    ShowDelSpedCheckBox: TCheckBox;
    SpedADOQuery: TADOQuery;
    SpedDataSource: TDataSource;
    TourDBGrid: TDBGridPro;
    NewTourButton: TButton;
    DelTourButton: TButton;
    EditTourButton: TButton;
    ShowDelTourCheckBox: TCheckBox;
    TourADOQuery: TADOQuery;
    TourDataSource: TDataSource;
    SpedDBGridPopupMenu: TPopupMenu;
    SpedDFUEClose: TMenuItem;
    RoutingTabelleimportieren1: TMenuItem;
    N1: TMenuItem;
    ACOListForm1: TACOListForm;
    N2: TMenuItem;
    SpedExportSendungMenuItem: TMenuItem;
    N3: TMenuItem;
    SpedPrintNVEListeMenuItem: TMenuItem;
    N4: TMenuItem;
    SpedForcastMenuItem: TMenuItem;
    SpedImportSendungMenuItem: TMenuItem;
    N5: TMenuItem;
    EditSpedGatewayMenuItem: TMenuItem;
    SpedDBGrid: TDBGridPro;
    N6: TMenuItem;
    EditVersandConfigMenuItem: TMenuItem;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure EditSpedButtonClick(Sender: TObject);
    procedure NewSpedButtonClick(Sender: TObject);
    procedure SpedDataSourceDataChange(Sender: TObject; Field: TField);
    procedure DelSpedButtonClick(Sender: TObject);
    procedure ShowDelSpedCheckBoxClick(Sender: TObject);
    procedure DBGridDrawColumnCell(Sender: TObject; const Rect: TRect;
      DataCol: Integer; Column: TColumn; State: TGridDrawState);
    procedure TabSheet1Show(Sender: TObject);
    procedure SpeditionenShow(Sender: TObject);
    procedure TourDataSourceDataChange(Sender: TObject; Field: TField);
    procedure ShowDelTourCheckBoxClick(Sender: TObject);
    procedure EditTourButtonClick(Sender: TObject);
    procedure DelTourButtonClick(Sender: TObject);
    procedure SpedDFUECloseClick(Sender: TObject);
    procedure SpedDBGridPopupMenuPopup(Sender: TObject);
    procedure RoutingTabelleimportieren1Click(Sender: TObject);
    procedure FormActivate(Sender: TObject);
    procedure SpedExportSendungMenuItemClick(Sender: TObject);
    procedure SpedPrintNVEListeMenuItemClick(Sender: TObject);
    procedure SpedForcastMenuItemClick(Sender: TObject);
    procedure TourDBGridDblClick(Sender: TObject);
    procedure SpedDBGridDblClick(Sender: TObject);
    procedure SpedImportSendungMenuItemClick(Sender: TObject);
    procedure EditSpedGatewayMenuItemClick(Sender: TObject);
    procedure EditVersandConfigMenuItemClick(Sender: TObject);
  private
    fEditTab : Integer; //Gibt an, welcher Tab zuletzt im EditForm ausgewählt war

    function UpdateSpedQuery : Integer;
    function UpdateTourQuery : Integer;
  public
  end;

implementation

{$R *.dfm}

uses ADOInt,
     VCLUtilitys, StrUtils, StringUtils, DatenModul, FrontendACOModul, ConfigModul, DBGridUtilModule, EditSpeditionDLG,
     FrontendUtils, LVSSpedInterface, EditSpedTourDLG, LVSWarenausgang, LVSDatenInterface,
     PrintModul, PrinterUtils, VersandAbwicklung, SelectImportDLG, LVSImport, SpedSendungExportDLG,
     BerichtDatumDLG, PrintVersandProto, ShowSpedAvisDLG, MessageExDLG, LVSExport,
     SprachModul, ResourceText, SpedGatewayDLG, ErrorTracking, SpedSendConfigDLG;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.NewSpedButtonClick(Sender: TObject);
var
  res,
  ref       : Integer;
  optstr,
  lblstr,
  dfuestr,
  chgoptstr : String;
  editform  : TEditSpeditionForm;
begin
  editform := TEditSpeditionForm.Create (Self);

  editform.Prepare (-1);

  if (Length (LVSDatenModul.AktMandant) = 0) Then begin
    editform.MandComboBox.Enabled := True;
    editform.MandComboBox.ItemIndex := -1
  end else begin
    editform.MandComboBox.Enabled := False;
    editform.MandComboBox.ItemIndex := editform.MandComboBox.IndexOf (LVSDatenModul.AktMandant);
  end;
  editform.MandComboBoxChange (editform.MandComboBox);

  if (Length (LVSDatenModul.AktLager) = 0) Then begin
    editform.LagerComboBox.ItemIndex := 0
  end else begin
    editform.LagerComboBox.ItemIndex := editform.LagerComboBox.IndexOf (LVSDatenModul.AktLager);
    editform.LagerComboBoxChange (editform.LagerComboBox);
  end;

  editform.Caption := GetResourceText (1410);
  editform.OkButton.Caption := GetResourceText (1060);

  if (editform.ShowModal = mrOk) then begin
    chgoptstr := '1111';

    if not (editform.EmpfLabelCheckBox.Checked) then
      optstr := '00'
    else if (editform.EmpfLabelLimitCheckBox.Checked) then
      optstr := '11'
    else
      optstr := '10';

    if (editform.DFUEComboBox.ItemIndex = 0) then
      dfuestr := ''
    else
      dfuestr := GetComboBoxDBItemWert (editform.DFUEComboBox);

    if (editform.LabelArtComboBox.ItemIndex = 0) then
      lblstr := ''
    else
      lblstr := GetComboBoxDBItemWert (editform.LabelArtComboBox);

    res := CreateSpedition (LVSDatenModul.AktLocationRef,
                            GetComboboxRef (editform.MandComboBox),
                            GetComboboxRef (editform.LagerComboBox),
                            editform.NummerEdit.Text,
                            editform.NameEdit.Text,
                            editform.KennungEdit.Text,
                            editform.DescEdit.Text,
                            editform.AvisMailEdit.Text,
                            GetComboboxRef (editform.VerlRelComboBox),
                            GetComboboxRef (editform.PackRelComboBox),
                            GetComboboxRef (editform.WELeerComboBox),
                            GetComboboxRef (editform.WALeerComboBox),
                            lblstr,
                            chgoptstr,
                            optstr,
                            ref);

    if (res = 0) then
      res := SetSpeditionAdresse (ref,
                                  '',
                                  editform.AdrNameEdit.Text,
                                  '',
                                  editform.AdrNameZusatzEdit.Text,
                                  editform.AdrStrasseEdit.Text,
                                  '',
                                  editform.AdrPLZEdit.Text,
                                  editform.AdrOrtEdit.Text,
                                  editform.AdrLandEdit.Text,
                                  editform.AdrFonEdit.Text,
                                  editform.AdrFaxEdit.Text,
                                  editform.AdrContactEdit.Text,
                                  editform.AdrMailEdit.Text);

    if (res = 0) then begin
      optstr := ';OPT_DATUM_FILTER:';

      if (editform.PlanDeliveryDateCheckBox.Checked) then
        optstr := optstr + '1'
      else optstr := optstr + '0';

      if editform.SendungNrCheckBox.Enabled and editform.SendungNrCheckBox.Checked then
        optstr := optstr + ';SENDUNGS_NR;OPT_ENTER_SHIPMEND_NO';

      if editform.SendungNrPflichtCheckBox.Checked then
        optstr := optstr + ';PFLICH_SENDUNGS_NR;OPT_SENDUNGS_NR';

      if editform.DimPflichtCheckBox.Checked then
        optstr := optstr + ';OPT_DIM_REQUIRED';

      if editform.VerlCloseInfoCheckBox.Visible then begin
        if editform.VerlInfoPflichCheckBox.Enabled and editform.VerlInfoPflichCheckBox.Checked then
          optstr := optstr + ';OPT_VERLADE_INFOS:2'
        else if editform.VerlCloseInfoCheckBox.Checked then
          optstr := optstr + ';OPT_VERLADE_INFOS:1';
      end;

      if not editform.BrokerCheckBox.Visible then
        optstr := optstr + ';~OPT_BROKER'
      else if editform.BrokerCheckBox.Checked then
        optstr := optstr + ';OPT_BROKER';

      if not editform.PresetDimCheckBox.Visible then
        optstr := optstr + ';~PRE_DIM'
      else if editform.PresetDimCheckBox.Checked then
        optstr := optstr + ';PRE_DIM';

      if not editform.PreSetWeightCheckBox.Visible then
        optstr := optstr + ';~PRE_WEIGHT'
      else if editform.PreSetWeightCheckBox.Checked then
        optstr := optstr + ';PRE_WEIGHT';

      if not editform.EDIDimCheckBox.Visible then
        optstr := optstr + ';~EDI_DIM'
      else if editform.EDIDimCheckBox.Checked then
        optstr := optstr + ';EDI_DIM';

      if not editform.EDIWeightCheckBox.Visible then
        optstr := optstr + ';~EDI_WEIGHT'
      else if editform.EDIWeightCheckBox.Checked then
        optstr := optstr + ';EDI_WEIGHT';

      res := ChangeSpeditionConfig (ref, GetComboBoxRef (editform.DefaultLTComboBox), editform.LogPlatformEdit.Text, optstr);
    end;

    if (res = 0) and (editform.DepotComboBox.Visible) then begin
      optstr := '0';

      if (editform.DepotPflichtCheckBox.Checked) then
        optstr := '1';

      res := SetSpeditionAnlieferDepot (ref, GetComboBoxRef (editform.DepotComboBox), optstr);
    end;

    if (res = 0) and (editform.VersandArtEdit.Visible) then begin
      res := SetSpeditionVersandart (ref, editform.VersandArtEdit.Text);
    end;

    if (res = 0) then
      res := SetIFTMINDaten (ref,
                             editform.DFUEKennungEdit.Text,
                             editform.ILNEdit.Text,
                             editform.AccountNrEdit.Text,
                             editform.DepotEdit.Text,
                             dfuestr);

    if (res <> 0) then
      MessageDLG (FormatMessageText (1171, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    else
      SpedDBGrid.Reload (ref);
  end;

  editform.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.03.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.RoutingTabelleimportieren1Click(Sender: TObject);
var
  res      : Integer;
  errstr,
  protostr : String;
  selform  : TSelectImportForm;
begin
  if SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1) and (SpedADOQuery.FieldByName ('STATUS').AsString <> 'DEL') then begin
    selform := TSelectImportForm.Create (Self);

    selform.Prepare ('SPED_ROUTING', SpedADOQuery.FieldByName ('REF_MAND').AsInteger, -1, SpedADOQuery.FieldByName ('REF_LAGER').AsInteger);

    if (selform.ShowModal = mrOk) then begin
      res := ImportSpedRoutingCSV (Self, SpedADOQuery.FieldByName('REF').AsInteger, selform.FileNameEdit.Text, GetComboBoxRef (selform.FormatComboBox), errstr, protostr);

      if (res <> 0) then
        MessageDLG ('Fehler beim Importieren der Routingdaten' + #13 + #13 + errstr, mtError, [mbOK], 0)
      else begin
        if (Length (protostr) > 0) then
          MessageDLG ('Warnungen beim Importieren der Routingdaten' + #13 + #13 + protostr, mtInformation, [mbOK], 0);
      end;
    end;

    selform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.ShowDelSpedCheckBoxClick(Sender: TObject);
begin
  UpdateSpedQuery;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.ShowDelTourCheckBoxClick(Sender: TObject);
begin
  UpdateTourQuery;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.SpedDataSourceDataChange(Sender: TObject; Field: TField);
begin
  if (LVSDatenModul.DatabaseVersion > 35) then begin
    EditSpedButton.Enabled := SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1) and (SpedADOQuery.FieldByName ('STATUS').AsString <> 'DEL');
    DelSpedButton.Enabled  := SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1) and (SpedADOQuery.FieldByName ('STATUS').AsString <> 'DEL');
  end else begin
    EditSpedButton.Enabled := SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1);
    DelSpedButton.Enabled  := SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//  Datum        : 21.08.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.SpedDBGridDblClick(Sender: TObject);
begin
  //Prüfen, ob Änderungen erlaubt sind
  if EditSpedButton.Visible and EditSpedButton.Enabled then
    EditSpedButtonClick (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.SpedDBGridPopupMenuPopup(Sender: TObject);
begin
  SpedDFUEClose.Enabled             := False;
  SpedExportSendungMenuItem.Enabled := False;
  SpedPrintNVEListeMenuItem.Enabled := False;
  SpedForcastMenuItem.Enabled       := False;

  if SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1) and (SpedADOQuery.FieldByName ('STATUS').AsString <> 'DEL') then begin
    SpedPrintNVEListeMenuItem.Enabled := True;

    SpedDFUEClose.Enabled := True;

    SpedExportSendungMenuItem.Enabled := (copy (SpedADOQuery.FieldByName ('DFUE_ART').AsString,1,7) = 'EXPORT_');

    if Assigned (SpedADOQuery.FindField ('AVIS_ART')) then
      SpedForcastMenuItem.Enabled := not (SpedADOQuery.FieldByName ('AVIS_ART').IsNull);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.SpedDFUECloseClick(Sender: TObject);
var
  res,
  idx,
  refab    : Integer;
  fpath,
  fbase,
  fname,
  errtxt,
  portstr,
  namestr,
  spedname,
  accountstr  : String;
  prtinfo     : TPrinterPorts;
  cname       : array [0..MAX_COMPUTERNAME_LENGTH] of char;
  csize       : DWORD;
  prtmodul    : TPrintVersandProtoModule;
  berichtform : TBerichtDatumForm;
  query       : TADOQuery;
begin
  if SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1) and (SpedADOQuery.FieldByName ('STATUS').AsString <> 'DEL') then begin
    if (copy (SpedADOQuery.FieldByName ('DFUE_ART').AsString, 1, 6) = 'SENDIT') then begin
      CheckConfigParameter (SpedADOQuery.FieldByName('REF_MAND').AsInteger, LVSDatenModul.AktLocationRef, -1, 'SENDIT_LEITSTAND', namestr);

      if (Length (namestr) > 0) then
        prtinfo.Leitstand := namestr
      else begin
        portstr := PrintModule.StdLaserPrinter.Port;

        if (Pos ('\\', portstr) = 0) then begin
          csize := sizeof (cname) - 1;
          GetComputerName (cname, csize);
          cname [csize] := #0;

          portstr := '\\'+StrPas (cname)+'\' + portstr;
        end;

        res := PrintModule.LoadPrinterPort (-1, portstr, prtinfo);
      end;

      if (Length (prtinfo.Leitstand) = 0) then
        MessageDlg(FormatMessageText (1172, []), mtError, [mbOK], 0)
      else begin
        res := CloseVersandDFUE (Self, SpedADOQuery.FieldByName ('REF').AsInteger, prtinfo.Port, prtinfo.Leitstand, errtxt);

        if ((res <> 0) or (Length (errtxt) > 0)) then begin
          MessageDlg (FormatMessageText (1173, [errtxt, IntToStr (res)]), mtError, [mbOK], 0);

          ErrorTrackingModule.WriteErrorLog ('Tagesabschluss: '+SpedADOQuery.FieldByName ('NAME').AsString,errtxt);
        end;
      end;
    end else if (SpedADOQuery.FieldByName ('DFUE_ART').AsString = 'GLS-IT-WEB') then begin
      res := OpenSpedTagesabschluss (SpedADOQuery.FieldByName ('REF').AsInteger, '', 'GLS-IT-WEB', '', refab);

      if (res <> 0) then
        errtxt := LVSDatenModul.LastLVSErrorText
      else begin
        res := CloseWorkDay (Self, SpedADOQuery.FieldByName ('REF').AsInteger, refab, errtxt);

        res := CloseSpedTagesabschluss (refab, '', '');
      end;

      if ((res <> 0) or (Length (errtxt) > 0)) then
        MessageDlg (FormatMessageText (1173, [errtxt, IntToStr (res)]), mtError, [mbOK], 0);
    end else if (SpedADOQuery.FieldByName ('DFUE_ART').AsString = 'EXPORT_CSV_DHL') then begin
      berichtform := TBerichtDatumForm.Create (Self);

      try
        berichtform.DataRef := SpedADOQuery.FieldByName ('REF').AsInteger;
        berichtform.DataRefStr := 'REF_SPED';

        berichtform.MandantPanel.Visible := False;
        berichtform.LagerPanel.Visible := False;
        berichtform.AlleLagerErlaubt := False;

        berichtform.DatumPanel.Visible := True;
        berichtform.DatumDateTimePicker.DateTime := Trunc (Now);
        berichtform.BisDatumDateTimePicker.Visible := FALSE;

        berichtform.BerichtName := GetResourceText (1413);

        berichtform.DatumLabel.Caption := GetResourceText (1414);
        berichtform.ShowButton.Caption := GetResourceText (1415);

        berichtform.CheckBox1.Visible := true;
        berichtform.CheckBox1.Caption := 'Nur abgeschlossene Aufträge exportieren';
        berichtform.CheckBox1.Checked := True;

        if (berichtform.ShowModal = mrOk) then begin
          idx := -1;

          fpath := UserReg.ReadRegValue ('ExportSpedPath');
          if (Length (fpath) > 0) and (fpath [Length (fpath)] <> '\') then fpath := fpath + '\';

          fbase := SpedADOQuery.FieldByName ('NAME').AsString + '_export_'+FormatDateTime ('yyyymmdd', trunc (berichtform.DatumDateTimePicker.DateTime));

          //Den nächsten, noch nicht benutzen Dateinamen finden
          repeat
            Inc (idx);

            if (idx = 0) then
              fname := fbase + '.csv'
            else
              fname := fbase + '-' + FormatIntToStr (idx, 2) + '.csv';
          until (not FileExists (fpath + fname) or (idx > 9));

          fname := OpenExcelFile (UserReg.ReadRegValue ('ExportSpedPath'), fname, 'Sendungsexport für ' + SpedADOQuery.FieldByName ('NAME').AsString, false);

          if (Length (fname) > 0) then begin
            UserReg.WriteRegValue ('ExportSpedPath', ExtractFilePath (fname));

            res := OpenSpedTagesabschluss (SpedADOQuery.FieldByName ('REF').AsInteger, '', 'EXPORT_CSV_DHL', ExtractFileName (fname), refab);

            if (res = 0) then
              res := ExportCSVDHL (Self, berichtform.DatumDateTimePicker.DateTime, SpedADOQuery.FieldByName ('REF').AsInteger, refab, berichtform.CheckBox1.Checked, fname);
          end;
        end;
      finally
        berichtform.Release;
      end;
    end else if (SpedADOQuery.FieldByName ('DFUE_ART').AsString = 'EXPORT_CSV_DPD') then begin
      berichtform := TBerichtDatumForm.Create (Self);

      try
        berichtform.DataRef := SpedADOQuery.FieldByName ('REF').AsInteger;
        berichtform.DataRefStr := 'REF_SPED';

        berichtform.MandantPanel.Visible := False;
        berichtform.LagerPanel.Visible := False;
        berichtform.AlleLagerErlaubt := False;

        berichtform.DatumPanel.Visible := True;
        berichtform.DatumDateTimePicker.DateTime := Trunc (Now);
        berichtform.BisDatumDateTimePicker.Visible := FALSE;

        berichtform.BerichtName := GetResourceText (1413);

        berichtform.DatumLabel.Caption := GetResourceText (1414);
        berichtform.ShowButton.Caption := GetResourceText (1415);

        if (berichtform.ShowModal = mrOk) then begin
          idx := -1;

          fpath := UserReg.ReadRegValue ('ExportSpedPath');
          if (Length (fpath) > 0) and (fpath [Length (fpath)] <> '\') then fpath := fpath + '\';

          fbase := SpedADOQuery.FieldByName ('NAME').AsString + '_export_'+FormatDateTime ('yyyymmdd', trunc (berichtform.DatumDateTimePicker.DateTime));

          //Den nächsten, noch nicht benutzen Dateinamen finden
          repeat
            Inc (idx);

            if (idx = 0) then
              fname := fbase + '.csv'
            else
              fname := fbase + '-' + FormatIntToStr (idx, 2) + '.csv';
          until (not FileExists (fpath + fname) or (idx > 9));

          fname := OpenExcelFile (UserReg.ReadRegValue ('ExportSpedPath'), fname, 'Sendungsexport für ' + SpedADOQuery.FieldByName ('NAME').AsString, false);

          if (Length (fname) > 0) then begin
            UserReg.WriteRegValue ('ExportSpedPath', ExtractFilePath (fname));

            res := OpenSpedTagesabschluss (SpedADOQuery.FieldByName ('REF').AsInteger, '', 'EXPORT_CSV_DPD', ExtractFileName (fname), refab);

            if (res = 0) then
              res := ExportCSVDPD (Self, berichtform.DatumDateTimePicker.DateTime, SpedADOQuery.FieldByName ('REF').AsInteger, refab, true, fname);
          end;
        end;
      finally
        berichtform.Release;
      end;
    end else if (SpedADOQuery.FieldByName ('DFUE_ART').AsString = 'EXPORT_CSV_DHL_EXP') then begin
      berichtform := TBerichtDatumForm.Create (Self);

      try
        berichtform.DataRef := SpedADOQuery.FieldByName ('REF').AsInteger;
        berichtform.DataRefStr := 'REF_SPED';

        berichtform.MandantPanel.Visible := False;
        berichtform.LagerPanel.Visible := False;
        berichtform.AlleLagerErlaubt := False;

        berichtform.DatumPanel.Visible := True;
        berichtform.DatumDateTimePicker.DateTime := Trunc (Now);
        berichtform.BisDatumDateTimePicker.Visible := FALSE;

        berichtform.BerichtName := GetResourceText (1413);

        berichtform.DatumLabel.Caption := GetResourceText (1414);
        berichtform.ShowButton.Caption := GetResourceText (1415);

        berichtform.CheckBox1.Visible := true;
        berichtform.CheckBox1.Caption := 'Nur abgeschlossene Aufträge exportieren';
        berichtform.CheckBox1.Checked := True;

        if (berichtform.ShowModal = mrOk) then begin
          idx := -1;

          fpath := UserReg.ReadRegValue ('ExportSpedPath');
          if (Length (fpath) > 0) and (fpath [Length (fpath)] <> '\') then fpath := fpath + '\';

          fbase := SpedADOQuery.FieldByName ('NAME').AsString + '_export_'+FormatDateTime ('yyyymmdd', trunc (berichtform.DatumDateTimePicker.DateTime));

          //Den nächsten, noch nicht benutzen Dateinamen finden
          repeat
            Inc (idx);

            if (idx = 0) then
              fname := fbase + '.csv'
            else
              fname := fbase + '-' + FormatIntToStr (idx, 2) + '.csv';
          until (not FileExists (fpath + fname) or (idx > 9));

          fname := OpenExcelFile (UserReg.ReadRegValue ('ExportSpedPath'), fname, 'Sendungsexport für ' + SpedADOQuery.FieldByName ('NAME').AsString, false);

          if (Length (fname) > 0) then begin
            UserReg.WriteRegValue ('ExportSpedPath', ExtractFilePath (fname));

            res := OpenSpedTagesabschluss (SpedADOQuery.FieldByName ('REF').AsInteger, '', 'EXPORT_CSV_DPD', ExtractFileName (fname), refab);

            if (res = 0) then
              res := ExportCSVDHLExpress (Self, berichtform.DatumDateTimePicker.DateTime, SpedADOQuery.FieldByName ('REF').AsInteger, refab, -1, berichtform.CheckBox1.Checked, fname);
          end;
        end;
      finally
        berichtform.Release;
      end;
    end else if (SpedADOQuery.FieldByName ('DFUE_ART').AsString = 'EXPORT_CSV_ASENDIA') then begin
      berichtform := TBerichtDatumForm.Create (Self);

      try
        berichtform.DataRef := SpedADOQuery.FieldByName ('REF').AsInteger;
        berichtform.DataRefStr := 'REF_SPED';

        berichtform.MandantPanel.Visible := False;
        berichtform.LagerPanel.Visible := False;
        berichtform.AlleLagerErlaubt := False;

        berichtform.DatumPanel.Visible := True;
        berichtform.DatumDateTimePicker.DateTime := Trunc (Now);
        berichtform.BisDatumDateTimePicker.Visible := FALSE;

        berichtform.BerichtName := GetResourceText (1413);

        berichtform.DatumLabel.Caption := GetResourceText (1414);
        berichtform.ShowButton.Caption := GetResourceText (1415);

        if (berichtform.ShowModal = mrOk) then begin
          query := TADOQuery.Create (Self);

          try
            query.Connection := LVSDatenModul.MainADOConnection;

            query.SQL.Add ('select sped.* from V_SPEDITIONEN sped where sped.REF=:ref');
            query.Parameters.ParamByName ('ref').Value := SpedADOQuery.FieldByName ('REF').AsInteger;

            query.Open;

            spedname   := query.FieldByName ('DFUE_KENNZEICHEN').AsString;
            accountstr := query.FieldByName ('ACCOUNT_NR').AsString;

            query.Close;
          finally
            query.Free;
          end;

          idx := -1;

          fpath := UserReg.ReadRegValue ('ExportSpedPath');
          if (Length (fpath) > 0) and (fpath [Length (fpath)] <> '\') then fpath := fpath + '\';

          fbase := accountstr + '_' + spedname + '_export_'+FormatDateTime ('yyyymmdd', trunc (berichtform.DatumDateTimePicker.DateTime));

          //Den nächsten, noch nicht benutzen Dateinamen finden
          repeat
            Inc (idx);

            if (idx = 0) then
              fname := fbase + '.csv'
            else
              fname := fbase + '-' + FormatIntToStr (idx, 2) + '.csv';
          until (not FileExists (fpath + fname) or (idx > 9));

          fname := OpenExcelFile (UserReg.ReadRegValue ('ExportSpedPath'), fname, 'Sendungsexport für ' + SpedADOQuery.FieldByName ('NAME').AsString, false);

          if (Length (fname) > 0) then begin
            UserReg.WriteRegValue ('ExportSpedPath', ExtractFilePath (fname));

            res := OpenSpedTagesabschluss (SpedADOQuery.FieldByName ('REF').AsInteger, '', 'EXPORT_CSV_ASENDIA', ExtractFileName (fname), refab);

            if (res = 0) then
              res := ExportCSVAsendia (Self, berichtform.DatumDateTimePicker.DateTime, SpedADOQuery.FieldByName ('REF').AsInteger, refab, fname);
          end;
        end;
      finally
        berichtform.Release;
      end;
    end else if (SpedADOQuery.FieldByName ('DFUE_ART').AsString = 'EXPORT_CSV_LIEFERY') then begin
      berichtform := TBerichtDatumForm.Create (Self);

      try
        berichtform.DataRef := SpedADOQuery.FieldByName ('REF').AsInteger;
        berichtform.DataRefStr := 'REF_SPED';

        berichtform.MandantPanel.Visible := False;
        berichtform.LagerPanel.Visible := False;
        berichtform.AlleLagerErlaubt := False;

        berichtform.DatumPanel.Visible := True;
        berichtform.DatumDateTimePicker.DateTime := Trunc (Now);
        berichtform.BisDatumDateTimePicker.Visible := FALSE;

        berichtform.SortPanel.Visible := False;
        berichtform.AuswahlPanel.Visible := False;

        berichtform.SortPanel.Visible := False;
        berichtform.AuswahlPanel.Visible := False;

        berichtform.SortPanel.Visible := True;
        berichtform.Label4.Caption := GetResourceText (1002);
        berichtform.SortMHDRadioButton.Visible := False;

        berichtform.SortARRadioButton.Visible := True;
        berichtform.SortARRadioButton.Caption := GetResourceText (1411);
        berichtform.SortARRadioButton.Checked := True;
        berichtform.SortLPRadioButton.Visible := True;
        berichtform.SortLPRadioButton.Caption := GetResourceText (1412);

        berichtform.BerichtName := GetResourceText (1413);

        berichtform.DatumLabel.Caption := GetResourceText (1414);
        berichtform.ShowButton.Caption := GetResourceText (1415);

        if (berichtform.ShowModal = mrOk) then begin
          idx := -1;

          fpath := UserReg.ReadRegValue ('ExportSpedPath');
          if (Length (fpath) > 0) and (fpath [Length (fpath)] <> '\') then fpath := fpath + '\';

          fbase := SpedADOQuery.FieldByName ('NAME').AsString + '_export_'+FormatDateTime ('yyyymmdd', trunc (berichtform.DatumDateTimePicker.DateTime));

          //Den nächsten, noch nicht benutzen Dateinamen finden
          repeat
            Inc (idx);

            if (idx = 0) then
              fname := fbase + '.csv'
            else
              fname := fbase + '-' + FormatIntToStr (idx, 2) + '.csv';
          until (not FileExists (fpath + fname) or (idx > 9));

          fname := OpenExcelFile (UserReg.ReadRegValue ('ExportSpedPath'), fname, 'Sendungsexport für ' + SpedADOQuery.FieldByName ('NAME').AsString, false);

          if (Length (fname) > 0) then begin
            UserReg.WriteRegValue ('ExportSpedPath', ExtractFilePath (fname));

            res := OpenSpedTagesabschluss (SpedADOQuery.FieldByName ('REF').AsInteger, '', 'EXPORT_CSV_LIEFERY', ExtractFileName (fname), refab);

            if (res = 0) then
              res := ExportCSVLivery (Self, berichtform.DatumDateTimePicker.DateTime, SpedADOQuery.FieldByName ('REF').AsInteger, refab, fname, berichtform.SortLPRadioButton.Checked);
          end;
        end;
      finally
        berichtform.Release;
      end;
    end else if (SpedADOQuery.FieldByName ('DFUE_ART').AsString = 'EXPORT_DHL_KURIER_API') then begin
    end else begin
      res := SetSpedSendungsDFUE (SpedADOQuery.FieldByName ('REF').AsInteger, '', refab);

      if (res <> 0) then
        MessageDlg (FormatMessageText (1173, [IntToStr (res), LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else if (refab > 0) then begin
        prtmodul := TPrintVersandProtoModule.Create (Self);

        try
          prtmodul.Ref := refab;

          prtmodul.DoPrinting;
        finally
          prtmodul.Free;
        end;
      end else begin
        {$ifdef Debug}
          prtmodul := TPrintVersandProtoModule.Create (Self);

          try
            prtmodul.Ref := 156;

            prtmodul.DoPrinting;
          finally
            prtmodul.Free;
          end;
        {$endif}
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.SpedExportSendungMenuItemClick(Sender: TObject);
var
  expform : TSpedSendungExport;
begin
  expform := TSpedSendungExport.Create (Self);

  expform.SubMandPanel.Visible := LVSConfigModul.UseSubMandanten;

  expform.Prepare (SpedADOQuery.FieldByName ('REF').AsInteger);

  expform.ShowModal;

  expform.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 28.11.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.SpedForcastMenuItemClick(Sender: TObject);
var
  res,
  ref,
  dlgres : Integer;
  optstr  : String;
  BerichtDatumForm: TBerichtDatumForm;
  avisform : TShowSpedAvisForm;
  query    : TADOQuery;
begin
  if SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1) and (SpedADOQuery.FieldByName ('STATUS').AsString <> 'DEL') then begin
    BerichtDatumForm := TBerichtDatumForm.Create (Self);

    try
      BerichtDatumForm.MandantPanel.Visible := true;
      BerichtDatumForm.LagerPanel.Visible := False;
      BerichtDatumForm.AlleLagerErlaubt := False;

      BerichtDatumForm.DatumPanel.Visible := True;
      BerichtDatumForm.BisDatumDateTimePicker.Visible := False;

      BerichtDatumForm.SortPanel.Visible := False;

      BerichtDatumForm.AuswahlPanel.Visible := False;

      BerichtDatumForm.CheckPanel.Visible := True;
      BerichtDatumForm.CheckBox1.Visible := True;
      BerichtDatumForm.CheckBox1.Caption := GetResourceText (1416);

      BerichtDatumForm.BerichtName := GetResourceText (1417);
      BerichtDatumForm.BerichtReport := '';

      BerichtDatumForm.DatumLabel.Caption := GetResourceText (1414);

      if (BerichtDatumForm.ShowModal = mrOk) then begin
        ref := -1;

        query := TADOQuery.Create (Self);

        try
          query.Connection := LVSDatenModul.MainADOConnection;

          query.SQL.Add ('select * from V_SPED_AVIS where SEND_DATUM is null and REF_MAND=:ref_mand and REF_SPEDITION=:ref_sped and VERLADE_DATUM=:datum');
          query.Parameters.ParamByName('ref_mand').Value  := GetComboBoxRef (BerichtDatumForm.MandantComboBox);
          query.Parameters.ParamByName('ref_sped').Value  := SpedADOQuery.FieldByName ('REF').AsInteger;
          query.Parameters.ParamByName('datum').ParameterObject.Type_ := adDBTimeStamp;
          query.Parameters.ParamByName('datum').Value := Trunc (BerichtDatumForm.DatumDateTimePicker.Date);

          query.Open;

          if not (query.FieldByName ('REF').IsNull) then
            ref := query.FieldByName ('REF').AsInteger;

          query.Close;
        finally
          query.Free;
        end;

        dlgres := mrNew;

        if (ref > 0) then begin
          MessageForm.Button3.Caption := 'Bestende anzeigen';
          MessageForm.Button3.ModalResult := mrOK;
          MessageForm.Button3.Visible  := True;

          MessageForm.Button1.Caption := 'Neue erzeugen';
          MessageForm.Button1.ModalResult := mrNew;
          MessageForm.Button1.Visible  := True;

          MessageForm.MessageLabel.Caption := 'Es existiert schon ein offene Avisierungsliste, wollen Sie dies bearbeiten oder eine neue erzeugen?';

          dlgres := MessageForm.ShowModal (MessageForm.Button3, 0);
        end;

        if (dlgres = mrCancel) then
          ref := -1
        else if (dlgres = mrNew) then begin
          optstr := '00000000';
          if (BerichtDatumForm.CheckBox1.Checked) then
            optstr [1] := '1';

          res := CreateSpeditionAvis (GetComboBoxRef (BerichtDatumForm.MandantComboBox), SpedADOQuery.FieldByName ('REF').AsInteger, BerichtDatumForm.DatumDateTimePicker.Date, optstr, ref);

          if (res <> 0) then
            MessageDLG ('Fehler beim Erzeugen der Avisierung'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
          else if (ref < 0) then
            MessageDLG ('Für den angegebenen Zeitraum sind keine passenden Sendungen vorhanden', mtInformation, [mbOk], 0)
        end;

        if (ref > 0)  then begin
          avisform := TShowSpedAvisForm.Create (Self);

          try
            avisform.RefAvis := ref;

            avisform.ShowModal;
          finally
            avisform.Release;
          end;
        end;
      end;
    finally
      BerichtDatumForm.Release;
    end;
  end;
end;

procedure TSpeditionenForm.SpedImportSendungMenuItemClick(Sender: TObject);
var
  res     : Integer;
  errtxt  : String;
begin
  if SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1) and (SpedADOQuery.FieldByName ('STATUS').AsString <> 'DEL') then begin
    if (copy (SpedADOQuery.FieldByName ('DFUE_ART').AsString, 1, 6) = 'SENDIT') then begin
      res := ImportVersandInfos (SpedADOQuery.FieldByName ('REF').AsInteger, 'SENDIT', errtxt);

      if (res <> 0) then
        MessageDlg(FormatMessageText (1173, [IntToStr (res), errtxt]), mtError, [mbOK], 0);
    end else if (copy (SpedADOQuery.FieldByName ('DFUE_ART').AsString, 1, 7) = 'BARSHIP') then begin
      res := ImportVersandInfos (SpedADOQuery.FieldByName ('REF').AsInteger, 'BARSHIP', errtxt);

      if (res <> 0) then
        MessageDlg(FormatMessageText (1173, [IntToStr (res), errtxt]), mtError, [mbOK], 0);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.DBGridDrawColumnCell(Sender: TObject; const Rect: TRect; DataCol: Integer; Column: TColumn; State: TGridDrawState);
var
  bm: TBitmap;
  idx: Integer;
begin
  if (PosEx ('STATUS', Column.FieldName) > 0) then begin
    if (LVSConfigModul.FrontendConfig.cfgStatIcons) then begin
      if (Column.Field.AsString = 'ANG') then
        idx := 17
      else if (Column.Field.AsString = 'DEL') then
        idx := 9
      else
        idx := -1;

      if (idx <> -1) then begin
        (Sender as TDBGridPro).Canvas.FillRect(rect);

        bm := TBitmap.Create;

        try
          if (idx = 0) then
            bm.handle := loadbitmap(hinstance, 'angelegt')
          else if (idx = 1) then
            bm.handle := loadbitmap(hinstance, 'abgeschlossen')
          else if (idx = 2) then
            bm.handle := loadbitmap(hinstance, 'fehler')
          else if (idx = 3) then
            bm.handle := loadbitmap(hinstance, 'transform')
          else if (idx = 4) then
            bm.handle := loadbitmap(hinstance, 'kommissionieren')
          else if (idx = 5) then
            bm.handle := loadbitmap(hinstance, 'warenausgang')
          else if (idx = 6) then
            bm.handle := loadbitmap(hinstance, 'interntransport')
          else if (idx = 7) then
            bm.handle := loadbitmap(hinstance, 'beendet')
          else if (idx = 8) then
            bm.handle := loadbitmap(hinstance, 'storniert')
          else if (idx = 9) then
            bm.handle := loadbitmap(hinstance, 'delete')
          else if (idx = 10) then
            bm.handle := loadbitmap(hinstance, 'reload')
          else if (idx = 11) then
            bm.handle := loadbitmap(hinstance, 'ifcerror')
          else if (idx = 12) then
            bm.handle := loadbitmap(hinstance, 'geplant')
          else if (idx = 13) then
            bm.handle := loadbitmap(hinstance, 'umpack')
          else if (idx = 14) then
            bm.handle := loadbitmap(hinstance, 'gabelstapler')
          else if (idx = 15) then
            bm.handle := loadbitmap(hinstance, 'crossdock')
          else if (idx = 16) then
            bm.handle := loadbitmap(hinstance, 'redcross')
          else if (idx = 17) then
            bm.handle := loadbitmap(hinstance, 'tick')
          else if (idx = 18) then begin
            bm.handle := loadbitmap(hinstance, 'oneway');
          end;

          DrawStatusBitmap ((Sender as TDBGridPro).Canvas, Rect, bm);
        finally
          bm.Free;
        end;
      end;
    end;
  end;
end;

procedure TSpeditionenForm.SpeditionenShow(Sender: TObject);
begin
  UpdateSpedQuery
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.TabSheet1Show(Sender: TObject);
begin
  UpdateTourQuery;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.TourDataSourceDataChange(Sender: TObject; Field: TField);
begin
  if (LVSDatenModul.DatabaseVersion > 35) then begin
    EditTourButton.Enabled := TourADOQuery.Active and (TourADOQuery.RecNo <> -1) and (TourADOQuery.FieldByName ('STATUS').AsString <> 'DEL');
    DelTourButton.Enabled  := TourADOQuery.Active and (TourADOQuery.RecNo <> -1) and (TourADOQuery.FieldByName ('STATUS').AsString <> 'DEL');
  end else begin
    EditTourButton.Enabled := TourADOQuery.Active and (TourADOQuery.RecNo <> -1);
    DelTourButton.Enabled  := TourADOQuery.Active and (TourADOQuery.RecNo <> -1);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.TourDBGridDblClick(Sender: TObject);
begin
  //Prüfen, ob Änderungen erlaubt sind
  if EditTourButton.Visible and EditTourButton.Enabled then
    EditTourButtonClick (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.DelSpedButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (MessageDLG('Die Spedition ' + SpedADOQuery.FieldByName('NAME').AsString + ' wirklich löschen?', mtConfirmation, [mbYes, mbNo], 0) = mrYes) then begin
    res := DeleteSpedition (SpedADOQuery.FieldByName('REF').AsInteger);

    if (res <> 0) then
      MessageDLG ('Fehler beim Löschen der Spedition'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
    else begin
      if not (ShowDelSpedCheckBox.Checked) then begin
        SpedDBGrid.DataSource.DataSet.Next;

        if SpedDBGrid.DataSource.DataSet.Eof then
          SpedDBGrid.DataSource.DataSet.Prior;
      end;

      SpedDBGrid.Reload;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.DelTourButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (MessageDLG('Die Tour ' + TourADOQuery.FieldByName('TOUR_NR').AsString + ' wirklich löschen?', mtConfirmation, [mbYes, mbNo], 0) = mrYes) then begin
    res := DeleteSpedTour (TourADOQuery.FieldByName('REF').AsInteger);

    if (res <> 0) then
      MessageDLG ('Fehler beim Löschen der Tour'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
    else begin
      if not (ShowDelTourCheckBox.Checked) then begin
        TourDBGrid.DataSource.DataSet.Next;

        if TourDBGrid.DataSource.DataSet.Eof then
          TourDBGrid.DataSource.DataSet.Prior;
      end;

      TourDBGrid.Reload;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.EditSpedButtonClick(Sender: TObject);
var
  res,
  minute     : Integer;
  optstr,
  lblstr,
  dfuestr,
  chgoptstr  : String;
  editform   : TEditSpeditionForm;
begin
  if SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1) and (SpedADOQuery.FieldByName ('STATUS').AsString <> 'DEL') then begin
    editform := TEditSpeditionForm.Create (Self);

    if (fEditTab <> -1) then
      editform.PageControl1.TabIndex := fEditTab;

    editform.Prepare (SpedADOQuery.FieldByName('REF').AsInteger);

    if (editform.ShowModal = mrOk) then begin
      fEditTab := editform.PageControl1.TabIndex;

      chgoptstr := SpedADOQuery.FieldByName ('CHANGE_OPTION').AsString;

      if (editform.NameEdit.Text <> SpedADOQuery.FieldByName('NAME').AsString) then
        chgoptstr := SetOpt (chgoptstr, 2);

      if (editform.DescEdit.Text <> SpedADOQuery.FieldByName('BESCHREIBUNG').AsString) then
        chgoptstr := SetOpt (chgoptstr, 3);

      if (LVSDatenModul.DatabaseVersion < 37) then
        optstr := ''
      else begin
        if not (editform.EmpfLabelCheckBox.Checked) then
          optstr := '00'
        else if (editform.EmpfLabelLimitCheckBox.Checked) then
          optstr := '11'
        else
          optstr := '10';
      end;

      if (editform.DFUEComboBox.ItemIndex = 0) then
        dfuestr := ''
      else
        dfuestr := GetComboBoxDBItemWert (editform.DFUEComboBox);

      if (editform.LabelArtComboBox.ItemIndex = 0) then
        lblstr := ''
      else
        lblstr := GetComboBoxDBItemWert (editform.LabelArtComboBox);

      res := ChangeSpedition (SpedADOQuery.FieldByName('REF').AsInteger,
                              editform.UpdCount,
                              GetComboboxRef (editform.LagerComboBox),
                              editform.NummerEdit.Text,
                              editform.NameEdit.Text,
                              editform.KennungEdit.Text,
                              editform.DescEdit.Text,
                              editform.AvisMailEdit.Text,
                              GetComboboxRef (editform.VerlRelComboBox),
                              GetComboboxRef (editform.PackRelComboBox),
                              GetComboboxRef (editform.WELeerComboBox),
                              GetComboboxRef (editform.WALeerComboBox),
                              lblstr,
                              chgoptstr,
                              optstr
                              );
      if (res = 0) then
        res := SetSpeditionAdresse (SpedADOQuery.FieldByName('REF').AsInteger,
                                    '',
                                    editform.AdrNameEdit.Text,
                                    '',
                                    editform.AdrNameZusatzEdit.Text,
                                    editform.AdrStrasseEdit.Text,
                                    '',
                                    editform.AdrPLZEdit.Text,
                                    editform.AdrOrtEdit.Text,
                                    editform.AdrLandEdit.Text,
                                    editform.AdrFonEdit.Text,
                                    editform.AdrFaxEdit.Text,
                                    editform.AdrContactEdit.Text,
                                    editform.AdrMailEdit.Text);

      if (res = 0) then begin
        optstr := ';OPT_DATUM_FILTER:';

        if (editform.PlanDeliveryDateCheckBox.Checked) then
          optstr := optstr + '1'
        else optstr := optstr + '0';

        if editform.SendungNrCheckBox.Enabled and editform.SendungNrCheckBox.Checked then
          optstr := optstr + ';SENDUNGS_NR;OPT_ENTER_SHIPMEND_NO';

        if editform.SendungNrPflichtCheckBox.Checked then
          optstr := optstr + ';PFLICH_SENDUNGS_NR;OPT_SENDUNGS_NR';

        if editform.DimPflichtCheckBox.Checked then
          optstr := optstr + ';OPT_DIM_REQUIRED';

        if editform.VerlCloseInfoCheckBox.Visible then begin
          if editform.VerlInfoPflichCheckBox.Enabled and editform.VerlInfoPflichCheckBox.Checked then
            optstr := optstr + ';OPT_VERLADE_INFOS:2'
          else if editform.VerlCloseInfoCheckBox.Checked then
            optstr := optstr + ';OPT_VERLADE_INFOS:1';
        end;

        if not editform.BrokerCheckBox.Visible then
          optstr := optstr + ';~OPT_BROKER'
        else if editform.BrokerCheckBox.Checked then
          optstr := optstr + ';OPT_BROKER';

        if not editform.PresetDimCheckBox.Visible then
          optstr := optstr + ';~PRE_DIM'
        else if editform.PresetDimCheckBox.Checked then
          optstr := optstr + ';PRE_DIM';

        if not editform.PreSetWeightCheckBox.Visible then
          optstr := optstr + ';~PRE_WEIGHT'
        else if editform.PreSetWeightCheckBox.Checked then
          optstr := optstr + ';PRE_WEIGHT';

        if not editform.EDIDimCheckBox.Visible then
          optstr := optstr + ';~EDI_DIM'
        else if editform.EDIDimCheckBox.Checked then
          optstr := optstr + ';EDI_DIM';

        if not editform.EDIWeightCheckBox.Visible then
          optstr := optstr + ';~EDI_WEIGHT'
        else if editform.EDIWeightCheckBox.Checked then
          optstr := optstr + ';EDI_WEIGHT';

        res := ChangeSpeditionConfig (SpedADOQuery.FieldByName('REF').AsInteger, GetComboBoxRef (editform.DefaultLTComboBox), editform.LogPlatformEdit.Text, optstr);
      end;

      if (res = 0) and (editform.DepotComboBox.Visible) then begin
        optstr := '0';

        if (editform.DepotPflichtCheckBox.Checked) then
          optstr := '1';

        res := SetSpeditionAnlieferDepot (SpedADOQuery.FieldByName('REF').AsInteger, GetComboBoxRef (editform.DepotComboBox), optstr);
      end;

      if (res = 0) and (editform.VersandArtEdit.Visible) then begin
        res := SetSpeditionVersandart (SpedADOQuery.FieldByName('REF').AsInteger, editform.VersandArtEdit.Text);
      end;

      if (res = 0) then
        res := SetIFTMINDaten (SpedADOQuery.FieldByName('REF').AsInteger,
                               editform.DFUEKennungEdit.Text,
                               editform.ILNEdit.Text,
                               editform.AccountNrEdit.Text,
                               editform.DepotEdit.Text,
                               dfuestr);

      if (res = 0) and (editform.CutOffGroupBox.Visible) then begin
        if (editform.CutOffHourUpDown.Position = 0) and (editform.CutOffMinUpDown.Position = 0) then
          minute := -1
        else minute := editform.CutOffHourUpDown.Position * 60 + editform.CutOffMinUpDown.Position;

        res := SetSpeditionCutOffTime (SpedADOQuery.FieldByName('REF').AsInteger, minute);
      end;

      if (res <> 0) then
        MessageDLG (FormatMessageText (1174, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else
        SpedDBGrid.Reload;
    end;

    editform.Free;
  end;
end;

procedure TSpeditionenForm.EditSpedGatewayMenuItemClick(Sender: TObject);
var
  dlgform: TSpedGatewayForm;
begin
  if SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1) and (SpedADOQuery.FieldByName ('STATUS').AsString <> 'DEL') then begin
    dlgform := TSpedGatewayForm.Create (Self);

    try
      dlgform.Prepare (SpedADOQuery.FieldByName ('REF').AsInteger);

      if (dlgform.ShowModal = mrOk) then begin
      end;
    finally
      dlgform.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.EditTourButtonClick(Sender: TObject);
var
  editform : TEditSpedTourForm;
begin
  if (Sender = NewTourButton) or (TourADOQuery.Active and (TourADOQuery.RecNo <> -1) and (TourADOQuery.FieldByName ('STATUS').AsString <> 'DEL')) then begin
    editform := TEditSpedTourForm.Create (Self);

    if (Sender = NewTourButton) then
      editform.RefTour := -1
    else
      editform.RefTour := TourADOQuery.FieldByName('REF').AsInteger;

    if (editform.ShowModal = mrOk) then
      TourDBGrid.Reload (editform.RefTour);

    editform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.FormActivate(Sender: TObject);
begin
  if Assigned (FrontendACOModule) then
    FrontendACOModule.SetBerechtigungen (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  SpedADOQuery.Close;
  TourADOQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.FormCreate(Sender: TObject);
begin
  fEditTab := -1;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, FehlerStatusLabel);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.FormShow(Sender: TObject);
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.RestoreFormInfo (Self);

  EditSpedGatewayMenuItem.Visible   := LVSDatenModul.ViewExits ('V_PCD_SPED_GATEWAY');
  EditVersandConfigMenuItem.Visible := LVSDatenModul.ViewExits ('V_PCD_SPED_VERSAND_CONFIG');
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TSpeditionenForm.UpdateSpedQuery : Integer;
begin
  SpedADOQuery.Close;

  SpedADOQuery.SQL.Clear;

  SpedADOQuery.SQL.Add ('select * from V_PCD_SPEDITIONEN where REF_LOCATION='+IntToStr (LVSDatenModul.AktLocationRef));

  if (LVSDatenModul.DatabaseVersion > 35) then begin
    if not (ShowDelTourCheckBox.Checked) then
      SpedADOQuery.SQL.Add ('and STATUS<>''DEL''');
  end;

  if (LVSDatenModul.AktMandantRef <> -1) then
    SpedADOQuery.SQL.Add ('and REF_MAND='+IntToStr (LVSDatenModul.AktMandantRef))
  else
    SpedADOQuery.SQL.Add ('and REF_MAND in (select REF from V_PCD_MANDANT)');

  if (LVSDatenModul.AktLagerRef <> -1) then
    SpedADOQuery.SQL.Add ('and (REF_LAGER is null or REF_LAGER='+IntToStr (LVSDatenModul.AktLagerRef) + ')')
  else
    SpedADOQuery.SQL.Add ('and (REF_LAGER is null or REF_LAGER in (select REF from V_PCD_LAGER where REF_LOCATION='+IntToStr (LVSDatenModul.AktLocationRef)+'))');

  try
    SpedADOQuery.Open;

    SpedDBGrid.SetColumnVisible ('CHANGE_OPTION', False);
    SpedDBGrid.SetColumnVisible ('OPTIONS', False);
    SpedDBGrid.SetColumnVisible ('DFUE_ART', False);
    SpedDBGrid.SetColumnVisible ('AVIS_ART', False);
    SpedDBGrid.SetColumnVisible ('LABEL_ART', False);
  except
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TSpeditionenForm.UpdateTourQuery : Integer;
begin
  TourADOQuery.Close;

  TourADOQuery.SQL.Clear;

  TourADOQuery.SQL.Add ('select * from V_PCD_SPED_TOUR where REF<>0');

  if (LVSDatenModul.DatabaseVersion > 35) then begin
    if not (ShowDelTourCheckBox.Checked) then
      TourADOQuery.SQL.Add ('and STATUS<>''DEL''');
  end;

  if (LVSDatenModul.AktMandantRef <> -1) then
    TourADOQuery.SQL.Add ('and REF_MAND='+IntToStr (LVSDatenModul.AktMandantRef))
  else
    TourADOQuery.SQL.Add ('and REF_MAND in (select REF from V_PCD_MANDANT)');

  try
    TourADOQuery.Open;

    //TourDBGrid.SetColumnVisible ('CHANGE_OPTION', False);
    //TourDBGrid.SetColumnVisible ('OPTIONS', False);
  except
  end;

  Result := 0;
end;

procedure TSpeditionenForm.EditVersandConfigMenuItemClick(Sender: TObject);
var
  dlgform: TSpedSendConfigForm;
begin
  if SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1) and (SpedADOQuery.FieldByName ('STATUS').AsString <> 'DEL') then begin
    dlgform := TSpedSendConfigForm.Create (Self);

    try
      dlgform.Prepare (SpedADOQuery.FieldByName ('REF').AsInteger, -1);

      if (dlgform.ShowModal = mrOk) then begin
      end;
    finally
      dlgform.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSpeditionenForm.SpedPrintNVEListeMenuItemClick(Sender: TObject);
var
  berichtform: TBerichtDatumForm;
begin
  if SpedADOQuery.Active and (SpedADOQuery.RecNo <> -1) and (SpedADOQuery.FieldByName ('STATUS').AsString <> 'DEL') then begin
    berichtform := TBerichtDatumForm.Create (Self);

    try
      berichtform.DataRef := SpedADOQuery.FieldByName ('REF').AsInteger;
      berichtform.DataRefStr := 'REF_SPED';

      berichtform.MandantPanel.Visible := False;
      berichtform.LagerPanel.Visible := False;
      berichtform.AlleLagerErlaubt := False;

      berichtform.DatumPanel.Visible := True;
      berichtform.DatumDateTimePicker.DateTime := Trunc (Now);
      berichtform.BisDatumDateTimePicker.Visible := FALSE;

      berichtform.SortPanel.Visible := False;
      berichtform.AuswahlPanel.Visible := False;

      berichtform.BerichtName := GetResourceText (1418);
      berichtform.BerichtReport := 'KEP_VERSANDLISTE';

      berichtform.DatumLabel.Caption := GetResourceText (1414);

      berichtform.ShowModal;
    finally
      berichtform.Release;
    end;
  end;
end;

end.
