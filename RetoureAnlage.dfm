object RetoureAnlageForm: TRetoureAnlageForm
  Left = 410
  Top = 331
  BorderStyle = bsDialog
  Caption = 'Neue Retourenannahme anlegen'
  ClientHeight = 602
  ClientWidth = 543
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poMainFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    543
    602)
  TextHeight = 13
  object Bevel4: TBevel
    Left = 14
    Top = 562
    Width = 516
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 336
  end
  object OkButton: TButton
    Left = 372
    Top = 571
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 6
  end
  object AbortButton: TButton
    Left = 455
    Top = 571
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 7
  end
  object MandPanel: TPanel
    Left = 0
    Top = 0
    Width = 543
    Height = 48
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label9: TLabel
      Left = 15
      Top = 8
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 16
      Top = 24
      Width = 514
      Height = 22
      Style = csOwnerDrawFixed
      ColWidth = 120
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 148
    Width = 543
    Height = 293
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      543
      293)
    object Bevel1: TBevel
      Left = 14
      Top = 109
      Width = 516
      Height = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label2: TLabel
      Left = 16
      Top = 250
      Width = 99
      Height = 13
      Caption = 'Lieferschein-Nummer'
    end
    object Label3: TLabel
      Left = 216
      Top = 8
      Width = 110
      Height = 13
      Caption = 'Wareneingangsbereich'
    end
    object Label4: TLabel
      Left = 16
      Top = 64
      Width = 44
      Height = 13
      Caption = 'Anlieferer'
    end
    object Label5: TLabel
      Left = 216
      Top = 64
      Width = 75
      Height = 13
      Caption = 'Lademittelkonto'
    end
    object Label6: TLabel
      Left = 16
      Top = 172
      Width = 265
      Height = 13
      Caption = 'Gegen diese Retoure aus der Warenwirtschaft erfassen:'
    end
    object Lieferant: TLabel
      Left = 16
      Top = 193
      Width = 31
      Height = 13
      Caption = 'Kunde'
    end
    object Label1: TLabel
      Left = 432
      Top = 190
      Width = 81
      Height = 13
      Caption = 'Retourennummer'
    end
    object Bevel3: TBevel
      Left = 14
      Top = 56
      Width = 516
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label7: TLabel
      Left = 312
      Top = 250
      Width = 91
      Height = 13
      Caption = 'Lieferschein-Datum'
    end
    object Label8: TLabel
      Left = 16
      Top = 8
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Label10: TLabel
      Left = 312
      Top = 190
      Width = 46
      Height = 13
      Caption = 'Auftragnr.'
    end
    object Bevel6: TBevel
      Left = 14
      Top = 164
      Width = 516
      Height = 5
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label11: TLabel
      Left = 16
      Top = 120
      Width = 72
      Height = 13
      Caption = 'Art der Retoure'
    end
    object Label12: TLabel
      Left = 215
      Top = 120
      Width = 88
      Height = 13
      Caption = 'Grund der Retoure'
    end
    object Bevel5: TBevel
      Left = 14
      Top = 4
      Width = 516
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Bevel7: TBevel
      Left = 14
      Top = 242
      Width = 516
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label18: TLabel
      Left = 175
      Top = 250
      Width = 66
      Height = 13
      Caption = 'RMA-Nummer'
    end
    object EmpfCountLabel: TLabel
      Left = 16
      Top = 229
      Width = 66
      Height = 11
      Caption = 'EmpfCountLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -8
      Font.Name = 'MS Reference Sans Serif'
      Font.Style = []
      ParentFont = False
    end
    object Label20: TLabel
      Left = 432
      Top = 250
      Width = 85
      Height = 13
      Caption = 'Kaufdatum Kunde'
    end
    object KaufDatumLabel: TLabel
      Left = 432
      Top = 269
      Width = 114
      Height = 16
      Caption = 'KaufDatumLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LSNrEdit: TEdit
      Left = 16
      Top = 266
      Width = 145
      Height = 21
      MaxLength = 30
      TabOrder = 12
      Text = 'LSNrEdit'
      OnKeyUp = LSNrEditKeyUp
    end
    object WEComboBox: TComboBoxPro
      Left = 216
      Top = 24
      Width = 314
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 1
    end
    object SpedComboBox: TComboBox
      Left = 16
      Top = 80
      Width = 193
      Height = 21
      TabOrder = 2
      Text = 'SpedComboBox'
      OnChange = SpedComboBoxChange
    end
    object LeerComboBox: TComboBoxPro
      Left = 216
      Top = 80
      Width = 314
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 120
      ItemHeight = 15
      TabOrder = 3
    end
    object RetourNrComboBox: TComboBox
      Left = 432
      Top = 209
      Width = 98
      Height = 21
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      MaxLength = 32
      ParentFont = False
      TabOrder = 10
      OnChange = RetourNrComboBoxChange
    end
    object LSDateDateTimePicker: TDateTimePicker
      Left = 312
      Top = 266
      Width = 114
      Height = 21
      Date = 38273.000000000000000000
      Time = 0.522373333333234800
      TabOrder = 14
    end
    object KundenComboBox: TComboBoxPro
      Left = 16
      Top = 208
      Width = 281
      Height = 22
      Style = csOwnerDrawFixed
      PopupMenu = KundenComboBoxPopupMenu
      TabOrder = 6
      OnChange = KundenComboBoxChange
      OnCloseUp = KundenComboBoxChange
    end
    object LagerComboBox: TComboBoxPro
      Left = 16
      Top = 24
      Width = 193
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 0
      OnChange = LagerComboBoxChange
    end
    object AufNrComboBox: TComboBox
      Left = 312
      Top = 209
      Width = 114
      Height = 21
      Style = csDropDownList
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      MaxLength = 32
      ParentFont = False
      TabOrder = 8
    end
    object RetArtComboBox: TComboBoxPro
      Left = 16
      Top = 136
      Width = 193
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 300
      ItemHeight = 15
      TabOrder = 4
      OnChange = RetArtComboBoxChange
    end
    object RetGrundComboBox: TComboBox
      Left = 215
      Top = 136
      Width = 315
      Height = 21
      Hint = 'Grund der Retoure'
      Style = csDropDownList
      TabOrder = 5
      OnChange = RetGrundComboBoxChange
    end
    object KundenEdit: TEdit
      Left = 80
      Top = 208
      Width = 121
      Height = 21
      MaxLength = 32
      TabOrder = 7
      Text = 'KundenEdit'
    end
    object AufNrEdit: TEdit
      Left = 328
      Top = 209
      Width = 63
      Height = 21
      MaxLength = 32
      TabOrder = 9
      Text = 'AufNrEdit'
    end
    object RetourNrEdit: TEdit
      Left = 432
      Top = 209
      Width = 63
      Height = 21
      MaxLength = 32
      TabOrder = 11
      Text = 'Edit2'
    end
    object RMANrEdit: TEdit
      Left = 175
      Top = 266
      Width = 122
      Height = 21
      MaxLength = 30
      TabOrder = 13
      Text = 'RMANrEdit'
      OnKeyUp = LSNrEditKeyUp
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 48
    Width = 543
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      543
      50)
    object Label17: TLabel
      Left = 16
      Top = 8
      Width = 67
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 16
      Top = 24
      Width = 514
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object TraderPanel: TPanel
    Left = 0
    Top = 98
    Width = 543
    Height = 50
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      543
      50)
    object Label16: TLabel
      Left = 16
      Top = 8
      Width = 46
      Height = 13
      Caption = 'Verk'#228'ufer'
    end
    object TraderComboBox: TComboBoxPro
      Left = 16
      Top = 24
      Width = 514
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 120
      TabOrder = 0
    end
  end
  object AddServicePanel: TPanel
    Left = 0
    Top = 441
    Width = 543
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      543
      57)
    object Label13: TLabel
      Left = 16
      Top = 12
      Width = 80
      Height = 13
      Caption = 'Zusatzleistungen'
    end
    object Label14: TLabel
      Left = 312
      Top = 12
      Width = 87
      Height = 13
      Caption = 'R'#252'cksendekosten'
    end
    object Label15: TLabel
      Left = 410
      Top = 31
      Width = 22
      Height = 13
      Caption = 'Euro'
    end
    object Bevel2: TBevel
      Left = 14
      Top = 4
      Width = 516
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object AddServiceComboBox: TComboBoxPro
      Left = 16
      Top = 28
      Width = 281
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 300
      ItemHeight = 15
      TabOrder = 0
    end
    object CostEdit: TEdit
      Left = 312
      Top = 28
      Width = 91
      Height = 21
      TabOrder = 1
      Text = 'CostEdit'
      OnKeyPress = FloatKeyPress
    end
  end
  object HintPanel: TPanel
    Left = 0
    Top = 498
    Width = 543
    Height = 61
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      543
      61)
    object Label19: TLabel
      Left = 16
      Top = 12
      Width = 43
      Height = 13
      Caption = 'Hinweise'
    end
    object Bevel8: TBevel
      Left = 14
      Top = 4
      Width = 516
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object HintEdit: TEdit
      Left = 16
      Top = 28
      Width = 514
      Height = 21
      MaxLength = 128
      TabOrder = 0
      Text = 'HintEdit'
    end
  end
  object ADOQuery1: TADOQuery
    LockType = ltReadOnly
    Parameters = <>
    Left = 56
    Top = 904
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 16
    Top = 900
  end
  object LFPopupMenu: TPopupMenu
    Left = 352
    Top = 228
    object NeuerLieferant1: TMenuItem
      Caption = 'Neuer Kunde...'
      ShortCut = 115
      OnClick = NeuerLieferant1Click
    end
  end
  object KundenComboBoxPopupMenu: TPopupMenu
    Left = 352
    Top = 228
    object MenuItem1: TMenuItem
      Caption = 'Neuer Kunde...'
      ShortCut = 115
      OnClick = NeuerLieferant1Click
    end
  end
end
