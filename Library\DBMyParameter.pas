unit DBMyParameter;

interface

uses 
  DB, ADODB;

type
  TMyParameter = class(TParameter)
  private
    function GetAsValue: variant;
    Procedure SetAsValue(const Value: variant);
  public
    property Value: variant read GetAsValue write SetAsValue;
  end;

implementation

uses
  Variants;

procedure TMyParameter.SetAsValue(const Value: variant);
var
  iPar      : Integer;
  basicType : Integer;
begin
  for iPar:= 0 to Collection.Count - 1 do
    if (Name = TParameter(Collection.Items[iPar]).Name) then begin
      (*
      if (TParameter(Collection.Items[iPar]).DataType = ftUnknown) then begin
        TParameter(Collection.Items[iPar]).SetDataType (VarTypeToDataType(VarType(Value)));
      end;
      *)

      TParameter(Collection.Items[iPar]).Value:= Value;
    end;
end;

function TMyParameter.GetAsValue: variant;
begin
  Result:= inherited Value;
end;

end.