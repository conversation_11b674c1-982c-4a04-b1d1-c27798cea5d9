unit VerladegruppenDLG;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Data.DB, Vcl.Grids, Vcl.DBGrids,
  SMDBGrid, DBGridPro, Vcl.ExtCtrls, Vcl.StdCtrls, Data.Win.ADODB;

type
  TVerladegruppenMainForm = class(TForm)
    VerladegruppenDBGridPro: TDBGridPro;
    UpperPanel: TPanel;
    UpperButtonPanel: TPanel;
    Splitter1: TSplitter;
    LowerPanel: TPanel;
    BottomPanel: TPanel;
    LowerButtonPanel: TPanel;
    CloseButton: TButton;
    SpeditionDBGridPro: TDBGridPro;
    NewGroupButton: TButton;
    EditGroupButton: TButton;
    DeleteGroupButton: TButton;
    VerladegruppenDataSource: TDataSource;
    VerladegruppenQuery: TADOQuery;
    SpeditionenQuery: TADOQuery;
    SpeditionenDataSource: TDataSource;
    AssignedCheckBox: TCheckBox;
    NotAssignedCheckBox: TCheckBox;
    OtherAssignedCheckBox: TCheckBox;
    AssignSelectionButton: TButton;
    UnassignButton: TButton;
    procedure DeleteGroupButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure CloseButtonClick(Sender: TObject);
    procedure NewGroupButtonClick(Sender: TObject);
    procedure EditGroupButtonClick(Sender: TObject);
    procedure FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
    procedure VerladegruppenDataSourceDataChange(Sender: TObject;
      Field: TField);
    procedure CheckBoxesClick(Sender: TObject);
    procedure AssignSelectionButtonClick(Sender: TObject);
    procedure UnassignButtonClick(Sender: TObject);
  private
    procedure RefreshVerladeGruppen;
    procedure RefreshSpeditionen;
    procedure OpenEditForm(const RefVerladeGruppe : Integer = -1);
  public
    procedure RefreshDBGrids;
  end;

implementation

uses
  FrontendMessages,
  ResourceText,
  SprachModul,
  DatenModul,
  DBGridUtilModule,
  ConfigModul,
  EditVerladegruppeDLG,
  LVSVerladegruppenInterface
  ;

{$R *.dfm}

procedure TVerladegruppenMainForm.AssignSelectionButtonClick(Sender: TObject);
var
  I : Integer;
  groupAlreadySet : Boolean;
begin
  groupAlreadySet := False;
  for I := 0 to SpeditionDBGridPro.SelectedRows.Count - 1 do
  begin
    SpeditionenQuery.GotoBookmark(TBookmark(SpeditionDBGridPro.SelectedRows[I]));
    if not SpeditionenQuery.FieldByName('REF_VERL_GRUPPE').IsNull then
    begin
      groupAlreadySet := True;
    end;
  end;

  if groupAlreadySet then
  begin
    if FrontendMessages.MessageDlg(FormatMessageText(1876, []), mtConfirmation, [mbYes,mbNo,mbCancel], 0) <> mrYes then
    begin
      exit;
    end;
  end;

  for I := 0 to SpeditionDBGridPro.SelectedRows.Count - 1 do
  begin
    SpeditionenQuery.GotoBookmark(TBookmark(SpeditionDBGridPro.SelectedRows[I]));

    SetSpeditionVerladeGruppe(VerladegruppenQuery.FieldByName('REF').AsInteger, SpeditionenQuery.FieldByName('REF').AsInteger);
  end;
  RefreshSpeditionen;
end;

procedure TVerladegruppenMainForm.UnassignButtonClick(Sender: TObject);
var
  I : Integer;
begin
  if (FrontendMessages.MessageDlg(FormatMessageText(1875, []), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then
  begin
    for I := 0 to SpeditionDBGridPro.SelectedRows.Count - 1 do
    begin
      SpeditionenQuery.GotoBookmark(TBookmark(SpeditionDBGridPro.SelectedRows[I]));

      UnassignSpeditionVerladeGruppe(SpeditionenQuery.FieldByName('REF').AsInteger);
    end;
    RefreshSpeditionen;
  end;
end;

procedure TVerladegruppenMainForm.CheckBoxesClick(Sender: TObject);
begin
  RefreshSpeditionen;
end;

procedure TVerladegruppenMainForm.CloseButtonClick(Sender: TObject);
begin
  self.Close;
end;

procedure TVerladegruppenMainForm.DeleteGroupButtonClick(Sender: TObject);
begin
  if (FrontendMessages.MessageDlg(FormatMessageText(1877, [VerladegruppenQuery.FieldByName('GRUPPENNAME').AsString]), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then
  begin
    DeleteVerladeGruppe(VerladegruppenQuery.FieldByName('REF').AsInteger);
    RefreshDBGrids;
  end;
end;

procedure TVerladegruppenMainForm.EditGroupButtonClick(Sender: TObject);
begin
  OpenEditForm(VerladegruppenQuery.FieldByName('REF').AsInteger);
end;

procedure TVerladegruppenMainForm.NewGroupButtonClick(Sender: TObject);
begin
  OpenEditForm;
end;

procedure TVerladegruppenMainForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
//    LVSSprachModul.SetNoTranslate (Self, KopfComboBox);
  {$endif}
end;

procedure TVerladegruppenMainForm.FormKeyDown(Sender: TObject; var Key: Word;
  Shift: TShiftState);
begin
  if (Key = VK_F5) then
  begin
    RefreshDBGrids;
  end
  else if (Key = VK_ESCAPE) then
  begin
    self.Close;
  end;
end;

procedure TVerladegruppenMainForm.FormShow(Sender: TObject);
begin
  RefreshDBGrids;
end;

procedure TVerladegruppenMainForm.RefreshDBGrids;
begin
  RefreshVerladeGruppen;
  RefreshSpeditionen;
end;

procedure TVerladegruppenMainForm.RefreshSpeditionen;
var
  FilterSet : Boolean;

  procedure addSQLFilter(filterstring: string);
  begin
    if not FilterSet then
    begin
      SpeditionenQuery.SQL.Add(' AND (');
      FilterSet := True;
    end
    else
    begin
      SpeditionenQuery.SQL.Add(' OR ');
    end;

    SpeditionenQuery.SQL.Add(filterstring);
  end;
begin
  SpeditionenQuery.Close;
  SpeditionenQuery.SQL.Text := 'SELECT * FROM V_PCD_SPEDITIONEN WHERE (REF_LOCATION = :location AND STATUS <> ''DEL'')';
  SpeditionenQuery.Parameters.ParamByName('location').Value := LVSDatenModul.AktLocationRef;

  if VerladegruppenQuery.RecordCount > 0 then
  begin
    FilterSet := False;
    if AssignedCheckBox.Checked then
    begin
      addSQLFilter('REF_VERL_GRUPPE = '+VerladegruppenQuery.FieldByName('REF').AsInteger.ToString);
    end;
    if NotAssignedCheckBox.Checked then
    begin
      addSQLFilter('REF_VERL_GRUPPE IS NULL');
    end;
    if OtherAssignedCheckBox.Checked then
    begin
      addSQLFilter('REF_VERL_GRUPPE IS NOT NULL AND REF_VERL_GRUPPE <> '+VerladegruppenQuery.FieldByName('REF').AsInteger.ToString);
    end;

    if FilterSet then
    begin
      SpeditionenQuery.SQL.Add(')');
    end;
  end;

  SpeditionenQuery.Active := True;
  SpeditionDBGridPro.RefreshData;
end;

procedure TVerladegruppenMainForm.RefreshVerladeGruppen;
begin
  VerladegruppenQuery.Close;
  VerladegruppenQuery.SQL.Text := 'SELECT * FROM V_PCD_VERLADE_GRUPPE WHERE REF_LOCATION = :location';
  VerladegruppenQuery.Parameters.ParamByName('location').Value := LVSDatenModul.AktLocationRef;
  VerladegruppenQuery.Active := True;
  VerladegruppenDBGridPro.RefreshData;
end;

procedure TVerladegruppenMainForm.VerladegruppenDataSourceDataChange(
  Sender: TObject; Field: TField);
begin
  RefreshSpeditionen;
end;

procedure TVerladegruppenMainForm.OpenEditForm(const RefVerladeGruppe : Integer = -1);
var
  EditVerladegruppeForm: TEditVerladegruppeForm;
begin
  EditVerladegruppeForm := TEditVerladegruppeForm.Create(self);
  EditVerladegruppeForm.Prepare(RefVerladeGruppe);
  if EditVerladegruppeForm.ShowModal = mrOk then
  begin
    RefreshDBGrids;
  end;
  EditVerladegruppeForm.Free;
end;

procedure TVerladegruppenMainForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

end.
