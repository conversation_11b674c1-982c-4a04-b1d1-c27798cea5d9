unit EditLPDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComCtrls, IntegerUpDown, ComboBoxPro;

type
  TEditLPForm = class(TForm)
    NrEdit: TEdit;
    ReiheEdit: TEdit;
    FeldEdit: TEdit;
    FachEdit: TEdit;
    TiefeEdit: TEdit;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    Label5: TLabel;
    OKButton: TButton;
    AbortButton: TButton;
    NameEdit: TEdit;
    Label6: TLabel;
    Bevel1: TBevel;
    Bevel2: TBevel;
    LTAnzEdit: TEdit;
    LTAnzUpDown: TIntegerUpDown;
    Label7: TLabel;
    LPArtComboBox: TComboBoxPro;
    Label8: TLabel;
    Bevel3: TBevel;
    Bevel4: TBevel;
    FolgeEdit: TEdit;
    Label9: TLabel;
    Bevel5: TBevel;
    RBGKoorEdit: TEdit;
    Label10: TLabel;
    Label11: TLabel;
    WWSPlatzEdit: TEdit;
    Bevel6: TBevel;
    Label12: TLabel;
    EbeneEdit: TEdit;
    Label13: TLabel;
    LPPrioEdit: TEdit;
    LPPrioUpDown: TIntegerUpDown;
    Bevel7: TBevel;
    Label14: TLabel;
    Label16: TLabel;
    LPHKLComboBox: TComboBoxPro;
    Label17: TLabel;
    MaxGewichtEdit: TEdit;
    Label18: TLabel;
    Bevel8: TBevel;
    Label24: TLabel;
    FBLEdit: TEdit;
    Label25: TLabel;
    FBBEdit: TEdit;
    Label33: TLabel;
    FBHEdit: TEdit;
    Label34: TLabel;
    LBZoneComboBox: TComboBoxPro;
    Bevel9: TBevel;
    BarcodeEdit: TEdit;
    Label15: TLabel;
    Label19: TLabel;
    Label20: TLabel;
    Label21: TLabel;
    Label22: TLabel;
    StellplatzNrEdit: TEdit;
    StapelFaktorEdit: TEdit;
    StapelFaktorUpDown: TIntegerUpDown;
    Label23: TLabel;
    Label26: TLabel;
    ABCComboBox: TComboBox;
    Bevel10: TBevel;
    ScanableCheckBox: TCheckBox;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure NrEditKeyPress(Sender: TObject; var Key: Char);
    procedure FormShow(Sender: TObject);
    procedure RegalKoorEditExit(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure IntegerEditKeyPress(Sender: TObject; var Key: Char);
    procedure LTAnzUpDownClick (Sender: TObject; Button: TUDBtnType);
  private
    fReiheSize   : Integer;

    fPlatzNrEdit : Boolean;
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses StringUtils, VCLUtilitys, FormsUtils, SprachModul, ResourceText,
  ConfigModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLPForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  gw,
  intwert   : Integer;
  int64wert : Int64;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    if (ReiheEdit.Enabled and (Length (ReiheEdit.Text) > fReiheSize)) Then begin
      CanClose := False;
      MessageDlg (FormatMessageText (1626, [IntToStr (fReiheSize)]), mtWarning, [mbOK], 0);
      ReiheEdit.SetFocus;
    end else if (NrEdit.Enabled and (Length (NrEdit.Text) = 0)) Then begin
      CanClose := False;
      MessageDlg (FormatMessageText (1230, []), mtWarning, [mbOK], 0);
      NrEdit.SetFocus;
    end else if (MaxGewichtEdit.Enabled and (Length (MaxGewichtEdit.Text) > 0) and (not TryStrToInt (MaxGewichtEdit.Text, gw)) or (gw > 1000*1000)) then begin //Nicht mehr als 1000 Tonnen
      CanClose := False;
      MessageDlg (GetResourceText (1254), mtWarning, [mbOK], 0);
      MaxGewichtEdit.SetFocus;
    end else if (Length (FolgeEdit.Text) > 0) and not (TryStrToInt64 (FolgeEdit.Text, int64wert)) then begin
      CanClose := False;
      MessageDlg (FormatMessageText (1231, []), mtWarning, [mbOK], 0);
      FolgeEdit.SetFocus;
    end else if (Length (FeldEdit.Text) > 0) and not (TryStrToInt (FeldEdit.Text, intwert)) then begin
      CanClose := False;
      MessageDlg (FormatMessageText (1232, []), mtWarning, [mbOK], 0);
      FeldEdit.SetFocus;
    end else if (Length (FachEdit.Text) > 0) and not (TryStrToInt (FachEdit.Text, intwert)) then begin
      CanClose := False;
      MessageDlg (FormatMessageText (1233, []), mtWarning, [mbOK], 0);
      FachEdit.SetFocus;
    end else if (Length (EbeneEdit.Text) > 0) and not (TryStrToInt (EbeneEdit.Text, intwert)) then begin
      CanClose := False;
      MessageDlg (FormatMessageText (1234, []), mtWarning, [mbOK], 0);
      EbeneEdit.SetFocus;
    end else if (Length (TiefeEdit.Text) > 0) and not (TryStrToInt (TiefeEdit.Text, intwert)) then begin
      CanClose := False;
      MessageDlg (FormatMessageText (1235, []), mtWarning, [mbOK], 0);
      TiefeEdit.SetFocus;
    end else if (Length (FBLEdit.Text) > 0) and not (TryStrToInt (FBLEdit.Text, intwert)) then begin
      CanClose := False;
      MessageDlg (FormatMessageText (1236, []), mtWarning, [mbOK], 0);
      FBLEdit.SetFocus;
    end else if (Length (FBBEdit.Text) > 0) and not (TryStrToInt (FBBEdit.Text, intwert)) then begin
      CanClose := False;
      MessageDlg (FormatMessageText (1237, []), mtWarning, [mbOK], 0);
      FBBEdit.SetFocus;
    end else if (Length (FBHEdit.Text) > 0) and not (TryStrToInt (FBHEdit.Text, intwert)) then begin
      CanClose := False;
      MessageDlg (FormatMessageText (1238, []), mtWarning, [mbOK], 0);
      TiefeEdit.SetFocus;
    end else begin
      CanClose := True
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLPForm.NrEditKeyPress(Sender: TObject; var Key: Char);
begin
  fPlatzNrEdit := True;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLPForm.FormShow(Sender: TObject);
begin
  Label5.Visible  := TiefeEdit.Visible;
  Label22.Visible := StellplatzNrEdit.Visible;
  Label15.Visible := BarcodeEdit.Visible;
  Label26.Visible := ABCComboBox.Visible;

  Label23.Visible := StapelFaktorEdit.Visible;
  StapelFaktorUpDown.Visible := StapelFaktorEdit.Visible;

  SetControlColor (NrEdit);
  SetControlColor (FolgeEdit);
  SetControlColor (ReiheEdit);
  SetControlColor (FeldEdit);
  SetControlColor (FachEdit);
  SetControlColor (TiefeEdit);
  SetControlColor (EbeneEdit);
  SetControlColor (StellplatzNrEdit);
  SetControlColor (BarcodeEdit);
  SetControlColor (StapelFaktorEdit);
  SetControlColor (RBGKoorEdit);
  SetControlColor (WWSPlatzEdit);
  SetControlColor (WWSPlatzEdit);

  fPlatzNrEdit := (Length (NrEdit.Text) > 0);

  if (LTAnzUpDown.Position < 0) Then
    LTAnzEdit.Text := '';

  if (Length (NrEdit.Text) = 0) and NrEdit.Enabled then
    NrEdit.SetFocus
  else begin
    //Sonderwunsch von Herrn Schwarzer
    if (FolgeEdit.Enabled) Then
      FolgeEdit.SetFocus
    else if NameEdit.Enabled then
      NameEdit.SetFocus;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLPForm.IntegerEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,^C,^V,'0'..'9'])  then begin
    Key := #0;
    Beep;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 30.06.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLPForm.LTAnzUpDownClick(Sender: TObject; Button: TUDBtnType);
begin
  if (LTAnzUpDown.Position < 0) Then
    LTAnzEdit.Text := '';
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLPForm.RegalKoorEditExit(Sender: TObject);
begin
  if not (fPlatzNrEdit) then begin
    NrEdit.Text := FormatStr (ReiheEdit.Text, -3, '0') + '-' + FormatStr (FeldEdit.Text, -3, '0') + '-' + FormatStr (FachEdit.Text, -3, '0') + '-' + FormatStr (EbeneEdit.Text, -3, '0');
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLPForm.FormCreate(Sender: TObject);
begin
  NrEdit.Text := '';
  ReiheEdit.Text := '';
  FeldEdit.Text := '';
  FachEdit.Text := '';
  EbeneEdit.Text := '';
  TiefeEdit.Text := '';
  NameEdit.Text := '';
  FolgeEdit.Text := '';
  RBGKoorEdit.Text := '';
  WWSPlatzEdit.Text := '';
  MaxGewichtEdit.Text := '';
  BarcodeEdit.Text := '';
  StellplatzNrEdit.Text := '';
  StapelFaktorEdit.Text := '';

  FBLEdit.Text := '';
  FBBEdit.Text := '';
  FBHEdit.Text := '';

  LTAnzEdit.Text := '';
  LTAnzUpDown.Position := 1;

  fReiheSize := 3;

  if not (LVSConfigModul.UseABCKlassen) then
    ABCComboBox.Visible := False;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LBZoneComboBox);
    LVSSprachModul.SetNoTranslate (Self, LPArtComboBox);
    LVSSprachModul.SetNoTranslate (Self, LPHKLComboBox);
    LVSSprachModul.SetNoTranslate (Self, ABCComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLPForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (LBZoneComboBox);
  ClearComboBoxObjects (LPArtComboBox);
  ClearComboBoxObjects (LPHKLComboBox);
  ClearComboBoxObjects (ABCComboBox);
end;

end.
