﻿unit RelationDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, DB, ADODB,
  CompTranslate, ExtCtrls,EditWATourDLG;

type
  TRelationForm = class(TForm)
    WARelDBGrid: TDBGridPro;
    CloseButton: TButton;
    RelNewButton: TButton;
    RelDelButton: TButton;
    WARelADOQuery: TADOQuery;
    WARelDataSource: TDataSource;
    Label1: TLabel;
    CompTranslateForm1: TCompTranslateForm;
    Bevel1: TBevel;
    RelChangeButton: TButton;
    ADOQuery1: TADOQuery;
    RelPrtPalButton: TButton;
    RelPrtLPButton: TButton;
    procedure RelNewButtonClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure WARelDBGridDblClick(Sender: TObject);
    procedure WARelDataSourceDataChange(Sender: TObject; Field: TField);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure RelDelButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    function TourDBGridColumnSort(Sender: TCustomDBGridPro; const ColumnName: string): string;
    procedure RelPrtPalButtonClick(Sender: TObject);
    procedure FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
  private
    fRefMand  : Integer;
    fRefLager : Integer;

    procedure UpdateTourQuery;
    procedure LoadKFZTypen (editform : TEditWATourForm);
  public
    RefRelation : Integer;
  end;

implementation

uses ComCtrls, VCLUtilitys, DatenModul, FrontendUtils, EditRelationDLG, LVSWarenausgang,
  ConfigModul, SprachModul, ComboBoxPro, DBGridUtilModule,
  IntegerUpDown, PrintLPLableDLG, TourInfoFrame, PrintModul;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRelationForm.RelNewButtonClick(Sender: TObject);
var
  res,
  ref,
  prio             : Integer;
  ok               : Boolean;
  artstr,
  optstr           : String;
  EditRelationForm : TEditRelationForm;
begin
  res := 0;

  EditRelationForm := TEditRelationForm.Create(Self);

  EditRelationForm.Prepare (fRefMand, fRefLager);

  repeat
    if (EditRelationForm.ShowModal = mrOk) then begin
      if (Length (EditRelationForm.BarcodeEdit.Text) = 0) then
        ok := True
      else begin
        ok := True;

        (*
        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ('select REF from V_PCD_WA_RELATION where REF<>'+IntToStr (ref)+' and REF_LOCATION='+IntToStr (LVSDatenModul.AktLocationRef)+' and BARCODE='+#39+EditRelationForm.BarcodeEdit.Text+#39);

        ADOQuery1.Open;

        ok := (ADOQuery1.RecordCount = 0);

        ADOQuery1.Close;
        *)
      end;

      if not (ok) then begin
        MessageDLG ('Es gibt bereits eine Relation mit dem Barcode', mtError, [mbOK], 0);
      end else begin
        with EditRelationForm do begin
          if (ArtRadioGroup.ItemIndex = 0) then
            artstr := 'WA'
          else if (ArtRadioGroup.ItemIndex = 1) then
            artstr := 'ZIEL'
          else if (ArtRadioGroup.ItemIndex = 2) then
            artstr := 'UEB'
          else if (ArtRadioGroup.ItemIndex = 3) then
            artstr := 'PACK';

          if (RadioPrioPanel.Visible) then begin
            if (PrioRadioGroup.ItemIndex = 0) then
              prio := -1
            else if (PrioRadioGroup.ItemIndex = 0) then
              prio := 10
            else if (PrioRadioGroup.ItemIndex = 1) then
              prio := 50
            else if (PrioRadioGroup.ItemIndex = 2) then
              prio := 100
            else prio := 500;
          end else if (PrioUpDown.Position = 0) then begin
            prio := -1;
          end else begin
            prio := PrioUpDown.Position;
          end;

          optstr := '';

          if (AVISCheckBox.Checked) then
            optstr := 'AVIS;';

          if (VerladungCheckBox.Checked) then begin
            optstr := optstr + 'VERL;';

            if (AutoNVEVerladenCheckBox.Checked) then
              optstr := optstr + 'AUTONVE;';
          end;

          if (CheckBestCheckBox.Checked) then
            optstr := optstr + 'OPT_CHECK_BEST;';

          if (AVISMailVerlCheckBox.Checked) then
            optstr := optstr + 'OPT_AVIS_MAIL_VERL;';

          if (AVISMailAvisCheckBox.Checked) then
            optstr := optstr + 'OPT_AVIS_MAIL_AVIS;';

          if (CheckBestLagerCheckBox.Checked) then
            optstr := optstr + 'OPT_CHECK_BEST_LAGER;';

          if (AVISMailLSCheckBox.Checked) then
            optstr := optstr + 'OPT_AVIS_MAIL_LS;';

          if (CheckVerladeListeCheckBox.Checked) then begin
            optstr := optstr + 'OPT_VERLADE_LIST_PRINT;';
          end;

          if (CheckVerladeLiefCheckBox.Checked) then begin
            optstr := optstr + 'OPT_VERLADE_LIEF_PRINT;';
          end;

          if (VerladeArtComboBox.Enabled) then
            res := CreateRelation (GetComboBoxRef (MandantComboBox),
                                   LVSDatenModul.AktLocationRef,
                                   GetComboBoxRef (LagerComboBox),
                                   artstr,
                                   NameEdit.Text,
                                   BezEdit.Text,
                                   HinweisEdit.Text,
                                   LabelTextMemo.Text,
                                   BarcodeEdit.Text,
                                   AvisMailEdit.Text,
                                   GetComboBoxRef (DepotComboBox),
                                   -1,
                                   GetComboBoxRef (LBComboBox),
                                   GetComboBoxRef (LPComboBox),
                                   GetComboBoxRef (SpedComboBox),
                                   prio,
                                   optstr,
                                   GetComboBoxDBItemWert(VerladeArtComboBox),
                                   ref)
          else
            res := CreateRelation (GetComboBoxRef (MandantComboBox),
                                   LVSDatenModul.AktLocationRef,
                                   GetComboBoxRef (LagerComboBox),
                                   artstr,
                                   NameEdit.Text,
                                   BezEdit.Text,
                                   HinweisEdit.Text,
                                   LabelTextMemo.Text,
                                   BarcodeEdit.Text,
                                   AvisMailEdit.Text,
                                   GetComboBoxRef (DepotComboBox),
                                   -1,
                                   GetComboBoxRef (LBComboBox),
                                   GetComboBoxRef (LPComboBox),
                                   GetComboBoxRef (SpedComboBox),
                                   prio,
                                   optstr,
                                   ref);

          if (res = 0) then
            res := SetRelationClearingFlag (ref, ClearingCheckBox.Checked);
        end;
      end;
    end else begin
      ok := True;
    end;
  until (ok);

  if (res = 0) Then
    WARelDBGrid.Reload (ref)
  else
    MessageDLG ('Fehler beim Anlegen der neuen WA-Relation'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);

  EditRelationForm.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRelationForm.UpdateTourQuery;
begin
  WARelADOQuery.SQL.Clear;
  WARelADOQuery.SQL.Add ('select * from V_PCD_WA_RELATION where REF_LOCATION='+IntToStr (LVSDatenModul.AktLocationRef));

  if (LVSDatenModul.AktMandantRef <> -1) then
    WARelADOQuery.SQL.Add ('and (REF_MAND is null or REF_MAND='+IntToStr (LVSDatenModul.AktMandantRef)+')')
  else WARelADOQuery.SQL.Add ('and (REF_MAND is null or REF_MAND in (select REF from V_PCD_MANDANT))');

  if (LVSDatenModul.AktLagerRef <> -1) then
    WARelADOQuery.SQL.Add ('and (REF_LAGER is null or REF_LAGER=+'+IntToStr (LVSDatenModul.AktLagerRef)+')')
  else WARelADOQuery.SQL.Add ('and (REF_LAGER is null or REF_LAGER in (select REF from V_PCD_LAGER where REF_LOCATION='+IntToStr (LVSDatenModul.AktLocationRef)+'))');

  WARelADOQuery.Open;

  WARelDBGrid.SetColumnVisible ('ABFAHRT_TIME', False);
  WARelDBGrid.SetColumnVisible ('AVIS_SENDEN', False);
  WARelDBGrid.SetColumnVisible ('DEFAULT_RELATION', False);
  WARelDBGrid.SetColumnVisible ('OPT_VERLADUNG', False);
  WARelDBGrid.SetColumnVisible ('OPT_CLEARING', False);
  WARelDBGrid.SetColumnVisible ('OPTIONS', False);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRelationForm.FormShow(Sender: TObject);
begin
  LVSConfigModul.RestoreFormInfo (Self);

  fRefLager := -1;
  fRefMand  := -1;

  UpdateTourQuery;

  if (RefRelation <> -1) then
    WARelADOQuery.Locate('REF', RefRelation, [])
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRelationForm.WARelDBGridDblClick(Sender: TObject);
var
  res,
  prio    : Integer;
  ok      : Boolean;
  optstr  : String;
  EditRelationForm: TEditRelationForm;
begin
  if (WARelADOQuery.Active) and (WARelADOQuery.RecordCount > 0) then begin
    res := 0;

    EditRelationForm := TEditRelationForm.Create(Self);

    EditRelationForm.Prepare(WARelADOQuery.FieldByName ('REF').AsInteger);

    repeat
      if (EditRelationForm.ShowModal = mrOk) then begin
        if (Length (EditRelationForm.BarcodeEdit.Text) = 0) then
          ok := True
        else begin
          ok := True;

          (*
          ADOQuery1.SQL.Clear;
          ADOQuery1.SQL.Add ('select REF from V_PCD_WA_RELATION where REF<>'+IntToStr (ref)+' and REF_LOCATION='+IntToStr (LVSDatenModul.AktLocationRef)+' and BARCODE='+#39+EditRelationForm.BarcodeEdit.Text+#39);

          ADOQuery1.Open;

          ok := (ADOQuery1.RecordCount = 0);

          ADOQuery1.Close;
          *)
        end;

        if not (ok) then begin
          MessageDLG ('Es gibt bereits eine Relation mit dem Barcode', mtError, [mbOK], 0);
        end else begin
          with EditRelationForm do begin
            if (RadioPrioPanel.Visible) then begin
              if (PrioRadioGroup.ItemIndex = 0) then
                prio := -1
              else if (PrioRadioGroup.ItemIndex = 0) then
                prio := 10
              else if (PrioRadioGroup.ItemIndex = 1) then
                prio := 50
              else if (PrioRadioGroup.ItemIndex = 2) then
                prio := 100
              else prio := 500;
            end else if (PrioUpDown.Position = 0) then begin
              prio := -1;
            end else begin
              prio := PrioUpDown.Position;
            end;

            optstr := '';

            if (AVISCheckBox.Checked) then
              optstr := optstr + 'AVIS;';

            if (VerladungCheckBox.Checked) then begin
              optstr := optstr + 'VERL;';

              if (AutoNVEVerladenCheckBox.Checked) then
                optstr := optstr + 'AUTONVE;';
            end;

            if (CheckBestCheckBox.Checked) then
              optstr := optstr + 'OPT_CHECK_BEST;';

            if (AVISMailVerlCheckBox.Checked) then
              optstr := optstr + 'OPT_AVIS_MAIL_VERL;';

            if (AVISMailAvisCheckBox.Checked) then
              optstr := optstr + 'OPT_AVIS_MAIL_AVIS;';

            if (CheckBestLagerCheckBox.Checked) then
              optstr := optstr + 'OPT_CHECK_BEST_LAGER;';

            if (AVISMailLSCheckBox.Checked) then
              optstr := optstr + 'OPT_AVIS_MAIL_LS;';

            if (CheckVerladeListeCheckBox.Checked) then begin
              optstr := optstr + 'OPT_VERLADE_LIST_PRINT;';
            end;

            if (CheckVerladeLiefCheckBox.Checked) then begin
              optstr := optstr + 'OPT_VERLADE_LIEF_PRINT;';
            end;


            if (VerladeArtComboBox.Enabled) then
              res := ChangeRelation (RefRel, UpdKey,
                                     GetComboBoxRef (MandantComboBox),
                                     GetComboBoxRef (LagerComboBox),
                                     NameEdit.Text,
                                     BezEdit.Text,
                                     HinweisEdit.Text,
                                     LabelTextMemo.Text,
                                     BarcodeEdit.Text,
                                     AvisMailEdit.Text,
                                     GetComboBoxRef (DepotComboBox),
                                     -1,
                                     GetComboBoxRef (LBComboBox),
                                     GetComboBoxRef (LPComboBox),
                                     GetComboBoxRef (SpedComboBox),
                                     prio,
                                     optstr,
                                     GetComboBoxDBItemWert(VerladeArtComboBox))
            else
              res := ChangeRelation (RefRel, UpdKey,
                                     GetComboBoxRef (MandantComboBox),
                                     GetComboBoxRef (LagerComboBox),
                                     NameEdit.Text,
                                     BezEdit.Text,
                                     HinweisEdit.Text,
                                     LabelTextMemo.Text,
                                     BarcodeEdit.Text,
                                     AvisMailEdit.Text,
                                     GetComboBoxRef (DepotComboBox),
                                     -1,
                                     GetComboBoxRef (LBComboBox),
                                     GetComboBoxRef (LPComboBox),
                                     GetComboBoxRef (SpedComboBox),
                                     prio,
                                     optstr);

            if (res = 0) then
              res := SetRelationClearingFlag (RefRel, ClearingCheckBox.Checked);
          end;
        end;
      end else begin
        ok := True;
      end;
    until (ok);

    if (res = 0) Then
      WARelDBGrid.Reload
    else
      MessageDLG ('Fehler beim Ändern der WA-Relation'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);

    EditRelationForm.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRelationForm.WARelDataSourceDataChange(Sender: TObject; Field: TField);
begin
  RelDelButton.Enabled := False;
  RelChangeButton.Enabled := False;
  RelPrtPalButton.Enabled := False;
  RelPrtLPButton.Enabled := False;

  if (WARelADOQuery.Active) and (WARelADOQuery.RecNo > 0) then begin
    RelDelButton.Enabled := True;
    RelChangeButton.Enabled := True;

    if (WARelADOQuery.FieldByName('ART').AsString = 'ZIEL') then
      RelPrtPalButton.Enabled := True;

    if (WARelADOQuery.FieldByName('ART').AsString = 'WA') or (WARelADOQuery.FieldByName('ART').AsString = 'UEB') then
      RelPrtLPButton.Enabled := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRelationForm.LoadKFZTypen (editform : TEditWATourForm);
var
  query  : TADOQuery;
begin
  editform.DefaultTourInfo.KFZTypComboBox.Items.Add ('');

  editform.MoTourInfo.KFZTypComboBox.Items.Add ('');
  editform.DiTourInfo.KFZTypComboBox.Items.Add ('');
  editform.MiTourInfo.KFZTypComboBox.Items.Add ('');
  editform.DoTourInfo.KFZTypComboBox.Items.Add ('');
  editform.FrTourInfo.KFZTypComboBox.Items.Add ('');
  editform.SaTourInfo.KFZTypComboBox.Items.Add ('');
  editform.SoTourInfo.KFZTypComboBox.Items.Add ('');

  query := TADOQuery.Create (Nil);
  query.LockType := ltReadOnly;
  query.Connection := LVSDatenModul.MainADOConnection;

  query.SQL.Clear;
  query.SQL.Add ('select REF,NAME from V_SPED_KFZ_TYPEN order by NAME');

  try
    query.Open;

    while not (query.Eof) do begin
      editform.DefaultTourInfo.KFZTypComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

      editform.MoTourInfo.KFZTypComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));
      editform.DiTourInfo.KFZTypComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));
      editform.MiTourInfo.KFZTypComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));
      editform.DoTourInfo.KFZTypComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));
      editform.FrTourInfo.KFZTypComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));
      editform.SaTourInfo.KFZTypComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));
      editform.SoTourInfo.KFZTypComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

      query.Next;
    end;

    query.Close;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRelationForm.RelPrtPalButtonClick(Sender: TObject);
var
  prtform : TPrintLPLableForm;
begin
  prtform := TPrintLPLableForm.Create (Self);

  prtform.Prepare (UserReg.ReadRegValue ('RELATION-PRINTER'), '', DBGetReferenz (WARelADOQuery.FieldByName ('REF_MAND')), LVSDatenModul.AktLocationRef, DBGetReferenz (WARelADOQuery.FieldByName ('REF_LAGER')), -1);

  prtform.RelationTabSheet.TabVisible := false;
  prtform.PageControl1.ActivePage := prtform.RelationTabSheet;

  prtform.RefRelation := WARelADOQuery.FieldByName ('REF').AsInteger;

  prtform.RelNameLabel.Caption    := WARelADOQuery.FieldByName ('NAME').AsString;
  prtform.RelDescLabel.Caption    := WARelADOQuery.FieldByName ('BESCHREIBUNG').AsString;
  prtform.RelCodeLabel.Caption    := WARelADOQuery.FieldByName ('BARCODE').AsString;
  prtform.RelTextLabel.Caption    := WARelADOQuery.FieldByName ('LABEL_TEXT').AsString;
  prtform.RelBereichLabel.Caption := WARelADOQuery.FieldByName ('LB').AsString;
  prtform.RelPlatzLabel.Caption   := WARelADOQuery.FieldByName ('LP_NR').AsString;

  if (Sender = RelPrtPalButton) then
    prtform.RelArtRadioGroup.ItemIndex := 0
  else
    prtform.RelArtRadioGroup.ItemIndex := 1;

  prtform.ShowModal;

  UserReg.WriteRegValue ('RELATION-PRINTER', prtform.GetSelectedPrinter);

  prtform.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRelationForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  WARelADOQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TRelationForm.TourDBGridColumnSort(Sender: TCustomDBGridPro; const ColumnName: string): string;
begin
  if (ColumnName = 'TOUR_NR') then
    Result := 'LPAD (' + ColumnName + ',9,''0'')'
  else Result := ColumnName;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRelationForm.RelDelButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (WARelADOQuery.Active) and (WARelADOQuery.RecordCount > 0) Then begin
    if (MessageDlg('Diese WA-Relation wirklich löschen?', mtConfirmation, [mbYes, mbNo, mbCancel], 0) = mrYes) then begin
      res := DeleteRelation (WARelADOQuery.FieldByName ('REF').AsInteger);

      if (res <> 0) Then
        MessageDLG ('Fehler beim Löschen der WA-Relation'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
      else begin
        if (WARelADOQuery.Eof) Then
          WARelADOQuery.Prior
        else WARelADOQuery.Next;

        WARelDBGrid.Reload;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRelationForm.FormCreate(Sender: TObject);
begin
  fRefMand    := -1;
  fRefLager   := -1;
  RefRelation := -1;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRelationForm.FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
begin
  if (Key = VK_F5) then
    UpdateTourQuery;
end;

end.
