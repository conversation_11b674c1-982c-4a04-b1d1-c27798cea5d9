object ABCAnalyseForm: TABCAnalyseForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'ABC Analyse'
  ClientHeight = 382
  ClientWidth = 265
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    265
    382)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Bevel2: TBevel
    Left = 8
    Top = 340
    Width = 249
    Height = 5
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 314
  end
  object Label5: TLabel
    Left = 8
    Top = 50
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Bevel5: TBevel
    Left = 8
    Top = 303
    Width = 249
    Height = 5
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
  end
  object Label2: TLabel
    Left = 8
    Top = 187
    Width = 107
    Height = 13
    Caption = 'Prozentanteil A-Artikel'
  end
  object Label3: TLabel
    Left = 8
    Top = 211
    Width = 172
    Height = 13
    Caption = 'Prozentanteil zus'#228'tzlich f'#252'r B-Artikel'
    OnClick = Label3Click
  end
  object Bevel1: TBevel
    Left = 8
    Top = 248
    Width = 249
    Height = 5
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label6: TLabel
    Left = 8
    Top = 252
    Width = 52
    Height = 13
    Caption = 'Von Datum'
  end
  object Label7: TLabel
    Left = 139
    Top = 252
    Width = 47
    Height = 13
    Caption = 'Bis Datum'
  end
  object Label8: TLabel
    Left = 245
    Top = 187
    Width = 11
    Height = 13
    Caption = '%'
  end
  object Label9: TLabel
    Left = 245
    Top = 211
    Width = 11
    Height = 13
    Caption = '%'
  end
  object OkButton: TButton
    Left = 101
    Top = 351
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    TabOrder = 8
    OnClick = OkButtonClick
  end
  object AbortButton: TButton
    Left = 182
    Top = 351
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 9
  end
  object MandantComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 249
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 80
    ItemHeight = 15
    TabOrder = 0
    OnChange = MandantComboBoxChange
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 66
    Width = 249
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 80
    ItemHeight = 15
    TabOrder = 1
  end
  object ExcelCheckBox: TCheckBox
    Left = 8
    Top = 314
    Width = 249
    Height = 17
    Caption = 'Ausgabe in Excel'
    TabOrder = 7
    Visible = False
  end
  object AProzenzEdit: TEdit
    Left = 200
    Top = 184
    Width = 41
    Height = 21
    TabOrder = 3
    Text = 'AProzenzEdit'
    OnChange = ProzenzEditChange
    OnKeyPress = ProzenzEditKeyPress
  end
  object BProzenzEdit: TEdit
    Left = 200
    Top = 208
    Width = 41
    Height = 21
    TabOrder = 4
    Text = 'BProzenzEdit'
    OnChange = ProzenzEditChange
    OnKeyPress = ProzenzEditKeyPress
  end
  object VonDatumEdit: TEdit
    Left = 8
    Top = 271
    Width = 118
    Height = 21
    TabOrder = 5
    Text = 'MHDEdit'
    OnExit = VonDatumEditExit
  end
  object BisDatumEdit: TEdit
    Left = 139
    Top = 271
    Width = 118
    Height = 21
    TabOrder = 6
    Text = 'MHDEdit'
    OnExit = BisDatumEditExit
  end
  object AuswertArtRadioGroup: TRadioGroup
    Left = 9
    Top = 91
    Width = 248
    Height = 87
    Caption = 'Auswertungsart'
    ItemIndex = 0
    Items.Strings = (
      'Bezogen auf die Gesamtmenge'
      'Bezogen auf das Gesamtgewicht'
      'Bezogen auf Gesamtanzahl der Zugriffe'
      'Bezogen auf den Gesamtwarenwert')
    TabOrder = 2
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 216
    Top = 8
  end
end
