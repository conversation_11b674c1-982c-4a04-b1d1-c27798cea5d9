unit ShowBestandResDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, BetterADODataSet, StdCtrls, Grids, DBGrids, SMDBGrid,
  DBGridPro, Menus, ACOList, FrontendImageModule;

type
  TShowBestandResForm = class(TForm)
    BestandResDBGrid: TDBGridPro;
    CloseButton: TButton;
    BestandResDataSet: TBetterADODataSet;
    BestandResDataSource: TDataSource;
    BestandResDBGridPopupMenu: TPopupMenu;
    DelBesResMenuItem: TMenuItem;
    ACOListForm1: TACOListForm;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure DelBesResMenuItemClick(Sender: TObject);
    procedure BestandResDBGridPopupMenuPopup(Sender: TObject);
    procedure FormActivate(Sender: TObject);
  private
    fChangeFlag   : Boolean;
    fReadOnlyFlag : Boolean;
  public
    property ChangeFlag   : Boolean read fChangeFlag;
    property ReadOnlyFlag : Boolean read fReadOnlyFlag write fReadOnlyFlag;

    procedure PrepareBestand  (const RefBes      : Integer); overload;
    procedure PrepareSumme    (const RefBesSumme : Integer); overload;
    procedure Prepare         (const RefLager, RefArEinheit : Integer); overload;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, LVSConst, DatenModul, DBGridUtilModule, FrontendUtils, ConfigModul, SperrGrundDLG, LVSDatenInterface,
  SprachModul, ResourceText, FrontendMessages, FrontendACOModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 23.02.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowBestandResForm.DelBesResMenuItemClick(Sender: TObject);
var
  res : Integer;
begin
  if not fReadOnlyFlag and (BestandResDataSet.Active) and (BestandResDataSet.RecNo > 0) then begin
    if (FrontendMessages.MessageDLG (FormatMessageText (1747, []), mtConfirmation, [mbYes, mbNo, mbCancel], 0) = mrYes) then begin
      res := DeleteBestandReservierung (BestandResDataSet.FieldByName ('REF').AsInteger, 'PCD');

      if (res <> 0) then
        FrontendMessages.MessageDLG (FormatMessageText (1749, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else begin
        fChangeFlag := true;
        BestandResDBGrid.Reload;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 23.02.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowBestandResForm.FormActivate(Sender: TObject);
begin
  if Assigned (FrontendACOModule) then
    FrontendACOModule.SetBerechtigungen (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowBestandResForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  BestandResDataSet.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowBestandResForm.FormCreate(Sender: TObject);
begin
  fChangeFlag := False;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, Label1);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowBestandResForm.FormShow(Sender: TObject);
begin
  LVSConfigModul.RestoreFormInfo (Self);

  BestandResDataSet.Open;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowBestandResForm.Prepare (const RefLager, RefArEinheit : Integer);
begin
  BestandResDataSet.CommandText := 'select * from V_ARTIKEL_RES_BESTAND where REF_LAGER=:ref_lager and REF_AR_EINHEIT=:ref_ae';
  BestandResDataSet.Parameters.ParamByName('ref_lager').Value := RefLager;
  BestandResDataSet.Parameters.ParamByName('ref_ae').Value := RefArEinheit;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 23.02.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowBestandResForm.BestandResDBGridPopupMenuPopup(Sender: TObject);
begin
  DelBesResMenuItem.Enabled := not fReadOnlyFlag and (BestandResDataSet.Active) and (BestandResDataSet.RecNo > 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowBestandResForm.PrepareSumme (const RefBesSumme : Integer);
begin
  BestandResDataSet.CommandText := 'select * from V_ARTIKEL_RES_BESTAND where REF_BES_SUM=:ref_bes_sum';
  BestandResDataSet.Parameters.ParamByName('ref_bes_sum').Value := RefBesSumme;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TShowBestandResForm.PrepareBestand (const RefBes : Integer);
begin
  BestandResDataSet.CommandText := 'select * from V_ARTIKEL_RES_BESTAND where REF_BESTAND=:ref_bes';
  BestandResDataSet.Parameters.ParamByName('ref_bes').Value := RefBes;
end;

end.
