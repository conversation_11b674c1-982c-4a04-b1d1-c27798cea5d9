object EditArtiklGroupForm: TEditArtiklGroupForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Artikelgruppe bearbeiten'
  ClientHeight = 537
  ClientWidth = 429
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    429
    537)
  TextHeight = 13
  object DatenPanel: TPanel
    Left = 0
    Top = 101
    Width = 429
    Height = 97
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      429
      97)
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 27
      Height = 13
      Caption = 'Name'
    end
    object Label4: TLabel
      Left = 274
      Top = 8
      Width = 39
      Height = 13
      Caption = 'Nummer'
    end
    object Label2: TLabel
      Left = 8
      Top = 52
      Width = 64
      Height = 13
      Caption = 'Beschreibung'
    end
    object Bevel5: TBevel
      Left = 4
      Top = 2
      Width = 415
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object NameEdit: TEdit
      Left = 8
      Top = 24
      Width = 137
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'NameEdit'
    end
    object NummerEdit: TEdit
      Left = 272
      Top = 24
      Width = 149
      Height = 21
      MaxLength = 32
      TabOrder = 1
      Text = 'NummerEdit'
    end
    object DescEdit: TEdit
      Left = 8
      Top = 68
      Width = 413
      Height = 21
      MaxLength = 64
      TabOrder = 2
      Text = 'DescEdit'
    end
  end
  object OkButton: TButton
    Left = 261
    Top = 504
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 7
  end
  object AbortButton: TButton
    Left = 346
    Top = 504
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 8
  end
  object Panel1: TPanel
    Left = 0
    Top = 239
    Width = 429
    Height = 8
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
  end
  object NamePanel: TPanel
    Left = 0
    Top = 198
    Width = 429
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    object Label7: TLabel
      Left = 8
      Top = 14
      Width = 68
      Height = 13
      Caption = 'Artikelgruppe:'
    end
    object NameLabel: TLabel
      Left = 104
      Top = 14
      Width = 62
      Height = 13
      Caption = 'NameLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object PageControl1: TPageControl
    Left = 0
    Top = 247
    Width = 429
    Height = 247
    ActivePage = VorhalteTabSheet
    Align = alTop
    TabOrder = 6
    object VorhalteTabSheet: TTabSheet
      Caption = 'Vorhaltemengen'
      object VorhalteKopfPanel: TPanel
        Left = 0
        Top = 0
        Width = 421
        Height = 90
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 0
        DesignSize = (
          421
          90)
        object Bevel1: TBevel
          Left = 4
          Top = 83
          Width = 413
          Height = 6
          Anchors = [akLeft, akRight, akBottom]
          Shape = bsTopLine
        end
        object Label3: TLabel
          Left = 8
          Top = 67
          Width = 56
          Height = 13
          Anchors = [akLeft, akBottom]
          Caption = 'Auftragsart'
          ExplicitTop = 43
        end
        object Label5: TLabel
          Left = 296
          Top = 66
          Width = 104
          Height = 13
          Anchors = [akRight, akBottom]
          Caption = 'Vorzuhaltende Menge'
          ExplicitTop = 8
        end
        object Bevel2: TBevel
          Left = 4
          Top = 56
          Width = 415
          Height = 6
          Anchors = [akLeft, akRight, akBottom]
          Shape = bsTopLine
        end
      end
    end
    object VersandGroupTabSheet: TTabSheet
      Caption = 'Versand Gruppe'
      ImageIndex = 4
      object GroupKopfPanel: TPanel
        Left = 0
        Top = 0
        Width = 421
        Height = 90
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 0
        DesignSize = (
          421
          90)
        object Bevel4: TBevel
          Left = 4
          Top = 83
          Width = 413
          Height = 6
          Anchors = [akLeft, akRight, akBottom]
          Shape = bsTopLine
        end
        object Label6: TLabel
          Left = 8
          Top = 67
          Width = 56
          Height = 13
          Anchors = [akLeft, akBottom]
          Caption = 'Auftragsart'
          ExplicitTop = 43
        end
        object Label10: TLabel
          Left = 296
          Top = 66
          Width = 73
          Height = 13
          Anchors = [akRight, akBottom]
          Caption = 'Versandgruppe'
        end
        object Bevel6: TBevel
          Left = 4
          Top = 56
          Width = 415
          Height = 6
          Anchors = [akLeft, akRight, akBottom]
          Shape = bsTopLine
        end
      end
    end
    object KommTabSheet: TTabSheet
      Caption = 'Kommissionierung'
      ImageIndex = 3
      object Gruppe: TLabel
        Left = 11
        Top = 10
        Width = 35
        Height = 13
        Caption = 'Gruppe'
      end
      object KommGrpComboBox: TComboBoxPro
        Left = 11
        Top = 26
        Width = 404
        Height = 22
        Style = csOwnerDrawFixed
        PopupMenu = KommGrpComboBoxPopupMenu
        TabOrder = 0
      end
    end
    object BeschaffungTabSheet: TTabSheet
      Caption = 'Beschaffung'
      ImageIndex = 1
      object Label27: TLabel
        Left = 8
        Top = 128
        Width = 140
        Height = 13
        Caption = 'Minimale Beschaffungsmenge'
      end
      object Label26: TLabel
        Left = 8
        Top = 80
        Width = 76
        Height = 13
        Caption = 'Mindestbestand'
      end
      object MinOrderBesEdit: TEdit
        Left = 8
        Top = 144
        Width = 121
        Height = 21
        TabOrder = 0
        Text = 'MinOrderBesEdit'
      end
      object MinArBesEdit: TEdit
        Left = 8
        Top = 96
        Width = 121
        Height = 21
        TabOrder = 1
        Text = 'MinArBesEdit'
      end
    end
    object NachschubTabSheet: TTabSheet
      Caption = 'Nachschub'
      ImageIndex = 2
      object Label9: TLabel
        Left = 8
        Top = 80
        Width = 76
        Height = 13
        Caption = 'Mindestbestand'
      end
      object Label11: TLabel
        Left = 8
        Top = 128
        Width = 52
        Height = 13
        Caption = 'Reichweite'
        Visible = False
      end
      object NachschubMengeEdit: TEdit
        Left = 8
        Top = 96
        Width = 121
        Height = 21
        TabOrder = 0
        Text = 'NachschubMengeEdit'
      end
      object ReichweiteEdit: TEdit
        Left = 8
        Top = 144
        Width = 121
        Height = 21
        TabOrder = 1
        Text = 'ReichweiteEdit'
        Visible = False
      end
    end
  end
  object MandPanel: TPanel
    Left = 0
    Top = 0
    Width = 429
    Height = 32
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      429
      32)
    object Label12: TLabel
      Left = 8
      Top = 11
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 88
      Top = 8
      Width = 332
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 32
    Width = 429
    Height = 28
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      429
      28)
    object Label13: TLabel
      Left = 8
      Top = 11
      Width = 69
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 88
      Top = 4
      Width = 332
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object LagerPanel: TPanel
    Left = 0
    Top = 60
    Width = 429
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    Visible = False
    DesignSize = (
      429
      41)
    object Label8: TLabel
      Left = 8
      Top = 16
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel3: TBevel
      Left = 4
      Top = 3
      Width = 415
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object LagerComboBox: TComboBoxPro
      Left = 88
      Top = 13
      Width = 332
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = LagerComboBoxChange
    end
  end
  object KommGrpComboBoxPopupMenu: TPopupMenu
    Left = 128
    Top = 304
    object NeuGruppeanlegen1: TMenuItem
      Caption = 'Neu Gruppe anlegen...'
      OnClick = NeuGruppeanlegen1Click
    end
  end
end
