unit LeergutKontoChange;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls;

type
  TLeergutKontoChangeForm = class(TForm)
    NameEdit: TEdit;
    Label1: TLabel;
    BesitzerEdit: TEdit;
    Label2: TLabel;
    OkButton: TButton;
    Button2: TButton;
    BeschreibungEdit: TEdit;
    Label3: TLabel;
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

var
  LeergutKontoChangeForm: TLeergutKontoChangeForm;

implementation

{$R *.dfm}

end.
