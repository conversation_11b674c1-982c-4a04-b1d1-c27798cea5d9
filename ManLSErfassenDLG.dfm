object ManLSErfassenForm: TManLSErfassenForm
  Left = 323
  Top = 349
  Caption = 'Manueller Lieferschein erfassen'
  ClientHeight = 621
  ClientWidth = 779
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    779
    621)
  TextHeight = 13
  object Label1: TLabel
    Left = 16
    Top = 8
    Width = 65
    Height = 13
    Caption = 'Kunden-Nr:'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label2: TLabel
    Left = 16
    Top = 24
    Width = 33
    Height = 13
    Caption = 'Name'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object KundenNrLabel: TLabel
    Left = 96
    Top = 8
    Width = 74
    Height = 13
    Caption = 'KundenNrLabel'
  end
  object KundenNameLabel: TLabel
    Left = 96
    Top = 24
    Width = 91
    Height = 13
    Caption = 'KundenNameLabel'
  end
  object Label5: TLabel
    Left = 16
    Top = 56
    Width = 22
    Height = 13
    Caption = 'EAN'
  end
  object Label6: TLabel
    Left = 304
    Top = 56
    Width = 41
    Height = 13
    Caption = 'Artikelnr.'
  end
  object Label7: TLabel
    Left = 16
    Top = 115
    Width = 33
    Height = 13
    Caption = 'Menge'
  end
  object Label8: TLabel
    Left = 152
    Top = 115
    Width = 39
    Height = 13
    Caption = 'Gewicht'
  end
  object Label9: TLabel
    Left = 16
    Top = 224
    Width = 25
    Height = 13
    Caption = 'MHD'
  end
  object Label10: TLabel
    Left = 168
    Top = 224
    Width = 34
    Height = 13
    Caption = 'Charge'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 272
    Width = 761
    Height = 9
    Shape = bsTopLine
  end
  object Bevel2: TBevel
    Left = 8
    Top = 49
    Width = 761
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label11: TLabel
    Left = 16
    Top = 163
    Width = 23
    Height = 13
    Caption = 'Preis'
    Visible = False
  end
  object Label3: TLabel
    Left = 456
    Top = 56
    Width = 46
    Height = 13
    Caption = 'Artikeltext'
  end
  object Bevel3: TBevel
    Left = 8
    Top = 105
    Width = 761
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Bevel4: TBevel
    Left = 8
    Top = 214
    Width = 761
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object Label4: TLabel
    Left = 8
    Top = 535
    Width = 72
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Gersamtbetrag:'
    ExplicitTop = 455
  end
  object LabelSumme: TLabel
    Left = 99
    Top = 535
    Width = 61
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'LabelSumme'
    ExplicitTop = 455
  end
  object EinheitLabel: TLabel
    Left = 102
    Top = 134
    Width = 35
    Height = 13
    AutoSize = False
    Caption = 'EinheitLabel'
  end
  object Label12: TLabel
    Left = 152
    Top = 163
    Width = 32
    Height = 13
    Caption = 'Rabatt'
    Visible = False
  end
  object Label13: TLabel
    Left = 208
    Top = 163
    Width = 65
    Height = 13
    Caption = 'Preisnachlass'
    Visible = False
  end
  object Label14: TLabel
    Left = 188
    Top = 182
    Width = 8
    Height = 13
    Caption = '%'
  end
  object PreisCurrencyLabel: TLabel
    Left = 84
    Top = 182
    Width = 38
    Height = 13
    Caption = 'Label14'
  end
  object NachlassCurrencyLabel: TLabel
    Left = 276
    Top = 182
    Width = 38
    Height = 13
    Caption = 'Label14'
  end
  object Label15: TLabel
    Left = 304
    Top = 224
    Width = 67
    Height = 13
    Caption = 'Seriennummer'
  end
  object Bevel5: TBevel
    Left = 8
    Top = 320
    Width = 761
    Height = 9
    Shape = bsTopLine
  end
  object Label16: TLabel
    Left = 16
    Top = 277
    Width = 37
    Height = 13
    Caption = 'Hinweis'
  end
  object ErfasstStringGrid: TStringGridPro
    Left = 8
    Top = 384
    Width = 761
    Height = 145
    Anchors = [akLeft, akTop, akRight, akBottom]
    ColCount = 9
    DefaultColWidth = 20
    DefaultRowHeight = 20
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
    PopupMenu = ErfasstStringGridPopupMenu
    TabOrder = 16
    TitelTexte.Strings = (
      ''
      'Artikel-Nr'
      'Artikel-Text'
      'Menge'
      'Einheit'
      'Gewicht'
      'Preis'
      'Seriennr.'
      'Hinweis')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'MS Sans Serif'
    TitelFont.Style = []
    ColWidths = (
      20
      49
      59
      39
      38
      45
      58
      85
      171)
  end
  object AbortButton: TButton
    Left = 694
    Top = 563
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 18
  end
  object OkButton: TButton
    Left = 606
    Top = 563
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    ModalResult = 1
    TabOrder = 17
  end
  object EANEdit: TEdit
    Left = 16
    Top = 72
    Width = 273
    Height = 21
    TabOrder = 0
    Text = 'EANEdit'
    OnChange = InputChange
    OnExit = EANEditExit
    OnKeyPress = EANEditKeyPress
  end
  object ArtikelNrEdit: TEdit
    Left = 304
    Top = 72
    Width = 89
    Height = 21
    TabOrder = 1
    Text = 'ArtikelNrEdit'
    OnChange = InputChange
    OnExit = ArtikelNrEditExit
    OnKeyPress = ArtikelNrEditKeyPress
  end
  object MengeEdit: TEdit
    Left = 16
    Top = 131
    Width = 64
    Height = 21
    TabOrder = 4
    Text = '0'
    OnChange = InputChange
    OnExit = MengeEditExit
  end
  object GewichtEdit: TEdit
    Left = 152
    Top = 131
    Width = 121
    Height = 21
    TabOrder = 6
    Text = 'GewichtEdit'
    OnChange = InputChange
    OnKeyPress = GewichtEditKeyPress
  end
  object MHDEdit: TEdit
    Left = 16
    Top = 240
    Width = 121
    Height = 21
    TabOrder = 10
    Text = 'MHDEdit'
    OnChange = InputChange
    OnExit = MHDEditExit
  end
  object InsertButton: TButton
    Left = 677
    Top = 344
    Width = 91
    Height = 25
    Anchors = [akTop, akRight]
    Caption = #220'bernehmen'
    TabOrder = 14
    OnClick = InsertButtonClick
  end
  object ChargeEdit: TEdit
    Left = 168
    Top = 240
    Width = 121
    Height = 21
    TabOrder = 11
    Text = 'ChargeEdit'
    OnChange = InputChange
  end
  object PreisEdit: TEdit
    Left = 16
    Top = 179
    Width = 65
    Height = 21
    TabOrder = 7
    Text = 'PreisEdit'
    Visible = False
    OnChange = InputChange
    OnKeyPress = PreisEditKeyPress
  end
  object MengeUpDown: TIntegerUpDown
    Left = 80
    Top = 131
    Width = 16
    Height = 21
    Associate = MengeEdit
    Max = 20000
    TabOrder = 5
    OnExit = MengeEditExit
  end
  object ArtikelTextEdit: TEdit
    Left = 456
    Top = 72
    Width = 305
    Height = 21
    TabStop = False
    Anchors = [akLeft, akTop, akRight]
    Enabled = False
    ReadOnly = True
    TabOrder = 2
    Text = 'ArtikelTextEdit'
  end
  object ListArtikelButton: TButton
    Left = 400
    Top = 72
    Width = 41
    Height = 21
    Caption = '...'
    TabOrder = 3
    OnClick = ListArtikelButtonClick
  end
  object FehlerLabel: TPanel
    Left = 16
    Top = 344
    Width = 649
    Height = 25
    Anchors = [akLeft, akTop, akRight]
    BevelOuter = bvNone
    Caption = 'FehlerLabel'
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 15
  end
  object StatusBar1: TStatusBar
    Left = 0
    Top = 602
    Width = 779
    Height = 19
    Panels = <
      item
        Width = 200
      end>
  end
  object STPanel: TPanel
    Left = 8
    Top = 563
    Width = 329
    Height = 25
    Anchors = [akLeft, akBottom]
    BevelOuter = bvNone
    Caption = 'St'#252'ckartikel'
    Color = clLime
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 19
  end
  object RabattEdit: TEdit
    Left = 152
    Top = 179
    Width = 34
    Height = 21
    TabOrder = 8
    Text = 'RabattEdit'
    Visible = False
    OnChange = InputChange
    OnKeyPress = RabattEditKeyPress
  end
  object NachlassEdit: TEdit
    Left = 208
    Top = 179
    Width = 62
    Height = 21
    TabOrder = 9
    Text = 'NachlassEdit'
    Visible = False
    OnChange = InputChange
    OnKeyPress = PreisEditKeyPress
  end
  object SerialEdit: TEdit
    Left = 304
    Top = 240
    Width = 212
    Height = 21
    TabOrder = 12
    Text = 'ChargeEdit'
    OnChange = InputChange
  end
  object HintEdit: TEdit
    Left = 16
    Top = 293
    Width = 745
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 13
    Text = 'ChargeEdit'
    OnChange = InputChange
  end
  object BestandStringGrid: TStringGridPro
    Left = 344
    Top = 114
    Width = 417
    Height = 94
    TabStop = False
    Anchors = [akLeft, akTop, akRight]
    ColCount = 4
    DefaultColWidth = 20
    DefaultRowHeight = 20
    Enabled = False
    FixedCols = 0
    RowCount = 4
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goColSizing, goRowSelect, goFixedRowDefAlign]
    ScrollBars = ssVertical
    TabOrder = 21
    TitelTexte.Strings = (
      'Bereich'
      'Platz'
      'Menge'
      'Reserviert')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -12
    TitelFont.Name = 'Segoe UI'
    TitelFont.Style = []
    ColWidths = (
      141
      120
      52
      60)
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 728
    Top = 8
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    OnChangeLanguage = CompTranslateForm1ChangeLanguage
    Left = 688
    Top = 8
  end
  object ErfasstStringGridPopupMenu: TPopupMenu
    Left = 640
    Top = 368
    object OptColmunWidthMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      OnClick = OptColmunWidthMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object EditPosMenuItem: TMenuItem
      Caption = 'Bearbeiten'
      OnClick = EditPosMenuItemClick
    end
    object DelPosMenuItem: TMenuItem
      Caption = 'L'#246'schen...'
      OnClick = DelPosMenuItemClick
    end
  end
end
