unit FrontendDatentypen;

interface

uses
  Classes, VCLUtilitys;

type
  PInfoEntry = ^TInfoEntry;
  TInfoEntry = record
    InfoTyp  : String [32];
    InfoText : array [0..1023] of char;
  end;

  TKommErfasstEntry = class (TObject)
    KomBenRef      : Integer;
    KomPosRef      : Integer;
    KommPosNr      : Integer;
    AufRef         : Integer;
    AufPosRef      : Integer;
    Fehlmenge      : Boolean;
    MengeErfasst   : Integer;
    GewichtErfasst : Integer;
    GewichtBuchen  : Integer;
    MHD            : TDateTime;
    HerstellDatum  : TDateTime;
    Charge         : String;
    LENr           : String;
    NVERef         : Integer;
    LERef          : Integer;
    LTTypRef       : Integer;
    InfoListe      : TList;
    CreateDate     : TDateTime;

    constructor Create; overload;
    destructor  Destroy; override;

    procedure AddInfoText (pInfoTyp, pInfoText : String);
  end;

  TKommErfasstList=class (TObject)
    private
      FItems:TList;
      function GetCount:integer;
      function GetItem(aIndex:integer):TKommErfasstEntry;
      procedure SetItem(aIndex:integer;aItem:TKommErfasstEntry);
    public
      constructor Create;
      destructor Destroy; override;

      function  Add (aItem : TKommErfasstEntry) : Integer;
      procedure Delete (aIndex:integer);
      procedure Clear;
      property Items [aIndex:integer] : TKommErfasstEntry read GetItem write SetItem; default;
      property Count:integer read GetCount;
      function Find (const pKommPosNr : Integer) : TKommErfasstEntry;
  end;

  TErfassenGridRef = class (TGridRef)
    Index : Integer;

    constructor Create (const RecRef, RecIndex : Integer);
  end;

implementation

uses
  SysUtils;

//*******************************************************************************
constructor TKommErfasstEntry.Create;
begin
  inherited Create;

  AufRef    := -1;
  AufPosRef := -1;
  LTTypRef  := -1;
  
  MengeErfasst   := -1;
  GewichtErfasst := -1;
  GewichtBuchen  := -1;

  InfoListe := Nil;
end;

destructor  TKommErfasstEntry.Destroy;
var
  idx : Integer;
begin
  if Assigned (InfoListe) then begin
    idx := 0;
    while (idx < InfoListe.Count) do begin
      if Assigned (InfoListe [idx]) then
        FreeMem (InfoListe [idx]);

      Inc (idx);
    end;

    InfoListe.Free;
  end;

  inherited Destroy
end;

procedure TKommErfasstEntry.AddInfoText (pInfoTyp, pInfoText : String);
var
  p : PInfoEntry;
begin
  if not (Assigned (InfoListe)) then
    InfoListe := TList.Create;

  GetMem (p, sizeof (TInfoEntry));

  p^.InfoTyp  := pInfoTyp;
  StrPCopy (p^.InfoText, pInfoText);

  InfoListe.Add (p);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TErfassenGridRef.Create (const RecRef, RecIndex : Integer);
begin
  inherited Create (RecRef);

  Index := RecIndex;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TKommErfasstList.Create;
begin
  inherited Create;

  FItems:=TList.Create;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
destructor TKommErfasstList.Destroy;
begin
  Clear;

  if Assigned (FItems) then
    FItems.Free;

  inherited Destroy;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TKommErfasstList.GetCount:integer;
begin
  Result:=FItems.Count;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TKommErfasstList.GetItem(aIndex:integer):TKommErfasstEntry;
begin
  Result:=TKommErfasstEntry (FItems [aIndex]);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TKommErfasstList.SetItem(aIndex:integer;aItem:TKommErfasstEntry);
begin
  FItems[aIndex]:=aItem;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TKommErfasstList.Add (aItem:TKommErfasstEntry) : Integer;
begin
  aItem.CreateDate := Now;

  Result := fItems.Add( aItem);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TKommErfasstList.Delete(aIndex:integer);
begin
  if Assigned (Items[aIndex]) then
    Items[aIndex].Free;

  FItems.Delete(aIndex);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TKommErfasstList.Clear;
begin
  while Count>0 do Delete(0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TKommErfasstList.Find (const pKommPosNr : Integer) : TKommErfasstEntry;
var
  idx   : Integer;
  found : Boolean;
begin
  idx := 0;
  found := False;

  while (idx < fItems.Count) and not (found) do begin
    with TKommErfasstEntry (fItems [idx]) do begin
      if (pKommPosNr = KommPosNr) then
        found := True
      else Inc (idx);
    end;
  end;

  if (found) then
    Result := TKommErfasstEntry (fItems [idx])
  else Result := Nil;
end;

end.
