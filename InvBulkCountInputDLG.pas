unit InvBulkCountInputDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComboBoxPro, FrontendUtils, ComCtrls;

type
  TInvBulkCountInputForm = class(TForm)
    MHDPanel: TPanel;
    Bevel6: TBevel;
    Label9: TLabel;
    MHDDutyLabel: TLabel;
    MHDEdit: TEdit;
    ChargePanel: TPanel;
    Bevel5: TBevel;
    Label8: TLabel;
    ChargeDutyLabel: TLabel;
    ChargeEdit: TEdit;
    FussPanel: TPanel;
    OkButton: TButton;
    AbortButton: TButton;
    MengePanel: TPanel;
    Bevel3: TBevel;
    Label4: TLabel;
    EinheitLabel: TLabel;
    CountLabel: TLabel;
    Label5: TLabel;
    SperrEinheitLabel: TLabel;
    Label7: TLabel;
    MengeEdit: TEdit;
    SperrMengeEdit: TEdit;
    SperrGrundEdit: TEdit;
    LPPanel: TPanel;
    Bevel1: TBevel;
    Label2: TLabel;
    LPLabel: TLabel;
    AnzLEEdit: TEdit;
    Label3: TLabel;
    Label6: TLabel;
    SelectArtikelPanel: TPanel;
    Label10: TLabel;
    Bevel4: TBevel;
    ArNrEdit: TEdit;
    ArtikelComboBox: TComboBoxPro;
    ListedCheckBox: TCheckBox;
    AnzLEUpDown: TUpDown;
    Label1: TLabel;
    LTComboBox: TComboBoxPro;
    procedure ArtikelComboBoxDropDown(Sender: TObject);
    procedure ArtikelComboBoxChange(Sender: TObject);
    procedure ArtikelComboBoxCloseUp(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure ArNrEditExit(Sender: TObject);
    procedure MengeEditExit(Sender: TObject);
    procedure MengeEditKeyPress(Sender: TObject; var Key: Char);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
    fRefMand    : Integer;
    fRefSubMand : Integer;
    fRefLager   : Integer;

    fRefLE,
    fRefArtikel,
    fRefEinheit,
    fRefArEinheit : Integer;

    fOldArNrEdit : String;
    fMHDChecked  : Boolean;
    fArtikelComboxLoaded : Boolean;

    arinfo : TArtikelInfo;

    function ReloadArtikel : Integer;
  public
    property RefArtikel   : Integer read fRefArtikel;
    property RefEinheit   : Integer read fRefEinheit;
    property RefArEinheit : Integer read fRefArEinheit;

    procedure Prepare (const RefPlan : Integer);
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DB, ADODB, BetterADODataSet, DatenModul, ConfigModul, SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function TInvBulkCountInputForm.ReloadArtikel : Integer;
var
  dlgres : Integer;
  query  : TBetterADODataSet;
begin
  Screen.Cursor := crSQLWait;

  query := TBetterADODataSet.Create (Nil);
  try
    query.Connection := LVSDatenModul.MainADOConnection;

    if (LVSConfigModul.UseArtikelCollis) then
      query.CommandText := 'select REF_AR_EINHEIT, ARTIKEL_NR||chr(9)||ARTIKEL_TEXT||chr(9)||EINHEIT||chr(9)||COLLI_NAME from V_ARTIKEL_SUCHE where nvl (OPT_MULTI_COLLI, ''0'')=''0'''
    else
      query.CommandText := 'select REF_AR_EINHEIT, ARTIKEL_NR||chr(9)||ARTIKEL_TEXT||chr(9)||EINHEIT from V_ARTIKEL_SUCHE where OPT_MASTER=''1''';

    if (fRefSubMand > 0) Then begin
      query.CommandText := query.CommandText + ' and REF_SUB_MAND=:ref_sub_mand';
      query.Parameters.ParamByName('ref_sub_mand').Value := fRefSubMand;
    end else begin
      query.CommandText := query.CommandText + ' and REF_MAND=:ref_mand';
      query.Parameters.ParamByName('ref_mand').Value := fRefMand;
    end;

    if (Length (ArNrEdit.Text) > 0) then begin
      query.CommandText := query.CommandText + ' and (ARTIKEL_NR like :ar_nr)';
      query.Parameters.ParamByName('ar_nr').Value := ArNrEdit.Text+'%';
    end;

    if not (ListedCheckBox.Checked) then begin
      query.CommandText := query.CommandText + ' and REF in (select REF_AR from V_ARTIKEL_REL_LAGER where nvl (OPT_DEPEND,''0'')=''0'' and STATUS in (''AKT'',''MAN'') and REF_LAGER=:ref_lager)';
      query.Parameters.ParamByName('ref_lager').Value := fRefLager;
    end;

    query.CommandText := query.CommandText + ' order by LPAD (ARTIKEL_NR,32,'' ''), COLLI_NAME';

    try
      query.Open;

      if (query.RecordCount < 10000) then
        dlgres := mrYes
      else
        dlgres := MessageDLG (FormatMessageText (1364, [IntToStr (query.RecordCount)]), mtConfirmation, [mbYes, mbNo, mbCancel], 0);

      if (dlgres = mrYes) then begin
        ArtikelComboBox.Items.BeginUpdate;

        try
          ClearComboBoxObjects (ArtikelComboBox);

          while not (query.Eof) do begin
            ArtikelComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

            query.Next;
          end;
        finally
          ArtikelComboBox.Items.EndUpdate;
        end;

        fOldArNrEdit := ArNrEdit.Text;
      end;

      query.Close;
    except
    end;
  finally
    query.Free;

    Screen.Cursor := crDefault;
  end;

  fArtikelComboxLoaded := True;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 01.04.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInvBulkCountInputForm.ArNrEditExit(Sender: TObject);
begin
  if not (AbortButton.Focused) then begin
    if not (fArtikelComboxLoaded) or (fOldArNrEdit <> ArNrEdit.Text) then
      ReloadArtikel;

    if (ArtikelComboBox.Items.Count = 1) then begin
      ArtikelComboBox.ItemIndex := 0;

      ArtikelComboBoxChange (Sender);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.12.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInvBulkCountInputForm.ArtikelComboBoxChange(Sender: TObject);
begin
  if (GetArtikelInfos (-1, GetComboBoxRef (ArtikelComboBox), arinfo) = 0) then begin
    fRefArEinheit := arinfo.RefArtikelEinheit;
    fRefArtikel := arinfo.RefArtikel;
    fRefEinheit := arinfo.RefEinheit;

    EinheitLabel.Caption := arinfo.Einheit;
    SperrEinheitLabel.Caption := arinfo.Einheit;

    MHDEdit.Enabled      := (arinfo.MHDArt <> 'O');
    MHDDutyLabel.Visible := MHDEdit.Enabled;

    ChargeEdit.Enabled      := (arinfo.ChargeArt <> 'N');
    ChargeDutyLabel.Visible := (arinfo.ChargeArt = 'P');
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 03.04.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInvBulkCountInputForm.ArtikelComboBoxCloseUp(Sender: TObject);
begin
  ArtikelComboBox.ColWidths [1] := 0;
  ArtikelComboBox.Prepare;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 03.04.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInvBulkCountInputForm.ArtikelComboBoxDropDown(Sender: TObject);
var
  textstr  : String;
  i,
  len,
  wbox,
  nrmaxlen,
  armaxlen,
  vpemaxlen,
  collimaxlen : Integer;
begin
  if not (fArtikelComboxLoaded) or (fOldArNrEdit <> ArNrEdit.Text) then
    ReloadArtikel;

  if (LVSConfigModul.UseArtikelCollis) then
    ArtikelComboBox.ColumeCount := 4
  else
    ArtikelComboBox.ColumeCount := 3;

  nrmaxlen    := 0;
  armaxlen    := 0;
  vpemaxlen   := 0;
  collimaxlen := 0;

  for i := 0 to ArtikelComboBox.Items.Count - 1 do begin
    textstr := ArtikelComboBox.GetItemText (i, 0);
    len := ArtikelComboBox.Canvas.TextWidth (textstr);

    if (len > nrmaxlen) then
      nrmaxlen := len;

    textstr := ArtikelComboBox.GetItemText (i, 1);
    len := ArtikelComboBox.Canvas.TextWidth (textstr);

    if (len > armaxlen) then
      armaxlen := len;

    textstr := ArtikelComboBox.GetItemText (i, 2);
    len := ArtikelComboBox.Canvas.TextWidth (textstr);

    if (len > vpemaxlen) then
      vpemaxlen := len;

    if (ArtikelComboBox.ColumeCount = 4) then begin
      textstr := ArtikelComboBox.GetItemText (i, 3);
      len := ArtikelComboBox.Canvas.TextWidth (textstr);

      if (len > collimaxlen) then
        collimaxlen := len;
    end;
  end;

  //Dann werden die drei Punkte nicht mehr dargestellt
  armaxlen  := armaxlen + 8;
  vpemaxlen := vpemaxlen + 8;

  wbox := nrmaxlen + armaxlen + vpemaxlen + collimaxlen + 32;

  ArtikelComboBox.ColWidths [0] := nrmaxlen;
  ArtikelComboBox.ColWidths [1] := ArtikelComboBox.ColWidths [0] + armaxlen;

  if (collimaxlen > 0) then
    ArtikelComboBox.ColWidths [2] := ArtikelComboBox.ColWidths [1] + vpemaxlen;

  ArtikelComboBox.Perform(CB_SETDROPPEDWIDTH, wbox, 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 01.04.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInvBulkCountInputForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  menge : Integer;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    if (SelectArtikelPanel.Visible and (ArtikelComboBox.ItemIndex < 0)) then begin
      CanClose := false;

      if (Length (ArNrEdit.Text) = 0) then
        ArNrEdit.SetFocus
      else
        ArtikelComboBox.SetFocus;
    end else if (Length (MengeEdit.Text) > 0) and not (TryStrToInt (MengeEdit.Text, menge)) then begin
      CanClose := false;
      MengeEdit.SetFocus;
    end else if (AnzLEUpDown.Position <= 0) then begin
      CanClose := false;
      AnzLEEdit.SetFocus;
    end else if (arinfo.MHDArt = 'M') and (Length (MHDEdit.Text) = 0) then begin
      CanClose := false;
      if MHDEdit.CanFocus then MHDEdit.SetFocus;
    end else if (arinfo.ChargeArt = 'P') and (Length (ChargeEdit.Text) = 0) then begin
      CanClose := false;
      if ChargeEdit.CanFocus then ChargeEdit.SetFocus;
    end else if (arinfo.UniqueCharge and (Length (MengeEdit.Text) > 0) and (menge > 1)) then begin
      CanClose := false;
      MengeEdit.SetFocus;
    end else if (Length (SperrMengeEdit.Text) > 0) and not (TryStrToInt (SperrMengeEdit.Text, menge)) then begin
      CanClose := false;
      SperrMengeEdit.SetFocus
    end else if not (GetComboBoxRef (LTComboBox) > 0) then begin
      CanClose := false;
      LTComboBox.SetFocus
    end else if (arinfo.UniqueCharge and (Length (SperrMengeEdit.Text) > 0) and (menge > 1)) then begin
      CanClose := false;
      SperrMengeEdit.SetFocus;
    end else
      CanClose := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 31.03.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInvBulkCountInputForm.FormCreate(Sender: TObject);
begin
  arinfo := TArtikelInfo.Create;

  AnzLEEdit.Text      := '';
  MengeEdit.Text      := '';
  SperrMengeEdit.Text := '';
  SperrGrundEdit.Text := '';
  ArNrEdit.Text       := '';
  MHDEdit.Text        := '';
  ChargeEdit.Text     := '';

  fMHDChecked          := False;
  fOldArNrEdit         := '';
  fArtikelComboxLoaded := False;

  fRefArtikel   := -1;
  fRefEinheit   := -1;
  fRefArEinheit := -1;

  SelectArtikelPanel.Visible := False;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LPLabel);
    LVSSprachModul.SetNoTranslate (Self, EinheitLabel);
    LVSSprachModul.SetNoTranslate (Self, SperrEinheitLabel);
    LVSSprachModul.SetNoTranslate (Self, ArtikelComboBox);
    LVSSprachModul.SetNoTranslate (Self, LTComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 31.03.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInvBulkCountInputForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects(ArtikelComboBox);
  ClearComboBoxObjects(LTComboBox);

  arinfo.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 23.01.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInvBulkCountInputForm.MengeEditExit(Sender: TObject);
begin
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 07.12.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInvBulkCountInputForm.MengeEditKeyPress(Sender: TObject; var Key: Char);
var
  intwert : Integer;
begin
  if not (Key in [#8,#9,^C,^V,'0'..'9']) then
    Key := #0
  else if (arinfo.UniqueCharge and (Key in ['0'..'9'])) then begin
    if not TryStrToInt (MengeEdit.Text + Key, intwert) then
      Key := #0
    else if (intwert > 1) then
      Key := #0;
  end;

  if (Key = #0) then
    Beep;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 27.03.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TInvBulkCountInputForm.Prepare (const RefPlan : Integer);
var
  intwert   : Integer;
  posquery  : TBetterADODataSet;
begin
  MHDPanel.Visible    := LVSConfigModul.UseMHD;
  ChargePanel.Visible := LVSConfigModul.UseCharge;

  posquery := TBetterADODataSet.Create(Self);

  try
    posquery.LockType := ltReadOnly;
    posquery.Connection := LVSDatenModul.MainADOConnection;

    MHDEdit.Text := '';
    ChargeEdit.Text := '';

    SelectArtikelPanel.Visible := true;

    EinheitLabel.Caption      := '';
    SperrEinheitLabel.Caption := '';

    posquery.CommandText := 'select i.REF_MAND,i.REF_SUB_MAND,i.REF_LAGER,plan.COUNT_NR,plan.REF_LP,plan.LP_NR,plan.LP_KOOR,plan.REF_LE from V_INVENTUR i, V_INVENTUR_POS plan where i.REF=plan.REF_INV and plan.REF=:ref';
    posquery.Parameters.ParamByName('ref').Value := RefPlan;

    posquery.Open;

    fRefMand    := posquery.FieldByName ('REF_MAND').AsInteger;
    fRefSubMand := DBGetReferenz (posquery.FieldByName ('REF_SUB_MAND'));
    fRefLager   := posquery.FieldByName ('REF_LAGER').AsInteger;

    LoadLTCombobox (LTCombobox, 'LE', LVSDatenModul.AktLocationRef, fRefLager);

    if (UserReg.ReadRegValue ('SortLP', intwert) <> 0) then
      intwert := 1;

    fRefLE := DBGetReferenz (posquery.FieldByName ('REF_LE'));

    if (posquery.FieldByName ('REF_LP').IsNull) then
      LPLabel.Caption := ''
    else if (intwert = 1) then
      LPLabel.Caption := posquery.FieldByName ('LP_NR').AsString + ' ('+posquery.FieldByName ('LP_KOOR').AsString + ')'
    else LPLabel.Caption := posquery.FieldByName ('LP_KOOR').AsString + ' ('+posquery.FieldByName ('LP_NR').AsString + ')';

    CountLabel.Caption := posquery.FieldByName ('COUNT_NR').AsString + GetResourceText (1734);

    posquery.Close;
  finally
    posquery.Free;
  end;
end;

end.
