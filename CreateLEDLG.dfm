object CreateLEForm: TCreateLEForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Neuer Ladungstr'#228'ger anlegen'
  ClientHeight = 211
  ClientWidth = 281
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    281
    211)
  PixelsPerInch = 96
  TextHeight = 13
  object Label7: TLabel
    Left = 8
    Top = 64
    Width = 89
    Height = 13
    Caption = 'Ladungstr'#228'ger-Art'
  end
  object Label5: TLabel
    Left = 8
    Top = 112
    Width = 113
    Height = 13
    Caption = 'Ladungstr'#228'ger-Nummer'
  end
  object Label2: TLabel
    Left = 8
    Top = 8
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object LTComboBox: TComboBoxPro
    Left = 8
    Top = 80
    Width = 265
    Height = 19
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 13
    TabOrder = 1
  end
  object Button2: TButton
    Left = 136
    Top = 126
    Width = 137
    Height = 25
    Caption = 'LE-Nummer erzeugen'
    TabOrder = 3
    OnClick = Button2Click
  end
  object LENrEdit: TEdit
    Left = 8
    Top = 128
    Width = 117
    Height = 21
    TabOrder = 2
    Text = 'LENrEdit'
  end
  object OkButton: TButton
    Left = 112
    Top = 181
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 4
  end
  object AbortButton: TButton
    Left = 198
    Top = 181
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 265
    Height = 22
    Style = csOwnerDrawFixed
    ItemHeight = 16
    TabOrder = 0
    OnChange = LagerComboBoxChange
  end
end
