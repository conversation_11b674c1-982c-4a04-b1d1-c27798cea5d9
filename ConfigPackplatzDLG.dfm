object ConfigPackplatzForm: TConfigPackplatzForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Packpl'#228'tze verwalten'
  ClientHeight = 514
  ClientWidth = 667
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    667
    514)
  PixelsPerInch = 96
  TextHeight = 13
  object Label3: TLabel
    Left = 8
    Top = 64
    Width = 51
    Height = 13
    Caption = 'Packpl'#228'tze'
  end
  object Label4: TLabel
    Left = 8
    Top = 8
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Bevel1: TBevel
    Left = 5
    Top = 55
    Width = 653
    Height = 6
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
  end
  object PackplatzDBGrid: TDBGridPro
    Left = 8
    Top = 80
    Width = 539
    Height = 153
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = PackplatzDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = PackplatzDBGridPopupMenu
    ReadOnly = True
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    OnDblClick = PackplatzDBGridDblClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\CS'
    RegistrySection = 'DBGrids'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object NewPlaceButton: TButton
    Left = 560
    Top = 80
    Width = 95
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Neuer Platz...'
    TabOrder = 2
    OnClick = NewPlaceButtonClick
  end
  object DelPlaceButton: TButton
    Left = 560
    Top = 208
    Width = 95
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Platz l'#246'schen...'
    TabOrder = 4
    OnClick = DelPlaceButtonClick
  end
  object CloseButton: TButton
    Left = 560
    Top = 481
    Width = 95
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 7
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 647
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 0
    OnChange = LagerComboBoxChange
  end
  object GroupBox2: TGroupBox
    Left = 8
    Top = 248
    Width = 647
    Height = 226
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'F'#228'cher'
    TabOrder = 6
    object FachStringGrid: TStringGridPro
      Left = 8
      Top = 27
      Width = 389
      Height = 190
      ColCount = 4
      DefaultColWidth = 20
      DefaultRowHeight = 18
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goRowSelect]
      PopupMenu = FachStringGridPopupMenu
      TabOrder = 0
      OnDblClick = NewCaseButtonClick
      TitelTexte.Strings = (
        ''
        'Bezeichnung'
        'Nummer'
        'Kennung')
      TitelFont.Charset = DEFAULT_CHARSET
      TitelFont.Color = clWindowText
      TitelFont.Height = -11
      TitelFont.Name = 'Tahoma'
      TitelFont.Style = []
      ColWidths = (
        20
        75
        60
        93)
    end
    object NewCaseButton: TButton
      Left = 403
      Top = 27
      Width = 95
      Height = 25
      Caption = 'Neues Fach...'
      TabOrder = 1
      OnClick = NewCaseButtonClick
    end
    object DelCaseButton: TButton
      Left = 403
      Top = 192
      Width = 95
      Height = 25
      Caption = 'Fach l'#246'schen...'
      TabOrder = 2
      OnClick = DelCaseButtonClick
    end
  end
  object EditPlaceButton: TButton
    Left = 560
    Top = 148
    Width = 95
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Bearbeiten...'
    TabOrder = 5
    OnClick = PackplatzDBGridDblClick
  end
  object CopyPlaceButton: TButton
    Left = 560
    Top = 114
    Width = 95
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Kopieren...'
    TabOrder = 3
    OnClick = CopyPlaceButtonClick
  end
  object FachStringGridPopupMenu: TPopupMenu
    OnPopup = FachStringGridPopupMenuPopup
    Left = 272
    Top = 408
    object NewCaseMenuItem: TMenuItem
      Caption = 'Neues Fach anlegen...'
      OnClick = NewCaseButtonClick
    end
    object EditFachMenuItem: TMenuItem
      Caption = 'Fachdaten '#228'ndern.'
      OnClick = NewCaseButtonClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object PrintFachLabelMenuItem: TMenuItem
      Caption = 'Fach-Etikett drucken...'
      OnClick = PrintFachLabelMenuItemClick
    end
    object PrintAllFachLabelMenuItem: TMenuItem
      Caption = 'Alle Fach-Etiketten drucken...'
      OnClick = PrintFachLabelMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object DelFachMenuItem: TMenuItem
      Caption = 'Fach l'#246'schen...'
    end
  end
  object PackplatzDataSource: TDataSource
    DataSet = PackplatzQuery
    OnDataChange = PackplatzDataSourceDataChange
    Left = 448
    Top = 88
  end
  object PackplatzQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 480
    Top = 88
  end
  object PackplatzDBGridPopupMenu: TPopupMenu
    OnPopup = PackplatzDBGridPopupMenuPopup
    Left = 288
    Top = 104
    object PackActiveMenuItem: TMenuItem
      Caption = 'Aktivieren'
      OnClick = PackActiveMenuItemClick
    end
    object PackDeactiveMenuItem: TMenuItem
      Caption = 'Deaktivieren'
      OnClick = PackActiveMenuItemClick
    end
  end
end
