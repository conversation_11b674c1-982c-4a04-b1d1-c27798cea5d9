object EditArtikelEinheitenForm: TEditArtikelEinheitenForm
  Left = 232
  Top = 92
  BorderStyle = bsDialog
  ClientHeight = 845
  ClientWidth = 504
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    504
    845)
  TextHeight = 13
  object OkButton: TButton
    Left = 334
    Top = 813
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 11
    OnClick = OkButtonClick
  end
  object AbortButton: TButton
    Left = 419
    Top = 813
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 12
  end
  object EinheitPanel: TPanel
    Left = 0
    Top = 40
    Width = 504
    Height = 35
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      504
      35)
    object Label1: TLabel
      Left = 8
      Top = 0
      Width = 32
      Height = 13
      Caption = 'Einheit'
    end
    object Bevel8: TBevel
      Left = 6
      Top = 29
      Width = 494
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 45
    end
    object EinheitComboBox: TComboBoxPro
      Left = 73
      Top = 0
      Width = 331
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 0
    end
    object VEPButton: TButton
      Left = 419
      Top = 0
      Width = 75
      Height = 21
      Anchors = [akTop, akRight]
      Caption = 'Anlegen...'
      TabOrder = 1
      OnClick = VEPButtonClick
    end
  end
  object OptionPanel: TPanel
    Left = 0
    Top = 75
    Width = 504
    Height = 109
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      504
      109)
    object Bevel6: TBevel
      Left = 6
      Top = 104
      Width = 494
      Height = 4
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 83
    end
    object MasterCheckBox: TCheckBox
      Left = 8
      Top = 2
      Width = 377
      Height = 17
      Caption = 'Dies ist die Handelseinheit'
      TabOrder = 0
    end
    object StueckCheckBox: TCheckBox
      Left = 8
      Top = 18
      Width = 377
      Height = 17
      Caption = 'Dies ist ein St'#252'ck-Artikel'
      TabOrder = 1
    end
    object GewichtCheckBox: TCheckBox
      Left = 8
      Top = 34
      Width = 388
      Height = 17
      Caption = 'Bei diesem Artikel muss das Gewicht erfasst werden'
      TabOrder = 2
    end
    object BeschaffungsCheckBox: TCheckBox
      Left = 8
      Top = 50
      Width = 388
      Height = 17
      Caption = 
        'Diese Artikeleinheit kann im WE vereinnahmt werden (Beschaffungs' +
        'einheit)'
      TabOrder = 3
    end
    object FullPalCheckBox: TCheckBox
      Left = 8
      Top = 66
      Width = 476
      Height = 17
      Caption = 'Ganzplatten-Einheit, nur Menge 1 zul'#228'ssig'
      TabOrder = 4
    end
    object PersistentCheckBox: TCheckBox
      Left = 8
      Top = 82
      Width = 476
      Height = 17
      Caption = 'Einheit nie automatisch l'#246'schen'
      TabOrder = 5
    end
  end
  object BarcodePanel: TPanel
    Left = 0
    Top = 184
    Width = 504
    Height = 54
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      504
      54)
    object Label5: TLabel
      Left = 8
      Top = 1
      Width = 22
      Height = 13
      Caption = 'EAN'
    end
    object Label22: TLabel
      Left = 120
      Top = 2
      Width = 40
      Height = 13
      Caption = 'Barcode'
    end
    object Bevel3: TBevel
      Left = 6
      Top = 46
      Width = 494
      Height = 8
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 394
    end
    object Label54: TLabel
      Left = 440
      Top = 2
      Width = 51
      Height = 13
      Caption = 'Produkt-ID'
    end
    object EANEdit: TEdit
      Left = 8
      Top = 17
      Width = 96
      Height = 21
      MaxLength = 32
      PopupMenu = EANEditPopupMenu
      TabOrder = 0
      Text = '99999999999999'
      OnChange = EANEditChange
      OnExit = EANEditExit
      OnKeyPress = EANEditKeyPress
    end
    object BarcodeEdit: TEdit
      Left = 120
      Top = 17
      Width = 307
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 1
      Text = 'Edit'
      OnExit = EANEditExit
    end
    object ProdIDEdit: TEdit
      Left = 440
      Top = 17
      Width = 54
      Height = 21
      MaxLength = 5
      TabOrder = 2
      Text = 'ProdIDEdit'
      OnKeyPress = EANEditKeyPress
    end
  end
  object InhaltPanel: TPanel
    Left = 0
    Top = 238
    Width = 504
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      504
      57)
    object Label2: TLabel
      Left = 8
      Top = 1
      Width = 54
      Height = 13
      Caption = 'Inhaltartikel'
    end
    object Label3: TLabel
      Left = 440
      Top = 1
      Width = 32
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'Anzahl'
    end
    object Bevel4: TBevel
      Left = 6
      Top = 48
      Width = 494
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object InhaltArComboBox: TComboBoxPro
      Left = 120
      Top = 17
      Width = 307
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 1
      OnChange = InhaltArComboBoxChange
      OnDropDown = InhaltArComboBoxDropDown
    end
    object InhaltAnzEdit: TEdit
      Left = 440
      Top = 17
      Width = 38
      Height = 21
      Anchors = [akTop, akRight]
      TabOrder = 2
      Text = '0'
      OnChange = InhaltAnzEditChange
      OnExit = InhaltAnzEditExit
      OnKeyPress = IntEditKeyPress
    end
    object InhaltAnzUpDown: TIntegerUpDown
      Left = 478
      Top = 17
      Width = 17
      Height = 21
      Anchors = [akTop, akRight]
      Associate = InhaltAnzEdit
      Max = 2500
      TabOrder = 3
      Thousands = False
    end
    object InhaltArNrEdit: TEdit
      Left = 8
      Top = 17
      Width = 96
      Height = 21
      TabOrder = 0
      Text = 'InhaltArNrEdit'
      OnChange = InhaltArNrEditChange
    end
  end
  object ContPanel: TPanel
    Left = 0
    Top = 295
    Width = 504
    Height = 68
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      504
      68)
    object Label53: TLabel
      Left = 8
      Top = 1
      Width = 54
      Height = 13
      Caption = 'Inhaltartikel'
    end
    object Label58: TLabel
      Left = 404
      Top = 4
      Width = 30
      Height = 13
      Alignment = taRightJustify
      Anchors = [akTop, akRight]
      Caption = 'Z'#228'hler'
    end
    object Bevel14: TBevel
      Left = 6
      Top = 62
      Width = 494
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label59: TLabel
      Left = 399
      Top = 35
      Width = 35
      Height = 13
      Alignment = taRightJustify
      Anchors = [akTop, akRight]
      Caption = 'Nenner'
    end
    object Bevel15: TBevel
      Left = 391
      Top = 26
      Width = 103
      Height = 8
      Shape = bsTopLine
    end
    object ContArComboBox: TComboBoxPro
      Left = 120
      Top = 17
      Width = 265
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 1
      OnChange = ContArComboBoxChange
      OnDropDown = InhaltArComboBoxDropDown
    end
    object ContDenominEdit: TEdit
      Left = 440
      Top = 32
      Width = 38
      Height = 21
      Anchors = [akTop, akRight]
      TabOrder = 4
      Text = '0'
      OnChange = InhaltAnzEditChange
      OnExit = InhaltAnzEditExit
      OnKeyPress = IntEditKeyPress
    end
    object ContDenominUpDown: TIntegerUpDown
      Left = 478
      Top = 32
      Width = 17
      Height = 21
      Anchors = [akTop, akRight]
      Associate = ContDenominEdit
      Max = 9999
      TabOrder = 5
      Thousands = False
    end
    object ContArNrEdit: TEdit
      Left = 8
      Top = 17
      Width = 96
      Height = 21
      TabOrder = 0
      Text = 'ContArNrEdit'
      OnChange = InhaltArNrEditChange
    end
    object ContNumerEdit: TEdit
      Left = 440
      Top = 1
      Width = 38
      Height = 21
      Anchors = [akTop, akRight]
      TabOrder = 2
      Text = '0'
      OnChange = InhaltAnzEditChange
      OnExit = InhaltAnzEditExit
      OnKeyPress = IntEditKeyPress
    end
    object ContNumerUpDown: TIntegerUpDown
      Left = 478
      Top = 1
      Width = 16
      Height = 21
      Anchors = [akTop, akRight]
      Associate = ContNumerEdit
      Max = 9999
      TabOrder = 3
      Thousands = False
    end
  end
  object GewichtPanel: TPanel
    Left = 0
    Top = 471
    Width = 504
    Height = 54
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 8
    DesignSize = (
      504
      54)
    object Label4: TLabel
      Left = 8
      Top = 1
      Width = 63
      Height = 13
      Caption = 'Nettogewicht'
    end
    object Label13: TLabel
      Left = 85
      Top = 20
      Width = 23
      Height = 13
      Caption = 'in kg'
    end
    object Label6: TLabel
      Left = 120
      Top = 1
      Width = 65
      Height = 13
      Caption = 'Bruttogewicht'
    end
    object Label14: TLabel
      Left = 197
      Top = 20
      Width = 23
      Height = 13
      Caption = 'in kg'
    end
    object Bevel2: TBevel
      Left = 6
      Top = 46
      Width = 494
      Height = 4
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 394
    end
    object Label41: TLabel
      Left = 268
      Top = 1
      Width = 59
      Height = 13
      Caption = 'Taragewicht'
    end
    object Label42: TLabel
      Left = 342
      Top = 20
      Width = 23
      Height = 13
      Caption = 'in kg'
    end
    object NettoEdit: TEdit
      Left = 8
      Top = 17
      Width = 70
      Height = 21
      TabOrder = 0
      Text = 'NettoEdit'
      OnExit = GewichtEditExit
      OnKeyPress = FloatEditKeyPress
    end
    object BruttoEdit: TEdit
      Left = 120
      Top = 17
      Width = 70
      Height = 21
      TabOrder = 1
      Text = 'BruttoEdit'
      OnExit = GewichtEditExit
      OnKeyPress = FloatEditKeyPress
    end
    object TaraEdit: TEdit
      Left = 268
      Top = 17
      Width = 70
      Height = 21
      TabOrder = 2
      Text = 'TaraEdit'
      OnExit = GewichtEditExit
      OnKeyPress = FloatEditKeyPress
    end
  end
  object AbmessungPanel: TPanel
    Left = 0
    Top = 417
    Width = 504
    Height = 54
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 7
    DesignSize = (
      504
      54)
    object Label7: TLabel
      Left = 8
      Top = 1
      Width = 30
      Height = 13
      Caption = 'L'#228'nge'
    end
    object Label8: TLabel
      Left = 64
      Top = 1
      Width = 27
      Height = 13
      Caption = 'Breite'
    end
    object Label9: TLabel
      Left = 120
      Top = 1
      Width = 26
      Height = 13
      Caption = 'H'#246'he'
    end
    object Label15: TLabel
      Left = 168
      Top = 20
      Width = 54
      Height = 13
      Caption = 'in Millimeter'
    end
    object Bevel10: TBevel
      Left = 6
      Top = 46
      Width = 494
      Height = 4
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 394
    end
    object Label34: TLabel
      Left = 268
      Top = 1
      Width = 41
      Height = 13
      Caption = 'Volumen'
    end
    object Label35: TLabel
      Left = 342
      Top = 20
      Width = 11
      Height = 13
      Caption = 'm'#179
    end
    object Label46: TLabel
      Left = 460
      Top = 20
      Width = 31
      Height = 13
      Caption = 'in Liter'
    end
    object Label47: TLabel
      Left = 400
      Top = 1
      Width = 48
      Height = 13
      Caption = 'F'#252'llmenge'
    end
    object LaengeEdit: TEdit
      Left = 8
      Top = 17
      Width = 40
      Height = 21
      MaxLength = 4
      TabOrder = 0
      Text = 'LaengeEdit'
      OnChange = AbmessungEditChange
      OnKeyPress = IntEditKeyPress
    end
    object BreiteEdit: TEdit
      Left = 64
      Top = 17
      Width = 40
      Height = 21
      MaxLength = 4
      TabOrder = 1
      Text = 'BreiteEdit'
      OnChange = AbmessungEditChange
      OnKeyPress = IntEditKeyPress
    end
    object HoeheEdit: TEdit
      Left = 120
      Top = 17
      Width = 40
      Height = 21
      MaxLength = 4
      TabOrder = 2
      Text = 'HoeheEdit'
      OnChange = AbmessungEditChange
      OnKeyPress = IntEditKeyPress
    end
    object VolumenEdit: TEdit
      Left = 268
      Top = 17
      Width = 70
      Height = 21
      TabOrder = 3
      Text = 'VolumenEdit'
      OnKeyPress = VolumenEditKeyPress
    end
    object FillingEdit: TEdit
      Left = 400
      Top = 17
      Width = 52
      Height = 21
      TabOrder = 4
      Text = 'FillingEdit'
      OnExit = FillingEditExit
      OnKeyPress = FloatEditKeyPress
    end
  end
  object PalettenFaktorPanel: TPanel
    Left = 0
    Top = 525
    Width = 504
    Height = 54
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 9
    DesignSize = (
      504
      54)
    object Label10: TLabel
      Left = 8
      Top = 1
      Width = 89
      Height = 13
      Caption = 'Einheiten pro Lage'
    end
    object Label11: TLabel
      Left = 120
      Top = 1
      Width = 98
      Height = 13
      Caption = 'Einheiten pro Palette'
    end
    object Bevel7: TBevel
      Left = 6
      Top = 46
      Width = 494
      Height = 4
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 394
    end
    object Label48: TLabel
      Left = 268
      Top = 1
      Width = 99
      Height = 13
      Caption = 'Paletten Stapelfaktor'
    end
    object LagenFaktorEdit: TEdit
      Left = 8
      Top = 17
      Width = 57
      Height = 21
      MaxLength = 5
      TabOrder = 0
      Text = 'LagenFaktorEdit'
      OnKeyPress = IntEditKeyPress
    end
    object PalFaktorEdit: TEdit
      Left = 120
      Top = 17
      Width = 57
      Height = 21
      MaxLength = 5
      TabOrder = 1
      Text = 'PalFaktorEdit'
      OnKeyPress = IntEditKeyPress
    end
    object StapelFaktorEdit: TEdit
      Left = 268
      Top = 17
      Width = 48
      Height = 21
      MaxLength = 1
      TabOrder = 2
      Text = 'StapelFaktorEdit'
    end
  end
  object PagePanel: TPanel
    Left = 0
    Top = 579
    Width = 504
    Height = 224
    Align = alTop
    BevelOuter = bvNone
    Caption = 'PagePanel'
    TabOrder = 10
    DesignSize = (
      504
      224)
    object PageControl1: TPageControl
      Left = 8
      Top = 0
      Width = 486
      Height = 224
      ActivePage = ColliTabSheet
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      object ColliTabSheet: TTabSheet
        Caption = 'Colli-Verwaltung'
        ImageIndex = 7
        object Label24: TLabel
          Left = 344
          Top = 16
          Width = 96
          Height = 13
          Caption = 'Einheite besteht aus'
        end
        object Label25: TLabel
          Left = 405
          Top = 35
          Width = 63
          Height = 13
          Caption = 'Packst'#252'cken'
        end
        object Label33: TLabel
          Left = 8
          Top = 16
          Width = 62
          Height = 13
          Caption = 'Bezeichnung'
        end
        object AnzColliEdit: TEdit
          Left = 344
          Top = 32
          Width = 56
          Height = 21
          TabOrder = 1
          Text = 'AnzColliEdit'
          OnKeyPress = IntEditKeyPress
        end
        object MuliColliPanel: TPanel
          Left = 0
          Top = 65
          Width = 478
          Height = 131
          Align = alBottom
          BevelOuter = bvNone
          TabOrder = 2
          object Bevel1: TBevel
            Left = 6
            Top = 6
            Width = 467
            Height = 12
            Shape = bsTopLine
          end
          object ColliCheckBox: TCheckBox
            Left = 8
            Top = 17
            Width = 458
            Height = 17
            Caption = 'Einheit '#252'ber mehreren Collis verteilen'
            TabOrder = 0
          end
          object ColliStringGrid: TStringGridPro
            Left = 8
            Top = 40
            Width = 464
            Height = 80
            ColCount = 6
            DefaultColWidth = 20
            DefaultRowHeight = 18
            RowCount = 3
            Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRowSelect]
            TabOrder = 1
            TitelTexte.Strings = (
              ''
              'Colli-Nr.'
              'Collibezeichung'
              'Mengen'
              'Einheit'
              'EAN')
            TitelFont.Charset = DEFAULT_CHARSET
            TitelFont.Color = clWindowText
            TitelFont.Height = -11
            TitelFont.Name = 'Tahoma'
            TitelFont.Style = []
            ColWidths = (
              20
              57
              116
              49
              64
              118)
          end
        end
        object NameEdit: TEdit
          Left = 8
          Top = 32
          Width = 318
          Height = 21
          MaxLength = 128
          TabOrder = 0
          Text = 'NameEdit'
        end
      end
      object SizeTabSheet: TTabSheet
        Caption = 'Gr'#246#223'e und Farbe'
        object Label28: TLabel
          Left = 8
          Top = 43
          Width = 29
          Height = 13
          Caption = 'Gr'#246#223'e'
        end
        object Label29: TLabel
          Left = 8
          Top = 73
          Width = 27
          Height = 13
          Caption = 'Farbe'
        end
        object Label30: TLabel
          Left = 104
          Top = 16
          Width = 25
          Height = 13
          Caption = 'Code'
        end
        object Label31: TLabel
          Left = 264
          Top = 16
          Width = 21
          Height = 13
          Caption = 'Text'
        end
        object Label49: TLabel
          Left = 8
          Top = 104
          Width = 37
          Height = 13
          Caption = 'Material'
        end
        object SizeCodeEdit: TEdit
          Left = 72
          Top = 40
          Width = 121
          Height = 21
          MaxLength = 32
          TabOrder = 0
          Text = 'SizeCodeEdit'
          OnChange = SizeColorEditChange
        end
        object ColorCodeEdit: TEdit
          Left = 72
          Top = 70
          Width = 121
          Height = 21
          MaxLength = 32
          TabOrder = 1
          Text = 'ColorCodeEdit'
          OnChange = SizeColorEditChange
        end
        object SizeTextEdit: TEdit
          Left = 232
          Top = 40
          Width = 121
          Height = 21
          MaxLength = 32
          TabOrder = 2
          Text = 'SizeTextEdit'
          OnChange = SizeColorEditChange
        end
        object ColorTextEdit: TEdit
          Left = 232
          Top = 70
          Width = 121
          Height = 21
          MaxLength = 32
          TabOrder = 3
          Text = 'ColorTextEdit'
          OnChange = SizeColorEditChange
        end
        object MaterialCodeEdit: TEdit
          Left = 72
          Top = 101
          Width = 121
          Height = 21
          MaxLength = 32
          TabOrder = 4
          Text = 'ColorCodeEdit'
          OnChange = SizeColorEditChange
        end
        object MaterialTextEdit: TEdit
          Left = 232
          Top = 101
          Width = 121
          Height = 21
          MaxLength = 32
          TabOrder = 5
          Text = 'MaterialTextEdit'
          OnChange = SizeColorEditChange
        end
      end
      object KommTabSheet: TTabSheet
        Caption = 'Kommissionierung'
        ImageIndex = 6
        DesignSize = (
          478
          196)
        object Label32: TLabel
          Left = 8
          Top = 10
          Width = 78
          Height = 13
          Caption = 'Kommissionierart'
        end
        object Label43: TLabel
          Left = 410
          Top = 10
          Width = 53
          Height = 13
          Caption = 'Stapelfolge'
        end
        object Bevel11: TBevel
          Left = 3
          Top = 56
          Width = 472
          Height = 8
          Anchors = [akLeft, akTop, akRight]
          Shape = bsTopLine
        end
        object Bevel12: TBevel
          Left = 3
          Top = 88
          Width = 472
          Height = 8
          Anchors = [akLeft, akTop, akRight]
          Shape = bsTopLine
        end
        object Bevel13: TBevel
          Left = 3
          Top = 144
          Width = 472
          Height = 8
          Anchors = [akLeft, akTop, akRight]
          Shape = bsTopLine
        end
        object KommArtComboBox: TComboBoxPro
          Left = 8
          Top = 26
          Width = 375
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 0
        end
        object BigItemCheckBox: TCheckBox
          Left = 8
          Top = 63
          Width = 375
          Height = 17
          Caption = 'Big Item'
          TabOrder = 2
        end
        object StapelEdit: TEdit
          Left = 410
          Top = 26
          Width = 58
          Height = 21
          TabOrder = 1
          Text = 'StapelEdit'
          OnKeyPress = IntEditKeyPress
        end
        object CuttingCheckBox: TCheckBox
          Left = 8
          Top = 98
          Width = 457
          Height = 17
          Caption = 'Ware muss abgel'#228'ngt werden'
          TabOrder = 3
          OnClick = CuttingCheckBoxClick
        end
        object CuttingFullCheckBox: TCheckBox
          Left = 28
          Top = 120
          Width = 437
          Height = 17
          Caption = 'Keine Teilst'#252'cke bilden'
          TabOrder = 4
        end
        object FullPalPickCheckBox: TCheckBox
          Left = 8
          Top = 153
          Width = 457
          Height = 17
          Caption = 'Ware nur auf Vollpaletten kommissionieren'
          TabOrder = 5
          OnClick = FullPalPickCheckBoxClick
        end
        object FullPalOverCheckBox: TCheckBox
          Left = 28
          Top = 174
          Width = 437
          Height = 17
          Caption = 'Bei Vollpaletten kann '#252'berbucht werden'
          TabOrder = 6
        end
      end
      object PfandTabSheet: TTabSheet
        Caption = 'Pfand'
        object Label23: TLabel
          Left = 8
          Top = 16
          Width = 92
          Height = 13
          Caption = 'Pfandladungstr'#228'ger'
        end
        object PfandTauschComboBox: TComboBoxPro
          Left = 8
          Top = 32
          Width = 217
          Height = 21
          Style = csOwnerDrawFixed
          ItemHeight = 15
          TabOrder = 0
          OnChange = PfandTauschComboBoxChange
        end
        object PfandOptCheckBox: TCheckBox
          Left = 8
          Top = 68
          Width = 165
          Height = 17
          Caption = 'automatisch verwalten'
          TabOrder = 1
        end
      end
      object LTTabSheet: TTabSheet
        Caption = 'Ladungstr'#228'ger'
        ImageIndex = 1
        DesignSize = (
          478
          196)
        object Label17: TLabel
          Left = 8
          Top = 64
          Width = 108
          Height = 13
          Caption = 'Standard H'#246'henklasse'
        end
        object Label18: TLabel
          Left = 8
          Top = 16
          Width = 148
          Height = 13
          Caption = 'Standard Ladungstr'#228'ger im WE'
        end
        object Label21: TLabel
          Left = 392
          Top = 64
          Width = 54
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'Stapelh'#246'he'
        end
        object Label19: TLabel
          Left = 449
          Top = 83
          Width = 16
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'mm'
        end
        object Label44: TLabel
          Left = 392
          Top = 112
          Width = 54
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'Lagenh'#246'he'
        end
        object Label45: TLabel
          Left = 449
          Top = 131
          Width = 16
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'mm'
        end
        object HKLComboBox: TComboBoxPro
          Left = 8
          Top = 80
          Width = 321
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 1
        end
        object LTWEComboBox: TComboBoxPro
          Left = 8
          Top = 32
          Width = 461
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 0
        end
        object PalHeightEdit: TEdit
          Left = 392
          Top = 80
          Width = 50
          Height = 21
          Anchors = [akTop, akRight]
          MaxLength = 4
          TabOrder = 2
          Text = 'PalHeightEdit'
          OnKeyPress = IntEditKeyPress
        end
        object LayerHeightEdit: TEdit
          Left = 392
          Top = 128
          Width = 50
          Height = 21
          Anchors = [akTop, akRight]
          MaxLength = 4
          TabOrder = 3
          Text = 'LayerHeightEdit'
          OnKeyPress = IntEditKeyPress
        end
      end
      object WertTabSheet: TTabSheet
        Caption = 'Warenwert'
        ImageIndex = 2
        object Label20: TLabel
          Left = 291
          Top = 35
          Width = 6
          Height = 13
          Caption = #8364
        end
        object Label16: TLabel
          Left = 160
          Top = 16
          Width = 76
          Height = 13
          Caption = 'Wert pro Einheit'
        end
        object Label12: TLabel
          Left = 8
          Top = 16
          Width = 54
          Height = 13
          Caption = 'Preiseinheit'
        end
        object PreisEdit: TEdit
          Left = 160
          Top = 32
          Width = 121
          Height = 21
          TabOrder = 1
          Text = 'PreisEdit'
          OnKeyPress = FloatEditKeyPress
        end
        object PreisEinheitComboBox: TComboBoxPro
          Left = 8
          Top = 32
          Width = 137
          Height = 21
          TabOrder = 0
          Text = 'PreisEinheitComboBox'
          OnChange = PreisEinheitComboBoxChange
          Items.Strings = (
            ''
            'Preis pro Einheit'
            'Preis pro Kilo')
        end
      end
      object OrderTabSheet: TTabSheet
        Caption = 'Beschaffung'
        ImageIndex = 3
        object Label26: TLabel
          Left = 8
          Top = 16
          Width = 75
          Height = 13
          Caption = 'Mindestbestand'
        end
        object Label27: TLabel
          Left = 8
          Top = 64
          Width = 141
          Height = 13
          Caption = 'Minimale Beschaffungsmenge'
        end
        object MinArBesEdit: TEdit
          Left = 8
          Top = 32
          Width = 121
          Height = 21
          TabOrder = 0
          Text = 'MinArBesEdit'
          OnChange = PurchesEditChange
          OnKeyPress = IntEditKeyPress
        end
        object MinOrderBesEdit: TEdit
          Left = 8
          Top = 80
          Width = 121
          Height = 21
          TabOrder = 1
          Text = 'MinOrderBesEdit'
          OnChange = PurchesEditChange
          OnKeyPress = IntEditKeyPress
        end
      end
      object InventarTabSheet: TTabSheet
        Caption = 'Inventar'
        ImageIndex = 5
        object LifeCheckBox: TCheckBox
          Left = 8
          Top = 16
          Width = 355
          Height = 17
          Caption = 'F'#252'r diese Einheit k'#246'nnen Inventarobjekte angelegt werden'
          TabOrder = 0
        end
      end
      object VersandTabSheet: TTabSheet
        Caption = 'Versand'
        ImageIndex = 8
        DesignSize = (
          478
          196)
        object Label51: TLabel
          Left = 8
          Top = 64
          Width = 75
          Height = 13
          Caption = 'Verpackungsart'
        end
        object Label52: TLabel
          Left = 128
          Top = 65
          Width = 83
          Height = 13
          Caption = 'Verpackungsform'
        end
        object Label55: TLabel
          Left = 8
          Top = 148
          Width = 51
          Height = 13
          Caption = 'Versandart'
        end
        object Label56: TLabel
          Left = 8
          Top = 106
          Width = 44
          Height = 13
          Caption = 'Spedition'
        end
        object Label57: TLabel
          Left = 239
          Top = 64
          Width = 125
          Height = 13
          Caption = 'Standard Verpackung WA'
        end
        object ReadyShipCheckBox: TCheckBox
          Left = 8
          Top = 16
          Width = 300
          Height = 17
          Caption = 'Die Einheit ist versandf'#228'hig verpackt'
          TabOrder = 0
        end
        object SperrgutCheckBox: TCheckBox
          Left = 8
          Top = 39
          Width = 300
          Height = 17
          Caption = 'Die Einheit muss als Sperrgut verschickt werden'
          TabOrder = 1
        end
        object VerpackArtEdit: TEdit
          Left = 8
          Top = 80
          Width = 100
          Height = 21
          MaxLength = 32
          TabOrder = 2
          Text = 'VerpackArtEdit'
        end
        object VerpackFormEdit: TEdit
          Left = 128
          Top = 80
          Width = 100
          Height = 21
          MaxLength = 32
          TabOrder = 3
          Text = 'VerpackFormEdit'
        end
        object VersandArtComboBox: TComboBoxPro
          Left = 8
          Top = 164
          Width = 464
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 5
        end
        object VersandSpedComboBox: TComboBoxPro
          Left = 8
          Top = 122
          Width = 464
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 6
        end
        object LTWAComboBox: TComboBoxPro
          Left = 239
          Top = 80
          Width = 233
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 4
        end
      end
    end
  end
  object UnitPanel: TPanel
    Left = 0
    Top = 363
    Width = 504
    Height = 54
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 6
    DesignSize = (
      504
      54)
    object Label36: TLabel
      Left = 8
      Top = 1
      Width = 109
      Height = 13
      Caption = 'Zus'#228'tzliche Ma'#223'einheit'
    end
    object Bevel5: TBevel
      Left = 6
      Top = 46
      Width = 494
      Height = 4
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 394
    end
    object Label37: TLabel
      Left = 268
      Top = 1
      Width = 136
      Height = 13
      Caption = 'Nettogewicht pro Ma'#223'einheit'
    end
    object Label38: TLabel
      Left = 342
      Top = 20
      Width = 23
      Height = 13
      Caption = 'in kg'
    end
    object Label39: TLabel
      Left = 148
      Top = 1
      Width = 98
      Height = 13
      Caption = 'Normma'#223' pro Einheit'
    end
    object Label40: TLabel
      Left = 225
      Top = 20
      Width = 8
      Height = 13
      Caption = 'm'
    end
    object UnitsComboBox: TComboBoxPro
      Left = 8
      Top = 17
      Width = 127
      Height = 19
      Style = csOwnerDrawFixed
      ItemHeight = 13
      TabOrder = 0
      OnChange = UnitsComboBoxChange
    end
    object UnitsNettoEdit: TEdit
      Left = 268
      Top = 17
      Width = 70
      Height = 21
      TabOrder = 2
      Text = 'UnitsNettoEdit'
      OnExit = GewichtEditExit
      OnKeyPress = FloatEditKeyPress
    end
    object UnitsNormEdit: TEdit
      Left = 148
      Top = 17
      Width = 70
      Height = 21
      TabOrder = 1
      Text = 'UnitsNormEdit'
      OnExit = UnitsNormEditExit
      OnKeyPress = FloatEditKeyPress
    end
  end
  object IDPanel: TPanel
    Left = 0
    Top = 0
    Width = 504
    Height = 40
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      504
      40)
    object Bevel9: TBevel
      Left = 6
      Top = 33
      Width = 494
      Height = 9
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 52
    end
    object Label50: TLabel
      Left = 8
      Top = 8
      Width = 59
      Height = 13
      Caption = 'Varianten-ID'
    end
    object IDEdit: TEdit
      Left = 73
      Top = 5
      Width = 396
      Height = 21
      MaxLength = 32
      TabOrder = 0
      Text = 'IDEdit'
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 336
    Top = 112
  end
  object EANEditPopupMenu: TPopupMenu
    OnPopup = EANEditPopupMenuPopup
    Left = 224
    Top = 112
    object CreateEANMenuItem: TMenuItem
      Caption = 'Neuen EAN erzeugen...'
      OnClick = CreateEANMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object Rckgngig1: TMenuItem
      Caption = 'R'#252'ckg'#228'ngig'
      OnClick = Rckgngig1Click
    end
    object N3: TMenuItem
      Caption = '-'
    end
    object Ausschneiden1: TMenuItem
      Caption = 'Ausschneiden'
      ShortCut = 16472
      OnClick = Ausschneiden1Click
    end
    object Kopieren1: TMenuItem
      Caption = 'Kopieren'
      ShortCut = 16451
      OnClick = Kopieren1Click
    end
    object Einfgen1: TMenuItem
      Caption = 'Einf'#252'gen'
      ShortCut = 16470
      OnClick = Einfgen1Click
    end
    object Lschen1: TMenuItem
      Caption = 'L'#246'schen'
      OnClick = Lschen1Click
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object Allesauswhlen1: TMenuItem
      Caption = 'Alles ausw'#228'hlen'
      OnClick = Allesauswhlen1Click
    end
  end
end
