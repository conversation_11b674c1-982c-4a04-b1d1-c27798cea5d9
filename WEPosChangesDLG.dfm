object WEPosChangesForm: TWEPosChangesForm
  Left = 0
  Top = 0
  Caption = 'WEPosChangesForm'
  ClientHeight = 411
  ClientWidth = 852
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object FussPanel: TPanel
    Left = 0
    Top = 370
    Width = 852
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      852
      41)
    object CloseButton: TButton
      Left = 769
      Top = 8
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 0
    end
  end
  object PosPanel: TPanel
    Left = 0
    Top = 0
    Width = 852
    Height = 370
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      852
      370)
    object Label2: TLabel
      Left = 8
      Top = 6
      Width = 59
      Height = 13
      Caption = #196'nderungen'
    end
    object RetoureChangeDBGrid: TDBGridPro
      Left = 8
      Top = 22
      Width = 836
      Height = 334
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = RetoureChangeDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
      ReadOnly = True
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'Tahoma'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
  end
  object RetoureChangeDataSource: TOraDataSource
    DataSet = RetoureChangeQuery
    Left = 480
    Top = 208
  end
  object RetoureChangeQuery: TOraQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 432
    Top = 208
  end
end
