unit EditVerladegruppeDLG;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, ComboBoxPro, Vcl.ExtCtrls;

type
  TEditVerladegruppeForm = class(TForm)
    NameLabel: TLabel;
    NameEdit: TEdit;
    MandComboBox: TComboBoxPro;
    LagerComboBox: TComboBoxPro;
    LagerLabel: TLabel;
    MandLabel: TLabel;
    Bevel4: TBevel;
    LTComboBox: TComboBoxPro;
    LTLabel: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    procedure AbortButtonClick(Sender: TObject);
    procedure MandComboBoxChange(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure LagerComboBoxChange(Sender: TObject);
    procedure OkButtonClick(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    fRefVerladeGruppe: Integer;
    procedure initComponents;
    function validFieldData: Boolean;
  public
    procedure Prepare(RefVerladeGruppe: Integer);
  end;

implementation

uses
  FrontendUtils
  , VCLUtilitys
  , DatenModul
  , ADODB
  , FrontendMessages
  , LVSVerladegruppenInterface
  , SprachModul
  , ResourceText
  ;

{$R *.dfm}

procedure TEditVerladegruppeForm.AbortButtonClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

procedure TEditVerladegruppeForm.FormCreate(Sender: TObject);
begin
  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, MandComboBox);
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
    LVSSprachModul.SetNoTranslate (Self, LTComboBox);
  {$endif}
end;

procedure TEditVerladegruppeForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects(MandComboBox);
  ClearComboBoxObjects(LagerComboBox);
  ClearComboBoxObjects(LTComboBox);
end;

procedure TEditVerladegruppeForm.initComponents;
begin
  LoadMandantCombobox (MandComboBox);
  if MandComboBox.Items.Count = 1 then
  begin
    MandComboBox.ItemIndex := 0;
    MandComboBox.Enabled := False;
    MandComboBoxChange(MandComboBox);
  end;
//  MandComboBox.ItemIndex := 0;
  NameEdit.Text := '';
end;

procedure TEditVerladegruppeForm.MandComboBoxChange(Sender: TObject);
var
  ref : Integer;
begin
  ref := GetComboBoxRef (LagerComboBox);

  if (GetComboBoxRef (MandComboBox) = -1) then
  begin
    LoadLagerCombobox (LagerComboBox, LVSDatenModul.AktLocation);
  end
  else
  begin
    LoadLagerCombobox (LagerComboBox, LVSDatenModul.AktLocation, GetComboBoxRef (MandComboBox));
  end;

  LagerComboBox.Items.Insert (0, '');

  if (MandComboBox.Enabled) then
  begin
    LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, ref);
    if (LagerComboBox.ItemIndex = -1) then
    begin
      LagerComboBox.ItemIndex := 0;
    end;

    LagerComboBoxChange (LagerComboBox);
  end;
end;

procedure TEditVerladegruppeForm.OkButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if validFieldData then
  begin
    res := SetVerladeGruppe(
      NameEdit.Text,
      GetComboBoxRef(LTComboBox),
      LVSDatenModul.AktLocationRef,
      GetComboBoxRef(LagerComboBox),
      GetComboBoxRef(MandComboBox),
      fRefVerladeGruppe
    );
    if res <> 0 then
    begin
      FrontendMessages.MessageDLG ('SQL-Fehler: '+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
    end
    else
    begin
      self.ModalResult := mrOK;
    end;
  end;
end;

procedure TEditVerladegruppeForm.LagerComboBoxChange(Sender: TObject);
var
  ref : Integer;
begin
  ref := GetComboBoxRef (LTComboBox);

  if (GetComboBoxRef (LagerComboBox) = -1) then
  begin
    LoadLTCombobox (LTComboBox, 'WA', LVSDatenModul.AktLocationRef);
  end
  else
  begin
    LoadLTCombobox (LTComboBox, 'WA', LVSDatenModul.AktLocationRef, GetComboBoxRef (LagerComboBox));
  end;

  LTComboBox.Items.Insert(0, '');

  LTComboBox.ItemIndex := FindComboboxRef (LTComboBox, ref);
  if (LTComboBox.ItemIndex = -1) then
  begin
    LTComboBox.ItemIndex := 0;
  end;
//  LagerComboBoxChange (LTComboBox);
end;

procedure TEditVerladegruppeForm.Prepare(RefVerladeGruppe: Integer);
var
  query : TADOQuery;
begin
  fRefVerladeGruppe := RefVerladeGruppe;
  initComponents;

  if fRefVerladeGruppe <> -1 then
  begin
    query := TADOQuery.Create (Self);
    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;
      query.SQL.Text := 'SELECT * FROM VERLADE_GRUPPE WHERE REF = :ref';
      query.Parameters.ParamByName('ref').Value := RefVerladeGruppe;
      query.Open;

      NameEdit.Text := query.FieldByName('NAME').AsString;

      MandComboBox.ItemIndex := FindComboboxRef (MandComboBox, query.FieldByName('REF_MAND').AsInteger);
      MandComboBoxChange (MandComboBox);

      LagerComboBox.ItemIndex := FindComboboxRef (LagerComboBox, query.FieldByName('REF_LAGER').AsInteger);
      LagerComboBoxChange(LagerComboBox);

      LTComboBox.ItemIndex := FindComboboxRef( LTComboBox, query.FieldByName('REF_LT').AsInteger);
    finally
      if query.Active then
      begin
        query.Close;
      end;
      query.Free;
    end;
  end;
end;

function TEditVerladegruppeForm.validFieldData: Boolean;
begin
  Result := True;

  if MandComboBox.Enabled then
  begin
    if MandComboBox.ItemIndex <= 0 then
    begin
      Result := False;
      FrontendMessages.MessageDLG (GetResourceText(1401), mtError, [mbOK], 0);
      exit;
    end;
  end;

  if LagerComboBox.ItemIndex <= 0 then
  begin
    Result := False;
    FrontendMessages.MessageDLG(GetResourceText(1402), mtError, [mbOK], 0);
    exit;
  end;

  if LTComboBox.ItemIndex <= 0 then
  begin
    Result := False;
    FrontendMessages.MessageDLG (FormatMessageText(1555, []), mtError, [mbOK], 0);
    exit;
  end;

  if NameEdit.Text = '' then
  begin
    Result := False;
    FrontendMessages.MessageDLG (FormatMessageText(1176, []), mtError, [mbOK], 0);
    exit;
  end;
end;

end.
