object DisplayWELTForm: TDisplayWELTForm
  Left = 0
  Top = 0
  Caption = 'Inhalte eines Ladungstr'#228'gers im Wareneingang'
  ClientHeight = 290
  ClientWidth = 671
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 671
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label1: TLabel
      Left = 16
      Top = 8
      Width = 69
      Height = 13
      Caption = 'LT-Nummer:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LENrLabel: TLabel
      Left = 96
      Top = 8
      Width = 47
      Height = 13
      Caption = 'LENrLabel'
    end
    object Label2: TLabel
      Left = 16
      Top = 24
      Width = 42
      Height = 13
      Caption = 'LT-Typ:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LETypLabel: TLabel
      Left = 96
      Top = 24
      Width = 54
      Height = 13
      Caption = 'LETypLabel'
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 249
    Width = 671
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      671
      41)
    object CloseButton: TButton
      Left = 581
      Top = 9
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 0
    end
  end
  object LTInhaltStringGrid: TStringGridPro
    Left = 0
    Top = 57
    Width = 671
    Height = 192
    Align = alClient
    ColCount = 7
    Constraints.MinHeight = 100
    DefaultColWidth = 20
    DefaultRowHeight = 18
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = []
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goColSizing, goRowSelect]
    ParentFont = False
    PopupMenu = LTInhaltStringGridPopupMenu
    TabOrder = 1
    OnBeforeSort = LTInhaltStringGridBeforeSort
    GridStyle.OddColor = clInfoBk
    TitelTexte.Strings = (
      ''
      'Artikel Nr.'
      'Artikel Text'
      'EAN'
      'Ausf'#252'hrung'
      'Menge'
      'Einheit')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'MS Sans Serif'
    TitelFont.Style = []
    ExplicitTop = 60
    ColWidths = (
      20
      68
      149
      76
      81
      87
      77)
  end
  object LTInhaltStringGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = LTInhaltStringGridPopupMenuPopup
    Left = 216
    Top = 136
    object MandCopyColMenuitem: TMenuItem
      Caption = 'Kopieren'
      ImageIndex = 26
      OnClick = StringGridCopyColMenuItemClick
    end
    object MandColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      ImageIndex = 27
      OnClick = StringGridColOptimalMenuItemClick
    end
  end
end
