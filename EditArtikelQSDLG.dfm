object EditArtikelQSForm: TEditArtikelQSForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'QS-Pr'#252'fungen bearbeiten'
  ClientHeight = 586
  ClientWidth = 663
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnShow = FormShow
  DesignSize = (
    663
    586)
  PixelsPerInch = 96
  TextHeight = 13
  object Label2: TLabel
    Left = 8
    Top = 8
    Width = 53
    Height = 13
    Caption = 'QS-Gruppe'
  end
  object AbortButton: TButton
    Left = 580
    Top = 553
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 6
  end
  object OkButton: TButton
    Left = 499
    Top = 553
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    Default = True
    ModalResult = 1
    TabOrder = 5
  end
  object QSGroupComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 647
    Height = 21
    Style = csOwnerDrawFixed
    ItemHeight = 15
    TabOrder = 0
    OnChange = QSGroupComboBoxChange
  end
  object QSGroupGroupBox: TGroupBox
    Left = 8
    Top = 98
    Width = 647
    Height = 447
    Caption = 'Daten der QS-Gruppe'
    TabOrder = 4
    object Label3: TLabel
      Left = 408
      Top = 20
      Width = 159
      Height = 13
      Caption = 'Qualit'#228'tskontrolle bei Anlieferung'
    end
    object Label1: TLabel
      Left = 408
      Top = 70
      Width = 143
      Height = 13
      Caption = 'Qualit'#228'tskontrolle bei Retoure'
    end
    object Label4: TLabel
      Left = 408
      Top = 120
      Width = 173
      Height = 13
      Caption = 'Qualit'#228'tskontrolle im Warenausgang'
    end
    object Label5: TLabel
      Left = 408
      Top = 170
      Width = 207
      Height = 13
      Caption = 'Qualit'#228'tskontrolle bei der Kommissionierung'
    end
    object Bevel1: TBevel
      Left = 8
      Top = 216
      Width = 632
      Height = 11
      Shape = bsTopLine
    end
    object Label6: TLabel
      Left = 8
      Top = 222
      Width = 50
      Height = 13
      Caption = 'Pr'#252'fungen'
    end
    object Label7: TLabel
      Left = 8
      Top = 20
      Width = 27
      Height = 13
      Caption = 'Name'
    end
    object Label8: TLabel
      Left = 8
      Top = 70
      Width = 64
      Height = 13
      Caption = 'Beschreibung'
    end
    object QSWEComboBox: TComboBox
      Left = 408
      Top = 36
      Width = 230
      Height = 21
      Style = csDropDownList
      ItemHeight = 13
      ItemIndex = 0
      TabOrder = 2
      Text = 'Nie'
      OnChange = DataChange
      Items.Strings = (
        'Nie'
        'Immer'
        'Selten (20%)'
        'Mittel 50%'
        #214'ffters (80%)')
    end
    object QSRETComboBox: TComboBox
      Left = 408
      Top = 86
      Width = 230
      Height = 21
      Style = csDropDownList
      ItemHeight = 13
      ItemIndex = 0
      TabOrder = 3
      Text = 'Nie'
      OnChange = DataChange
      Items.Strings = (
        'Nie'
        'Immer'
        'Selten (20%)'
        'Mittel 50%'
        #214'ffters (80%)')
    end
    object CheckStringGrid: TStringGridPro
      Left = 8
      Top = 241
      Width = 632
      Height = 160
      ColCount = 4
      DefaultColWidth = 20
      DefaultRowHeight = 18
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRowSelect]
      PopupMenu = CheckStringGridPopupMenu
      TabOrder = 4
      TitelFont.Charset = DEFAULT_CHARSET
      TitelFont.Color = clWindowText
      TitelFont.Height = -11
      TitelFont.Name = 'Tahoma'
      TitelFont.Style = []
      ColWidths = (
        20
        99
        118
        368)
    end
    object QSWAComboBox: TComboBox
      Left = 408
      Top = 136
      Width = 230
      Height = 21
      Style = csDropDownList
      Enabled = False
      ItemHeight = 13
      ItemIndex = 0
      TabOrder = 5
      Text = 'Nie'
      OnChange = DataChange
      Items.Strings = (
        'Nie'
        'Immer'
        'Selten (20%)'
        'Mittel 50%'
        #214'ffters (80%)')
    end
    object QSKOMMComboBox: TComboBox
      Left = 408
      Top = 186
      Width = 230
      Height = 21
      Style = csDropDownList
      Enabled = False
      ItemHeight = 13
      ItemIndex = 0
      TabOrder = 6
      Text = 'Nie'
      OnChange = DataChange
      Items.Strings = (
        'Nie'
        'Immer'
        'Selten (20%)'
        'Mittel 50%'
        #214'ffters (80%)')
    end
    object NameEdit: TEdit
      Left = 8
      Top = 36
      Width = 121
      Height = 21
      TabOrder = 0
      Text = 'NameEdit'
      OnChange = DataChange
    end
    object DescEdit: TEdit
      Left = 8
      Top = 86
      Width = 337
      Height = 21
      TabOrder = 1
      Text = 'DescEdit'
      OnChange = DataChange
    end
    object SaveDataButton: TButton
      Left = 565
      Top = 411
      Width = 75
      Height = 25
      Caption = #220'bernehmen'
      TabOrder = 7
      OnClick = SaveDataButtonClick
    end
  end
  object ChangeButton: TButton
    Left = 8
    Top = 51
    Width = 75
    Height = 25
    Caption = 'Bearbeiten'
    TabOrder = 1
    OnClick = ChangeButtonClick
  end
  object NewButton: TButton
    Left = 96
    Top = 51
    Width = 75
    Height = 25
    Caption = 'Neu'
    TabOrder = 2
    OnClick = NewButtonClick
  end
  object DeleteButton: TButton
    Left = 184
    Top = 51
    Width = 75
    Height = 25
    Caption = 'L'#246'schen'
    TabOrder = 3
  end
  object CheckStringGridPopupMenu: TPopupMenu
    Left = 408
    Top = 384
    object MenuItem1: TMenuItem
      Caption = 'Hinzuf'#252'gen...'
      OnClick = AddCheckMenuItemClick
    end
    object MenuItem2: TMenuItem
      Caption = 'Bearbeiten...'
      OnClick = EditCheckMenuItemClick
    end
    object MenuItem3: TMenuItem
      Caption = 'L'#246'schen...'
      ShortCut = 46
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 456
    Top = 40
  end
end
