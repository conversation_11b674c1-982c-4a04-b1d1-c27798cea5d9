unit EditLifeObjectDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TEditLifeObjectForm = class(TForm)
    Label1: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    DescEdit: TEdit;
    IDEdit: TEdit;
    SerialEdit: TEdit;
    InvNrEdit: TEdit;
    Label3: TLabel;
    Label4: TLabel;
    Label5: TLabel;
    Label6: TLabel;
    Label7: TLabel;
    Bevel1: TBevel;
    procedure FormCreate(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

procedure TEditLifeObjectForm.FormCreate(Sender: TObject);
begin
  DescEdit.Text   := '';
  IDEdit.Text     := '';
  SerialEdit.Text := '';
  InvNrEdit.Text  := '';
end;

end.
