object CreateNachschubForm: TCreateNachschubForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Nachschub anlegen'
  ClientHeight = 584
  ClientWidth = 467
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    467
    584)
  TextHeight = 13
  object Label8: TLabel
    Left = 8
    Top = 43
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Label12: TLabel
    Left = 8
    Top = 11
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Bevel5: TBevel
    Left = 6
    Top = 97
    Width = 452
    Height = 4
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 419
  end
  object Label2: TLabel
    Left = 8
    Top = 107
    Width = 55
    Height = 13
    Caption = 'Ziel-Bereich'
  end
  object Bevel1: TBevel
    Left = 6
    Top = 542
    Width = 452
    Height = 4
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 282
    ExplicitWidth = 419
  end
  object Label3: TLabel
    Left = 8
    Top = 139
    Width = 43
    Height = 13
    Caption = 'Ziel-Platz'
    Visible = False
  end
  object Bevel2: TBevel
    Left = 6
    Top = 167
    Width = 449
    Height = 4
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 416
  end
  object Label4: TLabel
    Left = 8
    Top = 73
    Width = 63
    Height = 13
    Caption = 'Quell-Bereich'
  end
  object Label13: TLabel
    Left = 8
    Top = 496
    Width = 111
    Height = 13
    Anchors = [akLeft, akBottom]
    Caption = 'Nachschubplanung f'#252'r:'
  end
  object LagerComboBox: TComboBoxPro
    Left = 80
    Top = 40
    Width = 379
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 1
    OnChange = LagerComboBoxChange
  end
  object MandantComboBox: TComboBoxPro
    Left = 80
    Top = 10
    Width = 379
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 0
    OnChange = MandantComboBoxChange
  end
  object OkButton: TButton
    Left = 292
    Top = 551
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 7
  end
  object AbortButton: TButton
    Left = 383
    Top = 551
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 8
  end
  object LBDestComboBox: TComboBoxPro
    Left = 80
    Top = 104
    Width = 379
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 3
    OnChange = LBDestComboBoxChange
  end
  object LPDestComboBox: TComboBoxPro
    Left = 80
    Top = 136
    Width = 379
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 4
  end
  object LBSourceComboBox: TComboBoxPro
    Left = 80
    Top = 70
    Width = 379
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 2
    OnChange = LBSourceComboBoxChange
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 171
    Width = 451
    Height = 319
    ActivePage = ArtikelTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 5
    object ArtikelTabSheet: TTabSheet
      Caption = 'Artikel'
      object ArtikelPanel: TPanel
        Left = 0
        Top = 0
        Width = 443
        Height = 44
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 0
        DesignSize = (
          443
          44)
        object Label1: TLabel
          Left = 8
          Top = 23
          Width = 30
          Height = 13
          Caption = 'Artikel'
        end
        object ArNrEdit: TEdit
          Left = 68
          Top = 20
          Width = 90
          Height = 21
          MaxLength = 32
          TabOrder = 0
          Text = 'ArNrEdit'
          OnExit = ArNrEditExit
        end
        object ArtikelComboBox: TComboBoxPro
          Left = 168
          Top = 20
          Width = 272
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 1
          OnChange = ArtikelComboBoxChange
          OnCloseUp = ArtikelComboBoxCloseUp
          OnDropDown = ArtikelComboBoxDropDown
        end
      end
      object VarPanel: TPanel
        Left = 0
        Top = 82
        Width = 443
        Height = 23
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 2
        DesignSize = (
          443
          23)
        object VarianteLabel: TLabel
          Left = 8
          Top = 7
          Width = 56
          Height = 13
          Caption = 'Ausf'#252'hrung'
        end
        object VarianteEdit: TEdit
          Left = 68
          Top = 4
          Width = 372
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 0
          Text = 'VarianteEdit'
        end
      end
      object MengePanel: TPanel
        Left = 0
        Top = 105
        Width = 443
        Height = 86
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 3
        object Label5: TLabel
          Left = 8
          Top = 19
          Width = 32
          Height = 13
          Caption = 'Menge'
        end
        object Bevel3: TBevel
          Left = 8
          Top = 8
          Width = 432
          Height = 8
          Shape = bsTopLine
        end
        object Label11: TLabel
          Left = 168
          Top = 19
          Width = 18
          Height = 13
          Caption = 'Prio'
        end
        object MengeEdit: TEdit
          Left = 68
          Top = 16
          Width = 73
          Height = 21
          TabOrder = 0
          Text = '0'
          OnKeyPress = IntegerEditKeyPress
        end
        object MengeUpDown: TIntegerUpDown
          Left = 141
          Top = 16
          Width = 16
          Height = 21
          Associate = MengeEdit
          Max = 10000
          TabOrder = 1
        end
        object MultiPlanCheckBox: TCheckBox
          Left = 68
          Top = 44
          Width = 339
          Height = 17
          Caption = 'Die gesamte Menge planen'
          TabOrder = 4
        end
        object AutoReleaseCheckBox: TCheckBox
          Left = 68
          Top = 67
          Width = 333
          Height = 17
          Caption = 'Nachschub direkt freigeben'
          TabOrder = 5
        end
        object PrioEdit: TEdit
          Left = 200
          Top = 16
          Width = 41
          Height = 21
          MaxLength = 3
          TabOrder = 2
          Text = '0'
          OnKeyPress = IntegerEditKeyPress
        end
        object PrioUpDown: TIntegerUpDown
          Left = 241
          Top = 16
          Width = 16
          Height = 21
          Associate = PrioEdit
          Max = 999
          TabOrder = 3
        end
      end
      object MHDPanel: TPanel
        Left = 0
        Top = 191
        Width = 443
        Height = 59
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 4
        object Label7: TLabel
          Left = 8
          Top = 15
          Width = 45
          Height = 13
          Caption = 'min. MHD'
        end
        object Label10: TLabel
          Left = 8
          Top = 39
          Width = 39
          Height = 13
          Caption = 'Fix MHD'
        end
        object Bevel4: TBevel
          Left = 8
          Top = 5
          Width = 432
          Height = 8
          Shape = bsTopLine
        end
        object MHDEdit: TEdit
          Left = 68
          Top = 12
          Width = 121
          Height = 21
          TabOrder = 0
          Text = 'MHDEdit'
          OnExit = MHDEditExit
        end
        object FixMHDEdit: TEdit
          Left = 68
          Top = 36
          Width = 121
          Height = 21
          TabOrder = 1
          Text = 'FixMHDEdit'
          OnExit = FixMHDEditExit
        end
      end
      object ChargePanel: TPanel
        Left = 0
        Top = 250
        Width = 443
        Height = 41
        Align = alClient
        BevelOuter = bvNone
        TabOrder = 5
        object Label9: TLabel
          Left = 8
          Top = 15
          Width = 35
          Height = 13
          Caption = 'Charge'
        end
        object ChargeEdit: TEdit
          Left = 68
          Top = 12
          Width = 121
          Height = 21
          TabOrder = 0
          Text = 'ChargeEdit'
        end
      end
      object EANPanel: TPanel
        Left = 0
        Top = 44
        Width = 443
        Height = 38
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 1
        Visible = False
        DesignSize = (
          443
          38)
        object Label19: TLabel
          Left = 8
          Top = 11
          Width = 20
          Height = 13
          Caption = 'EAN'
        end
        object EANEdit: TEdit
          Left = 68
          Top = 8
          Width = 90
          Height = 21
          MaxLength = 32
          TabOrder = 0
          Text = 'EANEdit'
          OnExit = ArNrEditExit
        end
        object EANComboBox: TComboBoxPro
          Left = 168
          Top = 8
          Width = 272
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 1
          OnChange = ArtikelComboBoxChange
          OnDropDown = ArtikelComboBoxDropDown
        end
      end
    end
    object ListTabSheet: TTabSheet
      Caption = 'Liste'
      ImageIndex = 1
      DesignSize = (
        443
        291)
      object Label6: TLabel
        Left = 8
        Top = 245
        Width = 154
        Height = 13
        Anchors = [akLeft, akBottom]
        Caption = 'Max. Anzahl VPS pro Nachschub'
        ExplicitTop = 232
      end
      object ImportStringGrid: TStringGridPro
        Left = 8
        Top = 7
        Width = 429
        Height = 186
        Anchors = [akLeft, akTop, akRight, akBottom]
        ColCount = 6
        DefaultColWidth = 20
        DefaultRowHeight = 16
        TabOrder = 0
        TitelTexte.Strings = (
          ''
          'Artikelnr.'
          'Colliname'
          'Collinr.'
          'Menge'
          'Geplant')
        TitelFont.Charset = DEFAULT_CHARSET
        TitelFont.Color = clWindowText
        TitelFont.Height = -11
        TitelFont.Name = 'Tahoma'
        TitelFont.Style = []
        ColWidths = (
          30
          160
          90
          40
          40
          40)
      end
      object ImportCSVButton: TButton
        Left = 8
        Top = 199
        Width = 129
        Height = 25
        Anchors = [akLeft, akBottom]
        Caption = 'Import CSV'
        TabOrder = 1
        OnClick = ImportCSVButtonClick
      end
      object MaxVPEEdit: TEdit
        Left = 8
        Top = 261
        Width = 54
        Height = 21
        Anchors = [akLeft, akBottom]
        TabOrder = 2
        Text = 'MaxVPEEdit'
        OnKeyPress = IntegerEditKeyPress
      end
      object ListePlanenButton: TButton
        Left = 362
        Top = 259
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Liste planen'
        TabOrder = 3
        OnClick = ListePlanenButtonClick
      end
    end
  end
  object NachUserComboBox: TComboBoxPro
    Left = 8
    Top = 512
    Width = 451
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akRight, akBottom]
    ColWidth = 100
    TabOrder = 6
    OnDrawItem = NachUserComboBoxDrawItem
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 288
    Top = 184
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 192
    Top = 544
  end
end
