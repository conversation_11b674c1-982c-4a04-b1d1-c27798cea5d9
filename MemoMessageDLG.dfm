object MemoMessageForm: TMemoMessageForm
  Left = 447
  Top = 276
  BorderStyle = bsDialog
  Caption = 'MemoMessageForm'
  ClientHeight = 295
  ClientWidth = 698
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poMainFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object TopPanel: TPanel
    Left = 0
    Top = 0
    Width = 698
    Height = 65
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      698
      65)
    object TopLabel: TLabel
      Left = 40
      Top = 8
      Width = 619
      Height = 51
      Anchors = [akLeft, akTop, akRight, akBottom]
      AutoSize = False
      Caption = 'TopLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      ExplicitHeight = 35
    end
  end
  object BottomPanel: TPanel
    Left = 0
    Top = 253
    Width = 698
    Height = 42
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 6
    DesignSize = (
      698
      42)
    object YesButton: TButton
      Left = 520
      Top = 7
      Width = 75
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'Das ist ein Test f'#252'r einen langen text'
      ModalResult = 6
      TabOrder = 0
      Visible = False
    end
    object NoButton: TButton
      Left = 616
      Top = 6
      Width = 75
      Height = 25
      Caption = 'Nein'
      ModalResult = 7
      TabOrder = 1
      Visible = False
    end
    object OkButton: TButton
      Left = 616
      Top = 6
      Width = 75
      Height = 25
      Caption = 'OK'
      ModalResult = 1
      TabOrder = 2
      Visible = False
    end
    object DialogCheckBox: TCheckBox
      Left = 14
      Top = 15
      Width = 211
      Height = 17
      Anchors = [akLeft, akBottom]
      Caption = 'Diesen Dialog nicht mehr anzeigen'
      TabOrder = 3
      Visible = False
    end
    object AbortButton: TButton
      Left = 616
      Top = 7
      Width = 75
      Height = 25
      Anchors = [akLeft, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 4
      Visible = False
    end
  end
  object MessageMemo: TMemo
    Left = 41
    Top = 65
    Width = 615
    Height = 110
    TabStop = False
    Align = alClient
    BevelInner = bvNone
    BevelOuter = bvNone
    BorderStyle = bsNone
    Color = clBtnFace
    Lines.Strings = (
      'MessageMemo')
    ReadOnly = True
    TabOrder = 2
    WantTabs = True
  end
  object Panel3: TPanel
    Left = 0
    Top = 65
    Width = 41
    Height = 110
    Align = alLeft
    BevelOuter = bvNone
    TabOrder = 1
  end
  object Panel4: TPanel
    Left = 656
    Top = 65
    Width = 42
    Height = 110
    Align = alRight
    BevelOuter = bvNone
    TabOrder = 3
  end
  object LabelPanel: TPanel
    Left = 0
    Top = 175
    Width = 698
    Height = 27
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 4
    object BottomLabel: TLabel
      Left = 41
      Top = 8
      Width = 71
      Height = 13
      Caption = 'BottomLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object GrundPanel: TPanel
    Left = 0
    Top = 202
    Width = 698
    Height = 51
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 5
    Visible = False
    DesignSize = (
      698
      51)
    object GrundLabel: TLabel
      Left = 40
      Top = 12
      Width = 26
      Height = 13
      Caption = 'Label'
    end
    object GrundComboBox: TComboBox
      Left = 40
      Top = 28
      Width = 651
      Height = 21
      Style = csDropDownList
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 13
      MaxLength = 64
      TabOrder = 0
      OnChange = GrundComboBoxChange
      Items.Strings = (
        'Ware mangelhaft')
    end
  end
end
