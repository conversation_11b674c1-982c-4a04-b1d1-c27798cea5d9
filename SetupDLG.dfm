object SetupForm: TSetupForm
  Left = 623
  Top = 449
  BorderStyle = bsDialog
  Caption = 'Einstellungen'
  ClientHeight = 625
  ClientWidth = 633
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poMainFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  DesignSize = (
    633
    625)
  TextHeight = 13
  object Label7: TLabel
    Left = 20
    Top = 211
    Width = 57
    Height = 13
    Caption = 'Schriftgr'#246#223'e'
  end
  object Label8: TLabel
    Left = 156
    Top = 211
    Width = 45
    Height = 13
    Caption = 'Textfarbe'
  end
  object Button1: TButton
    Left = 470
    Top = 592
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = '&OK'
    Default = True
    ModalResult = 1
    TabOrder = 0
  end
  object Button2: TButton
    Left = 550
    Top = 592
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = '&Abbrechen'
    ModalResult = 3
    TabOrder = 1
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 15
    Width = 618
    Height = 571
    ActivePage = PrinterTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    Images = ImageModule.FrontendImageList
    TabOrder = 2
    object TabSheet5: TTabSheet
      Caption = 'Parameter'
      ImageIndex = 13
      DesignSize = (
        610
        542)
      object SortGroupBox: TGroupBox
        Left = 3
        Top = 3
        Width = 602
        Height = 272
        Anchors = [akLeft, akTop, akRight, akBottom]
        Caption = 'Sortierung von Listen'
        TabOrder = 0
        DesignSize = (
          602
          272)
        object SortLieferantRadioGroup: TRadioGroup
          Left = 8
          Top = 24
          Width = 584
          Height = 49
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Lieferanten'
          Columns = 2
          ItemIndex = 0
          Items.Strings = (
            'Nach Nummern'
            'Nach Namen')
          TabOrder = 0
        end
        object SortEmpfRadioGroup: TRadioGroup
          Left = 8
          Top = 79
          Width = 584
          Height = 49
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Warenempf'#228'nger'
          Columns = 2
          ItemIndex = 0
          Items.Strings = (
            'Nach Nummern'
            'Nach Namen')
          TabOrder = 1
        end
        object SortLPRadioGroup: TRadioGroup
          Left = 8
          Top = 189
          Width = 584
          Height = 49
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Lagerpl'#228'tze'
          Columns = 3
          ItemIndex = 1
          Items.Strings = (
            'Nach Nummern'
            'Nach Koordinaten'
            'Nach Namen')
          TabOrder = 3
        end
        object SortUserRadioGroup: TRadioGroup
          Left = 8
          Top = 134
          Width = 584
          Height = 49
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Benutzer'
          Columns = 2
          ItemIndex = 0
          Items.Strings = (
            'Nach IDs'
            'Nach Namen')
          TabOrder = 2
        end
      end
      object WEParamGroupBox: TGroupBox
        Left = 5
        Top = 295
        Width = 602
        Height = 66
        Caption = 'Wareneingang'
        TabOrder = 1
        DesignSize = (
          602
          66)
        object WEAutoJumpCheckBox: TCheckBox
          Left = 8
          Top = 24
          Width = 585
          Height = 17
          Anchors = [akLeft, akTop, akRight]
          Caption = 
            'Bei vollst'#228'ndig belieferten Position automatisch zur n'#228'chsten sp' +
            'ringen'
          Checked = True
          State = cbChecked
          TabOrder = 0
        end
      end
    end
    object TabSheet1: TTabSheet
      Caption = 'Grids'
      ImageIndex = 6
      object GridFetchAllCheckBox: TCheckBox
        Left = 8
        Top = 16
        Width = 577
        Height = 17
        Caption = 'Daten in den Grids vollst'#228'ndig laden'
        TabOrder = 0
      end
      object GridAttrGroupBox: TGroupBox
        Left = 3
        Top = 72
        Width = 604
        Height = 417
        Caption = 'Grid Darstellung'
        TabOrder = 2
        DesignSize = (
          604
          417)
        object Label1: TLabel
          Left = 8
          Top = 40
          Width = 55
          Height = 13
          Caption = 'Hintergrund'
        end
        object Label2: TLabel
          Left = 8
          Top = 80
          Width = 67
          Height = 13
          Caption = '2. Hintergrund'
        end
        object Label3: TLabel
          Left = 8
          Top = 128
          Width = 57
          Height = 13
          Caption = 'Schriftgr'#246#223'e'
        end
        object Label4: TLabel
          Left = 140
          Top = 128
          Width = 45
          Height = 13
          Caption = 'Textfarbe'
        end
        object Label38: TLabel
          Left = 324
          Top = 128
          Width = 143
          Height = 13
          Caption = 'Farbe f'#252'r nicht aktive Auswahl'
          Visible = False
        end
        object Label40: TLabel
          Left = 8
          Top = 176
          Width = 21
          Height = 13
          Caption = 'Font'
        end
        object CheckBox1: TCheckBox
          Left = 8
          Top = 16
          Width = 97
          Height = 17
          Caption = 'Grids zweifarbig'
          Checked = True
          State = cbChecked
          TabOrder = 0
          OnClick = CheckBox1Click
        end
        object OddColorBox: TColorBox
          Left = 8
          Top = 56
          Width = 145
          Height = 22
          DefaultColorColor = clSkyBlue
          Selected = clSkyBlue
          TabOrder = 1
        end
        object EvenColorBox: TColorBox
          Left = 8
          Top = 96
          Width = 145
          Height = 22
          DefaultColorColor = clWindow
          Selected = clWindow
          TabOrder = 2
        end
        object FontSizeEdit: TEdit
          Left = 8
          Top = 144
          Width = 81
          Height = 21
          TabOrder = 3
          Text = '0'
        end
        object TextColorCombo: TExtColorCombo
          Left = 140
          Top = 144
          Width = 145
          Height = 22
          Style = csOwnerDrawFixed
          ItemIndex = 0
          TabOrder = 4
          Text = '#0|clBlack'
          Items.Strings = (
            '#0|clBlack'
            '#1|clMaroon'
            '#2|clGreen'
            '#3|clOlive'
            '#4|clNavy'
            '#5|clPurple'
            '#6|clTeal'
            '#7|clGray'
            '#8|clSilver'
            '#9|clRed'
            '#10|clLime'
            '#11|clYellow'
            '#12|clBlue'
            '#13|clFuchsia'
            '#14|clAqua'
            '#15|clWindowText'
            '#16|Benutzer...')
          ComboColors.Color0 = clBlack
          ComboColors.Color1 = clMaroon
          ComboColors.Color2 = clGreen
          ComboColors.Color3 = clOlive
          ComboColors.Color4 = clNavy
          ComboColors.Color5 = clPurple
          ComboColors.Color6 = clTeal
          ComboColors.Color7 = clGray
          ComboColors.Color8 = clSilver
          ComboColors.Color9 = clRed
          ComboColors.Color10 = clLime
          ComboColors.Color11 = clYellow
          ComboColors.Color12 = clBlue
          ComboColors.Color13 = clFuchsia
          ComboColors.Color14 = clAqua
          ComboColors.Color15 = clWindowText
          ComboColors.UserColor = clBlack
          ColorWidth = 24
          TextSpace = 3
          SelColor = clBlack
        end
        object GroupBox3: TGroupBox
          Left = 7
          Top = 248
          Width = 589
          Height = 161
          Anchors = [akLeft, akRight, akBottom]
          Caption = 'Grid-Titel'
          TabOrder = 5
          object Label9: TLabel
            Left = 8
            Top = 20
            Width = 57
            Height = 13
            Caption = 'Schriftgr'#246#223'e'
          end
          object Label10: TLabel
            Left = 8
            Top = 68
            Width = 45
            Height = 13
            Caption = 'Textfarbe'
          end
          object Label11: TLabel
            Left = 168
            Top = 68
            Width = 55
            Height = 13
            Caption = 'Hintergrund'
            Visible = False
          end
          object Label41: TLabel
            Left = 8
            Top = 116
            Width = 21
            Height = 13
            Caption = 'Font'
          end
          object TitleSizeEdit: TEdit
            Left = 8
            Top = 36
            Width = 73
            Height = 21
            TabOrder = 0
            Text = '0'
          end
          object TitleSizeUpDown: TUpDown
            Left = 81
            Top = 36
            Width = 16
            Height = 21
            Associate = TitleSizeEdit
            TabOrder = 1
          end
          object TitleColorCombo: TExtColorCombo
            Left = 8
            Top = 83
            Width = 145
            Height = 22
            Style = csOwnerDrawFixed
            ItemIndex = 0
            TabOrder = 2
            Text = '#0|clBlack'
            Items.Strings = (
              '#0|clBlack'
              '#1|clMaroon'
              '#2|clGreen'
              '#3|clOlive'
              '#4|clNavy'
              '#5|clPurple'
              '#6|clTeal'
              '#7|clGray'
              '#8|clSilver'
              '#9|clRed'
              '#10|clLime'
              '#11|clYellow'
              '#12|clBlue'
              '#13|clFuchsia'
              '#14|clAqua'
              '#15|clWindowText'
              '#16|Benutzer...')
            ComboColors.Color0 = clBlack
            ComboColors.Color1 = clMaroon
            ComboColors.Color2 = clGreen
            ComboColors.Color3 = clOlive
            ComboColors.Color4 = clNavy
            ComboColors.Color5 = clPurple
            ComboColors.Color6 = clTeal
            ComboColors.Color7 = clGray
            ComboColors.Color8 = clSilver
            ComboColors.Color9 = clRed
            ComboColors.Color10 = clLime
            ComboColors.Color11 = clYellow
            ComboColors.Color12 = clBlue
            ComboColors.Color13 = clFuchsia
            ComboColors.Color14 = clAqua
            ComboColors.Color15 = clWindowText
            ComboColors.UserColor = clBlack
            ColorWidth = 24
            TextSpace = 3
            SelColor = clBlack
          end
          object TitleWrapCheckBox: TCheckBox
            Left = 120
            Top = 40
            Width = 169
            Height = 17
            Caption = 'Automatischer Zeilenumbruch'
            TabOrder = 3
          end
          object TitleBackgndCombo: TExtColorCombo
            Left = 168
            Top = 83
            Width = 145
            Height = 22
            Style = csOwnerDrawFixed
            ItemIndex = 0
            TabOrder = 4
            Text = '#0|clBlack'
            Visible = False
            Items.Strings = (
              '#0|clBlack'
              '#1|clMaroon'
              '#2|clGreen'
              '#3|clOlive'
              '#4|clNavy'
              '#5|clPurple'
              '#6|clTeal'
              '#7|clGray'
              '#8|clSilver'
              '#9|clRed'
              '#10|clLime'
              '#11|clYellow'
              '#12|clBlue'
              '#13|clFuchsia'
              '#14|clAqua'
              '#15|clWindowText'
              '#16|Benutzer...')
            ComboColors.Color0 = clBlack
            ComboColors.Color1 = clMaroon
            ComboColors.Color2 = clGreen
            ComboColors.Color3 = clOlive
            ComboColors.Color4 = clNavy
            ComboColors.Color5 = clPurple
            ComboColors.Color6 = clTeal
            ComboColors.Color7 = clGray
            ComboColors.Color8 = clSilver
            ComboColors.Color9 = clRed
            ComboColors.Color10 = clLime
            ComboColors.Color11 = clYellow
            ComboColors.Color12 = clBlue
            ComboColors.Color13 = clFuchsia
            ComboColors.Color14 = clAqua
            ComboColors.Color15 = clWindowText
            ComboColors.UserColor = clBlack
            ColorWidth = 24
            TextSpace = 3
            SelColor = clBlack
          end
          object TitleFontSelector: TAdvFontSelector
            Left = 8
            Top = 131
            Width = 305
            Height = 19
            Button.Color = 16572875
            Button.ColorTo = 14722429
            Button.ColorHot = 14483455
            Button.ColorHotTo = 6013175
            Button.ColorDown = 557032
            Button.ColorDownTo = 8182519
            Button.Width = 12
            Button.ArrowColor = clWindowText
            BorderColor = clNone
            BorderHotColor = clBlack
            SelectionColor = 14722429
            SelectionTextColor = clWhite
            Text = ''
            Version = '*******'
            AllowedFontTypes = [aftBitmap, aftTrueType, aftPostScript, aftPrinter, aftFixedPitch, aftProportional]
            FontGlyphTT.Data = {
              D6000000424DD60000000000000076000000280000000D0000000C0000000100
              04000000000060000000120B0000120B00001000000010000000000000000000
              8000008000000080800080000000800080008080000080808000C0C0C0000000
              FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00DDDDD000000D
              D000DDDDDDD00DDDD000DDDDDDD00DDDD000D77777700DDDD000DDD77DD00DDD
              D000DDD70DD00DD0D000DDD70DD00DD0D000DDD700D00D00D0007DD700000000
              D0007DD77DD7DDDDD00077D77D77DDDDD00077777777DDDDD000}
            FontGlyphPS.Data = {
              D6000000424DD60000000000000076000000280000000D0000000C0000000100
              04000000000060000000120B0000120B00001000000010000000000000000000
              8000008000000080800080000000800080008080000080808000C0C0C0000000
              FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00DDDDDDDDDDDD
              D000D9997DD997DDD0009999979979DDD000997D7999D79DD000997DD7997DDD
              D000997DDD799DDDD000799DDDD997DDD000D997DDD799DDD000D799DDDD997D
              D000DD799DDD799DD000DDD799DD7997D000DDDDD9999779D000}
            FontGlyphPRN.Data = {
              D6000000424DD60000000000000076000000280000000D0000000C0000000100
              04000000000060000000120B0000120B00001000000010000000000000000000
              8000008000000080800080000000800080008080000080808000C0C0C0000000
              FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00D00000000000
              D00007777777AA7700000888888888870000D00000000000D000DD07FFFFF70D
              D000DD0F00000F0DD000DD07FFFFF70DD000DD0F00000F0DD000DD07FFFFF70D
              D000DD0F00000F0DD000DD07FFFFF70DD000DD000000000DD000}
            FontGlyphBMP.Data = {
              D6000000424DD60000000000000076000000280000000D0000000C0000000100
              04000000000060000000120B0000120B00001000000010000000000000000000
              8000008000000080800080000000800080008080000080808000C0C0C0000000
              FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00DDDDDDDDDDDD
              D000DDDDDD99DD99D000DDDDDD99DD99D000D11DD199DD99D000D11DD1999999
              D000D11DD199DD99D000D1111199DD99D000D11DD199DD99D000D11DD119999D
              D000D11DD11D99DDD000DD1111DDDDDDD000DDD11DDDDDDDD000}
            FontHeight = 12
            TabOrder = 5
            UIStyle = tsOffice2003Blue
          end
        end
        object FontSizeUpDown: TUpDown
          Left = 89
          Top = 144
          Width = 16
          Height = 21
          Associate = FontSizeEdit
          TabOrder = 6
        end
        object HiddenSelColorCombobox: TColorBox
          Left = 324
          Top = 144
          Width = 145
          Height = 22
          DefaultColorColor = clWindow
          Selected = clWindow
          TabOrder = 7
          Visible = False
        end
        object GridFontSelector: TAdvFontSelector
          Left = 8
          Top = 191
          Width = 277
          Height = 19
          Button.Color = 16572875
          Button.ColorTo = 14722429
          Button.ColorHot = 14483455
          Button.ColorHotTo = 6013175
          Button.ColorDown = 557032
          Button.ColorDownTo = 8182519
          Button.Width = 12
          Button.ArrowColor = clWindowText
          BorderColor = clNone
          BorderHotColor = clBlack
          SelectionColor = 14722429
          SelectionTextColor = clWhite
          Text = ''
          Version = '*******'
          AllowedFontTypes = [aftBitmap, aftTrueType, aftPostScript, aftPrinter, aftFixedPitch, aftProportional]
          FontGlyphTT.Data = {
            D6000000424DD60000000000000076000000280000000D0000000C0000000100
            04000000000060000000120B0000120B00001000000010000000000000000000
            8000008000000080800080000000800080008080000080808000C0C0C0000000
            FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00DDDDD000000D
            D000DDDDDDD00DDDD000DDDDDDD00DDDD000D77777700DDDD000DDD77DD00DDD
            D000DDD70DD00DD0D000DDD70DD00DD0D000DDD700D00D00D0007DD700000000
            D0007DD77DD7DDDDD00077D77D77DDDDD00077777777DDDDD000}
          FontGlyphPS.Data = {
            D6000000424DD60000000000000076000000280000000D0000000C0000000100
            04000000000060000000120B0000120B00001000000010000000000000000000
            8000008000000080800080000000800080008080000080808000C0C0C0000000
            FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00DDDDDDDDDDDD
            D000D9997DD997DDD0009999979979DDD000997D7999D79DD000997DD7997DDD
            D000997DDD799DDDD000799DDDD997DDD000D997DDD799DDD000D799DDDD997D
            D000DD799DDD799DD000DDD799DD7997D000DDDDD9999779D000}
          FontGlyphPRN.Data = {
            D6000000424DD60000000000000076000000280000000D0000000C0000000100
            04000000000060000000120B0000120B00001000000010000000000000000000
            8000008000000080800080000000800080008080000080808000C0C0C0000000
            FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00D00000000000
            D00007777777AA7700000888888888870000D00000000000D000DD07FFFFF70D
            D000DD0F00000F0DD000DD07FFFFF70DD000DD0F00000F0DD000DD07FFFFF70D
            D000DD0F00000F0DD000DD07FFFFF70DD000DD000000000DD000}
          FontGlyphBMP.Data = {
            D6000000424DD60000000000000076000000280000000D0000000C0000000100
            04000000000060000000120B0000120B00001000000010000000000000000000
            8000008000000080800080000000800080008080000080808000C0C0C0000000
            FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00DDDDDDDDDDDD
            D000DDDDDD99DD99D000DDDDDD99DD99D000D11DD199DD99D000D11DD1999999
            D000D11DD199DD99D000D1111199DD99D000D11DD199DD99D000D11DD119999D
            D000D11DD11D99DDD000DD1111DDDDDDD000DDD11DDDDDDDD000}
          FontHeight = 12
          TabOrder = 8
          UIStyle = tsOffice2003Blue
        end
      end
      object GridAutoUpdateCheckBox: TCheckBox
        Left = 8
        Top = 39
        Width = 577
        Height = 17
        Caption = 'Daten in den Grids automatsich aktualisieren'
        Checked = True
        State = cbChecked
        TabOrder = 1
      end
    end
    object TabSheet2: TTabSheet
      Caption = 'Scanner'
      ImageIndex = 7
      OnShow = TabSheet2Show
      object Label19: TLabel
        Left = 8
        Top = 77
        Width = 105
        Height = 13
        Caption = 'Ton bei Scannerfehler'
      end
      object ScannerEnableCheckBox: TCheckBox
        Left = 8
        Top = 8
        Width = 137
        Height = 17
        Caption = 'Scanner angeschlossen'
        TabOrder = 0
        OnClick = ScannerEnableCheckBoxClick
      end
      object ScannerComboBox: TComboBox
        Left = 8
        Top = 40
        Width = 209
        Height = 21
        Style = csDropDownList
        ItemIndex = 0
        TabOrder = 1
        Text = 'Symbol LS1908'
        Items.Strings = (
          'Symbol LS1908'
          'baracoda 2604')
      end
      object ScanErrSoundComboBox: TComboBox
        Left = 8
        Top = 96
        Width = 209
        Height = 21
        TabOrder = 2
        Text = 'ScanErrSoundComboBox'
        OnChange = ScanErrSoundComboBoxChange
      end
      object Button3: TButton
        Left = 264
        Top = 94
        Width = 75
        Height = 25
        Caption = 'Durchsuchen'
        TabOrder = 3
        Visible = False
      end
      object PlayScanErrSoundButton: TBitBtn
        Left = 223
        Top = 94
        Width = 26
        Height = 25
        Glyph.Data = {
          36050000424D3605000000000000360400002800000010000000100000000100
          0800000000000001000000000000000000000001000000000000000000000000
          80000080000000808000800000008000800080800000C0C0C000C0DCC000F0CA
          A6000020400000206000002080000020A0000020C0000020E000004000000040
          20000040400000406000004080000040A0000040C0000040E000006000000060
          20000060400000606000006080000060A0000060C0000060E000008000000080
          20000080400000806000008080000080A0000080C0000080E00000A0000000A0
          200000A0400000A0600000A0800000A0A00000A0C00000A0E00000C0000000C0
          200000C0400000C0600000C0800000C0A00000C0C00000C0E00000E0000000E0
          200000E0400000E0600000E0800000E0A00000E0C00000E0E000400000004000
          20004000400040006000400080004000A0004000C0004000E000402000004020
          20004020400040206000402080004020A0004020C0004020E000404000004040
          20004040400040406000404080004040A0004040C0004040E000406000004060
          20004060400040606000406080004060A0004060C0004060E000408000004080
          20004080400040806000408080004080A0004080C0004080E00040A0000040A0
          200040A0400040A0600040A0800040A0A00040A0C00040A0E00040C0000040C0
          200040C0400040C0600040C0800040C0A00040C0C00040C0E00040E0000040E0
          200040E0400040E0600040E0800040E0A00040E0C00040E0E000800000008000
          20008000400080006000800080008000A0008000C0008000E000802000008020
          20008020400080206000802080008020A0008020C0008020E000804000008040
          20008040400080406000804080008040A0008040C0008040E000806000008060
          20008060400080606000806080008060A0008060C0008060E000808000008080
          20008080400080806000808080008080A0008080C0008080E00080A0000080A0
          200080A0400080A0600080A0800080A0A00080A0C00080A0E00080C0000080C0
          200080C0400080C0600080C0800080C0A00080C0C00080C0E00080E0000080E0
          200080E0400080E0600080E0800080E0A00080E0C00080E0E000C0000000C000
          2000C0004000C0006000C0008000C000A000C000C000C000E000C0200000C020
          2000C0204000C0206000C0208000C020A000C020C000C020E000C0400000C040
          2000C0404000C0406000C0408000C040A000C040C000C040E000C0600000C060
          2000C0604000C0606000C0608000C060A000C060C000C060E000C0800000C080
          2000C0804000C0806000C0808000C080A000C080C000C080E000C0A00000C0A0
          2000C0A04000C0A06000C0A08000C0A0A000C0A0C000C0A0E000C0C00000C0C0
          2000C0C04000C0C06000C0C08000C0C0A000F0FBFF00A4A0A000808080000000
          FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00F7F7F7F7F7F7
          F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F70000F7
          F7F7F7F7F7F7F7F7F7F7F7F7F7000000F7F7F7F7F7F7F7F7F7F7F7F7F7000000
          00F7F7F7F7F7F7F7F7F7F7F7F70000000000F7F7F7F7F7F7F7F7F7F7F7000000
          000000F7F7F7F7F7F7F7F7F7F700000000000000F7F7F7F7F7F7F7F7F7000000
          00000000F7F7F7F7F7F7F7F7F7000000000000F7F7F7F7F7F7F7F7F7F7000000
          0000F7F7F7F7F7F7F7F7F7F7F700000000F7F7F7F7F7F7F7F7F7F7F7F7000000
          F7F7F7F7F7F7F7F7F7F7F7F7F70000F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7
          F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7F7}
        TabOrder = 4
        OnClick = PlayScanErrSoundButtonClick
      end
      object WEScanForegroundCheckBox: TCheckBox
        Left = 8
        Top = 146
        Width = 457
        Height = 17
        Caption = 'Die das Fenster f'#252'r die WE-Erfassung immer im Vordergrund halten'
        TabOrder = 5
      end
    end
    object PrinterTabSheet: TTabSheet
      Caption = 'Drucker'
      ImageIndex = 3
      OnShow = PrinterTabSheetShow
      DesignSize = (
        610
        542)
      object GroupBox1: TGroupBox
        Left = 8
        Top = 24
        Width = 594
        Height = 218
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Normalpapier-Drucker'
        TabOrder = 0
        DesignSize = (
          594
          218)
        object Label5: TLabel
          Left = 8
          Top = 28
          Width = 33
          Height = 13
          Caption = 'Belege'
        end
        object Label13: TLabel
          Left = 8
          Top = 92
          Width = 71
          Height = 13
          Caption = 'Verladepapiere'
        end
        object Label14: TLabel
          Left = 8
          Top = 60
          Width = 71
          Height = 13
          Caption = 'Komm-Scheine'
        end
        object Label15: TLabel
          Left = 8
          Top = 124
          Width = 57
          Height = 13
          Caption = 'Lieferschein'
        end
        object Label26: TLabel
          Left = 8
          Top = 156
          Width = 62
          Height = 13
          Caption = 'Rechnungen'
        end
        object Label39: TLabel
          Left = 8
          Top = 188
          Width = 70
          Height = 13
          Caption = 'Zolldokumente'
        end
        object StdPrinterComboBox: TComboBoxPro
          Left = 120
          Top = 25
          Width = 300
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 0
        end
        object VerladePrinterComboBox: TComboBoxPro
          Left = 120
          Top = 89
          Width = 300
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 1
        end
        object KommPrinterComboBox: TComboBoxPro
          Left = 120
          Top = 57
          Width = 300
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 2
        end
        object LieferPrintComboBox: TComboBoxPro
          Left = 120
          Top = 121
          Width = 300
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 3
        end
        object RechungPrintComboBox: TComboBoxPro
          Left = 120
          Top = 153
          Width = 300
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 4
        end
        object RechungBinComboBox: TComboBoxPro
          Left = 440
          Top = 153
          Width = 143
          Height = 21
          Anchors = [akTop, akRight]
          TabOrder = 5
          OnDropDown = RechungBinComboBoxDropDown
        end
        object LieferBinComboBox: TComboBoxPro
          Left = 440
          Top = 121
          Width = 143
          Height = 21
          Anchors = [akTop, akRight]
          TabOrder = 6
          OnDropDown = LieferBinComboBoxDropDown
        end
        object StdBinComboBox: TComboBoxPro
          Left = 440
          Top = 25
          Width = 143
          Height = 21
          Anchors = [akTop, akRight]
          TabOrder = 7
          OnDropDown = StdBinComboBoxDropDown
        end
        object KommBinComboBox: TComboBoxPro
          Left = 440
          Top = 57
          Width = 143
          Height = 21
          Anchors = [akTop, akRight]
          Enabled = False
          TabOrder = 8
          OnDropDown = KommBinComboBoxDropDown
        end
        object VerladeBinComboBox: TComboBoxPro
          Left = 440
          Top = 89
          Width = 143
          Height = 21
          Anchors = [akTop, akRight]
          Enabled = False
          TabOrder = 9
          OnDropDown = VerladeBinComboBoxDropDown
        end
        object ZollPrintComboBox: TComboBoxPro
          Left = 120
          Top = 185
          Width = 300
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 10
        end
        object ZollBinComboBox: TComboBoxPro
          Left = 440
          Top = 185
          Width = 143
          Height = 21
          Anchors = [akTop, akRight]
          TabOrder = 11
          OnDropDown = ZollBinComboBoxDropDown
        end
      end
      object LabelPrtGroupBox: TGroupBox
        Left = 8
        Top = 233
        Width = 594
        Height = 290
        Anchors = [akLeft, akRight, akBottom]
        Caption = 'Etikettendrucker'
        TabOrder = 1
        DesignSize = (
          594
          290)
        object Label6: TLabel
          Left = 8
          Top = 33
          Width = 56
          Height = 13
          Caption = 'NVE-Labels'
        end
        object Label12: TLabel
          Left = 8
          Top = 60
          Width = 65
          Height = 13
          Caption = 'Karton-Labels'
        end
        object Label16: TLabel
          Left = 8
          Top = 123
          Width = 70
          Height = 13
          Caption = 'WE-PAL-Label'
        end
        object Label18: TLabel
          Left = 8
          Top = 151
          Width = 95
          Height = 13
          Caption = 'Auto-WE-PAL-Label'
        end
        object Label17: TLabel
          Left = 8
          Top = 179
          Width = 86
          Height = 13
          Caption = 'Komm. NVE-Label'
        end
        object PrinterLocLabel: TLabel
          Left = 8
          Top = 96
          Width = 74
          Height = 13
          Caption = 'PrinterLocLabel'
        end
        object PrinterLocBevel: TBevel
          Left = 52
          Top = 102
          Width = 533
          Height = 5
          Anchors = [akLeft, akTop, akRight]
          Shape = bsTopLine
          ExplicitWidth = 536
        end
        object Label20: TLabel
          Left = 8
          Top = 207
          Width = 92
          Height = 13
          Caption = 'Komm. Lieferschein'
        end
        object Label42: TLabel
          Left = 8
          Top = 235
          Width = 62
          Height = 13
          Caption = 'Paket-Labels'
        end
        object Label43: TLabel
          Left = 8
          Top = 262
          Width = 78
          Height = 13
          Caption = 'Bestands-Labels'
        end
        object NVELabelComboBox: TComboBoxPro
          Left = 120
          Top = 30
          Width = 463
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 0
        end
        object VPELabelComboBox: TComboBoxPro
          Left = 120
          Top = 57
          Width = 463
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 1
        end
        object WEPALLabelComboBox: TComboBoxPro
          Left = 120
          Top = 120
          Width = 463
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 2
        end
        object AutoWEPALLabelComboBox: TComboBoxPro
          Left = 120
          Top = 148
          Width = 463
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 3
        end
        object KommNVEComboBox: TComboBoxPro
          Left = 120
          Top = 176
          Width = 463
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 4
        end
        object KommLSComboBox: TComboBoxPro
          Left = 120
          Top = 204
          Width = 463
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 5
        end
        object PaketLabelCombobox: TComboBoxPro
          Left = 120
          Top = 232
          Width = 463
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 6
        end
        object BestandLabelCombobox: TComboBoxPro
          Left = 120
          Top = 259
          Width = 463
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ColWidth = 320
          ItemHeight = 15
          PopupMenu = PrinterPopupMenu
          TabOrder = 7
        end
      end
      object PrintingCheckBox: TCheckBox
        Left = 0
        Top = 3
        Width = 486
        Height = 17
        Caption = 'Massendruck '#252'ber den Printing-Prozess'
        TabOrder = 2
        Visible = False
      end
    end
    object TabSheet4: TTabSheet
      Caption = 'Anzeige'
      ImageIndex = 8
      OnShow = TabSheet4Show
      DesignSize = (
        610
        542)
      object GroupBox4: TGroupBox
        Left = 8
        Top = 8
        Width = 594
        Height = 73
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Future-Pad'
        TabOrder = 0
        object TouchCheckBox: TCheckBox
          Left = 8
          Top = 24
          Width = 97
          Height = 17
          Caption = 'Touch-Screen'
          TabOrder = 0
        end
        object ScreenKeysCheckBox: TCheckBox
          Left = 8
          Top = 48
          Width = 121
          Height = 17
          Caption = 'Bildschirmtastatur'
          TabOrder = 1
        end
      end
      object GroupBox5: TGroupBox
        Left = 13
        Top = 87
        Width = 594
        Height = 105
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Darstellung'
        TabOrder = 1
        object Label44: TLabel
          Left = 8
          Top = 57
          Width = 23
          Height = 13
          Caption = 'Style'
        end
        object StatIconCheckBox: TCheckBox
          Left = 8
          Top = 24
          Width = 169
          Height = 17
          Caption = 'Status als Icon'
          TabOrder = 0
        end
        object StyleComboBox: TComboBox
          Left = 8
          Top = 73
          Width = 313
          Height = 21
          Style = csDropDownList
          TabOrder = 1
          OnChange = StyleComboBoxChange
          Items.Strings = (
            'Windows'
            'Windows 10'
            'Windows 10 Dark'
            'Windows 10 Grey'
            'Windows 10 Blue'
            'Windows 10 Green'
            'Windows 10 Purple')
        end
      end
      object GroupBox7: TGroupBox
        Left = 8
        Top = 303
        Width = 594
        Height = 81
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Unterreiter'
        TabOrder = 2
        object Label36: TLabel
          Left = 112
          Top = 24
          Width = 57
          Height = 13
          Caption = 'Schriftgr'#246#223'e'
        end
        object Label37: TLabel
          Left = 212
          Top = 24
          Width = 45
          Height = 13
          Caption = 'Textfarbe'
          Visible = False
        end
        object Label31: TLabel
          Left = 8
          Top = 24
          Width = 52
          Height = 13
          Caption = 'Reiterh'#246'he'
        end
        object SubTabFontSizeEdit: TEdit
          Left = 112
          Top = 40
          Width = 64
          Height = 21
          TabOrder = 0
          Text = '0'
        end
        object SubTabFontColorCombobox: TExtColorCombo
          Left = 212
          Top = 39
          Width = 145
          Height = 22
          Style = csOwnerDrawFixed
          ItemIndex = 0
          TabOrder = 1
          Text = '#0|clBlack'
          Visible = False
          Items.Strings = (
            '#0|clBlack'
            '#1|clMaroon'
            '#2|clGreen'
            '#3|clOlive'
            '#4|clNavy'
            '#5|clPurple'
            '#6|clTeal'
            '#7|clGray'
            '#8|clSilver'
            '#9|clRed'
            '#10|clLime'
            '#11|clYellow'
            '#12|clBlue'
            '#13|clFuchsia'
            '#14|clAqua'
            '#15|clWindowText'
            '#16|Benutzer...')
          ComboColors.Color0 = clBlack
          ComboColors.Color1 = clMaroon
          ComboColors.Color2 = clGreen
          ComboColors.Color3 = clOlive
          ComboColors.Color4 = clNavy
          ComboColors.Color5 = clPurple
          ComboColors.Color6 = clTeal
          ComboColors.Color7 = clGray
          ComboColors.Color8 = clSilver
          ComboColors.Color9 = clRed
          ComboColors.Color10 = clLime
          ComboColors.Color11 = clYellow
          ComboColors.Color12 = clBlue
          ComboColors.Color13 = clFuchsia
          ComboColors.Color14 = clAqua
          ComboColors.Color15 = clWindowText
          ComboColors.UserColor = clBlack
          ColorWidth = 24
          TextSpace = 3
          SelColor = clBlack
        end
        object SubTabFontBoldCheckbox: TCheckBox
          Left = 392
          Top = 41
          Width = 97
          Height = 17
          Caption = 'Fett'
          TabOrder = 2
        end
        object SubTabFontSizeUpDown: TUpDown
          Left = 176
          Top = 40
          Width = 16
          Height = 21
          Associate = SubTabFontSizeEdit
          TabOrder = 3
        end
        object SubTabHeightEdit: TEdit
          Left = 8
          Top = 39
          Width = 64
          Height = 21
          TabOrder = 4
          Text = '0'
        end
        object SubTabHeightUpDown: TUpDown
          Left = 72
          Top = 39
          Width = 16
          Height = 21
          Associate = SubTabHeightEdit
          TabOrder = 5
        end
      end
      object GroupBox6: TGroupBox
        Left = 8
        Top = 208
        Width = 594
        Height = 81
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Hauptreiter'
        Color = clBtnHighlight
        ParentBackground = False
        ParentColor = False
        TabOrder = 3
        object Label32: TLabel
          Left = 112
          Top = 24
          Width = 57
          Height = 13
          Caption = 'Schriftgr'#246#223'e'
        end
        object Label33: TLabel
          Left = 212
          Top = 24
          Width = 45
          Height = 13
          Caption = 'Textfarbe'
          Visible = False
        end
        object Label30: TLabel
          Left = 8
          Top = 24
          Width = 52
          Height = 13
          Caption = 'Reiterh'#246'he'
        end
        object MainTabFontSizeEdit: TEdit
          Left = 112
          Top = 40
          Width = 64
          Height = 21
          TabOrder = 0
          Text = '0'
        end
        object MainTabFontColorCombobox: TExtColorCombo
          Left = 212
          Top = 39
          Width = 145
          Height = 22
          Style = csOwnerDrawFixed
          ItemIndex = 0
          TabOrder = 1
          Text = '#0|clBlack'
          Visible = False
          Items.Strings = (
            '#0|clBlack'
            '#1|clMaroon'
            '#2|clGreen'
            '#3|clOlive'
            '#4|clNavy'
            '#5|clPurple'
            '#6|clTeal'
            '#7|clGray'
            '#8|clSilver'
            '#9|clRed'
            '#10|clLime'
            '#11|clYellow'
            '#12|clBlue'
            '#13|clFuchsia'
            '#14|clAqua'
            '#15|clWindowText'
            '#16|Benutzer...')
          ComboColors.Color0 = clBlack
          ComboColors.Color1 = clMaroon
          ComboColors.Color2 = clGreen
          ComboColors.Color3 = clOlive
          ComboColors.Color4 = clNavy
          ComboColors.Color5 = clPurple
          ComboColors.Color6 = clTeal
          ComboColors.Color7 = clGray
          ComboColors.Color8 = clSilver
          ComboColors.Color9 = clRed
          ComboColors.Color10 = clLime
          ComboColors.Color11 = clYellow
          ComboColors.Color12 = clBlue
          ComboColors.Color13 = clFuchsia
          ComboColors.Color14 = clAqua
          ComboColors.Color15 = clWindowText
          ComboColors.UserColor = clBlack
          ColorWidth = 24
          TextSpace = 3
          SelColor = clBlack
        end
        object CheckBox3: TCheckBox
          Left = -480
          Top = -371
          Width = 97
          Height = 17
          Caption = 'Fett'
          TabOrder = 2
        end
        object MainTabFontBoldCheckbox: TCheckBox
          Left = 392
          Top = 41
          Width = 97
          Height = 17
          Caption = 'Fett'
          TabOrder = 3
        end
        object MainTabHeightEdit: TEdit
          Left = 8
          Top = 39
          Width = 64
          Height = 21
          TabOrder = 4
          Text = '0'
        end
        object MainTabFontSizeUpDown: TUpDown
          Left = 176
          Top = 40
          Width = 16
          Height = 21
          Associate = MainTabFontSizeEdit
          TabOrder = 5
        end
        object MainTabHeightUpDown: TUpDown
          Left = 72
          Top = 39
          Width = 16
          Height = 21
          Associate = MainTabHeightEdit
          TabOrder = 6
        end
      end
      object GroupBox2: TGroupBox
        Left = 8
        Top = 398
        Width = 594
        Height = 81
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Drucker'
        TabOrder = 4
        object Label45: TLabel
          Left = 8
          Top = 24
          Width = 79
          Height = 13
          Caption = 'Hintergrundfarbe'
          Visible = False
        end
        object HighlightKommPosColorCombobox: TExtColorCombo
          Left = 8
          Top = 39
          Width = 145
          Height = 22
          Style = csOwnerDrawFixed
          ItemIndex = 10
          TabOrder = 0
          Text = '#10|clLime'
          Items.Strings = (
            '#0|clBlack'
            '#1|clMaroon'
            '#2|clGreen'
            '#3|clOlive'
            '#4|clNavy'
            '#5|clPurple'
            '#6|clTeal'
            '#7|clGray'
            '#8|clSilver'
            '#9|clRed'
            '#10|clLime'
            '#11|clYellow'
            '#12|clBlue'
            '#13|clFuchsia'
            '#14|clAqua'
            '#15|clWindowText'
            '#16|Benutzer...')
          ComboColors.Color0 = clBlack
          ComboColors.Color1 = clMaroon
          ComboColors.Color2 = clGreen
          ComboColors.Color3 = clOlive
          ComboColors.Color4 = clNavy
          ComboColors.Color5 = clPurple
          ComboColors.Color6 = clTeal
          ComboColors.Color7 = clGray
          ComboColors.Color8 = clSilver
          ComboColors.Color9 = clRed
          ComboColors.Color10 = clLime
          ComboColors.Color11 = clYellow
          ComboColors.Color12 = clBlue
          ComboColors.Color13 = clFuchsia
          ComboColors.Color14 = clAqua
          ComboColors.Color15 = clWindowText
          ComboColors.UserColor = clBlack
          ColorWidth = 24
          TextSpace = 3
          SelColor = clLime
        end
        object HighlightKommPosCheckbox: TCheckBox
          Left = 188
          Top = 41
          Width = 181
          Height = 17
          Caption = 'Komm Pos farblich makieren'
          TabOrder = 1
        end
      end
    end
    object LeitstandTabSheet: TTabSheet
      Caption = 'Arbeitsplatz'
      ImageIndex = 21
      OnShow = LeitstandTabSheetShow
      DesignSize = (
        610
        542)
      object Label22: TLabel
        Left = 8
        Top = 16
        Width = 116
        Height = 13
        Caption = 'Name des Arbeitsplatzes'
      end
      object LeitstandEdit: TEdit
        Left = 8
        Top = 32
        Width = 594
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 0
        Text = 'LeitstandEdit'
      end
      object Konfiguartion: TPageControl
        Left = 8
        Top = 59
        Width = 594
        Height = 448
        ActivePage = LeitstandCfgTabSheet
        Anchors = [akLeft, akTop, akRight, akBottom]
        TabOrder = 1
        OnChange = KonfiguartionChange
        object LeitstandCfgTabSheet: TTabSheet
          Caption = 'Konfiguration'
          DesignSize = (
            586
            420)
          object PrinterGroupBox: TGroupBox
            Left = 4
            Top = 6
            Width = 578
            Height = 99
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Drucker'
            TabOrder = 0
            DesignSize = (
              578
              99)
            object Label21: TLabel
              Left = 8
              Top = 28
              Width = 62
              Height = 13
              Caption = 'Normalpapier'
            end
            object Label23: TLabel
              Left = 8
              Top = 60
              Width = 42
              Height = 13
              Caption = 'Etiketten'
            end
            object LeitstandLaserComboBox: TComboBoxPro
              Left = 120
              Top = 25
              Width = 448
              Height = 21
              Style = csOwnerDrawFixed
              Anchors = [akLeft, akTop, akRight]
              ColWidth = 200
              ItemHeight = 15
              TabOrder = 0
            end
            object LeitstandLabelComboBox: TComboBoxPro
              Left = 120
              Top = 57
              Width = 448
              Height = 21
              Style = csOwnerDrawFixed
              Anchors = [akLeft, akTop, akRight]
              ColWidth = 200
              ItemHeight = 15
              TabOrder = 1
            end
          end
          object WEGroupBox: TGroupBox
            Left = 4
            Top = 111
            Width = 578
            Height = 89
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Wareneingang'
            TabOrder = 1
            DesignSize = (
              578
              89)
            object Label34: TLabel
              Left = 8
              Top = 28
              Width = 57
              Height = 13
              Caption = 'WE-Bereich'
            end
            object Label35: TLabel
              Left = 8
              Top = 60
              Width = 77
              Height = 13
              Caption = 'Retoure-Bereich'
            end
            object WELBComboBox: TComboBoxPro
              Left = 120
              Top = 25
              Width = 448
              Height = 22
              Style = csOwnerDrawFixed
              Anchors = [akLeft, akTop, akRight]
              TabOrder = 0
            end
            object RETLBComboBox: TComboBoxPro
              Left = 120
              Top = 57
              Width = 448
              Height = 22
              Style = csOwnerDrawFixed
              Anchors = [akLeft, akTop, akRight]
              TabOrder = 1
            end
          end
          object PackGroupBox: TGroupBox
            Left = 4
            Top = 206
            Width = 578
            Height = 99
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Verpackung'
            TabOrder = 2
            DesignSize = (
              578
              99)
            object Label27: TLabel
              Left = 8
              Top = 28
              Width = 47
              Height = 13
              Caption = 'Packplatz'
            end
            object Label28: TLabel
              Left = 8
              Top = 67
              Width = 95
              Height = 13
              Caption = 'Wartezeit pro Artikel'
            end
            object Label29: TLabel
              Left = 187
              Top = 67
              Width = 49
              Height = 13
              Caption = 'Sekunden'
            end
            object PackplatzComboBox: TComboBoxPro
              Left = 120
              Top = 25
              Width = 448
              Height = 22
              Style = csOwnerDrawFixed
              Anchors = [akLeft, akTop, akRight]
              TabOrder = 0
            end
            object PackDisplayWaitTimeEdit: TEdit
              Left = 120
              Top = 64
              Width = 61
              Height = 21
              TabOrder = 1
              Text = '15'
              OnKeyPress = PackDisplayWaitTimeEditKeyPress
            end
          end
          object PicktoLightGroupBox: TGroupBox
            Left = 4
            Top = 306
            Width = 578
            Height = 100
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Pick to Light'
            TabOrder = 3
            DesignSize = (
              578
              100)
            object Label24: TLabel
              Left = 8
              Top = 28
              Width = 33
              Height = 13
              Caption = 'Station'
            end
            object Label25: TLabel
              Left = 8
              Top = 60
              Width = 51
              Height = 13
              Caption = 'LED-Farbe'
            end
            object PickByLightColorComboBox: TComboBox
              Left = 120
              Top = 57
              Width = 448
              Height = 21
              Style = csDropDownList
              Anchors = [akLeft, akTop, akRight]
              TabOrder = 0
              Items.Strings = (
                'Rot'
                'Gr'#252'n'
                'Blau')
            end
            object PickByLightStationComboBox: TComboBoxPro
              Left = 120
              Top = 25
              Width = 448
              Height = 22
              Style = csOwnerDrawFixed
              Anchors = [akLeft, akTop, akRight]
              TabOrder = 1
              OnChange = PickByLightStationComboBoxChange
            end
          end
        end
        object LeitstandMsgTagSheet: TTabSheet
          Caption = 'Benachrichtigungen'
          ImageIndex = 1
          object LeitMsgAufPopupCheckBox: TCheckBox
            Left = 15
            Top = 16
            Width = 554
            Height = 17
            Caption = 'Popup bei neuen Auftr'#228'gen'
            TabOrder = 0
          end
          object LeitMsgCheckInvPopupCheckBox: TCheckBox
            Left = 15
            Top = 55
            Width = 370
            Height = 17
            Caption = 'Popup bei neuer Check-Inventur'
            TabOrder = 1
          end
          object LeitMsgAutoCheckBox: TCheckBox
            Left = 15
            Top = 95
            Width = 370
            Height = 17
            Caption = 'Popup Automationsfehlern'
            TabOrder = 2
          end
        end
      end
    end
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 480
    Top = 8
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 416
    Top = 16
  end
  object PrinterPopupMenu: TPopupMenu
    Left = 480
    Top = 48
    object PrtPropertiesMenuItem: TMenuItem
      Caption = 'Eigenschaften...'
      OnClick = PrtPropertiesMenuItemClick
    end
  end
end
