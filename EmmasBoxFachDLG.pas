unit EmmasBoxFachDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, ComCtrls;

type
  TEmmasboxForm = class(TForm)
    OkButton: TButton;
    CancleButton: TButton;
    AmbientSmallEdit: TEdit;
    AmbientSmallUpDown: TUpDown;
    AmbientLargeUpDown: TUpDown;
    AmbientLargeEdit: TEdit;
    ChilledSmallUpDown: TUpDown;
    ChilledSmallEdit: TEdit;
    ChilledLargeUpDown: TUpDown;
    ChilledLargeEdit: TEdit;
    FrozenSmallUpDown: TUpDown;
    FrozenSmallEdit: TEdit;
    FrozenLargeEdit: TEdit;
    FrozenLargeUpDown: TUpDown;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    Label5: TLabel;
    Bevel1: TBevel;
    Label6: TLabel;
    Label7: TLabel;
    Label8: TLabel;
    LieferLabel: TLabel;
    AuftragLabel: TLabel;
    KundenLabel: TLabel;
    Bevel2: TBevel;
    ASResLabel: TLabel;
    CSResLabel: TLabel;
    FSResLabel: TLabel;
    ALResLabel: TLabel;
    CLResLabel: TLabel;
    FLResLabel: TLabel;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
  private
    fRefAuf     : Integer;
    fRefMand    : Integer;
    fRefLager   : Integer;
    fAccessCode : String;
    fAuftragNr  : String;
    fEmmasCode  : String;
  public
    procedure Prepare (const RefAuf : Integer);
  end;

function SetEmmasboxReservierung (const RefAuf         : Integer;
                                  const AccessCode     : String;
                                  const AmbientSmall   : integer;
                                  const ChilledSmall   : integer;
                                  const FrozenSmall    : integer;
                                  const AmbientLarge   : integer;
                                  const ChilledLarge   : integer;
                                  const FrozenLargevar : integer) : Integer;

implementation

{$R *.dfm}

uses
  DB, ADODB, DatenModul, ConfigModul, PrintModul, SprachModul, SendHTTPRequest, EncdDecd,
  uLkJSON, ResourceText, LogFile, TerminalServices, LVSGlobalDaten, LVSDatenInterface;

const
  URLAPICall = 'v1/api/storelogix/order';

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 19.07.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetEmmasboxReservierung (const RefAuf         : Integer;
                                  const AccessCode     : String;
                                  const AmbientSmall   : integer;
                                  const ChilledSmall   : integer;
                                  const FrozenSmall    : integer;
                                  const AmbientLarge   : integer;
                                  const ChilledLarge   : integer;
                                  const FrozenLargevar : integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_EMMASBOX.SET_FACH_RESERVIERUNG';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAuf',ftInteger,pdInput, 12, RefAuf);
    Parameters.CreateParameter('pAccessCode',ftString,pdInput, 32, AccessCode);
    Parameters.CreateParameter('pAmbientSmall',ftInteger,pdInput, 12, GetPLSQLParameter (AmbientSmall));
    Parameters.CreateParameter('pChilledSmall',ftInteger,pdInput, 12, GetPLSQLParameter (ChilledSmall));
    Parameters.CreateParameter('pFrozenSmall',ftInteger,pdInput, 12, GetPLSQLParameter (FrozenSmall));
    Parameters.CreateParameter('pAmbientLarge',ftInteger,pdInput, 12, GetPLSQLParameter (AmbientLarge));
    Parameters.CreateParameter('pChilledLarge',ftInteger,pdInput, 12, GetPLSQLParameter (ChilledLarge));
    Parameters.CreateParameter('pFrozenLarge',ftInteger,pdInput, 12, GetPLSQLParameter (FrozenLargevar));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEmmasboxForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  prtres,
  errcode   : Integer;
  fpath,
  station,
  fname,
  errtext,
  urldata   : String;
  apiurl,
  apiuser,
  apipasswd : String;
  js        : TlkJSONobject;
  fs,
  errfs     : TlkJSONbase;
  emmaslog  : TLogFile;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else if ((AmbientSmallUpDown.Position = 0) and
           (ChilledSmallUpDown.Position = 0) and
           (FrozenSmallUpDown.Position = 0) and
           (AmbientLargeUpDown.Position = 0) and
           (ChilledLargeUpDown.Position = 0) and
           (FrozenLargeUpDown.Position = 0)) then begin
    CanClose := False;
    MessageDlg(FormatMessageText (1141, []), mtError, [mbOK], 0);
  end else begin
    CheckConfigParameter (fRefMand, -1, fRefLager, 'YND_EMMASBOXAPI_URL', apiurl);
    CheckConfigParameter (fRefMand, -1, fRefLager, 'YND_EMMASBOXAPI_User', apiuser);
    CheckConfigParameter (fRefMand, -1, fRefLager, 'YND_EMMASBOXAPI_Passwd', apipasswd);

    if (Length (fEmmasCode) > 0) then
      urldata := '{"order":{"order_id":"'+fEmmasCode+'", "emmasbox": {'
    else
      urldata := '{"order":{"order_id":"'+fAuftragNr+'", "emmasbox": {';


    urldata := urldata + '"ambient_small": '+IntToStr (AmbientSmallUpDown.Position);
    urldata := urldata + ',"chilled_small": '+IntToStr (ChilledSmallUpDown.Position);
    urldata := urldata + ',"frozen_small": '+IntToStr (FrozenSmallUpDown.Position);
    urldata := urldata + ',"ambient_large": '+IntToStr (AmbientLargeUpDown.Position);
    urldata := urldata + ',"chilled_large": '+IntToStr (ChilledLargeUpDown.Position);
    urldata := urldata + ',"frozen_large": '+IntToStr (FrozenLargeUpDown.Position);

    urldata := urldata +'}}}';

    emmaslog := TLogFile.Create;
    try
      emmaslog.LogFileName := LVSConfigModul.GetSessionLogDir ('Emmasbox') + 'emmasbox.log';

      emmaslog.Open;

      emmaslog.Write(OSUserName+':'+LVSDatenModul.AktClientName);
      emmaslog.Write('  ==> : '+apiurl+'\'+URLAPICall);
      emmaslog.Write('  ==> : '+urldata);

      fpath := LVSConfigModul.GetSessionDataDir;
      fpath := fpath + 'Emmasbox';

      ForceDirectories (fpath);

      fname := fpath + '\emmas_'+fAuftragNr+'.json';

      if SendRequest (apiurl, // Host,
                      -1,
                      URLAPICall, // Service
                      'POST', //Methode
                      '', // Proxy,
                      apiuser, apipasswd,
                      '',
                      'application/json',
                      [],
                      urldata, // RequestData
                      fname, // Antwortdatei
                      errcode, // Fehlercode
                      errtext) // Fehlertext
                    then
      begin
        fs := nil;

        js := TlkJSONstreamed.loadfromfile (fname) as TlkJsonObject;

        if not Assigned (js) then begin
          errtext := 'API error message';

          emmaslog.Write('  <== : '+ errtext);
        end else begin
          emmaslog.Write('  <== : '+ TlkJSON.GenerateText(js));

          try
            fs := js.Field['status'];
          except
          end;
        end;

        if Assigned (fs) then begin
          if (fs.Value = 'success') then begin
            CanClose := True;

            errfs := js.Field['delivery_code'];

            if Assigned (errfs) then
              fAccessCode := errfs.Value;

            res := SetEmmasboxReservierung (fRefAuf, fAccessCode, AmbientSmallUpDown.Position, ChilledSmallUpDown.Position, FrozenSmallUpDown.Position, AmbientLargeUpDown.Position, ChilledLargeUpDown.Position, FrozenLargeUpDown.Position);

            if (res = 0) then begin
              CanClose := true;

              prtres := PrintModule.PrintReport('', fRefMand, fRefLager, '', 'EMMASBOX-DELIVERY', '', ['REF:' + IntToStr (fRefAuf)], errtext, False, 0);
            end else begin
              CanClose := false;

              MessageDlg('Fehler bei Ablegen der Emmasbox-Daten'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
            end;
          end else begin
            errfs := js.Field['message'];

            if Assigned (errfs) then
              errtext := errfs.Value
            else begin
              errfs := js.Field['error'];

              if Assigned (errfs) then
                errtext := errfs.Value
            end;

            CanClose := False;
            MessageDlg(FormatMessageText (1142, [errtext]), mtError, [mbOK], 0);
          end;
        end else begin
          emmaslog.Write('  !!! : '+ errtext);

          CanClose := False;
          MessageDlg(FormatMessageText (1142, [errtext]), mtError, [mbOK], 0);
        end;
      end else begin
        CanClose := False;
        MessageDlg(FormatMessageText (1142, [errtext]), mtError, [mbOK], 0);
      end;
    finally
      emmaslog.Close;
      emmaslog.Free;
    end;
  end;
end;

procedure TEmmasboxForm.FormCreate(Sender: TObject);
begin
  fRefAuf     := -1;
  fRefMand    := -1;
  fRefLager   := -1;

  ASResLabel.Caption := '';
  CSResLabel.Caption := '';
  FSResLabel.Caption := '';
  ALResLabel.Caption := '';
  CLResLabel.Caption := '';
  FLResLabel.Caption := '';

  fAuftragNr  := '';
  fEmmasCode  := '';
  fAccessCode := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AmbientSmallEdit);
    LVSSprachModul.SetNoTranslate (Self, AmbientLargeEdit);
    LVSSprachModul.SetNoTranslate (Self, ChilledSmallEdit);
    LVSSprachModul.SetNoTranslate (Self, ChilledLargeEdit);
    LVSSprachModul.SetNoTranslate (Self, FrozenSmallEdit);
    LVSSprachModul.SetNoTranslate (Self, FrozenLargeEdit);
    LVSSprachModul.SetNoTranslate (Self, KundenLabel);
    LVSSprachModul.SetNoTranslate (Self, AuftragLabel);
    LVSSprachModul.SetNoTranslate (Self, LieferLabel);
  {$endif}
end;

procedure TEmmasboxForm.Prepare (const RefAuf : Integer);
var
  i,
  iwert    : Integer;
  slist    : TStringList;
  aufquery : TADOQuery;
begin
  fRefAuf := RefAuf;

  aufquery := TADOQuery.Create (Self);

  try
    aufquery.Connection := LVSDatenModul.MainADOConnection;

    aufquery.SQL.Clear;
    aufquery.SQL.Add ('select a.REF,a.REF_MAND,a.REF_LAGER,a.KUNDEN_NR,a.KUNDEN_NAME,a.AUFTRAG_NR,a.AUF_REFERENZ,a.VERSAND_DATUM,a.LIEFER_DATUM,a.TOUR_INDEX,a.PRIO_LAGER,qa.ANLIEFERZEIT_VON,qa.ANLIEFERZEIT_BIS,adr.COLLI_ADR_TEXT,adr.REFERENCE_CODE');
    aufquery.SQL.Add ('from V_AUFTRAG a inner join VQ_AUFTRAG qa on (qa.REF=a.REF) inner join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF) inner join V_AUFTRAG_ADR adr on (adr.REF=qa.REF_LIEFER_ADR) where a.REF=:ref');
    aufquery.Parameters [0].Value := fRefAuf;

    aufquery.Open;

    fRefMand   := aufquery.FieldByName ('REF_MAND').AsInteger;
    fRefLager  := aufquery.FieldByName ('REF_LAGER').AsInteger;

    fAuftragNr := aufquery.FieldByName ('AUFTRAG_NR').AsString;
    fEmmasCode := aufquery.FieldByName ('REFERENCE_CODE').AsString;

    KundenLabel.Caption  := aufquery.FieldByName ('KUNDEN_NR').AsString + ' / ' + aufquery.FieldByName ('KUNDEN_NAME').AsString;
    AuftragLabel.Caption := aufquery.FieldByName ('AUFTRAG_NR').AsString;

    try
      LieferLabel.Caption := aufquery.FieldByName ('LIEFER_DATUM').AsString;
    except
      LieferLabel.Caption := '??.??.????';
    end;

    slist := TStringList.Create;

    try
      slist.Delimiter := ';';
      slist.DelimitedText := aufquery.FieldByName('COLLI_ADR_TEXT').AsString;

      for i:=0 to slist.Count - 1 do begin
        if TryStrToInt (copy (slist [i], 4), iwert) and (iwert > 0) then begin
          if (Copy (slist [i], 1, 2) = 'AS') then
            ASResLabel.Caption := IntToStr (iwert)
          else if (Copy (slist [i], 1, 2) = 'AL') then
            ALResLabel.Caption := IntToStr (iwert)
          else if (Copy (slist [i], 1, 2) = 'FS') then
            FSResLabel.Caption := IntToStr (iwert)
          else if (Copy (slist [i], 1, 2) = 'FL') then
            FLResLabel.Caption := IntToStr (iwert)
          else if (Copy (slist [i], 1, 2) = 'CS') then
            CSResLabel.Caption := IntToStr (iwert)
          else if (Copy (slist [i], 1, 2) = 'CL') then
            CLResLabel.Caption := IntToStr (iwert);
        end;
      end;
    finally
      slist.Free;
    end;

    aufquery.Close;

    aufquery.SQL.Clear;
    aufquery.SQL.Add ('select * from VQ_EMMASBOX_RESERVATION where REF_AUF_KOPF=:ref and STATUS=''AKT''');
    aufquery.Parameters [0].Value := fRefAuf;

    aufquery.Open;

    if (aufquery.RecordCount > 0) then begin
      AmbientSmallUpDown.Position := aufquery.FieldByName('AMBIENT_SMALL').AsInteger;
      ChilledSmallUpDown.Position := aufquery.FieldByName('CHILLED_SMALL').AsInteger;
      FrozenSmallUpDown.Position  := aufquery.FieldByName('FROZEN_SMALL').AsInteger;
      AmbientLargeUpDown.Position := aufquery.FieldByName('AMBIENT_LARGE').AsInteger;
      ChilledLargeUpDown.Position := aufquery.FieldByName('CHILLED_LARGE').AsInteger;
      FrozenLargeUpDown.Position  := aufquery.FieldByName('FROZEN_LARGE').AsInteger;
    end;

    aufquery.Close;
  finally
    aufquery.Free;
  end;
end;

end.
