unit Led;

interface
uses
  SysUtils, WinTypes, WinProcs, Messages, Classes, Graphics, Controls,
  Forms, Dialogs, ExtCtrls;


type
  TLEDFarbeType = (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>gent<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>);
  TLEDStatusType = (<PERSON>, <PERSON>, Aus, Blink);

type
  TLED = class;

  {$IFDEF WIN32}
    TBlinkThread = class(TThread)
      private
        Handler : TLED;
      protected
        procedure Execute; override;
      public
        constructor Create (LedHandler: TLED);
    end;
  {$ENDIF}

  TLED = class(Tgraphiccontrol)
  private
    fFarbe:TLEDFarbeType;
    fLedStatus:TLEDStatusType;
    fStatus:TLEDStatusType;
//    fBlinkStatus:TLEDStatusType;
    fBlinkTime:Integer;
    fBlinkTimeout : DWORD;
    fOnStatChange : TNotifyEvent;

    BlinkThread : TBlinkThread;
    { Private-Deklarationen }
  protected
    procedure Paint; override;
    procedure writefledfarbe(afarbe:TLEDFarbeType);
    procedure writefledEinAus(aEinAus:TLEDStatusType);
  public
   constructor create (aowner:tcomponent);override;
   destructor  Destroy;override;
  published
    property Enabled;
    property Visible; 
    property Width default 17;
    property Height default 17;
    property BlinkTime : Integer         read fBlinkTime   write fBlinkTime default 500;
    property Farbe     : TLEDFarbeType   read fFarbe       write writeFLEDFarbe Default Rot;
    property Status    : TLEDStatusType  read fLedStatus   write writeFLEDEinAus;

    property OnStatusChange : TNotifyEvent read fOnStatChange write fOnStatChange;
  end;

procedure Register;

implementation

{$R led.res}

{$IFDEF WIN32}
  constructor TBlinkThread.Create (LedHandler: TLed);
  begin
    inherited Create (True);

    Handler := LedHandler;

    FreeOnTerminate := True;

    Resume;
  end;

  procedure TBlinkThread.Execute;
  begin
    while Not (Terminated) do begin
      if (Handler.fLEDStatus = Blink) and (GetTickCount > Handler.fBlinkTimeout) then begin
        if (Handler.fStatus = Ein) then
          Handler.fStatus := Aus
        else Handler.fStatus := Ein;

        Handler.invalidate;

        Handler.fBlinkTimeout := GetTickCount + DWORD (Handler.fBlinkTime);
      end;

      Sleep (100);
    end;
  end;
{$ENDIF}

constructor TLED.create (aowner:tcomponent);
begin
  inherited create(aowner);

  fFarbe     := Rot;
  fLedStatus := Invisible;
  fStatus    := Invisible;
  fBlinkTime := 500;

  Width := 17;
  Height := 17;

{$IFNDEF NO_THREADS}
  if not (csDesigning in ComponentState) then
    BlinkThread := TBlinkThread.Create (Self);
{$ENDIF}
end;

destructor TLED.Destroy;
begin
  {$IFDEF WIN32}
    if (BlinkThread <> nil) then begin
      if not (BlinkThread.Terminated) then
        BlinkThread.Terminate;
    end;
  {$ENDIF}

  inherited Destroy;
end;

procedure TLED.Paint;
var bm:tbitmap;
begin
  if (fStatus <> Invisible) then begin
    bm:=tbitmap.create;

    if (fStatus = ein) then begin
      if ffarbe=rot then bm.handle:=loadbitmap(hinstance,'LED_ROT') else
      if ffarbe=gruen then bm.handle:=loadbitmap(hinstance,'LED_GRUEN')else
      if ffarbe=blau then bm.handle:=loadbitmap(hinstance,'LED_BLAU')else
      if ffarbe=Gelb then bm.handle:=loadbitmap(hinstance,'LED_GELB')else
      if ffarbe=mangenta then bm.handle:=loadbitmap(hinstance,'LED_MANGENTA')else
      if ffarbe=grau then bm.handle:=loadbitmap(hinstance,'LED_GRAU')else
      if ffarbe=Cyan then bm.handle:=loadbitmap(hinstance,'LED_CYAN')else
      if ffarbe=Weiss then bm.handle:=loadbitmap(hinstance,'LED_WEISS')
    end else begin
      if ffarbe=rot then bm.handle:=loadbitmap(hinstance,'LED_ROT_DUNKEL') else
      if ffarbe=gruen then bm.handle:=loadbitmap(hinstance,'LED_GRUEN_DUNKEL')else
      if ffarbe=blau then bm.handle:=loadbitmap(hinstance,'LED_BLAU_DUNKEL')else
      if ffarbe=Gelb then bm.handle:=loadbitmap(hinstance,'LED_GELB_DUNKEL')else
      if ffarbe=mangenta then bm.handle:=loadbitmap(hinstance,'LED_MANGENTA_DUNKEL')else
      if ffarbe=grau then bm.handle:=loadbitmap(hinstance,'LED_SCHWARZ')else
      if ffarbe=Cyan then bm.handle:=loadbitmap(hinstance,'LED_CYAN_DUNKEL')else
      if ffarbe=Weiss then bm.handle:=loadbitmap(hinstance,'LED_GRAU_DUNKEL');
    end;

    bm.Transparent := True;
    bm.TransparentColor := bm.canvas.pixels[0,0];
    bm.TransparentMode := tmAuto;
    canvas.drAW(0,0,bm);

    bm.free;
  end;
end;

 procedure TLED.writefledfarbe(afarbe:TLEDFarbeType);
 begin
   if (ffarbe <> afarbe) then begin
     ffarbe:=afarbe;
     invalidate;
   end;
  end;

 procedure TLED.writefledEinAus(aEinAus:TLEDStatusType);
 begin
   if (fLedStatus <> aEinAus) then begin
     fLedStatus :=aEinAus;

     if (fLedStatus = Blink) then
       fBlinkTimeout := GetTickCount + DWORD (fBlinkTime)
     else begin
       fStatus := fLedStatus;
       invalidate;
     end;

     if Assigned (fOnStatChange) then
       fOnStatChange (Self);
   end;
 end;

procedure Register;
begin
  RegisterComponents('Fun', [TLED]);
end;

end.

