unit WebDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, OleCtrls, SHDocVw_TLB, MSHTML, StdCtrls;

type
  TWebForm = class(TForm)
    WebBrowser: TCppWebBrowser;
    procedure FormResize(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormKeyPress(Sender: TObject; var Key: Char);
    procedure FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
    procedure WebBrowserDocumentComplete(ASender: TObject;
      const pDisp: IDispatch; var URL: OleVariant);
    procedure FormShow(Sender: TObject);
    procedure Button1Click(Sender: TObject);
  private
    fScaleFit       : Boolean;
    fGoogelMapsAddr : String;
    HTMLWindow2     : IHTMLWindow2;
  public
    property ScaleFit : Boolean      read fScaleFit       write fScaleFit;
    property GoogelMapsAddr : String read fGoogelMapsAddr write fGoogelMapsAddr;
  end;

implementation

{$R *.dfm}

uses
  ActiveX;

const
GoogelMapsStr: AnsiString =
'<html> '+
'<head> '+
'<meta name="viewport" content="initial-scale=1.0, user-scalable=yes" /> '+
'<script type="text/javascript" src="http://maps.google.com/maps/api/js?sensor=true"></script> '+
'<script type="text/javascript"> '+
''+
''+
'  var geocoder; '+
'  var map;  '+
'  var trafficLayer;'+
'  var bikeLayer;'+
'  var markersArray = [];'+
''+
''+
'  function initialize() { '+
'    geocoder = new google.maps.Geocoder();'+
'    var latlng = new google.maps.LatLng(51.4607492,7.2253623); '+
'    var myOptions = { '+
'      zoom: 13, '+
'      center: latlng, '+
'      mapTypeId: google.maps.MapTypeId.ROADMAP '+
'    }; '+
'    map = new google.maps.Map(document.getElementById("map_canvas"), myOptions); '+
'    trafficLayer = new google.maps.TrafficLayer();'+
'    bikeLayer = new google.maps.BicyclingLayer();'+
'    map.set("streetViewControl", false);'+
'  } '+
''+
''+
'  function codeAddress(address) { '+
'    if (geocoder) {'+
'      geocoder.geocode( { address: address}, function(results, status) { '+
'        if (status == google.maps.GeocoderStatus.OK) {'+
'          map.setCenter(results[0].geometry.location);'+
'          PutMarker(results[0].geometry.location.lat(), results[0].geometry.location.lng(), results[0].geometry.location.lat()+","+results[0].geometry.location.lng());'+
'        } else {'+
'          alert("Geocode was not successful for the following reason: " + status);'+
'        }'+
'      });'+
'    }'+
'  }'+
''+
''+
'  function GotoLatLng(Lat, Lang) { '+
'   var latlng = new google.maps.LatLng(Lat,Lang);'+
'   map.setCenter(latlng);'+
'   PutMarker(Lat, Lang, Lat+","+Lang);'+
'  }'+
''+
''+
'function ClearMarkers() {  '+
'  if (markersArray) {        '+
'    for (i in markersArray) {  '+
'      markersArray[i].setMap(null); '+
'    } '+
'  } '+
'}  '+
''+
'  function PutMarker(Lat, Lang, Msg) { '+
'   var latlng = new google.maps.LatLng(Lat,Lang);'+
'   var marker = new google.maps.Marker({'+
'      position: latlng, '+
'      map: map,'+
'      title: Msg+" ("+Lat+","+Lang+")"'+
'  });'+
' markersArray.push(marker); '+
'  }'+
''+
''+
'  function TrafficOn()   { trafficLayer.setMap(map); }'+
''+
'  function TrafficOff()  { trafficLayer.setMap(null); }'+
''+''+
'  function BicyclingOn() { bikeLayer.setMap(map); }'+
''+
'  function BicyclingOff(){ bikeLayer.setMap(null);}'+
''+
'  function StreetViewOn() { map.set("streetViewControl", true); }'+
''+
'  function StreetViewOff() { map.set("streetViewControl", false); }'+
''+
''+'</script> '+
'</head> '+
'<body onload="initialize()"> '+
'  <div id="map_canvas" style="width:100%; height:100%"></div> '+
'</body> '+
'</html> ';

procedure TWebForm.Button1Click(Sender: TObject);
var
  address : String;
begin
  if (Length (fGoogelMapsAddr) > 0) and Assigned (HTMLWindow2) then begin
    while WebBrowser.ReadyState < READYSTATE_INTERACTIVE do
      Application.ProcessMessages;

    address := fGoogelMapsAddr;
    address := StringReplace(StringReplace(Trim(address), #13, ' ', [rfReplaceAll]), #10, ' ', [rfReplaceAll]);

    try
      HTMLWindow2.execScript(Format('codeAddress(%s)',[QuotedStr(address)]), 'JavaScript');
    except
      MessageDLG ('Die Adresse kann nicht angezeigt werden', mtError, [mbOk], 0);
    end;
  end;
end;

procedure TWebForm.FormCreate(Sender: TObject);
begin
  fScaleFit := False;
  HTMLWindow2 := Nil;
end;


procedure TWebForm.FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
begin
  if (Key = VK_ESCAPE) then
   self.Close;
end;

procedure TWebForm.FormKeyPress(Sender: TObject; var Key: Char);
begin
  if (Key = #27) then
    Close;
end;

procedure TWebForm.FormResize(Sender: TObject);
var
  lZoomX,
  lZoomY   : real;
  htmlbody : IHTMLElement;
begin
  if (fScaleFit) then begin
    while WebBrowser.ReadyState < READYSTATE_INTERACTIVE do
       Application.ProcessMessages;

    if Assigned (WebBrowser.Document) then begin
      htmlbody := (WebBrowser.Document as IHTMLDocument2).Body;

      if Assigned (htmlbody) then begin
        try
          lZoomX := WebBrowser.Width  / WebBrowser.OleObject.Document.Body.ScrollWidth;
          lZoomY := WebBrowser.Height / WebBrowser.OleObject.Document.Body.ScrollHeight;

          if lZoomX<lZoomY then
            WebBrowser.OleObject.Document.Body.Style.Zoom := lZoomX
          else
            WebBrowser.OleObject.Document.Body.Style.Zoom := lZoomY;
        except
        end;
      end;
    end;
  end;
end;

procedure TWebForm.FormShow(Sender: TObject);
var
  aStream     : TMemoryStream;
begin
  if (Length (fGoogelMapsAddr) > 0) then begin
    WebBrowser.Navigate('about:blank');

    if Assigned(WebBrowser.Document) then begin
      try
        aStream := TMemoryStream.Create;
        try
          aStream.WriteBuffer(Pointer(GoogelMapsStr)^, Length(GoogelMapsStr));

          aStream.Seek(0, soFromBeginning);

          (WebBrowser.Document as IPersistStreamInit).Load (TStreamAdapter.Create(aStream));
        finally
          aStream.Free;
        end;

        HTMLWindow2 := (WebBrowser.Document as IHTMLDocument2).parentWindow;

        Application.ProcessMessages;
        Sleep (100);
        Application.ProcessMessages;

        Button1Click (Sender);
      except
        MessageDLG ('Die Webseite kann nicht dargestellt werden', mtError, [mbOk], 0);
      end;
    end;
  end;
end;

procedure TWebForm.WebBrowserDocumentComplete(ASender: TObject; const pDisp: IDispatch; var URL: OleVariant);
begin
  FormResize (ASender);

  if fScaleFit then begin
    try
      WebBrowser.OleObject.Document.Body.Style.OverflowX := 'hidden';
      WebBrowser.OleObject.Document.Body.Style.OverflowY := 'hidden';
    except
    end;
  end;
end;

end.
