unit NewCfgTempDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TNewCfgTempForm = class(TForm)
    NameEdit: TEdit;
    Label1: TLabel;
    TempTypeRadioGroup: TRadioGroup;
    AbortButton: TButton;
    OkButton: TButton;
    procedure FormCreate(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

procedure TNewCfgTempForm.FormCreate(Sender: TObject);
begin
  NameEdit.Text := '';
end;

end.
