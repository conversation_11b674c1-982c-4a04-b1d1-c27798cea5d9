unit Timers;

{$undef Trace}

{ $ R-}

interface

uses Classes, Windows;

type
  TTimeout = class (TPersistent)
  private
    fStartTick   : DWORD;
    fTimeoutTick : Int64;
    fEnable      : Boolean;

    procedure SetEnable (const Flag : Boolean);
  public
    property  Enabled : <PERSON><PERSON><PERSON> read fEnable write SetEnable;

    constructor Create;
    procedure   SetTimeout (const Timeout : Integer);
    function    CheckTimeout : Boolean;
    function    GetTimeout : Integer;
  end;

function ReadCPUCounter          : Int64;
function GetCPUFrequency         : Int64;
function GetPerformanceCount     : Int64;
function GetPerformanceFrequency : Int64;

function GetPerformanceCountDiff (const LastCounter : Int64) : Int64; overload;
function GetPerformanceCountDiff (const AktCounter, LastCounter : Int64) : Int64; overload;

implementation

uses
  {$ifdef Trace}
    Trace,
  {$endif}

  SysUtils;

var
  QPF     : Int64 = 0;
  CPUFreq : Int64 = 0;

function GetCPUFrequency : Int64;
begin
  Result := CPUFreq;
end;

function ReadCPUCounter: Int64;
asm
  db $0F, $31  //opcode for RDTSC
end;

//Wert in us
function GetPerformanceCount : Int64;
var
  ticks : Int64;
begin
  QueryPerformanceCounter (ticks);

  Result := (ticks * 100) div (QPF div 10000);
end;

function GetPerformanceFrequency : Int64;
begin
  Result := QPF;
end;

function GetPerformanceCountDiff (const LastCounter : Int64) : Int64;
var
  pctime : Int64;
begin
  pctime := GetPerformanceCount;

  if (pctime >= LastCounter) then
    Result := (pctime - LastCounter)
  else
    Result := (pctime + 1 + ($ffffffffffffffff - LastCounter));
end;

function GetPerformanceCountDiff (const AktCounter, LastCounter : Int64) : Int64;
begin
  if (AktCounter >= LastCounter) then
    Result := (AktCounter - LastCounter)
  else
    Result := (AktCounter + 1 + ($ffffffffffffffff - LastCounter));
end;

constructor TTimeout.Create;
begin
  inherited Create;

  fStartTick   := 0;
  fTimeoutTick := 0;

  fEnable := false;
end;

procedure TTimeout.SetEnable (const Flag : Boolean);
begin
  fEnable := Flag;
end;

procedure TTimeout.SetTimeout (const Timeout : Integer);
begin
  {$ifdef Trace}
    ProcedureStart ('TTimeout.SetTimeout');
    TraceParameter ('Timeout', Timeout);
  {$endif}

  if (Timeout = -1) then
    fEnable := False
  else begin
    fStartTick   := GetTickCount;

    fTimeoutTick := Int64 (GetTickCount);
    fTimeoutTick := fTimeoutTick + Int64 (Timeout);

    {$ifdef Trace}
      TraceResult ('fStartTick  ', IntToHex (fStartTick, 8));
      TraceResult ('fTimeoutTick', IntToHex (fTimeoutTick, 8));
    {$endif}

    fEnable := True;
  end;

  {$ifdef Trace}
    ProcedureStop;
  {$endif}
end;

function  TTimeout.GetTimeout : Integer;
var
  ticks : DWORD;
begin
  {$ifdef Trace}
    FunctionStart ('TTimeout.GetTimeout');
  {$endif}

  if not (Enabled) then
    Result := -1
  else begin
    ticks := GetTickCount;

    {$ifdef Trace}
      TraceResult ('ticks       ', IntToHex (ticks, 8));
      TraceResult ('fStartTick  ', IntToHex (fStartTick, 8));
      TraceResult ('fTimeoutTick', IntToHex (fTimeoutTick, 8));
    {$endif}

    if (fTimeoutTick < $100000000) then
      Result := fTimeoutTick - ticks
    else begin
      if (ticks >= fStartTick) then
        Result := 0
      else begin
        fTimeoutTick := fTimeoutTick - $100000000;

        Result := fTimeoutTick - ticks;
      end;
    end;
  end;

  {$ifdef Trace}
    FunctionStop (Result);
  {$endif}
end;

function  TTimeout.CheckTimeout : Boolean;
var
  ticks : DWORD;
begin
  {$ifdef Trace}
    FunctionStart ('TTimeout.CheckTimeout');
  {$endif}

  if not (Enabled) then
    Result := True
  else begin
    ticks := GetTickCount;

    {$ifdef Trace}
      TraceResult ('ticks       ', IntToHex (ticks, 8));
      TraceResult ('fStartTick  ', IntToHex (fStartTick, 8));
      TraceResult ('fTimeoutTick', IntToHex (fTimeoutTick, 8));
    {$endif}

    if (fTimeoutTick < $100000000) then
      Result := (ticks > fTimeoutTick)
    else begin
      if (ticks >= fStartTick) then
        Result := False
      else begin
        fTimeoutTick := fTimeoutTick - $100000000;

        Result := (ticks > fTimeoutTick);
      end;
    end;
  end;

  {$ifdef Trace}
    FunctionStop (Result);
  {$endif}
end;

var
  C,S,E,D,T: Int64;

initialization
  QueryPerformanceFrequency(QPF);

  //CPU-Taktfrequenz bestimmen
  C := 1000000;
  QueryPerformanceCounter(S);
  D := ReadCPUCounter;
  while C > 0 do Dec(C);
  QueryPerformanceCounter(E);
  T := ReadCPUCounter;

  CPUFreq := Round((T - D) * QPF / (E - S));

end.
