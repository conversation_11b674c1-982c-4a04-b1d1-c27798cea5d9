object AbverkaufVerrechnenForm: TAbverkaufVerrechnenForm
  Left = 0
  Top = 0
  Caption = 'Verrechung der Abverk'#228'ufe'
  ClientHeight = 533
  ClientWidth = 852
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel3: TPanel
    Left = 0
    Top = 492
    Width = 852
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      852
      41)
    object CloseButton: TButton
      Left = 768
      Top = 9
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 0
    end
  end
  object MandPanel: TPanel
    Left = 0
    Top = 0
    Width = 852
    Height = 32
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      852
      32)
    object Label2: TLabel
      Left = 8
      Top = 11
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 104
      Top = 8
      Width = 740
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 32
    Width = 852
    Height = 28
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      852
      28)
    object Label11: TLabel
      Left = 8
      Top = 11
      Width = 69
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 104
      Top = 4
      Width = 740
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 60
    Width = 852
    Height = 28
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      852
      28)
    object Label3: TLabel
      Left = 8
      Top = 11
      Width = 85
      Height = 13
      Caption = 'Vertriebsmandant'
    end
    object ComboBoxPro1: TComboBoxPro
      Left = 104
      Top = 4
      Width = 740
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object BillingPageControl: TPageControl
    Left = 0
    Top = 129
    Width = 852
    Height = 363
    ActivePage = OpenBillingTabSheet
    Align = alClient
    TabOrder = 4
    object OpenBillingTabSheet: TTabSheet
      Caption = 'Offene Positionen'
      OnShow = OpenBillingTabSheetShow
      object OpenBillingDBGrid: TDBGridPro
        AlignWithMargins = True
        Left = 3
        Top = 3
        Width = 838
        Height = 288
        Align = alClient
        DataSource = OpenBillingDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 24
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object Panel5: TPanel
        Left = 0
        Top = 294
        Width = 844
        Height = 41
        Align = alBottom
        BevelOuter = bvNone
        TabOrder = 1
        object AbrechnenButton: TButton
          Left = 4
          Top = 9
          Width = 189
          Height = 25
          Caption = 'Alles abrechnen...'
          TabOrder = 0
          OnClick = AbrechnenButtonClick
        end
      end
    end
    object BillingTabSheet: TTabSheet
      Caption = 'Abrechnungen'
      ImageIndex = 1
      OnShow = BillingTabSheetShow
      object Splitter1: TSplitter
        Left = 0
        Top = 144
        Width = 844
        Height = 3
        Cursor = crVSplit
        Align = alTop
        ExplicitLeft = -3
        ExplicitTop = 135
      end
      object BillingDBGrid: TDBGridPro
        AlignWithMargins = True
        Left = 3
        Top = 3
        Width = 838
        Height = 138
        Align = alTop
        DataSource = BillingDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 24
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object Panel1: TPanel
        Left = 0
        Top = 147
        Width = 844
        Height = 41
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 1
      end
      object DBGridPro2: TDBGridPro
        AlignWithMargins = True
        Left = 3
        Top = 191
        Width = 838
        Height = 141
        Align = alClient
        DataSource = BillingPosDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 2
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 24
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
    end
  end
  object Panel4: TPanel
    Left = 0
    Top = 88
    Width = 852
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
  end
  object OpenBillingDataSource: TDataSource
    DataSet = OpenBillingDataSet
    Left = 24
    Top = 360
  end
  object OpenBillingDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    IndexDefs = <>
    Left = 56
    Top = 360
  end
  object BillingDataSource: TDataSource
    DataSet = BillingDataSet
    OnDataChange = BillingDataSourceDataChange
    Left = 104
    Top = 360
  end
  object BillingDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    IndexDefs = <>
    Left = 136
    Top = 360
  end
  object BillingPosDataSource: TDataSource
    DataSet = BillingPosDataSet
    Left = 176
    Top = 360
  end
  object BillingPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    IndexDefs = <>
    Left = 208
    Top = 360
  end
end
