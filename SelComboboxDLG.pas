unit SelComboboxDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro;

type
  TSelComboboxForm = class(TForm)
    SelListBox: TListBox;
    procedure SelArComboBoxChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormKeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
    procedure ListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
    procedure SelListBoxDblClick(Sender: TObject);
  private
    fListHeight       : Integer;
    fParentSelControl : TWinControl;

    fTab : array [0..16] of Integer;
  public
    property ListHeight       : Integer read fListHeight write fListHeight;
    property ParentSelControl : TWinControl read fParentSelControl write fParentSelControl;
  end;

implementation

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelComboboxForm.ListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
var
  line     : String;
  strpos,
  tabcount : Integer;
begin
  if (Control is TListBox) then begin
    (Control as TListBox).Canvas.FillRect(Rect);

    line := (Control as TListBox).Items [Index];

    strpos   := 1;
    tabcount := 0;

    while (Length (line) > 0) do begin
      strpos := Pos ('|', line);

      with (Control as TListBox).Canvas do begin
        if (strpos = 0) then begin
          if (tabcount = 0) then
            TextOut (Rect.Left, Rect.Top, line)
          else TextOut (Rect.Left + fTab [tabcount - 1], Rect.Top, line);

          line := '';
        end else begin
          if (tabcount = 0) then
            TextOut (Rect.Left, Rect.Top, Copy (line, 1, strpos - 1))
          else TextOut (Rect.Left + fTab [tabcount - 1], Rect.Top, Copy (line, 1, strpos - 1));

          line := Copy (line, strpos + 1);
        end;
      end;

      Inc (tabcount);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelComboboxForm.FormCreate(Sender: TObject);
begin
  fTab [0] := 60;
  fTab [1] := 80;
  fTab [2] := 120;
  fTab [3] := 200;

  fListHeight := 4;
  fParentSelControl := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelComboboxForm.FormKeyPress(Sender: TObject; var Key: Char);
begin
  if (Key = #27) then begin
    Key := #0;

    ModalResult := mrAbort;
    Close;
  end else if (Key = #13) then begin
    Key := #0;

    ModalResult := mrOk;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelComboboxForm.FormShow(Sender: TObject);

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function GetItemText (const Index : Integer; const TextIndex : Integer = 0) : String;
  var
   idx,
   strpos  : Integer;
   line,
   resline : String;
  begin
    line := SelListBox.Items [Index];

    idx := 0;

    repeat
      strpos := Pos ('|', line);

      if (strpos = 0) then begin
        resline := line;
        line := '';
      end else begin
        resline := Copy (line,1,strpos - 1);
        line := Copy (line,strpos + 1, Length (line) - strpos);
      end;

      Inc (idx);
    until (idx > TextIndex);

    Result := resline;
  end;

var
  i,j,
  len,
  maxlen  : Integer;
  textstr : String;
  cl      : TPoint;
begin
  if (SelListBox.Items.Count > 0) then begin
    //Spaltenweite bestimmen
    for i := 0 to 3 do begin
      maxlen := 0;

      for j := 0 to SelListBox.Items.Count - 1 do begin
        textstr := GetItemText (j, i);
        len := SelListBox.Canvas.TextWidth (textstr);

        if (len > maxlen) then
          maxlen := len;
      end;

      if (maxlen < 32) then
        maxlen := 16 + 32
      else if (maxlen > ((ClientWidth * 3) div 2)) then
        maxlen := (ClientWidth * 3) div 2
      else
        maxlen := 16 + maxlen;

      if (i = 0) then
        fTab [0] := maxlen
      else
        fTab [i] := fTab [i - 1] + maxlen;
    end;
  end;

  if Assigned (fParentSelControl) and Assigned (fParentSelControl.Parent) then begin
    cl := fParentSelControl.Parent.ClientToScreen (Point (fParentSelControl.Left + (fParentSelControl.Width div 2), fParentSelControl.Top + (fParentSelControl.Height div 2)));

    Top  := cl.Y;
    Left := cl.X;
  end;

  SelListBox.SetFocus;

  if (SelListBox.Items.Count > fListHeight) then
    ClientHeight := SelListBox.BevelWidth * 4 + fListHeight * SelListBox.ItemHeight
  else
    ClientHeight := SelListBox.BevelWidth * 4 + SelListBox.Items.Count * SelListBox.ItemHeight;

  if (SelListBox.ItemIndex >= 0) then
    SelListBox.Selected [SelListBox.ItemIndex];
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelComboboxForm.SelArComboBoxChange(Sender: TObject);
begin
  ModalResult := mrOk;
  Close;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelComboboxForm.SelListBoxDblClick(Sender: TObject);
begin
  ModalResult := mrOk;
end;

end.
