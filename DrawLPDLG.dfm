object DrawLPForm: TDrawLPForm
  Left = 52
  Top = 32
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Lagerspiegel darstellen'
  ClientHeight = 480
  ClientWidth = 961
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnHide = FormHide
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 961
    Height = 153
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label1: TLabel
      Left = 415
      Top = 85
      Width = 20
      Height = 26
      Alignment = taCenter
      BiDiMode = bdLeftToRight
      Caption = 'Feld'#13#10'von'
      ParentBiDiMode = False
    end
    object Label2: TLabel
      Left = 518
      Top = 85
      Width = 20
      Height = 26
      Alignment = taCenter
      BiDiMode = bdLeftToRight
      Caption = 'Feld'#13#10'bis'
      ParentBiDiMode = False
    end
    object Label3: TLabel
      Left = 8
      Top = 89
      Width = 36
      Height = 13
      Caption = 'Bereich'
    end
    object Label4: TLabel
      Left = 279
      Top = 91
      Width = 28
      Height = 13
      Caption = 'Regal'
    end
    object Label6: TLabel
      Left = 8
      Top = 59
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Label84: TLabel
      Left = 7
      Top = 22
      Width = 39
      Height = 13
      Caption = 'Planung'
    end
    object LBComboBox: TComboBoxPro
      Left = 58
      Top = 88
      Width = 215
      Height = 21
      Style = csOwnerDrawFixed
      ItemHeight = 15
      TabOrder = 2
      OnChange = LBComboBoxChange
    end
    object ReiheComboBox: TComboBox
      Left = 320
      Top = 88
      Width = 81
      Height = 21
      Style = csDropDownList
      ItemHeight = 0
      TabOrder = 3
      OnChange = ReiheComboBoxChange
    end
    object FeldVonComboBox: TComboBox
      Left = 440
      Top = 88
      Width = 75
      Height = 21
      Style = csDropDownList
      ItemHeight = 0
      TabOrder = 4
    end
    object FeldBisComboBox: TComboBox
      Left = 544
      Top = 88
      Width = 75
      Height = 21
      Style = csDropDownList
      ItemHeight = 0
      TabOrder = 5
    end
    object CheckBox1: TCheckBox
      Left = 656
      Top = 16
      Width = 209
      Height = 17
      Caption = 'Nur Kommissionierpl'#228'tze'
      TabOrder = 6
    end
    object CheckBox2: TCheckBox
      Left = 656
      Top = 72
      Width = 217
      Height = 17
      Caption = 'Belegung anzeigen'
      TabOrder = 8
    end
    object CheckBox3: TCheckBox
      Left = 656
      Top = 90
      Width = 233
      Height = 17
      Caption = 'Artiklezuordnung anzeigen'
      Checked = True
      State = cbChecked
      TabOrder = 9
      OnClick = CheckBox3Click
    end
    object Button2: TButton
      Left = 8
      Top = 122
      Width = 75
      Height = 25
      Caption = 'Zeichnen'
      TabOrder = 11
      OnClick = Button2Click
    end
    object CheckBox4: TCheckBox
      Left = 656
      Top = 39
      Width = 281
      Height = 17
      Caption = 'Reihenausrichtung umdrehen'
      TabOrder = 7
    end
    object LagerComboBox: TComboBoxPro
      Left = 58
      Top = 56
      Width = 343
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 1
      OnChange = LagerComboBoxChange
    end
    object PlanungComboBox: TComboBoxPro
      Left = 58
      Top = 18
      Width = 343
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 0
      OnChange = PlanungComboBoxChange
    end
    object CheckBox5: TCheckBox
      Left = 656
      Top = 108
      Width = 97
      Height = 17
      Caption = 'LE-Nr. anzeigen'
      TabOrder = 10
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 440
    Width = 961
    Height = 40
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      961
      40)
    object Button1: TButton
      Left = 879
      Top = 10
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Schlie'#223'en'
      Default = True
      ModalResult = 1
      TabOrder = 0
      OnClick = Button1Click
    end
    object PrintButton: TButton
      Left = 8
      Top = 10
      Width = 75
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'Drucken...'
      TabOrder = 1
      OnClick = PrintButtonClick
    end
    object SaveButton: TButton
      Left = 96
      Top = 10
      Width = 75
      Height = 25
      Anchors = [akLeft, akBottom]
      Caption = 'Speichern...'
      TabOrder = 2
      OnClick = SaveButtonClick
    end
  end
  object ScrollBox1: TScrollBox
    Left = 8
    Top = 153
    Width = 945
    Height = 287
    Align = alClient
    TabOrder = 2
  end
  object Panel3: TPanel
    Left = 0
    Top = 153
    Width = 8
    Height = 287
    Align = alLeft
    BevelOuter = bvNone
    TabOrder = 3
  end
  object Panel4: TPanel
    Left = 953
    Top = 153
    Width = 8
    Height = 287
    Align = alRight
    BevelOuter = bvNone
    TabOrder = 4
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 608
    Top = 193
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 608
    Top = 233
  end
  object ADOQuery3: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 608
    Top = 273
  end
  object ADOQuery4: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 608
    Top = 313
  end
  object PrintDialog1: TPrintDialog
    Left = 608
    Top = 153
  end
  object SavePictureDialog1: TSavePictureDialog
    Left = 608
    Top = 121
  end
  object PreviewPrinter1: TPreviewPrinter
    Orientation = poPortrait
    TextOptions.DrawStyle = dsStandard
    TextOptions.MarginLeft = 1.000000000000000000
    TextOptions.MarginTop = 1.000000000000000000
    TextOptions.MarginRight = 1.000000000000000000
    TextOptions.MarginBottom = 1.000000000000000000
    TextOptions.BodyFont.Charset = DEFAULT_CHARSET
    TextOptions.BodyFont.Color = clWindowText
    TextOptions.BodyFont.Height = -13
    TextOptions.BodyFont.Name = 'Arial'
    TextOptions.BodyFont.Style = []
    TextOptions.HeaderFont.Charset = DEFAULT_CHARSET
    TextOptions.HeaderFont.Color = clWindowText
    TextOptions.HeaderFont.Height = -24
    TextOptions.HeaderFont.Name = 'Times New Roman'
    TextOptions.HeaderFont.Style = [fsBold]
    TextOptions.FooterFont.Charset = DEFAULT_CHARSET
    TextOptions.FooterFont.Color = clWindowText
    TextOptions.FooterFont.Height = -13
    TextOptions.FooterFont.Name = 'Times New Roman'
    TextOptions.FooterFont.Style = [fsItalic]
    TextOptions.PageNumFont.Charset = DEFAULT_CHARSET
    TextOptions.PageNumFont.Color = clWindowText
    TextOptions.PageNumFont.Height = -13
    TextOptions.PageNumFont.Name = 'Times New Roman'
    TextOptions.PageNumFont.Style = [fsItalic]
    TextOptions.HeaderMargin = 0.500000000000000000
    TextOptions.FooterMargin = 0.750000000000000000
    TextOptions.HeaderAlign = taCenter
    TextOptions.FooterAlign = taCenter
    TextOptions.PrintPageNumber = pnBottom
    TextOptions.PageNumAlign = taRightJustify
    TextOptions.PageNumText = 'Page %d'
    Units = unInches
    ShowGrid = False
    ZoomOption = zoFitToPage
    ZoomVal = 100
    Left = 720
    Top = 193
  end
end
