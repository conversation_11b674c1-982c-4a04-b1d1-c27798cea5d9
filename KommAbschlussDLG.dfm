object KommAbschlussForm: TKommAbschlussForm
  Left = 152
  Top = 184
  BorderIcons = [biSystemMenu]
  Caption = 'Kommissionierung zur'#252'ckmelden'
  ClientHeight = 615
  ClientWidth = 864
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnHide = FormHide
  OnKeyDown = FormKeyDown
  OnShow = FormShow
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 8
    Height = 615
    Align = alLeft
    BevelOuter = bvNone
    TabOrder = 0
  end
  object Panel2: TPanel
    Left = 856
    Top = 0
    Width = 8
    Height = 615
    Align = alRight
    BevelOuter = bvNone
    TabOrder = 1
  end
  object Panel3: TPanel
    Left = 8
    Top = 0
    Width = 848
    Height = 615
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 2
    object Panel4: TPanel
      Left = 0
      Top = 0
      Width = 848
      Height = 57
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 0
      DesignSize = (
        848
        57)
      object Label1: TLabel
        Left = 0
        Top = 8
        Width = 79
        Height = 13
        Caption = 'Auftrag-Nummer:'
      end
      object AufNrLabel: TLabel
        Left = 96
        Top = 8
        Width = 64
        Height = 13
        Caption = 'AufNrLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label5: TLabel
        Left = 0
        Top = 32
        Width = 87
        Height = 13
        Caption = 'Kommissionierung:'
      end
      object KommNrLabel: TLabel
        Left = 97
        Top = 32
        Width = 78
        Height = 13
        Caption = 'KommNrLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label85: TLabel
        Left = 476
        Top = 32
        Width = 119
        Height = 13
        Anchors = [akTop, akRight]
        Caption = 'Aktueller Kommissionierer'
      end
      object KommUserComboBox: TComboBoxPro
        Left = 601
        Top = 29
        Width = 247
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 0
      end
    end
    object Panel5: TPanel
      Left = 0
      Top = 264
      Width = 848
      Height = 351
      Align = alBottom
      BevelOuter = bvNone
      TabOrder = 2
      DesignSize = (
        848
        351)
      object GroupBox1: TGroupBox
        Left = 0
        Top = 40
        Width = 849
        Height = 273
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Kommissionierposition'
        TabOrder = 1
        DesignSize = (
          849
          273)
        object Label4: TLabel
          Left = 8
          Top = 16
          Width = 46
          Height = 13
          Caption = 'Artikel-Nr:'
        end
        object ArtNrLabel: TLabel
          Left = 80
          Top = 16
          Width = 61
          Height = 13
          Caption = 'ArtNrLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object Label2: TLabel
          Left = 8
          Top = 40
          Width = 53
          Height = 13
          Caption = 'Menge Soll'
        end
        object SollMengeLabel: TLabel
          Left = 80
          Top = 40
          Width = 91
          Height = 13
          Caption = 'SollMengeLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object Bevel1: TBevel
          Left = 0
          Top = 60
          Width = 850
          Height = 5
          Anchors = [akLeft, akTop, akRight]
          Shape = bsTopLine
          ExplicitWidth = 849
        end
        object Label8: TLabel
          Left = 232
          Top = 16
          Width = 28
          Height = 13
          Caption = 'MHD:'
        end
        object Label9: TLabel
          Left = 232
          Top = 40
          Width = 37
          Height = 13
          Caption = 'Charge:'
        end
        object MHDLabel: TLabel
          Left = 296
          Top = 16
          Width = 60
          Height = 13
          Caption = 'MHDLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object ChargeLabel: TLabel
          Left = 296
          Top = 40
          Width = 72
          Height = 13
          Caption = 'ChargeLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object Label11: TLabel
          Left = 424
          Top = 16
          Width = 83
          Height = 13
          Caption = 'Aktuelle NVE-Nr.:'
        end
        object NVELabel: TLabel
          Left = 520
          Top = 16
          Width = 57
          Height = 13
          Caption = 'NVELabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object UpdateButton: TButton
          Left = 220
          Top = 241
          Width = 75
          Height = 25
          Anchors = [akLeft, akBottom]
          Caption = #220'bernehmen'
          TabOrder = 1
          OnClick = UpdateButtonClick
        end
        object ClearButton: TButton
          Left = 139
          Top = 241
          Width = 75
          Height = 25
          Anchors = [akLeft, akBottom]
          Caption = 'Verwerfen'
          TabOrder = 2
          OnClick = ClearButtonClick
        end
        object ErfasstStringGrid: TStringGridPro
          Left = 301
          Top = 71
          Width = 541
          Height = 194
          Anchors = [akLeft, akTop, akRight]
          ColCount = 7
          DefaultColWidth = 20
          DefaultRowHeight = 20
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = []
          Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
          ParentFont = False
          PopupMenu = ErfassenGridPopupMenu
          TabOrder = 4
          OnPostDrawCell = ErfasstStringGridDrawCell
          TitelTexte.Strings = (
            ''
            'Menge'
            'Gewicht'
            'MHD'
            'Charge'
            'NVE')
          TitelFont.Charset = DEFAULT_CHARSET
          TitelFont.Color = clWindowText
          TitelFont.Height = -11
          TitelFont.Name = 'Tahoma'
          TitelFont.Style = []
          ColWidths = (
            20
            48
            54
            68
            76
            116
            145)
        end
        object FehlButton: TButton
          Left = 6
          Top = 241
          Width = 95
          Height = 25
          Anchors = [akLeft, akBottom]
          Caption = 'Fehlmenge (F2)'
          TabOrder = 3
          OnClick = FehlButtonClick
        end
        object InputPageControl: TPageControl
          Left = 6
          Top = 71
          Width = 289
          Height = 164
          ActivePage = PickTabSheet
          TabOrder = 0
          object PickTabSheet: TTabSheet
            Caption = 'Pick'
            object Label3: TLabel
              Left = 2
              Top = 8
              Width = 47
              Height = 13
              Caption = 'Menge Ist'
            end
            object Label6: TLabel
              Left = 156
              Top = 8
              Width = 53
              Height = 13
              Caption = 'Gewicht Ist'
            end
            object Label12: TLabel
              Left = 260
              Top = 27
              Width = 12
              Height = 13
              Caption = 'kg'
            end
            object Bevel2: TBevel
              Left = 1
              Top = 56
              Width = 276
              Height = 9
              Shape = bsTopLine
            end
            object Label7: TLabel
              Left = 2
              Top = 64
              Width = 59
              Height = 13
              Caption = 'Neues MHD'
            end
            object Label10: TLabel
              Left = 156
              Top = 64
              Width = 34
              Height = 13
              Caption = 'Charge'
            end
            object MengeEdit: TEdit
              Left = 2
              Top = 24
              Width = 105
              Height = 21
              MaxLength = 8
              TabOrder = 0
              Text = '0'
              OnChange = MengeEditChange
              OnEnter = MengeEditEnter
              OnExit = MengeEditExit
              OnKeyPress = MengeEditKeyPress
            end
            object MengeUpDown: TIntegerUpDown
              Left = 107
              Top = 24
              Width = 16
              Height = 21
              Associate = MengeEdit
              Max = 99999
              TabOrder = 1
            end
            object GewichtEdit: TEdit
              Left = 156
              Top = 24
              Width = 100
              Height = 21
              TabOrder = 2
              Text = 'GewichtEdit'
              OnChange = GewichtEditChange
              OnEnter = GewichtEditEnter
              OnExit = GewichtEditExit
              OnKeyPress = GewichtEditKeyPress
            end
            object DatumEdit: TEdit
              Left = 2
              Top = 80
              Width = 121
              Height = 21
              TabOrder = 3
              Text = 'DatumEdit'
              OnChange = DataChange
              OnExit = DatumEditExit
              OnKeyPress = DatumEditKeyPress
            end
            object ChargeEdit: TEdit
              Left = 156
              Top = 80
              Width = 121
              Height = 21
              TabOrder = 4
              Text = 'ChargeEdit'
              OnChange = DataChange
              OnKeyPress = ChargeEditKeyPress
            end
            object NVEButton: TButton
              Left = 2
              Top = 107
              Width = 95
              Height = 25
              Caption = 'Neue NVE (F7)'
              TabOrder = 5
              OnClick = NVEButtonClick
            end
          end
          object PalTabSheet: TTabSheet
            Caption = 'Vollpalette'
            ImageIndex = 1
            TabVisible = False
            object Label13: TLabel
              Left = 2
              Top = 8
              Width = 30
              Height = 13
              Caption = 'LE-Nr.'
            end
            object LENrEdit: TEdit
              Left = 2
              Top = 24
              Width = 129
              Height = 21
              MaxLength = 32
              TabOrder = 0
              Text = 'LENrEdit'
              OnChange = DataChange
            end
          end
        end
      end
      object AbortButton: TButton
        Left = 773
        Top = 319
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Cancel = True
        Caption = 'Abbrechen'
        ModalResult = 3
        TabOrder = 2
      end
      object OkButton: TButton
        Left = 680
        Top = 319
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'Ok'
        ModalResult = 1
        TabOrder = 3
      end
      object FehlerLabel: TPanel
        Left = 0
        Top = 0
        Width = 848
        Height = 24
        Align = alTop
        BevelOuter = bvNone
        Caption = 'FehlerLabel'
        Color = clRed
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentBackground = False
        ParentFont = False
        TabOrder = 0
      end
    end
    object KommPosDBGrid: TDBGridPro
      Left = 0
      Top = 57
      Width = 848
      Height = 207
      Align = alClient
      DataSource = KommPosDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
      PopupMenu = KommPosDBGridPopupMenu
      ReadOnly = True
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      OnDrawColumnCell = KommPosDBGridDrawColumnCell
      OnDblClick = KommPosDBGridDblClick
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'MS Sans Serif'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
  end
  object KommPosDataSource: TDataSource
    DataSet = KommPosDataSet
    OnDataChange = KommPosDataSourceDataChange
    Left = 272
    Top = 72
  end
  object KommPosDataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 312
    Top = 72
  end
  object ErfassenGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = ErfassenGridPopupMenuPopup
    Left = 480
    Top = 400
    object ErfasstColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      ImageIndex = 27
      OnClick = StringGridColOptimalMenuItemClick
    end
    object ErfasstCopyColMenuItem: TMenuItem
      Caption = 'Kopieren'
      ImageIndex = 26
      OnClick = StringGridCopyColMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object ErfassenDelMenuItem: TMenuItem
      Caption = 'L'#246'schen'
      ImageIndex = 23
      OnClick = ErfassenDelMenuItemClick
    end
  end
  object KommPosDBGridPopupMenu: TPopupMenu
    OnPopup = KommPosDBGridPopupMenuPopup
    Left = 352
    Top = 72
    object KommPosErsatzMenuItem: TMenuItem
      Caption = 'Ersatzartikel buchen....'
      OnClick = KommPosErsatzMenuItemClick
    end
  end
end
