object WALeergutErfassenForm: TWALeergutErfassenForm
  Left = 383
  Top = 161
  BorderIcons = [biSystemMenu]
  BorderStyle = bsDialog
  Caption = 'WALeergutErfassenForm'
  ClientHeight = 550
  ClientWidth = 397
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  TextHeight = 13
  object InfoPanel: TPanel
    Left = 0
    Top = 137
    Width = 397
    Height = 97
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      397
      97)
    object Label1: TLabel
      Left = 8
      Top = 24
      Width = 54
      Height = 13
      Caption = 'Auftrag-Nr.:'
    end
    object Label2: TLabel
      Left = 8
      Top = 66
      Width = 82
      Height = 13
      Caption = 'Ladungstr'#228'ger'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LabelAnzahl: TLabel
      Left = 130
      Top = 66
      Width = 39
      Height = 13
      Caption = 'Anzahl'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Label5: TLabel
      Left = 8
      Top = 8
      Width = 34
      Height = 13
      Caption = 'Kunde:'
    end
    object HinweisLabel: TLabel
      Left = 8
      Top = 45
      Width = 381
      Height = 16
      Alignment = taCenter
      Anchors = [akLeft, akTop, akRight]
      AutoSize = False
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      ExplicitWidth = 342
    end
    object Bevel2: TBevel
      Left = 8
      Top = 92
      Width = 384
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 345
    end
    object KundeLabel: TLabel
      Left = 72
      Top = 8
      Width = 57
      Height = 13
      Caption = 'KundeLabel'
    end
    object AuftragNrLabel: TLabel
      Left = 72
      Top = 24
      Width = 71
      Height = 13
      Caption = 'AuftragNrLabel'
    end
    object Label4: TLabel
      Left = 265
      Top = 56
      Width = 85
      Height = 13
      Caption = 'Gesamt Anzahl'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LagerAnzLabel: TLabel
      Left = 304
      Top = 73
      Width = 27
      Height = 13
      Caption = 'Lager'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
    end
    object AvisAnzLabel: TLabel
      Left = 354
      Top = 73
      Width = 20
      Height = 13
      Caption = 'Avis'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
    end
    object AuftragAnzLabel: TLabel
      Left = 258
      Top = 73
      Width = 34
      Height = 13
      Caption = 'Auftrag'
    end
    object TauschLabel: TLabel
      Left = 186
      Top = 66
      Width = 43
      Height = 13
      Caption = 'Tausch'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object FussPanel: TPanel
    Left = 0
    Top = 500
    Width = 397
    Height = 50
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      397
      50)
    object Bevel1: TBevel
      Left = 8
      Top = 4
      Width = 384
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 345
    end
    object OkButton: TButton
      Left = 227
      Top = 14
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Ok'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
    object AbortButton: TButton
      Left = 315
      Top = 14
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
    object CloseButton: TButton
      Left = 16
      Top = 16
      Width = 75
      Height = 25
      Caption = 'Schlie'#223'en'
      ModalResult = 2
      TabOrder = 2
      Visible = False
    end
  end
  object LTScrollBox: TScrollBox
    Left = 0
    Top = 234
    Width = 397
    Height = 266
    Align = alClient
    BevelInner = bvNone
    BevelOuter = bvNone
    BorderStyle = bsNone
    TabOrder = 2
  end
  object ReturnPanel: TPanel
    Left = 0
    Top = 0
    Width = 397
    Height = 137
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      397
      137)
    object Label6: TLabel
      Left = 8
      Top = 8
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Label7: TLabel
      Left = 8
      Top = 48
      Width = 103
      Height = 13
      Caption = 'R'#252'ckgabelieferschein'
    end
    object Label8: TLabel
      Left = 8
      Top = 88
      Width = 79
      Height = 13
      Caption = 'R'#252'ckgabedatum'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 132
      Width = 384
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 384
      Height = 21
      TabOrder = 0
      Text = 'LagerComboBox'
    end
    object LSNrEdit: TEdit
      Left = 8
      Top = 64
      Width = 381
      Height = 21
      TabOrder = 1
      Text = 'LSNrEdit'
    end
    object ReturnDateTimePicker: TDateTimePicker
      Left = 8
      Top = 104
      Width = 83
      Height = 21
      Date = 42105.000000000000000000
      Time = 0.730312997693545200
      TabOrder = 2
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 264
    Top = 16
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 304
    Top = 16
  end
end
