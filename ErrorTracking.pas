﻿{$WARN UNIT_PLATFORM OFF}
{$WARN SYMBOL_PLATFORM OFF}

unit ErrorTracking;

interface

uses
  SysUtils, Classes, madExceptVcl;

type
  TErrorTrackingModule = class(TDataModule)
    MadExceptionHandler1: TMadExceptionHandler;
    procedure MadExceptionHandler1Exception(const exceptIntf: IMEException; var handled: Boolean);
    procedure DataModuleCreate(Sender: TObject);
  private
    fLogErrorFile : String;
  public
    property ErrorLogFile : String read fLogErrorFile write fLogErrorFile;

    procedure Setup (const ProjectName, ErrorPath, MailServer, ErrorMail : String);

    procedure WriteErrorLog     (const HeaderText : String; Stream : TStream); overload;
    procedure WriteErrorLog     (const HeaderText, ErrorText : String); overload;
    
    procedure WriteErrorLogNoDB (const HeaderText, ErrorText : String);

    procedure WriteErrorText (const ErrorText : String);
  end;

var
  ErrorTrackingModule: TErrorTrackingModule;

implementation

{$R *.dfm}

uses madExcept, Windows, DatenModul, ConfigModul;

const
  CRLF = #13+#10;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TErrorTrackingModule.DataModuleCreate(Sender: TObject);
begin
  fLogErrorFile := '';
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TErrorTrackingModule.MadExceptionHandler1Exception(const exceptIntf: IMEException; var handled: Boolean);
begin
  WriteErrorLog ('Execption', exceptIntf.ExceptClass + ' > ' + exceptIntf.ExceptMessage);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TErrorTrackingModule.Setup (const ProjectName, ErrorPath, MailServer, ErrorMail : String);
begin
  if Assigned (LVSConfigModul) and (Length (LVSConfigModul.LeitstandName) > 0) then
    madExcept.MESettings().BugReportFile := IncludeTrailingBackslash (ErrorPath) + 'bugreport_'+LVSConfigModul.LeitstandName+'.txt'
  else
    madExcept.MESettings().BugReportFile := IncludeTrailingBackslash (ErrorPath) + 'bugreport.txt';

  if (Length (MailServer) > 0) then
    madExcept.MESettings().SmtpServer := MailServer;

  if (Length (ErrorMail) > 0) then
    madExcept.MESettings().MailAddr := ErrorMail;

  madExcept.MESettings().MailSubject := 'Frontend Bugreport: '+ProjectName;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TErrorTrackingModule.WriteErrorLog (const HeaderText : String; Stream : TStream);
var
  timeout    : DWORD;
  filestream : TFileStream;
  outstr     : AnsiString;
  buffer     : Pointer;
  i          : Integer;
begin
  if (Length (fLogErrorFile) > 0) then begin
    timeout := GetTickCount + 5000;

    if not (FileExists (fLogErrorFile)) then begin
      try
        filestream := TFileStream.Create (fLogErrorFile, fmCreate);
      except
        filestream := Nil;
      end;

      if Assigned (filestream) then
        filestream.Free;
    end;

    repeat
      try
        filestream := TFileStream.Create (fLogErrorFile, fmOpenReadWrite + fmShareDenyWrite);
      except
        filestream := Nil;
      end;

      if (GetTickCount < timeout) and (filestream = Nil) then
        Sleep (200);
    until (GetTickCount > timeout) or (filestream <> Nil);

    if Assigned (filestream) then begin
      filestream.Seek (0, soFromEnd);

      outstr := HeaderText;
      outstr := outstr + CRLF + 'Datum : ' + DateToStr (Now);
      outstr := outstr + CRLF + 'Zeit  : ' + TimeToStr (Now);
      outstr := outstr + CRLF;

      filestream.Write (outstr [1], Length (outstr));


      if Assigned (Stream) and (Stream.Size > 0) then begin
        GetMem (buffer, Stream.Size);

        Stream.Position := 0;
        Stream.ReadBuffer (buffer^, Stream.Size);

        filestream.Write(buffer^, Stream.Size);

        FreeMem (buffer);
      end;

      {$ifdef UseODAC}
      {$else}
        with LVSDatenModul.MainADOConnection do begin
          if (Errors.Count > 1) then begin
            for i:= 0 to Errors.Count - 1 do begin
              outstr := Format('Source: %s', [Errors[i].Source]) + CRLF;
              outstr := outstr + Format('NativeError: %d', [Errors[i].NativeError]) + CRLF;
              outstr := outstr + Format('%s; (SQLState: %s)', [Errors[i].Description, Errors[i].SQLState]) + CRLF;

              filestream.Write (outstr [1], Length (outstr));
            end;
          end;
        end;
      {$endif}

      filestream.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TErrorTrackingModule.WriteErrorLog (const HeaderText, ErrorText : String);
var
  timeout    : DWORD;
  outstr     : AnsiString;
  filestream : TFileStream;
  i          : Integer;
begin
  if (Length (fLogErrorFile) > 0) then begin
    timeout := GetTickCount + 5000;

    if not (FileExists (fLogErrorFile)) then begin
      try
        filestream := TFileStream.Create (fLogErrorFile, fmCreate);
      except
        filestream := Nil;
      end;

      if Assigned (filestream) then
        filestream.Free;
    end;

    repeat
      try
        filestream := TFileStream.Create (fLogErrorFile, fmOpenReadWrite + fmShareDenyWrite);
      except
        filestream := Nil;
      end;

      if (GetTickCount < timeout) and (filestream = Nil) then
        Sleep (200);
    until (GetTickCount > timeout) or (filestream <> Nil);

    if Assigned (filestream) then begin
      filestream.Seek (0, soFromEnd);

      outstr := HeaderText;
      outstr := outstr + CRLF + 'Datum : ' + DateToStr (Now);
      outstr := outstr + CRLF + 'Zeit  : ' + TimeToStr (Now);
      outstr := outstr + CRLF;
      outstr := outstr + CRLF + 'Schema:        ' + LVSDatenModul.Schema;
      outstr := outstr + CRLF + 'User:          ' + LVSDatenModul.AktUser;
      outstr := outstr + CRLF + 'Mandant:       ' + LVSDatenModul.AktMandant;
      outstr := outstr + CRLF + 'Niederlassung: ' + LVSDatenModul.AktLocation;
      outstr := outstr + CRLF + 'Lager:         ' + LVSDatenModul.AktLager;
      outstr := outstr + CRLF + 'Leitstand:     ' + LVSConfigModul.LeitstandName;
      outstr := outstr + CRLF + 'Host:          ' + LVSDatenModul.AktHost;
      outstr := outstr + CRLF + 'Client:        ' + LVSDatenModul.AktClientName;
      outstr := outstr + CRLF + 'Version:       ' + LVSDatenModul.AktVersion;
      outstr := outstr + CRLF + '--------------------------------------' + CRLF + CRLF;
      outstr := outstr + ErrorText + CRLF;

      filestream.Write(outstr[1], Length (outstr));

      {$ifdef UseODAC}
      {$else}
        with LVSDatenModul.MainADOConnection do begin
          for i:= 0 to Errors.Count - 1 do begin
            outstr := Format('Source: %s', [Errors[i].Source]) + CRLF;
            outstr := outstr + Format('NativeError: %d', [Errors[i].NativeError]) + CRLF;
            outstr := outstr + Format('%s; (SQLState: %s)', [Errors[i].Description, Errors[i].SQLState]) + CRLF;
            outstr := outstr + Format('%d; %s', [Errors[i].Number, Errors[i].Source]) + CRLF;

            filestream.Write (outstr [1], Length (outstr));
          end;
        end;
      {$endif}

      filestream.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TErrorTrackingModule.WriteErrorLogNoDB (const HeaderText, ErrorText : String);
var
  timeout    : DWORD;
  outstr     : AnsiString;
  filestream : TFileStream;
begin
  if (Length (fLogErrorFile) > 0) then begin
    timeout := GetTickCount + 5000;

    if not (FileExists (fLogErrorFile)) then begin
      try
        filestream := TFileStream.Create (fLogErrorFile, fmCreate);
      except
        filestream := Nil;
      end;

      if Assigned (filestream) then
        filestream.Free;
    end;

    repeat
      try
        filestream := TFileStream.Create (fLogErrorFile, fmOpenReadWrite + fmShareDenyWrite);
      except
        filestream := Nil;
      end;

      if (GetTickCount < timeout) and (filestream = Nil) then
        Sleep (200);
    until (GetTickCount > timeout) or (filestream <> Nil);

    if Assigned (filestream) then begin
      filestream.Seek (0, soFromEnd);

      outstr := HeaderText;
      outstr := outstr + CRLF + 'Datum : ' + DateToStr (Now);
      outstr := outstr + CRLF + 'Zeit  : ' + TimeToStr (Now);
      outstr := outstr + CRLF;
      outstr := outstr + CRLF + 'Schema:        ' + LVSDatenModul.Schema;
      outstr := outstr + CRLF + 'User:          ' + LVSDatenModul.AktUser;
      outstr := outstr + CRLF + 'Mandant:       ' + LVSDatenModul.AktMandant;
      outstr := outstr + CRLF + 'Niederlassung: ' + LVSDatenModul.AktLocation;
      outstr := outstr + CRLF + 'Lager:         ' + LVSDatenModul.AktLager;
      outstr := outstr + CRLF + 'Leitstand:     ' + LVSConfigModul.LeitstandName;
      outstr := outstr + CRLF + 'Host:          ' + LVSDatenModul.AktHost;
      outstr := outstr + CRLF + 'Client:        ' + LVSDatenModul.AktClientName;
      outstr := outstr + CRLF + 'Version:       ' + LVSDatenModul.AktVersion;
      outstr := outstr + CRLF + '--------------------------------------' + CRLF + CRLF;
      outstr := outstr + ErrorText + CRLF;

      filestream.Write(outstr[1], Length (outstr));

      filestream.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TErrorTrackingModule.WriteErrorText (const ErrorText : String);
var
  timeout    : DWORD;
  outstr     : AnsiString;
  filestream : TFileStream;
begin
  if (Length (fLogErrorFile) > 0) then begin
    timeout := GetTickCount + 5000;

    if not (FileExists (fLogErrorFile)) then begin
      try
        filestream := TFileStream.Create (fLogErrorFile, fmCreate);
      except
        filestream := Nil;
      end;

      if Assigned (filestream) then
        filestream.Free;
    end;

    repeat
      try
        filestream := TFileStream.Create (fLogErrorFile, fmOpenReadWrite + fmShareDenyWrite);
      except
        filestream := Nil;
      end;

      if (GetTickCount < timeout) and (filestream = Nil) then
        Sleep (200);
    until (GetTickCount > timeout) or (filestream <> Nil);

    if Assigned (filestream) then begin
      filestream.Seek (0, soFromEnd);

      outstr := DateTimeToStr (Now) + ' : ' + ErrorText + CRLF;

      filestream.Write(outstr [1], Length (outstr));
      filestream.Free;
    end;
  end;
end;

end.
