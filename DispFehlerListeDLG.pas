unit DispFehlerListeDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, Grids, DBGrids, SMDBGrid, DBGridPro, StdCtrls,
  ExtCtrls;

type
  TDispFehlerListeForm = class(TForm)
    Panel1: TPanel;
    Button1: TButton;
    DBGridPro1: TDBGridPro;
    DataSource1: TDataSource;
    FehlerQuery: TADOQuery;
    Panel2: TPanel;
    FehlerStatusLabel: TLabel;
    FehlerTextLabel: TLabel;
    Panel3: TPanel;
    Panel4: TPanel;
    CheckZeitLabel: TLabel;
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses DatenModul, ConfigModul, DBGridUtilModule, SprachModul;

procedure TDispFehlerListeForm.FormShow(Sender: TObject);
begin
  LVSConfigModul.RestoreFormInfo (Self);

  if (Length (FehlerQuery.SQL.Text) > 0) Then begin
    try
      FehlerQuery.Open;

      DBGridPro1.SetColumnVisible ('POS_NR', False);
    except
    end;
  end;
end;

procedure TDispFehlerListeForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, FehlerStatusLabel);
    LVSSprachModul.SetNoTranslate (Self, FehlerTextLabel);
    LVSSprachModul.SetNoTranslate (Self, CheckZeitLabel);
  {$endif}
end;

procedure TDispFehlerListeForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  FehlerQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
end;

end.
