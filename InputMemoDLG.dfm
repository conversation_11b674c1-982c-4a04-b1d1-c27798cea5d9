object InputMemoForm: TInputMemoForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Weiter Infos'
  ClientHeight = 292
  ClientWidth = 562
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    562
    292)
  TextHeight = 13
  object InputLabel: TLabel
    Left = 8
    Top = 8
    Width = 51
    Height = 13
    Caption = 'InputLabel'
  end
  object erfassteSeriennummernLabel: TLabel
    Left = 8
    Top = 45
    Width = 139
    Height = 13
    Caption = 'erfassteSeriennummernLabel'
  end
  object InputMemoLabel: TLabel
    Left = 290
    Top = 45
    Width = 79
    Height = 13
    Caption = 'InputMemoLabel'
  end
  object OkButton: TButton
    Left = 400
    Top = 259
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Hinzuf'#252'gen'
    Default = True
    ModalResult = 1
    TabOrder = 1
  end
  object AbortButton: TButton
    Left = 481
    Top = 259
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    ModalResult = 3
    TabOrder = 2
  end
  object InputMemo: TMemo
    Left = 290
    Top = 64
    Width = 257
    Height = 161
    Anchors = [akLeft, akTop, akRight]
    Lines.Strings = (
      'InputMemo')
    TabOrder = 0
  end
  object SNStringGridPro: TStringGridPro
    Left = 8
    Top = 64
    Width = 257
    Height = 161
    ColCount = 2
    DefaultColWidth = 20
    RowCount = 2
    Options = [goFixedVertLine, goFixedHorzLine, goRangeSelect, goFixedRowDefAlign]
    PopupMenu = PopupMenu1
    TabOrder = 3
    Visible = False
    TitelTexte.Strings = (
      ''
      'Seriennummern')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -12
    TitelFont.Name = 'Segoe UI'
    TitelFont.Style = []
  end
  object PopupMenu1: TPopupMenu
    Left = 232
    Top = 8
    object Lschen1: TMenuItem
      Caption = 'L'#246'schen'
      OnClick = Lschen1Click
    end
  end
end
