unit SelectSetPositionDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComCtrls, ExtCtrls;

type
  TSelectSetPositionForm = class(TForm)
    KopfPanel: TPanel;
    FussPanel: TPanel;
    DatenPanel: TPanel;
    Label1: TLabel;
    ArNrEdit: TEdit;
    ArtikelListView: TListView;
    Label2: TLabel;
    ArTextEdit: TEdit;
    AbortButton: TButton;
    OkButton: TButton;
    MengeEdit: TEdit;
    Label3: TLabel;
    HinweisMemo: TMemo;
    Label4: TLabel;
    BruchProzentEdit: TEdit;
    Label5: TLabel;
    ProdPartCheckBox: TCheckBox;
    procedure EditExit(Sender: TObject);
    procedure EditKeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormShow(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure ArtikelListViewCompare(Sender: TObject; Item1, Item2: TListItem;
      Data: Integer; var Compare: Integer);
    procedure ArtikelListViewColumnClick(Sender: TObject; Column: TListColumn);
  private
    fRefMand    : Integer;
    fRefSubMand : Integer;
    fRefAr      : Integer;
    fRefSet     : Integer;
    fRefSetPos  : Integer;

    fIndexToSort : Integer;
    fLastToSort  : Integer;
    fSortDir     : Integer;

    procedure SetColumnImage(List: TListView; Column, Image: Integer;  ShowImage: Boolean);

    function UpdateArtikeListe (Sender: TObject) : Integer;
  public
    property RefSetPos : Integer read fRefSetPos;

    procedure Prepare (const RefSet : Integer);
  end;

implementation

{$R *.dfm}

uses
  CommCtrl, VCLUtilitys, DB, ADODB, FrontEndUtils, DatenModul, ConfigModul, LVSArtikelInterface,
  SprachModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectSetPositionForm.EditKeyPress(Sender: TObject; var Key: Char);
begin
  if (Key = #13) then
    UpdateArtikeListe (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectSetPositionForm.FormClose(Sender: TObject; var Action: TCloseAction);
var
  idx  : Integer;
  line : String;
begin
  if Assigned (LVSConfigModul) then begin
    for idx := 0 to ArtikelListView.Columns.Count - 1 do begin
      if (idx = 0) then
        line := IntToStr (ArtikelListView.Columns [idx].Width)
      else
        line := line + ';' + IntToStr (ArtikelListView.Columns [idx].Width);
    end;

    LVSConfigModul.SaveFormParameter (Self, 'ArtikelListView', line);

    LVSConfigModul.SaveFormInfo (Self);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.04.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectSetPositionForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  refpos   : Integer;
  menge,
  prozent  : Double;
  optstr   : String;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    prozent := 0.0;

    if not (Assigned (ArtikelListView.Selected) and Assigned (ArtikelListView.Selected.Data)) then begin
      CanClose := False;
      ArtikelListView.SetFocus;
    end else if not TryStrToFloat (MengeEdit.Text, menge) then begin
      CanClose := False;
      MengeEdit.SetFocus;
    end else if (Length (BruchProzentEdit.Text) > 0) and not TryStrToFloat (BruchProzentEdit.Text, prozent) then begin
      CanClose := False;
      BruchProzentEdit.SetFocus;
    end else begin
      optstr := '';

      if (ProdPartCheckBox.Checked) then
        optstr := optstr + 'PROD;';

      res := InsertArtikelSetPosition (fRefSet, TComboBoxRef (ArtikelListView.Selected.Data).Ref, menge, prozent, HinweisMemo.Text, optstr, refpos);

      if (res = 0) then begin
        fRefSetPos := refpos;

        CanClose := True;
      end else begin
        CanClose := False;
        MessageDLG ('Fehler beim Anlegen einer neuen Setposition'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectSetPositionForm.FormCreate(Sender: TObject);
var
  i,
  iwert  : Integer;
  colstr : String;
  slist  : TStringList;
begin
  fRefSet    := -1;
  fRefSetPos := -1;

  ArNrEdit.Text         := '';
  ArTextEdit.Text       := '';
  MengeEdit.Text        := '';
  BruchProzentEdit.Text := '';
  HinweisMemo.Text      := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
  {$endif}

  slist := TStringList.Create;

  try
    slist.Delimiter := ';';

    LVSConfigModul.ReadFormParameter (Self, 'ArtikelListView', colstr);

    if (Length (colstr) > 0) then begin
      slist.DelimitedText := colstr;

      for i := 0 to slist.Count - 1 do begin
        if TryStrToInt (slist [i], iwert) then begin
          if (i < ArtikelListView.Columns.Count) then
            ArtikelListView.Columns [i].Width := iwert;
        end;
      end;
    end;
  finally
    slist.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectSetPositionForm.FormShow(Sender: TObject);
begin
  if ArNrEdit.CanFocus then ArNrEdit.SetFocus;

  if (Length (ArNrEdit.Text) > 0) then
    UpdateArtikeListe (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectSetPositionForm.SetColumnImage(List: TListView; Column, Image: Integer;  ShowImage: Boolean);
var
  Align,hHeader: integer;
  HD: HD_ITEM;
begin
  hHeader := SendMessage(List.Handle, LVM_GETHEADER, 0, 0);

  with HD do begin
    FillChar(HD, SizeOf(HD), 0);

    Mask := HDI_FORMAT;

    pszText := PChar(List.Columns[Column].Caption);

    case List.Columns[Column].Alignment of
      taLeftJustify: Align := HDF_LEFT;
      taCenter: Align := HDF_CENTER;
      taRightJustify: Align := HDF_RIGHT;
    else
      Align := HDF_LEFT;
    end;

    if not ShowImage then
      fmt := HDF_STRING or Align
    else if fSortDir = 0 then
      fmt := HDF_STRING or Align or HDF_SORTUP
    else
      fmt := HDF_STRING or Align or HDF_SORTDOWN;
  end;

  SendMessage(hHeader, HDM_SETITEM, Column, Integer(@HD));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectSetPositionForm.ArtikelListViewColumnClick(Sender: TObject;  Column: TListColumn);
var
  i : Integer;
begin
  fIndexToSort := Column.Index;
  if fIndexToSort = fLastToSort then
    fSortDir := 1 - fSortDir
  else
    fSortDir := 0;
  fLastToSort := fIndexToSort;
  (Sender as TCustomListView).AlphaSort;

  for i := 0 to (Sender as TListView).Columns.Count - 1 do
    SetColumnImage((Sender as TListView), (Sender as TListView).Columns [i].Index, 0, (Sender as TListView).Columns [i].Index = fIndexToSort);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectSetPositionForm.ArtikelListViewCompare(Sender: TObject; Item1, Item2: TListItem; Data: Integer; var Compare: Integer);
var
  TempStr, TextToSort1, TextToSort2: String;
begin
  //Texte zuweisen
  if fIndexToSort = 0 then
  begin
    TextToSort1 := Item1.Caption;
    TextToSort2 := Item2.Caption;
  end //if ColumnToSort = 0 then
  else
  begin
    TextToSort1 := Item1.SubItems[fIndexToSort - 1];
    TextToSort2 := Item2.SubItems[fIndexToSort - 1];
  end; //if ColumnToSort <> 0 then

//Je nach Sortierrichtung evtl. Texte vertauschen
  if fSortDir <> 0 then
  begin
    TempStr := TextToSort1;
    TextToSort1 := TextToSort2;
    TextToSort2 := TempStr;
  end; //if SortDir <> 0 then

  //Texte je nach Tag der Spalte unterschiedlich vergleichen
  case fIndexToSort of
  //Integer-Werte
    99: begin
        Compare := StrToIntDef(TextToSort1,0)-StrToIntDef(TextToSort2,0);
       end;
    98 : Compare := Round (StrToFloatDef(TextToSort1,0)-StrToFloatDef(TextToSort2,0));
    //DateTime-Werte
    97: begin
        Compare := 0;
        if StrToDateTime(TextToSort1) > StrToDateTime(TextToSort2) then
          Compare := Trunc(StrToDateTime(TextToSort1)-StrToDateTime(TextToSort2))+1;
        if StrToDateTime(TextToSort1) < StrToDateTime(TextToSort2) then
          Compare := Trunc(StrToDateTime(TextToSort1)-StrToDateTime(TextToSort2))-1;
       end; //3
    //Alles andere sind Strings
    else
      Compare := CompareText(TextToSort1,TextToSort2);
  end; //case (Sender as TListView).Columns[ColumnToSort].Tag of
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectSetPositionForm.EditExit(Sender: TObject);
begin
  UpdateArtikeListe (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSelectSetPositionForm.Prepare (const RefSet : Integer);
var
  query : TADOQuery;
begin
  fRefSet := RefSet;

  query := TADOQuery.Create (Self);

  try
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select REF_MAND, REF_SUB_MAND from V_ARTIKEL_SET where REF='+IntToStr (fRefSet));

    query.Open;

    fRefMand    := query.Fields [0].AsInteger;
    fRefSubMand := DBGetReferenz (query.Fields [1]);

    query.Close;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TSelectSetPositionForm.UpdateArtikeListe (Sender: TObject) : Integer;
var
  i,
  res,
  ref,
  selidx : Integer;
  item   : TListItem;
  numstr : String;
  query  : TADOQuery;
begin
  res := 0;
  ref := -1;
  selidx := -1;

  if Assigned (ArtikelListView.Selected) and Assigned (ArtikelListView.Selected.Data) then
    ref := TComboBoxRef (ArtikelListView.Selected.Data).Ref;


  query := TADOQuery.Create (Self);

  try
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select ae.REF, m.NAME as MANDANT, a.ARTIKEL_NR, a.ARTIKEL_TEXT, ae.EINHEIT, ae.NETTO_GEWICHT, ae.PREIS'
                  +' from V_ARTIKEL a, V_ARTIKEL_EINHEIT ae, V_MANDANT m'
                  +' where ae.REF_AR=a.REF and m.REF=nvl (a.REF_SUB_MAND,a.REF_MAND) and a.STATUS=''AKT'' and ae.STATUS=''AKT'''
                  +' and (a.REF_ARTIKEL_SET is null or a.REF_ARTIKEL_SET in (select REF from V_ARTIKEL_SET where OPT_PRODUCTION_SET=''1''))'
                  );

    if (fRefSubMand > 0) then begin
      query.SQL.Add ('and (a.REF_SUB_MAND=:ref_sub_mand or a.REF_SUB_MAND in (select REF_BES_MAND from V_MANDANT_REL_BES_MAND where REF_MAND=:ref_bes_mand))');
      query.Parameters.ParamByName ('ref_sub_mand').Value := fRefSubMand;
      query.Parameters.ParamByName ('ref_bes_mand').Value := fRefSubMand;
    end else begin
      query.SQL.Add ('and (a.REF_MAND=:ref_mand or a.REF_MAND in (select REF_BES_MAND from V_MANDANT_REL_BES_MAND where REF_MAND=:ref_bes_mand))');
      query.Parameters.ParamByName ('ref_mand').Value := fRefMand;
      query.Parameters.ParamByName ('ref_bes_mand').Value := fRefMand;
    end;

    if (Length (ArNrEdit.Text) > 0) then
      query.SQL.Add ('and a.ARTIKEL_NR like '+#39+ArNrEdit.Text+'%'+#39);

    if (Length (ArTextEdit.Text) > 0) then
      query.SQL.Add ('and a.ARTIKEL_TEXT like '+#39+ArTextEdit.Text+'%'+#39);

    query.SQL.Add ('order by MANDANT, lpad (ARTIKEL_NR, 32, '' '')');

    ArtikelListView.Clear;

    Screen.Cursor := crSQLWait;
    ArtikelListView.Items.BeginUpdate;

    try
      query.Open;

      while not (query.Eof) do begin
        item := ArtikelListView.Items.Add;
        item.Caption := query.FieldByName ('MANDANT').AsString;
        item.Data := TComboBoxRef.Create(query.FieldByName ('REF').AsInteger);

        if (Ref > 0) and (Ref = query.FieldByName ('REF').AsInteger) then
          selidx := item.Index;

        item.SubItems.Add (query.FieldByName ('ARTIKEL_NR').AsString);
        item.SubItems.Add (query.FieldByName ('ARTIKEL_TEXT').AsString);
        item.SubItems.Add (query.FieldByName ('EINHEIT').AsString);

        if (query.FieldByName ('NETTO_GEWICHT').IsNull) then
          numstr := ''
        else numstr := Format ('%0.3f', [query.FieldByName ('NETTO_GEWICHT').AsInteger / 1000]);
        item.SubItems.Add (numstr);

        if (query.FieldByName ('PREIS').IsNull) then
          numstr := ''
        else numstr := Format ('%0.3f', [query.FieldByName ('PREIS').AsInteger / 1000]);
        item.SubItems.Add (numstr);

        query.Next;
      end;

      query.Close;

      if (ArtikelListView.Items.Count > 0) then begin
        if (selidx = -1) then
          ArtikelListView.ItemIndex := 0
        else if (selidx < ArtikelListView.Items.Count) then
          ArtikelListView.ItemIndex := selidx;
      end;

      ArtikelListView.AlphaSort;

      for i := 0 to ArtikelListView.Columns.Count - 1 do
        SetColumnImage(ArtikelListView, ArtikelListView.Columns [i].Index, 0, ArtikelListView.Columns [i].Index = fIndexToSort);
    finally
      ArtikelListView.Items.EndUpdate;

      Screen.Cursor := crDefault;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

end.
