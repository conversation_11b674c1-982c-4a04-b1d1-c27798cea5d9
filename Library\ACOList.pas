﻿//******************************************************************************
//* Modul Name: ACOList
//* Author    : <PERSON>
//******************************************************************************
//* $Revision: 10 $
//* $Date: 10.11.16 13:37 $
//******************************************************************************
//* Description: Komponenten zum Ver<PERSON>ten von AccessControls von Formularen
//******************************************************************************
unit ACOList;

interface

uses
  SysUtils, Classes, Forms, ExtCtrls;

type
  TItemType = (tlCaption, tlHint, tlItem, tlMenuItem, tlFixtext);

  TACOListEntry = class (TPersistent)
    protected
      procedure AssignTo (Source : TACOListEntry);

    public
      ACOVersion : Integer;

      ACORef   : Integer;
      FormName : String;
      CompType : String;
      CompName : String;
      ACOType  : String;
      ACOObject: String;
      ACOName  : String;
      ACOText  : String;
      ACOID    : Integer;
      ACOGroup : String;
      ComponentPath : String;

      constructor Create;
  end;

  TACOList=class (TObject)
    private
      FItems:TList;
      function GetCount:integer;
      function GetItem(aIndex:integer):TACOListEntry;
      procedure SetItem(aIndex:integer;aItem:TACOListEntry);
    public
      constructor Create;
      destructor Destroy; override;

      procedure Add (aItem:TACOListEntry); overload;
      procedure Delete (aIndex:integer);
      procedure Clear;

      function FindACO   (const FormName, CompName : String) : TACOListEntry; overload;
      function FindACO   (const ACOName : String) : TACOListEntry; overload;
      function FindACO   (const ACOID : Integer) : TACOListEntry; overload;

      function FindACOIndex (const FormName, CompName : String) : Integer; overload;
      function FindACOIndex (const ACOID : Integer) : Integer; overload;

      property Items[aIndex:integer]:TACOListEntry read GetItem write SetItem; default;
      property Count:integer read GetCount;
  end;


  TACOListManager = class(TComponent)
  private
      fACOList       : TACOList;
      fFormList      : TList;

      procedure LoadCompProperty (Reader: TReader);
      procedure StoreCompProperty (Writer: TWriter);
      function  DoWrite(Filer: TFiler): Boolean;
  protected
      procedure DefineProperties (Filer: TFiler); override;
  public
    property FormList : TList    read fFormList write fFormList;
    property ACOList  : TACOList read fACOList  write fACOList stored true;
  published
    constructor Create(AOwner: TComponent); override;
    destructor  Destroy; override;

    procedure   Loaded; override;
    procedure   Editor;
  end;

  TACOListForm = class(TComponent)
  private
    fMaster : TACOListManager;

    procedure SetMaster (Master : TACOListManager);
  public
    OwnerForm : TForm;
  published
    constructor Create(AOwner: TComponent); override;
    destructor  Destroy; override;

    procedure   Loaded; override;
    procedure   Editor;

    property Master : TACOListManager read fMaster write SetMaster;
  end;

type
   eTransCompError = class(Exception)
     ErrorNo : Integer;
     constructor Create(const nNo: Integer; const sMsg: string);
   end;

procedure Register;

implementation

uses
{$ifdef TRACE}
  Trace,
{$endif}
  Windows,
  Menus, Controls, StdCtrls, ComCtrls, ACOListEdit;

procedure Register;
begin
  RegisterComponents('c+s', [TACOListManager]);
  RegisterComponents('c+s', [TACOListForm]);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor eTransCompError.Create(const nNo: Integer; const sMsg: string);
var
  s, sNo : string;
begin
  Str(nNo, sNo);
  s := 'Error:' + sNo + ' ' + sMsg;
  inherited Create(s);
  ErrorNo := nNo;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TACOListEntry.Create;
begin
  inherited Create;

  ACOVersion := 2;
  ACORef   := -1;
  FormName := '';
  CompType := '';
  CompName := '';
  ACOType  := '';
  ACOObject:= '';
  ACOName  := '';
  ACOText  := '';
  ACOID    := -1;
  ACOGroup := '';
  ComponentPath := '';
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListEntry.AssignTo (Source : TACOListEntry);
begin
  ACOVersion := Source.ACOVersion;
  ACORef   := Source.ACORef;
  FormName := Source.FormName;
  CompType := Source.CompType;
  CompName := Source.CompName;
  ACOType  := Source.ACOType;
  ACOObject:= Source.ACOObject;
  ACOName  := Source.ACOName;
  ACOText  := Source.ACOText;
  ACOID    := Source.ACOID;
  ACOGroup := Source.ACOGroup;
  ComponentPath := Source.ComponentPath;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TACOList.Create;
begin
  inherited Create;

  FItems:=TList.Create;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
destructor TACOList.Destroy;
begin
  Clear;

  if Assigned (fItems) then
    fItems.Free;
  fItems := Nil;

  inherited Destroy;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACOList.GetCount:integer;
begin
  Result:=FItems.Count;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACOList.FindACO (const FormName, CompName : String) : TACOListEntry;
var
  idx : Integer;
  found : Boolean;
begin
  idx := 0;
  found := False;

  while (idx < Count) and not (found) do begin
    if (Items [idx].FormName = FormName) and (Items [idx].CompName = CompName) then
      found := True
    else Inc (idx);
  end;

  if (found) then
    Result := Items [idx]
  else Result := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACOList.FindACOIndex (const FormName, CompName : String) : Integer;
var
  idx : Integer;
  found : Boolean;
begin
  idx := 0;
  found := False;

  while (idx < Count) and not (found) do begin
    if (Items [idx].FormName = FormName) and (Items [idx].CompName = CompName) then
      found := True
    else Inc (idx);
  end;

  if (found) then
    Result := idx
  else Result := -1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACOList.FindACO (const ACOName : String) : TACOListEntry;
var
  idx : Integer;
  found : Boolean;
begin
  idx := 0;
  found := False;

  while (idx < Count) and not (found) do begin
    if (Items [idx].ACOName = ACOName) then
      found := True
    else Inc (idx);
  end;

  if (found) then
    Result := Items [idx]
  else Result := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACOList.FindACO (const ACOID : Integer) : TACOListEntry;
var
  idx : Integer;
  found : Boolean;
begin
  idx := 0;
  found := False;

  while (idx < Count) and not (found) do begin
    if (Items [idx].ACOID = ACOID) then
      found := True
    else Inc (idx);
  end;

  if (found) then
    Result := Items [idx]
  else Result := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACOList.FindACOIndex (const ACOID : Integer) : Integer;
var
  idx : Integer;
  found : Boolean;
begin
  idx := 0;
  found := False;

  while (idx < Count) and not (found) do begin
    if (Items [idx].ACOID = ACOID) then
      found := True
    else Inc (idx);
  end;

  if (found) then
    Result := idx
  else Result := -1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACOList.GetItem(aIndex:integer):TACOListEntry;
begin
  if (aIndex < Count) then
    Result:=TACOListEntry(FItems[aIndex])
  else Result := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOList.SetItem(aIndex:integer;aItem:TACOListEntry);
begin
  if (aIndex >= 0) and (aIndex < Count) and Assigned (FItems[aIndex]) then begin
    if (aIndex = 0) then
      TACOListEntry (FItems[aIndex]).AssignTo (aItem)
    else
      TACOListEntry (FItems[aIndex]).AssignTo (aItem);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOList.Add(aItem:TACOListEntry);
begin
  FItems.Add(aItem);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOList.Delete(aIndex:integer);
begin
  if (aIndex < Count) then begin
    Items[aIndex].Free;

    FItems.Delete(aIndex);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOList.Clear;
begin
  while Count>0 do Delete(0);
end;

//************************************************************************

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TACOListManager.Create(AOwner: TComponent);
begin
  inherited Create (AOwner);

  fFormList := TList.Create;
  fACOList  := TACOList.Create;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
destructor  TACOListManager.Destroy;
var
  idx : Integer;
begin
  if Assigned (fACOList) then
    fACOList.Free;
  fACOList := Nil;

  if Assigned (fFormList) then begin
    idx := 0;
    while (idx < fFormList.Count) do begin
      if Assigned (fFormList.Items [idx]) then
        TACOListForm (fFormList.Items [idx]).Master := Nil;

      Inc (idx);
    end;
  end;

  if Assigned (fFormList) then
    fFormList.Free;
  fFormList := Nil;

  inherited Destroy;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListManager.Loaded;
var
  i, j, idx : Integer;
  idcount : Integer;
begin
{$ifdef TRACE}
  ProcedureStart ('TACOListManager.Loaded');
{$endif}

  inherited Loaded;

{$ifdef TRACE}
  TraceParameter ('Name', Name);
{$endif}

  idcount := 1;

  idx := 0;
  while (idx < fACOList.Count) do begin
    if (fACOList [idx].ACOID > 0) and (fACOList [idx].ACOID > idcount) then
      idcount := fACOList [idx].ACOID;

    Inc (idx);
  end;

  idx := 0;
  while (idx < fACOList.Count) do begin
    if (fACOList [idx].ACOID <= 0) then begin
      fACOList [idx].ACOID := idcount;

      Inc (idcount);
    end;

    Inc (idx);
  end;

  for j := 0 to Application.ComponentCount -1 do begin
    if Application.Components[j] is TForm then begin
      with Application.Components[j] as TForm do begin
        for i:=0 to ComponentCount - 1 do begin
          if Components [i] is TACOListForm then begin
            idx := fFormList.IndexOf (Components [i]);

            if (idx = -1) then begin
              {$ifdef TRACE}
                TraceString ('add '+Components [i].Name+' to list');
              {$endif}

              fFormList.Add (Components [i]);
            end;
          end;
        end;
      end;
    end;
  end;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListManager.LoadCompProperty(Reader: TReader);
var
  data    : TACOListEntry;
  csvstr  : String;
  csvlist : TStringList;
begin
  {$ifdef DEBUG}
  OutputDebugString ('TACOListManager.LoadCompProperty');
  {$endif}

  csvlist := TStringList.Create;

  try
    csvlist.Delimiter := ';';
    csvlist.StrictDelimiter := True;

    try
      Reader.ReadListBegin;

      while not (Reader.EndOfList) do begin
        data := TACOListEntry.Create;

        csvstr := Reader.ReadString;

        if (Pos (';', csvstr) = 0) then begin
          data.FormName := csvstr;
          data.CompType := Reader.ReadString;
          data.CompName := Reader.ReadString;
          data.ACOName  := Reader.ReadString;
          data.ACOText  := Reader.ReadString;
        end else begin
          csvlist.DelimitedText := csvstr;

          data.FormName := csvlist [0];
          data.CompType := csvlist [1];
          data.CompName := csvlist [2];
          data.ACOName  := csvlist [3];
          data.ACOText  := csvlist [4];

          if (Length (csvlist [5]) = 0) then
            data.ACOID := -1
          else if not (TryStrToInt (csvlist [5], data.ACOID)) then
            data.ACOID := -1;

          data.ACOGroup := csvlist [6];

          if (csvlist.Count > 7) then
            data.ComponentPath := csvlist [7]
          else
            data.ComponentPath := '';
        end;

        fACOList.Add (data);
      end;

      Reader.ReadListEnd;
    except
      {$ifdef DEBUG}
      OutputDebugString ('Error LoadCompProperty');
      {$endif}
    end;
  finally
    csvlist.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListManager.StoreCompProperty(Writer: TWriter);
var
  idx : Integer;
  csvstr : String;
begin
  {$ifdef DEBUG}
  OutputDebugString ('TACOListManager.StoreCompProperty');
  {$endif}

  try
    Writer.WriteListBegin;

    idx := 0;
    while (idx < fACOList.Count) do begin
      csvstr := fACOList [idx].FormName+';'+fACOList [idx].CompType+';'+fACOList [idx].CompName+';'+fACOList [idx].ACOName+';'+fACOList [idx].ACOText+';';

      if (fACOList [idx].ACOID > 0) then
        csvstr := csvstr + IntToStr (fACOList [idx].ACOID);

      csvstr := csvstr + ';' + fACOList [idx].ACOGroup + ';' + fACOList [idx].ComponentPath;

      Writer.WriteString (csvstr);

      Inc (idx);
    end;

    Writer.WriteListEnd;
  except
    {$ifdef DEBUG}
    OutputDebugString ('Error StoreCompProperty');
    {$endif}
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACOListManager.DoWrite(Filer: TFiler): Boolean;
begin
  {$ifdef DEBUG}
  OutputDebugString ('TACOListManager.DoWrite');
  {$endif}

  if Filer.Ancestor <> nil then begin { check Ancestor for an inherited value }
    Result := False;
  end else begin { no inherited value -- check for default (nil) value }
    Result := fACOList.Count > 0;
  end;

  {$ifdef DEBUG}
  if (Result) then
    OutputDebugString ('TACOListManager.DoWrite -> True')
  else OutputDebugString ('TACOListManager.DoWrite -> False');
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListManager.DefineProperties(Filer: TFiler);
begin
  {$ifdef DEBUG}
  OutputDebugString ('TACOListManager.DefineProperties');
  {$endif}

  inherited DefineProperties (Filer);

  Filer.DefineProperty('ACOItems', LoadCompProperty, StoreCompProperty, DoWrite (Filer));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListManager.Editor;
var
  editor : TACOListEditForm;
begin
  editor := Nil;

  try
    editor := TACOListEditForm.Create (Nil);
    editor.TransOwner := Self;
    editor.OwnerForm := Nil;

    editor.DoEditor;
  except
  end;

  if Assigned (editor) then
    editor.Free;
end;

//******************************************************************************

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TACOLISTForm.Create(AOwner: TComponent);
begin
  inherited Create (AOwner);

  fMaster   := Nil;

  if Assigned (Owner) and (Owner is TForm) then
    OwnerForm := (Owner as TForm)
  else OwnerForm := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
destructor TACOLISTForm.Destroy;
var
  idx : Integer;
begin
{$ifdef TRACE}
  ProcedureStart ('TCompTranslateForm.Destroy');
  TraceParameter ('Name', Name);
{$endif}

  if Assigned (fMaster) and Assigned (fMaster.FormList) then begin
    idx := fMaster.FormList.IndexOf (Self);

    if (idx <> -1) then
      fMaster.FormList.Delete (idx);
  end;

  fMaster := Nil;
  OwnerForm := Nil;

  inherited Destroy;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOLISTForm.SetMaster (Master : TACOListManager);
var
  idx : Integer;
begin
  if (Master <> fMaster) then begin
    fMaster := Master;

    if Assigned (fMaster) and Assigned (fMaster.FormList)then begin
      idx := fMaster.FormList.IndexOf (Self);

      if (idx = -1) then begin
        {$ifdef TRACE}
          TraceString ('add to list');
        {$endif}

        fMaster.FormList.Add (Self);
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOLISTForm.Loaded;
var
  idx : Integer;
begin
{$ifdef TRACE}
  ProcedureStart ('TACOLISTForm.Loaded');
{$endif}

  inherited Loaded;

{$ifdef TRACE}
  TraceParameter ('Name', Name);
{$endif}

  if Assigned (fMaster) and Assigned (fMaster.FormList)then begin
    idx := fMaster.FormList.IndexOf (Self);

    if (idx = -1) then begin
      {$ifdef TRACE}
        TraceString ('add to list');
      {$endif}
      fMaster.FormList.Add (Self);
    end;
  end;

{$ifdef TRACE}
  ProcedureStop;
{$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOListForm.Editor;
var
  editor : TACOListEditForm;
begin
  editor := Nil;

  try
    editor := TACOListEditForm.Create (Nil);
    editor.TransOwner := fMaster;
    editor.OwnerForm := Self.OwnerForm;

    editor.DoEditor;
  except
  end;

  if Assigned (editor) then
    editor.Free;
end;

end.
