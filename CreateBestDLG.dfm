object CreateBestForm: TCreateBestForm
  Left = 426
  Top = 388
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Create new purchase order'
  ClientHeight = 574
  ClientWidth = 634
  Color = clBtnFace
  Constraints.MinHeight = 500
  Constraints.MinWidth = 650
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = True
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object MandPanel: TPanel
    Left = 0
    Top = 0
    Width = 634
    Height = 48
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      634
      48)
    object Label2: TLabel
      Left = 8
      Top = 8
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 9
      Top = 24
      Width = 616
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 48
    Width = 634
    Height = 46
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      634
      46)
    object Label11: TLabel
      Left = 8
      Top = 6
      Width = 67
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 617
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object LagerPanel: TPanel
    Left = 0
    Top = 94
    Width = 634
    Height = 46
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      634
      46)
    object Label3: TLabel
      Left = 8
      Top = 6
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 617
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      TabOrder = 0
      OnChange = LagerComboBoxChange
    end
  end
  object Panel1: TPanel
    Left = 0
    Top = 528
    Width = 634
    Height = 46
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      634
      46)
    object OkButton: TButton
      Left = 461
      Top = 13
      Width = 76
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
    object AbortButton: TButton
      Left = 549
      Top = 13
      Width = 76
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 140
    Width = 634
    Height = 202
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      634
      202)
    object Label6: TLabel
      Left = 8
      Top = 60
      Width = 43
      Height = 13
      Caption = 'Bestellnr.'
    end
    object Label4: TLabel
      Left = 119
      Top = 60
      Width = 83
      Height = 13
      Caption = 'Kunden Bestellnr.'
    end
    object Label10: TLabel
      Left = 232
      Top = 60
      Width = 43
      Height = 13
      Alignment = taRightJustify
      Caption = 'Bestellart'
    end
    object Label9: TLabel
      Left = 426
      Top = 60
      Width = 55
      Height = 13
      Alignment = taRightJustify
      Anchors = [akTop, akRight]
      Caption = 'Lieferdatum'
      ExplicitLeft = 432
    end
    object Label7: TLabel
      Left = 9
      Top = 147
      Width = 37
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'Hinweis'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 192
      Width = 617
      Height = 8
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 623
    end
    object Bevel2: TBevel
      Left = 8
      Top = 4
      Width = 617
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 623
    end
    object Label1: TLabel
      Left = 8
      Top = 10
      Width = 41
      Height = 13
      Caption = 'Lieferant'
    end
    object Label5: TLabel
      Left = 531
      Top = 60
      Width = 69
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'Lieferscheinnr.'
      ExplicitLeft = 537
    end
    object Label12: TLabel
      Left = 8
      Top = 102
      Width = 53
      Height = 13
      Caption = 'Anliefertour'
    end
    object Label19: TLabel
      Left = 119
      Top = 102
      Width = 74
      Height = 13
      Alignment = taRightJustify
      Caption = 'Prozessnummer'
    end
    object Label8: TLabel
      Left = 231
      Top = 102
      Width = 47
      Height = 13
      Alignment = taRightJustify
      Caption = 'Projekt-ID'
    end
    object Label13: TLabel
      Left = 427
      Top = 102
      Width = 54
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'Uhrzeit von'
    end
    object Label14: TLabel
      Left = 531
      Top = 102
      Width = 49
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'Uhrzeit bis'
    end
    object BestNrEdit: TEdit
      Left = 8
      Top = 76
      Width = 96
      Height = 21
      MaxLength = 32
      TabOrder = 1
      Text = 'BestNrEdit'
      OnChange = BestChange
    end
    object KdBestNrEdit: TEdit
      Left = 118
      Top = 76
      Width = 96
      Height = 21
      MaxLength = 32
      TabOrder = 2
      Text = 'KdBestNrEdit'
      OnChange = BestChange
    end
    object BestArtComboBox: TComboBoxPro
      Left = 232
      Top = 76
      Width = 177
      Height = 21
      Style = csDropDownList
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 13
      TabOrder = 3
      OnChange = BestChange
    end
    object LiefDateTimePicker: TDateTimePicker
      Left = 427
      Top = 76
      Width = 90
      Height = 21
      Anchors = [akTop, akRight]
      Date = 41221.655059525460000000
      Time = 41221.655059525460000000
      TabOrder = 7
      OnChange = BestChange
    end
    object HintEdit: TEdit
      Left = 9
      Top = 163
      Width = 616
      Height = 21
      Anchors = [akLeft, akRight, akBottom]
      MaxLength = 64
      TabOrder = 11
      Text = 'HintEdit'
      OnChange = BestChange
    end
    object LieferantComboBox: TComboBoxPro
      Left = 8
      Top = 26
      Width = 617
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 16
      PopupMenu = LFPopupMenu
      TabOrder = 0
      OnChange = BestChange
    end
    object LiefNrEdit: TEdit
      Left = 531
      Top = 76
      Width = 94
      Height = 21
      Anchors = [akTop, akRight]
      MaxLength = 32
      TabOrder = 10
      Text = 'LiefNrEdit'
      OnChange = BestChange
    end
    object BestTourEdit: TEdit
      Left = 7
      Top = 118
      Width = 96
      Height = 21
      MaxLength = 32
      TabOrder = 4
      Text = 'BestTourEdit'
      OnChange = BestChange
    end
    object BestProcessEdit: TEdit
      Left = 119
      Top = 118
      Width = 88
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 5
      Text = 'BestProcessEdit'
      OnChange = BestChange
    end
    object BestProjectIDEdit: TEdit
      Left = 232
      Top = 118
      Width = 87
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 32
      TabOrder = 6
      Text = 'BestProjectIDEdit'
      OnChange = BestChange
    end
    object LiefTimeFromTimePicker: TDateTimePicker
      Left = 427
      Top = 118
      Width = 90
      Height = 21
      Anchors = [akTop, akRight]
      Date = 41221.655059525460000000
      Time = 41221.655059525460000000
      ShowCheckbox = True
      Kind = dtkTime
      TabOrder = 8
      OnChange = BestChange
    end
    object LiefTimeToTimePicker: TDateTimePicker
      Left = 531
      Top = 118
      Width = 90
      Height = 21
      Anchors = [akTop, akRight]
      Date = 41221.655059525460000000
      Time = 41221.655059525460000000
      ShowCheckbox = True
      Kind = dtkTime
      TabOrder = 9
      OnChange = BestChange
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 342
    Width = 634
    Height = 186
    Align = alClient
    BevelOuter = bvNone
    Caption = 'Panel2'
    TabOrder = 5
    DesignSize = (
      634
      186)
    object DeletePosButton: TButton
      Left = 545
      Top = 140
      Width = 80
      Height = 26
      Anchors = [akRight, akBottom]
      Caption = 'L'#246'schen...'
      TabOrder = 0
      OnClick = DeletePosButtonClick
    end
    object EditPosButton: TButton
      Left = 547
      Top = 44
      Width = 78
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Bearbeiten...'
      TabOrder = 1
      OnClick = ChangePosButtonClick
    end
    object NewPosButton: TButton
      Left = 547
      Top = 13
      Width = 78
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Neu...'
      TabOrder = 2
      OnClick = ChangePosButtonClick
    end
    object BestPosDBGrid: TDBGridPro
      Left = 4
      Top = 12
      Width = 529
      Height = 154
      Anchors = [akLeft, akTop, akRight, akBottom]
      DataSource = PosDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
      ReadOnly = True
      TabOrder = 3
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -11
      BandsFont.Name = 'Tahoma'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
      RegistryKey = 'Software\CS'
      RegistrySection = 'DBGrids'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 216
    Top = 8
  end
  object KundenQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 112
    Top = 704
  end
  object KundenDataSource: TDataSource
    DataSet = KundenQuery
    Left = 152
    Top = 704
  end
  object PosDataSource: TDataSource
    DataSet = PosQuery
    OnDataChange = PosDataSourceDataChange
    Left = 64
    Top = 704
  end
  object PosQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 24
    Top = 704
  end
  object AufNVEADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 192
    Top = 704
  end
  object AufNVEDataSource: TDataSource
    DataSet = AufNVEADOQuery
    Left = 232
    Top = 704
  end
  object LFPopupMenu: TPopupMenu
    Left = 336
    Top = 168
    object NeuerLieferant1: TMenuItem
      Caption = 'Neuer Lieferant...'
      ShortCut = 115
      OnClick = NeuerLieferant1Click
    end
  end
end
