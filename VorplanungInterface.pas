unit VorplanungInterface;

interface

function InsertVorplanung   (const pRefMand, pRefLager, pRefBereich : Integer; const pName, pBeschreibung, pArt,pVorgang : String; const pValidDate : TDateTime; var oRef : Integer) : Integer;
function ChangeVorplanung   (const pRef : Integer; const pName, pBeschreibung : String; const pValidDate : TDateTime) : Integer;
function CopyToVorplanung   (const pRef : Integer) : Integer;
function DeleteVorplanung   (const pRef : Integer) : Integer;
function ActivateVorplanung (const pRef : Integer) : Integer;

implementation

uses <PERSON>, <PERSON><PERSON><PERSON>, Variants, DatenModul;

//******************************************************************************
//* Function Name:
//* Author       : <PERSON>
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertVorplanung (const pRefMand, pRefLager, pRefBereich : Integer; const pName, pBeschreibung, pArt,pVorgang : String; const pValidDate : TDateTime; var oRef : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
  param           : TParameter;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_VORPLANUNG.INSERT_VORPLANUNG';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    if (pRefMand = -1) then
      Parameters.CreateParameter('pMandRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMandRef',ftInteger,pdInput, 12, pRefMand);

    if (pRefLager = -1) then
      Parameters.CreateParameter('pLagerRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLagerRef',ftInteger,pdInput, 12, pRefLager);

    if (pRefBereich = -1) then
      Parameters.CreateParameter('pBereichRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pBereichRef',ftInteger,pdInput, 12, pRefBereich);

    Parameters.CreateParameter('pName',ftString,pdInput, 64, pName);
    Parameters.CreateParameter('pDesc',ftString,pdInput, 128, pBeschreibung);
    Parameters.CreateParameter('pArt',ftString,pdInput, 16, pArt);
    Parameters.CreateParameter('pVorgang',ftString,pdInput, 16, pVorgang);

    if (pValidDate = 0) then
      Parameters.CreateParameter('pValidDate',ftString,pdInput, 32, NULL)
    else
      Parameters.CreateParameter('pValidDate',ftDateTime,pdInput, 32, pValidDate);

    Parameters.CreateParameter('oRef', ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres <> 0) Then
    oRef := -1
  else begin
    param := StoredProcedure.Parameters.FindParam ('oRefAufKommPos');
    if Assigned (param) then begin
      if (param.Value = NULL) then
        oRef := -1
      else oRef := param.Value;
    end;
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeVorplanung (const pRef : Integer; const pName, pBeschreibung : String; const pValidDate : TDateTime) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_VORPLANUNG.CHANGE_VORPLANUNG';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, pRef);
    Parameters.CreateParameter('pName',ftString,pdInput, 64, pName);
    Parameters.CreateParameter('pDesc',ftString,pdInput, 128, pBeschreibung);

    if (pValidDate = 0) then
      Parameters.CreateParameter('pValidDate',ftString,pdInput, 32, NULL)
    else
      Parameters.CreateParameter('pValidDate',ftDateTime,pdInput, 32, pValidDate);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CopyToVorplanung   (const pRef : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_VORPLANUNG.COPY_TO_VORPLANUNG';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, pRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteVorplanung (const pRef : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_VORPLANUNG.DELETE_VORPLANUNG';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, pRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ActivateVorplanung (const pRef : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_VORPLANUNG.ACTIVATE_VORPLANUNG';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, pRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

end.
