object ChangeLPZuordnungForm: TChangeLPZuordnungForm
  Left = 331
  Top = 329
  BorderStyle = bsDialog
  Caption = 'GB'
  ClientHeight = 493
  ClientWidth = 512
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  DesignSize = (
    512
    493)
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Label3: TLabel
    Left = 8
    Top = 64
    Width = 75
    Height = 13
    Caption = 'Lagerplatztypen'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 56
    Width = 497
    Height = 9
    Shape = bsTopLine
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 497
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 120
    ItemHeight = 15
    TabOrder = 0
    OnChange = LagerComboBoxChange
  end
  object LPTypenListBox: TListBox
    Left = 8
    Top = 80
    Width = 497
    Height = 121
    Style = lbOwnerDrawFixed
    ItemHeight = 13
    TabOrder = 1
    OnClick = LPTypenListBoxClick
    OnDrawItem = ListBoxDrawItem
  end
  object GroupBox1: TGroupBox
    Left = 8
    Top = 216
    Width = 497
    Height = 240
    Caption = 'Eigenschaften des Lagerplatztypen'
    TabOrder = 2
    DesignSize = (
      497
      240)
    object Label2: TLabel
      Left = 8
      Top = 111
      Width = 194
      Height = 13
      Anchors = [akLeft, akBottom]
      Caption = 'F'#252'r diesen Lagerplatztypen zul'#228'ssige LTs'
      ExplicitTop = 64
    end
    object LPBesCheckBox: TCheckBox
      Left = 8
      Top = 19
      Width = 193
      Height = 17
      Caption = 'Bestand auf Lagerplatz buchbar'
      TabOrder = 0
      OnClick = ChangeClick
    end
    object UpdateButton: TButton
      Left = 416
      Top = 207
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      TabOrder = 5
      OnClick = UpdateButtonClick
    end
    object ZuordnungCheckListBox: TCheckListBox
      Left = 8
      Top = 127
      Width = 393
      Height = 105
      Anchors = [akLeft, akRight, akBottom]
      ItemHeight = 13
      Style = lbOwnerDrawFixed
      TabOrder = 4
      OnClickCheck = ChangeClick
      OnDrawItem = ListBoxDrawItem
    end
    object MixedCheckBox: TCheckBox
      Left = 8
      Top = 37
      Width = 457
      Height = 17
      Caption = 'Gemischte Belegung mit LEs und Bestand zul'#228'ssig'
      TabOrder = 1
      OnClick = ChangeClick
    end
    object TransferCheckBox: TCheckBox
      Left = 8
      Top = 55
      Width = 281
      Height = 17
      Caption = 'Diese Lagerpl'#228'tze sind Transferpl'#228'tze'
      TabOrder = 2
      OnClick = ChangeClick
    end
    object OverLTCheckBox: TCheckBox
      Left = 8
      Top = 73
      Width = 281
      Height = 17
      BiDiMode = bdLeftToRight
      Caption = #220'berbreite LT pr'#252'fen'
      ParentBiDiMode = False
      TabOrder = 3
      OnClick = ChangeClick
    end
  end
  object CloseButton: TButton
    Left = 430
    Top = 462
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 3
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 416
    Top = 112
  end
end
