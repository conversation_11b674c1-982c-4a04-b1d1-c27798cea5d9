object InvBulkCountInputForm: TInvBulkCountInputForm
  Left = 0
  Top = 0
  Caption = 'Blockplatz erfassen'
  ClientHeight = 371
  ClientWidth = 706
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  PixelsPerInch = 96
  TextHeight = 13
  object MHDPanel: TPanel
    Left = 0
    Top = 243
    Width = 706
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      706
      41)
    object Bevel6: TBevel
      Left = 16
      Top = 35
      Width = 680
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 631
    end
    object Label9: TLabel
      Left = 16
      Top = 10
      Width = 22
      Height = 13
      Caption = 'MHD'
    end
    object MHDDutyLabel: TLabel
      Left = 99
      Top = 6
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object MHDEdit: TEdit
      Left = 112
      Top = 7
      Width = 121
      Height = 21
      TabOrder = 0
      Text = 'MHDEdit'
    end
  end
  object ChargePanel: TPanel
    Left = 0
    Top = 284
    Width = 706
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      706
      41)
    object Bevel5: TBevel
      Left = 16
      Top = 35
      Width = 680
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitWidth = 631
    end
    object Label8: TLabel
      Left = 16
      Top = 10
      Width = 35
      Height = 13
      Caption = 'Charge'
    end
    object ChargeDutyLabel: TLabel
      Left = 99
      Top = 5
      Width = 11
      Height = 23
      Caption = '*'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = 'Symbol'
      Font.Style = [fsBold]
      ParentFont = False
      Visible = False
    end
    object ChargeEdit: TEdit
      Left = 112
      Top = 7
      Width = 121
      Height = 21
      TabOrder = 0
      Text = 'ChargeEdit'
    end
  end
  object FussPanel: TPanel
    Left = 0
    Top = 325
    Width = 706
    Height = 46
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      706
      46)
    object OkButton: TButton
      Left = 533
      Top = 11
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      Default = True
      ModalResult = 1
      TabOrder = 0
    end
    object AbortButton: TButton
      Left = 621
      Top = 11
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
  end
  object MengePanel: TPanel
    Left = 0
    Top = 97
    Width = 706
    Height = 146
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      706
      146)
    object Bevel3: TBevel
      Left = 16
      Top = 144
      Width = 680
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 81
      ExplicitWidth = 748
    end
    object Label4: TLabel
      Left = 24
      Top = 56
      Width = 48
      Height = 13
      Caption = 'verf'#252'gbar'
    end
    object EinheitLabel: TLabel
      Left = 184
      Top = 56
      Width = 57
      Height = 13
      Caption = 'EinheitLabel'
    end
    object CountLabel: TLabel
      Left = 16
      Top = 6
      Width = 54
      Height = 13
      Caption = 'CountLabel'
      Visible = False
    end
    object Label5: TLabel
      Left = 24
      Top = 80
      Width = 41
      Height = 13
      Caption = 'gesperrt'
      Visible = False
    end
    object SperrEinheitLabel: TLabel
      Left = 184
      Top = 80
      Width = 83
      Height = 13
      Caption = 'SperrEinheitLabel'
      Visible = False
    end
    object Label7: TLabel
      Left = 296
      Top = 80
      Width = 54
      Height = 13
      Caption = 'Sperrgrund'
      Visible = False
    end
    object Label3: TLabel
      Left = 16
      Top = 115
      Width = 55
      Height = 13
      Caption = 'Anzahl LEs:'
    end
    object Label6: TLabel
      Left = 16
      Top = 31
      Width = 69
      Height = 13
      Caption = 'Menge pro LE:'
    end
    object Label1: TLabel
      Left = 317
      Top = 112
      Width = 33
      Height = 13
      Alignment = taRightJustify
      Anchors = [akLeft, akBottom]
      Caption = 'LT-Typ'
    end
    object MengeEdit: TEdit
      Left = 112
      Top = 53
      Width = 66
      Height = 21
      TabOrder = 0
      Text = 'MengeEdit'
      OnExit = MengeEditExit
      OnKeyPress = MengeEditKeyPress
    end
    object SperrMengeEdit: TEdit
      Left = 112
      Top = 77
      Width = 66
      Height = 21
      TabOrder = 1
      Text = 'SperrMengeEdit'
      Visible = False
    end
    object SperrGrundEdit: TEdit
      Left = 366
      Top = 77
      Width = 330
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 2
      Text = 'SperrGrundEdit'
      Visible = False
    end
    object AnzLEEdit: TEdit
      Left = 112
      Top = 112
      Width = 49
      Height = 21
      TabOrder = 3
      Text = '1'
    end
    object AnzLEUpDown: TUpDown
      Left = 161
      Top = 112
      Width = 16
      Height = 21
      Associate = AnzLEEdit
      Min = 1
      Max = 999
      Position = 1
      TabOrder = 4
    end
    object LTComboBox: TComboBoxPro
      Left = 366
      Top = 109
      Width = 330
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akBottom]
      ItemHeight = 15
      TabOrder = 5
    end
  end
  object LPPanel: TPanel
    Left = 0
    Top = 0
    Width = 706
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      706
      41)
    object Bevel1: TBevel
      Left = 16
      Top = 37
      Width = 680
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 49
      ExplicitWidth = 748
    end
    object Label2: TLabel
      Left = 16
      Top = 16
      Width = 15
      Height = 13
      Caption = 'LP:'
    end
    object LPLabel: TLabel
      Left = 72
      Top = 16
      Width = 36
      Height = 13
      Caption = 'LPLabel'
    end
  end
  object SelectArtikelPanel: TPanel
    Left = 0
    Top = 41
    Width = 706
    Height = 56
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      706
      56)
    object Label10: TLabel
      Left = 16
      Top = 8
      Width = 34
      Height = 13
      Caption = 'Artikel:'
    end
    object Bevel4: TBevel
      Left = 16
      Top = 52
      Width = 680
      Height = 6
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 49
      ExplicitWidth = 748
    end
    object ArNrEdit: TEdit
      Left = 72
      Top = 5
      Width = 98
      Height = 21
      TabOrder = 0
      Text = 'ArNrEdit'
      OnExit = ArNrEditExit
    end
    object ArtikelComboBox: TComboBoxPro
      Left = 184
      Top = 5
      Width = 512
      Height = 21
      ItemDelimiter = #9
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 1
      OnChange = ArtikelComboBoxChange
      OnCloseUp = ArtikelComboBoxCloseUp
      OnDropDown = ArtikelComboBoxDropDown
    end
    object ListedCheckBox: TCheckBox
      Left = 72
      Top = 31
      Width = 343
      Height = 17
      Caption = 'nicht gelistete Artikel auch auff'#252'hren'
      TabOrder = 2
    end
  end
end
