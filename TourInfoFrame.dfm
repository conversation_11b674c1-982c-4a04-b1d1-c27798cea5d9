object TourInfo: TTourInfo
  Left = 0
  Top = 0
  Width = 573
  Height = 171
  TabOrder = 0
  TabStop = True
  DesignSize = (
    573
    171)
  object Label15: TLabel
    Left = 1
    Top = 7
    Width = 44
    Height = 13
    Caption = 'Spedition'
  end
  object Label3: TLabel
    Left = 1
    Top = 75
    Width = 32
    Height = 13
    Caption = 'Fahrer'
  end
  object Label4: TLabel
    Left = 429
    Top = 75
    Width = 60
    Height = 13
    Anchors = [akTop, akRight]
    Caption = 'Kennzeichen'
  end
  object Bevel1: TBevel
    Left = 1
    Top = 62
    Width = 569
    Height = 1
    Shape = bsTopLine
  end
  object Bevel2: TBevel
    Left = 1
    Top = 102
    Width = 569
    Height = 1
    Shape = bsTopLine
  end
  object Label5: TLabel
    Left = 208
    Top = 75
    Width = 61
    Height = 13
    Caption = 'Fahrzeugtyp'
  end
  object SpedComboBox: TComboBoxPro
    Left = 70
    Top = 4
    Width = 500
    Height = 22
    Style = csOwnerDrawFixed
    ItemHeight = 16
    TabOrder = 0
  end
  object KommPlanGroupBox: TGroupBox
    Left = 70
    Top = 119
    Width = 164
    Height = 49
    TabOrder = 6
    object Label8: TLabel
      Left = 56
      Top = 25
      Width = 6
      Height = 13
      Caption = 'h'
    end
    object Label9: TLabel
      Left = 128
      Top = 25
      Width = 16
      Height = 13
      Caption = 'min'
    end
    object KommHourEdit: TEdit
      Left = 8
      Top = 20
      Width = 28
      Height = 21
      TabOrder = 0
      Text = '0'
      OnKeyPress = HourEditKeyPress
    end
    object KommHourUpDown: TIntegerUpDown
      Left = 36
      Top = 20
      Width = 16
      Height = 21
      Associate = KommHourEdit
      Max = 23
      TabOrder = 1
    end
    object KommMinUpDown: TIntegerUpDown
      Left = 108
      Top = 20
      Width = 16
      Height = 21
      Associate = KommMinEdit
      Max = 59
      TabOrder = 3
    end
    object KommMinEdit: TEdit
      Left = 80
      Top = 20
      Width = 28
      Height = 21
      TabOrder = 2
      Text = '0'
      OnKeyPress = MinEditKeyPress
    end
  end
  object GroupBox2: TGroupBox
    Left = 238
    Top = 116
    Width = 164
    Height = 52
    TabOrder = 8
    object Label10: TLabel
      Left = 56
      Top = 23
      Width = 6
      Height = 13
      Caption = 'h'
    end
    object Label11: TLabel
      Left = 128
      Top = 23
      Width = 16
      Height = 13
      Caption = 'min'
    end
    object AbgHourEdit: TEdit
      Left = 8
      Top = 20
      Width = 28
      Height = 21
      TabOrder = 0
      Text = '0'
      OnKeyPress = HourEditKeyPress
    end
    object AbgHourUpDown: TIntegerUpDown
      Left = 36
      Top = 20
      Width = 16
      Height = 21
      Associate = AbgHourEdit
      Max = 23
      TabOrder = 1
    end
    object AbgMinEdit: TEdit
      Left = 80
      Top = 20
      Width = 28
      Height = 21
      TabOrder = 2
      Text = '0'
      OnKeyPress = MinEditKeyPress
    end
    object AbgMinUpDown: TIntegerUpDown
      Left = 108
      Top = 20
      Width = 16
      Height = 21
      Associate = AbgMinEdit
      Max = 59
      TabOrder = 3
    end
  end
  object GroupBox3: TGroupBox
    Left = 406
    Top = 116
    Width = 164
    Height = 52
    Anchors = [akTop, akRight]
    TabOrder = 10
    object Label1: TLabel
      Left = 56
      Top = 23
      Width = 6
      Height = 13
      Caption = 'h'
    end
    object Label2: TLabel
      Left = 128
      Top = 23
      Width = 16
      Height = 13
      Caption = 'min'
    end
    object ForecastHourEdit: TEdit
      Left = 8
      Top = 20
      Width = 28
      Height = 21
      TabOrder = 0
      Text = '0'
      OnKeyPress = HourEditKeyPress
    end
    object ForecastHourUpDown: TIntegerUpDown
      Left = 36
      Top = 20
      Width = 16
      Height = 21
      Associate = ForecastHourEdit
      Max = 23
      TabOrder = 1
    end
    object ForecastMinEdit: TEdit
      Left = 80
      Top = 20
      Width = 28
      Height = 21
      TabOrder = 2
      Text = '0'
      OnKeyPress = MinEditKeyPress
    end
    object ForecastMinUpDown: TIntegerUpDown
      Left = 108
      Top = 20
      Width = 16
      Height = 21
      Associate = ForecastMinEdit
      Max = 59
      TabOrder = 3
    end
  end
  object DistributionCheckBox: TCheckBox
    Left = 70
    Top = 32
    Width = 546
    Height = 17
    Caption = 'Die Feinverteilung erfolgt auch durch die Spedition'
    TabOrder = 1
  end
  object AutoKommCheckBox: TCheckBox
    Left = 78
    Top = 112
    Width = 145
    Height = 17
    Caption = 'Automatische Planung ab'
    Checked = True
    State = cbChecked
    TabOrder = 5
    OnClick = AutoKommCheckBoxClick
  end
  object AbgTimeCheckBox: TCheckBox
    Left = 246
    Top = 112
    Width = 153
    Height = 17
    Caption = 'Sp'#228'testens abschlossen um'
    TabOrder = 7
    OnClick = AbgTimeCheckBoxClick
  end
  object ForecastTimeCheckBox: TCheckBox
    Left = 414
    Top = 112
    Width = 145
    Height = 17
    Anchors = [akTop, akRight]
    Caption = 'Forecastmeldungen um'
    Checked = True
    State = cbChecked
    TabOrder = 9
    OnClick = ForecastTimeCheckBoxClick
  end
  object FahrerEdit: TEdit
    Left = 70
    Top = 72
    Width = 124
    Height = 21
    TabOrder = 2
    Text = 'FahrerEdit'
  end
  object FahrzeugEdit: TEdit
    Left = 496
    Top = 72
    Width = 74
    Height = 21
    Anchors = [akTop, akRight]
    TabOrder = 4
    Text = 'FahrzeugEdit'
  end
  object KFZTypComboBox: TComboBox
    Left = 275
    Top = 72
    Width = 142
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 13
    TabOrder = 3
    Text = 'KFZTypComboBox'
  end
end
