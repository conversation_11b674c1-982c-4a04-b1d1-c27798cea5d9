object BerichtLieferantForm: TBerichtLieferantForm
  Left = 542
  Top = 180
  BorderStyle = bsDialog
  Caption = 'BerichtDatumForm'
  ClientHeight = 357
  ClientWidth = 240
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poMainFormCenter
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    240
    357)
  PixelsPerInch = 96
  TextHeight = 13
  object Bevel3: TBevel
    Left = 4
    Top = 312
    Width = 232
    Height = 9
    Anchors = [akLeft, akBottom]
    Shape = bsTopLine
    ExplicitTop = 261
  end
  object ShowButton: TButton
    Left = 8
    Top = 324
    Width = 113
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Bericht anzeigen...'
    Default = True
    TabOrder = 1
    OnClick = ShowButtonClick
  end
  object AbortButton: TButton
    Left = 158
    Top = 325
    Width = 73
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 2
  end
  object VorschauCheckBox: TCheckBox
    Left = 8
    Top = 288
    Width = 217
    Height = 17
    Anchors = [akLeft, akBottom]
    Caption = 'Mit Druckvorschau'
    Checked = True
    State = cbChecked
    TabOrder = 0
  end
  object LagerPanel: TPanel
    Left = 0
    Top = 57
    Width = 240
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    object Label2: TLabel
      Left = 8
      Top = 8
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object Bevel2: TBevel
      Left = 4
      Top = 56
      Width = 232
      Height = 9
      Shape = bsTopLine
    end
    object LagerComboBox: TComboBoxPro
      Left = 8
      Top = 25
      Width = 225
      Height = 22
      Style = csOwnerDrawFixed
      ColWidth = 60
      ItemHeight = 16
      TabOrder = 0
      OnChange = LagerComboBoxChange
    end
  end
  object MandantPanel: TPanel
    Left = 0
    Top = 0
    Width = 240
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    object Label3: TLabel
      Left = 8
      Top = 8
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object Bevel4: TBevel
      Left = 4
      Top = 56
      Width = 232
      Height = 9
      Shape = bsTopLine
    end
    object MandantComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 225
      Height = 22
      Style = csOwnerDrawFixed
      ColWidth = 60
      ItemHeight = 16
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object DatumPanel: TPanel
    Left = 0
    Top = 171
    Width = 240
    Height = 55
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    object DatumLabel: TLabel
      Left = 8
      Top = 8
      Width = 31
      Height = 13
      Caption = 'Datum'
    end
    object Bevel1: TBevel
      Left = 4
      Top = 54
      Width = 232
      Height = 9
      Shape = bsTopLine
    end
    object BisDatumLabel: TLabel
      Left = 128
      Top = 8
      Width = 48
      Height = 13
      Caption = 'Bis Datum'
    end
    object DatumDateTimePicker: TDateTimePicker
      Left = 8
      Top = 24
      Width = 105
      Height = 21
      Date = 38275.496297569450000000
      Time = 38275.496297569450000000
      TabOrder = 0
    end
    object BisDatumDateTimePicker: TDateTimePicker
      Left = 128
      Top = 24
      Width = 105
      Height = 21
      Date = 38275.496297569450000000
      Time = 38275.496297569450000000
      TabOrder = 1
      OnChange = BisDatumDateTimePickerChange
    end
  end
  object CheckPanel: TPanel
    Left = 0
    Top = 226
    Width = 240
    Height = 55
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 6
    Visible = False
    object Bevel6: TBevel
      Left = 4
      Top = 54
      Width = 232
      Height = 9
      Shape = bsTopLine
    end
    object CheckBox1: TCheckBox
      Left = 8
      Top = 6
      Width = 97
      Height = 17
      Caption = 'CheckBox1'
      TabOrder = 0
      Visible = False
    end
    object CheckBox2: TCheckBox
      Left = 8
      Top = 31
      Width = 97
      Height = 17
      Caption = 'CheckBox2'
      TabOrder = 1
      Visible = False
    end
  end
  object LieferantPanel: TPanel
    Left = 0
    Top = 114
    Width = 240
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 7
    object Label1: TLabel
      Left = 8
      Top = 8
      Width = 41
      Height = 13
      Caption = 'Lieferant'
    end
    object Bevel5: TBevel
      Left = 4
      Top = 56
      Width = 232
      Height = 9
      Shape = bsTopLine
    end
    object LieferantComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 225
      Height = 22
      Style = csOwnerDrawFixed
      ColWidth = 60
      ItemHeight = 16
      TabOrder = 0
    end
  end
end
