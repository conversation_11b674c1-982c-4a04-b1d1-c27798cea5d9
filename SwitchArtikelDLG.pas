unit SwitchArtikelDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls;

type
  TSwitchArtikelForm = class(TForm)
    ArtikelPanel: TPanel;
    Label1: TLabel;
    Label11: TLabel;
    Bevel3: TBevel;
    ArtikelComboBox: TComboBoxPro;
    VarianteEdit: TEdit;
    ArNrEdit: TEdit;
    ListedCheckBox: TCheckBox;
    Panel1: TPanel;
    Label14: TLabel;
    OkButton: TButton;
    AbortButton: TButton;
    Bevel1: TBevel;
    Label13: TLabel;
    GrundComboBox: TComboBox;
    MandComboBox: TComboBoxPro;
    Label2: TLabel;
    VorgangEdit: TEdit;
    Label3: TLabel;
    procedure ArtikelComboBoxDropDown(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure Artikel<PERSON>omboBoxCloseUp(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure GrundComboBoxChange(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure ListedCheckBoxClick(Sender: TObject);
  private
    fRefBes     : Integer;
    fRefNewBes  : Integer;
    fRefNewSum  : Integer;
    fRefNewAE   : Integer;
    fRefMand    : Integer;
    fRefSubMand : Integer;
    fRefLager   : Integer;

    fOldArNrEdit : String;
    fBesUpdate   : Boolean;
    fArtikelComboxLoaded : Boolean;

    function ReloadArtikel : Integer;
  public
    property RefNewBes   : Integer read fRefNewBes;
    property RefNewSum   : Integer read fRefNewSum;
    property RefNewAE    : Integer read fRefNewAE;

    function Prepare (const RefBes : Integer; const BesUpdate : Boolean) : Integer;
  end;

implementation

{$R *.dfm}

uses
  DB, ADODB, VCLUtilitys, LVSGlobalDaten, DatenModul, FrontendUtils, ConfigModul, LVSDatenInterface, FrontendMainUtils,
  SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.09.2017
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSwitchArtikelForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res : Integer;
begin
  if (ModalResult = mrOk) then begin
    CanClose := False;

    if (GetComboBoxRef (ArtikelComboBox) = -1) then
      ArtikelComboBox.SetFocus
    else if not (fBesUpdate) then begin
      CanClose := True;

      fRefNewAE := GetComboBoxRef (ArtikelComboBox);
    end else begin
      res := 0;

      if (fRefBes > 0) then
        res := ChangeBestandArtikel (fRefBes, GetComboBoxRef (ArtikelComboBox), -1, -1, VorgangEdit.Text, GrundComboBox.Text, fRefNewBes, fRefNewSum);

      if (res <> 0) then
        MessageDLG(FormatMessageText (1815, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else begin
        CanClose := True;

        fRefNewAE := GetComboBoxRef (ArtikelComboBox);
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.09.2017
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSwitchArtikelForm.FormCreate(Sender: TObject);
begin
  fRefBes     := -1;
  fRefNewBes  := -1;
  fRefMand    := -1;
  fRefSubMand := -1;

  fOldArNrEdit := '';
  fArtikelComboxLoaded := False;

  ArNrEdit.Text := '';
  VarianteEdit.Text := '';
  VorgangEdit.Text := '';
  ArtikelComboBox.ItemIndex := -1;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, MandComboBox);
    LVSSprachModul.SetNoTranslate (Self, ArtikelComboBox);
    LVSSprachModul.SetNoTranslate (Self, GrundComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 27.10.2017
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSwitchArtikelForm.FormShow(Sender: TObject);
begin
  Label11.Visible := VarianteEdit.Visible;

  if not (VarianteEdit.Visible) then begin
    ArtikelPanel.Height := ArtikelPanel.Height - VarianteEdit.Height;
    Height := Height - VarianteEdit.Height;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.09.2017
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSwitchArtikelForm.GrundComboBoxChange(Sender: TObject);
begin
  DoSysTextComboxboxChange (Self, GrundComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.01.2017
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSwitchArtikelForm.ListedCheckBoxClick(Sender: TObject);
begin
  ReloadArtikel;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.09.2017
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function TSwitchArtikelForm.Prepare (const RefBes : Integer; const BesUpdate : Boolean) : Integer;
var
  query : TADOQuery;
begin
  fRefBes    := RefBes;
  fBesUpdate := BesUpdate;

  if (LVSConfigModul.UseSubMandanten) then
    LoadMandantSubMandantCombobox(MandComboBox)
  else
    LoadMandantCombobox(MandComboBox);

  query := TADOQuery.Create (Self);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select bes.REF_MAND,bes.REF_LAGER,bes.MENGE_FREI,bes.MENGE_SPERR,ar.REF_SUB_MAND from VQ_LAGER_BESTAND bes inner join VQ_ARTIKEL ar on (ar.REF=bes.REF_AR) where bes.REF=:ref');
    query.Parameters [0].Value := RefBes;

    query.Open;

    fRefMand    := query.FieldByName('REF_MAND').AsInteger;
    fRefSubMand := DBGetReferenz (query.FieldByName('REF_SUB_MAND'));
    fRefLager   := query.FieldByName('REF_LAGER').AsInteger;

    if (fRefSubMand > 0) then
      MandComboBox.ItemIndex := FindMandantRef(MandComboBox, fRefSubMand)
    else
      MandComboBox.ItemIndex := FindComboboxRef(MandComboBox, fRefMand);

    query.Close;
  finally
    query.Free;
  end;

  LoadSysTexte (GrundComboBox, 'SWITCH_BES', fRefMand, False);

  if (GrundComboBox.Items.Count = 0) then begin
    GrundComboBox.Style := csDropDown;
    GrundComboBox.Items.Add('');
  end;

  VarianteEdit.Visible := LVSConfigModul.UseVarianten;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function TSwitchArtikelForm.ReloadArtikel : Integer;
var
  ref,
  idx,
  selidx,
  dlgres,
  refmand,
  refsubmand : Integer;
  query      : TADOQuery;
begin
  selidx := -1;

  GetMandantRef (MandComboBox, -1, refmand, refsubmand);

  ref := GetComboBoxRef (ArtikelComboBox);

  Screen.Cursor := crSQLWait;

  query := TADOQuery.Create (Self);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    try
      if (LVSConfigModul.UseArtikelCollis) then
        query.SQL.Add ('select REF_AR_EINHEIT, ARTIKEL_NR||''|''||ARTIKEL_TEXT||''|''||EINHEIT||''|''||COLLI_NAME from V_ARTIKEL_SUCHE where REF_MAND=:ref_mand and nvl (OPT_MULTI_COLLI, ''0'')=''0''')
      else
        query.SQL.Add ('select REF_AR_EINHEIT, ARTIKEL_NR||''|''||ARTIKEL_TEXT||''|''||EINHEIT from V_ARTIKEL_SUCHE where REF_MAND=:ref_mand');
      query.Parameters.ParamByName('ref_mand').Value := refmand;

      if (LVSConfigModul.UseSubMandanten and (refsubmand > 0)) then begin
        query.SQL.Add ('and REF_SUB_MAND=:ref_sub_mand');
        query.Parameters.ParamByName('REF_SUB_MAND').Value := refsubmand;
      end;

      if UseEinheitBarcode then
        query.SQL.Add ('and ((REF_BARCODE is null and REF_MASTER_BARCODE is null) or REF_BARCODE=REF_MASTER_BARCODE)');

      if (Length (ArNrEdit.Text) > 0) then begin
        query.SQL.Add ('and (ARTIKEL_NR like :ar_nr)');
        query.Parameters.ParamByName('ar_nr').Value := ArNrEdit.Text+'%';
      end;

      if not (ListedCheckBox.Checked) then begin
        query.SQL.Add ('and REF in (select REF_AR from V_ARTIKEL_REL_LAGER where nvl (OPT_DEPEND,''0'')=''0'' and STATUS in (''AKT'',''MAN'') and REF_LAGER=:ref_lager)');
        query.Parameters.ParamByName('ref_lager').Value := fRefLager;
      end;

      query.SQL.Add ('order by LPAD (ARTIKEL_NR,32,'' ''), case when OPT_MASTER=''1'' then 0 else 1 end asc, COLLI_NAME');

      try
        query.Open;

        if (query.RecordCount < 10000) then
          dlgres := mrYes
        else
          dlgres := MessageDLG (FormatMessageText (1364, [IntToStr (query.RecordCount)]), mtConfirmation, [mbYes, mbNo, mbCancel], 0);

        if (dlgres = mrYes) then begin
          ArtikelComboBox.Items.BeginUpdate;

          try
            ClearComboBoxObjects (ArtikelComboBox);

            while not (query.Eof) do begin
              idx := ArtikelComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

              if (ref > 0) and (ref = query.Fields [0].AsInteger) then
                selidx := idx;

              query.Next;
            end;
          finally
            ArtikelComboBox.Items.EndUpdate;
          end;

          fOldArNrEdit := ArNrEdit.Text;
        end;

        query.Close;
      except
      end;

      if (selidx > 0) then
        ArtikelComboBox.ItemIndex := selidx
      else ArtikelComboBox.ItemIndex := 0;
    finally
      Screen.Cursor := crDefault;
    end;
  finally
    query.Free;
  end;

  Label14.Caption := IntToStr (ArtikelComboBox.Items.Count);

  fArtikelComboxLoaded := True;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.09.2017
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSwitchArtikelForm.ArtikelComboBoxCloseUp(Sender: TObject);
begin
  ArtikelComboBox.ColWidths [1] := 0;
  ArtikelComboBox.Prepare;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 05.09.2017
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TSwitchArtikelForm.ArtikelComboBoxDropDown(Sender: TObject);
var
  textstr  : String;
  i,
  len,
  wbox,
  armaxlen,
  vpemaxlen,
  collimaxlen : Integer;
begin
  if not (fArtikelComboxLoaded) or (fOldArNrEdit <> ArNrEdit.Text) then
    ReloadArtikel;

  if (LVSConfigModul.UseArtikelCollis) then
    ArtikelComboBox.ColumeCount := 4
  else
    ArtikelComboBox.ColumeCount := 3;

  armaxlen    := ArtikelComboBox.ColWidths [1] - ArtikelComboBox.ColWidths [0] - 8;
  vpemaxlen   := 0;
  collimaxlen := 0;

  for i := 0 to ArtikelComboBox.Items.Count - 1 do begin
    textstr := ArtikelComboBox.GetItemText (i, 1);
    len := ArtikelComboBox.Canvas.TextWidth (textstr);

    if (len > armaxlen) then
      armaxlen := len;

    textstr := ArtikelComboBox.GetItemText (i, 2);
    len := ArtikelComboBox.Canvas.TextWidth (textstr);

    if (len > vpemaxlen) then
      vpemaxlen := len;

    if (ArtikelComboBox.ColumeCount = 4) then begin
      textstr := ArtikelComboBox.GetItemText (i, 3);
      len := ArtikelComboBox.Canvas.TextWidth (textstr);

      if (len > collimaxlen) then
        collimaxlen := len;
    end;
  end;

  //Dann werden die drei Punkte nicht mehr dargestellt
  armaxlen  := armaxlen + 8;
  vpemaxlen := vpemaxlen + 8;

  wbox := ArtikelComboBox.ColWidths [0] + armaxlen + vpemaxlen + collimaxlen + 32;

  ArtikelComboBox.ColWidths [1] := ArtikelComboBox.ColWidths [0] + armaxlen;

  if (collimaxlen > 0) then
    ArtikelComboBox.ColWidths [2] := ArtikelComboBox.ColWidths [1] + vpemaxlen;

  ArtikelComboBox.Perform(CB_SETDROPPEDWIDTH, wbox, 0);
end;

end.
