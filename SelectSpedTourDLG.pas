﻿unit SelectSpedTourDLG;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Data.DB, Vcl.StdCtrls, Vcl.Grids, Vcl.DBGrids, SMDBGrid, DBGridPro, MemDS,
  DBAccess, Ora, OraSmart;

type
  TSelectSpedTourForm = class(TForm)
    Panel1: TPanel;
    TourDBGrid: TDBGridPro;
    NewButton: TButton;
    OkButton: TButton;
    Button3: TButton;
    TourDataSource: TOraDataSource;
    TourQuery: TSmartQuery;
    procedure FormShow(Sender: TObject);
    procedure NewButtonClick(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
  private
    fRefLager : Integer;
    fRefTour  : Integer;

    fNewFlag  : boolean;
  public
    property RefLager : Integer read fRefLager write fRefLager;
    property RefTour : Integer read fRefTour write fRefTour;
  end;

implementation

{$R *.dfm}

uses
  SprachModul, DatenModul, DBGridUtilModule, ConfigModul;

procedure TSelectSpedTourForm.NewButtonClick(Sender: TObject);
begin
  fNewFlag     := true;
  ModalResult := mrOk;
end;

procedure TSelectSpedTourForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  TourQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

procedure TSelectSpedTourForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := true
  else if (fNewFlag) then begin
    fRefTour := -1;
    CanClose := true;
  end else begin
    CanClose := true;

    if (TourQuery.RecordCount = 0) then
      fRefTour := -1
    else
      fRefTour := TourQuery.FieldByName ('REF').AsInteger;
  end;
end;

procedure TSelectSpedTourForm.FormCreate(Sender: TObject);
begin
  fNewFlag := false;
  fRefTour := -1;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
  {$endif}
end;

procedure TSelectSpedTourForm.FormShow(Sender: TObject);
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.RestoreFormInfo (Self);

  TourQuery.Session := LVSDatenModul.OraMainSession;

  TourQuery.SQL.Clear;
  TourQuery.SQL.Add ('select * from V_PCD_AUFTRAG_SPED_TOUR where STATUS=''ANG'' and REF_LAGER=:ref_lager');
  TourQuery.Params [0].Value := fRefLager;

  TourQuery.Open;
end;

end.
