object HACCPCheckForm: THACCPCheckForm
  Left = 639
  Top = 137
  BorderIcons = [biSystemMenu]
  Caption = 'Pr'#252'fungen'
  ClientHeight = 347
  ClientWidth = 669
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OnCreate = FormCreate
  OnShow = FormShow
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 669
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label1: TLabel
      Left = 16
      Top = 8
      Width = 32
      Height = 13
      Caption = 'Label1'
    end
    object Label2: TLabel
      Left = 16
      Top = 32
      Width = 32
      Height = 13
      Caption = 'Label2'
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 273
    Width = 669
    Height = 74
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      669
      74)
    object FehlerLabel: TLabel
      Left = 8
      Top = 8
      Width = 654
      Height = 18
      Alignment = taCenter
      Anchors = [akLeft, akTop, akRight]
      AutoSize = False
      Caption = 'FehlerLabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object OkButton: TButton
      Left = 499
      Top = 38
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Ok'
      Default = True
      TabOrder = 0
      OnClick = OkButtonClick
      ExplicitTop = 37
    end
    object AbortButton: TButton
      Left = 587
      Top = 38
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
      ExplicitTop = 37
    end
  end
  object ScrollBox1: TScrollBox
    Left = 0
    Top = 57
    Width = 669
    Height = 216
    Align = alClient
    BevelInner = bvNone
    BevelOuter = bvNone
    TabOrder = 2
    object HACCPPanel: TPanel
      Left = 0
      Top = 0
      Width = 644
      Height = 207
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 0
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 392
    Top = 16
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 360
    Top = 16
  end
end
