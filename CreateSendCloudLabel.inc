﻿  //******************************************************************************
  //* Function Name: CreateSendCloudLabel
  //* Author       : <PERSON>
  //* Datum        : 03.08.2020
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateSendCloudLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    idx,
    res,
    ref,
    keyidx,
    errcode  : Integer;
    errtext  : String;
    xmlstr   : String;
    urlparam : String;
    body     : String;
    resp     : String;
    hdr      : String;
    nve      : String;
    labelurl : String;
    nrstr    : String;
    orderid  : String;
    timestr  : String;
    user     : String;
    pw       : String;
    gwstr    : String;
    prstr    : String;
    spedid,
    carrier,
    spedname : String;
    keystr,
    streetstr,
    vorstr,
    nachstr  : String;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    js       : TlkJSONobject;
    fs,
    us,
    errfs    : TlkJSONbase;
    cfgquery : TSmartQuery;
    olddec   : Char;
    found    : boolean;
  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      ErrorText := '';

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select ACCOUNT_NR,SUB_ACCOUNT_NR,SERVICE from V_SPED_VERSAND_CONFIG where REF_SPED=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED').AsInteger;

        cfgquery.Open;

        spedid   := cfgquery.Fields [0].AsString;
        spedname := cfgquery.Fields [1].AsString;
        carrier  := cfgquery.Fields [2].AsString;

        cfgquery.Close;

        if (Length (spedid) = 0) then
          ErrorText := 'No shipper id'
        else if (Length (spedname) = 0) then
          ErrorText := 'No shipper name'
        else if (Length (carrier) = 0) then
          ErrorText := 'No carrier'
        else begin
          cfgquery.SQL.Clear;
          cfgquery.SQL.Add ('select * from V_SPED_GATEWAY where REF=:ref');
          cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

          cfgquery.Open;

          if not Assigned (cfgquery.FindField('API_KEY')) then
            ErrorText := 'Not perpared for api key'
          else if cfgquery.FieldByName('API_KEY').IsNull then
            ErrorText := 'No api key'
          else
            keystr := cfgquery.FieldByName('API_KEY').AsString;

          cfgquery.Close;
        end;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        //Die Zugangsdaten stehen im API_KEY
        strlist := TStringList.Create;
        try
          strlist.Delimiter := ';';
          strlist.StrictDelimiter := true;
          strlist.DelimitedText := keystr;

          if (strlist.Count < 2) then
            res := 24
          else begin
            user := strlist [0];     //SIGLA SEDE
            pw   := strlist [1];     //CODICE CLIENTE
          end;
        finally
          strlist.Free;
        end;
     end;

     if (res = 0) then begin
        sdata := TMemoryStream.Create;

        try
          nve := query.FieldByName('NVE_NR').AsString;

          if (query.FieldByName('COUNT_FREIGABE').AsInteger > 1) then
            orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('COUNT_FREIGABE').AsString
          else if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
            orderid := query.FieldByName('AUFTRAG_NR').AsString
          else
            orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;

          if Assigned (SendITLog) then begin
            SendITLog.Logging (clNormal, 'Versand: Sendcloud, RefNVE: '+IntToStr (query.FieldByName('REF_NVE').AsInteger)+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
          end;

          ForceDirectories(DatenPath + RESTDumpDir+'Sendcloud\'+FormatDateTime ('yyyymmdd', Now));

          sdata.Position := 0;
          sdata.SaveToFile(DatenPath + RESTDumpDir+'Sendcloud\'+FormatDateTime ('yyyymmdd', Now)+'\post_orders_'+nve+'.json');

          //Token erzeugen
          urlparam := '';

          body := '{'+
                  '  "parcel":{';

          nrstr  := '';
          streetstr := liefquery.FieldByName ('STRASSE').AsString;

          idx := Length (streetstr);
          while (idx > 1) and (streetstr [idx] <> ' ') do
            Dec (idx);

          if (idx > 1) then begin
            nrstr := copy (streetstr, idx + 1);
            streetstr := Copy (streetstr, 1, idx);
          end;

          ExtractVorUndNachname (vorstr, nachstr);

          if (liefquery.FieldByName('COMPANY').IsNull) then begin
            body := body + '  "name": "'+ ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('NAME1').AsString, ';', ',', [rfReplaceAll]))+'",';
            body := body + '  "company_name": "",'
          end else begin
            body := body + '  "name": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('NAME2').AsString, ';', ',', [rfReplaceAll]))+'",';
            body := body + '  "company_name": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('COMPANY').AsString, ';', ',', [rfReplaceAll]))+'",';
          end;

          if not (kepemail) or adrquery.FieldByName('EMAIL').IsNull then
            body := body + '  "email": "'+ConvertJSONSonderzeichen (StringReplace (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString, ';', ',', [rfReplaceAll]))+'",'
          else
            body := body + '  "email": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll]))+'",';

          body := body + '  "address": "'+ConvertJSONSonderzeichen (StringReplace (streetstr, ';', ',', [rfReplaceAll]))+'",'+
                         '  "house_number": "'+ConvertJSONSonderzeichen (nrstr)+'",'+
                         '  "address_2": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]))+'",'+
                         '  "postal_code": "'+liefquery.FieldByName('PLZ').AsString+'",'+
                         '  "city": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('ORT').AsString, ';', ',', [rfReplaceAll]))+'",'+
                         '  "country": "'+landstr+'",'+
                         '  "telephone": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll]))+'",';

          gwstr := FormatFloat ('0.0', query.FieldByName ('BRUTTO_GEWICHT').AsFloat);

          body := body + '  "external_reference": "'+nve+'",'+
                         '  "order_number": "'+orderid+'",'+
                         '  "weight": "'+gwstr+'",'+
                         '  "request_label":"true",'+
                         '  "quantity":1,'+
                         '  "shipment": {'+
                         '    "id": '+spedid+
                         //'    ",name": "'+spedname+'"'+
                         //'    ",carrier": "'+carrier+'"'+
                         '  }';

          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            //Die Zollinfos der Artikel
            //Alle Bestände auf der NVE, welche keine Textartikel sind, das Gewicht ist das Artikelgewicht
            cfgquery.SQL.Add('select mcfg.OPT_AUTO_CUSTOMS,ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN'') as ARTIKEL_TEXT,bes.MENGE,vpe.KURZ_BEZEICHNUNG,ar.COUNTRY_OF_ORIGIN'
                            +',nvl (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER) as TARIC_NUMBER,nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT)/1000 as GEWICHT'
                            +',nvl (rep.NETTO_BETRAG,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT) / 1000 as NETTO_BETRAG'
                            +' from V_LAGER_NVE_BESTAND bes, VQ_AUFTRAG auf, V_AUFTRAG_RECHNUNG re, V_AUFTRAG_POS pos, V_AUFTRAG_POS_RECHNUNG rep, V_ARTIKEL ar, VQ_ARTIKEL_EINHEIT ae, V_ARTIKEL_VPE vpe, V_MANDANT_CONFIG mcfg'
                            +' where'
                            +' pos.REF=bes.REF_AUF_POS and auf.REF=pos.REF_AUF_KOPF'
                            +' and ar.REF=pos.REF_AR and ae.REF=pos.REF_AR_EINHEIT and vpe.REF=ae.REF_EINHEIT'
                            +' and mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND)'
                            +' and pos.REF_PARENT_POS is null and re.REF_AUF_KOPF=auf.REF and rep.REF_AUF_POS=pos.REF and nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0'
                            +' and bes.REF_NVE=:ref'
                            );
            cfgquery.Params [0].Value := query.FieldByName('REF_MAIN_NVE').AsInteger;

            cfgquery.Open;

            body := body + '  ,"parcel_items":[';

            while not (cfgquery.Eof) do begin
              if (cfgquery.RecNo > 1) then body := body + ',';

              if (cfgquery.FieldByName ('GEWICHT').IsNull or (cfgquery.FieldByName ('GEWICHT').AsFloat < 0.01)) then
                gwstr := '0.01'
              else
                gwstr := FormatFloat ('0.0', cfgquery.FieldByName ('GEWICHT').AsFloat);

              if (cfgquery.FieldByName ('NETTO_BETRAG').IsNull or (cfgquery.FieldByName ('NETTO_BETRAG').AsFloat < 0.01)) then
                prstr := '0.01'
              else
                prstr := FormatFloat ('0.0', cfgquery.FieldByName ('NETTO_BETRAG').AsFloat);

              body := body + '  {'+
                             '    "hs_code": "'+ConvertJSONSonderzeichen (StringReplace (cfgquery.FieldByName('TARIC_NUMBER').AsString, ';', ',', [rfReplaceAll]))+'",'+
                             '    "description": "'+ConvertJSONSonderzeichen (StringReplace (cfgquery.FieldByName('ARTIKEL_TEXT').AsString, ';', ',', [rfReplaceAll]))+'",'+
                             '    "quantity": '+cfgquery.FieldByName('MENGE').AsString+','+
                             '    "weight": '+gwstr+','+
                             '    "value": '+prstr+','+
                             '    "sku": "'+ConvertJSONSonderzeichen (StringReplace (cfgquery.FieldByName('ARTIKEL_NR').AsString, ';', ',', [rfReplaceAll]))+'",'+
                             '    "origin_country": "'+ConvertJSONSonderzeichen (StringReplace (cfgquery.FieldByName('COUNTRY_OF_ORIGIN').AsString, ';', ',', [rfReplaceAll]))+'"'+
                             '  }';

              cfgquery.Next;
            end;

            body := body + ']';

            cfgquery.Close;
          finally
            cfgquery.Free;
          end;


          body := body + '  }'+
                         '}';

          StrToFile (DatenPath + RESTDumpDir+'Sendcloud\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_'+nve+'.json', body);


          sdata.Clear;
          if SendRequest('panel.sendcloud.sc', // Host,
                          -1, //Port
                          'api/v2/parcels', // Service
                          'POST', //Methode
                          '', // Proxy,
                          user, pw, // User , PW
                          '', //Action
                          'application/json', //ContentType
                          [hdr], //AddHeader
                          StringUtils.StringToUTF (body),         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            StrToFile (DatenPath + RESTDumpDir+'Sendcloud\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_resp_'+nve+'.txt', resp);

            labelurl := '';

            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+'Sendcloud\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nve+'.json');

            sdata.Position := 0;
            js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;

            if not Assigned (js) then begin
              res := 11;
              ErrorText := 'API error message';
            end else begin
              try
                us := nil;

                fs := js.Field['parcel'];
                if Assigned (fs) then
                  us := fs.Field['tracking_number'];

                if not Assigned (fs) or not Assigned (us) then begin
                  res := 15;
                  ErrorText := 'tracking_number code error';

                  fs := js.Field['error'];
                  if Assigned (fs) then begin
                    us := fs.Field['message'];

                    if Assigned (us) then
                      ErrorText := ErrorText + ':' + StringReplace (us.Value, '\u0027', '''', [rfReplaceAll]);
                  end;
                end else begin
                  SendungsNr := us.Value;

                  us := fs.Field['id'];

                  if not Assigned (us) then begin
                    res := 16;
                    ErrorText := 'id error'
                  end else begin
                    body := '';
                    LabelImage.Clear;

                    SendungsID := us.Value;

                    if SendRequest('panel.sendcloud.sc', // Host,
                                    -1, //Port
                                    'api/v2/parcels/'+SendungsID+'/documents/label', // Service
                                    'GET', //Methode
                                    '', // Proxy,
                                    user, pw, // User , PW
                                    '', //Action
                                    'application/json', //ContentType
                                    ['Accept: '+'application/zpl'], //AddHeader
                                    body,         // RequestData
                                    resp,
                                    LabelImage, //ResponseStream
                                    errcode, // Fehlercode
                                    errtext) // Fehlertext
                    then begin
                      StrToFile (DatenPath + RESTDumpDir+'Sendcloud\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_resp_'+nve+'.txt', resp);

                      if (Pos ('404 Not Found', resp) > 0) then begin
                        res := 26;
                        ErrorText := 'No label found';
                      end else begin
                        if (Pos ('application/zpl', resp) > 0) then
                          LabelFormat := 'zpl'
                        else if (Pos ('application/pdf', resp) > 0)  then
                          LabelFormat := 'pdf'
                        else
                          LabelFormat := '###';

                        ForceDirectories(DatenPath + LabelDumpDir + 'Sendcloud');

                        LabelImage.Position := 0;

                        try
                          LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'Sendcloud\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                        except
                        end;

                        if Assigned (SendITLog) then begin
                          SendITLog.Logging (clNormal, 'Versand: Sendcloud, RefNVE: '+IntToStr (query.FieldByName('REF_NVE').AsInteger)+', Versender: '+Versender+', SendungsNr: '+SendungsNr);
                        end;
                      end;
                    end else begin
                      res := 25;

                      ErrorText := 'Download error '+IntToStr (errcode)+' ('+errtext;
                    end;
                  end;
                end;
              except
                res := 10;

                ErrorText := 'Label data error';
              end;
            end;
          end else begin
            res := 11;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: Sendcloud, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;
