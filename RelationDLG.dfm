object RelationForm: TRelationForm
  Left = 375
  Top = 239
  BorderIcons = [biSystemMenu]
  Caption = 'Touren und Warenausgangs-Relationen'
  ClientHeight = 407
  ClientWidth = 717
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnKeyDown = FormKeyDown
  OnShow = FormShow
  DesignSize = (
    717
    407)
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 51
    Height = 13
    Caption = 'Relationen'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 368
    Width = 705
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 400
    ExplicitWidth = 570
  end
  object WARelDBGrid: TDBGridPro
    Left = 10
    Top = 27
    Width = 574
    Height = 326
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = WARelDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    OnDblClick = WARelDBGridDblClick
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object CloseButton: TButton
    Left = 590
    Top = 377
    Width = 122
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Schlie'#223'en'
    ModalResult = 1
    TabOrder = 6
  end
  object RelNewButton: TButton
    Left = 590
    Top = 27
    Width = 122
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Neu...'
    TabOrder = 1
    OnClick = RelNewButtonClick
  end
  object RelDelButton: TButton
    Left = 590
    Top = 99
    Width = 122
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'L'#246'schen...'
    TabOrder = 3
    OnClick = RelDelButtonClick
  end
  object RelChangeButton: TButton
    Left = 590
    Top = 59
    Width = 122
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Bearbeiten...'
    TabOrder = 2
    OnClick = WARelDBGridDblClick
  end
  object RelPrtPalButton: TButton
    Left = 590
    Top = 328
    Width = 122
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Relationslabel drucken'
    TabOrder = 4
    OnClick = RelPrtPalButtonClick
  end
  object RelPrtLPButton: TButton
    Left = 590
    Top = 296
    Width = 122
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'WA-Platzlabel drucken'
    TabOrder = 5
    OnClick = RelPrtPalButtonClick
  end
  object WARelADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 448
    Top = 16
  end
  object WARelDataSource: TDataSource
    DataSet = WARelADOQuery
    OnDataChange = WARelDataSourceDataChange
    Left = 408
    Top = 16
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 536
    Top = 72
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 384
    Top = 56
  end
end
