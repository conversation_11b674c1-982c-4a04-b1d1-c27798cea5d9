﻿unit CreateVersandLabel;

{$i compilers.inc}

interface

uses
  Classes, PrinterUtils, LogFile, DB, Ora, OraSmart;

const
  RESTDumpDir       = 'Labels\soap\';
  LabelDumpDir      = 'Labels\';
  SENDIT            = 'SendIT';
  BARCODE_SHIPPING  = 'BarcodeShipping';

var
  SendITServerPath : String = '';
  SendITServerURL  : String = '';
  VCEServerURL     : String = '';
  DatenPath        : String = '';
  SendITLog        : TLogFile = nil;
  RequesteFileName : String;
  RequesteRetFileName : String;
  LabelUserNumID  : String = '';
  LabelClientName : String;
  AktSendITIFCImpVersion    : Integer = 1;

procedure ResetVersandSystem;
function  CheckSendITPath (const PathName : String; var ErrorText : String) : Integer;

function StartPrintVersandLabel (Query : TSmartQuery; const LabelType : String; const PrtInfo : TPrinterPorts; var DoneFlag, ResponsFlag : Boolean; var VersandApp, Versender, SendungsID, SendungsNr, TrackUrl, Barcode, LabelFormat : String; LabelImage : TMemoryStream; var RefLabel : Integer; var ErrorText : String) : Integer;

implementation

uses
  {$ifdef Trace}
   Trace,
  {$endif}

  System.NetEncoding,

  Windows, Math, SysUtils, DateUtils, Variants, EncdDecd, StringUtils, VCLUtilitys,
  xml_base, xml_parser, xml_utils, uLkJSON, Timers,
  SendHTTPRequest

  {$ifdef ErrorTracking}
    ,ErrorTracking
  {$endif}

  {$ifdef ResourceText}
    ,ResourceText
  {$endif}

  {$ifdef UNICODE}
    ,WideStrUtils
  {$endif}

  {$ifdef LVS}
    ,PrintModul
  {$endif}

  ,DatenModul,LVSDatenInterface;

type
  TAccessTokens = record
    AccessKey    : String;
    AccessToken  : String;
    CreateAt     : TDateTime;
  end;


  TURLEntry = record
    URL     : String;
    Port    : Integer;
    Scheme  : String;
    Host    : String;
    Path    : String;
  end;

var
  NonEUList       : TStringList;

  fRESTToken : array [0..31] of TAccessTokens;

  SendITMapping : TXMLFile;

  AktSendITPath             : String  = '';
  AktSendITIFCExpVersion    : Integer = 1;
  AktSendITIFCImpSubVersion : Integer = 0;
  AktSendITIFCAutoReprint   : Boolean = False;
  AktSendITIFCAbsender      : Boolean = True;
  AktSendITIFCCombineID     : Boolean = False;
  AktSendITIFCExtDHL2Mann   : Boolean = False;
  AktSendITIFCHausnummer    : Boolean = False;
  AktSendITIFCFilialRouting : Boolean = False;
  AktSendITIFCAblageOrt     : Boolean = False;
  AktSendITIFCZoll          : Integer = 0;
  AktSendITIFCIncoterm      : Integer = 0;


procedure DecodeURL (const URL:String; var URLEntry : TURLEntry);
var
  urlstr,
  hoststr  : String;
  idx,
  strpos,
  intwert : Integer;
begin
  URLEntry.URL := URL;

  URLEntry.Port   := -1;
  URLEntry.Scheme := '';
  URLEntry.Host   := '';
  URLEntry.Path   := '';

  if (copy (lowercase (URLEntry.URL), 1, 8) = 'https://') then begin
    urlstr := copy (URLEntry.URL, 9);
    URLEntry.Scheme := 'https://';
  end else if (copy (lowercase (URLEntry.URL), 1, 7) = 'http://') then  begin
    urlstr := copy (URLEntry.URL, 8);
    URLEntry.Scheme := 'http://';
  end else begin
    urlstr := URLEntry.URL;
  end;

  hoststr := urlstr;

  idx := 1;
  while (idx <= Length (hoststr)) do begin
    if (hoststr [idx] = '/') then begin
      URLEntry.Path := copy (hoststr, idx + 1);

      hoststr := copy (hoststr, 1, idx - 1);

      break;
    end;

    Inc (idx);
  end;

  strpos := Pos (':', hoststr);

  if (strpos > 0) then begin
    URLEntry.Host := copy (hoststr, 1, strpos - 1);

    if TryStrToInt (copy (hoststr, strpos + 1), intwert) then
      URLEntry.Port := intwert;
  end else begin
    URLEntry.Host := hoststr;
  end;

end;

//****************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//****************************************************************************
//* Description
//****************************************************************************
//* Return Value :
//****************************************************************************
procedure ResetVersandSystem;
var
  i : Integer;
begin
  AktSendITPath           := '';
  AktSendITIFCImpVersion  := 1;
  AktSendITIFCExpVersion  := 1;
  AktSendITIFCAutoReprint := False;
  AktSendITIFCAbsender    := True;
  AktSendITIFCCombineID   := False;
  AktSendITIFCExtDHL2Mann := False;
  AktSendITIFCHausnummer  := False;
  AktSendITIFCFilialRouting := False;
  AktSendITIFCAblageOrt     := False;
  AktSendITIFCZoll          := 0;
  AktSendITIFCIncoterm      := 0;

  for i := 0 to 31 do fRESTToken [i].AccessKey := '';

  if Assigned (SendITMapping) then
    SendITMapping.Free;
  SendITMapping := nil;
end;

//****************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//****************************************************************************
//* Description
//****************************************************************************
//* Return Value :
//****************************************************************************
function CleanupResponse (const FileName : String) : Integer;
var
  fres : Integer;
  srec : TSearchRec;
begin
  {$ifdef Trace}
    FunctionStart ('CleanupResponse');
    TraceParameter ('FileName', FileName);
  {$endif}

  if not (DirectoryExists (SendITServerPath + 'Export\')) then begin
    Result := -1;
  end else begin
    fres := FindFirst (SendITServerPath + 'Export\*' + FileName + '.*', faArchive, srec);

    if (fres = 0) then begin
      while (fres = 0) do begin
        DeleteFile (SendITServerPath + 'Export\' + srec.Name);

        fres := FindNext (srec);
      end;

      FindClose (srec);
    end;

    Result := 0;
  end;
end;

//****************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//****************************************************************************
//* Description
//****************************************************************************
//* Return Value :
//****************************************************************************
function CheckSendITPath (const PathName : String; var ErrorText : String) : Integer;
var
  res       : Integer;
  fstream   : TFileStream;
  strpos,
  readanz   : Integer;
  inbuf     : array [0..512] of AnsiChar;
begin
  res := 0;

  if (Length (AktSendITPath) = 0) or (AktSendITPath <> UpperCase (PathName)) then begin
    if not (DirectoryExists (PathName + '\Import')) then begin
      res := -2;
      ErrorText := 'Kein SendIT Import-Verzeichnis gefunden';
    end else begin
      AktSendITIFCImpVersion    := 1;
      AktSendITIFCImpSubVersion := 0;
      AktSendITIFCExpVersion    := 1;
      AktSendITIFCAutoReprint   := False;
      AktSendITIFCAbsender      := True;
      AktSendITIFCHausnummer    := False;
      AktSendITIFCFilialRouting := False;
      AktSendITIFCAblageOrt     := False;
      AktSendITIFCZoll          := 0;
      AktSendITIFCIncoterm      := 0;

      if Assigned (SendITMapping) then
        SendITMapping.Free;

      SendITMapping := Nil;

      //Die Datei erstmal vollständig erzeugen
      try
        fstream := TFileStream.Create (PathName + '\Import\version.ver', fmOpenRead);
      except
        fstream := Nil;
      end;

      if Assigned (fstream) then begin
        readanz := fstream.Read (inbuf, sizeof (inbuf));

        if (readanz > 4) then begin
          if ((inbuf [0] = 'V') and (inbuf [1] = 'E')  and (inbuf [2] = 'R') and (inbuf [3] = '=')) then begin
            strpos := 4;

            AktSendITIFCImpVersion := 0;

            while (strpos <= Length (inbuf)) and (CharInSet (inbuf [strpos], ['0'..'9'])) do begin
              AktSendITIFCImpVersion := AktSendITIFCImpVersion * 10 + (Ord (inbuf [strpos]) - Ord ('0'));

              Inc (strpos);
            end;

            if (strpos <= Length (inbuf)) then begin
              if (inbuf [strpos] = '.') and (Length (inbuf) > strpos) then begin
                AktSendITIFCImpSubVersion := Ord (inbuf [strpos + 1]) - Ord ('0');

                Inc (strpos, 2);
              end;
            end;

            if (strpos <= Length (inbuf)) then begin
              //Option 1
              if (inbuf [strpos] = ',') and (Length (inbuf) > strpos + 1) then begin
                if (inbuf [strpos + 1] = ',') then
                  strpos := strpos + 1
                else begin
                  if (inbuf [strpos + 1] = '1') then
                    AktSendITIFCAutoReprint := true;

                  strpos := strpos + 2;
                end;
              end;
            end;

            if (strpos <= Length (inbuf)) then begin
              //Option 2
              if (inbuf [strpos] = ',') and (Length (inbuf) > strpos + 1) then begin
                if (inbuf [strpos + 1] = ',') then
                  strpos := strpos + 1
                else begin
                  if (inbuf [strpos + 1] = '1') then
                    AktSendITIFCCombineID := true;
                  strpos := strpos + 2;
                end;
              end;
            end;

            if (strpos <= Length (inbuf)) then begin
              //Option 3
              if (inbuf [strpos] = ',') and (Length (inbuf) > strpos + 1) then begin
                if (inbuf [strpos + 1] = ',') then
                  strpos := strpos + 1
                else begin
                  if (inbuf [strpos + 1] = '1') then
                    AktSendITIFCExtDHL2Mann := true;
                  strpos := strpos + 2;
                end;
              end;
            end;

            if (strpos <= Length (inbuf)) then begin
              //Option 4 : Hausnummer -> ShipToHouseNr
              if (inbuf [strpos] = ',') and (Length (inbuf) > strpos + 1) then begin
                if (inbuf [strpos + 1] = ',') then
                  strpos := strpos + 1
                else begin
                  if (inbuf [strpos + 1] = '1') then
                    AktSendITIFCHausnummer := true;
                  strpos := strpos + 2;
                end;
              end;
            end;

            if (strpos <= Length (inbuf)) then begin
              //Option 5 : FilialRouting -> AdvanceInstructions, AdvanceInstructionsEMail
              if (inbuf [strpos] = ',') and (Length (inbuf) > strpos + 1) then begin
                if (inbuf [strpos + 1] = ',') then
                  strpos := strpos + 1
                else begin
                  if (inbuf [strpos + 1] = '1') then
                    AktSendITIFCFilialRouting := true;
                  strpos := strpos + 2;
                end;
              end;
            end;

            if (strpos <= Length (inbuf)) then begin
              //Option 6 : AblageOrt -> SafePlace
              if (inbuf [strpos] = ',') and (Length (inbuf) > strpos + 1) then begin
                if (inbuf [strpos + 1] = ',') then
                  strpos := strpos + 1
                else begin
                  if (inbuf [strpos + 1] = '1') then
                    AktSendITIFCAblageOrt := true;
                  strpos := strpos + 2;
                end;
              end;
            end;

            if (strpos <= Length (inbuf)) then begin
              //Option 7 : Incoterm und Zoll -> Incoterm, Anzahl Zoll-Einträge
              if (inbuf [strpos] = ',') and (Length (inbuf) > strpos + 1) then begin
                if (inbuf [strpos + 1] = ',') then
                  strpos := strpos + 1
                else begin
                  if (inbuf [strpos + 1] > '0') then
                    AktSendITIFCZoll := ord (inbuf [strpos + 1]) - ord ('0');
                  strpos := strpos + 2;
                end;
              end;
            end;

            if (strpos <= Length (inbuf)) then begin
              //Option 8 : Incoterm un Payer
              if (inbuf [strpos] = ',') and (Length (inbuf) > strpos + 1) then begin
                if (inbuf [strpos + 1] = ',') then
                  strpos := strpos + 1
                else begin
                  if (inbuf [strpos + 1] > '0') then
                    AktSendITIFCIncoterm := ord (inbuf [strpos + 1]) - ord ('0');
                  strpos := strpos + 2;
                end;
              end;
            end;
          end;
        end;

        fstream.Free;
      end;

      if (res = 0) then begin
        AktSendITPath := UpperCase (PathName);

        //Die Datei erstmal vollständig erzeugen
        try
          fstream := TFileStream.Create (PathName + '\SendIT.BlackBoxService\MappingCSV.config', fmOpenRead);
        except
          fstream := Nil;
        end;

        if Assigned (fstream) then begin
          SendITMapping := TXMLFile.Create(Nil);

          SendITMapping.ParseXMLFile (fstream);

          fstream.Free;
        end;
      end;

    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 01.10.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function StrToFile(const FileName, SourceString : string; const Endcoding : Boolean = true) : Integer overload;
var
  res    : Integer;
  dirstr : String;
  outstr : AnsiString;
  Stream : TFileStream;
begin
  res := ERROR_SUCCESS;

  dirstr := ExtractFileDir(FileName);

  if (Length (dirstr) > 0) and not (DirectoryExists (dirstr)) then begin
    try
      ForceDirectories (dirstr);
    except
      res := GetLastError;
    end;
  end;

  if (res = 0) then begin
    try
      Stream:= TFileStream.Create(FileName, fmCreate);

      try
        if not (Endcoding) then
          Stream.WriteBuffer(Pointer(SourceString)^, Length(SourceString))
        else begin
          outstr := UTF8Encode (SourceString);

          Stream.WriteBuffer(Pointer(outstr)^, Length(outstr));
        end;
      finally
        Stream.Free;
      end;
    except
      res := GetLastError;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 01.10.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function StrToFile(const FileName : string; const SourceString : UTF8String; const Endcoding : Boolean = true) : Integer overload;
var
  res    : Integer;
  dirstr : String;
  outstr : AnsiString;
  Stream : TFileStream;
begin
  res := ERROR_SUCCESS;

  dirstr := ExtractFileDir(FileName);

  if (Length (dirstr) > 0) and not (DirectoryExists (dirstr)) then begin
    try
      ForceDirectories (dirstr);
    except
      res := GetLastError;
    end;
  end;

  if (res = 0) then begin
    try
      Stream:= TFileStream.Create(FileName, fmCreate);

      try
        if not (Endcoding) then
          Stream.WriteBuffer(Pointer(SourceString)^, Length(SourceString))
        else begin
          outstr := SourceString;

          Stream.WriteBuffer(Pointer(outstr)^, Length(outstr));
        end;
      finally
        Stream.Free;
      end;
    except
      res := GetLastError;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: IsLandEU
//* Author       : Stefan Graf
//* Datum        : 15.10.2014
//******************************************************************************
//* Description  :  Prüfen ob das Land zur EU gehört
//******************************************************************************
//* Return Value :
//******************************************************************************
function IsLandEU (const ISOLand : String; Query : TSmartQuery = Nil; const ZIPCode : String = '') : Boolean;
var
  res      : Boolean;
  idx      : Integer;
  selquery : TSmartQuery;
  csvlist  : TStringList;
begin
  if (Length (ISOLand) = 0) then
    res := true
  else if (ISOLand = 'BE') then
    res := true
  else if (ISOLand = 'BG') then
    res := true
  else if (ISOLand = 'CZ') then
    res := true
  else if (ISOLand = 'DK') then
    res := true
  else if (ISOLand = 'DE') then
    res := true
  else if (ISOLand = 'EE') then
    res := true
  else if (ISOLand = 'IE') then
    res := true
  else if (ISOLand = 'EL') then
    res := true
  else if (ISOLand = 'ES') then
    res := true
  else if (ISOLand = 'FR') then
    res := true
  else if (ISOLand = 'HR') then
    res := true
  else if (ISOLand = 'IT') then
    res := true
  else if (ISOLand = 'CY') then
    res := true
  else if (ISOLand = 'LV') then
    res := true
  else if (ISOLand = 'LT') then
    res := true
  else if (ISOLand = 'LU') then
    res := true
  else if (ISOLand = 'HU') then
    res := true
  else if (ISOLand = 'MT') then
    res := true
  else if (ISOLand = 'NL') then
    res := true
  else if (ISOLand = 'AT') then
    res := true
  else if (ISOLand = 'PL') then
    res := true
  else if (ISOLand = 'PT') then
    res := true
  else if (ISOLand = 'RO') then
    res := true
  else if (ISOLand = 'SI') then
    res := true
  else if (ISOLand = 'SK') then
    res := true
  else if (ISOLand = 'FI') then
    res := true
  else if (ISOLand = 'SE') then
    res := true
  else if (ISOLand = 'GR') then
    res := true
  (*Großbritannien ist ab 1.1.2021 nicht mehr in der EU
  else if (ISOLand = 'GB') or (ISOLand = 'UK') then begin
    res := true;

    //Prüfen, ob der Brexit erfolgt ist
    if (ReadConfigValue ('Brexit', cfgint, cfgstr) = 0) and (cfgint = 1) then
      res := false;
  end
  *)
  else
    res := false;

  if res and Assigned (Query) and (Length (ZIPCode) > 0) then begin
    if not Assigned (NonEUList) then begin
      NonEUList := TStringList.Create;

      selquery := TSmartQuery.Create (Nil);

      try
        selquery.ReadOnly := True;
        selquery.Session := Query.Session;

        selquery.SQL.Add('select ISO2, ZIP_FROM, ZIP_TO from V_ISO_COUNTRY_NO_EU order by ISO2, ZIP_FROM, ZIP_TO');

        try
          selquery.Open;

          while not (selquery.Eof) do begin
            NonEUList.Add (selquery.Fields [0].AsString+';'+selquery.Fields [1].AsString+';'+selquery.Fields [2].AsString);

            selquery.Next;
          end;

          selquery.Close;
        except
        end;
      finally
        selquery.Free;
      end;
    end;

    csvlist := TStringList.Create;
    csvlist.Delimiter := ';';
    csvlist.StrictDelimiter := true;

    try
      idx := 0;

      while (idx < NonEUList.Count) do begin
        csvlist.DelimitedText := NonEUList [idx];

        if (csvlist [0] = ISOLand) Then begin
          if ((Length (csvlist [1]) = 0) and (Length (csvlist [2]) = 0)) then begin
            res := false;
            break;
          end else if (((Length (csvlist [1]) = 0) or (ZIPCode >= csvlist [1])) and ((Length (csvlist [2]) = 0) or (ZIPCode <= csvlist [2]))) then begin
            res := false;
            break;
          end;
        end;

        Inc (idx);
      end;
    finally
      csvlist.Free;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: DBGetReferenz
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert bei NULL -1, ansosnten den Integerwert
//******************************************************************************
//* Return Value :
//******************************************************************************
function DBGetReferenz (Field : TField) : Integer;
begin
  if (Field.IsNull) then
    Result := -1
  else
    Result := Field.AsInteger;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function StartPrintVersandLabel (Query : TSmartQuery; const LabelType : String; const PrtInfo : TPrinterPorts; var DoneFlag, ResponsFlag : Boolean; var VersandApp, Versender, SendungsID, SendungsNr, TrackUrl, Barcode, LabelFormat : String; LabelImage : TMemoryStream; var RefLabel : Integer; var ErrorText : String) : Integer;
var
  res       : Integer;
  zolldesc  : String;
  landstr   : String;     //Das Zielland als ISO-Code
  kepemail  : Boolean;    //True, Wenn die Mail-Adresse an den Versender übermittelt werden soll
  dataquery : TSmartQuery;
  adrquery  : TSmartQuery;
  liefquery : TSmartQuery;
  gatequery : TSmartQuery;

  //****************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //****************************************************************************
  //* Description
  //****************************************************************************
  //* Return Value :
  //****************************************************************************
  function GetSendungsReferenz : String;
  begin
    if (Pos ('REF=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      Result := query.FieldByName('KD_KOMM_NR').AsString
    else if (Pos ('REF=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      Result := query.FieldByName('AUF_REFERENZ').AsString
    else if (Pos ('REF=KD_BESTELL_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) and not query.FieldByName('KD_BESTELL_NR').IsNull then
      Result := query.FieldByName('KD_BESTELL_NR').AsString
    else if (Pos ('REF=KD_AUFTRAG_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) and not query.FieldByName('KD_AUFTRAG_NR').IsNull then
      Result := query.FieldByName('KD_AUFTRAG_NR').AsString
    else if (Pos ('REF=LIEFERSCHEIN_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      Result := query.FieldByName('LIEFERSCHEIN_NR').AsString
    else if (Pos ('REF=RECHNUNGS_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      Result := query.FieldByName('RECHNUNGS_NR').AsString
    else
      Result := query.FieldByName('AUFTRAG_NR').AsString;
  end;

  //****************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //****************************************************************************
  //* Description
  //****************************************************************************
  //* Return Value :
  //****************************************************************************
  function GetSendungsReferenz2 : String;
  begin
    if (Pos ('REF2=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      Result := query.FieldByName('KD_KOMM_NR').AsString
    else if (Pos ('REF2=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      Result := query.FieldByName('AUF_REFERENZ').AsString
    else if (Pos ('REF2=KD_BESTELL_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) and not query.FieldByName('KD_BESTELL_NR').IsNull then
      Result := query.FieldByName('KD_BESTELL_NR').AsString
    else if (Pos ('REF2=KD_AUFTRAG_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) and not query.FieldByName('KD_AUFTRAG_NR').IsNull then
      Result := query.FieldByName('KD_AUFTRAG_NR').AsString
    else if (Pos ('REF2=LIEFERSCHEIN_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      Result := query.FieldByName('LIEFERSCHEIN_NR').AsString
    else if (Pos ('REF2=RECHNUNGS_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      Result := query.FieldByName('AUF_REFERENZ').AsString
    else
      Result := '';
  end;

  //****************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //****************************************************************************
  //* Description
  //****************************************************************************
  //* Return Value :
  //****************************************************************************
  function ConvertSonderzeichen (const Line : String) : String;
  var
    idx : Integer;
  begin
    Result := '';

    idx := 1;

    while (idx <= Length (Line)) do begin
      if (Line [idx] = 'ß') and (landstr <> 'DE') then
        //ß gibt es nur in DE
        Result := Result + 'ss'
      else if (Line [idx] = #9) or (Line [idx] = #10) or (Line [idx] = #13) then
        //Führt sonst zu Problemen im CSV
        Result := Result + ' '
      else
        Result := Result + Line [idx];

      Inc (idx);
    end;

    //Das bringt SendIT sonst aus dem Tritt
   Result := StringReplace (Result, '&amp,', '&', [rfReplaceAll, rfIgnoreCase]);
  end;

  //****************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //****************************************************************************
  //* Description
  //****************************************************************************
  //* Return Value :
  //****************************************************************************
  function ConvertJSONSonderzeichen (const Line : String) : String;
  var
    idx : Integer;
  begin
    Result := '';

    idx := 1;

    while (idx <= Length (Line)) do begin
      if (Line [idx] = '"') then
        Result := Result + '\"'
      else if (Line [idx] = '\') then
        Result := Result + '\\'
      else if (Line [idx] = #$0a) then
        Result := Result + '\n'
      else if (Line [idx] = #$0d) then
        Result := Result + '\r'
      else if (Line [idx] = #$09) then
        Result := Result + '\t'
      (*
      else if (Line [idx] = 'ß') then
        Result := Result + 'ss'
        //Result := Result + CharToUTF (Line [idx])
      *)
      else
        Result := Result + Line [idx];

      Inc (idx);
    end;

    //Das bringt SendIT sonst aus dem Tritt
   Result := StringReplace (Result, '&', '&amp;', [rfReplaceAll, rfIgnoreCase]);
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  procedure ExtractVorUndNachname (var Vorname, Nachname : String);
  var
    idx     : Integer;
    namestr : String;
  begin
    if liefquery.FieldByName('NACHNAME').IsNull then begin
      if (liefquery.FieldByName('COMPANY').IsNull) then
        namestr := liefquery.FieldByName ('NAME1').AsString
      else
        namestr := liefquery.FieldByName ('NAME2').AsString;

      idx := Length (namestr);

      //Das erste Leerzeichen nach dem Nachnamen finden
      while (idx > 1) and (namestr [idx] <> ' ') do
        Dec (idx);

      if (idx > 1) then begin
        Nachname := copy (namestr, idx + 1);

        //Leerzeichen zwischen Vor- und Nachname entfernen
        while (idx > 1) and (namestr [idx] = ' ') do
          Dec (idx);

        Vorname := copy (namestr, 1, idx);
      end else begin
        Vorname  := '';
        Nachname := namestr;
      end;
    end else begin
      Vorname  := liefquery.FieldByName('VORNAME').AsString;
      Nachname := liefquery.FieldByName('NACHNAME').AsString;
    end;
  end;

  //******************************************************************************
  //* Function Name: CreateGLSITLabel
  //* Author       : Stefan Graf
  //* Datum        : 05.03.2021
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateGLSITLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    res,
    errcode  : Integer;
    errtext  : String;
    urlparam : String;
    body     : String;
    resp     : String;
    nve      : String;
    labelurl : String;
    orderid  : String;
    telstr   : String;
    tagstr   : String;
    gipw,
    giuser,
    gicode,
    gisede   : String;

    keystr   : String;
    outstr   : AnsiString;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    cfgquery : TSmartQuery;
    olddec   : Char;
    gw       : Double;
    xmlrd    : TXMLFile;
    xmlnd    : TXMLTagEntry;
    xmsnd    : TXMLTagEntry;
  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      TrackUrl  := '';
      Barcode   := '';
      ErrorText := '';

      if not Assigned (gatequery.FindField('API_KEY')) then
        ErrorText := 'Not perpared for api key'
      else if gatequery.FieldByName('API_KEY').IsNull then
        ErrorText := 'No api key'
      else
        keystr := gatequery.FieldByName('API_KEY').AsString;

      telstr := gatequery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        //Die Zugangsdaten stehen im API_KEY
        strlist := TStringList.Create;
        try
          strlist.Delimiter := ';';
          strlist.StrictDelimiter := true;
          strlist.DelimitedText := keystr;

          if (strlist.Count < 4) then
            res := 24
          else begin
            gisede := strlist [0];     //SIGLA SEDE
            giuser := strlist [1];     //CODICE CLIENTE
            gipw   := strlist [2];     //PASSWORD
            gicode := strlist [3];     //CONTRATTO
          end;
        finally
          strlist.Free;
        end;
     end;

     if (res = 0) then begin
        nve := query.FieldByName('NVE_NR').AsString;

        orderid := GetSendungsReferenz;

        if (query.FieldByName('PACKAGE_NR').AsInteger > 1) then
          orderid := orderid + '-' + query.FieldByName('PACKAGE_NR').AsString;

        if Assigned (SendITLog) then begin
          SendITLog.Logging (clNormal, 'Versand: GLS-IT, RefNVE: '+query.FieldByName('REF_NVE').AsString+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
        end;

        sdata := TMemoryStream.Create;

        try
          ForceDirectories(DatenPath + RESTDumpDir+'GLS_IT\'+FormatDateTime ('yyyymmdd', Now));

          //Token erzeugen
          urlparam := '';

          body := 'XMLInfoParcel='
                 +'<Info>'
                 +'  <SedeGls>'+gisede+'</SedeGls>'
                 +'  <CodiceClienteGls>'+giuser+'</CodiceClienteGls>'
                 +'  <PasswordClienteGls>'+gipw+'</PasswordClienteGls>'
                 +'  <Parcel>'
                 +'    <CodiceContrattoGls>'+gicode+'</CodiceContrattoGls>'
                 +'    <RagioneSociale>'+XMLString (StringReplace (copy (liefquery.FieldByName ('NAME1').AsString, 1, 35), '&', '', [rfReplaceAll]))+'</RagioneSociale>'
                 +'    <Indirizzo>'+XMLString (StringReplace (copy (liefquery.FieldByName ('STRASSE').AsString, 1, 35), '&', '', [rfReplaceAll]))+'</Indirizzo>'
                 +'    <Notespedizione>'+XMLString (StringReplace (copy (liefquery.FieldByName ('STRASSE_2').AsString, 1, 35), '&', '', [rfReplaceAll]))+'</Notespedizione>';

          if (liefquery.FieldByName ('COMPANY').IsNull) then
            body := body  +'    <Noteaggiuntive></Noteaggiuntive>'
          else
            body := body  +'    <Noteaggiuntive>'+XMLString (StringReplace (copy (liefquery.FieldByName ('NAME2').AsString, 1, 35), '&', '', [rfReplaceAll]))+'</Noteaggiuntive>';

          body := body +'    <Localita>'+liefquery.FieldByName ('ORT').AsString+'</Localita>'
                 +'    <Zipcode>'+liefquery.FieldByName ('PLZ').AsString+'</Zipcode>'
                 +'    <Provincia></Provincia>'
                 +'    <Bda>'+orderid+'</Bda>'
                 +'    <Colli>1</Colli>'
                 +'    <Incoterm></Incoterm>';

          if query.FieldByName('BRUTTO_GEWICHT').IsNull then
            gw := 0
          else
            gw := query.FieldByName('BRUTTO_GEWICHT').AsFloat;

          try
            {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

            if (gw < 0.001) then
              body := body + '    <PesoReale>'+'0.1'+'</PesoReale>'
            else
              body := body + '    <PesoReale>'+FormatFloat ('0.#', gw)+'</PesoReale>';

            //Nachnahme nur, wenn es auch einen Wert gibt
            if (WarenWert <= 0) or (query.FieldByName('OPT_NACHNAHME').AsString = '0') then
              body := body + '<ImportoContrassegno></ImportoContrassegno>'
            else
              body := body + '<ImportoContrassegno>'+FormatFloat ('0.##', WarenWert)+'</ImportoContrassegno>';
          finally
            {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
          end;

          body := body + '    <TipoPorto></TipoPorto>'
                 +'    <Assicurazione></Assicurazione>'
                 +'    <PesoVolume></PesoVolume>'
                 +'    <RiferimentoCliente></RiferimentoCliente>'
                 +'    <CodiceClienteDestinatario></CodiceClienteDestinatario>'
                 +'    <TipoCollo></TipoCollo>';

          if not (kepemail) or liefquery.FieldByName('EMAIL').IsNull then
            body := body +'    <Email>'+XMLString (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString)+'</Email>'
          else
            body := body +'    <Email>'+XMLString (liefquery.FieldByName('EMAIL').AsString)+'</Email>';

          body := body +'    <Cellulare1>'+XMLString (liefquery.FieldByName('TELEFON').AsString)+'</Cellulare1>'
                 +'    <ServiziAccessori></ServiziAccessori>';

          //Nachnahme nur, wenn es auch einen Wert gibt
          if (WarenWert <= 0) or (query.FieldByName('OPT_NACHNAHME').AsString = '0') then
            body := body +'    <ModalitaIncasso></ModalitaIncasso>'
          else
            body := body +'    <ModalitaIncasso>CONT</ModalitaIncasso>';

          body := body +'    <DataPrenotazioneGDO></DataPrenotazioneGDO>'
                 +'    <OrarioNoteGDO></OrarioNoteGDO >'
                 +'    <GeneraPdf>6</GeneraPdf>'
                 +'    <FormatoPdf>A6</FormatoPdf>'
                 +'    <ContatoreProgressivo>'+query.FieldByName('REF_NVE').AsString+'</ContatoreProgressivo>'
                 +'    <NumDayListSped></NumDayListSped>'
                 +'    <IdentPIN></IdentPIN>'
                 +'    <AssicurazioneIntegrativa></AssicurazioneIntegrativa >'
                 +'    <TipoSpedizione>N</TipoSpedizione>'
                 +'    <ValoreDichiarato></ValoreDichiarato>'
                 +'    <PersonaRiferimento></PersonaRiferimento>'
                 +'    <Contenuto></Contenuto>'
                 +'    <TelefonoDestinatario>'+XMLString (liefquery.FieldByName('TELEFON').AsString)+'</TelefonoDestinatario>'
                 +'    <CategoriaMerceologica></CategoriaMerceologica>'
                 +'    <FatturaDoganale></FatturaDoganale>'
                 +'    <DataFatturaDoganale></DataFatturaDoganale>'
                 +'    <PezziDichiarati></PezziDichiarati>'
                 +'    <NazioneOrigine></NazioneOrigine>'
                 +'    <TelefonoMittente>'+telstr+'</TelefonoMittente>'
                 +'    <IdReso></IdReso>'
                 +'    <NumeroFatturaCOD></NumeroFatturaCOD>'
                 +'    <DataFatturaCOD></DataFatturaCOD>'
                 +'    <NoteIncoterm></NoteIncoterm>'
                 +'   </Parcel>'
                 +'</Info>';

          StrToFile (DatenPath + RESTDumpDir+'GLS_IT\'+FormatDateTime ('yyyymmdd', Now)+'\post_labels_'+nve+'.xml', body);

          sdata.Clear;
          if SendRequest('labelservice.gls-italy.com', // Host,
                          -1, //Port
                          'ilswebservice.asmx/AddParcel', // Service
                          'POST', //Methode
                          '', //Kein proxy
                          //'localhost:8888', // Fiddler als Proxy
                          '', '', // User , PW
                          '', //Action
                          'application/x-www-form-urlencoded', //ContentType
                          [], //AddHeader
                          body,         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            labelurl := '';

            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+'GLS_IT\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nve+'.xml');

            xmlrd := TXMLFile.Create (Nil);

            try
              sdata.Position := 0;
              xmlrd.ParseXMLFile (sdata);

              xmsnd := xmlrd.FindTag ('NumeroSpedizione');
              if not Assigned (xmsnd) then begin
                res := 25;

                xmlnd := xmlrd.FindTag ('NoteSpedizione');

                if Assigned (xmlnd) then
                  ErrorText := xmlnd.NodeValue
                else begin
                  xmlnd := xmlrd.FindTag ('DescrizioneErrore');

                  if Assigned (xmlnd) then
                    ErrorText := xmlnd.NodeValue
                  else
                    ErrorText := 'tracking code error'
                end;
              end else begin
                //Prüfen, ob es sich um ein Fehlerlabel handelt
                xmlnd := xmlrd.FindTag ('DescrizioneSedeDestino');

                if Assigned (xmlnd) then begin
                  tagstr := xmlnd.NodeValue;

                  //if (tagstr = 'GLS Check') or (uppercase (tagstr) = 'ERRORE') then begin
                  if (uppercase (tagstr) = 'ERRORE') then begin
                    res := 25;

                    xmlnd := xmlrd.FindTag ('DescrizioneErrore');

                    if Assigned (xmlnd) then
                      ErrorText := xmlnd.NodeValue
                    else begin
                      xmlnd := xmlrd.FindTag ('NoteSpedizione');

                      if Assigned (xmlnd) then
                        ErrorText := xmlnd.NodeValue
                      else
                        ErrorText := 'tracking code error'
                    end;
                  end;
                end;

                if (res = 0) then begin
                  SendungsNr := gisede + xmsnd.NodeValue;

                  //Den 1D-Cardoe ermitteln
                  xmlnd := xmlrd.FindTag ('Barcode2D');
                  if Assigned (xmlnd) and (copy (xmlnd.NodeValue, 1, 4) = '!*AA') then
                    Barcode := copy (xmlnd.NodeValue, 5, 18);

                  xmlnd := xmlrd.FindTag ('Zpl');
                  if not Assigned (xmlnd) then begin
                    res := 26;
                    ErrorText := 'Label data error';
                  end else if (Length (xmlnd.NodeValue) = 0) then begin
                    res := 27;
                    ErrorText := 'Label data empty'
                  end else begin
                    ForceDirectories(DatenPath + LabelDumpDir + 'GLS_IT');

                    LabelFormat := 'zpl';

                    //res := StrToFile (DatenPath + 'GLS_IT\' + SendungsNr + '.'+LabelFormat, xmlnd.NodeValue);

                    //Muss AnsiString sein
                    outstr := xmlnd.NodeValue;

                    LabelImage.WriteBuffer (Pointer(outstr)^, Length(outstr));

                    try
                      LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'GLS_IT\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                    except
                    end;

                    if (res <> 0) then
                      ErrorText := 'Error '+IntToStr (res) + ' while save shipping label data';
                  end;
                end;
              end;

              if Assigned (SendITLog) then begin
                SendITLog.Logging (clNormal, 'Versand: GLS-IT, RefNVE: '+query.FieldByName('REF_NVE').AsString+', Versender: '+Versender+', SendungsNr: '+SendungsNr);
              end;
            finally
              xmlrd.Free;
            end;
          end else begin
            res := 21;

            ErrorText := 'Fehler beim GLS server';

            if (Length (errtext) > 0) then
              ErrorText := ErrorText + #13 + errtext;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: GLS-IT, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

  //******************************************************************************
  //* Function Name: CreateHeyworldLabel
  //* Author       : Stefan Graf
  //* Datum        : 10.05.2023
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateHeyworldLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;

    procedure ExtractDomain (url : string; var Host, Service : String);
     var
       p : cardinal;
     begin
       Host:='';
       Service := '';

       if trim(url)='' then exit;

       url:=trim(url);
       p:=pos('://',url);
       if p>0 then delete(url,1,p+pred(length('://')));
       p:=pos('/',url);

       if (p = 0) then
         Host:= url
       else begin
         Host:=copy(url,1, p - 1);
         Service:=copy(url, p + 1);
       end;
     end;

  var
    idx,
    eidx,
    res,
    keyidx,
    errcode  : Integer;
    errtext  : String;
    xmlstr   : String;
    urlparam : String;
    body     : String;
    resp     : String;
    hdr      : String;
    nve      : String;
    labelsvr,
    labelurl,
    labelhost: String;
    nrstr    : String;
    orderid  : String;
    timestr  : String;
    namestr  : String;
    telstr   : String;
    tagstr   : String;
    gipw,
    giuser,
    gicode,
    gisede,
    numstr   : String;
    js       : TlkJSONobject;
    fs,
    us,
    ls,
    errfs    : TlkJSONbase;
    prttype,
    keystr,
    streetstr,
    urlstr,
    prodstr,
    codestr,
    customstr,
    custommailstr,
    locstr,
    vorstr,
    nachstr  : String;
    outstr   : AnsiString;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    cfgquery : TSmartQuery;
    olddec   : Char;
    gw       : Integer;       //Sendungsgewicht in Gramm
    found    : boolean;
    xmlrd    : TXMLFile;
    xmlnd    : TXMLTagEntry;
    xmsnd    : TXMLTagEntry;
    stat,
    absstr,
    absstrasse,
    absname,
    absort,
    absplz,
    absland   : String;

    {$ifdef UNICODE}
      utfstr       : AnsiString;
      datastr      : String;
    {$endif}
  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      TrackUrl  := '';
      Barcode   := '';
      ErrorText := '';

      absstr := '';
      absstrasse := '';
      absname := '';
      absort := '';
      absplz := '';
      absland := '';

      //Aktuell keine Nachnahme
      //if (query.FieldByName('OPT_NACHNAHME').AsString > '0') then begin
      if ('0' > '0') then begin
        res := 9;
        ErrorText := 'Nachnahme wird für '+Versender+' nicht unterstützt';
      end else begin
        if (PrtInfo.Model = 'ZPL_300') then
          prttype := 'Zebra|Generic ZPL II 300 dpi'
        else
          prttype := 'Zebra|Generic ZPL II 200 dpi';

        cfgquery  := TSmartQuery.Create (Nil);

        try
          cfgquery.ReadOnly := True;
          cfgquery.Session := Query.Session;

          cfgquery.SQL.Add ('select cfg.REST_URL as SPED_REST_URL, g.* from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
          cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

          cfgquery.Open;

          if not Assigned (cfgquery.FindField('API_KEY')) then
            ErrorText := 'Not perpared for api key'
          else if cfgquery.FieldByName('API_KEY').IsNull then
            ErrorText := 'No api key'
          else begin
            keystr  := cfgquery.FieldByName('API_KEY').AsString;
            customstr := cfgquery.FieldByName('SENDIT_CLIENT').AsString;
            locstr  := cfgquery.FieldByName('SENDIT_LOCATION').AsString;
            codestr := cfgquery.FieldByName('GATEWAY').AsString;
            prodstr := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;
            telstr  := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;
            custommailstr := cfgquery.FieldByName('DEFAULT_EMAIL_ADRESS').AsString;

            if Assigned (cfgquery.FindField ('ABSENDER')) then
              absstr  := cfgquery.FieldByName('ABSENDER').AsString;

            if Assigned (cfgquery.FindField ('REST_URL')) then
              urlstr  := cfgquery.FieldByName('REST_URL').AsString;

            if (Length (urlstr) = 0) then
              urlstr  := cfgquery.FieldByName('SPED_REST_URL').AsString;

            if (Length (urlstr) = 0) then
              ErrorText := 'Not REST url defined';
          end;

          cfgquery.Close;
        finally
          cfgquery.Free;
        end;

        if (Length (keystr) = 0) then
          res := 23
        else begin
          if (Length (absstr) > 0) then begin
            strlist := TStringList.Create;
            try
              strlist.Delimiter := ';';
              strlist.StrictDelimiter := true;

              strlist.DelimitedText := absstr;

              if (strlist.Count < 5) then begin
                res := 24;
                ErrorText := 'Sender address not complete';
              end else begin
                absname := strlist [0];
                absstrasse := strlist [1];
                absplz := strlist [2];
                absort := strlist [3];
                absland := strlist [4];
              end;
            finally
              strlist.Free;
            end;
          end;
        end;
      end;

      if (res = 0) then begin
        nve := query.FieldByName('NVE_NR').AsString;

        if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
          orderid := query.FieldByName('AUFTRAG_NR').AsString
        else
          orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;

        if query.FieldByName('BRUTTO_GEWICHT').IsNull then
          gw := 10
        else if (query.FieldByName('BRUTTO_GEWICHT').AsFloat < 0.01) then
          gw := 10
        else
          gw := round (query.FieldByName('BRUTTO_GEWICHT').AsFloat * 1000);

        if Assigned (SendITLog) then begin
          SendITLog.Logging (clNormal, 'Versand: Heyworld, RefNVE: '+query.FieldByName('REF_NVE').AsString+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
        end;

        sdata := TMemoryStream.Create;

        try
          ForceDirectories(DatenPath + RESTDumpDir+'Heyworld\'+FormatDateTime ('yyyymmdd', Now));

          if (Length (SendungsID) = 0) then begin
            //Token erzeugen
            urlparam := '';

            body := '{'
                    +'  "importCustoms": false,'
                    +'  "exportCustoms": false,'
                    +'  "requireReturnLabel": false,'
                    +'  "shipper": "'+prodstr+'",';

            (*
            //Absender Adresse übergeben
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              //Bei Dropship wird die Traderadresse angezeigt
              cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                                 +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                                 +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                                 +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                                 +'coalesce (translate (tadr.LAND using CHAR_CS),loc.LAND,''DE'') as LAND,'
                                 +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON'
                                +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                                +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                                +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                                +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                +' where auf.REF=:ref_auf'
                                );

              cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

              cfgquery.Open;

              if (cfgquery.FieldByName('REF').IsNull) then begin
                cfgquery.Close;

                cfgquery.SQL.Clear;
                cfgquery.SQL.Add ( 'select coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as NAME1,ml.ABSENDER_ZUSATZ as NAMEZUSATZ,nvl (ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE'
                                  +',nvl (ml.ABSENDER_PLZ,loc.PLZ) as PLZ,nvl (ml.ABSENDER_ORT,loc.ORT) as ORT,nvl (loc.LAND,''DE'') as LAND,loc.TELEFON'
                                  +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                  +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                  +' where m.REF=:ref_mand'
                                  );

                cfgquery.Params.ParamByName('ref_loc').Value := query.FieldByName('REF_LOCATION').AsInteger;

                if query.FieldByName('REF_SUB_MAND').IsNull then
                  cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
                else cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

                cfgquery.Open;
              end;

              numstr := '';

              if (Length (absstrasse) > 0) then
                streetstr := absstrasse
              else
                streetstr := cfgquery.FieldByName ('STRASSE').AsString;

              idx := Length (streetstr);
              while (idx > 1) and (streetstr [idx] <> ' ') do
                Dec (idx);

              if (idx > 1) then begin
                numstr := copy (streetstr, idx + 1);

                while (idx > 1) and (streetstr [idx] = ' ') do
                  Dec (idx);

                streetstr := copy (streetstr, 1, idx);
              end;

              if (Length (absname) = 0) then
                absname := cfgquery.FieldByName ('NAME1').AsString;

              if (Length (absort) = 0) then
                absort := cfgquery.FieldByName ('ORT').AsString;

              if (Length (absplz) = 0) then
                absplz := cfgquery.FieldByName ('PLZ').AsString;

              if (Length (absland) = 0) then
                absland := cfgquery.FieldByName ('LAND').AsString;


              body := body
                            +'"City": "'+ConvertJSONSonderzeichen (absort)+'",'
                            +'"CompanyName": "'+ConvertJSONSonderzeichen (absname)+'",'
                            +'"Street": "'+ConvertJSONSonderzeichen (streetstr)+'",'
                            +'"HouseNr": "'+numstr+'",'
                            +'"Zipcode": "'+absplz+'",'
                            +'"Countrycode": "'+ConvertJSONSonderzeichen (absland)+'"';

              cfgquery.Close;

            finally
              cfgquery.Free;
            end;
            *)

            body := body
                      +'"consignee": {'
                      +'  "address": {'
                      +'    "name": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('NAME1').AsString, 1, 35), '&', '', [rfReplaceAll]))+'",'
                      +'    "firstAddressLine": "'+ConvertJSONSonderzeichen (StringReplace (trim (liefquery.FieldByName ('STRASSE').AsString), '&', '', [rfReplaceAll]))+'"';

            if not (liefquery.FieldByName ('STRASSE_2').IsNull) then
            body := body
                      +',   "secondAddressLine": "'+ConvertJSONSonderzeichen (StringReplace (trim (liefquery.FieldByName ('STRASSE_2').AsString), '&', '', [rfReplaceAll]))+'"';

            body := body
                      +',   "city": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('ORT').AsString)+'",'
                      +'    "postalCode": "'+liefquery.FieldByName ('PLZ').AsString+'",';

            //Die kanarischen Inseln müssen mit ES als ISO gemeldet werden
            if (landstr = 'IC') then
              body := body +'    "country": "'+'ES'+'"'
            else
              body := body +'    "country": "'+landstr+'"';

            body := body +'  }';

            if not (kepemail) or liefquery.FieldByName('EMAIL').IsNull then
              body := body +',    "email": "'+ConvertJSONSonderzeichen (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString)+'"'
            else
              body := body +',    "email": "'+ConvertJSONSonderzeichen (liefquery.FieldByName('EMAIL').AsString)+'"';

            if not liefquery.FieldByName('TELEFON').IsNull then
              body := body +',    "telephone": "'+ConvertJSONSonderzeichen (liefquery.FieldByName('TELEFON').AsString)+'"'
            else if not adrquery.FieldByName('TELEFON').IsNull then
              body := body +',    "telephone": "'+ConvertJSONSonderzeichen (adrquery.FieldByName('TELEFON').AsString)+'"'
            else if not query.FieldByName('DEFAULT_PHONE_NUMBER').IsNull then
              body := body +',    "telephone": "'+ConvertJSONSonderzeichen (query.FieldByName('DEFAULT_PHONE_NUMBER').AsString)+'"'
            else
              body := body +',    "telephone": "'+ConvertJSONSonderzeichen (telstr)+'"';

            body := body
                      +'}';

            body := body
                      +',"parcel": {';

            //body := body +'  "id": "'+nve+'",';

            if (query.FieldByName('RECHNUNGS_NR').IsNull) then begin
              body := body +'  "invoiceNumber": "'+ConvertJSONSonderzeichen (query.FieldByName('AUFTRAG_NR').AsString)+'"';
              body := body +'  ,"invoiceDownloadUrl": "https://example.com/'+ConvertJSONSonderzeichen (query.FieldByName('AUFTRAG_NR').AsString)+'"';
            end else begin
              body := body +'  "invoiceNumber": "'+ConvertJSONSonderzeichen (query.FieldByName('RECHNUNGS_NR').AsString)+'"';
              body := body +'  ,"invoiceDownloadUrl": "https://example.com/'+ConvertJSONSonderzeichen (query.FieldByName('RECHNUNGS_NR').AsString)+'"';
            end;

            body := body
                      +'  ,"height": '+IntToStr (query.FieldByName('L').AsInteger div 10)
                      +'  ,"length": '+IntToStr (query.FieldByName('B').AsInteger div 10)
                      +'  ,"width": '+IntToStr (query.FieldByName('H').AsInteger div 10)
                      +'  ,"weight": '+IntToStr (gw)
                      +'  ,"priceCurrency": "'+'EUR'+'"';

            body := body + '  ,"price": '+Format('%6.3f', [WarenWert]);

            body := body
                      +',"articles": [';

            //if not IsLandEU (query.FieldByName('LIEFER_LAND_ISO').AsString) then
            begin
              cfgquery  := TSmartQuery.Create (Nil);

              try
                cfgquery.ReadOnly := True;
                cfgquery.Session := Query.Session;

                //Die Zollinfos der Artikel
                cfgquery.SQL.Clear;
                //Alle Positionen des Auftrags, welche keine Textartikel sind, das Gewicht ist das Artikelgewicht
                cfgquery.SQL.Add('  SELECT OPT_AUTO_CUSTOMS,'
                                +'         ARTIKEL_NR,'
                                +'         ARTIKEL_TEXT,'
                                +'         KURZ_BEZEICHNUNG,'
                                +'         COUNTRY_OF_ORIGIN,'
                                +'         TARIC_NUMBER,'
                                +'         CURRENCY,'
                                +'         SUM (MENGE_GESAMT)     AS SUM_MENGE_GESAMT,'
                                +'         SUM (BRUTTO_GEWICHT)   AS SUM_BRUTTO_GEWICHT,'
                                +'         SUM (NETTO_BETRAG)     AS SUM_NETTO_BETRAG'
                                +'    FROM (SELECT mcfg.OPT_AUTO_CUSTOMS,'
                                +'                 ar.ARTIKEL_NR,'
                                +'                 GETARTIKELTEXT (ar.REF, ''EN'') AS ARTIKEL_TEXT,'
                                +'                 vpe.KURZ_BEZEICHNUNG,'
                                +'                 NVL (ar.COUNTRY_OF_ORIGIN, ''DE'') AS COUNTRY_OF_ORIGIN,'
                                +'                 NVL (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER) AS TARIC_NUMBER,'
                                +'                 NVL (re.CURRENCY, ''EUR'') AS CURRENCY,'
                                +'                 pos.MENGE_GESAMT AS MENGE_GESAMT,'
                                +'                 NVL (ae.BRUTTO_GEWICHT, ae.NETTO_GEWICHT) AS BRUTTO_GEWICHT,'
                                +'                 NVL (CASE WHEN rep.NETTO_BETRAG = 0 THEN NULL ELSE rep.NETTO_BETRAG END, (re.NETTO_BETRAG / auf.SUM_VPE) * pos.MENGE_BESTELLT) AS NETTO_BETRAG'
                                +'            FROM VQ_AUFTRAG            auf,'
                                +'                 V_AUFTRAG_RECHNUNG    re,'
                                +'                 V_AUFTRAG_POS         pos,'
                                +'                 V_AUFTRAG_POS_RECHNUNG rep,'
                                +'                 V_ARTIKEL             ar,'
                                +'                 VQ_ARTIKEL_EINHEIT    ae,'
                                +'                 V_ARTIKEL_VPE         vpe,'
                                +'                 V_MANDANT_CONFIG      mcfg'
                                +'           WHERE'
                                +'                 pos.REF_AUF_KOPF = auf.REF'
                                +'                 AND ar.REF = pos.REF_AR'
                                +'                 AND ae.REF = pos.REF_AR_EINHEIT'
                                +'                 AND vpe.REF = ae.REF_EINHEIT'
                                +'                 AND mcfg.REF_MAND = NVL (auf.REF_SUB_MAND, auf.REF_MAND)'
                                +'                 AND pos.REF_PARENT_POS IS NULL'
                                +'                 AND re.REF_AUF_KOPF = auf.REF'
                                +'                 AND rep.REF_AUF_POS = pos.REF'
                                +'                 AND NVL (ar.OPT_TEXT_ARTIKEL, ''0'') = ''0'''
                                +'                 AND NVL (pos.MENGE_GESAMT, 0) > 0'
                                +'                 AND auf.REF = :REF)'
                                +'GROUP BY OPT_AUTO_CUSTOMS,'
                                +'         ARTIKEL_NR,'
                                +'         ARTIKEL_TEXT,'
                                +'         KURZ_BEZEICHNUNG,'
                                +'         COUNTRY_OF_ORIGIN,'
                                +'         TARIC_NUMBER,'
                                +'         CURRENCY'
                                );
(*
                cfgquery.SQL.Add('select mcfg.OPT_AUTO_CUSTOMS,ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN'') as ARTIKEL_TEXT,pos.MENGE_GESAMT,vpe.KURZ_BEZEICHNUNG,nvl (ar.COUNTRY_OF_ORIGIN, ''DE'') as COUNTRY_OF_ORIGIN'
                                +',nvl (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER) as TARIC_NUMBER,nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT) as BRUTTO_GEWICHT'
                                +',nvl (case when rep.NETTO_BETRAG=0 then null else rep.NETTO_BETRAG end,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT) as NETTO_BETRAG,nvl (re.CURRENCY, ''EUR'') as CURRENCY'
                                +' from VQ_AUFTRAG auf, V_AUFTRAG_RECHNUNG re, V_AUFTRAG_POS pos, V_AUFTRAG_POS_RECHNUNG rep, V_ARTIKEL ar, VQ_ARTIKEL_EINHEIT ae, V_ARTIKEL_VPE vpe, V_MANDANT_CONFIG mcfg'
                                +' where pos.REF_AUF_KOPF=auf.REF and ar.REF=pos.REF_AR and ae.REF=pos.REF_AR_EINHEIT and vpe.REF=ae.REF_EINHEIT and mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND)'
                                +' and pos.REF_PARENT_POS is null and re.REF_AUF_KOPF=auf.REF and rep.REF_AUF_POS=pos.REF and nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0 and auf.REF=:ref'
                                );
*)
                cfgquery.Params [0].Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

                cfgquery.Open;

                while not (cfgquery.Eof) do begin
                  if (cfgquery.RecNo > 1) then body := body + ',';

                  body := body
                     +'{'
                       +'  "countryOfOrigin": "'+cfgquery.FieldByName('COUNTRY_OF_ORIGIN').AsString+'",'
                       +'  "sku": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName('ARTIKEL_NR').AsString)+'",'
                       +'  "name": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName('ARTIKEL_TEXT').AsString)+'",'
                       +'  "hsCode": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName('TARIC_NUMBER').AsString)+'",'
                       +'  "quantity": '+cfgquery.FieldByName('SUM_MENGE_GESAMT').AsString+','
                       +'  "priceCurrency": "'+cfgquery.FieldByName('CURRENCY').AsString+'",';

                  //PriceX, Betrag ist min. 10 Cent
                  if (cfgquery.FieldByName('SUM_NETTO_BETRAG').IsNull or (cfgquery.FieldByName('SUM_NETTO_BETRAG').AsInteger < 10)) then
                    body := body + '  "price": '+Format('%6.3f', [0.1])+','
                  else
                    body := body + '  "price": '+Format('%6.3f', [cfgquery.FieldByName('SUM_NETTO_BETRAG').AsInteger / 1000])+',';

                  if (cfgquery.FieldByName('SUM_BRUTTO_GEWICHT').AsInteger >= 1) then
                    body := body +'  "weight": '+cfgquery.FieldByName('SUM_BRUTTO_GEWICHT').AsString
                  else
                    body := body +'  "weight": 1';


                  body := body + '}';

                  cfgquery.Next;
                end;

                cfgquery.Close;
              finally
                cfgquery.Free;
              end;
            end;

            body := body + ']';
            body := body + '}';


            body := body
                +','
                +'"pickup": {'
                +'  "type": "firstMile",'
                +'  "warehouse": "WAREHOUSE_BER"'
                +'}';

            body := body
                +','
                +'"delivery": {'
                +'  "type": "lastMile"'
                +'}';

            if (query.FieldByName('OPT_NACHNAHME').AsString = '1') then
              //body := body + '  ,"cashOnDelivery": true';
              body := body + '  ,"requiresCashOnDelivery": true';

            body := body
                +'}';

            StrToFile (DatenPath + RESTDumpDir+'Heyworld\'+FormatDateTime ('yyyymmdd', Now)+'\post_labels_'+nve+'.json', body);

            sdata.Clear;
            if SendRequest(urlstr, // Host,
                            -1, //Port
                            'shipping-orders', // Service
                            'POST', //Methode
                            '', //Kein proxy
                            //'localhost:8888', // Fiddler als Proxy
                            '', '', // User , PW
                            '', //Action
                            'application/json', //ContentType
                            ['X-Api-Key: '+keystr,'Content-Type: application/json','accept: application/json'], //AddHeader
                            UTF8Encode (body),         // RequestData
                            resp,
                            sdata, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
                          then
            begin
              labelurl := '';

              sdata.Position := 0;
              sdata.SaveToFile(DatenPath + RESTDumpDir+'Heyworld\'+FormatDateTime ('yyyymmdd', Now)+'\post_labels_resp_'+nve+'.json');

              fs := Nil;

              {$ifdef UNICODE}
                SetLength(utfstr, sdata.Size);
                sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                datastr := StringUtils.StringToUTF (utfstr);

                js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
              {$else}
                sdata.Position := 0;
                js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
              {$endif}

              if not Assigned (js) then begin
                ErrorText := 'API error message';
              end else begin
                try
                  fs := js.Field['id'];

                  if not Assigned (fs) then begin
                    res := 37;

                    fs := js.Field['Errors'];
                    if Assigned (fs) then begin
                      for idx := 0 to fs.Count - 1 do begin
                        us := fs.Child [idx].Field['Description'];

                        if Assigned (us) and not (us.Value = NULL) then
                          ErrorText := ErrorText + ':' + StringReplace (us.Value, '\u0027', '''', [rfReplaceAll]);
                      end;
                    end else begin
                      fs := js.Field['title'];
                      if Assigned (fs) and not (fs.Value = NULL) then begin
                        ErrorText := ErrorText + ':' + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll]);

                        fs := js.Field['detail'];
                        if Assigned (fs) and not (fs.Value = NULL)  then
                          ErrorText := ErrorText + #13+#10 + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll]);
                      end;
                    end;

                    if (Length (ErrorText) = 0) then
                      ErrorText := 'tracking code error';
                  end else begin
                    SendungsID := fs.Value;
                  end;
                except
                  res := 37;
                  ErrorText := 'Label data error';
                end;
              end;
            end else begin
              res := 22;

              ErrorText := 'Fehler beim Heyworld server';

              if (Length (errtext) > 0) then
                ErrorText := ErrorText + #13 + errtext;
            end;
          end;

          if (res = 0) then begin
            body := '';

            sdata.Clear;
            if SendRequest(urlstr, // Host,
                            -1, //Port
                            'shipping-orders/'+SendungsID, // Service
                            'GET', //Methode
                            '', //Kein proxy
                            //'localhost:8888', // Fiddler als Proxy
                            '', '', // User , PW
                            '', //Action
                            'application/json', //ContentType
                            ['X-Api-Key: '+keystr,'Content-Type: application/json','accept: application/json'], //AddHeader
                            UTF8Encode (body),         // RequestData
                            resp,
                            sdata, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
                          then
            begin
              labelurl := '';

              sdata.Position := 0;
              sdata.SaveToFile(DatenPath + RESTDumpDir+'Heyworld\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_resp'+nve+'.json');

              {$ifdef UNICODE}
                SetLength(utfstr, sdata.Size);
                sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                datastr := StringUtils.StringToUTF (utfstr);

                js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
              {$else}
                sdata.Position := 0;
                js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
              {$endif}

              if not Assigned (js) then begin
                ErrorText := 'API error message';
              end else begin
                stat := '';

                try
                  fs := js.Field['processingStatus'];

                  if not Assigned (fs) then begin
                    res := 37;

                    fs := js.Field['Errors'];
                    if Assigned (fs) then begin
                      for idx := 0 to fs.Count - 1 do begin
                        us := fs.Child [idx].Field['Description'];

                        if Assigned (us) then
                          ErrorText := ErrorText + ':' + StringReplace (us.Value, '\u0027', '''', [rfReplaceAll]);
                      end;
                    end;

                    if (Length (ErrorText) = 0) then
                      ErrorText := 'tracking code error';
                  end else begin
                    us := fs.Field['status'];

                    if Assigned (us) and not (us.Value = NULL) then
                      stat := us.Value;
                  end;

                  if (stat = 'failed') then begin
                    res := 38;

                    fs := js.Field['processingStatus'];

                    if Assigned (fs) then begin
                      us := fs.Field['errors'];
                      if Assigned (us) then begin
                        for idx := 0 to us.Count - 1 do begin
                          ls := us.Child [idx].Field['type'];
                          if Assigned (ls) then
                            ErrorText := ErrorText + StringReplace (ls.Value, '\u0027', '''', [rfReplaceAll])
                          else
                            ErrorText := ErrorText + 'Error in label generation';


                          ls := us.Child [idx].Field['additionalInformation'];

                          if Assigned (ls) then begin
                            us := ls.Field['reasons'];

                            if Assigned (us) then begin
                              for eidx := 0 to us.Count - 1 do begin
                                if not (us.Child [eidx].Value = NULL) then
                                  ErrorText := ErrorText + #13+#10 + '  ' + StringReplace (us.Child [eidx].Value, '\u0027', '''', [rfReplaceAll]);
                              end;
                            end;
                          end;
                        end;
                      end;
                    end;

                    if (Length (ErrorText) = 0) then
                      ErrorText := 'shipping order error';
                  end else if (stat = 'finished') then begin
                    fs := js.Field['tracking'];

                    if not Assigned (fs) then begin
                      res := 37;

                      fs := js.Field['Errors'];
                      if Assigned (fs) then begin
                        for idx := 0 to fs.Count - 1 do begin
                          us := fs.Child [idx].Field['Description'];

                          if Assigned (us) and not (us.Value = NULL) then
                            ErrorText := ErrorText + ':' + StringReplace (us.Value, '\u0027', '''', [rfReplaceAll]);
                        end;
                      end;

                      if (Length (ErrorText) = 0) then
                        ErrorText := 'tracking code error';
                    end else begin
                      us := fs.Field['trackingCode'];

                      if Assigned (us) and not (us.Value = NULL) then
                        Barcode := us.Value;

                      us := fs.Field['publicUrl'];

                      if Assigned (us) and not (us.Value = NULL) then
                        TrackUrl := us.Value;

                      us := fs.Field['externalTrackingCode'];

                      if Assigned (us) and not (us.Value = NULL) then
                        SendungsNr := us.Value
                      else
                        SendungsNr := Barcode;

                      us := js.Field['label'];

                      if Assigned (us) then begin
                        ls := us.Field['href'];

                        if Assigned (ls) and not (ls.Value = NULL) then
                          labelurl := ls.Value
                        else begin
                          ls := us.Field['url'];

                          if Assigned (ls) and not (ls.Value = NULL) then
                            labelurl := ls.Value;
                        end;
                      end;

                      if (Length (labelurl) = 0) then begin
                        res := 38;
                        ErrorText := 'label_zpl error'
                      end else begin
                        ExtractDomain (labelurl, labelhost, labelsvr);

                        if SendRequest(labelhost, // Host,
                                        -1, //Port
                                        labelsvr, // Service
                                        'GET', //Methode
                                        '', // Proxy,
                                        '', '', // User , PW
                                        '', //Action
                                        'application/json', //ContentType
                                        [], //AddHeader
                                        body,         // RequestData
                                        resp,
                                        LabelImage, //ResponseStream
                                        errcode, // Fehlercode
                                        errtext) // Fehlertext
                        then begin
                          StrToFile (DatenPath + RESTDumpDir+'Heyworld\'+FormatDateTime ('yyyymmdd', Now)+'\label_resp_'+nve+'.txt', resp);

                          if (Pos ('404 Not Found', resp) > 0) then begin
                            res := 26;
                            ErrorText := 'No label found';
                          end else begin
                            if (Pos ('application/zpl', resp) > 0) then
                              LabelFormat := 'zpl'
                            else if (Pos ('application/pdf', resp) > 0)  then
                              LabelFormat := 'pdf'
                            else
                              LabelFormat := '###';

                            ForceDirectories(DatenPath + LabelDumpDir + 'Heyworld');

                            LabelImage.Position := 0;

                            try
                              LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'Heyworld\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                            except
                            end;

                            if Assigned (SendITLog) then begin
                              SendITLog.Logging (clNormal, 'Versand: Heyworld, RefNVE: '+IntToStr (query.FieldByName('REF_NVE').AsInteger)+', Versender: '+Versender+', SendungsNr: '+SendungsNr);
                            end;
                          end;
                        end;
                      end;
                    end;
                  end;
                except
                  res := 37;
                  ErrorText := 'Label data error';
                end;
              end;
            end else begin
              res := 21;

              ErrorText := 'Fehler beim Heyworld server';

              if (Length (errtext) > 0) then
                ErrorText := ErrorText + #13 + errtext;
            end;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: Heyworld, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

  //******************************************************************************
  //* Function Name: CreatePostNLLabel
  //* Author       : Stefan Graf
  //* Datum        : 22.03.2022
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreatePostNLLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    idx,
    res,
    keyidx,
    errcode  : Integer;
    errtext  : String;
    xmlstr   : String;
    urlparam : String;
    body     : String;
    resp     : String;
    hdr      : String;
    nve      : String;
    labelurl : String;
    nrstr    : String;
    orderid  : String;
    timestr  : String;
    namestr  : String;
    telstr   : String;
    tagstr   : String;
    gipw,
    giuser,
    gicode,
    gisede,
    numstr   : String;
    js       : TlkJSONobject;
    fs,
    us,
    errfs    : TlkJSONbase;
    prttype,
    keystr,
    streetstr,
    urlstr,
    prodstr,
    codestr,
    customstr,
    custommailstr,
    locstr,
    vorstr,
    nachstr  : String;
    outstr   : AnsiString;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    cfgquery : TSmartQuery;
    olddec   : Char;
    gw       : Double;
    found    : boolean;
    xmlrd    : TXMLFile;
    xmlnd    : TXMLTagEntry;
    xmsnd    : TXMLTagEntry;
    absstr,
    absstrasse,
    absname,
    absort,
    absplz,
    absland   : String;
  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      TrackUrl  := '';
      Barcode   := '';
      ErrorText := '';

      absstr := '';
      absstrasse := '';
      absname := '';
      absort := '';
      absplz := '';
      absland := '';

      if (PrtInfo.Model = 'ZPL_300') then
        prttype := 'Zebra|Generic ZPL II 300 dpi'
      else
        prttype := 'Zebra|Generic ZPL II 200 dpi';

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select cfg.REST_URL as SPED_REST_URL, g.* from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else begin
          keystr  := cfgquery.FieldByName('API_KEY').AsString;
          customstr := cfgquery.FieldByName('SENDIT_CLIENT').AsString;
          locstr  := cfgquery.FieldByName('SENDIT_LOCATION').AsString;
          codestr := cfgquery.FieldByName('GATEWAY').AsString;
          prodstr := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;
          telstr  := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;
          custommailstr := cfgquery.FieldByName('DEFAULT_EMAIL_ADRESS').AsString;

          if Assigned (cfgquery.FindField ('ABSENDER')) then
            absstr  := cfgquery.FieldByName('ABSENDER').AsString;

          if Assigned (cfgquery.FindField ('REST_URL')) then
            urlstr  := cfgquery.FieldByName('REST_URL').AsString;

          if (Length (urlstr) = 0) then
            urlstr  := cfgquery.FieldByName('SPED_REST_URL').AsString;

          if (Length (urlstr) = 0) then
            ErrorText := 'Not REST url defined';
        end;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        if (Length (absstr) > 0) then begin
          strlist := TStringList.Create;
          try
            strlist.Delimiter := ';';
            strlist.StrictDelimiter := true;

            strlist.DelimitedText := absstr;

            if (strlist.Count < 5) then begin
              res := 24;
              ErrorText := 'Sender address not complete';
            end else begin
              absname := strlist [0];
              absstrasse := strlist [1];
              absplz := strlist [2];
              absort := strlist [3];
              absland := strlist [4];
            end;
          finally
            strlist.Free;
          end;
        end;
      end;

      if (res = 0) then begin
        nve := query.FieldByName('NVE_NR').AsString;

        if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
          orderid := query.FieldByName('AUFTRAG_NR').AsString
        else
          orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;

        if Assigned (SendITLog) then begin
          SendITLog.Logging (clNormal, 'Versand: PostNL, RefNVE: '+query.FieldByName('REF_NVE').AsString+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
        end;

        sdata := TMemoryStream.Create;

        try
          ForceDirectories(DatenPath + RESTDumpDir+'Post_NL\'+FormatDateTime ('yyyymmdd', Now));

          if query.FieldByName('BRUTTO_GEWICHT').IsNull then
            gw := 0
          else
            gw := query.FieldByName('BRUTTO_GEWICHT').AsFloat;

          //Token erzeugen
          urlparam := '';

          body := '{'
                  +'"Customer": {'
                    +'"Address": {'
                        +'"AddressType": "02",';

          //Absender Adresse übergeben
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            //Bei Dropship wird die Traderadresse angezeigt
            cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                               +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                               +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                               +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                               +'coalesce (translate (tadr.LAND using CHAR_CS),loc.LAND,''DE'') as LAND,'
                               +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON'
                              +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                              +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                              +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                              +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                              +' where auf.REF=:ref_auf'
                              );

            cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

            cfgquery.Open;

            if (cfgquery.FieldByName('REF').IsNull) then begin
              cfgquery.Close;

              cfgquery.SQL.Clear;
              cfgquery.SQL.Add ( 'select coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as NAME1,ml.ABSENDER_ZUSATZ as NAMEZUSATZ,nvl (ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE'
                                +',nvl (ml.ABSENDER_PLZ,loc.PLZ) as PLZ,nvl (ml.ABSENDER_ORT,loc.ORT) as ORT,nvl (loc.LAND,''DE'') as LAND,loc.TELEFON'
                                +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                +' where m.REF=:ref_mand'
                                );

              cfgquery.Params.ParamByName('ref_loc').Value := query.FieldByName('REF_LOCATION').AsInteger;

              if query.FieldByName('REF_SUB_MAND').IsNull then
                cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
              else cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

              cfgquery.Open;
            end;

            numstr := '';

            if (Length (absstrasse) > 0) then
              streetstr := absstrasse
            else
              streetstr := cfgquery.FieldByName ('STRASSE').AsString;

            idx := Length (streetstr);
            while (idx > 1) and (streetstr [idx] <> ' ') do
              Dec (idx);

            if (idx > 1) then begin
              numstr := copy (streetstr, idx + 1);

              while (idx > 1) and (streetstr [idx] = ' ') do
                Dec (idx);

              streetstr := copy (streetstr, 1, idx);
            end;

            if (Length (absname) = 0) then
              absname := cfgquery.FieldByName ('NAME1').AsString;

            if (Length (absort) = 0) then
              absort := cfgquery.FieldByName ('ORT').AsString;

            if (Length (absplz) = 0) then
              absplz := cfgquery.FieldByName ('PLZ').AsString;

            if (Length (absland) = 0) then
              absland := cfgquery.FieldByName ('LAND').AsString;


            body := body
                          +'"City": "'+ConvertJSONSonderzeichen (absort)+'",'
                          +'"CompanyName": "'+AnsiToUtf8 (ConvertJSONSonderzeichen (absname))+'",'
                          +'"Street": "'+AnsiToUtf8 (ConvertJSONSonderzeichen (streetstr))+'",'
                          +'"HouseNr": "'+numstr+'",'
                          +'"Zipcode": "'+absplz+'",'
                          +'"Countrycode": "'+ConvertJSONSonderzeichen (absland)+'"';

            cfgquery.Close;

          finally
            cfgquery.Free;
          end;

          numstr := '';
          streetstr := trim (liefquery.FieldByName ('STRASSE').AsString);
          idx := Length (streetstr);
          while (idx > 1) and (streetstr [idx] <> ' ') do
            Dec (idx);

          if (idx > 1) then begin
            numstr := copy (streetstr, idx + 1);

            while (idx > 1) and (streetstr [idx] = ' ') do
              Dec (idx);

            streetstr := copy (streetstr, 1, idx);
          end;

          body := body
                    +'},'
                    +'"CollectionLocation": "'+locstr+'",'
                    +'"CustomerCode": "'+codestr+'",'
                    +'"CustomerNumber": "'+customstr+'",'
                    +'"Email": "'+custommailstr+'"'
                +'},'
                +'"Message": {'
                    +'"Printertype": "'+prttype+'"'
                +'},'
                +'"Shipments": ['
                    +'{'
                        //+'"Barcode": "'+query.FieldByName('NVE_NR').AsString+'",'
                        +'"Reference": "'+query.FieldByName('AUFTRAG_NR').AsString+'",'
                        +'"ReferenceCollect": "'+query.FieldByName('AUFTRAG_NR').AsString+'",'
                        +'"CustomerOrderNumber": "'+query.FieldByName('AUFTRAG_NR').AsString+'",'
                        +'"Remark": "'+query.FieldByName('AUFTRAG_NR').AsString+'",'
                        +'"CostCenter": "'+query.FieldByName('MANDANT_ERP_ID').AsString+'",'
                        +'"Addresses": ['
                        +'{'
                            +'"AddressType": "01",'
                            +'"City": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('ORT').AsString)+'",'
                            +'"Countrycode": "'+landstr+'",';

           body := body     +'"FirstName": "",'
                            +'"HouseNr": "'+numstr+'",'
                            +'"HouseNrExt": "",'
                            +'"Name": "'+AnsiToUtf8 (ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('NAME1').AsString, 1, 35), '&', '', [rfReplaceAll])))+'",'
                            +'"Street": "'+AnsiToUtf8 (ConvertJSONSonderzeichen (streetstr))+'",'
                            +'"Zipcode": "'+liefquery.FieldByName ('PLZ').AsString+'"'
                        +'}'
                        +'],'
                        +'"Contacts": ['
                            +'{'
                                +'"ContactType": "01",';

          if not (kepemail) or liefquery.FieldByName('EMAIL').IsNull then
            body := body +'    "Email": "'+ConvertJSONSonderzeichen (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString)+'",'
          else
            body := body +'    "Email": "'+ConvertJSONSonderzeichen (liefquery.FieldByName('EMAIL').AsString)+'",';

          body := body      +'"SMSNr": ""'
                            +'}'
                        +'],';

          if not IsLandEU (query.FieldByName('LIEFER_LAND_ISO').AsString) then begin
            body := body +
              '"Customs": {'
               +'"Content": [';

            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              //Die Zollinfos der Artikel
              cfgquery.SQL.Clear;
              //Alle Positionen des Auftrags, welche keine Textartikel sind, das Gewicht ist das Artikelgewicht
              cfgquery.SQL.Add('select mcfg.OPT_AUTO_CUSTOMS,ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN'') as ARTIKEL_TEXT,pos.MENGE_GESAMT,vpe.KURZ_BEZEICHNUNG,nvl (ar.COUNTRY_OF_ORIGIN, ''DE'') as COUNTRY_OF_ORIGIN'
                              +',nvl (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER) as TARIC_NUMBER,nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT) as BRUTTO_GEWICHT'
                              +',nvl (case when rep.NETTO_BETRAG=0 then null else rep.NETTO_BETRAG end,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT) as NETTO_BETRAG'
                              +' from VQ_AUFTRAG auf, V_AUFTRAG_RECHNUNG re, V_AUFTRAG_POS pos, V_AUFTRAG_POS_RECHNUNG rep, V_ARTIKEL ar, VQ_ARTIKEL_EINHEIT ae, V_ARTIKEL_VPE vpe, V_MANDANT_CONFIG mcfg'
                              +' where pos.REF_AUF_KOPF=auf.REF and ar.REF=pos.REF_AR and ae.REF=pos.REF_AR_EINHEIT and vpe.REF=ae.REF_EINHEIT and mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND)'
                              +' and pos.REF_PARENT_POS is null and re.REF_AUF_KOPF=auf.REF and rep.REF_AUF_POS=pos.REF and nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0 and auf.REF=:ref'
                              );
              cfgquery.Params [0].Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

              cfgquery.Open;

              while not (cfgquery.Eof) do begin
                if (cfgquery.RecNo > 1) then body := body + ',';

                body := body
                   +'{'
                     +'"CountryOfOrigin": "'+cfgquery.FieldByName('COUNTRY_OF_ORIGIN').AsString+'",'
                     +'"Description": "'+AnsiToUtf8 (ConvertJSONSonderzeichen (cfgquery.FieldByName('ARTIKEL_TEXT').AsString))+'",'
                     +'"HSTariffNr": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName('TARIC_NUMBER').AsString)+'",'
                     +'"Quantity": "'+cfgquery.FieldByName('MENGE_GESAMT').AsString+'",';

                //PriceX, Betrag ist min. 10 Cent
                if (cfgquery.FieldByName('NETTO_BETRAG').IsNull or (cfgquery.FieldByName('NETTO_BETRAG').AsInteger < 10)) then
                  body := body + '"Value": "'+Format('%6.3f', [0.1])+'",'
                else
                  body := body + '"Value": "'+Format('%6.3f', [cfgquery.FieldByName('NETTO_BETRAG').AsInteger / 1000])+'",';

                body := body
                     +'"Weight": "'+cfgquery.FieldByName('BRUTTO_GEWICHT').AsString+'"'
                   +'}';

                cfgquery.Next;
              end;

              body := body + ']';

              cfgquery.Close;
            finally
              cfgquery.Free;
            end;

            body := body
                +','
                +'"Currency": "'+query.FieldByName('CURRENCY').AsString+'",'
                +'"HandleAsNonDeliverable": "false",'
                +'"Invoice": "true",';

            if (query.FieldByName('RECHNUNGS_NR').IsNull) then
              body := body +'"InvoiceNr": "'+ConvertJSONSonderzeichen (query.FieldByName('AUFTRAG_NR').AsString)+'",'
            else
              body := body +'"InvoiceNr": "'+ConvertJSONSonderzeichen (query.FieldByName('RECHNUNGS_NR').AsString)+'",';

            body := body
                +'"ShipmentType": "Commercial Goods"'
            +'},';
        end;


          body := body +'"Dimension": {'
                            +'"Height": "'+query.FieldByName('L').AsString+'",'
                            +'"Length": "'+query.FieldByName('B').AsString+'",'
                            +'"Width": "'+query.FieldByName('H').AsString+'",'
                            +'"Weight": "'+IntToStr (round (gw))+'"'
                        +'},'
                        +'"ProductCodeDelivery": "'+prodstr+'"'
                    +'}'
                +']'
            +'}';

          StrToFile (DatenPath + RESTDumpDir+'Post_NL\'+FormatDateTime ('yyyymmdd', Now)+'\post_labels_'+nve+'.json', body);

          sdata.Clear;
          if SendRequest(urlstr, // Host,
                          -1, //Port
                          'v1/shipment', // Service
                          'POST', //Methode
                          '', //Kein proxy
                          //'localhost:8888', // Fiddler als Proxy
                          '', '', // User , PW
                          '', //Action
                          'application/json', //ContentType
                          ['apikey: '+keystr], //AddHeader
                          body,         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            labelurl := '';

            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+'Post_NL\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nve+'.json');

            sdata.Position := 0;
            js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;

            if not Assigned (js) then begin
              ErrorText := 'API error message';
            end else begin
              try
                fs := js.Field['ResponseShipments'];

                if not Assigned (fs) or (fs.Count = 0) then begin
                  res := 37;

                  fs := js.Field['Errors'];
                  if Assigned (fs) then begin
                    for idx := 0 to fs.Count - 1 do begin
                      us := fs.Child [idx].Field['Description'];

                      if Assigned (us) then
                        ErrorText := ErrorText + ':' + StringReplace (us.Value, '\u0027', '''', [rfReplaceAll]);
                    end;
                  end;

                  if (Length (ErrorText) = 0) then
                    ErrorText := 'tracking code error';
                end else begin
                  us := fs.Child [0].Field['Barcode'];

                  if Assigned (us) and not (us.Value = NULL) then
                    SendungsNr := us.Value;

                  us := fs.Child [0].Field['Labels'];
                  if Assigned (us) and (us.Count > 0) then
                    us := us.Child [0].Field['Content'];

                  if not Assigned (us) then begin
                    res := 38;
                    ErrorText := 'label_zpl error'
                  end else begin
                    outstr := DecodeString (us.Value);

                    LabelFormat := 'zpl';

                    ForceDirectories(DatenPath + LabelDumpDir + 'PostNL');

                    LabelImage.WriteBuffer (Pointer(outstr)^, Length(outstr));

                    try
                      LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'PostNL\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                    except
                    end;

                    if (res <> 0) then
                      ErrorText := 'Error '+IntToStr (res) + ' while save shipping label data';
                  end;
                end;
              except
                res := 37;
                ErrorText := 'Label data error';
              end;
            end;
          end else begin
            res := 21;

            ErrorText := 'Fehler beim PostNL server';

            if (Length (errtext) > 0) then
              ErrorText := ErrorText + #13 + errtext;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: PostNL, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

  //******************************************************************************
  //* Function Name: CreateSpringLabel
  //* Author       : Stefan Graf
  //* Datum        : 30.03.2022
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateCourierLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    res,
    strpos,
    errcode  : Integer;
    errtext  : String;
    urlparam : String;
    body     : String;
    resp     : String;
    nve      : String;
    labelurl : String;
    orderid  : String;
    telstr   : String;
    prodstr,
    urlstr   : String;
    js       : TlkJSONobject;
    fs,
    us       : TlkJSONbase;
    keystr   : String;
    outstr   : AnsiString;
    sdata    : TMemoryStream;
    cfgquery : TSmartQuery;
    olddec   : Char;
    gw       : Double;

    {$ifdef UNICODE}
      utfstr       : AnsiString;
      datastr      : String;
    {$endif}

    function GetDim (Field : TField; const Faktor : Integer) : String;
    begin
      if Field.IsNull then
        Result := ''
      else if Field.AsInteger = 0 then
        Result := ''
      else
        Result := IntToStr (Field.AsInteger div 10);
    end;

  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      TrackUrl  := '';
      Barcode   := '';
      ErrorText := '';

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select cfg.REST_URL, g.SENDIT_PRODUKT, g.API_KEY, g.DEFAULT_PHONE_NUMBER from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if cfgquery.FieldByName('SENDIT_PRODUKT').IsNull then
          ErrorText := 'No product defined'
        else if cfgquery.FieldByName('REST_URL').IsNull then
          ErrorText := 'Not REST url defined'
        else if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else begin
          keystr  := cfgquery.FieldByName('API_KEY').AsString;
          urlstr  := cfgquery.FieldByName('REST_URL').AsString;
          prodstr := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;
          telstr  := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;

          strpos := Pos (';', urlstr);

          if (strpos = 0) then
            urlparam := ''
          else begin
            urlparam := Copy (urlstr, strpos + 1);
            urlstr := Copy (urlstr, 1, strpos - 1);
          end;
        end;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        nve := query.FieldByName('NVE_NR').AsString;

        if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
          orderid := query.FieldByName('AUFTRAG_NR').AsString
        else
          orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;

        if Assigned (SendITLog) then begin
          SendITLog.Logging (clNormal, 'Versand: '+Versender+', RefNVE: '+query.FieldByName('REF_NVE').AsString+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
        end;

        sdata := TMemoryStream.Create;

        try
          ForceDirectories(DatenPath + RESTDumpDir+Versender+'\'+FormatDateTime ('yyyymmdd', Now));

          if query.FieldByName('BRUTTO_GEWICHT').IsNull then
            gw := 0.1
          else
            gw := query.FieldByName('BRUTTO_GEWICHT').AsFloat;

          body := '{'
                    +' "Apikey": "'+keystr+'",'
                    +' "Command": "OrderShipment",'
                    +' "Shipment": {'
                    +' "LabelFormat": "ZPL200",'
                    +' "Source": "storelogix V4.8",'
                    +' "ShipperReference": "'+query.FieldByName('NVE_NR').AsString+'",'
                    +' "OrderReference": "'+query.FieldByName('AUFTRAG_NR').AsString+'",'
                    +' "DisplayId": "",';

            if (query.FieldByName('RECHNUNGS_NR').IsNull) then
              body := body +'"InvoiceNumber": "'+ConvertJSONSonderzeichen (query.FieldByName('AUFTRAG_NR').AsString)+'",'
            else
              body := body +'"InvoiceNumber": "'+ConvertJSONSonderzeichen (query.FieldByName('RECHNUNGS_NR').AsString)+'",';

           body := body
                    +'  "Service": "'+prodstr+'",'
                    +'  "ConsignorAddress": {';
          //Absender Adresse übergeben
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            //Bei Dropship wird die Traderadresse angezeigt
            cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                               +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                               +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                               +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                               +'coalesce (translate (tadr.LAND using CHAR_CS),loc.LAND,''DE'') as LAND,'
                               +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON,'
                               +'coalesce (tadr.ANSPRECHPARTNER,loc.ANSPRECHPARTNER) as CONTACT'
                              +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                              +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                              +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                              +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                              +' where auf.REF=:ref_auf'
                              );

            cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

            cfgquery.Open;

            if (cfgquery.FieldByName('REF').IsNull) then begin
              cfgquery.Close;

              cfgquery.SQL.Clear;
              cfgquery.SQL.Add ( 'select coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as NAME1,ml.ABSENDER_ZUSATZ as NAMEZUSATZ,nvl (ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE'
                                +',nvl (ml.ABSENDER_PLZ,loc.PLZ) as PLZ,nvl (ml.ABSENDER_ORT,loc.ORT) as ORT,nvl (loc.LAND,''DE'') as LAND,nvl (ml.ABSENDER_TELEFON, loc.TELEFON),nvl (ml.ABSENDER_CONTACT, loc.ANSPRECHPARTNER) as CONTACT'
                                +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                +' where m.REF=:ref_mand'
                                );

              cfgquery.Params.ParamByName('ref_loc').Value := query.FieldByName('REF_LOCATION').AsInteger;

              if query.FieldByName('REF_SUB_MAND').IsNull then
                cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
              else cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

              cfgquery.Open;
            end;

            body := body
              +'"Company": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('NAME1').AsString)+'",'
              +'"Name": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('CONTACT').AsString)+'"';

            cfgquery.Close;

          finally
            cfgquery.Free;
          end;

          body := body
                +'},'
                +'"ConsigneeAddress": {'
                  +'"City": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('ORT').AsString)+'",'
                  +'"State": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STATE').AsString)+'",'
                  +'"Country": "'+landstr+'",';

          if (liefquery.FieldByName ('COMPANY').IsNull) then begin
            body := body
                    +'"Name": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('NAME1').AsString, 1, 35), '&', '', [rfReplaceAll]))+'",';

            if (liefquery.FieldByName ('NAME2').IsNull) then begin
              body := body
                    +'"AddressLine1": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE').AsString)+'",'
                    +'"AddressLine2": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE_2').AsString)+'",';
            end else begin
              body := body
                    +'"AddressLine1": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('NAME2').AsString)+'",'
                    +'"AddressLine2": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE').AsString)+'",'
                    +'"AddressLine3": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE_2').AsString)+'",';
            end;
          end else begin
            body := body
                  +'"Company": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('COMPANY').AsString, 1, 35), '&', '', [rfReplaceAll]))+'",'
                  +'"Name": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('NAME2').AsString, 1, 35), '&', '', [rfReplaceAll]))+'",';

            if (liefquery.FieldByName ('NAMEZUSATZ').IsNull) then begin
              body := body
                    +'"AddressLine1": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE').AsString)+'",'
                    +'"AddressLine2": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE_2').AsString)+'",';
            end else begin
              body := body
                    +'"AddressLine1": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('NAMEZUSATZ').AsString)+'",'
                    +'"AddressLine2": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE').AsString)+'",'
                    +'"AddressLine3": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE_2').AsString)+'",';
            end;
          end;

          body := body +'"Zip": "'+liefquery.FieldByName ('PLZ').AsString+'",';

          if not (liefquery.FieldByName ('TELEFON').IsNull) then
            body := body +'"Phone": "'+liefquery.FieldByName ('TELEFON').AsString+'",'
          else
            body := body +'"Phone": "'+telstr+'",';

          if not (kepemail) or liefquery.FieldByName('EMAIL').IsNull then
            body := body +'"Email": "'+ConvertJSONSonderzeichen (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString)+'"'
          else
            body := body +'"Email": "'+ConvertJSONSonderzeichen (liefquery.FieldByName('EMAIL').AsString)+'"';

          body := body
                +'},'
                  +'"Height": "'+GetDim (query.FieldByName('L'), 10)+'",'
                  +'"Length": "'+GetDim (query.FieldByName('B'), 10)+'",'
                  +'"Width": "'+GetDim (query.FieldByName('H'), 10)+'",'
                  +'"DimUnit": "cm",'
                  +'"Weight": "'+Format('%6.3f', [gw])+'",'
                  +'"WeightUnit": "kg"';

          if not IsLandEU (query.FieldByName('LIEFER_LAND_ISO').AsString) then begin
            body := body
              +',"Description": "CD",'
              +'"DeclarationType": "SaleOfGoods",'
              +'"Products": [';

            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              //Die Zollinfos der Artikel
              cfgquery.SQL.Clear;
              //Alle Positionen des Auftrags, welche keine Textartikel sind, das Gewicht ist das Artikelgewicht
              cfgquery.SQL.Add('select mcfg.OPT_AUTO_CUSTOMS,ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN'') as ARTIKEL_TEXT,pos.MENGE_GESAMT,vpe.KURZ_BEZEICHNUNG,nvl (ar.COUNTRY_OF_ORIGIN, ''DE'') as COUNTRY_OF_ORIGIN'
                              +',nvl (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER) as TARIC_NUMBER,nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT) as BRUTTO_GEWICHT'
                              +',nvl (case when rep.NETTO_BETRAG=0 then null else rep.NETTO_BETRAG end,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT) as NETTO_BETRAG'
                              +' from VQ_AUFTRAG auf, V_AUFTRAG_RECHNUNG re, V_AUFTRAG_POS pos, V_AUFTRAG_POS_RECHNUNG rep, V_ARTIKEL ar, VQ_ARTIKEL_EINHEIT ae, V_ARTIKEL_VPE vpe, V_MANDANT_CONFIG mcfg'
                              +' where pos.REF_AUF_KOPF=auf.REF and ar.REF=pos.REF_AR and ae.REF=pos.REF_AR_EINHEIT and vpe.REF=ae.REF_EINHEIT and mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND)'
                              +' and pos.REF_PARENT_POS is null and re.REF_AUF_KOPF=auf.REF and rep.REF_AUF_POS=pos.REF and nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0 and auf.REF=:ref'
                              );
              cfgquery.Params [0].Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

              cfgquery.Open;

              while not (cfgquery.Eof) do begin
                if (cfgquery.RecNo > 1) then body := body + ',';

                body := body
                   +'{'
                     +'"Sku": "'+cfgquery.FieldByName('ARTIKEL_NR').AsString+'",'
                     +'"OriginCountry": "'+cfgquery.FieldByName('COUNTRY_OF_ORIGIN').AsString+'",'
                     +'"Description": "'+ConvertJSONSonderzeichen (copy (cfgquery.FieldByName('ARTIKEL_TEXT').AsString, 1, 40))+'",'
                     +'"HsCode": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName('TARIC_NUMBER').AsString)+'",'
                     +'"Quantity": "'+cfgquery.FieldByName('MENGE_GESAMT').AsString+'",';

                //PriceX, Betrag ist min. 10 Cent
                if (cfgquery.FieldByName('NETTO_BETRAG').IsNull or (cfgquery.FieldByName('NETTO_BETRAG').AsInteger < 10)) then
                  body := body + '"Value": "'+Format('%6.3f', [0.1])+'",'
                else
                  body := body + '"Value": "'+Format('%6.3f', [cfgquery.FieldByName('NETTO_BETRAG').AsInteger / 1000])+'",';

                body := body
                     +'"Weight": "'+Format('%6.3f', [cfgquery.FieldByName('BRUTTO_GEWICHT').AsInteger / 1000])+'"'
                   +'}';

                cfgquery.Next;
              end;

              body := body + ']';

              cfgquery.Close;
            finally
              cfgquery.Free;
            end;
          end;

          body := body
            +'  }'
            +'}';


          StrToFile (DatenPath + RESTDumpDir+Versender+'\'+FormatDateTime ('yyyymmdd', Now)+'\post_labels_'+nve+'.json', body);

          sdata.Clear;
          if SendRequest(urlstr, // Host,
                          -1, //Port
                          urlparam, // Service
                          'POST', //Methode
                          '', //Kein proxy
                          //'localhost:8888', // Fiddler als Proxy
                          '', '', // User , PW
                          '', //Action
                          'text/json', //ContentType
                          [], //AddHeader
                          UTF8Encode (body),         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            StrToFile (DatenPath + RESTDumpDir+Versender+'\'+FormatDateTime ('yyyymmdd', Now)+'\labels_resp_'+nve+'.txt', resp);

            labelurl := '';

            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+Versender+'\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nve+'.json');

            {$ifdef UNICODE}
              SetLength(utfstr, sdata.Size);
              sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
              datastr := UTF8ToString (utfstr);

              js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
            {$else}
              sdata.Position := 0;
              js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
            {$endif}

            if not Assigned (js) then begin
              ErrorText := 'API error message';
            end else begin
              try
                fs := js.Field['ErrorLevel'];
                if not Assigned (fs) then begin
                  res := 37;
                  ErrorText := 'tracking code error';

                  fs := js.Field['detail'];
                  if Assigned (fs) then
                    ErrorText := ErrorText + ':' + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll]);
                end else if (fs.Value > 0) then begin
                  res := 28;
                  fs := js.Field['Error'];
                  if Assigned (fs) then
                    ErrorText := StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll]);
                end else begin
                  fs := js.Field['Shipment'];

                  if not Assigned (fs) then begin
                    res := 37;
                    ErrorText := 'tracking code error';

                    fs := js.Field['detail'];
                    if Assigned (fs) then
                      ErrorText := ErrorText + ':' + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll]);
                  end else begin
                    us := fs.Field['TrackingNumber'];

                    if Assigned (us) and not (us.Value = NULL) then
                      SendungsNr := us.Value;

                    us := fs.Field['CarrierTrackingUrl'];

                    if Assigned (us) and not (us.Value = NULL) then
                      TrackUrl := StringReplace (us.Value, '\/', '//', [rfReplaceAll]);

                    us := fs.Field['CarrierLocalTrackingNumber'];

                    if Assigned (us) and not (us.Value = NULL) then
                      Barcode := us.Value;

                    us := fs.Field['LabelImage'];

                    if not Assigned (us) then begin
                      res := 38;
                      ErrorText := 'label_zpl error'
                    end else begin
                      outstr := DecodeString (us.Value);

                      LabelFormat := 'zpl';

                      ForceDirectories(DatenPath + LabelDumpDir + Versender);

                      LabelImage.WriteBuffer (Pointer(outstr)^, Length(outstr));

                      try
                        LabelImage.SaveToFile(DatenPath + LabelDumpDir + Versender + '\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                      except
                      end;

                      if (res <> 0) then
                        ErrorText := 'Error '+IntToStr (res) + ' while save shipping label data';
                    end;
                  end;
                end;
              except
                res := 37;
                ErrorText := 'Label data error';
              end;
            end;
          end else begin
            res := 21;

            ErrorText := 'Fehler beim eCourier server';

            if (Length (errtext) > 0) then
              ErrorText := ErrorText + #13 + errtext;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: '+Versender+', Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

  //******************************************************************************
  //* Function Name: CreateSpringLabel
  //* Author       : Stefan Graf
  //* Datum        : 30.03.2022
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateSpringLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    res,
    strpos,
    errcode  : Integer;
    errtext  : String;
    urlparam : String;
    body     : String;
    resp     : String;
    nve      : String;
    labelurl : String;
    orderid  : String;
    telstr   : String;
    prodstr,
    urlstr   : String;
    js       : TlkJSONobject;
    fs,
    us       : TlkJSONbase;
    keystr   : String;
    outstr   : AnsiString;
    sdata    : TMemoryStream;
    cfgquery : TSmartQuery;
    olddec   : Char;
    gw       : Double;

    {$ifdef UNICODE}
      utfstr       : AnsiString;
      datastr      : String;
    {$endif}

    function GetDim (Field : TField; const Faktor : Integer) : String;
    begin
      if Field.IsNull then
        Result := ''
      else if Field.AsInteger = 0 then
        Result := ''
      else
        Result := IntToStr (Field.AsInteger div 10);
    end;

  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      TrackUrl  := '';
      Barcode   := '';
      ErrorText := '';

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select cfg.REST_URL, g.SENDIT_PRODUKT, g.API_KEY, g.DEFAULT_PHONE_NUMBER from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if cfgquery.FieldByName('SENDIT_PRODUKT').IsNull then
          ErrorText := 'No product defined'
        else if cfgquery.FieldByName('REST_URL').IsNull then
          ErrorText := 'Not REST url defined'
        else if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else begin
          keystr  := cfgquery.FieldByName('API_KEY').AsString;
          urlstr  := cfgquery.FieldByName('REST_URL').AsString;
          prodstr := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;
          telstr  := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;

          strpos := Pos (';', urlstr);

          if (strpos = 0) then
            urlparam := ''
          else begin
            urlparam := Copy (urlstr, strpos + 1);
            urlstr := Copy (urlstr, 1, strpos - 1);
          end;
        end;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        nve := query.FieldByName('NVE_NR').AsString;

        if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
          orderid := query.FieldByName('AUFTRAG_NR').AsString
        else
          orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;

        if Assigned (SendITLog) then begin
          SendITLog.Logging (clNormal, 'Versand: Spring, RefNVE: '+query.FieldByName('REF_NVE').AsString+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
        end;

        sdata := TMemoryStream.Create;

        try
          ForceDirectories(DatenPath + RESTDumpDir+'Spring\'+FormatDateTime ('yyyymmdd', Now));

          if query.FieldByName('BRUTTO_GEWICHT').IsNull then
            gw := 0.1
          else
            gw := query.FieldByName('BRUTTO_GEWICHT').AsFloat;

          body := '{'
                    +' "Apikey": "'+keystr+'",'
                    +' "Command": "OrderShipment",'
                    +' "Shipment": {'
                    +' "LabelFormat": "ZPL200",'
                    +' "Source": "storelogix V4.8",'
                    +' "ShipperReference": "'+query.FieldByName('NVE_NR').AsString+'",'
                    +' "OrderReference": "'+query.FieldByName('AUFTRAG_NR').AsString+'",'
                    +' "DisplayId": "",';

            if (query.FieldByName('RECHNUNGS_NR').IsNull) then
              body := body +'"InvoiceNumber": "'+ConvertJSONSonderzeichen (query.FieldByName('AUFTRAG_NR').AsString)+'",'
            else
              body := body +'"InvoiceNumber": "'+ConvertJSONSonderzeichen (query.FieldByName('RECHNUNGS_NR').AsString)+'",';

           body := body
                    +'  "Service": "'+prodstr+'",'
                    +'  "ConsignorAddress": {';
          //Absender Adresse übergeben
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            //Bei Dropship wird die Traderadresse angezeigt
            cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                               +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                               +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                               +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                               +'coalesce (translate (tadr.LAND using CHAR_CS),loc.LAND,''DE'') as LAND,'
                               +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON,'
                               +'coalesce (tadr.ANSPRECHPARTNER,loc.ANSPRECHPARTNER) as CONTACT'
                              +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                              +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                              +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                              +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                              +' where auf.REF=:ref_auf'
                              );

            cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

            cfgquery.Open;

            if (cfgquery.FieldByName('REF').IsNull) then begin
              cfgquery.Close;

              cfgquery.SQL.Clear;
              cfgquery.SQL.Add ( 'select coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as NAME1,ml.ABSENDER_ZUSATZ as NAMEZUSATZ,nvl (ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE'
                                +',nvl (ml.ABSENDER_PLZ,loc.PLZ) as PLZ,nvl (ml.ABSENDER_ORT,loc.ORT) as ORT,nvl (loc.LAND,''DE'') as LAND,nvl (ml.ABSENDER_TELEFON, loc.TELEFON),nvl (ml.ABSENDER_CONTACT, loc.ANSPRECHPARTNER) as CONTACT'
                                +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                +' where m.REF=:ref_mand'
                                );

              cfgquery.Params.ParamByName('ref_loc').Value := query.FieldByName('REF_LOCATION').AsInteger;

              if query.FieldByName('REF_SUB_MAND').IsNull then
                cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
              else cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

              cfgquery.Open;
            end;

            body := body
              +'"Company": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('NAME1').AsString)+'",'
              +'"Name": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('CONTACT').AsString)+'"';

            cfgquery.Close;

          finally
            cfgquery.Free;
          end;

          body := body
                +'},'
                +'"ConsigneeAddress": {'
                  +'"City": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('ORT').AsString)+'",'
                  +'"State": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STATE').AsString)+'",'
                  +'"Country": "'+landstr+'",';

          if (liefquery.FieldByName ('COMPANY').IsNull) then begin
            body := body
                    +'"Name": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('NAME1').AsString, 1, 35), '&', '', [rfReplaceAll]))+'",';

            if (liefquery.FieldByName ('NAME2').IsNull) then begin
              body := body
                    +'"AddressLine1": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE').AsString)+'",'
                    +'"AddressLine2": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE_2').AsString)+'",';
            end else begin
              body := body
                    +'"AddressLine1": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('NAME2').AsString)+'",'
                    +'"AddressLine2": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE').AsString)+'",'
                    +'"AddressLine3": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE_2').AsString)+'",';
            end;
          end else begin
            body := body
                  +'"Company": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('COMPANY').AsString, 1, 35), '&', '', [rfReplaceAll]))+'",'
                  +'"Name": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('NAME2').AsString, 1, 35), '&', '', [rfReplaceAll]))+'",';

            if (liefquery.FieldByName ('NAMEZUSATZ').IsNull) then begin
              body := body
                    +'"AddressLine1": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE').AsString)+'",'
                    +'"AddressLine2": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE_2').AsString)+'",';
            end else begin
              body := body
                    +'"AddressLine1": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('NAMEZUSATZ').AsString)+'",'
                    +'"AddressLine2": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE').AsString)+'",'
                    +'"AddressLine3": "'+ConvertJSONSonderzeichen (liefquery.FieldByName ('STRASSE_2').AsString)+'",';
            end;
          end;

          body := body +'"Zip": "'+liefquery.FieldByName ('PLZ').AsString+'",';

          if not (liefquery.FieldByName ('TELEFON').IsNull) then
            body := body +'"Phone": "'+liefquery.FieldByName ('TELEFON').AsString+'",'
          else
            body := body +'"Phone": "'+telstr+'",';

          if not (kepemail) or liefquery.FieldByName('EMAIL').IsNull then
            body := body +'"Email": "'+ConvertJSONSonderzeichen (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString)+'"'
          else
            body := body +'"Email": "'+ConvertJSONSonderzeichen (liefquery.FieldByName('EMAIL').AsString)+'"';

          body := body
                +'},'
                  +'"Height": "'+GetDim (query.FieldByName('L'), 10)+'",'
                  +'"Length": "'+GetDim (query.FieldByName('B'), 10)+'",'
                  +'"Width": "'+GetDim (query.FieldByName('H'), 10)+'",'
                  +'"DimUnit": "cm",'
                  +'"Weight": "'+Format('%6.3f', [gw])+'",'
                  +'"WeightUnit": "kg"';

          if not IsLandEU (query.FieldByName('LIEFER_LAND_ISO').AsString) then begin
            body := body
              +',"Description": "CD",'
              +'"DeclarationType": "SaleOfGoods",'
              +'"Products": [';

            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              //Die Zollinfos der Artikel
              cfgquery.SQL.Clear;
              //Alle Positionen des Auftrags, welche keine Textartikel sind, das Gewicht ist das Artikelgewicht
              cfgquery.SQL.Add('select mcfg.OPT_AUTO_CUSTOMS,ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN'') as ARTIKEL_TEXT,pos.MENGE_GESAMT,vpe.KURZ_BEZEICHNUNG,nvl (ar.COUNTRY_OF_ORIGIN, ''DE'') as COUNTRY_OF_ORIGIN'
                              +',nvl (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER) as TARIC_NUMBER,nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT) as BRUTTO_GEWICHT'
                              +',nvl (case when rep.NETTO_BETRAG=0 then null else rep.NETTO_BETRAG end,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT) as NETTO_BETRAG'
                              +' from VQ_AUFTRAG auf, V_AUFTRAG_RECHNUNG re, V_AUFTRAG_POS pos, V_AUFTRAG_POS_RECHNUNG rep, V_ARTIKEL ar, VQ_ARTIKEL_EINHEIT ae, V_ARTIKEL_VPE vpe, V_MANDANT_CONFIG mcfg'
                              +' where pos.REF_AUF_KOPF=auf.REF and ar.REF=pos.REF_AR and ae.REF=pos.REF_AR_EINHEIT and vpe.REF=ae.REF_EINHEIT and mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND)'
                              +' and pos.REF_PARENT_POS is null and re.REF_AUF_KOPF=auf.REF and rep.REF_AUF_POS=pos.REF and nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0 and auf.REF=:ref'
                              );
              cfgquery.Params [0].Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

              cfgquery.Open;

              while not (cfgquery.Eof) do begin
                if (cfgquery.RecNo > 1) then body := body + ',';

                body := body
                   +'{'
                     +'"Sku": "'+cfgquery.FieldByName('ARTIKEL_NR').AsString+'",'
                     +'"OriginCountry": "'+cfgquery.FieldByName('COUNTRY_OF_ORIGIN').AsString+'",'
                     +'"Description": "'+ConvertJSONSonderzeichen (copy (cfgquery.FieldByName('ARTIKEL_TEXT').AsString, 1, 40))+'",'
                     +'"HsCode": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName('TARIC_NUMBER').AsString)+'",'
                     +'"Quantity": "'+cfgquery.FieldByName('MENGE_GESAMT').AsString+'",';

                //PriceX, Betrag ist min. 10 Cent
                if (cfgquery.FieldByName('NETTO_BETRAG').IsNull or (cfgquery.FieldByName('NETTO_BETRAG').AsInteger < 10)) then
                  body := body + '"Value": "'+Format('%6.3f', [0.1])+'",'
                else
                  body := body + '"Value": "'+Format('%6.3f', [cfgquery.FieldByName('NETTO_BETRAG').AsInteger / 1000])+'",';

                body := body
                     +'"Weight": "'+Format('%6.3f', [cfgquery.FieldByName('BRUTTO_GEWICHT').AsInteger / 1000])+'"'
                   +'}';

                cfgquery.Next;
              end;

              body := body + ']';

              cfgquery.Close;
            finally
              cfgquery.Free;
            end;
          end;

          body := body
            +'  }'
            +'}';


          StrToFile (DatenPath + RESTDumpDir+'Spring\'+FormatDateTime ('yyyymmdd', Now)+'\post_labels_'+nve+'.json', body);

          sdata.Clear;
          if SendRequest(urlstr, // Host,
                          -1, //Port
                          urlparam, // Service
                          'POST', //Methode
                          '', //Kein proxy
                          //'localhost:8888', // Fiddler als Proxy
                          '', '', // User , PW
                          '', //Action
                          'text/json', //ContentType
                          [], //AddHeader
                          UTF8Encode (body),         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            StrToFile (DatenPath + RESTDumpDir+'Spring\'+FormatDateTime ('yyyymmdd', Now)+'\labels_resp_'+nve+'.txt', resp);

            labelurl := '';

            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+'Spring\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nve+'.json');

            {$ifdef UNICODE}
              SetLength(utfstr, sdata.Size);
              sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
              datastr := UTF8ToString (utfstr);

              js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
            {$else}
              sdata.Position := 0;
              js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
            {$endif}

            if not Assigned (js) then begin
              ErrorText := 'API error message';
            end else begin
              try
                fs := js.Field['ErrorLevel'];
                if not Assigned (fs) then begin
                  res := 37;
                  ErrorText := 'tracking code error';

                  fs := js.Field['detail'];
                  if Assigned (fs) then
                    ErrorText := ErrorText + ':' + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll]);
                end else if (fs.Value > 0) then begin
                  res := 28;
                  fs := js.Field['Error'];
                  if Assigned (fs) then
                    ErrorText := StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll]);
                end else begin
                  fs := js.Field['Shipment'];

                  if not Assigned (fs) then begin
                    res := 37;
                    ErrorText := 'tracking code error';

                    fs := js.Field['detail'];
                    if Assigned (fs) then
                      ErrorText := ErrorText + ':' + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll]);
                  end else begin
                    us := fs.Field['TrackingNumber'];

                    if Assigned (us) and not (us.Value = NULL) then
                      SendungsNr := us.Value;

                    us := fs.Field['CarrierTrackingUrl'];

                    if Assigned (us) and not (us.Value = NULL) then
                      TrackUrl := StringReplace (us.Value, '\/', '//', [rfReplaceAll]);

                    us := fs.Field['CarrierLocalTrackingNumber'];

                    if Assigned (us) and not (us.Value = NULL) then
                      Barcode := us.Value;

                    us := fs.Field['LabelImage'];

                    if not Assigned (us) then begin
                      res := 38;
                      ErrorText := 'label_zpl error'
                    end else begin
                      outstr := DecodeString (us.Value);

                      LabelFormat := 'zpl';

                      ForceDirectories(DatenPath + LabelDumpDir + 'Spring');

                      LabelImage.WriteBuffer (Pointer(outstr)^, Length(outstr));

                      try
                        LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'Spring\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                      except
                      end;

                      if (res <> 0) then
                        ErrorText := 'Error '+IntToStr (res) + ' while save shipping label data';
                    end;
                  end;
                end;
              except
                res := 37;
                ErrorText := 'Label data error';
              end;
            end;
          end else begin
            res := 21;

            ErrorText := 'Fehler beim Spring server';

            if (Length (errtext) > 0) then
              ErrorText := ErrorText + #13 + errtext;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: Spring, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

  //******************************************************************************
  //* Function Name: CreateGOLabel
  //* Author       : Stefan Graf
  //* Datum        : 24.07.2023
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateGOLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    idx,
    res,
    keyidx,
    errcode  : Integer;
    errtext  : String;
    xmlstr   : String;
    urlparam : String;
    body     : String;
    resp     : String;
    hdr      : String;
    nve      : String;
    labelurl : String;
    nrstr    : String;
    orderid  : String;
    timestr  : String;
    namestr  : String;
    telstr   : String;
    tagstr   : String;
    shipid   : String;
    gipw,
    giuser,
    gicode,
    gisede,
    numstr   : String;
    js       : TlkJSONobject;
    fs,
    us,
    errfs    : TlkJSONbase;
    prttype,
    keystr,
    streetstr,
    urlstr,
    prodstr,
    codestr,
    customstr,
    custommailstr,
    locstr,
    vorstr,
    nachstr  : String;
    outstr   : AnsiString;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    cfgquery : TSmartQuery;
    olddec   : Char;
    gw       : Double;
    found    : boolean;
    xmlrd    : TXMLFile;
    xmlnd    : TXMLTagEntry;
    xmsnd    : TXMLTagEntry;
    absstr,
    absstrasse,
    absname,
    absort,
    absplz,
    absland   : String;

    function GetDim (Field : TField; const Faktor : Integer) : String;
    begin
      if Field.IsNull then
        Result := ''
      else if Field.AsInteger = 0 then
        Result := ''
      else
        Result := IntToStr (Field.AsInteger div Faktor);
    end;

  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};
    {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

    try
      TrackUrl  := '';
      Barcode   := '';
      ErrorText := '';

      absstr := '';
      absstrasse := '';
      absname := '';
      absort := '';
      absplz := '';
      absland := '';

      if (PrtInfo.Model = 'ZPL_300') then
        prttype := 'Zebra|Generic ZPL II 300 dpi'
      else
        prttype := 'Zebra|Generic ZPL II 200 dpi';

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select cfg.REST_URL as SPED_REST_URL, g.* from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else begin
          keystr  := cfgquery.FieldByName('API_KEY').AsString;
          customstr := cfgquery.FieldByName('SENDIT_CLIENT').AsString;
          locstr  := cfgquery.FieldByName('SENDIT_LOCATION').AsString;
          codestr := cfgquery.FieldByName('GATEWAY').AsString;
          prodstr := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;
          telstr  := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;
          custommailstr := cfgquery.FieldByName('DEFAULT_EMAIL_ADRESS').AsString;

          if Assigned (cfgquery.FindField ('ABSENDER')) then
            absstr  := cfgquery.FieldByName('ABSENDER').AsString;

          if Assigned (cfgquery.FindField ('REST_URL')) then
            urlstr  := cfgquery.FieldByName('REST_URL').AsString;

          if (Length (urlstr) = 0) then
            urlstr  := cfgquery.FieldByName('SPED_REST_URL').AsString;

          if (Length (urlstr) = 0) then
            ErrorText := 'Not REST url defined';
        end;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        strlist := TStringList.Create;
        try
          strlist.Delimiter := ';';
          strlist.StrictDelimiter := true;

          strlist.DelimitedText := keystr;

          if (strlist.Count < 2) then begin
            res := 24;
            ErrorText := 'Credentials not complete';
          end else begin
            giuser := strlist [0];
            gipw := strlist [1];

            strlist.DelimitedText := customstr;

            if (strlist.Count < 2) then begin
              res := 24;
              ErrorText := 'Account not complete';
            end else begin
              gicode := strlist [0];
              gisede := strlist [1];
            end;
          end;
        finally
          strlist.Free;
        end;
      end;

      if (res = 0) then begin
        //Das wird die ShipmendID
        if (Pos ('REF=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
          shipid := query.FieldByName('KD_KOMM_NR').AsString
        else if (Pos ('REF=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
          shipid := query.FieldByName('AUF_REFERENZ').AsString
        else if query.FieldByName('AUSLIEFER_NR').IsNull then
          shipid := query.FieldByName('AUFTRAG_NR').AsString
        else
          shipid := query.FieldByName('AUSLIEFER_NR').AsString;

        nve := query.FieldByName('NVE_NR').AsString;

        if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
          orderid := query.FieldByName('AUFTRAG_NR').AsString
        else
          orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;

        if Assigned (SendITLog) then begin
          SendITLog.Logging (clNormal, 'Versand: Go, RefNVE: '+query.FieldByName('REF_NVE').AsString+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
        end;

        sdata := TMemoryStream.Create;

        try
          ForceDirectories(DatenPath + RESTDumpDir+'Go\'+FormatDateTime ('yyyymmdd', Now));

          if query.FieldByName('BRUTTO_GEWICHT').IsNull then
            gw := 0
          else
            gw := query.FieldByName('BRUTTO_GEWICHT').AsFloat;

          //Token erzeugen
          urlparam := '';

          body := '{'
                 +'"responsibleStation": "'+gicode+'",'
                 +'"customerId": "'+gisede+'",'
                 +'"shipment": {'
                 +'  "hwbNumber": "",'
                 +'  "orderStatus": "New",'
                 +'  "service": "ON",'
                 +'  "customerReference": "'+shipid+'",'
                 +'  "content": "",'
                 +'  "validation": "",'
                 +'  "dimensions": "",'
                 +'  "selfPickup": "",'
                 +'  "selfDelivery": "",'
                 +'  "receiptNotice": "",'
                 +'  "freightCollect": "",'
                 +'  "isNeutralPickup": "",'
                 +'  "packageCount": "1"';

          if (query.FieldByName('BRUTTO_GEWICHT').AsInteger >= 1) then
            body := body +',"weight": "'+query.FieldByName('BRUTTO_GEWICHT').AsString+'"'
          else
            body := body +',"weight": "1"';

          body := body
                 +',"pickup": {'
                 +'  "date": "'+DateToStr (Now)+'",'
                 +'  "timeFrom": "14:00",'
                 +'  "timeTill": "16:00"'
                 +'}';

          body := body
                 +',"delivery": {'
                 +'   "date": "",'
                 +'   "avisFrom": "",'
                 +'   "avisTill": "",'
                 +'   "timeFrom": "",'
                 +'   "timeTill": "",'
                 +'   "weekendOrHolidayIndicator": ""'
                 +'}';

          body := body
                 +',"insurance": {'
                 +' 	"amount": "",'
                 +'	  "currency": ""'
                 +'}';

          body := body
                 +',"valueOfGoods": {'
                 +' 	"amount": "",'
                 +' 	"currency": ""'
                 +'}';

          body := body
                 +',"cashOnDelivery": {'
                 +' 	"amount": "",'
                 +'	  "currency": ""'
                 +'}';

          body := body
                 +'}'
                 ;

          //Absender Adresse übergeben
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            //Bei Dropship wird die Traderadresse angezeigt
            cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                               +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                               +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                               +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                               +'coalesce (translate (tadr.LAND using CHAR_CS),loc.LAND,''DE'') as LAND,'
                               +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON'
                              +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                              +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                              +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                              +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                              +' where auf.REF=:ref_auf'
                              );

            cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

            cfgquery.Open;

            if (cfgquery.FieldByName('REF').IsNull) then begin
              cfgquery.Close;

              cfgquery.SQL.Clear;
              cfgquery.SQL.Add ( 'select coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as NAME1,ml.ABSENDER_ZUSATZ as NAMEZUSATZ,nvl (ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE'
                                +',nvl (ml.ABSENDER_PLZ,loc.PLZ) as PLZ,nvl (ml.ABSENDER_ORT,loc.ORT) as ORT,nvl (loc.LAND,''DE'') as LAND,loc.TELEFON'
                                +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                +' where m.REF=:ref_mand'
                                );

              cfgquery.Params.ParamByName('ref_loc').Value := query.FieldByName('REF_LOCATION').AsInteger;

              if query.FieldByName('REF_SUB_MAND').IsNull then
                cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
              else cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

              cfgquery.Open;
            end;

            numstr := '';

            if (Length (absstrasse) > 0) then
              streetstr := absstrasse
            else
              streetstr := cfgquery.FieldByName ('STRASSE').AsString;

            idx := Length (streetstr);
            while (idx > 1) and (streetstr [idx] <> ' ') do
              Dec (idx);

            if (idx > 1) then begin
              numstr := copy (streetstr, idx + 1);

              while (idx > 1) and (streetstr [idx] = ' ') do
                Dec (idx);

              streetstr := copy (streetstr, 1, idx);
            end;

            if (Length (absname) = 0) then
              absname := cfgquery.FieldByName ('NAME1').AsString;

            if (Length (absort) = 0) then
              absort := cfgquery.FieldByName ('ORT').AsString;

            if (Length (absplz) = 0) then
              absplz := cfgquery.FieldByName ('PLZ').AsString;

            if (Length (absland) = 0) then
              absland := cfgquery.FieldByName ('LAND').AsString;


            body := body
                          +',"consignorAddress": {'
                          +'  "name1": "'+ConvertJSONSonderzeichen (absname)+'",'
                          +'  "name2": "",'
                          +'  "name3": "",'
                          +'  "street": "'+ConvertJSONSonderzeichen (streetstr)+'",'
                          +'  "houseNumber": "'+numstr+'",'
                          +'  "zipCode": "'+absplz+'",'
                          +'  "city": "'+ConvertJSONSonderzeichen (absort)+'",'
                          +'  "country": "'+ConvertJSONSonderzeichen (absland)+'",'
                          +'  "phoneNumber": "",'
                          +'  "remarks": "",'
                          +'  "email": ""'
                          +'}';

            cfgquery.Close;

          finally
            cfgquery.Free;
          end;

          body := body
                        +',"neutralAddress": {'
                        +'  		"name1": "",'
                        +'  		"name2": "",'
                        +'  		"name3": "",'
                        +'  		"street": "",'
                        +'  		"houseNumber": "",'
                        +'  		"zipCode": "",'
                        +'  		"city": "",'
                        +'  		"country": ""'
                        +'}';

          numstr := '';
          streetstr := trim (liefquery.FieldByName ('STRASSE').AsString);
          idx := Length (streetstr);
          while (idx > 1) and (streetstr [idx] <> ' ') do
            Dec (idx);

          if (idx > 1) then begin
            numstr := copy (streetstr, idx + 1);

            while (idx > 1) and (streetstr [idx] = ' ') do
              Dec (idx);

            streetstr := copy (streetstr, 1, idx);
          end;

          body := body  +',"consigneeAddress":{';

          if liefquery.FieldByName ('COMPANY').IsNull then
            body := body +'  "name1": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('NAME1').AsString, 1, 40), '&', '', [rfReplaceAll]))+'",'
                         +'  "name2": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('NAME2').AsString, 1, 40), '&', '', [rfReplaceAll]))+'",'
                         +'  "name3": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('NAMEZUSATZ').AsString, 1, 40), '&', '', [rfReplaceAll]))+'",'
          else
            body := body +'  "name1": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('COMPANY').AsString, 1, 40), '&', '', [rfReplaceAll]))+'",'
                         +'  "name2": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('NAME1').AsString, 1, 40), '&', '', [rfReplaceAll]))+'",'
                         +'  "name3": "'+ConvertJSONSonderzeichen (StringReplace (copy (liefquery.FieldByName ('NAME2').AsString, 1, 40), '&', '', [rfReplaceAll]))+'",';

          body := body
                        +'  "street": "'+ConvertJSONSonderzeichen (copy (streetstr, 1, 60))+'",'
                        +'  "houseNumber": "'+numstr+'",'
                        +'  "zipCode": "'+liefquery.FieldByName ('PLZ').AsString+'",'
                        +'  "city": "'+ConvertJSONSonderzeichen (copy (liefquery.FieldByName ('ORT').AsString, 1, 30))+'",'
                        +'  "country": "'+landstr+'",'
                        +'  "phoneNumber": "",'
                        +'  "remarks": ""';

          if not (kepemail) or liefquery.FieldByName('EMAIL').IsNull then
            body := body +',"email": "'+ConvertJSONSonderzeichen (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString)+'"'
          else
            body := body +',"email": "'+ConvertJSONSonderzeichen (liefquery.FieldByName('EMAIL').AsString)+'"';

          body := body +'}';

          body := body  +',"label": "1"';

          body := body
                +',"packages": [{'
                  +'"height": "'+GetDim (query.FieldByName('L'), 10)+'",'
                  +'"length": "'+GetDim (query.FieldByName('B'), 10)+'",'
                  +'"width": "'+GetDim (query.FieldByName('H'), 10)+'"'
                  +'}]';

          body := body+'}';

          StrToFile (DatenPath + RESTDumpDir+'Go\'+FormatDateTime ('yyyymmdd', Now)+'\post_labels_'+nve+'.json', body);

          sdata.Clear;
          if SendRequest(urlstr, // Host,
                          -1, //Port
                          'external/ci/order/api/v1/createOrder', // Service
                          'POST', //Methode
                          '', //Kein proxy
                          //'localhost:8888', // Fiddler als Proxy
                          giuser, gipw, // User , PW
                          '', //Action
                          'application/json', //ContentType
                          [], //AddHeader
                          UTF8Encode (body),         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            labelurl := '';

            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+'Go\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nve+'.json');

            sdata.Position := 0;
            js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;

            if not Assigned (js) then begin
              ErrorText := 'API error message';
            end else begin
              try
                fs := js.Field['hwbNumber'];

                if not Assigned (fs) then begin
                  res := 37;

                  fs := js.Field['Errors'];
                  if Assigned (fs) then begin
                    for idx := 0 to fs.Count - 1 do begin
                      us := fs.Child [idx].Field['Description'];

                      if Assigned (us) then
                        ErrorText := ErrorText + ':' + StringReplace (us.Value, '\u0027', '''', [rfReplaceAll]);
                    end;
                  end;

                  if (Length (ErrorText) = 0) then
                    ErrorText := 'tracking code error';
                end else begin
                  if not (fs.Value = NULL) then
                    SendungsNr := fs.Value;

                  fs := js.Field['package'];
                  if Assigned (fs) and (fs.Count > 0) then begin
                    us := fs.Child [0].Field['barcode'];

                    if Assigned (us) and not (us.Value = NULL) then
                      Barcode := us.Value;
                  end;


                  us := js.Field['hwbOrPackageLabel'];

                  if not Assigned (us) then begin
                    res := 38;
                    ErrorText := 'label_zpl error'
                  end else begin
                    outstr := us.Value;
                    //outstr := DecodeString (us.Value);

                    LabelFormat := 'zpl';              
                    ForceDirectories(DatenPath + LabelDumpDir + 'Go');

                    LabelImage.WriteBuffer (Pointer(outstr)^, Length(outstr));

                    try
                      LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'Go\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                    except
                    end;

                    if (res <> 0) then
                      ErrorText := 'Error '+IntToStr (res) + ' while save shipping label data';
                  end;
                end;
              except
                res := 37;
                ErrorText := 'Label data error';
              end;
            end;
          end else begin
            res := 21;

            ErrorText := 'Fehler beim Go server';

            if (Length (errtext) > 0) then
              ErrorText := ErrorText + #13 + errtext;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: Go, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

  //******************************************************************************
  //* Function Name: CreateDPDWEBLabel
  //* Author       : Stefan Graf
  //* Datum        : 08.06.2021
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateDPDWEBLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    idx,
    res,
    keyidx,
    errcode  : Integer;
    errtext  : String;
    xmlstr   : String;
    urlparam : String;
    body     : String;
    resp     : String;
    hdr      : String;
    nve      : String;
    labelurl : String;
    nrstr    : String;
    orderid  : String;
    timestr  : String;
    namestr  : String;
    telstr   : String;
    tagstr   : String;
    gipw,
    giid,
    giuser,
    gidepot  : String;
    aufref,
    numstr,
    keystr,
    streetstr,
    vorstr,
    nachstr  : String;
    zplstr   : String;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    cfgquery : TSmartQuery;
    olddec   : Char;
    found    : boolean;
    xmlrd    : TXMLFile;
    xmlnd    : TXMLTagEntry;
    xmsnd    : TXMLTagEntry;
    xmlout   : TXMLTagEntry;
  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      TrackUrl  := '';
      Barcode   := '';
      ErrorText := '';

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select * from V_SPED_GATEWAY where REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else
          keystr := cfgquery.FieldByName('API_KEY').AsString;

        telstr := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        //Die Zugangsdaten stehen im API_KEY
        strlist := TStringList.Create;
        try
          strlist.Delimiter := ';';
          strlist.StrictDelimiter := true;
          strlist.DelimitedText := keystr;

          if (strlist.Count < 3) then
            res := 24
          else begin
            giuser  := strlist [0];     //Delis-ID
            gipw    := strlist [1];     //Passwort
            gidepot := strlist [2];     //Depot
            giid    := strlist [1];     //Passwort
          end;
        finally
          strlist.Free;
        end;
     end;

     if (res = 0) then begin
      keyidx := 0;

      sdata := TMemoryStream.Create;

      try
        while (keyidx < 32) do begin
          if (fRESTToken [keyidx].AccessKey = keystr) then
            break
          else if (Length (fRESTToken [keyidx].AccessKey) = 0) then begin
            fRESTToken [keyidx].AccessKey := keystr;
            fRESTToken [keyidx].AccessToken := '';

            break;
          end;

          Inc (keyidx);
        end;

        if (Length (fRESTToken [keyidx].AccessToken) = 0) or (MinutesBetween (fRESTToken [keyidx].CreateAt, Now) > 50) then begin
          ForceDirectories(DatenPath + RESTDumpDir+'DPD\'+FormatDateTime ('yyyymmdd', Now));

          //Neues Token erzeugen
          urlparam := '';

          body := '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns="http://dpd.com/common/service/types/LoginService/2.0">'
                 +' <soapenv:Header/>'
                 +' <soapenv:Body>'
                 +' <ns:getAuth>'
                 +'   <delisId>'+giuser+'</delisId>'
                 +'  <password>'+gipw+'</password>'
                 +'  <messageLanguage>de_DE</messageLanguage>'
                 +'</ns:getAuth>'
                 +' </soapenv:Body>'
                 +'<soapenv:Envelope>';

          if SendRequest('public-ws-stage.dpd.com', // Host,
                          -1, //Port
                          'services/LoginService/V2_0/getAuth', // Service
                          'POST', //Methode
                          '', // Proxy,
                          '', '', // User , PW
                          '', //Action
                          'application/xml', //ContentType
                          [],
                          body,         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            sdata.Position := 0;
            xmlrd := TXMLFile.Create (Nil);

            try
              sdata.Position := 0;
              xmlrd.ParseXMLFile (sdata);

              xmsnd := xmlrd.FindTag ('authToken');

              if not Assigned (xmsnd) then begin
                ErrorText := 'API error message (token)';
              end else begin
                fRESTToken [keyidx].AccessToken := xmsnd.TagValue;
                fRESTToken [keyidx].CreateAt := Now;
              end;
            finally
              xmlrd.Free;
            end;
          end else begin
            ErrorText := 'Fehler token';
          end;
        end;

        if (Length (fRESTToken [keyidx].AccessToken) > 0) then begin
          nve := query.FieldByName('NVE_NR').AsString;

          if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
            orderid := query.FieldByName('AUFTRAG_NR').AsString
          else
            orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;

          aufref := GetSendungsReferenz;

          if Assigned (SendITLog) then begin
            SendITLog.Logging (clNormal, 'Versand: DPD, RefNVE: '+query.FieldByName('REF_NVE').AsString+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
          end;

          //Token erzeugen
          urlparam := '';

          body := '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns="http://dpd.com/common/service/types/Authentication/2.0" xmlns:ns1="http://dpd.com/common/service/types/ShipmentService/4.4">'
                 +'  <soapenv:Header>'
                 +'    <ns:authentication>'
                 +'      <delisId>'+giuser+'</delisId>'
                 +'      <authToken>'+fRESTToken [keyidx].AccessToken+'</authToken>'
                 +'      <messageLanguage>de_DE</messageLanguage>'
                 +'    </ns:authentication>'
                 +'  </soapenv:Header>'
                 +'  <soapenv:Body>'
                 +'    <ns1:storeOrders>'
                 +'      <printOptions>'
                 +'        <printOption>'
                 +'          <outputFormat>ZPL</outputFormat>'
                 +'          <paperFormat>A6</paperFormat>'
                 +'          <startPosition>UPPER_LEFT</startPosition>'
                 +'        </printOption>'
                 +'      </printOptions>';

         body := body + '<order>'
                +'  <generalShipmentData>'
                +'    <mpsCustomerReferenceNumber1>'+HTTPString (aufref)+'</mpsCustomerReferenceNumber1>'
                +'    <mpsCustomerReferenceNumber2></mpsCustomerReferenceNumber2>'
                +'    <mpsCustomerReferenceNumber3></mpsCustomerReferenceNumber3>'
                +'    <mpsCustomerReferenceNumber4></mpsCustomerReferenceNumber4>'
                +'    <identificationNumber>'+giid+'</identificationNumber>'
                +'    <sendingDepot>'+gidepot+'</sendingDepot>'
                +'    <product>CL</product>'
                +'    <mpsWeight>'+IntToStr (Round (query.FieldByName ('BRUTTO_GEWICHT').AsFloat * 100))+'</mpsWeight>';

          //Absender Adresse übergeben
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            //Bei Dropship wird die Traderadresse angezeigt
            cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                               +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                               +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                               +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                               +'coalesce (translate (tadr.LAND using CHAR_CS),loc.LAND,''DE'') as LAND,'
                               +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON'
                              +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                              +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                              +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                              +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                              +' where auf.REF=:ref_auf'
                              );

            cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

            cfgquery.Open;

            if (cfgquery.FieldByName('REF').IsNull) then begin
              cfgquery.Close;

              cfgquery.SQL.Clear;
              cfgquery.SQL.Add ( 'select coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as NAME1,ml.ABSENDER_ZUSATZ as NAMEZUSATZ,nvl (ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE'
                                +',nvl (ml.ABSENDER_PLZ,loc.PLZ) as PLZ,nvl (ml.ABSENDER_ORT,loc.ORT) as ORT,nvl (loc.LAND,''DE'') as LAND,loc.TELEFON'
                                +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                +' where m.REF=:ref_mand'
                                );

              cfgquery.Params.ParamByName('ref_loc').Value := query.FieldByName('REF_LOCATION').AsInteger;

              if query.FieldByName('REF_SUB_MAND').IsNull then
                cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
              else cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

              cfgquery.Open;
            end;

            numstr := '';
            streetstr := cfgquery.FieldByName ('STRASSE').AsString;
            idx := Length (streetstr);
            while (idx > 1) and (streetstr [idx] <> ' ') do
              Dec (idx);

            if (idx > 1) then begin
              numstr := copy (streetstr, idx + 1);

              while (idx > 1) and (streetstr [idx] = ' ') do
                Dec (idx);

              streetstr := copy (streetstr, 1, idx);
            end;

            body := body + '<sender>'
                +' <name1>'+HTTPString (cfgquery.FieldByName ('NAME1').AsString)+'</name1>'
                +' <name2>'+HTTPString (cfgquery.FieldByName ('NAMEZUSATZ').AsString)+'</name2>'
                +' <street>'+HTTPString (streetstr)+'</street>'
                +' <houseNo>'+HTTPString (numstr)+'</houseNo>'
                +' <country>'+HTTPString (cfgquery.FieldByName ('LAND').AsString)+'</country>'
                +' <zipCode>'+HTTPString (cfgquery.FieldByName ('PLZ').AsString)+'</zipCode>'
                +' <city>'+HTTPString (cfgquery.FieldByName ('ORT').AsString)+'</city>'
                +' <gln>0</gln>'
                +'</sender>';

            cfgquery.Close;
          finally
            cfgquery.Free;
          end;

        numstr := '';
        streetstr := liefquery.FieldByName ('STRASSE').AsString;
        idx := Length (streetstr);
        while (idx > 1) and (streetstr [idx] <> ' ') do
          Dec (idx);

        if (idx > 1) then begin
          numstr := copy (streetstr, idx + 1);

          while (idx > 1) and (streetstr [idx] = ' ') do
            Dec (idx);

          streetstr := copy (streetstr, 1, idx);
        end;

        body := body + '<recipient>'
          +'        <name1>'+HTTPString (liefquery.FieldByName ('NAME1').AsString)+'</name1>'
          +'        <name2>'+HTTPString (liefquery.FieldByName ('NAME2').AsString)+'</name2>'
          +'        <street>'+HTTPString (streetstr)+'</street>'
          +'        <houseNo>'+HTTPString (numstr)+'</houseNo>'
          +'        <country>'+HTTPString (liefquery.FieldByName ('LIEFER_LAND_ISO').AsString)+'</country>'
          +'        <zipCode>'+HTTPString (liefquery.FieldByName ('PLZ').AsString)+'</zipCode>'
          +'        <city>'+HTTPString (liefquery.FieldByName ('ORT').AsString)+'</city>'
          +'        <gln>0</gln>'
          +'      </recipient>';

        body := body + '</generalShipmentData>'
        +'      <parcels>'
        +'        <customerReferenceNumber1></customerReferenceNumber1>'
        +'        <customerReferenceNumber2></customerReferenceNumber2>'
        +'        <customerReferenceNumber3></customerReferenceNumber3>'
        +'        <customerReferenceNumber4></customerReferenceNumber4>'
        +'        <weight>'+IntToStr (Round (query.FieldByName ('BRUTTO_GEWICHT').AsFloat * 100))+'</weight>'
        +'      </parcels>'
        +'      <productAndServiceData>'
        +'        <orderType>consignment</orderType>'
        +'        <food>true</food>'
        +'        <predict>'
        +'         <channel>1</channel>'
        +'          <value><EMAIL></value>'
        +'          <language>DE</language>'
        +'        </predict>'
        +'      </productAndServiceData>'
      +'      </order>'
    +'      </ns1:storeOrders>'
  +'      </soapenv:Body>'
+'      </soapenv:Envelope>';

          StrToFile (DatenPath + RESTDumpDir+'DPD\'+FormatDateTime ('yyyymmdd', Now)+'\post_labels_'+nve+'.xml', body, false);

          sdata.Clear;
          if SendRequest('public-ws-stage.dpd.com', // Host,
                          -1, //Port
                          'services/ShipmentService/V4_4/storeOrders', // Service
                          'POST', //Methode
                          '', // Proxy,
                          '', '', // User , PW
                          '', //Action
                          'application/xml', //ContentType
                          [], //AddHeader
                          body,         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            labelurl := '';

            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+'DPD\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nve+'.xml');

            xmlrd := TXMLFile.Create (Nil);

            try
              sdata.Position := 0;
              xmlrd.ParseXMLFile (sdata);

              xmsnd := xmlrd.FindTag ('parcelLabelNumber');
              if not Assigned (xmsnd) then begin
                res := 25;
                ErrorText := 'tracking code error';

                xmlnd := xmlrd.FindTag ('faults');

                if Assigned (xmlnd) then begin
                  xmlout := xmlnd.FindTag ('message');

                  if Assigned (xmlout) then
                    ErrorText := xmlout.NodeValue;
                end;
              end else begin
                Barcode    := xmsnd.NodeValue;
                SendungsNr := xmsnd.NodeValue;

                xmlout := xmlrd.FindTag ('output');
                if not Assigned (xmlout) then begin
                  res := 26;
                  ErrorText := 'Label data error';
                end else begin
                  xmlnd := xmlout.FindTag ('content');

                  if not Assigned (xmlnd) or (Length (xmlnd.NodeValue) = 0) then begin
                    res := 27;
                    ErrorText := 'Label data empty'
                  end else begin
                    ForceDirectories(DatenPath + LabelDumpDir + 'DPD');

                    zplstr := DecodeString (xmlnd.NodeValue);

                    xmlnd := xmlout.FindTag ('format');

                    if (xmlnd.NodeValue = 'ZPL') then
                      res := StrToFile (DatenPath + 'DPD\' + SendungsNr + '.zpl', zplstr, false)
                    else if (xmlnd.NodeValue = 'PDF') then
                      res := StrToFile (DatenPath + 'DPD\' + SendungsNr + '.pdf', zplstr, false)
                    else
                      res := StrToFile (DatenPath + 'DPD\' + SendungsNr + '.txt', zplstr, false);

                    if (res <> 0) then
                      ErrorText := 'Error '+IntToStr (res) + ' while save shipping label data';
                  end;
                end;
              end;

              if Assigned (SendITLog) then begin
                SendITLog.Logging (clNormal, 'Versand: DPD, RefNVE: '+query.FieldByName('REF_NVE').AsString+', Versender: '+Versender+', SendungsNr: '+SendungsNr);
              end;
            finally
              xmlrd.Free;
            end;
          end else begin
            res := 21;
            ErrorText := 'Fehler orders';
          end;
        end;
      finally
        sdata.Free;
      end;
     end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: DPD, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

  //******************************************************************************
  //* Function Name: CreateDPDWEBLabel
  //* Author       : Stefan Graf
  //* Datum        : 08.06.2021
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateDPDCloudLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    idx,
    res,
    keyidx,
    errcode  : Integer;
    errtext  : String;
    xmlstr   : String;
    urlparam : String;
    body     : String;
    resp     : String;
    hdr      : String;
    nve      : String;
    labelurl : String;
    nrstr    : String;
    orderid  : String;
    timestr  : String;
    namestr  : String;
    telstr   : String;
    tagstr   : String;
    gipw,
    giid,
    giuser,
    gidepot  : String;
    aufref,
    numstr,
    keystr,
    streetstr,
    vorstr,
    nachstr  : String;
    zplstr   : String;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    cfgquery : TSmartQuery;
    olddec   : Char;
    found    : boolean;
    xmlrd    : TXMLFile;
    xmlnd    : TXMLTagEntry;
    xmsnd    : TXMLTagEntry;
    xmlout   : TXMLTagEntry;
  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      TrackUrl  := '';
      Barcode   := '';
      ErrorText := '';

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select * from V_SPED_GATEWAY where REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else
          keystr := cfgquery.FieldByName('API_KEY').AsString;

        telstr := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        //Die Zugangsdaten stehen im API_KEY
        strlist := TStringList.Create;
        try
          strlist.Delimiter := ';';
          strlist.StrictDelimiter := true;
          strlist.DelimitedText := keystr;

          if (strlist.Count < 3) then
            res := 24
          else begin
            giuser  := strlist [0];     //Delis-ID
            gipw    := strlist [1];     //Passwort
            gidepot := strlist [2];     //Depot
            giid    := strlist [1];     //Passwort
          end;
        finally
          strlist.Free;
        end;
     end;

     if (res = 0) then begin
      keyidx := 0;

      sdata := TMemoryStream.Create;

      try
        while (keyidx < 32) do begin
          if (fRESTToken [keyidx].AccessKey = keystr) then
            break
          else if (Length (fRESTToken [keyidx].AccessKey) = 0) then begin
            fRESTToken [keyidx].AccessKey := keystr;
            fRESTToken [keyidx].AccessToken := '';

            break;
          end;

          Inc (keyidx);
        end;

        if (Length (fRESTToken [keyidx].AccessToken) = 0) or (MinutesBetween (fRESTToken [keyidx].CreateAt, Now) > 50) then begin
          ForceDirectories(DatenPath + RESTDumpDir+'DPD\'+FormatDateTime ('yyyymmdd', Now));

          //Neues Token erzeugen
          urlparam := '';

          body := '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns="http://dpd.com/common/service/types/LoginService/2.0">'
                 +' <soapenv:Header/>'
                 +' <soapenv:Body>'
                 +' <ns:getAuth>'
                 +'   <delisId>'+giuser+'</delisId>'
                 +'  <password>'+gipw+'</password>'
                 +'  <messageLanguage>de_DE</messageLanguage>'
                 +'</ns:getAuth>'
                 +' </soapenv:Body>'
                 +'<soapenv:Envelope>';

          if SendRequest('public-ws-stage.dpd.com', // Host,
                          -1, //Port
                          'services/LoginService/V2_0/getAuth', // Service
                          'POST', //Methode
                          '', // Proxy,
                          '', '', // User , PW
                          '', //Action
                          'application/xml', //ContentType
                          [],
                          body,         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            sdata.Position := 0;
            xmlrd := TXMLFile.Create (Nil);

            try
              sdata.Position := 0;
              xmlrd.ParseXMLFile (sdata);

              xmsnd := xmlrd.FindTag ('authToken');

              if not Assigned (xmsnd) then begin
                ErrorText := 'API error message (token)';
              end else begin
                fRESTToken [keyidx].AccessToken := xmsnd.TagValue;
                fRESTToken [keyidx].CreateAt := Now;
              end;
            finally
              xmlrd.Free;
            end;
          end else begin
            ErrorText := 'Fehler token';
          end;
        end;

        if (Length (fRESTToken [keyidx].AccessToken) > 0) then begin
          nve := query.FieldByName('NVE_NR').AsString;

          if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
            orderid := query.FieldByName('AUFTRAG_NR').AsString
          else
            orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;

          aufref := GetSendungsReferenz;

          if Assigned (SendITLog) then begin
            SendITLog.Logging (clNormal, 'Versand: DPD, RefNVE: '+query.FieldByName('REF_NVE').AsString+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
          end;

          //Token erzeugen
          urlparam := '';

          body := '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns="http://dpd.com/common/service/types/Authentication/2.0" xmlns:ns1="http://dpd.com/common/service/types/ShipmentService/4.4">'
                 +'  <soapenv:Header>'
                 +'    <ns:authentication>'
                 +'      <delisId>'+giuser+'</delisId>'
                 +'      <authToken>'+fRESTToken [keyidx].AccessToken+'</authToken>'
                 +'      <messageLanguage>de_DE</messageLanguage>'
                 +'    </ns:authentication>'
                 +'  </soapenv:Header>'
                 +'  <soapenv:Body>'
                 +'    <ns1:storeOrders>'
                 +'      <printOptions>'
                 +'        <printOption>'
                 +'          <outputFormat>ZPL</outputFormat>'
                 +'          <paperFormat>A6</paperFormat>'
                 +'          <startPosition>UPPER_LEFT</startPosition>'
                 +'        </printOption>'
                 +'      </printOptions>';

         body := body + '<order>'
                +'  <generalShipmentData>'
                +'    <mpsCustomerReferenceNumber1>'+HTTPString (aufref)+'</mpsCustomerReferenceNumber1>'
                +'    <mpsCustomerReferenceNumber2></mpsCustomerReferenceNumber2>'
                +'    <mpsCustomerReferenceNumber3></mpsCustomerReferenceNumber3>'
                +'    <mpsCustomerReferenceNumber4></mpsCustomerReferenceNumber4>'
                +'    <identificationNumber>'+giid+'</identificationNumber>'
                +'    <sendingDepot>'+gidepot+'</sendingDepot>'
                +'    <product>CL</product>'
                +'    <mpsWeight>'+IntToStr (Round (query.FieldByName ('BRUTTO_GEWICHT').AsFloat * 100))+'</mpsWeight>';

          //Absender Adresse übergeben
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            //Bei Dropship wird die Traderadresse angezeigt
            cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                               +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                               +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                               +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                               +'coalesce (translate (tadr.LAND using CHAR_CS),loc.LAND,''DE'') as LAND,'
                               +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON'
                              +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                              +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                              +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                              +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                              +' where auf.REF=:ref_auf'
                              );

            cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

            cfgquery.Open;

            if (cfgquery.FieldByName('REF').IsNull) then begin
              cfgquery.Close;

              cfgquery.SQL.Clear;
              cfgquery.SQL.Add ( 'select coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as NAME1,ml.ABSENDER_ZUSATZ as NAMEZUSATZ,nvl (ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE'
                                +',nvl (ml.ABSENDER_PLZ,loc.PLZ) as PLZ,nvl (ml.ABSENDER_ORT,loc.ORT) as ORT,nvl (loc.LAND,''DE'') as LAND,loc.TELEFON'
                                +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                +' where m.REF=:ref_mand'
                                );

              cfgquery.Params.ParamByName('ref_loc').Value := query.FieldByName('REF_LOCATION').AsInteger;

              if query.FieldByName('REF_SUB_MAND').IsNull then
                cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
              else cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

              cfgquery.Open;
            end;

            numstr := '';
            streetstr := cfgquery.FieldByName ('STRASSE').AsString;
            idx := Length (streetstr);
            while (idx > 1) and (streetstr [idx] <> ' ') do
              Dec (idx);

            if (idx > 1) then begin
              numstr := copy (streetstr, idx + 1);

              while (idx > 1) and (streetstr [idx] = ' ') do
                Dec (idx);

              streetstr := copy (streetstr, 1, idx);
            end;

            body := body + '<sender>'
                +' <name1>'+HTTPString (cfgquery.FieldByName ('NAME1').AsString)+'</name1>'
                +' <name2>'+HTTPString (cfgquery.FieldByName ('NAMEZUSATZ').AsString)+'</name2>'
                +' <street>'+HTTPString (streetstr)+'</street>'
                +' <houseNo>'+HTTPString (numstr)+'</houseNo>'
                +' <country>'+HTTPString (cfgquery.FieldByName ('LAND').AsString)+'</country>'
                +' <zipCode>'+HTTPString (cfgquery.FieldByName ('PLZ').AsString)+'</zipCode>'
                +' <city>'+HTTPString (cfgquery.FieldByName ('ORT').AsString)+'</city>'
                +' <gln>0</gln>'
                +'</sender>';

            cfgquery.Close;
          finally
            cfgquery.Free;
          end;

        numstr := '';
        streetstr := liefquery.FieldByName ('STRASSE').AsString;
        idx := Length (streetstr);
        while (idx > 1) and (streetstr [idx] <> ' ') do
          Dec (idx);

        if (idx > 1) then begin
          numstr := copy (streetstr, idx + 1);

          while (idx > 1) and (streetstr [idx] = ' ') do
            Dec (idx);

          streetstr := copy (streetstr, 1, idx);
        end;

        body := body + '<recipient>'
          +'        <name1>'+HTTPString (liefquery.FieldByName ('NAME1').AsString)+'</name1>'
          +'        <name2>'+HTTPString (liefquery.FieldByName ('NAME2').AsString)+'</name2>'
          +'        <street>'+HTTPString (streetstr)+'</street>'
          +'        <houseNo>'+HTTPString (numstr)+'</houseNo>'
          +'        <country>'+HTTPString (liefquery.FieldByName ('LIEFER_LAND_ISO').AsString)+'</country>'
          +'        <zipCode>'+HTTPString (liefquery.FieldByName ('PLZ').AsString)+'</zipCode>'
          +'        <city>'+HTTPString (liefquery.FieldByName ('ORT').AsString)+'</city>'
          +'        <gln>0</gln>'
          +'      </recipient>';

        body := body + '</generalShipmentData>'
        +'      <parcels>'
        +'        <customerReferenceNumber1></customerReferenceNumber1>'
        +'        <customerReferenceNumber2></customerReferenceNumber2>'
        +'        <customerReferenceNumber3></customerReferenceNumber3>'
        +'        <customerReferenceNumber4></customerReferenceNumber4>'
        +'        <weight>'+IntToStr (Round (query.FieldByName ('BRUTTO_GEWICHT').AsFloat * 100))+'</weight>'
        +'      </parcels>'
        +'      <productAndServiceData>'
        +'        <orderType>consignment</orderType>'
        +'        <food>true</food>'
        +'        <predict>'
        +'         <channel>1</channel>'
        +'          <value><EMAIL></value>'
        +'          <language>DE</language>'
        +'        </predict>'
        +'      </productAndServiceData>'
      +'      </order>'
    +'      </ns1:storeOrders>'
  +'      </soapenv:Body>'
+'      </soapenv:Envelope>';

          StrToFile (DatenPath + RESTDumpDir+'DPD\'+FormatDateTime ('yyyymmdd', Now)+'\post_labels_'+nve+'.xml', body, false);

          sdata.Clear;
          if SendRequest('public-ws-stage.dpd.com', // Host,
                          -1, //Port
                          'services/ShipmentService/V4_4/storeOrders', // Service
                          'POST', //Methode
                          '', // Proxy,
                          '', '', // User , PW
                          '', //Action
                          'application/xml', //ContentType
                          [], //AddHeader
                          body,         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            labelurl := '';

            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + RESTDumpDir+'DPD\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nve+'.xml');

            xmlrd := TXMLFile.Create (Nil);

            try
              sdata.Position := 0;
              xmlrd.ParseXMLFile (sdata);

              xmsnd := xmlrd.FindTag ('parcelLabelNumber');
              if not Assigned (xmsnd) then begin
                res := 25;
                ErrorText := 'tracking code error';

                xmlnd := xmlrd.FindTag ('faults');

                if Assigned (xmlnd) then begin
                  xmlout := xmlnd.FindTag ('message');

                  if Assigned (xmlout) then
                    ErrorText := xmlout.NodeValue;
                end;
              end else begin
                Barcode    := xmsnd.NodeValue;
                SendungsNr := xmsnd.NodeValue;

                xmlout := xmlrd.FindTag ('output');
                if not Assigned (xmlout) then begin
                  res := 26;
                  ErrorText := 'Label data error';
                end else begin
                  xmlnd := xmlout.FindTag ('content');

                  if not Assigned (xmlnd) or (Length (xmlnd.NodeValue) = 0) then begin
                    res := 27;
                    ErrorText := 'Label data empty'
                  end else begin
                    ForceDirectories(DatenPath + LabelDumpDir + 'DPD');

                    zplstr := DecodeString (xmlnd.NodeValue);

                    xmlnd := xmlout.FindTag ('format');

                    if (xmlnd.NodeValue = 'ZPL') then
                      res := StrToFile (DatenPath + 'DPD\' + SendungsNr + '.zpl', zplstr, false)
                    else if (xmlnd.NodeValue = 'PDF') then
                      res := StrToFile (DatenPath + 'DPD\' + SendungsNr + '.pdf', zplstr, false)
                    else
                      res := StrToFile (DatenPath + 'DPD\' + SendungsNr + '.txt', zplstr, false);

                    if (res <> 0) then
                      ErrorText := 'Error '+IntToStr (res) + ' while save shipping label data';
                  end;
                end;
              end;

              if Assigned (SendITLog) then begin
                SendITLog.Logging (clNormal, 'Versand: DPD, RefNVE: '+query.FieldByName('REF_NVE').AsString+', Versender: '+Versender+', SendungsNr: '+SendungsNr);
              end;
            finally
              xmlrd.Free;
            end;
          end else begin
            res := 21;
            ErrorText := 'Fehler orders';
          end;
        end;
      finally
        sdata.Free;
      end;
     end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: DPD, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

  //******************************************************************************
  //* Function Name: CreateShipTrackLabel
  //* Author       : Stefan Graf
  //* Datum        : 27.11.2020
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateShipTrackLabel (const Art : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    res,
    errcode  : Integer;
    errtext  : String;
    body     : String;
    resp     : String;
    hdr      : String;
    nve      : String;
    labelurl : String;
    orderid  : String;
    timestr  : String;
    keystr,
    lstr,
    bstr,
    hstr,
    gwstr,
    mengestr : String;
    sdata    : TMemoryStream;
    js       : TlkJSONobject;
    fs       : TlkJSONbase;
    posquery : TSmartQuery;
    cfgquery : TSmartQuery;
    olddec   : Char;
    zplstr   : String;
  begin
    res := 0;

    ErrorText := '';

    Versender  := '';
    SendungsNr := '';

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      posquery  := TSmartQuery.Create (Nil);
      cfgquery  := TSmartQuery.Create (Nil);

      try
        posquery.ReadOnly := True;
        posquery.Session := Query.Session;

        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select cfg.REST_URL, g.API_KEY from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if cfgquery.FieldByName('REST_URL').IsNull then
          ErrorText := 'Not REST url defined'
        else if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else
          keystr := cfgquery.FieldByName('API_KEY').AsString;

        if (Length (keystr) = 0) then
          res := 23
        else begin
          sdata := TMemoryStream.Create;

          ForceDirectories(DatenPath + RESTDumpDir+'ShipTrack\'+FormatDateTime ('yyyymmdd', Now));

          try
            hdr := 'Authorization: Bearer '+keystr;

            nve := query.FieldByName('NVE_NR').AsString;
            orderid := query.FieldByName('AUFTRAG_NR').AsString;

            if Assigned (SendITLog) then begin
              SendITLog.Logging (clNormal, 'Versand: ShipTrak, RefNVE: '+query.FieldByName('REF_NVE').AsString+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
            end;

            DateTimeToString(timestr, 'yyyy-mm-dd hh:nn:ss', Now);
            timestr [11] := 'T';
            timestr := timestr + '.000Z';

            if (query.FieldByName('L').IsNull) then
              lstr := '15'
            else
              lstr := query.FieldByName('L').AsString;

            if (query.FieldByName('B').IsNull) then
              bstr := '15'
            else
              bstr := query.FieldByName('B').AsString;

            if (query.FieldByName('H').IsNull) then
              hstr := '10'
            else
              hstr := query.FieldByName('H').AsString;

            if (query.FieldByName('BRUTTO_GEWICHT').IsNull) then
              gwstr := '500'
            else
              gwstr := IntToStr (Round (query.FieldByName('BRUTTO_GEWICHT').AsFloat * 1000));

            body := '{'+
                    '  "du_id": "'+nve+'",'+
                    '  "width": '+lstr+','+
                    '  "height": '+bstr+','+
                    '  "length": '+hstr+','+
                    '  "weight": '+gwstr+','+
                    '  "order_id": "'+orderid+'",'+
                    '  "items":'+
                    '  [';

           posquery.SQl.Clear;
           posquery.SQL.Add ('select * from V_NVE_INHALT where REF_NVE=:ref_nve order by ARTIKEL_NR');
           posquery.Params [0].Value := query.FieldByName('REF_NVE').AsInteger;

           posquery.Open;

           while not posquery.Eof do begin
             if (posquery.FieldByName('MENGE').IsNull) then
               mengestr := '0'
             else
               mengestr := posquery.FieldByName('MENGE').AsString;

             body := body +
                      '   {'+
                      '    "sku": "'+posquery.FieldByName('ARTIKEL_NR').AsString+'",'+
                      '    "quantity": '+mengestr+
                      '   }';

             posquery.Next;

             if not posquery.Eof then
               body := body + ',';
           end;

           posquery.Close;

           body := body +
                    '  ]'+
                    '}';

            StrToFile (DatenPath + RESTDumpDir+'ShipTrack\'+FormatDateTime ('yyyymmdd', Now)+'\post_labels_'+nve+'.json', body);

            sdata.Clear;
            if SendRequest(cfgquery.FieldByName('REST_URL').AsString, // Host,
                            -1, //Port
                            '/shiptrack/api/v1/shipments', // Service
                            'POST', //Methode
                            '', // Proxy,
                            '', '', // User , PW
                            '', //Action
                            'text/plain', //ContentType
                            ['Accept: '+'application/json', hdr], //AddHeader
                            body,         // RequestData
                            resp,
                            sdata, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
                          then
            begin
              labelurl := '';

              sdata.Position := 0;
              sdata.SaveToFile(DatenPath + RESTDumpDir+'ShipTrack\'+FormatDateTime ('yyyymmdd', Now)+'\shipments_'+nve+'.json');

              sdata.Position := 0;
              js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;

              if not Assigned (js) then begin
                ErrorText := 'API error message';
              end else begin
                try
                  fs := js.Field['tracking_number'];

                  if not Assigned (fs) then begin
                    res := 37;
                    ErrorText := 'tracking code error';

                    fs := js.Field['detail'];
                    if Assigned (fs) then
                      ErrorText := ErrorText + ':' + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll]);
                  end else begin
                    if not (fs.Value = NULL) then
                      SendungsNr := fs.Value;

                    fs := js.Field['carrier'];

                    if Assigned (fs) then
                      Versender := fs.Value;

                    fs := js.Field['label_zpl'];

                    if not Assigned (fs) then begin
                      res := 38;
                      ErrorText := 'label_zpl error'
                    end else begin
                      zplstr := DecodeString (fs.Value);

                      LabelFormat := 'zpl';
                      LabelImage.WriteBuffer (Pointer(zplstr)^, Length(zplstr));

                      if (Length (SendungsNr) > 0) then
                        res := StrToFile (DatenPath + 'ShipTrack\' + SendungsNr + '.zpl', zplstr)
                      else if (Length (nve) > 0) then
                        res := StrToFile (DatenPath + 'ShipTrack\' + nve + '.zpl', zplstr);

                      if (res <> 0) then
                        ErrorText := 'Error '+IntToStr (res) + ' while save shipping label data';
                    end;
                  end;
                except
                  res := 37;
                  ErrorText := 'Label data error';
                end;
              end;
            end else begin
              res := 10;
              ErrorText := errtext + ' (' + IntToStr (errcode) + ')';
            end;
          finally
            sdata.Free;
          end;
        end;

        cfgquery.Close;
      finally
        cfgquery.Free;
        posquery.Free;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: ShipTrak, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

  {$I CreateSendITLabel.inc}
  //{$I CreateVCSCarmaLabel.inc}
  //{$I CreateVLOGLabel.inc}
  {$I CreateBarShippingLabel.inc}
  //{$I CreateDELIExport.inc}
  //{$I CreateIntraShipExport.inc}
  {$I CreateSevenSendersLabel.inc}
  {$I CreateSendCloudLabel.inc}
  {$I CreateFairSendenLabel.inc}
  {$I CreateLieferGruenLabel.inc}

  //****************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //****************************************************************************
  //* Description
  //****************************************************************************
  //* Return Value :
  //****************************************************************************
  function CreateVCECarmaLabel (Art, Absender, Versender, IDNummer : String;
                                const WarenWert : Double;
                                var   SendungsNr, TrackUrl, Barcode, LabelFormat : String;
                                LabelImage : TMemoryStream; var ErrorText : String) : Integer;
  var
    s,
    i,
    res,
    idx,
    stridx,
    sumvpe,
    strpos       : Integer;
    shipid,
    csvstr       : AnsiString;
    firstsvr,
    noneuflag    : boolean;
    numstr,
    optstr,
    arnr,
    versart,
    textstr,
    namestr,
    prtname,
    nrstr,
    idstr,
    taricstr,
    placestr,
    streetstr,
    absnrstr,
    absstreetstr,
    lnstr,
    wistr,
    histr,
    user,
    passwd,
    gwstr,
    ltstr,
    isostr,
    urlstr,
    apistr,
    telstr,
    mailstr,
    nvestr,
    retstr,
    vornamestr,
    nachnamestr  : String;
    aranz,
    portint      : Integer;
    fstream      : TFileStream;
    cfgquery     : TSmartQuery;
    spedquery    : TSmartQuery;
    opt_ident    : String;
    opt_routing  : String;
    incstr       : String;
    dutaccstr    : String;
    delaccstr    : String;
    sendaccstr   : String;
    recvaccstr   : String;
    jsonstr      : String;
    resp         : String;
    errcode      : Integer;
    errtext      : String;
    lblstr       : UTF8String;
    pdfstr       : AnsiString;
    sdata        : TMemoryStream;
    js           : TlkJSONobject;
    fs,
    us,
    ps,
    cs,
    errfs        : TlkJSONbase;
    vcepwd,
    vceuser      : String;
    keystr       : String;
    customstr    : String;
    locstr       : String;
    codestr      : String;
    prodnatstr   : String;
    prodintstr   : String;
    custommailstr: String;
    absstr       : String;
    svrstr,
    addsvrstr    : String;
    strlist      : TStringList;
    olddec       : Char;

    urlentry     : TURLEntry;

    {$ifdef UNICODE}
      utfstr       : AnsiString;
      datastr      : String;
    {$endif}

    bytes     : TBytes;

  begin
    res := 0;

    SendungsNr  := '';
    TrackUrl    := '';
    Barcode     := '';
    LabelFormat := '';
    ErrorText   := '';
    LabelImage.Clear;

    CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'VCE_CARMA_API_USER', keystr);
    CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'VCE_CARMA_API_URL', urlstr);

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select cfg.REST_URL as SPED_REST_URL, g.* from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if Assigned (cfgquery.FindField ('API_KEY')) and not (cfgquery.FieldByName('API_KEY').IsNull) then
          keystr  := cfgquery.FieldByName('API_KEY').AsString;

        customstr := cfgquery.FieldByName('SENDIT_CLIENT').AsString;
        locstr  := cfgquery.FieldByName('SENDIT_LOCATION').AsString;
        codestr := cfgquery.FieldByName('GATEWAY').AsString;
        prodnatstr := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;
        prodintstr := cfgquery.FieldByName('SENDIT_PRODUKT_AUSLAND').AsString;
        telstr  := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;
        custommailstr := cfgquery.FieldByName('DEFAULT_EMAIL_ADRESS').AsString;

        if Assigned (cfgquery.FindField ('ABSENDER')) then
          absstr  := cfgquery.FieldByName('ABSENDER').AsString;

        if Assigned (cfgquery.FindField ('REST_URL')) and not (cfgquery.FieldByName('REST_URL').IsNull) then
          urlstr := cfgquery.FieldByName('REST_URL').AsString;

        if (Length (urlstr) = 0) then
          urlstr  := cfgquery.FieldByName('SPED_REST_URL').AsString;

        cfgquery.Close;

        if (Length (urlstr) = 0) then begin
          res := 21;
          ErrorText := 'No api url';
        end else begin
          DecodeURL (urlstr, urlentry);

          if (Length (urlentry.Host) = 0) then begin
            res := 22;
            ErrorText := 'Wrong api url';
          end;
        end;

        if (res = 0) then begin
          if (Length (keystr) = 0) then begin
            res := 23;
            ErrorText := 'No api credentials';
          end else begin
            strlist := TStringList.Create;
            try
              strlist.Delimiter := ';';
              strlist.StrictDelimiter := true;

              strlist.DelimitedText := keystr;

              if (strlist.Count < 2) then begin
                res := 24;
                ErrorText := 'Credentials not complete';
              end else begin
                vceuser := strlist [0];
                vcepwd := strlist [1];
              end;
            finally
              strlist.Free;
            end;
          end;
        end;

        if (res = 0) then begin
          strpos := Pos (';', PrtInfo.Leitstand);

          if (strpos > 0) then
            prtname := Copy (PrtInfo.Leitstand, 1, strpos - 1)
          else
            prtname := PrtInfo.Leitstand;

          //Das wird die ShipmendID
          if (Pos ('REF=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            shipid := query.FieldByName('KD_KOMM_NR').AsString
          else if (Pos ('REF=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            shipid := query.FieldByName('AUF_REFERENZ').AsString
          else if query.FieldByName('AUSLIEFER_NR').IsNull then
            shipid := query.FieldByName('AUFTRAG_NR').AsString
          else
            shipid := query.FieldByName('AUSLIEFER_NR').AsString;

          //Bei OPT_MULTI_SHIPMEND=0 ist jedes Packstück eine eigen Sendungen
          if (query.FieldByName('OPT_MULTI_SHIPMEND').AsString = '0') then begin
            //Die ShipmendID muss hierbei eindeutig sein
            if (Length (shipid) > 16) then
              shipid := copy (shipid, Length (shipid) - 16) + '-' + copy (query.FieldByName('NVE_NR').AsString, Length (query.FieldByName('NVE_NR').AsString) - 3)
            else if (Length (shipid) > 14) then
              shipid := shipid + copy (query.FieldByName('NVE_NR').AsString, Length (query.FieldByName('NVE_NR').AsString) + 1 - (20 - Length (shipid)))
            else
              shipid := shipid + '-' + copy (query.FieldByName('NVE_NR').AsString, Length (query.FieldByName('NVE_NR').AsString) - 5);
          end;

          //Strasse und Hausnummer ggf. trennen
          nrstr      := '';
          streetstr := StringReplace (liefquery.FieldByName('STRASSE').AsString, ';', ',', [rfReplaceAll]);

          idx := Length (streetstr);
          while (idx > 1) and (streetstr [idx] <> ' ') do
            Dec (idx);

          if (idx > 1) then begin
            nrstr := copy (streetstr, idx + 1);

            //Bei Hermes kann die Hausnummer maximal 4 Zeichne lange sein, daher dürfen längere Hausnummern nicht abgetrennt werden
            if (copy (Versender, 1, 3) = 'HVS') and (Length (nrstr) > 4) then
              nrstr := ''
            else
              streetstr := Copy (streetstr, 1, idx);
          end;

          //Zweiter Teil der Strasse
          if not (liefquery.FieldByName('STRASSE_2').IsNull) then begin
            //Kurze Zusätze wie Hausnummer usw. wird an die Strasse angehangen
            if (Length (liefquery.FieldByName('STRASSE_2').AsString) <= 4) then
              streetstr := streetstr + ' ' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]))
            else if (copy (Versender, 1, 3) = 'HVS') and (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
              //Bei Hermes gibt es keinen zweiten Teil der Strasse, somit muss das angehangen werden
              streetstr := streetstr + ' ' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]));
          end;

          if liefquery.FieldByName('NACHNAME').IsNull then begin
            namestr := Trim (liefquery.FieldByName ('NAME1').AsString);

            idx := Length (namestr);

            //Das erste Leerzeichen nach dem Nachnamen finden
            while (idx > 1) and (namestr [idx] <> ' ') do
              Dec (idx);

            if (idx > 1) then begin
              nachnamestr := copy (namestr, idx + 1);

              //Leerzeichen zwischen Vor- und Nachname entfernen
              while (idx > 1) and (namestr [idx] = ' ') do
                Dec (idx);

              vornamestr := copy (namestr, 1, idx);
            end else begin
              vornamestr  := '';
              nachnamestr := namestr;
            end;
          end else begin
            vornamestr := liefquery.FieldByName('VORNAME').AsString;
            nachnamestr := liefquery.FieldByName('NACHNAME').AsString;
          end;

          if (liefquery.FieldByName('LAND').IsNull) and (liefquery.FieldByName('LAND_ISO').IsNull) then
            isostr := 'DE'
          else if (liefquery.FieldByName('LAND_ISO').IsNull) then
            isostr := UpperCase (StringReplace (liefquery.FieldByName('LAND').AsString, ';', ',', [rfReplaceAll]))
          else
            isostr := liefquery.FieldByName('LAND_ISO').AsString;

          if (Length (IDNummer) > 0) then
            nvestr := IDNummer
          else
            nvestr := query.FieldByName('NVE_NR').AsString;

          if (LabelType = 'Return') then begin
            if (query.FieldByName ('RETOUREN_NR').IsNull) then
              idstr := 'ret_'+query.FieldByName ('AUFTRAG_NR').AsString
            else
              idstr := 'ret_'+query.FieldByName ('RETOUREN_NR').AsString
          end else begin
            idstr := nvestr;
          end;

          if not (kepemail) or liefquery.FieldByName('EMAIL').IsNull then
            mailstr := StringReplace (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString, ';', ',', [rfReplaceAll])
          else
            mailstr := StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll]);

          noneuflag := not IsLandEU (landstr, query, liefquery.FieldByName ('PLZ').AsString);

          if not (liefquery.FieldByName('TELEFON').IsNull) then
            telstr := StringReplace (liefquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll])
          else if not (adrquery.FieldByName('TELEFON').IsNull) then
            telstr := StringReplace (adrquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll])
          else
            telstr := StringReplace (query.FieldByName('DEFAULT_PHONE_NUMBER').AsString, ';', ',', [rfReplaceAll]);

          spedquery  := TSmartQuery.Create (Nil);

          try
            spedquery.ReadOnly := True;
            spedquery.Session := Query.Session;

            spedquery.SQL.Add ('select cfg.* from V_SPED_CONFIG cfg where cfg.REF_SPED=:ref');
            spedquery.Params [0].Value := query.FieldByName('REF_SPED').AsInteger;

            spedquery.Open;

            lnstr := '';
            wistr := '';
            histr := '';

            if not (Assigned (spedquery.FindField ('OPT_DIM_SEND'))) or (spedquery.FieldByName ('OPT_DIM_SEND').AsString = '1') then begin
              lnstr := '15';
              wistr := '15';
              histr := '11';

              (*Das klappte bei DHL nicht, muss geprüft werden, daher erst mal ohne Abmessungen, wird nur bei DPD - Kleinpaketerkennung - benötigt*)
              if (Pos ('Return', Versender) > 0) then begin
                //Bei Retouren nur die minimale Paketgrösse angeben
              end else if (copy (Versender, 1, 3) = 'DPD') then begin
                if (query.FieldByName('L').IsNull or query.FieldByName('B').IsNull or query.FieldByName('H').IsNull) then begin
                  if not (query.FieldByName('VOLUMEN').IsNull) then begin
                    s := Round (Power (query.FieldByName('VOLUMEN').AsFloat, 1/3.0) / 10.0);

                    lnstr := IntToStr (s);
                    wistr := IntToStr (s);
                    histr := IntToStr (s);
                  end;
                end else begin
                  lnstr := Format('%6.1f', [query.FieldByName('L').AsInteger / 10]);
                  wistr := Format('%6.1f', [query.FieldByName('B').AsInteger / 10]);
                  histr := Format('%6.1f', [query.FieldByName('H').AsInteger / 10]);
                end;
              end else if (query.FieldByName('OPT_DIM_REQUIRED').AsString = '1') then begin
                if (query.FieldByName('L').IsNull or query.FieldByName('B').IsNull or query.FieldByName('H').IsNull) then begin
                  if not (query.FieldByName('VOLUMEN').IsNull) then begin
                    s := Round (Power (query.FieldByName('VOLUMEN').AsFloat, 1/3.0) / 10.0);

                    lnstr := IntToStr (s);
                    wistr := IntToStr (s);
                    histr := IntToStr (s);
                  end;
                end else if ((Int64 (query.FieldByName('L').AsInteger) * Int64 (query.FieldByName('B').AsInteger) * Int64 (query.FieldByName('H').AsInteger)) > 50*50*50) then begin
                  lnstr := Format('%6.1f', [query.FieldByName('L').AsInteger / 10]);
                  wistr := Format('%6.1f', [query.FieldByName('B').AsInteger / 10]);
                  histr := Format('%6.1f', [query.FieldByName('H').AsInteger / 10]);
                end;
              end else if not (query.FieldByName('L').IsNull or query.FieldByName('B').IsNull or query.FieldByName('H').IsNull) then begin
                if (Versender = 'DHL') then begin
                  //Beim DHL Standardpaket darf die Packgrösse nicht kleiner als 15x15x11 cm sein
                  if (query.FieldByName('L').AsInteger < 150) then
                    lnstr := '15'
                  else
                    lnstr := Format('%6.1f', [query.FieldByName('L').AsInteger / 10]);

                  if (query.FieldByName('B').AsInteger < 150) then
                    wistr :=  '15'
                  else
                    wistr :=  Format('%6.1f', [query.FieldByName('B').AsInteger / 10]);

                  if (query.FieldByName('H').AsInteger < 110) then
                    histr :=  '11'
                  else
                    histr := Format('%6.1f', [query.FieldByName('H').AsInteger / 10]);
                end else begin
                  lnstr := Format('%6.1f', [query.FieldByName('L').AsInteger / 10]);
                  wistr := Format('%6.1f', [query.FieldByName('B').AsInteger / 10]);
                  histr := Format('%6.1f', [query.FieldByName('H').AsInteger / 10]);
                end;
              end;
            end;

          if (query.FieldByName('EDI_CODE').IsNull) then begin
            ltstr := 'PARCEL'
          end else begin
            ltstr := query.FieldByName('EDI_CODE').AsString;
          end;

            gwstr := '';

            if not (Assigned (spedquery.FindField ('OPT_WEIGHT_SEND'))) or (spedquery.FieldByName ('OPT_WEIGHT_SEND').AsString = '1') then begin
              //Das Gesamtgewicht der Sendung
              if (query.FieldByName('OPT_MULTI_SHIPMEND').AsString = '1') then
                gwstr := Format('%6.3f', [query.FieldByName('SENDUNG_BRUTTO').AsFloat])
              else
                gwstr := Format('%6.3f', [query.FieldByName('BRUTTO_GEWICHT').AsFloat]);
            end;

            if (Art = 'Reprint') then begin
              apistr := 'ship';

              jsonstr := '[{';

              jsonstr := jsonstr + '"user"'+':'+'"'+query.FieldByName('SENDIT_CLIENT').AsString+'"';

              jsonstr := jsonstr + ',"packages": [';
              jsonstr := jsonstr + '{';
              jsonstr := jsonstr + '"trackingNo":"'+query.FieldByName('SENDUNGS_NR').AsString+'"';
              jsonstr := jsonstr + '}';
              jsonstr := jsonstr + ']';

              jsonstr := jsonstr + '}]';
            end else begin
              if (Art = 'Return') then
                apistr := 'ship/return'
              else
                apistr := 'ship';

              jsonstr := '[{';

              jsonstr := jsonstr + '"user"'+':'+'"'+query.FieldByName('SENDIT_CLIENT').AsString+'"';
              jsonstr := jsonstr + ',"reference"'+':'+'"'+shipid+'"';


              svrstr    := '';
              addsvrstr := '';

              strlist := TStringList.Create;
              try
                strlist.Delimiter := ';';
                strlist.StrictDelimiter := true;

                if (Art = 'Return') then begin
                  if Assigned (gatequery.FindField('SENDIT_RETURN_PRODUKT')) then
                    strlist.DelimitedText := gatequery.FieldByName('SENDIT_RETURN_PRODUKT').AsString;
                end else if (landstr = 'DE') then
                  strlist.DelimitedText := prodnatstr
                else
                  strlist.DelimitedText := prodintstr;

                if (strlist.Count = 0) then begin
                  res := 50;

                  if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13;

                  {$ifdef ResourceText}
                    if (Art = 'Return') then
                      ErrorText := ErrorText + FormatMessageText (1884, [])
                    else if (landstr = 'DE') then
                      ErrorText := ErrorText + FormatMessageText (1882, [])
                    else
                      ErrorText := ErrorText + FormatMessageText (1883, []);
                  {$else}
                    if (Art = 'Return') then
                      ErrorText := ErrorText + Format ('Kein Retouren Versandprodukt definiert', [])
                    else if (landstr = 'DE') then
                      ErrorText := ErrorText + Format ('Kein nationales Versandprodukt definiert', [])
                    else
                      ErrorText := ErrorText + Format ('Kein internationales Versandprodukt definiert', []);
                  {$endif}
                end else begin
                  versart := strlist[0];

                  jsonstr := jsonstr + ',"tpls"'+':'+'["'+versart+'"]';

                  for i:= 1 to strlist.Count - 1 do begin
                    if (strlist[i] = 'PRIORITY') or (strlist[i] = 'TRACKED') then begin
                      if (Length (addsvrstr) > 0) then addsvrstr := addsvrstr + ',';
                      addsvrstr := addsvrstr + '"'+strlist[i]+'"';
                    end else begin
                      if (Length (svrstr) > 0) then svrstr := svrstr + ',';
                      svrstr := svrstr + '"'+strlist[i]+'"';
                    end;
                  end;
                end;
              finally
                strlist.Free;
              end;

              if (query.FieldByName('OPT_SPERRGUT').AsString = '1') then begin
                if not (query.FieldByName('OPT_SPERRGUT_AS_NORMAL').AsString = '1') then begin
                  if (Length (addsvrstr) > 0) then addsvrstr := addsvrstr + ',';
                  addsvrstr := addsvrstr + '"BULKY_GOODS"';
                end;
              end;

              if (WarenWert > 0) and (query.FieldByName('OPT_NACHNAHME').AsString > '0') then begin
                if (Length (addsvrstr) > 0) then addsvrstr := addsvrstr + ',';
                addsvrstr := addsvrstr + '"CASH_ON_DELIVERY"';
              end;

              if (Length (svrstr) > 0) then
                jsonstr := jsonstr + ',"services"'+':'+'['+svrstr+']';

              if (Length (addsvrstr) > 0) then
                jsonstr := jsonstr + ',"additionalServices":['+addsvrstr+']';

              if Assigned (gatequery.FindField('INCOTERM')) and not (gatequery.FieldByName('INCOTERM').IsNull) then
                jsonstr := jsonstr + ',"incoterms"'+':'+'"'+gatequery.FieldByName('INCOTERM').AsString+'"'
              else begin
                cfgquery.SQL.Clear;
                cfgquery.SQL.Add ('select * from V_AUFTRAG_ADD_INFOS where REF_AUF_KOPF=:ref');
                cfgquery.Params [0].Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

                cfgquery.Open;

                if Assigned (cfgquery.FindField('INCOTERMS_CODE')) and not (cfgquery.FieldByName('INCOTERMS_CODE').IsNull) then
                  jsonstr := jsonstr + ',"incoterms"'+':'+'"'+cfgquery.FieldByName('INCOTERMS_CODE').AsString+'"';

                cfgquery.Close;
              end;

              if (PrtInfo.Port = 'pdf') then begin
                jsonstr := jsonstr + ',"labelFormat"'+':'+'"PDF"';
                jsonstr := jsonstr + ',"paperSize"'+':'+'"A4"';
              end else begin
                jsonstr := jsonstr + ',"labelFormat"'+':'+'"ZPLA6"';
                jsonstr := jsonstr + ',"paperSize"'+':'+'"A6"';
              end;

              jsonstr := jsonstr + ',"skipPrinting"'+':'+'true';

              if Assigned (gatequery.FindField ('ABSENDER')) and not gatequery.FieldByName('ABSENDER').IsNull then begin
                strlist := TStringList.Create;
                try
                  strlist.Delimiter := ';';
                  strlist.StrictDelimiter := true;

                  strlist.DelimitedText := gatequery.FieldByName('ABSENDER').AsString;

                  if (strlist.Count > 2) then begin
                    if (Art = 'Return') then
                      jsonstr := jsonstr + ',"receiver": {'
                    else
                      jsonstr := jsonstr + ',"sender": {';

                      jsonstr := jsonstr + '"name1"'+':'+'"'+ConvertJSONSonderzeichen (strlist[0])+'"';

                      if (Length (strlist[1]) > 0) and (strlist[0] <> strlist[1]) then
                        jsonstr := jsonstr + ',"name2"'+':'+'"'+ConvertJSONSonderzeichen (strlist[1])+'"';

                      if (strlist.Count > 2) then
                        jsonstr := jsonstr + ',"street"'+':'+'"'+ConvertJSONSonderzeichen (strlist[2])+'"';

                      if (strlist.Count > 3) then
                        jsonstr := jsonstr + ',"houseNo"'+':'+'"'+ConvertJSONSonderzeichen (strlist[3])+'"';

                      if (strlist.Count > 4) then
                        jsonstr := jsonstr + ',"postCode"'+':'+'"'+strlist[4]+'"';

                      if (strlist.Count > 5) then
                        jsonstr := jsonstr + ',"city"'+':'+'"'+ConvertJSONSonderzeichen (strlist[5])+'"';

                      if (strlist.Count > 6) then
                        jsonstr := jsonstr + ',"country"'+':'+'"'+strlist[6]+'"';

                    jsonstr := jsonstr + '}';
                  end;
                finally
                  strlist.Free;
                end;
              end else begin
                cfgquery.SQL.Clear;
                //Bei Dropship wird die Traderadresse angezeigt
                cfgquery.SQL.Add ( 'select'
                                  +'  tadr.REF as REF,'
                                  +'  translate (tadr.NAME1 using CHAR_CS) as TRADER_NAME,'
                                  +'  translate (tadr.NAMEZUSATZ using CHAR_CS) as TRADER_NAMEZUSATZ,'
                                  +'  coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as ABSENDER,'
                                  +'  ml.ABSENDER_ZUSATZ,'
                                  +'  coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                                  +'  coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                                  +'  coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                                  +'  coalesce (translate (tadr.LAND_ISO using CHAR_CS),ml.ABSENDER_LAND,loc.LAND,''DE'') as LAND,'
                                  +'  coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON'
                                  +' from'
                                  +'  V_AUFTRAG auf'
                                  +'  inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                                  +'  inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                                  +'  left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                                  +'  left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF and (ml.ZIEL_LAND_ISO is null or ml.ZIEL_LAND_ISO=:land_iso))'
                                  +' where auf.REF=:ref_auf'
                                  +' order by ml.ZIEL_LAND_ISO nulls last'
                                  );

                cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;
                cfgquery.Params.ParamByName ('land_iso').Value := isostr;

                cfgquery.Open;

                if (cfgquery.FieldByName('TRADER_NAME').IsNull and cfgquery.FieldByName('ABSENDER').IsNull) then begin
                  ;
                end else begin
                  if (Art = 'Return') then
                    jsonstr := jsonstr + ',"receiver": {'
                  else
                    jsonstr := jsonstr + ',"sender": {';

                    if not (cfgquery.FieldByName('TRADER_NAME').IsNull) then begin
                      jsonstr := jsonstr + '"name1"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('TRADER_NAME').AsString)+'"';

                      if not cfgquery.FieldByName('TRADER_NAMEZUSATZ').IsNull and (cfgquery.FieldByName('TRADER_NAME').AsString <> cfgquery.FieldByName('TRADER_NAMEZUSATZ').AsString) then
                        jsonstr := jsonstr + ',"name2"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('TRADER_NAMEZUSATZ').AsString)+'"';
                    end else begin
                      jsonstr := jsonstr + '"name1"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('ABSENDER').AsString)+'"';

                      if not cfgquery.FieldByName('ABSENDER_ZUSATZ').IsNull and (cfgquery.FieldByName('ABSENDER').AsString <> cfgquery.FieldByName('ABSENDER_ZUSATZ').AsString) then
                        jsonstr := jsonstr + ',"name2"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('ABSENDER_ZUSATZ').AsString)+'"';
                    end;

                    absnrstr     := '';
                    absstreetstr := cfgquery.FieldByName('STRASSE').AsString;

                    idx := Length (absstreetstr);
                    while (idx > 1) and (absstreetstr [idx] <> ' ') do
                      Dec (idx);

                    if (idx > 1) then begin
                      absnrstr := copy (absstreetstr, idx + 1);
                      absstreetstr := Copy (absstreetstr, 1, idx);
                    end;

                    jsonstr := jsonstr + ',"street"'+':'+'"'+ConvertJSONSonderzeichen (absstreetstr)+'"';
                    jsonstr := jsonstr + ',"houseNo"'+':'+'"'+ConvertJSONSonderzeichen (absnrstr)+'"';
                    jsonstr := jsonstr + ',"postCode"'+':'+'"'+cfgquery.FieldByName('PLZ').AsString+'"';
                    jsonstr := jsonstr + ',"city"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('ORT').AsString)+'"';
                    jsonstr := jsonstr + ',"country"'+':'+'"'+cfgquery.FieldByName('LAND').AsString+'"';

                  jsonstr := jsonstr + '}';
                end;
              end;

              if (Art = 'Return') then
                jsonstr := jsonstr + ',"sender": {'
              else
                jsonstr := jsonstr + ',"receiver": {';

                //ShipToAdresse1
                jsonstr := jsonstr + '"name1"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('NAME1').AsString)+'"';

                if (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
                begin
                  if (liefquery.FieldByName('NAME1').AsString <> liefquery.FieldByName('NAME2').AsString) then
                    jsonstr := jsonstr + ',"name2"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('NAME2').AsString)+'"'
                end else if (Length (liefquery.FieldByName('STRASSE_2').AsString) > 4) then
                  //VCE kennt keine Strasse_2, daher kommt das ab 4 Zeichen in ShipToAdresse2
                  jsonstr := jsonstr + ',"name2"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('STRASSE_2').AsString)+'"';

                jsonstr := jsonstr + ',"street"'+':'+'"'+ConvertJSONSonderzeichen (streetstr)+'"';
                jsonstr := jsonstr + ',"houseNo"'+':'+'"'+ConvertJSONSonderzeichen (nrstr)+'"';

                //VCE kennt IC für die kanarischen Inseln nicht
                if (isostr = 'IC') then
                  jsonstr := jsonstr + ',"country"'+':'+'"'+'ES'+'"'
                else
                  jsonstr := jsonstr + ',"country"'+':'+'"'+isostr+'"';

                //Für UPS wird der State-Code für US benötigt.
                if Assigned (liefquery.FindField ('STATE')) and not (liefquery.FieldByName('STATE').IsNull) then
                  jsonstr := jsonstr + ',"state"'+':'+'"'+liefquery.FieldByName('STATE').AsString+'"';

                jsonstr := jsonstr + ',"postCode"'+':'+'"'+liefquery.FieldByName('PLZ').AsString+'"';
                jsonstr := jsonstr + ',"city"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('ORT').AsString)+'"';
                jsonstr := jsonstr + ',"phone"'+':'+'"'+telstr+'"';
                jsonstr := jsonstr + ',"mail"'+':'+'"'+mailstr+'"';
              jsonstr := jsonstr + '}';

              jsonstr := jsonstr + ',"packages": [';
                jsonstr := jsonstr + '{';
                  jsonstr := jsonstr + '"reference"'+':'+'"'+idstr+'"';

                  jsonstr := jsonstr + ',"orderNo"'+':'+'"'+GetSendungsReferenz+'"';
                  jsonstr := jsonstr + ',"reference2"'+':'+'"'+GetSendungsReferenz2+'"';

                  if (Length (lnstr) > 0) then
                    jsonstr := jsonstr + ',"length"'+':'+lnstr;
                  if (Length (wistr) > 0) then
                    jsonstr := jsonstr + ',"width"'+':'+wistr;
                  if (Length (histr) > 0) then
                    jsonstr := jsonstr + ',"height"'+':'+histr;

                  if (Length (gwstr) > 0) then
                    jsonstr := jsonstr + ',"weight"'+':'+StringReplace (gwstr, ',', '.', [rfReplaceAll]);  //Gewicht mit .

                  jsonstr := jsonstr + ',"type"'+':"'+ltstr+'"';

                  if (WarenWert > 0) and (query.FieldByName('OPT_NACHNAHME').AsString > '0') then begin
                    if query.FieldByName('CURRENCY').IsNull then
                      jsonstr := jsonstr + ',"cashCurrency"'+':'+'"'+'EUR'+'"'
                    else
                      jsonstr := jsonstr + ',"cashCurrency"'+':'+'"'+query.FieldByName('CURRENCY').AsString+'"';

                    jsonstr := jsonstr + ',"cashValue":'+StringReplace (Format('%6.3f', [WarenWert]), ',', '.', [rfReplaceAll]);  //Betrag mit .

                    if not (query.FieldByName('RECHNUNGS_NR').IsNull) then
                      textstr := ConvertSonderzeichen ('invoice no.: '+query.FieldByName('RECHNUNGS_NR').AsString+' from ' + query.FieldByName('VERSAND_DATUM').AsString + ', '+adrquery.FieldByName('NAME1').AsString+', '+adrquery.FieldByName('ORT').AsString)
                    else
                      textstr := ConvertSonderzeichen ('order no.: '+query.FieldByName('AUFTRAG_NR').AsString+' from ' + query.FieldByName('VERSAND_DATUM').AsString + ', '+adrquery.FieldByName('NAME1').AsString+', '+adrquery.FieldByName('ORT').AsString);

                    jsonstr := jsonstr + ',"codReference":"'+textstr+'"';
                  end;

                  //Die Zollinfos der Artikel
                  cfgquery.SQL.Clear;
                  //Alle Bestände auf der NVE, welche keine Textartikel sind, das Gewicht ist das Artikelgewicht
                  cfgquery.SQL.Add('select mcfg.OPT_AUTO_CUSTOMS,ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN''),bes.MENGE,vpe.KURZ_BEZEICHNUNG,ar.COUNTRY_OF_ORIGIN'
                                  +',nvl (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER),nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT)/1000'
                                  +',nvl (rep.NETTO_BETRAG,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT) as NETTO_BETRAG'
                                  +',nvl (arset.ANZAHL_VPE, 1) as ANZAHL_VPE'
                                  +',re.CURRENCY'
                                  +',pos.REF_AR as POS_REF_AR, ae.REF_AR'
                                  +' from'
                                  +'   V_LAGER_NVE_BESTAND bes'
                                  +'   inner join V_AUFTRAG_POS pos on (pos.REF=bes.REF_AUF_POS)'
                                  +'   inner join VQ_AUFTRAG auf on (auf.REF=pos.REF_AUF_KOPF)'
                                  +'   inner join V_AUFTRAG_RECHNUNG re on (re.REF_AUF_KOPF=auf.REF)'
                                  +'   inner join V_AUFTRAG_POS_RECHNUNG rep on (rep.REF_AUF_POS=pos.REF)'
                                  +'   inner join V_ARTIKEL posar on (posar.REF=pos.REF_AR)'
                                  +'   inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=bes.REF_AR_EINHEIT)'
                                  +'   inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'
                                  +'   inner join V_ARTIKEL_VPE vpe on (vpe.REF=ae.REF_EINHEIT)'
                                  +'   inner join V_MANDANT_CONFIG mcfg on (mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND))'
                                  +'   left outer join V_ARTIKEL_SET arset on (arset.REF=posar.REF_ARTIKEL_SET)'
                                  +' where'
                                  +'   nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0'
                                  +'   and bes.REF_NVE=:ref'
                                  );
                  cfgquery.Params [0].Value := query.FieldByName('REF_MAIN_NVE').AsInteger;

                  cfgquery.Open;

                  idx := 0;

                  jsonstr := jsonstr + ',"packageDetails":[';

                  while not (cfgquery.Eof) and (res = 0) do begin
                    Inc (idx);

                    //Prüfung auf Preis und Zolltarifnummer nur wenn die Ware nach NoEU geht
                    if not IsLandEU (landstr, query, liefquery.FieldByName ('PLZ').AsString) then begin
                      if (cfgquery.Fields [0].AsString > '0') then begin
                        if (cfgquery.Fields [6].IsNull) then begin
                          res := 56;

                          if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13;

                          {$ifdef ResourceText}
                            ErrorText := ErrorText + FormatMessageText (1688, [cfgquery.Fields [1].AsString]);
                          {$else}
                            ErrorText := ErrorText + Format ('Export nicht möglich, fehlende Zolltarifnummer für Artikel %s', [cfgquery.Fields [1].AsString]);
                          {$endif}
                        end;

                        if (cfgquery.Fields [8].IsNull) then begin
                          res := 57;

                          if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13;
                          {$ifdef ResourceText}
                            ErrorText := ErrorText + FormatMessageText (1689, [cfgquery.Fields [1].AsString]);
                          {$else}
                            ErrorText := ErrorText + Format ('Export nicht möglich, fehlende Preis für Artikel %s', [cfgquery.Fields [1].AsString]);
                          {$endif}
                        end;
                      end;
                    end;

                    if (res = 0) then begin
                      if (idx > 1) then
                        jsonstr := jsonstr + ',{'
                      else
                        jsonstr := jsonstr + '{';

                      jsonstr := jsonstr + '"description"'+':'+'"'+ConvertJSONSonderzeichen (copy (cfgquery.Fields [2].AsString, 1, 32))+'"';
                      jsonstr := jsonstr + ',"quantity"'+':'+copy (cfgquery.Fields [3].AsString, 1, 32);

                      //WeightX, Gewicht ist min. 10 Gramm
                      if (cfgquery.Fields [7].IsNull or (cfgquery.Fields [7].AsFloat < 0.01)) then
                        csvstr := '0,01'
                      else
                        csvstr := Format('%6.3f', [cfgquery.Fields [7].AsFloat]);

                      jsonstr := jsonstr + ',"weight"'+':'+StringReplace (csvstr, ',', '.', [rfReplaceAll]);


                      jsonstr := jsonstr + ',"customsData":{';

                      //jsonstr := jsonstr + ',"PartNumber"'+':'+'"'+ConvertJSONSonderzeichen (copy (cfgquery.Fields [1].AsString, 1, 32))+'"';
                      jsonstr := jsonstr + '"description"'+':'+'"'+ConvertJSONSonderzeichen (copy (cfgquery.Fields [2].AsString, 1, 32))+'"';

                      //Nur ausgeben, wenn die Zollabwicklung aktiv ist
                      if noneuflag and (cfgquery.Fields [0].AsString > '0') then begin
                         //PriceX, Betrag ist min. 10 Cent
                        if (cfgquery.Fields [8].IsNull or (cfgquery.Fields [8].AsInteger < 10)) then
                          csvstr := Format('%6.3f', [0.1])
                        else begin
                          if (cfgquery.FieldByName ('ANZAHL_VPE').AsInteger > 1) and (cfgquery.FieldByName ('REF_AR').AsInteger <> cfgquery.FieldByName ('POS_REF_AR').AsInteger) then
                            csvstr := Format('%6.3f', [cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000 / cfgquery.FieldByName ('ANZAHL_VPE').AsInteger])
                          else
                            csvstr := Format('%6.3f', [cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000]);
                        end;
                        jsonstr := jsonstr + ',"value"'+':'+csvstr;

                        if cfgquery.FieldByName('CURRENCY').IsNull then
                          jsonstr := jsonstr + ',"currency"'+':'+'"'+'EUR'+'"'
                        else
                          jsonstr := jsonstr + ',"currency"'+':'+'"'+cfgquery.FieldByName('CURRENCY').AsString+'"';

                        if (cfgquery.Fields [6].IsNull) then
                          taricstr := ''
                        else
                          taricstr := StringReplace (copy (cfgquery.Fields [6].AsString, 1, 32), ';', ',', [rfReplaceAll]);
                        jsonstr := jsonstr + ',"code"'+':'+'"'+taricstr+'"';
                      end;

                      jsonstr := jsonstr + ',"quantity"'+':'+copy (cfgquery.Fields [3].AsString, 1, 32);

                      //OriginCountryX
                      if (cfgquery.Fields [5].IsNull) then
                        csvstr := 'DE'
                      else
                        csvstr := StringReplace (copy (cfgquery.Fields [5].AsString, 1, 32), ';', ',', [rfReplaceAll]);
                      jsonstr := jsonstr + ',"countryOfOrigin"'+':'+'"'+csvstr+'"';

                      jsonstr := jsonstr + '}';
                      jsonstr := jsonstr + '}';
                    end;

                    cfgquery.Next;
                  end;

                  jsonstr := jsonstr + ']';
                  jsonstr := jsonstr + '}';

                  cfgquery.Close;

                  jsonstr := jsonstr + ']';

              jsonstr := jsonstr + '}]';
            end;

            if (res = 0) then begin
              ForceDirectories(DatenPath + RESTDumpDir+'VCE\'+FormatDateTime ('yyyymmdd', Now));

              StrToFile (DatenPath + RESTDumpDir+'VCE\'+FormatDateTime ('yyyymmdd', Now)+'\'+StringReplace (apistr, '/', '_',[rfReplaceAll])+'_'+versart+'_'+nvestr+'.json', jsonstr);

              sdata := TMemoryStream.Create;

              try
                sdata.Clear;
                if SendRequest (urlentry.Host, // Host,
                                urlentry.Port, //Port
                                urlentry.Path + '/' + apistr, // Service
                                'POST', //Methode
                                '', // Proxy,
                                vceuser, vcepwd, // User , PW
                                '', //Action
                                'application/json', //ContentType
                                [], //AddHeader
                                UTF8Encode (jsonstr),         // RequestData
                                resp,
                                sdata, //ResponseStream
                                errcode, // Fehlercode
                                errtext) // Fehlertext
                              then
                begin
                  StrToFile (DatenPath + RESTDumpDir+'VCE\'+FormatDateTime ('yyyymmdd', Now)+'\'+StringReplace (apistr, '/', '_',[rfReplaceAll])+'_'+versart+'_resp_'+nvestr+'.txt', resp);

                  sdata.Position := 0;
                  sdata.SaveToFile(DatenPath + RESTDumpDir+'VCE\'+FormatDateTime ('yyyymmdd', Now)+'\'+StringReplace (apistr, '/', '_',[rfReplaceAll])+'_'+versart+'_tracking_'+nvestr+'.json');

                  if (copy (uppercase (resp), 1, 12) <> 'HTTP/1.1 200') then begin
                    res := 55;

                    fs := Nil;
                    js := Nil;

                    try
                      {$ifdef UNICODE}
                        SetLength(utfstr, sdata.Size);
                        sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                        datastr := StringUtils.StringToUTF (utfstr);

                        js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
                      {$else}
                        sdata.Position := 0;
                        js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
                      {$endif}

                      if not Assigned (js) then begin
                        {$ifdef UNICODE}
                          ErrorText := datastr;
                        {$else}
                          ErrorText := MemoryStreamToString (sdata);
                        {$endif}
                      end else begin
                        us := js.Field['message'];
                        if Assigned (us) then
                          ErrorText := us.Value
                        else
                          {$ifdef UNICODE}
                            ErrorText := datastr;
                          {$else}
                            ErrorText := MemoryStreamToString (sdata)
                          {$endif}
                      end;
                    finally
                      if Assigned (js) then
                        js.Free;
                    end;
                  end else begin
                    fs := Nil;
                    js := Nil;

                    try
                      {$ifdef UNICODE}
                        SetLength(utfstr, sdata.Size);
                        sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                        datastr := StringUtils.StringToUTF (utfstr);

                        //Die [] entfernen
                        js := TlkJSONstreamed.ParseText (copy (datastr, 2, Length (datastr) - 2)) as TlkJsonObject;
                      {$else}
                        sdata.Position := 0;
                        js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
                      {$endif}

                      if Assigned (js) then begin
                        fs := js.Field['error'];
                      end;

                      if not Assigned (fs) then begin
                        res := 11;

                        ErrorText := 'API error message';
                      end else begin
                        if VarIsNull (fs.Value) or (fs.Value) then begin
                          res := 11;

                          ErrorText := 'API error message';

                          us := js.Field['errorDesc'];
                          if Assigned (us) then
                            ErrorText := ErrorText + #13 + us.Value;
                        end else begin
                          us := js.Field['masterTrackingNo'];
                          if Assigned (us) then begin
                            SendungsID := us.Value;
                          end;

                          fs := js.Field['details'];

                          if not Assigned (fs) then begin
                            res := 11;

                            ErrorText := 'API error message';
                          end else begin
                            cs := fs.Child[0];

                            if (Art = 'Return') then begin
                              us := cs.Field['returnTrackingNo'];
                              if not Assigned (us) then begin
                                res := 15;
                                ErrorText := 'returnTrackingNo code error';
                              end else begin
                                SendungsNr := us.Value;
                              end;

                              ps := cs.Field['parameters'];
                              if Assigned (ps) then begin
                                for i := 0 to ps.Count - 1 do begin
                                  us := ps.Child [i].Field['key'];
                                  if Assigned (us) then begin
                                    if (us.Value = 'trackinglink') then begin
                                      us := ps.Child [i].Field['value'];
                                      if Assigned (us) then begin
                                        TrackUrl := us.Value;
                                      end;
                                    end;
                                  end;
                                end;
                              end;

                              us := cs.Field['returnLabelFormat'];
                              if Assigned (us) then begin
                                if (us.Value = 'ZPL') then
                                  LabelFormat := 'zpl'
                                else if (us.Value = 'PDFA4') then
                                  LabelFormat := 'pdf'
                                else
                                  LabelFormat := '';
                              end;

                              if (LabelFormat = 'zpl') then
                                us := cs.Field['returnZPLLabelData']
                              else if (LabelFormat = 'pdf') then
                                us := cs.Field['returnPDFLabelData']
                              else
                                us := nil;

                              if Assigned (us) then begin
                                if not (VarIsNull (us.Value)) then begin
                                  lblstr := us.Value;

                                  if (Length (lblstr) > 0) then begin
                                    if (LabelFormat = 'zpl') then
                                      LabelImage.WriteBuffer (Pointer(lblstr)^, Length (lblstr))
                                    else begin
                                      try
                                        bytes  := TNetEncoding.Base64.DecodeStringToBytes(lblstr);

                                        pdfstr := '';

                                        idx := Low (bytes);
                                        while (idx <= High (bytes)) do begin
                                          pdfstr := pdfstr + AnsiChar (bytes [idx]);

                                          Inc (idx);
                                        end;
                                      except
                                      end;

                                      LabelImage.WriteBuffer (Pointer(pdfstr)^, Length (pdfstr))
                                    end;

                                    if not (DirectoryExists (DatenPath + LabelDumpDir+'VCE')) then
                                      ForceDirectories(DatenPath + LabelDumpDir + 'VCE');

                                    LabelImage.Position := 0;

                                    try
                                      LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'VCE\' + versart+'_'+SendungsNr + '.' + LabelFormat);
                                    except
                                    end;
                                  end;
                                end;
                              end;
                            end else begin
                              us := cs.Field['trackingNo'];
                              if not Assigned (us) then begin
                                res := 15;
                                ErrorText := 'trackingNo code error';
                              end else begin
                                SendungsNr := us.Value;
                              end;

                              ps := cs.Field['parameters'];
                              if Assigned (ps) then begin
                                for i := 0 to ps.Count - 1 do begin
                                  us := ps.Child [i].Field['key'];
                                  if Assigned (us) then begin
                                    if (us.Value = 'trackinglink') then begin
                                      us := ps.Child [i].Field['value'];
                                      if Assigned (us) then begin
                                        TrackUrl := us.Value;
                                      end;
                                    end;
                                  end;
                                end;
                              end;

                              us := cs.Field['labelFormat'];
                              if Assigned (us) then begin
                                if (us.Value = 'ZPL') then
                                  LabelFormat := 'zpl'
                                else if (us.Value = 'PDF') then
                                  LabelFormat := 'pdf'
                                else
                                  LabelFormat := '';
                              end;

                              if (LabelFormat = 'zpl') then
                                us := cs.Field['zplLabelData']
                              else if (LabelFormat = 'pdf') then
                                us := cs.Field['zplLabelData']
                              else
                                us := nil;

                              if Assigned (us) then begin
                                if not (VarIsNull (us.Value)) then begin
                                  lblstr := us.Value;

                                  if (Length (lblstr) > 0) then begin
                                    LabelImage.WriteBuffer (Pointer(lblstr)^, Length (lblstr));

                                    if not (DirectoryExists (DatenPath + LabelDumpDir+'VCE')) then
                                      ForceDirectories(DatenPath + LabelDumpDir + 'VCE');

                                    LabelImage.Position := 0;

                                    try
                                      LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'VCE\' + versart+'_'+SendungsNr + '.' + LabelFormat);
                                    except
                                    end;
                                  end;
                                end;
                              end;
                            end;
                          end;
                        end;
                      end;
                    finally
                      if Assigned (js) then
                        js.Free;
                    end;
                  end;
                end else begin
                  res := errcode;
                  ErrorText := errtext;

                  StrToFile (DatenPath + RESTDumpDir + 'VCE\'+FormatDateTime ('yyyymmdd', Now)+'\'+StringReplace (apistr, '/', '_',[rfReplaceAll])+'_'+versart+'_resp_'+nvestr+'.json', resp);
                end;
              finally
                sdata.Free;
              end;
            end;
          finally
            spedquery.Free;
          end;
        end;
      finally
        cfgquery.Free;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    Result := res;
  end;

  //******************************************************************************
  //* Function Name: CreateVLOGLabel
  //* Author       : Stefan Graf
  //* Datum        : 11.06.2024
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateVLOGLabel (const Art, Absender, Versender, IDNummer : String;
                            const WarenWert : Double;
                            var   SendungsNr, TrackUrl, Barcode, LabelFormat : String;
                            LabelImage : TMemoryStream; var ErrorText : String) : Integer;
  var
    idx,
    res,
    keyidx,
    errcode  : Integer;
    errtext  : String;
    xmlstr   : AnsiString;
    urlparam : String;
    body     : UTF8String;
    resp     : String;
    hdr      : String;
    nve      : String;
    labelurl : String;
    nrstr    : String;
    orderid  : String;
    timestr  : String;
    keystr,
    streetstr,
    datastr  : String;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    cfgquery : TSmartQuery;
    dhmquery : TSmartQuery;
    olddec   : Char;
    found    : boolean;
    urlstr,
    authurlstr,
    apiurlstr,
    prodstr,
    carrierstr,
    telstr,
    idstr,
    cfgstr,
    userid,
    usersec   : String;
    gw,
    strpos,
    suchofs,
    urlport   : Integer;
    xmlresp   : TXMLFile;
    xmllnd    : TXMLTagEntry;
  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      ErrorText := '';

      urlstr  := '';

      cfgquery  := TSmartQuery.Create (Nil);

      try
        cfgquery.ReadOnly := True;
        cfgquery.Session := Query.Session;

        cfgquery.SQL.Add ('select cfg.REST_URL as SPED_REST_URL, g.* from V_SPED_GATEWAY g, V_SPED_CONFIG cfg where cfg.REF_SPED=g.REF_SPED and g.REF=:ref');
        cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        cfgquery.Open;

        if cfgquery.FieldByName('SENDIT_PRODUKT').IsNull then
          ErrorText := 'No product defined'
        else begin
          strlist := TStringList.Create;
          strlist.Delimiter := ';';
          strlist.StrictDelimiter := true;

          strlist.DelimitedText := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;

          carrierstr := strlist [0];
          if (strlist.Count > 1) then
            prodstr := strlist [1]
          else
            prodstr := 'STD';

          telstr  := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;
          cfgstr  := cfgquery.FieldByName('SENDIT_CONFIG').AsString;

          if Assigned (cfgquery.FindField ('REST_URL')) then
            urlstr  := cfgquery.FieldByName('REST_URL').AsString;

          if (Length (urlstr) = 0) then
            urlstr  := cfgquery.FieldByName('SPED_REST_URL').AsString;

          if (Length (urlstr) = 0) then
            ErrorText := 'Not REST url defined'
          else begin
            if (lowercase (copy (urlstr, 1, 7)) = 'http://') then
              suchofs := 8
            else if (lowercase (copy (urlstr, 1, 8)) = 'https://') then
              suchofs := 9
            else
              suchofs := 1;

            strpos := Pos (':', urlstr, suchofs);

            if (strpos = 0) then
              urlport := -1
            else if not (TryStrToInt (copy (urlstr, strpos + 1), urlport)) then
              ErrorText := 'Port definition invalide'
            else begin
              urlstr := copy (urlstr, 1, strpos - 1);
            end;
          end;

          if (Length (ErrorText) > 0) then
            res := 29;
        end;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (res = 0) then begin
        if not (ForceDirectories(DatenPath + 'soap\VLOG\'+FormatDateTime ('yyyymmdd', Now))) then begin
          if Assigned (SendITLog) then begin
            SendITLog.Logging (clError, 'Versand: VLOG, error ForceDirectories, DatenPath='+DatenPath+CR+LF);
          end;
        end;

        sdata := TMemoryStream.Create;

        try
          nve := query.FieldByName('NVE_NR').AsString;

          if (query.FieldByName('COUNT_FREIGABE').AsInteger > 1) then
            orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('COUNT_FREIGABE').AsString
          else if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
            orderid := query.FieldByName('AUFTRAG_NR').AsString
          else
            orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;

          if query.FieldByName('BRUTTO_GEWICHT').IsNull then
            gw := 10
          else if (query.FieldByName('BRUTTO_GEWICHT').AsFloat < 0.01) then
            gw := 10
          else
            gw := round (query.FieldByName('BRUTTO_GEWICHT').AsFloat * 1000);

          if Assigned (SendITLog) then begin
            SendITLog.Logging (clNormal, 'Versand: VLOG, RefNVE: '+IntToStr (query.FieldByName('REF_NVE').AsInteger)+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
          end;

          body := '<workitem>';
          body := body + '  <shipment collection ="true">';

          body := body + '  <send_referenznr_01>'+orderid+'</send_referenznr_01>';
          body := body + '  <send_mandant>'+query.FieldByName('SENDIT_CLIENT').AsString+'</send_mandant>';
          body := body + '  <frachtfuehrer>'+carrierstr+'</frachtfuehrer>';

          nrstr  := '';
          streetstr := liefquery.FieldByName ('STRASSE').AsString;

          idx := Length (streetstr);
          while (idx > 1) and (streetstr [idx] <> ' ') do
            Dec (idx);

          if (idx > 1) then begin
            nrstr := copy (streetstr, idx + 1);
            streetstr := Copy (streetstr, 1, idx);
          end;

          body := body + '<empf_adr_01>'+XMLString (StringReplace (liefquery.FieldByName('NAME1').AsString, ';', ',', [rfReplaceAll]))+'</empf_adr_01>';
          body := body + '<empf_adr_02>'+XMLString (StringReplace (liefquery.FieldByName('NAME2').AsString, ';', ',', [rfReplaceAll]))+'</empf_adr_02>';

          if not (kepemail) or liefquery.FieldByName('EMAIL').IsNull then
            body := body + '<empf_Mail>'+XMLString (StringReplace (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString, ';', ',', [rfReplaceAll]))+'</empf_Mail>'
          else
            body := body + '<empf_Mail>'+XMLString (StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll]))+'</empf_Mail>';

          body := body + '<empf_Tel>'+XMLString (StringReplace (liefquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll]))+'</empf_Tel>';

          body := body + '<empf_strasse>'+XMLString (StringReplace (streetstr, ';', ',', [rfReplaceAll]))+'</empf_strasse>'+
                         '<empf_hnr>'+XMLString (StringReplace (nrstr, ';', ',', [rfReplaceAll]))+'</empf_hnr>';

          body := body + '<empf_plz>'+liefquery.FieldByName('PLZ').AsString+'</empf_plz>'+
                         '<empf_ort>'+XMLString (StringReplace (liefquery.FieldByName('ORT').AsString, ';', ',', [rfReplaceAll]))+'</empf_ort>'+
                         '<empf_land_kurz>'+landstr+'</empf_land_kurz>';
          (*
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            cfgquery.SQL.Add ('select * from V_NVE_INHALT where REF_NVE=:ref');
            cfgquery.Params [0].Value := query.FieldByName('REF_MAIN_NVE').AsInteger;

            cfgquery.Open;

            body := body + '  ,"Parcel_items":[';

            while not (cfgquery.Eof) do begin
              if (cfgquery.RecNo > 1) then body := body + ',';

              body := body + '  {'+
                             '    "description": "'+AnsiToUtf8 (ConvertJSONSonderzeichen (StringReplace (cfgquery.FieldByName('ARTIKEL_TEXT').AsString, ';', ',', [rfReplaceAll])))+'",'+
                             '    "quantity": '+cfgquery.FieldByName('MENGE').AsString+','+
                             '    "sku": "'+AnsiToUtf8 (ConvertJSONSonderzeichen (StringReplace (cfgquery.FieldByName('ARTIKEL_NR').AsString, ';', ',', [rfReplaceAll])))+'"'+
                             '  }';

              cfgquery.Next;
            end;

            body := body + ']';

            cfgquery.Close;
          finally
            cfgquery.Free;
          end;
          *)

          //Zusätzliche Versandoptioen über Textartikel DHL - 2MH
          if (carrierstr='DHM') then begin
            dhmquery  := TSmartQuery.Create (Nil);

            try
              dhmquery.ReadOnly := True;
              dhmquery.Session := Query.Session;

              dhmquery.SQL.Add ('select'
                               +'   case when arq.VERSAND_ART=''~'' then null else arq.VERSAND_ART end, ar.ARTIKEL_TEXT'
                               +' from'
                               +'   VQ_AUFTRAG_POS pos'
                               +'   inner join V_ARTIKEL ar on (ar.REF=pos.REF_AR)'
                               +'   inner join VQ_ARTIKEL arq on (arq.REF=ar.REF)'
                               +' where'
                               +'   (nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''1'')'
                               +'   and arq.VERSAND_ART in (''SZ'')'
                               +'   and pos.REF_AUF_KOPF=:ref_auf'
                               +' group by'
                               +'   arq.VERSAND_ART, ar.ARTIKEL_TEXT');
              dhmquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

              try
                dhmquery.Open;

                idx := 1;

                while not (dhmquery.Eof) do begin
                  body := body + '<Send_hilf_01>'+XMLString (dhmquery.Fields [0].AsString)+'</Send_hilf_01>';

                  dhmquery.Next;
                end;

                dhmquery.Close;
              except
              end;
            finally
              dhmquery.Free;
            end;
          end;

          body := body + '<package collection="true">';

          body := body + '<send_referenznr_06>'+nve+'</send_referenznr_06>';
          body := body + '<send_nr>'+'1'+'</send_nr>';
          body := body + '<Artikel_Pos_Anz>'+'1'+'</Artikel_Pos_Anz>';
          body := body + '<Send_BeschreibungInhalt>'+'Goods'+'</Send_BeschreibungInhalt>';
          body := body + '<send_2n_prodcode>'+prodstr+'</send_2n_prodcode>';

          body := body + '<Send_Laenge>'+IntToStr (query.FieldByName('L').AsInteger div 10)+'</Send_Laenge>'
                        +'<Send_Breite>'+IntToStr (query.FieldByName('B').AsInteger div 10)+'</Send_Breite>'
                        +'<Send_Hoehe>'+IntToStr (query.FieldByName('H').AsInteger div 10)+'</Send_Hoehe>'
                        +'<send_gewichteinzel>'+FormatFloat ('0.#', gw / 1000)+'</send_gewichteinzel>';

          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;
            //Alle Bestände auf der NVE, welche keine Textartikel sind, das Gewicht ist das Artikelgewicht
            cfgquery.SQL.Add('select mcfg.OPT_AUTO_CUSTOMS,ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN'') as ARTIKEL_TEXT,bes.MENGE,vpe.KURZ_BEZEICHNUNG,ar.COUNTRY_OF_ORIGIN'
                            +',nvl (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER) as TARIC_NUMBER'
                            +',nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT)/1000 as BRUTTO_GEWICHT'
                            +',nvl (rep.NETTO_BETRAG,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT) as NETTO_BETRAG'
                            +',nvl (arset.ANZAHL_VPE, 1) as ANZAHL_VPE'
                            +',ae.VERPACKUNG_FORM'
                            +',pos.REF_AR as POS_REF_AR, ae.REF_AR'
                            +' from'
                            +'   V_LAGER_NVE_BESTAND bes'
                            +'   inner join V_AUFTRAG_POS pos on (pos.REF=bes.REF_AUF_POS)'
                            +'   inner join VQ_AUFTRAG auf on (auf.REF=pos.REF_AUF_KOPF)'
                            +'   inner join V_AUFTRAG_RECHNUNG re on (re.REF_AUF_KOPF=auf.REF)'
                            +'   inner join V_AUFTRAG_POS_RECHNUNG rep on (rep.REF_AUF_POS=pos.REF)'
                            +'   inner join V_ARTIKEL posar on (posar.REF=pos.REF_AR)'
                            +'   inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=bes.REF_AR_EINHEIT)'
                            +'   inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'
                            +'   inner join V_ARTIKEL_VPE vpe on (vpe.REF=ae.REF_EINHEIT)'
                            +'   inner join V_MANDANT_CONFIG mcfg on (mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND))'
                            +'   left outer join V_ARTIKEL_SET arset on (arset.REF=posar.REF_ARTIKEL_SET)'
                            +' where'
                            +'   nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0'
                            +'   and bes.REF_NVE=:ref'
                            +' order by'
                            +'    pos.AUF_POS_NR'
                            );
            cfgquery.Params [0].Value := query.FieldByName('REF_MAIN_NVE').AsInteger;

            cfgquery.Open;

            idx := 1;

            while not (cfgquery.Eof) and (res = 0) do begin
              body := body + '<article collection="true">';

              body := body + '<Article_ID>'+cfgquery.FieldByName('ARTIKEL_NR').AsString+'</Article_ID>';
              body := body + '<Artikel_Pos>'+IntToStr (idx)+'</Artikel_Pos>';
              body := body + '<Artikel_Nr_01>'+cfgquery.FieldByName('ARTIKEL_NR').AsString+'</Artikel_Nr_01>';
              body := body + '<Artikel_Anz>'+cfgquery.FieldByName('ANZAHL_VPE').AsString+'</Artikel_Anz>';
              body := body + '<Artikel_Gruppe>'+cfgquery.FieldByName('VERPACKUNG_FORM').AsString+'</Artikel_Gruppe>';
              body := body + '<Artikel_Bezeichnung_01>'+XMLString (cfgquery.FieldByName('ARTIKEL_TEXT').AsString)+'</Artikel_Bezeichnung_01>';

               //PriceX, Betrag ist min. 10 Cent
              if (cfgquery.FieldByName ('NETTO_BETRAG').IsNull or (cfgquery.FieldByName ('NETTO_BETRAG').AsInteger < 10)) then
                datastr := Format('%6.3f', [0.1])
              else begin
                if (cfgquery.FieldByName ('ANZAHL_VPE').AsInteger > 1) and (cfgquery.FieldByName ('REF_AR').AsInteger <> cfgquery.FieldByName ('POS_REF_AR').AsInteger) then
                  datastr := Format('%6.3f', [cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000 / cfgquery.FieldByName ('ANZAHL_VPE').AsInteger])
                else
                  datastr := Format('%6.3f', [cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000]);
              end;

              //body := body + '<Artikel_Gewicht>'+datastr+'</Artikel_Gewicht>';

              body := body + '<Artikel_Gewicht>'+Format('%6.3f', [cfgquery.FieldByName ('BRUTTO_GEWICHT').AsFloat])+'</Artikel_Gewicht>';


              //Zusätzliche Versandoptioen über Textartikel DHL - 2MH
              if (carrierstr='DHM') then begin
                dhmquery  := TSmartQuery.Create (Nil);

                try
                  dhmquery.ReadOnly := True;
                  dhmquery.Session := Query.Session;

                  dhmquery.SQL.Add ('select'
                                   +'   case when arq.VERSAND_ART=''~'' then null else arq.VERSAND_ART end, ar.ARTIKEL_TEXT'
                                   +' from'
                                   +'   VQ_AUFTRAG_POS pos'
                                   +'   inner join V_ARTIKEL ar on (ar.REF=pos.REF_AR)'
                                   +'   inner join VQ_ARTIKEL arq on (arq.REF=ar.REF)'
                                   +' where'
                                   +'   (nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''1'')'
                                   +'   and arq.VERSAND_ART is not null'
                                   +'   and arq.VERSAND_ART not in (''SZ'')'
                                   +'   and pos.REF_AUF_KOPF=:ref_auf'
                                   +' group by'
                                   +'   arq.VERSAND_ART, ar.ARTIKEL_TEXT');
                  dhmquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

                  try
                    dhmquery.Open;

                    idx := 1;

                    while not (dhmquery.Eof) do begin
                      if not (dhmquery.Fields [0].IsNull) then begin
                        body := body + '<serviceindicator collection="true">';

                        body := body + '<collection_index>'+IntToStr (idx)+'</collection_index>';
                        body := body + '<serviceindicatorcode>'+XMLString (dhmquery.Fields [0].AsString)+'</serviceindicatorcode>';

                        body := body + '</serviceindicator>';

                        Inc (idx);
                      end;

                      dhmquery.Next;
                    end;

                    dhmquery.Close;
                  except
                  end;
                finally
                  dhmquery.Free;
                end;
              end;

              body := body + '</article>';

              Inc (idx);

              cfgquery.Next;
            end;

            cfgquery.Close;
          finally
            cfgquery.Free;
          end;

          body := body + '</package>';

          body := body + '</shipment>';
          body := body + '</workitem>';

          body :=  '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:wor="http://mhp-net.de/services/workflowexecutor">'
                  +'	<soapenv:Header/>'
                  +'	<soapenv:Body>'
                  +'		<wor:Execute>'
                  +'			<wor:command>Label:Print</wor:command>'
                  +'			<wor:serializedWorkItem>'
                  +'				<![CDATA['
                  +Trim (body)
                  +'        ]]>'
                  +'			</wor:serializedWorkItem>'
                  +'		</wor:Execute>'
                  +'	</soapenv:Body>'
                  +'</soapenv:Envelope>';

          StrToFile (DatenPath + 'soap\VLOG\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_'+nve+'.xml', body);

          sdata.Clear;
          if SendRequest (urlstr, // Host,
                          urlport, //Port
                          'workflowinterface', // Service
                          'POST', //Methode
                          '', // Proxy,
                          '', '', // User , PW
                          '', //Action
                          'text/xml', //ContentType
                          ['SOAPAction: http://mhp-net.de/services/workflowexecutor/Executor/Execute'], //AddHeader
                          UTF8Encode (body),         // RequestData
                          resp,
                          sdata, //ResponseStream
                          errcode, // Fehlercode
                          errtext) // Fehlertext
                        then
          begin
            StrToFile (DatenPath + 'soap\VLOG\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_resp_h_'+nve+'.txt', resp);

            labelurl := '';

            sdata.Position := 0;
            sdata.SaveToFile(DatenPath + 'soap\VLOG\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nve+'.xml');

            xmlresp := TXMLFile.Create(Nil);

            sdata.Position := 0;
            res := xmlresp.ParseXMLFile (sdata);

            (*
            if (res <> 0) then begin
              res := 11;
              ErrorText := 'API error message';
            end else begin
            *)
              try
                xmllnd := xmlresp.FindTag ('a:string');

                if not Assigned (xmllnd) then begin
                  res := 11;
                  ErrorText := 'API error message';
                end else begin
                  xmlstr := xmllnd.TagValue;

                  StrToFile (DatenPath + 'soap\VLOG\'+FormatDateTime ('yyyymmdd', Now)+'\parcels_resp_'+nve+'.xml', xmlstr);

                  sdata.Clear;
                  sdata.WriteBuffer(Pointer(xmlstr)^, Length(xmlstr));

                  sdata.Position := 0;
                  res := xmlresp.ParseXMLFile (sdata);

                  xmllnd := xmlresp.FindTag ('shipment_status');

                  if not Assigned (xmllnd) then begin
                    res := 11;
                    ErrorText := 'API error message';
                  end else begin
                    xmllnd := xmlresp.FindTag ('shipment_fehlertext');
                    if Assigned (xmllnd) and (Length (xmllnd.TagValue) > 0) then begin
                      ErrorText := xmllnd.TagValue;
                    end;

                    xmllnd := xmlresp.FindTag ('package_fehlertext');
                    if Assigned (xmllnd) and (Length (xmllnd.TagValue) > 0)  then begin
                      ErrorText := xmllnd.TagValue;
                    end;

                    if (Length (ErrorText) = 0) then begin
                      xmllnd := xmlresp.FindTag ('send_id');
                      if Assigned (xmllnd) then
                        SendungsNr := xmllnd.TagValue;

                      xmllnd := xmlresp.FindTag ('send_referenznr_06');
                      if Assigned (xmllnd) then
                        Barcode := xmllnd.TagValue;
                    end;
                  end;
                end;
              except
                res := 10;

                ErrorText := 'Label data error';
              end;
            //end;
          end else begin
            res := 11;
            ErrorText := 'API error message'+#13+errtext;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: VLOG, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

  //******************************************************************************
  //* Function Name: CreateSevenSendersLabel
  //* Author       : Stefan Graf
  //* Datum        : 03.08.2020
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CreateSevenSendersLabel (const Art, Versender : String; const WarenWert : Double; var ErrorText : String) : Integer;
  var
    idx,
    res,
    keyidx,
    strpos,
    suchofs,
    errcode  : Integer;
    errtext  : String;
    xmlstr   : String;
    urlparam : String;
    body     : String;
    resp     : String;
    hdr      : String;
    nve      : String;
    labelurl : String;
    nrstr    : String;
    orderid  : String;
    timestr  : String;
    valstr   : String;
    keystr,
    streetstr,
    vorstr,
    nachstr  : String;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    js       : TlkJSONobject;
    fs,
    us,
    errfs     : TlkJSONbase;
    cfgquery,
    spedquery : TSmartQuery;
    olddec    : Char;
    found     : boolean;
    urlstr    : String;
    urlport   : Integer;
    product,
    service,
    serviceid : String;

    {$ifdef UNICODE}
      utfstr       : AnsiString;
      datastr      : String;
    {$endif}
  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      ErrorText := '';

      urlstr  := '';
      urlport := -1;

      if not Assigned (cfgquery.FindField('API_KEY')) then
        ErrorText := 'Not perpared for api key'
      else if cfgquery.FieldByName('API_KEY').IsNull then
        ErrorText := 'No api key'
      else begin
        product := '';
        service := 'standard';
        serviceid := '';

        keystr := cfgquery.FieldByName('API_KEY').AsString;

        if Assigned (cfgquery.FindField ('PRODUCT_PARAMETER')) then begin
          strlist := TStringList.Create;

          try
            strlist.Delimiter := ';';
            strlist.StrictDelimiter := true;

            strlist.DelimitedText := cfgquery.FieldByName('PRODUCT_PARAMETER').AsString;

            if (strlist.Count > 0) then
              product := strlist [0];

            if (strlist.Count > 1) then
              service   := strlist [1];

            if (strlist.Count > 2) then
              serviceid := strlist [2];
          finally
            strlist.Free;
          end;
        end else begin
          product := cfgquery.FieldByName('SENDIT_PRODUKT').AsString;
        end;

        if Assigned (cfgquery.FindField ('REST_URL')) then
          urlstr  := cfgquery.FieldByName('REST_URL').AsString;

        if (Length (urlstr) = 0) then
          urlstr  := cfgquery.FieldByName('SPED_REST_URL').AsString;

        if (Length (urlstr) = 0) then
          ErrorText := 'Not REST url defined'
        else if (Length (product) = 0) then
          ErrorText := 'Not REST product defined'
        else begin
          if (lowercase (copy (urlstr, 1, 7)) = 'http://') then
            suchofs := 8
          else if (lowercase (copy (urlstr, 1, 8)) = 'https://') then
            suchofs := 9
          else
            suchofs := 1;

          strpos := Pos (':', urlstr, suchofs);

          if (strpos = 0) then
            urlport := -1
          else if not (TryStrToInt (copy (urlstr, strpos + 1), urlport)) then
            ErrorText := 'Port definition invalide'
          else begin
            urlstr := copy (urlstr, 1, strpos - 1);
          end;
        end;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        sdata := TMemoryStream.Create;

        try
          keyidx := 0;

          while (keyidx < 32) do begin
            if (fRESTToken [keyidx].AccessKey = keystr) then
              break
            else if (Length (fRESTToken [keyidx].AccessKey) = 0) then begin
              fRESTToken [keyidx].AccessKey := keystr;
              fRESTToken [keyidx].AccessToken := '';

              break;
            end;

            Inc (keyidx);
          end;

          ForceDirectories(DatenPath + RESTDumpDir + '7Sendres\'+FormatDateTime ('yyyymmdd', Now));

          if (Length (fRESTToken [keyidx].AccessToken) = 0) or (MinutesBetween (fRESTToken [keyidx].CreateAt, Now) > 50) then begin
            //Neues Token erzeugen
            urlparam := '';

            body := '{"access_key": "'+fRESTToken [keyidx].AccessKey+'"}';

            if SendRequest (urlstr, // Host,
                            -1, //Port
                            'v2/token', // Service
                            'POST', //Methode
                            '', // Proxy,
                            '', '', // User , PW
                            '', //Action
                            'application/json', //ContentType
                            [],
                            body,         // RequestData
                            resp,
                            sdata, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
                          then
            begin
              StrToFile (DatenPath + RESTDumpDir+'7Sendres\'+FormatDateTime ('yyyymmdd', Now)+'\token_'+FormatDateTime ('hhmmss', Now)+'.txt', resp);

              sdata.Position := 0;
              sdata.SaveToFile(DatenPath + RESTDumpDir+'7Sendres\'+FormatDateTime ('yyyymmdd', Now)+'\token_'+FormatDateTime ('hhmmss', Now)+'.xml');

              {$ifdef UNICODE}
                SetLength(utfstr, sdata.Size);
                sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                datastr := StringUtils.StringToUTF (utfstr);

                js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
              {$else}
                sdata.Position := 0;
                js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
              {$endif}

              if not Assigned (js) then begin
                ErrorText := 'API error message (token)';
              end else begin
                try
                  fs := js.Field['token'];
                except
                  fs := nil;
                end;

                if not Assigned (fs) then
                  ErrorText := 'Fehler token'
                else begin
                  fRESTToken [keyidx].AccessToken := fs.Value;
                  fRESTToken [keyidx].CreateAt := Now;
                end;
              end;
            end else begin
              ErrorText := 'Fehler token';
            end;
          end;

          if (Length (fRESTToken [keyidx].AccessToken) > 0) then begin
            hdr := 'Authorization: Bearer '+fRESTToken [keyidx].AccessToken;

            nve := query.FieldByName('NVE_NR').AsString;

            (*
            if (query.FieldByName('COUNT_FREIGABE').AsInteger > 1) then
              orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('COUNT_FREIGABE').AsString
            else if (query.FieldByName('PACKAGE_NR').IsNull or (query.FieldByName('PACKAGE_NR').AsInteger = 1)) then
              orderid := query.FieldByName('AUFTRAG_NR').AsString
            else
              orderid := query.FieldByName('AUFTRAG_NR').AsString+'-'+query.FieldByName('PACKAGE_NR').AsString;
            *)

            orderid := query.FieldByName('AUFTRAG_NR').AsString;

            if Assigned (SendITLog) then begin
              SendITLog.Logging (clNormal, 'Versand: 7Senders, RefNVE: '+IntToStr (query.FieldByName('REF_NVE').AsInteger)+', OrderID: '+orderid+', NVE: '+nve+CR+LF);
            end;

            DateTimeToString(timestr, 'yyyy-mm-dd hh:nn:ss', Now);
            timestr [11] := 'T';
            timestr := timestr + '.000Z';

            found := false;

            body := '';

            //urlparam := '/'+nve;
            urlparam := '/'+orderid;

            sdata.Clear;
            if SendRequest (urlstr, // Host,
                            -1, //Port
                            'v2/orders'+urlparam, // Service
                            'GET', //Methode
                            '', // Proxy,
                            '', '', // User , PW
                            '', //Action
                            'application/json', //ContentType
                            [hdr], //AddHeader
                            body,         // RequestData
                            resp,
                            sdata, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
                          then
            begin
              sdata.Position := 0;
              sdata.SaveToFile(DatenPath + RESTDumpDir+'7Sendres\'+FormatDateTime ('yyyymmdd', Now)+'\get_orders_'+nve+'.json');

              {$ifdef UNICODE}
                SetLength(utfstr, sdata.Size);
                sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                datastr := StringUtils.StringToUTF (utfstr);

                js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
              {$else}
                sdata.Position := 0;
                js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
              {$endif}

              if not Assigned (js) then begin
                ErrorText := 'API error message';
              end else begin
                try
                  fs := js.Field['order_id'];

                  if Assigned (fs) then begin
                    found := true;
                  end;
                except
                end;
              end;
            end;

            spedquery  := TSmartQuery.Create (Nil);

            try
              spedquery.ReadOnly := True;
              spedquery.Session := Query.Session;

              spedquery.SQL.Add ('select cfg.* from V_SPED_CONFIG cfg where cfg.REF_SPED=:ref');
              spedquery.Params [0].Value := query.FieldByName('REF_SPED').AsInteger;

              spedquery.Open;

              if not (found) then begin
                body := '';
                urlparam := '';

                body := '{'+
                        '    "order_id": "'+orderid+'",'+
                        '    "order_url": "",'+
                        '    "order_date": "'+timestr+'",'+
                        '    "boarding_complete": true,'+
                        '    "state": "new",'+
                        '    "language": "de"'+
                        '}';

                sdata.Clear;
                if SendRequest(urlstr, // Host,
                                -1, //Port
                                'v2/orders', // Service
                                'POST', //Methode
                                '', // Proxy,
                                '', '', // User , PW
                                '', //Action
                                'application/json', //ContentType
                                [hdr], //AddHeader
                                body,         // RequestData
                                resp,
                                sdata, //ResponseStream
                                errcode, // Fehlercode
                                errtext) // Fehlertext
                              then
                begin
                  sdata.Position := 0;
                  sdata.SaveToFile(DatenPath + RESTDumpDir+'7Sendres\'+FormatDateTime ('yyyymmdd', Now)+'\post_orders_'+nve+'.json');
                end else begin
                  res := 21;
                  ErrorText := 'Fehler orders';
                end;
              end;

              if (res = 0) then begin
                urlparam := '';

                body := '{'+
                          '  "warehouse": "'+query.FieldByName('SENDIT_LOCATION').AsString+'",'+
                          '  "format": "zpl",'+
                          '  "reference_number": "'+ nve +'",'+
                          '  "package_no": '+query.FieldByName('PACKAGE_NR').AsString+',';

                //Absender Adresse übergeben
                cfgquery  := TSmartQuery.Create (Nil);

                try
                  cfgquery.ReadOnly := True;
                  cfgquery.Session := Query.Session;

                  cfgquery.SQL.Add ('select'
                                   +' to_char (trunc (auf.VERSAND_DATUM) + (nvl (sped.CUT_OFF_TIME, 12*60)/86400), ''yyyy-mm-dd"T"HH24:MI:SS'')||SESSIONTIMEZONE as CUT_OFF_TIME_UCT'
                                   +',to_char (trunc (auf.VERSAND_DATUM) + 4 + (nvl (sped.CUT_OFF_TIME, 12*60)/86400), ''yyyy-mm-dd"T"HH24:MI:SS'')||SESSIONTIMEZONE as DELIVERY_DATE_UCT'
                                   +' from V_SPEDITIONEN sped, VQ_AUFTRAG auf where sped.REF=:ref_sped and auf.REF=:ref_auf');
                  cfgquery.Params.ParamByName ('ref_sped').Value := query.FieldByName('REF_SPED').AsInteger;
                  cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

                  cfgquery.Open;

                  body := body + '  "planned_pickup_datetime":"'+cfgquery.FieldByName('CUT_OFF_TIME_UCT').AsString+'",';

                  body := body + '"order": {';

                  body := body + '  "order_id":"'+orderid+'",';

                  body := body + '  "promised_delivery_date":"'+cfgquery.FieldByName('DELIVERY_DATE_UCT').AsString+'"';

                  body := body + '},';

                  cfgquery.Close;
                finally
                  cfgquery.Free;
                end;

                body := body + '  "sender_first_name": "",'+
                               '  "sender_last_name": "",';

                //Absender Adresse übergeben
                cfgquery  := TSmartQuery.Create (Nil);

                try
                  cfgquery.ReadOnly := True;
                  cfgquery.Session := Query.Session;

                  //Bei Dropship wird die Traderadresse angezeigt
                  cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                                     +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                                     +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                                     +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                                     +'coalesce (translate (tadr.LAND_ISO using CHAR_CS),loc.LAND,''DE'') as LAND,'
                                     +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON,'
                                     +'coalesce (tadr.EMAIL,loc.MAIL) as EMAIL'
                                    +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                                    +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                                    +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                                    +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                    +' where auf.REF=:ref_auf'
                                    );

                  cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

                  cfgquery.Open;

                  if not (cfgquery.FieldByName('REF').IsNull) then begin
                    body := body + '  "sender_company_name": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('NAME1').AsString)+'",';
                    body := body + '  "sender_street": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('STRASSE').AsString)+'",';
                    body := body + '  "sender_house_no" :"",';
                    body := body + '  "sender_zip": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('PLZ').AsString)+'",';
                    body := body + '  "sender_city": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('ORT').AsString)+'",';
                    body := body + '  "sender_country": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('LAND').AsString)+'",';
                    body := body + '  "sender_phone": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('TELEFON').AsString)+'",';
                    body := body + '  "sender_email": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('EMAIL').AsString)+'",';
                  end else begin
                    //Ansonsten die vom Lager
                    cfgquery.Close;

                    cfgquery.SQL.Clear;

                    cfgquery.SQL.Add ( 'select '
                                      +'coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as NAME1,'
                                      +'ml.ABSENDER_ZUSATZ,'
                                      +'nvl (ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                                      +'nvl (ml.ABSENDER_PLZ,loc.PLZ) as PLZ,'
                                      +'nvl (ml.ABSENDER_ORT,loc.ORT) as ORT,'
                                      +'nvl (loc.LAND,''DE'') as LAND,'
                                      +'loc.TELEFON,'
                                      +'loc.MAIL'
                                      +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                      +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                      +' where m.REF=:ref_mand'
                                      );

                    cfgquery.Params.ParamByName('ref_loc').Value := query.FieldByName('REF_LOCATION').AsInteger;

                    if query.FieldByName('REF_SUB_MAND').IsNull then
                      cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
                    else cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

                    cfgquery.Open;

                    body := body + '  "sender_company_name": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('NAME1').AsString)+'",';
                    body := body + '  "sender_street": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('STRASSE').AsString)+'",';
                    body := body + '  "sender_house_no" :"",';
                    body := body + '  "sender_zip": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('PLZ').AsString)+'",';
                    body := body + '  "sender_city": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('ORT').AsString)+'",';
                    body := body + '  "sender_country": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('LAND').AsString)+'",';
                    body := body + '  "sender_phone": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('TELEFON').AsString)+'",';
                    body := body + '  "sender_email": "'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('MAIL').AsString)+'",';
                  end;

                  cfgquery.Close;
                finally
                  cfgquery.Free;
                end;

                if (query.FieldByName('OPT_NACHNAHME').AsString = '0') then
                  body := body + '  "cod": false'
                else begin
                  body := body + '  "cod": true,'+
                                 '  "cod_value": '+FormatFloat ('0.00', WarenWert);

                  if not (query.FieldByName ('CURRENCY').IsNull) then
                    body := body + ',"cod_currency"'+':'+'"'+query.FieldByName ('CURRENCY').AsString+'"'
                  else
                    body := body + ',"cod_currency"'+':'+'"'+'EUR'+'"';
                end;

                body := body + '  ,"goods_value": '+FormatFloat ('0.00', WarenWert);

                if not (query.FieldByName ('CURRENCY').IsNull) then
                  body := body + ',"goods_value_currency"'+':'+'"'+query.FieldByName ('CURRENCY').AsString+'"'
                else
                  body := body + ',"goods_value_currency"'+':'+'"'+'EUR'+'"';

                body := body + '  ,"carrier": {'+
                               '    "name": "'+product+'",'+
                               '    "country": "'+landstr+'"'+
                               '  }'+
                               '  ,"carrier_service": "'+service+'"';

                if (Length (serviceid) > 0) then
                  body := body + '  ,"carrier_service_id":"'+serviceid+'"';

                cfgquery  := TSmartQuery.Create (Nil);

                try
                  cfgquery.ReadOnly := True;
                  cfgquery.Session := Query.Session;

                  //Die Zollinfos der Artikel
                  cfgquery.SQL.Clear;
                  //Die Zollinfos der Artikel
                  cfgquery.SQL.Clear;
                  if LVSDatenModul.ViewExits ('V_CUSTOM_AUFTRAG_POS') then begin
                    cfgquery.SQL.Add('select * from V_CUSTOM_AUFTRAG_POS where REF_NVE=:ref');
                  end else begin
                    //Alle Bestände auf der NVE, welche keine Textartikel sind, das Gewicht ist das Artikelgewicht
                    cfgquery.SQL.Add('select'
                                    +' mcfg.OPT_AUTO_CUSTOMS'
                                    +',ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN'') as EXPORT_DESCRIPTION'
                                    +',bes.MENGE'
                                    +',vpe.KURZ_BEZEICHNUNG as EINHEIT'
                                    +',ar.COUNTRY_OF_ORIGIN'
                                    +',nvl (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER) as TARIC_NUMBER'
                                    +',nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT)/1000 as BRUTTO_GEWICHT_KG'
                                    +',nvl (rep.NETTO_BETRAG,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT) as NETTO_BETRAG'
                                    +',nvl (arset.ANZAHL_VPE, 1) as ANZAHL_VPE'
                                    +',pos.REF_AR as POS_REF_AR, ae.REF_AR'
                                    +' from'
                                    +'   V_LAGER_NVE_BESTAND bes'
                                    +'   inner join V_AUFTRAG_POS pos on (pos.REF=bes.REF_AUF_POS)'
                                    +'   inner join VQ_AUFTRAG auf on (auf.REF=pos.REF_AUF_KOPF)'
                                    +'   inner join V_AUFTRAG_RECHNUNG re on (re.REF_AUF_KOPF=auf.REF)'
                                    +'   inner join V_AUFTRAG_POS_RECHNUNG rep on (rep.REF_AUF_POS=pos.REF)'
                                    +'   inner join V_ARTIKEL posar on (posar.REF=pos.REF_AR)'
                                    +'   inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=bes.REF_AR_EINHEIT)'
                                    +'   inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'
                                    +'   inner join V_ARTIKEL_VPE vpe on (vpe.REF=ae.REF_EINHEIT)'
                                    +'   inner join V_MANDANT_CONFIG mcfg on (mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND))'
                                    +'   left outer join V_ARTIKEL_SET arset on (arset.REF=posar.REF_ARTIKEL_SET)'
                                    +' where'
                                    +'   nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0'
                                    +'   and bes.REF_NVE=:ref'
                                    );
                  end;
                  cfgquery.Params [0].Value := query.FieldByName('REF_MAIN_NVE').AsInteger;

                  cfgquery.Open;

                  idx := 0;

                  body := body + ',"parcel":{';
                  body := body + '"description":"'+ConvertJSONSonderzeichen (cfgquery.FieldByName ('EXPORT_DESCRIPTION').AsString)+'"';
                  body := body + ',"sku":[';

                  while not (cfgquery.Eof) and (res = 0) do begin
                    Inc (idx);

                    if (cfgquery.FieldByName ('OPT_AUTO_CUSTOMS').AsString > '0') then begin
                      if (cfgquery.FieldByName ('TARIC_NUMBER').IsNull) then begin
                        res := 56;

                        if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13;

                        {$ifdef ResourceText}
                          ErrorText := ErrorText + FormatMessageText (1688, [cfgquery.FieldByName ('ARTIKEL_NR').AsString]);
                        {$else}
                          ErrorText := ErrorText + Format ('Export nicht möglich, fehlende Zolltarifnummer für Artikel %s', [cfgquery.Fields [1].AsString]);
                        {$endif}
                      end;

                      if (cfgquery.FieldByName ('NETTO_BETRAG').IsNull) then begin
                        res := 57;

                        if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13;
                        {$ifdef ResourceText}
                          ErrorText := ErrorText + FormatMessageText (1689, [cfgquery.FieldByName ('ARTIKEL_NR').AsString]);
                        {$else}
                          ErrorText := ErrorText + Format ('Export nicht möglich, fehlende Preis für Artikel %s', [cfgquery.Fields [1].AsString]);
                        {$endif}
                      end;
                    end;

                    if (res = 0) then begin
                      if (idx > 1) then
                        body := body + ',{'
                      else
                        body := body + '{';

                      body := body + '"article_id"'+':'+'"'+ConvertJSONSonderzeichen (copy (cfgquery.FieldByName ('ARTIKEL_NR').AsString, 1, 32))+'"';
                      body := body + ',"arrival_country_description"'+':'+'"'+ConvertJSONSonderzeichen (copy (cfgquery.FieldByName ('EXPORT_DESCRIPTION').AsString, 1, 32))+'"';

                      //Nur ausgeben, wenn die Zollabwicklung aktiv ist
                      if (cfgquery.FieldByName ('OPT_AUTO_CUSTOMS').AsString > '0') then begin
                         //PriceX, Betrag ist min. 10 Cent
                        if (cfgquery.Fields [8].IsNull or (cfgquery.FieldByName ('NETTO_BETRAG').AsInteger < 10)) then
                          valstr := Format('%6.3f', [0.1])
                        else begin
                          if (cfgquery.FieldByName ('ANZAHL_VPE').AsInteger > 1) and (cfgquery.FieldByName ('REF_AR').AsInteger <> cfgquery.FieldByName ('POS_REF_AR').AsInteger) then
                            valstr := Format('%6.3f', [cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000 / cfgquery.FieldByName ('ANZAHL_VPE').AsInteger])
                          else
                            valstr := Format('%6.3f', [cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000]);
                        end;
                        body := body + ',"sales_value_total"'+':'+valstr;

                        if not (query.FieldByName ('CURRENCY').IsNull) then
                          body := body + ',"currency"'+':'+'"'+query.FieldByName ('CURRENCY').AsString+'"'
                        else
                          body := body + ',"currency"'+':'+'"'+'EUR'+'"';

                        if (cfgquery.FieldByName ('TARIC_NUMBER').IsNull) then
                          valstr := ''
                        else
                          valstr := StringReplace (copy (cfgquery.FieldByName ('TARIC_NUMBER').AsString, 1, 32), ';', ',', [rfReplaceAll]);
                        body := body + ',"arrival_country_tariff_number"'+':'+'"'+valstr+'"';
                      end;

                      body := body + ',"qty"'+':'+copy (cfgquery.FieldByName ('MENGE').AsString, 1, 32);

                      //WeightX, Gewicht ist min. 10 Gramm
                      if (cfgquery.FieldByName ('BRUTTO_GEWICHT_KG').IsNull or (cfgquery.FieldByName ('BRUTTO_GEWICHT_KG').AsFloat < 0.01)) then
                        valstr := '0.01'
                      else
                        valstr := cfgquery.FieldByName ('BRUTTO_GEWICHT_KG').AsString;

                      body := body + ',"total_net_weight"'+':'+valstr;

                      //OriginCountryX
                      if (cfgquery.FieldByName ('COUNTRY_OF_ORIGIN').IsNull) then
                        valstr := 'DE'
                      else
                        valstr := StringReplace (copy (cfgquery.FieldByName ('COUNTRY_OF_ORIGIN').AsString, 1, 32), ';', ',', [rfReplaceAll]);
                      body := body + ',"country_of_origin"'+':'+'"'+valstr+'"';

                      body := body + '}';
                    end;

                    cfgquery.Next;
                  end;

                  body := body + ']';
                  body := body + '},';

                  cfgquery.Close;
                finally
                  cfgquery.Free;
                end;

                nrstr  := '';
                streetstr := liefquery.FieldByName ('STRASSE').AsString;

                idx := Length (streetstr);
                while (idx > 1) and (streetstr [idx] <> ' ') do
                  Dec (idx);

                if (idx > 1) then begin
                  nrstr := copy (streetstr, idx + 1);
                  streetstr := Copy (streetstr, 1, idx);
                end;

                ExtractVorUndNachname (vorstr, nachstr);

                body := body + '  "recipient_first_name": "'+ConvertJSONSonderzeichen (StringReplace (vorstr, ';', ',', [rfReplaceAll]))+'",'+
                               '  "recipient_last_name": "'+ConvertJSONSonderzeichen (StringReplace (nachstr, ';', ',', [rfReplaceAll]))+'",';

                if (liefquery.FieldByName('COMPANY').IsNull) then
                  body := body + '  "recipient_company_name": "",'
                else
                  body := body + '  "recipient_company_name": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('COMPANY').AsString, ';', ',', [rfReplaceAll]))+'",';

                if not (kepemail) or liefquery.FieldByName('EMAIL').IsNull then
                  body := body + '  "recipient_email": "'+ConvertJSONSonderzeichen (StringReplace (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString, ';', ',', [rfReplaceAll]))+'",'
                else
                  body := body + '  "recipient_email": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll]))+'",';

                body := body + '  "recipient_street": "'+ConvertJSONSonderzeichen (StringReplace (streetstr, ';', ',', [rfReplaceAll]))+'",'+
                               '  "recipient_house_no": "'+ConvertJSONSonderzeichen (nrstr)+'",'+
                               '  "recipient_zip": "'+liefquery.FieldByName('PLZ').AsString+'",'+
                               '  "recipient_city": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('ORT').AsString, ';', ',', [rfReplaceAll]))+'",'+
                               '  "recipient_country": "'+landstr+'",'+
                               '  "recipient_phone": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll]))+'",';

                if (liefquery.FieldByName('COMPANY').IsNull) and not liefquery.FieldByName('NAME2').IsNull then
                  body := body + '  "recipient_address_details": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('NAME2').AsString, ';', ',', [rfReplaceAll]))+'",'
                else if (not liefquery.FieldByName('COMPANY').IsNull) and not liefquery.FieldByName('NAMEZUSATZ').IsNull then
                  body := body + '  "recipient_address_details": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('NAMEZUSATZ').AsString, ';', ',', [rfReplaceAll]))+'",'
                else if not liefquery.FieldByName('STRASSE_2').IsNull then
                  body := body + '  "recipient_address_details": "'+ConvertJSONSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]))+'",';

                body := body + '  "return_parcel": "outbound"'+
                               '  ,"weight": '+FormatFloat ('0.0', query.FieldByName('BRUTTO_GEWICHT').AsFloat);

                if not (Assigned (spedquery.FindField ('OPT_DIM_SEND'))) or (spedquery.FieldByName ('OPT_DIM_SEND').AsString = '1') then begin
                  body := body + '  ,"dimensions": {'
                               +'       "height": '+IntToStr (query.FieldByName('H').AsInteger div 10)+','
                               +'       "length": '+IntToStr (query.FieldByName('L').AsInteger div 10)+','
                               +'       "width": '+IntToStr (query.FieldByName('B').AsInteger div 10)
                               +'  }';
                end;

                body := body + '}';

                StrToFile (DatenPath + RESTDumpDir+'7Sendres\'+FormatDateTime ('yyyymmdd', Now)+'\post_labels_'+nve+'.json', body);

                sdata.Clear;
                if SendRequest(urlstr, // Host,
                                -1, //Port
                                'v2/labels', // Service
                                'POST', //Methode
                                '', // Proxy,
                                '', '', // User , PW
                                '', //Action
                                'application/json', //ContentType
                                [hdr], //AddHeader
                                body,         // RequestData
                                resp,
                                sdata, //ResponseStream
                                errcode, // Fehlercode
                                errtext) // Fehlertext
                              then
                begin
                  labelurl := '';

                  sdata.Position := 0;
                  sdata.SaveToFile(DatenPath + RESTDumpDir+'7Sendres\'+FormatDateTime ('yyyymmdd', Now)+'\tracking_'+nve+'.json');

                  {$ifdef UNICODE}
                    SetLength(utfstr, sdata.Size);
                    sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                    datastr := StringUtils.StringToUTF (utfstr);

                    js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
                  {$else}
                    sdata.Position := 0;
                    js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
                  {$endif}

                  if not Assigned (js) then begin
                    ErrorText := 'API error message';
                  end else begin
                    try
                      fs := js.Field['tracking_code'];

                      if not Assigned (fs) then begin
                        ErrorText := 'tracking code error';

                        fs := js.Field['detail'];
                        if Assigned (fs) then
                          ErrorText := ErrorText + ':' + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll])
                        else begin
                          fs := js.Field['details'];
                          if Assigned (fs) then
                            ErrorText := ErrorText + ':' + StringReplace (fs.Value, '\u0027', '''', [rfReplaceAll]);
                        end;
                      end else begin
                        SendungsNr := fs.Value;

                        us := js.Field['outbound'];

                        if not Assigned (us) then
                          ErrorText := 'outbound error'
                        else begin
                          fs := us.Field['label_url'];

                          if not Assigned (fs) then
                            ErrorText := 'outbound error'
                          else begin
                            labelurl := fs.Value;

                            labelurl := StringReplace (labelurl, '\/', '/', [rfReplaceAll]);

                            fs := us.Field['shipment_id'];

                            if Assigned (fs) then
                              SendungsID := fs.Value;
                          end;
                        end;
                      end;
                    except
                      ErrorText := 'Label data error';
                    end;
                  end;

                  if (Length (labelurl) = 0) then
                    res := 24
                  else begin
                    body := '';
                    sdata.Clear;

                    idx := Length (labelurl);
                    while (idx > 0) and (labelurl [idx] <> '/') do
                      Dec (idx);

                    nrstr := Copy (labelurl, idx + 1);

                    SendungsID := nrstr;

                    if SendRequest(urlstr, // Host,
                                    -1, //Port
                                    '/v2/label-download/'+nrstr, // Service
                                    'GET', //Methode
                                    '', // Proxy,
                                    '', '', // User , PW
                                    '', //Action
                                    'application/json', //ContentType
                                    [hdr], //AddHeader
                                    body,         // RequestData
                                    resp,
                                    LabelImage, //ResponseStream
                                    errcode, // Fehlercode
                                    errtext) // Fehlertext
                    then begin
                      LabelFormat := 'zpl';

                      LabelImage.Position := 0;

                      try
                        ForceDirectories     (DatenPath + RESTDumpDir + '7Sendres\Label\'+FormatDateTime ('yyyymmdd', Now));
                        LabelImage.SaveToFile(DatenPath + RESTDumpDir + '7Senders\Label\'+FormatDateTime ('yyyymmdd', Now) + '\'+ Versender+'_'+SendungsNr + '.zpl');
                      except
                      end;

                      if Assigned (SendITLog) then begin
                        SendITLog.Logging (clNormal, 'Versand: 7Senders, RefNVE: '+IntToStr (query.FieldByName('REF_NVE').AsInteger)+', Versender: '+Versender+', SendungsNr: '+SendungsNr);
                      end;
                    end else begin
                      res := 25;

                      ErrorText := 'Download error '+IntToStr (errcode)+' ('+errtext;
                    end;
                  end;
                end else begin
                  res := 21;
                  ErrorText := 'Fehler labels';
                end;
              end;

              spedquery.Close;
            finally
              spedquery.Free;
            end;
          end;
        finally
          sdata.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: 7Senders, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

var
  fname,
  versstr,
  csvstr,
  artstr,
  errstr,
  prodstr,
  cfgstr,
  errtxt,
  absender,
  lblname      : String;
  kopie,
  strpos,
  refadr,
  refsend      : Integer;
  codofs,
  warenwert    : Double;
  fstream      : TFileStream;
  lblref       : Integer;
  odacquery    : TSmartQuery;
  timeout      : TTimeout;
begin
  res := 0;
  ErrorText := '';
  VersandApp := '';
  RefLabel := -1;

  {$ifdef Trace}
    FunctionStart ('StartPrintVersandLabel');
    TraceParameter ('Port     ', PrtInfo.Port);
    TraceParameter ('Leitstand', PrtInfo.Leitstand);
  {$endif}

  Versender := query.FieldByName('DFUE_KENNZEICHEN').AsString;

  adrquery  := TSmartQuery.Create (Nil);
  liefquery := TSmartQuery.Create (Nil);
  dataquery := TSmartQuery.Create (Nil);
  gatequery := TSmartQuery.Create (Nil);

  try
    adrquery.ReadOnly := True;
    adrquery.Session := Query.Session;

    liefquery.ReadOnly := True;
    liefquery.Session := Query.Session;

    dataquery.ReadOnly := True;
    dataquery.Session := Query.Session;

    try
      warenwert := 0;

      if (query.FieldByName('REF_AUF_KOPF').AsInteger > 0) then begin
        codofs := 0;

        if (query.FieldByName('OPT_NACHNAHME').AsString = '0') then begin
          //Warenwert anhand der Mengen im Paket bestimmen, die Sicht gibt es nicht in allen Projekten, daher über eine Excpetion abgefangen
          dataquery.SQL.Clear;
          dataquery.SQL.Add ('select'
                            +'    sum ((rp.BRUTTO_BETRAG * bes.MENGE) / nvl (arset.ANZAHL_VPE,1))'
                            +'  from'
                            +'    V_LAGER_NVE_BESTAND bes'
                            +'    inner join V_AUFTRAG_POS ap on (ap.REF=bes.REF_AUF_POS)'
                            +'    inner join V_AUFTRAG_POS_RECHNUNG rp on (rp.REF_AUF_POS=ap.REF)'
                            +'    inner join VQ_ARTIKEL ar on (ar.REF=ap.REF_AR)'
                            +'    left outer join V_ARTIKEL_SET arset on (arset.REF=ar.REF_ARTIKEL_SET)'
                            +'  where'
                            +'    ap.REF=bes.REF_AUF_POS and'
                            +'    bes.REF_NVE=:ref_nve'
                            );
          dataquery.Params.ParamByName('ref_nve').Value := query.FieldByName('REF_MAIN_NVE').AsInteger;

          try
            dataquery.Open;

            warenwert := dataquery.Fields [0].AsInteger / 1000;

            dataquery.Close;

            if (warenwert <= 0) then
              warenwert := query.FieldByName('BRUTTO_BETRAG').AsInteger / 1000;
          except
            warenwert := -1;
          end;
        end else if (query.FieldByName('OPT_NACHNAHME').AsString = '1') then begin
          //Nachnahme pro Paket bestimmen
          //Betrag anhand der Mengen im Paket bestimmen, die Sicht gibt es nicht in allen Projekten, daher über eine Excpetion abgefangen
          dataquery.SQL.Clear;
          dataquery.SQL.Add ('select'
                            +'    sum ((rp.BRUTTO_BETRAG * bes.MENGE) / nvl (arset.ANZAHL_VPE,1))'
                            +'  from'
                            +'    V_LAGER_NVE_BESTAND bes'
                            +'    inner join V_AUFTRAG_POS ap on (ap.REF=bes.REF_AUF_POS)'
                            +'    inner join V_AUFTRAG_POS_RECHNUNG rp on (rp.REF_AUF_POS=ap.REF)'
                            +'    inner join VQ_ARTIKEL ar on (ar.REF=ap.REF_AR)'
                            +'    left outer join V_ARTIKEL_SET arset on (arset.REF=ar.REF_ARTIKEL_SET)'
                            +'  where'
                            +'    ap.REF=bes.REF_AUF_POS and'
                            +'    bes.REF_NVE=:ref_nve'
                            );
          dataquery.Params.ParamByName('ref_nve').Value := query.FieldByName('REF_MAIN_NVE').AsInteger;

          try
            dataquery.Open;

            warenwert := dataquery.Fields [0].AsInteger / 1000;

            dataquery.Close;
          except
            warenwert := -1;
          end;

          if (warenwert <= 0) then begin
            //Warenwerte an Hand des Infoelementes WARENWERT oder der Rechnung bestimmen
            dataquery.SQL.Clear;
            dataquery.SQL.Add ('select INFO_WERT from V_AUFTRAG_INFO where INFO_ART=''WARENWERT'' and REF_AUF_KOPF=:RefAuf');
            dataquery.Params.ParamByName('RefAuf').Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

            dataquery.Open;

            if not (dataquery.Fields [0].IsNull) then
              warenwert := dataquery.Fields [0].AsInteger / 1000
            else begin
              dataquery.Close;

              dataquery.SQL.Clear;
              dataquery.SQL.Add ('select nvl (BRUTTO_BETRAG_CALC, BRUTTO_BETRAG) from V_AUFTRAG_RECHNUNG where REF_AUF_KOPF=:RefAuf');
              dataquery.Params.ParamByName('RefAuf').Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

              dataquery.Open;

              if not (dataquery.Fields [0].IsNull) then
                warenwert := dataquery.Fields [0].AsInteger / 1000;
            end;

            dataquery.Close;
          end;

          if (warenwert <= 0) then begin
            res := -41;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText (1778, []);
            {$else}
              ErrorText := 'Es konnten kein Rechnungsbetrag für die Nachnahme ermittelt werden';
            {$endif}
          end else begin
            //Bei Nachnahme prüfen, ob für diesen Mandanten die Gebühren bereits im Bruttopreis enthalten sind
            dataquery.SQL.Clear;
            dataquery.SQL.Add ('select * from V_SPED_GATEWAY where REF=:ref_gate');
            dataquery.Params.ParamByName('ref_gate').Value := query.FieldByName ('REF_SPED_GATEWAY').AsInteger;

            dataquery.Open;

            if Assigned (dataquery.FindField('CONTAINDE_COD_AMOUNT')) then begin
              if not (dataquery.FieldByName ('CONTAINDE_COD_AMOUNT').IsNull) then begin
                //Wenn ja, muss der Bruttopreis für die Einzahlung reduziert werden
                warenwert := warenwert - dataquery.FieldByName ('CONTAINDE_COD_AMOUNT').AsInteger / 1000;
              end;
            end;

            if Assigned (dataquery.FindField('CALC_COD_AMOUNT')) then begin
              cfgstr := dataquery.FieldByName ('CALC_COD_AMOUNT').AsString;

              //Soll die Nachnnahmegebühren errechnet werden?
              if (cfgstr > '0') then begin
                //Wenn ja, aus der Summer der Positionspreise und den Gesamtbruttobetragen, die Gebühren bestimmen
                dataquery.SQL.Clear;
                dataquery.SQL.Clear;
                dataquery.SQL.Add ('select'
                                  +'    sum (rp.BRUTTO_BETRAG * ap.MENGE_BESTELLT)'
                                  +'  from'
                                  +'    V_AUFTRAG_POS ap'
                                  +'    inner join V_AUFTRAG_POS_RECHNUNG rp on (rp.REF_AUF_POS=ap.REF)'
                                  +'    inner join VQ_ARTIKEL ar on (ar.REF=ap.REF_AR)'
                                  +'    left outer join V_ARTIKEL_SET arset on (arset.REF=ar.REF_ARTIKEL_SET)'
                                  +'  where'
                                  +'    ap.REF_AUF_KOPF=:ref_auf'
                                  );
                dataquery.Params.ParamByName('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

                try
                  dataquery.Open;

                  codofs := (query.FieldByName('BRUTTO_BETRAG').AsInteger - dataquery.Fields [0].AsInteger) / 1000;

                  dataquery.Close;
                except
                  codofs := -1;
                end;

                //Diese dann zum Paketwert dazu rechnen
                if (codofs > 0) then begin
                  if (cfgstr = '1') then begin
                    //Nur beim ersten Paket
                    if (query.FieldByName ('PACKAGE_NR').AsInteger <= 1) then
                      warenwert := warenwert + codofs;
                  end;
                end;
              end;
            end;

            dataquery.Close;
          end;
        end else if (query.FieldByName ('PACKAGE_NR').AsInteger <= 1) then begin
          //Nachnahme über den Gesamtbetrag beim ersten Packet, alle anderen haben dann keine Nachnahme mehr
          dataquery.SQL.Clear;
          dataquery.SQL.Add ('select nvl (BRUTTO_BETRAG_CALC, BRUTTO_BETRAG) from V_AUFTRAG_RECHNUNG where REF_AUF_KOPF=:RefAuf');
          dataquery.Params.ParamByName('RefAuf').Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

          dataquery.Open;

          if not (dataquery.Fields [0].IsNull) then
            warenwert := dataquery.Fields [0].AsInteger / 1000;

          dataquery.Close;

          if (warenwert <= 0) then begin
            res := -41;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText (1778, []);
            {$else}
              ErrorText := 'Es konnten kein Rechnungsbetrag für die Nachnahme ermittelt werden';
            {$endif}
          end;
        end;
      end;

      if (res = 0) then begin
        if not (query.FieldByName('NOTIFICATION_BY_MAIL').IsNull) then
          kepemail := (query.FieldByName('NOTIFICATION_BY_MAIL').AsString > '0')
        else
          CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'KEP_TRANSFER_EMAIL', kepemail);

        if not (query.FieldByName('ZOLL_DEKLARATION').IsNull) then
          zolldesc := query.FieldByName('ZOLL_DEKLARATION').AsString
        else if Assigned (query.FindField('CARRIER_EXPORT_DESCRIPTION')) and not (query.FieldByName('CARRIER_EXPORT_DESCRIPTION').IsNull) then
          zolldesc := query.FieldByName('CARRIER_EXPORT_DESCRIPTION').AsString
        else
          CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'ZOLL_DEKLARATION', zolldesc);
      end;

      if (res = 0) then begin
        refadr := -1;

        dataquery.SQL.Clear;
        dataquery.SQL.Add ('select * from V_SPEDITIONEN where REF=:ref_sped');
        dataquery.Params.ParamByName('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;

        dataquery.Open;

        if Assigned (dataquery.FindField ('REF_ROUTING_ADR')) then
          refadr := DBGetReferenz (dataquery.FieldByName ('REF_ROUTING_ADR'));

        dataquery.Close;

        if (refadr > 0) then begin
          //Für den Falle der Vorfracht, zeigt adrquery auf die Lieferadresse des Endkunden und liefquery auf die Anschrift der Vortfracht-Hubs (Exporto)

          liefquery.SQL.Add ('select'
                            +' null as COMPANY'
                            +',null as VORNAME'
                            +',null as MIDDELNAME'
                            +',null as NACHNAME'
                            +',NAME1, NAME2, NAMEZUSATZ'
                            +',STRASSE1 as STRASSE, STRASSE2 as STRASSE_2'
                            +',LAND, LAND as LAND_ISO'
                            +',PLZ, ORT, null as STATE'
                            +',TELEFON, EMAIL, CONTACT, CONTACT as ANSPRECHPARTNER'
                            +' from V_SPED_ADRESSE where REF=:RefAdr'
                            );
          liefquery.Params.ParamByName('RefAdr').Value := refadr;

          liefquery.Open;

          adrquery.SQL.Add ('select * from V_AUFTRAG_ADR where REF=:RefAdr');

          if query.FieldByName ('REF_LIEFER_ADR').IsNull then
            adrquery.Params.ParamByName('RefAdr').Value := query.FieldByName ('REF_KUNDEN_ADR').AsInteger
          else
            adrquery.Params.ParamByName('RefAdr').Value := query.FieldByName ('REF_LIEFER_ADR').AsInteger;

          adrquery.Open;
        end else if not (query.FieldByName ('REF_KUNDEN_ADR').IsNull) then begin
          adrquery.SQL.Add ('select * from V_AUFTRAG_ADR where REF=:RefAdr');
          adrquery.Params.ParamByName('RefAdr').Value := query.FieldByName ('REF_KUNDEN_ADR').AsInteger;

          adrquery.Open;

          liefquery.SQL.Add ('select * from V_AUFTRAG_ADR where REF=:RefAdr');

          if query.FieldByName ('REF_LIEFER_ADR').IsNull then
            liefquery.Params.ParamByName('RefAdr').Value := query.FieldByName ('REF_KUNDEN_ADR').AsInteger
          else
            liefquery.Params.ParamByName('RefAdr').Value := query.FieldByName ('REF_LIEFER_ADR').AsInteger;

          liefquery.Open;
        end else if not (query.FieldByName ('REF_NVE_WA').IsNull) then begin
          adrquery.SQL.Add ('select * from V_AUFTRAG_ADR where (ART=''LIEFER_ADR'' or ART=''KUNDEN_ADR'') and REF_WA=:RefWA order by case when ART=''KUNDEN_ADR'' then 0 else 9 end asc');
          adrquery.Params.ParamByName('RefWA').Value := query.FieldByName ('REF_NVE_WA').AsInteger;

          adrquery.Open;

          liefquery.SQL.Add ('select * from V_AUFTRAG_ADR where (ART=''LIEFER_ADR'' or ART=''KUNDEN_ADR'') and REF_WA=:RefWA order by case when ART=''LIEFER_ADR'' then 0 else 9 end asc');
          liefquery.Params.ParamByName('RefWA').Value := query.FieldByName ('REF_NVE_WA').AsInteger;

          liefquery.Open;
        end;

        if (liefquery.FieldByName ('LAND_ISO').IsNull) then
          landstr := 'DE'
        else landstr := liefquery.FieldByName ('LAND_ISO').AsString;

        gatequery.SQL.Add ('select * from V_SPED_GATEWAY where REF=:ref');
        gatequery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

        gatequery.Open;

        if (query.FieldByName ('DFUE_ART').AsString ='SENDIT') or (query.FieldByName ('DFUE_ART').AsString ='SENDIT-PRT') then begin
          DoneFlag := True;
          VersandApp := SENDIT;

          if (Length (PrtInfo.Leitstand) = 0) then begin
            res := -10;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText (1057, [PrtInfo.Name]);
            {$else}
              ErrorText := Format ('Dem Drucker %s ist noch keinem Leitstand zugeordnet', [PrtInfo.Name]);
            {$endif}
          end else begin
            if (Length (SendITServerPath) = 0) then begin
              res := -11;
              {$ifdef ResourceText}
                ErrorText := FormatMessageText (1455, []);
              {$else}
                ErrorText := 'Kein Exportverzeichnis für SendIT definiert';
              {$endif}
            end else if (CheckSendITPath (SendITServerPath, ErrorText) <> 0) then begin
              res := -12;
              {$ifdef ResourceText}
                ErrorText := FormatMessageText (1456, []);
              {$else}
                ErrorText := 'Exportverzeichnis für SendIT ungültig';
              {$endif}
            end else begin
              if (LabelType = 'Return') then begin
                if (query.FieldByName ('RETOUREN_NR').IsNull) then
                  fname := 'pcd_r_' + LabelClientName + '_' + query.FieldByName ('AUFTRAG_NR').AsString
                else
                  fname := 'pcd_r_' + LabelClientName + '_' + query.FieldByName ('RETOUREN_NR').AsString;

                RequesteRetFileName := fname;

                if AktSendITIFCAutoReprint then
                  artstr := 'Print'
                else if (query.FieldByName('RET_SENDUNGS_NR').IsNull) then
                  artstr := 'Print'
                else
                  artstr := 'Print';

                if not (query.FieldByName('SENDIT_RETURN_LOCATION').IsNull) then
                  versstr := query.FieldByName('SENDIT_RETURN_LOCATION').AsString
                else if not (query.FieldByName('SENDIT_LOCATION').IsNull) then
                  versstr := query.FieldByName('SENDIT_LOCATION').AsString
                else if not (query.FieldByName('GATEWAY').IsNull) then
                  versstr := query.FieldByName('GATEWAY').AsString
                else
                  versstr := '1000';

                if not (query.FieldByName('SENDIT_RETURN_CLIENT').IsNull) then
                  absender := query.FieldByName('SENDIT_RETURN_CLIENT').AsString
                else if not (query.FieldByName('SENDIT_CLIENT').IsNull) then
                  absender := query.FieldByName('SENDIT_CLIENT').AsString
                else
                  absender := query.FieldByName('MANDANT').AsString;

                if Assigned (gatequery.FindField ('SENDIT_RETURN_PRODUKT')) and not (gatequery.FieldByName('SENDIT_RETURN_PRODUKT').IsNull) then
                  prodstr := gatequery.FieldByName('SENDIT_RETURN_PRODUKT').AsString
                else if not gatequery.FieldByName('DFUE_KENNZEICHEN').IsNull then
                  prodstr := gatequery.FieldByName('DFUE_KENNZEICHEN').AsString + 'Return'
                else
                  prodstr := gatequery.FieldByName('SPED_NAME').AsString + 'Return';
              end else begin
                fname := 'pcd_' + LabelClientName + '_' + query.FieldByName('NVE_NR').AsString;
                RequesteFileName := fname;

                if AktSendITIFCAutoReprint then
                  artstr := 'Print'
                else if (query.FieldByName('SENDUNGS_NR').IsNull) then
                  artstr := 'Print'
                else
                  artstr := 'Reprint';

                dataquery.SQL.Clear;
                dataquery.SQL.Add ('select SENDIT_PRODUKT from V_SPED_GATEWAY_SERVICES'+
                                   ' where'+
                                   '   ((REF_SPED_GATEWAY is not null and REF_SPED_GATEWAY=:ref_gate) or (REF_SPED is not null and REF_SPED=:ref_sped))'+
                                   '   and (LAND_ISO is null or (instr (LAND_ISO, :land) > 0))'+
                                   '   and (REF_SPED_PRODUKTE is null or REF_SPED_PRODUKTE=:ref_prod)'+
                                   ' order by REF_SPED_GATEWAY nulls last, REF_SPED nulls last, REF_SPED_PRODUKTE nulls last, LAND_ISO nulls last');
                dataquery.Params.ParamByName('ref_gate').Value := query.FieldByName ('REF_SPED_GATEWAY').AsInteger;
                dataquery.Params.ParamByName('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;
                dataquery.Params.ParamByName('ref_prod').Value := DBGetReferenz (query.FieldByName ('REF_SPED_PRODUKT'));
                dataquery.Params.ParamByName('land').Value := landstr;

                try
                  dataquery.Open;

                  if (dataquery.RecordCount = 0) then
                    prodstr := ''
                  else
                    prodstr := dataquery.Fields [0].AsString;

                  dataquery.Close;
                except
                  prodstr := '';
                end;

                if (Length (prodstr) = 0) then begin
                  if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
                    prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
                  end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
                    prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
                  end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
                    prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
                  end else if ((query.FieldByName('SPED_NAME').AsString = 'DHL') or (query.FieldByName('DFUE_KENNZEICHEN').AsString = 'DHL')) then begin
                    //Sonderbehandlung für DHL
                    //Nachnahme nur, wenn es auch einen Wert gibt
                    if (WarenWert > 0) and (query.FieldByName('OPT_NACHNAHME').AsString > '0') then begin
                      if ((Length (landstr) = 0) or (landstr = 'DE')) then
                        prodstr := 'DHLNN'       //Inland Nachnahme
                      else
                        prodstr := 'DHLWPAKPNN'  //Weltpaket mit Nachnahme
                    end else if (query.FieldByName('VERSAND_ART').AsString = 'WPAKP') and (Length (landstr) > 0) and (landstr <> 'DE') then begin
                      if IsLandEU (query.FieldByName('LIEFER_LAND_ISO').AsString) then
                        prodstr := 'DHLWPAKP'  //Das Weltpakte Premium ist nur ausserhalb Deutschlands in der EU gültig
                      else
                        prodstr := 'DHLWPAK'  //Das Weltpakte ist nur ausserhalb Deutschlands und nicht EU gültig
                    end else if (Length (landstr) > 0) and (landstr <> 'DE') then begin
                      if IsLandEU (query.FieldByName('LIEFER_LAND_ISO').AsString) then
                        prodstr := 'DHLWPAKP'  //Das Weltpakte Premium ist nur ausserhalb Deutschlands in der EU gültig
                      else
                        prodstr := 'DHLWPAK'  //Das Weltpakte ist nur ausserhalb Deutschlands und nicht EU gültig
                    end else
                      prodstr := 'DHL';
                  end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
                    prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
                  end else begin
                    prodstr := query.FieldByName('SPED_NAME').AsString;
                  end;
                end;

                //GLS geht jetzt immer
                //Bei GLS geht die Ankündigung per Mail nur in DACH
                //if ((query.FieldByName('SPED_NAME').AsString = 'GLS') and not ((landstr = 'DE') or (landstr = 'CH') or (landstr = 'AT'))) then
                //  kepemail := false
                //Bei DPD Express oder Letter darf keine Mail-Adresse angeben
                //else
                if ((query.FieldByName('SPED_NAME').AsString = 'DPD') and ((copy (query.FieldByName('PRODUKT_ID').AsString, 1, 7) = 'EXPRESS') or (query.FieldByName('PRODUKT_ID').AsString = 'LETTER'))) then
                  kepemail := false;

                if not (query.FieldByName('SENDIT_CLIENT').IsNull) then
                  absender := query.FieldByName('SENDIT_CLIENT').AsString
                else
                  absender := query.FieldByName('MANDANT').AsString;

                if not (query.FieldByName('SENDIT_LOCATION').IsNull) then
                  versstr := query.FieldByName('SENDIT_LOCATION').AsString
                else if not (query.FieldByName('GATEWAY').IsNull) then
                  versstr := query.FieldByName('GATEWAY').AsString
                else
                  versstr := '1000';
              end;

              CleanupResponse (fname);

              res := CreateSendITLabel (SendITServerPath + 'Import\' + fname, artstr, absender, prodstr, '', versstr, warenwert, SendungsNr, TrackUrl, Barcode, LabelFormat, LabelImage, ErrorText);

              if (res <> 0) then begin
                ResponsFlag := false;
              end else begin
                if (Length (SendungsNr) = 0) then
                  ResponsFlag := true
                else begin
                  //Wenn REST-API dann muss der Response nicht abgeholt werden
                  ResponsFlag := false;
                end;
              end;

              adrquery.Close;
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString = 'BARSHIP_CSV') then begin
          DoneFlag := True;

          if (Length (SendITServerPath) = 0) then
            res := -11
          else begin
            if (LabelType = 'Return') then begin
              if not (query.FieldByName('SENDIT_RETURN_LOCATION').IsNull) then
                versstr := query.FieldByName('SENDIT_RETURN_LOCATION').AsString
              else if not (query.FieldByName('SENDIT_LOCATION').IsNull) then
                versstr := query.FieldByName('SENDIT_LOCATION').AsString
              else if not (query.FieldByName('GATEWAY').IsNull) then
                versstr := query.FieldByName('GATEWAY').AsString
              else
                versstr := '1000';

              if not (query.FieldByName('SENDIT_RETURN_CLIENT').IsNull) then
                absender := query.FieldByName('SENDIT_RETURN_CLIENT').AsString
              else if not (query.FieldByName('SENDIT_CLIENT').IsNull) then
                absender := query.FieldByName('SENDIT_CLIENT').AsString
              else
                absender := query.FieldByName('MANDANT').AsString;
            end else begin
              if AktSendITIFCAutoReprint then
                artstr := 'PRINT'
              else if (query.FieldByName('SENDUNGS_NR').IsNull) then
                artstr := 'PRINT'
              else
                artstr := 'REPRINT';

              //Bei GLS geht die Ankündigung per Mail nur in DACH
              if ((query.FieldByName('SPED_NAME').AsString = 'GLS') and not ((landstr = 'DE') or (landstr = 'CH') or (landstr = 'AT'))) then
                kepemail := false
              //Bei DPD Express oder Letter darf keine Mail-Adresse angeben
              else if ((query.FieldByName('SPED_NAME').AsString = 'DPD') and ((copy (query.FieldByName('PRODUKT_ID').AsString, 1, 7) = 'EXPRESS') or (query.FieldByName('PRODUKT_ID').AsString = 'LETTER'))) then
                kepemail := false;

              if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
                prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
              end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
                prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
              end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
                prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
              end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
                prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
              end else begin
                prodstr := query.FieldByName('SPED_NAME').AsString;
              end;

              if not (query.FieldByName('SENDIT_CLIENT').IsNull) then
                absender := query.FieldByName('SENDIT_CLIENT').AsString
              else
                absender := query.FieldByName('MANDANT').AsString;

              if not (query.FieldByName('SENDIT_LOCATION').IsNull) then
                versstr := query.FieldByName('SENDIT_LOCATION').AsString
              else if not (query.FieldByName('GATEWAY').IsNull) then
                versstr := query.FieldByName('GATEWAY').AsString
              else
                versstr := '1000';
            end;

            VersandApp := BARCODE_SHIPPING;

            RequesteFileName := 'pcd_bar_' + LabelClientName + '_' + query.FieldByName('NVE_NR').AsString;

            CleanupResponse (RequesteFileName);

            res := CreateBarShippingLabel (SendITServerPath + 'Import\' + RequesteFileName, artstr, absender, prodstr, '', versstr, warenwert);

            if (res = 0) then
              ResponsFlag := true;

            adrquery.Close;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString ='DELIS') then begin
          (*
          DoneFlag := True;

          CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'DELIS_CSV_PATH', csvpath);

          if (Length (csvpath) = 0) then
            res := -11
          else begin
            artstr := 'NP';

            //Nachnahme nur, wenn es auch einen Wert gibt
            if (WarenWert > 0) and (query.FieldByName('OPT_NACHNAHME').AsString > '0') then
              prodstr := 'NN'
            else
              prodstr := '';

            if (csvpath [Length (csvpath)] <> '\') then csvpath := csvpath + '\';

            VersandApp := 'Delis';

            RequesteFileName := 'pcd_' + query.FieldByName('AUFTRAG_NR').AsString + '.csv';

            //res := CreateDELIExport (csvpath + RequesteFileName, artstr, prodstr, '', warenwert);

            if (res <> 0) then
              RequesteFileName := ''
            else
              ResponsFlag := false;
          end;
          *)
        end else if (query.FieldByName ('DFUE_ART').AsString ='INTRASHIP') then begin
          (*
          DoneFlag := True;

          CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'INTRASHIP_CSV_PATH', csvpath);

          if (Length (csvpath) = 0) then
            res := -11
          else begin
            if (query.FieldByName('DFUE_KENNZEICHEN').AsString = 'DHL') then begin
              if (query.FieldByName('VERSAND_ART').AsString = 'WPAKP') and not (liefquery.FieldByName ('LAND_ISO').IsNull) and (liefquery.FieldByName ('LAND_ISO').AsString <> 'DE') then
                artstr := 'BPI'  //DHL Weltpackte
              else
                artstr := 'EPN';  //DHL National
            end else if (query.FieldByName('DFUE_KENNZEICHEN').AsString = 'DHL-EX') then
              artstr := 'EXP';  //DHL Express

            //Nachnahme nur, wenn es auch einen Wert gibt
            if (WarenWert > 0) and (query.FieldByName('OPT_NACHNAHME').AsString > '0') then
              prodstr := 'NN';

            if (csvpath [Length (csvpath)] <> '\') then csvpath := csvpath + '\';

            VersandApp := 'Intraship';

            RequesteFileName := 'Intraship_' + FormatDateTime ('yyyymmdd',Now) + '_' + query.FieldByName('NVE_NR').AsString;

            if FileExists (csvpath + RequesteFileName + '.csv') then
              DeleteFile (csvpath + RequesteFileName + '.csv');

            //res := CreateIntraShipExport (csvpath + RequesteFileName, artstr, prodstr, '', warenwert);

            if (res <> 0) then
              RequesteFileName := ''
            else begin
              if (PrtInfo.PrtTyp = 'LASER') then begin
                res := PrintModule.PrintReport('', PrtInfo.Port, '', -1, query.FieldByName ('REF_LAGER').AsInteger, query.FieldByName ('REF_MAND').AsInteger, '', 'INTRASHIP_IMPORT', '', ['REF:' + query.FieldByName('NVENr').AsString], ErrorText, False);
              end else begin
                res := CreateNVEPrintJob (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_MAND').AsInteger, query.FieldByName ('REF_LAGER').AsInteger, -1, PrtInfo.Name);

                if (res <> 0) then
                  ErrorText := LVSDatenModul.LastSQLErrorText;
              end;
            end;
          end;
          *)
        end else if (query.FieldByName ('DFUE_ART').AsString ='7SENDERS') then begin
          VersandApp := '7SENDERS';

          DoneFlag := True;

          if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
            prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
          end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
            prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
          end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
            prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
          end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
            prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
          end else begin
            prodstr := query.FieldByName('SPED_NAME').AsString;
          end;

          if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
            SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

            res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
          end;

          if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
            RefLabel := -1;
            artstr  := 'PRINT';

            res := CreateSevenSendersLabel (artstr, prodstr, warenwert, ErrorText);

            if (res = 0) and (Length (SendungsID) > 0) then begin
              if (query.FieldByName ('SENDUNGS_ID').AsString <> SendungsID) then
                res := SetNVESendungsID (query.FieldByName('REF_NVE').AsInteger, VersandApp, Versender, SendungsID);
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString ='SHIPTRACK') then begin
          VersandApp := 'SHIPTRACK';

          DoneFlag := True;

          if (Length (PrtInfo.Port) = 0) then begin
            res := -1;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText(1443, []);
            {$else}
              ErrorText := 'Es ist noch kein NVE-Drucker definiert';
            {$endif}
          end else begin
            res := 0;

            if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
            end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
            end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
              prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
            end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
              prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
            end else begin
              prodstr := query.FieldByName('SPED_NAME').AsString;
            end;

            if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
              SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

              res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
            end;

            if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
              RefLabel := -1;
              artstr  := 'PRINT';

              res := CreateShipTrackLabel (artstr, warenwert, ErrorText);
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString ='SENDCLOUD') then begin
          VersandApp := 'SENDCLOUD';

          DoneFlag := True;

          if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
            prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
          end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
            prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
          end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
            prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
          end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
            prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
          end else begin
            prodstr := query.FieldByName('SPED_NAME').AsString;
          end;

          if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
            SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

            res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
          end else if not (query.FieldByName('SENDUNGS_ID').IsNull) then begin
            SendungsID := query.FieldByName ('SENDUNGS_ID').AsString;
          end;

          if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
            RefLabel := -1;
            artstr  := 'PRINT';

            res := CreateSendCloudLabel (artstr, prodstr, warenwert, ErrorText);

            if (res = 0) and (Length (SendungsID) > 0) and (Length (SendungsNr) = 0) then begin
              if (query.FieldByName ('SENDUNGS_ID').AsString <> SendungsID) then
                res := SetNVESendungsID (query.FieldByName('REF_NVE').AsInteger, VersandApp, Versender, SendungsID);
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString ='FAIRSENDEN') then begin
          VersandApp := 'FAIRSENDEN';

          DoneFlag := True;

          if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
            prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
          end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
            prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
          end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
            prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
          end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
            prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
          end else begin
            prodstr := query.FieldByName('SPED_NAME').AsString;
          end;

          if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
            SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

            res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
          end;

          if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
            RefLabel := -1;
            artstr  := 'PRINT';

            res := CreateFairSendenLabel (artstr, prodstr, warenwert, ErrorText);
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString = 'LIEFERGRUEN') then begin
          VersandApp := 'LIEFERGRUEN';

          DoneFlag := True;

          if (Length (PrtInfo.Port) = 0) then begin
            res := -1;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText(1443, []);
            {$else}
              ErrorText := 'Es ist noch kein NVE-Drucker definiert';
            {$endif}
          end else begin
            if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
            end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
            end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
              prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
            end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
              prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
            end else begin
              prodstr := query.FieldByName('SPED_NAME').AsString;
            end;

            if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
              SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

              res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
            end else if not (query.FieldByName('SENDUNGS_ID').IsNull) then begin
              SendungsID := query.FieldByName ('SENDUNGS_ID').AsString;
            end;

            if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
              RefLabel := -1;
              artstr  := 'PRINT';

              if (Length (SendungsID) = 0) then
                res := CreateLieferGruenLabel (artstr, prodstr, warenwert, ErrorText);

              if (res = 0) and (Length (SendungsID) > 0) and (Length (SendungsNr) = 0) then begin
                if (query.FieldByName ('SENDUNGS_ID').AsString <> SendungsID) then
                  res := SetNVESendungsID (query.FieldByName('REF_NVE').AsInteger, VersandApp, Versender, SendungsID);

                timeout := TTimeout.Create;

                try
                  timeout.SetTimeout (60 * 1000);

                  repeat
                    res := GetLieferGruenLabel (artstr, prodstr, warenwert, ErrorText);

                    if (res = 0) and (Length (SendungsNr) = 0) then
                      Sleep (5000);
                  until (res <> 0) or (Length (SendungsNr) > 0) or timeout.CheckTimeout;

                  if (res = 0) and (Length (SendungsNr) = 0) then begin
                    res := 49;

                    {$ifdef ResourceText}
                      ErrorText := 'Es wurde keine Sendungsnummer erzeugt';
                    {$else}
                      ErrorText := 'Es wurde keine Sendungsnummer erzeugt';
                    {$endif}
                  end;
                finally
                  timeout.Free;
                end;
              end;
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString = 'GLS-IT-WEB') then begin
          VersandApp := 'GLS-IT-WEB';

          DoneFlag := True;

          if (Length (PrtInfo.Port) = 0) then begin
            res := -1;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText(1443, []);
            {$else}
              ErrorText := 'Es ist noch kein NVE-Drucker definiert';
            {$endif}
          end else begin
            if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
            end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
            end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
              prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
            end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
              prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
            end else begin
              prodstr := query.FieldByName('SPED_NAME').AsString;
            end;

            if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
              SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

              res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
            end;

            if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
              RefLabel := -1;
              artstr  := 'PRINT';

              res := CreateGLSITLabel (artstr, 'GLS-IT', warenwert, ErrorText);
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString = 'POST-NL-API') then begin
          VersandApp := 'POST-NL-API';

          DoneFlag := True;

          if (Length (PrtInfo.Port) = 0) then begin
            res := -1;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText(1443, []);
            {$else}
              ErrorText := 'Es ist noch kein NVE-Drucker definiert';
            {$endif}
          end else begin
            if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
            end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
            end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
              prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
            end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
              prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
            end else begin
              prodstr := query.FieldByName('SPED_NAME').AsString;
            end;

            if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
              SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

              res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
            end;

            if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
              RefLabel := -1;
              artstr  := 'PRINT';

              res := CreatePostNLLabel (artstr, 'POST-NL', warenwert, ErrorText);
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString = 'GO-API') then begin
          VersandApp := 'GO-API';

          DoneFlag := True;

          if (Length (PrtInfo.Port) = 0) then begin
            res := -1;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText(1443, []);
            {$else}
              ErrorText := 'Es ist noch kein NVE-Drucker definiert';
            {$endif}
          end else begin
            if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
            end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
            end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
              prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
            end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
              prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
            end else begin
              prodstr := query.FieldByName('SPED_NAME').AsString;
            end;

            if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
              SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

              res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
            end;

            if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
              RefLabel := -1;
              artstr  := 'PRINT';

              res := CreateGoLabel (artstr, 'GO', warenwert, ErrorText);
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString = 'HEYWORLD-API') then begin
          VersandApp := 'HEYWORLD-API';

          DoneFlag := True;

          if (Length (PrtInfo.Port) = 0) then begin
            res := -1;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText(1443, []);
            {$else}
              ErrorText := 'Es ist noch kein NVE-Drucker definiert';
            {$endif}
          end else begin
            if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
            end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
            end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
              prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
            end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
              prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
            end else begin
              prodstr := query.FieldByName('SPED_NAME').AsString;
            end;

            if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
              SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

              res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
            end else if not (query.FieldByName('SENDUNGS_ID').IsNull) then begin
              SendungsID := query.FieldByName ('SENDUNGS_ID').AsString;
            end;

            if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
              RefLabel := -1;
              artstr  := 'PRINT';

              if (Length (SendungsID) = 0) then
                res := CreateHeyworldLabel (artstr, 'HEYWORLD', warenwert, ErrorText);

              if (res = 0) and (Length (SendungsID) > 0) and (Length (SendungsNr) = 0) then begin
                if (query.FieldByName ('SENDUNGS_ID').AsString <> SendungsID) then
                  res := SetNVESendungsID (query.FieldByName('REF_NVE').AsInteger, VersandApp, Versender, SendungsID);

                {$ifdef LVS}
                  PrintModule.PrepareInfoWin;
                  PrintModule.ShowInfoWinText ('Warten auf das Versandlabel...');
                {$endif}

                timeout := TTimeout.Create;

                try
                  timeout.SetTimeout (60 * 1000);

                  repeat
                    res := CreateHeyworldLabel (artstr, 'HEYWORLD', warenwert, ErrorText);

                    if (res = 0) and (Length (SendungsNr) = 0) then
                      Sleep (5000);
                  until (res <> 0) or (Length (SendungsNr) > 0) or timeout.CheckTimeout;

                  if (res = 0) and (Length (SendungsNr) = 0) then begin
                    res := 49;

                    {$ifdef ResourceText}
                      ErrorText := 'Es wurde keine Sendungsnummer erzeugt';
                    {$else}
                      ErrorText := 'Es wurde keine Sendungsnummer erzeugt';
                    {$endif}
                  end;
                finally
                  timeout.Free;
                end;

                {$ifdef LVS}
                  PrintModule.CloseInfoWin;
                {$endif}
              end;

            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString = 'COURIER-API') then begin
          VersandApp := 'COURIER-API';

          DoneFlag := True;

          if (Length (PrtInfo.Port) = 0) then begin
            res := -1;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText(1443, []);
            {$else}
              ErrorText := 'Es ist noch kein NVE-Drucker definiert';
            {$endif}
          end else begin
            if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
            end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
            end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
              prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
            end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
              prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
            end else begin
              prodstr := query.FieldByName('SPED_NAME').AsString;
            end;

            if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
              SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

              res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
            end;

            if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
              RefLabel := -1;
              artstr  := 'PRINT';

              res := CreateCourierLabel (artstr, 'Courier', warenwert, ErrorText);
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString = 'SPRING-API') then begin
          VersandApp := 'SPRING-API';

          DoneFlag := True;

          if (Length (PrtInfo.Port) = 0) then begin
            res := -1;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText(1443, []);
            {$else}
              ErrorText := 'Es ist noch kein NVE-Drucker definiert';
            {$endif}
          end else begin
            if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
            end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
            end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
              prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
            end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
              prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
            end else begin
              prodstr := query.FieldByName('SPED_NAME').AsString;
            end;

            if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
              SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

              res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
            end;

            if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
              RefLabel := -1;
              artstr  := 'PRINT';

              res := CreateSpringLabel (artstr, 'Spring', warenwert, ErrorText);
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString = 'VLOG') then begin
          VersandApp := 'VLOG';

          DoneFlag := True;

          if (Length (PrtInfo.Port) = 0) then begin
            res := -1;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText(1443, []);
            {$else}
              ErrorText := 'Es ist noch kein NVE-Drucker definiert';
            {$endif}
          end else begin
            if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
            end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
            end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
              prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
            end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
              prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
            end else begin
              prodstr := query.FieldByName('SPED_NAME').AsString;
            end;

            if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
              SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

              res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
            end else if not (query.FieldByName('SENDUNGS_ID').IsNull) then begin
              SendungsID := query.FieldByName ('SENDUNGS_ID').AsString;
            end;

            if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
              RefLabel := -1;
              artstr  := 'PRINT';

              res := CreateVLOGLabel (artstr, absender, prodstr, SendungsID, warenwert, SendungsNr, TrackUrl, Barcode, LabelFormat, LabelImage, ErrorText);

              if (res = 0) then begin
                if (query.FieldByName ('SENDUNGS_ID').AsString <> SendungsID) then
                  res := SetNVESendungsID (query.FieldByName('REF_NVE').AsInteger, VersandApp, Versender, SendungsID);
              end;
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString = 'VCE') then begin
          VersandApp := 'VCE';

          DoneFlag := True;

          if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
            prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
          end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
            prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
          end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
            prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
          end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
            prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
          end else begin
            prodstr := query.FieldByName('SPED_NAME').AsString;
          end;

          if (LabelType = 'Return') then begin
            RefLabel := -1;

            artstr  := 'Return';

            res := CreateVCECarmaLabel (artstr, absender, prodstr, SendungsID, warenwert, SendungsNr, TrackUrl, Barcode, LabelFormat, LabelImage, ErrorText);
          end else begin
            if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
              SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

              res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
            end else if not (query.FieldByName('SENDUNGS_ID').IsNull) then begin
              SendungsID := query.FieldByName ('SENDUNGS_ID').AsString;
            end;

            if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
              RefLabel := -1;

              artstr  := 'Print';

              res := CreateVCECarmaLabel (artstr, absender, prodstr, SendungsID, warenwert, SendungsNr, TrackUrl, Barcode, LabelFormat, LabelImage, ErrorText);

              if (res = 0) then begin
                if (query.FieldByName ('SENDUNGS_ID').AsString <> SendungsID) then
                  res := SetNVESendungsID (query.FieldByName('REF_NVE').AsInteger, VersandApp, Versender, SendungsID);
              end;
            end;
          end;
        end else if (query.FieldByName ('DFUE_ART').AsString = 'DPD-WEB') then begin
          VersandApp := 'DPD-WEB';

          DoneFlag := True;

          if (Length (PrtInfo.Port) = 0) then begin
            res := -1;
            {$ifdef ResourceText}
              ErrorText := FormatMessageText(1443, []);
            {$else}
              ErrorText := 'Es ist noch kein NVE-Drucker definiert';
            {$endif}
          end else begin
            if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT').IsNull) and ((Length (landstr) = 0) or (landstr = 'DE')) then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT').AsString;
            end else if not (query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').IsNull) and (Length (landstr) > 0) and (landstr <> 'DE') then begin
              prodstr := query.FieldByName('GATEWAY_SENDIT_PRODUKT_AUS').AsString;
            end else if not (query.FieldByName('SENDIT_PRODUKT').IsNull) then begin
              prodstr := query.FieldByName('SENDIT_PRODUKT').AsString;
            end else if not query.FieldByName('DFUE_KENNZEICHEN').IsNull then begin
              prodstr := query.FieldByName('DFUE_KENNZEICHEN').AsString;
            end else begin
              prodstr := query.FieldByName('SPED_NAME').AsString;
            end;

            if not (query.FieldByName('SENDUNGS_NR').IsNull) then begin
              SendungsNr := query.FieldByName ('SENDUNGS_NR').AsString;

              res := GetNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, LabelFormat, LabelImage, RefLabel);
            end;

            if ((Length (SendungsNr) = 0) or (LabelImage.Size = 0)) then begin
              RefLabel := -1;
              artstr  := 'PRINT';

              res := CreateDPDWEBLabel (artstr, 'DPD-WEB', warenwert, ErrorText);
            end;
          end;
        end;

        adrquery.Close;
        liefquery.Close;
      end;
    except
      on  E: Exception do begin
        res := -9;
        ErrorText := 'Fehler in der SendIT Datenaufbereitung';

        {$ifdef ErrorTracking}
          ErrorTrackingModule.WriteErrorLog ('Exception StartPrintVersandLabel (1)', e.ClassName + ' : ' + e.Message);
        {$else}
          if Assigned (MasterLog) then MasterLog.Write ('Exception StartPrintVersandLabel (1): '+ e.ClassName + ' : ' + e.Message);
        {$endif}
      end;
    end;
  finally
    gatequery.Free;
    dataquery.Free;
    liefquery.Free;
    adrquery.Free;
  end;

  if (Length (ErrorText) > 0) then begin
    {$ifdef ErrorTracking}
      ErrorTrackingModule.WriteErrorLogNoDB ('StartPrintVersandLabel', ErrorText);
    {$else}
       if Assigned (MasterLog) then MasterLog.Write ('StartPrintVersandLabel: '+ ErrorText);
    {$endif}
  end;

  Result := res;

  {$ifdef Trace}
    TraceResult ('ErrorText', ErrorText);
    FunctionStop (Result);
  {$endif}
end;

initialization
  AktSendITPath          := '';
  AktSendITIFCImpVersion := 1;
  AktSendITIFCExpVersion := 1;

  SendITLog     := Nil;
  SendITMapping := Nil;
  NonEUList     := Nil;

finalization
  if Assigned (SendITLog) then
    SendITLog.Free;

  if Assigned (SendITMapping) then
    SendITMapping.Free;

  if Assigned (NonEUList) then
    NonEUList.Free;
end.
