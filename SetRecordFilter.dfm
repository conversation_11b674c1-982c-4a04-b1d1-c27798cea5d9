object RecordFilterForm: TRecordFilterForm
  Left = 394
  Top = 351
  BorderStyle = bsDialog
  Caption = 'Aufwahlfilter definieren'
  ClientHeight = 286
  ClientWidth = 570
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    570
    286)
  TextHeight = 13
  object PageControl1: TPageControl
    Left = 8
    Top = 8
    Width = 553
    Height = 236
    ActivePage = LagerTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 0
    object WETabSheet: TTabSheet
      Caption = 'Warenerwartung'
      ImageIndex = 4
      OnShow = WETabSheetShow
      object Label5: TLabel
        Left = 16
        Top = 43
        Width = 52
        Height = 13
        Caption = 'Kundennr.:'
      end
      object Label6: TLabel
        Left = 16
        Top = 75
        Width = 44
        Height = 13
        Caption = 'Artikelnr.:'
      end
      object Label7: TLabel
        Left = 16
        Top = 107
        Width = 65
        Height = 13
        Caption = 'Artikelgruppe:'
      end
      object Label1: TLabel
        Left = 16
        Top = 139
        Width = 69
        Height = 13
        Caption = 'Lieferscheinnr.'
      end
      object Label19: TLabel
        Left = 16
        Top = 171
        Width = 56
        Height = 13
        Caption = 'NVE im WE'
      end
      object Label27: TLabel
        Left = 16
        Top = 11
        Width = 67
        Height = 13
        Caption = 'Untermandant'
      end
      object WEKdEdit: TEdit
        Left = 96
        Top = 40
        Width = 329
        Height = 21
        TabOrder = 1
      end
      object WEArEdit: TEdit
        Left = 96
        Top = 72
        Width = 329
        Height = 21
        TabOrder = 2
      end
      object WEArGrpComboBox: TComboBoxPro
        Left = 96
        Top = 104
        Width = 329
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 3
      end
      object WELSEdit: TEdit
        Left = 96
        Top = 136
        Width = 329
        Height = 21
        TabOrder = 4
      end
      object WENVEEdit: TEdit
        Left = 96
        Top = 168
        Width = 329
        Height = 21
        TabOrder = 5
        Text = 'WENVEEdit'
      end
      object WESubMandComboBox: TComboBoxPro
        Left = 96
        Top = 8
        Width = 329
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 0
        OnChange = AufSubMandComboBoxChange
      end
    end
    object RetTabSheet: TTabSheet
      Caption = 'Retouren'
      ImageIndex = 7
      OnShow = RetTabSheetShow
      object Label20: TLabel
        Left = 16
        Top = 43
        Width = 52
        Height = 13
        Caption = 'Kundennr.:'
      end
      object Label21: TLabel
        Left = 16
        Top = 75
        Width = 44
        Height = 13
        Caption = 'Artikelnr.:'
      end
      object Label28: TLabel
        Left = 16
        Top = 11
        Width = 67
        Height = 13
        Caption = 'Untermandant'
      end
      object RetKDEdit: TEdit
        Left = 96
        Top = 40
        Width = 329
        Height = 21
        TabOrder = 1
      end
      object RetArEdit: TEdit
        Left = 96
        Top = 72
        Width = 329
        Height = 21
        TabOrder = 2
      end
      object RetSubMandComboBox: TComboBoxPro
        Left = 96
        Top = 8
        Width = 329
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 0
        OnChange = AufSubMandComboBoxChange
      end
    end
    object AufTabSheet: TTabSheet
      Caption = 'Auftrag'
      OnShow = AufTabSheetShow
      object Label2: TLabel
        Left = 16
        Top = 107
        Width = 65
        Height = 13
        Caption = 'Artikelgruppe:'
      end
      object Label3: TLabel
        Left = 16
        Top = 43
        Width = 52
        Height = 13
        Caption = 'Kundennr.:'
      end
      object Label4: TLabel
        Left = 16
        Top = 75
        Width = 44
        Height = 13
        Caption = 'Artikelnr.:'
      end
      object Label17: TLabel
        Left = 16
        Top = 11
        Width = 67
        Height = 13
        Caption = 'Untermandant'
      end
      object Label22: TLabel
        Left = 16
        Top = 139
        Width = 47
        Height = 13
        Caption = 'Spedition:'
      end
      object AufKdEdit: TEdit
        Left = 96
        Top = 40
        Width = 329
        Height = 21
        TabOrder = 1
      end
      object AufArEdit: TEdit
        Left = 96
        Top = 72
        Width = 329
        Height = 21
        TabOrder = 2
      end
      object AufArGrpComboBox: TComboBoxPro
        Left = 96
        Top = 104
        Width = 329
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 3
      end
      object AufSubMandComboBox: TComboBoxPro
        Left = 96
        Top = 8
        Width = 329
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 0
        OnChange = AufSubMandComboBoxChange
      end
      object AufSpedComboBox: TComboBoxPro
        Left = 96
        Top = 136
        Width = 329
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 4
      end
    end
    object BatchTabSheet: TTabSheet
      Caption = 'Batchlauf'
      ImageIndex = 6
      object Label14: TLabel
        Left = 16
        Top = 19
        Width = 52
        Height = 13
        Caption = 'Kundennr.:'
      end
      object Label15: TLabel
        Left = 16
        Top = 51
        Width = 44
        Height = 13
        Caption = 'Artikelnr.:'
      end
      object Label16: TLabel
        Left = 16
        Top = 83
        Width = 49
        Height = 13
        Caption = 'Auftragnr.:'
      end
      object BatchArNrEdit: TEdit
        Left = 96
        Top = 48
        Width = 329
        Height = 21
        TabOrder = 0
      end
      object BachtKdNrEdit: TEdit
        Left = 96
        Top = 16
        Width = 329
        Height = 21
        TabOrder = 1
      end
      object BatchAufNrEdit: TEdit
        Left = 96
        Top = 80
        Width = 329
        Height = 21
        TabOrder = 2
      end
    end
    object KommTabSheet: TTabSheet
      Caption = 'Kommissionierung'
      ImageIndex = 1
      object Label13: TLabel
        Left = 16
        Top = 51
        Width = 44
        Height = 13
        Caption = 'Artikelnr.:'
      end
      object Label24: TLabel
        Left = 16
        Top = 91
        Width = 46
        Height = 13
        Caption = 'Auftragnr.'
      end
      object KommArEdit: TEdit
        Left = 96
        Top = 48
        Width = 329
        Height = 21
        TabOrder = 0
      end
      object KommAufNrEdit: TEdit
        Left = 96
        Top = 88
        Width = 329
        Height = 21
        TabOrder = 1
        Text = 'KommAufNrEdit'
      end
    end
    object WATabSheet: TTabSheet
      Caption = 'Warenausgang'
      ImageIndex = 3
      OnShow = WATabSheetShow
      object Label11: TLabel
        Left = 16
        Top = 43
        Width = 52
        Height = 13
        Caption = 'Kundennr.:'
      end
      object Label12: TLabel
        Left = 16
        Top = 75
        Width = 44
        Height = 13
        Caption = 'Artikelnr.:'
      end
      object Label18: TLabel
        Left = 16
        Top = 11
        Width = 67
        Height = 13
        Caption = 'Untermandant'
      end
      object Label23: TLabel
        Left = 16
        Top = 107
        Width = 47
        Height = 13
        Caption = 'Spedition:'
      end
      object Label25: TLabel
        Left = 16
        Top = 139
        Width = 42
        Height = 13
        Caption = 'NVE-Nr.:'
      end
      object Label26: TLabel
        Left = 16
        Top = 171
        Width = 63
        Height = 13
        Caption = 'Sendungsnr.:'
      end
      object WAKdEdit: TEdit
        Left = 96
        Top = 40
        Width = 329
        Height = 21
        TabOrder = 1
      end
      object WAArEdit: TEdit
        Left = 96
        Top = 72
        Width = 329
        Height = 21
        TabOrder = 2
      end
      object WASubMandComboBox: TComboBoxPro
        Left = 96
        Top = 8
        Width = 329
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 0
      end
      object WASpedComboBox: TComboBoxPro
        Left = 96
        Top = 104
        Width = 329
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 3
      end
      object WANVEEdit: TEdit
        Left = 96
        Top = 136
        Width = 329
        Height = 21
        TabOrder = 4
      end
      object WASendNrEdit: TEdit
        Left = 96
        Top = 168
        Width = 329
        Height = 21
        TabOrder = 5
      end
    end
    object VerlTabSheet: TTabSheet
      Caption = 'Verladung'
      ImageIndex = 4
      object Label8: TLabel
        Left = 24
        Top = 25
        Width = 43
        Height = 13
        Caption = 'Bestellnr.'
      end
      object Label9: TLabel
        Left = 24
        Top = 65
        Width = 46
        Height = 13
        Caption = 'Auftragnr.'
      end
      object VerlBestEdit: TEdit
        Left = 88
        Top = 22
        Width = 265
        Height = 21
        TabOrder = 0
        Text = 'VerlBestEdit'
      end
      object VerlAufEdit: TEdit
        Left = 88
        Top = 62
        Width = 265
        Height = 21
        TabOrder = 1
        Text = 'VerlBestEdit'
      end
    end
    object LagerTabSheet: TTabSheet
      Caption = 'Lager'
      ImageIndex = 5
      OnShow = LagerTabSheetShow
      object Label10: TLabel
        Left = 16
        Top = 51
        Width = 44
        Height = 13
        Caption = 'Artikelnr.:'
      end
      object Label151: TLabel
        Left = 16
        Top = 11
        Width = 67
        Height = 13
        Caption = 'Untermandant'
      end
      object LagerAREdit: TEdit
        Left = 96
        Top = 48
        Width = 329
        Height = 21
        TabOrder = 1
      end
      object LagerSubMandComboBox: TComboBoxPro
        Left = 96
        Top = 8
        Width = 329
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 0
      end
    end
  end
  object OkButton: TButton
    Left = 392
    Top = 255
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 1
  end
  object AbortButton: TButton
    Left = 486
    Top = 255
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 2
  end
  object ADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 344
    Top = 224
  end
end
