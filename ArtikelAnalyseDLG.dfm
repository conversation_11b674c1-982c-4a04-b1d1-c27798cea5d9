object ArtikelAnalyseForm: TArtikelAnalyseForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Artikel Analyse'
  ClientHeight = 478
  ClientWidth = 265
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    265
    478)
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Bevel2: TBevel
    Left = 8
    Top = 436
    Width = 249
    Height = 5
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 314
  end
  object Label5: TLabel
    Left = 8
    Top = 48
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Bevel5: TBevel
    Left = 8
    Top = 400
    Width = 249
    Height = 5
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 413
  end
  object Bevel1: TBevel
    Left = 8
    Top = 323
    Width = 249
    Height = 5
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 340
  end
  object Label6: TLabel
    Left = 8
    Top = 328
    Width = 52
    Height = 13
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Von Datum'
  end
  object Label7: TLabel
    Left = 139
    Top = 328
    Width = 47
    Height = 13
    Anchors = [akRight, akBottom]
    Caption = 'Bis Datum'
  end
  object Label2: TLabel
    Left = 8
    Top = 196
    Width = 66
    Height = 13
    Caption = 'Warengruppe'
  end
  object Label3: TLabel
    Left = 8
    Top = 276
    Width = 28
    Height = 13
    Caption = 'Brand'
  end
  object Label4: TLabel
    Left = 8
    Top = 236
    Width = 46
    Height = 13
    Caption = 'Hersteller'
  end
  object OkButton: TButton
    Left = 101
    Top = 447
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    TabOrder = 10
    OnClick = OkButtonClick
    ExplicitTop = 464
  end
  object AbortButton: TButton
    Left = 182
    Top = 447
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 11
    ExplicitTop = 464
  end
  object MandantComboBox: TComboBoxPro
    Left = 8
    Top = 24
    Width = 249
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 80
    ItemHeight = 15
    TabOrder = 0
    OnChange = MandantComboBoxChange
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 64
    Width = 249
    Height = 21
    Style = csOwnerDrawFixed
    ColWidth = 80
    ItemHeight = 15
    TabOrder = 1
  end
  object ExcelCheckBox: TCheckBox
    Left = 8
    Top = 413
    Width = 249
    Height = 17
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Ausgabe in Excel'
    TabOrder = 9
    Visible = False
    ExplicitTop = 430
  end
  object VonDatumEdit: TEdit
    Left = 8
    Top = 346
    Width = 118
    Height = 21
    Anchors = [akLeft, akBottom]
    TabOrder = 6
    Text = 'MHDEdit'
    OnExit = VonDatumEditExit
    ExplicitTop = 363
  end
  object BisDatumEdit: TEdit
    Left = 139
    Top = 346
    Width = 118
    Height = 21
    Anchors = [akRight, akBottom]
    TabOrder = 7
    Text = 'MHDEdit'
    OnExit = BisDatumEditExit
    ExplicitTop = 363
  end
  object AuswertArtRadioGroup: TRadioGroup
    Left = 9
    Top = 91
    Width = 248
    Height = 99
    Caption = 'Auswertungsart'
    ItemIndex = 0
    Items.Strings = (
      'Sortiert nach der Gesamtmenge'
      'Sortiert nach der Gesamtgewicht'
      'Sortiert nach Anzahl der Auftragspositionen'
      'Sortiert nach Artikelnummern')
    TabOrder = 2
  end
  object ARGrpComboBox: TComboBoxPro
    Left = 8
    Top = 212
    Width = 249
    Height = 22
    Style = csOwnerDrawFixed
    TabOrder = 3
  end
  object BrandComboBox: TComboBoxPro
    Left = 8
    Top = 292
    Width = 249
    Height = 22
    Style = csOwnerDrawFixed
    TabOrder = 5
  end
  object ManfComboBox: TComboBoxPro
    Left = 8
    Top = 252
    Width = 249
    Height = 22
    Style = csOwnerDrawFixed
    TabOrder = 4
    OnChange = ManfComboBoxChange
  end
  object NoValueCheckBox: TCheckBox
    Left = 8
    Top = 376
    Width = 249
    Height = 17
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Artikel ohne Werte auch ber'#252'cksichtigen'
    TabOrder = 8
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 216
    Top = 8
  end
end
