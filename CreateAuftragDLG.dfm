object CreateAuftragForm: TCreateAuftragForm
  Left = 426
  Top = 388
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Auftrag '#228'ndern'
  ClientHeight = 874
  ClientWidth = 758
  Color = clBtnFace
  Constraints.MinHeight = 500
  Constraints.MinWidth = 700
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  TextHeight = 13
  object MandPanel: TPanel
    Left = 0
    Top = 0
    Width = 758
    Height = 32
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      758
      32)
    object Label2: TLabel
      Left = 8
      Top = 11
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object MandantComboBox: TComboBoxPro
      Left = 88
      Top = 8
      Width = 661
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
  end
  object SubMandPanel: TPanel
    Left = 0
    Top = 32
    Width = 758
    Height = 28
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      758
      28)
    object Label11: TLabel
      Left = 8
      Top = 11
      Width = 67
      Height = 13
      Caption = 'Untermandant'
    end
    object SubMandComboBox: TComboBoxPro
      Left = 88
      Top = 4
      Width = 661
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = SubMandComboBoxChange
    end
  end
  object LagerPanel: TPanel
    Left = 0
    Top = 108
    Width = 758
    Height = 36
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      758
      36)
    object Label3: TLabel
      Left = 8
      Top = 11
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object LagerComboBox: TComboBoxPro
      Left = 88
      Top = 8
      Width = 661
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      OnChange = LagerComboBoxChange
    end
  end
  object EmpfPanel: TPanel
    Left = 0
    Top = 144
    Width = 758
    Height = 221
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 4
    object EmpfKopfPanel: TPanel
      Left = 0
      Top = 0
      Width = 758
      Height = 49
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 0
      DesignSize = (
        758
        49)
      object Label12: TLabel
        Left = 8
        Top = 17
        Width = 78
        Height = 13
        Caption = 'Warenempf. Nr.:'
      end
      object Bevel1: TBevel
        Left = 8
        Top = 2
        Width = 741
        Height = 4
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
        ExplicitWidth = 601
      end
      object EmpfNameLabel: TLabel
        Left = 224
        Top = 17
        Width = 78
        Height = 13
        Caption = 'EmpfNameLabel'
      end
      object EmpfNrEdit: TEdit
        Left = 88
        Top = 14
        Width = 121
        Height = 21
        TabOrder = 0
        Text = 'EmpfNrEdit'
        OnExit = EmpfNrEditExit
      end
      object ShowEmpfGridButton: TButton
        Left = 549
        Top = 12
        Width = 200
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Warenempf'#228'nger anzeigen...'
        TabOrder = 1
        TabStop = False
        OnClick = ShowEmpfGridButtonClick
      end
    end
    object EmpfGridPanel: TPanel
      Left = 0
      Top = 49
      Width = 758
      Height = 172
      Align = alClient
      BevelOuter = bvNone
      Color = clSkyBlue
      ParentBackground = False
      TabOrder = 1
      Visible = False
      DesignSize = (
        758
        172)
      object Label1: TLabel
        Left = 8
        Top = 10
        Width = 82
        Height = 13
        Caption = 'Warenempf'#228'nger'
        PopupMenu = KundePopupMenu
      end
      object KundenDBGrid: TDBGridPro
        Left = 7
        Top = 29
        Width = 742
        Height = 136
        Anchors = [akLeft, akTop, akRight, akBottom]
        BiDiMode = bdLeftToRight
        DataSource = KundenDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ParentBiDiMode = False
        PopupMenu = KundenGridPopupMenu
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        OnCellClick = KundenDBGridCellClick
        OnExit = KundenDBGridExit
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'MS Sans Serif'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
        RegistryKey = 'Software\Scalabium'
        RegistrySection = 'SMDBGrid'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
        OnColumnSort = KundenDBGridColumnSort
      end
      object AllEmpfRadioButton: TRadioButton
        Left = 285
        Top = 7
        Width = 60
        Height = 17
        Anchors = [akTop, akRight]
        Caption = 'Alle'
        TabOrder = 1
        OnClick = AllEmpfCheckBoxClick
      end
      object LiefEmpfRadioButton: TRadioButton
        Left = 631
        Top = 7
        Width = 120
        Height = 17
        Anchors = [akTop, akRight]
        Caption = 'Nur Lieferanten'
        TabOrder = 2
        OnClick = AllEmpfCheckBoxClick
      end
      object MandEmpfRadioButton: TRadioButton
        Left = 509
        Top = 7
        Width = 120
        Height = 17
        Anchors = [akTop, akRight]
        Caption = 'Nur Mandanten'
        TabOrder = 3
        OnClick = AllEmpfCheckBoxClick
      end
      object AktiveEmpfRadioButton: TRadioButton
        Left = 386
        Top = 7
        Width = 120
        Height = 17
        Anchors = [akTop, akRight]
        Caption = 'Nur aktive Kunden'
        Checked = True
        TabOrder = 4
        TabStop = True
        OnClick = AllEmpfCheckBoxClick
      end
    end
  end
  object FussPanel: TPanel
    Left = 0
    Top = 829
    Width = 758
    Height = 45
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 7
    DesignSize = (
      758
      45)
    object OkButton: TButton
      Left = 549
      Top = 12
      Width = 112
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      ModalResult = 1
      TabOrder = 0
    end
    object AbortButton: TButton
      Left = 674
      Top = 12
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 365
    Width = 758
    Height = 291
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 5
    DesignSize = (
      758
      291)
    object Bevel4: TBevel
      Left = 8
      Top = 166
      Width = 741
      Height = 4
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 777
    end
    object Label5: TLabel
      Left = 8
      Top = 177
      Width = 74
      Height = 13
      Caption = 'Lieferscheintext'
    end
    object Label7: TLabel
      Left = 8
      Top = 203
      Width = 69
      Height = 13
      Caption = 'Komm-Hinweis'
    end
    object Label8: TLabel
      Left = 8
      Top = 259
      Width = 44
      Height = 13
      Caption = 'Spedition'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 286
      Width = 741
      Height = 8
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
    end
    object Bevel2: TBevel
      Left = 8
      Top = 2
      Width = 741
      Height = 4
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 601
    end
    object Label15: TLabel
      Left = 614
      Top = 177
      Width = 30
      Height = 13
      Alignment = taRightJustify
      Anchors = [akTop, akRight]
      Caption = 'LS-Nr.'
      ExplicitLeft = 616
    end
    object Label24: TLabel
      Left = 675
      Top = 259
      Width = 18
      Height = 13
      Alignment = taRightJustify
      Anchors = [akTop, akRight]
      Caption = 'Prio'
    end
    object Label28: TLabel
      Left = 326
      Top = 259
      Width = 68
      Height = 13
      Alignment = taRightJustify
      Caption = 'Sped.-Produkt'
    end
    object Label31: TLabel
      Left = 8
      Top = 229
      Width = 65
      Height = 13
      Caption = 'Pack-Hinweis'
    end
    object SpedComboBox: TComboBoxPro
      Left = 88
      Top = 256
      Width = 214
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 5
      OnChange = SpedComboBoxChange
    end
    object LSHintEdit: TEdit
      Left = 88
      Top = 200
      Width = 503
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 1024
      TabOrder = 3
      Text = 'LSHintEdit'
      OnChange = AufChange
    end
    object LSTextEdit: TEdit
      Left = 88
      Top = 174
      Width = 503
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 1024
      TabOrder = 1
      Text = 'LSTextEdit'
      OnChange = AufChange
    end
    object LSNrEdit: TEdit
      Left = 651
      Top = 174
      Width = 97
      Height = 21
      Anchors = [akTop, akRight]
      MaxLength = 32
      TabOrder = 2
      Text = 'LSNrEdit'
      OnChange = AufChange
    end
    object PrioEdit: TEdit
      Left = 701
      Top = 255
      Width = 28
      Height = 21
      Anchors = [akTop, akRight]
      MaxLength = 3
      TabOrder = 7
      Text = '0'
      OnChange = AufChange
    end
    object PrioUpDown: TIntegerUpDown
      Left = 729
      Top = 255
      Width = 18
      Height = 21
      Anchors = [akTop, akRight]
      Associate = PrioEdit
      Max = 9999
      TabOrder = 8
      Thousands = False
      OnClick = PrioUpDownClick
    end
    object AufDataPageControl: TPageControl
      Left = 7
      Top = 7
      Width = 746
      Height = 154
      ActivePage = AddTabSheet
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      object BaseTabSheet: TTabSheet
        Caption = 'Allgemein'
        DesignSize = (
          738
          126)
        object Label6: TLabel
          Left = 8
          Top = 15
          Width = 46
          Height = 13
          Caption = 'Auftragnr.'
        end
        object Label4: TLabel
          Left = 8
          Top = 43
          Width = 43
          Height = 13
          Caption = 'Bestellnr.'
        end
        object Label16: TLabel
          Left = 8
          Top = 71
          Width = 49
          Height = 13
          Caption = 'Auftragref.'
        end
        object Label14: TLabel
          Left = 207
          Top = 43
          Width = 66
          Height = 13
          Alignment = taRightJustify
          Caption = 'Ladehilfsmittel'
        end
        object Label10: TLabel
          Left = 227
          Top = 15
          Width = 46
          Height = 13
          Alignment = taRightJustify
          Caption = 'Auftragart'
        end
        object Label9: TLabel
          Left = 573
          Top = 71
          Width = 55
          Height = 13
          Alignment = taRightJustify
          Anchors = [akTop, akRight]
          Caption = 'Lieferdatum'
        end
        object Label13: TLabel
          Left = 560
          Top = 43
          Width = 68
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'Versanddatum'
        end
        object Label22: TLabel
          Left = 562
          Top = 15
          Width = 66
          Height = 13
          Anchors = [akTop, akRight]
          Caption = 'Komm.-Datum'
        end
        object Label25: TLabel
          Left = 8
          Top = 97
          Width = 47
          Height = 13
          Alignment = taRightJustify
          Caption = 'Projekt-ID'
        end
        object AufNrEdit: TEdit
          Left = 88
          Top = 12
          Width = 108
          Height = 21
          MaxLength = 32
          TabOrder = 0
          Text = 'AufNrEdit'
          OnChange = AufChange
        end
        object BestNrEdit: TEdit
          Left = 88
          Top = 40
          Width = 108
          Height = 21
          MaxLength = 32
          TabOrder = 1
          Text = 'BestNrEdit'
          OnChange = AufChange
        end
        object AufRefEdit: TEdit
          Left = 88
          Top = 68
          Width = 108
          Height = 21
          MaxLength = 32
          TabOrder = 2
          Text = 'AufRefEdit'
          OnChange = AufChange
        end
        object AufArtComboBox: TComboBoxPro
          Left = 280
          Top = 12
          Width = 266
          Height = 21
          Style = csDropDownList
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 4
          OnChange = AufArtComboBoxChange
        end
        object LTComboBox: TComboBoxPro
          Left = 280
          Top = 40
          Width = 266
          Height = 21
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          ItemHeight = 15
          TabOrder = 5
          OnChange = AufChange
        end
        object KommDateTimePicker: TDateTimePicker
          Left = 633
          Top = 12
          Width = 97
          Height = 21
          Anchors = [akTop, akRight]
          Date = 41221.000000000000000000
          Time = 0.655059525459364500
          ShowCheckbox = True
          TabOrder = 6
          OnChange = VesandDateTimePickerChange
        end
        object VesandDateTimePicker: TDateTimePicker
          Left = 633
          Top = 40
          Width = 97
          Height = 21
          Anchors = [akTop, akRight]
          Date = 41221.000000000000000000
          Time = 0.655059525459364500
          ShowCheckbox = True
          TabOrder = 7
          OnChange = VesandDateTimePickerChange
        end
        object LiefDateTimePicker: TDateTimePicker
          Left = 633
          Top = 68
          Width = 97
          Height = 21
          Anchors = [akTop, akRight]
          Date = 41221.000000000000000000
          Time = 0.655059525459364500
          ShowCheckbox = True
          TabOrder = 8
          OnChange = AufChange
        end
        object AufProjectIDEdit: TEdit
          Left = 88
          Top = 94
          Width = 108
          Height = 21
          MaxLength = 32
          TabOrder = 3
          Text = 'AufProjectIDEdit'
        end
      end
      object AddTabSheet: TTabSheet
        Caption = 'Zus'#228'tzliche Infos'
        ImageIndex = 1
        DesignSize = (
          738
          126)
        object Label21: TLabel
          Left = 528
          Top = 71
          Width = 57
          Height = 13
          Alignment = taRightJustify
          Anchors = [akTop, akRight]
          Caption = 'Kostenstelle'
        end
        object Label20: TLabel
          Left = 539
          Top = 11
          Width = 46
          Height = 13
          Alignment = taRightJustify
          Anchors = [akTop, akRight]
          Caption = 'Komm-Nr.'
        end
        object Label26: TLabel
          Left = 8
          Top = 41
          Width = 62
          Height = 13
          Caption = 'Kennzeichen'
        end
        object Label18: TLabel
          Left = 287
          Top = 41
          Width = 66
          Height = 13
          Alignment = taRightJustify
          Anchors = [akTop, akRight]
          Caption = 'Prozessdatum'
        end
        object Label19: TLabel
          Left = 279
          Top = 11
          Width = 74
          Height = 13
          Alignment = taRightJustify
          Anchors = [akTop, akRight]
          Caption = 'Prozessnummer'
        end
        object Label27: TLabel
          Left = 8
          Top = 11
          Width = 46
          Height = 13
          Caption = 'Incoterms'
        end
        object Label17: TLabel
          Left = 331
          Top = 71
          Width = 22
          Height = 13
          Alignment = taRightJustify
          Anchors = [akTop, akRight]
          Caption = 'Tour'
        end
        object Label23: TLabel
          Left = 8
          Top = 71
          Width = 40
          Height = 13
          Caption = 'Vers. Art'
        end
        object Label29: TLabel
          Left = 8
          Top = 101
          Width = 48
          Height = 13
          Caption = 'Komm,-Art'
        end
        object Label30: TLabel
          Left = 518
          Top = 41
          Width = 67
          Height = 13
          Alignment = taRightJustify
          Anchors = [akTop, akRight]
          Caption = 'Sendungsavis'
        end
        object ConstCenterEdit: TEdit
          Left = 591
          Top = 68
          Width = 122
          Height = 21
          Anchors = [akTop, akRight]
          MaxLength = 32
          TabOrder = 8
          Text = 'ConstCenterEdit'
          OnChange = AufChange
        end
        object AufKommEdit: TEdit
          Left = 591
          Top = 8
          Width = 122
          Height = 21
          Anchors = [akTop, akRight]
          MaxLength = 32
          TabOrder = 7
          Text = 'AufKommEdit'
          OnChange = AufChange
        end
        object IndicaterComboBox: TComboBoxPro
          Left = 80
          Top = 38
          Width = 168
          Height = 22
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 1
          OnChange = AufChange
        end
        object ProcessDateTimePicker: TDateTimePicker
          Left = 360
          Top = 38
          Width = 122
          Height = 21
          Anchors = [akTop, akRight]
          Date = 41221.000000000000000000
          Time = 0.655059525459364500
          ShowCheckbox = True
          TabOrder = 5
          OnChange = AufChange
        end
        object AufProcessEdit: TEdit
          Left = 360
          Top = 8
          Width = 122
          Height = 21
          Anchors = [akTop, akRight]
          MaxLength = 32
          TabOrder = 4
          Text = 'AufProcessEdit'
          OnChange = AufChange
        end
        object IncotermComboBox: TComboBoxPro
          Left = 80
          Top = 8
          Width = 168
          Height = 22
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 0
          OnChange = AufChange
        end
        object AufTourNrEdit: TEdit
          Left = 360
          Top = 68
          Width = 122
          Height = 21
          Anchors = [akTop, akRight]
          MaxLength = 32
          TabOrder = 6
          Text = 'AufTourNrEdit'
          OnChange = AufChange
        end
        object VersArtComboBox: TComboBoxPro
          Left = 80
          Top = 68
          Width = 168
          Height = 22
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 2
          OnChange = AufChange
        end
        object KommArtComboBox: TComboBoxPro
          Left = 81
          Top = 98
          Width = 168
          Height = 22
          Style = csOwnerDrawFixed
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 3
          OnChange = AufChange
        end
        object TallLiftCheckBox: TCheckBox
          Left = 360
          Top = 100
          Width = 183
          Height = 17
          Anchors = [akTop, akRight]
          Caption = 'Anlieferung mit Hebeb'#252'hne'
          TabOrder = 9
        end
        object DESADVCheckBox: TCheckBox
          Left = 592
          Top = 100
          Width = 97
          Height = 17
          Caption = 'DESADV senden'
          TabOrder = 10
        end
        object AvisComboBox: TComboBoxPro
          Left = 592
          Top = 38
          Width = 122
          Height = 22
          Style = csOwnerDrawFixed
          Anchors = [akTop, akRight]
          TabOrder = 11
          OnChange = AufChange
        end
      end
    end
    object SpedProdComboBox: TComboBoxPro
      Left = 400
      Top = 255
      Width = 191
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 6
      OnChange = AufChange
    end
    object PackHintEdit: TEdit
      Left = 88
      Top = 226
      Width = 503
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 1024
      TabOrder = 4
      Text = 'PackHintEdit'
      OnChange = AufChange
    end
  end
  object AufPosPageControl: TNoBoarderPageControl
    Left = 0
    Top = 656
    Width = 758
    Height = 173
    ActivePage = AufPosTabSheet
    Align = alClient
    TabOrder = 6
    object AufPosTabSheet: TTabSheet
      Caption = 'Auslieferpositionen'
      OnShow = AufPosTabSheetShow
      DesignSize = (
        750
        145)
      object AufPosDBGrid: TDBGridPro
        Left = 4
        Top = 2
        Width = 653
        Height = 140
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = PosDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
        RegistryKey = 'Software\CS'
        RegistrySection = 'DBGrids'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object NewPosButton: TButton
        Left = 671
        Top = 3
        Width = 74
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Neu...'
        TabOrder = 1
        OnClick = ChangePosButtonClick
      end
      object EditPosButton: TButton
        Left = 671
        Top = 34
        Width = 74
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Bearbeiten...'
        TabOrder = 2
        OnClick = ChangePosButtonClick
      end
      object DeletePosButton: TButton
        Left = 670
        Top = 117
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'L'#246'schen...'
        TabOrder = 3
        OnClick = DeletePosButtonClick
      end
    end
    object AufLTPosTabSheet: TTabSheet
      Caption = 'Auszuliefernede NVEs'
      ImageIndex = 1
      OnShow = AufLTPosTabSheetShow
      DesignSize = (
        750
        145)
      object AufNVEDBGrid: TDBGridPro
        Left = 4
        Top = 2
        Width = 653
        Height = 140
        Anchors = [akLeft, akTop, akRight, akBottom]
        DataSource = AufNVEDataSource
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
        ReadOnly = True
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'MS Sans Serif'
        TitleFont.Style = []
        Flat = False
        BandsFont.Charset = DEFAULT_CHARSET
        BandsFont.Color = clWindowText
        BandsFont.Height = -11
        BandsFont.Name = 'Tahoma'
        BandsFont.Style = []
        Groupings = <>
        GridStyle.Style = gsCustom
        GridStyle.OddColor = clWindow
        GridStyle.EvenColor = clWindow
        TitleHeight.PixelCount = 24
        FooterColor = clBtnFace
        ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
        RegistryKey = 'Software\CS'
        RegistrySection = 'DBGrids'
        WidthOfIndicator = 11
        DefaultRowHeight = 17
        ScrollBars = ssHorizontal
        ColCount = 2
        RowCount = 2
      end
      object DelNVEButton: TButton
        Left = 670
        Top = 117
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'L'#246'schen...'
        TabOrder = 1
        Visible = False
        ExplicitTop = 138
      end
      object NewNVEButton: TButton
        Left = 671
        Top = 3
        Width = 74
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Neu...'
        TabOrder = 2
        OnClick = ChangePosButtonClick
      end
    end
  end
  object VorlagePanel: TPanel
    Left = 0
    Top = 60
    Width = 758
    Height = 48
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      758
      48)
    object Bevel5: TBevel
      Left = 8
      Top = 45
      Width = 741
      Height = 4
      Anchors = [akLeft, akRight, akBottom]
      Shape = bsTopLine
      ExplicitTop = 38
      ExplicitWidth = 624
    end
    object VorlageButton: TButton
      Left = 88
      Top = 9
      Width = 660
      Height = 25
      Anchors = [akLeft, akTop, akRight]
      Caption = 'Auftragsvorlage ausw'#228'hlen'
      TabOrder = 0
    end
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 216
    Top = 8
  end
  object KundenQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 112
    Top = 704
  end
  object KundenDataSource: TDataSource
    DataSet = KundenQuery
    Left = 152
    Top = 704
  end
  object PosDataSource: TDataSource
    DataSet = PosQuery
    OnDataChange = PosDataSourceDataChange
    Left = 64
    Top = 704
  end
  object PosQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 24
    Top = 704
  end
  object KundenGridPopupMenu: TPopupMenu
    Left = 360
    Top = 240
    object NeuerWarenempfngeranlegen1: TMenuItem
      Caption = 'Neuer Warenempf'#228'nger anlegen...'
      OnClick = NeuerWarenempfngeranlegen1Click
    end
    object Warenempfngerbearbeiten1: TMenuItem
      Caption = 'Warenempf'#228'nger bearbeiten...'
      OnClick = Warenempfngerbearbeiten1Click
    end
    object N1: TMenuItem
      Caption = '-'
    end
  end
  object KundePopupMenu: TPopupMenu
    OnPopup = KundePopupMenuPopup
    Left = 408
    Top = 240
    object EmpfUpdateAdrMenuItem: TMenuItem
      Caption = 'Adresse '#252'bernehmen'
      OnClick = EmpfUpdateAdrMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object EmpfChangeMenuItem: TMenuItem
      Caption = 'Warenempf'#228'nger '#228'ndern'
      OnClick = EmpfChangeMenuItemClick
    end
  end
  object AufNVEADOQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 192
    Top = 704
  end
  object AufNVEDataSource: TDataSource
    DataSet = AufNVEADOQuery
    OnDataChange = KundenDataSourceDataChange
    Left = 232
    Top = 704
  end
end
