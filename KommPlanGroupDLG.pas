unit KommPlanGroupDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, StdCtrls, ComboBoxPro, Grids, DBGrids, SMDBGrid, DBGridPro;

type
  TKommPlanGroupForm = class(TForm)
    KommPlanGroupDBGrid: TDBGridPro;
    NewPlaceButton: TButton;
    EditPlaceButton: TButton;
    Label4: TLabel;
    LagerComboBox: TComboBoxPro;
    DelPlaceButton: TButton;
    Label3: TLabel;
    CloseButton: TButton;
    KommPlanGroupDataSource: TDataSource;
    KommPlanGroupQuery: TADOQuery;
    procedure LagerComboBoxChange(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure EditPlaceButtonClick(Sender: TObject);
    procedure NewPlaceButtonClick(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    procedure Prepare;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, StringUtils, DatenModul, FrontendUtils, DBGridUtilModule, ConfigModul, LVSSecurity, SprachModul,
  EditKommPlanGroupDLG;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 30.04.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TKommPlanGroupForm.Prepare;
begin
  LoadLagerCombobox (LagerComboBox, LVSDatenModul.AktLocationRef);

  if (LagerComboBox.Items.Count = 1) then begin
    LagerComboBox.Enabled := False;
    LagerComboBox.ItemIndex := 0;
  end else if (LVSDatenModul.AktLagerRef = -1) then
    LagerComboBox.ItemIndex := 0
  else begin
    LagerComboBox.ItemIndex := FindComboboxRef(LagerComboBox, LVSDatenModul.AktLagerRef);
    if (LagerComboBox.ItemIndex = -1) then LagerComboBox.ItemIndex := 0;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 30.04.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TKommPlanGroupForm.EditPlaceButtonClick(Sender: TObject);
var
  editfrom : TEditKommPlanGroupForm;
begin
  if (KommPlanGroupQuery.Active and (KommPlanGroupQuery.RecNo > 0)) then begin
    editfrom := TEditKommPlanGroupForm.Create (Self);
    editfrom.Prepare (KommPlanGroupQuery.FieldByName('REF').AsInteger);

    if (editfrom.ShowModal = mrOk) then
      KommPlanGroupDBGrid.Reload;

    editfrom.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 30.04.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TKommPlanGroupForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 30.04.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TKommPlanGroupForm.FormShow(Sender: TObject);
begin
  LVSConfigModul.RestoreFormInfo (Self);

  LagerComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 30.04.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TKommPlanGroupForm.LagerComboBoxChange(Sender: TObject);
begin
  KommPlanGroupQuery.Close;

  if (GetComboBoxRef (LagerComboBox) > 0) then begin
    KommPlanGroupQuery.SQL.Clear;
    KommPlanGroupQuery.SQL.Add ('select * from V_PCD_KOMM_PLAN_GROUP where REF_LAGER=:ref_lager');
    KommPlanGroupQuery.Parameters [0].Value := GetComboBoxRef (LagerComboBox);

    KommPlanGroupQuery.Open;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 30.04.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TKommPlanGroupForm.NewPlaceButtonClick(Sender: TObject);
var
  editfrom : TEditKommPlanGroupForm;
begin
  editfrom := TEditKommPlanGroupForm.Create (Self);
  editfrom.Prepare (-1);

  if (editfrom.ShowModal = mrOk) then
    KommPlanGroupDBGrid.Reload (editfrom.Ref);

  editfrom.Release;
end;

end.
