object DashBoardForm: TDashBoardForm
  Left = 0
  Top = 0
  Caption = 'storelogix Dashboard'
  ClientHeight = 504
  ClientWidth = 1013
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poMainFormCenter
  OnCreate = FormCreate
  OnKeyDown = FormKeyDown
  OnShow = FormShow
  DesignSize = (
    1013
    504)
  PixelsPerInch = 96
  TextHeight = 13
  object Label11: TLabel
    Left = 32
    Top = 40
    Width = 124
    Height = 52
    Caption = 'Label4'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -43
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentFont = False
  end
  object Label12: TLabel
    Left = 272
    Top = 40
    Width = 124
    Height = 52
    Caption = 'Label6'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -43
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentFont = False
  end
  object Label13: TLabel
    Left = 40
    Top = 48
    Width = 124
    Height = 52
    Caption = 'Label4'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -43
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentFont = False
  end
  object Label14: TLabel
    Left = 280
    Top = 48
    Width = 124
    Height = 52
    Caption = 'Label6'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -43
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentFont = False
  end
  object CloseButton: TButton
    Left = 930
    Top = 471
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Schlie'#223'en'
    Default = True
    TabOrder = 0
    OnClick = CloseButtonClick
  end
  object CockpitPageControl: TPageControl
    Left = 8
    Top = 8
    Width = 997
    Height = 457
    ActivePage = DashboardTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 1
    OnChange = CockpitPageControlChange
    object TabSheet1: TTabSheet
      Caption = 'Kennzahlen'
      TabVisible = False
      OnShow = TabSheet1Show
      ExplicitLeft = 0
      ExplicitTop = 0
      ExplicitWidth = 0
      ExplicitHeight = 0
      object GroupBox5: TGroupBox
        Left = 8
        Top = 16
        Width = 971
        Height = 217
        Caption = 'Auftragsauswertung'
        TabOrder = 0
        object AufStatistikListView: TListView
          Left = 16
          Top = 24
          Width = 945
          Height = 177
          Columns = <
            item
              Caption = 'Heute'
            end
            item
              Caption = 'Gestern'
            end
            item
              Caption = 'aktueller Monat'
            end
            item
              Caption = 'letzten Monat'
            end>
          ReadOnly = True
          RowSelect = True
          TabOrder = 0
          ViewStyle = vsReport
        end
      end
    end
    object ActualTabSheet: TTabSheet
      Caption = 'Aktuell'
      ImageIndex = 1
      TabVisible = False
      ExplicitLeft = 0
      ExplicitTop = 28
      ExplicitWidth = 0
      ExplicitHeight = 0
      DesignSize = (
        989
        429)
      object GroupBox1: TGroupBox
        Left = 8
        Top = 0
        Width = 971
        Height = 105
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Auftr'#228'ge'
        TabOrder = 0
        object Label1: TLabel
          Left = 16
          Top = 16
          Width = 85
          Height = 13
          Caption = 'Auftr'#228'ge gesamt:'
        end
        object Label2: TLabel
          Left = 408
          Top = 16
          Width = 73
          Height = 13
          Caption = 'VPEs gesammt:'
        end
        object Label3: TLabel
          Left = 16
          Top = 43
          Width = 103
          Height = 13
          Caption = 'Noch offen Auftr'#228'ge:'
        end
        object AufAnzOpenLabel: TLabel
          Left = 128
          Top = 43
          Width = 86
          Height = 13
          Caption = 'AufAnzOpenLabel'
        end
        object Bevel1: TBevel
          Left = 3
          Top = 35
          Width = 991
          Height = 6
          Shape = bsTopLine
        end
        object AufAnzLabel: TLabel
          Left = 107
          Top = 16
          Width = 60
          Height = 13
          Caption = 'AufAnzLabel'
        end
        object AufVPELabel: TLabel
          Left = 487
          Top = 16
          Width = 60
          Height = 13
          Caption = 'AufVPELabel'
        end
        object Label7: TLabel
          Left = 200
          Top = 16
          Width = 99
          Height = 13
          Caption = 'Positionen gesammt:'
        end
        object AufPosAnzLabel: TLabel
          Left = 303
          Top = 16
          Width = 77
          Height = 13
          Caption = 'AufPosAnzLabel'
        end
        object Label5: TLabel
          Left = 240
          Top = 43
          Width = 176
          Height = 13
          Caption = 'Offene Auftr'#228'ge mit Bestandsfehler:'
        end
        object AufBesOpenLabel: TLabel
          Left = 424
          Top = 43
          Width = 85
          Height = 13
          Caption = 'AufBesOpenLabel'
        end
        object Label10: TLabel
          Left = 528
          Top = 43
          Width = 191
          Height = 13
          Caption = 'Abgeschlossene Auftr'#228'ge mit Fehlware:'
        end
        object AufFehlwareLabel: TLabel
          Left = 728
          Top = 43
          Width = 86
          Height = 13
          Caption = 'AufFehlwareLabel'
        end
      end
      object GroupBox2: TGroupBox
        Left = 8
        Top = 120
        Width = 971
        Height = 105
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Kommissionierung'
        TabOrder = 1
      end
      object GroupBox3: TGroupBox
        Left = 8
        Top = 247
        Width = 971
        Height = 105
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Umlagerungen und Nachschub'
        TabOrder = 2
      end
    end
    object DashboardTabSheet: TTabSheet
      Caption = 'Dashboard'
      ImageIndex = 2
      OnShow = DashboardTabSheetShow
      ExplicitLeft = 0
      ExplicitTop = 28
      ExplicitWidth = 0
      ExplicitHeight = 0
      object Label4: TLabel
        Left = 32
        Top = 100
        Width = 122
        Height = 39
        Caption = 'Auftr'#228'ge'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -32
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object DashOrdersLabel: TLabel
        Left = 237
        Top = 89
        Width = 324
        Height = 52
        Alignment = taRightJustify
        Caption = 'DashOrdersLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -43
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object Label8: TLabel
        Left = 32
        Top = 160
        Width = 292
        Height = 39
        Caption = 'Kommissionierungen'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -32
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object DashKommsLabel: TLabel
        Left = 226
        Top = 149
        Width = 335
        Height = 52
        Alignment = taRightJustify
        Caption = 'DashKommsLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -43
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object Label15: TLabel
        Left = 32
        Top = 315
        Width = 155
        Height = 39
        Caption = 'Transporte'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -32
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object DashTransLabel: TLabel
        Left = 258
        Top = 301
        Width = 303
        Height = 52
        Alignment = taRightJustify
        Caption = 'DashTransLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -43
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object Label17: TLabel
        Left = 32
        Top = 375
        Width = 171
        Height = 39
        Caption = 'Nachsch'#252'be'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -32
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object DashNachschubLabel: TLabel
        Left = 158
        Top = 364
        Width = 403
        Height = 52
        Alignment = taRightJustify
        Caption = 'DashNachschubLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -43
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object DashTimeLabel: TLabel
        Left = 904
        Top = 408
        Width = 71
        Height = 13
        Alignment = taRightJustify
        Caption = 'DashTimeLabel'
      end
      object DashOrderVPELabel: TLabel
        Left = 466
        Top = 89
        Width = 324
        Height = 52
        Alignment = taRightJustify
        Caption = 'DashOrdersLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -43
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object DashKommVPELabel: TLabel
        Left = 400
        Top = 149
        Width = 390
        Height = 52
        Alignment = taRightJustify
        Caption = 'DashKommVPELabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -43
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object DashTranVPELabel: TLabel
        Left = 466
        Top = 301
        Width = 324
        Height = 52
        Alignment = taRightJustify
        Caption = 'DashOrdersLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -43
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object DashNachVPELabel: TLabel
        Left = 466
        Top = 364
        Width = 324
        Height = 52
        Alignment = taRightJustify
        Caption = 'DashOrdersLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -43
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object Label6: TLabel
        Left = 472
        Top = 48
        Width = 52
        Height = 25
        Caption = 'Offen'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -21
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object Label9: TLabel
        Left = 712
        Top = 48
        Width = 46
        Height = 25
        Caption = 'VPEs'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -21
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object Label16: TLabel
        Left = 32
        Top = 220
        Width = 213
        Height = 39
        Caption = 'Warenausgang'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -32
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object DashWAsLabel: TLabel
        Left = 226
        Top = 209
        Width = 335
        Height = 52
        Alignment = taRightJustify
        Caption = 'DashKommsLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -43
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object DashWAVPELabel: TLabel
        Left = 466
        Top = 209
        Width = 324
        Height = 52
        Alignment = taRightJustify
        Caption = 'DashOrdersLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -43
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
    end
  end
  object UpdateTimer: TTimer
    Interval = 10000
    OnTimer = UpdateTimerTimer
    Left = 8
    Top = 472
  end
end
