clone:
  depth: full

pipelines:
  default:
    - step:
        runs-on:
          - self.hosted
          - windows
        name: "Build and Test"
        script:
          - touch aVersionInfo.rc
          - msbuild.exe "SLManager.dproj" /t:Build /p:config=Release /p:Platform=Win32
          - madExceptPatch.exe Win32\Release\Exe\SLManager.exe SLManager.mes /enabled=1
          - (Get-ChildItem Win32\Release\Exe\SLManager.exe).Length / 1KB

  branches:
    master:
      - step:
          runs-on:
            - self.hosted
            - windows
          name: "Build and save artifacts"
          script:
            - touch aVersionInfo.rc
            - msbuild.exe "SLManager.dproj" /t:Build /p:config=Release /p:Platform=Win32
            - madExceptPatch.exe Win32\Release\Exe\SLManager.exe SLManager.mes /enabled=1
            - (Get-ChildItem Win32\Release\Exe\SLManager.exe).Length / 1KB
            - $gitversion = git describe | Out-String
            - $gitversion = $gitversion.Substring(0,$gitversion.Length-2)
            - mkdir C:\Projekte\StorelogixDistribute\$gitversion
            - copy Win32\Release\Exe\SLManager.exe C:\Projekte\StorelogixDistribute\$gitversion\SLManager.exe
            - copy lvsmain.xml C:\Projekte\StorelogixDistribute\$gitversion\lvsmain.xml
            - copy Win32\Release\Exe\SLManager.exe C:\Projekte\StorelogixDistribute\SLManager.exe
            - copy lvsmain.xml C:\Projekte\StorelogixDistribute\lvsmain.xml
            - copy lvsmain.xml Win32\Release\Exe\lvsmain.xml
            - powershell -Command "Get-ChildItem -Path 'Win32\Release\Exe\*.xml', 'Win32\Release\Exe\*.exe' | Compress-Archive -DestinationPath 'SLManager_lvsmain.zip'"
            - $versionDetailsFile = "C:\Projekte\StorelogixDistribute\$gitversion\VersionDetails.md"
            - $gitTagDetails = git show $gitversion --quiet | Out-String # get tag details
            - Set-Content -Path $versionDetailsFile -Value "## Version Details"
            - Add-Content  -Path $versionDetailsFile -Value $gitTagDetails
            - copy $versionDetailsFile C:\Projekte\StorelogixDistribute\VersionDetails.md # copy VersionDetails.md to root folder
          artifacts:
            - "SLManager_lvsmain.zip"
