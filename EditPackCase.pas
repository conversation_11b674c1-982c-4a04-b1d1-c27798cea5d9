unit EditPackCase;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls;

type
  TEditPackCaseForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    DescEdit: TEdit;
    NrEdit: TEdit;
    IDEdit: TEdit;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Bevel1: TBevel;
    procedure FormCreate(Sender: TObject);
  private
    fRef : Integer;
  public
    property Ref : Integer read fRef write fRef;
  end;

implementation

uses SprachModul;

{$R *.dfm}

procedure TEditPackCaseForm.FormCreate(Sender: TObject);
begin
  fRef := -1;

  DescEdit.Text := '';
  NrEdit.Text := '';
  IDEdit.Text := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
  {$endif}
end;

end.
