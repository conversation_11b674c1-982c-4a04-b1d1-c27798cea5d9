object WEPosForm: TWEPosForm
  Left = 367
  Top = 266
  Width = 851
  Height = 537
  BorderIcons = [biSystemMenu]
  Caption = 'WEPosForm'
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  OldCreateOrder = False
  Position = poMainFormCenter
  OnHide = FormHide
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 843
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    object Label1: TLabel
      Left = 8
      Top = 24
      Width = 117
      Height = 13
      Caption = 'Vereinnahmte Positionen'
    end
  end
  object Panel2: TPanel
    Left = 0
    Top = 254
    Width = 843
    Height = 249
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      843
      249)
    object HACCPLabel: TLabel
      Left = 408
      Top = 40
      Width = 3
      Height = 13
    end
    object Label2: TLabel
      Left = 8
      Top = 16
      Width = 88
      Height = 13
      Caption = 'HACCP-Pr'#252'fungen'
    end
    object Button1: TButton
      Left = 760
      Top = 216
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      BiDiMode = bdLeftToRight
      Cancel = True
      Caption = 'OK'
      Default = True
      ModalResult = 1
      ParentBiDiMode = False
      TabOrder = 0
    end
    object HACCPDBGrid: TDBGridPro
      Left = 8
      Top = 32
      Width = 385
      Height = 177
      DataSource = DataSource2
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
      ReadOnly = True
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'MS Sans Serif'
      TitleFont.Style = []
      Flat = False
      Groupings = <>
      GridStyle.Style = gsCustom
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
    object GroupBox1: TGroupBox
      Left = 408
      Top = 88
      Width = 425
      Height = 121
      Anchors = [akLeft, akTop, akRight]
      Caption = 'HACCP-Ergebnisse'
      TabOrder = 2
      DesignSize = (
        425
        121)
      object HACCPMemo: TMemo
        Left = 8
        Top = 16
        Width = 409
        Height = 97
        Anchors = [akLeft, akTop, akRight, akBottom]
        Enabled = False
        Lines.Strings = (
          'HACCPMemo')
        ReadOnly = True
        TabOrder = 0
      end
      object HACCPEdit: TEdit
        Left = 8
        Top = 16
        Width = 409
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        Enabled = False
        ReadOnly = True
        TabOrder = 1
        Text = 'HACCPEdit'
      end
      object HACCPCheckBox: TCheckBox
        Left = 8
        Top = 16
        Width = 97
        Height = 17
        Caption = 'Gepr'#252'ft'
        Enabled = False
        TabOrder = 2
      end
      object HACCPRadioGroup: TRadioGroup
        Left = 8
        Top = 16
        Width = 409
        Height = 97
        Anchors = [akLeft, akTop, akRight, akBottom]
        Enabled = False
        ItemIndex = 1
        Items.Strings = (
          'Ja'
          'Nein')
        TabOrder = 3
      end
    end
  end
  object DBGridPro1: TDBGridPro
    Left = 0
    Top = 41
    Width = 843
    Height = 213
    Align = alClient
    DataSource = DataSource1
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 2
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'MS Sans Serif'
    TitleFont.Style = []
    Flat = False
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 728
    Top = 8
  end
  object DataSource1: TDataSource
    DataSet = ADOQuery1
    Left = 16
    Top = 88
  end
  object CompTranslateForm1: TCompTranslateForm
    Master = LVSSprachModul.LVSCompTranslate
    Left = 632
    Top = 24
  end
  object ADOQuery2: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 760
    Top = 8
  end
  object DataSource2: TDataSource
    DataSet = ADOQuery2
    OnDataChange = DataSource2DataChange
    Left = 16
    Top = 326
  end
end
