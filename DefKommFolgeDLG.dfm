object DefKommFolgeForm: TDefKommFolgeForm
  Left = 544
  Top = 257
  BorderStyle = bsDialog
  Caption = 'Kommissionierfolge neue festlegen'
  ClientHeight = 473
  ClientWidth = 369
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  DesignSize = (
    369
    473)
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 56
    Width = 62
    Height = 13
    Caption = 'Lagerbereich'
  end
  object Label2: TLabel
    Left = 8
    Top = 112
    Width = 28
    Height = 13
    Caption = 'Regal'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 100
    Width = 351
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 322
  end
  object Bevel4: TBevel
    Left = 8
    Top = 244
    Width = 351
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 322
  end
  object Label9: TLabel
    Left = 128
    Top = 248
    Width = 81
    Height = 13
    Caption = 'Max. Teilfachanz'
  end
  object Bevel6: TBevel
    Left = 8
    Top = 47
    Width = 351
    Height = 10
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 322
  end
  object Label11: TLabel
    Left = 8
    Top = 3
    Width = 27
    Height = 13
    Caption = 'Lager'
  end
  object Label12: TLabel
    Left = 8
    Top = 248
    Width = 42
    Height = 13
    Caption = 'Startwert'
  end
  object Bevel2: TBevel
    Left = 8
    Top = 428
    Width = 351
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitWidth = 322
  end
  object LBComboBox: TComboBoxPro
    Left = 8
    Top = 72
    Width = 351
    Height = 22
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 80
    ItemHeight = 16
    TabOrder = 1
    OnChange = LBComboBoxChange
  end
  object StepEdit: TEdit
    Left = 128
    Top = 264
    Width = 89
    Height = 21
    TabOrder = 7
    Text = 'StepEdit'
    OnKeyPress = NumEditKeyPress
  end
  object OkButton: TButton
    Left = 195
    Top = 439
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 10
  end
  object AbortButton: TButton
    Left = 283
    Top = 439
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 11
  end
  object LagerComboBox: TComboBoxPro
    Left = 8
    Top = 19
    Width = 351
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ColWidth = 80
    ItemHeight = 15
    TabOrder = 0
    OnChange = LagerComboBoxChange
  end
  object ReiheComboBox: TComboBox
    Left = 8
    Top = 128
    Width = 350
    Height = 21
    Style = csDropDownList
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 13
    TabOrder = 2
  end
  object StartEdit: TEdit
    Left = 8
    Top = 264
    Width = 89
    Height = 21
    TabOrder = 6
    Text = 'StartEdit'
    OnKeyPress = NumEditKeyPress
  end
  object EbeneGroupBox: TGroupBox
    Left = 128
    Top = 168
    Width = 113
    Height = 65
    Caption = 'Ebene'
    TabOrder = 4
    object Label6: TLabel
      Left = 64
      Top = 20
      Width = 14
      Height = 13
      Caption = 'Bis'
    end
    object Label5: TLabel
      Left = 8
      Top = 20
      Width = 19
      Height = 13
      Caption = 'Von'
    end
    object EbeneBisEdit: TEdit
      Left = 64
      Top = 36
      Width = 41
      Height = 21
      TabOrder = 1
      Text = 'Edit3'
      OnExit = NrEditExit
      OnKeyPress = NumEditKeyPress
    end
    object EbeneVonEdit: TEdit
      Left = 8
      Top = 36
      Width = 41
      Height = 21
      TabOrder = 0
      Text = 'Edit3'
      OnExit = NrEditExit
      OnKeyPress = NumEditKeyPress
    end
  end
  object FachGroupBox: TGroupBox
    Left = 8
    Top = 168
    Width = 113
    Height = 65
    Caption = 'Feld'
    TabOrder = 3
    object Label3: TLabel
      Left = 8
      Top = 20
      Width = 19
      Height = 13
      Caption = 'Von'
    end
    object Label4: TLabel
      Left = 64
      Top = 20
      Width = 14
      Height = 13
      Caption = 'Bis'
    end
    object PlatzVonEdit: TEdit
      Left = 8
      Top = 36
      Width = 41
      Height = 21
      TabOrder = 0
      Text = 'PlatzVonEdit'
      OnExit = NrEditExit
      OnKeyPress = NumEditKeyPress
    end
    object PlatzBisEdit: TEdit
      Left = 64
      Top = 36
      Width = 41
      Height = 21
      TabOrder = 1
      Text = 'PlatzBisEdit'
      OnExit = NrEditExit
      OnKeyPress = NumEditKeyPress
    end
  end
  object SetLPNrCheckBox: TCheckBox
    Left = 8
    Top = 400
    Width = 161
    Height = 17
    Caption = 'Lagerplatz-Nummer setzen'
    TabOrder = 9
    OnClick = SetLPNrCheckBoxClick
  end
  object GroupBox1: TGroupBox
    Left = 8
    Top = 296
    Width = 353
    Height = 97
    Caption = 'Art der Nummerierung'
    TabOrder = 8
    object Panel1: TPanel
      Left = 2
      Top = 15
      Width = 349
      Height = 24
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 0
      object Bevel3: TBevel
        Left = 0
        Top = 19
        Width = 349
        Height = 5
        Align = alBottom
        Shape = bsBottomLine
        Visible = False
        ExplicitWidth = 317
      end
      object AufRadioButton: TRadioButton
        Left = 8
        Top = 1
        Width = 113
        Height = 17
        Caption = 'Aufsteigend'
        Checked = True
        TabOrder = 0
        TabStop = True
      end
      object AbRadioButton: TRadioButton
        Left = 150
        Top = 1
        Width = 113
        Height = 17
        Caption = 'Absteigend'
        TabOrder = 1
      end
    end
    object Panel2: TPanel
      Left = 2
      Top = 39
      Width = 349
      Height = 28
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 1
      Visible = False
      object Bevel5: TBevel
        Left = 0
        Top = 23
        Width = 349
        Height = 5
        Align = alBottom
        Shape = bsBottomLine
        Visible = False
        ExplicitWidth = 317
      end
      object DownUpRadioButton: TRadioButton
        Left = 8
        Top = 4
        Width = 137
        Height = 17
        Caption = 'Von Unten nach oben'
        Checked = True
        TabOrder = 0
        TabStop = True
        Visible = False
      end
      object UpDownRadioButton: TRadioButton
        Left = 150
        Top = 4
        Width = 170
        Height = 17
        Caption = 'Von Oben nach unten'
        TabOrder = 1
        Visible = False
      end
    end
    object Panel3: TPanel
      Left = 2
      Top = 67
      Width = 349
      Height = 24
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 2
      object ProFachRadioButton: TRadioButton
        Left = 8
        Top = 4
        Width = 113
        Height = 17
        Caption = 'Ebene, Fach'
        Checked = True
        TabOrder = 0
        TabStop = True
      end
      object ProEbeneRadioButton: TRadioButton
        Left = 150
        Top = 4
        Width = 113
        Height = 17
        Caption = 'Fach, Ebene'
        TabOrder = 1
      end
    end
  end
  object TiefeGroupBox: TGroupBox
    Left = 248
    Top = 168
    Width = 113
    Height = 65
    Caption = 'Tiefe'
    TabOrder = 5
    Visible = False
    object Label7: TLabel
      Left = 68
      Top = 17
      Width = 14
      Height = 13
      Caption = 'Bis'
    end
    object Label8: TLabel
      Left = 8
      Top = 20
      Width = 19
      Height = 13
      Caption = 'Von'
    end
    object TiefeBisEdit: TEdit
      Left = 64
      Top = 36
      Width = 41
      Height = 21
      TabOrder = 1
      Text = 'Edit3'
      OnExit = NrEditExit
      OnKeyPress = NumEditKeyPress
    end
    object TiefeVonEdit: TEdit
      Left = 8
      Top = 36
      Width = 41
      Height = 21
      TabOrder = 0
      Text = 'Edit3'
      OnExit = NrEditExit
      OnKeyPress = NumEditKeyPress
    end
  end
end
