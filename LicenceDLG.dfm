object LicenceForm: TLicenceForm
  Left = 0
  Top = 0
  Caption = 'Tageslizenzen verwalten'
  ClientHeight = 486
  ClientWidth = 748
  Color = clBtnFace
  Constraints.MinHeight = 524
  Constraints.MinWidth = 720
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  TextHeight = 15
  object TopPanel: TPanel
    Left = 0
    Top = 0
    Width = 748
    Height = 241
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      748
      241)
    object GroupBoxSelection: TGroupBox
      Left = 18
      Top = 176
      Width = 702
      Height = 60
      Anchors = [akLeft, akTop, akRight]
      Caption = 'Auswahl'
      TabOrder = 0
      DesignSize = (
        702
        60)
      object Label1: TLabel
        Left = 348
        Top = 27
        Width = 122
        Height = 15
        Alignment = taRightJustify
        Anchors = [akTop, akRight, akBottom]
        AutoSize = False
        Caption = 'Buchungsdatum: Von'
        ExplicitLeft = 312
      end
      object Label2: TLabel
        Left = 571
        Top = 27
        Width = 15
        Height = 15
        Alignment = taRightJustify
        Anchors = [akTop, akRight, akBottom]
        Caption = 'Bis'
        ExplicitLeft = 532
      end
      object cbOnlyActiveLicences: TCheckBox
        Left = 16
        Top = 24
        Width = 257
        Height = 17
        Caption = 'nur aktive Lizenzen anzeigen (< 24 Stunden)'
        TabOrder = 0
        OnClick = cbOnlyActiveLicencesClick
      end
      object FromDatePicker: TDateTimePicker
        Left = 476
        Top = 23
        Width = 89
        Height = 23
        Anchors = [akTop, akRight, akBottom]
        Date = 45245.000000000000000000
        Time = 0.390540694446826800
        TabOrder = 1
        OnChange = DatePickerChange
      end
      object ToDatePicker: TDateTimePicker
        Left = 592
        Top = 23
        Width = 89
        Height = 23
        Anchors = [akTop, akRight, akBottom]
        Date = 45245.000000000000000000
        Time = 0.390540694446826800
        TabOrder = 2
        OnChange = DatePickerChange
      end
    end
    object FrameGroupBox: TGroupBox
      Left = 18
      Top = 13
      Width = 702
      Height = 100
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 1
      DesignSize = (
        702
        100)
      object Label3: TLabel
        Left = 14
        Top = 6
        Width = 30
        Height = 15
        Caption = 'Firma'
      end
      object Label5: TLabel
        Left = 14
        Top = 30
        Width = 75
        Height = 15
        Caption = 'Niederlassung'
        Visible = False
      end
      object Label4: TLabel
        Left = 14
        Top = 54
        Width = 267
        Height = 15
        Caption = 'Anzahl gebuchter Lizenzen im gew'#228'hlten Zeitraum'
      end
      object MaxLicLabel: TLabel
        Left = 14
        Top = 75
        Width = 329
        Height = 15
        Caption = 'Maximal buchbare Tageslizenzen (30% von xx Gesamtlizenzen)'
      end
      object FirmaLabel: TLabel
        Left = 411
        Top = 7
        Width = 58
        Height = 15
        Caption = 'FirmaLabel'
      end
      object LocLabel: TLabel
        Left = 411
        Top = 31
        Width = 47
        Height = 15
        Caption = 'LocLabel'
        Visible = False
      end
      object LicCountLabel: TLabel
        Left = 411
        Top = 55
        Width = 76
        Height = 15
        Caption = 'LicCountLabel'
      end
      object DayLicCountLabel: TLabel
        Left = 411
        Top = 76
        Width = 96
        Height = 15
        Caption = 'DayLicCountLabel'
      end
      object SessionFirmaComboBox: TComboBox
        Left = 501
        Top = 4
        Width = 198
        Height = 23
        Style = csDropDownList
        Anchors = [akTop, akRight]
        TabOrder = 0
        Visible = False
        OnChange = SessionFirmaComboBoxChange
      end
    end
    object CreateDayLicButton: TButton
      Left = 18
      Top = 119
      Width = 239
      Height = 51
      Caption = 'Tageslizenz buchen...'
      TabOrder = 2
      OnClick = CreateDayLicButtonClick
    end
  end
  object LicDBGrid: TDBGridPro
    Left = 0
    Top = 241
    Width = 748
    Height = 204
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Align = alClient
    DataSource = LicOraDataSource
    Options = [dgEditing, dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = LicDBGridPopupMenu
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -12
    TitleFont.Name = 'Segoe UI'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -12
    BandsFont.Name = 'Segoe UI'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsNormal
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 19
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object BottomPanel: TPanel
    Left = 0
    Top = 445
    Width = 748
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      748
      41)
    object CloseButton: TButton
      Left = 661
      Top = 8
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 0
    end
  end
  object LicQuery: TSmartQuery
    Session = LVSDatenModul.OraMainSession
    ReadOnly = True
    Left = 304
    Top = 160
  end
  object LicOraDataSource: TOraDataSource
    DataSet = LicQuery
    Left = 432
    Top = 128
  end
  object LicDBGridPopupMenu: TPopupMenu
    OnPopup = LicDBGridPopupMenuPopup
    Left = 200
    Top = 208
    object CreateDayLicMenuItem: TMenuItem
      Caption = 'Tageslizenz buchen...'
      OnClick = CreateDayLicMenuItemClick
    end
  end
end
