object EditWarenEmpfForm: TEditWarenEmpfForm
  Left = 192
  Top = 135
  Align = alCustom
  BorderIcons = [biSystemMenu]
  BorderStyle = bsDialog
  Caption = 'Verwalten Warenempf'#228'nger'
  ClientHeight = 493
  ClientWidth = 621
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    621
    493)
  TextHeight = 13
  object OKButton: TButton
    Left = 451
    Top = 460
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'OK'
    Default = True
    ModalResult = 1
    TabOrder = 0
  end
  object AbbrechenButton: TButton
    Left = 538
    Top = 460
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 2
    TabOrder = 2
  end
  object WEPageControl: TPageControl
    Left = 8
    Top = 8
    Width = 605
    Height = 444
    ActivePage = TabSheet1
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 1
    object TabSheet1: TTabSheet
      Caption = 'Allgemeines'
      DesignSize = (
        597
        416)
      object KundenNrLabel: TLabel
        Left = 8
        Top = 52
        Width = 79
        Height = 13
        Caption = 'Kunden Nummer'
      end
      object WENameLabel: TLabel
        Left = 8
        Top = 94
        Width = 28
        Height = 13
        Caption = 'Name'
      end
      object ILNLabel: TLabel
        Left = 315
        Top = 52
        Width = 17
        Height = 13
        Caption = 'ILN'
      end
      object Label4: TLabel
        Left = 8
        Top = 136
        Width = 40
        Height = 13
        Caption = 'Sprache'
      end
      object Label5: TLabel
        Left = 315
        Top = 136
        Width = 44
        Height = 13
        Caption = 'W'#228'hrung'
      end
      object Label11: TLabel
        Left = 8
        Top = 8
        Width = 42
        Height = 13
        Caption = 'Mandant'
      end
      object KundenNrEdit: TEdit
        Left = 8
        Top = 68
        Width = 217
        Height = 21
        MaxLength = 32
        TabOrder = 1
        OnKeyPress = KundenNrEditKeyPress
      end
      object WENameEdit: TEdit
        Left = 8
        Top = 110
        Width = 581
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 3
      end
      object ILNEdit: TEdit
        Left = 315
        Top = 68
        Width = 274
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 13
        TabOrder = 2
        OnKeyPress = ILNEditKeyPress
      end
      object AdresseGroupBox1: TGroupBox
        Left = 4
        Top = 176
        Width = 590
        Height = 237
        Caption = 'Adresse'
        TabOrder = 6
        DesignSize = (
          590
          237)
        object StrasseLabel: TLabel
          Left = 8
          Top = 148
          Width = 80
          Height = 13
          Caption = 'Strasse, Haus Nr'
        end
        object LandLabel: TLabel
          Left = 455
          Top = 188
          Width = 24
          Height = 13
          Caption = 'Land'
        end
        object OrtLabel: TLabel
          Left = 99
          Top = 188
          Width = 14
          Height = 13
          Caption = 'Ort'
        end
        object PLZLabel: TLabel
          Left = 8
          Top = 188
          Width = 53
          Height = 13
          Caption = 'Postleitzahl'
        end
        object Label1: TLabel
          Left = 8
          Top = 108
          Width = 28
          Height = 13
          Caption = 'Name'
        end
        object Bevel1: TBevel
          Left = 6
          Top = 43
          Width = 578
          Height = 4
          Shape = bsTopLine
        end
        object Label2: TLabel
          Left = 311
          Top = 148
          Width = 70
          Height = 13
          Caption = 'Strasse Zusatz'
        end
        object Label3: TLabel
          Left = 8
          Top = 50
          Width = 39
          Height = 13
          Caption = 'Nummer'
        end
        object Label6: TLabel
          Left = 312
          Top = 50
          Width = 87
          Height = 13
          Caption = 'Distanz zum Lager'
        end
        object km: TLabel
          Left = 405
          Top = 69
          Width = 14
          Height = 13
          Caption = 'km'
        end
        object Label8: TLabel
          Left = 162
          Top = 50
          Width = 52
          Height = 13
          Caption = 'Adress ILN'
        end
        object Bevel2: TBevel
          Left = 6
          Top = 99
          Width = 578
          Height = 4
          Shape = bsTopLine
        end
        object StrasseEdit: TEdit
          Left = 8
          Top = 164
          Width = 281
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 6
          OnChange = AdrEditChange
        end
        object OrtEdit: TEdit
          Left = 99
          Top = 204
          Width = 350
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 9
          OnChange = AdrEditChange
        end
        object LandComboBox: TComboBox
          Left = 455
          Top = 204
          Width = 127
          Height = 21
          Style = csDropDownList
          MaxLength = 2
          TabOrder = 10
          OnChange = AdrEditChange
        end
        object PLZEdit: TEdit
          Left = 8
          Top = 204
          Width = 85
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 8
          TabOrder = 8
          OnChange = AdrEditChange
        end
        object AdrArtComboBox: TComboBox
          Left = 8
          Top = 16
          Width = 574
          Height = 21
          Style = csDropDownList
          PopupMenu = AdrArtComboBoxPopupMenu
          TabOrder = 0
          OnChange = AdrArtComboBoxChange
          Items.Strings = (
            'Kunden-Anschrift')
        end
        object Name1Edit: TEdit
          Left = 8
          Top = 124
          Width = 281
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 4
          Text = 'Name1Edit'
          OnChange = AdrEditChange
        end
        object Name2Edit: TEdit
          Left = 312
          Top = 124
          Width = 270
          Height = 21
          MaxLength = 64
          TabOrder = 5
          Text = 'Name2Edit'
          OnChange = AdrEditChange
        end
        object Strasse2Edit: TEdit
          Left = 311
          Top = 164
          Width = 271
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 7
          OnChange = AdrEditChange
        end
        object NummerEdit: TEdit
          Left = 8
          Top = 66
          Width = 129
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 32
          TabOrder = 1
          Text = 'NummerEdit'
          OnChange = AdrEditChange
        end
        object DistanceEdit: TEdit
          Left = 312
          Top = 66
          Width = 87
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 3
          Text = 'DistanceEdit'
          OnChange = AdrEditChange
        end
        object AdrILNEdit: TEdit
          Left = 162
          Top = 66
          Width = 129
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 16
          TabOrder = 2
          Text = 'AdrILNEdit'
          OnChange = AdrEditChange
        end
      end
      object SprachComboBox: TComboBoxPro
        Left = 8
        Top = 152
        Width = 217
        Height = 21
        TabOrder = 4
        Text = 'SprachComboBox'
      end
      object CurrencyComboBox: TComboBoxPro
        Left = 315
        Top = 152
        Width = 274
        Height = 21
        TabOrder = 5
        Text = 'CurrencyComboBox'
      end
      object MandantComboBox: TComboBoxPro
        Left = 8
        Top = 24
        Width = 581
        Height = 22
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        ColWidth = 120
        TabOrder = 0
      end
    end
    object DeliveryTabSheet: TTabSheet
      Caption = 'Lieferung'
      ImageIndex = 1
      DesignSize = (
        597
        416)
      object LTextLabel: TLabel
        Left = 8
        Top = 320
        Width = 74
        Height = 13
        Caption = 'Lieferscheintext'
      end
      object KTextLabel: TLabel
        Left = 8
        Top = 364
        Width = 78
        Height = 13
        Caption = 'Kommisioniertext'
      end
      object AnzKopLabel: TLabel
        Left = 8
        Top = 224
        Width = 124
        Height = 13
        Caption = 'Anzahl Lieferscheinkopien'
      end
      object WarenannhmezeitenGroupBox: TGroupBox
        Left = 8
        Top = 142
        Width = 257
        Height = 77
        Caption = 'Warenannahmezeiten'
        TabOrder = 1
        DesignSize = (
          257
          77)
        object AnnZBisLabel: TLabel
          Left = 136
          Top = 20
          Width = 59
          Height = 13
          Caption = 'bis (HH:MM)'
        end
        object AnnZVonLabel: TLabel
          Left = 8
          Top = 20
          Width = 64
          Height = 13
          Caption = 'von (HH:MM)'
        end
        object ZeitVonEdit: TEdit
          Left = 8
          Top = 40
          Width = 100
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 5
          TabOrder = 0
          OnEnter = ZeitVonEditEnter
          OnExit = ZeitVonEditExit
          OnKeyDown = ZeitVonEditKeyDown
          OnKeyPress = ZeitVonEditKeyPress
        end
        object ZeitBisEdit: TEdit
          Left = 136
          Top = 40
          Width = 100
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 128
          TabOrder = 1
          OnEnter = ZeitBisEditEnter
          OnExit = ZeitBisEditExit
          OnKeyDown = ZeitBisEditKeyDown
          OnKeyPress = ZeitBisEditKeyPress
        end
      end
      object LadungstraegerGroupBox: TGroupBox
        Left = 279
        Top = 142
        Width = 315
        Height = 121
        Caption = 'Ladungstr'#228'ger'
        TabOrder = 3
        object LTBelLabel: TLabel
          Left = 8
          Top = 20
          Width = 45
          Height = 13
          Caption = 'Beladung'
        end
        object LTNameLabel: TLabel
          Left = 8
          Top = 64
          Width = 13
          Height = 13
          Caption = 'Art'
        end
        object LT_BeladungComboBox: TComboBoxPro
          Left = 8
          Top = 36
          Width = 300
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 0
        end
        object LTNComboBox: TComboBoxPro
          Left = 8
          Top = 80
          Width = 300
          Height = 21
          Style = csOwnerDrawFixed
          ItemHeight = 15
          TabOrder = 1
        end
      end
      object LSTextEdit: TEdit
        Left = 8
        Top = 336
        Width = 579
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 128
        TabOrder = 5
        Text = 'LSTextEdit'
      end
      object KommTextEdit: TEdit
        Left = 8
        Top = 380
        Width = 579
        Height = 21
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 128
        TabOrder = 6
        Text = 'KommTextEdit'
      end
      object PfandRadioGroup: TRadioGroup
        Left = 8
        Top = 269
        Width = 586
        Height = 44
        Caption = 'Pfandgut'
        Columns = 3
        Items.Strings = (
          'Nicht ber'#252'cksichtigen'
          'Nur Bestand f'#252'hren'
          'Auch zur'#252'ckmelden')
        TabOrder = 4
      end
      object VersandGroupbox: TGroupBox
        Left = 8
        Top = 3
        Width = 586
        Height = 133
        Caption = 'Versand'
        TabOrder = 0
        object Spedition: TLabel
          Left = 8
          Top = 48
          Width = 44
          Height = 13
          Caption = 'Spedition'
        end
        object Label7: TLabel
          Left = 8
          Top = 88
          Width = 80
          Height = 13
          Caption = 'Versandoptionen'
        end
        object Label9: TLabel
          Left = 213
          Top = 24
          Width = 75
          Height = 13
          Caption = 'Lademittelkonto'
        end
        object SelbstabholerCheckBox: TCheckBox
          Left = 8
          Top = 21
          Width = 116
          Height = 17
          Caption = 'Selbstabholer'
          TabOrder = 0
          OnClick = SelbstabholerCheckBoxClick
        end
        object SpedComboBox: TComboBoxPro
          Left = 8
          Top = 64
          Width = 571
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 2
        end
        object SpedOptionComboBox: TComboBoxPro
          Left = 8
          Top = 104
          Width = 571
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 3
        end
        object LeerComboBox: TComboBoxPro
          Left = 296
          Top = 21
          Width = 283
          Height = 21
          Style = csOwnerDrawFixed
          ItemHeight = 15
          PopupMenu = LeergutPopupMenu
          TabOrder = 1
        end
      end
      object AnzahlKopienComboBox: TComboBox
        Left = 8
        Top = 240
        Width = 121
        Height = 21
        Style = csDropDownList
        TabOrder = 2
      end
      object VDACheckBox: TCheckBox
        Left = 144
        Top = 240
        Width = 121
        Height = 17
        Caption = 'VDA-Warenanh'#228'nger'
        TabOrder = 7
      end
    end
    object PurchaseTabSheet: TTabSheet
      Caption = 'Bestellung'
      ImageIndex = 3
      object Label10: TLabel
        Left = 8
        Top = 16
        Width = 105
        Height = 13
        Caption = 'Verbundener Lieferant'
      end
      object LieferantComboBox: TComboBoxPro
        Left = 8
        Top = 32
        Width = 587
        Height = 22
        Style = csOwnerDrawFixed
        PopupMenu = LFPopupMenu
        TabOrder = 0
      end
    end
    object AvisTabSheet: TTabSheet
      Caption = 'Avisierung'
      ImageIndex = 2
      DesignSize = (
        597
        416)
      object GroupBox2: TGroupBox
        Left = 8
        Top = 17
        Width = 584
        Height = 128
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Avisierung nach Lieferabschluss'
        TabOrder = 0
        DesignSize = (
          584
          128)
        object Label12: TLabel
          Left = 8
          Top = 24
          Width = 82
          Height = 13
          Caption = 'E-Mail Adresse(n)'
        end
        object AVISMailLSCheckBox: TCheckBox
          Left = 259
          Top = 83
          Width = 171
          Height = 17
          Caption = 'Lieferscheine per Mail senden'
          TabOrder = 2
        end
        object AvisMailEdit: TEdit
          Left = 8
          Top = 40
          Width = 567
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 256
          TabOrder = 0
          Text = 'AvisMailEdit'
        end
        object AVISMailAvisCheckBox: TCheckBox
          Left = 8
          Top = 83
          Width = 171
          Height = 17
          Caption = 'Lieferavis per Mail senden'
          TabOrder = 1
        end
      end
    end
  end
  object WEBetterADODataSet: TBetterADODataSet
    Connection = LVSDatenModul.MainADOConnection
    EnableBCD = False
    Parameters = <>
    IndexDefs = <>
    Left = 56
    Top = 376
  end
  object DataSource3: TDataSource
    DataSet = WEBetterADODataSet
    Left = 16
    Top = 376
  end
  object AdrArtComboBoxPopupMenu: TPopupMenu
    Left = 560
    Top = 184
    object NeueLieferadresse1: TMenuItem
      Caption = 'Neue Lieferadresse'
      OnClick = NeueLieferadresse1Click
    end
    object Lieferadresselschen1: TMenuItem
      Caption = 'Lieferadresse l'#246'schen'
      Visible = False
    end
  end
  object LeergutPopupMenu: TPopupMenu
    Left = 96
    Top = 408
    object NeuesLeergutkontoanlegen1: TMenuItem
      Caption = 'Neues Leergutkonto anlegen...'
      OnClick = NeuesLeergutkontoanlegen1Click
    end
  end
  object LFPopupMenu: TPopupMenu
    Left = 512
    Top = 72
    object NeuerLieferant1: TMenuItem
      Caption = 'Neuer Lieferant...'
      ShortCut = 115
      OnClick = NeuerLieferant1Click
    end
  end
end
