object ArtikelGroupForm: TArtikelGroupForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Artikelgruppen'
  ClientHeight = 488
  ClientWidth = 709
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    709
    488)
  TextHeight = 13
  object GridLabel: TLabel
    Left = 8
    Top = 16
    Width = 70
    Height = 13
    Caption = 'Artikelgruppen'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 430
    Width = 693
    Height = 6
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 406
    ExplicitWidth = 982
  end
  object NewButton: TButton
    Left = 581
    Top = 32
    Width = 120
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Neue Gruppe...'
    TabOrder = 1
    OnClick = NewButtonClick
  end
  object CloseButton: TButton
    Left = 626
    Top = 455
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Schlie'#223'en'
    Default = True
    ModalResult = 1
    TabOrder = 4
  end
  object EditButton: TButton
    Left = 581
    Top = 63
    Width = 120
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Gruppe bearbeiten...'
    TabOrder = 2
    OnClick = EditButtonClick
  end
  object DeleteButton: TButton
    Left = 581
    Top = 392
    Width = 120
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Gruppe l'#246'schen...'
    TabOrder = 3
    OnClick = DeleteButtonClick
  end
  object GroupTreeView: TTreeView
    Left = 8
    Top = 32
    Width = 560
    Height = 385
    Anchors = [akLeft, akTop, akRight, akBottom]
    Indent = 19
    PopupMenu = TreePopupMenu
    ReadOnly = True
    RightClickSelect = True
    RowSelect = True
    TabOrder = 0
    OnChange = GroupTreeViewChange
    OnDeletion = GroupTreeViewDeletion
  end
  object TreePopupMenu: TPopupMenu
    OnPopup = TreePopupMenuPopup
    Left = 192
    Top = 264
    object VorhalteMengenMenuItem: TMenuItem
      Caption = 'Vorhaltemengen besetzen...'
      OnClick = VorhalteMengenMenuItemClick
    end
    object Versandgruppe1: TMenuItem
      Caption = 'Versandgruppe definieren'
      OnClick = Versandgruppe1Click
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object EditLagerAssigneMenuItem: TMenuItem
      Caption = 'Lagerzuordnung bearbeiten....'
      OnClick = EditLagerAssigneMenuItemClick
    end
  end
end
