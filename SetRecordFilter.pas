unit SetRecordFilter;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComCtrls, DB, ADODB, ComboBoxPro, BarCodeScanner;

type
  TFilterDaten = record
    WEKdEdit        : String;
    WEArEdit        : String;
    WELSEdit        : String;
    WENVEEdit       : String;
    WERefSubMand    : Integer;
    WEArGrpRef      : Integer;
    RetKdEdit       : String;
    RetArEdit       : String;
    RetRefSubMand   : Integer;
    AufKdEdit       : String;
    AufArEdit       : String;
    AufArGrpRef     : Integer;
    AufSpedRef      : Integer;
    AufRefSubMand   : Integer;
    VerlAufNr       : String;
    VerlBestNr      : String;
    LagerArEdit     : String;
    LagerRefSubMand : Integer;
    WAKdEdit        : String;
    WAArEdit        : String;
    WARefSubMand    : Integer;
    WASpedRef       : Integer;
    WANVEEdit       : String;
    WASendNrEdit    : String;
    KommArEdit      : String;
    KommAufNrEdit   : String;
    BatchKdNrEdit   : String;
    BatchArNrEdit   : String;
    BatchAufNrEdit  : String;
  end;

  TRecordFilterForm = class(TForm)
    PageControl1: TPageControl;
    OkButton: TButton;
    AbortButton: TButton;
    AufTabSheet: TTabSheet;
    KommTabSheet: TTabSheet;
    WETabSheet: TTabSheet;
    WATabSheet: TTabSheet;
    AufKdEdit: TEdit;
    AufArEdit: TEdit;
    Label2: TLabel;
    AufArGrpComboBox: TComboBoxPro;
    ADOQuery: TADOQuery;
    Label3: TLabel;
    Label4: TLabel;
    VerlTabSheet: TTabSheet;
    VerlBestEdit: TEdit;
    Label8: TLabel;
    Label9: TLabel;
    VerlAufEdit: TEdit;
    LagerTabSheet: TTabSheet;
    Label10: TLabel;
    LagerAREdit: TEdit;
    Label11: TLabel;
    WAKdEdit: TEdit;
    Label12: TLabel;
    WAArEdit: TEdit;
    Label13: TLabel;
    KommArEdit: TEdit;
    BatchTabSheet: TTabSheet;
    Label14: TLabel;
    Label15: TLabel;
    BatchArNrEdit: TEdit;
    BachtKdNrEdit: TEdit;
    Label16: TLabel;
    BatchAufNrEdit: TEdit;
    Label151: TLabel;
    LagerSubMandComboBox: TComboBoxPro;
    Label17: TLabel;
    AufSubMandComboBox: TComboBoxPro;
    Label18: TLabel;
    WASubMandComboBox: TComboBoxPro;
    Label5: TLabel;
    WEKdEdit: TEdit;
    Label6: TLabel;
    WEArEdit: TEdit;
    Label7: TLabel;
    WEArGrpComboBox: TComboBoxPro;
    Label1: TLabel;
    WELSEdit: TEdit;
    WENVEEdit: TEdit;
    Label19: TLabel;
    RetTabSheet: TTabSheet;
    Label20: TLabel;
    RetKDEdit: TEdit;
    Label21: TLabel;
    RetArEdit: TEdit;
    Label22: TLabel;
    AufSpedComboBox: TComboBoxPro;
    Label23: TLabel;
    WASpedComboBox: TComboBoxPro;
    KommAufNrEdit: TEdit;
    Label24: TLabel;
    Label25: TLabel;
    WANVEEdit: TEdit;
    Label26: TLabel;
    WASendNrEdit: TEdit;
    Label27: TLabel;
    WESubMandComboBox: TComboBoxPro;
    Label28: TLabel;
    RetSubMandComboBox: TComboBoxPro;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure AufTabSheetShow(Sender: TObject);
    procedure TabSheet2Show(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure LagerTabSheetShow(Sender: TObject);
    procedure WATabSheetShow(Sender: TObject);
    procedure AufSubMandComboBoxChange(Sender: TObject);
    procedure WETabSheetShow(Sender: TObject);
    procedure RetTabSheetShow(Sender: TObject);
  private
    fFilterDaten : TFilterDaten;

    procedure ReloadArtikelGruppen (ComboBox : TComboBoxPro; const Ref, RefSubMand : Integer);
    procedure ReloadSpedition (ComboBox : TComboBoxPro; const Ref : Integer);

    procedure ScannerErfassung (var Message: TMessage); message WM_USER + SCANNER_DETECT;
  public
    property FilterDaten : TFilterDaten read fFilterDaten write fFilterDaten;
  end;

var
  RecordFilterForm: TRecordFilterForm;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DatenModul, FrontendUtils, ConfigModul, SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    CanClose := True;

    if (CanClose) then begin
      if (AufSubMandComboBox.Enabled) then
        fFilterDaten.AufRefSubMand := GetComboBoxRef (AufSubMandComboBox)
      else
        fFilterDaten.AufRefSubMand := -1;

      fFilterDaten.AufSpedRef  := GetComboBoxRef (AufSpedComboBox);
      fFilterDaten.AufArGrpRef := GetComboBoxRef (AufArGrpComboBox);
      fFilterDaten.AufArEdit   := AufArEdit.Text;
      fFilterDaten.AufKdEdit   := AufKdEdit.Text;

      fFilterDaten.BatchArNrEdit   := BatchArNrEdit.Text;
      fFilterDaten.BatchKdNrEdit   := BachtKdNrEdit.Text;
      fFilterDaten.BatchAufNrEdit  := BatchAufNrEdit.Text;

      fFilterDaten.WEKdEdit  := WEKdEdit.Text;
      fFilterDaten.WEArEdit  := WEArEdit.Text;
      fFilterDaten.WELSEdit  := WELSEdit.Text;
      fFilterDaten.WENVEEdit := WENVEEdit.Text;

      fFilterDaten.RetKdEdit  := RetKdEdit.Text;
      fFilterDaten.RetArEdit  := RetArEdit.Text;

      fFilterDaten.VerlAufNr  := VerlAufEdit.Text;
      fFilterDaten.VerlBestNr := VerlBestEdit.Text;

      fFilterDaten.WASpedRef    := GetComboBoxRef (WASpedComboBox);
      fFilterDaten.WAArEdit     := WAArEdit.Text;
      fFilterDaten.WAKdEdit     := WAKdEdit.Text;
      fFilterDaten.WANVEEdit    := WANVEEdit.Text;
      fFilterDaten.WASendNrEdit := WASendNrEdit.Text;

      if (WASubMandComboBox.Enabled) then
        fFilterDaten.WARefSubMand := GetComboBoxRef (WASubMandComboBox)
      else
        fFilterDaten.WARefSubMand := -1;

      if (WESubMandComboBox.Enabled) then
        fFilterDaten.WERefSubMand := GetComboBoxRef (WESubMandComboBox)
      else
        fFilterDaten.WERefSubMand := -1;

      if (RetSubMandComboBox.Enabled) then
        fFilterDaten.RetRefSubMand := GetComboBoxRef (RetSubMandComboBox)
      else
        fFilterDaten.RetRefSubMand := -1;

      fFilterDaten.KommArEdit    := KommArEdit.Text;
      fFilterDaten.KommAufNrEdit := KommAufNrEdit.Text;

      if (LagerSubMandComboBox.Enabled) then
        fFilterDaten.LagerRefSubMand := GetComboBoxRef(LagerSubMandComboBox)
      else
        fFilterDaten.LagerRefSubMand := -1;

      fFilterDaten.LagerArEdit := LagerAREdit.Text;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.FormCreate(Sender: TObject);
begin
  fFilterDaten.WEKdEdit        := '';
  fFilterDaten.WEArEdit        := '';
  fFilterDaten.WELSEdit        := '';
  fFilterDaten.WEArGrpRef      := -1;
  fFilterDaten.WENVEEdit       := '';
  fFilterDaten.WERefSubMand    := -1;
  fFilterDaten.RetKdEdit       := '';
  fFilterDaten.RetArEdit       := '';
  fFilterDaten.AufKdEdit       := '';
  fFilterDaten.AufArEdit       := '';
  fFilterDaten.AufArGrpRef     := -1;
  fFilterDaten.AufSpedRef      := -1;
  fFilterDaten.AufRefSubMand   := -1;
  fFilterDaten.VerlAufNr       := '';
  fFilterDaten.VerlBestNr      := '';
  fFilterDaten.LagerArEdit     := '';
  fFilterDaten.WAKdEdit        := '';
  fFilterDaten.WAArEdit        := '';
  fFilterDaten.WARefSubMand    := -1;
  fFilterDaten.WASpedRef       := -1;
  fFilterDaten.WANVEEdit       := '';
  fFilterDaten.WASendNrEdit    := '';
  fFilterDaten.KommArEdit      := '';
  fFilterDaten.KommAufNrEdit   := '';
  fFilterDaten.BatchArNrEdit   := '';
  fFilterDaten.BatchKdNrEdit   := '';
  fFilterDaten.BatchAufNrEdit  := '';
  fFilterDaten.LagerRefSubMand := -1;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, AufArGrpComboBox);
    LVSSprachModul.SetNoTranslate (Self, WEArGrpComboBox);
    LVSSprachModul.SetNoTranslate (Self, WASpedComboBox);
    LVSSprachModul.SetNoTranslate (Self, AufSpedComboBox);
    LVSSprachModul.SetNoTranslate (Self, AufSubMandComboBox);
    LVSSprachModul.SetNoTranslate (Self, WASubMandComboBox);
    LVSSprachModul.SetNoTranslate (Self, WESubMandComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (AufArGrpComboBox);
  ClearComboBoxObjects (WEArGrpComboBox);
  ClearComboBoxObjects (WASpedComboBox);
  ClearComboBoxObjects (AufSpedComboBox);
  ClearComboBoxObjects (AufSubMandComboBox);
  ClearComboBoxObjects (WASubMandComboBox);
  ClearComboBoxObjects (LagerSubMandComboBox);
  ClearComboBoxObjects (WESubMandComboBox);
  ClearComboBoxObjects (RetSubMandComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.ReloadSpedition (ComboBox : TComboBoxPro; const Ref : Integer);
var
  idx : Integer;
begin
  ClearComboBoxObjects (ComboBox);

  if not (ADOQuery.Connection.Connected) then begin
    ComboBox.Enabled := False;
    ComboBox.ItemIndex := -1;
  end else begin
    ADOQuery.SQL.Clear;

    if (LVSDatenModul.AktLagerRef > 0) then begin
      ADOQuery.SQL.Add ('SELECT * FROM V_SPEDITIONEN where STATUS=''ANG'' and ((REF_LAGER is not null and REF_LAGER=:ref_lager))');
      ADOQuery.Parameters.ParamByName('ref_lager').Value := LVSDatenModul.AktLagerRef;
    end else begin
      ADOQuery.SQL.Add ('SELECT * FROM V_SPEDITIONEN where STATUS=''ANG'' and ((REF_LAGER is not null and REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc_1)) or (REF_LAGER is null and (REF_LOCATION is null or REF_LOCATION=:ref_loc_2)))');
      ADOQuery.Parameters.ParamByName('ref_loc_1').Value := LVSDatenModul.AktLocationRef;
      ADOQuery.Parameters.ParamByName('ref_loc_2').Value := LVSDatenModul.AktLocationRef;
    end;

    ADOQuery.SQL.Add ('order by REF_MAND nulls last, upper (NAME)');

    try
      ADOQuery.Open;

      while not (ADOQuery.Eof) do begin
        idx := ComboBox.AddItemIndex (ADOQuery.FieldByName('NAME').AsString+'|'+ADOQuery.FieldByName('BESCHREIBUNG').AsString, TComboBoxRef.Create (ADOQuery.FieldByName('REF').AsInteger));

        if (ADOQuery.FieldByName('REF').AsInteger = Ref) then
          ComboBox.ItemIndex := idx;

        ADOQuery.Next;
      end;

      ADOQuery.Close;

      if (ComboBox.Items.Count = 0) then begin
        ComboBox.Enabled := False;
        ComboBox.ItemIndex := -1;
      end else begin
        ComboBox.Items.Insert (0, GetResourceText (1020));
        if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;
      end;
    except
    end;
  end;
end;

procedure TRecordFilterForm.RetTabSheetShow(Sender: TObject);
var
  ref : Integer;
begin
  if not (LVSConfigModul.UseSubMandanten) then begin
    RetSubMandComboBox.Visible    := False;
  end;

  if not (RetSubMandComboBox.Visible) then begin
    Label28.Visible := False;
    RetSubMandComboBox.Enabled := false;
  end else begin
    if (RetSubMandComboBox.Items.Count = 0) then
      //Die Combobox wurde noch nie geladen
      ref := fFilterDaten.RetRefSubMand
    else
      ref := GetComboBoxRef (RetSubMandComboBox);

    LoadSubMandantCombobox (RetSubMandComboBox, LVSDatenModul.AktLocationRef, LVSDatenModul.AktMandantRef);
    RetSubMandComboBox.Items.Insert (0, GetResourceText (1020));

    if (ref = -1) then
      RetSubMandComboBox.ItemIndex := 0
    else begin
      RetSubMandComboBox.ItemIndex := FindComboboxRef(RetSubMandComboBox, ref);
      if (RetSubMandComboBox.ItemIndex = -1) then RetSubMandComboBox.ItemIndex := 0;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.ReloadArtikelGruppen (ComboBox : TComboBoxPro; const Ref, RefSubMand : Integer);
begin
  if not (ADOQuery.Connection.Connected) then begin
    ComboBox.Enabled := False;
    ComboBox.ItemIndex := -1;
  end else if (LVSDatenModul.AktMandantRef = -1) then begin
    ComboBox.Enabled := False;
    ComboBox.ItemIndex := -1;
  end else begin
    LoadWarengruppenCombobox (ComboBox, LVSDatenModul.AktLocationRef, LVSDatenModul.AktMandantRef, RefSubMand);

    if (ComboBox.Items.Count < 2) then begin
      ComboBox.Enabled := False;
      ComboBox.ItemIndex := -1;
    end else begin
      ComboBox.ItemIndex := FindComboboxRef (ComboBox, Ref);
      if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.TabSheet2Show(Sender: TObject);
begin
  ReloadArtikelGruppen (WEArGrpComboBox, fFilterDaten.WEArGrpRef, -1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.06.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.WATabSheetShow(Sender: TObject);
var
  ref : Integer;
begin
  if not (LVSConfigModul.UseSubMandanten) then begin
    WASubMandComboBox.Visible    := False;
    AufSubMandComboBox.Visible   := False;
    LagerSubMandComboBox.Visible := False;
  end;

  if not (WASubMandComboBox.Visible) then begin
    Label18.Visible := False;
    WASubMandComboBox.Enabled := false;
  end else begin
    if (WASubMandComboBox.Items.Count = 0) then
      //Die Combobox wurde noch nie geladen
      ref := fFilterDaten.WARefSubMand
    else
      ref := GetComboBoxRef (WASubMandComboBox);

    LoadSubMandantCombobox (WASubMandComboBox, LVSDatenModul.AktLocationRef, LVSDatenModul.AktMandantRef);
    WASubMandComboBox.Items.Insert (0, GetResourceText (1020));

    if (ref = -1) then
      WASubMandComboBox.ItemIndex := 0
    else begin
      WASubMandComboBox.ItemIndex := FindComboboxRef(WASubMandComboBox, ref);
      if (WASubMandComboBox.ItemIndex = -1) then WASubMandComboBox.ItemIndex := 0;
    end;
  end;

  ReloadSpedition (WASpedComboBox, fFilterDaten.WASpedRef);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 11.11.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.WETabSheetShow(Sender: TObject);
var
  ref : Integer;
begin
  if not (LVSConfigModul.UseSubMandanten) then begin
    WESubMandComboBox.Visible    := False;
  end;

  if not (WESubMandComboBox.Visible) then begin
    Label27.Visible := False;
    WESubMandComboBox.Enabled := false;
  end else begin
    if (WESubMandComboBox.Items.Count = 0) then
      //Die Combobox wurde noch nie geladen
      ref := fFilterDaten.WERefSubMand
    else
      ref := GetComboBoxRef (WESubMandComboBox);

    LoadSubMandantCombobox (WESubMandComboBox, LVSDatenModul.AktLocationRef, LVSDatenModul.AktMandantRef);
    WESubMandComboBox.Items.Insert (0, GetResourceText (1020));

    if (ref = -1) then
      WESubMandComboBox.ItemIndex := 0
    else begin
      WESubMandComboBox.ItemIndex := FindComboboxRef(WESubMandComboBox, ref);
      if (WESubMandComboBox.ItemIndex = -1) then WESubMandComboBox.ItemIndex := 0;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 01.03.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.AufSubMandComboBoxChange(Sender: TObject);
begin
  ReloadArtikelGruppen (AufArGrpComboBox, fFilterDaten.AufArGrpRef, GetComboBoxRef(AufSubMandComboBox));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.AufTabSheetShow(Sender: TObject);
var
  ref : Integer;
begin
  if not (LVSConfigModul.UseSubMandanten) then begin
    WASubMandComboBox.Visible    := False;
    AufSubMandComboBox.Visible   := False;
    LagerSubMandComboBox.Visible := False;
  end;

  if not (AufSubMandComboBox.Visible) then begin
    Label17.Visible := False;
    AufSubMandComboBox.Enabled := false;
  end else begin
    if (AufSubMandComboBox.Items.Count = 0) then
      //Die Combobox wurde noch nie geladen
      ref := fFilterDaten.AufRefSubMand
    else
      ref := GetComboBoxRef (AufSubMandComboBox);

    LoadSubMandantCombobox (AufSubMandComboBox, LVSDatenModul.AktLocationRef, LVSDatenModul.AktMandantRef);
    AufSubMandComboBox.Items.Insert (0, GetResourceText (1020));

    if (ref = -1) then
      AufSubMandComboBox.ItemIndex := 0
    else begin
      AufSubMandComboBox.ItemIndex := FindComboboxRef(AufSubMandComboBox, ref);
      if (AufSubMandComboBox.ItemIndex = -1) then AufSubMandComboBox.ItemIndex := 0;
    end;
  end;

  ReloadArtikelGruppen (AufArGrpComboBox, fFilterDaten.AufArGrpRef, GetComboBoxRef(AufSubMandComboBox));
  ReloadSpedition (AufSpedComboBox, fFilterDaten.AufSpedRef);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.FormShow(Sender: TObject);
begin
  if not (LVSConfigModul.UseSubMandanten) then begin
    WESubMandComboBox.Visible    := False;
    WESubMandComboBox.Enabled    := False;
    RetSubMandComboBox.Visible   := False;
    RetSubMandComboBox.Enabled   := False;
    WASubMandComboBox.Visible    := False;
    WASubMandComboBox.Enabled    := False;
    AufSubMandComboBox.Visible   := False;
    AufSubMandComboBox.Enabled   := False;
    LagerSubMandComboBox.Visible := False;
    LagerSubMandComboBox.Enabled := False;
  end;

  AufArEdit.Text       := fFilterDaten.AufArEdit;
  AufKdEdit.Text       := fFilterDaten.AufKdEdit;

  BatchArNrEdit.Text   := fFilterDaten.BatchArNrEdit;
  BachtKdNrEdit.Text   := fFilterDaten.BatchKdNrEdit;
  BatchAufNrEdit.Text  := fFilterDaten.BatchAufNrEdit;

  WEKdEdit.Text        := fFilterDaten.WEKdEdit;
  WEArEdit.Text        := fFilterDaten.WEArEdit;
  WELSEdit.Text        := fFilterDaten.WELSEdit;
  WENVEEdit.Text       := fFilterDaten.WENVEEdit;

  RetKdEdit.Text        := fFilterDaten.RetKdEdit;
  RetArEdit.Text        := fFilterDaten.RetArEdit;

  VerlAufEdit.Text     := fFilterDaten.VerlAufNr;
  VerlBestEdit.Text    := fFilterDaten.VerlBestNr;

  WAKdEdit.Text        := fFilterDaten.WAKdEdit;
  WAArEdit.Text        := fFilterDaten.WAArEdit;
  WANVEEdit.Text       := fFilterDaten.WANVEEdit;
  WASendNrEdit.Text    := fFilterDaten.WASendNrEdit;

  KommArEdit.Text      := fFilterDaten.KommArEdit;
  KommAufNrEdit.Text   := fFilterDaten.KommAufNrEdit;

  LagerAREdit.Text     := fFilterDaten.LagerArEdit;

  if Assigned (PageControl1.ActivePage) and Assigned (PageControl1.ActivePage.OnShow) then
    PageControl1.ActivePage.OnShow (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.06.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.LagerTabSheetShow(Sender: TObject);
var
  ref : Integer;
begin
  if not (LVSConfigModul.UseSubMandanten) then begin
    AufSubMandComboBox.Visible   := False;
    LagerSubMandComboBox.Visible := False;
  end;

  if not (LagerSubMandComboBox.Visible) then begin
    Label151.Visible := False;
    LagerSubMandComboBox.Enabled := false;
  end else begin
    if (LagerSubMandComboBox.Items.Count = 0) then
      //Die Combobox wurde noch nie geladen
      ref := fFilterDaten.LagerRefSubMand
    else
      ref := GetComboBoxRef (LagerSubMandComboBox);

    LoadSubMandantCombobox (LagerSubMandComboBox, LVSDatenModul.AktLocationRef, LVSDatenModul.AktMandantRef);
    LagerSubMandComboBox.Items.Insert (0, GetResourceText (1020));

    if (ref = -1) then
      LagerSubMandComboBox.ItemIndex := 0
    else begin
      LagerSubMandComboBox.ItemIndex := FindComboboxRef(LagerSubMandComboBox, ref);
      if (LagerSubMandComboBox.ItemIndex = -1) then LagerSubMandComboBox.ItemIndex := 0;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TRecordFilterForm.ScannerErfassung (var Message: TMessage);
var
  res,
  refar,
  refae      : Integer;
  arnr,
  einh,
  errmsg     : String;
begin
  res := DetectArtikelBarcode (LVSDatenModul.AktMandantRef, refar, refae, arnr, einh, errmsg);

  if (res = 0) and (Length (errmsg) = 0) then begin
    if (PageControl1.ActivePage = WETabSheet) then
      WEArEdit.Text := arnr
    else if (PageControl1.ActivePage = RetTabSheet) then
      RetArEdit.Text := arnr
    else if (PageControl1.ActivePage = AufTabSheet) then
      AufArEdit.Text := arnr
    else if (PageControl1.ActivePage = BatchTabSheet) then
      BatchArNrEdit.Text := arnr
    else if (PageControl1.ActivePage = WATabSheet) then
      WAArEdit.Text := arnr
    else if (PageControl1.ActivePage = KommTabSheet) then
      KommArEdit.Text := arnr
    else if (PageControl1.ActivePage = LagerTabSheet) then
      LagerAREdit.Text := arnr;
  end;

  if (Length (errmsg) > 0) then
    MessageDLG (errmsg, mtInformation	, [mbOk], 0);
end;

end.
