unit ChangeLPLBDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro;

type
  TChangeLPLBForm = class(TForm)
    LBComboBox: TComboBoxPro;
    OkButton: TButton;
    AbortButton: TButton;
    Label1: TLabel;
    Label2: TLabel;
    LBZoneComboBox: TComboBoxPro;
    procedure LBComboBoxChange(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, FrontendUtils, SprachModul;

procedure TChangeLPLBForm.FormCreate(Sender: TObject);
begin
  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LBComboBox);
    LVSSprachModul.SetNoTranslate (Self, LBZoneComboBox);
  {$endif}
end;

procedure TChangeLPLBForm.LBComboBoxChange(Sender: TObject);
begin
  LoadLBZoneCombobox (LBZoneComboBox, GetComboBoxRef (LBComboBox));

  LBZoneComboBox.Enabled := (LBZoneComboBox.Items.Count > 0);
end;

end.
