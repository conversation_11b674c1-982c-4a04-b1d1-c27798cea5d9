unit zpl_pdfconverter;

interface

uses
  System.SysUtils, System.Classes, SendHTTPRequest;

function RequestLabelAsPDF(const ZPL_Label: string): TMemoryStream;
procedure SavePDFToFile(PDFStream: TMemoryStream; const Daten<PERSON><PERSON>, LabelDumpDir, <PERSON>erse<PERSON>, SendungsNr: string);

implementation

function RequestLabelAsPDF(const ZPL_Label: string): TMemoryStream;
var
  Host, Service, Method, ContentType, ResponseHeader, Boundary: string;
  ErrCode: Integer;
  ErrText: string;
  Headers: array of UTF8String;
  RequestBody: UTF8String;
begin
  Result := TMemoryStream.Create;

  try
    Host := 'http://api.labelary.com';
    Service := '/v1/printers/8dpmm/labels/4x6/0/';
    Method := 'POST';
    Boundary := '----WebKitFormBoundary' + IntToStr(Random(1000000));
    ContentType := 'multipart/form-data; boundary=' + Boundary;

    RequestBody := '--' + Boundary + #13#10 +
                   'Content-Disposition: form-data; name="file"; filename="label.zpl"' + #13#10 +
                   'Content-Type: application/octet-stream' + #13#10 +
                   #13#10 +
                   ZPL_Label + #13#10 +
                   '--' + Boundary + '--' + #13#10;

    SetLength(Headers, 1);
    Headers[0] := 'Accept: application/pdf';

    if SendRequest(
      Host,                          // Host
      80,                            // Port
      Service,                       // Service
      Method,                        // Method
      '',                            // Proxy
      '',                            // Username
      '',                            // Password
      '',                            // Action
      ContentType,                   // ContentType
      Headers,                       // Additional Headers
      '',                            // RequestKey
      RequestBody,                   // RequestBody
      ResponseHeader,                // ResponseHeader
      Result,                          // ResponseStream
      ErrCode,                       // ErrorCode
      ErrText                        // ErrorText
    ) and (Copy(UpperCase(ResponseHeader), 1, 12) <> 'HTTP/1.1 200') then
    begin
      FreeAndNil(Result);
    end;

  except
    on E: Exception do
    begin
      Result := nil;
    end;
  end;

end;


	procedure SavePDFToFile(PDFStream: TMemoryStream; const DatenPath, LabelDumpDir, Versender, SendungsNr: string);
	var
	 ExePath, FilePath: string;
	begin
	  ExePath := ExtractFilePath(ParamStr(0));
	  if not DirectoryExists(DatenPath + LabelDumpDir + 'SendIT') then begin
      ForceDirectories(DatenPath + LabelDumpDir + 'SendIT');
    end;


	  FilePath := Format('%s%sSendIT\%s_%s.pdf', [DatenPath, LabelDumpDir, Versender, SendungsNr]);

	  PDFStream.Position := 0;
	  PDFStream.SaveToFile(FilePath);
	end;
end.
