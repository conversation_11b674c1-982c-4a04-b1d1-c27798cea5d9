object EditKommLPARForm: TEditKommLPARForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Verwaltungsdaten eines Kommplatzes'
  ClientHeight = 210
  ClientWidth = 324
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    324
    210)
  PixelsPerInch = 96
  TextHeight = 13
  object KommLPLabel: TLabel
    Left = 72
    Top = 8
    Width = 80
    Height = 13
    Caption = 'KommLPLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Bevel1: TBevel
    Left = 8
    Top = 56
    Width = 308
    Height = 4
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 228
  end
  object Bevel2: TBevel
    Left = 8
    Top = 170
    Width = 308
    Height = 4
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 260
    ExplicitWidth = 228
  end
  object Label3: TLabel
    Left = 8
    Top = 64
    Width = 70
    Height = 13
    Caption = 'Artikelvariante'
  end
  object ARNrLabel: TLabel
    Left = 72
    Top = 32
    Width = 62
    Height = 13
    Caption = 'ARNrLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Label2: TLabel
    Left = 8
    Top = 32
    Width = 53
    Height = 13
    Caption = 'Artikel-Nr.:'
  end
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 59
    Height = 13
    Caption = 'Komm-Platz:'
  end
  object Bevel3: TBevel
    Left = 8
    Top = 112
    Width = 308
    Height = 4
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 350
  end
  object Label10: TLabel
    Left = 8
    Top = 124
    Width = 106
    Height = 13
    Caption = 'Automatisch einlagern'
  end
  object AbortButton: TButton
    Left = 241
    Top = 180
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 1
  end
  object OkButton: TButton
    Left = 153
    Top = 180
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 2
  end
  object AZPEdit: TEdit
    Left = 8
    Top = 80
    Width = 308
    Height = 21
    Anchors = [akLeft, akTop, akRight]
    TabOrder = 0
    Text = 'AZPEdit'
  end
  object AutoStoreComboBox: TComboBox
    Left = 8
    Top = 140
    Width = 308
    Height = 21
    Style = csDropDownList
    ItemHeight = 13
    ItemIndex = 0
    TabOrder = 3
    Text = 'Vorgabe aus dem Lagerbereich'
    Items.Strings = (
      'Vorgabe aus dem Lagerbereich'
      'Nein'
      'Ja, unmittelbar'
      'Ja, '#252'ber Einlagertransport')
  end
end
