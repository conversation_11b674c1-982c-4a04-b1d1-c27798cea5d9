object BatchVerteilungTouchForm: TBatchVerteilungTouchForm
  Left = 0
  Top = 0
  Caption = 'Batch Verteilung'
  ClientHeight = 512
  ClientWidth = 1075
  Color = clBtnFace
  Constraints.MinHeight = 500
  Constraints.MinWidth = 1000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poScreenCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  TextHeight = 13
  object PageControl1: TPageControl
    Left = 0
    Top = 0
    Width = 1075
    Height = 380
    ActivePage = DistributTabSheet
    Align = alClient
    TabOrder = 0
    object StartTabSheet: TTabSheet
      Caption = 'StartTabSheet'
      OnShow = StartTabSheetShow
      DesignSize = (
        1067
        352)
      object BatchBeginLabel: TLabel
        Left = 3
        Top = 16
        Width = 1061
        Height = 218
        Alignment = taCenter
        Anchors = [akLeft, akTop, akRight, akBottom]
        AutoSize = False
        Caption = 'Die Batchverteilung ist abgeschlossen'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -35
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object KommLELabel: TLabel
        Left = 3
        Top = 240
        Width = 1061
        Height = 109
        Anchors = [akLeft, akRight, akBottom]
        AutoSize = False
        Caption = 'Zugeh'#246'rige LEs'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -27
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
    end
    object AssignTabSheet: TTabSheet
      Caption = 'AssignTabSheet'
      ImageIndex = 1
      OnShow = AssignTabSheetShow
      DesignSize = (
        1067
        352)
      object Label1: TLabel
        Left = 16
        Top = 96
        Width = 59
        Height = 25
        Caption = 'Kunde'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -21
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object Label2: TLabel
        Left = 16
        Top = 64
        Width = 96
        Height = 25
        Caption = 'Auftragnr.'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -21
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object Label4: TLabel
        Left = 272
        Top = 237
        Width = 248
        Height = 52
        Caption = 'Fachnummer'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -43
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object AuftragNrLabel: TLabel
        Left = 200
        Top = 64
        Width = 141
        Height = 25
        Caption = 'AuftragNrLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -21
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object WarenempfLabel: TLabel
        Left = 200
        Top = 96
        Width = 158
        Height = 25
        Caption = 'WarenempfLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -21
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object LTFachNrLabel: TLabel
        Left = 568
        Top = 234
        Width = 344
        Height = 64
        Caption = 'LTFachNrLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -53
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object Label14: TLabel
        Left = 3
        Top = 184
        Width = 1061
        Height = 29
        Alignment = taCenter
        AutoSize = False
        Caption = 
          'Bitte Verteil-KLT in das entsprechende Fach stellen und den Barc' +
          'ode einscannen'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -24
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label15: TLabel
        Left = 16
        Top = 3
        Width = 445
        Height = 39
        Caption = 'Vorbereitung des Verteilregales'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -32
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object Bevel1: TBevel
        Left = 16
        Top = 48
        Width = 1033
        Height = 6
        Shape = bsTopLine
      end
      object Label16: TLabel
        Left = 16
        Top = 128
        Width = 122
        Height = 25
        Caption = 'Anzahl VPEs:'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -21
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object VPECountLabel: TLabel
        Left = 200
        Top = 128
        Width = 141
        Height = 25
        Caption = 'VPECountLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -21
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object AssignProgressBar: TProgressBar
        Left = 16
        Top = 313
        Width = 1033
        Height = 29
        Anchors = [akLeft, akRight, akBottom]
        TabOrder = 0
      end
    end
    object DistributTabSheet: TTabSheet
      Caption = 'DistributTabSheet'
      ImageIndex = 2
      OnShow = DistributTabSheetShow
      DesignSize = (
        1067
        352)
      object DistributProgressBar: TProgressBar
        Left = 16
        Top = 313
        Width = 1033
        Height = 29
        Anchors = [akLeft, akRight, akBottom]
        TabOrder = 0
      end
      object DistributeArtikelPanel: TPanel
        Left = 0
        Top = 0
        Width = 1067
        Height = 313
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 1
        DesignSize = (
          1067
          313)
        object EANLabelLabel: TLabel
          Left = 16
          Top = 16
          Width = 39
          Height = 25
          Caption = 'EAN'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object Label6: TLabel
          Left = 16
          Top = 48
          Width = 59
          Height = 25
          Caption = 'Artikel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object Label7: TLabel
          Left = 16
          Top = 80
          Width = 64
          Height = 25
          Caption = 'Einheit'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object Label11: TLabel
          Left = 16
          Top = 112
          Width = 62
          Height = 25
          Caption = 'Menge'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object Label10: TLabel
          Left = 16
          Top = 161
          Width = 71
          Height = 25
          Caption = 'Kunden'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object Label12: TLabel
          Left = 16
          Top = 192
          Width = 70
          Height = 25
          Caption = 'Auftrag'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object DisAuftragLabel: TLabel
          Left = 200
          Top = 192
          Width = 147
          Height = 25
          Caption = 'DisAuftragLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object DisKundeLabel: TLabel
          Left = 200
          Top = 160
          Width = 585
          Height = 25
          AutoSize = False
          Caption = 'DisKundeLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object MengeLabel: TLabel
          Left = 200
          Top = 112
          Width = 111
          Height = 25
          Caption = 'MengeLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object EinheitLabel: TLabel
          Left = 200
          Top = 80
          Width = 113
          Height = 25
          Caption = 'EinheitLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object ArtikelLabel: TLabel
          Left = 200
          Top = 48
          Width = 585
          Height = 25
          AutoSize = False
          Caption = 'ArtikelLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object EANLabel: TLabel
          Left = 200
          Top = 16
          Width = 88
          Height = 25
          Caption = 'EANLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object Label8: TLabel
          Left = 72
          Top = 252
          Width = 248
          Height = 52
          Caption = 'Fachnummer'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -43
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object ARFachNrLabel: TLabel
          Left = 368
          Top = 244
          Width = 386
          Height = 64
          AutoSize = False
          Caption = 'ARFachNrLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -53
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object ArtikelImagePanel: TPanel
          Left = 792
          Top = 46
          Width = 265
          Height = 261
          Anchors = [akTop, akRight, akBottom]
          BevelOuter = bvNone
          TabOrder = 0
          object ArtikelImage: TImage32
            Left = 0
            Top = 0
            Width = 265
            Height = 261
            Align = alClient
            Bitmap.ResamplerClassName = 'TNearestResampler'
            BitmapAlign = baTopLeft
            Scale = 1.000000000000000000
            ScaleMode = smNormal
            TabOrder = 0
          end
        end
      end
      object DistributeChangeNVEPanel: TPanel
        Left = 301
        Top = 67
        Width = 705
        Height = 204
        TabOrder = 3
        DesignSize = (
          705
          204)
        object HintLabel: TLabel
          Left = 248
          Top = 160
          Width = 688
          Height = 127
          Alignment = taCenter
          Anchors = [akLeft, akTop, akRight]
          AutoSize = False
          Caption = 'Test'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -35
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
      end
      object DistributeWaitPanel: TPanel
        Left = 270
        Top = 24
        Width = 705
        Height = 204
        TabOrder = 2
        DesignSize = (
          705
          204)
        object Label5: TLabel
          Left = 17
          Top = 42
          Width = 688
          Height = 127
          Alignment = taCenter
          Anchors = [akLeft, akTop, akRight]
          AutoSize = False
          Caption = 'Bitte Artikel scannen'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -35
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object Label17: TLabel
          Left = 17
          Top = 159
          Width = 269
          Height = 39
          Anchors = [akLeft, akBottom]
          Caption = 'Aktuelle Verteil-LE:'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -32
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
        object VTLNrLabel: TLabel
          Left = 312
          Top = 159
          Width = 80
          Height = 39
          Anchors = [akLeft, akBottom]
          Caption = '1234'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -32
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
        end
      end
    end
    object EndeTabSheet: TTabSheet
      Caption = 'EndeTabSheet'
      ImageIndex = 3
      DesignSize = (
        1067
        352)
      object Label9: TLabel
        Left = 3
        Top = 112
        Width = 1061
        Height = 74
        Alignment = taCenter
        Anchors = [akLeft, akTop, akRight]
        AutoSize = False
        Caption = 'Die Batchverteilung ist abgeschlossen'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -61
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
    end
  end
  object ButtonPanel: TPanel
    Left = 0
    Top = 421
    Width = 1075
    Height = 72
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      1075
      72)
    object PickToLigthPaintBox: TPaintBox
      Left = 688
      Top = 6
      Width = 59
      Height = 59
      Color = clOlive
      ParentColor = False
      OnPaint = PickToLigthPaintBoxPaint
    end
    object OkButton: TButton
      Left = 405
      Top = 7
      Width = 265
      Height = 59
      Caption = 'Best'#228'tigen'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -32
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      OnClick = OkButtonClick
    end
    object AbortButton1: TButton
      Left = 996
      Top = 41
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Abbrechen'
      ModalResult = 2
      TabOrder = 1
    end
    object ShowVerteilPlanButton: TButton
      Left = 4
      Top = 41
      Width = 157
      Height = 25
      Caption = #220'bersicht Verteilboxen'
      TabOrder = 2
      OnClick = ShowVerteilPlanButtonClick
    end
  end
  object FehlerPanel: TPanel
    Left = 0
    Top = 380
    Width = 1075
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 2
  end
  object StatusBar1: TStatusBar
    Left = 0
    Top = 493
    Width = 1075
    Height = 19
    Panels = <
      item
        Width = 200
      end
      item
        Width = 50
      end>
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 1000
    Top = 448
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 944
    Top = 440
  end
end
