object EditRetZustandForm: TEditRetZustandForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Returenzustand'
  ClientHeight = 566
  ClientWidth = 472
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    472
    566)
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 9
    Width = 42
    Height = 13
    Caption = 'Mandant'
  end
  object Label5: TLabel
    Left = 231
    Top = 65
    Width = 57
    Height = 13
    Caption = 'Reihenfolge'
  end
  object Label6: TLabel
    Left = 8
    Top = 109
    Width = 45
    Height = 13
    Caption = 'Definition'
  end
  object Label7: TLabel
    Left = 303
    Top = 65
    Width = 52
    Height = 13
    Caption = 'Verlinkt mit'
  end
  object Label8: TLabel
    Left = 383
    Top = 65
    Width = 79
    Height = 13
    Caption = 'Referenziert auf'
  end
  object Label3: TLabel
    Left = 8
    Top = 65
    Width = 39
    Height = 13
    Caption = 'Nummer'
  end
  object Label10: TLabel
    Left = 231
    Top = 109
    Width = 131
    Height = 13
    Caption = 'R'#252'ckmeldung ans Interface'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 159
    Width = 456
    Height = 8
    Shape = bsTopLine
  end
  object Bevel2: TBevel
    Left = 8
    Top = 57
    Width = 456
    Height = 8
    Shape = bsTopLine
  end
  object MandComboBox: TComboBoxPro
    Left = 8
    Top = 25
    Width = 447
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 0
  end
  object OkButton: TButton
    Left = 308
    Top = 533
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    Default = True
    ModalResult = 1
    TabOrder = 17
  end
  object AbortButton: TButton
    Left = 389
    Top = 533
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 18
  end
  object SelEdit: TEdit
    Left = 231
    Top = 81
    Width = 57
    Height = 21
    MaxLength = 6
    TabOrder = 2
    Text = 'SelEdit'
    OnKeyPress = NumberEditKeyPress
  end
  object DefEdit: TEdit
    Left = 8
    Top = 125
    Width = 209
    Height = 21
    MaxLength = 64
    TabOrder = 5
    Text = 'DefEdit'
  end
  object LinkEdit: TEdit
    Left = 303
    Top = 81
    Width = 66
    Height = 21
    MaxLength = 6
    TabOrder = 3
    Text = 'LinkEdit'
    OnKeyPress = NumberEditKeyPress
  end
  object RefEdit: TEdit
    Left = 383
    Top = 81
    Width = 72
    Height = 21
    MaxLength = 6
    TabOrder = 4
    Text = 'RefEdit'
    OnKeyPress = NumberEditKeyPress
  end
  object NrEdit: TEdit
    Left = 8
    Top = 81
    Width = 57
    Height = 21
    MaxLength = 6
    TabOrder = 1
    Text = 'NrEdit'
    OnKeyPress = NumberEditKeyPress
  end
  object AdminCheckBox: TCheckBox
    Left = 8
    Top = 176
    Width = 456
    Height = 17
    Caption = 'Nur mit besondere Berechtigung ausw'#228'hlbar'
    TabOrder = 7
  end
  object GroupBox1: TGroupBox
    Left = 8
    Top = 364
    Width = 456
    Height = 161
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Text'
    TabOrder = 16
    DesignSize = (
      456
      161)
    object Label4: TLabel
      Left = 8
      Top = 66
      Width = 22
      Height = 13
      Caption = 'Text'
    end
    object Label2: TLabel
      Left = 8
      Top = 20
      Width = 39
      Height = 13
      Caption = 'Sprache'
    end
    object Label9: TLabel
      Left = 8
      Top = 108
      Width = 44
      Height = 13
      Caption = 'Variablen'
    end
    object LangComboBox: TComboBoxPro
      Left = 8
      Top = 36
      Width = 439
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ItemHeight = 15
      TabOrder = 0
      OnChange = LangComboBoxChange
    end
    object TextEdit: TEdit
      Left = 8
      Top = 82
      Width = 439
      Height = 21
      TabOrder = 1
      Text = 'TextEdit'
      OnExit = TextEditExit
    end
    object VariableEdit: TEdit
      Left = 8
      Top = 124
      Width = 439
      Height = 21
      Hint = 
        'Variablen werden bei der Auswahl automatisch abgefragt und an St' +
        'elle von %1, %2 oder %3 im Text eingesetzt'
      MaxLength = 64
      TabOrder = 2
      Text = 'VariableEdit'
      OnExit = VariableEditExit
    end
  end
  object AddInfosCheckBox: TCheckBox
    Left = 8
    Top = 198
    Width = 447
    Height = 17
    Caption = 'Bei Auswahl dieses Zustandes zus'#228'tzliche Infos erfassen'
    TabOrder = 8
  end
  object SperrBesCheckBox: TCheckBox
    Left = 8
    Top = 260
    Width = 446
    Height = 17
    Caption = 'Best'#228'nde als gesperrte '#252'bernehmen'
    TabOrder = 11
  end
  object ReprintLabelCheckBox: TCheckBox
    Left = 8
    Top = 284
    Width = 447
    Height = 17
    Caption = 'SKU Label automatisch drucken'
    TabOrder = 12
  end
  object FinalStatCheckBox: TCheckBox
    Left = 8
    Top = 302
    Width = 447
    Height = 17
    Caption = 'Finaler Zustand, kann nicht mehr ver'#228'ndert werden'
    TabOrder = 13
  end
  object BeiAnnahmeCheckBox: TCheckBox
    Left = 8
    Top = 320
    Width = 447
    Height = 17
    Caption = 'Zustand schon bei Annnahem w'#228'hlbar'
    TabOrder = 14
  end
  object DefaultCheckBox: TCheckBox
    Left = 8
    Top = 338
    Width = 447
    Height = 17
    Caption = 'Defaultzustand'
    TabOrder = 15
  end
  object AddInfosErfassenCheckBox: TCheckBox
    Left = 16
    Top = 217
    Width = 369
    Height = 17
    Caption = 'Die zus'#228'tzliche Infos m'#252'ssen schon beim Erfassen angegeben'
    TabOrder = 9
  end
  object AddInfosMandatoryCheckBox: TCheckBox
    Left = 16
    Top = 233
    Width = 369
    Height = 17
    Caption = 
      'Die zus'#228'tzliche Infos m'#252'ssen erste bei '#196'nderung und zum Abschlus' +
      's'
    TabOrder = 10
  end
  object IFCEdit: TEdit
    Left = 231
    Top = 125
    Width = 224
    Height = 21
    MaxLength = 64
    TabOrder = 6
    Text = 'IFCEdit'
  end
end
