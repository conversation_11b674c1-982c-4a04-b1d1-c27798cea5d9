﻿//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : FrontendUtils
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/XE11/FrontendUtils.pas $
// $Revision: 276 $
// $Modtime: 15.01.24 8:41 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Hilfsfunktionen für das Frontend
//*****************************************************************************
{$i compilers.inc}

unit FrontendUtils;

interface

uses
  Windows, Classes, StdCtrls, Forms, Grids, RegistryUtils, VCLUtilitys, DB, Menus, ComboboxPro,
  TerminalServices, ComCtrls, Graphics, LogFile, EAN128Utils, GR32, GR32_Image, LVSGlobalDaten, CheckLst;

type
  TArryOfInteger = array of integer;

  TMHDCheckOption = (cmhdVergangenheit, cmhdZukunft, cmhdCheckVergangenheit);
  TMHDCheckOptions = set of TMHDCheckOption;

  TDBItemsDaten = class (TObject)
    ItemsCol  : String;
    ItemsWert : String;
    ItemsText : String;
    ItemsDesc : String;
    ItemsArt  : String;

    constructor Create (const RecItemsCol, RecItemsWert, RecItemsText, RecItemsDesc, RecItemsArt : String);
  end;

  TComboboxMandantRef = class (TComboboxRef)
    RefSubMand   : Integer;
    DefLiefDauer : Integer;
    CfgOptions   : String;
    Mandant      : String;
    SubMandant   : String;

    constructor Create (const pRefMand, pRefSubMand : Integer); overload;
    constructor Create (const pMandant, pSubMandant : String; const pRefMand, pRefSubMand, pDefLiefDauer : Integer; const pCfgOptions : String); overload;
  end;

  TComboBoxArtikelRef = class (TComboBoxRef)
    RefAR   : Integer;

    constructor Create (const pRecRefAE, pRecRefAR : Integer);
  end;

  TComboboxItems = class (TComboboxRef)
    ItemsWert : String;
    ItemsText : String;
    ItemsDesc : String;

    constructor Create (const pRef : Integer; const RecItemsWert, RecItemsText, RecItemsDesc : String);
  end;

  TComboboxPlanRef = class (TComboboxRef)
    LagerRef   : Integer;
    BereichRef : Integer;

    constructor Create (const pRef, pLagerRef, pBereichRef : Integer);
  end;

  TComboboxSysBenRef = class (TComboboxRef)
    Art : Integer;
    Default : Boolean;

    constructor Create (const pRef, pArt : Integer; const pDefault : Boolean);
  end;

  TComboBoxKommLBRef = class (TComboBoxRef)
    LagerRef : Integer;

    constructor Create (const pRecRef, pRecLagerRef : Integer);
  end;

  TComboBoxLBPlusZoneRef = class (TComboBoxRef)
    RefLager : Integer;
    RefZone  : Integer;

    constructor Create (const pRecRef, pRecRefLager, pRecRefZone : Integer);
  end;

  TComboBoxLBLPRef = class (TComboBoxRef)
    RefLP   : Integer;
    Options : String;

    constructor Create (const pRecRef, pRecLPRef : Integer; const OptStr : String = '');
  end;

  TComboBoxLTData = class (TComboBoxRef)
    LagerRef : Integer;
    LTID : String;

    constructor Create (const pRecRef, pRecLagerRef : Integer; const pRecLTID : String);
  end;

  TArtikelEinheitEinfo = class (TComboBoxRef)
    RefArEinheit : Integer;

    constructor Create (const RecRef, EinheitRef : Integer);
  end;

  TComboboxLandStr = class (TComboboxStr)
    ISO3       : String;
    ISO2       : String;
    HasState   : boolean;
    HasZIPCode : boolean;

    constructor Create (const pISO2, pISO3 : String; const pHasState, pHasZIPCode : Boolean);
  end;

  TComboboxSysTexte = class (TComboBoxRef)
    SelectText : String;
    Variablen  : String;
    Definition : String;
    Attribut   : String;

    constructor Create (const RecRef : Integer; const RecDef, RecSel, RecVar, RecAttr : String);
  end;

  TComboboxZustand = class (TComboBoxRef)
    OptAskClarifyHint : Char;
    OptSperrBestand   : Char;
    OptLockStatus     : Char;
    OptReprint        : Char;
    OptDefault        : Char;
    Definition        : String;
    ZustandNr         : Integer;

    constructor Create (const RecRef, pZustandNr : Integer; const pOptAskClarifyHint : Char; const pOptSperrBestand, pOptLockStatus, pOptReprint, pOptDefault : Char; const pDefinition : String);
  end;

  TComboboxSped = class (TComboBoxRef)
    SpedKennung : String;

    constructor Create (const RecRef : Integer; const pSpedKennung : String);
  end;

  TComboBoxSpedition = class (TComboBoxRef)
    RefProd : Integer;

    constructor Create (const pRecRef, pRecRefProd : Integer);
  end;

  TBatchConfigEntry = class (TComboBoxRef)
    MaxAuftrag     : Integer;
    RefKommPlanGrp : Integer;
    RefKommBen     : Integer;
    RefKommGrp     : Integer;
    MinVPE         : Integer;
    MaxVPE         : Integer;


    AblaufArt      : String;
    AuftragArt     : String;

    constructor Create (const pRef : Integer; const pAuftragArt, pAblaufArt : String; const pMinVPE, pMaxVPE, pMaxAuftrag, pRefKommPlanGrp, pRefKommBen, pRefKommGrp : Integer);
  end;

  TArtikelInfo = class (TObject)
    RefMand,
    RefSubMand,
    RefArtikel,
    RefArtikelEinheit : Integer;
    RefEinheit        : Integer;
    RefInhalt         : Integer;
    RefLT             : Integer;
    ArtikelNr         : String;
    MandArtikelNr     : String;
    Einheit           : String;
    BasisEANCode      : String;
    BasisBarcode      : String;
    InhaltArtikelNr   : String;
    InhaltEANCode     : String;
    InhaltBarcode     : String;
    InhaltAnzahl      : Integer;
    UVPAnzahl         : Integer; //Menge der kleinsten Umverpackung, exitiert z. Z. nur bei Rehnau
    ArtikelText       : String;
    ColliName         : String;
    PicturePath       : String;
    RestlaufzeitWE    : Integer;
    RestlaufzeitWA    : Integer;
    RestlaufzeitPROD  : Integer;
    RestlaufzeitKomm  : Integer;
    MaxRestlaufzeitWE : Integer;
    MaxRestlaufzeitWA : Integer;
    NettoGewicht      : Double; // Gewicht des Artikels in Gramm
    GewichtFlag       : Boolean;
    MehrMengeFlag     : Boolean;
    MHDArt            : Char;
    ChargeArt         : Char;
    UniqueCharge      : Boolean;
    AutoCharge        : Boolean;
    BestandIDArt      : Char;
    UniqueBestandID   : Boolean;
    OrgaInventFlag    : Boolean;
    SerialFlag        : Boolean;
    BesSerialFlag     : Boolean;
    IsColliFlag       : Boolean;
    MultiColliFlag    : Boolean;
    Volumen           : Int64;
    MaxSize           : Integer;
    PALFaktor         : Integer;
    PalHeight         : Integer;
    UnitEinheit       : Integer;
    UnitNorm          : Integer;
    UnitNetto         : Integer;
    OptSperrgut       : Boolean;
    OptBigItem        : Boolean;
    OptReadyShip      : Boolean;
    OptPfandArtikel   : Boolean;
    OptBeschaffung    : Boolean;

    procedure Clear;
  end;

  TSpedInfos = record
    RefSped         : Integer;
    RefSpedProd     : Integer;
    SpedName        : String;
    SpedDesc        : String;
    SpedKennung     : String;
    SpedMaxGw       : Integer;
    PrintCN23       : Boolean;
    AutoExport      : Boolean;
    GewichtPflich   : Boolean;
    DefaultGewicht  : Integer;
    LTDimRequired   : Boolean;
    SingleColliPack : Boolean;
    RefDefaultLT    : Integer;
    DefaultLT       : String;
    DefaultLTTara     : Integer;
    DefaultLTL        : Integer;
    DefaultLTB        : Integer;
    DefaultLTH        : Integer;
    NeedShippingUnits : boolean;
    NeedShippingNo    : boolean;
    EnterShippingNo   : boolean;
    SpedLogo          : String;
    SpedColor         : TColor;
    PresetDIM         : boolean;
    PresetWeight      : boolean;
  end;


var
  UserLog         : TLogfile         = Nil;
  UserReg         : TRegistryModule  = Nil;
  TerminalService : TTerminalService = Nil;
  NonEUList       : TStringList;

function OpenLVSDatabase (const AppKennung, SubKeyName  : String; const AdminFlag : Boolean = False): Integer;
function ChangeMandantLager (const MandantName, LocationName, LagerName : String; var ErrorText : String): Integer;

function  GetMandantName  (const Ref : Integer) : String;
function  GetLagerName    (const Ref : Integer) : String;
function  GetLocationName (const Ref : Integer) : String;

function  GetArtikelInfos        (const Ref : Integer; ArtikelInfo : TArtikelInfo) : Integer; overload;
function  GetArtikelInfos        (const RefAR, RefAREinheit : Integer; ArtikelInfo : TArtikelInfo) : Integer; overload;
function  GetArtikelEinheitInfos (const RefAR, RefEinheit : Integer; ArtikelInfo : TArtikelInfo) : Integer; overload;
function  GetArtikelEinheitInfos (const RefAR, RefEinheit, RefBarcode : Integer; ArtikelInfo : TArtikelInfo) : Integer; overload;

function  GetArtikelNummerInfos        (const Mandant, ArtikelNr : String; ArtikelInfo : TArtikelInfo) : Integer;overload;
function  GetArtikelNummerInfos        (const RefMand : Integer; const ArtikelNr : String; ArtikelInfo : TArtikelInfo) : Integer;overload;
function  GetArtikelNummerInfos        (const RefMand, RefSubMand : Integer; const ArtikelNr : String; ArtikelInfo : TArtikelInfo) : Integer;overload;
function  GetMandantArtikelNummerInfos (const RefMand, RefSubMand : Integer; const MandArtikelNr : String; ArtikelInfo : TArtikelInfo) : Integer;
function  GetArtikelColliInfos         (const RefMand : Integer; const ArtikelNr, ColliNr : String; ArtikelInfo : TArtikelInfo) : Integer;overload;

function  GetArtikelEANInfos            (const Mandant, EAN : String; ArtikelInfo : TArtikelInfo) : Integer; overload;
function  GetArtikelEANInfos            (const RefMand : Integer; const EAN : String; ArtikelInfo : TArtikelInfo) : Integer; overload;
function  GetArtikelEANInfos            (const RefMand, RefSubMand : Integer; const Process, EAN : String; ArtikelInfo : TArtikelInfo; var RecordCount : Integer) : Integer; overload;
function  GetBatchArtikelEANInfos       (const RefBatch : Integer; const EAN : String; ArtikelInfo : TArtikelInfo) : Integer;
function  GetWEArtikelEANInfos          (const RefMand, RefSubMand, RefBestellung : Integer; const EAN : String; ArtikelInfo : TArtikelInfo; MasterArtikelInfo : TArtikelInfo; var RecordCount : Integer) : Integer;
function  GetArtikelBarcodeInfos        (const RefMand : Integer; const BarStr : String; ArtikelInfo : TArtikelInfo) : Integer; overload;
function  GetArtikelBarcodeInfos        (const RefMand, RefSubMand : Integer; const BarStr : String; ArtikelInfo : TArtikelInfo) : Integer; overload;
function  GetBatchArtikelBarcodeInfos   (const RefBatch : Integer; const BarStr : String; ArtikelInfo : TArtikelInfo) : Integer; overload;
function  GetAuftragArtikelBarcodeInfos (const RefAuf : Integer; const BarStr : String; ArtikelInfo : TArtikelInfo) : Integer; overload;
function  GetAuftragArtikelEANInfos     (const RefAuf : Integer; const EAN : String; ArtikelInfo : TArtikelInfo) : Integer; overload;
function  GetRetAvisArtikelEANInfos     (const RefRetAvis : Integer; const EAN : String; ArtikelInfo : TArtikelInfo) : Integer; overload;

function  LoadMandantCombobox            (ComboBox : TCustomComboBox; const RefLocation : Integer = -1; const RefMandant : Integer = -1) : Integer;
function  LoadSubMandantCombobox         (ComboBox : TCustomComboBox; const RefLocation : Integer; const RefMandant : Integer; const VerwendungsArt : String = '') : Integer;
function  LoadMandantSubMandantCombobox  (ComboBox : TCustomComboBox; const RefLocation : Integer = -1; const RefMandant : Integer = -1) : Integer;
function  GetMandantRef                  (ComboBox : TCustomComboBox; const Index : Integer; var RefMand, RefSubMand : Integer) : Integer;
function  FindMandantRef                 (ComboBox : TCustomComboBox; const RefMand : Integer) : Integer;
function  LoadTraderComboBox             (ComboBox : TCustomComboBox; const RefLocation : Integer; const RefMandant : Integer) : Integer; overload;
function  LoadTraderComboBox             (ComboBox : TCustomComboBox; const RefLocation : Integer; const RefMandant, RefSubMand : Integer) : Integer; overload;

function  LoadLocationCombobox     (ComboBox : TCustomComboBox; const RefMandant : Integer = -1) : Integer;
function  LoadLagerCombobox        (ComboBox : TCustomComboBox; const Location : String=''; const RefMandant : Integer = -1; const RefLager : Integer = -1) : Integer; overload;
function  LoadLagerCombobox        (ComboBox : TCustomComboBox; const RefLocation : Integer; const RefMandant : Integer = -1; const RefLager : Integer = -1) : Integer; overload;
function  LoadOptionLagerCombobox  (ComboBox : TCustomComboBox; const Option : Integer; const Location : String=''; const RefMandant : Integer = -1; const RefLager : Integer = -1) : Integer;
function  LoadManagedLagerCombobox (ComboBox : TCustomComboBox; const Location : String=''; const RefMandant : Integer = -1; const RefLager : Integer = -1) : Integer;
function  LoadAlleLagerCombobox    (ComboBox : TCustomComboBox; const LagerArt : String; const RefLocation : Integer = -1; const RefMandant : Integer = -1; const RefLager : Integer = -1) : Integer;
function  LoadDepotCombobox        (ComboBox : TCustomComboBox; const RefMandant : Integer) : Integer;
function  LoadSpedProdukt          (ComboBox : TCustomComboBox; const RefSped : Integer; var DefaultIndex : Integer) : Integer;
function  LoadSpedDepotCombobox    (ComboBox : TCustomComboBox; const RefMandant : Integer; const SpedKennung : String) : Integer;
function  LoadAlleDepotCombobox    (ComboBox : TCustomComboBox; const RefLocation : Integer; const RefMandant : Integer; const RefLager : Integer) : Integer;
function  LoadLTCombobox           (ComboBox : TCustomComboBox; const Einsatz: string; const RefLocation: Integer; const RefLager: Integer = -1) : Integer;
function  LoadLBCombobox           (ComboBox : TCustomComboBox; const Einsatz: String; const RefLager: Integer = -1; const RefLocation: Integer = -1; const RefMand : Integer = -1) : Integer;
function  LoadLBComboboxNullEntry  (ComboBox : TCustomComboBox; const Einsatz, NullEntry: String; const RefLager: Integer = -1; const RefLocation: Integer = -1; const RefMand : Integer = -1) : Integer;
function  LoadLBPlusLBZoneCombobox (ComboBox : TCustomComboBox; const Einsatz, NullEntry : String; const RefLocation, RefLager, RefMand : Integer) : Integer;
function  LoadLBZoneCombobox       (ComboBox : TCustomComboBox; const RefLB: Integer) : Integer;
function  LoadLPCombobox           (ComboBox : TCustomComboBox; const RefLB: Integer; const FreiFlag : Boolean; const RefAR, RefLP: Integer) : Integer;
function  LoadLPZoneCombobox       (ComboBox : TCustomComboBox; const RefLBZone: Integer; const FreiFlag : Boolean; const RefAR, RefLP: Integer) : Integer;
function  LoadSpedition            (ComboBox : TCustomComboBox; const Einsatz, NullEntry : String; const RefLocation, RefLager, RefMand : Integer) : Integer;
function  LoadSpeditionAndProd     (ComboBox : TCustomComboBox; const Einsatz, NullEntry : String; const RefLocation, RefLager, RefMand : Integer) : Integer;
function  LoadUserGruppe           (ComboBox : TCustomComboBox; const GruppenName : String; const RefLoc : Integer = -1) : Integer;
function  LoadGruppe               (ComboBox : TCustomComboBox; const GruppenArt, GruppenName : String; const RefLoc : Integer = -1) : Integer;
function  LoadPlanungCombobox      (ComboBox : TCustomComboBox; const ArtStr : String) : Integer;
function  LoadSachbearbeiter       (ComboBox : TCustomComboBox; const ArtStr : String; const RefMandant : Integer = -1) : Integer;
function  LoadRelationen           (ComboBox : TCustomComboBox; const ArtStr : String; const RefLager : Integer = -1) : Integer;
function  LoadPackplatz            (ComboBox : TComboBoxPro; const RefLager : Integer = -1; const Options : String = '') : Integer;
function  LoadDepotRelationen      (ComboBox : TCustomComboBox; const ArtStr : String; const RefDepot : Integer) : Integer;
function  LoadSysTexte             (ComboBox : TCustomComboBox; const AreaStr : String; const RefMandant : Integer; const LeerEntry : Boolean) : Integer; overload;
function  LoadSysTexte             (ComboBox : TCustomComboBox; const AreaStr, SubAreaStr : String; const RefMandant : Integer; const LeerEntry : Boolean) : Integer; overload;
function  LoadZustandTexte         (ComboBox : TCustomComboBox; const AreaStr : String; const RefMandant : Integer; const AdminFlag, AnnahmeFlag : Boolean) : Integer;
function  FindComboboxDefinition   (ComboBox : TCustomComboBox; const DefStr : String; const Default : Integer = -1) : Integer;
function  LoadRetoureCategory      (ComboBox : TCustomComboBox; const RefMandant, RefSubMandant, RefLager : Integer; const AdminFlag : Boolean) : Integer;
function  LoadLandCombobox         (ComboBox : TCustomComboBox) : Integer;
function  FindComboboxZustandRef   (ComboBox : TCustomComboBox; const ZustandRef : Integer; const Default : Integer = -1) : Integer;
function  FindComboboxLand         (ComboBox : TCustomComboBox; const LandStr : String; const Default : Integer = -1) : Integer;
function  FindComboboxSysText      (ComboBox : TCustomComboBox; const DefStr, TextStr : String; const Default : Integer = -1) : Integer;

function  LoadWarengruppenCombobox    (ComboBox : TCustomComboBox; const RefLocation, RefMandant, RefSubMand : Integer) : Integer;
function  LoadWarenLieferantCombobox  (ComboBox : TCustomComboBox; const SortType : Integer; const RefMandant, RefSubmand, RefLager : Integer) : Integer;

function  LoadBestandCategoryCombobox (ComboBox : TCustomComboBox; const RefMandant, RefLager : Integer; const Usage : String; const RefSel : Integer) : Integer;

function  FindComboboxDefault    (ComboBox : TCustomComboBox; const Default : Integer = -1) : integer;

function  GetComboBoxItems      (ComboBox : TCustomComboBox; const Index : Integer = -1) : TComboboxItems;
function  GetComboBoxItemWert   (ComboBox : TCustomComboBox; const Index : Integer = -1) : String;

function  LoadComboxDBItems      (ComboBox : TCustomComboBox; const TableName, ColumnName : String; const pDisplayDBItems : Boolean = True; const pDisplayDesc : Boolean = False) : Integer;
function  GetComboBoxDBItem      (ComboBox : TCustomComboBox; const Index : Integer = -1) : TDBItemsDaten;
function  GetComboBoxDBItemWert  (ComboBox : TCustomComboBox; const Index : Integer = -1) : String;
function  FindComboboxDBItem     (ComboBox : TCustomComboBox; const pDBItem : String; const Default : Integer = -1): Integer;
function  FindComboboxDBItemWert (ComboBox : TCustomComboBox; const pDBItem : String; const Default : Integer = -1): Integer;

function  GetComboBoxLBRef      (ComboBox : TCustomComboBox; const Default : Integer = -1): Integer;
function  GetComboBoxLPRef      (ComboBox : TCustomComboBox; const Default : Integer = -1): Integer;
function  FindComboboxLBLPRef   (ComboBox : TCustomComboBox; const RefLB, RefLP : Integer; const Default : Integer = -1): Integer;

function  FindComboboxMandRef   (ComboBox : TCustomComboBox; const RefMand, RefSubMand : Integer; const Default : Integer = -1): Integer;

function LoadMandantCheckListBox            (CheckListBox : TCheckListBox; const OnlyActive : boolean; const activeRefs: TArryOfInteger; const RefLocation: Integer = -1; const RefMandant : Integer = -1): Integer;
function LoadMandantSubMandantCheckListBox  (CheckListBox : TCheckListBox; const OnlyActive : boolean; const activeRefs: TArryOfInteger; const RefLocation: Integer = -1; const RefMandant : Integer = -1): Integer;

function  LoadListDBItems       (List : TList; const TableName, ColumnName : String) : Integer; overload;
function  LoadListDBItems       (List : TList; const TableName : String; const ColumnNames : array of string) : Integer; overload;

function  CheckMHD (const DateStr : String; const MinRLZ : Integer; const FehlerMsg, DatumName : String; var DateTime : TDateTime; var ErrorText : String) : Boolean; overload;
function  CheckMHD (const DateStr : String; const CheckOptions : TMHDCheckOptions; const MinRLZ : Integer; const FehlerMsg, DatumName : String; var DateTime : TDateTime; var ErrorText : String) : Boolean; overload;

function  CheckMischNVE (const RefNVE : Integer) : Boolean;

function GetLSMaxFutureDays (const pRefMand: Integer;var Tage : Integer; var errormsg: string) : Integer;

function  GetOpt        (const OptStr : String; const Index : Integer) : Char; overload;
function  GetOpt        (const FirstOptStr, SecondOptStr : String; const Index : Integer) : Char; overload;
function  CheckOpt      (const OptStr : String; const Index : Integer) : Boolean; overload;
function  CheckOpt      (const FirstOptStr, SecondOptStr : String; const Index : Integer) : Boolean; overload;
function  CheckOptState (const OptStr : String; const Index : Integer) : TCheckBoxState;
function  SetOpt        (const OptStr : String; const Index : Integer) : String; overload;
function  SetOpt        (const OptStr : String; const Index : Integer; const OptChar : Char) : String; overload;
function  SetOpt        (const OptStr : String; const Index : Integer; const State : TCheckBoxState) : String; overload;
function  ResetOpt      (const OptStr : String; const Index : Integer) : String;

function  MinRestlaufzeit (const MinimalZeit, Restlaufzeit  : Integer) : Integer;

function  DBGetReferenz    (Field : TField) : Integer;
function  DBGetIntegerNull (Field : TField) : Integer; overload;
function  DBGetDoubleNull  (Field : TField) : Double; overload;

function LoadQueryMenu (Sender : TObject; const MenuName : String; StartMenuItem : TMenuItem; OnClickEvent : TNotifyEvent) : Integer;

function GetMandantConfigOpt (const RefMand : Integer) : String;

function GetWhereMandLager : String;
function GetWhereMand      : String;

function DetectArtikelBarcode (const RefMand : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var errmsg : String) : Integer; overload;
function DetectArtikelBarcode (const RefMand : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var EANBarcode : TEANBarcode; var errmsg : String) : Integer; overload;
function DetectArtikelBarcode (const RefMand : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var EANBarcode : TEANBarcode; var BarcodeInfos : TBarcodeInfos; var errmsg : String) : Integer; overload;

function DetectAuftragArtikelBarcode (const RefAuf : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var errmsg : String) : Integer; overload;
function DetectAuftragArtikelBarcode (const RefAuf : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var EANBarcode : TEANBarcode; var errmsg : String) : Integer; overload;
function DetectAuftragArtikelBarcode (const RefAuf : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var EANBarcode : TEANBarcode; var BarcodeInfos : TBarcodeInfos; var errmsg : String) : Integer; overload;
function DetectLTTypeEAN             (const EAN : String; var RefLT : Integer; var LTName : String) : integer;
function DetectLTTypeID              (const ID : String; var RefLT : Integer; var LTName : String) : integer;

function CheckLEBarcode (const ScanCode : AnsiString; var LENr : String) : boolean;
function CheckLPNrBarcode (const ScanCode : AnsiString; var LPNr : String) : boolean;

function IsArbeitstag (const Location : Integer; const Datum : TDateTime) : Boolean;
function IsLandEU (const ISOLand : String; const ZIPCode : String = '') : Boolean;

procedure SetTabSheetTabWide (PageControl : TPageControl);

procedure PlaySound (const WaveName : String);

procedure DrawStatusBitmap (Canvas : TCanvas; const rect : TRect; bm : TBitmap);

function GetSpedInfos (const RefSped, RefSpedProd : Integer; const Ausland : Boolean; var SpedInfos : TSpedInfos) : Integer;

function GetIntegerParameter (const Line : String) : Integer;

procedure ShowArtikelPicture (ArtikelImage : TImage32; const Filename : String);

function GetLabelPrinterPort (const RefPrt : Integer) : String;

function StrToFile(const FileName, SourceString : string; const Endcoding : Boolean = true) : Integer;

procedure CheckScreen (Form : TForm);

function GetEinlagerVorschlag (const RefLager, RefAE : Integer; var InfoStr : String) : Integer;

procedure ClearNonEUList;

function  GetMandantRefFromName (const Name : string) : Integer;

implementation

uses
  {$ifdef Trace}
    Trace,
  {$endif}
  Messages, DateUtils, ResourceText, LVSKundenBarcodes, LVSConst,
  BetterADODataSet, BarCodeScanner, LVSWarenausgang,
  Controls, SysUtils, StringUtils, DatenModul, ADODB, Dialogs, EncryptUtils, DBGridUtilModule, VerInfos, PrintModul,
  LVSBenutzer, ACOModul, LVSSecurity, LVSFrontLogin, ConfigModul, LVSDatenInterface, ErrorTracking,

  {$ifdef Numpad}
    NumPad,
  {$endif}

  FrontendMessages, MailUtils, CmdLineUtils, mmSystem, JPEG,
  {$ifndef DELPHIXE_UP}GraphicEx,{$else}GR32_PortableNetworkGraphic,GR32_PNG,{$endif}
  Ora, OraSmart, LablePrinterUtils;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TDBItemsDaten.Create (const RecItemsCol, RecItemsWert, RecItemsText, RecItemsDesc, RecItemsArt : String);
begin
  inherited Create;

  ItemsCol  := RecItemsCol;
  ItemsWert := RecItemsWert;
  ItemsText := RecItemsText;
  ItemsDesc := RecItemsDesc;
  ItemsArt  := RecItemsArt;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboboxItems.Create (const pRef : Integer; const RecItemsWert, RecItemsText, RecItemsDesc : String);
begin
  inherited Create (pRef);

  ItemsWert := RecItemsWert;
  ItemsText := RecItemsText;
  ItemsDesc := RecItemsDesc;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboboxPlanRef.Create (const pRef, pLagerRef, pBereichRef : Integer);
begin
  inherited Create (pRef);

  LagerRef   := pLagerRef;
  BereichRef := pBereichRef;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboboxMandantRef.Create (const pRefMand, pRefSubMand : Integer);
begin
  inherited Create (pRefMand);
  RefSubMand   := pRefSubMand;

  Mandant    := '';
  SubMandant := '';

  DefLiefDauer := -1;
  CfgOptions   := '';
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboboxMandantRef.Create (const pMandant, pSubMandant : String; const pRefMand, pRefSubMand, pDefLiefDauer : Integer; const pCfgOptions : String);
begin
  inherited Create (pRefMand);

  Mandant := pMandant;
  SubMandant := pSubMandant;

  RefSubMand   := pRefSubMand;
  DefLiefDauer := pDefLiefDauer;
  CfgOptions   := pCfgOptions;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboBoxArtikelRef.Create (const pRecRefAE, pRecRefAR : Integer);
begin
  inherited Create (pRecRefAE);

  RefAR := pRecRefAR;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboboxSysBenRef.Create (const pRef, pArt : Integer; const pDefault : Boolean);
begin
  inherited Create (pRef);

  Art     := pArt;
  Default := pDefault;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboBoxLBPlusZoneRef.Create (const pRecRef, pRecRefLager, pRecRefZone : Integer);
begin
  inherited Create (pRecRef);

  RefZone  := pRecRefZone;
  RefLager := pRecRefLager;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboBoxKommLBRef.Create (const pRecRef, pRecLagerRef : Integer);
begin
  inherited Create (pRecRef);

  LagerRef := pRecLagerRef;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboBoxLBLPRef.Create (const pRecRef, pRecLPRef : Integer; const OptStr : String);
begin
  inherited Create (pRecRef);

  RefLP   := pRecLPRef;
  Options := OptStr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboBoxLTData.Create (const pRecRef, pRecLagerRef : Integer; const pRecLTID : String);
begin
  inherited Create (pRecRef);

  LTID     := pRecLTID;
  LagerRef := pRecLagerRef;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TArtikelEinheitEinfo.Create (const RecRef, EinheitRef  : Integer);
begin
  inherited Create (RecRef);

  RefArEinheit := EinheitRef;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboboxLandStr.Create (const pISO2, pISO3 : String; const pHasState, pHasZIPCode : Boolean);
begin
  inherited Create (pISO2);

  ISO2 := pISO2;
  ISO3 := pISO3;
  HasState := pHasState;
  HasZIPCode := pHasZIPCode;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboboxSysTexte.Create (const RecRef : Integer; const RecDef, RecSel, RecVar, RecAttr : String);
begin
  inherited Create (RecRef);

  SelectText := RecSel;
  Variablen  := RecVar;
  Definition := RecDef;
  Attribut   := RecAttr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboboxZustand.Create (const RecRef, pZustandNr : Integer; const pOptAskClarifyHint : Char; const pOptSperrBestand, pOptLockStatus, pOptReprint, pOptDefault : Char; const pDefinition : String);
begin
  inherited Create (RecRef);

  Definition        := pDefinition;
  OptDefault        := pOptDefault;
  OptSperrBestand   := pOptSperrBestand;
  OptAskClarifyHint := pOptAskClarifyHint;
  OptLockStatus     := pOptLockStatus;
  OptReprint        := pOptReprint;
  ZustandNr         := pZustandNr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboboxSped.Create (const RecRef : Integer; const pSpedKennung : String);
begin
  inherited Create (RecRef);

  SpedKennung := pSpedKennung;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 26.10.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboBoxSpedition.Create (const pRecRef, pRecRefProd : Integer);
begin
  inherited Create (pRecRef);

  RefProd := pRecRefProd;
end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TBatchconfigEntry.Create (const pRef : Integer; const pAuftragArt, pAblaufArt : String; const pMinVPE, pMaxVPE, pMaxAuftrag, pRefKommPlanGrp, pRefKommBen, pRefKommGrp : Integer);
begin
  inherited Create (pRef);

  MinVPE         := pMinVPE;
  MaxVPE         := pMaxVPE;
  MaxAuftrag     := pMaxAuftrag;
  AblaufArt      := pAblaufArt;
  AuftragArt     := pAuftragArt;
  RefKommPlanGrp := pRefKommPlanGrp;
  RefKommBen     := pRefKommBen;
  RefKommGrp     := pRefKommGrp;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TArtikelInfo.Clear;
begin
  RefArtikel        := -1;
  RefEinheit        := -1;
  RefArtikelEinheit := -1;
  RefLT             := -1;

  ArtikelNr         := '';
  MandArtikelNr     := '';
  BasisEANCode      := '';
  ArtikelText       := '';
  Einheit           := '';
  InhaltEANCode     := '';
  InhaltArtikelNr   := '';
  ColliName         := '';
  PicturePath       := '';

  InhaltAnzahl      := -1;
  UVPAnzahl         := -1;  //Menge der kleinsten Umverpackung
  NettoGewicht      := -1;
  RestlaufzeitWA    := -1;
  RestlaufzeitWE    := -1;
  RestlaufzeitPROD  := -1;
  MaxRestlaufzeitWE := -1;
  MaxRestlaufzeitWA := -1;

  Volumen        := -1;
  MaxSize        := -1;
  PalHeight      := -1;
  PALFaktor      := -1;
  GewichtFlag    := False;
  MehrMengeFlag  := False;
  OrgaInventFlag := False;
  MultiColliFlag := False;
  IsColliFlag    := False;
  MHDArt         := #0;
  ChargeArt      := 'O';
  UniqueCharge   := False;
  AutoCharge     := False;
  BestandIDArt    := 'N';
  UniqueBestandID := False;

  UnitEinheit    := -1;
  UnitNetto      := -1;

  OptSperrgut    := False;
  OptBigItem     := False;
  OptReadyShip   := False;
  OptPfandArtikel:= False;
  OptBeschaffung := False;
end;

//******************************************************************************
//* Function Name: LoadLagerCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetMandantName  (const Ref : Integer) : String;
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add('select NAME from V_MANDANT where REF=:Ref');
    query.Parameters.ParamByName('Ref').Value := Ref;

    query.Open;

    Result := query.Fields [0].AsString;

    query.Close;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name: LoadLagerCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetLagerName    (const Ref : Integer) : String;
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add('select NAME from V_LAGER where REF=:Ref');
    query.Parameters.ParamByName('Ref').Value := Ref;

    query.Open;

    Result := query.Fields [0].AsString;

    query.Close;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name: LoadLagerCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetLocationName (const Ref : Integer) : String;
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add('select NAME from V_LOCATION wher REF=:Ref');
    query.Parameters.ParamByName('Ref').Value := Ref;

    query.Open;

    Result := query.Fields [0].AsString;

    query.Close;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name: LoadLagerCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle sichtbaren Lager in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBopPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadLocationCombobox (ComboBox : TCustomComboBox; const RefMandant : Integer = -1) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadLocationCombobox');
    TraceParameter ('RefMandant', RefMandant);
  {$ENDIF}

  ComboBox.Items.BeginUpdate;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    query.SQL.Add ('select REF,NAME,BESCHREIBUNG from V_PCD_LOCATION');
    if (RefMandant <> -1) then begin
      query.SQL.Add ('where REF in (select REF_LOCATION from V_LOCATION_REL_LAGER where REF_LAGER in (select REF_LAGER from V_MANDANT_REL_LAGER where REF_MAND=:RefMandant))');
      query.Parameters.ParamByName('RefMandant').Value := RefMandant;
    end;
    query.SQL.Add ('order by lower (NAME)');

    try
      query.Open;

      while not (query.Eof) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadLagerCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle sichtbaren Lager in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBopPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadAlleLagerCombobox (ComboBox : TCustomComboBox; const LagerArt : String; const RefLocation : Integer; const RefMandant : Integer; const RefLager : Integer) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  wherestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadAlleLagerCombobox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('LagerArt   ', LagerArt);
    TraceParameter ('RefLocation', RefLocation);
    TraceParameter ('RefMandant ', RefMandant);
    TraceParameter ('RefLager   ', RefLager);
  {$ENDIF}

  ComboBox.Items.BeginUpdate;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    query.SQL.Add ('select REF,LOCATION,NAME,BESCHREIBUNG from V_PCD_LAGER_ART');

    wherestr := '';

    if (Length (LagerArt) > 0) then
      wherestr := wherestr + 'LAGER_ART in ('+LagerArt+')';

    if (RefLager <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF='+IntToStr (RefLager);
    end;

    if (RefLocation <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_LOCATION='+IntToStr (RefLocation);
    end;

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF in (select REF_LAGER from V_MANDANT_REL_LAGER where REF_MAND='+IntToStr (RefMandant)+')';
    end;

    if (Length (wherestr) > 0) Then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by lower (NAME)');

    try
      query.Open;

      while not (query.Eof) do begin
        if (RefLocation <> -1) then
          ComboBox.Items.AddObject (query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger))
        else ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadLagerCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle sichtbaren Lager in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBopPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadAlleDepotCombobox (ComboBox : TCustomComboBox; const RefLocation : Integer; const RefMandant : Integer; const RefLager : Integer) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  wherestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadAlleDepotCombobox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('RefLocation', RefLocation);
    TraceParameter ('RefMandant ', RefMandant);
    TraceParameter ('RefLager   ', RefLager);
  {$ENDIF}

  ComboBox.Items.BeginUpdate;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    query.SQL.Add ('select REF,NAME,BESCHREIBUNG from V_DEPOT');

    wherestr := '';

    if (RefLager <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF='+IntToStr (RefLager);
    end;

    if (RefLocation <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_LOCATION='+IntToStr (RefLocation);
    end;

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF in (select REF_LAGER from V_MANDANT_REL_LAGER where REF_MAND='+IntToStr (RefMandant)+')';
    end;

    if (Length (wherestr) > 0) Then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by lower (NAME)');

    try
      query.Open;

      while not (query.Eof) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadLagerCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle sichtbaren Lager in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBopPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadDepotCombobox (ComboBox : TCustomComboBox; const RefMandant : Integer) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  wherestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadDepotCombobox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('RefMandant ', RefMandant);
  {$ENDIF}

  ComboBox.Items.BeginUpdate;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    query.SQL.Add ('select REF,NAME,BESCHREIBUNG from V_DEPOT');

    wherestr := '';

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_MAND='+IntToStr (RefMandant);
    end;

    if (Length (wherestr) > 0) Then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by NAME');

    try
      query.Open;

      while not (query.Eof) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadSpedProdukt
//* Author       : Stefan Graf
//* Datum        : 31.12.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadSpedProdukt (ComboBox : TCustomComboBox; const RefSped : Integer; var DefaultIndex : Integer) : Integer;
var
  res,
  idx      : Integer;
  query    : TADOQuery;
begin
  res := 0;

  DefaultIndex := -1;

  {$IFDEF TRACE}
    FunctionStart('LoadSpedProdukt');
    TraceParameter ('ComboBox', ComboBox.Name);
    TraceParameter ('RefSped ', RefSped);
  {$ENDIF}

  ComboBox.Items.BeginUpdate;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    query.SQL.Add ('select REF,NAME,DESCRIPTION,SPED_DEFAULT from V_SPED_PRODUKTE where REF_SPED=:ref_sped order by REIHENFOLGE');
    query.Parameters [0].Value := RefSped;

    try
      query.Open;

      while not (query.Eof) do begin
        idx := ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        if (query.Fields [3].AsString = '1') then
          DefaultIndex := idx;

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadSpedDepotCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle Depots für in SpedKennung die Combobox.
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadSpedDepotCombobox (ComboBox : TCustomComboBox; const RefMandant : Integer; const SpedKennung : String) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  wherestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadSpedDepotCombobox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('RefMandant ', RefMandant);
    TraceParameter ('SpedKennung', SpedKennung);
  {$ENDIF}

  ComboBox.Items.BeginUpdate;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    query.SQL.Add ('select REF,NAME,BESCHREIBUNG from V_DEPOT');

    wherestr := '';

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_MAND='+IntToStr (RefMandant);
    end;

    if (Length (SpedKennung) > 0) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'SPED_KENNUNG='+#39+SpedKennung+#39;
    end;

    if (Length (wherestr) > 0) Then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by NAME');

    try
      query.Open;

      while not (query.Eof) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadOptionLagerCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle sichtbaren Lager mit der Option <Option> in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBopPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadOptionLagerCombobox (ComboBox : TCustomComboBox; const Option : Integer; const Location : String; const RefMandant : Integer; const RefLager : Integer) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  wherestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadOptionLagerCombobox');
    TraceParameter ('ComboBox  ', ComboBox.Name);
    TraceParameter ('Option    ', Option);
    TraceParameter ('Location  ', Location);
    TraceParameter ('RefMandant', RefMandant);
    TraceParameter ('RefLager  ', RefLager);
  {$ENDIF}

  ComboBox.Items.BeginUpdate;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    query.SQL.Add ('select REF,LOCATION,NAME,BESCHREIBUNG from V_PCD_LAGER');

    wherestr := '';

    if (Option <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := 'nvl (substr (OPTIONS,'+IntToStr (Option)+',1), ''0'')=''1''';
    end;

    if (RefLager <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF='+IntToStr (RefLager);
    end;

    if (Length (Location) > 0) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_LOCATION=(select REF FROM V_PCD_LOCATION where NAME='+#39+Location+#39+')';
    end;

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF in (select REF_LAGER from V_MANDANT_REL_LAGER where REF_MAND='+IntToStr (RefMandant)+')';
    end;

    if (Length (wherestr) > 0) Then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by REIHEN_FOLGE nulls last,lower (NAME)');

    try
      query.Open;

      while not (query.Eof) do begin
        if (Length (Location) > 0) then
          ComboBox.Items.AddObject (query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger))
        else ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadLagerCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle sichtbaren Lager in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBopPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadLagerCombobox (ComboBox : TCustomComboBox; const RefLocation : Integer; const RefMandant : Integer = -1; const RefLager : Integer = -1) : Integer;
var
  res,
  ref      : Integer;
  query    : TADOQuery;
  wherestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadLagerCombobox');
    TraceParameter ('ComboBox    ', ComboBox.Name);
    TraceParameter ('RefLocation ', RefLocation);
    TraceParameter ('RefMandant  ', RefMandant);
    TraceParameter ('RefLager    ', RefLager);
  {$ENDIF}

  ref := GetComboBoxRef (ComboBox);

  ComboBox.Items.BeginUpdate;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    query.SQL.Add ('select REF,LOCATION,NAME,BESCHREIBUNG from V_PCD_LAGER');

    wherestr := '';

    if (RefLager <> -1) then
      wherestr := wherestr + 'REF='+IntToStr (RefLager);

    if (RefLocation <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_LOCATION='+IntToStr (RefLocation);
    end;

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF in (select REF_LAGER from V_MANDANT_REL_LAGER where REF_MAND='+IntToStr (RefMandant)+')';
    end;

    if (Length (wherestr) > 0) Then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by REIHEN_FOLGE nulls last,lower (NAME)');

    try
      query.Open;

      while not (query.Eof) do begin
        ComboBox.Items.AddObject (query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;

      if (ref = -1) then
        ComboBox.ItemIndex := -1
      else begin
        ComboBox.ItemIndex := FindComboboxRef (ComboBox, ref);
      end;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadLagerCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle sichtbaren Lager in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBopPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadLagerCombobox (ComboBox : TCustomComboBox; const Location : String; const RefMandant : Integer; const RefLager : Integer) : Integer;
var
  res,
  ref      : Integer;
  query    : TADOQuery;
  wherestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadLagerCombobox');
    TraceParameter ('ComboBox  ', ComboBox.Name);
    TraceParameter ('Location  ', Location);
    TraceParameter ('RefMandant', RefMandant);
    TraceParameter ('RefLager  ', RefLager);
  {$ENDIF}

  ref := GetComboBoxRef (ComboBox);

  ComboBox.Items.BeginUpdate;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    query.SQL.Add ('select REF,LOCATION,NAME,BESCHREIBUNG from V_PCD_LAGER');

    wherestr := '';

    if (RefLager <> -1) then
      wherestr := wherestr + 'REF='+IntToStr (RefLager);

    if (Length (Location) > 0) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_LOCATION=(select REF FROM V_PCD_LOCATION where NAME='+#39+Location+#39+')';
    end;

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF in (select REF_LAGER from V_MANDANT_REL_LAGER where REF_MAND='+IntToStr (RefMandant)+')';
    end;

    if (Length (wherestr) > 0) Then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by REIHEN_FOLGE nulls last,lower (NAME)');

    try
      query.Open;

      while not (query.Eof) do begin
        if (Length (Location) > 0) then
          ComboBox.Items.AddObject (query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger))
        else ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;

      if (ref = -1) then
        ComboBox.ItemIndex := -1
      else begin
        ComboBox.ItemIndex := FindComboboxRef (ComboBox, ref);
      end;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadLagerCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle sichtbaren Lager in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBopPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadManagedLagerCombobox (ComboBox : TCustomComboBox; const Location : String; const RefMandant : Integer; const RefLager : Integer) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  wherestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadManagedLagerCombobox');
    TraceParameter ('ComboBox  ', ComboBox.Name);
    TraceParameter ('Location  ', Location);
    TraceParameter ('RefMandant', RefMandant);
    TraceParameter ('RefLager  ', RefLager);
  {$ENDIF}

  ComboBox.Items.BeginUpdate;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    query.SQL.Add ('select REF,LOCATION,NAME,BESCHREIBUNG from V_PCD_LAGER');

    wherestr := 'MANAGED_BY is null';

    if (RefLager <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF='+IntToStr (RefLager);
    end;

    if (Length (Location) > 0) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_LOCATION=(select REF FROM V_PCD_LOCATION where NAME='+#39+Location+#39+')';
    end;

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) Then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF in (select REF_LAGER from V_MANDANT_REL_LAGER where REF_MAND='+IntToStr (RefMandant)+')';
    end;

    if (Length (wherestr) > 0) Then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by REIHEN_FOLGE nulls last,lower (NAME)');

    try
      query.Open;

      while not (query.Eof) do begin
        if (Length (Location) > 0) then
          ComboBox.Items.AddObject (query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger))
        else ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString+'|'+query.Fields [3].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadMandantCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle sichtbaren Mandant in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBoxPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadMandantCombobox (ComboBox : TCustomComboBox; const RefLocation : Integer; const RefMandant : Integer) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  wherestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadMandantCombobox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('RefLocation', RefLocation);
    TraceParameter ('RefMandant ', RefMandant);
  {$ENDIF}


  ComboBox.Items.BeginUpdate;;

  query := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    wherestr := '';

    query.SQL.Clear;
    query.SQL.Add ('select REF,NAME,BESCHREIBUNG,CONFIG_OPT,DEFAULT_LIEFER_DAUER from V_PCD_MANDANT');

    if (RefLocation <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF in (select REF_MAND from V_MANDANT_REL_LAGER where REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION='+IntToStr (RefLocation)+'))';
    end;

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF='+IntToStr (RefMandant);
    end;

    if (length (wherestr) > 0) then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by lower (NAME)');

    try
      query.Open;

      while not (query.Eof) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboboxMandantRef.Create (query.Fields [1].AsString, '', query.Fields [0].AsInteger, -1, query.Fields [4].AsInteger, query.Fields [3].AsString));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadTraderComboBox
//* Author       : Stefan Graf
//* Datum        : 08.04.2021
//******************************************************************************
//* Description  : Lade alle sichtbaren Trader des Mandanten in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBoxPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadTraderComboBox (ComboBox : TCustomComboBox; const RefLocation : Integer; const RefMandant : Integer) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  wherestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadTraderComboBox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('RefLocation', RefLocation);
    TraceParameter ('RefMandant ', RefMandant);
  {$ENDIF}


  ComboBox.Items.BeginUpdate;;

  query := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    wherestr := '';

    query.SQL.Clear;
    query.SQL.Add ('select REF,NAME,DESCRIPTION from V_TRADER');

    if (RefLocation <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_MAND in (select REF_MAND from V_MANDANT_REL_LAGER where REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION='+IntToStr (RefLocation)+'))';
    end;

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_MAND='+IntToStr (RefMandant);
    end;

    if (length (wherestr) > 0) then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by lower (NAME)');

    try
      query.Open;

      while not (query.Eof) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadTraderComboBox
//* Author       : Stefan Graf
//* Datum        : 08.04.2021
//******************************************************************************
//* Description  : Lade alle sichtbaren Trader des Mandanten in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBoxPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadTraderComboBox (ComboBox : TCustomComboBox; const RefLocation : Integer; const RefMandant, RefSubMand : Integer) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  wherestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadTraderComboBox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('RefLocation', RefLocation);
    TraceParameter ('RefMandant ', RefMandant);
  {$ENDIF}


  ComboBox.Items.BeginUpdate;;

  query := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    wherestr := '';

    query.SQL.Clear;
    query.SQL.Add ('select REF,NAME,DESCRIPTION from V_TRADER');

    if (RefLocation <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_MAND in (select REF_MAND from V_MANDANT_REL_LAGER where REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION='+IntToStr (RefLocation)+'))';
    end;

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_MAND='+IntToStr (RefMandant);
    end;

    if (RefSubMand > 0) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_SUB_MAND='+IntToStr (RefSubMand);
    end else begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_SUB_MAND is null';
    end;

    if (length (wherestr) > 0) then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by lower (NAME)');

    try
      query.Open;

      while not (query.Eof) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadSubMandantCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle sichtbaren SubMandant in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBoxPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadSubMandantCombobox (ComboBox : TCustomComboBox; const RefLocation : Integer; const RefMandant : Integer; const VerwendungsArt : String) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  cfgm,
  cfgmm,
  wherestr : String;
  ok       : Boolean;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadSubMandantCombobox');
    TraceParameter ('ComboBox      ', ComboBox.Name);
    TraceParameter ('RefLocation   ', RefLocation);
    TraceParameter ('RefMandant    ', RefMandant);
    TraceParameter ('VerwendungsArt', VerwendungsArt);
  {$ENDIF}


  ComboBox.Items.BeginUpdate;

  query := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    ClearComboBoxObjects (ComboBox);

    wherestr := '';

    //Normal sterbliche d�rfen gel�schte Untermandanten nicht sehen ;-)
    if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then
      wherestr := 'm.STATUS in (''ANG'', ''AKT'')';

    query.SQL.Clear;
    query.SQL.Add ('select m.REF,m.NAME,m.BESCHREIBUNG,m.CONFIG_OPT,m.DEFAULT_LIEFER_DAUER,m.STATUS,mm.CONFIG_OPT from V_MANDANT m left outer join V_MANDANT mm on (mm.REF=m.REF_MASTER_MAND)');

    if (RefLocation <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'm.REF in (select REF_MAND from V_MANDANT_REL_LOCATION where REF_LOCATION='+IntToStr (LVSDatenModul.AktLocationRef)+')';
    end;

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'm.REF_MASTER_MAND='+IntToStr (RefMandant);
    end else begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'm.REF_MASTER_MAND in (select REF from V_PCD_MANDANT where REF_MASTER_MAND is null)';

      if (RefLocation <> -1) then
        wherestr := wherestr + ' and m.REF_MASTER_MAND in (select REF_MAND from V_MANDANT_REL_LAGER where REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION='+IntToStr (RefLocation)+'))';
    end;

    if (length (wherestr) > 0) then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by lower (m.NAME)');


    try
      query.Open;

      while not (query.Eof) do begin
        ok := False;

        if ((query.Fields [5].AsString = 'AKT') or (query.Fields [5].AsString = 'ANG')) then begin
          if (Length (VerwendungsArt) = 0) then
            ok := true
          else begin
            cfgm  := query.Fields [3].AsString;
            cfgmm := query.Fields [6].AsString;

            if ((VerwendungsArt = 'BEST') and (copy (cfgm, cMandBestEnabled, 1) = '1')) then
              ok := true
            else if ((VerwendungsArt = 'BEST') and (GetOpt (cfgm, cMandBestEnabled) in [#0, ' ']) and (GetOpt (cfgmm, cMandBestEnabled) = '1')) then
              ok := true
            else if ((VerwendungsArt = 'AUF') and (copy (cfgm, cMandAufEnabled, 1 ) = '1')) then
              ok := true
            else if ((VerwendungsArt = 'AUF') and (GetOpt (cfgm, cMandAufEnabled) in [#0, ' ']) and (GetOpt (cfgmm, cMandAufEnabled) = '1')) then
              ok := true;
          end;
        end;

        if ok then
          ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboboxMandantRef.Create (query.Fields [1].AsString, '', query.Fields [0].AsInteger, -1, query.Fields [4].AsInteger, query.Fields [3].AsString));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;

    ComboBox.Items.EndUpdate;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadMandantSubMandantCombobox
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Lade alle sichtbaren Mandanten und deren SubMandant in die Combobox.
//*                Dabei wird Name und Bezeichung angegeben.
//*                Optimiert für die Anzeige mit der TComboBoxPro
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadMandantSubMandantCombobox (ComboBox : TCustomComboBox; const RefLocation : Integer = -1; const RefMandant : Integer = -1) : Integer;
var
  res      : Integer;
  query1   : TADOQuery;
  query2   : TADOQuery;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadMandantSubMandantCombobox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('RefLocation', RefLocation);
    TraceParameter ('RefMandant ', RefMandant);
  {$ENDIF}

  ClearComboBoxObjects (ComboBox);

  query1 := TADOQuery.Create (Nil);
  query2 := TADOQuery.Create (Nil);
  try
    query1.LockType := ltReadOnly;
    query1.Connection := LVSDatenModul.MainADOConnection;

    query2.LockType := ltReadOnly;
    query2.Connection := LVSDatenModul.MainADOConnection;

    query2.SQL.Add ('select REF,NAME,BESCHREIBUNG,CONFIG_OPT,DEFAULT_LIEFER_DAUER from V_MANDANT where STATUS in (''ANG'', ''AKT'') and REF_MASTER_MAND=:RefMand and REF in (select REF_MAND from V_MANDANT_REL_LOCATION where REF_LOCATION=:ref_loc) order by upper (NAME)');
    query2.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    query1.SQL.Clear;
    query1.SQL.Add ('select REF,NAME,BESCHREIBUNG,CONFIG_OPT,DEFAULT_LIEFER_DAUER from V_PCD_MANDANT order by lower (NAME)');

    try
      query1.Open;

      while not (query1.Eof) do begin
        ComboBox.Items.AddObject (query1.Fields [1].AsString+'|'+query1.Fields [2].AsString, TComboboxMandantRef.Create (query1.Fields [1].AsString, '', query1.Fields [0].AsInteger, -1, query1.Fields [4].AsInteger, query1.Fields [3].AsString));

        query2.Parameters.ParamByName('RefMand').Value := query1.Fields [0].AsInteger;

        try
          query2.Open;

          while not (query2.Eof) do begin
            ComboBox.Items.AddObject (query1.Fields [1].AsString+':'+query2.Fields [1].AsString+'|'+query2.Fields [2].AsString, TComboboxMandantRef.Create (query1.Fields [1].AsString, query2.Fields [1].AsString, query1.Fields [0].AsInteger, query2.Fields [0].AsInteger, query1.Fields [4].AsInteger, query1.Fields [3].AsString));

            query2.Next;
          end;

          query2.Close;
        except
          res := -9;
        end;

        query1.Next;
      end;

      query1.Close;
    except
      res := -9;
    end;
  finally
    query1.Free;
    query2.Free;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: GetMandantRef
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetMandantRef (ComboBox : TCustomComboBox; const Index : Integer; var RefMand, RefSubMand : Integer) : Integer;
var
  idx : Integer;
begin
  if (Index >= 0) then
    idx := Index
  else
    idx := ComboBox.ItemIndex;

  Result     := -1;

  RefMand    := -1;
  RefSubMand := -1;

  if (idx < 0) or (idx >= ComboBox.Items.Count) then
    Result := -1
  else if not Assigned (ComboBox.Items.Objects [idx]) then
    Result := -1
  else if (ComboBox.Items.Objects [idx] is TComboboxMandantRef) then begin
    RefMand := (ComboBox.Items.Objects [idx] as TComboboxMandantRef).Ref;
    RefSubMand := (ComboBox.Items.Objects [idx] as TComboboxMandantRef).RefSubMand;

    if ((ComboBox.Items.Objects [idx] as TComboboxMandantRef).RefSubMand > 0) then
      Result := (ComboBox.Items.Objects [idx] as TComboboxMandantRef).RefSubMand
    else
      Result := (ComboBox.Items.Objects [idx] as TComboboxMandantRef).Ref;
  end else if (ComboBox.Items.Objects [idx] is TComboboxRef) then begin
    RefMand := (ComboBox.Items.Objects [idx] as TComboboxRef).Ref;
    Result := (ComboBox.Items.Objects [idx] as TComboboxRef).Ref;
  end;
end;

//******************************************************************************
//* Function Name: FindMandantRef
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindMandantRef (ComboBox : TCustomComboBox; const RefMand : Integer) : Integer;
var
  idx : Integer;
begin
  idx := 0;

  while (idx < ComboBox.Items.Count) do begin
    if Assigned (ComboBox.Items.Objects [idx]) then begin
      if (ComboBox.Items.Objects [idx] is TComboboxMandantRef) then begin
        if ((ComboBox.Items.Objects [idx] as TComboboxMandantRef).RefSubMand = RefMand) or ((ComboBox.Items.Objects [idx] as TComboboxMandantRef).Ref = RefMand) then
          break;
      end else if (ComboBox.Items.Objects [idx] is TComboboxRef) then begin
        if ((ComboBox.Items.Objects [idx] as TComboboxRef).Ref = RefMand) then
          break;
      end;
    end;

    Inc (idx);
  end;

  if (idx < ComboBox.Items.Count) then
    Result := idx
  else
    Result := -1;
end;

//******************************************************************************
//* Function Name: LoadUserGruppe
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadUserGruppe (ComboBox : TCustomComboBox; const GruppenName : String; const RefLoc : Integer) : Integer;
var
  res,
  intwert : Integer;
  query   : TADOQuery;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadUserGruppe');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('GruppenName', GruppenName);
    TraceParameter ('RefLoc     ', RefLoc);
  {$ENDIF}

  if (UserReg.ReadRegValue ('SortUser', intwert) <> 0) then
    intwert := 0;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;

    if (intwert = 1) then
      query.SQL.Add ('select REF,USER_NAME,USER_ID from V_SYS_BEN where STATUS=''AKT''')
    else
      query.SQL.Add ('select REF,USER_ID,USER_NAME from V_SYS_BEN where STATUS=''AKT''');

    if (RefLoc = -1) then begin
      query.SQL.Add ('and REF in (select REF_BEN from V_SYS_REL_BEN_GRP where REF_GRP in (select REF from V_SYS_BEN_GRP where REF_LOCATION is null and upper (GROUPE_ID) like upper (:GruppenName)))');
      query.Parameters.ParamByName('GruppenName').Value := GruppenName;
    end else begin
      query.SQL.Add ('and REF in (select REF_BEN from V_SYS_REL_BEN_GRP where REF_GRP in (select REF from V_SYS_BEN_GRP where REF_LOCATION=:RefLoc and upper (GROUPE_ID) like upper (:GruppenName)))');
      query.Parameters.ParamByName('RefLoc').Value := RefLoc;
      query.Parameters.ParamByName('GruppenName').Value := GruppenName;
    end;

    if (intwert = 1) then
      query.SQL.Add ('order by USER_NAME, USER_ID')
    else
      query.SQL.Add ('order by USER_ID, USER_NAME');

    try
      query.Open;

      while not (query.Eof) do begin
        ComboBox.AddItem (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboboxSysBenRef.Create (query.Fields [0].AsInteger, 1, false));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadGruppe
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadGruppe (ComboBox : TCustomComboBox; const GruppenArt, GruppenName : String; const RefLoc : Integer) : Integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadGruppe');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('GruppenArt ', GruppenArt);
    TraceParameter ('GruppenName', GruppenArt);
    TraceParameter ('RefLoc     ', RefLoc);
  {$ENDIF}

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select REF,GROUPE_ID,GROUPE_NAME,DEFAULT_GROUPE from V_SYS_BEN_GRP where GROUPE_ART=:art');
    query.Parameters.ParamByName('art').Value := GruppenArt;

    if (Length (GruppenName) > 0) then begin
      if (Pos ('%', GruppenName) > 0) then
        query.SQL.Add ('and upper (GROUPE_ID) like upper (:id)')
      else
        query.SQL.Add ('and upper (GROUPE_ID)=:id');
      query.Parameters.ParamByName('id').Value := GruppenName;
    end;

    if (RefLoc = -1) then
      query.SQL.Add ('and REF_LOCATION is null')
    else begin
      query.SQL.Add ('and REF_LOCATION=:ref_loc');
      query.Parameters.ParamByName('ref_loc').Value := RefLoc;
    end;

    query.SQL.Add ('order by REIHENFOLGE nulls last, GROUPE_ID');

    try
      query.Open;

      while not (query.Eof) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboboxSysBenRef.Create (query.Fields [0].AsInteger, 0, (query.Fields [3].AsString = '1')));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: CollectArtikelInfos
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function CollectArtikelInfos (ds : TDataSet; ArtikelInfo : TArtikelInfo) : Integer;
var
  field : TField;
begin
  try
    if (ds.FieldByName ('REF').IsNull) then begin
      ArtikelInfo.Clear;

      Result := 2;
    end else begin
      Result := 0;

      ArtikelInfo.RefMand           := ds.FieldByName ('REF_MAND').AsInteger;
      ArtikelInfo.RefSubMand        := ds.FieldByName ('REF_SUB_MAND').AsInteger;
      ArtikelInfo.RefArtikel        := ds.FieldByName ('REF').AsInteger;
      ArtikelInfo.RefEinheit        := ds.FieldByName ('REF_EINHEIT').AsInteger;
      ArtikelInfo.RefArtikelEinheit := ds.FieldByName ('REF_AR_EINHEIT').AsInteger;

      if (ds.FieldByName ('REF_INHALT').IsNull) then
        ArtikelInfo.RefInhalt := -1
      else
        ArtikelInfo.RefInhalt := ds.FieldByName ('REF_INHALT').AsInteger;

      field := ds.FindField ('UNIT_EINHEIT');

      if not Assigned (field) then begin
        ArtikelInfo.UnitEinheit := -1;
        ArtikelInfo.UnitNorm    := -1;
        ArtikelInfo.UnitNetto   := -1;
      end else begin
        ArtikelInfo.UnitEinheit := DBGetIntegerNull (field);
        ArtikelInfo.UnitNorm    := DBGetIntegerNull (ds.FieldByName ('UNIT_EINHEIT_NORM'));
        ArtikelInfo.UnitNetto   := DBGetIntegerNull (ds.FieldByName ('UNIT_EINHEIT_NETTO'));
      end;

      field := ds.FindField ('UVP_ANZAHL');

      if Assigned (field) then
        ArtikelInfo.UVPAnzahl := DBGetIntegerNull (field)
      else
        ArtikelInfo.UVPAnzahl := -1;

      ArtikelInfo.ArtikelNr     := ds.FieldByName ('ARTIKEL_NR').AsString;

      if Assigned (ds.FindField ('ARTIKEL_NR_MANDANT')) then
        ArtikelInfo.MandArtikelNr := ds.FieldByName ('ARTIKEL_NR_MANDANT').AsString;

      if Assigned (ds.FindField ('REF_DEFAULT_LT')) then
        ArtikelInfo.RefLT := DBGetIntegerNull (ds.FieldByName ('REF_DEFAULT_LT'));

      ArtikelInfo.Einheit       := ds.FieldByName ('EINHEIT').AsString;
      ArtikelInfo.BasisEANCode  := ds.FieldByName ('EAN').AsString;
      ArtikelInfo.BasisBarcode  := ds.FieldByName ('BARCODE').AsString;
      ArtikelInfo.ArtikelText   := ds.FieldByName ('ARTIKEL_TEXT').AsString;
      ArtikelInfo.ColliName     := ds.FieldByName ('COLLI_NAME').AsString;
      ArtikelInfo.PicturePath   := ds.FieldByName ('PICTURE_PATH').AsString;

      if not (ds.FieldByName ('L').IsNull or ds.FieldByName ('B').IsNull or ds.FieldByName ('H').IsNull) then
        ArtikelInfo.Volumen := Int64 (ds.FieldByName ('L').AsInteger) * Int64 (ds.FieldByName ('B').AsInteger) * Int64 (ds.FieldByName ('H').AsInteger);

      if not (ds.FieldByName ('L').IsNull) and (ds.FieldByName ('L').AsInteger > ArtikelInfo.MaxSize) then
        ArtikelInfo.MaxSize := ds.FieldByName ('L').AsInteger;

      if not (ds.FieldByName ('B').IsNull) and (ds.FieldByName ('B').AsInteger > ArtikelInfo.MaxSize) then
        ArtikelInfo.MaxSize := ds.FieldByName ('B').AsInteger;

      if not (ds.FieldByName ('H').IsNull) and (ds.FieldByName ('H').AsInteger > ArtikelInfo.MaxSize) then
        ArtikelInfo.MaxSize := ds.FieldByName ('H').AsInteger;

      if Assigned (ds.FindField ('PAL_HEIGHT')) then begin
        if (ds.FieldByName ('PAL_HEIGHT').IsNull) then
          ArtikelInfo.PalHeight := -1
        else ArtikelInfo.PalHeight := ds.FieldByName ('PAL_HEIGHT').AsInteger;
      end;

      if (ds.FieldByName ('PAL_FAKTOR').IsNull) then
        ArtikelInfo.PALFaktor := -1
      else ArtikelInfo.PALFaktor := ds.FieldByName ('PAL_FAKTOR').AsInteger;

      if (ds.FieldByName ('INHALT_ANZAHL').IsNull) then
        ArtikelInfo.InhaltAnzahl := -1
      else ArtikelInfo.InhaltAnzahl := ds.FieldByName ('INHALT_ANZAHL').AsInteger;

      if (ds.FieldByName ('NETTO_GEWICHT').IsNull) then
        ArtikelInfo.NettoGewicht := -1
      else if ArtikelGewichtIsFloat then
        ArtikelInfo.NettoGewicht := ds.FieldByName ('NETTO_GEWICHT').AsFloat
      else
        ArtikelInfo.NettoGewicht := ds.FieldByName ('NETTO_GEWICHT').AsFloat;

      if (ds.FieldByName ('OPT_GEWICHT').IsNull) then
        ArtikelInfo.GewichtFlag := False
      else ArtikelInfo.GewichtFlag := (ds.FieldByName ('OPT_GEWICHT').AsString = '1');

      if (ds.FieldByName ('OPT_MEHR_MENGE').IsNull) then
        ArtikelInfo.MehrMengeFlag := False
      else ArtikelInfo.MehrMengeFlag := (ds.FieldByName ('OPT_MEHR_MENGE').AsString = '1');

      if (ds.FieldByName ('OPT_MHD_PFLICHT').IsNull) then
        ArtikelInfo.MHDArt := #0
      else ArtikelInfo.MHDArt := ds.FieldByName ('OPT_MHD_PFLICHT').AsString [1];

      if (ds.FieldByName ('RLZ_WE').IsNull) then
        ArtikelInfo.RestlaufzeitWE := -1
      else ArtikelInfo.RestlaufzeitWE := ds.FieldByName ('RLZ_WE').AsInteger;

      if (ds.FieldByName ('RLZ_WE_MAX').IsNull) then
        ArtikelInfo.MaxRestlaufzeitWE := -1
      else ArtikelInfo.MaxRestlaufzeitWE := ds.FieldByName ('RLZ_WE_MAX').AsInteger;

      if (ds.FieldByName ('RLZ_WA').IsNull) then
        ArtikelInfo.RestlaufzeitWA := -1
      else ArtikelInfo.RestlaufzeitWA := ds.FieldByName ('RLZ_WA').AsInteger;

      field := ds.FindField ('RLZ_PROD');
      if not Assigned (field) then
        ArtikelInfo.RestlaufzeitPROD := -1
      else ArtikelInfo.RestlaufzeitPROD := field.AsInteger;

      field := ds.FindField ('RLZ_KOMM');
      if not Assigned (field) then
        ArtikelInfo.RestlaufzeitKomm := -1
      else ArtikelInfo.RestlaufzeitKomm := field.AsInteger;

      if (ds.FieldByName ('RLZ_WA_MAX').IsNull) then
        ArtikelInfo.MaxRestlaufzeitWA := -1
      else ArtikelInfo.MaxRestlaufzeitWA := ds.FieldByName ('RLZ_WA_MAX').AsInteger;

      if (ds.FieldByName ('OPT_ORGA_INVENT').IsNull) then
        ArtikelInfo.OrgaInventFlag := False
      else ArtikelInfo.OrgaInventFlag := (ds.FieldByName ('OPT_ORGA_INVENT').AsString = '1');

      if (ds.FieldByName ('OPT_CHARGE_PFLICHT').IsNull) then begin
        ArtikelInfo.ChargeArt    := 'O';
        ArtikelInfo.UniqueCharge := False;
        ArtikelInfo.AutoCharge   := False;
      end else begin
        ArtikelInfo.ChargeArt    := ds.FieldByName ('OPT_CHARGE_PFLICHT').AsString [1];

        field := ds.FindField ('OPT_AUTO_CHARGE');

        if Assigned (field) then
          ArtikelInfo.AutoCharge := (field.AsString > '0')
        else ArtikelInfo.AutoCharge := False;

        if (ArtikelInfo.ChargeArt <> 'P') then
          ArtikelInfo.UniqueCharge := false
        else begin
          field := ds.FindField ('OPT_UNIQUE_SKU_CHANGE');

          if Assigned (field) then
            ArtikelInfo.UniqueCharge := (field.AsString = '1')
          else ArtikelInfo.UniqueCharge := False;
        end;
      end;

      field := ds.FindField ('OPT_BESTAND_ID');
      if Assigned (field) then begin
        ArtikelInfo.BestandIDArt := field.AsString[1];

        field := ds.FindField ('OPT_UNIQUE_BESTAND_ID');
        if Assigned (field) then
          ArtikelInfo.UniqueBestandID := (field.AsString = '1')
      end;

      if (ds.FieldByName ('OPT_SERIAL').IsNull) then begin
        ArtikelInfo.SerialFlag := False;
        ArtikelInfo.BesSerialFlag := False;
      end else begin
        ArtikelInfo.SerialFlag := (ds.FieldByName ('OPT_SERIAL').AsString > '0');
        ArtikelInfo.BesSerialFlag := (ds.FieldByName ('OPT_SERIAL').AsString = '2');
      end;

      if not Assigned (ds.FindField ('OPT_MULTI_COLLI')) then
        ArtikelInfo.MultiColliFlag := False
      else if (ds.FieldByName ('OPT_MULTI_COLLI').IsNull) then
        ArtikelInfo.MultiColliFlag := False
      else ArtikelInfo.MultiColliFlag := (ds.FieldByName ('OPT_MULTI_COLLI').AsString = '1');

      if not Assigned (ds.FindField ('OPT_IS_COLLI')) then
        ArtikelInfo.IsColliFlag := False
      else if (ds.FieldByName ('OPT_IS_COLLI').IsNull) then
        ArtikelInfo.IsColliFlag := False
      else ArtikelInfo.IsColliFlag := (ds.FieldByName ('OPT_IS_COLLI').AsString = '1');

      ArtikelInfo.OptSperrgut     := (ds.FieldByName ('OPT_SPERRGUT').AsString = '1');
      ArtikelInfo.OptBigItem      := (ds.FieldByName ('OPT_BIG_ITEM').AsString = '1');
      ArtikelInfo.OptReadyShip    := (ds.FieldByName ('OPT_READY_FOR_SHIP').AsString = '1');
      ArtikelInfo.OptBeschaffung  := (ds.FieldByName ('OPT_BESCHAFFUNG').AsString = '1');
    end;
  except
    Result := -9;
  end;
end;

//******************************************************************************
//* Function Name: GetArtikelInfos
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetArtikelEANInfos (const Mandant, EAN : String; ArtikelInfo : TArtikelInfo) : Integer;
var
  res,
  vperef : Integer;
  query  : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;

    if (Copy (EAN, 1, 2) = '28') and (Length (EAN) = 13) then begin
      //Bei Gewichts und Preis EANs nur die ersten 7 Ziffern vergleichen
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where ((EAN_SEARCH=:ean) or (substr (EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (EAN_SEARCH, 8, 5)=''00000''))');
    end else if (Copy (EAN, 1, 2) = '29') and (Length (EAN) = 13) then begin
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where ((EAN_SEARCH=:ean) or (substr (EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (EAN_SEARCH, 8, 5)=''00000''))');
    end else begin
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where STATUS=''AKT'' and EAN_SEARCH=:ean');
    end;
    query.Parameters.ParamByName('ean').Value := EAN;

    if (Length (Mandant) > 0) Then begin
      query.SQL.Add ('and MANDANT=:mand');
      query.Parameters.ParamByName('mand').Value := Mandant;
    end;

    if LVSConfigModul.UseLocationListing then begin
      query.SQL.Add ('and REF in (select REF_AR from V_ARTIKEL_REL_LOCATION where STATUS=''AKT'' and REF_LOCATION=:ref_loc)');
      query.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
    end;

    query.SQL.Add('order by case when OPT_MASTER=''1'' then 0 else 1 end asc');

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Parameters.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetArtikelInfos
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetBatchArtikelEANInfos (const RefBatch : Integer; const EAN : String; ArtikelInfo : TArtikelInfo) : Integer;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    if (Copy (EAN, 1, 2) = '28') and (Length (EAN) = 13) then begin
      //Bei Gewichts und Preis EANs nur die ersten 7 Ziffern vergleichen
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where ((EAN_SEARCH=:ean) or (substr (EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (EAN_SEARCH, 8, 5)=''00000''))');
    end else if (Copy (EAN, 1, 2) = '29') and (Length (EAN) = 13) then begin
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where ((EAN_SEARCH=:ean) or (substr (EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (EAN_SEARCH, 8, 5)=''00000''))');
    end else begin
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where EAN_SEARCH=:ean');
    end;
    query.Params.ParamByName('ean').Value := EAN;

    if LVSConfigModul.UseLocationListing then begin
      query.SQL.Add ('and REF in (select REF_AR from V_ARTIKEL_REL_LOCATION where STATUS=''AKT'' and REF_LOCATION=:ref_loc)');
      query.Params.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
    end;

    //Alle Artikel und Inhalte von Artikelsets ber�cksichtigen, die im Batch vorkommen
    query.SQL.Add('and (REF in (select REF_AR from V_AUFTRAG_BATCHLAUF_POS where REF_BATCHLAUF=:ref_batch_1)');
    query.SQL.Add('or (REF in (select ae.REF_AR from V_ARTIKEL_SET s, V_ARTIKEL_SET_POS sp, VQ_ARTIKEL_EINHEIT ae');
    query.SQL.Add('where ae.REF=sp.REF_AR_EINHEIT and s.REF=sp.REF_SET and s.REF_AR in (select REF_AR from V_AUFTRAG_BATCHLAUF_POS where REF_BATCHLAUF=:ref_batch_2))))');
    query.Params.ParamByName('ref_batch_1').Value := RefBatch;
    query.Params.ParamByName('ref_batch_2').Value := RefBatch;

    query.SQL.Add('order by case when OPT_MASTER=''1'' then 0 else 1 end asc, case when OPT_BESCHAFFUNG=''1'' then 0 else 1 end desc');

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetArtikelInfos
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetArtikelEANInfos (const RefMand : Integer; const EAN : String; ArtikelInfo : TArtikelInfo) : Integer;
var
  recanz : Integer;
begin
  Result := GetArtikelEANInfos (RefMand, -1, '', EAN, ArtikelInfo, recanz);
end;

function  GetArtikelEANInfos (const RefMand, RefSubMand : Integer; const Process, EAN : String; ArtikelInfo : TArtikelInfo; var RecordCount : Integer) : Integer;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    if (Copy (EAN, 1, 2) = '28') and (Length (EAN) = 13) then begin
      //Bei Gewichts und Preis EANs nur die ersten 7 Ziffern vergleichen
      query.SQL.Add('select ar.* from V_ARTIKEL_SUCHE ar inner join VQ_ARTIKEL qar on (qar.REF=ar.REF) where ((ar.EAN_SEARCH=:ean) or (substr (ar.EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (ar.EAN_SEARCH, 8, 5)=''00000''))');
    end else if (Copy (EAN, 1, 2) = '29') and (Length (EAN) = 13) then begin
      query.SQL.Add('select ar.* from V_ARTIKEL_SUCHE ar inner join VQ_ARTIKEL qar on (qar.REF=ar.REF) where ((ar.EAN_SEARCH=:ean) or (substr (ar.EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (ar.EAN_SEARCH, 8, 5)=''00000''))');
    end else begin
      query.SQL.Add('select ar.* from V_ARTIKEL_SUCHE ar inner join VQ_ARTIKEL qar on (qar.REF=ar.REF) where ar.STATUS=''AKT'' and ar.EAN_SEARCH=:ean');
    end;
    query.Params.ParamByName('ean').Value := EAN;

    if (RefSubMand <> -1) Then begin
      query.SQL.Add ('and ar.REF_SUB_MAND=:ref_sub_mand');
      query.Params.ParamByName('ref_sub_mand').Value := RefSubMand;
    end else if (RefMand <> -1) Then begin
      query.SQL.Add ('and ar.REF_MAND=:ref_mand');
      query.Params.ParamByName('ref_mand').Value := RefMand;
    end;

    if LVSConfigModul.UseLocationListing then begin
      query.SQL.Add ('and ar.REF in (select REF_AR from V_ARTIKEL_REL_LOCATION where STATUS=''AKT'' and REF_LOCATION=:ref_loc)');
      query.Params.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
    end;

    //Setartikel machen hier keinen Sinn
    if (Process = 'WE') then
      query.SQL.Add ('and qar.REF_ARTIKEL_SET is null');

    query.SQL.Add('order by case when ar.OPT_MASTER=''1'' then 0 else 1 end asc, case when ar.OPT_BESCHAFFUNG=''1'' then 0 else 1 end desc, case when qar.REF_ARTIKEL_SET is null then 0 else 1 end desc');

    try
      query.Open;

      RecordCount := query.RecordCount;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetAuftragArtikelEANInfos
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Sucht den Artikel mit EAN, der zu dem Auftrag gepasst
//*                Damit umgeht man das Problem mit doppelten EANs
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetAuftragArtikelEANInfos (const RefAuf : Integer; const EAN : String; ArtikelInfo : TArtikelInfo) : Integer; overload;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Clear;

    if (Copy (EAN, 1, 2) = '28') and (Length (EAN) = 13) then begin
      //Bei Gewichts und Preis EANs nur die ersten 7 Ziffern vergleichen
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where ((EAN_SEARCH=:ean) or (substr (EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (EAN_SEARCH, 8, 5)=''00000''))');
    end else if (Copy (EAN, 1, 2) = '29') and (Length (EAN) = 13) then begin
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where ((EAN_SEARCH=:ean) or (substr (EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (EAN_SEARCH, 8, 5)=''00000''))');
    end else begin
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where (EAN_SEARCH=:ean or BARCODE=:ean)');
    end;
    query.Params.ParamByName('ean').Value := EAN;

    if LVSConfigModul.UseLocationListing then begin
      query.SQL.Add ('and REF in (select REF_AR from V_ARTIKEL_REL_LOCATION where STATUS=''AKT'' and REF_LOCATION=:ref_loc)');
      query.Params.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
    end;

    if (RefAuf <> -1) Then begin
      query.SQL.Add ('and REF in (select REF_AR from VQ_AUFTRAG_KOMM_POS where REF_AUF_KOPF=:ref_auf)');
      query.Params.ParamByName('ref_auf').Value := RefAuf;
    end;

    query.SQL.Add('order by case when OPT_MASTER=''1'' then 0 else 1 end asc, case when OPT_BESCHAFFUNG=''1'' then 0 else 1 end desc');

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetRetAvisArtikelEANInfos
//* Author       : Stefan Graf
//* Datum        : 02.08.2022
//******************************************************************************
//* Description  : Sucht den Artikel mit EAN, der zu dem Retouren-Avis gepasst
//*                Damit umgeht man das Problem mit doppelten EANs
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetRetAvisArtikelEANInfos  (const RefRetAvis : Integer; const EAN : String; ArtikelInfo : TArtikelInfo) : Integer; overload;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Clear;

    if (Copy (EAN, 1, 2) = '28') and (Length (EAN) = 13) then begin
      //Bei Gewichts und Preis EANs nur die ersten 7 Ziffern vergleichen
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where ((EAN_SEARCH=:ean) or (substr (EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (EAN_SEARCH, 8, 5)=''00000''))');
    end else if (Copy (EAN, 1, 2) = '29') and (Length (EAN) = 13) then begin
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where ((EAN_SEARCH=:ean) or (substr (EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (EAN_SEARCH, 8, 5)=''00000''))');
    end else begin
      query.SQL.Add('select * from V_ARTIKEL_SUCHE where EAN_SEARCH=:ean');
    end;
    query.Params.ParamByName('ean').Value := EAN;

    if LVSConfigModul.UseLocationListing then begin
      query.SQL.Add ('and REF in (select REF_AR from V_ARTIKEL_REL_LOCATION where STATUS=''AKT'' and REF_LOCATION=:ref_loc)');
      query.Params.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
    end;

    if (RefRetAvis <> -1) Then begin
      query.SQL.Add ('and REF in (select REF_AR from V_RETOUREN_AVIS_POS where REF_RETOUREN_AVIS=:ref_ret_avis)');
      query.Params.ParamByName('ref_ret_avis').Value := RefRetAvis;
    end;

    query.SQL.Add('order by case when OPT_MASTER=''1'' then 0 else 1 end asc, case when OPT_BESCHAFFUNG=''1'' then 0 else 1 end desc');

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetArtikelInfos
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetWEArtikelEANInfos (const RefMand, RefSubMand, RefBestellung : Integer; const EAN : String; ArtikelInfo : TArtikelInfo; MasterArtikelInfo : TArtikelInfo; var RecordCount : Integer) : Integer;
var
  res,
  vperef   : Integer;
  query    : TSmartQuery;
  subquery : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    if (Copy (EAN, 1, 2) = '28') and (Length (EAN) = 13) then begin
      //Bei Gewichts und Preis EANs nur die ersten 7 Ziffern vergleichen
      query.SQL.Add('select ar.* from V_ARTIKEL_SUCHE ar inner join VQ_ARTIKEL qar on (qar.REF=ar.REF) where ((EAN_SEARCH=:ean) or (substr (EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (EAN_SEARCH, 8, 5)=''00000''))');
    end else if (Copy (EAN, 1, 2) = '29') and (Length (EAN) = 13) then begin
      query.SQL.Add('select ar.* from V_ARTIKEL_SUCHE ar inner join VQ_ARTIKEL qar on (qar.REF=ar.REF) where ((EAN_SEARCH=:ean) or (substr (EAN_SEARCH, 1, 7)=substr (:ean, 1, 7) and substr (EAN_SEARCH, 8, 5)=''00000''))');
    end else begin
      query.SQL.Add('select ar.* from V_ARTIKEL_SUCHE ar inner join VQ_ARTIKEL qar on (qar.REF=ar.REF) where EAN_SEARCH=:ean');
    end;
    query.Params.ParamByName('ean').Value := EAN;

    if (RefSubMand <> -1) Then begin
      query.SQL.Add ('and ar.REF_SUB_MAND=:ref_sub_mand');
      query.Params.ParamByName('ref_sub_mand').Value := RefSubMand;
    end else if (RefMand <> -1) Then begin
      query.SQL.Add ('and ar.REF_MAND=:ref_mand');
      query.Params.ParamByName('ref_mand').Value := RefMand;
    end;

    if (RefBestellung > 0) then begin
      query.SQL.Add ('and ar.REF_AR_EINHEIT in (select REF_AR_EINHEIT from VQ_BESTELL_POS where REF_BEST_KOPF=:ref_best)');
      query.Params.ParamByName('ref_best').Value := RefBestellung;
    end;

    if LVSConfigModul.UseLocationListing then begin
      query.SQL.Add ('and ar.REF in (select REF_AR from V_ARTIKEL_REL_LOCATION where STATUS=''AKT'' and REF_LOCATION=:ref_loc)');
      query.Params.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;
    end;

    query.SQL.Add ('and qar.REF_ARTIKEL_SET is null');

    query.SQL.Add('order by case when ar.OPT_BESCHAFFUNG=''1'' then 0 else 1 end asc, case when ar.OPT_MASTER=''1'' then 0 else 1 end asc');

    try
      query.Open;

      RecordCount := query.RecordCount;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          subquery := TSmartQuery.Create (Nil);

          try
            subquery.ReadOnly := True;
            subquery.Session := LVSDatenModul.OraMainSession;

            subquery.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
            subquery.Params.ParamByName('ref').Value := vperef;

            LVSDatenModul.TraceSql (subquery.SQL.Text);

            subquery.Open;

            if (subquery.Fields [1].IsNull) then
              ArtikelInfo.InhaltEANCode := ''
            else ArtikelInfo.InhaltEANCode := subquery.Fields [1].AsString;

            if (subquery.Fields [2].IsNull) then
              ArtikelInfo.InhaltBarcode := ''
            else ArtikelInfo.InhaltBarcode := subquery.Fields [2].AsString;

            if (subquery.Fields [3].IsNull) then
              ArtikelInfo.InhaltArtikelNr := ''
            else ArtikelInfo.InhaltArtikelNr := subquery.Fields [3].AsString;
          finally
            subquery.Free;
          end;
        end;
      end;

      if (query.RecordCount > 1) then begin
        query.Next;

        res := CollectArtikelInfos (query, MasterArtikelInfo);

        if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
            vperef := query.FieldByName ('REF_INHALT').AsInteger;

            subquery := TSmartQuery.Create (Nil);

            try
              subquery.ReadOnly := True;
              subquery.Session := LVSDatenModul.OraMainSession;

              subquery.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
              subquery.Params.ParamByName('ref').Value := vperef;

              LVSDatenModul.TraceSql (subquery.SQL.Text);

              subquery.Open;

              if (subquery.Fields [1].IsNull) then
                ArtikelInfo.InhaltEANCode := ''
              else ArtikelInfo.InhaltEANCode := subquery.Fields [1].AsString;

              if (subquery.Fields [2].IsNull) then
                ArtikelInfo.InhaltBarcode := ''
              else ArtikelInfo.InhaltBarcode := subquery.Fields [2].AsString;

              if (subquery.Fields [3].IsNull) then
                ArtikelInfo.InhaltArtikelNr := ''
              else ArtikelInfo.InhaltArtikelNr := subquery.Fields [3].AsString;
            finally
              subquery.Free;
            end;
          end;
        end;
      end else begin
        MasterArtikelInfo.Clear;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetArtikelBarcodeInfos
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetArtikelBarcodeInfos (const RefMand : Integer; const BarStr : String; ArtikelInfo : TArtikelInfo) : Integer;
begin
  Result := GetArtikelBarcodeInfos (RefMand, -1, BarStr, ArtikelInfo);
end;

function  GetArtikelBarcodeInfos (const RefMand, RefSubMand : Integer; const BarStr : String; ArtikelInfo : TArtikelInfo) : Integer;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Clear;
    query.SQL.Add('select * from V_ARTIKEL_SUCHE where BARCODE=:barcode');
    query.Params.ParamByName('barcode').Value := BarStr;

    if (RefSubMand > 0) Then begin
      query.SQL.Add ('and REF_SUB_MAND=:ref_sub_mand');
      query.Params.ParamByName('ref_sub_mand').Value := RefSubMand;
    end else if (RefMand > 0) Then begin
      query.SQL.Add ('and REF_MAND=:ref_mand');
      query.Params.ParamByName('ref_mand').Value := RefMand;
    end;

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetBatchArtikelBarcodeInfos
//* Author       : Stefan Graf
//* Datum        : 01.04.2016
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetBatchArtikelBarcodeInfos (const RefBatch : Integer; const BarStr : String; ArtikelInfo : TArtikelInfo) : Integer; overload;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Clear;
    query.SQL.Add('select * from V_ARTIKEL_SUCHE where BARCODE=:barcode');
    query.Params.ParamByName('barcode').Value := BarStr;

    //Alle Artikel und Inhalte von Artikelsets ber�cksichtigen, die im Batch vorkommen
    query.SQL.Add('and (REF in (select REF_AR from V_AUFTRAG_BATCHLAUF_POS where REF_BATCHLAUF=:ref_batch_1)');
    query.SQL.Add('or (REF in (select ae.REF_AR from V_ARTIKEL_SET s, V_ARTIKEL_SET_POS sp, VQ_ARTIKEL_EINHEIT ae');
    query.SQL.Add('where ae.REF=sp.REF_AR_EINHEIT and s.REF=sp.REF_SET and s.REF_AR in (select REF_AR from V_AUFTRAG_BATCHLAUF_POS where REF_BATCHLAUF=:ref_batch_2))))');
    query.Params.ParamByName('ref_batch_1').Value := RefBatch;
    query.Params.ParamByName('ref_batch_2').Value := RefBatch;

    query.SQL.Add('order by case when OPT_MASTER=''1'' then 0 else 1 end asc, case when OPT_BESCHAFFUNG=''1'' then 0 else 1 end desc');

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetArtikelInfos
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetAuftragArtikelBarcodeInfos (const RefAuf : Integer; const BarStr : String; ArtikelInfo : TArtikelInfo) : Integer; overload;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Clear;
    query.SQL.Add('select * from V_ARTIKEL_SUCHE where BARCODE=:barcode');
    query.Params.ParamByName('barcode').Value := BarStr;

    if (RefAuf <> -1) Then begin
      query.SQL.Add ('and REF in (select REF_AR from VQ_AUFTRAG_KOMM_POS where REF_AUF_KOPF=:ref_auf)');
      query.Params.ParamByName('ref_auf').Value := RefAuf;
    end;

    query.SQL.Add('order by case when OPT_MASTER=''1'' then 0 else 1 end asc');

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetArtikelNummerInfos
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetArtikelNummerInfos (const Mandant, ArtikelNr : String; ArtikelInfo : TArtikelInfo) : Integer;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Add ('select * from V_ARTIKEL_SUCHE where OPT_MASTER=''1'' and upper (ARTIKEL_NR)=upper (:arnr)');
    query.Params.ParamByName('arnr').Value := ArtikelNr;

    if (Length (Mandant) > 0) Then begin
      query.SQL.Add('and MANDANT=:mand');
      query.Params.ParamByName('mand').Value := Mandant;
    end;

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetMandantArtikelNummerInfos
//* Author       : Stefan Graf
//* Datum        : 21.04.2017
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetMandantArtikelNummerInfos (const RefMand, RefSubMand : Integer; const MandArtikelNr : String; ArtikelInfo : TArtikelInfo) : Integer;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Add ('select * from V_ARTIKEL_SUCHE where OPT_MASTER=''1'' and upper (ARTIKEL_NR_MANDANT)=upper (:arnr)');
    query.Params.ParamByName('arnr').Value := MandArtikelNr;

    if (RefSubMand > 0) Then begin
      query.SQL.Add('and REF_SUB_MAND=:ref_sub_mand');
      query.Params.ParamByName('ref_sub_mand').Value := RefSubMand;
    end else if (RefMand > 0) Then begin
      query.SQL.Add('and REF_MAND=:ref_mand');
      query.Params.ParamByName('ref_mand').Value := RefMand;
    end;

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetArtikelInfos
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetArtikelNummerInfos (const RefMand, RefSubMand : Integer; const ArtikelNr : String; ArtikelInfo : TArtikelInfo) : Integer;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Add ('select * from V_ARTIKEL_SUCHE where OPT_MASTER=''1'' and (upper (ARTIKEL_NR)=upper (:arnr) or upper (ARTIKEL_NR_HERSTELLER)=upper (:manfarnr))');
    query.Params.ParamByName('arnr').Value := ArtikelNr;
    query.Params.ParamByName('manfarnr').Value := ArtikelNr;

    if (RefSubMand > 0) Then begin
      query.SQL.Add('and REF_SUB_MAND=:ref_sub_mand');
      query.Params.ParamByName('ref_sub_mand').Value := RefSubMand;
    end else if (RefMand > 0) Then begin
      query.SQL.Add('and REF_MAND=:ref_mand');
      query.Params.ParamByName('ref_mand').Value := RefMand;
    end;

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetArtikelNummerInfos
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetArtikelNummerInfos (const RefMand : Integer; const ArtikelNr : String; ArtikelInfo : TArtikelInfo) : Integer;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Clear;
    query.SQL.Add('select * from V_ARTIKEL_SUCHE where OPT_MASTER=''1'' and upper (ARTIKEL_NR)=upper (:arnr)');
    query.Params.ParamByName('arnr').Value := ArtikelNr;

    if (RefMand <> -1) Then begin
      query.SQL.Add ('and REF_MAND=:ref_mand');
      query.Params.ParamByName('ref_mand').Value := RefMand;
    end;

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: GetArtikelColliInfos
//* Author       : Stefan Graf
//* Datum        : 12.09.2017
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetArtikelColliInfos (const RefMand : Integer; const ArtikelNr, ColliNr : String; ArtikelInfo : TArtikelInfo) : Integer;overload;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;


    query.SQL.Clear;
    query.SQL.Add('select s.* from V_ARTIKEL_SUCHE s inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=s.REF_AR_EINHEIT) where s.OPT_IS_COLLI=''1'' and upper (s.ARTIKEL_NR)=upper (:arnr) and ae.COLLI_NR=:colli_nr');
    query.Params.ParamByName('arnr').Value := ArtikelNr;
    query.Params.ParamByName('colli_nr').Value := ColliNr;

    if (RefMand <> -1) Then begin
      query.SQL.Add ('and s.REF_MAND=:ref_mand');
      query.Params.ParamByName('ref_mand').Value := RefMand;
    end;

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetArtikelInfos (const Ref : Integer; ArtikelInfo : TArtikelInfo) : Integer;
begin
  Result := GetArtikelInfos (Ref, -1, ArtikelInfo);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetArtikelEinheitInfos (const RefAR, RefEinheit : Integer; ArtikelInfo : TArtikelInfo) : Integer; overload;
begin
  Result := GetArtikelEinheitInfos (RefAR, RefEinheit, -1, ArtikelInfo);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Dateum       : 09.04.2020
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetArtikelEinheitInfos (const RefAR, RefEinheit, RefBarcode : Integer; ArtikelInfo : TArtikelInfo) : Integer; overload;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Clear;

    query.SQL.Add('select ar.*,arl.OPT_SPERRGUT as LOC_OPT_SPERRGUT,arl.OPT_BIG_ITEM as LOC_OPT_BIG_ITEM,arl.OPT_READY_FOR_SHIP as LOC_OPT_READY_FOR_SHIP from V_ARTIKEL_SUCHE ar');
    query.SQL.Add('left outer join V_ARTIKEL_EINHEIT_REL_LAGER arl on (arl.REF_AR_EINHEIT=ar.REF_AR_EINHEIT and arl.REF_LOCATION=:ref_loc)');
    query.Params.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    if (RefBarcode > 0) then begin
      query.SQL.Add('where ar.REF=:refar and ar.REF_BARCODE=:refbar');
      query.Params.ParamByName('refar').Value := RefAR;
      query.Params.ParamByName('refbar').Value := RefBarcode;
    end else if (RefEinheit = -1) then begin
      query.SQL.Add('where ar.REF=:refar order by case when ar.OPT_MASTER=''1'' then 0 else 9 end asc');
      query.Params.ParamByName('refar').Value := RefAR;
    end else begin
      query.SQL.Add('where ar.REF=:refar and ar.REF_EINHEIT=:refvpe order by case when ar.OPT_MASTER=''1'' then 0 else 9 end asc');
      query.Params.ParamByName('refar').Value := RefAR;
      query.Params.ParamByName('refvpe').Value := RefEinheit;
    end;

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if not (query.FieldByName ('LOC_OPT_SPERRGUT').IsNull) then
          ArtikelInfo.OptSperrgut := (query.FieldByName ('LOC_OPT_SPERRGUT').AsString = '1');
        if not (query.FieldByName ('LOC_OPT_BIG_ITEM').IsNull) then
          ArtikelInfo.OptBigItem := (query.FieldByName ('LOC_OPT_BIG_ITEM').AsString = '1');
        if not (query.FieldByName ('LOC_OPT_READY_FOR_SHIP').IsNull) then
          ArtikelInfo.OptReadyShip := (query.FieldByName ('LOC_OPT_READY_FOR_SHIP').AsString = '1');

        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetArtikelInfos (const RefAR, RefAREinheit : Integer; ArtikelInfo : TArtikelInfo) : Integer;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Clear;

    query.SQL.Add('select ar.*,arl.OPT_SPERRGUT as LOC_OPT_SPERRGUT,arl.OPT_BIG_ITEM as LOC_OPT_BIG_ITEM,arl.OPT_READY_FOR_SHIP as LOC_OPT_READY_FOR_SHIP, a.RLZ_KOMM from V_ARTIKEL_SUCHE ar');
    query.SQL.Add('left outer join V_ARTIKEL_EINHEIT_REL_LAGER arl on (arl.REF_AR_EINHEIT=ar.REF_AR_EINHEIT and arl.REF_LOCATION=:ref_loc)');
    query.SQL.Add('inner join V_ARTIKEL a ON (a.REF = ar.REF)');
    query.Params.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    if (RefAREinheit = -1) then begin
      query.SQL.Add('where ar.REF=:refar order by case when ar.OPT_MASTER=''1'' then 0 else 9 end asc');
      query.Params.ParamByName('refar').Value := RefAR;
    end else if (RefAR = -1) then begin
      query.SQL.Add('where ar.REF_AR_EINHEIT=:refae');
      query.Params.ParamByName('refae').Value := RefAREinheit;
    end else begin
      query.SQL.Add('where ar.REF=:refar and ar.REF_AR_EINHEIT=:refae');
      query.Params.ParamByName('refar').Value := RefAR;
      query.Params.ParamByName('refae').Value := RefAREinheit;
    end;

    try
      query.Open;

      res := CollectArtikelInfos (query, ArtikelInfo);

      if (res = 0) then begin
        if not (query.FieldByName ('LOC_OPT_SPERRGUT').IsNull) then
          ArtikelInfo.OptSperrgut := (query.FieldByName ('LOC_OPT_SPERRGUT').AsString = '1');
        if not (query.FieldByName ('LOC_OPT_BIG_ITEM').IsNull) then
          ArtikelInfo.OptBigItem := (query.FieldByName ('LOC_OPT_BIG_ITEM').AsString = '1');
        if not (query.FieldByName ('LOC_OPT_READY_FOR_SHIP').IsNull) then
          ArtikelInfo.OptReadyShip := (query.FieldByName ('LOC_OPT_READY_FOR_SHIP').AsString = '1');

        if (query.FieldByName ('REF_INHALT').IsNull) then begin
          ArtikelInfo.InhaltEANCode := '';
          ArtikelInfo.InhaltArtikelNr := '';
        end else begin
          vperef := query.FieldByName ('REF_INHALT').AsInteger;

          query.Close;

          query.SQL.Clear;
          query.SQL.Add('select ae.REF,ae.EAN,ae.BARCODE,ar.ARTIKEL_NR from VQ_ARTIKEL_EINHEIT ae inner join VQ_ARTIKEL ar on (ar.REF=ae.REF_AR) where ae.REF=:ref');
          query.Params.ParamByName('ref').Value := vperef;

          LVSDatenModul.TraceSql (query.SQL.Text);

          query.Open;
          if (query.Fields [1].IsNull) then
            ArtikelInfo.InhaltEANCode := ''
          else ArtikelInfo.InhaltEANCode := query.Fields [1].AsString;

          if (query.Fields [2].IsNull) then
            ArtikelInfo.InhaltBarcode := ''
          else ArtikelInfo.InhaltBarcode := query.Fields [2].AsString;

          if (query.Fields [3].IsNull) then
            ArtikelInfo.InhaltArtikelNr := ''
          else ArtikelInfo.InhaltArtikelNr := query.Fields [3].AsString;
        end;
      end;

      query.Close;
    except
      on E: Exception do begin
        res := -9;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckMHD (const DateStr : String; const MinRLZ : Integer; const FehlerMsg, DatumName : String; var DateTime : TDateTime; var ErrorText : String) : Boolean;
begin
  Result := CheckMHD (DateStr, [cmhdVergangenheit], MinRLZ, FehlerMsg, DatumName, DateTime, ErrorText);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckMHD (const DateStr : String; const CheckOptions : TMHDCheckOptions; const MinRLZ : Integer; const FehlerMsg, DatumName : String; var DateTime : TDateTime; var ErrorText : String) : Boolean;
var
  dt : TDateTime;
  intwert : Integer;
  jahr, monat, tag : Word;
begin
  Result := True;

  if (Length (DateStr) > 0) then begin
    try
      if (DateStr [1] = '#') then begin
        if not (TryStrToInt (Copy (DateStr,2,2), intwert)) then begin
          Result := False;
          ErrorText := FormatMessageText (1846, [DatumName]);
        end else begin
          jahr := intwert + 2000;

          if not (TryStrToInt (Copy (DateStr,4,2), intwert)) then begin
            Result := False;
            ErrorText :='Ungültiger Woche für das '+DatumName;
          end else if (intwert < 1) or (intwert > 52) then begin
            Result := False;
            ErrorText :='Ungültiger Woche für das '+DatumName;
          end else begin
            dt := EncodeDateWeek (jahr, intwert);
            DecodeDate (dt, jahr, monat, tag);

            if not (TryStrToInt (Copy (DateStr,6,2), intwert)) then begin
              Result := False;
              ErrorText :='Ungültiger Tag für das '+DatumName;
            end else begin
              if not (TryEncodeDate (jahr, monat, intwert, DateTime)) then begin
                Result := False;
                ErrorText := FormatMessageText (1846, [DatumName]);
              end;
            end;
          end;
        end;
      end else if (Pos ('.', DateStr) = 0) then begin
        DecodeDate (Now, jahr, monat, tag);

        try
          tag := StrToInt (Copy (DateStr,1,2));

          if (Length (DateStr) > 2) then
            monat := StrToInt (Copy (DateStr,3,2));

          if (Length (DateStr) > 6) then
            jahr := StrToInt (Copy (DateStr,5,4))
          else if (Length (DateStr) > 4) then
            jahr := 2000 + StrToInt (Copy (DateStr,5,2));

          if not (TryEncodeDate (jahr, monat, tag, DateTime)) then begin
            Result := False;
            ErrorText := FormatMessageText (1847, [DatumName]);
          end;
        except
          Result := False;
          ErrorText := FormatMessageText (1848, [DatumName]);
        end;
      end else if not (TryStrToDate (DateStr, DateTime)) then begin
        Result := False;
        ErrorText := FormatMessageText (1847, [DatumName]);
      end;

      if (Result) then begin
        DateTime := Round (DateTime);

        if (cmhdCheckVergangenheit in CheckOptions) and (DateTime < (Now - 1)) then begin
          Result := (FrontendMessages.MessageDlg ('Das '+DatumName+' Datum liegt in der Vergangenheit, dennoch übernehmen?', mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes);

          if not (Result) then
            ErrorText := FormatResourceText (1849, [DatumName]);
        end else if (cmhdVergangenheit in CheckOptions) and (DateTime < (Now - 1)) then begin
          Result := False;
          ErrorText := FormatMessageText (1849, [DatumName]);
        end else if (cmhdZukunft in CheckOptions) and (DateTime > Now) then begin
          Result := False;
          ErrorText := FormatMessageText (1850, [DatumName]);
        end;

        if Result and (MinRLZ > 0) then begin
          if (DateTime < (Round (Now) + (MinRLZ - 1))) then begin
            if (Length (FehlerMsg) > 0) Then begin
              Result := (FrontendMessages.MessageDlg (Format (FehlerMsg, [IntToStr (Round ((Now + (MinRLZ - 1) - DateTime)))]), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes);
            end else Result := False;

            if not (Result) then
              ErrorText :=DatumName+' unterschreitet die min. Restlaufzeit um '+IntToStr (Round ((Now + (MinRLZ - 1) - DateTime)))+' Tage';
          end;
        end;
      end;
    except
      Result := False;
      ErrorText := FormatMessageText (1847, [DatumName]);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadComboxDBItems (ComboBox : TCustomComboBox; const TableName, ColumnName : String; const pDisplayDBItems : Boolean; const pDisplayDesc : Boolean) : Integer;
var
  res,
  idx,
  selidx : Integer;
  desc   : String;
  query  : TADOQuery;
begin
  res := 0;

  selidx := -1;

  ClearComboBoxObjects (ComboBox);

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from V_INF_WERTE where TABELLE=:TableName and SPALTE=:ColumnName order by REIHEN_FOLGE, WERT');
    query.Parameters.ParamByName('TableName').Value := TableName;
    query.Parameters.ParamByName('ColumnName').Value := ColumnName;

    try
      query.Open;

      while not (query.Eof) do begin
        if not (pDisplayDBItems) then
          desc := query.FieldByName ('TEXT').AsString
        else begin
          if Assigned (query.FindField ('STR_WERT')) then
            desc := query.FieldByName ('STR_WERT').AsString
          else
            desc := query.FieldByName ('WERT').AsString;

          if (ComboBox is TComboboxPro) then
            desc := desc + (ComboBox as TComboboxPro).ItemDelimiter + query.FieldByName ('TEXT').AsString
          else
            desc := desc + '|' + query.FieldByName ('TEXT').AsString;
        end;

        if pDisplayDesc then begin
          if (ComboBox is TComboboxPro) then
            desc :=  desc + (ComboBox as TComboboxPro).ItemDelimiter + query.FieldByName ('BESCHREIBUNG').AsString
          else
            desc :=  desc + '|' + query.FieldByName ('BESCHREIBUNG').AsString;
        end;

        idx := ComboBox.Items.AddObject (desc, TDBItemsDaten.Create (query.FieldByName ('SPALTE').AsString, query.FieldByName ('WERT').AsString, query.FieldByName ('TEXT').AsString, query.FieldByName ('DEFAULT_SPALTE').AsString, query.FieldByName ('ART').AsString));

        if not (query.FieldByName ('DEFAULT_SPALTE').IsNull) and (query.FieldByName ('DEFAULT_SPALTE').AsInteger = 1) then
          selidx := idx;

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  if (selidx <> -1) then
    ComboBox.ItemIndex := selidx;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadListDBItems (List : TList; const TableName, ColumnName : String) : Integer;
var
  res    : Integer;
  query  : TADOQuery;
begin
  res := 0;

  ClearListObjects (List);

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select SPALTE,WERT,TEXT,BESCHREIBUNG,DEFAULT_SPALTE,ART from V_INF_WERTE where TABELLE=:TableName and SPALTE=:ColumnName order by REIHEN_FOLGE, WERT');
    query.Parameters.ParamByName('TableName').Value := TableName;
    query.Parameters.ParamByName('ColumnName').Value := ColumnName;

    try
      query.Open;

      while not (query.Eof) do begin
        List.Add (TDBItemsDaten.Create (query.Fields [0].AsString, query.Fields [1].AsString, query.Fields [2].AsString, query.Fields [4].AsString, query.Fields [5].AsString));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadListDBItems (List : TList; const TableName : String; const ColumnNames : array of string) : Integer;
var
  i,
  res    : Integer;
  instr  : String;
  query  : TADOQuery;
begin
  res := 0;

  instr := '';
  for i:= Low (ColumnNames) to High (ColumnNames) do begin
    if (Length (instr) > 0) then
      instr := instr + ',';

    instr := instr + #39 + ColumnNames [i] + #39;
  end;

  ClearListObjects (List);

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('select SPALTE,WERT,TEXT,BESCHREIBUNG,DEFAULT_SPALTE,ART from V_INF_WERTE where TABELLE=:TableName and SPALTE in ('+instr+') order by REIHEN_FOLGE, WERT');
    query.Parameters.ParamByName('TableName').Value := TableName;

    try
      query.Open;

      while not (query.Eof) do begin
        List.Add (TDBItemsDaten.Create (query.Fields [0].AsString, query.Fields [1].AsString, query.Fields [2].AsString, query.Fields [4].AsString, query.Fields [4].AsString));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetComboBoxItems (ComboBox : TCustomComboBox; const Index : Integer = -1) : TComboboxItems;
var
  idx : Integer;
begin
  try
    if (Index = -1) then
      idx := ComboBox.ItemIndex
    else idx := Index;

    if (idx >= 0) and (idx < ComboBox.Items.Count) then begin
      if Assigned (ComboBox.Items.Objects [idx]) then begin
        if (ComboBox.Items.Objects [idx] is TComboboxItems) Then
          Result := TComboboxItems (ComboBox.Items.Objects[idx])
        else
          Result := Nil;
      end else Result := Nil;
    end else Result := Nil;
  except
    Result := Nil;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetComboBoxItemWert (ComboBox : TCustomComboBox; const Index : Integer = -1) : String;
var
  idx : Integer;
begin
  try
    if (Index = -1) then
      idx := ComboBox.ItemIndex
    else idx := Index;

    if (idx >= 0) and (idx < ComboBox.Items.Count) then begin
      if Assigned (ComboBox.Items.Objects [idx]) then begin
        if (ComboBox.Items.Objects [idx] is TComboboxItems) Then
          Result := TComboboxItems (ComboBox.Items.Objects[idx]).ItemsWert
        else
          Result := '';
      end else Result := '';
    end else Result := '';
  except
    Result := '';
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetComboBoxDBItem (ComboBox : TCustomComboBox; const Index : Integer = -1) : TDBItemsDaten;
var
  idx : Integer;
begin
  try
    if (Index = -1) then
      idx := ComboBox.ItemIndex
    else idx := Index;

    if (idx >= 0) and (idx < ComboBox.Items.Count) then begin
      if Assigned (ComboBox.Items.Objects [idx]) then begin
        if (ComboBox.Items.Objects [idx] is TDBItemsDaten) Then
          Result := TDBItemsDaten (ComboBox.Items.Objects[idx])
        else
          Result := Nil;
      end else Result := Nil;
    end else Result := Nil;
  except
    Result := Nil;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetComboBoxDBItemWert (ComboBox : TCustomComboBox; const Index : Integer = -1) : String;
var
  idx : Integer;
begin
  try
    if (Index = -1) then
      idx := ComboBox.ItemIndex
    else idx := Index;

    if (idx >= 0) and (idx < ComboBox.Items.Count) then begin
      if Assigned (ComboBox.Items.Objects [idx]) then begin
        if (ComboBox.Items.Objects [idx] is TDBItemsDaten) Then
          Result := TDBItemsDaten (ComboBox.Items.Objects[idx]).ItemsWert
        else
          Result := '';
      end else Result := '';
    end else Result := '';
  except
    Result := '';
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindComboboxDBItem (ComboBox : TCustomComboBox; const pDBItem : String; const Default : Integer = -1): Integer;
var
  i: Integer;
begin
  i := 0;

  while (i < ComboBox.Items.Count) do begin
    if Assigned (ComboBox.Items.Objects[i]) and (ComboBox.Items.Objects[i] is TDBItemsDaten) and (TDBItemsDaten (ComboBox.Items.Objects[i]).ItemsWert = pDBItem) then
      break;

    Inc (i);
  end;

  if (i < ComboBox.Items.Count) then
    Result := i
  else Result := Default;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 09.06.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindComboboxDBItemWert (ComboBox : TCustomComboBox; const pDBItem : String; const Default : Integer = -1): Integer;
var
  i: Integer;
begin
  i := 0;

  while (i < ComboBox.Items.Count) do begin
    if Assigned (ComboBox.Items.Objects[i]) and (ComboBox.Items.Objects[i] is TDBItemsDaten) and (TDBItemsDaten (ComboBox.Items.Objects[i]).ItemsWert = pDBItem) then
      break;

    Inc (i);
  end;

  if (i < ComboBox.Items.Count) then
    Result := i
  else Result := Default;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetComboBoxLBRef (ComboBox : TCustomComboBox; const Default : Integer): Integer;
var
  idx : Integer;
begin
  try
    idx := ComboBox.ItemIndex;

    if (idx >= 0) and (idx < ComboBox.Items.Count) then begin
      if Assigned (ComboBox.Items.Objects [idx]) then begin
        if (ComboBox.Items.Objects [idx] is TComboBoxLBLPRef) Then
          Result := TComboBoxLBLPRef (ComboBox.Items.Objects[idx]).Ref
        else if (ComboBox.Items.Objects [idx] is TComboBoxLBPlusZoneRef) Then
          Result := TComboBoxLBPlusZoneRef (ComboBox.Items.Objects[idx]).Ref
        else
          Result := -1;
      end else Result := -1;
    end else Result := -1;
  except
    Result := -1;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetComboBoxLPRef (ComboBox : TCustomComboBox; const Default : Integer = -1): Integer;
var
  idx : Integer;
begin
  try
    idx := ComboBox.ItemIndex;

    if (idx >= 0) and (idx < ComboBox.Items.Count) then begin
      if Assigned (ComboBox.Items.Objects [idx]) then begin
        if not (ComboBox.Items.Objects [idx] is TComboBoxLBLPRef) Then
          Result := -1
        else
          Result := TComboBoxLBLPRef (ComboBox.Items.Objects[idx]).RefLP;
      end else Result := -1;
    end else Result := -1;
  except
    Result := -1;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindComboboxLBLPRef (ComboBox : TCustomComboBox; const RefLB, RefLP : Integer; const Default : Integer): Integer;
var
  i,
  lbidx : Integer;
begin
  i := 0;
  lbidx := -1;

  while (i < ComboBox.Items.Count) do begin
    if Assigned (ComboBox.Items.Objects[i]) and (TComboBoxLBLPRef (ComboBox.Items.Objects[i]).Ref = RefLB) then begin
      lbidx := i;

      if ((RefLP = -1) or (TComboBoxLBLPRef (ComboBox.Items.Objects[i]).RefLP = RefLP)) then
        break;
    end;

    Inc (i);
  end;

  if (i < ComboBox.Items.Count) then
    Result := i
  else if (lbidx > 0) then
    Result := lbidx
  else
    Result := Default;
end;

//******************************************************************************
//* Function Name: FindComboboxMandRef
//* Author       : Stefan Graf
//* Datum        : 25.10.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindComboboxMandRef (ComboBox : TCustomComboBox; const RefMand, RefSubMand : Integer; const Default : Integer = -1): Integer;
var
  i : Integer;
begin
  i := 0;

  while (i < ComboBox.Items.Count) do begin
    if (RefSubMand > 0) then begin
      if Assigned (ComboBox.Items.Objects[i]) and (ComboBox.Items.Objects[i] is TComboboxMandantRef) and (TComboboxMandantRef (ComboBox.Items.Objects[i]).RefSubMand = RefSubMand) then
        break;
    end else begin
      if Assigned (ComboBox.Items.Objects[i]) and (TComboboxRef (ComboBox.Items.Objects[i]).Ref = RefMand) then
        break;
    end;

    Inc (i);
  end;

  if (i < ComboBox.Items.Count) then
    Result := i
  else
    Result := Default;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 01.09.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  FindComboboxDefault (ComboBox : TCustomComboBox; const Default : Integer) : integer;
var
  i : Integer;
begin
  i := 0;

  while (i < ComboBox.Items.Count) do begin
    if Assigned (ComboBox.Items.Objects[i]) and (ComboBox.Items.Objects[i] is TComboboxZustand) and (TComboboxZustand (ComboBox.Items.Objects[i]).OptDefault = '1') then
      break;

    Inc (i);
  end;

  if (i < ComboBox.Items.Count) then
    Result := i
  else
    Result := Default;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function OpenLVSDatabase (const AppKennung, SubKeyName : String; const AdminFlag : Boolean) : Integer;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function GetTerminalSessionInfo : Integer;
  var
    ts     : TTerminalService;
    clname : String;
    cname  : array [0..MAX_COMPUTERNAME_LENGTH] of char;
    csize  : DWORD;
  begin
    csize := sizeof (cname) - 1;
    GetComputerName (cname, csize);
    cname [csize] := #0;
    LVSDatenModul.AktHost := StrPas (cname);

    ts := TTerminalService.Create;

    try
      LVSDatenModul.AktConfigName := OSUserName;

      if (ts.IsRemoteSession) then begin
        clname := GetParamVal ('ICA_CLIENT');

        if (Length (clname) > 0) then
          LVSDatenModul.AktClientName := clname
        else
          LVSDatenModul.AktClientName := ts.ClientName;
      end else begin
        LVSDatenModul.AktClientName := LVSDatenModul.AktHost;
      end;
    finally
      ts.Free;
    end;

    Result := 0;
  end;

var
  res,
  count,
  scandev,
  strpos,
  maxsessions : Integer;
  subreg      : TRegistryModule;
  recht       : TLVSREchte;
  opt,
  pwstr,
  typstr,
  userstr,
  dbuserstr,
  locstr,
  mandstr,
  lagerstr,
  errtext,
  logline,
  passwdstr   : String;
  flag,
  change,
  abort       : Boolean;
  msgtext     : String;
  DBLoginForm : TDBLoginForm;

  {$ifdef Trace}
    trcname : String;
  {$endif}
begin
  Assert (Assigned (LVSDatenModul),     'Das Modul "LVSDatenModul" ist nicht mit dem Projekt verbunden');
  Assert (Assigned (LVSConfigModul),    'Das Modul "LVSConfigModul" ist nicht mit dem Projekt verbunden');
  Assert (Assigned (LVSSecurityModule), 'Das Modul "LVSSecurityModule" ist nicht mit dem Projekt verbunden');

  GetTerminalSessionInfo;

  DBLoginForm := TDBLoginForm.Create (Nil);
  DBLoginForm.RegKeyStr := 'Software\'+LVSConfigModul.MasterRegKeyName+'\' + SubKeyName;
  DBLoginForm.SubKeyStr := SubKeyName;
  DBLoginForm.AdminFlag := AdminFlag;

  DBLoginForm.Prepare;

  abort := False;

  repeat
    Result := 0;

    if (DBLoginForm.ShowModal <> mrOk) then begin
      abort := True;
      Result := -1;
    end else if (DBLoginForm.SchemaComboBox.ItemIndex = -1) then begin
      abort := True;
      Result := -2
    end else begin
      LVSDatenModul.System := DBLoginForm.SystemComboBox.Text;
      LVSDatenModul.Schema := DBLoginForm.SchemaFeld [DBLoginForm.SchemaComboBox.ItemIndex];
      LVSDatenModul.ConnectionString := DBLoginForm.DBConnection;

      LVSConfigModul.SectionName  := DBLoginForm.SystemComboBox.Text;
      LVSConfigModul.RegKeyName   := 'Software\'+LVSConfigModul.MasterRegKeyName+'\Systeme\' + LVSConfigModul.SectionName + '\' + LVSDatenModul.Schema;

      if (Length (SubKeyName) > 0) then
        LVSConfigModul.RegKeyName  := LVSConfigModul.RegKeyName + '\' + SubKeyName;

      if (DBLoginForm.DomCheckBox.Checked) then begin
        with LVSDatenModul.MainADOConnection do
          ConnectionString := ConnectionString + ';OSAuthent=1';

        userstr   := '/';
        passwdstr := '';
      end else begin
        userstr   := DBLoginForm.UserEdit.Text;
        passwdstr := DBLoginForm.PassEdit.Text;
      end;

      {$ifdef Trace}
        trcname := 'LVSFrontEnd_'+DateToStr (Now)+'.trc';

        if Assigned (LVSConfigModul) then begin
          if (LVSConfigModul.SectionItems.IndexOfName('TraceFiles') <> -1) then
            trcname := AddBackSlash (LVSConfigModul.SectionItems.Values ['TraceFiles']) + OSUserName + '\' + trcname
          else if (LVSConfigModul.SectionItems.IndexOfName('LogFiles') <> -1) then
            trcname := AddBackSlash (LVSConfigModul.SectionItems.Values ['LogFiles']) + 'Log\' + OSUserName + '\' + trcname;
        end;

        if (Length (ExtractFilePath (trcname)) > 0) and not (DirectoryExists (ExtractFilePath (trcname))) then
          ForceDirectories (ExtractFilePath (trcname));

        if not (IsTraceOpen) or (trcname <> GetTraceFilename) then
          OpenTrace (trcname);
      {$endif}

      if (LVSConfigModul.SectionItems.IndexOfName('MaxLogFiles') <> -1) then begin
        try
          LVSDatenModul.MaxTraceFileCount := StrToInt (LVSConfigModul.SectionItems.Values ['MaxLogFiles']);
        except
        end;
      end;

      if (LVSConfigModul.SectionItems.IndexOfName('LogFileSize') <> -1) then begin
        try
          LVSDatenModul.MaxTraceFileSize := StrToInt (LVSConfigModul.SectionItems.Values ['LogFileSize']);
        except
        end;
      end;

      if (UpperCase (userstr) = UpperCase (LVSDatenModul.Schema)) then
        dbuserstr := userstr
      else if (Length (userstr) > 2) and (userstr [1] = '"') and (userstr [Length (userstr)] = '"') then begin
        userstr   := Copy (userstr, 2, Length (userstr) - 2);
        dbuserstr := userstr;
      end else if (Length (LVSConfigModul.DBUserPrefix) > 0) then
        dbuserstr := UpperCase (LVSConfigModul.DBUserPrefix) + '-' + userstr
      else
        dbuserstr := userstr;

      if (LVSDatenModul.OpenDatabase (userstr, dbuserstr, passwdstr, errtext) <> 0) Then begin
        Result := -2;
        DBLoginForm.PassEdit.Text := '';

        FrontendMessages.MessageDLG (FormatMessageText (1026, [errtext]), mtError, [mbOK], 0);
      end;
    end;
  until (Result = 0) or (abort);

  if (Result = 0) then begin
    res := CheckDatabase;

    if (res = 0) Then begin
      if (LVSDatenModul.AktDBUser <> LVSDatenModul.Schema) then
        res := CheckMaintenance (flag, msgtext)
      else begin
        res := 0;
        flag := False;
      end;
    end;

    if (res <> 0) or flag then begin
      if (Length (msgtext) = 0) then
        msgtext := FormatMessageText (1027, []);

      FrontendMessages.MessageDLG (msgtext, mtInformation, [mbOK], 0);
      Result := -3;
    end else begin
      if (LVSDatenModul.AktDBUser <> LVSDatenModul.Schema) then begin
        res := GetLogonMessage (msgtext);

        if (res = 0) and (Length (msgtext) > 0) then begin
          if (FrontendMessages.MessageDlg (msgtext, mtConfirmation, [mbYes,mbNo], 0) = mrYes) then
            Result := -4;
        end;
      end;

      if (Result = 0) Then begin
        maxsessions := LVSDatenModul.MaxSessions;

        //if (CheckSessions (LVSDatenModul.AktUserRef, LVSDatenModul.AktClientName, count) = 0) then begin
        if (CheckSessions (LVSDatenModul.AktUserRef, LVSDatenModul.AktClientName, maxsessions, count) = 0) then begin
          if (maxsessions <> -1) and (count > 0) and (count >= maxsessions) then begin
            FrontendMessages.MessageDLG (FormatMessageText (1028, []), mtError, [mbOK], 0);
            Result := -7;
          end;
        end;
      end;

      if (Result = 0) Then begin
        if Assigned (LVSSecurityModule) then begin
          Screen.Cursor := crSQLWait;

          try
            LVSSecurityModule.UpdateSecurity (AppKennung, LVSDatenModul.AktUser);

            GetRechte (LVSDatenModul.AktUser, AppKennung, recht);

            if (LVSDatenModul.AktUser = LVSDatenModul.Schema) or (Admin in recht) then begin
              if Assigned (LVSSecurityModule.ACOModul) Then begin
                LVSSecurityModule.ACOModul.CheckBerechtigungen (AppKennung, change);

                if (change) then begin
                  //Danach muss man die Rechte neu einlesen
                  LVSSecurityModule.UpdateSecurity (AppKennung, LVSDatenModul.AktUser);
                end;
              end;
            end else begin
              if not (Exec in recht) then begin
                FrontendMessages.MessageDLG (FormatMessageText (1029, []), mtError, [mbOK], 0);
                Result := -3;
              end;
            end;
          finally
            Screen.Cursor := crDefault;
          end;
        end;

        if (Result = 0) then begin
          if Assigned (UserReg) then
            UserReg.WriteRegValue('LastDB', DBLoginForm.SystemComboBox.Text);

          subreg := TRegistryModule.Create;

          if (subreg.OpenKey (HKEY_CURRENT_USER, LVSConfigModul.RegKeyName, KEY_READ or KEY_WRITE, True) = 0) Then begin
            if (DBLoginForm.DomCheckBox.Checked) then
              subreg.WriteRegValue ('NTSAuth', 'TRUE')
            else subreg.WriteRegValue ('NTSAuth', 'FALSE');

            subreg.WriteRegValue ('DBUser', DBLoginForm.UserEdit.Text);

            if (DBLoginForm.SavePassCheckBox.Checked) then
              subreg.WriteRegValue ('SavePasswd', 'TRUE')
            else subreg.WriteRegValue ('SavePasswd', 'FALSE');

            if not (DBLoginForm.SavePassCheckBox.Checked) then
              subreg.WriteRegValue ('DBPasswd', '')
            else begin
              pwstr := #$aa + #$55 + Encrypt (DBLoginForm.PassEdit.Text, LVSPassKey);
              subreg.WriteRegValue ('DBPasswd', pwstr);
            end;

            subreg.CloseKey;
          end;

          if (subreg.OpenKey (HKEY_CURRENT_USER, 'Software\'+LVSConfigModul.MasterRegKeyName+'\Systeme\' + LVSConfigModul.SectionName, KEY_READ or KEY_WRITE or KEY_ENUMERATE_SUB_KEYS, True) = 0) Then begin
            subreg.WriteRegValue ('DBSchema', LVSDatenModul.Schema);

            subreg.DeleteRegKey ('DBGrids', True);
          end;

          subreg.Free;

          if (DBLoginForm.LeitstandPanel.Visible) then begin
            if (GetComboBoxRef (DBLoginForm.LeitstandComboBox) > 0) then
              AssignLeitstand (GetComboBoxRef (DBLoginForm.LeitstandComboBox), DBLoginForm.ClientName);

            LVSConfigModul.UseLeitstand := True;
            LVSConfigModul.RefLeitstand := GetComboBoxRef (DBLoginForm.LeitstandComboBox);
          end else begin
            LVSConfigModul.UseLeitstand := False;
            LVSConfigModul.RefLeitstand := -1;
          end;

          LVSConfigModul.ReadConfig;

          if Assigned (DBGridUtils) Then begin
            with LVSConfigModul.DBGrids do begin
              DBGridUtils.TextColor      := TextColor;
              DBGridUtils.HiddenSelColor := HiddenSelColor;
              DBGridUtils.FontName       := FontName;
              DBGridUtils.FontSize       := FontSize;
              DBGridUtils.TitleWrap      := TitleWrap;
              DBGridUtils.TitleColor     := TitleColor;
              DBGridUtils.TitleBackgnd   := TitleBackgnd;
              DBGridUtils.TitleFont      := TitleFont;
              DBGridUtils.TitleSize      := TitleSize;
              DBGridUtils.OddColorFlag   := AlternateColors;
              DBGridUtils.EvenColor      := EvenColor;
              DBGridUtils.OddColor       := OddColor;
              DBGridUtils.GridFetchAll   := GridFetchAll;
              DBGridUtils.GridAutoUpdate := GridAutoUpdate;
            end;

            DBGridUtils.ChangeDBGrids;
          end;

          LVSConfigModul.ConfigForm;


          LVSDatenModul.AktVersion   := FileVersion (4, 2);

          if (Length (LVSDatenModul.AktSprache) = 0) then
            LVSDatenModul.AktSprache   := 'DE';

          Screen.Cursor := crHourGlass;

          try
            if (ReadDeviceConfig (LVSDatenModul.AktConfigName, scandev, opt) = 1) then begin
              ReadDeviceConfig (LVSDatenModul.AktClientName, scandev, opt);
              ChangeDeviceConfig (LVSDatenModul.AktConfigName, scandev, opt);
            end;

            SetupScanner (scandev);

            if (Length (opt) > 0) and (opt [1] = '1') then
              AppOptions := AppOptions + [aoTouch]
            else AppOptions := AppOptions - [aoTouch];

            {$ifdef Numpad}
              if (Length (opt) > 1) and (opt [2] = '1') then
                NumericKeypad.Visible := True;
            {$endif}

            if Assigned (PrintModule) then begin
              if LVSConfigModul.UseLeitstand and (LVSConfigModul.RefLeitstand <> -1) then begin
                ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'LASER_PRINTER', PrintModule.LeitLaserPrinter.Port);
                ReadLeitstandConfigValue (LVSConfigModul.RefLeitstand, 'LABEL_PRINTER', PrintModule.LeitLabelPrinter.Name);
              end;

              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'STD_PRINTER',        PrintModule.StdLaserPrinter.Port);
              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'STD_PRINTER_BIN',    PrintModule.StdLaserPrinter.BinNr);
              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'KOMM_PRINTER',       PrintModule.KommLaserPrinter.Port);
              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'KOMM_PRINTER_BIN',   PrintModule.KommLaserPrinter.BinNr);
              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'VERLADE_PRINTER',    PrintModule.VerladeLaserPrinter.Port);
              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'VERLADE_PRINTER_BIN',PrintModule.VerladeLaserPrinter.BinNr);
              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'LS_PRINTER',         PrintModule.LSLaserPrinter.Port);
              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'LS_PRINTER_BIN',     PrintModule.LSLaserPrinter.BinNr);
              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'RE_PRINTER',         PrintModule.RELaserPrinter.Port);
              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'RE_PRINTER_BIN',     PrintModule.RELaserPrinter.BinNr);
              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'ZOLL_PRINTER',       PrintModule.ZollLaserPrinter.Port);
              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'ZOLL_PRINTER_BIN',   PrintModule.ZollLaserPrinter.BinNr);

              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'VPE_PRINTER', PrintModule.VPELabelPrinter.Ref, PrintModule.VPELabelPrinter.Name);
              if (PrintModule.VPELabelPrinter.Ref = -1) then
                PrintModule.VPELabelPrinter.Port := PrintModule.VPELabelPrinter.Name;

              ReadDeviceConfigValue (LVSDatenModul.AktConfigName, 'NVE_PRINTER', PrintModule.NVELabelPrinter.Ref, PrintModule.NVELabelPrinter.Name);
              if (PrintModule.NVELabelPrinter.Ref = -1) then begin
                strpos := Pos (';', PrintModule.NVELabelPrinter.Name);

                if (strpos = 0) then
                  PrintModule.NVELabelPrinter.Port := PrintModule.NVELabelPrinter.Name
                else PrintModule.NVELabelPrinter.Port := copy (PrintModule.NVELabelPrinter.Name, 1, strpos -1);
              end;


              //Wenn nicht angeben ist, die Leitstandsangaben übernehmen
              if (Length (PrintModule.StdLaserPrinter.Port) = 0) then
                PrintModule.StdLaserPrinter.Port :=  PrintModule.LeitLaserPrinter.Port;

              if (Length (PrintModule.LSLaserPrinter.Port) = 0) then
                PrintModule.LSLaserPrinter.Port :=  PrintModule.LeitLaserPrinter.Port;

              if (Length (PrintModule.RELaserPrinter.Port) = 0) then
                PrintModule.RELaserPrinter.Port :=  PrintModule.LeitLaserPrinter.Port;

              if (Length (PrintModule.ZollLaserPrinter.Port) = 0) then
                PrintModule.ZollLaserPrinter.Port :=  PrintModule.LeitLaserPrinter.Port;

              if (Length (PrintModule.KommLaserPrinter.Port) = 0) then
                PrintModule.KommLaserPrinter.Port  := PrintModule.LeitLaserPrinter.Port;

              if (Length (PrintModule.VerladeLaserPrinter.Port) = 0) then
                PrintModule.VerladeLaserPrinter.Port := PrintModule.LeitLaserPrinter.Port;


              if (Length (PrintModule.NVELabelPrinter.Name) = 0) then
                PrintModule.NVELabelPrinter.Name := PrintModule.LeitLabelPrinter.Name;

              if (Length (PrintModule.VPELabelPrinter.Name) = 0) then
                PrintModule.VPELabelPrinter.Name   := PrintModule.LeitLabelPrinter.Name;
            end;

            //Mandant, Niederlassung und Lager prüfen
            if not (DBLoginForm.LocationComboBox.Visible) then begin
              locstr   := '';
            end else begin
              if (GetComboBoxRef (DBLoginForm.LocationComboBox) <> -1) then
                locstr := DBLoginForm.LocationComboBox.GetItemText
              else locstr := '';
            end;

            if (GetComboBoxRef (DBLoginForm.MandantComboBox) <> -1) then
              mandstr := DBLoginForm.MandantComboBox.GetItemText
            else mandstr := '';

            if (GetComboBoxRef (DBLoginForm.LagerComboBox) <> -1) then
              lagerstr := DBLoginForm.LagerComboBox.GetItemText
            else lagerstr := '';

            if (Length (mandstr) > 0) or (Length (locstr) > 0) or (Length (lagerstr) > 0) then
              res := ChangeMandantLager (mandstr, locstr, lagerstr, errtext)
            else
              LVSSecurityModule.ACOModul.SetBerechtigungen;

            if (res <> 0) then begin
              Result := -4;

              FrontendMessages.MessageDLG (errtext, mtError, [mbOK], 0);
            end;
          finally
            Screen.Cursor := crDefault;
          end;
        end;
      end;
    end;
  end;

  if (Result <> 0) Then
    LVSDatenModul.MainADOConnection.Close;

  DBLoginForm.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeMandantLager (const MandantName, LocationName, LagerName : String; var ErrorText : String): Integer;
var
  res     : Integer;
  lrecht,
  mrecht  : TLVSRechte;
  subreg  : TRegistryModule;
begin
  res := 0;

  if (Length (LocationName) > 0) then
    res  := LVSDatenModul.CheckLVSLogin (MandantName, LocationName, LagerName, ErrorText);

  if (res = 0) then begin
    if (Length (MandantName) = 0) then begin
      res := GetRechte (LVSDatenModul.AktUser, 'MANDANT', mrecht);
      mrecht := [Read, Write];

      if (res = 0) and (LVSDatenModul.AktUser = LVSDatenModul.Schema) then
        mrecht := [Admin];
    end else res := GetRechte (LVSDatenModul.AktUser, 'MANDANT.' + MandantName, mrecht);

    if (res = 0) then begin
      if not ((Read in mrecht) or (Admin in mrecht)) then
        res := 1
      else begin
        if (Length (LagerName) > 0) then begin
          res := GetRechte (LVSDatenModul.AktUser, 'LAGER.' + LagerName, lrecht);

          if (res = 0) and not ((Read in lrecht) or (Admin in lrecht)) then
            res := 2;
        end else if (Length (LocationName) > 0) then begin
          res := GetRechte (LVSDatenModul.AktUser, 'LOC.' + LocationName, lrecht);

          if (res = 0) and not ((Read in lrecht) or (Admin in lrecht)) then begin
            if (LVSDatenModul.AktUser = LVSDatenModul.Schema) then
              lrecht := [Admin]
            else res := 3;
          end;
        end else
          lrecht := [Read, Write];
      end;
    end;

    if (res = 0) Then begin
      LVSDatenModul.CloseAllDatasets;

      res  := LVSDatenModul.LVSLogin (MandantName, LocationName, LagerName);

      if (res = 0) then begin
        if (LVSConfigModul.RefLeitstand > 0) then
          AssignLeitstandLocation (LVSConfigModul.RefLeitstand, LVSDatenModul.AktLocationRef);

        AktMandantRecht := mrecht;

        subreg := TRegistryModule.Create;
        if (subreg.OpenKey (HKEY_CURRENT_USER, LVSConfigModul.RegKeyName, KEY_READ or KEY_WRITE, True) = 0) Then begin
          subreg.WriteRegValue('Location', LVSDatenModul.AktLocation);
          subreg.WriteRegValue('Lager'   , LVSDatenModul.AktLager);
          subreg.WriteRegValue('Mandant' , LVSDatenModul.AktMandant);
        end;
        subreg.Free;

        if (LVSDatenModul.AktLocationRef <> -1) then begin
          LVSConfigModul.SetLocationConfig (LVSDatenModul.AktLocationRef);

          DBGridUtils.ChangeDBGrids;
        end;

        if Assigned (LVSSecurityModule.ACOModul) then begin
          SendMessage (Application.MainForm.Handle,  WM_SETREDRAW, 0, 0);

          try
            LVSSecurityModule.ACOModul.SetBerechtigungen;
          finally
            SendMessage (Application.MainForm.Handle,  WM_SETREDRAW, 1, 0);

            Application.MainForm.Invalidate;
          end;
        end;
      end else begin
        ErrorText := FormatMessageText (1030, [LVSDatenModul.LastLVSErrorText])
      end;
    end else if (res = 1) then
      ErrorText := FormatMessageText (1031, [MandantName])
    else if (res = 2) then
      ErrorText := FormatMessageText (1032, [LagerName])
    else if (res = 3) then
      ErrorText := FormatMessageText (1033, [LocationName])
    else if (res = 4) then
      ErrorText := FormatMessageText (1034, [LocationName])
    else
      ErrorText := FormatMessageText (1035, []);
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadQueryMenu (Sender : TObject; const MenuName : String; StartMenuItem : TMenuItem; OnClickEvent : TNotifyEvent) : Integer;
var
  res     : Integer;
  query   : TADOQuery;
  lastgrp : String;
  submenu,
  grpmenu : TMenuItem;
begin
  res := 0;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    StartMenuItem.Clear;

    query.SQL.Clear;
    query.SQL.Add ('select * from V_PCD_AUSWERTUNG_QUERY where MENU=:menu order by MENU_GRUPPE, REIHENFOLGE nulls last, MENU_TEXT');
    query.Parameters [0].Value := MenuName;

    query.Open;

    submenu := Nil;
    grpmenu := Nil;

    while not (query.Eof) do begin
      if not (Assigned (submenu)) or (lastgrp <> query.FieldByName ('MENU_GRUPPE').AsString) then begin
        grpmenu := TMenuItem.Create (Sender as TComponent);
        grpmenu.Name := 'ExeclMenuGrp'+query.FieldByName ('REF').AsString;
        grpmenu.Caption := query.FieldByName ('MENU_GRUPPE').AsString;
        StartMenuItem.Add (grpmenu);
        lastgrp := query.FieldByName ('MENU_GRUPPE').AsString
      end;

      submenu := TMenuItem.Create (Sender as TComponent);
      submenu.Name := 'ExeclMenu'+query.FieldByName ('REF').AsString;
      submenu.Caption := query.FieldByName ('MENU_TEXT').AsString;
      submenu.Hint    := query.FieldByName ('TEXT_KURZ').AsString;
      submenu.Tag     := query.FieldByName ('REF').AsInteger;
      submenu.OnClick := OnClickEvent;

      if Assigned (grpmenu) then
        grpmenu.Add (submenu)
      else
        StartMenuItem.Add (submenu);

      query.Next;
    end;

    StartMenuItem.Visible := (StartMenuItem.Count > 0);

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadSachbearbeiter (ComboBox: TCustomComboBox; const ArtStr : String; const RefMandant : Integer) : Integer;
var
  res     : Integer;
  query   : TADOQuery;
  namestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadSachbearbeiter');
    TraceParameter ('ComboBox  ', ComboBox.Name);
    TraceParameter ('ArtStr    ', ArtStr);
    TraceParameter ('RefMandant', RefMandant);
  {$ENDIF}

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (ComboBox.ItemIndex > 0) then
      namestr := ComboBox.Items [ComboBox.ItemIndex]
    else namestr := '';

    ClearComboBoxObjects (ComboBox);
    ComboBox.Items.Add (GetResourceText (1020));

    query.SQL.Clear;
    query.SQL.Add ('select REF, NAME from V_ANSPRECHPARTNER where ART='+#39+ArtStr+#39);

    if (RefMandant <> -1) then
      query.SQL.Add ('and REF_MAND='+IntToStr (RefMandant));

    query.SQL.Add ('order by NAME');

    try
      query.Open;

      while not (query.EOF) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString, TComboboxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;
      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  ComboBox.ItemIndex := ComboBox.Items.IndexOf (namestr);
  if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadRelationen (ComboBox: TCustomComboBox; const ArtStr : String; const RefLager : Integer) : Integer;
var
  res     : Integer;
  query   : TADOQuery;
  namestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadRelationen');
    TraceParameter ('ComboBox', ComboBox.Name);
    TraceParameter ('ArtStr  ', ArtStr);
    TraceParameter ('RefLager', RefLager);
  {$ENDIF}

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (ComboBox.ItemIndex > 0) then
      namestr := ComboBox.Items [ComboBox.ItemIndex]
    else namestr := '';

    ClearComboBoxObjects (ComboBox);

    query.SQL.Clear;
    query.SQL.Add ('select REF, NAME, BESCHREIBUNG from V_WA_RELATION where ART=:art');
    query.Parameters.ParamByName('art').Value := ArtStr;

    if (RefLager <> -1) then begin
      query.SQL.Add ('and (REF_LAGER is null and REF_LOCATION in (select REF_LOCATION from V_LOCATION_REL_LAGER where REF_LAGER=:ref_lager_2) or REF_LAGER=:ref_lager_1)');
      query.Parameters.ParamByName('ref_lager_1').Value := RefLager;
      query.Parameters.ParamByName('ref_lager_2').Value := RefLager;
    end else begin
      query.SQL.Add ('and REF_LOCATION=:ref_loc');
      query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
    end;

    query.SQL.Add ('order by NAME');

    try
      query.Open;

      while not (query.EOF) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboboxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;
      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  ComboBox.ItemIndex := ComboBox.Items.IndexOf (namestr);
  if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadPackplatz (ComboBox : TComboBoxPro; const RefLager : Integer; const Options : String) : Integer;
var
  res     : Integer;
  query   : TADOQuery;
  namestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadPackplatz');
    TraceParameter ('ComboBox', ComboBox.Name);
    TraceParameter ('RefLager', RefLager);
  {$ENDIF}

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (ComboBox.ItemIndex > 0) then
      namestr := ComboBox.GetItemText [ComboBox.ItemIndex]
    else namestr := '';

    ClearComboBoxObjects (ComboBox);

    query.SQL.Clear;
    query.SQL.Add ('select REF, NAME, DESCRIPTION from V_WA_PACKPLATZ where STATUS=''AKT''');

    if (RefLager <> -1) then begin
      query.SQL.Add ('and REF_LAGER=:ref_lager');
      query.Parameters.ParamByName('ref_lager').Value := RefLager;
    end else begin
      query.SQL.Add ('and REF_LOCATION=:ref_loc');
      query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
    end;

    if (Pos ('AUTO', Options) > 0) then
      query.SQL.Add ('and nvl (OPT_AUTO_PACK, ''0'')>''0''');

    query.SQL.Add ('order by NAME');

    try
      query.Open;

      while not (query.EOF) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboboxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;
      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  ComboBox.ItemIndex := ComboBox.IndexOf (namestr);
  if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadDepotRelationen (ComboBox: TCustomComboBox; const ArtStr : String; const RefDepot : Integer) : Integer;
var
  res     : Integer;
  query   : TADOQuery;
  namestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadDepotRelationen');
    TraceParameter ('ComboBox', ComboBox.Name);
    TraceParameter ('ArtStr  ', ArtStr);
    TraceParameter ('RefDepot', RefDepot);
  {$ENDIF}

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (ComboBox.ItemIndex > 0) then
      namestr := ComboBox.Items [ComboBox.ItemIndex]
    else namestr := '';

    ClearComboBoxObjects (ComboBox);
    ComboBox.Items.Add (GetResourceText (1020));

    query.SQL.Add ('select REF, NAME from V_WA_RELATION where ART=:art and REF_DEPOT=:ref_depot order by NAME');
    query.Parameters.ParamByName('art').Value := ArtStr;
    query.Parameters.ParamByName('ref_depot').Value := RefDepot;

    try
      query.Open;

      while not (query.EOF) do begin
        ComboBox.Items.AddObject (query.Fields [1].AsString, TComboboxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;
      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  ComboBox.ItemIndex := ComboBox.Items.IndexOf (namestr);
  if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadSysTexte (ComboBox: TCustomComboBox; const AreaStr : String; const RefMandant : Integer; const LeerEntry : Boolean) : Integer;
begin
  Result := LoadSysTexte (ComboBox, AreaStr, '', RefMandant, LeerEntry);
end;

function  LoadSysTexte (ComboBox: TCustomComboBox; const AreaStr, SubAreaStr : String; const RefMandant : Integer; const LeerEntry : Boolean) : Integer;
var
  i,
  res,
  idx,
  defidx   : Integer;
  query    : TADOQuery;
  strlist  : TStringList;
  subflag  : Boolean;
  defstr,
  varstr,
  namestr,
  attrstr,
  combostr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadSysTexte');
    TraceParameter ('ComboBox  ', ComboBox.Name);
    TraceParameter ('AreaStr   ', AreaStr);
    TraceParameter ('SubAreaStr', SubAreaStr);
    TraceParameter ('RefMandant', RefMandant);
  {$ENDIF}

  defidx := -1;
  defstr := '';

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (ComboBox.ItemIndex > 0) then
      namestr := ComboBox.Items [ComboBox.ItemIndex]
    else namestr := '';

    ClearComboBoxObjects (ComboBox);

    if (RefMandant <= 0) then
      query.SQL.Add ('select * from V_SYS_TEXTE where AREA=:AreaStr and SPRACHE=:AktSprache order by SEL nulls last, lower (TEXT)')
    else begin
      query.SQL.Add ('select * from V_SYS_TEXTE where (REF_MAND is null or REF_MAND=:RefMandant) and AREA=:AreaStr and SPRACHE=:AktSprache order by SEL nulls last, lower (TEXT)');
      query.Parameters.ParamByName('RefMandant').Value := RefMandant;
    end;
    query.Parameters.ParamByName('AreaStr').Value := AreaStr;
    query.Parameters.ParamByName('AktSprache').Value := LVSDatenModul.AktSprache;

    try
      query.Open;

      if (query.RecordCount > 0) then begin
        subflag := Assigned (query.FindField ('SUB_AREA'));

        while not (query.EOF) do begin
          if ((Length (SubAreaStr) = 0) or (subflag and (query.FieldByName ('SUB_AREA').IsNull or (query.FieldByName ('SUB_AREA').AsString = SubAreaStr)))) then begin
            if (query.FieldByName ('TEXT').AsString ='#?#') then begin
              if (ComboBox is TComboBox) then
                (ComboBox as TComboBox).Style := csDropDown
              else if (ComboBox is TComboBoxPro) then
                (ComboBox as TComboBoxPro).Style := csDropDown;
            end else if (Length (query.FieldByName ('TEXT').AsString) > 0) then begin
              combostr := query.FieldByName ('TEXT').AsString;

              varstr := '';

              if Assigned (query.FindField ('VARIABLEN')) then
                varstr := query.FieldByName ('VARIABLEN').AsString;

              if (Length (varstr) > 0) then begin
                strlist := TStringList.Create;

                try
                  strlist.Delimiter := ';';
                  strlist.StrictDelimiter := True;

                  strlist.DelimitedText := varstr;

                  for i:=0 to strlist.Count - 1 do begin
                    //Das Pflichtkennzeichen ausblenden
                    if (Length (strlist[i]) > 1) and (strlist[i][1] = '!') then
                      strlist[i] := Copy (strlist[i], 2);
                  end;

                  //Die Variablen-Wildcards durch die jeweiligen Variablentexte ersetzen
                  if (strlist.Count > 0) then
                    combostr := StringReplace (combostr, '%1', '<'+strlist[0]+'>', []);

                  if (strlist.Count > 1) then
                    combostr := StringReplace (combostr, '%2', '<'+strlist[1]+'>', []);

                  if (strlist.Count > 2) then
                    combostr := StringReplace (combostr, '%3', '<'+strlist[2]+'>', []);
                finally
                  strlist.Free;
                end;
              end;

              attrstr := '';

              if Assigned (query.FindField ('ATTRIBUT')) then
                attrstr := query.FieldByName ('ATTRIBUT').AsString;

              idx := ComboBox.Items.AddObject (combostr, TComboboxSysTexte.Create(DBGetReferenz (query.FieldByName ('TEXT_NR')), query.FieldByName ('DEFINITION').AsString, query.FieldByName ('TEXT').AsString, varstr, attrstr));

              if (query.FieldByName ('OPT_DEFAULT').AsString = '1') then begin
                defidx := idx;
                defstr := query.FieldByName ('TEXT').AsString;
              end;
            end;
          end;

          query.Next;
        end;

        if (LeerEntry) then begin
          ComboBox.Items.Insert (0, '');

          if (defidx <> -1) then
            Inc (defidx);
        end;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  if (Length (namestr) > 0) then begin
    ComboBox.ItemIndex := ComboBox.Items.IndexOf (namestr);
    if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := defidx;
    if (ComboBox.ItemIndex = -1) and (LeerEntry) then ComboBox.ItemIndex := 0;
  end else if (LeerEntry) then begin
    if (defidx = -1) then
      ComboBox.ItemIndex := 0
    else ComboBox.ItemIndex := defidx;
  end else
    ComboBox.ItemIndex := defidx;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadZustandTexte (ComboBox: TCustomComboBox; const AreaStr : String; const RefMandant : Integer; const AdminFlag, AnnahmeFlag : Boolean) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  opthint,
  optdef,
  optsperr,
  optlock,
  optprint : Char;
  idx,
  selidx   : Integer;
  textstr,
  namestr  : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadZustandTexte');
    TraceParameter ('ComboBox  ', ComboBox.Name);
    TraceParameter ('AreaStr   ', AreaStr);
    TraceParameter ('RefMandant', RefMandant);
  {$ENDIF}

  selidx := -1;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (ComboBox.ItemIndex > 0) then
      namestr := ComboBox.Items [ComboBox.ItemIndex]
    else namestr := '';

    query.SQL.Clear;
    query.SQL.Add ('select REF,TEXT,OPT_ASK_CLARIFY_HINT,OPT_SPERR_BESTAND,OPT_FINAL_STATUS,OPT_REPRINT_LABEL,OPT_DEFAULT,DEFINITION,ZUSTAND_NR from V_RETOUREN_ZUSTAND where STATUS=''ANG'' and (REF_MAND is null or REF_MAND=:RefMandant) and AREA=:AreaStr');

    if not AdminFlag then
      query.SQL.Add ('and nvl (OPT_ADMIN, ''0'')=''0''');

    if AnnahmeFlag then
      query.SQL.Add ('and nvl (OPT_ANNAHME_STATUS, ''0'')=''1''');

    query.SQL.Add ('order by REIHENFOLGE nulls first');

    query.Parameters.ParamByName('RefMandant').Value := RefMandant;
    query.Parameters.ParamByName('AreaStr').Value := AreaStr;


    try
      query.Open;

      if (query.RecordCount > 0) then begin
        ClearComboBoxObjects (ComboBox);

        while not (query.EOF) do begin
          if (query.Fields [2].IsNull) then
            opthint := '0'
          else opthint := query.Fields [2].AsString [1];

          if (query.Fields [3].IsNull) then
            optsperr := '0'
          else optsperr := query.Fields [3].AsString [1];

          if (query.Fields [4].IsNull) then
            optlock := '0'
          else optlock := query.Fields [4].AsString [1];

          if (query.Fields [5].IsNull) then
            optprint := '0'
          else optprint := query.Fields [5].AsString [1];

          if (query.Fields [6].IsNull) then
            optdef := '0'
          else optdef := query.Fields [6].AsString [1];

          if not (query.Fields [1].IsNull) then
            textstr := query.Fields [1].AsString
          else
            textstr := query.Fields [7].AsString;

          idx := ComboBox.Items.AddObject (textstr, TComboboxZustand.Create(query.Fields [0].AsInteger, DBGetIntegerNull (query.FieldByName ('ZUSTAND_NR')), opthint, optsperr, optlock, optprint, optdef, query.Fields [7].AsString));

          if (optdef = '1') then
            selidx := idx;

          query.Next;
        end;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  if (Length (namestr) > 0) then begin
    ComboBox.ItemIndex := ComboBox.Items.IndexOf (namestr);
    if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;
  end else if (selidx >= 0) then
    ComboBox.ItemIndex := selidx
  else
    ComboBox.ItemIndex := -1;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: FindComboboxZustandRef
//* Author       : Stefan Graf
//* Datum        ; 08.06.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  FindComboboxZustandRef   (ComboBox : TCustomComboBox; const ZustandRef : Integer; const Default : Integer = -1) : Integer;
var
  i: Integer;
begin
  {$IFDEF TRACE}
    FunctionStart('FindComboboxZustandRef');
    TraceParameter ('ComboBox  ', ComboBox.Name);
    TraceParameter ('ZustandRef', ZustandRef);
  {$ENDIF}

  try
    i := 0;
    while (i < ComboBox.Items.Count) and ((ComboBox.Items.Objects[i] = Nil) or (TComboboxZustand (ComboBox.Items.Objects[i]).Ref <> ZustandRef)) do
      Inc (i);

    if (i < ComboBox.Items.Count) then
      Result := i
    else Result := Default;
  except
    Result := -1;
  end;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$ENDIF}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.12.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  FindComboboxDefinition (ComboBox: TCustomComboBox; const DefStr : String; const Default : Integer) : Integer;
var
  i: Integer;
begin
  i := 0;
  while (i < ComboBox.Items.Count) and (not (Assigned (ComboBox.Items.Objects[i])) or (TComboboxZustand (ComboBox.Items.Objects[i]).Definition <> DefStr)) do
    Inc (i);

  if (i < ComboBox.Items.Count) then
    Result := i
  else Result := Default;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 19.09.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  FindComboboxSysText (ComboBox: TCustomComboBox; const DefStr, TextStr : String; const Default : Integer = -1) : Integer;
var
  i: Integer;
begin
  i := 0;
  while (i < ComboBox.Items.Count) and (not (Assigned (ComboBox.Items.Objects[i])) or (TComboboxSysTexte (ComboBox.Items.Objects[i]).Definition <> DefStr)) do
    Inc (i);

  if (i < ComboBox.Items.Count) then
    Result := i
  else Result := Default;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadRetoureCategory (ComboBox: TCustomComboBox; const RefMandant, RefSubMandant, RefLager : Integer; const AdminFlag : Boolean) : Integer;
var
  res      : Integer;
  query    : TADOQuery;
  opthint  : Char;
  namestr  : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadRetoureCategory');
    TraceParameter ('ComboBox  ', ComboBox.Name);
    TraceParameter ('RefMandant', RefMandant);
    TraceParameter ('RefSubMandant', RefSubMandant);
  {$ENDIF}

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (ComboBox.ItemIndex > 0) then
      namestr := ComboBox.Items [ComboBox.ItemIndex]
    else namestr := '';

    ClearComboBoxObjects (ComboBox);

    query.SQL.Add ('select REF,NAME,OPT_ASK_CLARIFY_HINT,DEFINITION from V_LAGER_BESTAND_CATEGORY where REF>0 and STATUS=''AKT'' and USE_FOR_RET=''1'' and REF_MAND=:ref_mand');
    query.SQL.Add ('and (REF_LAGER=:ref_lager or (REF_LAGER is null and REF_LOCATION is null) or (REF_LAGER is null and REF_LOCATION=:ref_loc))');
    query.Parameters.ParamByName('ref_mand').Value := RefMandant;
    query.Parameters.ParamByName('ref_lager').Value := RefLager;
    query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    if not (AdminFlag) then
      query.SQL.Add ('and nvl (OPT_ADMIN, ''0'')=''0''');

    if (RefSubMandant = -1) then
      query.SQL.Add ('and (REF_SUB_MAND is null)')
    else begin
      query.SQL.Add ('and (REF_SUB_MAND is null or (REF_SUB_MAND=:ref_sub_mand))');
      query.Parameters.ParamByName('ref_sub_mand').Value := RefSubMandant;
    end;

    query.SQL.Add ('order by REIHENFOLGE nulls last');

    try
      query.Open;

      while not (query.EOF) do begin
          if (query.Fields [2].IsNull) then
            opthint := '0'
          else opthint := query.Fields [2].AsString [1];

        ComboBox.Items.AddObject (query.Fields [1].AsString, TComboboxZustand.Create(query.Fields [0].AsInteger, -1, opthint, '0', '0', '0', '0', query.Fields [3].AsString));

        query.Next;
      end;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;

  if (Length (namestr) > 0) then begin
    ComboBox.ItemIndex := ComboBox.Items.IndexOf (namestr);
    if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;
  end else
    ComboBox.ItemIndex := -1;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadLandCombobox (ComboBox : TCustomComboBox) : Integer;
var
  namestr : String;
  query   : TSmartQuery;
begin
  ClearComboBoxObjects (ComboBox);

  query := LVSDatenModul.CreateSmartQuery (Nil, 'LoadLandCombobox');

  try
    if (LVSDatenModul.AktSprache = 'EN') then
      namestr := 'NAME_EN'
    else if (LVSDatenModul.AktSprache = 'FR') then
      namestr := 'NAME_FR'
    else
      namestr := 'NAME_DE';

    query.SQL.Add ('select distinct ISO2,ISO3,'+namestr+',REF_LOCATION,ORDER_BY, HAS_STATES, HAS_ZIP_CODE from V_ISO_COUNTRY_USED where (REF_LOCATION is null or REF_LOCATION=:ref_loc) order by ORDER_BY nulls last,'+namestr+',REF_LOCATION nulls last');
    query.Params.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    query.Open;

    while not (query.Eof) do begin
      if ComboBox.Items.IndexOf (query.Fields [2].AsString) = -1 then
        ComboBox.Items.AddObject (query.Fields [2].AsString, TComboboxLandStr.Create (query.Fields [0].AsString, query.Fields [1].AsString, (query.Fields [5].AsString = '1'), (query.Fields [6].AsString = '1')));

      query.Next;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FindComboboxLand (ComboBox : TCustomComboBox; const LandStr : String; const Default : Integer) : Integer;
var
  i       : Integer;
  suchstr : String;
begin
  if (Length (LandStr) = 1) then begin
    if (LandStr = 'D') then
      suchstr := 'DE'
    else if (LandStr = 'A') then
      suchstr := 'AT'
    else if (LandStr = 'I') then
      suchstr := 'IT'
    else if (LandStr = 'L') then
      suchstr := 'LU'
    else if (LandStr = 'E') then
      suchstr := 'ES'
    else if (LandStr = 'F') then
      suchstr := 'FR'
    else if (LandStr = 'H') then
      suchstr := 'HU'
    else if (LandStr = 'B') then
      suchstr := 'BE'
    else if (LandStr = 'P') then
      suchstr := 'PL'
    else
      suchstr := '';
  end else
    suchstr := LandStr;

  if (Length (suchstr) = 0) then
    Result := -1
  else if (Length (suchstr) = 2) then begin
    try
      i := 0;
      while (i < ComboBox.Items.Count) and (not Assigned (ComboBox.Items.Objects[i]) or (TComboboxLandStr (ComboBox.Items.Objects[i]).ISO2 <> suchstr)) do
        Inc (i);

      if (i < ComboBox.Items.Count) then
        Result := i
      else Result := Default;
    except
      Result := -1;
    end;
  end else if (Length (suchstr) = 3) then begin
    try
      i := 0;
      while (i < ComboBox.Items.Count) and (not Assigned (ComboBox.Items.Objects[i]) or (TComboboxLandStr (ComboBox.Items.Objects[i]).ISO3 <> suchstr)) do
        Inc (i);

      if (i < ComboBox.Items.Count) then
        Result := i
      else Result := Default;
    except
      Result := -1;
    end;
  end else
    Result := -1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadWarengruppenCombobox (ComboBox : TCustomComboBox; const RefLocation, RefMandant, RefSubMand : Integer) : Integer;
var
  res     : Integer;
  query   : TADOQuery;
  oldref  : Integer;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadWarengruppenCombobox');
    TraceParameter ('ComboBox', ComboBox.Name);
    TraceParameter ('RefLocation', RefLocation);
    TraceParameter ('RefMandant', RefMandant);
    TraceParameter ('RefSubMand', RefSubMand);
  {$ENDIF}

  query := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (ComboBox.ItemIndex > 0) then
      oldref := GetComboBoxRef (ComboBox)
    else oldref := -1;

    ClearComboBoxObjects (ComboBox);
    ComboBox.Items.Add (GetResourceText (1020));

    query.SQL.Add ('select REF, nvl (PATH_NAME, NAME), BEZEICHNUNG from V_ARTIKEL_GRUPPE');

    if (RefSubMand > 0) then begin
      query.SQL.Add ('where REF_SUB_MAND=:RefSubMand');
      query.Parameters.ParamByName('RefSubMand').Value := RefSubMand;
    end else begin
      query.SQL.Add ('where REF_MAND=:RefMandant and (REF_SUB_MAND is null or REF_SUB_MAND in (select REF_MAND from V_MANDANT_REL_LOCATION where REF_LOCATION=:ref_loc))');
      query.Parameters.ParamByName('RefMandant').Value := RefMandant;
      query.Parameters.ParamByName('ref_loc').Value := RefLocation;
    end;

    query.SQL.Add ('order by case when (substr (nvl (PATH_NAME, NAME),1,1) between ''0'' and ''9'') then lpad (nvl (PATH_NAME, NAME), 9, '' '') else upper (nvl (PATH_NAME, NAME)) end');

    try
      query.Open;

      while not (query.EOF) do begin
        if (query.Fields [2].IsNull) then
          ComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger))
        else
          ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  if (oldref = -1) then
    ComboBox.ItemIndex := 0
  else begin
    ComboBox.ItemIndex := FindComboboxRef (ComboBox, oldref);
    if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadWarenLieferantCombobox (ComboBox : TCustomComboBox; const SortType : Integer; const RefMandant, RefSubmand, RefLager : Integer) : Integer;
var
  res     : Integer;
  query   : TADOQuery;
  oldref  : Integer;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadWarenLieferantCombobox');
    TraceParameter ('ComboBox', ComboBox.Name);
    TraceParameter ('RefMandant', RefMandant);
    TraceParameter ('RefLager', RefLager);
  {$ENDIF}

  query := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (ComboBox.ItemIndex > 0) then
      oldref := GetComboBoxRef (ComboBox)
    else oldref := -1;

    ComboBox.Items.BeginUpdate;

    try
      ClearComboBoxObjects (ComboBox);
      ComboBox.Items.Add (GetResourceText (1020));

      query.SQL.Clear;
      query.SQL.Add ('select distinct REF, LIEFERANT_NR, LIEFERANT_NAME from V_LIEFERANTEN where STATUS=''AKT'' and REF_MAND=:ref_mand');
      query.Parameters.ParamByName('ref_mand').Value := RefMandant;

      if (RefSubmand > 0) then begin
        query.SQL.Add ('and (REF_SUB_MAND is null or REF_SUB_MAND=:ref_sub_mand)');
        query.Parameters.ParamByName('ref_sub_mand').Value := RefSubmand;
      end;

      if (RefLager <> -1) then begin
        query.SQL.Add ('and REF in (select REF_LIEFERANT from V_LIEFERANT_REL_LAGER where STATUS=''AKT'' and REF_LAGER=:ref_lager)');
        query.Parameters.ParamByName('ref_lager').Value := RefLager;
      end else if (LVSDatenModul.AktLocationRef <> -1) then begin
        query.SQL.Add ('and REF in (select rel.REF_LIEFERANT from V_LIEFERANT_REL_LAGER rel, V_PCD_LAGER l where rel.STATUS=''AKT'' and l.REF=rel.REF_LAGER and l.REF_LOCATION=:ref_loc)');
        query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
      end;

      if (SortType = 0) then
        query.SQL.Add ('order by LPAD (LIEFERANT_NR, 9,''0''), upper (LIEFERANT_NAME)')
      else
        query.SQL.Add ('order by upper (LIEFERANT_NAME),LPAD (LIEFERANT_NR, 9,''0'')');

      try
        query.Open;

        while not (query.EOF) do begin
          if (SortType = 0) then
            ComboBox.Items.AddObject (query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger))
          else
            ComboBox.Items.AddObject (query.Fields [2].AsString+'|'+query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

          query.Next;
        end;

        query.Close;
      except
        res := -9;
      end;
    finally
      ComboBox.Items.EndUpdate;
    end;
  finally
    query.Free;
  end;

  if (oldref = -1) then
    ComboBox.ItemIndex := 0
  else begin
    ComboBox.ItemIndex := FindComboboxRef (ComboBox, oldref);
    if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 13.04.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadBestandCategoryCombobox (ComboBox : TCustomComboBox; const RefMandant, RefLager : Integer; const Usage : String; const RefSel : Integer) : Integer;
var
  res,
  ref     : Integer;
  query   : TADOQuery;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadBestandCategoryCombobox');
    TraceParameter ('ComboBox  ', ComboBox.Name);
    TraceParameter ('RefMandant', RefMandant);
    TraceParameter ('Usage     ', Usage);
    TraceParameter ('RefSel    ', RefSel);
  {$ENDIF}

  query := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select REF, NAME, DEFAULT_CATEGORY from V_LAGER_BESTAND_CATEGORY where STATUS=''AKT'' and USE_FOR_BES=''1''');
    query.SQL.Add ('and (REF_LAGER=:ref_lager or (REF_LAGER is null and REF_LOCATION is null) or (REF_LAGER is null and REF_LOCATION=:ref_loc))');
    query.Parameters.ParamByName('ref_lager').Value := RefLager;
    query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    if (RefMandant > 0) then begin
      query.SQL.Add ('and (REF_MAND is null or REF_MAND=:RefMandant)');
      query.Parameters.ParamByName('RefMandant').Value := RefMandant;
    end;

    //Die Standart-Categotry mit REF=0 immer als erstes
    query.SQL.Add ('order by case when REF<=0 then 0 else null end asc nulls last, REIHENFOLGE nulls last');

    try
      query.Open;

      while not (query.EOF) do begin
        ref := query.Fields [0].AsInteger;

        //Die Standard-Categotry ist immer -1
        if (ref = 0) or (query.Fields [2].AsString = '1') then
          ref := -1;

        ComboBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (ref));

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  if (RefSel = -1) then
    ComboBox.ItemIndex := 0
  else begin
    ComboBox.ItemIndex := FindComboboxRef (ComboBox, RefSel);
    if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadPlanungCombobox (ComboBox: TCustomComboBox; const ArtStr : String) : Integer;
var
  res     : Integer;
  query   : TADOQuery;
  namestr : String;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadPlanungCombobox');
    TraceParameter ('ComboBox', ComboBox.Name);
    TraceParameter ('ArtStr', ArtStr);
  {$ENDIF}

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (ComboBox.ItemIndex > 0) then
      namestr := ComboBox.Items [ComboBox.ItemIndex]
    else namestr := '';

    ClearComboBoxObjects (ComboBox);
    ComboBox.Items.Add (GetResourceText (1190));

    query.SQL.Clear;
    query.SQL.Add ('select REF, REF_LAGER, REF_LB, NAME, BESCHREIBUNG,');
    query.SQL.Add ('(select nvl (BESCHREIBUNG,VORGANG) from V_INF_WERTE where TABELLE=''VORPLANUNGEN'' and SPALTE=''VORGANG'' and WERT=VORGANG)');
    query.SQL.Add ('from V_VORPLANUNGEN where STATUS in (''ANG'',''AKT'') and ART='+#39+ArtStr+#39);
    if (LVSDatenModul.AktLagerRef <> -1) then
      query.SQL.Add ('and REF_LAGER='+IntToStr (LVSDatenModul.AktLagerRef))
    else
      query.SQL.Add ('and REF_LAGER in (select REF from V_PCD_LAGER where REF_LOCATION=' + IntToStr(LVSDatenModul.AktLocationRef) + ')');

    try
      query.Open;

      while not (query.EOF) do begin
        ComboBox.Items.AddObject (query.Fields [3].AsString+' ('+query.Fields [5].AsString+')|'+query.Fields [4].AsString, TComboboxPlanRef.Create (query.Fields [0].AsInteger, DBGetReferenz (query.Fields [1]), DBGetReferenz (query.Fields [2])));

        query.Next;
      end;
      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  ComboBox.ItemIndex := ComboBox.Items.IndexOf (namestr);
  if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadLTCombobox (ComboBox : TCustomComboBox; const Einsatz: string; const RefLocation: Integer; const RefLager: Integer) : Integer;
var
  res,
  reflt,
  selidx   : Integer;
  sqlstr,
  wherestr : string;
  query    : TBetterADODataSet;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadLTCombobox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('RefLager   ', RefLager);
  {$ENDIF}

  wherestr := 'REF_MAND is null';

  reflt := GetComboBoxRef (ComboBox);

  selidx := -1;

  ClearComboBoxObjects (ComboBox);

  if (RefLager <> -1) then begin
    wherestr := wherestr + ' and (REF_LAGER is null and REF_LOCATION=(select REF_LOCATION from V_LOCATION_REL_LAGER where REF_LAGER='+IntToStr (RefLager)+') or REF_LAGER='+IntToStr (RefLager)+')';
  end else if (RefLocation > 0) then begin
    wherestr := wherestr + ' and (REF_LAGER is null and REF_LOCATION='+IntToStr (RefLocation)+')';
  end else begin
    wherestr := wherestr + ' and (REF_LAGER is null and REF_LOCATION is null)';
  end;

  if (Length (Einsatz) > 0) then begin
    if (Pos (#39, Einsatz) > 0) then
      wherestr := wherestr + ' and EINSATZ in ('+Einsatz+')'
    else
      wherestr := wherestr + ' and EINSATZ='+#39+Einsatz+#39;
  end;

  query := TBetterADODataSet.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    sqlstr := 'select REF,REF_LAGER,LAGER,NAME,BESCHREIBUNG,LT_ID from V_LT_TYPEN where STATUS=''AKT'' and REF in (select distinct REF from V_LT_EINSATZ';
    if (Length (wherestr) > 0) then
      sqlstr := sqlstr + ' where ' + wherestr;

    sqlstr := sqlstr + ') order by ';

    if (Pos ('NVE', Einsatz) > 0) then
      sqlstr := sqlstr + 'case when DEFAULT_LT=''1'' then 1 else 9 end asc'
    else if (Pos ('KOMM', Einsatz) > 0) then
      sqlstr := sqlstr + 'case when DEFAULT_KOMM_LT=''1'' then 1 else 9 end asc'
    else if (Pos ('LE', Einsatz) > 0) then
      sqlstr := sqlstr + 'case when DEFAULT_EINLAGER_LT=''1'' then 1 else 9 end asc'
    else
      sqlstr := sqlstr + 'case when DEFAULT_LT=''1'' then 1 else 9 end asc';

    sqlstr := sqlstr + ',LAGER,REIHENFOLGE,NAME';

    query.CommandText := sqlstr;

    try
      query.Open;

      while not (query.EOF) do begin
        if (RefLager = -1) then
          ComboBox.Items.AddObject(query.Fields[2].AsString + '|' + query.Fields[3].AsString + '|' + query.Fields[4].AsString, TComboBoxLTData.Create (query.Fields[0].AsInteger, query.Fields[1].AsInteger, query.Fields[5].AsString))
        else begin
          ComboBox.Items.AddObject(query.Fields[3].AsString + '|' + query.Fields[4].AsString, TComboBoxLTData.Create(query.Fields[0].AsInteger, query.Fields[1].AsInteger, query.Fields[5].AsString));
        end;

        if (reflt <> -1) and (reflt = query.Fields[0].AsInteger) then
          selidx := ComboBox.Items.Count - 1;

        query.Next;
      end;
    except
      res := -9;
    end;

    query.Close;
  finally
    query.Free;
  end;

  ComboBox.ItemIndex := selidx;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadLBCombobox(ComboBox: TCustomComboBox; const Einsatz: string; const RefLager: Integer; const RefLocation: Integer; const RefMand : Integer) : Integer;
begin
  Result := LoadLBComboboxNullEntry (ComboBox, Einsatz, '', RefLager, RefLocation, RefMand);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  LoadLBComboboxNullEntry (ComboBox : TCustomComboBox; const Einsatz, NullEntry: String; const RefLager: Integer; const RefLocation : Integer; const RefMand : Integer) : Integer;
var
  res      : Integer;
  lbname,
  wherestr : string;
  query    : TBetterADODataSet;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadLBCombobox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('Einsatz    ', Einsatz);
    TraceParameter ('NullEntry  ', NullEntry);
    TraceParameter ('RefLager   ', RefLager);
    TraceParameter ('RefLocation', RefLocation);
    TraceParameter ('RefMand    ', RefMand);
  {$ENDIF}

  wherestr := '';

  if (ComboBox.ItemIndex = -1) then
    lbname := ''
  else if not (Assigned (ComboBox.Items.Objects [ComboBox.ItemIndex])) then
    lbname := ''
  else lbname := ComboBox.Items[ComboBox.ItemIndex];

  ClearComboBoxObjects (ComboBox);

  if (NullEntry = ' ') or (NullEntry = #0) then
    ComboBox.Items.Add ('')
  else if (Length (NullEntry) > 0) then
    ComboBox.Items.Add (NullEntry);

  if (RefLager <> -1) then
    wherestr := 'REF_LAGER='+IntToStr (RefLager)
  else if (RefLocation <> -1) then begin
    wherestr := 'REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION='+IntToStr (RefLocation)+')';

    if (RefMand <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF_LAGER in (select REF_LAGER from V_MANDANT_REL_LAGER where REF_MAND='+IntToStr (RefMand)+')'
    end;
  end;

  if (Length(Einsatz) > 0) then begin
    if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';

    if (Einsatz = '*K') then
      wherestr := wherestr + 'USE_FOR_KOMM=''1'''
    else if (Einsatz = '*V') then
      wherestr := wherestr + 'USE_FOR_VORRAT=''1'''
    else if (Einsatz [1] = '-') then
      wherestr := wherestr + 'LB_ART not in (' + Copy (Einsatz, 2, Length (Einsatz) - 1) + ')'
    else
      wherestr := wherestr + 'LB_ART in (' + Einsatz + ')'
  end;

  query := TBetterADODataSet.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.CommandText := 'select REF, REF_LAGER, LAGER, NAME, BESCHREIBUNG from V_PCD_LB';
    if (Length (wherestr) > 0) then
      query.CommandText := query.CommandText + ' where ' + wherestr;

    query.CommandText := query.CommandText + ' order by LAGER, lower (NAME)';

    try
      query.Open;

      while not (query.EOF) do begin
        if (RefLager = -1) then
          ComboBox.Items.AddObject(query.Fields[2].AsString + '|' + query.Fields[3].AsString + '|' + query.Fields[4].AsString, TComboBoxKommLBRef.Create (query.Fields[0].AsInteger, query.Fields[1].AsInteger))
        else begin
          ComboBox.Items.AddObject(query.Fields[3].AsString + '|' + query.Fields[4].AsString, TComboBoxKommLBRef.Create(query.Fields[0].AsInteger, query.Fields[1].AsInteger));
        end;

        query.Next;
      end;
    except
      res := -9;
    end;

    query.Close;
  finally
    query.Free;
  end;

  ComboBox.ItemIndex := ComboBox.Items.IndexOf (lbname);
  if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: LoadLPCombobox
//* Author       : Stefan Graf
//* Datum        : 17.04.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadLPCombobox(ComboBox: TCustomComboBox; const RefLB: Integer; const FreiFlag : Boolean; const RefAR, RefLP: Integer) : Integer;
var
  res,
  styp   : Integer;
  query  : TSmartQuery;
begin
  res := 0;

  if (UserReg.ReadRegValue ('SortLP', styp) <> 0) then
    styp := 1;

  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    if (styp = 2) then
      query.SQL.Add ('select REF,LP_DISP,nvl (LP_KOOR,LP_NR)')
    else if (styp = 1) then
      query.SQL.Add ('select REF,LP_KOOR,nvl (LP_NR,LP_DISP)')
    else
      query.SQL.Add ('select REF,LP_NR,nvl (LP_KOOR,LP_DISP)');

    query.SQL.Add ('from V_LP where REF_LB=:LBRef');
    query.Params.ParamByName('LBRef').Value := RefLB;

    if (FreiFlag) then begin
      query.SQL.Add ('and (STATUS=''FREI''');

      if (RefAR > 0) then begin
        query.SQL.Add ('or REF in (select bes.REF_LP_BES from V_BES_ARTIKEL bes, VQ_ARTIKEL_EINHEIT ae where ae.REF_AR=:ref_ar and bes.REF_AR=ae.REF_AR)');
        query.Params.ParamByName('ref_ar').Value := RefAR;
      end;

      query.SQL.Add (')');
    end;

    if (styp = 2) then
      query.SQL.Add ('order by LP_DISP, LP_KOOR, LP_NR')
    else if (styp = 1) then
      query.SQL.Add ('order by LP_KOOR, LP_NR')
    else
      query.SQL.Add ('order by LP_NR, LP_KOOR');

    Screen.Cursor := crSQLWait;

    ComboBox.Items.BeginUpdate;
    ClearComboBoxObjects (ComboBox);

    try
      try
        query.Open;

        while not (query.EOF) do begin
          ComboBox.Items.AddObject (query.Fields [1].AsString+'|('+query.Fields [2].AsString+')', TComboBoxRef.Create (query.Fields [0].AsInteger));

          query.Next;
        end;

        query.Close;
      except
        res := -9;
      end;
    finally
      ComboBox.Items.EndUpdate;

      Screen.Cursor := crDefault;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: LoadLPZoneCombobox
//* Author       : Stefan Graf
//* Datum        : 24.09.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadLPZoneCombobox (ComboBox: TCustomComboBox; const RefLBZone: Integer; const FreiFlag : Boolean; const RefAR, RefLP: Integer) : Integer;
var
  res,
  styp   : Integer;
  query  : TSmartQuery;
begin
  res := 0;

  if (UserReg.ReadRegValue ('SortLP', styp) <> 0) then
    styp := 1;

  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    if (styp = 2) then
      query.SQL.Add ('select REF,LP_DISP,nvl (LP_KOOR,LP_NR) from V_LP')
    else if (styp = 1) then
      query.SQL.Add ('select REF,LP_KOOR,nvl (LP_NR,LP_DISP) from V_LP')
    else
      query.SQL.Add ('select REF,LP_NR,nvl (LP_KOOR,LP_DISP) from V_LP');

    if (RefLBZone > 0) then
      //Nur die Plätze ohne Zone
      query.SQL.Add ('where REF_LB_ZONE is null')
    else begin
      //Nur die Plätze in der Zone
      query.SQL.Add ('where REF_LB_ZONE=:LBZoneRef');
      query.Params.ParamByName('LBZoneRef').Value := RefLBZone;
    end;

    if (FreiFlag) then begin
      query.SQL.Add ('and (STATUS=''FREI''');

      if (RefAR > 0) then begin
        query.SQL.Add ('or REF in (select bes.REF_LP_BES from V_BES_ARTIKEL bes, VQ_ARTIKEL_EINHEIT ae where ae.REF_AR=:ref_ar and bes.REF_AR=ae.REF_AR)');
        query.Params.ParamByName('ref_ar').Value := RefAR;
      end;

      query.SQL.Add (')');
    end;

    if (styp = 2) then
      query.SQL.Add ('order by LP_DISP, LP_KOOR, LP_NR')
    else if (styp = 1) then
      query.SQL.Add ('order by LP_KOOR, LP_NR')
    else
      query.SQL.Add ('order by LP_NR, LP_KOOR');

    Screen.Cursor := crSQLWait;

    ComboBox.Items.BeginUpdate;
    ClearComboBoxObjects (ComboBox);

    try
      try
        query.Open;

        while not (query.EOF) do begin
          ComboBox.Items.AddObject (query.Fields [1].AsString+'|('+query.Fields [2].AsString+')', TComboBoxRef.Create (query.Fields [0].AsInteger));

          query.Next;
        end;

        query.Close;
      except
        res := -9;
      end;
    finally
      ComboBox.Items.EndUpdate;

      Screen.Cursor := crDefault;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadSpedition (ComboBox : TCustomComboBox; const Einsatz, NullEntry : String; const RefLocation, RefLager, RefMand : Integer) : Integer;
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('SELECT * FROM V_SPEDITIONEN where STATUS=''ANG'' and ((REF_LAGER is not null and REF_LAGER=:ref_lager) or (REF_LAGER is null and (REF_LOCATION is null or REF_LOCATION=:ref_loc))) and (REF_MAND is null or REF_MAND=:ref_mand)');

    if (Einsatz = 'AVIS') then
      query.SQL.Add ('and AVIS_ART is not null');

    query.SQl.Add ('order by REF_MAND nulls last, upper (NAME)');
    query.Parameters.ParamByName ('ref_lager').Value := RefLager;
    query.Parameters.ParamByName ('ref_loc').Value   := RefLocation;
    query.Parameters.ParamByName ('ref_mand').Value  := RefMand;

    ClearComboBoxObjects (ComboBox);

    if (NullEntry = ' ') or (NullEntry = #0) then
      ComboBox.Items.Add ('')
    else if (Length (NullEntry) > 0) then
      ComboBox.Items.Add (NullEntry);

    try
      query.Open;

      while not (query.Eof) do begin
        ComboBox.Items.AddObject (query.FieldByName('NAME').AsString+'|'+query.FieldByName('BESCHREIBUNG').AsString, TComboBoxRef.Create (query.FieldByName('REF').AsInteger));

        query.Next;
      end;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadSpeditionAndProd (ComboBox : TCustomComboBox; const Einsatz, NullEntry : String; const RefLocation, RefLager, RefMand : Integer) : Integer;
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Clear;
    query.SQL.Add ('SELECT sped.*,prod.REF as REF_PRODUKTE, prod.NAME as PROD_NAME FROM V_SPEDITIONEN sped left outer join V_SPED_PRODUKTE prod on (prod.REF_SPED=sped.REF and prod.STATUS=''ANG'')'
                  +' where sped.STATUS=''ANG'' and ((sped.REF_LAGER is not null and sped.REF_LAGER=:ref_lager) or (sped.REF_LAGER is null and (sped.REF_LOCATION is null or sped.REF_LOCATION=:ref_loc))) and (sped.REF_MAND is null or sped.REF_MAND=:ref_mand)'
                  );

    if (Einsatz = 'AVIS') then
      query.SQL.Add ('and sped.AVIS_ART is not null');

    query.SQl.Add ('order by sped.REF_MAND nulls last, upper (sped.NAME), upper (prod.NAME) nulls first');
    query.Parameters.ParamByName ('ref_lager').Value := RefLager;
    query.Parameters.ParamByName ('ref_loc').Value   := RefLocation;
    query.Parameters.ParamByName ('ref_mand').Value  := RefMand;

    ClearComboBoxObjects (ComboBox);

    if (NullEntry = ' ') or (NullEntry = #0) then
      ComboBox.Items.Add ('')
    else if (Length (NullEntry) > 0) then
      ComboBox.Items.Add (NullEntry);

    try
      query.Open;

      while not (query.Eof) do begin
        if (query.FieldByName('REF_PRODUKTE').IsNull) then
          ComboBox.Items.AddObject (query.FieldByName('NAME').AsString+'|'+query.FieldByName('BESCHREIBUNG').AsString, TComboBoxSpedition.Create (query.FieldByName('REF').AsInteger, -1))
        else
          ComboBox.Items.AddObject (query.FieldByName('NAME').AsString+' ('+query.FieldByName('PROD_NAME').AsString+')|'+query.FieldByName('BESCHREIBUNG').AsString, TComboBoxSpedition.Create (query.FieldByName('REF').AsInteger, DBGetReferenz (query.FieldByName('REF_PRODUKTE'))));

        query.Next;
      end;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadLBPlusLBZoneCombobox (ComboBox : TCustomComboBox; const Einsatz, NullEntry : String; const RefLocation, RefLager, RefMand : Integer) : Integer;
var
  res      : Integer;
  lbstr,
  lbname   : string;
  query    : TBetterADODataSet;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadLBPlusLBZoneCombobox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('NullEntry  ', NullEntry);
  {$ENDIF}

  if (ComboBox.ItemIndex > 0) then
    lbname := ComboBox.Items[ComboBox.ItemIndex]
  else lbname := '';

  ClearComboBoxObjects (ComboBox);

  if (NullEntry = ' ') or (NullEntry = #0) then
    ComboBox.Items.Add ('')
  else if (Length (NullEntry) > 0) then
    ComboBox.Items.Add (NullEntry);

  query := TBetterADODataSet.Create (Nil);
  query.LockType := ltReadOnly;
  query.Connection := LVSDatenModul.MainADOConnection;

  try
    query.CommandText := 'select lb.LAGER,lb.REF,lb.NAME,z.REF,z.NAME,lb.REF_LAGER from V_LB lb left outer join V_LB_ZONE z on (z.REF_LB=lb.REF)';

    if (RefLager <> -1) then begin
      query.CommandText := query.CommandText + ' where lb.REF_LAGER=:ref_lager';
      query.Parameters.ParamByName('ref_lager').Value := RefLager;
    end else if (RefLocation <> -1) then begin
      query.CommandText := query.CommandText + ' where lb.REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc)';
      query.Parameters.ParamByName('ref_loc').Value := RefLocation;

      if (RefMand <> -1) then begin
        query.CommandText := query.CommandText + ' and lb.REF_LAGER in (select REF_LAGER from V_MANDANT_REL_LAGER where REF_MAND=:ref_mand)';
        query.Parameters.ParamByName('ref_mand').Value := RefMand;
      end;
    end;

    if (Length(Einsatz) > 0) then begin
      if (Einsatz = '*K') then
        query.CommandText := query.CommandText + ' and lb.USE_FOR_KOMM=''1'''
      else if (Einsatz = '*V') then
        query.CommandText := query.CommandText + ' and lb.USE_FOR_VORRAT=''1'''
      else if (Einsatz [1] = '-') then
        query.CommandText := query.CommandText + ' and lb.LB_ART not in (' + Copy (Einsatz, 2, Length (Einsatz) - 1) + ')'
      else
        query.CommandText := query.CommandText + ' and lb.LB_ART in (' + Einsatz + ')'
    end;

    query.CommandText := query.CommandText + ' order by lb.LAGER, lower (lb.NAME), lower (z.NAME)';

    try
      query.Open;

      while not (query.EOF) do begin
        if (RefLager > 0) then
          lbstr := query.Fields[2].AsString
        else
          lbstr := query.Fields[0].AsString+'|'+query.Fields[2].AsString;

        if (not (query.Fields[3].IsNull) and (FindComboboxRef (ComboBox, query.Fields[1].AsInteger) = -1)) then
          ComboBox.Items.AddObject(lbstr, TComboBoxLBPlusZoneRef.Create(query.Fields[1].AsInteger, query.Fields[5].AsInteger, -1));

        if (query.Fields[3].IsNull) then
          ComboBox.Items.AddObject(lbstr, TComboBoxLBPlusZoneRef.Create(query.Fields[1].AsInteger, query.Fields[5].AsInteger, -1))
        else
          ComboBox.Items.AddObject(lbstr + '| Zone:' + query.Fields[4].AsString, TComboBoxLBPlusZoneRef.Create(query.Fields[1].AsInteger, query.Fields[5].AsInteger, DBGetReferenz (query.Fields[3])));

        query.Next;
      end;
    except
      res := -9;
    end;

    query.Close;
  finally
    query.Free;
  end;

  ComboBox.ItemIndex := ComboBox.Items.IndexOf (lbname);
  if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadLBZoneCombobox (ComboBox : TCustomComboBox; const RefLB: Integer) : Integer;
var
  res      : Integer;
  lbname,
  wherestr : string;
  query    : TBetterADODataSet;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadLBZoneCombobox');
    TraceParameter ('ComboBox   ', ComboBox.Name);
    TraceParameter ('RefLB      ', RefLB);
  {$ENDIF}

  wherestr := '';

  if (ComboBox.ItemIndex > 0) then
    lbname := ComboBox.Items[ComboBox.ItemIndex]
  else lbname := '';

  ClearComboBoxObjects (ComboBox);

  query := TBetterADODataSet.Create (Nil);
  query.LockType := ltReadOnly;
  query.Connection := LVSDatenModul.MainADOConnection;

  try
    query.CommandText := 'select REF, NAME, BESCHREIBUNG from V_LB_ZONE where REF_LB=:RefLB order by NAME';
    query.Parameters.ParamByName('RefLB').Value := RefLB;

    try
      query.Open;

      while not (query.EOF) do begin
        ComboBox.Items.AddObject(query.Fields[1].AsString + '|' + query.Fields[2].AsString, TComboBoxRef.Create(query.Fields[0].AsInteger));

        query.Next;
      end;
    except
      res := -9;
    end;

    query.Close;
  finally
    query.Free;
  end;

  ComboBox.ItemIndex := ComboBox.Items.IndexOf (lbname);
  if (ComboBox.ItemIndex = -1) then ComboBox.ItemIndex := 0;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Sebastian Schütte
//* Datum        : 01.05.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetLSMaxFutureDays (const pRefMand: Integer;var Tage : Integer; var errormsg: string) : Integer;
var
  res,
  vperef : Integer;
  query  : TSmartQuery;
begin
  res := 0;

  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    query.SQL.Add('select m.*, cfg.* from V_MANDANT m, V_MANDANT_CONFIG cfg where cfg.REF_MAND=m.REF and m.REF=:ref');

    query.Params.ParamByName('ref').Value := pRefMand;

    try
      query.Open;

      if (Assigned(query.FindField('OPT_LS_DATUM_MAX_TAGE'))) then begin
        Tage := query.FieldByName ('OPT_LS_DATUM_MAX_TAGE').AsInteger;
      end else begin
        Tage := 3
      end;


      query.Close;
    except
      on E: Exception do begin
        res := -9;
        errormsg := E.Message;
        LVSDatenModul.TraceSql ('ERROR: '+ E.Message);
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 12.05.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetOpt (const OptStr : String; const Index : Integer) : Char;
begin
  if (Length (OptStr) >= Index) then
    Result := OptStr [Index]
  else Result := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.09.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetOpt (const FirstOptStr, SecondOptStr : String; const Index : Integer) : Char; overload;
begin
  if (Length (FirstOptStr) < Index) then
    Result := #0
  else begin
    if not ((FirstOptStr [Index] = #0) or (FirstOptStr [Index] = ' ')) then
      Result := FirstOptStr [Index]
    else if (Length (SecondOptStr) < Index) then
      Result := #0
    else
      Result := SecondOptStr [Index];
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckOpt (const OptStr : String; const Index : Integer) : Boolean;
begin
  if (Length (OptStr) >= Index) then
    Result := OptStr [Index] = '1'
  else Result := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  CheckOpt (const FirstOptStr, SecondOptStr : String; const Index : Integer) : Boolean; overload;
begin
  if (Length (FirstOptStr) >= Index) and (FirstOptStr [Index] <> ' ') then
    Result := FirstOptStr [Index] = '1'
  else if (Length (SecondOptStr) >= Index) then
    Result := SecondOptStr [Index] = '1'
  else Result := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckOptState (const OptStr : String; const Index : Integer) : TCheckBoxState;
begin
  if (Length (OptStr) < Index) then
    Result := cbGrayed
  else if OptStr [Index] = ' ' then
    Result := cbGrayed
  else if OptStr [Index] = '0' then
    Result := cbUnchecked
  else
    Result := cbChecked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetOpt (const OptStr : String; const Index : Integer) : String;
var
  outstr : String;
begin
  outstr := OptStr;
  while (Length (outstr) < Index) do outstr := outstr + '0';
  outstr [Index] := '1';

  Result := outstr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.04.2014
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  SetOpt (const OptStr : String; const Index : Integer; const OptChar : Char) : String; overload;
var
  outstr : String;
begin
  outstr := OptStr;
  while (Length (outstr) < Index) do outstr := outstr + '0';
  outstr [Index] := OptChar;

  Result := outstr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function  SetOpt(const OptStr : String; const Index : Integer; const State : TCheckBoxState) : String; overload;
var
  outstr : String;
begin
  outstr := OptStr;
  while (Length (outstr) < Index) do outstr := outstr + ' ';

  if (State = cbGrayed) then
    outstr [Index] := ' '
  else if (State = cbUnchecked) then
    outstr [Index] := '0'
  else
    outstr [Index] := '1';

  Result := outstr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ResetOpt (const OptStr : String; const Index : Integer) : String;
var
  outstr : String;
begin
  outstr := OptStr;
  while (Length (outstr) < Index) do outstr := outstr + '0';
  outstr [Index] := '0';

  Result := outstr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function MinRestlaufzeit (const MinimalZeit, Restlaufzeit  : Integer) : Integer;
begin
  if (MinimalZeit > Restlaufzeit) then
    Result := MinimalZeit
  else Result :=  Restlaufzeit;
end;

//******************************************************************************
//* Function Name: DBGetIntegerNull
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert bei NULL -1, ansosnten den Integerwert
//******************************************************************************
//* Return Value :
//******************************************************************************
function DBGetIntegerNull (Field : TField) : Integer;
begin
  if (Field.IsNull) then
    Result := -1
  else
    Result := Field.AsInteger;
end;

//******************************************************************************
//* Function Name: DBGetDoubleNull
//* Author       : Stefan Graf
//* Datum        : 08.01.2024
//******************************************************************************
//* Description    Liefert bei NULL -1, ansosnten den Integerwert
//******************************************************************************
//* Return Value :
//******************************************************************************
function DBGetDoubleNull (Field : TField) : Double;
begin
  if (Field.IsNull) then
    Result := -1
  else
    Result := Field.AsFloat;
end;

//******************************************************************************
//* Function Name: DBGetReferenz
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert bei NULL -1, ansosnten den Integerwert
//******************************************************************************
//* Return Value :
//******************************************************************************
function DBGetReferenz (Field : TField) : Integer;
begin
  if (Field.IsNull) then
    Result := -1
  else
    Result := Field.AsInteger;
end;

//******************************************************************************
//* Function Name: GetMandantConfigOpt
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert bei NULL -1, ansonsten den Integerwert
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetMandantConfigOpt (const RefMand : Integer) : String;
var
  query : TADOQuery;
begin
  if (RefMand <= 0) then
    Result := ''
  else begin
    query := TADOQuery.Create (Nil);
    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQl.Add ('select REF,CONFIG_OPT from V_MANDANT where REF=:RefMand');
      query.Parameters.ParamByName('RefMand').Value := RefMand;

      query.Open;

      Result := query.Fields [1].AsString;

      query.Close;
    finally
      query.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name: GetWhereMandLager
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert die where-Bedingung für Mandand und Lager
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetWhereMandLager : String;
var
  wherestr : String;
begin
  wherestr := '';

  if (LVSDatenModul.AktMandantRef <> -1) then
    wherestr := wherestr + 'REF_MAND='+IntToStr (LVSDatenModul.AktMandantRef);

  if (LVSDatenModul.AktLagerRef <> -1) then begin
    if (Length(wherestr) > 0) then wherestr := wherestr + ' and ';
    wherestr := wherestr + 'REF_LAGER='+IntToStr (LVSDatenModul.AktLagerRef)
  end else if (LVSDatenModul.AktLocationRef <> -1) then begin
    if (Length(wherestr) > 0) then wherestr := wherestr + ' and ';
    wherestr := wherestr + 'REF_LAGER in (select REF from V_PCD_LAGER where REF_LOCATION=' + IntToStr(LVSDatenModul.AktLocationRef) + ')';
  end;

  Result := wherestr;
end;


//******************************************************************************
//* Function Name: GetWhereMand
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Liefert die where-Bedingung für Mandand
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetWhereMand : String;
var
  wherestr : String;
begin
  wherestr := '';

  if (LVSDatenModul.AktMandantRef <> -1) then
    wherestr := wherestr + 'REF_MAND='+IntToStr (LVSDatenModul.AktMandantRef);

  Result := wherestr;
end;

//******************************************************************************
//* Function Name: DetectArtikelBarcode
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DetectArtikelBarcode (const RefMand : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var errmsg : String) : Integer;
var
  barcode : TEANBarcode;
  barinfo : TBarcodeInfos;
begin
  Result := DetectArtikelBarcode (RefMand, ARRef, AREinheitRef, ArtikelNr, Einheit, barcode, barinfo, errmsg);
end;

//******************************************************************************
//* Function Name: DetectArtikelBarcode
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DetectAuftragArtikelBarcode (const RefAuf : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var errmsg : String) : Integer;
var
  barcode : TEANBarcode;
  barinfo : TBarcodeInfos;
begin
  Result := DetectAuftragArtikelBarcode (RefAuf, ARRef, AREinheitRef, ArtikelNr, Einheit, barcode, barinfo, errmsg);
end;

//******************************************************************************
//* Function Name: DetectArtikelBarcode
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DetectArtikelBarcode (const RefMand : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var EANBarcode : TEANBarcode; var errmsg : String) : Integer;
var
  barinfo : TBarcodeInfos;
begin
  Result := DetectArtikelBarcode (RefMand, ARRef, AREinheitRef, ArtikelNr, Einheit, EANBarcode, barinfo, errmsg);
end;

//******************************************************************************
//* Function Name: DetectArtikelBarcode
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DetectArtikelBarcode (const RefMand : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var EANBarcode : TEANBarcode; var BarcodeInfos: TBarcodeInfos; var errmsg : String) : Integer;
var
  res,
  idx,
  ridx,
  selidx,
  bestidx,
  posidx,
  errcode    : Integer;
  ean_nr,
  bar_nr,
  ar_nr,
  ean128code : String;
  ch         : Char;
  found,
  doneflag   : Boolean;
  nveinfo    : TNVEInfo;
  arinfo     : TArtikelInfo;
begin
  res := 0;

  arinfo := TArtikelInfo.Create;

  try
    FillChar (EANBarcode, sizeof (TEANBarcode), 0);
    FillChar (BarcodeInfos, sizeof (TBarcodeInfos), 0);

    EANBarcode.NVE := '';
    arinfo.ArtikelNr := '';

    ar_nr  := '';
    ean_nr := '';
    bar_nr := '';
    errmsg := '';

    DecodeKundenBarcode (ScanCode, doneflag, BarcodeInfos, errcode, errmsg);

    if doneflag then begin
      if (Length (BarcodeInfos.EAN) > 0) then
        ean_nr := BarcodeInfos.EAN
      else if (Length (BarcodeInfos.ArtikelNr) > 0) then
        ar_nr := BarcodeInfos.ArtikelNr;
    end else begin
      if (ScanCode [1] = EAN13ID) then begin
        if (Length (ScanCode) = 14) then begin
          if (Copy (ScanCode, 2, 2) = '28') then begin
            arinfo.ArtikelNr := Copy (ScanCode, 4, 5);

            //Führende Nullen entfernen
            while (Length (arinfo.ArtikelNr) > 0) and (arinfo.ArtikelNr [1] = '0') do
              Delete (arinfo.ArtikelNr,1,1);
          end else begin
            ean_nr := Copy (ScanCode, 2, 13);
            EANBarcode.EAN := ean_nr;
          end;
        end else begin
          ean_nr := Copy (ScanCode, 2, Length (ScanCode) - 1);

          if (Length (ean_nr) < 8) then
            errmsg := FormatMessageText (1046, [])
          else begin
            ch := GetEANCheckDigit (Copy (ean_nr, 1, Length (ean_nr) - 1));

            if (ch <> ean_nr [Length (ean_nr)]) then
              errmsg := FormatMessageText (1036, [])
            else
              EANBarcode.EAN := ean_nr;
          end;
        end;
      end else if (ScannerTyp in [0,2,4]) and (Length (ScanCode) = 9) and (ScanCode [1] = EAN8ID) then begin
        ean_nr := Copy (ScanCode, 2, 8);
        EANBarcode.EAN := ean_nr;
      end else if (ScannerTyp in [1,3]) and (Length (ScanCode) = 10) and (ScanCode [1] = EAN8ID) and (ScanCode [2] = 'F') then begin
        ean_nr := Copy (ScanCode, 3, 8);
        EANBarcode.EAN := ean_nr;
      end else if (Length (ScanCode) > 3) and (ScanCode [1] = EAN128ID) then begin
        ean_nr := '';

        ean128code := Copy (ScanCode, 2, Length (ScanCode) - 1);

        if (Length (ean128code) > 10) and (Length (ean128code) < 15) then begin
          ch := GetEANCheckDigit (Copy (ean128code, 1, Length (ean128code) - 1));

          if (ch = ean128code [Length (ean128code)]) then
            ean_nr := ean128code;
        end;

        if (Length (ean_nr) = 0) then begin
          res := DecodeEANBarcode (ean128code, EANBarcode, errmsg);

          if (res <> 0) then
            errmsg := FormatMessageText (1037, [])
          else begin
            if (Length (EANBarcode.EAN) > 0) then
              ean_nr := EANBarcode.EAN
            else if (Length (EANBarcode.InhaltEAN) > 0) then
              ean_nr := EANBarcode.InhaltEAN
            else
              errmsg := FormatMessageText (1038, []);

            if (Length (ean_nr) = 14) and (ean_nr [1] = '9') then begin
              ean_nr := Copy (ean_nr,2,12)+GetEANCheckDigit (Copy (ean_nr,2,12));
            end;
          end;
        end;
      end else if (Length(ScanCode) > 3) and (ScanCode[1] = Code128ID) and (ScanCode [2] = '0') and (ScanCode [3] = '4') then begin
        //Prüfen, ob mit Barcode-Prüfziffer oder ohne
        BarcodeInfos.BestandID := copy (ScanCode, 4, Length (ScanCode) - 4);
      end else begin
        if ((Length (ScanCode) - 1) > MaxBarcodeSize) then
          errmsg := FormatMessageText (1039, [])
        else begin
          bar_nr := Copy (ScanCode, 2, Length (ScanCode) - 1);
        end;
      end;
    end;

    if (Length (errmsg) = 0) then begin
      if (Length (ar_nr) > 0) then begin
        res := GetArtikelNummerInfos (RefMand, ar_nr, arinfo);

        if (res < 0) then
          errmsg := FormatMessageText (1040, [])
        else if (res > 0) then
          errmsg := FormatMessageText (1132, [ar_nr])
        else if (Length (arinfo.ArtikelNr) = 0) then begin
          res := 3;
          errmsg := FormatMessageText (1132, [ar_nr]);
        end;
      end else if (Length (ean_nr) > 0) then begin
        while (Length (ean_nr) > 0) and (ean_nr [1] = '0') do Delete (ean_nr,1,1);

        res := GetArtikelEANInfos (RefMand, ean_nr, arinfo);

        if (res < 0) then
          errmsg := FormatMessageText (1040, [])
        else if (res > 0) then
          errmsg := FormatMessageText (1041, [ean_nr])
        else if (Length (arinfo.ArtikelNr) = 0) then begin
          res := 3;
          errmsg := FormatMessageText (1041, [ean_nr]);
        end;
      end else if (Length (bar_nr) > 0) then begin
        while (Length (bar_nr) > 0) and (bar_nr [1] = '0') do Delete (bar_nr,1,1);

        res := GetArtikelBarcodeInfos (RefMand, bar_nr, arinfo);

        if (res <> 0) then
          res := GetArtikelEANInfos (RefMand, bar_nr, arinfo);

        if (res < 0) then
          errmsg := FormatMessageText (1040, [])
        else if (res > 0) then
          errmsg := FormatMessageText (1042, [bar_nr])
        else if (Length (arinfo.ArtikelNr) = 0) then begin
          res := 3;
          errmsg := FormatMessageText (1042, [bar_nr]);
        end;
      end;

      ARRef        := arinfo.RefArtikel;
      AREinheitRef := arinfo.RefArtikelEinheit;
      ArtikelNr    := arinfo.ArtikelNr;
      Einheit      := arinfo.Einheit;
    end;
  finally
    arinfo.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: DetectArtikelBarcode
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DetectAuftragArtikelBarcode (const RefAuf : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var EANBarcode : TEANBarcode; var errmsg : String) : Integer;
var
  barinfo : TBarcodeInfos;
begin
  Result := DetectAuftragArtikelBarcode (RefAuf, ARRef, AREinheitRef, ArtikelNr, Einheit, EANBarcode, barinfo, errmsg);
end;

//******************************************************************************
//* Function Name: DetectArtikelBarcode
//* Author       : Stefan Graf
//* Datum        : 21.10.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DetectAuftragArtikelBarcode (const RefAuf : Integer; var ARRef, AREinheitRef : Integer; var ArtikelNr, Einheit : String; var EANBarcode : TEANBarcode; var BarcodeInfos : TBarcodeInfos; var errmsg : String) : Integer; overload;
var
  res,
  idx,
  ridx,
  selidx,
  bestidx,
  posidx,
  errcode    : Integer;
  ean_nr,
  bar_nr,
  ean128code : String;
  ch         : Char;
  found,
  doneflag   : Boolean;
  nveinfo    : TNVEInfo;
  arinfo     : TArtikelInfo;
  barinfo    : TBarcodeInfos;
begin
  res := 0;

  arinfo := TArtikelInfo.Create;

  try
    FillChar (EANBarcode, sizeof (TEANBarcode), 0);
    FillChar (BarcodeInfos, sizeof (TBarcodeInfos), 0);

    EANBarcode.NVE := '';
    arinfo.ArtikelNr := '';

    ean_nr := '';
    bar_nr := '';
    errmsg := '';

    DecodeKundenBarcode (ScanCode, doneflag, barinfo, errcode, errmsg);

    if doneflag then begin
      ean_nr := barinfo.EAN;
    end else if (Length (ScanCode) > 2) then begin
      if (ScanCode [1] = EAN13ID) then begin
        if (Length (ScanCode) = 14) then begin
          if (Copy (ScanCode, 2, 2) = '28') then begin
            arinfo.ArtikelNr := Copy (ScanCode, 4, 5);

            //Führende Nullen entfernen
            while (Length (arinfo.ArtikelNr) > 0) and (arinfo.ArtikelNr [1] = '0') do
              Delete (arinfo.ArtikelNr,1,1);
          end else begin
            ean_nr := Copy (ScanCode, 2, 13);
          end;
        end else begin
          ean_nr := Copy (ScanCode, 2, Length (ScanCode) - 1);

          if (Length (ean_nr) < 8) then
            errmsg := FormatMessageText (1046, [])
          else begin
            ch := GetEANCheckDigit (Copy (ean_nr, 1, Length (ean_nr) - 1));

            if (ch <> ean_nr [Length (ean_nr)]) then
              errmsg := FormatMessageText (1036, []);
          end;
        end;
      end else if (ScannerTyp in [0,2,4]) and (Length (ScanCode) = 9) and (ScanCode [1] = EAN8ID) then begin
        ean_nr := Copy (ScanCode, 2, 8);
      end else if (ScannerTyp in [1,3]) and (Length (ScanCode) = 10) and (ScanCode [1] = EAN8ID) and (ScanCode [2] = 'F') then begin
        ean_nr := Copy (ScanCode, 3, 8);
      end else if (Length (ScanCode) > 3) and (ScanCode [1] = EAN128ID) then begin
        ean_nr := '';

        ean128code := Copy (ScanCode, 2, Length (ScanCode) - 1);

        if (Length (ean128code) > 10) and (Length (ean128code) < 15) then begin
          ch := GetEANCheckDigit (Copy (ean128code, 1, Length (ean128code) - 1));

          if (ch = ean128code [Length (ean128code)]) then
            ean_nr := ean128code;
        end;

        if (Length (ean_nr) = 0) then begin
          res := DecodeEANBarcode (ean128code, EANBarcode, errmsg);


          if (res <> 0) then
            errmsg := FormatMessageText (1037, [])
          else begin
            if (Length (EANBarcode.EAN) > 0) then
              ean_nr := EANBarcode.EAN
            else if (Length (EANBarcode.InhaltEAN) > 0) then
              ean_nr := EANBarcode.InhaltEAN;

            if (Length (ean_nr) > 0) then begin
              if (Length (ean_nr) = 14) and (ean_nr [1] = '9') then begin
                ean_nr := Copy (ean_nr,2,12)+GetEANCheckDigit (Copy (ean_nr,2,12));
              end;
            end;
          end;
        end;
      end else if (Length(ScanCode) > 3) and (ScanCode[1] = Code128ID) and (ScanCode [2] = '0') and (ScanCode [3] = '4') and ((ScanCode [Length(ScanCode)] = GetLELPCheckChar (copy (ScanCode, 2, Length(ScanCode) - 2)))) then begin
        //Prüfen, ob mit Barcode-Prüfziffer oder ohne
        BarcodeInfos.BestandID := copy (ScanCode, 4, Length (ScanCode) - 4);
      end else begin
        if ((Length (ScanCode) - 1) > MaxBarcodeSize) then
          errmsg := FormatMessageText (1039, [])
        else begin
          bar_nr := Copy (ScanCode, 2, Length (ScanCode) - 1);
        end;
      end;
    end;

    if (Length (errmsg) = 0) then begin
      if (Length (ean_nr) > 0) then begin
        while (Length (ean_nr) > 0) and (ean_nr [1] = '0') do Delete (ean_nr,1,1);

        res := GetAuftragArtikelEANInfos (RefAuf, ean_nr, arinfo);

        //Wenn kein passender Artikel gefunden wurde
        if (res = 2) then
          res := GetAuftragArtikelEANInfos (-1, ean_nr, arinfo);

        if (res < 0) then
          errmsg := FormatMessageText (1040, [])
        else if (res > 0) then
          errmsg := FormatMessageText (1041, [ean_nr])
        else if (Length (arinfo.ArtikelNr) = 0) then begin
          res := 3;
          errmsg := FormatMessageText (1041, [ean_nr]);
        end;
      end else if (Length (bar_nr) > 0) then begin
        while (Length (bar_nr) > 0) and (bar_nr [1] = '0') do Delete (bar_nr,1,1);

        res := GetAuftragArtikelBarcodeInfos (RefAuf, bar_nr, arinfo);

        if (res <> 0) then begin
          res := GetAuftragArtikelEANInfos (RefAuf, bar_nr, arinfo);

          if (res <> 0) then
            res := GetAuftragArtikelEANInfos (-1, bar_nr, arinfo);
        end;

        if (res < 0) then
          errmsg := FormatMessageText (1040, [])
        else if (res > 0) then
          errmsg := FormatMessageText (1042, [bar_nr])
        else if (Length (arinfo.ArtikelNr) = 0) then begin
          res := 3;
          errmsg := FormatMessageText (1042, [bar_nr]);
        end;
      end;

      ARRef        := arinfo.RefArtikel;
      AREinheitRef := arinfo.RefArtikelEinheit;
      ArtikelNr    := arinfo.ArtikelNr;
      Einheit      := arinfo.Einheit;
    end;
  finally
    arinfo.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: DetectLTTypeEAN
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DetectLTTypeEAN (const EAN : String; var RefLT : Integer; var LTName : String) : integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  res := 0;

  RefLT  := -1;
  LTName := '';

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add('select REF,NAME from (select REF,NAME from V_LT_TYPEN where STATUS=''AKT'' and REF_LOCATION=:ref_loc and TRIM(leading ''0'' from EAN)=TRIM(leading ''0'' from :ean)) where ROWNUM=1');
    query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
    query.Parameters.ParamByName('ean').Value := EAN;

    query.Open;

    if not (query.Fields [0].IsNull) then begin
      RefLT := query.Fields [0].AsInteger;
      LTName := query.Fields [1].AsString;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: CheckLEBarcode
//* Author       : Stefan Graf
//* Datum        : 07.01.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckLEBarcode (const ScanCode : AnsiString; var LENr : String) : boolean;
var
  ok     : Boolean;
  barstr : AnsiString;
  len,
  idx     : Integer;
begin
  LENr := '';
  Result := False;

  //LE Nummer müssen min. 3 Stellen haben
  if (Length (ScanCode) > 4) then begin
    if (ScanCode[1] <> ITFID) then
      barstr := ScanCode
    else
      barstr := Copy (ScanCode, 1, Length (ScanCode) - 1); //ITF Prüfziffer entfernen

    len := Length(barstr);

    //Erkennne von LE-Nr Barcodes mit ITF Prüfziffer oder Code128
    if ((((len = 12) and (barstr[1] = ITFID)) or ((len = 12) and (barstr[1] = Code128ID))) and (barstr[2] = '1') and (barstr [len] = GetLELPCheckChar (copy (barstr, 2, len - 2)))) then begin
      ok  := true;
      idx := 2;
      while (idx <= Length (ScanCode)) do begin
        if not (ScanCode [idx] in ['A'..'Z', '0'..'9']) then begin
          ok := false;
          break;
        end;

        Inc (idx);
      end;

      if (ok) then begin
        Result := True;

        LENr := Copy (barstr, 3 , len - 3);

        while (Length (LENr) > 0) and (LENr [1] = '0') do Delete (LENr, 1, 1);
      end;
    end else if (LVSConfigModul.KundenID = 2084) and ((ScanCode[1] = Code128ID) and (Length (ScanCode) = 6) and (ScanCode[2] = 'V') and (ScanCode[3] in ['0'..'5'])) then begin
      Result := True;                                //Zen, die VaryTotes 5stellige Nummer, im Bereich von V0001 bis V5000
      LENr   := Copy (ScanCode, 2);
    end else if (LVSConfigModul.KundenID = 2128) and ((ScanCode[1] = Code128ID) and (Length (ScanCode) = 5)) then begin
      Result := True;                                //Transco, die Autostore Verteilbehälter
      LENr   := Copy (ScanCode, 2);
    end else if (LVSConfigModul.KundenID = 2109) and ((ScanCode[1] = Code128ID) and (Length (ScanCode) = 11) and (copy (ScanCode, 2, 4) = '0100')) then begin
      Result := True;                                //Kaufland Bönen, die Totes
      LENr   := Copy (ScanCode, 2);
    end;
  end;
end;

//******************************************************************************
//* Function Name: CheckLPNrBarcode
//* Author       : Stefan Graf
//* Datum        : 07.01.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CheckLPNrBarcode (const ScanCode : AnsiString; var LPNr : String) : boolean;
begin
  LPNr := '';
  Result := False;

  if (((Length(ScanCode) = 11) and (ScanCode[1] = ITFID)) or ((Length(ScanCode) = 10) and (ScanCode[1] = Code128ID))) and (ScanCode[2] = '2') then begin
    //Normal mit 7 Ziffern
    Result := True;

    LPNr := Copy (ScanCode, 3, 7);
    while (Length (LPNr) > 0) and (LPNr [1] = '0') do Delete (LPNr, 1, 1);
  end else if (((Length(ScanCode) = 13) and (ScanCode[1] = ITFID)) or ((Length(ScanCode) = 12) and (ScanCode[1] = Code128ID))) and (ScanCode[2] = '2') then begin
    //Nur wenn mal mehr als 7 Ziffern benötigt würden
    Result := True;

    LPNr := Copy (ScanCode, 3, 9);
    while (Length (LPNr) > 0) and (LPNr [1] = '0') do Delete (LPNr, 1, 1);
  end else if (Length(ScanCode) = 11) and (ScanCode[1] = ITFID) and (ScanCode[2] = '0') and (ScanCode[3] = '2') then begin
    //Das entsteht, wenn die ITF Prüfziffer nicht mit ausgegeben wird
    Result := True;

    LPNr := Copy (ScanCode, 4, 7);
    while (Length (LPNr) > 0) and (LPNr [1] = '0') do Delete (LPNr, 1, 1);
  end else if (Length(ScanCode) = 12) and (ScanCode[1] = Code128ID) and (ScanCode[2] = '2') then begin
    Result := True;

    LPNr := Copy (ScanCode, 3, Length (ScanCode) - 3);
    while (Length (LPNr) > 0) and (LPNr [1] = '0') do Delete (LPNr, 1, 1);
  end;
end;

//******************************************************************************
//* Function Name: DetectLTTypeID
//* Author       : Stefan Graf
//* Datum        : 20.09.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DetectLTTypeID (const ID : String; var RefLT : Integer; var LTName : String) : integer;
var
  res   : Integer;
  query : TADOQuery;
begin
  res := 0;

  RefLT  := -1;
  LTName := '';

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add('select REF,NAME from (select REF,NAME from V_LT_TYPEN where STATUS=''AKT'' and REF_LOCATION=:ref_loc and LT_ID=:id) where ROWNUM=1');
    query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
    query.Parameters.ParamByName('id').Value := ID;

    query.Open;

    if not (query.Fields [0].IsNull) then begin
      RefLT  := query.Fields [0].AsInteger;
      LTName := query.Fields [1].AsString;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: IsArbeitstag
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function IsArbeitstag (const Location : Integer; const Datum : TDateTime) : Boolean;
const
  OsternTage : array [0..10] of integer = (94, 114, 99, 90, 110, 94, 86, 105, 90, 110, 102);

var
  y, m, d, wt, oyd, yd : Word;
begin
  DecodeDate (Datum, y, m, d);

  wt := DayOfTheWeek (Datum);

  if (wt = 6) or (wt = 7) then
    Result := False
  else if ((m=12) and ((d = 25) or (d=26))) then
    Result := False
  else if ((m=5) and (d=1)) then
    Result := False
  else if ((m=10) and (d=3)) then
    Result := False
  else if ((Location in [56,72,76]) and (m=10) and (d=31)) then
    Result := False
  else if ((Location in [75]) and (m=11) and (d=1)) then
    Result := False
  else if ((y >= 2010) and (y <= (2010 + High (OsternTage))) and (m >= 3) and (d >= 22) and (m <= 4) and (d <= 25)) then begin //Ostern bestimmen
    oyd := OsternTage [y -2010];

    if (oyd > 0) then begin
      DecodeDateDay (Datum, y, yd);

      if (yd = (oyd - 2)) then       //Karfreitag
        Result := False
      else if (yd = (oyd + 1)) then  //Oster Montag
        Result := False
      else if (yd = (oyd + 39)) then  //Christi Himmelfahrt
        Result := False
      else if (yd = (oyd + 50)) then  //Pfingstmontag
        Result := False
      else if (yd = (oyd + 60)) then  //Fronleichnam
        Result := False
      else
        Result := True;
    end else
      Result := True;
  end else
    Result := True;
end;

//******************************************************************************
//* Function Name: ClearNonEUList
//* Author       : Stefan Graf
//* Datum        : 21.10.2022
//******************************************************************************
//* Description  :  Prüfen ob das Land zur EU geh�rt
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ClearNonEUList;
begin
  NonEUList.Free;
  NonEUList := nil;
end;

//******************************************************************************
//* Function Name: IsLandEU
//* Author       : Stefan Graf
//* Datum        : 15.10.2014
//******************************************************************************
//* Description  :  Prüfen ob das Land zur EU geh�rt
//******************************************************************************
//* Return Value :
//******************************************************************************
function IsLandEU (const ISOLand : String; const ZIPCode : String) : Boolean;
var
  res     : Boolean;
  idx     : Integer;
  query   : TADOQuery;
  csvlist : TStringList;
begin
  if (Length (ISOLand) = 0) then
    res := true
  else if (ISOLand = 'BE') then
    res := true
  else if (ISOLand = 'BG') then
    res := true
  else if (ISOLand = 'CZ') then
    res := true
  else if (ISOLand = 'DK') then
    res := true
  else if (ISOLand = 'DE') then
    res := true
  else if (ISOLand = 'EE') then
    res := true
  else if (ISOLand = 'IE') then
    res := true
  else if (ISOLand = 'EL') then
    res := true
  else if (ISOLand = 'ES') then
    res := true
  else if (ISOLand = 'FR') then
    res := true
  else if (ISOLand = 'HR') then
    res := true
  else if (ISOLand = 'IT') then
    res := true
  else if (ISOLand = 'CY') then
    res := true
  else if (ISOLand = 'LV') then
    res := true
  else if (ISOLand = 'LT') then
    res := true
  else if (ISOLand = 'LU') then
    res := true
  else if (ISOLand = 'HU') then
    res := true
  else if (ISOLand = 'MT') then
    res := true
  else if (ISOLand = 'NL') then
    res := true
  else if (ISOLand = 'AT') then
    res := true
  else if (ISOLand = 'PL') then
    res := true
  else if (ISOLand = 'PT') then
    res := true
  else if (ISOLand = 'RO') then
    res := true
  else if (ISOLand = 'SI') then
    res := true
  else if (ISOLand = 'SK') then
    res := true
  else if (ISOLand = 'FI') then
    res := true
  else if (ISOLand = 'SE') then
    res := true
  else if (ISOLand = 'GR') then
    res := true
  (*Großbritannien ist ab 1.1.2021 nicht mehr in der EU
  else if (ISOLand = 'GB') or (ISOLand = 'UK') then begin
    res := true;

    //Prüfen, ob der Brexit erfolgt ist
    if (ReadConfigValue ('Brexit', cfgint, cfgstr) = 0) and (cfgint = 1) then
      res := false;
  end
  *)
  else
    res := false;

  if res and (Length (ZIPCode) > 0) then begin
    if not Assigned (NonEUList) then begin
      NonEUList := TStringList.Create;

      query := TADOQuery.Create (Nil);

      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        query.SQL.Add('select ISO2, ZIP_FROM, ZIP_TO from V_ISO_COUNTRY_NO_EU order by ISO2, ZIP_FROM, ZIP_TO');

        try
          query.Open;

          while not (query.Eof) do begin
            NonEUList.Add (query.Fields [0].AsString+';'+query.Fields [1].AsString+';'+query.Fields [2].AsString);

            query.Next;
          end;

          query.Close;
        except
        end;
      finally
        query.Free;
      end;
    end;

    csvlist := TStringList.Create;
    csvlist.Delimiter := ';';
    csvlist.StrictDelimiter := true;

    try
      idx := 0;

      while (idx < NonEUList.Count) do begin
        csvlist.DelimitedText := NonEUList [idx];

        if (csvlist [0] = ISOLand) Then begin
          if ((Length (csvlist [1]) = 0) and (Length (csvlist [2]) = 0)) then begin
            res := false;
            break;
          end else if (((Length (csvlist [1]) = 0) or (ZIPCode >= csvlist [1])) and ((Length (csvlist [2]) = 0) or (ZIPCode <= csvlist [2]))) then begin
            res := false;
            break;
          end;
        end;

        Inc (idx);
      end;
    finally
      csvlist.Free;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: SetTabSheetTabWide
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Berechnet die Weiter der Tabs in Abh�ngigkeit der Tab-Texte
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure SetTabSheetTabWide (PageControl : TPageControl);
var
  w,
  idx,
  maxw : Integer;
begin
  idx := 0;
  maxw := 0;
  while (idx < PageControl.PageCount) do begin
    if PageControl.Pages [idx].TabVisible then begin
      w := PageControl.Canvas.TextWidth (PageControl.Pages [idx].Caption);

      if (w > maxw) then maxw := w;
    end;

    Inc (idx);
  end;

  PageControl.TabWidth := 2 * 16 + maxw;
end;

//******************************************************************************
//* Function Name: DrawStatusBitmap
//* Author       : Stefan Graf
//******************************************************************************
//* Description    Berechnet die Weiter der Tabs in Abh�ngigkeit der Tab-Texte
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure DrawStatusBitmap (Canvas : TCanvas; const rect : TRect; bm : TBitmap);
var
  drawrect : TRect;
begin
  bm.Transparent := True;
  bm.TransparentColor := bm.canvas.pixels[0, 0];
  bm.TransparentMode := tmAuto;

  if (((rect.Bottom - rect.Top) - 4) < ((bm.Height * 120) div 100)) then
    Canvas.Draw(rect.left + ((Rect.Right - Rect.Left - bm.Width) div 2), rect.top + ((Rect.Bottom - Rect.Top - bm.Height) div 2), bm)
  else begin
    drawrect.Top    := rect.Top + 2;
    drawrect.Bottom := rect.Bottom - 2;
    drawrect.Left   := rect.Left + ((rect.Right - rect.Left) div 2) - ((drawrect.Bottom - drawrect.Top) div 2);
    drawrect.Right  := drawrect.Left + (drawrect.Bottom - drawrect.Top);

    Canvas.StretchDraw (drawrect, bm);
  end;
end;

//******************************************************************************
//* Function Name: CheckMischNVE
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Pr�ft, ob auf der NVE unterschiedliche Artikel bzw. MHDs liegen
//******************************************************************************
//* Return Value : TRUE wenn nur gleiche Artikel, ansonsten FALSE
//******************************************************************************
function CheckMischNVE (const RefNVE : Integer) : Boolean;
var
  query: TADOQuery;
begin
  Result := True;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select sum (MENGE) from V_NVE_INHALT where REF_NVE=:RefNVE group by ARTIKEL_NR, MHD, CHARGE');
    query.Parameters.ParamByName('RefNVE').Value := RefNVE;

    try
      query.Open;

      Result := (query.RecordCount > 1);

      query.Close;
    except
      Result := True;
    end;
  finally
    query.Free;
  end;
end;

procedure PlaySound (const WaveName : String);
var
  FPUControlWord: Word;
begin
  try
    asm
      // Avoid FPU control word change in NETRAP.dll, NETAPI32.dll, etc
      FNSTCW  FPUControlWord
    end;
    try
      Set8087CW($133F);
      mmSystem.PlaySound (PChar (WaveName), 0, SND_ASYNC);
    finally
      asm
        FNCLEX
        FLDCW FPUControlWord
      end;
    end;
  except

  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 29.10.2014
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetSpedInfos (const RefSped, RefSpedProd : Integer; const Ausland : Boolean; var SpedInfos : TSpedInfos) : Integer;
var
  res     : Integer;
  rwert,
  gwert,
  bwert   : Integer;
  wertstr : String;
  strlist : TStringList;
  query   : TADOQuery;
begin
  res := 0;

  SpedInfos.RefSped           := RefSped;
  SpedInfos.RefSpedProd       := RefSpedProd;
  SpedInfos.SpedMaxGw         := -1;
  SpedInfos.SpedName          := '';
  SpedInfos.SpedDesc          := '';
  SpedInfos.SpedKennung       := '';
  SpedInfos.GewichtPflich     := False;
  SpedInfos.DefaultGewicht    := -1;
  SpedInfos.PrintCN23         := False;
  SpedInfos.AutoExport        := False;
  SpedInfos.LTDimRequired     := False;
  SpedInfos.SingleColliPack   := False;
  SpedInfos.RefDefaultLT      := -1;
  SpedInfos.SpedLogo          := '';
  SpedInfos.SpedColor         := -1;
  SpedInfos.NeedShippingNo    := false;
  SpedInfos.NeedShippingUnits := false;
  SpedInfos.EnterShippingNo   := false;
  SpedInfos.PresetDIM         := True;
  SpedInfos.PresetWeight      := True;

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (RefSpedProd > 0) then begin
      query.SQL.Add ( 'select sped.REF,sped.NAME||'' (''||prod.NAME||'')'' as NAME,sped.BESCHREIBUNG,sped.DFUE_KENNZEICHEN,cfg.*'
                     +' from V_SPEDITIONEN sped left outer join V_SPED_PRODUKTE prod on (prod.REF=:ref_prod) left outer join V_SPED_CONFIG cfg on (cfg.REF_SPED=sped.REF) where sped.REF=:ref');
      query.Parameters.ParamByName('ref_prod').Value := RefSpedProd;
    end else begin
      query.SQL.Add ('select sped.REF,sped.NAME,sped.BESCHREIBUNG,sped.DFUE_KENNZEICHEN,cfg.* from V_SPEDITIONEN sped left outer join V_SPED_CONFIG cfg on (cfg.REF_SPED=sped.REF) where sped.REF=:ref');
    end;
    query.Parameters.ParamByName('ref').Value := RefSped;

    try
      query.Open;

      if query.FieldByName('REF').IsNull then
        res := -1
      else begin
        SpedInfos.SpedName := query.FieldByName ('NAME').AsString;

        if query.FieldByName('BESCHREIBUNG').IsNull then
          SpedInfos.SpedDesc := SpedInfos.SpedName
        else
          SpedInfos.SpedDesc := query.FieldByName ('BESCHREIBUNG').AsString;

        SpedInfos.SpedKennung  := query.FieldByName ('DFUE_KENNZEICHEN').AsString;
        SpedInfos.SpedMaxGw    := DBGetIntegerNull (query.FieldByName ('MAX_GEWICHT'));
        SpedInfos.RefDefaultLT := DBGetIntegerNull (query.FieldByName ('REF_DEFAULT_LT'));

        SpedInfos.PrintCN23         := (query.FieldByName('OPT_CN23').AsString > '0');
        SpedInfos.AutoExport        := (query.FieldByName('OPT_AUTO_NONE_EU_SEND').AsString = '1');
        SpedInfos.LTDimRequired     := (query.FieldByName('OPT_DIM_REQUIRED').AsString = '1');
        SpedInfos.NeedShippingUnits := query.FieldByName('OPT_MULTI_SHIPMEND').AsString = '2';
        SpedInfos.NeedShippingNo    := query.FieldByName('OPT_SENDUNGS_NR').AsString = '1';
        SpedInfos.EnterShippingNo   := query.FieldByName('OPT_ENTER_SHIPMEND_NO').AsString = '1';

        if Assigned (query.FindField ('OPT_SINGLE_COLLI')) then
          SpedInfos.SingleColliPack := (query.FieldByName('OPT_SINGLE_COLLI').AsString = '1');

        if Assigned (query.FindField ('OPT_DIM_PRESET')) then
          SpedInfos.PresetDIM := (query.FieldByName('OPT_DIM_PRESET').AsString = '1');

        if Assigned (query.FindField ('OPT_WEIGHT_PRESET')) then
          SpedInfos.PresetWeight := (query.FieldByName('OPT_WEIGHT_PRESET').AsString = '1');

        if Assigned (query.FindField ('LOGO')) then
          SpedInfos.SpedLogo := query.FieldByName('LOGO').AsString;

        if Assigned (query.FindField ('COLOR')) then begin
          if not (query.FieldByName('COLOR').IsNull) Then begin
            wertstr := query.FieldByName('COLOR').AsString;

            rwert := -1;
            gwert := -1;
            bwert := -1;

            if ((Length (wertstr) > 6) and ((wertstr [1] = '#') or (wertstr [1] = 'x'))) then begin
              if not TryStrToInt (copy (wertstr, 2, 2), rwert) then
                rwert := -1;
              if not TryStrToInt (copy (wertstr, 2, 2), gwert) then
                gwert := -1;
              if not TryStrToInt (copy (wertstr, 2, 2), bwert) then
                bwert := -1;
            end else begin
              strlist := TStringList.Create;

              try
                strlist.Delimiter := ',';
                strlist.DelimitedText := wertstr;

                if (strlist.Count < 1) or not TryStrToInt (strlist [0], rwert) then
                  rwert := -1;
                if (strlist.Count < 2) or not TryStrToInt (strlist [1], gwert) then
                  gwert := -1;
                if (strlist.Count < 3) or not TryStrToInt (strlist [2], bwert) then
                  bwert := -1;
              finally
                strlist.Free;
              end;
            end;

            if (rwert >= 0) and (bwert >= 0) and (gwert >= 0) then
              SpedInfos.SpedColor := RGB (rwert, gwert, bwert);
          end;
        end;

        if (Ausland) then begin
          SpedInfos.GewichtPflich  := query.FieldByName ('OPT_AUSLAND_GEWICHT_MUSS').AsString = '1';
          SpedInfos.DefaultGewicht := DBGetIntegerNull (query.FieldByName ('AUSLAND_DEFAULT_BRUTTO'));
        end else begin
          SpedInfos.GewichtPflich  := query.FieldByName ('OPT_INLAND_GEWICHT_MUSS').AsString = '1';
          SpedInfos.DefaultGewicht := DBGetIntegerNull (query.FieldByName ('INLAND_DEFAULT_BRUTTO'));
        end;
      end;

      query.Close;

      if (SpedInfos.RefDefaultLT <= 0) then begin
        SpedInfos.DefaultLT     := '';
        SpedInfos.DefaultLTTara := -1;
        SpedInfos.DefaultLTL    := -1;
        SpedInfos.DefaultLTB    := -1;
        SpedInfos.DefaultLTH    := -1;
      end else begin
        query.SQL.Clear;
        query.SQL.Add ('select * from V_LT_TYPEN where REF=:ref');
        query.Parameters.ParamByName('ref').Value := SpedInfos.RefDefaultLT;

        query.Open;

        SpedInfos.DefaultLT     := query.FieldByName ('NAME').AsString;
        SpedInfos.DefaultLTTara := DBGetIntegerNull (query.FieldByName ('TARA_GEWICHT'));
        SpedInfos.DefaultLTL    := DBGetIntegerNull (query.FieldByName ('L'));
        SpedInfos.DefaultLTB    := DBGetIntegerNull (query.FieldByName ('B'));
        SpedInfos.DefaultLTH    := DBGetIntegerNull (query.FieldByName ('H'));

        query.Close;
      end;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 27.01.2016
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetIntegerParameter (const Line : String) : Integer;
var
  intwert : Integer;
begin
  if (Length (Line) = 0) then
    intwert := -1
  else if not (TryStrToInt(Line, intwert)) then
    intwert := -1;

  Result := intwert;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.02.2014
//******************************************************************************
//* Description  : Bilder ausgeben, PGN, JPG und BMP werden unterst�tzt
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure ShowArtikelPicture (ArtikelImage : TImage32; const Filename : String);
var
  extstr  : String;
  png     : {$ifdef DELPHIXE_UP}TPortableNetworkGraphic32{$else}TPNGGraphic{$endif};
  jpg     : TJPEGImage;
  fstream : TFileStream;
  buf     : array [0..5] of AnsiChar;
begin
  if FileExists (Filename) then begin
    try
      fstream := TFileStream.Create (Filename, fmOpenRead);
    except
      fstream := Nil;
    end;

    if Assigned (fstream) then begin
      try
        fstream.ReadBuffer(buf, sizeof (buf));

        fstream.Position := 0;

        //Die Datei solle min. 3 Bytes gross sein
        if (fstream.Size > 3) then begin
          //Ermitteln, welche Grafikart enthalten ist
          if (buf [1] = 'P') and (buf [2] = 'N') and (buf [3] = 'G') then
            extstr := '.png'
          else if (buf [0] = 'B') and (buf [1] = 'M') then
            extstr := '.bmp'
          else if (buf [0] = #$ff) and (buf [1] = #$D8) then
            extstr := '.jpg'
          else
            extstr := LowerCase (ExtractFileExt (Filename));

          if (extstr = '.png') then begin
            png := {$ifdef DELPHIXE_UP}TPortableNetworkGraphic32.Create{$else}TPNGGraphic.Create{$endif};

            try
              try
                png.LoadFromStream (fstream);

                ArtikelImage.Bitmap.DrawMode := dmTransparent;

                ArtikelImage.Bitmap.Assign (png);
                ArtikelImage.Visible := True;
              except
                on  E: Exception do begin
                  ErrorTrackingModule.WriteErrorLog ('ShowArtikelPicture TPNGGraphic:', Filename + ' : ' + e.Message);
                end;
              end;
            finally
              png.Free;
            end;
          end else if (extstr = '.jpg') then begin
            jpg := TJPEGImage.Create;

            try
              try
                jpg.LoadFromStream (fstream);
                ArtikelImage.Bitmap.Assign (jpg);
                ArtikelImage.Visible := True;
              except
                on  E: Exception do begin
                  ErrorTrackingModule.WriteErrorLog ('ShowArtikelPicture TPNGGraphic:', Filename + ' : ' + e.Message);
                end;
              end;
            finally
              jpg.Free;
            end;
          end else if (extstr = '.bmp') then begin
            try
              ArtikelImage.Bitmap.LoadFromStream (fstream);
              ArtikelImage.Visible := True;
            except
              on  E: Exception do begin
                ErrorTrackingModule.WriteErrorLog ('ShowArtikelPicture TPNGGraphic:', Filename + ' : ' + e.Message);
              end;
            end;
          end;
        end;
      except
        on  E: Exception do begin
          ErrorTrackingModule.WriteErrorLog ('ShowArtikelPicture ', Filename + ' : ' + e.Message);
        end;
      end;

      fstream.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        :
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetLabelPrinterPort (const RefPrt : Integer) : String;
var
  query : TADOQuery;
begin
  if (RefPrt <= 0) then
    Result := ''
  else begin
    query := TADOQuery.Create (Nil);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add('select PORT from V_PRT_PRINTER where REF=:ref');
      query.Parameters [0].Value := RefPrt;

      query.Open;

      Result := query.Fields [0].AsString;

      query.Close;
    finally
      query.Free;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 01.10.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function StrToFile(const FileName, SourceString : string; const Endcoding : Boolean) : Integer;
var
  res    : Integer;
  dirstr : String;
  outstr : AnsiString;
  Stream : TFileStream;
begin
  res := ERROR_SUCCESS;

  dirstr := ExtractFileDir(FileName);

  if (Length (dirstr) > 0) and not (DirectoryExists (dirstr)) then begin
    try
      ForceDirectories (dirstr);
    except
      res := GetLastError;
    end;
  end;

  if (res = 0) then begin
    try
      Stream:= TFileStream.Create(FileName, fmCreate);

      try
        if not (Endcoding) then
          Stream.WriteBuffer(Pointer(SourceString)^, Length(SourceString))
        else begin
          outstr := UTF8Encode (SourceString);

          Stream.WriteBuffer(Pointer(outstr)^, Length(outstr));
        end;
      finally
        Stream.Free;
      end;
    except
      res := GetLastError;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 01.10.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure CheckScreen (Form : TForm);
begin
  if (Form.Height > Screen.DesktopWidth) then
    Form.Height := Screen.DesktopWidth - 20;

  if (Form.Top > Form.Monitor.Height) Then begin
    Form.Top := (Form.Monitor.Height - Form.Height) + 16;
    if (Form.Top < 1) then Form.Top := 1;
  end;

  if (Form.Width > Screen.DesktopWidth) then
    Form.Width := Screen.DesktopWidth - 20;

  if (Form.Left > Screen.DesktopWidth) then begin
    Form.Left := (Screen.DesktopWidth - Form.Width) + 16;
    if (Form.Left < 1) then Form.Left := 1;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 14.12.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetEinlagerVorschlag (const RefLager, RefAE : Integer; var InfoStr : String) : integer;
var
  query : TADOQuery;
begin
  InfoStr := '';

  if (RefLager <= 0) or (RefAE <= 0) then
    Result := 4
  else begin
    query := TADOQuery.Create (Nil);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add('select vor.EINLAGER_INFO from V_ARTIKEL_EINLAGER_VORSCHLAG vor, VQ_ARTIKEL_EINHEIT ae where vor.REF_LAGER=:ref_lager and vor.REF_AR=ae.REF_AR and vor.REF_EINHEIT=ae.REF_EINHEIT and ae.REF=:ref_ae');
      query.Parameters.ParamByName ('ref_lager').Value := RefLager;
      query.Parameters.ParamByName ('ref_ae').Value := RefAE;

      try
        query.Open;

        InfoStr := query.Fields [0].AsString;

        query.Close;
      except
      end;
    finally
      query.Free;
    end;
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Sebastian Sch�tte
//* Datum        : 11.07.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadMandantCheckListBox  (CheckListBox : TCheckListBox; const OnlyActive : boolean; const activeRefs: TArryOfInteger; const RefLocation: Integer = -1; const RefMandant : Integer = -1): Integer;
var
  aRef,
  res      : Integer;
  query    : TADOQuery;
  wherestr : String;
  found    : Boolean;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadMandantCheckListBox');
    TraceParameter ('CheckListBox    ', CheckListBox.Name);
    TraceParameter ('activeRefsCount ', length(activeRefs));
    TraceParameter ('RefLocation     ', RefLocation);
    TraceParameter ('RefMandant      ', RefMandant);
  {$ENDIF}

  CheckListBox.Items.Clear;

  query := TADOQuery.Create (Nil);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    wherestr := '';

    query.SQL.Clear;
    query.SQL.Add ('select REF,NAME,BESCHREIBUNG,CONFIG_OPT,DEFAULT_LIEFER_DAUER from V_PCD_MANDANT');

    if (RefLocation <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF in (select REF_MAND from V_MANDANT_REL_LAGER where REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION='+IntToStr (RefLocation)+'))';
    end;

    if (RefMandant <> -1) then begin
      if (Length (wherestr) > 0) then wherestr := wherestr + ' and ';
      wherestr := wherestr + 'REF='+IntToStr (RefMandant);
    end;

    if (length (wherestr) > 0) then
      query.SQL.Add ('where ' + wherestr);

    query.SQL.Add ('order by NAME');

    try
      query.Open;

      while not (query.Eof) do begin
        found := false;

        for aRef in activeRefs do begin
          if (aRef = query.Fields [0].AsInteger) then begin
            found := true;

            break;
          end;
        end;

        if (not OnlyActive or found) then begin
          CheckListBox.Items.Add(query.Fields [1].AsString);

          CheckListBox.Checked[CheckListBox.Items.IndexOf(query.Fields [1].AsString)] := found;
        end;

        query.Next;
      end;

      query.Close;
    except
      res := -9;
    end;
  finally
    query.Free;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Sebastian Sch�tte
//* Datum        : 11.07.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function LoadMandantSubMandantCheckListBox  (CheckListBox : TCheckListBox; const OnlyActive : boolean; const activeRefs: TArryOfInteger; const RefLocation: Integer = -1; const RefMandant : Integer = -1): Integer;
var
  aRef,
  res,
  idx      : Integer;
  query1   : TADOQuery;
  query2   : TADOQuery;
  found    : Boolean;
begin
  res := 0;

  {$IFDEF TRACE}
    FunctionStart('LoadMandantCheckListBox');
    TraceParameter ('CheckListBox    ', CheckListBox.Name);
    TraceParameter ('activeRefsCount ', length(activeRefs));
    TraceParameter ('RefLocation     ', RefLocation);
    TraceParameter ('RefMandant      ', RefMandant);
  {$ENDIF}

  CheckListBox.Items.Clear;

  query1 := TADOQuery.Create (Nil);
  query2 := TADOQuery.Create (Nil);
  try
    query1.LockType := ltReadOnly;
    query1.Connection := LVSDatenModul.MainADOConnection;

    query2.LockType := ltReadOnly;
    query2.Connection := LVSDatenModul.MainADOConnection;

    query2.SQL.Add ('select REF,NAME,BESCHREIBUNG,CONFIG_OPT,DEFAULT_LIEFER_DAUER from V_MANDANT where STATUS in (''ANG'', ''AKT'') and REF_MASTER_MAND=:RefMand and REF in (select REF_MAND from V_MANDANT_REL_LOCATION where REF_LOCATION=:ref_loc) order by upper (NAME)');
    query2.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    query1.SQL.Clear;
    query1.SQL.Add ('select REF,NAME,BESCHREIBUNG,CONFIG_OPT,DEFAULT_LIEFER_DAUER from V_PCD_MANDANT order by upper (NAME)');

    try
      query1.Open;

      while not (query1.Eof) do begin
        found := false;

        for aRef in activeRefs do begin
          if (aRef = query1.Fields [0].AsInteger) then begin
            found := true;

            break;
          end;
        end;

        if (not OnlyActive or found) then begin
          idx := CheckListBox.Items.Add(query1.Fields [1].AsString);
          CheckListBox.Checked[idx] := found;
        end;

        query2.Parameters.ParamByName('RefMand').Value := query1.Fields [0].AsInteger;

        try
          query2.Open;

          while not (query2.Eof) do begin
            found := false;

            for aRef in activeRefs do begin
              if (aRef = query2.Fields [0].AsInteger) then begin
                found := true;

                break;
              end;
            end;

            if (not OnlyActive or found) then begin
              idx := CheckListBox.Items.Add(query2.Fields [1].AsString);

              CheckListBox.Checked[idx] := found;
            end;

            query2.Next;
          end;

          query2.Close;
        except
          res := -9;
        end;

        query1.Next;
      end;

      query1.Close;
    except
      res := -9;
    end;
  finally
    query1.Free;
    query2.Free;
  end;

  Result := res;

  {$IFDEF TRACE}
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name: GetMandantRef
//* Author       : Sebastian Sch�tte
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function  GetMandantRefFromName (const Name : string) : Integer;
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add('select REF from V_MANDANT where NAME=:name');
    query.Parameters.ParamByName('name').Value := Name;

    query.Open;

    Result := query.Fields [0].AsInteger;

    query.Close;
  finally
    query.Free;
  end;
end;

//******************************************************************************
initialization
  UserReg         := TRegistryModule.Create;
  TerminalService := tTerminalService.Create;

//******************************************************************************
finalization
  if Assigned (TerminalService) then
    TerminalService.Free;
  TerminalService := Nil;

  if Assigned (UserReg) then
    UserReg.Free;
  UserReg := Nil;

  if Assigned (UserLog) then begin
    UserLog.Close;
    UserLog.Free;
  end;
  UserLog := Nil;
end.
