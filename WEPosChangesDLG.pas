unit WEPosChangesDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, MemDS, DBAccess, Ora, Grids, DBGrids, SMDBGrid, DBGridPro,
  StdCtrls, ExtCtrls;

type
  TWEPosChangesForm = class(TForm)
    FussPanel: TPanel;
    CloseButton: TButton;
    PosPanel: TPanel;
    Label2: TLabel;
    RetoureChangeDBGrid: TDBGridPro;
    RetoureChangeDataSource: TOraDataSource;
    RetoureChangeQuery: TOraQuery;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
  private
    fRefWEPos : Integer;
  public
    property RefWEPos : Integer read fRefWEPos write fRefWEPos;
  end;

implementation

{$R *.dfm}

uses
  VCL<PERSON><PERSON>s, LVSConst, DatenModul, DBGridUtilModule, FrontendUtils, ConfigModul, SprachModul;

procedure TWEPosChangesForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  RetoureChangeQuery.Close;

  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormInfo (Self);
end;

procedure TWEPosChangesForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, AuftragNrLabel);
  {$endif}
end;

procedure TWEPosChangesForm.FormShow(Sender: TObject);
begin
  RetoureChangeQuery.SQL.Add ('select * from V_WE_POS_CHANGES where REF_WE_POS=:ref');
  RetoureChangeQuery.Params [0].Value := fRefWEPos;

  RetoureChangeQuery.Open;
end;

end.

