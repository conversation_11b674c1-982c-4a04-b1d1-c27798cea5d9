unit EditMDEDLG;

{$i compilers.inc}

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TEditMDEForm = class(TForm)
    NameEdit: TEdit;
    Label1: TLabel;
    ScalingGroupBox: TGroupBox;
    OkButton: TButton;
    AbortButton: TButton;
    GiantEdit: TEdit;
    LargeEdit: TEdit;
    MediumEdit: TEdit;
    DefaultEdit: TEdit;
    SmallEdit: TEdit;
    ExtraSmallEdit: TEdit;
    Label2: TLabel;
    Label3: TLabel;
    Label4: TLabel;
    Label5: TLabel;
    Label6: TLabel;
    Label7: TLabel;
    Label8: TLabel;
    MDEID: TLabel;
    Label10: TLabel;
    MDEType: TLabel;
    Bevel1: TBevel;
    procedure FormShow(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
     fRefTerm : Integer;
  public
    function Prepare (const RefTerm : Integer) : Integer;
  end;

implementation

uses
  Ora, OraSmart,
  DatenModul, StrUtils, StringUtils, LVSSystemInterface;

{$R *.dfm}

procedure TEditMDEForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res     : integer;
  wert    : Double;
  scalstr : String;
  olddec  : char;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    res := 0;

    if NameEdit.Visible then
      res := SetTerminalName (fRefTerm, NameEdit.Text);

    if ((res = 0) and ScalingGroupBox.Visible) then begin
      olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

      try
        {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

        scalstr := '';

        if (Length (GiantEdit.Text) > 0) then begin
          if not TryStrToFloat(GiantEdit.Text, wert) then begin
            res := -1;
            GiantEdit.SetFocus;
          end else begin
            if (Length (scalstr) > 0) then scalstr := scalstr + ';';
            scalstr := scalstr + 'giant='+FloatToStr (wert);
          end;
        end;

        if (Length (LargeEdit.Text) > 0) then begin
          if not TryStrToFloat(LargeEdit.Text, wert) then begin
            res := -1;
            ExtraSmallEdit.SetFocus;
          end else begin
            if (Length (scalstr) > 0) then scalstr := scalstr + ';';
            scalstr := scalstr + 'large='+FloatToStr (wert);
          end;
        end;

        if (Length (MediumEdit.Text) > 0) then begin
          if not TryStrToFloat(MediumEdit.Text, wert) then begin
            res := -1;
            ExtraSmallEdit.SetFocus;
          end else begin
            if (Length (scalstr) > 0) then scalstr := scalstr + ';';
            scalstr := scalstr + 'medium='+FloatToStr (wert);
          end;
        end;

        if (Length (DefaultEdit.Text) > 0) then begin
          if not TryStrToFloat(DefaultEdit.Text, wert) then begin
            res := -1;
            ExtraSmallEdit.SetFocus;
          end else begin
            if (Length (scalstr) > 0) then scalstr := scalstr + ';';
            scalstr := scalstr + 'default='+FloatToStr (wert);
          end;
        end;

        if (Length (SmallEdit.Text) > 0) then begin
          if not TryStrToFloat(SmallEdit.Text, wert) then begin
            res := -1;
            ExtraSmallEdit.SetFocus;
          end else begin
            if (Length (scalstr) > 0) then scalstr := scalstr + ';';
            scalstr := scalstr + 'small='+FloatToStr (wert);
          end;
        end;

        if (Length (ExtraSmallEdit.Text) > 0) then begin
          if not TryStrToFloat(ExtraSmallEdit.Text, wert) then begin
            res := -1;
            ExtraSmallEdit.SetFocus;
          end else begin
            if (Length (scalstr) > 0) then scalstr := scalstr + ';';
            scalstr := scalstr + 'extra-small='+FloatToStr (wert);
          end;
        end;
      finally
        {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
      end;

      if (res = 0) then
        res := SetTerminalScaling (fRefTerm, scalstr);
    end;

    CanClose := (res = 0);
  end;
end;

procedure TEditMDEForm.FormShow(Sender: TObject);
begin
  Label1.Visible := NameEdit.Visible;
end;

function TEditMDEForm.Prepare (const RefTerm : Integer) : Integer;
var
  i       : Integer;
  strlist : TStringList;
  strval  : TStringList;
  query   : TSmartQuery;
begin
  fRefTerm := RefTerm;

  query := TSmartQuery.Create (Self);

  try
    query.ReadOnly := True;
    query.Session  := LVSDatenModul.OraMainSession;

    query.SQL.Add ('select * from V_TERMINAL where REF=:ref');
    query.Params.ParamByName('ref').Value := fRefTerm;

    query.Open;

    MDEID.Caption := query.FieldByName ('TERM_ID').AsString;
    MDEType.Caption := query.FieldByName ('TERM_TYP').AsString;

    if Assigned (query.FindField ('TERM_NAME')) then
      NameEdit.Text := query.FieldByName ('TERM_NAME').AsString
    else begin
      NameEdit.Visible := false;
    end;

    if not Assigned (query.FindField ('SCALING')) then
      ScalingGroupBox.Visible := false
    else begin
      GiantEdit.Text := '';
      LargeEdit.Text := '';
      MediumEdit.Text := '';
      DefaultEdit.Text := '';
      SmallEdit.Text := '';
      ExtraSmallEdit.Text := '';

      strlist := TStringList.Create;

      try
        strlist.Delimiter := ';';
        strlist.StrictDelimiter := true;
        strlist.DelimitedText := query.FieldByName ('SCALING').AsString;

        if (strlist.Count > 0) then begin
          strval := TStringList.Create;

          try
            strval.Delimiter := '=';
            strval.StrictDelimiter := true;

            for i:=0 to strlist.Count -1 do begin
              strval.DelimitedText := strlist [i];

              if (strval.Count > 1) then begin
                if (strval [0] = 'giant') then
                  GiantEdit.Text := strval [1]
                else if (strval [0] = 'large') then
                  LargeEdit.Text := strval [1]
                else if (strval [0] = 'medium') then
                  MediumEdit.Text := strval [1]
                else if (strval [0] = 'default') then
                  DefaultEdit.Text := strval [1]
                else if (strval [0] = 'small') then
                  SmallEdit.Text := strval [1]
                else if (strval [0] = 'extra-small') then
                  ExtraSmallEdit.Text := strval [1];
              end;
            end;
          finally
            strval.Free;
          end;
        end;
      finally
        strlist.Free;
      end;
    end;

    query.Close;
  finally
    query.Free;
  end;

  Result := 0;
end;

end.
