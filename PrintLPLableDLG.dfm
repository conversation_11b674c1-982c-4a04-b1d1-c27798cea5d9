object PrintLPLableForm: TPrintLPLableForm
  Left = 327
  Top = 143
  BorderStyle = bsDialog
  Caption = 'Etiketten drucken'
  ClientHeight = 425
  ClientWidth = 544
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    544
    425)
  TextHeight = 13
  object Label17: TLabel
    Left = 119
    Top = 32
    Width = 3
    Height = 13
    Caption = '-'
  end
  object Panel2: TPanel
    Left = 0
    Top = 352
    Width = 544
    Height = 73
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 0
    DesignSize = (
      544
      73)
    object Label8: TLabel
      Left = 8
      Top = 6
      Width = 38
      Height = 13
      Caption = 'Drucker'
    end
    object PrinterComboBox: TComboBoxPro
      Left = 8
      Top = 22
      Width = 289
      Height = 21
      Style = csOwnerDrawFixed
      ColWidth = 100
      ItemHeight = 15
      TabOrder = 0
      OnChange = PrinterComboBoxChange
    end
    object PrintButton: TButton
      Left = 303
      Top = 20
      Width = 75
      Height = 25
      Caption = 'Drucken'
      Default = True
      TabOrder = 2
      OnClick = PrintButtonClick
    end
    object CloseButton: TButton
      Left = 462
      Top = 42
      Width = 75
      Height = 25
      Anchors = [akTop]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 3
    end
    object PrtPreviewCheckBox: TCheckBox
      Left = 8
      Top = 49
      Width = 289
      Height = 17
      Caption = 'Druckvorschau anzeigen'
      TabOrder = 1
    end
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 2
    Width = 530
    Height = 350
    ActivePage = LPTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 1
    object LPTabSheet: TTabSheet
      Caption = 'Lagerplatz-Label'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
      DesignSize = (
        522
        322)
      object LPNrLabel: TLabel
        Left = 350
        Top = 27
        Width = 60
        Height = 13
        Caption = 'LPNrLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object LPKoorLabel: TLabel
        Left = 95
        Top = 46
        Width = 73
        Height = 13
        Caption = 'LPKoorLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label1: TLabel
        Left = 263
        Top = 27
        Width = 43
        Height = 13
        Caption = 'Platz-Nr.:'
      end
      object Label12: TLabel
        Left = 8
        Top = 46
        Width = 80
        Height = 13
        Caption = 'Platz-Koordinate:'
      end
      object Label15: TLabel
        Left = 153
        Top = 46
        Width = 5
        Height = 13
        Caption = '-'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label16: TLabel
        Left = 8
        Top = 27
        Width = 52
        Height = 13
        Caption = 'Platzname:'
      end
      object LPNameLabel: TLabel
        Left = 95
        Top = 27
        Width = 79
        Height = 13
        Caption = 'LPNameLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label32: TLabel
        Left = 8
        Top = 65
        Width = 44
        Height = 13
        Caption = 'Pr'#252'fziffer:'
      end
      object LPCheckLabel: TLabel
        Left = 95
        Top = 65
        Width = 83
        Height = 13
        Caption = 'LPCheckLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label36: TLabel
        Left = 8
        Top = 8
        Width = 59
        Height = 13
        Caption = 'Lagerberich:'
      end
      object LPLBNameLabel: TLabel
        Left = 95
        Top = 8
        Width = 94
        Height = 13
        Caption = 'LPLBNameLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object LPLBZoneNameLabel: TLabel
        Left = 350
        Top = 8
        Width = 123
        Height = 13
        Caption = 'LPLBZoneNameLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label37: TLabel
        Left = 264
        Top = 8
        Width = 28
        Height = 13
        Caption = 'Zone:'
      end
      object Label41: TLabel
        Left = 8
        Top = 94
        Width = 32
        Height = 13
        Caption = 'Layout'
      end
      object LPArtRadioGroup: TRadioGroup
        Left = 8
        Top = 139
        Width = 509
        Height = 49
        Caption = 'Auszeichnungsart'
        Columns = 5
        ItemIndex = 3
        Items.Strings = (
          'Platz-Nr'
          'Ohne Ebene'
          'Mit Ebene'
          'Koor. mit LP-Nr'
          'Projektspezifisch')
        TabOrder = 1
      end
      object LPExportButton: TButton
        Left = 442
        Top = 293
        Width = 75
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = 'CSV-Export'
        TabOrder = 3
        OnClick = LPExportButtonClick
      end
      object LPLayoutComboBox: TComboBoxPro
        Left = 8
        Top = 110
        Width = 509
        Height = 21
        Style = csOwnerDrawFixed
        ItemHeight = 15
        TabOrder = 0
      end
      object BarcodeGroupBox: TGroupBox
        Left = 3
        Top = 196
        Width = 406
        Height = 123
        Anchors = [akLeft, akTop, akRight, akBottom]
        Caption = 'Barcode-Typ'
        TabOrder = 2
        object LPITFRadioButton: TRadioButton
          Left = 24
          Top = 17
          Width = 113
          Height = 17
          Caption = '1D I2F5'
          Checked = True
          TabOrder = 0
          TabStop = True
          OnClick = LPITFRadioButtonClick
        end
        object LPQRRadioButton: TRadioButton
          Left = 24
          Top = 80
          Width = 113
          Height = 17
          Caption = 'QR-Code'
          TabOrder = 1
        end
        object LPDMRadioButton: TRadioButton
          Left = 24
          Top = 100
          Width = 113
          Height = 17
          Caption = 'Datamatrix'
          TabOrder = 2
        end
        object I2F5CheckCheckBox: TCheckBox
          Left = 46
          Top = 36
          Width = 494
          Height = 17
          Caption = 'I2F5 Pr'#252'fziffer im Barcoder erzeugen'
          TabOrder = 3
        end
        object LP128RadioButton: TRadioButton
          Left = 24
          Top = 60
          Width = 113
          Height = 17
          Caption = '1D Code128'
          TabOrder = 4
          Visible = False
        end
      end
    end
    object KommLPTabSheet: TTabSheet
      Caption = 'Kommissionierplatz-Label'
      ImageIndex = 1
      object Label2: TLabel
        Left = 8
        Top = 8
        Width = 37
        Height = 13
        Caption = 'Platz-Nr'
      end
      object Label3: TLabel
        Left = 8
        Top = 24
        Width = 46
        Height = 13
        Caption = 'Artikel-Nr.'
      end
      object Label4: TLabel
        Left = 8
        Top = 40
        Width = 32
        Height = 13
        Caption = 'Einheit'
      end
      object KommLPNrLabel: TLabel
        Left = 79
        Top = 8
        Width = 93
        Height = 13
        Caption = 'KommLPNrLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object KommArNrLabel: TLabel
        Left = 80
        Top = 24
        Width = 90
        Height = 13
        Caption = 'KommArNrLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label6: TLabel
        Left = 216
        Top = 24
        Width = 46
        Height = 13
        Caption = 'Artikeltext'
      end
      object KommArTextLabel: TLabel
        Left = 272
        Top = 24
        Width = 102
        Height = 13
        Caption = 'KommArTextLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object KommArVPELabel: TLabel
        Left = 80
        Top = 40
        Width = 101
        Height = 13
        Caption = 'KommArVPELabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label7: TLabel
        Left = 216
        Top = 40
        Width = 24
        Height = 13
        Caption = 'AZP:'
      end
      object KommArAZPLabel: TLabel
        Left = 272
        Top = 40
        Width = 101
        Height = 13
        Caption = 'KommArAZPLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label5: TLabel
        Left = 8
        Top = 56
        Width = 59
        Height = 13
        Caption = 'St'#252'ckeinheit'
      end
      object KommArSTVPELabel: TLabel
        Left = 80
        Top = 56
        Width = 117
        Height = 13
        Caption = 'KommArSTVPELabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label42: TLabel
        Left = 216
        Top = 56
        Width = 22
        Height = 13
        Caption = 'EAN'
      end
      object KommArEANLabel: TLabel
        Left = 272
        Top = 56
        Width = 102
        Height = 13
        Caption = 'KommArEANLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object UpRadioButton: TRadioButton
        Left = 8
        Top = 88
        Width = 113
        Height = 17
        Caption = 'Pfeil nach oben'
        TabOrder = 0
      end
      object DownRadioButton: TRadioButton
        Left = 320
        Top = 88
        Width = 113
        Height = 17
        Caption = 'Pfeil nach unten'
        TabOrder = 1
      end
    end
    object RelationTabSheet: TTabSheet
      Caption = 'Relations-Etiketten'
      ImageIndex = 2
      object Label9: TLabel
        Left = 8
        Top = 8
        Width = 42
        Height = 13
        Caption = 'Relation:'
      end
      object Label10: TLabel
        Left = 8
        Top = 40
        Width = 43
        Height = 13
        Caption = 'Barcode:'
      end
      object RelNameLabel: TLabel
        Left = 84
        Top = 8
        Width = 83
        Height = 13
        Caption = 'RelNameLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object RelCodeLabel: TLabel
        Left = 84
        Top = 40
        Width = 80
        Height = 13
        Caption = 'RelCodeLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label11: TLabel
        Left = 272
        Top = 11
        Width = 3
        Height = 13
        Caption = ':'
      end
      object Label18: TLabel
        Left = 8
        Top = 72
        Width = 42
        Height = 13
        Caption = 'Stellplatz'
      end
      object RelPlatzLabel: TLabel
        Left = 84
        Top = 72
        Width = 79
        Height = 13
        Caption = 'RelPlatzLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label19: TLabel
        Left = 8
        Top = 159
        Width = 61
        Height = 13
        Caption = 'Label-Anzahl'
      end
      object Label20: TLabel
        Left = 8
        Top = 56
        Width = 36
        Height = 13
        Caption = 'Bereich'
      end
      object RelBereichLabel: TLabel
        Left = 84
        Top = 56
        Width = 94
        Height = 13
        Caption = 'RelBereichLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label21: TLabel
        Left = 8
        Top = 24
        Width = 65
        Height = 13
        Caption = 'Beschreibung'
      end
      object RelDescLabel: TLabel
        Left = 84
        Top = 24
        Width = 80
        Height = 13
        Caption = 'RelDescLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object RelAnzahlEdit: TEdit
        Left = 80
        Top = 156
        Width = 43
        Height = 21
        TabOrder = 0
        Text = '0'
      end
      object RelAnzahlUpDown: TUpDown
        Left = 123
        Top = 156
        Width = 15
        Height = 21
        Associate = RelAnzahlEdit
        TabOrder = 1
      end
      object RelArtRadioGroup: TRadioGroup
        Left = 8
        Top = 93
        Width = 241
        Height = 52
        Caption = 'Labelart'
        Columns = 2
        Items.Strings = (
          'Palettenlabel'
          'Platzlabel')
        TabOrder = 2
      end
      object GroupBox1: TGroupBox
        Left = 272
        Top = 3
        Width = 238
        Height = 142
        Caption = 'Text'
        TabOrder = 3
        object RelTextLabel: TLabel
          Left = 13
          Top = 19
          Width = 76
          Height = 13
          Caption = 'RelTextLabel'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'MS Sans Serif'
          Font.Style = [fsBold]
          ParentFont = False
        end
      end
    end
    object LETabSheet: TTabSheet
      Caption = 'LE-Etiketten'
      ImageIndex = 3
      OnShow = LETabSheetShow
      DesignSize = (
        522
        322)
      object Label22: TLabel
        Left = 8
        Top = 8
        Width = 40
        Height = 13
        Caption = 'LT-Type'
      end
      object Label23: TLabel
        Left = 154
        Top = 91
        Width = 57
        Height = 13
        Alignment = taRightJustify
        Caption = 'Labelanzahl'
      end
      object Label24: TLabel
        Left = 194
        Top = 117
        Width = 19
        Height = 13
        Alignment = taRightJustify
        Caption = 'Von'
      end
      object Label25: TLabel
        Left = 352
        Top = 117
        Width = 14
        Height = 13
        Alignment = taRightJustify
        Caption = 'Bis'
      end
      object TLabel
        Left = 155
        Top = 64
        Width = 55
        Height = 13
        Alignment = taRightJustify
        Caption = 'Fachanzahl'
      end
      object Label33: TLabel
        Left = 333
        Top = 64
        Width = 33
        Height = 13
        Alignment = taRightJustify
        Caption = 'Kopien'
      end
      object LTComboBox: TComboBoxPro
        Left = 8
        Top = 24
        Width = 510
        Height = 22
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 0
        OnChange = LTComboBoxChange
      end
      object LEVonEdit: TEdit
        Left = 224
        Top = 114
        Width = 96
        Height = 21
        TabOrder = 6
        Text = 'LEVonEdit'
        OnChange = LEVonEditChange
        OnKeyPress = LECountEditKeyPress
      end
      object LECountEdit: TEdit
        Left = 224
        Top = 88
        Width = 96
        Height = 21
        TabOrder = 4
        Text = 'LECountEdit'
        OnChange = LECountEditChange
        OnKeyPress = LECountEditKeyPress
      end
      object LEBisEdit: TEdit
        Left = 376
        Top = 114
        Width = 96
        Height = 21
        TabOrder = 7
        Text = 'LEBisEdit'
        OnKeyPress = LECountEditKeyPress
      end
      object LENeuRadioButton: TRadioButton
        Left = 8
        Top = 90
        Width = 113
        Height = 17
        Caption = 'Neue Etiketten'
        TabOrder = 3
        OnClick = LERadioButtonClick
      end
      object LEReprintRadioButton: TRadioButton
        Left = 8
        Top = 116
        Width = 113
        Height = 17
        Caption = 'Etiketten nachdrucken'
        TabOrder = 5
        OnClick = LERadioButtonClick
      end
      object LECSVButton: TButton
        Left = 8
        Top = 293
        Width = 75
        Height = 25
        Anchors = [akLeft, akBottom]
        Caption = 'CSV Export'
        TabOrder = 8
        OnClick = LECSVButtonClick
      end
      object FachEdit: TEdit
        Left = 224
        Top = 59
        Width = 96
        Height = 21
        TabOrder = 1
        Text = 'FachEdit'
        OnKeyPress = LECountEditKeyPress
      end
      object LEKopienComboBox: TComboBox
        Left = 376
        Top = 59
        Width = 96
        Height = 21
        ItemIndex = 0
        TabOrder = 2
        Text = '1'
        Items.Strings = (
          '1'
          '2'
          '3'
          '4'
          '5'
          '6')
      end
      object LEArtGroupBox: TGroupBox
        Left = 9
        Top = 139
        Width = 510
        Height = 148
        Anchors = [akLeft, akTop, akRight, akBottom]
        Caption = 'Auszeichnungsart'
        TabOrder = 9
        DesignSize = (
          510
          148)
        object LEPalRadioButton: TRadioButton
          Left = 8
          Top = 20
          Width = 113
          Height = 17
          Caption = 'Palette'
          TabOrder = 0
        end
        object LEKLTRadioButton: TRadioButton
          Left = 8
          Top = 40
          Width = 113
          Height = 17
          Caption = 'KLT'
          TabOrder = 1
        end
        object LEKLTBigRadioButton: TRadioButton
          Left = 8
          Top = 60
          Width = 113
          Height = 17
          Caption = 'KLT gross'
          TabOrder = 2
        end
        object LETypRadioButton: TRadioButton
          Left = 134
          Top = 19
          Width = 113
          Height = 17
          Caption = 'Typ'
          TabOrder = 3
        end
        object LELeer5RadioButton: TRadioButton
          Left = 261
          Top = 40
          Width = 113
          Height = 17
          Caption = 'Leergut (5ner)'
          TabOrder = 7
        end
        object LELeer10RadioButton: TRadioButton
          Left = 261
          Top = 60
          Width = 113
          Height = 17
          Caption = 'Leergut (10ner)'
          TabOrder = 8
        end
        object LEEANRadioButton: TRadioButton
          Left = 134
          Top = 40
          Width = 113
          Height = 17
          Caption = 'EAN'
          TabOrder = 4
        end
        object LESerialRadioButton: TRadioButton
          Left = 134
          Top = 60
          Width = 113
          Height = 17
          Caption = 'Seriennr.'
          TabOrder = 5
        end
        object LELeerRadioButton: TRadioButton
          Left = 261
          Top = 19
          Width = 113
          Height = 17
          Caption = 'Leergut (1er)'
          TabOrder = 6
        end
        object LEKommRadioButton: TRadioButton
          Left = 388
          Top = 19
          Width = 113
          Height = 17
          Caption = 'Komm-Wagen'
          TabOrder = 9
        end
        object LEPackAutomatRadioButton: TRadioButton
          Left = 388
          Top = 40
          Width = 113
          Height = 17
          Caption = 'Automaten-LT'
          TabOrder = 10
        end
        object RadioButton12: TRadioButton
          Left = 388
          Top = 60
          Width = 113
          Height = 17
          Caption = 'RadioButton1'
          TabOrder = 11
          Visible = False
        end
        object GroupBox2: TGroupBox
          Left = 8
          Top = 85
          Width = 493
          Height = 59
          Anchors = [akLeft, akRight, akBottom]
          Caption = 'Barcode-Typ'
          TabOrder = 12
          object LEITFRadioButton: TRadioButton
            Left = 24
            Top = 17
            Width = 113
            Height = 17
            Caption = '1D I2F5'
            Checked = True
            TabOrder = 0
            TabStop = True
            OnClick = LEITFRadioButtonClick
          end
          object LEQRRadioButton: TRadioButton
            Left = 287
            Top = 17
            Width = 83
            Height = 17
            Caption = 'QR-Code'
            TabOrder = 1
          end
          object LEDMRadioButton: TRadioButton
            Left = 388
            Top = 17
            Width = 80
            Height = 17
            Caption = 'Datamatrix'
            TabOrder = 2
          end
          object LEI2F5CheckCheckBox: TCheckBox
            Left = 27
            Top = 38
            Width = 200
            Height = 17
            Caption = 'I2F5 Pr'#252'fziffer im Barcoder erzeugen'
            TabOrder = 3
          end
          object LE128RadioButton: TRadioButton
            Left = 160
            Top = 17
            Width = 113
            Height = 17
            Caption = '1D Code128'
            TabOrder = 4
            Visible = False
          end
        end
      end
    end
    object LBZonenTabSheet: TTabSheet
      Caption = 'Lagerbereich und Zonen'
      ImageIndex = 4
      object Label26: TLabel
        Left = 8
        Top = 51
        Width = 60
        Height = 13
        Caption = 'Zonenname:'
      end
      object ZonenNameLabel: TLabel
        Left = 111
        Top = 51
        Width = 100
        Height = 13
        Caption = 'ZonenNameLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label28: TLabel
        Left = 8
        Top = 70
        Width = 51
        Height = 13
        Caption = 'Zonen-Nr.:'
      end
      object ZonenNrLabel: TLabel
        Left = 111
        Top = 70
        Width = 81
        Height = 13
        Caption = 'ZonenNrLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label27: TLabel
        Left = 8
        Top = 8
        Width = 65
        Height = 13
        Caption = 'Lagerbereich:'
      end
      object LBNameLabel: TLabel
        Left = 111
        Top = 8
        Width = 79
        Height = 13
        Caption = 'LBNameLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label40: TLabel
        Left = 8
        Top = 27
        Width = 85
        Height = 13
        Caption = 'Kurzbezeichnung:'
      end
      object LBShortNameLabel: TLabel
        Left = 111
        Top = 27
        Width = 109
        Height = 13
        Caption = 'LBShortNameLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
    end
    object GebindeTabSheet: TTabSheet
      Caption = 'Gebinde'
      ImageIndex = 5
      object Label29: TLabel
        Left = 183
        Top = 91
        Width = 32
        Height = 13
        Alignment = taRightJustify
        Caption = 'Anzahl'
      end
      object Label30: TLabel
        Left = 196
        Top = 128
        Width = 19
        Height = 13
        Alignment = taRightJustify
        Caption = 'Von'
      end
      object Label31: TLabel
        Left = 201
        Top = 155
        Width = 14
        Height = 13
        Alignment = taRightJustify
        Caption = 'Bis'
      end
      object GBNeuRadioButton: TRadioButton
        Left = 22
        Top = 90
        Width = 113
        Height = 17
        Caption = 'Neue Etiketten'
        TabOrder = 0
        OnClick = GBNeuRadioButtonClick
      end
      object GBCountEdit: TEdit
        Left = 224
        Top = 88
        Width = 121
        Height = 21
        TabOrder = 1
        Text = 'GBCountEdit'
        OnKeyPress = LECountEditKeyPress
      end
      object GBReprintRadioButton: TRadioButton
        Left = 22
        Top = 140
        Width = 113
        Height = 17
        Caption = 'Etiketten nachdrucken'
        TabOrder = 2
        OnClick = LERadioButtonClick
      end
      object GBBisEdit: TEdit
        Left = 224
        Top = 152
        Width = 121
        Height = 21
        TabOrder = 3
        Text = 'GBBisEdit'
      end
      object GBVonEdit: TEdit
        Left = 224
        Top = 125
        Width = 121
        Height = 21
        TabOrder = 4
        Text = 'GBVonEdit'
      end
    end
    object DocIDTabSheet: TTabSheet
      Caption = 'Dokumenten-IDs'
      ImageIndex = 6
      object Label34: TLabel
        Left = 22
        Top = 93
        Width = 32
        Height = 13
        Caption = 'Anzahl'
      end
      object Label35: TLabel
        Left = 22
        Top = 62
        Width = 21
        Height = 13
        Caption = 'Text'
      end
      object DocIDCountEdit: TEdit
        Left = 73
        Top = 90
        Width = 121
        Height = 21
        TabOrder = 0
        Text = 'DocIDCountEdit'
        OnKeyPress = LECountEditKeyPress
      end
      object DocIDTextEdit: TEdit
        Left = 73
        Top = 58
        Width = 121
        Height = 21
        TabOrder = 1
        Text = 'DocIDTextEdit'
      end
    end
    object PrinterTabSheet: TTabSheet
      Caption = 'Drucker'
      ImageIndex = 7
      object Label13: TLabel
        Left = 22
        Top = 32
        Width = 64
        Height = 13
        Caption = 'Druckername'
      end
      object Label14: TLabel
        Left = 22
        Top = 52
        Width = 65
        Height = 13
        Caption = 'Beschreibung'
      end
      object Label38: TLabel
        Left = 22
        Top = 72
        Width = 19
        Height = 13
        Caption = 'Port'
      end
      object PrinterNameLabel: TLabel
        Left = 115
        Top = 32
        Width = 151
        Height = 13
        AutoSize = False
        Caption = 'PrinterNameLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object PrinterDescLabel: TLabel
        Left = 115
        Top = 52
        Width = 98
        Height = 13
        Caption = 'PrinterDescLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object PrinterPortLabel: TLabel
        Left = 115
        Top = 72
        Width = 92
        Height = 13
        Caption = 'PrinterPortLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object Label39: TLabel
        Left = 292
        Top = 32
        Width = 39
        Height = 13
        Caption = 'Nummer'
      end
      object PrinterNumberLabel: TLabel
        Left = 345
        Top = 32
        Width = 112
        Height = 13
        Caption = 'PrinterNumberLabel'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'MS Sans Serif'
        Font.Style = [fsBold]
        ParentFont = False
      end
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 456
    Top = 48
  end
  object SaveDialog1: TSaveDialog
    DefaultExt = '*.csv'
    Filter = 'CSV-Datei|*.csv'
    Options = [ofOverwritePrompt, ofHideReadOnly, ofEnableSizing]
    Left = 424
    Top = 48
  end
end
