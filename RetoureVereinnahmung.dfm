object RetoureVereinnahmungForm: TRetoureVereinnahmungForm
  Left = 430
  Top = 240
  BorderIcons = [biSystemMenu]
  Caption = 'RetoureVereinnahmungForm'
  ClientHeight = 795
  ClientWidth = 931
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poMainFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnKeyDown = FormKeyDown
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  TextHeight = 13
  object DatenPanel: TPanel
    Left = 0
    Top = 0
    Width = 931
    Height = 81
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    OnResize = DatenPanelResize
    DesignSize = (
      931
      81)
    object Label6: TLabel
      Left = 8
      Top = 11
      Width = 19
      Height = 13
      Caption = 'Von'
      Visible = False
    end
    object Label7: TLabel
      Left = 152
      Top = 11
      Width = 14
      Height = 13
      Caption = 'Bis'
      Visible = False
    end
    object Bevel1: TBevel
      Left = 6
      Top = 36
      Width = 919
      Height = 17
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 725
    end
    object Label2: TLabel
      Left = 8
      Top = 44
      Width = 34
      Height = 13
      Caption = 'Kunde:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
    end
    object Label1: TLabel
      Left = 356
      Top = 44
      Width = 52
      Height = 13
      Caption = 'Bestellung:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
    end
    object Label3: TLabel
      Left = 734
      Top = 44
      Width = 60
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'Lieferschein:'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
      ExplicitLeft = 577
    end
    object Label5: TLabel
      Left = 734
      Top = 11
      Width = 38
      Height = 13
      Alignment = taRightJustify
      Anchors = [akTop, akRight]
      Caption = 'Anzeige'
      ExplicitLeft = 540
    end
    object Label14: TLabel
      Left = 8
      Top = 63
      Width = 56
      Height = 13
      Caption = 'Retoure f'#252'r:'
    end
    object StaticText3: TLabel
      Left = 794
      Top = 44
      Width = 66
      Height = 13
      Caption = 'StaticText3'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object StaticText2: TLabel
      Left = 414
      Top = 44
      Width = 66
      Height = 13
      Caption = 'StaticText2'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object RetournQSImage: TImage
      Left = 310
      Top = 43
      Width = 32
      Height = 32
      Transparent = True
    end
    object VonDateTimePicker: TDateTimePicker
      Left = 40
      Top = 7
      Width = 89
      Height = 21
      Date = 38180.000000000000000000
      Time = 0.896834571758518000
      TabOrder = 0
      Visible = False
      OnChange = ChangeDateRage
    end
    object BisDateTimePicker: TDateTimePicker
      Left = 176
      Top = 7
      Width = 89
      Height = 21
      Date = 38180.000000000000000000
      Time = 0.896923368061834500
      TabOrder = 1
      Visible = False
      OnChange = ChangeDateRage
    end
    object StaticText1: TStaticText
      Left = 81
      Top = 44
      Width = 69
      Height = 17
      BevelInner = bvLowered
      BevelOuter = bvRaised
      Caption = 'StaticText1'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 3
    end
    object StatComboBox: TComboBox
      Left = 777
      Top = 8
      Width = 145
      Height = 21
      Style = csDropDownList
      Anchors = [akTop, akRight]
      ItemIndex = 1
      TabOrder = 2
      Text = 'Offene Positionen'
      OnChange = StatComboBoxChange
      Items.Strings = (
        'Alle Positionen'
        'Offene Positionen'
        'Erfasste Positionen')
    end
    object RetInfoStaticText: TStaticText
      Left = 81
      Top = 63
      Width = 104
      Height = 17
      BevelInner = bvLowered
      BevelOuter = bvRaised
      Caption = 'RetInfoStaticText'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 4
    end
  end
  object EingabePanel: TPanel
    Left = 0
    Top = 521
    Width = 931
    Height = 217
    Align = alBottom
    BevelOuter = bvNone
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = []
    ParentBackground = False
    ParentFont = False
    TabOrder = 5
    DesignSize = (
      931
      217)
    object Label4: TLabel
      Left = 286
      Top = 7
      Width = 328
      Height = 13
      Caption = 
        'Alle erfassten Positionen f'#252'r diesen Artikel im aktuellen Warene' +
        'ingang'
    end
    object GroupBox1: TGroupBox
      Left = 8
      Top = 7
      Width = 274
      Height = 204
      Anchors = [akLeft, akTop, akBottom]
      Caption = 'Position erfassen '
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      object EditDataPanel: TPanel
        Left = 2
        Top = 48
        Width = 270
        Height = 141
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 2
        object Label8: TLabel
          Left = 4
          Top = 5
          Width = 33
          Height = 13
          Caption = 'Menge'
        end
        object Label9: TLabel
          Left = 4
          Top = 29
          Width = 39
          Height = 13
          Caption = 'Gewicht'
        end
        object Label12: TLabel
          Left = 158
          Top = 29
          Width = 12
          Height = 13
          Caption = 'kg'
        end
        object MHDLabel: TLabel
          Left = 4
          Top = 54
          Width = 25
          Height = 13
          Caption = 'MHD'
        end
        object Label10: TLabel
          Left = 4
          Top = 79
          Width = 34
          Height = 13
          Caption = 'Charge'
        end
        object MHDDutyLabel: TLabel
          Left = 71
          Top = 49
          Width = 11
          Height = 23
          Caption = '*'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Symbol'
          Font.Style = [fsBold]
          ParentFont = False
          Visible = False
        end
        object ChargeDutyLabel: TLabel
          Left = 71
          Top = 74
          Width = 11
          Height = 23
          Caption = '*'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Symbol'
          Font.Style = [fsBold]
          ParentFont = False
          Visible = False
        end
        object Label26: TLabel
          Left = 71
          Top = -1
          Width = 11
          Height = 23
          Caption = '*'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Symbol'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object MengeEdit: TEdit
          Left = 84
          Top = 1
          Width = 73
          Height = 21
          TabOrder = 0
          Text = '0'
          OnChange = PosEditChange
          OnEnter = MengeEditEnter
          OnExit = MengeEditExit
          OnKeyPress = MengeEditKeyPress
        end
        object MengeUpDown: TIntegerUpDown
          Left = 157
          Top = 1
          Width = 16
          Height = 21
          Associate = MengeEdit
          Max = 1
          TabOrder = 1
          OnChanging = MengeUpDownChanging
          OnChangingEx = MengeUpDownChangingEx
        end
        object GewichtEdit: TEdit
          Left = 84
          Top = 26
          Width = 65
          Height = 21
          TabOrder = 2
          Text = 'GewichtEdit'
          OnChange = PosEditChange
          OnExit = GewichtEditExit
          OnKeyPress = GewichtEditKeyPress
        end
        object MHDEdit: TEdit
          Left = 84
          Top = 51
          Width = 89
          Height = 21
          TabOrder = 3
          Text = 'MHDEdit'
          OnChange = PosEditChange
          OnExit = MHDEditExit
          OnKeyPress = MHDEditKeyPress
        end
        object ChargeEdit: TEdit
          Left = 84
          Top = 76
          Width = 89
          Height = 21
          MaxLength = 32
          TabOrder = 4
          Text = 'ChargeEdit'
          OnChange = PosEditChange
          OnExit = ChargeEditExit
          OnKeyPress = ChargeEditKeyPress
        end
        object SerialPanel: TPanel
          Left = 0
          Top = 102
          Width = 270
          Height = 39
          Align = alBottom
          BevelOuter = bvNone
          TabOrder = 5
          DesignSize = (
            270
            39)
          object Label19: TLabel
            Left = 4
            Top = 9
            Width = 42
            Height = 13
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Seriennr.'
          end
          object SerialEdit: TEdit
            Left = 84
            Top = 6
            Width = 181
            Height = 21
            Anchors = [akLeft, akTop, akRight]
            MaxLength = 32
            TabOrder = 0
            Text = 'SerialEdit'
            OnChange = PosEditChange
            OnExit = ChargeEditExit
            OnKeyPress = ChargeEditKeyPress
          end
        end
      end
      object Panel3: TPanel
        Left = 2
        Top = 15
        Width = 270
        Height = 8
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 0
      end
      object EditLTPanel: TPanel
        Left = 2
        Top = 23
        Width = 270
        Height = 25
        Align = alTop
        BevelOuter = bvNone
        TabOrder = 1
        object Label13: TLabel
          Left = 4
          Top = 3
          Width = 30
          Height = 13
          Caption = 'LT-Nr.'
        end
        object LTEdit: TEdit
          Left = 84
          Top = 0
          Width = 141
          Height = 21
          MaxLength = 32
          TabOrder = 0
          Text = 'LTEdit'
          OnKeyPress = EditKeyPress
        end
      end
      object QualityPanel: TPanel
        Left = 2
        Top = 53
        Width = 270
        Height = 25
        Align = alBottom
        BevelOuter = bvNone
        TabOrder = 4
        DesignSize = (
          270
          25)
        object GrundLabel: TLabel
          Left = 4
          Top = 7
          Width = 39
          Height = 13
          Anchors = [akLeft, akBottom]
          Caption = 'Zustand'
        end
        object Label22: TLabel
          Left = 264
          Top = 8
          Width = 6
          Height = 13
          Caption = '2'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -6
          Font.Name = 'MS Sans Serif'
          Font.Style = []
          ParentFont = False
        end
        object QualityComboBox: TComboBox
          Left = 84
          Top = 4
          Width = 182
          Height = 21
          Style = csDropDownList
          Anchors = [akLeft, akRight, akBottom]
          MaxLength = 64
          TabOrder = 0
          OnDropDown = ComboBoxDropDown
          OnEnter = ComboBoxEnter
        end
      end
      object ZustandPanel: TPanel
        Left = 2
        Top = 78
        Width = 270
        Height = 25
        Align = alBottom
        BevelOuter = bvNone
        TabOrder = 5
        DesignSize = (
          270
          25)
        object Label15: TLabel
          Left = 4
          Top = 7
          Width = 57
          Height = 13
          Anchors = [akLeft, akBottom]
          Caption = 'Zustandkat.'
        end
        object Label23: TLabel
          Left = 264
          Top = 8
          Width = 6
          Height = 13
          Caption = '3'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -6
          Font.Name = 'MS Sans Serif'
          Font.Style = []
          ParentFont = False
        end
        object ZustandComboBox: TComboBox
          Left = 84
          Top = 4
          Width = 182
          Height = 21
          Style = csDropDownList
          Anchors = [akLeft, akRight, akBottom]
          MaxLength = 64
          TabOrder = 0
          OnDropDown = ComboBoxDropDown
          OnEnter = ComboBoxEnter
        end
      end
      object PosButtonPanel: TPanel
        Left = 2
        Top = 153
        Width = 270
        Height = 49
        Align = alBottom
        BevelOuter = bvNone
        TabOrder = 8
        DesignSize = (
          270
          49)
        object BesClearButton: TButton
          Left = 104
          Top = 16
          Width = 75
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = 'Verwerfen'
          TabOrder = 0
          OnClick = BesClearButtonClick
        end
        object BesUeberButton: TButton
          Left = 185
          Top = 16
          Width = 80
          Height = 25
          Anchors = [akRight, akBottom]
          Caption = #220'bernehmen'
          TabOrder = 1
          OnClick = BesUeberButtonClick
        end
      end
      object CategoryPanel: TPanel
        Left = 2
        Top = 128
        Width = 270
        Height = 25
        Align = alBottom
        BevelOuter = bvNone
        TabOrder = 7
        DesignSize = (
          270
          25)
        object Label16: TLabel
          Left = 4
          Top = 5
          Width = 45
          Height = 13
          Anchors = [akLeft, akBottom]
          Caption = 'Kategorie'
        end
        object Label25: TLabel
          Left = 264
          Top = 8
          Width = 6
          Height = 13
          Caption = '5'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -6
          Font.Name = 'MS Sans Serif'
          Font.Style = []
          ParentFont = False
        end
        object CategoryComboBox: TComboBox
          Left = 84
          Top = 4
          Width = 182
          Height = 21
          Style = csDropDownList
          Anchors = [akLeft, akRight, akBottom]
          MaxLength = 64
          TabOrder = 0
          OnDropDown = ComboBoxDropDown
          OnEnter = ComboBoxEnter
        end
      end
      object GrundPanel: TPanel
        Left = 2
        Top = 28
        Width = 270
        Height = 25
        Align = alBottom
        BevelOuter = bvNone
        TabOrder = 3
        DesignSize = (
          270
          25)
        object Label17: TLabel
          Left = 4
          Top = 7
          Width = 76
          Height = 13
          Anchors = [akLeft, akBottom]
          Caption = 'Retouren-Grund'
        end
        object Label21: TLabel
          Left = 264
          Top = 8
          Width = 6
          Height = 13
          Caption = '1'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -6
          Font.Name = 'MS Sans Serif'
          Font.Style = []
          ParentFont = False
        end
        object GrundComboBox: TComboBox
          Left = 84
          Top = 4
          Width = 182
          Height = 21
          Style = csDropDownList
          Anchors = [akLeft, akRight, akBottom]
          MaxLength = 64
          TabOrder = 0
          OnEnter = ComboBoxEnter
        end
      end
      object ZustandCommentPanel: TPanel
        Left = 2
        Top = 103
        Width = 270
        Height = 25
        Align = alBottom
        BevelOuter = bvNone
        TabOrder = 6
        DesignSize = (
          270
          25)
        object Label18: TLabel
          Left = 4
          Top = 8
          Width = 54
          Height = 13
          Anchors = [akLeft, akBottom]
          Caption = 'Bemerkung'
        end
        object Label24: TLabel
          Left = 266
          Top = 9
          Width = 6
          Height = 13
          Caption = '4'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -6
          Font.Name = 'MS Sans Serif'
          Font.Style = []
          ParentFont = False
        end
        object ZustandCommentComboBox: TComboBox
          Left = 84
          Top = 4
          Width = 182
          Height = 21
          Style = csDropDownList
          Anchors = [akLeft, akRight, akBottom]
          MaxLength = 64
          TabOrder = 0
          OnDropDown = ComboBoxDropDown
          OnEnter = ComboBoxEnter
        end
      end
    end
    object BestPosStringGrid: TStringGridPro
      Left = 286
      Top = 24
      Width = 393
      Height = 187
      Anchors = [akLeft, akTop, akRight, akBottom]
      ColCount = 2
      DefaultColWidth = 20
      DefaultRowHeight = 18
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goColSizing, goRowSelect]
      ParentFont = False
      PopupMenu = BestPosStringGridPopupMenu
      TabOrder = 1
      GridStyle.OddColor = clInfoBk
      TitelTexte.Strings = (
        ''
        'Menge'
        'Gewicht')
      TitelFont.Charset = DEFAULT_CHARSET
      TitelFont.Color = clWindowText
      TitelFont.Height = -11
      TitelFont.Name = 'MS Sans Serif'
      TitelFont.Style = []
    end
    object ArtikelImagePanel: TPanel
      Left = 685
      Top = 24
      Width = 238
      Height = 187
      Anchors = [akTop, akRight, akBottom]
      BevelOuter = bvSpace
      Color = clWhite
      ParentBackground = False
      TabOrder = 2
      OnResize = ArtikelImagePanelResize
      object ArtikelImage: TImage32
        Left = 1
        Top = 1
        Width = 236
        Height = 185
        Align = alClient
        Bitmap.ResamplerClassName = 'TDraftResampler'
        BitmapAlign = baCenter
        Scale = 1.000000000000000000
        ScaleMode = smOptimal
        TabOrder = 0
      end
    end
  end
  object BestStringGrid: TStringGridPro
    AlignWithMargins = True
    Left = 8
    Top = 164
    Width = 915
    Height = 216
    Margins.Left = 8
    Margins.Right = 8
    Align = alClient
    ColCount = 10
    Constraints.MinHeight = 100
    DefaultColWidth = 20
    DefaultRowHeight = 18
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'MS Sans Serif'
    Font.Style = []
    Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goColSizing, goRowSelect]
    ParentFont = False
    PopupMenu = ArtikelGridPopupMenu
    TabOrder = 2
    OnClick = BestStringGridClick
    OnDblClick = BestStringGridDblClick
    OnKeyPress = BestStringGridKeyPress
    OnBeforeSort = BestStringGridBeforeSort
    GridStyle.OddColor = clInfoBk
    TitelTexte.Strings = (
      ''
      'Artikel Nr.'
      'Artikel Text'
      'Ausf'#252'hrung'
      'Bestelldatum'
      'Bestellnr.'
      'Lieferdatum'
      'Menge Soll'
      'Einheit'
      'Menge Erfasst')
    TitelFont.Charset = DEFAULT_CHARSET
    TitelFont.Color = clWindowText
    TitelFont.Height = -11
    TitelFont.Name = 'MS Sans Serif'
    TitelFont.Style = []
  end
  object FehlerLabel: TPanel
    Left = 0
    Top = 383
    Width = 931
    Height = 24
    Align = alBottom
    BevelOuter = bvNone
    Caption = 'FehlerLabel'
    Color = clRed
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -16
    Font.Name = 'MS Sans Serif'
    Font.Style = [fsBold]
    ParentBackground = False
    ParentFont = False
    TabOrder = 3
  end
  object WELEPanel: TPanel
    Left = 0
    Top = 407
    Width = 931
    Height = 34
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 4
    DesignSize = (
      931
      34)
    object Label11: TLabel
      Left = 8
      Top = 13
      Width = 147
      Height = 13
      Caption = 'Sammel-LE f'#252'r die Einlagerung:'
    end
    object WELELabel: TLabel
      Left = 176
      Top = 6
      Width = 137
      Height = 29
      Caption = 'WELELabel'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -24
      Font.Name = 'MS Sans Serif'
      Font.Style = [fsBold]
      ParentFont = False
      PopupMenu = WELEPopupMenu
    end
    object ChangeWELEButton: TButton
      Left = 753
      Top = 5
      Width = 169
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Neue Einlagerung-LE anlegen'
      TabOrder = 1
      OnClick = ChangeWELEButtonClick
    end
    object LTComboBox: TComboBoxPro
      Left = 533
      Top = 7
      Width = 213
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akTop, akRight]
      ItemHeight = 15
      TabOrder = 0
      OnChange = LTComboBoxChange
    end
  end
  object ButtonPanel: TPanel
    Left = 0
    Top = 738
    Width = 931
    Height = 57
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 7
    DesignSize = (
      931
      57)
    object OkButton: TButton
      Left = 757
      Top = 4
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Caption = 'Ok'
      ModalResult = 1
      TabOrder = 0
    end
    object AbortButton: TButton
      Left = 847
      Top = 4
      Width = 75
      Height = 25
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Abbrechen'
      ModalResult = 3
      TabOrder = 1
    end
    object StatusBar1: TStatusBar
      Left = 0
      Top = 38
      Width = 931
      Height = 19
      Panels = <
        item
          Width = 200
        end
        item
          Width = 200
        end
        item
          Width = 250
        end
        item
          Width = 50
        end>
      PopupMenu = StatusPopupMenu
    end
  end
  object ReturnHintPanel: TPanel
    Left = 0
    Top = 81
    Width = 931
    Height = 80
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      931
      80)
    object Label20: TLabel
      Left = 8
      Top = 6
      Width = 184
      Height = 13
      Caption = 'Retourenanweisungen des Mandanten'
    end
    object Bevel2: TBevel
      Left = 6
      Top = 1
      Width = 919
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 913
    end
    object ReturnHintMemo: TMemo
      Left = 8
      Top = 23
      Width = 915
      Height = 51
      Anchors = [akLeft, akTop, akRight, akBottom]
      Lines.Strings = (
        'RetunrHintMemo')
      ReadOnly = True
      TabOrder = 0
    end
  end
  object ArReturnHintPanel: TPanel
    Left = 0
    Top = 441
    Width = 931
    Height = 80
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 6
    DesignSize = (
      931
      80)
    object Bevel3: TBevel
      Left = 6
      Top = 1
      Width = 919
      Height = 6
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 913
    end
    object Label27: TLabel
      Left = 8
      Top = 8
      Width = 121
      Height = 13
      Caption = 'Retoureanweisung Artikel'
    end
    object ArReturnHintMemo: TMemo
      Left = 8
      Top = 24
      Width = 915
      Height = 54
      Anchors = [akLeft, akTop, akRight, akBottom]
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = 'MS Sans Serif'
      Font.Style = []
      Lines.Strings = (
        'ArReturnHintMemo')
      ParentFont = False
      ReadOnly = True
      TabOrder = 0
    end
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    LockType = ltReadOnly
    Parameters = <>
    Left = 412
    Top = 572
  end
  object Timer1: TTimer
    OnTimer = Timer1Timer
    Left = 332
    Top = 572
  end
  object ArtikelGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = ArtikelGridPopupMenuPopup
    Left = 200
    Top = 144
    object BestColCopyMenuItem: TMenuItem
      Caption = 'Kopieren'
      OnClick = StrinGridColCopyMenuItemClick
    end
    object BestColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      OnClick = StrinGridColOptimalMenuItemClick
    end
    object N11: TMenuItem
      Caption = '-'
    end
    object AddRetArtikelMenuItem: TMenuItem
      Caption = 'Neuer Artikel...'
      ShortCut = 115
      OnClick = AddRetArtikelMenuItemClick
    end
    object RejectRetArtikelMenuItem: TMenuItem
      Caption = 'Annahme verweigern'
      ImageIndex = 22
      OnClick = RejectRetArtikelMenuItemClick
    end
    object N10: TMenuItem
      Caption = '-'
    end
    object AddFullMultiColliMenuItem: TMenuItem
      Caption = 'Multi-Colli Artikel vollst'#228'ndig vereinnahmen'
      OnClick = AddFullMultiColliMenuItemClick
    end
    object N3: TMenuItem
      Caption = '-'
    end
    object ShowArtikelPictureMenuItem: TMenuItem
      Caption = 'Artikelbild anzeigen...'
      ImageIndex = 24
      OnClick = ShowArtikelPictureMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object Nichtsortieren1: TMenuItem
      Caption = 'Nicht sortieren'
      OnClick = Nichtsortieren1Click
    end
    object N6: TMenuItem
      Caption = '-'
    end
    object EAN13Labelnachdrucken1: TMenuItem
      Caption = 'Artikel Label nachdrucken...'
      OnClick = EAN13Labelnachdrucken1Click
    end
    object TMenuItem
      Caption = '-'
    end
    object BestExcelExportMenuItem: TMenuItem
      Caption = 'Export nach Excel...'
      OnClick = BestExcelExportMenuItemClick
    end
    object N8: TMenuItem
      Caption = '-'
    end
  end
  object ACOListForm1: TACOListForm
    Master = FrontendACOModule.ACOListManager1
    Left = 448
    Top = 576
  end
  object BestPosStringGridPopupMenu: TPopupMenu
    Images = ImageModule.FrontendImageList
    OnPopup = BestPosStringGridPopupMenuPopup
    Left = 464
    Top = 384
    object BestPosColCopyMenuItem: TMenuItem
      Caption = 'Kopieren'
      ImageIndex = 26
      OnClick = StrinGridColCopyMenuItemClick
    end
    object BestPosColOptimalMenuItem: TMenuItem
      Caption = 'Optimale Spaltenbreite'
      OnClick = StrinGridColOptimalMenuItemClick
    end
    object N12: TMenuItem
      Caption = '-'
    end
    object ShowLTInhaltMenuItem: TMenuItem
      Caption = 'Alle Artikel auf diesem LT anzeigen...'
      ImageIndex = 15
      OnClick = ShowLTInhaltMenuItemClick
    end
    object N4: TMenuItem
      Caption = '-'
    end
    object WEPosPrintEAN13MenuItem: TMenuItem
      Caption = 'EAN13-Etiketten drucken...'
      OnClick = WEPosPrintEANMenuItemClick
    end
    object WEPosPrintEAN128MenuItem: TMenuItem
      Caption = 'EAN128-Etiketten drucken...'
      OnClick = WEPosPrintEANMenuItemClick
    end
    object N7: TMenuItem
      Caption = '-'
    end
    object WEPosPrintBestandMenuItem: TMenuItem
      Caption = 'Bestandlabel drucken'
      OnClick = WEPosPrintBestandMenuItemClick
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object DelBestPosErfassenMenuItem: TMenuItem
      Caption = 'Position l'#246'schen...'
      ImageIndex = 23
      OnClick = DelBestPosErfassenMenuItemClick
    end
    object N9: TMenuItem
      Caption = '-'
    end
  end
  object WELEPopupMenu: TPopupMenu
    OnPopup = WELEPopupMenuPopup
    Left = 264
    Top = 224
    object ShowAktLTInhaltMenuItem: TMenuItem
      Caption = 'Inhalt der LE anzeigen...'
      OnClick = ShowAktLTInhaltMenuItemClick
    end
    object N5: TMenuItem
      Caption = '-'
    end
    object IncWELEFachNrMenuItem: TMenuItem
      Caption = 'Fachnr. erh'#246'hen'
      OnClick = WELEFachNrMenuItemClick
    end
    object DecWELEFachNrMenuItem: TMenuItem
      Caption = 'Fachnr. verringern'
      OnClick = WELEFachNrMenuItemClick
    end
  end
  object StatusPopupMenu: TPopupMenu
    OnPopup = StatusPopupMenuPopup
    Left = 528
    Top = 536
    object StatusAutoScanBuchOnMenuItem: TMenuItem
      Caption = 'Automatisch Scannerbuchung ein'
      OnClick = StatusAutoScanBuchMenuItemClick
    end
    object StatusAutoScanBuchOffMenuItem: TMenuItem
      Caption = 'Automatisch Scannerbuchung aus'
      OnClick = StatusAutoScanBuchMenuItemClick
    end
  end
end
