object LPStatistikForm: TLPStatistikForm
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMaximize]
  Caption = 'Lagerbereichauswertung'
  ClientHeight = 163
  ClientWidth = 852
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object BottomPanel: TPanel
    Left = 0
    Top = 122
    Width = 852
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    DesignSize = (
      852
      41)
    object CloseButton: TButton
      Left = 768
      Top = 8
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Schlie'#223'en'
      ModalResult = 1
      TabOrder = 0
    end
    object PrintButton: TButton
      Left = 16
      Top = 8
      Width = 75
      Height = 25
      Caption = 'Drucken...'
      TabOrder = 1
      OnClick = PrintButtonClick
    end
    object ExportButton: TButton
      Left = 104
      Top = 8
      Width = 75
      Height = 25
      Caption = 'Export...'
      TabOrder = 2
      Visible = False
    end
  end
  object ScrollBox: TScrollBox
    Left = 0
    Top = 57
    Width = 852
    Height = 65
    HorzScrollBar.Visible = False
    Align = alClient
    BevelInner = bvNone
    BevelOuter = bvNone
    BorderStyle = bsNone
    Constraints.MinWidth = 600
    TabOrder = 0
  end
  object DatumsPanel: TPanel
    Left = 0
    Top = 0
    Width = 852
    Height = 57
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    DesignSize = (
      852
      57)
    object DateLabel: TLabel
      Left = 3
      Top = 41
      Width = 844
      Height = 13
      Alignment = taCenter
      Anchors = [akLeft, akTop, akRight]
      AutoSize = False
      Caption = 'DateLabel'
    end
    object Bevel1: TBevel
      Left = 3
      Top = 34
      Width = 844
      Height = 8
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
    end
    object Label1: TLabel
      Left = 154
      Top = 11
      Width = 56
      Height = 13
      Alignment = taRightJustify
      Caption = 'oder Datum'
    end
    object TimePicker: TDateTimePicker
      Left = 320
      Top = 8
      Width = 89
      Height = 21
      Date = 42422.548764479160000000
      Time = 42422.548764479160000000
      Kind = dtkTime
      TabOrder = 1
      OnChange = UpdateEvent
    end
    object AktuellCheckBox: TCheckBox
      Left = 16
      Top = 10
      Width = 97
      Height = 17
      Caption = 'Aktuell'
      Checked = True
      State = cbChecked
      TabOrder = 0
      OnClick = AktuellCheckBoxClick
    end
    object DatePicker: TDateTimePicker
      Left = 216
      Top = 8
      Width = 89
      Height = 21
      Date = 42422.550476134260000000
      Time = 42422.550476134260000000
      TabOrder = 2
      OnCloseUp = UpdateEvent
      OnChange = UpdateEvent
    end
    object ExtendetCheckBox: TCheckBox
      Left = 746
      Top = 11
      Width = 97
      Height = 17
      Anchors = [akLeft, akBottom]
      Caption = 'Erweitert'
      TabOrder = 3
      OnClick = ExtendetCheckBoxClick
    end
  end
end
