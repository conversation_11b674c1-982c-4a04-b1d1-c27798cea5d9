object AssignPackplatzForm: TAssignPackplatzForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Packp<PERSON> zuweisen'
  ClientHeight = 151
  ClientWidth = 653
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  DesignSize = (
    653
    151)
  PixelsPerInch = 96
  TextHeight = 13
  object GrundLabel: TLabel
    Left = 8
    Top = 53
    Width = 45
    Height = 13
    Caption = 'Packplatz'
  end
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 31
    Height = 13
    Caption = 'Label1'
  end
  object OrderLabel: TLabel
    Left = 88
    Top = 8
    Width = 62
    Height = 13
    Caption = 'OrderLabel'
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = [fsBold]
    ParentFont = False
  end
  object Bevel1: TBevel
    Left = 6
    Top = 38
    Width = 641
    Height = 8
    Shape = bsTopLine
  end
  object PackComboBox: TComboBoxPro
    Left = 8
    Top = 69
    Width = 637
    Height = 21
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    MaxLength = 64
    TabOrder = 0
    Items.Strings = (
      'Ware mangelhaft')
  end
  object OkButton: TButton
    Left = 481
    Top = 118
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 1
  end
  object AbortButton: TButton
    Left = 570
    Top = 118
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 2
  end
end
