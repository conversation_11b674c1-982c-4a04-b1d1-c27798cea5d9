unit DeleteInvResDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, ComboBoxPro;

type
  TDeleteInvResForm = class(TForm)
    YesButton: TButton;
    AbortButton: TButton;
    InvResComboBox: TComboBoxPro;
    Label1: TLabel;
    Bevel1: TBevel;
    Bevel2: TBevel;
    Label2: TLabel;
    Label3: TLabel;
    NameLabel: TLabel;
    BenutzerLabel: TLabel;
    procedure FormDestroy(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys;

procedure TDeleteInvResForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (InvResComboBox);
end;

end.
